apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
def ip = getLocalIPv4()
android {
    compileSdkVersion 33
    buildToolsVersion "33.0.0"
    defaultConfig {
        applicationId "com.example.chaayosstocktake"
        minSdkVersion 19
        targetSdkVersion 33
        multiDexEnabled true
        versionCode 1
        versionName "1.0"
        vectorDrawables.useSupportLibrary = true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        dev {
            storeFile file("dev.jks")
            storePassword 'chaayos123'
            keyAlias 'stocktake'
            keyPassword 'chaayos123'
        }
    }
    buildTypes {
        prod {
            minifyEnabled false
            signingConfig signingConfigs.dev
            buildConfigField "String", 'BASE', '"https://relax.chaayos.com/master-service/rest/v1/"'
            buildConfigField "String", 'SCM_BASE','"https://relax.chaayos.com/scm-service/rest/v1/"'
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "boolean", "ENABLE_CRASHLYTICS_ANALYTICS", "true"
        }
        dev {
            debuggable true
            signingConfig signingConfigs.dev
            buildConfigField "String", 'BASE', '"http://dev.kettle.chaayos.com:8080/master-service/rest/v1/"'
            buildConfigField "String", 'SCM_BASE','"http://dev.kettle.chaayos.com:8080/scm-service/rest/v1/"'
            buildConfigField "boolean", "ENABLE_CRASHLYTICS_ANALYTICS", "false"
        }
        stage {
            debuggable true
            signingConfig signingConfigs.dev
            buildConfigField "String", 'BASE', '"http://stage.kettle.chaayos.com:8080/master-service/rest/v1/"'
            buildConfigField "String", 'SCM_BASE','"http://stage.kettle.chaayos.com:8080/scm-service/rest/v1/"'
            buildConfigField "boolean", "ENABLE_CRASHLYTICS_ANALYTICS", "false"
        }
        local {
            signingConfig signingConfigs.dev
            buildConfigField "String", 'BASE', ip + ':' + 9696 + "/master-service/rest/v1/" + '"'
            buildConfigField "String", 'SCM_BASE', ip + ':' + 9898 + "/scm-service/rest/v1/" + '"'
            buildConfigField "boolean", "ENABLE_CRASHLYTICS_ANALYTICS", "false"
            debuggable true
        }
        debug {
            signingConfig signingConfigs.dev
            buildConfigField "String", 'BASE', ip + ':' + 9696 + "/master-service/rest/v1/" + '"'
            buildConfigField "String", 'SCM_BASE', ip + ':' + 9898 + "/scm-service/rest/v1/" + '"'
            buildConfigField "boolean", "ENABLE_CRASHLYTICS_ANALYTICS", "false"
            debuggable true
        }

    }

    compileOptions {
        sourceCompatibility = 11
        targetCompatibility = 11
    }

}
def masterPort(){
    def mPort = project.getProperties().get("mPort")
    return mPort!=null? mPort : '9696'
}

def scmPort(){
    def mPort = project.getProperties().get("sPort")
    return mPort!=null? mPort : '9898'
}

static def getLocalIPv4() {
    def ip4s = []
    NetworkInterface.getNetworkInterfaces()
            .findAll { it.isUp() && !it.isLoopback() && !it.isVirtual() }
            .each {
                it.getInetAddresses()
                        .findAll { !it.isLoopbackAddress() && it instanceof Inet4Address }
                        .each { ip4s << it }
            }
    return '"http:/' + ip4s[0].toString()
}




dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.google.android.material:material:1.1.0'
    implementation 'androidx.annotation:annotation:1.1.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'com.google.firebase:firebase-messaging:23.3.1'
    implementation 'joda-time:joda-time:2.10.14'
    testImplementation 'junit:junit:4.13'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation 'com.squareup.retrofit2:retrofit:2.7.1'
    implementation 'com.squareup.retrofit2:converter-gson:2.7.1'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'com.journeyapps:zxing-android-embedded:3.4.0'
    implementation 'com.squareup.okhttp3:okhttp:4.9.2'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'com.toptoche.searchablespinner:searchablespinnerlibrary:1.3.1'
    implementation 'com.github.dhaval2404:imagepicker:2.1'
    implementation "com.google.firebase:firebase-bom:32.5.0"
    implementation 'com.google.firebase:firebase-crashlytics:18.5.1'
    implementation 'com.google.firebase:firebase-analytics:21.5.0'
    implementation 'androidx.multidex:multidex:2.0.1'
}
