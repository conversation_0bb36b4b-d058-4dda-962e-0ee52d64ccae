<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">

    // The width and color of the border
<!--    <stroke-->
<!--        android:width="4dp"-->
<!--        android:color="#de3d3d" />-->

    // The desired corner radius. reduce it to keep it less rounded
    <corners android:radius="8dp" />
    <solid android:color="@color/white"/>

    // Add your desired padding
    <padding
        android:left="20dp"
        android:top="10dp"
        android:right="20dp"
        android:bottom="10dp"    >
    </padding>

</shape>