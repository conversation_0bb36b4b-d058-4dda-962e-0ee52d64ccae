<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/sub_category_layout"
    android:layout_width="match_parent"
    android:layout_marginStart="6dp"
    android:layout_marginTop="6dp"
    android:layout_marginEnd="6dp"
    android:layout_marginBottom="6dp"
    android:background="@drawable/rounded_shape_green_fill"

    android:padding="8dp"
    android:layout_height="100dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <TextView
        android:id="@+id/sub_category_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_corners"
        android:fontFamily="@font/alice"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Sub Category Name"
        android:textAlignment="center"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/sub_category_event_created_by"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Event Created By"
        android:textAlignment="center"
        android:textColor="#FFFFFF"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sub_category_name" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <TextView
        android:id="@+id/sub_category_event_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="Status"
        android:textAlignment="center"
        android:textColor="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/sub_category_event_created_by"
        app:layout_constraintTop_toBottomOf="@+id/sub_category_name" />

    <Button
        android:id="@+id/sub_category_event_start_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="Start Button"
        android:textAlignment="center"
        android:textColor="#FFFFFF"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/sub_category_event_created_by"
        app:layout_constraintTop_toBottomOf="@+id/sub_category_name" />

</androidx.constraintlayout.widget.ConstraintLayout>