<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Events">

    <androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/coordinator_layout_events_activity"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:context=".Events">


            <TextView
                android:id="@+id/welcome_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="TextView"
                android:textAppearance="@style/TextAppearance.AppCompat.Large"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/avatar"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="50dp"
                android:layout_marginBottom="50dp"
                app:layout_constraintBottom_toTopOf="@+id/welcome_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/male_avatar" />

            <Button
                android:id="@+id/refresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginRight="5dp"
                android:drawableTop="@drawable/refresh_event_status"
                tools:ignore="MissingConstraints" />

            <com.example.chaayosStockTake.MovableButton
                android:id="@+id/transfer_out"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                android:layout_marginBottom="32dp"
                android:backgroundTint="@color/chaayosBrown"
                android:text="Transfer Out"
                android:textColor="@color/white"
                app:layout_constraintBottom_toTopOf="@+id/pending_receiving_button"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <com.example.chaayosStockTake.MovableButton
                android:id="@+id/pending_receiving_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                android:text="Pending/Receiving Event"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/stock_take_nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:menu="@menu/main_menu">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/deviceInfoTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="8dp"
                android:text="Version"
                android:layout_marginBottom="30dp"
                android:textColor="#888888"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/versionTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="8dp"
                android:text="Version"
                android:textColor="#888888"
                android:textSize="16sp" />
        </LinearLayout>
    </com.google.android.material.navigation.NavigationView>
</androidx.drawerlayout.widget.DrawerLayout>