<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".AppVersionNotMatched">

    <TextView
        android:id="@+id/messageTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Please Update Your App!!!"
        android:textColor="@color/red"
        android:background="@color/cardview_light_background"
        android:textSize="30sp"
        android:textAlignment="center"
        android:gravity="center"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <Button
        android:id="@+id/updateAppButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Update App"
        android:textSize="20sp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/messageTextView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
