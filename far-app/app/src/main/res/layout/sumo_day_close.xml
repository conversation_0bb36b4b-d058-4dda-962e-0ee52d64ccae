<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/start_sumo_day_close_linear_layout"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        android:layout_height="match_parent">
        <TextView
            android:id="@+id/sumo_day_close_text_view"
            android:layout_width="wrap_content"
            android:text="Sumo Day Close"
            android:layout_gravity="center"
            android:textStyle="bold"
            android:textSize="30sp"
            android:layout_height="wrap_content">
        </TextView>
        <LinearLayout
            android:id="@+id/select_stock_take_type"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/stock_take_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:layout_marginTop="25dp"
                android:layout_marginStart="5dp"
                android:text="Stock take Type *"/>

            <Spinner
                android:id="@+id/stock_take_type_spinner"
                android:layout_width="match_parent"
                android:layout_height="25dp"
                android:layout_marginVertical="10dp"
                android:visibility="visible" />
        <Button
            android:id="@+id/start_sumo_dayClose_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorPrimaryDark"
            android:text="Start"
            android:layout_gravity="center"
            android:textSize="20sp"
            android:layout_marginTop="10dp"
            android:layout_marginLeft="50dp"
            android:layout_marginRight="50dp"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/resume_sumo_day_close_linear_layout"
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="visible"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:layout_marginLeft="10dp"
                    android:text="Event Id : "/>

                <TextView
                    android:id="@+id/sumo_day_close_event_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ems="10"
                    android:layout_marginLeft="10dp"
                    android:layout_gravity="center"
                    android:text=""/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:layout_marginLeft="10dp"
                    android:text="Initiated By : "/>

                <TextView
                    android:id="@+id/sumo_day_close_event_initiated_by"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ems="10"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:text=""/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:layout_marginLeft="10dp"
                    android:text="Stock Take Type : "/>

                <TextView
                    android:id="@+id/sumo_day_close_stock_take_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginLeft="10dp"
                    android:ems="10"
                    android:text=""/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_marginTop="20dp"
                android:orientation="horizontal"
                android:layout_height="match_parent">
                <Button
                    android:id="@+id/cancel_sumo_day_close_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/red"
                    android:layout_marginLeft="20dp"
                    android:layout_weight="1"
                    android:text="Cancel"
                    android:textSize="20sp"/>
                <Button
                    android:id="@+id/continue_sumo_day_close_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/colorPrimaryDark"
                    android:layout_marginLeft="25dp"
                    android:text="Continue"
                    android:layout_weight="1"
                    android:textSize="20sp"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/list_of_products_linear_layout"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_weight="1"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_weight="0.1"
            android:background="@color/yellow"
            android:layout_height="wrap_content">
        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_list_of_products"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="0.5"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp"
            android:background="@color/white"
            android:autoText="true"
            app:iconifiedByDefault="false"
            app:queryHint="Search"
            android:maxLines="1"
            android:singleLine="true"
            tools:ignore="Deprecated" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_weight="0.5"
                android:orientation="vertical"
                android:layout_marginTop="5dp"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/Work_Station_Type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:text="Station Type"/>

                <Spinner
                    android:id="@+id/work_station_type_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_weight="0.9"
            android:orientation="vertical"
            android:layout_height="wrap_content">
            <TableRow android:background="@color/yellow"
                android:layout_width="match_parent" android:layout_height="wrap_content" android:padding="5dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Product Name"
                    android:layout_weight="0.65"
                    android:textColor="@android:color/black"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Quantity"
                    android:layout_weight="0.2"
                    android:textColor="@android:color/black"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="UOM"
                    android:layout_weight="0.15"
                    android:textColor="@android:color/black"/>
            </TableRow>
            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/list_of_products_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FFFFFF" />
            </androidx.core.widget.NestedScrollView>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:id="@+id/last_row"
            android:layout_weight="0.1"
            android:layout_gravity="bottom"
            android:layout_height="wrap_content">
            <Button
                android:id="@+id/previous_product_type_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="0.25"
                android:backgroundTint="@color/colorPrimaryDark"
                android:text="Previous"
                android:layout_gravity="bottom"
                android:textSize="10sp"/>
            <Button
                android:id="@+id/save_product_type_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/yellow"
                android:layout_weight="0.5"
                android:text="Save"
                android:layout_gravity="bottom"
                android:textSize="15sp"/>
            <Button
                android:id="@+id/next_product_type_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorPrimaryDark"
                android:text="Next"
                android:layout_weight="0.25"
                android:layout_gravity="bottom"
                android:textSize="10sp"/>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>