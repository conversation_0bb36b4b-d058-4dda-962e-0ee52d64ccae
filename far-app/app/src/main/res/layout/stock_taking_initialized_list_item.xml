<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    android:id="@+id/parent_ST_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="5dp"
        app:cardElevation="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        card_view:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/event_id_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:padding="8dp"
                android:text="EventId"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/event_status_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:text="Event Status"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/resume_ST_event_btn"
                app:layout_constraintStart_toEndOf="@+id/event_id_tv"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/resume_ST_event_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#00FFFFFF"
                android:clickable="true"
                android:padding="16dp"
                app:layout_constraintEnd_toStartOf="@+id/cancel_ST_event_btn"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/baseline_play_circle_filled_24" />

            <ImageButton
                android:id="@+id/cancel_ST_event_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#00FFFFFF"
                android:clickable="true"
                android:padding="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/baseline_cancel_24" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>