<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#75FFFFFF"
    tools:context=".SwitchAssetScanner">

    <com.journeyapps.barcodescanner.DecoratedBarcodeView
        android:id="@+id/dbv_barcode_scanner"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        app:layout_constraintBottom_toTopOf="@+id/guideline2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/view_asset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:backgroundTint="@color/yellow"
                app:maxImageSize="40dp"
                app:fabSize="mini"
                app:srcCompat="@android:drawable/ic_menu_view"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.journeyapps.barcodescanner.DecoratedBarcodeView>

    <Button
        android:id="@+id/assets_next_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="@color/chaayosGreen"
        android:backgroundTint="@color/colorPrimaryDark"
        android:text="Next"
        android:textColor="#FFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.35" />
    <TextView
        android:id="@+id/scan_alert_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Please Scan the Asset...!"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="visible"
        app:layout_constraintVertical_bias="0.5"
        app:layout_constraintHorizontal_bias="0.5"
        android:layout_gravity="center"
        android:gravity="center"
        tools:ignore="MissingConstraints" />


    <Button
        android:id="@+id/scan_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="@color/chaayosGreen"
        android:backgroundTint="#F5E014"
        android:text="Start Scanning"
        android:textColor="#FFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/upload_images_constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/scan_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dbv_barcode_scanner">


        <TextView
            android:id="@+id/new_asset_comment_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="40dp"
            android:layout_marginStart="5dp"
            android:text="Enter Comment"
            tools:ignore="MissingConstraints" />

        <EditText
            android:id="@+id/new_asset_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#CCCCCC"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="10dp"
            android:height="50dp"
            app:layout_constraintTop_toBottomOf="@+id/new_asset_comment_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:labelFor="@+id/new_asset_comment_label"
            android:layout_weight="1"
            android:inputType="textMultiLine"
            tools:ignore="MissingConstraints"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/new_asset_nested_view_c_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@+id/new_asset_comment"
            app:layout_constraintBottom_toBottomOf="@+id/upload_images_constraint_layout"
            >
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/new_asset_nested_view">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/upload_images_recycler_view_new"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#FFFFFF"
                android:visibility="invisible"/>

        </androidx.core.widget.NestedScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>



        <TextView
            android:id="@+id/old_asset_comment_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="25dp"
            android:layout_marginStart="5dp"
            android:text="Enter Comment"
            tools:ignore="MissingConstraints" />

        <EditText
            android:id="@+id/old_asset_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#CCCCCC"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="15dp"
            android:height="50dp"
            app:layout_constraintTop_toBottomOf="@+id/old_asset_comment_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:labelFor="@+id/old_asset_comment_label"
            android:layout_weight="1"
            android:inputType="textMultiLine"
            tools:ignore="MissingConstraints"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/old_asset_nested_view_c_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@+id/old_asset_comment"
            app:layout_constraintBottom_toBottomOf="@+id/upload_images_constraint_layout"
            >
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/old_asset_nested_view"
            tools:ignore="MissingConstraints">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/upload_images_recycler_view_old"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#FFFFFF"
                android:visibility="invisible" />

        </androidx.core.widget.NestedScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/validation_liner_layout"
        android:orientation="vertical">

        <TextView
            android:id="@+id/switch_asset_summary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:layout_marginStart="5dp"
            android:text="Switch Asset Summary"
            android:textStyle="bold"
            tools:ignore="MissingConstraints" />

        <TableLayout xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/switch_asset_summary_table"
            android:layout_width="match_parent"
            android:labelFor="@+id/switch_asset_summary"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp">
            <TableRow android:background="@color/yellow" android:padding="5dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Type"
                    android:textColor="@android:color/black"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Unit"
                    android:textColor="@android:color/black"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Asset Tag"
                    android:textColor="@android:color/black"/>
            </TableRow>
            <TableRow android:id="@+id/scannable" android:background="@color/cornerlightGreen" android:padding="5dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="New Asset"
                    android:textColor="@android:color/black"/>
                <TextView
                    android:id="@+id/new_asset_unit_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    />
                <TextView
                    android:id="@+id/new_asset_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    />
            </TableRow>
            <TableRow  android:id="@+id/nonscannable" android:background="@color/cornerlightGreen" android:padding="5dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Old Asset"
                    android:textColor="@android:color/black"/>
                <TextView
                    android:id="@+id/old_asset_unit_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    />
                <TextView
                    android:id="@+id/old_asset_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    />
            </TableRow>
        </TableLayout>

        <Button
            android:id="@+id/otp_validate"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="3dp"
            android:background="@color/colorPrimary"
            android:text="Validate"
            android:textColor="@color/white"
            app:layout_constraintHorizontal_bias="0.6" />

        <EditText
            android:id="@+id/otp_entered"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginTop="25dp"
            android:hint="Please Enter OTP"
            android:inputType="number"
            android:minHeight="48dp"
            android:textStyle="bold" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>