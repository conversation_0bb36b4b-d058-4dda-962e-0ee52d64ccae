<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="#FFF"
    tools:context=".StockTaking">
    <TextView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:text="Your App Version Is Old Please update Your App!!">

    </TextView>

    <ImageView
        android:id="@+id/stock_taking_bg_img"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="40dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="40dp"
        android:layout_marginBottom="40dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/create_new_stock_taking_event_card_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/stock_taking_02" />

    <androidx.cardview.widget.CardView
        android:id="@+id/new_ST_event_spinners_cv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="16dp"
        android:background="#65FFFFFF"
        android:visibility="gone"
        app:cardCornerRadius="5dp"
        app:layout_constraintBottom_toTopOf="@id/intialized_event_card_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#32517736">

            <TextView
                android:id="@+id/event_type_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:fontFamily="sans-serif-condensed-medium"
                android:text="Event Type"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Spinner
                android:id="@+id/event_type_spinner"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/event_type_tv"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/event_sub_type_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="sans-serif-condensed-medium"
                android:text="Subtype"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="@+id/event_type_tv"
                app:layout_constraintTop_toBottomOf="@id/event_type_tv" />

            <Spinner
                android:id="@+id/event_sub_type_spinner"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/event_type_tv"
                app:layout_constraintTop_toBottomOf="@id/event_type_spinner" />

            <TextView
                android:id="@+id/auditor_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="sans-serif-condensed-medium"
                android:text="Auditor"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:visibility="gone"
                app:layout_constraintEnd_toStartOf="@+id/auditor_spinner"
                app:layout_constraintTop_toBottomOf="@+id/event_sub_type_tv" />

            <Spinner
                android:id="@+id/auditor_spinner"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/event_type_spinner"
                app:layout_constraintStart_toStartOf="@+id/event_type_spinner"
                app:layout_constraintTop_toBottomOf="@+id/event_sub_type_tv" />


            <TextView
                android:id="@+id/auditor_password_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-condensed-medium"
                android:gravity="bottom"
                android:text="Password"
                android:textAlignment="gravity"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/auditor_password"
                app:layout_constraintEnd_toEndOf="@+id/auditor_tv"
                app:layout_constraintTop_toTopOf="@+id/auditor_password" />

            <EditText
                android:id="@+id/auditor_password"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:hint="Enter Password"
                android:inputType="textPassword"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/auditor_spinner"
                app:layout_constraintStart_toStartOf="@+id/auditor_spinner"
                app:layout_constraintTop_toBottomOf="@+id/auditor_spinner" />

            <Button
                android:id="@+id/validate_auditor_button"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="8dp"
                android:background="@color/yellow"
                android:text="Validate Auditor"
                android:textColor="@color/chaayosGreen"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/auditor_password_tv" />

            <Button
                android:id="@+id/create_new_event_btn_final"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="8dp"
                android:background="@color/chaayosGreen"
                android:text="Create"
                android:textColor="@color/white"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/auditor_password_tv" />

            <ProgressBar
                android:id="@+id/authentication_progressbar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/auditor_password" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/create_new_stock_taking_event_card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="16dp"
        android:outlineSpotShadowColor="@color/colorPrimary"
        app:cardCornerRadius="5dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toTopOf="@+id/new_ST_event_spinners_cv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        card_view:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:background="@color/chaayosGreen"
            android:layout_height="match_parent">

            <Button
                android:id="@+id/new_stock_taking_event_btn"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:layout_marginBottom="3dp"
                android:background="@color/colorPrimary"
                android:text="Create New Event"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.6"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>


    <androidx.cardview.widget.CardView
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="85dp"
        android:outlineSpotShadowColor="@color/colorPrimary"
        android:visibility="gone"
        app:cardCornerRadius="5dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        card_view:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/ev_id"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="@color/chaayosBrown"
                android:text="Event Id"
                android:textColor="@color/cardview_dark_background"
                android:shadowRadius="@android:integer/config_mediumAnimTime"
                android:shadowColor="@color/cardview_dark_background"
                app:layout_constraintStart_toEndOf="@id/sub_type"
                app:layout_constraintEnd_toStartOf="@id/user"
                app:layout_constraintBottom_toBottomOf="parent"/>


            <TextView
                android:id="@+id/sub_type"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="Event Type"
                android:background="@color/chaayosBrown"
                android:shadowRadius="@android:integer/config_mediumAnimTime"
                android:shadowColor="@color/cardview_dark_background"
                android:focusable="true"
                android:textColor="@color/cardview_dark_background"
                app:layout_constraintBottom_toBottomOf="parent"
                />

            <TextView
                android:id="@+id/user"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="Intitiated By"
                android:background="@color/chaayosBrown"
                android:shadowRadius="@android:integer/config_mediumAnimTime"
                android:shadowColor="@color/cardview_dark_background"
                android:clickable="true"
                android:focusable="true"
                android:padding="12dp"
                android:textColor="@color/cardview_dark_background"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />




        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>






    <androidx.cardview.widget.CardView
        android:id="@+id/intialized_event_card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:outlineSpotShadowColor="@color/colorPrimary"
        android:visibility="gone"
        app:cardCornerRadius="5dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        card_view:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/event_id_cv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:fontFamily="sans-serif-smallcaps"
                android:padding="8dp"
                android:text="EventId"
                android:textAlignment="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/resume_ST_event_btn"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/event_status_cv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:text="Event Status"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/resume_ST_event_btn"
                app:layout_constraintStart_toEndOf="@+id/event_id_cv"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/event_sub_type"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:text="Event Type"
                android:visibility="gone"
                android:background="@color/yellow"
                android:focusable="true"
                android:textColor="@color/colorPrimaryDark"
                app:layout_constraintBottom_toBottomOf="parent"
                 />

            <TextView
                android:id="@+id/user_id"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:visibility="gone"
                android:background="@color/chaayosGreen"
                android:clickable="true"
                android:focusable="true"
                android:padding="12dp"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/resume_ST_event_btn"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="@color/yellow"
                android:shadowColor="#FFF"
                android:text="Resume"
                android:visibility="gone"
                android:textColor="@color/colorPrimaryDark"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/cancel_ST_event_btn"
                app:layout_constraintStart_toEndOf="@+id/event_id_cv"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/cancel_ST_event_btn"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="#E64A19"
                android:clickable="true"
                android:focusable="true"
                android:padding="12dp"
                android:text="Cancel "
                android:visibility="gone"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>