<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/switch_asset_images"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:padding="5dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@android:color/darker_gray"
        tools:ignore="MissingConstraints" />
    <ImageView
        android:id="@+id/switch_asset_upload_image"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:scaleType="fitCenter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@android:drawable/ic_menu_add"
        tools:ignore="MissingConstraints" />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/remove_uploaded_photo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:elevation="15dp"
        android:clickable="true"
        app:maxImageSize="20dp"
        app:fabSize="mini"
        android:scaleType="center"
        app:backgroundTint="@color/red"
        app:srcCompat="@drawable/baseline_cancel_black_48dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="MissingConstraints" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <View
        android:layout_width="match_parent"
        app:layout_constraintStart_toEndOf="@+id/switch_asset_upload_image"
        android:layout_height="1dp"
        android:background="@android:color/darker_gray"
        tools:ignore="MissingConstraints" />
</androidx.constraintlayout.widget.ConstraintLayout>