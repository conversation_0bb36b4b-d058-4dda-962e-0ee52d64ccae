<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_marginTop="4dp"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/product_basic_layout"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_weight="1"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/product_name"
            android:layout_width="wrap_content"
            android:text="Product Name"
            android:layout_gravity="center"
            android:textStyle="bold"
            android:layout_weight="0.65"
            android:textSize="14sp"
            android:layout_height="wrap_content">
        </TextView>
        <TextView
            android:id="@+id/entered_quantity"
            android:layout_width="wrap_content"
            android:text="5.23"
            android:layout_weight="0.2"
            android:textStyle="bold"
            android:textSize="14sp"
            android:layout_height="wrap_content">
        </TextView>
        <TextView
            android:id="@+id/uom"
            android:layout_width="wrap_content"
            android:text="KG"
            android:layout_weight="0.15"
            android:textStyle="bold"
            android:textSize="14sp"
            android:layout_height="wrap_content">
        </TextView>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/product_packaging_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/product_packaging_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="15dp"
            android:background="#FFFFFF" />
    </LinearLayout>

</LinearLayout>