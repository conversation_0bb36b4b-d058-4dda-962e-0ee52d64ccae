<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_margin="5dp"
    android:layout_width="match_parent"
    android:layout_height="100dp">


        <TableLayout
            android:id="@+id/table"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingLeft="2dp"
            android:paddingRight="2dp">
            <TableRow android:padding="5dp">
                <TextView
                    android:id="@+id/lost_found_asset_id"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:textSize="14sp"
                    android:text="Scannable"
                    android:textColor="@android:color/black"/>
                <TextView
                    android:id="@+id/lost_found_asset_name"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:textSize="14sp"
                    android:text="dumm1"
                    android:textColor="@android:color/black"
                    />
                <TextView
                    android:id="@+id/lost_found_asset_tag"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:textSize="14sp"
                    android:text="dummy2"
                    android:textColor="@android:color/black"
                    />
            </TableRow>
        </TableLayout>
</LinearLayout>
