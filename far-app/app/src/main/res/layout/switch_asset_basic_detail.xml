<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:id="@+id/switch_asset_basic_details"
    android:layout_height="match_parent">
<LinearLayout android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <TextView
        android:id="@+id/switch_with_unit_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textStyle="bold"
        android:layout_marginTop="25dp"
        android:layout_marginStart="5dp"
        android:text="Switch With Unit *"/>

    <com.toptoche.searchablespinnerlibrary.SearchableSpinner
        android:id="@+id/switch_with_unit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Switch With Unit"
        android:layout_marginTop="25dp"
        android:imeOptions="actionDone"
        android:labelFor="@+id/switch_with_unit_label"
        android:selectAllOnFocus="true" />

    <TextView
        android:id="@+id/received_by_employee_label"
        android:layout_width="match_parent"
        android:textStyle="bold"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:layout_marginStart="5dp"
        android:text="Received By *"/>

    <com.toptoche.searchablespinnerlibrary.SearchableSpinner
        android:id="@+id/received_by_employee"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Switch With Unit"
        android:layout_marginTop="25dp"
        android:imeOptions="actionDone"
        android:labelFor="@+id/switch_with_unit_label"
        android:selectAllOnFocus="true" />

    <TextView
        android:id="@+id/bir_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textStyle="bold"
        android:layout_marginTop="25dp"
        android:layout_marginStart="5dp"
        android:text="BIR Ticket *"/>

    <EditText
        android:id="@+id/bir_ticket"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ems="10"
        android:labelFor="@id/bir_label"
        android:layout_marginTop="25dp"
        android:layout_marginStart="5dp"
        android:inputType="text"
        android:text=""/>

    <TextView
        android:id="@+id/switch_reason_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textStyle="bold"
        android:layout_marginTop="25dp"
        android:layout_marginStart="5dp"
        android:text="Switch Reason *"/>

    <Spinner
        android:id="@+id/switch_reason"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:layout_marginVertical="10dp"
        android:visibility="visible" />


</LinearLayout>


    <Button
        android:id="@+id/switch_asset_next_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="@color/colorPrimaryDark"
        android:text="Next"
        android:textColor="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />


</androidx.constraintlayout.widget.ConstraintLayout>