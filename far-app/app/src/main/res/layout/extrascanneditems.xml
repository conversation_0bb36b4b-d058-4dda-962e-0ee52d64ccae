<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TableLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/parentTable"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:ignore="MissingConstraints">
        <TableRow android:background="@color/yellow"
            android:id="@+id/parentRow"
            android:padding="5dp" android:layout_weight="1">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Asset ID" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Assets Name" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Unit Id" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Unit Name" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Tag Value" />
        </TableRow>

        <ScrollView
            android:layout_width="wrap_content"
            android:layout_height="300dp"
            android:scrollbars="vertical"
            app:layout_constraintTop_toBottomOf="@id/parentRow"
            >
            <HorizontalScrollView
                android:layout_width="match_parent" android:layout_height="fill_parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TableLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    android:id="@+id/extraScanned"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp"
                    android:textAlignment="center"
                    android:visibility="gone"
                    android:orientation="vertical"
                    android:maxHeight="300dp">
                </TableLayout>
            </LinearLayout>
            </HorizontalScrollView>
        </ScrollView>
    </TableLayout>

    <TextView
        android:id="@+id/infoText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:visibility="visible"
        android:background="@color/cornerlightGreen"
        app:layout_constraintTop_toTopOf="@id/textLayout"
        app:layout_constraintBottom_toBottomOf="@+id/parentTable"
        android:layout_marginRight="@dimen/activity_horizontal_margin"
        android:text="PLease Enter Detail Of Assets Found With No Sticker!!"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="@android:color/black"
        tools:ignore="MissingConstraints" />
    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/textLayout"
        tools:context=".MainActivity"
        app:layout_constraintTop_toBottomOf="@id/infoText"
        tools:ignore="MissingConstraints"
        android:background="@color/yellow"

        >

        <!-- add Camera Button to open the Camera -->
        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            android:textAlignment="center"
            android:text="Product Name"
            android:layout_weight="1"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="@color/colorPrimaryDark" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignRight="@+id/textView1"
            android:visibility="visible"
            android:text="Comment"
            android:textAlignment="center"
            android:layout_weight="1"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="@color/colorPrimaryDark" />



    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/inputlayout"
        app:layout_constraintTop_toBottomOf="@id/textLayout"
        tools:ignore="MissingConstraints"
        android:background="@color/cornerlightGreen">
        <EditText
            android:id="@+id/editText1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#CCCCCC"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:height="150dp"
            android:layout_weight="1"
            android:inputType="textMultiLine" >
            <requestFocus/>
        </EditText>

        <EditText
            android:id="@+id/editText2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:layout_weight="1"
            android:height="150dp"
            android:background="#CCCCCC"
            app:layout_constraintTop_toBottomOf="@id/editText1"
            android:inputType="textMultiLine">
        <requestFocus/>
        </EditText>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/inputlayout">
        <Button
            android:id="@+id/camera_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Click" />

        <!-- add ImageView to display the captured image -->
        <ImageView
            android:id="@+id/click_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_toRightOf="@id/camera_button" />
        <Button
            android:id="@+id/add_button"
            android:layout_width="wrap_content"
            android:background="@color/chaayosGreen"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Add"
            />
    </LinearLayout>
    <Button
        android:id="@+id/back_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="@color/yellow"
        android:text="Back"
        android:visibility="gone"
        android:textColor="@color/colorPrimaryDark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <Button
        android:id="@+id/next_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="@color/colorPrimaryDark"
        android:text="Next"
        android:textColor="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline" />


</androidx.constraintlayout.widget.ConstraintLayout>