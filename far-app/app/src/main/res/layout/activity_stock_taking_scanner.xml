<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/chaayosGreen"
    tools:context=".StockTakingScanner">

    <com.journeyapps.barcodescanner.DecoratedBarcodeView
        android:id="@+id/dbv_barcode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        app:layout_constraintBottom_toTopOf="@+id/guideline3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

    </com.journeyapps.barcodescanner.DecoratedBarcodeView>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/items_recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="8dp"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@+id/pause_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dbv_barcode" />


    <Button
        android:id="@+id/submit_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="@color/colorPrimaryDark"
        android:padding="16dp"
        android:shadowColor="@color/chaayosBrown"
        android:text="Next"
        android:textColor="#FFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.35" />

    <Button
        android:id="@+id/pause_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="#F5E014"
        android:padding="16dp"
        android:shadowColor="@color/chaayosBrown"
        android:text="Save"
        android:textColor="@color/colorPrimaryDark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline"
        app:layout_constraintStart_toStartOf="parent" />



    <Button
        android:id="@+id/cancel_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:backgroundTint="#DB0000"
        android:padding="16dp"
        android:text="@android:string/cancel"
        android:textColor="#FFF"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
    <Button
        android:id="@+id/torch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="70dp"
        android:layout_marginRight="5dp"
        android:background="@drawable/torch"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >
    </Button>


     <RelativeLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:textAppearance="@style/TextAppearance.AppCompat.Small"
        app:layout_constraintBottom_toTopOf="@+id/submit_btn"
        app:layout_constraintTop_toBottomOf="@+id/dbv_barcode"
        tools:context=".StockTakingScanner"
         tools:ignore="MissingConstraints">

         <Spinner
             android:id="@+id/listTypeFilter"
             android:layout_width="match_parent"
             android:background="@color/yellow"
             android:layout_marginRight="5dp"
             android:layout_height="25dp"
             android:layout_marginVertical="10dp"
             android:visibility="visible" />

         <com.google.android.material.floatingactionbutton.FloatingActionButton
             android:id="@+id/floatingActionButton2"
             android:layout_width="wrap_content"
             android:background="@color/yellow"
             android:backgroundTint="@color/yellow"
             app:borderWidth="0dp"
             app:elevation="2dp"
             app:maxImageSize="12dp"
             android:layout_marginTop="2dp"
             android:layout_marginRight="-5dp"
             app:fabSize="mini"
             android:layout_height="wrap_content"
             android:layout_alignEnd="@+id/listTypeFilter"
             android:clickable="false"
             app:srcCompat="@android:drawable/arrow_down_float" />


         <ExpandableListView
             android:id="@+id/expandableListView"
             android:layout_marginTop="0.2dp"
             android:background="@color/chaayosGreen"
             android:layout_width="wrap_content"
             android:layout_height="wrap_content"
             android:layout_marginBottom="250dp"
             android:divider="@android:color/background_light"
             android:indicatorLeft="?android:attr/expandableListPreferredItemIndicatorLeft"
             android:dividerHeight="5dp"
             app:layout_constraintEnd_toEndOf="parent"
             app:layout_constraintStart_toStartOf="parent"
             android:nestedScrollingEnabled="true"
             android:layout_below="@id/listTypeFilter"
             />

    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>

