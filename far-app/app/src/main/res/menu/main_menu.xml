<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

<!--    <item-->
<!--        android:id="@+id/action_settings"-->
<!--        app:showAsAction="never"-->
<!--        android:title="Settings">-->
<!--    </item>-->

    <group>
        <item
            android:id="@+id/asset_lookup_button"
            android:icon="@drawable/asset_look_up_search"
            app:showAsAction="never"
            android:title="Asset LookUp">
        </item>
        <item
            android:id="@+id/stock_taking_button"
            android:icon="@drawable/stock_take_image"
            app:showAsAction="never"
            android:title="Stock Take Event">
        </item>
        <item
            android:id="@+id/switch_asset_button"
            android:icon="@drawable/switch_asset_image"
            app:showAsAction="never"
            android:title="Switch Assets">
        </item>
        <item
            android:id="@+id/sumo_day_close"
            android:icon="@drawable/food_icon"
            app:showAsAction="never"
            android:title="Day Close">
        </item>
        <item
            android:id="@+id/action_logout"
            android:icon="@drawable/baseline_power_settings_new_white_48"
            app:showAsAction="never"
            android:title="Logout">
        </item>
    </group>
</menu>