package com.example.chaayosStockTake.domain;

import java.math.BigDecimal;

public class DayCloseProductPackagingMappingsDTO {

    private Integer productPackagingMappingId;

    private Integer sumoDayCloseProductItemId;

    private Integer packagingId;

    private String packagingName;

    private BigDecimal quantity;

    private BigDecimal conversionRatio;

    public Integer getProductPackagingMappingId() {
        return productPackagingMappingId;
    }

    public void setProductPackagingMappingId(Integer productPackagingMappingId) {
        this.productPackagingMappingId = productPackagingMappingId;
    }

    public Integer getSumoDayCloseProductItemId() {
        return sumoDayCloseProductItemId;
    }

    public void setSumoDayCloseProductItemId(Integer sumoDayCloseProductItemId) {
        this.sumoDayCloseProductItemId = sumoDayCloseProductItemId;
    }

    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    public String getPackagingName() {
        return packagingName;
    }

    public void setPackagingName(String packagingName) {
        this.packagingName = packagingName;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }
}
