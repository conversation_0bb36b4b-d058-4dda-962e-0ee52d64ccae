package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;

public class Unit {
    @SerializedName("address")
    private String address;
    private Integer cafeManagerId;
    private String category;
    private String city;
    private Integer companyId;
    private String contact;
    private String email;
    private boolean hotAndColdMerged;
    private Integer id;
    private String latitude;
    private boolean live;
    private boolean liveInventoryEnabled;
    private String locationCode;
    private String longitude;
    private String name;
    private Integer noOfTakeawayTerminals;
    private Integer noOfTerminal;
    private String packagingType;
    private Integer packagingValue;
    private boolean partnerPriced;
    private String referenceName;
    private String region;
    private String state;
    private String stateCode;
    private String status;
    private String subCategory;
    private String tin;
    private boolean tokenEnabled;
    private String trueCallerEnabled;
    private Integer unitManagerId;
    private boolean workStationEnabled;
    private String _id = null;
    private Location location;

    public Unit(String address, Integer cafeManagerId, String category, String city, Integer companyId, String contact, String email, boolean hotAndColdMerged, Integer id, String latitude, boolean live, boolean liveInventoryEnabled, String locationCode, String longitude, String name, Integer noOfTakeawayTerminals, Integer noOfTerminal, String packagingType, Integer packagingValue, boolean partnerPriced, String referenceName, String region, String state, String stateCode, String status, String subCategory, String tin, boolean tokenEnabled, String trueCallerEnabled, Integer unitManagerId, boolean workStationEnabled, String _id, Location location) {
        this.address = address;
        this.cafeManagerId = cafeManagerId;
        this.category = category;
        this.city = city;
        this.companyId = companyId;
        this.contact = contact;
        this.email = email;
        this.hotAndColdMerged = hotAndColdMerged;
        this.id = id;
        this.latitude = latitude;
        this.live = live;
        this.liveInventoryEnabled = liveInventoryEnabled;
        this.locationCode = locationCode;
        this.longitude = longitude;
        this.name = name;
        this.noOfTakeawayTerminals = noOfTakeawayTerminals;
        this.noOfTerminal = noOfTerminal;
        this.packagingType = packagingType;
        this.packagingValue = packagingValue;
        this.partnerPriced = partnerPriced;
        this.referenceName = referenceName;
        this.region = region;
        this.state = state;
        this.stateCode = stateCode;
        this.status = status;
        this.subCategory = subCategory;
        this.tin = tin;
        this.tokenEnabled = tokenEnabled;
        this.trueCallerEnabled = trueCallerEnabled;
        this.unitManagerId = unitManagerId;
        this.workStationEnabled = workStationEnabled;
        this._id = _id;
        this.location = location;
    }

    public String getAddress() {
        return address;
    }

    public Integer getCafeManagerId() {
        return cafeManagerId;
    }

    public String getCategory() {
        return category;
    }

    public String getCity() {
        return city;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public String getContact() {
        return contact;
    }

    public String getEmail() {
        return email;
    }

    public boolean isHotAndColdMerged() {
        return hotAndColdMerged;
    }

    public Integer getId() {
        return id;
    }

    public String getLatitude() {
        return latitude;
    }

    public boolean isLive() {
        return live;
    }

    public boolean isLiveInventoryEnabled() {
        return liveInventoryEnabled;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public String getLongitude() {
        return longitude;
    }

    public String getName() {
        return name;
    }

    public Integer getNoOfTakeawayTerminals() {
        return noOfTakeawayTerminals;
    }

    public Integer getNoOfTerminal() {
        return noOfTerminal;
    }

    public String getPackagingType() {
        return packagingType;
    }

    public Integer getPackagingValue() {
        return packagingValue;
    }

    public boolean isPartnerPriced() {
        return partnerPriced;
    }

    public String getReferenceName() {
        return referenceName;
    }

    public String getRegion() {
        return region;
    }

    public String getState() {
        return state;
    }

    public String getStateCode() {
        return stateCode;
    }

    public String getStatus() {
        return status;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public String getTin() {
        return tin;
    }

    public boolean isTokenEnabled() {
        return tokenEnabled;
    }

    public String getTrueCallerEnabled() {
        return trueCallerEnabled;
    }

    public Integer getUnitManagerId() {
        return unitManagerId;
    }

    public boolean isWorkStationEnabled() {
        return workStationEnabled;
    }

    public String get_id() {
        return _id;
    }

    public Location getLocation() {
        return location;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setCafeManagerId(Integer cafeManagerId) {
        this.cafeManagerId = cafeManagerId;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setHotAndColdMerged(boolean hotAndColdMerged) {
        this.hotAndColdMerged = hotAndColdMerged;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public void setLive(boolean live) {
        this.live = live;
    }

    public void setLiveInventoryEnabled(boolean liveInventoryEnabled) {
        this.liveInventoryEnabled = liveInventoryEnabled;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setNoOfTakeawayTerminals(Integer noOfTakeawayTerminals) {
        this.noOfTakeawayTerminals = noOfTakeawayTerminals;
    }

    public void setNoOfTerminal(Integer noOfTerminal) {
        this.noOfTerminal = noOfTerminal;
    }

    public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
    }

    public void setPackagingValue(Integer packagingValue) {
        this.packagingValue = packagingValue;
    }

    public void setPartnerPriced(boolean partnerPriced) {
        this.partnerPriced = partnerPriced;
    }

    public void setReferenceName(String referenceName) {
        this.referenceName = referenceName;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public void setState(String state) {
        this.state = state;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public void setTokenEnabled(boolean tokenEnabled) {
        this.tokenEnabled = tokenEnabled;
    }

    public void setTrueCallerEnabled(String trueCallerEnabled) {
        this.trueCallerEnabled = trueCallerEnabled;
    }

    public void setUnitManagerId(Integer unitManagerId) {
        this.unitManagerId = unitManagerId;
    }

    public void setWorkStationEnabled(boolean workStationEnabled) {
        this.workStationEnabled = workStationEnabled;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    @NonNull
    @Override
    public String toString() {
        return referenceName;
    }
}
