package com.example.chaayosStockTake;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON> on 2/25/2020.
 */
public class AttributesListAdapter extends ArrayAdapter<String> {
    private final Activity context;
    private final ArrayList<String> attributeIds;
    private final ArrayList<String> attributeNames;

    public AttributesListAdapter(Activity context, ArrayList<String> attributeIds, ArrayList<String> attributeNames) {
        super(context, R.layout.attributes_listview, attributeNames);
        this.context = context;
        this.attributeIds = attributeIds;
        this.attributeNames = attributeNames;
    }

    public View getView(int position, View view, ViewGroup parent) {
        LayoutInflater inflater = context.getLayoutInflater();
        View rowView = inflater.inflate(R.layout.attributes_listview, null, true);

        TextView attributeId = rowView.findViewById(R.id.attribute_id);
        TextView attributeName = rowView.findViewById(R.id.attribute_name);

        attributeId.setText(attributeIds.get(position));
        attributeName.setText(attributeNames.get(position));
        return rowView;
    }

    ;
}
