package com.example.chaayosStockTake;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

public class AppVersionNotMatched extends AppCompatActivity implements View.OnClickListener {

    Button updateAppButton;

    @SuppressLint({"RestrictedApi", "MissingInflatedId", "SetTextI18n"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.app_version_not_matched);
        updateAppButton = findViewById(R.id.updateAppButton);
        updateAppButton.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(AppVersionNotMatched.this);
        alertDialog.setTitle("ALERT");
        alertDialog.setMessage("A new Version Of Stock Take App is Available.Please Download the App and Install it.");
        alertDialog.setNegativeButton("CANCEL", (dialog, which) -> dialog.cancel());
        alertDialog.setPositiveButton("INSTALL", (dialog, which) -> {
            String url = "https://drive.google.com/drive/folders/1UDPrKSsLeeh61IL78MmlUTAVh5WAUKXr";
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse(url));
            intent.setPackage("com.android.chrome");
            startActivity(intent);
        });
        AlertDialog dialog = alertDialog.create();
        dialog.show();
    }
}
