package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.media.MediaPlayer;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.StrictMode;
import android.provider.Settings;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ExpandableListView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.Exceptions.SCMError;
import com.example.chaayosStockTake.util.AppConstants;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class StockTakingScanner extends AppCompatActivity implements View.OnClickListener,Serializable, AdapterView.OnItemSelectedListener {
    private DecoratedBarcodeView dbvScanner;
    private ArrayList<String> itemsListData;
    private StockTakingEvent stockTakingEvent;
    AlertDialog.Builder builder;
    RecyclerView itemsList;

    private Boolean torchOn = false;


    private Integer eventId;
    private Integer unitId;
    TextView nothingScanned;
    private StockTakingEvent stockTakingEventResponse;

    private StockTakingRequest stockTakingRequest;
    private User eventAuditor;

    private ExpandableListView expandableListView;
    private CustomizedExpandableListAdapter expandableListAdapter;
    private List<String> expandableTitleList;
    private HashMap<String, List<StockTakingScannedItem>> expandableDetailList;

    private HashMap<String,List<StockTakingScannedItem>> scannedAssetsMap = new HashMap<>();

    private HashMap<String,StockTakingScannedItem> assetTagMap;

    private Boolean isTransferOut = false;

    private StockTakingRequest transferOutEvent;

    private Map<String,Boolean> assetScannedMap;

    private HashMap<String,List<StockTakingScannedItem>> productAssetMap = new HashMap<>();


    private List<StockTakingScannedItem> extraScannedItems = new ArrayList<>();

    private Map<String,Boolean> extraScanMap = new HashMap<>();

    private MediaPlayer mp;

    private String lastScannedItem;

    private Snackbar snackbar;

    private String isSplit = AppConstants.NO;

    private Map<String, StockTakingScannedItem> completeUnitAssetList = new HashMap<>();

    private List<String> dropDownListFilter = UtilClass.utilClass.getFilterTypes();

    private Spinner dropDownFilterSelection;

    private String currentSelectedFilter = "CATEGORY";

    private StockTakingScannedItem lastScannedTransferOutItem;

    private AlertDialog alertDialog;

    private OkHttpClient httpClient;

    private boolean hideAssetTag;

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setDeviceInfo();
        int SDK_INT = android.os.Build.VERSION.SDK_INT;
        if (SDK_INT > 8)
        {
            StrictMode.ThreadPolicy policy = new StrictMode.ThreadPolicy.Builder()
                    .permitAll().build();
            StrictMode.setThreadPolicy(policy);
            //your codes here

        }


        super.onCreate(savedInstanceState);
        httpClient = new OkHttpClient.Builder()
                .connectTimeout(2, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .writeTimeout(2, TimeUnit.MINUTES)
                .build();
        assetTagMap = new HashMap<>();
        setContentView(R.layout.activity_stock_taking_scanner);
        requestPermission();
        stockTakingEvent = null;
        Intent intent = getIntent();
        Gson gson = new Gson();
        alertDialog = UtilClass.utilClass.getDialogProgressBar(this).create();

        SharedPreferences activityPREF = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Map<String,?> activityPrefs = activityPREF.getAll();

        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();


        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        if(Objects.nonNull(activityPrefs.get("unitName"))){
            getSupportActionBar().setSubtitle((String) activityPrefs.get("unitName"));
        }

        if(prefs.containsKey("transferOut")){
            isTransferOut = true;
            stockTakingRequest = gson.fromJson((String) prefs.get("transferOut"),StockTakingRequest.class);
            Button pauseBtn = findViewById(R.id.pause_btn);
            pauseBtn.setText("Cancel");

            if (stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT)) {
                Type type = new TypeToken<HashMap<String, List<StockTakingScannedItem>>>() {
                }.getType();
                expandableDetailList = gson.fromJson((String) prefs.get("productMap"), type);

                Log.d("productMap:", (String) prefs.get("productMap"));
                Log.d("expandable list:", gson.toJson(expandableDetailList));
            } else {
                HashMap<String, List<StockTakingScannedItem>> pendingAssetMap = new HashMap<>();
                for (StockTakingScannedItem item : stockTakingRequest.getAvailableAssets()) {
                    if (!pendingAssetMap.containsKey(item.getSubCategory())) {
                        pendingAssetMap.put(item.getSubCategory(), new ArrayList<>());
                    }
                    pendingAssetMap.get(item.getSubCategory()).add(item);
                    assetTagMap.put(item.getAssetTagValue(),item);
                }
                expandableDetailList = pendingAssetMap;
                sortPendingAssets();
            }

            List<StockTakingScannedItem> items = stockTakingRequest.getAvailableAssets();
            for(StockTakingScannedItem item : items){
                assetTagMap.put(item.getAssetTagValue(),item);
            }
            if(prefs.containsKey("StockTakingPrevListData")){
                String previouslyScannedJson = (String) prefs.get("StockTakingPrevListData");
                Type jsonType = new TypeToken<ArrayList<String>>(){}.getType();
                itemsListData = gson.fromJson(previouslyScannedJson,jsonType);
            }
        } else if (prefs.containsKey("eventResponse")) {
            //stockTakingEventResponse = (StockTakingEvent) intent.getSerializableExtra("StockTakingEventResponse");
            //String temp = intent.getStringExtra("eventResponse");
            String temp = (String) prefs.get("eventResponse");
            stockTakingRequest = gson.fromJson(temp, StockTakingRequest.class);
            List<StockTakingScannedItem> items = stockTakingRequest.getAvailableAssets();
            for(StockTakingScannedItem item : items){
                assetTagMap.put(item.getAssetTagValue(),item);
            }
            if(Objects.nonNull(stockTakingRequest.getExtraScannedItems())){
                extraScannedItems = stockTakingRequest.getExtraScannedItems();
            }
        }

        if (prefs.containsKey("isEventSplit")) {
            isSplit =  (String) prefs.get("isEventSplit");
            Type jsonType = new TypeToken<List<StockTakingScannedItem>>(){}.getType();
            List<StockTakingScannedItem> unitsAssetList =  gson.fromJson((String) prefs.get("completeUnitAssetList"), jsonType);
            if (Objects.nonNull(unitsAssetList)) {
                for (StockTakingScannedItem item : unitsAssetList) {
                    completeUnitAssetList.put(item.getAssetTagValue(), item);
                }
            }
        }
        if (Objects.nonNull(stockTakingRequest) && Objects.nonNull(stockTakingRequest.getEventType()) && stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.ASSET_RECEIVING)) {
            getSupportActionBar().setTitle("Asset Receiving");
        } else {
            getSupportActionBar().setTitle(stockTakingRequest.getSubType());
        }

        //Log.d("pending assets ::", (String) prefs.get("pendingAssetsJson"));
        if(Objects.nonNull(prefs.get("pendingAssetsJson")) && Boolean.FALSE.equals(isTransferOut)){
            String pendingAssetsJson = (String) prefs.get("pendingAssetsJson");
            Type type = new TypeToken<HashMap<String,List<StockTakingScannedItem>>>(){}.getType();
            expandableDetailList = gson.fromJson(pendingAssetsJson,type);
            sortPendingAssets();
        }else if(Objects.nonNull(prefs.get("subCategoryMap"))  && Boolean.FALSE.equals(isTransferOut)){
                String subCategoryJson = (String) prefs.get("subCategoryMap");
                Type type = new TypeToken<HashMap<String,List<StockTakingScannedItem>>>(){}.getType();
                productAssetMap = new Gson().fromJson(subCategoryJson, type);
                expandableDetailList = (HashMap<String, List<StockTakingScannedItem>>) productAssetMap;
                sortPendingAssets();
        }

        if(Objects.nonNull(prefs.get("scannedAssets"))  && Boolean.FALSE.equals(isTransferOut)){
            String scannedAssetsJson = (String) prefs.get("scannedAssets");
            Log.d("scanned",scannedAssetsJson);
            Type type = new TypeToken<HashMap<String,List<StockTakingScannedItem>>>(){}.getType();
            scannedAssetsMap = gson.fromJson(scannedAssetsJson,type);
            scannedAssetsMap = removeDuplicate(scannedAssetsMap);
        }else{
            scannedAssetsMap = getInitializedList();
        }

        createScannedAssetIdMap();

        createExtraScannedMap();


        initialize();

        expandableListView = (ExpandableListView) findViewById(R.id.expandableListView);


        expandableTitleList = new ArrayList<String>(expandableDetailList.keySet());
        hideAssetTag = Objects.nonNull(stockTakingRequest) && Objects.nonNull(stockTakingRequest.getEventType()) && stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.ASSET_RECEIVING);
        expandableListAdapter = new CustomizedExpandableListAdapter(this, expandableTitleList, expandableDetailList,false, false, hideAssetTag);
        expandableListView.setAdapter(expandableListAdapter);

        dropDownFilterSelection = findViewById(R.id.listTypeFilter);
        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>(StockTakingScanner.this, android.R.layout.simple_spinner_item, dropDownListFilter);
        dropDownFilterSelection.setAdapter(spinnerArrayAdapter);
        dropDownFilterSelection.setOnItemSelectedListener(StockTakingScanner.this);

        if (Objects.nonNull(stockTakingRequest) && Objects.nonNull(stockTakingRequest.getEventType()) &&
                (stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.ASSET_RECEIVING) ||stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT))) {
            if (Objects.nonNull(itemsListData)) {
                itemsListData.clear();
            }
        }


        dbvScanner.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                String scannedItem = result.getText();
                pauseScanner();
                if (itemsListData.contains(scannedItem) ||assetScannedMap.containsKey(scannedItem)
                        || extraScanMap.containsKey(scannedItem)){
                    StockTakingScannedItem tempItem = assetTagMap.get(scannedItem);
                    if(Objects.nonNull(tempItem) && tempItem.isFound().equals(Boolean.FALSE)){
                        processScannedAsset(scannedItem, true);
                        playMusic("SUCCESS");
                        View rootView = findViewById(android.R.id.content);
                        String msg = tempItem.getAssetName() +
                                " - " +
                                tempItem.getAssetTagValue();
                        lastScannedItem = scannedItem;
                        showSnackBar(rootView, "Scanned Item : \n" + msg, tempItem, true);
                    }else{
                        tempItem = Objects.nonNull(assetTagMap.get(scannedItem)) ? assetTagMap.get(scannedItem) : getExtraScannedItem(scannedItem);
                        if (Objects.nonNull(tempItem)) {
                            Toast.makeText(getApplicationContext(), "Duplicate Scan Detected! \n " +
                                            tempItem.getAssetName() + " - " + tempItem.getAssetTagValue(),
                                    Toast.LENGTH_SHORT).show();
                        }
                        playMusic("DUPLICATE");
                        if (Objects.nonNull(lastScannedItem) && !lastScannedItem.equalsIgnoreCase(scannedItem)) {
                            dismissSnackBar();
                        }
                    }
                } else if (!assetTagMap.containsKey(scannedItem)) {
                    if (isSplit.equalsIgnoreCase(AppConstants.YES)) {
                        if (completeUnitAssetList.containsKey(scannedItem)) {
                            View rootView = findViewById(android.R.id.content);
                            StockTakingRequest stockTakingRequest1 = new Gson().fromJson((String) prefs.get("SavedChildStockTakingEvent"), StockTakingRequest.class);
                            String currentSubCategory = Objects.nonNull(stockTakingRequest.getSubCategory()) ? stockTakingRequest.getSubCategory() : stockTakingRequest1.getSubCategory();
                            Snackbar.make(rootView, "Scanned Asset Belongs - " + completeUnitAssetList.get(scannedItem).getSubCategory()
                                            + "\nCurrent Event Sub category is : " + currentSubCategory
                                    , Snackbar.LENGTH_SHORT).show();
                            playMusic("OTHER_SUB_CATEGORY");
                        } else {
                            showAssetDetails(scannedItem);
                        }
                    } else {
                        if (Objects.nonNull(stockTakingRequest) && Objects.nonNull(stockTakingRequest.getEventType()))
                            if (stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.ASSET_RECEIVING)) {
                                UtilClass.utilClass.getToast(StockTakingScanner.this, "Scanned Item : " + scannedItem + " is not in the Receiving List").show();
                            } else if (stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT)) {
                                UtilClass.utilClass.getToast(StockTakingScanner.this, "Scanned Item : " + scannedItem + " is not in the Transfer List").show();
                            } else {
                                showAssetDetails(scannedItem);
                            }
                    }
                } else {
                    processScannedAsset(scannedItem, true);
                    playMusic("SUCCESS");
                    View rootView = findViewById(android.R.id.content);
                    StockTakingScannedItem tempItem = assetTagMap.get(scannedItem);
                    String msg = tempItem.getAssetName() +
                            " - " +
                            tempItem.getAssetTagValue();
                    lastScannedItem = scannedItem;
                    showSnackBar(rootView, "Scanned Item : \n" + msg, tempItem, true);
                }
                new Handler().postDelayed(() -> resumeScanner(), 2000);
            }

            @Override
            public void possibleResultPoints(List<ResultPoint> resultPoints) {

            }
        });         //Barcode Scanner Result Handled here
    }

    private StockTakingScannedItem getExtraScannedItem(String scannedItem) {
        if (Objects.nonNull(extraScannedItems)) {
            for (StockTakingScannedItem item : extraScannedItems) {
                if (scannedItem.equalsIgnoreCase(item.getAssetTagValue())) {
                    return item;
                }
            }
        }
        return null;
    }

    private void dismissSnackBar() {
        if (snackbar != null && snackbar.isShown()) {
            snackbar.dismiss();
        }
    }

    public void showSnackBar(View view, String message, StockTakingScannedItem scannedTempItem, Boolean isCurrentUnitsAsset) {
        snackbar = Snackbar.make(view, message, Snackbar.LENGTH_LONG);
        SpannableString spannableString = new SpannableString("REMOVE");
        spannableString.setSpan(new ForegroundColorSpan(Color.RED), 0, spannableString.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        snackbar.setDuration(10000);
        Handler handler = new Handler(Looper.myLooper());
        handler.postDelayed(() -> {
            if (snackbar.isShown()) {
                snackbar.dismiss();
            }
        }, 10000);
        snackbar.setAction(spannableString, view1 -> {
            if (isCurrentUnitsAsset) {
                processScannedAsset(scannedTempItem.getAssetTagValue(), false);
            } else {
                extraScanMap.remove(scannedTempItem.getAssetTagValue());
                extraScannedItems.remove(scannedTempItem);
            }
            if (snackbar.isShown()) {
                snackbar.dismiss();
            }
            handler.removeCallbacksAndMessages(null);
        });
        snackbar.setAnchorView(R.id.torch);
        snackbar.show();
    }

    public void playMusic(String typeOfMusic) {
        try {
            if (mp != null) {
                if (mp.isPlaying()) {
                    mp.stop();
                    mp.release();
                }
                mp = null;
            }
            if (typeOfMusic.equalsIgnoreCase("SUCCESS")) {
                mp = MediaPlayer.create(StockTakingScanner.this, R.raw.success_scanned);
            } else if (typeOfMusic.equalsIgnoreCase("DUPLICATE")) {
                mp = MediaPlayer.create(StockTakingScanner.this, R.raw.duplicate_scanned);
            } else if (typeOfMusic.equalsIgnoreCase("OTHER_SUB_CATEGORY")) {
                mp = MediaPlayer.create(StockTakingScanner.this, R.raw.other_sub_category_scanned);
            } else {
                mp = MediaPlayer.create(StockTakingScanner.this, R.raw.other_unit_asset_found);
            }
            mp.start();
        } catch (Exception e) {
            System.out.println("Exception Occurred while Playing Music :: " + typeOfMusic);
            e.printStackTrace();
        }
    }

    private void processScannedAsset(String scannedItem, Boolean isScanned){
        Gson gson = new GsonBuilder().create();
        if (isScanned) {
            itemsListData.removeAll(Collections.singletonList(scannedItem));
            itemsListData.add(scannedItem);
//            StockTakingScannedItem stockTakingScannedItem = new StockTakingScannedItem(eventId, unitId, scannedItem, true, eventAuditor);
//            stockTakingEvent.getStockTakingScannedItemsList().add(stockTakingScannedItem);
        } else {
            itemsListData.removeAll(Collections.singletonList(scannedItem));
//            Iterator<StockTakingScannedItem> iterator = stockTakingEvent.getStockTakingScannedItemsList().iterator();
//            while (iterator.hasNext()) {
//                StockTakingScannedItem item = iterator.next();
//                if (item.getAssetTagValue().equalsIgnoreCase(scannedItem)) {
//                    iterator.remove();
//                }
//            }
        }
        StockTakingScannedItem scannedItem1 = assetTagMap.get(scannedItem);
        scannedItem1.setFound(isScanned);
        assetTagMap.put(scannedItem, scannedItem1);
                    if(Objects.nonNull(scannedItem1)){
                        Log.d("scanned :",gson.toJson(scannedItem1));
                        scannedItem1.setFound(isScanned);
                        if(isTransferOut.equals(Boolean.TRUE)){
                            if (Objects.nonNull(stockTakingRequest) && Objects.nonNull(stockTakingRequest.getEventType())) {
                                if (stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT)) {
                                    if (isScanned && Objects.isNull(expandableDetailList.get(getType(scannedItem1)))) {
                                        Toast.makeText(getApplicationContext(), scannedItem1.getAssetName() + " Is Not In Transfer List ",
                                                Toast.LENGTH_SHORT).show();
                                        dbvScanner.resume();
                                        return;
                                    }
                                }
                            }
                            if (stockTakingRequest.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT)) {
                                if (isScanned) {
                                    if (expandableDetailList.containsKey(getType(scannedItem1)) && Objects.nonNull(expandableDetailList.get(getType(scannedItem1)))) {
                                        int index = 0;
                                        for (int i=0;i< expandableDetailList.get(getType(scannedItem1)).size(); i++) {
                                            StockTakingScannedItem item = expandableDetailList.get(getType(scannedItem1)).get(i);
                                            if (Objects.nonNull(currentSelectedFilter)) {
                                                if (currentSelectedFilter.equalsIgnoreCase("CATEGORY")) {
                                                    if (scannedItem1.getSubCategory().equalsIgnoreCase(item.getSubCategory())) {
                                                        index = i;
                                                        break;
                                                    }
                                                } else if (currentSelectedFilter.equalsIgnoreCase("PRODUCT")) {
                                                    if (scannedItem1.getProductName().equalsIgnoreCase(item.getProductName())) {
                                                        index = i;
                                                        break;
                                                    }
                                                } else {
                                                    if (scannedItem1.getSkuName().equalsIgnoreCase(item.getSkuName())) {
                                                        index = i;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        List<StockTakingScannedItem> tempArr = expandableDetailList.get(getType(scannedItem1));
                                        lastScannedTransferOutItem = tempArr.get(index);
                                        tempArr.remove(lastScannedTransferOutItem);
                                        expandableDetailList.put(getType(scannedItem1), tempArr);
                                        if (expandableDetailList.get(getType(scannedItem1)).isEmpty()) {
                                            expandableDetailList.remove(getType(scannedItem1));
                                            expandableTitleList.remove(getType(scannedItem1));
                                            expandableListAdapter.notifyDataSetChanged();
                                        } else {
                                            expandableDetailList.put(getType(scannedItem1), UtilClass.utilClass.sortAssets(expandableDetailList.get(getType(scannedItem1))));
                                        }
                                    }
                                } else {
                                    List<StockTakingScannedItem> tempArr = null;
                                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                                        tempArr = expandableDetailList.getOrDefault(getType(scannedItem1), new ArrayList<>());
                                    }
                                    tempArr.add(Objects.nonNull(lastScannedTransferOutItem) ? lastScannedTransferOutItem : scannedItem1);
                                    expandableDetailList.put(getType(scannedItem1), tempArr);
                                    expandableDetailList.put(getType(scannedItem1), UtilClass.utilClass.sortAssets(expandableDetailList.get(getType(scannedItem1))));
                                    if (!expandableTitleList.contains(getType(scannedItem1))) {
                                        expandableTitleList.add(getType(scannedItem1));
                                    }
                                }
                            } else {
                                if (isScanned) {
                                    if (expandableDetailList.containsKey(getType(scannedItem1))) {
                                        expandableDetailList.get(getType(scannedItem1)).remove(scannedItem1);
                                        if (expandableDetailList.get(getType(scannedItem1)).isEmpty()) {
                                            expandableDetailList.remove(getType(scannedItem1));
                                            expandableTitleList.remove(getType(scannedItem1));
                                            expandableListAdapter.notifyDataSetChanged();
                                        } else {
                                            expandableDetailList.put(getType(scannedItem1), UtilClass.utilClass.sortAssets(expandableDetailList.get(getType(scannedItem1))));
                                        }
                                    }
                                } else {
                                    if (expandableDetailList.containsKey(getType(scannedItem1))) {
                                        expandableDetailList.get(getType(scannedItem1)).add(scannedItem1);
                                    } else {
                                        expandableDetailList.put(getType(scannedItem1), new ArrayList<>());
                                        expandableDetailList.get(getType(scannedItem1)).add(scannedItem1);
                                    }
                                    if (!expandableTitleList.contains(getType(scannedItem1))) {
                                        expandableTitleList.add(getType(scannedItem1));
                                    }
                                    List<StockTakingScannedItem> items = UtilClass.utilClass.sortAssets(expandableDetailList.get(getType(scannedItem1)));
                                    expandableDetailList.put(getType(scannedItem1), items);
                                }
                            }
                        } else{
                            if (isScanned) {
                                if (expandableDetailList.containsKey(getType(scannedItem1))) {
                                    expandableDetailList.get(getType(scannedItem1)).remove(scannedItem1);
                                    if (expandableDetailList.get(getType(scannedItem1)).isEmpty()) {
                                        expandableDetailList.remove(getType(scannedItem1));
                                        expandableTitleList.remove(getType(scannedItem1));
                                        expandableListAdapter.notifyDataSetChanged();
                                    } else {
                                        expandableDetailList.put(getType(scannedItem1), UtilClass.utilClass.sortAssets(expandableDetailList.get(getType(scannedItem1))));
                                    }
                                }
                            } else {
                                if (expandableDetailList.containsKey(getType(scannedItem1))) {
                                    expandableDetailList.get(getType(scannedItem1)).add(scannedItem1);
                                } else {
                                    expandableDetailList.put(getType(scannedItem1), new ArrayList<>());
                                    expandableDetailList.get(getType(scannedItem1)).add(scannedItem1);
                                }
                                if (!expandableTitleList.contains(getType(scannedItem1))) {
                                    expandableTitleList.add(getType(scannedItem1));
                                }
                                List<StockTakingScannedItem> items = UtilClass.utilClass.sortAssets(expandableDetailList.get(getType(scannedItem1)));
                                expandableDetailList.put(getType(scannedItem1), items);
                            }
                        }
                        Toast.makeText(getApplicationContext(), isScanned ? (
                                scannedItem1.getAssetName() + " Is Scanned ( " + scannedItem + ")") : scannedItem1.getAssetName() + " Is Removed ( " + scannedItem + ")",
                                Toast.LENGTH_SHORT).show();
                        expandableListAdapter.setExpandableDetailList(expandableDetailList);

            if(Boolean.TRUE.equals(isTransferOut)){
                if (isScanned) {
                    if (!scannedAssetsMap.containsKey(getType(scannedItem1))) {
                        scannedAssetsMap.put(getType(scannedItem1), new ArrayList<>());
                    }
                    scannedAssetsMap.get(getType(scannedItem1)).add(scannedItem1);
                } else {
                    if (scannedAssetsMap.containsKey(getType(scannedItem1))) {
                        List<StockTakingScannedItem> items = scannedAssetsMap.get(getType(scannedItem1));
                        if (items != null) {
                            if (items.contains(scannedItem1)) {
                                items.remove(scannedItem1);
                            }
                            if (items.isEmpty()) {
                                scannedAssetsMap.remove(getType(scannedItem1));
                            }
                        }
                    }
                }
            }else{
                if (isScanned) {
                    if (!scannedAssetsMap.containsKey(getType(scannedItem1))) {
                        scannedAssetsMap.put(getType(scannedItem1), new ArrayList<>());
                    }
                    scannedAssetsMap.get(getType(scannedItem1)).add(scannedItem1);
                } else {
                    if (scannedAssetsMap.containsKey(getType(scannedItem1))) {
                        List<StockTakingScannedItem> items = scannedAssetsMap.get(getType(scannedItem1));
                        if (items != null) {
                            if (items.contains(scannedItem1)) {
                                items.remove(scannedItem1);
                            }
                            if (items.isEmpty()) {
                                scannedAssetsMap.remove(getType(scannedItem1));
                            }
                        }
                    }
                }
            }


        }

        saveStockTakingEvent();
        /* mAdapter.notifyItemInserted(itemsListData.size() - 1);*/
        if (itemsListData.size() > 0) {
            String scannedItemsJson = gson.toJson(itemsListData);
            SharedPreferences sharedPreferences = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
            SharedPreferences.Editor myEdit = sharedPreferences.edit();
            myEdit.putString("StockTakingPrevListData", scannedItemsJson);
            myEdit.apply();
        }
        if (!isScanned) {
            resumeScanner();
        }
        //nothingScanned.setVisibility(View.INVISIBLE);
    }

    private String getType(StockTakingScannedItem scannedItem1) {
        if (Objects.nonNull(currentSelectedFilter)) {
            if (currentSelectedFilter.equalsIgnoreCase("CATEGORY")) {
                return scannedItem1.getSubCategory();
            } else if (currentSelectedFilter.equalsIgnoreCase("PRODUCT")) {
                return scannedItem1.getProductName();
            } else {
                return scannedItem1.getSkuName();
            }
        }
        return scannedItem1.getSubCategory();
    }


    private void sortPendingAssets(){
        for(String subCategory : expandableDetailList.keySet()){
            List<StockTakingScannedItem> items = expandableDetailList.get(subCategory);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                items.sort(new Comparator<StockTakingScannedItem>() {
                    @Override
                    public int compare(StockTakingScannedItem o1, StockTakingScannedItem o2) {
                        return o1.getAssetName().compareTo(o2.getAssetName()) ;
                    }
                });
            }
            expandableDetailList.put(subCategory,items);
        }
    }

    private void createExtraScannedMap(){
        for(StockTakingScannedItem item : extraScannedItems){
            extraScanMap.put(item.getAssetTagValue(),true);
        }
    }

    private void createScannedAssetIdMap(){
        assetScannedMap = new HashMap<>();
        for(String type : scannedAssetsMap.keySet()){
            for(StockTakingScannedItem scannedItem : scannedAssetsMap.get(type)){
                assetScannedMap.put(scannedItem.getAssetTagValue(),true);
            }
        }
    }

    private void showAssetDetails(String scannedItem) {
        dbvScanner.pause();
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .client(httpClient)
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sharedPreferences.getString("jwtToken", null);

        Call<Object> assetLookupCall = unitsApiService.getAssetDetails(token, scannedItem);
        assetLookupCall.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                dbvScanner.resume();
                if(response.body()== null){
//                    Toast.makeText(getApplicationContext(),"Invalid QR Code",Toast.LENGTH_SHORT).show();
                    saveInvalidTag(token, stockTakingEvent.getEventId(), scannedItem);
                    return;
                }
                Toast.makeText(getApplicationContext(),"Scanned Asset Is In Another Unit Inventory!!",Toast.LENGTH_SHORT).show();

                extraScanMap.put(scannedItem,true);
                String asset = new Gson().toJson(response.body());
                JsonObject jsonObject = new Gson().fromJson(asset, JsonObject.class);

                String assetNameValue = jsonObject.get("assetName").toString();
                String assetIdValue = jsonObject.get("assetId").toString();
                String assetUnitIdValue = jsonObject.get("unitId").toString();
                String assetUnitName = jsonObject.get("unitName").toString();



                assetNameValue = assetNameValue.substring(1, assetNameValue.length()-1);
                assetIdValue = assetIdValue.substring(0, assetIdValue.length()-2);
                assetUnitIdValue = assetUnitIdValue.substring(0, assetUnitIdValue.length()-2);
                assetUnitName = assetUnitName.substring(1, assetUnitName.length()-1);


                StockTakingScannedItem stockTakingScannedItem = new StockTakingScannedItem();
                stockTakingScannedItem.setUnitName(assetUnitName);
                stockTakingScannedItem.setAssetId(jsonObject.get("assetId").getAsInt());
                stockTakingScannedItem.setUnitId(jsonObject.get("unitId").getAsInt());
                stockTakingScannedItem.setAssetName(assetNameValue);
                stockTakingScannedItem.setAssetTagValue(scannedItem);
                extraScannedItems.add(stockTakingScannedItem);

                String assetValue = assetNameValue + " ( " + assetIdValue + " )";

                String unitNameValue = jsonObject.get("unitName").toString();
                unitNameValue = unitNameValue.substring(1,unitNameValue.length()-1);

                String assetUnitValue = unitNameValue + " ( " + assetUnitIdValue + " ) ";

                playMusic("OTHER_UNIT");
                View rootView = findViewById(android.R.id.content);
                String msg = stockTakingScannedItem.getAssetName() +
                        " - " +
                        stockTakingScannedItem.getAssetTagValue();
                lastScannedItem = scannedItem;
                showSnackBar(rootView, "Scanned Item : \n" + msg, stockTakingScannedItem, false);
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Toast.makeText(getApplicationContext(),"Server Error",Toast.LENGTH_SHORT).show();
                return;
            }
        });
    }


    private HashMap<String,List<StockTakingScannedItem>> removeDuplicate(HashMap<String,List<StockTakingScannedItem>> scannedAssetsMap){
        HashMap<String,List<StockTakingScannedItem>> finalScannedAssets = new HashMap<>();
        for(String subCategory : scannedAssetsMap.keySet()){
            Map<Integer,Boolean> tempMap = new HashMap<>();
            List<StockTakingScannedItem> finalItems = new ArrayList<>();
            for(StockTakingScannedItem item : scannedAssetsMap.get(subCategory)){
                if(tempMap.containsKey(item.getAssetId())){
                    continue;
                }
                finalItems.add(item);
                tempMap.put(item.getAssetId(),true);
            }
            finalScannedAssets.put(subCategory,finalItems);
        }
        return finalScannedAssets;

    }

    private void saveInvalidTag(String token, Integer eventId, String tagValue){
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<Object> saveInvalidTagCall = unitsApiService.saveInvalidTag(token, eventId, tagValue);
        saveInvalidTagCall.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if (!response.isSuccessful()) {
                    if (Objects.nonNull(response.errorBody())) {
                        try {
                            String errorBodyString = response.errorBody().string();
                            SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                            if (Objects.nonNull(scmError) && Objects.nonNull(scmError.getErrorMessage())) {
                                Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                            }
                        } catch (Exception e) {
                            Toast.makeText(getApplicationContext(), "Submit API Response is Not Successful", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Toast.makeText(getApplicationContext(), "Could Not Save Invalid Tag", Toast.LENGTH_SHORT).show();
                    }
                    return;
                }
                return;
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Log.d("Invalid Tag Not Saved:",t.toString());
                return;
            }
        });
    }

    private HashMap<String,List<StockTakingScannedItem>> getInitializedList(){
        HashMap<String,List<StockTakingScannedItem>> result = new HashMap<>();
        Set<String> filterTypeList =  expandableDetailList.keySet();
        for(String  type : filterTypeList){
            result.put(type,new ArrayList<>());
        }
        return  result;
    }

    private void  initialize() {
        dbvScanner = findViewById(R.id.dbv_barcode);
        itemsList = findViewById(R.id.items_recycler_view);
        Button cancelBtn = findViewById(R.id.cancel_btn);
        Button submitBtn = findViewById(R.id.submit_btn);
        Button pauseBtn = findViewById(R.id.pause_btn);
        Button torchBtn = findViewById(R.id.torch);
        CardView itemList = findViewById(R.id.expandedListItem);
        //nothingScanned = findViewById(R.id.nothing_scanned_tv);

        cancelBtn.setOnClickListener(this);
        submitBtn.setOnClickListener(this);
        pauseBtn.setOnClickListener(this);
        torchBtn.setOnClickListener(this);


        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);


        itemsListData = new ArrayList<>();
        ArrayList<String> object = null;
        eventAuditor = null;
        Intent intent = getIntent();
        SharedPreferences initiatedEvents = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
        String list = initiatedEvents.getString("StockTakingPrevListData", null);

        if (Objects.nonNull(list)) {
            ArrayList javaArrayListFromGSON = new Gson().fromJson(list, ArrayList.class);
            //String objectString = (String) prefs.get("initializedEventsList");
            object = javaArrayListFromGSON;
        }
        if (intent.hasExtra("eventId")) {
            eventId = intent.getIntExtra("eventId", -1);
        }
        if(intent.hasExtra("auditedByName") && intent.hasExtra("auditedById")){
            String auditorName = intent.getStringExtra("auditedByName");
            Integer auditorId = intent.getIntExtra("auditedById",-1);
            eventAuditor = new User(auditorId,auditorName);
        }
        if (object != null) {
            itemsListData = object;
            //nothingScanned.setVisibility(View.INVISIBLE);
        }


        SharedPreferences sh = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        unitId = sh.getInt("unitId", -1);
        Log.d("unitId", String.valueOf(unitId));

        SharedPreferences sharedPref = getSharedPreferences("SavedStockTakingEventPREF", MODE_PRIVATE);
        Gson gson = new Gson();
        String json = sharedPref.getString("SavedStockTakingEvent", null);

        if (json != null && object!= null) {
            //stockTakingEvent = gson.fromJson(json, StockTakingEvent.class);
            try{
                json = object.get(0);
                stockTakingEvent = gson.fromJson(json, StockTakingEvent.class);
            }catch (Exception e){
                stockTakingEvent = createNewStockTakingEventObject();
            }
           // stockTakingEvent.setEventId(eventId);
        } else {
            stockTakingEvent = createNewStockTakingEventObject();
        }

    }

    private StockTakingEvent createNewStockTakingEventObject() {
        SharedPreferences sh = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Integer unitId = sh.getInt("unitId", -1);
        return new StockTakingEvent(eventId, unitId, EventAssetMappingStatus.VERIFY, new ArrayList<StockTakingScannedItem>());
    }

    public AlertDialog.Builder getDialogProgressBar() {

        if (builder == null) {
            builder = new AlertDialog.Builder(this);

            builder.setTitle("Please Wait...");

            final ProgressBar progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(true);
            builder.setView(progressBar);
        }
        return builder;
    }       // AlertDialog

    @Override
    protected void onResume() {
        super.onResume();
        setDeviceInfo();
        resumeScanner();
    }

    protected void resumeScanner() {
        if (!dbvScanner.isActivated())
            dbvScanner.resume();

    }

    protected void pauseScanner() {
        //Toast.makeText(getApplicationContext(),itemsListData.toString(),Toast.LENGTH_SHORT).show();
        dbvScanner.pause();
    }

    @Override
    protected void onPause() {
        super.onPause();
        pauseScanner();
    }

    void requestPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, 0);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.cancel_btn:
                showAlert();
                break;
            case R.id.submit_btn:
                /*if (itemsListData.size() == 0) {
                    Snackbar.make(v, "Please scan something to submit!", Snackbar.LENGTH_SHORT).show();
                    break;
                }*/
                if(!isConnected()){
                    Snackbar.make(v, "No internet connection!", Snackbar.LENGTH_SHORT).show();
                    break;
                }
                alertDialog.show();
                submitEvent(true);
                break;
            case R.id.pause_btn:
                alertDialog.show();
                if (Objects.nonNull(isTransferOut) && isTransferOut) {
                    onBackPressed();
                } else {
                    submitEvent(false);
                }
                break;
            case R.id.torch:
                Log.d("button","clicked");
                if(Boolean.TRUE.equals(torchOn)){
                    dbvScanner.setTorchOff();
                    torchOn = false;
                }else{
                    dbvScanner.setTorchOn();
                    torchOn = true;
                }
                break;
            default:
                break;
        }
    }

    private void submitEvent(boolean isSubmit) {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .client(httpClient)
                .build();
        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sharedPreferences.getString("jwtToken", null);


        /*StockTakingEvent stockTakingEvent = new S*/
        Log.d("jwt",token);
        if(Boolean.TRUE.equals(isTransferOut)){
            List<StockTakingScannedItem> scannedAssets = new ArrayList<>();
            for(List<StockTakingScannedItem> items : scannedAssetsMap.values()){
                scannedAssets.addAll(items);
            }
            stockTakingEvent.setStockTakingScannedItemsList(scannedAssets);
        }else{
            stockTakingEvent.setStockTakingScannedItemsList(stockTakingRequest.getAvailableAssets());
        }
        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();
        if(!Boolean.TRUE.equals(isTransferOut)) {
            if (prefs.containsKey("eventResponse")) {
                try {
                    StockTakingRequest stockTakingRequest = UtilClass.gson.fromJson((String) prefs.get("eventResponse"), StockTakingRequest.class);
                    if (Objects.nonNull(stockTakingRequest)) {
                        stockTakingEvent.setParentId(stockTakingRequest.getParentId());
                        stockTakingEvent.setSubCategory(stockTakingRequest.getSubCategory());
                    }
                } catch (Exception e) {
                    Toast.makeText(getApplicationContext(), "Exception Occurred while Setting Parent Id", Toast.LENGTH_SHORT).show();
                }
            }
        }
        stockTakingEvent.setEventId(stockTakingRequest.getEventId());
        if(Objects.nonNull(extraScannedItems) && !extraScannedItems.isEmpty()){
            stockTakingEvent.setExtraScannedItems(extraScannedItems);
        }
        Log.d("id :", String.valueOf(stockTakingEvent.getEventId()));
        //Call<StockTakingEvent> verifyAndCreationCall = unitsApiService.verifyAndCreateEventAssetMapping(token, stockTakingEvent);
        stockTakingEvent.setDeviceInfo(Objects.isNull(UtilClass.getDeviceInfo()) ? setDeviceInfo() : UtilClass.getDeviceInfo());
        StockTakingEvent requestCall;
        if (!isSubmit) {
            requestCall = new StockTakingEvent();
            requestCall.setEventId(stockTakingEvent.getEventId());
            requestCall.setSubCategory(stockTakingEvent.getSubCategory());
            requestCall.setDeviceInfo(Objects.isNull(UtilClass.getDeviceInfo()) ? setDeviceInfo() : UtilClass.getDeviceInfo());
            Map<Integer, Boolean> assetFoundMap = new HashMap<>();
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                assetFoundMap = stockTakingEvent.getStockTakingScannedItemsList().stream().collect(Collectors.toMap(StockTakingScannedItem::getAssetId,
                        StockTakingScannedItem::isFound, (a1, a2) -> {
                            return a1;
                        }));
            } else {
                AlertDialog alertDialog = UtilClass.utilClass.getAlertDialog(StockTakingScanner.this, "Device Not Supported..!", "Your Device Is Not Supported To Use Stock Take App..!");
                alertDialog.show();
                return;
            }
            requestCall.setExtraScannedItems(stockTakingEvent.getExtraScannedItems());
            requestCall.setParentId(stockTakingEvent.getParentId());
            requestCall.setAssetFoundMap(assetFoundMap);
        } else {
            requestCall = stockTakingEvent;
        }
        String json = new Gson().toJson(requestCall);
        Log.d("payload",json);
        Call<Boolean> pauseCall = unitsApiService.pauseSubmitStockEvent(token, requestCall,false);
        pauseCall.enqueue(new Callback<Boolean>() {
            @Override
            public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                if (!response.isSuccessful()) {
                    if (Objects.nonNull(response.errorBody())) {
                        try {
                            String errorBodyString = response.errorBody().string();
                            SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                            if (Objects.nonNull(scmError) && Objects.nonNull(scmError.getErrorMessage())) {
                                Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                                if (scmError.getErrorMessage().startsWith("DEVICE_INFO")) {
                                    String[] deviceInfo = scmError.getErrorMessage().split("#");
                                    AlertDialog.Builder builder = new AlertDialog.Builder(StockTakingScanner.this);
                                    builder.setTitle(deviceInfo[0])
                                            .setMessage("Event is Going on in Some Other Device : " + deviceInfo[1] + " - " + deviceInfo[2])
                                            .setPositiveButton("OK", (dialog, which) -> {
                                                Intent intent = new Intent(StockTakingScanner.this, StockTaking.class);
                                                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                                                startActivity(intent);
                                                finish();
                                                dialog.cancel();
                                            });

                                    AlertDialog dialog = builder.create();
                                    dialog.show();
                                }
                            }
                        } catch (Exception e) {
                            Toast.makeText(getApplicationContext(), "API Response is Not Successful while Saving..!", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Toast.makeText(getApplicationContext(), "Error Occurred while Saving..!", Toast.LENGTH_SHORT).show();
                    }
                    alertDialog.dismiss();
                    return;
                }
                if (response.body() == null) {
                    Toast.makeText(StockTakingScanner.this, "Null response on Event Verification", Toast.LENGTH_SHORT).show();
                    return;
                }
                if(response.errorBody()!=null){
                    Toast.makeText(StockTakingScanner.this, "Error body!", Toast.LENGTH_SHORT).show();
                    return;
                }
                SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
                SharedPreferences.Editor edt = pref.edit();
                Gson gson = new Gson();
                String assetsJson = gson.toJson(stockTakingRequest.getAvailableAssets());
                edt.putString("AssetList",assetsJson);
                edt.apply();

                stockTakingEventResponse = stockTakingEvent;
                stockTakingEventResponse.setStockTakingScannedItemsList(stockTakingRequest.getAvailableAssets());
                if(isTransferOut.equals(Boolean.FALSE)){
                    //Intent intent = new Intent(StockTakingScanner.this, StockTakingEventSummary.class);
                    Gson googleJson = new Gson();

                    String pendingAssetsJson = googleJson.toJson(expandableDetailList);
                    String scannedAssetsJson = googleJson.toJson(scannedAssetsMap);

                    edt.putString("pendingAssetsJson",pendingAssetsJson);
                    edt.putString("scannedAssets",scannedAssetsJson);
                    //This might cause a crash
                    stockTakingEventResponse.setSubType(stockTakingEvent.getSubType());
                    String stockTakingEventResponseJSON = googleJson.toJson(stockTakingEventResponse, StockTakingEvent.class);
                    String extraScannedItemsJson = googleJson.toJson(extraScannedItems);
                    edt.putString("StockTakingEventResponse",stockTakingEventResponseJSON);

                    edt.apply();
                    alertDialog.dismiss();
                    if (isSubmit) {
                        Intent intent = new Intent(StockTakingScanner.this, ExtraScannedScreen.class);
                        intent.putExtra("extraScannedItems",extraScannedItemsJson);
                        startActivity(intent);
                    } else  {
                        UtilClass.utilClass.getToast(getApplicationContext(), "Saved Successfully..!").show();
                    }
                }else{
                    alertDialog.dismiss();
                    finish();
                }
            }


            @Override
            public void onFailure(Call<Boolean> call, Throwable t) {
                Toast.makeText(StockTakingScanner.this, "Some error occurred", Toast.LENGTH_SHORT).show();
                alertDialog.dismiss();
            }
        });
    }

    private boolean isConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        return connectivityManager.getActiveNetworkInfo() != null && connectivityManager.getActiveNetworkInfo().isConnected();
    }

    private void showAlert() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(StockTakingScanner.this);
        alertDialog.setTitle("Abandon Event");
        alertDialog.setMessage("Are you sure you want to abandon this Stock Taking Event?");
        alertDialog.setPositiveButton("CANCEL", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.cancel();
            }
        });
        alertDialog.setNegativeButton("YES", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                setResult(2);
                dialog.cancel();
                finish();
            }
        });
        alertDialog.show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        onBackPressed();
        return true;
    }

    @Override
    public void onBackPressed() {
        alertDialog.show();
        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        SharedPreferences.Editor edt = pref.edit();
        Gson gson = new Gson();
        String assetsJson = gson.toJson(stockTakingRequest.getAvailableAssets());
        edt.putString("AssetList",assetsJson);
        edt.apply();
        if(Boolean.TRUE.equals(isTransferOut)) {
            submitEvent(true);
        } else {
            pauseEvent();
        }
    }

    private void pauseEvent(){
        alertDialog.show();
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .client(httpClient)
                .build();
        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sharedPreferences.getString("jwtToken", null);
        stockTakingEvent.setEventId(stockTakingRequest.getEventId());
        stockTakingEvent.setStockTakingScannedItemsList(stockTakingRequest.getAvailableAssets());
        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();
        if (prefs.containsKey("eventResponse")) {
            try {
                StockTakingRequest stockTakingRequest = UtilClass.gson.fromJson((String) prefs.get("eventResponse"), StockTakingRequest.class);
                if (Objects.nonNull(stockTakingRequest)) {
                    stockTakingEvent.setParentId(stockTakingRequest.getParentId());
                    stockTakingEvent.setSubCategory(stockTakingRequest.getSubCategory());
                }
            } catch (Exception e) {
                Toast.makeText(getApplicationContext(), "Exception Occurred while Setting Parent Id", Toast.LENGTH_SHORT).show();
            }
        }
        stockTakingEvent.setDeviceInfo(Objects.isNull(UtilClass.getDeviceInfo()) ? setDeviceInfo() : UtilClass.getDeviceInfo());
        //Call<StockTakingEvent> verifyAndCreationCall = unitsApiService.verifyAndCreateEventAssetMapping(token, stockTakingEvent);
        StockTakingEvent requestCall = new StockTakingEvent();
        requestCall.setEventId(stockTakingEvent.getEventId());
        requestCall.setSubCategory(stockTakingEvent.getSubCategory());
        requestCall.setDeviceInfo(Objects.isNull(UtilClass.getDeviceInfo()) ? setDeviceInfo() : UtilClass.getDeviceInfo());
        Map<Integer, Boolean> assetFoundMap = new HashMap<>();
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            assetFoundMap = stockTakingEvent.getStockTakingScannedItemsList().stream().collect(Collectors.toMap(StockTakingScannedItem::getAssetId,
                    StockTakingScannedItem::isFound, (a1, a2) -> {
                        return a1;
                    }));
        } else {
            AlertDialog alertDialog = UtilClass.utilClass.getAlertDialog(StockTakingScanner.this, "Device Not Supported..!", "Your Device Is Not Supported To Use Stock Take App..!");
            alertDialog.show();
            return;
        }
        requestCall.setExtraScannedItems(stockTakingEvent.getExtraScannedItems());
        requestCall.setParentId(stockTakingEvent.getParentId());
        requestCall.setAssetFoundMap(assetFoundMap);
        Call<Boolean> pauseCall = unitsApiService.pauseSubmitStockEvent(token, requestCall,false);
        pauseCall.enqueue(new Callback<Boolean>() {
            @Override
            public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                if (!response.isSuccessful()) {
                    if (Objects.nonNull(response.errorBody())) {
                        try {
                            String errorBodyString = response.errorBody().string();
                            SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                            if (Objects.nonNull(scmError) && Objects.nonNull(scmError.getErrorMessage())) {
                                Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                                if (scmError.getErrorMessage().startsWith("DEVICE_INFO")) {
                                    String[] deviceInfo = scmError.getErrorMessage().split("#");
                                    AlertDialog.Builder builder = new AlertDialog.Builder(StockTakingScanner.this);
                                    builder.setTitle(deviceInfo[0])
                                            .setMessage("Event is Going on in Some Other Device : " + deviceInfo[1] + " - " + deviceInfo[2])
                                            .setPositiveButton("OK", (dialog, which) -> {
                                                Intent intent = new Intent(StockTakingScanner.this, StockTaking.class);
                                                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                                                startActivity(intent);
                                                finish();
                                                dialog.cancel();
                                            });

                                    AlertDialog dialog = builder.create();
                                    dialog.show();
                                }
                            }
                        } catch (Exception e) {
                            Toast.makeText(getApplicationContext(), "API Response is Not Successful while Saving..!", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Toast.makeText(getApplicationContext(), "Error Occurred while Saving..!", Toast.LENGTH_SHORT).show();
                    }
                    alertDialog.dismiss();
                    return;
                }
                if (response.body() == null) {
                    Toast.makeText(StockTakingScanner.this, "Null response on Pause Event..!", Toast.LENGTH_SHORT).show();
                    return;
                }
                if(response.errorBody()!=null){
                    Toast.makeText(StockTakingScanner.this, "Error body on Pause Event!", Toast.LENGTH_SHORT).show();
                    return;
                }
                SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
                SharedPreferences.Editor edt = pref.edit();
                Gson gson = new Gson();
                String assetListJson = gson.toJson(stockTakingRequest.getAvailableAssets());
                edt.putString("AssetList",assetListJson);
                edt.apply();

                Gson googleJson = new Gson();

                String pendingAssetsJson = googleJson.toJson(expandableDetailList);
                String scannedAssetsJson = googleJson.toJson(scannedAssetsMap);

                edt.putString("pendingAssetsJson",pendingAssetsJson);
                edt.putString("scannedAssets",scannedAssetsJson);
                edt.apply();
                alertDialog.dismiss();
                finish();
            }

            @Override
            public void onFailure(Call<Boolean> call, Throwable t) {
                Toast.makeText(StockTakingScanner.this, "Some error occurred", Toast.LENGTH_SHORT).show();
                alertDialog.dismiss();
            }
        });
    }

    private void saveStockTakingEvent(){
        SharedPreferences sharedPref = getSharedPreferences("SavedStockTakingEventPREF", MODE_PRIVATE);
        SharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
        Gson gson = new Gson();
        String json = gson.toJson(stockTakingEvent);
        sharedPrefEditor.putString("SavedStockTakingEvent", json);
        sharedPrefEditor.apply();
        resumeScanner();
    }

    @Override
    public boolean onSupportNavigateUp() {
//        setResult(3);
        finish();
        return true;
    }

    @Override
    public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
        expandableListView = (ExpandableListView) findViewById(R.id.expandableListView);
        currentSelectedFilter = dropDownListFilter.get(i);
        expandableDetailList = getFinalExpandableList();
        expandableTitleList = new ArrayList<String>(expandableDetailList.keySet());
        expandableListAdapter = new CustomizedExpandableListAdapter(this, expandableTitleList, expandableDetailList,false, false, hideAssetTag);
        expandableListView.setAdapter(expandableListAdapter);
    }

    private HashMap<String, List<StockTakingScannedItem>> getFinalExpandableList() {
        HashMap<String, List<StockTakingScannedItem>> result = new HashMap<>();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            expandableDetailList.forEach((key, stockTakingScannedItems) -> {
                    stockTakingScannedItems.forEach(scannedItem -> {
                        if (result.containsKey(getType(scannedItem))) {
                            result.get(getType(scannedItem)).add(scannedItem);
                        } else {
                            List<StockTakingScannedItem> items = new ArrayList<>();
                            items.add(scannedItem);
                            result.put(getType(scannedItem), items);
                        }
                    });
            });
        }
        return result;
    }

    @Override
    public void onNothingSelected(AdapterView<?> adapterView) {

    }

    private String setDeviceInfo() {
        String buildIdentityAndroid;
        try {
            String androidID = "";
            androidID = Settings.Secure.getString(this.getContentResolver(), Settings.Secure.ANDROID_ID);
            buildIdentityAndroid = Build.MANUFACTURER + "#" + Build.MODEL + "#" + androidID;
            Log.i("BUILD_INFO", buildIdentityAndroid);
        } catch (Exception e) {
            buildIdentityAndroid = Build.MANUFACTURER + "#" + Build.MODEL;
            Log.i("BUILD_INFO", buildIdentityAndroid);
        }
        UtilClass.setDeviceInfo(buildIdentityAndroid);
        return UtilClass.getDeviceInfo();
    }
}
