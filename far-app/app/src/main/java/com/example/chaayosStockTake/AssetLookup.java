package com.example.chaayosStockTake;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TableLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.example.chaayosStockTake.Adapters.LostFoundScannedListViewAdapter;
import com.example.chaayosStockTake.Exceptions.SCMError;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;


import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class AssetLookup extends AppCompatActivity implements View.OnClickListener {
    private TextView assetUnitId;
    private TextView assetId;
    private TextView assetName;
    private ListView attributesListView;
    private DecoratedBarcodeView decoratedBarcodeViewScanner;
    private Button scanButton;
    private TextView scanSomethingMessage;
    private ConstraintLayout assetDetailsConstraintLayout;

    private RecyclerView lostFoundScannedRecyclerView;

    private LostFoundScannedListViewAdapter lostFoundScannedListViewAdapter;
    private TextView qrTagValue;

    private boolean isLostFound;

    private Integer lostFoundEventId;

    private AlertDialog progressDialog;

    private Set<String> lostFoundAssets = new HashSet<>();

    private List<User> lostFoundAssetsList = new ArrayList<>();

    private TableLayout tableLayout;


    @SuppressLint("SetTextI18n")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_asset_lookup);
        requestPermission();

        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Asset Lookup");

        assetId = findViewById(R.id.asset_id_tv);
        assetName = findViewById(R.id.asset_name_tv);
        assetUnitId = findViewById(R.id.unit_id_tv);
        attributesListView = findViewById(R.id.attributes_list);
        scanSomethingMessage = findViewById(R.id.scan_something_message);
        scanButton = findViewById(R.id.scan_btn);
        assetDetailsConstraintLayout = findViewById(R.id.asset_details_constraint_layout);
        qrTagValue = findViewById(R.id.qr_tag_value);
        scanButton.setOnClickListener(this);
        progressDialog = UtilClass.utilClass.getDialogProgressBar(this).create();
        progressDialog.setCancelable(false);
        tableLayout = findViewById(R.id.lost_found_table);
        Intent i = getIntent();
        if (i.hasExtra("FOUND_ASSET") && i.getBooleanExtra("FOUND_ASSET", false)) {
            if (i.hasExtra("LOST_FOUND_ASSETS")) {
                Type type = new TypeToken<List<User>>(){}.getType();
                lostFoundAssetsList = UtilClass.gson.fromJson(i.getStringExtra("LOST_FOUND_ASSETS"), type);
            }
            lostFoundScannedRecyclerView = findViewById(R.id.lost_found_scanned_recycler_view);
            lostFoundScannedRecyclerView.setLayoutManager(new LinearLayoutManager(this));
            lostFoundScannedListViewAdapter = new LostFoundScannedListViewAdapter(lostFoundAssetsList);
            lostFoundScannedRecyclerView.setAdapter(lostFoundScannedListViewAdapter);
            isLostFound = true;
            assetDetailsConstraintLayout.setVisibility(View.INVISIBLE);
            lostFoundEventId = i.getIntExtra("EVENT_ID", 0);
            getSupportActionBar().setTitle("Found Lost Asset");
            scanButton.setText("SUBMIT");
            if (!lostFoundAssetsList.isEmpty()) {
                tableLayout.setVisibility(View.VISIBLE);
                lostFoundScannedRecyclerView.setVisibility(View.VISIBLE);
                for (User user : lostFoundAssetsList) {
                    lostFoundAssets.add(user.getCode());
                }
             }
        } else {
            tableLayout.setVisibility(View.INVISIBLE);
        }

        decoratedBarcodeViewScanner = findViewById(R.id.dbv_barcode_scanner);
        decoratedBarcodeViewScanner.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                String scannedItem = result.getText();
                qrTagValue.setText(scannedItem);
                pauseScanner();
                if (isLostFound) {
                    if (!lostFoundAssets.contains(scannedItem)) {
                        if (lostFoundAssetsList.size() == 5) {
                            AlertDialog alertDialog = UtilClass.utilClass.getAlertDialog(AssetLookup.this, "Exceeded Limit..!",
                                    "Only 5 Assets are Allowed in a Single Event..!");
                            alertDialog.show();
                        } else {
                            markLostFoundAssetScanned(scannedItem);
                        }
                    } else {
                        Toast.makeText(getApplicationContext(), "Asset Tag Already Scanned : " + scannedItem, Toast.LENGTH_SHORT).show();
                    }
                    new Handler().postDelayed(() -> resumeScanner(), 2000);
                } else {
                    showAssetDetails(scannedItem);
                }
            }

            @Override
            public void possibleResultPoints(List<ResultPoint> resultPoints) {

            }
        });
        if (isLostFound) {
            resumeScanner();
        }

    }

    private void markLostFoundAssetScanned(String qrTagValue) {
            progressDialog.show();
            Retrofit retrofit = UtilClass.getRetrofitScm();
            SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            String token = pref.getString("jwtToken", null);

            UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
            Call<Object> appVersionCall = unitsApiService.getAndSaveLostAssetData(token, qrTagValue, lostFoundEventId);
            appVersionCall.enqueue(new Callback<Object>() {
                @Override
                public void onResponse(Call<Object> call, Response<Object> response) {
                    if (!response.isSuccessful()) {
                        if (Objects.nonNull(response.errorBody())) {
                            try {
                                String errorBodyString = response.errorBody().string();
                                SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                                if (Objects.nonNull(scmError) && (Objects.nonNull(scmError.getErrorMessage())  || Objects.nonNull(scmError.getErrorMsg()))) {
                                    Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                                    AlertDialog alertDialog = UtilClass.utilClass.getAlertDialog(AssetLookup.this, "FOUND_LOST_ASSET_ERROR",
                                            Objects.nonNull(scmError.getErrorMessage()) ? scmError.getErrorMessage() : scmError.getErrorMsg());
                                    alertDialog.show();
                                    progressDialog.dismiss();
                                    return;
                                }
                            } catch (Exception e) {
                                Toast.makeText(getApplicationContext(), "API Response is Not Successful while Saving..!", Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            Toast.makeText(getApplicationContext(), "Error Occurred while Saving..!", Toast.LENGTH_SHORT).show();
                        }
                        progressDialog.dismiss();
                        return;
                    }
                    if (response.body() == null) {
                        Toast.makeText(AssetLookup.this, "Null response on Lost Found Asset Call", Toast.LENGTH_SHORT).show();
                        progressDialog.dismiss();
                        return;
                    }
                    String asset = new Gson().toJson(response.body());
                    JsonObject jsonObject = new Gson().fromJson(asset, JsonObject.class);

                    String assetNameValue = jsonObject.get("assetName").toString().substring(1);
                    String assetIdValue = jsonObject.get("assetId").toString();

                    User idCodeName = new User((int) Double.parseDouble(assetIdValue), qrTagValue, assetNameValue);
                    lostFoundAssets.add(qrTagValue);
                    lostFoundAssetsList.add(idCodeName);
                    lostFoundScannedRecyclerView.setVisibility(View.VISIBLE);
                    lostFoundScannedListViewAdapter = new LostFoundScannedListViewAdapter(lostFoundAssetsList);
                    lostFoundScannedRecyclerView.setAdapter(lostFoundScannedListViewAdapter);
                    tableLayout.setVisibility(View.VISIBLE);
                    progressDialog.dismiss();
                }

                @Override
                public void onFailure(Call<Object> call, Throwable t) {
                    progressDialog.dismiss();
                    Toast.makeText(AssetLookup.this, " on Failure  Lost Found Asset Call..!", Toast.LENGTH_SHORT).show();
                }
            });
    }

    private void showAssetDetails(String scannedItem) {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sharedPreferences.getString("jwtToken", null);

        Call<Object> assetLookupCall = unitsApiService.getAssetDetails(token, scannedItem);
        assetLookupCall.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if(response.body()== null){
                    decoratedBarcodeViewScanner.pause();
                    Toast.makeText(getApplicationContext(),"Invalid QR Code",Toast.LENGTH_SHORT).show();
                    return;
                }
                scanSomethingMessage.setVisibility(View.GONE);
                assetDetailsConstraintLayout.setVisibility(View.VISIBLE);

                String asset = new Gson().toJson(response.body());
                JsonObject jsonObject = new Gson().fromJson(asset, JsonObject.class);

                String assetNameValue = jsonObject.get("assetName").toString();
                String assetIdValue = jsonObject.get("assetId").toString();
                String assetUnitIdValue = jsonObject.get("unitId").toString();


                assetNameValue = assetNameValue.substring(1, assetNameValue.length()-1);
                assetIdValue = assetIdValue.substring(0, assetIdValue.length()-2);
                assetUnitIdValue = assetUnitIdValue.substring(0, assetUnitIdValue.length()-2);

                String assetValue = assetNameValue + " ( " + assetIdValue + " )";

                String unitNameValue = jsonObject.get("unitName").toString();
                unitNameValue = unitNameValue.substring(1,unitNameValue.length()-1);

                String assetUnitValue = unitNameValue + " ( " + assetUnitIdValue + " ) ";

                assetName.setText(assetNameValue);
                assetId.setText(assetValue);
                assetUnitId.setText(assetUnitValue);
                JsonArray attributes = jsonObject.getAsJsonArray("entityAttributeValueMappings");
                decoratedBarcodeViewScanner.pause();
                if(attributes!=null)
                    setAttributesListView(attributes);
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Toast.makeText(getApplicationContext(),"Server Error",Toast.LENGTH_SHORT).show();
                return;
            }
        });
    }

    private void setAttributesListView(JsonArray attributes) {
        ArrayList<String> attributeIdList = new ArrayList<>();
        ArrayList<String> attributeNameList = new ArrayList<>();
        for(int i=0;i<attributes.size();i++){
            JsonObject attr = attributes.get(i).getAsJsonObject();
            String attrName = attr.get("attributeName").toString();
            String attrId = attr.get("attributeId").toString();
            attrId = attrId.substring(0,attrId.length()-2);
            attrName = attrName.substring(1,attrName.length()-1);
            attributeIdList.add(attrId);
            attributeNameList.add(attrName);
        }

        AttributesListAdapter adapter1 = new AttributesListAdapter(this,attributeIdList,attributeNameList);
        attributesListView.setAdapter(adapter1);
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    protected void resumeScanner() {
        if (!decoratedBarcodeViewScanner.isActivated())
            decoratedBarcodeViewScanner.resume();
    }

    protected void pauseScanner() {
        //Toast.makeText(getApplicationContext(),itemsListData.toString(),Toast.LENGTH_SHORT).show();
        decoratedBarcodeViewScanner.pause();
    }

    void requestPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, 0);
        }
    }


    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.scan_btn){
            if (!isLostFound) {
                if (!decoratedBarcodeViewScanner.isActivated()) {
                    decoratedBarcodeViewScanner.resume();
                    scanSomethingMessage.setVisibility(View.VISIBLE);
                    assetDetailsConstraintLayout.setVisibility(View.GONE);
                    scanButton.setText("Scan Again");
                }
            } else {
                if (!lostFoundAssetsList.isEmpty()) {
                    AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
                    alertDialog.setTitle("Are You Sure. You Want to Submit?");
                    alertDialog.setMessage("This Event Will Be Submitted On CLicking Yes");
                    alertDialog.setNegativeButton("NO", (dialog, which) -> {
                        dialog.cancel();
                    });
                    alertDialog.setPositiveButton("Yes", (dialog, which) -> {
                        dialog.cancel();
                        submitLostFoundAsset();
                    });
                    alertDialog.show();
                } else {
                    UtilClass.utilClass.getToast(getApplicationContext(), "Please Scan At least 1 Asset To Submit").show();
                }
            }
        }
    }

    private void submitLostFoundAsset() {
        progressDialog.show();
        Retrofit retrofit = UtilClass.getRetrofitScm();
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<Boolean> appVersionCall = unitsApiService.submitLostFoundAsset(token, lostFoundEventId);
        appVersionCall.enqueue(new Callback<Boolean>() {
            @Override
            public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                if (!response.isSuccessful()) {
                    if (Objects.nonNull(response.errorBody())) {
                        try {
                            String errorBodyString = response.errorBody().string();
                            SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                            if (Objects.nonNull(scmError) && (Objects.nonNull(scmError.getErrorMessage())  || Objects.nonNull(scmError.getErrorMsg()))) {
                                Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                                AlertDialog alertDialog = UtilClass.utilClass.getAlertDialog(AssetLookup.this, "FOUND_LOST_ASSET_ERROR",
                                        Objects.nonNull(scmError.getErrorMessage()) ? scmError.getErrorMessage() : scmError.getErrorMsg());
                                alertDialog.show();
                                progressDialog.dismiss();
                                return;
                            }
                        } catch (Exception e) {
                            Toast.makeText(getApplicationContext(), "API Response is Not Successful while Submitting Lost Found Asset..!", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Toast.makeText(getApplicationContext(), "Error Occurred while Saving Submitting Lost Found Asset..!", Toast.LENGTH_SHORT).show();
                    }
                    progressDialog.dismiss();
                    return;
                }
                if (response.body() == null) {
                    Toast.makeText(AssetLookup.this, "Null response on Submitting Lost Found Asset..!", Toast.LENGTH_SHORT).show();
                    return;
                }
                Boolean result = response.body();
                if (Objects.nonNull(result) && result) {
                    Intent intent = new Intent(AssetLookup.this, StockTaking.class);
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    startActivity(intent);
                    finish();
                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(Call<Boolean> call, Throwable t) {
                progressDialog.dismiss();
                Toast.makeText(AssetLookup.this, " on Failure  Lost Found Asset Call..!", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        pauseScanner();
    }

    @Override
    public boolean onNavigateUp() {
        pauseScanner();
        return super.onNavigateUp();
    }
}
