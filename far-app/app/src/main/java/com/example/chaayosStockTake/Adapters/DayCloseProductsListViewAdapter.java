package com.example.chaayosStockTake.Adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.InputFilter;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.R;
import com.example.chaayosStockTake.domain.StockTakeSumoDayCloseProductsDTO;

import java.util.ArrayList;
import java.util.List;

public class DayCloseProductsListViewAdapter extends RecyclerView.Adapter<DayCloseProductsListViewAdapter.DayCloseProductViewHolder> implements Filterable {

    private List<StockTakeSumoDayCloseProductsDTO> originalProductsList;
    private List<StockTakeSumoDayCloseProductsDTO> filteredProductsList;

    private Context context;

    private InputFilter decimalInputFilter;

    private LinearLayoutManager linearLayoutManager;

    private RecyclerView listOfProductsRecyclerView;

    private boolean isPackagingEditable;

    private Button saveProductTypeButton;

    private String currentDisplayProductType;

    public List<StockTakeSumoDayCloseProductsDTO> getOriginalProductsList() {
        return originalProductsList;
    }

    public List<StockTakeSumoDayCloseProductsDTO> getFilteredProductsList() {
        return filteredProductsList;
    }

    public void setOriginalProductsList(List<StockTakeSumoDayCloseProductsDTO> prodsList) {
        this.originalProductsList = prodsList;
        this.filteredProductsList = prodsList;
    }

    public void setPackagingEditable(boolean packagingEditable) {
        isPackagingEditable = packagingEditable;
    }

    public Context getContext() {
        return context;
    }

    public void setContext(Context context) {
        this.context = context;
    }

    public DayCloseProductsListViewAdapter(List<StockTakeSumoDayCloseProductsDTO> originalProductsList, Context context, InputFilter decimalInputFilter,
                                           LinearLayoutManager linearLayoutManager, RecyclerView listOfProductsRecyclerView, boolean isPackagingEditable,
                                           Button saveProductTypeButton, String currentDisplayProductType) {
        this.originalProductsList = originalProductsList;
        this.filteredProductsList = originalProductsList;
        this.context = context;
        this.decimalInputFilter = decimalInputFilter;
        this.linearLayoutManager = linearLayoutManager;
        this.listOfProductsRecyclerView = listOfProductsRecyclerView;
        this.isPackagingEditable = isPackagingEditable;
        this.saveProductTypeButton = saveProductTypeButton;
        this.currentDisplayProductType = currentDisplayProductType;
    }

    @NonNull
    @Override
    public DayCloseProductViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.day_close_product_item,parent,false);
        return new DayCloseProductViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DayCloseProductViewHolder holder, @SuppressLint("RecyclerView") int position) {
        if (!filteredProductsList.isEmpty() && position <= filteredProductsList.size() - 1) {
            StockTakeSumoDayCloseProductsDTO productsDTO = filteredProductsList.get(position);
            if (productsDTO != null) {
                holder.productName.setText(productsDTO.getProductName());
                holder.uom.setText(productsDTO.getUom());
                holder.enteredQuantity.setText(null);
                RecyclerView.LayoutManager mGridLayoutManager = new GridLayoutManager(context, 2);
                holder.productPackagingRecyclerView.setLayoutManager(mGridLayoutManager);
                ProductPackagingListViewAdapter mAdapter = new ProductPackagingListViewAdapter(holder, productsDTO, productsDTO.getDayCloseProductPackagingMappings(),
                        decimalInputFilter, isPackagingEditable, context, originalProductsList, saveProductTypeButton, currentDisplayProductType);
                holder.productPackagingRecyclerView.setAdapter(mAdapter);
                holder.productPackagingRecyclerView.setHasFixedSize(true);
                holder.productPackagingLayout.setVisibility(View.GONE);
                mAdapter.calculateEnteredQuantity();
                holder.productBasicLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        for (int i = 0; i < listOfProductsRecyclerView.getChildCount(); i++) {
                            if (i != position) {
                                View childView = listOfProductsRecyclerView.getChildAt(i);
                                DayCloseProductViewHolder viewHolder = (DayCloseProductViewHolder) listOfProductsRecyclerView.getChildViewHolder(childView);
                                if (viewHolder.productPackagingLayout.getVisibility() == View.VISIBLE) {
                                    viewHolder.productPackagingLayout.setVisibility(View.GONE);
                                }
                            }
                        }
                        if (holder.productPackagingLayout.getVisibility() == View.GONE) {
                            holder.productPackagingLayout.setVisibility(View.VISIBLE);
                        } else {
                            holder.productPackagingLayout.setVisibility(View.GONE);
                        }
                    }
                });
            }
        }
    }

    @Override
    public int getItemCount() {
        return filteredProductsList.size();
    }

    public static class DayCloseProductViewHolder extends RecyclerView.ViewHolder {

        LinearLayout productBasicLayout;

        TextView productName;

        TextView enteredQuantity;

        TextView uom;

        LinearLayout productPackagingLayout;

        RecyclerView productPackagingRecyclerView;

        public DayCloseProductViewHolder(@NonNull View itemView) {
            super(itemView);
            productBasicLayout = itemView.findViewById(R.id.product_basic_layout);
            productName = itemView.findViewById(R.id.product_name);
            enteredQuantity = itemView.findViewById(R.id.entered_quantity);
            uom = itemView.findViewById(R.id.uom);
            productPackagingLayout = itemView.findViewById(R.id.product_packaging_layout);
            productPackagingRecyclerView = itemView.findViewById(R.id.product_packaging_recycler_view);
        }
    }

    @Override
    public Filter getFilter() {
        return new ItemFilter();
    }

    private class ItemFilter extends Filter {
        @Override
        protected FilterResults performFiltering(CharSequence constraint) {
            String filterString = constraint.toString().toLowerCase();

            FilterResults results = new FilterResults();

            List<StockTakeSumoDayCloseProductsDTO> originalList = originalProductsList;

            List<StockTakeSumoDayCloseProductsDTO> filteredList = new ArrayList<>(originalList.size());

            for (StockTakeSumoDayCloseProductsDTO item : originalList) {
                if (item.getProductName().toLowerCase().contains(filterString)) {
                    filteredList.add(item);
                }
            }

            results.values = filteredList;
            results.count = filteredList.size();

            return results;
        }

        @Override
        protected void publishResults(CharSequence constraint, FilterResults results) {
            filteredProductsList = (List<StockTakeSumoDayCloseProductsDTO>) results.values;
            notifyDataSetChanged();
        }
    }
}
