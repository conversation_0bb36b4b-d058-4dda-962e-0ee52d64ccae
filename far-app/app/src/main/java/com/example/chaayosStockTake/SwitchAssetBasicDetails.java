package com.example.chaayosStockTake;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.example.chaayosStockTake.domain.UnitEmployeesData;
import com.example.chaayosStockTake.util.AppConstants;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.gson.reflect.TypeToken;
import com.toptoche.searchablespinnerlibrary.SearchableSpinner;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;

public class SwitchAssetBasicDetails extends AppCompatActivity implements AdapterView.OnItemSelectedListener, View.OnClickListener {

    private SearchableSpinner switchWithUnit;
    private SearchableSpinner receivedByEmployee;
    private EditText birTicket;

    private final Boolean isInitialUnitSelection = true;

    private AlertDialog progressDialog;

    private UnitEmployeesData unitEmployeesData;

    private List<Unit> unitsList;

    private Unit switchWith;

    private Integer switchAssetReceivedBy;

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.switch_asset_basic_detail);
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Map<String, ?> prefs = pref.getAll();
        switchWithUnit = findViewById(R.id.switch_with_unit);
        receivedByEmployee = findViewById(R.id.received_by_employee);
        birTicket = findViewById(R.id.bir_ticket);
        Button nextButton = findViewById(R.id.switch_asset_next_button);
        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Switch Asset");
        if (Objects.nonNull(prefs.get("unitName"))) {
            getSupportActionBar().setSubtitle((String) prefs.get("unitName"));
        }
        Type type = new TypeToken<List<Unit>>() {
        }.getType();
        unitsList = UtilClass.gson.fromJson(pref.getString("unitsList", null), type);
        ArrayAdapter<Unit> spinnerArrayAdapter = new ArrayAdapter<Unit>(SwitchAssetBasicDetails.this, android.R.layout.simple_spinner_item, unitsList);
        switchWithUnit.setAdapter(spinnerArrayAdapter);
        switchWithUnit.setTitle("Switch With Unit");
        switchWithUnit.setPositiveButton("OK");
        switchWithUnit.setOnItemSelectedListener(SwitchAssetBasicDetails.this);

        progressDialog = UtilClass.utilClass.getDialogProgressBar(this).create();
        progressDialog.setCancelable(false);
        nextButton.setOnClickListener(this);

        Spinner switchReason = findViewById(R.id.switch_reason);
        ArrayAdapter<String> switchReasonArrayAdapter = new ArrayAdapter<String>(SwitchAssetBasicDetails.this, android.R.layout.simple_spinner_item, AppConstants.SWITCH_FILTERS);
        switchReason.setAdapter(switchReasonArrayAdapter);
        switchReason.setOnItemSelectedListener(SwitchAssetBasicDetails.this);
    }

    @Override
    public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
        if (adapterView.getId() == R.id.switch_with_unit) {
            progressDialog.show();
            switchWith = unitsList.get(i);
            Retrofit retrofit = UtilClass.getRetrofitMaster();
            UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
            SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            SharedPreferences switchAssetPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
            SharedPreferences.Editor switchAssetEdit = switchAssetPref.edit();
            switchAssetEdit.putString("switchWithUnit", String.valueOf(switchWith.getId()));
            switchAssetEdit.apply();
            String token = pref.getString("jwtToken", null);
            Integer unitId = pref.getInt("unitId", -1);
            Call<UnitEmployeesData> unitEmployeesDataCall = unitsApiService.getUnitEmployees(token, switchWith.getId());

            unitEmployeesDataCall.enqueue(new Callback<>() {
                @Override
                public void onResponse(Call<UnitEmployeesData> call, Response<UnitEmployeesData> response) {
                    if (!response.isSuccessful()) {
                        UtilClass.utilClass.parseScmError(response, SwitchAssetBasicDetails.this, "getting unit Employees");
                        progressDialog.dismiss();
                        return;
                    }
                    if (response.body() == null) {
                        Toast.makeText(getApplicationContext(), "Error getting Unit Employees! - NULL!", Toast.LENGTH_SHORT).show();
                        progressDialog.dismiss();
                        return;
                    }
                    unitEmployeesData = response.body();

                    ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>(SwitchAssetBasicDetails.this, android.R.layout.simple_spinner_item, unitEmployeesData.getEmpNameAndId());

                    receivedByEmployee.setAdapter(spinnerArrayAdapter);
                    receivedByEmployee.setTitle("Received By ");
                    receivedByEmployee.setPositiveButton("OK");
                    receivedByEmployee.setOnItemSelectedListener(SwitchAssetBasicDetails.this);
                    progressDialog.dismiss();
                }

                @Override
                public void onFailure(Call<UnitEmployeesData> call, Throwable t) {
                    Toast.makeText(getApplicationContext(), "Unit Employees : Failure", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                }
            });
        } else if (adapterView.getId() == R.id.received_by_employee) {
            if (Objects.nonNull(receivedByEmployee) && Objects.nonNull(unitEmployeesData) && Objects.nonNull(unitEmployeesData.getEmpNameAndId()) && !unitEmployeesData.getEmpNameAndId().isEmpty()) {
                SharedPreferences switchAssetPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
                SharedPreferences.Editor switchAssetEdit = switchAssetPref.edit();
                String employee = unitEmployeesData.getEmpNameAndId().get(i);
                String stringWithoutQuotes = employee.replaceAll("^\"|\"$", "");
                String[] user = stringWithoutQuotes.split("-");
                switchAssetReceivedBy = Integer.valueOf(user[1]);
                switchAssetEdit.putString("switchAssetReceivedBy", UtilClass.gson.toJson(unitEmployeesData.getEmpNameAndId().get(i)));
                switchAssetEdit.apply();
            }
        } else {
            String selectedReason = AppConstants.SWITCH_FILTERS.get(i);
            if (Objects.nonNull(selectedReason)) {
                SharedPreferences switchAssetPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
                SharedPreferences.Editor switchAssetEdit = switchAssetPref.edit();
                switchAssetEdit.putString("switchReason", selectedReason);
                switchAssetEdit.apply();
            }
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> adapterView) {

    }

    @Override
    public boolean onNavigateUp() {
        SharedPreferences sharedPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPref.edit();
        editor.clear();
        editor.apply();
        return super.onNavigateUp();
    }

    @Override
    public void onClick(View view) {
        if (Objects.nonNull(switchWithUnit) && Objects.isNull(switchWithUnit.getSelectedItem())) {
            UtilClass.utilClass.getToast(SwitchAssetBasicDetails.this, "Please Select Unit").show();
            return;
        } else {
            SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            Integer unitId = pref.getInt("unitId", -1);
            if (unitId.equals(switchWith.getId())) {
                UtilClass.utilClass.getToast(SwitchAssetBasicDetails.this, "Cannot Switch Assets with Same Unit..!").show();
                return;
            }
        }
        if (Objects.nonNull(receivedByEmployee) && Objects.isNull(receivedByEmployee.getSelectedItem())) {
            UtilClass.utilClass.getToast(SwitchAssetBasicDetails.this, "Please Select Received By").show();
            return;
        } else {
            SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            Integer userId = pref.getInt("id", -1);
            if (switchAssetReceivedBy.equals(userId)) {
                UtilClass.utilClass.getToast(SwitchAssetBasicDetails.this, "Cannot Receive Assets with Same Employee Login..!").show();
                return;
            }
        }
        if (Objects.nonNull(birTicket) && Objects.nonNull(birTicket.getText()) && birTicket.getText().length() == 0) {
            UtilClass.utilClass.getToast(SwitchAssetBasicDetails.this, "Please Enter BIR Ticket").show();
            return;
        } else {
            SharedPreferences switchAssetPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
            SharedPreferences.Editor switchAssetEdit = switchAssetPref.edit();
            switchAssetEdit.putString("birTicket", birTicket.getText().toString());
            switchAssetEdit.apply();
        }

        Intent intent = new Intent(SwitchAssetBasicDetails.this, SwitchAssetScanner.class);
        startActivity(intent);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        onBackPressed();
        return true;
    }
}
