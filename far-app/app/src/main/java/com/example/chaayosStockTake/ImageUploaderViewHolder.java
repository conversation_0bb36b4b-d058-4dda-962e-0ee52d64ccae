package com.example.chaayosStockTake;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.util.UtilClass;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

public class ImageUploaderViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

    private ConstraintLayout constraintLayout;
    private ImageView imageView;

    private FloatingActionButton cancelButton;

    private Button startButton;

    private Context context;

    private Integer currentPosition;

    public ImageUploaderViewHolder(View itemView, Context context) {
        super(itemView);
        constraintLayout = itemView.findViewById(R.id.switch_asset_images);
        imageView = itemView.findViewById(R.id.switch_asset_upload_image);
        cancelButton = itemView.findViewById(R.id.remove_uploaded_photo);
        constraintLayout.setVisibility(View.VISIBLE);
        imageView.setVisibility(View.VISIBLE);
        cancelButton.setVisibility(View.VISIBLE);
        imageView.setOnClickListener(this);
        cancelButton.setOnClickListener(this);
        this.context = context;
    }

    public ConstraintLayout getConstraintLayout() {
        return constraintLayout;
    }

    public void setConstraintLayout(ConstraintLayout constraintLayout) {
        this.constraintLayout = constraintLayout;
    }

    public Button getStartButton() {
        return startButton;
    }

    public void setStartButton(Button startButton) {
        this.startButton = startButton;
    }

    public ImageView getImageView() {
        return imageView;
    }

    public void setImageView(ImageView imageView) {
        this.imageView = imageView;
    }

    public FloatingActionButton getCancelButton() {
        return cancelButton;
    }

    public void setCancelButton(FloatingActionButton cancelButton) {
        this.cancelButton = cancelButton;
    }

    public Context getContext() {
        return context;
    }

    public void setContext(Context context) {
        this.context = context;
    }

    public Integer getCurrentPosition() {
        return currentPosition;
    }

    public void setCurrentPosition(Integer currentPosition) {
        this.currentPosition = currentPosition;
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.switch_asset_upload_image) {
            ((SwitchAssetScanner) view.getContext()).uploadImage(this.currentPosition);
        } else if (view.getId() == R.id.remove_uploaded_photo) {
            ((SwitchAssetScanner) view.getContext()).removeImageOnIndex(this.currentPosition);
        } else {

        }
    }

    private Context getApplicationContext() {
        return this.context;
    }
}
