package com.example.chaayosStockTake.domain;

import java.util.List;

public class AssetSwitch {
    Integer newAssetId;
    List<Integer> newAssetDocIds;
    String newAssetComments;
    Integer oldAssetId;
    List<Integer> oldAssetDocIds;
    String oldAssetComments;
    String switchReason;
    Integer createdBy;
    Integer requestedBy;
    Integer createdByUnit;
    Integer requestingUnit;
    String ticketId;
    String otpValue;

    public AssetSwitch() {
    }

    public AssetSwitch(Integer newAssetId, List<Integer> newAssetDocIds, String newAssetComments, Integer oldAssetId,
                       List<Integer> oldAssetDocIds, String oldAssetComments, String switchReason, Integer createdBy,
                       Integer requestedBy, Integer createdByUnit, Integer requestingUnit, String ticketId, String otpValue) {
        this.newAssetId = newAssetId;
        this.newAssetDocIds = newAssetDocIds;
        this.newAssetComments = newAssetComments;
        this.oldAssetId = oldAssetId;
        this.oldAssetDocIds = oldAssetDocIds;
        this.oldAssetComments = oldAssetComments;
        this.switchReason = switchReason;
        this.createdBy = createdBy;
        this.requestedBy = requestedBy;
        this.createdByUnit = createdByUnit;
        this.requestingUnit = requestingUnit;
        this.ticketId = ticketId;
        this.otpValue = otpValue;
    }

    public Integer getNewAssetId() {
        return newAssetId;
    }

    public void setNewAssetId(Integer newAssetId) {
        this.newAssetId = newAssetId;
    }

    public List<Integer> getNewAssetDocIds() {
        return newAssetDocIds;
    }

    public void setNewAssetDocIds(List<Integer> newAssetDocIds) {
        this.newAssetDocIds = newAssetDocIds;
    }

    public String getNewAssetComments() {
        return newAssetComments;
    }

    public void setNewAssetComments(String newAssetComments) {
        this.newAssetComments = newAssetComments;
    }

    public Integer getOldAssetId() {
        return oldAssetId;
    }

    public void setOldAssetId(Integer oldAssetId) {
        this.oldAssetId = oldAssetId;
    }

    public List<Integer> getOldAssetDocIds() {
        return oldAssetDocIds;
    }

    public void setOldAssetDocIds(List<Integer> oldAssetDocIds) {
        this.oldAssetDocIds = oldAssetDocIds;
    }

    public String getOldAssetComments() {
        return oldAssetComments;
    }

    public void setOldAssetComments(String oldAssetComments) {
        this.oldAssetComments = oldAssetComments;
    }

    public String getSwitchReason() {
        return switchReason;
    }

    public void setSwitchReason(String switchReason) {
        this.switchReason = switchReason;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Integer getRequestedBy() {
        return requestedBy;
    }

    public void setRequestedBy(Integer requestedBy) {
        this.requestedBy = requestedBy;
    }

    public Integer getCreatedByUnit() {
        return createdByUnit;
    }

    public void setCreatedByUnit(Integer createdByUnit) {
        this.createdByUnit = createdByUnit;
    }

    public Integer getRequestingUnit() {
        return requestingUnit;
    }

    public void setRequestingUnit(Integer requestingUnit) {
        this.requestingUnit = requestingUnit;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getOtpValue() {
        return otpValue;
    }

    public void setOtpValue(String otpValue) {
        this.otpValue = otpValue;
    }
}
