package com.example.chaayosStockTake.util;

import com.google.gson.Gson;

import java.util.Arrays;
import java.util.List;

public class AppConstants {

    public static final String YES = "Y";
    public static final String NO = "N";
    public static final String NEW_ASSET = "NEW_ASSET";
    public static final String OLD_ASSET = "OLD_ASSET";
    public static final String VALIDATE = "VALIDATE";
    public static final String PENDING_APPROVAL = "PENDING_APPROVAL";

    public static final String TRANSFER_OUT = "TRANSFER_OUT";

    public static final String ASSET_RECEIVING = "ASSET_RECEIVING";
    public static final List<String> SWITCH_FILTERS = Arrays.asList("BROKEN","REPLACING");

    public static final List<String> STOCK_TAKE_TYPES = Arrays.asList("DAILY","WEEKLY","MONTHLY");

    public static final String OTHERS = "OTHERS";
}
