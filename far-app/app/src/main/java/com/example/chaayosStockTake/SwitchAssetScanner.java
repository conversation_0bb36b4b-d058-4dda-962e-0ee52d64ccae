package com.example.chaayosStockTake;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Paint;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.domain.AssetSwitch;
import com.example.chaayosStockTake.domain.Pair;
import com.example.chaayosStockTake.domain.SwitchAssetDefinition;
import com.example.chaayosStockTake.util.AppConstants;
import com.example.chaayosStockTake.util.UtilClass;
import com.github.dhaval2404.imagepicker.ImagePicker;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.gson.reflect.TypeToken;
import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;

public class SwitchAssetScanner extends AppCompatActivity implements View.OnClickListener {
    private DecoratedBarcodeView decoratedBarcodeViewScanner;
    private Button scanButton;

    private SwitchAssetDefinition newAsset;

    private SwitchAssetDefinition oldAsset;

    private String currentScreen = AppConstants.NEW_ASSET;
    private ConstraintLayout uploadImagesConstraintLayout;

    private ImageUploadButtonsAdapter oldImageUploadButtonsAdapter;

    private ImageUploadButtonsAdapter newImageUploadButtonsAdapter;

    private RecyclerView oldImagesUploadRecyclerView;

    private RecyclerView newImagesUploadRecyclerView;

    private AlertDialog progressDialog;

    private Integer currentImagePosition;

    private Button nextButton;

    private Map<Integer, Pair<Integer, Bitmap>> newAssetFiles;

    private Map<Integer, Pair<Integer, Bitmap>> oldAssetFiles;

    private TextView textView;

    private LinearLayout validationLinearLayout;

    private EditText enteredOtp;

    private boolean isValidated;

    private TextView newAssetCommentLabel;

    private TextView oldAssetCommentLabel;

    private EditText newAssetComment;

    private EditText oldAssetComment;

    private NestedScrollView newAssetNestedScrollView;

    private NestedScrollView oldAssetNestedScrollView;

    private TextView oldAssetUnitName;

    private TextView oldAssetTag;

    private TextView newAssetUnitName;

    private TextView newAssetTag;

    private Unit newUnit;

    private Unit oldUnit;

    private TextView summaryLabel;

    private ConstraintLayout oldAssetNestedViewCLayout;

    private ConstraintLayout newAssetNestedViewCLayout;

    @SuppressLint({"MissingInflatedId", "SetTextI18n"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.switch_asset_scanner);
        uploadImagesConstraintLayout = findViewById(R.id.upload_images_constraint_layout);
        newImagesUploadRecyclerView = findViewById(R.id.upload_images_recycler_view_new);
        oldImagesUploadRecyclerView = findViewById(R.id.upload_images_recycler_view_old);
        nextButton = findViewById(R.id.assets_next_button);
        textView = findViewById(R.id.scan_alert_message);
        Button validateOtpButton = findViewById(R.id.otp_validate);
        validateOtpButton.setOnClickListener(this);
        enteredOtp = findViewById(R.id.otp_entered);
        validationLinearLayout = findViewById(R.id.validation_liner_layout);
        newAssetCommentLabel = findViewById(R.id.new_asset_comment_label);
        newAssetCommentLabel.setVisibility(View.INVISIBLE);
        newAssetComment = findViewById(R.id.new_asset_comment);
        newAssetComment.setVisibility(View.INVISIBLE);
        oldAssetCommentLabel = findViewById(R.id.old_asset_comment_label);
        oldAssetCommentLabel.setVisibility(View.INVISIBLE);
        oldAssetComment = findViewById(R.id.old_asset_comment);
        oldAssetComment.setVisibility(View.INVISIBLE);
        validationLinearLayout.setVisibility(View.INVISIBLE);
        nextButton.setOnClickListener(this);
        newImagesUploadRecyclerView.setVisibility(View.VISIBLE);
        oldImagesUploadRecyclerView.setVisibility(View.INVISIBLE);
        newAssetNestedScrollView = findViewById(R.id.new_asset_nested_view);
        oldAssetNestedScrollView = findViewById(R.id.old_asset_nested_view);
        textView.setVisibility(View.VISIBLE);
        currentScreen = AppConstants.NEW_ASSET;
        oldAssetUnitName = findViewById(R.id.old_asset_unit_name);
        oldAssetTag = findViewById(R.id.old_asset_tag);
        newAssetUnitName = findViewById(R.id.new_asset_unit_name);
        newAssetTag = findViewById(R.id.new_asset_tag);
        FloatingActionButton viewScannedAsset = findViewById(R.id.view_asset);
        oldAssetNestedViewCLayout = findViewById(R.id.old_asset_nested_view_c_layout);
        oldAssetNestedViewCLayout.setVisibility(View.GONE);
        newAssetNestedViewCLayout = findViewById(R.id.new_asset_nested_view_c_layout);
        viewScannedAsset.setOnClickListener(this);
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String unitsList = sharedPreferences.getString("unitsList", "");
        Type type = new TypeToken<List<Unit>>() {
        }.getType();
        List<Unit> units = UtilClass.gson.fromJson(unitsList, type);
        setUnits(units);
        summaryLabel = findViewById(R.id.switch_asset_summary);
        isValidated = false;
        setSubTitle();
        requestPermission();
        progressDialog = UtilClass.utilClass.getDialogProgressBar(SwitchAssetScanner.this).create();
        progressDialog.setCancelable(false);
        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Switch Asset");
        newAssetFiles = new HashMap<>();
        oldAssetFiles = new HashMap<>();

        scanButton = findViewById(R.id.scan_btn);
        uploadImagesConstraintLayout = findViewById(R.id.upload_images_constraint_layout);
        scanButton.setOnClickListener(this);

        decoratedBarcodeViewScanner = findViewById(R.id.dbv_barcode_scanner);
        decoratedBarcodeViewScanner.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                String scannedItem = result.getText();
                startStopScanner();
                showAssetDetails(scannedItem);
            }

            @Override
            public void possibleResultPoints(List<ResultPoint> resultPoints) {

            }
        });
    }

    private void setUnits(List<Unit> units) {
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Integer createdByUnit = sharedPreferences.getInt("unitId", -1);
        SharedPreferences switchAssetPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
        Integer requestingUnit = Integer.valueOf(switchAssetPref.getString("switchWithUnit", null));
        for (Unit unit : units) {
            if (unit.getId().equals(createdByUnit)) {
                newUnit = unit;
                break;
            }
        }
        for (Unit unit : units) {
            if (unit.getId().equals(requestingUnit)) {
                oldUnit = unit;
                break;
            }
        }
    }

    private void showAssetDetails(String scannedItem) {
        progressDialog.show();
        Retrofit retrofit = UtilClass.getRetrofitScm();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sharedPreferences.getString("jwtToken", null);
        Integer unitId = sharedPreferences.getInt("unitId", 0);
        Boolean isNewAsset = getOldOrNew();
        SharedPreferences switchAssetPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
        Integer switchWithUnitId = Integer.valueOf(switchAssetPref.getString("switchWithUnit", null));
        Call<SwitchAssetDefinition> assetDefinitionCall = unitsApiService.getAssetDefinition(token,
                isNewAsset ? unitId : switchWithUnitId, scannedItem);
        assetDefinitionCall.enqueue(new Callback<SwitchAssetDefinition>() {
            @Override
            public void onResponse(Call<SwitchAssetDefinition> call, Response<SwitchAssetDefinition> response) {
                if (!response.isSuccessful()) {
                    UtilClass.utilClass.parseScmError(response, SwitchAssetScanner.this, "getting Asset Data");
                    progressDialog.dismiss();
                    return;
                }
                if (response.body() == null) {
                    Toast.makeText(getApplicationContext(), "Invalid QR Code", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                    return;
                }

                if (currentScreen.equalsIgnoreCase(AppConstants.NEW_ASSET)) {
                    newAsset = response.body();
                    newAssetCommentLabel.setVisibility(View.VISIBLE);
                    newAssetComment.setVisibility(View.VISIBLE);
                    newImageUploadButtonsAdapter = new ImageUploadButtonsAdapter(getInitialImages(true), SwitchAssetScanner.this);
                    newImagesUploadRecyclerView.setLayoutManager(new LinearLayoutManager(SwitchAssetScanner.this));
                    newImageUploadButtonsAdapter.setHasStableIds(true);
                    newImagesUploadRecyclerView.setAdapter(newImageUploadButtonsAdapter);
                    textView.setVisibility(View.GONE);
                } else if (currentScreen.equalsIgnoreCase(AppConstants.OLD_ASSET)) {
                    oldAsset = response.body();
                    if (!newAsset.getProductId().equals(oldAsset.getProductId())) {
                        oldAsset = null;
                        progressDialog.dismiss();
                        AlertDialog.Builder alertDialog = new AlertDialog.Builder(SwitchAssetScanner.this);
                        alertDialog.setTitle("Assets Mis Match..!");
                        alertDialog.setMessage("Old Asset is not matching with the new Asset" + "\n Please Scan Asset Related to the Product : " + newAsset.getProductName() + " ["
                                + newAsset.getProductId() + "]");
                        alertDialog.setPositiveButton("Cancel", (dialog, which) -> {
                            dialog.cancel();
                        });
                        alertDialog.show();
                        return;
                    } else {
                        oldAssetCommentLabel.setVisibility(View.VISIBLE);
                        oldAssetComment.setVisibility(View.VISIBLE);
                        oldImageUploadButtonsAdapter = new ImageUploadButtonsAdapter(getInitialImages(false), SwitchAssetScanner.this);
                        oldImagesUploadRecyclerView.setLayoutManager(new LinearLayoutManager(SwitchAssetScanner.this));
                        oldImageUploadButtonsAdapter.setHasStableIds(true);
                        oldImagesUploadRecyclerView.setAdapter(oldImageUploadButtonsAdapter);
                        textView.setVisibility(View.GONE);
                        oldAssetNestedViewCLayout.setVisibility(View.VISIBLE);
                        newAssetNestedViewCLayout.setVisibility(View.GONE);
                        oldAssetNestedScrollView.setVisibility(View.VISIBLE);
                        oldImagesUploadRecyclerView.setVisibility(View.VISIBLE);
                    }
                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(Call<SwitchAssetDefinition> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Server Error", Toast.LENGTH_SHORT).show();
                progressDialog.dismiss();
            }
        });
    }


    private List<Bitmap> getInitialImages(Boolean isNewAsset) {
        Map<Integer, Pair<Integer, Bitmap>> assetFiles = new HashMap<>();
        List<Bitmap> result = new ArrayList<>();
        Bitmap bitmap = BitmapFactory.decodeResource(getResources(), R.drawable.chaayos_logo_green);
        for (int i = 0; i < 10; i++) {
            assetFiles.put(i, new Pair<>(null, bitmap));
            result.add(bitmap);
        }
        if (isNewAsset) {
            newAssetFiles = assetFiles;
        } else {
            oldAssetFiles = assetFiles;
        }
        return result;
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    void requestPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, 0);
        }
    }


    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.scan_btn) {
            startStopScanner();
        } else if (v.getId() == R.id.assets_next_button) {
            InputMethodManager imm = null;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            }
            View view = getCurrentFocus();
            if (view != null) {
                imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
            }
            String nextScreen = getNextPreviousScreen(true);
            if (!nextScreen.equalsIgnoreCase("SUBMIT")) {
                if (!currentScreen.equalsIgnoreCase(AppConstants.VALIDATE)) {
                    Boolean isNewScreen = getOldOrNew();
                    if (isNewScreen) {
                        if (Objects.isNull(newAsset)) {
                            UtilClass.utilClass.getToast(this, "Please Scan the NEW asset first...!").show();
                            progressDialog.dismiss();
                            return;
                        } else {
                            int uploadedCount = 0;
                            for (Map.Entry<Integer, Pair<Integer, Bitmap>> integerPairEntry : newAssetFiles.entrySet()) {
                                if (Objects.nonNull(integerPairEntry.getValue().getKey())) {
                                    uploadedCount++;
                                }
                            }
                            if (uploadedCount < 2) {
                                UtilClass.utilClass.getToast(this, "Please Upload At least 2 Images..!").show();
                                progressDialog.dismiss();
                                return;
                            }
                        }
                    } else {
                        if (Objects.isNull(oldAsset)) {
                            UtilClass.utilClass.getToast(this, "Please Scan the OLD asset first...!").show();
                            progressDialog.dismiss();
                            return;
                        } else {
                            int uploadedCount = 0;
                            for (Map.Entry<Integer, Pair<Integer, Bitmap>> integerPairEntry : oldAssetFiles.entrySet()) {
                                if (Objects.nonNull(integerPairEntry.getValue().getKey())) {
                                    uploadedCount++;
                                }
                            }
                            if (uploadedCount < 2) {
                                UtilClass.utilClass.getToast(this, "Please Upload At least 2 Images..!").show();
                                progressDialog.dismiss();
                                return;
                            }
                        }
                    }
                }
                currentScreen = nextScreen;
                setSubTitle();
                if (nextScreen.equalsIgnoreCase(AppConstants.OLD_ASSET)) {
                    if (Objects.isNull(oldAsset)) {
                        textView.setVisibility(View.VISIBLE);
                    }
                    decoratedBarcodeViewScanner.setVisibility(View.VISIBLE);
                    scanButton.setVisibility(View.VISIBLE);
                    uploadImagesConstraintLayout.setVisibility(View.VISIBLE);
                    oldImagesUploadRecyclerView.setVisibility(View.INVISIBLE);
                    newImagesUploadRecyclerView.setVisibility(View.INVISIBLE);
                    validationLinearLayout.setVisibility(View.INVISIBLE);
                    newAssetCommentLabel.setVisibility(View.INVISIBLE);
                    newAssetComment.setVisibility(View.INVISIBLE);
                    oldAssetCommentLabel.setVisibility(View.INVISIBLE);
                    oldAssetComment.setVisibility(View.INVISIBLE);
                } else {
                    decoratedBarcodeViewScanner.setVisibility(View.INVISIBLE);
                    textView.setVisibility(View.INVISIBLE);
                    scanButton.setVisibility(View.INVISIBLE);
                    uploadImagesConstraintLayout.setVisibility(View.INVISIBLE);
                    validationLinearLayout.setVisibility(View.VISIBLE);
                    ViewGroup.LayoutParams layoutParams = nextButton.getLayoutParams();
                    layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
                    nextButton.setLayoutParams(layoutParams);
                    enteredOtp.setVisibility(View.INVISIBLE);
                    nextButton.setVisibility(View.INVISIBLE);
                    oldAssetUnitName.setText(oldUnit.getName());
                    oldAssetTag.setText(oldAsset.getAssetTag());
                    newAssetUnitName.setText(newUnit.getName());
                    newAssetTag.setText(newAsset.getAssetTag());
                    String msg = "Switch Asset Summary (" + oldAsset.getProductName() + " )";
                    summaryLabel.setText(msg);
                    summaryLabel.setPaintFlags(textView.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
                    oldAssetNestedViewCLayout.setVisibility(View.INVISIBLE);
                    newAssetNestedScrollView.setVisibility(View.INVISIBLE);
                }
            } else {
                submitSwitchAssetProcess();
            }
        } else if (v.getId() == R.id.otp_validate) {
            progressDialog.show();
            Retrofit retrofit = UtilClass.getRetrofitScm();
            UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
            SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            String token = sharedPreferences.getString("jwtToken", null);
            SharedPreferences switchAssetPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
            String userName = switchAssetPref.getString("switchAssetReceivedBy", "0");
            String stringWithoutQuotes = userName.replaceAll("^\"|\"$", "");
            String[] user = stringWithoutQuotes.split("-");
            Integer userId = Integer.valueOf(user[1]);
            Call<Boolean> validateOtpCall = unitsApiService.validateOtpCall(token, userId);
            validateOtpCall.enqueue(new Callback<Boolean>() {
                @Override
                public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                    if (!response.isSuccessful()) {
                        UtilClass.utilClass.parseScmError(response, SwitchAssetScanner.this, "Validation");
                        progressDialog.dismiss();
                        return;
                    }
                    Boolean result = response.body();
                    if (result) {
                        enteredOtp.setVisibility(View.VISIBLE);
                        nextButton.setVisibility(View.VISIBLE);
                        isValidated = true;
                    } else {
                        UtilClass.utilClass.getToast(SwitchAssetScanner.this, "Something Went Wrong While generating OTP..!").show();
                    }
                    progressDialog.dismiss();
                }

                @Override
                public void onFailure(Call<Boolean> call, Throwable t) {
                    Toast.makeText(getApplicationContext(), "Error Occurred While Validation", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                }
            });
        } else if (v.getId() == R.id.view_asset) {
            Boolean isNewAsset = getOldOrNew();
            SwitchAssetDefinition assetDefinition = isNewAsset ? newAsset : oldAsset;
            if (Objects.nonNull(assetDefinition)) {
                AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
                alertDialog.setTitle(isNewAsset ? "New Asset Scanned" : "Old Asset Scanned");
                StringBuilder msg = new StringBuilder("Asset Id : ");
                msg.append(assetDefinition.getAssetId()).append("\n").append("Asset Tag : ").append(assetDefinition.getAssetTag()).append("\n").
                        append("Product : ").append(assetDefinition.getAssetName()).append("\n");
                alertDialog.setMessage(msg.toString());
                alertDialog.setPositiveButton("Close", (dialog, which) -> {
                    dialog.cancel();
                });
                alertDialog.show();
            } else {
                UtilClass.utilClass.getToast(this, "Please Scan the Asset First..!").show();
            }
        }
    }

    private void submitSwitchAssetProcess() {
        String otp = enteredOtp.getText().toString();
        if (Objects.nonNull(otp) && otp.length() == 4) {
            progressDialog.show();
            List<Integer> newAssetDocIDs = new ArrayList<>();
            for (Map.Entry<Integer, Pair<Integer, Bitmap>> integerPairEntry : newAssetFiles.entrySet()) {
                if (Objects.nonNull(integerPairEntry.getValue().getKey())) {
                    newAssetDocIDs.add(integerPairEntry.getValue().getKey());
                }
            }
            List<Integer> oldAssetDocIDs = new ArrayList<>();
            for (Map.Entry<Integer, Pair<Integer, Bitmap>> integerPairEntry : oldAssetFiles.entrySet()) {
                if (Objects.nonNull(integerPairEntry.getValue().getKey())) {
                    oldAssetDocIDs.add(integerPairEntry.getValue().getKey());
                }
            }
            SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            Integer createdBy = sharedPreferences.getInt("id", -1);
            Integer createdByUnit = sharedPreferences.getInt("unitId", -1);
            SharedPreferences switchAssetPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
            String userName = switchAssetPref.getString("switchAssetReceivedBy", "0");
            String stringWithoutQuotes = userName.replaceAll("^\"|\"$", "");
            String[] user = stringWithoutQuotes.split("-");
            Integer requestedBy = Integer.valueOf(user[1]);
            Integer requestingUnit = Integer.valueOf(switchAssetPref.getString("switchWithUnit", null));
            String birTicket = switchAssetPref.getString("birTicket", null);
            String switchReason = switchAssetPref.getString("switchReason", null);
            AssetSwitch assetSwitch = new AssetSwitch(newAsset.getAssetId(), newAssetDocIDs, newAssetComment.getText().toString(),
                    oldAsset.getAssetId(), oldAssetDocIDs, oldAssetComment.getText().toString(), switchReason, createdBy, requestedBy, createdByUnit, requestingUnit,
                    birTicket, enteredOtp.getText().toString());

            Retrofit retrofit = UtilClass.getRetrofitScm();
            UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
            String token = sharedPreferences.getString("jwtToken", null);

            Call<Boolean> startSwitchAssetCall = unitsApiService.startSwitchAssetProcess(token, assetSwitch);

            startSwitchAssetCall.enqueue(new Callback<Boolean>() {
                @Override
                public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                    if (!response.isSuccessful()) {
                        UtilClass.utilClass.parseScmError(response, SwitchAssetScanner.this, "while Switching Asset");
                        progressDialog.dismiss();
                        return;
                    }
                    if (response.body() == null) {
                        UtilClass.utilClass.getToast(SwitchAssetScanner.this, "Can not Switch the Asset - NULL..!").show();
                        progressDialog.dismiss();
                        return;
                    }
                    Boolean result = response.body();
                    if (result) {
                        progressDialog.dismiss();
                        AlertDialog.Builder alertDialog = new AlertDialog.Builder(SwitchAssetScanner.this);
                        alertDialog.setTitle("Asset Switched Successfully");
                        alertDialog.setMessage("Asset : " + newAsset.getProductName() + " Switched Successfully");
                        alertDialog.setPositiveButton("Ok", (dialog, which) -> {
                            dialog.cancel();
                            SharedPreferences sharedPref = getSharedPreferences("switchAssetData", MODE_PRIVATE);
                            SharedPreferences.Editor editor = sharedPref.edit();
                            editor.clear();
                            editor.apply();
                            Intent intent = new Intent(SwitchAssetScanner.this, Events.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            startActivity(intent);
                            finish();
                        });
                        alertDialog.setCancelable(false);
                        alertDialog.show();
                    } else {
                        UtilClass.utilClass.getToast(SwitchAssetScanner.this, "Can not Switch the Asset..!").show();
                    }
                    progressDialog.dismiss();
                }

                @Override
                public void onFailure(Call<Boolean> call, Throwable t) {
                    Toast.makeText(getApplicationContext(), "Cannot Complete the Switch Asset Process ..!", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                }
            });
        } else {
            UtilClass.utilClass.getToast(this, "Please Enter OTP..!");
        }
    }

    private void startStopScanner() {
        if (scanButton.getText().toString().equalsIgnoreCase("Start Scanning")) {
            if (!decoratedBarcodeViewScanner.isActivated()) {
                decoratedBarcodeViewScanner.resume();
                scanButton.setText("Stop Scanning");
            }
        } else {
            decoratedBarcodeViewScanner.pause();
            scanButton.setText("Start Scanning");
        }
    }

    private String getNextPreviousScreen(Boolean isNext) {
        if (isNext) {
            if (currentScreen.equalsIgnoreCase(AppConstants.NEW_ASSET)) {
                return AppConstants.OLD_ASSET;
            } else if (currentScreen.equalsIgnoreCase(AppConstants.OLD_ASSET)) {
                return AppConstants.VALIDATE;
            } else {
                return "SUBMIT";
            }
        } else {
            if (currentScreen.equalsIgnoreCase(AppConstants.VALIDATE)) {
                return AppConstants.OLD_ASSET;
            } else if (currentScreen.equalsIgnoreCase(AppConstants.OLD_ASSET)) {
                return AppConstants.NEW_ASSET;
            }
        }
        return null;
    }

    public void uploadImage(Integer currentPosition) {
        currentImagePosition = currentPosition;
        ImagePicker.with(this)
                .crop()
                .compress(1024).start();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void removeImageOnIndex(Integer position) {
        progressDialog.show();
        Boolean isNewAsset = getOldOrNew();
        Map<Integer, Pair<Integer, Bitmap>> assetFiles = isNewAsset ? newAssetFiles : oldAssetFiles;
        Pair<Integer, Bitmap> docIdBitMap = assetFiles.get(position);
        if (Objects.nonNull(docIdBitMap)) {
            docIdBitMap.setKey(null);
            docIdBitMap.setValue(BitmapFactory.decodeResource(getResources(), R.drawable.chaayos_logo_green));
        } else {
            docIdBitMap = new Pair<>(null, BitmapFactory.decodeResource(getResources(), R.drawable.chaayos_logo_green));
        }
        assetFiles.put(position, docIdBitMap);
        ImageUploaderViewHolder viewHolder = (ImageUploaderViewHolder) newImagesUploadRecyclerView.findViewHolderForAdapterPosition(position);
        if (viewHolder != null) {
            viewHolder.getImageView().setImageURI(Uri.parse("android.resource://" + getPackageName() + "/drawable/" + android.R.drawable.ic_menu_add));
            Toast.makeText(getApplicationContext(), "Image Removed..! ", Toast.LENGTH_SHORT).show();
            if (isNewAsset) {
                newImageUploadButtonsAdapter.notifyDataSetChanged();
            } else {
                oldImageUploadButtonsAdapter.notifyDataSetChanged();
            }
        }
        if (isNewAsset) {
            newAssetFiles = assetFiles;
        } else {
            oldAssetFiles = assetFiles;
        }
        progressDialog.dismiss();
    }

    private Boolean getOldOrNew() {
        return currentScreen.equalsIgnoreCase(AppConstants.NEW_ASSET);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        progressDialog.show();
        if (Objects.nonNull(data) && Objects.nonNull(data.getData())) {
            progressDialog.show();
            Uri uri = data.getData();
            Bitmap bitmap = null;
            try {
                bitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), uri);
            } catch (IOException e) {
                progressDialog.dismiss();
                return;
            }
            Retrofit retrofit = UtilClass.getRetrofitScm();
            Boolean isNewAsset = getOldOrNew();
            UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
            SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            String token = sharedPreferences.getString("jwtToken", null);
            File imageFile = UtilClass.utilClass.saveBitmap(bitmap, this);
            MultipartBody.Part filePart = MultipartBody.Part.createFormData("file", imageFile.getName(),
                    RequestBody.create(MediaType.parse("image/*"), imageFile));
            RequestBody type = RequestBody.create(MediaType.parse("text/plain"), "ASSET_IMAGE");
            RequestBody mimeType = RequestBody.create(MediaType.parse("text/plain"), "PNG");
            RequestBody docType = RequestBody.create(MediaType.parse("text/plain"), "PROOF_OF_DELIVERY");
            RequestBody userId = RequestBody.create(MediaType.parse("text/plain"), String.valueOf(sharedPreferences.getInt("id", -1)));

            Call<Integer> switchAssetUploadImageCall = unitsApiService.uploadSwitchAssetImage(token, filePart, type, mimeType, docType, userId, isNewAsset);
            Bitmap finalBitmap = bitmap;
            switchAssetUploadImageCall.enqueue(new Callback<Integer>() {
                @SuppressLint("NotifyDataSetChanged")
                @Override
                public void onResponse(Call<Integer> call, Response<Integer> response) {
                    Integer uploadedDocId = response.body();
                    Map<Integer, Pair<Integer, Bitmap>> assetFiles = isNewAsset ? newAssetFiles : oldAssetFiles;
                    Pair<Integer, Bitmap> docIdBitMap = assetFiles.get(currentImagePosition);
                    if (Objects.nonNull(docIdBitMap)) {
                        docIdBitMap.setKey(uploadedDocId);
                        docIdBitMap.setValue(finalBitmap);
                    } else {
                        docIdBitMap = new Pair<>(uploadedDocId, finalBitmap);
                    }
                    assetFiles.put(currentImagePosition, docIdBitMap);
                    Toast.makeText(getApplicationContext(), "Image Uploaded Successfully", Toast.LENGTH_SHORT).show();
                    ImageUploaderViewHolder viewHolder = (ImageUploaderViewHolder) (isNewAsset ? newImagesUploadRecyclerView : oldImagesUploadRecyclerView).findViewHolderForAdapterPosition(currentImagePosition);
                    if (viewHolder != null) {
                        viewHolder.getImageView().setImageURI(uri);
                        if (isNewAsset) {
                            newImageUploadButtonsAdapter.notifyDataSetChanged();
                        } else {
                            oldImageUploadButtonsAdapter.notifyDataSetChanged();
                        }
                    }
                    if (imageFile.exists()) {
                        imageFile.delete();
                    }
                    if (isNewAsset) {
                        newAssetFiles = assetFiles;
                    } else {
                        oldAssetFiles = assetFiles;
                    }
                    progressDialog.dismiss();
                }

                @Override
                public void onFailure(Call<Integer> call, Throwable t) {
                    Toast.makeText(getApplicationContext(), "Cannot Upload Image ..!", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                    if (imageFile.exists()) {
                        imageFile.delete();
                    }
                }
            });
        } else {
            progressDialog.dismiss();
        }
    }

    @SuppressLint("SetTextI18n")
    public void setSubTitle() {
        if (currentScreen.equalsIgnoreCase(AppConstants.NEW_ASSET)) {
            Objects.requireNonNull(getSupportActionBar()).setSubtitle("New Asset");
            nextButton.setText("NEXT");
        } else if (currentScreen.equalsIgnoreCase(AppConstants.OLD_ASSET)) {
            nextButton.setText("VALIDATE");
            Objects.requireNonNull(getSupportActionBar()).setSubtitle("Old Asset");
        } else {
            Objects.requireNonNull(getSupportActionBar()).setSubtitle("Validation");
            nextButton.setText("SUBMIT");
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        onBackPressed();
        return true;
    }

    @Override
    public void onBackPressed() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
        alertDialog.setTitle("Are You Sure.?");
        alertDialog.setMessage("Do You Want to Go Back ? . All the Data Will be Lost..!");
        alertDialog.setNegativeButton("NO", (dialog, which) -> {
            dialog.cancel();
        });
        alertDialog.setPositiveButton("Yes", (dialog, which) -> {
            dialog.cancel();
            finish();
        });
        alertDialog.show();
    }
}
