package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;

public class ScannedListAdapter extends RecyclerView.Adapter<ScannedListAdapter.ScannedListViewHolder> {


    private ArrayList<String> scannedItems;
    private Context mContext;
    public ScannedListAdapter(Context context, ArrayList<String> scannedItems) {
        this.scannedItems = scannedItems;
        this.mContext = context;
    }

    @NonNull
    @Override
    public ScannedListViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.scanned_list_item, parent, false);
        return new ScannedListViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ScannedListViewHolder holder, int position) {
        String item = scannedItems.get(position);
        holder.listItem.setText(item);
        holder.parentLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //Toast.makeText(mContext,scannedItems.get(position),Toast.LENGTH_SHORT).show();
            }
        });
    }


    @Override
    public int getItemCount() {
        return scannedItems.size();
    }


    public class ScannedListViewHolder extends RecyclerView.ViewHolder {

        TextView listItem;
        LinearLayout parentLayout;

        public ScannedListViewHolder(@NonNull View itemView) {
            super(itemView);
            parentLayout = itemView.findViewById(R.id.parent_layout);
            listItem = itemView.findViewById(R.id.list_item);
        }
    }
}
