package com.example.chaayosStockTake;

import android.content.Context;
import android.os.Build;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class StockTakingNonScannableItemSummary  extends RecyclerView.Adapter<StockTakingNonScannableItemSummary.StockTakingNonScannableSummaryItemViewHolder> {

    public List<StockTakingScannedItem> assets;
    private Context mContext;

    private Boolean isEmptyNonScannableList = Boolean.TRUE;

    public StockTakingNonScannableItemSummary( Context mContext, List<StockTakingScannedItem> assets) {
        this.assets = filterByScannable(assets);
        this.mContext = mContext;
        if (!this.assets.isEmpty()) {
            this.isEmptyNonScannableList = Boolean.FALSE;
        }
    }

    public Boolean getEmptyNonScannableList() {
        return isEmptyNonScannableList;
    }

    private List<StockTakingScannedItem> filterByScannable(List<StockTakingScannedItem> assets){
            List<StockTakingScannedItem> filteredResult = new ArrayList<>();
            for(StockTakingScannedItem asset : assets){
                Gson googleJson = new Gson();
                if(Boolean.TRUE.equals(asset.getNonScannable())){
                    filteredResult.add(asset);
                }
            }

            return sortAssets(filteredResult);

    }

    private List<StockTakingScannedItem> sortAssets(List<StockTakingScannedItem> assetList){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            assetList.sort(new Comparator<StockTakingScannedItem>() {
                @Override
                public int compare(StockTakingScannedItem o1, StockTakingScannedItem o2) {
                    return o1.getAssetName().compareTo(o2.getAssetName()) ;
                }
            });
        }
        return assetList;
    }

    @NonNull
    @Override
    public StockTakingNonScannableItemSummary.StockTakingNonScannableSummaryItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.stock_taking_non_scannable_summary_list_item,parent,false);
        return new StockTakingNonScannableItemSummary.StockTakingNonScannableSummaryItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull StockTakingNonScannableSummaryItemViewHolder holder, int position) {
        StockTakingScannedItem item = assets.get(position);
        String tagValue = item.getAssetTagValue();
        holder.assetTag.setText(tagValue);
        holder.assetName.setText(item.getAssetName());

        if(!item.isFound()){
            holder.assetTagValidity.setText(R.string.lost);
            holder.parentLayout.setBackgroundResource(R.color.cornerlightRed);
            holder.subLayout.setBackgroundResource(R.drawable.rounded_shape_red_fill);
            holder.isNonScannableAsset.setChecked(false);
        }else if(item.isFound()){
            holder.assetTagValidity.setText(R.string.valid);
            holder.subLayout.setBackgroundResource(R.drawable.rounded_shape_green_fill);
            holder.parentLayout.setBackgroundResource(R.color.cornerlightGreen);
            holder.isNonScannableAsset.setChecked(true);

        }
    }




    @Override
    public int getItemCount() {
        return assets.size();
    }





    public class StockTakingNonScannableSummaryItemViewHolder extends RecyclerView.ViewHolder{
        TextView assetTag;
        TextView assetTagValidity;

        CheckBox isNonScannableAsset;
        TextView assetName;
        LinearLayout parentLayout;
        ConstraintLayout subLayout;

        public StockTakingNonScannableSummaryItemViewHolder(@NonNull View itemView) {
            super(itemView);
            assetTag = itemView.findViewById(R.id.asset_tag_tv);
            assetName = itemView.findViewById(R.id.asset_name_tv);
            assetTagValidity = itemView.findViewById(R.id.asset_tag_validity_tv);
            parentLayout = itemView.findViewById(R.id.outline_layout);
            subLayout = itemView.findViewById(R.id.sub_layout);
            isNonScannableAsset =  (CheckBox)itemView.findViewById(R.id.checkBox);
            isNonScannableAsset.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {


                    boolean checked = ((CheckBox) v).isChecked();

                    StockTakingNonScannableItemSummary.StockTakingNonScannableSummaryItemViewHolder holder = new StockTakingNonScannableItemSummary.StockTakingNonScannableSummaryItemViewHolder(itemView);
                    for(StockTakingScannedItem asset : StockTakingNonScannableItemSummary.this.assets){
                        if(asset.getAssetTagValue().equals(holder.assetTag.getText().toString())){
                            asset.setChecked(checked);
                            asset.setFound(checked);
                        }
                    }



                    // Check which checkbox was clicked
                    switch(v.getId()) {
                        case R.id.checkBox:
                            if (checked){
                                holder.subLayout.setBackgroundResource(R.drawable.rounded_shape_green_fill);
                                holder.parentLayout.setBackgroundResource(R.color.cornerlightGreen);
                                holder.assetTagValidity.setText(R.string.valid);
                            }else{
                                holder.subLayout.setBackgroundResource(R.drawable.rounded_shape_red_fill);
                                holder.parentLayout.setBackgroundResource(R.color.cornerlightRed);
                                holder.assetTagValidity.setText(R.string.lost);
                            }
                            break;

                    }
                }
            });
        }
    }

}
