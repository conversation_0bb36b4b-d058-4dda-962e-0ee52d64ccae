package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import com.example.chaayosStockTake.Exceptions.SCMError;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.toptoche.searchablespinnerlibrary.SearchableSpinner;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class Login extends AppCompatActivity implements AdapterView.OnItemSelectedListener {
    private Button loginButton;
    private EditText userId, password;
    private Spinner unitSpinner;

    private SearchableSpinner searchableSpinner;
    private Retrofit retrofit = null;
    private Integer selectedUnitCode;
    private String selectedUnitCategory;
    private UnitsApiService unitsApiService;
    private String sessionKey;
    private String jwtToken;
    private String name;
    private ProgressBar progressBar;

    private Map<Integer, Unit> unitMap = new HashMap<>();

    private String unitName;

    private static final int MY_PERMISSION_REQUEST_CODE = 1011;

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        loginButton = findViewById(R.id.login);
        userId = findViewById(R.id.username);
        password = findViewById(R.id.password);
        unitSpinner = findViewById(R.id.unitSpinner);
        searchableSpinner = findViewById(R.id.searchableSpinnerUnit);
        searchableSpinner.setTitle("Select Unit");
        progressBar = findViewById(R.id.progressBar);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPushNotificationsPermission();
        }
        //getting units for spinner here

        assert getSupportActionBar() != null;
        getSupportActionBar().hide();


        userId.addTextChangedListener(new TextWatcher() {
                                          @Override
                                          public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                                          }

                                          @Override
                                          public void onTextChanged(CharSequence s, int start, int before, int count) {
                                          }

                                          @Override
                                          public void afterTextChanged(Editable s) {
                                              if(s.toString().length() > 6){
                                                  s.clear();
                                              }
                                              if(s.toString().compareTo("")!=0 && s.toString().length() == 6){
                                                  connectAndGetApiData(Integer.parseInt(s.toString()));
                                              }
                                          }
                                      }
        );

        loginButton.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if (userId.getText().toString().equals("")) {
                    userId.requestFocus();
                    return;
                }
                if (password.getText().toString().equals("")) {
                    password.requestFocus();
                    return;
                }
                if (Objects.isNull(selectedUnitCode)) {
                    Toast.makeText(getApplicationContext(), "Please Select An Unit..!",Toast.LENGTH_SHORT ).show();
                    return;
                }
                loginButton.setEnabled(false);
                progressBar.setVisibility(View.VISIBLE);
                String etUserId = userId.getText().toString();
                int userIdValue = Integer.parseInt(etUserId);
                String etPasscode = password.getText().toString();
                //int passcode = Integer.parseInt(etPasscode);
                int terminalId = 0;
                String application = "SCM_SERVICE";
                LoginRequest loginDetails = new LoginRequest(userIdValue, etPasscode, selectedUnitCode, terminalId, application);
                UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
                Call loginCall = unitsApiService.getLoginDetails(loginDetails);

                loginCall.enqueue(new Callback() {
                    @Override
                    public void onResponse(Call call, Response response) {
                        if (response.body() == null) {
                            Toast.makeText(getApplicationContext(), "Wrong Credentials", Toast.LENGTH_SHORT).show();
                            loginButton.setEnabled(true);
                            progressBar.setVisibility(View.INVISIBLE);
                            return;
                        }
                        Log.e("TAG", "response 33: " + new Gson().toJson(response.body()));
                        String resp = new Gson().toJson(response.body());
                        Integer unitId = null;
                        SharedPreferences pref = getSharedPreferences("ActivityPREF", Context.MODE_PRIVATE);
                        SharedPreferences.Editor edt = pref.edit();
                        try {
                            JSONObject jsonObject = new JSONObject(resp);
                            sessionKey = jsonObject.get("sessionKeyId").toString();
                            jwtToken = jsonObject.get("jwtToken").toString();
                            JSONObject user = (JSONObject) jsonObject.get("user");
                            name = user.get("name").toString();
                            unitId = Double.valueOf((double)jsonObject.get("unitId")).intValue();
                            unitName = unitMap.get(Double.valueOf((Double) jsonObject.get("unitId")).intValue()).getName();
                            String aclDataString = jsonObject.get("acl").toString();
                            Map<String, Map<String, Boolean>> aclData = new HashMap<>();
                            Type type = new TypeToken<Map<String, Map<String, Boolean>>>(){}.getType();
                            aclData = UtilClass.gson.fromJson(aclDataString, type);
                            UtilClass.setAcl(aclData);
                            edt.putString("acl", aclDataString);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        Toast.makeText(getApplicationContext(), "Login Successful", Toast.LENGTH_SHORT).show();
                        edt.putBoolean("activity_executed", true);
                        edt.putInt("unitId",unitId);
                        edt.putInt("id", userIdValue);
                        edt.putString("name", name);
                        edt.putString("jwtToken", jwtToken);
                        edt.putString("unitName", unitName);
                        edt.apply();
                        getUnitsForStockTakeThroughApp(unitId);
                    }

                    @Override
                    public void onFailure(Call call, Throwable t) {
                        progressBar.setVisibility(View.INVISIBLE);
                        loginButton.setEnabled(true);
                        Toast.makeText(getApplicationContext(), "Check internet connection!", Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });

    }

    private void getUnitsForStockTakeThroughApp(Integer unitId) {
        progressBar.setVisibility(View.VISIBLE);
        Retrofit retrofit = UtilClass.getRetrofitScm();
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<List<Integer>> getUnitsForStockTake = unitsApiService.getUnitsForStockTakeThroughApp(token);
        getUnitsForStockTake.enqueue(new Callback<List<Integer>>() {
            @Override
            public void onResponse(Call<List<Integer>> call, Response<List<Integer>> response) {
                if (!response.isSuccessful()) {
                    if (Objects.nonNull(response.errorBody())) {
                        try {
                            String errorBodyString = response.errorBody().string();
                            SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                            if (Objects.nonNull(scmError) && (Objects.nonNull(scmError.getErrorMessage())  || Objects.nonNull(scmError.getErrorMsg()))) {
                                Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                                AlertDialog alertDialog = UtilClass.utilClass.getAlertDialog(Login.this, "ERROR",
                                        Objects.nonNull(scmError.getErrorMessage()) ? scmError.getErrorMessage() : scmError.getErrorMsg());
                                alertDialog.show();
                                progressBar.setVisibility(View.INVISIBLE);
                                return;
                            }
                        } catch (Exception e) {
                            Toast.makeText(getApplicationContext(), "API Response is Not Successful while getUnitsForStockTakeThroughApp..!", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Toast.makeText(getApplicationContext(), "Error Occurred while getUnitsForStockTakeThroughApp..!", Toast.LENGTH_SHORT).show();
                    }
                    progressBar.setVisibility(View.INVISIBLE);
                    return;
                }
                if (response.body() == null) {
                    Toast.makeText(Login.this, "Null response on getUnitsForStockTakeThroughApp Call", Toast.LENGTH_SHORT).show();
                    progressBar.setVisibility(View.INVISIBLE);
                    return;
                }
                List<Integer> unitsEnabledForStockTakeThroughApp = response.body();
                if (Objects.isNull(unitsEnabledForStockTakeThroughApp)) {
                    unitsEnabledForStockTakeThroughApp = new ArrayList<>();
                }
                SharedPreferences pref = getSharedPreferences("ActivityPREF", Context.MODE_PRIVATE);
                SharedPreferences.Editor edt = pref.edit();
                edt.putBoolean("unitEnabledForStockTakeThroughApp", unitsEnabledForStockTakeThroughApp.contains(unitId));
                edt.apply();
                UtilClass.setUnitEnabledForStockTakeThroughApp(unitsEnabledForStockTakeThroughApp.contains(unitId));
                progressBar.setVisibility(View.INVISIBLE);
                startActivity(new Intent(getApplicationContext(), Events.class));
                finish();
            }

            @Override
            public void onFailure(Call<List<Integer>> call, Throwable t) {
                progressBar.setVisibility(View.INVISIBLE);
                Toast.makeText(Login.this, " on Failure getUnitsForStockTakeThroughApp Call..!", Toast.LENGTH_SHORT).show();
            }
        });

    }

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    private void requestPushNotificationsPermission() {
        if (ActivityCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(getApplicationContext(), "Please Give All Permissions..!", Toast.LENGTH_SHORT).show();
            ActivityCompat.requestPermissions(this, new String[]{android.Manifest.permission.POST_NOTIFICATIONS}, MY_PERMISSION_REQUEST_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == MY_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {

            } else {
                Toast.makeText(this, "Permission not granted. Please grant the permission.", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        loginButton.setEnabled(true);
        progressBar.setVisibility(View.INVISIBLE);
    }

    public void connectAndGetApiData(Integer employeeId) {
        Log.d("empl", String.valueOf(employeeId));
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();
        if (retrofit == null) {
            retrofit = new Retrofit.Builder().baseUrl(BuildConfig.BASE)
                    .client(okHttpClient)
                    .addConverterFactory(GsonConverterFactory.create())
                    .build();
        }

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Map<String, Object> request = new HashMap<>();
        request.put("employeeId" , employeeId);
        request.put("onlyActive",true);
        Call<List<Unit>> call = unitsApiService.getAllUnits(request);

        call.enqueue(new Callback<List<Unit>>() {
            @Override
            public void onResponse(Call<List<Unit>> call, Response<List<Unit>> response) {
                if (response.code() == 401) {
                    return;
                }
                List<Unit> unitsList = response.body();
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    unitsList.sort(new Comparator<Unit>() {
                        @Override
                        public int compare(Unit o1, Unit o2) {
                            return o1.getName().compareTo(o2.getName());
                        }
                    });
                }
                String[] units = new String[unitsList.size()];

                SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
                SharedPreferences.Editor edt = pref.edit();
                edt.putString("unitsList", UtilClass.gson.toJson(unitsList));
                edt.apply();

                for (int i = 0; i < unitsList.size(); i++) {
                    unitMap.put(unitsList.get(i).getId(), unitsList.get(i));
                    units[i] = unitsList.get(i).getReferenceName();
                }
                ArrayAdapter<Unit> spinnerArrayAdapter = new ArrayAdapter<Unit>(Login.this, android.R.layout.simple_spinner_item, unitsList);
                unitSpinner.setAdapter(spinnerArrayAdapter);
                unitSpinner.setOnItemSelectedListener(Login.this);
                unitSpinner.setVisibility(View.GONE);

                searchableSpinner.setAdapter(spinnerArrayAdapter);
                searchableSpinner.setTitle("Select Unit");
                searchableSpinner.setPositiveButton("OK");
                searchableSpinner.setOnItemSelectedListener(Login.this);
            }

            @Override
            public void onFailure(Call<List<Unit>> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Check internet connection!", Toast.LENGTH_LONG).show();
            }

        });

    }




    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        closeKeyboard();
        Unit selectedUnit = (Unit) parent.getSelectedItem();
        selectedUnitCode = selectedUnit.getId();
        selectedUnitCategory = selectedUnit.getCategory();
        SharedPreferences pref = getSharedPreferences("ActivityPREF", Context.MODE_PRIVATE);
        SharedPreferences.Editor edt = pref.edit();
        edt.putString("unitCategory", selectedUnitCategory);
        edt.apply();
        //Toast.makeText(parent.getContext(),selectedUnitCode.toString(),Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    private void closeKeyboard() {
        View view = this.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }
}
