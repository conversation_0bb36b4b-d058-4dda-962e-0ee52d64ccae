package com.example.chaayosStockTake;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.domain.Pair;
import com.example.chaayosStockTake.domain.SubCategoryFlattenData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class MultipleButtonsAdapter extends RecyclerView.Adapter<ButtonViewHolder>{
    private List<SubCategoryFlattenData> buttonDataList;
    private final Context context;

    private Map<String, Pair<Integer, Integer>> assetCountBySubCategory = new HashMap<>();

    public MultipleButtonsAdapter(List<SubCategoryFlattenData> buttonDataList, Context context, Map<String, Pair<Integer, Integer>> assetCountBySubCategory) {
        this.buttonDataList = buttonDataList;
        this.context = context;
        this.assetCountBySubCategory = assetCountBySubCategory;
    }

    @Override
    public ButtonViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.day_close_subcategory_button, parent, false);
        return new ButtonViewHolder(view, this.context);
    }

    @Override
    public void onBindViewHolder(ButtonViewHolder holder, int position) {
        SubCategoryFlattenData flattenData = buttonDataList.get(position);
        holder.setSubCategory(flattenData.getSubCategoryName());
        StringBuilder header = new StringBuilder(Objects.nonNull(flattenData.getSubCategoryName()) ? flattenData.getSubCategoryName() : "-");
        if (Objects.nonNull(assetCountBySubCategory) && Objects.nonNull(assetCountBySubCategory.get(flattenData.getSubCategoryName()))) {
            Pair<Integer, Integer> count = assetCountBySubCategory.get(flattenData.getSubCategoryName());
            if (Objects.nonNull(count)) {
                header.append(" ( ").append(count.getKey()).append(" / ").append(count.getValue()).append(" )");
            }
        }
        holder.getCreateBy().setText(Objects.nonNull(flattenData.getCompletedBy()) ? flattenData.getCompletedBy() : "-");
        holder.getSubCategoryName().setText(header.toString());
        String subEventStatus = Objects.nonNull(flattenData.getStatus()) ? flattenData.getStatus() : "Not Started";
        holder.getSubEventStatus().setText(subEventStatus);
        if (subEventStatus.equalsIgnoreCase("Not Started")) {
            holder.getConstraintLayout().setBackgroundResource(R.drawable.rounded_shape_red_fill);
            holder.getStartButton().setText("START");
            holder.getStartButton().setVisibility(View.VISIBLE);
            holder.getSubEventStatus().setVisibility(View.INVISIBLE);
        } else if (subEventStatus.equalsIgnoreCase("IN_PROCESS")) {
            holder.getConstraintLayout().setBackgroundResource(R.drawable.rounded_shape_yellow_fill);
        } else {
            holder.getConstraintLayout().setBackgroundResource(R.drawable.rounded_shape_green_fill);
        }
    }

    @Override
    public int getItemCount() {
        return buttonDataList.size();
    }
}
