package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */

import com.example.chaayosStockTake.domain.AssetSwitch;
import com.example.chaayosStockTake.domain.StockTakeDayCloseSaveRequest;
import com.example.chaayosStockTake.domain.StockTakeInitResponse;
import com.example.chaayosStockTake.domain.StockTakeSumoDayCloseEventDTO;
import com.example.chaayosStockTake.domain.StockTakeSumoDayCloseProductsDTO;
import com.example.chaayosStockTake.domain.SwitchAssetDefinition;
import com.example.chaayosStockTake.domain.UnitEmployeesData;

import java.util.List;
import java.util.Map;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.Headers;
import retrofit2.http.Multi<PERSON>t;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Part;
import retrofit2.http.Query;

public interface UnitsApiService {

/*    String BASE = "http://**************:8089/master-service/rest/v1/";
    String SCM_BASE = "http://**************:9090/scm-service/rest/v1/";*/

    /*@GET("unit-metadata/all-units-list")
    Call<List<Unit>> getAllUnits();
*/
    @POST("user-management/user/units")
    Call<List<Unit>> getAllUnits(@Body Map<String, Object> request);

    @POST("users/login")
    Call<Object> getLoginDetails(@Body LoginRequest loginDetails);

    @Headers("Content-Type: application/json")
    @POST("asset-management/stock-event")
    Call<StockTakeInitResponse> initiateNewStockEvent(@Header("auth") String jwtToken, @Body StockTakingRequest request);

    @GET("asset-management/stock-event-unit")
    Call<StockTakeInitResponse> getStockTakingEvents(@Header("auth") String jwtToken, @Query("unitId") Integer unitId,
                                                     @Query("eventStatus") String eventStatus);

    @Headers("Content-Type: application/json")
    @PUT("asset-management/stock-event")
    Call<StockTakingRequest> abandonEvent(@Header("auth") String jwtToken, @Body StockTakingRequest request);

    @Headers("Content-Type: application/json")
    @POST("asset-management/stock-event-verification-creation")
    Call<StockTakingEvent> verifyAndCreateEventAssetMapping(@Header("auth") String jwtToken, @Body StockTakingEvent stockTakingEvent);

    @Headers("Content-Type: application/json")
    @POST("asset-management/stock-event-pause-submit")
    Call<Boolean> pauseSubmitStockEvent(@Header("auth") String jwtToken, @Body StockTakingEvent stockTakingEvent , @Query("isSubmit") Boolean isSubmit);

    @GET("asset-management/stock-take-list")
    Call<List<String>> getStockTakeList(@Header("auth") String jwtToken);

    @GET("user-management/user-role")
    Call<Map<String, String>> getAuditors(@Header("auth") String token, @Query("role") String role);

    @GET("asset-management/asset-tagValue")
    Call<Object> getAssetDetails(@Header("auth") String token, @Query("tagValue") String tagValue);

    @GET("asset-management/stock-take-list-sub")
    Call<Object> getStockTakeSubList(@Header("auth") String token);

    @POST("user-management/users/unit")
    Call<Object> getEmployeesForUnit(@Header("auth") String token, @Body Integer unitId);

    @Multipart
    @POST("asset-management/save-excess-asset-found")
    Call<Object> saveExtraItemData(@Header("auth") String token , @Part MultipartBody.Part file ,
                                   @Part("comment") String comment , @Part("eventId") Integer eventId ,
                                   @Part("productName") String productname);

    @POST("asset-management/fa-stock-take-report")
    Call<Boolean> generateReport(@Header("auth") String token , @Query("eventId") Integer eventId);


    @Headers("Content-Type: application/json")
    @POST("asset-management/child-stock-event")
    Call<StockTakingRequest> initiateChildEvent(@Header("auth") String jwtToken, @Body StockTakingRequest stockTakingChildRequest);

    @Headers("Content-Type: application/json")
    @PUT("asset-management/update-event-device-info")
    Call<Boolean> updateEventDeviceInfo(@Header("auth") String token, @Query("deviceInfo")String deviceInfo,@Query("eventId")Integer eventId);

    @Headers("Content-Type: application/json")
    @GET("employee-management/get-unit-employees")
    Call<UnitEmployeesData> getUnitEmployees(@Header("auth") String jwtToken, @Query("unitId") Integer unitId);

    @Headers("Content-Type: application/json")
    @GET("switch-asset/get-valid-asset")
    Call<SwitchAssetDefinition> getAssetDefinition(@Header("auth") String jwtToken, @Query("unitId") Integer unitId,
                                                   @Query("tagValue") String tagValue);

    @Multipart
    @POST("switch-asset/upload-asset-image")
    Call<Integer> uploadSwitchAssetImage(@Header("auth") String token , @Part MultipartBody.Part file,
                                        @Part("type") RequestBody type, @Part("mimeType") RequestBody mimeType,
                                        @Part("docType") RequestBody docType, @Part("userId") RequestBody userId, @Query("isNewAsset") Boolean isNewAsset);

    @GET("switch-asset/send-otp")
    Call<Boolean> validateOtpCall(@Header("auth")String token,@Query("userId") Integer userId);

    @POST("switch-asset/start-switch-asset-process")
    Call<Boolean> startSwitchAssetProcess(@Header("auth")String token,@Body AssetSwitch switchAssetBody);

    @POST("asset-management/save-invalid-tag")
    Call<Object> saveInvalidTag(@Header("auth") String token, @Query("eventId") Integer eventId, @Query("tagValue") String tagValue);

    @GET("asset-management/get-stock-take-app-version")
    Call<String> getAppVersion(@Header("auth")String token);

    @POST("asset-management/get-save-lost-asset-data")
    Call<Object> getAndSaveLostAssetData(@Header("auth")String token, @Query("assetTagValue") String assetTagValue, @Query("eventId") Integer eventId);

    @POST("asset-management/submit-found-asset-event")
    Call<Boolean> submitLostFoundAsset(@Header("auth")String token, @Query("eventId") Integer eventId);

    @Headers("Content-Type: application/json")
    @GET("stock-management/get-units-for-stock-take")
    Call<List<Integer>> getUnitsForStockTakeThroughApp(@Header("auth") String token);

    @Headers("Content-Type: application/json")
    @POST("stock-management/checkForStockTakeSumoDayClose")
    Call<StockTakeSumoDayCloseEventDTO> checkForStockTakeSumoDayClose(@Header("auth")String token, @Query("unitId") Integer unitId, @Query("status") String status);

    @Headers("Content-Type: application/json")
    @POST("stock-management/startStockTakeSumoDayClose")
    Call<StockTakeSumoDayCloseEventDTO> startStockTakeSumoDayClose(@Header("auth")String token, @Body StockTakeSumoDayCloseEventDTO stockTakeSumoDayCloseEventDTO);

    @Headers("Content-Type: application/json")
    @GET("stock-management/get-latest-kettle-day-close")
    Call<Integer> getLatestKettleDayClose(@Header("auth")String token, @Query("unitId") Integer unitId);

    @Headers("Content-Type: application/json")
    @POST("stock-management/update-sumo-day-close-device-info")
    Call<Boolean> updateSumoDayCloseDeviceInfo(@Header("auth") String token, @Query("deviceInfo")String deviceInfo, @Query("eventId")Integer eventId);

    @Headers("Content-Type: application/json")
    @POST("stock-management/cancel-stock-take-sumo-day-close-event")
    Call<Boolean> cancelStockTakeSumoDayCloseEvent(@Header("auth") String token,@Query("eventId") Integer eventId);

    @Headers("Content-Type: application/json")
    @POST("stock-management/save-submit-day-close-products")
    Call<List<StockTakeSumoDayCloseProductsDTO>> saveSubmitDayCloseProducts(@Header("auth") String token, @Body StockTakeDayCloseSaveRequest stockTakeDayCloseSaveRequest,
                                                                            @Query("eventId") Integer eventId, @Query("isSubmit") boolean isSubmit,
                                                                            @Query("unitId") Integer unitId,@Query("deviceInfo") String deviceInfo);
}
