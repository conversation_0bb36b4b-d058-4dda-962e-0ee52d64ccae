package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ExpandableListAdapter;
import android.widget.ExpandableListView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.example.chaayosStockTake.util.UtilClass;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class StockTakingEventSummary extends AppCompatActivity implements Serializable, View.OnClickListener, AdapterView.OnItemSelectedListener {

    private StockTakingEvent stockTakingEventResponse;
    private int lostAssetsCount = 0;
    private int lostAssetsCountDup = 0;
    private int markedAsLostCount = 0;
    private AlertDialog.Builder pleaseWaitBuilder = null;
    private AlertDialog pleaseWait;
    private View v;

    private ExpandableListView expandableListViewFound;
    private ExpandableListView expandableListViewLost;
    private ExpandableListAdapter expandableListAdapterFound;
    private ExpandableListAdapter expandableListAdapterLost;
    private List<String> expandableTitleList;

     private HashMap<String, List<StockTakingScannedItem>> expandableDetailListFound;
     private HashMap<String, List<StockTakingScannedItem>> expandableDetailListLost;

     private Map<String,List<StockTakingScannedItem>> assetCategoryMap;


     private List<StockTakingScannedItem> assetsList;

     private List<StockTakingScannedItem> extraScannedItems;

    private List<String> dropDownListFilter = UtilClass.utilClass.getFilterTypes();

    private Spinner dropDownFilterSelection;

    private String currentSelectedFilter = "CATEGORY";


    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_stock_taking_event_summary);
        RecyclerView assetsSummaryRecyclerView = findViewById(R.id.assets_summary_recycler_view);
        dropDownFilterSelection = findViewById(R.id.listTypeFilterSummary);
        Button scanAgainButton = findViewById(R.id.scan_again_btn);
        Button submitAnywayButton = findViewById(R.id.submit_anyway_btn);
        Button reportButton = findViewById(R.id.reportButton);
        reportButton.setOnClickListener(this);
        v = findViewById(R.id.scan_again_btn);
        pleaseWaitBuilder = getDialogProgressBar();
        pleaseWait = pleaseWaitBuilder.create();

        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();

        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Summary");
        if(Objects.nonNull( prefs.get("unitName"))){
            getSupportActionBar().setSubtitle((String) prefs.get("unitName"));
        }

        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>(StockTakingEventSummary.this, android.R.layout.simple_spinner_item, dropDownListFilter);
        dropDownFilterSelection.setAdapter(spinnerArrayAdapter);
        dropDownFilterSelection.setOnItemSelectedListener(StockTakingEventSummary.this);


        assetsSummaryRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        StockTakingSummaryListAdapter mAdapter = new StockTakingSummaryListAdapter(StockTakingEventSummary.this, new ArrayList<>());
        assetsSummaryRecyclerView.setAdapter(mAdapter);


        scanAgainButton.setOnClickListener(this);
        submitAnywayButton.setOnClickListener(this);



    }

    private void processItemsToDisplayByFilter() {
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        SharedPreferences previousPref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();
        Map<String,?> previousPrefs = previousPref.getAll();
        Log.d("sd",new Gson().toJson(prefs));
        Log.d("version",  pref.getString("version","0"));
        HashMap<String, List<StockTakingScannedItem>> expandableDetailListFoundTemp = new HashMap<>();
        HashMap<String, List<StockTakingScannedItem>> expandableDetailListLostTemp = new HashMap<>();
        if (Objects.nonNull(expandableDetailListFound)) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                expandableDetailListFound.forEach((key, stockTakingScannedItems) -> {
                    expandableDetailListFoundTemp.put(key, stockTakingScannedItems);
                });
            }
            expandableDetailListFound.clear();
        }
        if (Objects.nonNull(expandableDetailListLost)) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                expandableDetailListLost.forEach((key, stockTakingScannedItems) -> {
                    expandableDetailListLostTemp.put(key, stockTakingScannedItems);
                });
            }
            expandableDetailListLost.clear();
        }


        Intent intent = getIntent();
        if (previousPrefs.containsKey("StockTakingEventResponse")) {
            Gson gson = new Gson();
            String temp  = (String) previousPrefs.get("StockTakingEventResponse");
            stockTakingEventResponse = gson.fromJson(temp,StockTakingEvent.class);
        }
        Gson gson = new Gson();
        if(previousPrefs.containsKey("pendingAssetsJson")){
            String pendingAssetsJson = (String) previousPrefs.get("pendingAssetsJson");
            String scannedAssetsJson = (String) previousPrefs.get("scannedAssets");

            Type type = new TypeToken<HashMap<String,List<StockTakingScannedItem>>>(){}.getType();
            if (Objects.nonNull(expandableDetailListLost)) {
                expandableDetailListLost = expandableDetailListLostTemp;
            } else {
                expandableDetailListLost = gson.fromJson(pendingAssetsJson,type);
            }

            if (Objects.nonNull(expandableDetailListFound)) {
                expandableDetailListFound = expandableDetailListFoundTemp;
            } else {
                expandableDetailListFound = gson.fromJson(scannedAssetsJson, type);
            }

        }

        HashMap<String, List<StockTakingScannedItem>> expandableDetailListFoundDup = new HashMap<>();
        HashMap<String, List<StockTakingScannedItem>> expandableDetailListLostDup = new HashMap<>();

        for(List<StockTakingScannedItem> items : expandableDetailListLost.values()){
            for(StockTakingScannedItem item : items){
                if(!expandableDetailListLostDup.containsKey(getType(item))){
                    expandableDetailListLostDup.put(getType(item),new ArrayList<>());
                }
                expandableDetailListLostDup.get(getType(item)).add(item);
            }
        }

        expandableDetailListLost = expandableDetailListLostDup;

        for(List<StockTakingScannedItem> items : expandableDetailListFound.values()){
            for(StockTakingScannedItem item : items){
                if(!expandableDetailListFoundDup.containsKey(getType(item))){
                    expandableDetailListFoundDup.put(getType(item),new ArrayList<>());
                }
                expandableDetailListFoundDup.get(getType(item)).add(item);
            }
        }

        expandableDetailListFound = expandableDetailListFoundDup;

        if(intent.hasExtra("extraScannedItems")){
            String extraScannedItemsJson = intent.getStringExtra("extraScannedItems");
            Type type = new TypeToken<List<StockTakingScannedItem>>(){}.getType();
            extraScannedItems = gson.fromJson(extraScannedItemsJson,type);
        }
        List<StockTakingScannedItem> assets = stockTakingEventResponse.getStockTakingScannedItemsList();
        Integer nonScannableFound = 0;
        Integer nonScannableLost = 0;
        Integer scannableFound = 0;
        Integer scannableLost = 0;
        for(StockTakingScannedItem item : assets){
            if(Boolean.TRUE.equals(item.getNonScannable())){
                if(item.isFound()){
                    if(!expandableDetailListFound.containsKey(getType(item))){
                        expandableDetailListFound.put(getType(item),new ArrayList<>());
                        if(!expandableDetailListLost.containsKey(getType(item))){
                            expandableDetailListLost.put(getType(item),new ArrayList<>());
                        }
                    }
                    expandableDetailListFound.get(getType(item)).add(item);
                    nonScannableFound++;
                }else{
                    if(!expandableDetailListLost.containsKey(getType(item))){
                        expandableDetailListLost.put(getType(item),new ArrayList<>());
                        if(!expandableDetailListFound.containsKey(getType(item))){
                            expandableDetailListFound.put(getType(item),new ArrayList<>());
                        }
                    }
                    expandableDetailListLost.get(getType(item)).add(item);
                    nonScannableLost++;
                }
            }else{
                if(item.isFound()){
                    scannableFound++;
                }else{
                    scannableLost++;
                }
            }
        }

        TextView nonScannableFoundView = findViewById(R.id.nonscannableFound);
        nonScannableFoundView.setText(String.valueOf(nonScannableFound));

        TextView nonScannableLostView = findViewById(R.id.nonscannableLost);
        nonScannableLostView.setText(String.valueOf(nonScannableLost));

        TextView scannableFoundView = findViewById(R.id.scannableFound);
        scannableFoundView.setText(String.valueOf(scannableFound));

        TextView scannableLostView = findViewById(R.id.scannableLost);
        scannableLostView.setText(String.valueOf(scannableLost));


         mergeMaps();
    }


    private void mergeMaps(){
        lostAssetsCount = 0;
        List<StockTakingScannedItem> allAssets = new ArrayList<>();
        Map<String, List<StockTakingScannedItem>> mergedMap = new HashMap<>();



        for(List<StockTakingScannedItem> items : expandableDetailListLost.values()){
            allAssets.addAll(items);
            for(StockTakingScannedItem item : items){
                if(!mergedMap.containsKey(getType(item))){
                    mergedMap.put(getType(item),new ArrayList<>());
                }
                item.setFound(false);
                lostAssetsCount++;
                mergedMap.get(getType(item)).add(item);

            }
        }

        for(List<StockTakingScannedItem> items : expandableDetailListFound.values()){
            allAssets.addAll(items);
            for(StockTakingScannedItem item : items){
                if(!mergedMap.containsKey(getType(item))){
                    mergedMap.put(getType(item),new ArrayList<>());
                }
                item.setFound(true);
                mergedMap.get(getType(item)).add(item);

            }
        }


        for(Map.Entry<String,List<StockTakingScannedItem>> itemEntry  : mergedMap.entrySet()){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                try{
                    itemEntry.getValue().sort(new Comparator<>() {
                        @Override
                        public int compare(StockTakingScannedItem o1, StockTakingScannedItem o2) {
                            int flagComparison = Boolean.compare(o1.isFound(), o2.isFound());
                            if (flagComparison != 0) {
                                return flagComparison;
                            }
                            return o1.getAssetName().compareTo(o2.getAssetName());
                        }
                    });
                }catch (Exception e){
                    //
                }

                mergedMap.put(itemEntry.getKey(),itemEntry.getValue());
            }
        }

        assetCategoryMap = mergedMap;

        stockTakingEventResponse.setStockTakingScannedItemsList(allAssets);
        assetsList = allAssets;
        expandableListViewFound = (ExpandableListView) findViewById(R.id.expandableListViewFound);
        expandableTitleList = new ArrayList<String>(assetCategoryMap.keySet());
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            expandableTitleList.sort(new Comparator<String>() {
                @Override
                public int compare(String s, String t1) {
                    return s.compareTo(t1);
                }
            });
        }
//        boolean showMarkLost = false;
        SharedPreferences pref = getSharedPreferences("ActivityPREF", Context.MODE_PRIVATE);
        String unitCategory = pref.getString("unitCategory", "");
//        if (Objects.nonNull(unitCategory) && unitCategory.equalsIgnoreCase("CAFE")) {
//            showMarkLost = true;
//        }
        expandableListAdapterFound = new CustomizedExpandableListAdapter(this, expandableTitleList, assetCategoryMap,
                expandableDetailListFound,expandableDetailListLost,false,true);
        expandableListViewFound.setAdapter(expandableListAdapterFound);
    }

    private int getLostAssetsCount() {
        List<StockTakingScannedItem> list = stockTakingEventResponse.getStockTakingScannedItemsList();
        int n = list.size();
        int lostCount = 0;
        for (int i = 0; i < n; i++) {
            StockTakingScannedItem item = list.get(i);
            if (item.isExists() && !item.isFound())
                lostCount++;
        }
        return lostCount;
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.scan_again_btn) {
            onBackPressed();
        } else if (v.getId() == R.id.submit_anyway_btn) {
            if(!isConnected()){
                Snackbar.make(v,"Check internet connection!",Snackbar.LENGTH_SHORT).show();
                return;
            }
            markedAsLostCount = 0;
            for (StockTakingScannedItem item : assetsList) {
                if (Objects.nonNull(item.getMarkedAsLost()) && Boolean.TRUE.equals(item.getMarkedAsLost())) {
                    markedAsLostCount++;
                }
            }
            lostAssetsCountDup = lostAssetsCount;
            lostAssetsCountDup = lostAssetsCountDup - markedAsLostCount;
            if(lostAssetsCountDup > 0) {
                showAlert(true);
            } else {
                if (lostAssetsCountDup == 0 && markedAsLostCount == 0) {
                    submitEventAnyway();
                } else {
                    showAlert(false);
                }
            }
        }else if (v.getId() == R.id.reportButton){
            generateReport();
        }
    }

    private void showAlert(boolean isLostAlert) {
        SharedPreferences pref = getSharedPreferences("ActivityPREF", Context.MODE_PRIVATE);
        String unitCategory = pref.getString("unitCategory", "");
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(StockTakingEventSummary.this);
        alertDialog.setTitle("Submit Event");
        String msg = null;
        if (isLostAlert) {
            msg = lostAssetsCountDup + " assets are NOT FOUND.\nPlease Scan them";
            if (Objects.nonNull(unitCategory) && unitCategory.equalsIgnoreCase("CAFE")) {
                msg = msg + " or Mark them as LOST to submit\nMarked Lost - " + markedAsLostCount;
            }
        } else {
            msg = markedAsLostCount + " assets are MARKED AS LOST.\nAre you sure you want to submit..?";
            alertDialog.setNegativeButton("NO", (dialog, which) -> {
                dialog.cancel();
            });
        }
        alertDialog.setMessage(msg);
        alertDialog.setPositiveButton(isLostAlert ? "OK" : "YES", (dialog, which) -> {
                if (isLostAlert) {
                    dialog.cancel();
                } else {
                    submitEventAnyway();
                }
            });
        alertDialog.show();
    }



    private void submitEventAnyway() {
        pleaseWait.show();
        SharedPreferences sh = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sh.getString("jwtToken", null);

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();

        Retrofit retrofit = new Retrofit.Builder().baseUrl(BuildConfig.SCM_BASE)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        //stockTakingEventResponse.setStatus(EventAssetMappingStatus.CREATE);
        stockTakingEventResponse.setStockTakingScannedItemsList(assetsList);
        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();
        if (prefs.containsKey("eventResponse")) {
            try {
                StockTakingRequest stockTakingRequest = UtilClass.gson.fromJson((String) prefs.get("eventResponse"), StockTakingRequest.class);
                if (Objects.nonNull(stockTakingRequest)) {
                    stockTakingEventResponse.setParentId(stockTakingRequest.getParentId());
                    stockTakingEventResponse.setSubCategory(stockTakingRequest.getSubCategory());
                }
            } catch (Exception e) {
                Toast.makeText(getApplicationContext(), "Exception Occurred while Setting Parent Id", Toast.LENGTH_SHORT).show();
            }
        }
        Call<Boolean> submitCall = unitsApiService.pauseSubmitStockEvent(token, stockTakingEventResponse,true);
        Gson gson = new Gson();
        String json = gson.toJson(stockTakingEventResponse);
        submitCall.enqueue(new Callback<Boolean>() {
            @Override
            public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                if (!response.isSuccessful()) {
                    UtilClass.utilClass.parseScmError(response, StockTakingEventSummary.this, "submitting event");
                    pleaseWait.dismiss();
                    return;
                }
                if (response.body() == null) {
                    Snackbar.make(v, "Submit Anyway: Null response received.", Snackbar.LENGTH_SHORT).show();
                    pleaseWait.dismiss();
                    return;
                }
                    SharedPreferences sharedPreferences = getSharedPreferences("SavedStockTakingEventPREF", MODE_PRIVATE);
                    SharedPreferences.Editor editor = sharedPreferences.edit();
                    editor.clear();
                    editor.apply();

                    SharedPreferences sharedPreferences1 = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                    SharedPreferences.Editor myEditor = sharedPreferences1.edit();
                    myEditor.clear();
                    myEditor.apply();

                    pleaseWait.dismiss();
//                if(response.errorBody())
                    Toast.makeText(StockTakingEventSummary.this, "Event submitted successfully!", Toast.LENGTH_LONG).show();
                    Intent intent = new Intent(StockTakingEventSummary.this, StockTaking.class);
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    startActivity(intent);
                    finish();
            }

            @Override
            public void onFailure(Call<Boolean> call, Throwable t) {
                Snackbar.make(v, "Submit Anyway: Some error Occurred", Snackbar.LENGTH_SHORT).show();
                pleaseWait.dismiss();
            }
        });
    }

    private void generateReport(){
        SharedPreferences sh = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sh.getString("jwtToken", null);

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();

        Retrofit retrofit = new Retrofit.Builder().baseUrl(BuildConfig.SCM_BASE)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        stockTakingEventResponse.setStockTakingScannedItemsList(assetsList);
        Call<Boolean> reportCall = unitsApiService.generateReport(token,stockTakingEventResponse.getEventId());
        Gson gson = new Gson();
        String json = gson.toJson(stockTakingEventResponse);
        reportCall.enqueue(new Callback<Boolean>() {
            @Override
            public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                if (response.body() == null) {
                    Snackbar.make(v, "Couldn't Generate Report", Snackbar.LENGTH_SHORT).show();
                }else{
                    Snackbar.make(v, "SuccessFully Generated Report", Snackbar.LENGTH_SHORT).show();
                }
                pleaseWait.dismiss();
            }
            @Override
            public void onFailure(Call<Boolean> call, Throwable t) {
                Snackbar.make(v, "Couldn't Generate Report", Snackbar.LENGTH_SHORT).show();
                pleaseWait.dismiss();
            }
        });

    }

    private boolean isConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        return connectivityManager.getActiveNetworkInfo() != null && connectivityManager.getActiveNetworkInfo().isConnected();
    }


    public AlertDialog.Builder getDialogProgressBar() {

        if (pleaseWaitBuilder == null) {
            pleaseWaitBuilder = new AlertDialog.Builder(this);

            pleaseWaitBuilder.setTitle("Please Wait...");

            final ProgressBar progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(true);
            pleaseWaitBuilder.setView(progressBar);
        }
        return pleaseWaitBuilder;
    }       // AlertDialog


    private String getType(StockTakingScannedItem scannedItem1) {
        if (Objects.nonNull(currentSelectedFilter)) {
            if (currentSelectedFilter.equalsIgnoreCase("CATEGORY")) {
                return scannedItem1.getSubCategory();
            } else if (currentSelectedFilter.equalsIgnoreCase("PRODUCT")) {
                return scannedItem1.getProductName();
            } else {
                return scannedItem1.getSkuName();
            }
        }
        return scannedItem1.getSubCategory();
    }
    @Override
    public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
        currentSelectedFilter = dropDownListFilter.get(i);
        processItemsToDisplayByFilter();
    }

    @Override
    public void onNothingSelected(AdapterView<?> adapterView) {

    }
}
