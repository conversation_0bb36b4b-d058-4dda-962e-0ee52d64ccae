package com.example.chaayosStockTake;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.util.UtilClass;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class NonScannableStockEventSummary extends AppCompatActivity implements Serializable, View.OnClickListener {
    private StockTakingRequest stockTakingEventResponse;
    private int lostAssetsCount;
    private AlertDialog.Builder pleaseWaitBuilder = null;
    private AlertDialog pleaseWait;
    private View v;

    private List<StockTakingScannedItem> assetList;

    private StockTakingNonScannableItemSummary mAdapter;

    private RecyclerView assetsSummaryRecyclerView;

    private TextView nonScannableAssetsView;

    @SuppressLint("RestrictedApi")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_stock_taking_non_scannable_summary);
        assetsSummaryRecyclerView = findViewById(R.id.assets_summary_recycler_view);
        Button pauseBtn = findViewById(R.id.pause_btn);
        Button submitAnywayButton = findViewById(R.id.submit_anyway_btn);
        v = findViewById(R.id.pause_btn);
       pleaseWaitBuilder = getDialogProgressBar();
        pleaseWait = pleaseWaitBuilder.create();

        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();

        SharedPreferences activityPREF = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Map<String,?> activityPrefs = activityPREF.getAll();



        Intent intent = getIntent();

        String assetListJson = (String) prefs.get("AssetList");

        Type type = new TypeToken<List<StockTakingScannedItem>>(){}.getType();
        assetList = new Gson().fromJson(assetListJson, type);
        Gson gson = new Gson();

        if (intent.hasExtra("eventResponse")) {
            //stockTakingEventResponse = (StockTakingEvent) intent.getSerializableExtra("StockTakingEventResponse");
            String temp = intent.getStringExtra("eventResponse");
            stockTakingEventResponse = gson.fromJson(temp, StockTakingRequest.class);
            SharedPreferences.Editor edt = pref.edit();
            edt.putString("eventResponse",temp);
            edt.apply();
        }else{
           String responseJson = (String) prefs.get("eventResponse");
           stockTakingEventResponse = gson.fromJson(responseJson, StockTakingRequest.class);
           stockTakingEventResponse.setAvailableAssets(assetList);
        }
        stockTakingEventResponse.setAvailableAssets(sortAssets(stockTakingEventResponse.getAvailableAssets()));


        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowTitleEnabled(true);
        getSupportActionBar().setTitle("Countable" + "(" + stockTakingEventResponse.getSubType() + ")");
        getSupportActionBar().setWindowTitle(stockTakingEventResponse.getSubType());
        if(Objects.nonNull(activityPrefs.get("unitName"))){
            getSupportActionBar().setSubtitle((String) activityPrefs.get("unitName"));
        }

        if(stockTakingEventResponse.getAvailableAssets().isEmpty()){
            showSummaryScreen();
        }


        assetsSummaryRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        mAdapter = new StockTakingNonScannableItemSummary(NonScannableStockEventSummary.this, stockTakingEventResponse.getAvailableAssets());
        assetsSummaryRecyclerView.setAdapter(mAdapter);

        pauseBtn.setOnClickListener(this);
        submitAnywayButton.setOnClickListener(this);
        lostAssetsCount = getLostAssetsCount();

        Boolean emptyFlag = mAdapter.getEmptyNonScannableList();
        if (emptyFlag.equals(Boolean.TRUE)) {
            nonScannableAssetsView = findViewById(R.id.empty_non_scannable_list);
            nonScannableAssetsView.setVisibility(View.VISIBLE);
        }

    }

    private List<StockTakingScannedItem> sortAssets(List<StockTakingScannedItem> assetList){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                assetList.sort(new Comparator<StockTakingScannedItem>() {
                    @Override
                    public int compare(StockTakingScannedItem o1, StockTakingScannedItem o2) {
                        return o1.getAssetName().compareTo(o2.getAssetName()) ;
                    }
                });
            }
            return assetList;
    }

    private int getLostAssetsCount() {
        List<StockTakingScannedItem> list = stockTakingEventResponse.getAvailableAssets();
        int n = list.size();
        int lostCount = 0;
        for (int i = 0; i < n; i++) {
            StockTakingScannedItem item = list.get(i);
            if (Boolean.TRUE.equals(item.getNonScannable()) && !item.isFound())
                lostCount++;
        }
        return lostCount;
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    public void onBackPressed() {
        Intent intent = new Intent(this, StockTaking.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
        finish();
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.pause_btn) {
            SharedPreferences pref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
            SharedPreferences.Editor edt = pref.edit();
            Gson googleJson = new Gson();
            String assetListJson = googleJson.toJson(assetList);
            edt.putString("AssetList",assetListJson);
            edt.apply();
            UtilClass.utilClass.getToast(getApplicationContext(), "Saved Successfully..!").show();
        } else if (v.getId() == R.id.submit_anyway_btn) {
            if (!isConnected()) {
                Snackbar.make(v, "Check internet connection!", Snackbar.LENGTH_SHORT).show();
                return;
            }
            Integer lostCount = getLostAssetsCount();
            if (lostCount > 0) {
                showAlert(lostCount);
            } else {
                showSummaryScreen();
            }
            // submitEventAnyway();
        }
    }


    private void showAlert(Integer lostCount) {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(NonScannableStockEventSummary.this);
        alertDialog.setTitle("Non-Scannable");
        alertDialog.setMessage(lostCount + " assets are not Checked. Do you want to Proceed anyway?");
        alertDialog.setPositiveButton("CANCEL", (dialog, which) -> dialog.cancel());
        alertDialog.setNegativeButton("YES", (dialog, which) -> {
            //submitEventAnyway();
            dialog.cancel();
            showSummaryScreen();
        });
        alertDialog.show();
    }


    private void showSummaryScreen() {
        pleaseWait.show();
        List<StockTakingScannedItem> list = stockTakingEventResponse.getAvailableAssets();
        Map<Integer,Boolean> nonScannableFound = new HashMap<>();

        for(StockTakingScannedItem stockTakingScannedItem : list){
            if (Boolean.TRUE.equals(stockTakingScannedItem.getNonScannable())){
                nonScannableFound.put(stockTakingScannedItem.getAssetId(),stockTakingScannedItem.isFound());
            }
        }
        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();

        String assetListJson = (String) prefs.get("AssetList");

        Type type = new TypeToken<List<StockTakingScannedItem>>(){}.getType();
        List<StockTakingScannedItem> assetList = new Gson().fromJson(assetListJson, type);


        for(StockTakingScannedItem item : assetList){
            if(nonScannableFound.containsKey(item.getAssetId())){
                item.setFound(nonScannableFound.get(item.getAssetId()));
            }
        }


        Map<String,List<StockTakingScannedItem>> assetSubCategoryMap = new HashMap<>();
        for(StockTakingScannedItem scannedItem : assetList){
            if(!assetSubCategoryMap.containsKey(scannedItem.getSubCategory())){
                assetSubCategoryMap.put(scannedItem.getSubCategory(),new ArrayList<>());
            }
            if(!Boolean.TRUE.equals(scannedItem.getNonScannable())){
                assetSubCategoryMap.get(scannedItem.getSubCategory()).add(scannedItem);
            }
        }

        stockTakingEventResponse.setAvailableAssets(assetList);
        mAdapter = new StockTakingNonScannableItemSummary(NonScannableStockEventSummary.this, stockTakingEventResponse.getAvailableAssets());
        assetsSummaryRecyclerView.setAdapter(mAdapter);



        SharedPreferences.Editor edt = pref.edit();


        Intent intent = new Intent(NonScannableStockEventSummary.this, StockTakingScanner.class);


        Gson googleJson = new Gson();

        String assetResponse = googleJson.toJson(assetList);
        edt.putString("AssetList",assetResponse);
        edt.apply();



        String stockTakingEventResponseJSON = googleJson.toJson(stockTakingEventResponse, StockTakingRequest.class);
        //intent.putExtra("eventResponse", stockTakingEventResponseJSON);

        edt.putString("eventResponse",stockTakingEventResponseJSON);


        Intent currentIntent = getIntent();
      /*  if (currentIntent.hasExtra("initializedEventsList")) {
            intent.putExtra("initializedEventsList",
                    (ArrayList<String>) currentIntent.getSerializableExtra("initializedEventsList"));
        }*/
        if (currentIntent.hasExtra("eventId")) {
            intent.putExtra("eventId",currentIntent.getIntExtra("eventId", -1));
        }
        if(currentIntent.hasExtra("auditedByName") && currentIntent.hasExtra("auditedById")){
            intent.putExtra("auditedByName", currentIntent.getStringExtra("auditedByName"));
            intent.putExtra("auditedById",currentIntent.getIntExtra("auditedById",-1));
        }

        String subCategoryMapJson = googleJson.toJson(assetSubCategoryMap);
        edt.putString("subCategoryMap",subCategoryMapJson);
        /*intent.putExtra("subCategoryMap",subCategoryMapJson);*/
        edt.apply();

        pleaseWait.dismiss();

        startActivity(intent);
    }


    private boolean isConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        return connectivityManager.getActiveNetworkInfo() != null && connectivityManager.getActiveNetworkInfo().isConnected();
    }


    public AlertDialog.Builder getDialogProgressBar() {

        if (pleaseWaitBuilder == null) {
            pleaseWaitBuilder = new AlertDialog.Builder(this);

            pleaseWaitBuilder.setTitle("Please Wait...");

            final ProgressBar progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(false);
            pleaseWaitBuilder.setView(progressBar);
        }
        return pleaseWaitBuilder;
    }       // AlertDialog


}
