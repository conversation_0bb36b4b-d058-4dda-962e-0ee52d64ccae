package com.example.chaayosStockTake.Adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.R;
import com.example.chaayosStockTake.User;

import java.util.List;

public class LostFoundScannedListViewAdapter extends RecyclerView.Adapter<LostFoundScannedListViewAdapter.LostFoundScannedItemViewHolder>{

    List<User> lostFoundScannedAssets;

    public LostFoundScannedListViewAdapter(List<User> lostFoundScannedAssets) {
        this.lostFoundScannedAssets = lostFoundScannedAssets;
    }

    @NonNull
    @Override
    public LostFoundScannedItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.lost_found_scanned_list_item,parent,false);
        return new LostFoundScannedItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull LostFoundScannedItemViewHolder holder, int position) {
        User item = lostFoundScannedAssets.get(position);
        if (item != null) {
            holder.lostFoundAssetId.setText(item.getId().toString());
            holder.lostFoundAssetName.setText(item.getName());
            holder.lostFoundAssetTag.setText(item.getCode());
        }
    }

    @Override
    public int getItemCount() {
        return lostFoundScannedAssets.size();
    }

    public static class LostFoundScannedItemViewHolder extends RecyclerView.ViewHolder {

        TextView lostFoundAssetId;

        TextView lostFoundAssetName;

        TextView lostFoundAssetTag;
        public LostFoundScannedItemViewHolder(@NonNull View itemView) {
            super(itemView);
            lostFoundAssetId = (TextView) itemView.findViewById(R.id.lost_found_asset_id);
            lostFoundAssetName = (TextView) itemView.findViewById(R.id.lost_found_asset_name);
            lostFoundAssetTag = (TextView) itemView.findViewById(R.id.lost_found_asset_tag);
        }
    }
}
