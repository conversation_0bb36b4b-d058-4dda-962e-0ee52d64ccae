package com.example.chaayosStockTake.domain;

public class SubCategoryFlattenData {
    private String subCategoryName;
    private String completedBy;

    private String status;

    public SubCategoryFlattenData(String subCategoryName, String completedBy, String status) {
        this.subCategoryName = subCategoryName;
        this.completedBy = completedBy;
        this.status = status;
    }

    public String getSubCategoryName() {
        return subCategoryName;
    }

    public void setSubCategoryName(String subCategoryName) {
        this.subCategoryName = subCategoryName;
    }

    public String getCompletedBy() {
        return completedBy;
    }

    public void setCompletedBy(String completedBy) {
        this.completedBy = completedBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
