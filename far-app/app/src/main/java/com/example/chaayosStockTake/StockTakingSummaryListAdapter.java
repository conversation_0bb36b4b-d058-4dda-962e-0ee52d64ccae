package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import android.app.AlertDialog;
import android.content.Context;
import android.opengl.Visibility;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class StockTakingSummaryListAdapter extends RecyclerView.Adapter<StockTakingSummaryListAdapter.StockTakingSummaryItemViewHolder>  {

    public List<StockTakingScannedItem> assets;
    private Context mContext;

    public StockTakingSummaryListAdapter( Context mContext, List<StockTakingScannedItem> assets) {
        this.assets = assets;
        this.mContext = mContext;
    }

    @NonNull
    @Override
    public StockTakingSummaryItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.stock_taking_summary_list_item,parent,false);
        return new StockTakingSummaryItemViewHolder(view);
    }




    @Override
    public void onBindViewHolder(@NonNull StockTakingSummaryItemViewHolder holder, int position) {
        StockTakingScannedItem item = assets.get(position);
        String tagValue = item.getAssetTagValue();
        holder.assetTag.setText(tagValue);
        holder.assetName.setText(item.getAssetName());

        if(item.isExists() && !item.isFound()){
            holder.assetTagValidity.setText(R.string.lost);
            holder.parentLayout.setBackgroundResource(R.color.cornerlightRed);
            holder.subLayout.setBackgroundResource(R.drawable.rounded_shape_red_fill);
        }
        else if(item.isFound() && !item.isExists()){
            holder.assetTagValidity.setText(R.string.invalid);
            holder.parentLayout.setBackgroundResource(R.color.cornerlightRed);
            holder.subLayout.setBackgroundResource(R.drawable.rounded_shape_red_fill);
            holder.assetName.setText(tagValue);
            //holder.assetTag.setVisibility(View.INVISIBLE);
        }
        else{
            holder.assetTagValidity.setText(R.string.valid);
            holder.subLayout.setBackgroundResource(R.drawable.rounded_shape_green_fill);
            holder.parentLayout.setBackgroundResource(R.color.cornerlightGreen);
        }
    }

    @Override
    public int getItemCount() {
        return assets.size();
    }





    public class StockTakingSummaryItemViewHolder extends RecyclerView.ViewHolder{
        TextView assetTag;
        TextView assetTagValidity;
        TextView assetName;
        LinearLayout parentLayout;
        ConstraintLayout subLayout;

        public StockTakingSummaryItemViewHolder(@NonNull View itemView) {
            super(itemView);
            assetTag = itemView.findViewById(R.id.asset_tag_tv);
            assetName = itemView.findViewById(R.id.asset_name_tv);
            assetTagValidity = itemView.findViewById(R.id.asset_tag_validity_tv);
            parentLayout = itemView.findViewById(R.id.outline_layout);
            subLayout = itemView.findViewById(R.id.sub_layout);

        }
    }
}
