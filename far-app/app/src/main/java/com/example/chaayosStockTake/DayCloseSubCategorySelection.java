package com.example.chaayosStockTake;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.domain.Pair;
import com.example.chaayosStockTake.domain.SubCategoryFlattenData;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class DayCloseSubCategorySelection extends AppCompatActivity implements Serializable{

    private MultipleButtonsAdapter multipleButtonsAdapter;

    private RecyclerView buttonRecyclerView;

    private List<SubCategoryFlattenData> subCategoryFlattenData = new ArrayList<>();

    private Map<String, Pair<Integer, Integer>> assetCountBySubCategory = new HashMap<>();


    @SuppressLint("RestrictedApi")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        UtilClass utilClass = new UtilClass();
        setContentView(R.layout.day_close_sub_category_selection);
        buttonRecyclerView = findViewById(R.id.day_close_sub_category_selection_recycle_view);
        buttonRecyclerView.setVisibility(View.VISIBLE);
        AlertDialog builder = utilClass.getDialogProgressBar(DayCloseSubCategorySelection.this).create();
        builder.setCancelable(false);
        builder.show();

        SharedPreferences pref = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();

        SharedPreferences activityPREF = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        Map<String,?> activityPrefs = activityPREF.getAll();
        Type type = new TypeToken<List<SubCategoryFlattenData>>(){}.getType();
        subCategoryFlattenData = new Gson().fromJson((String) prefs.get("subCategoryFlattenData"), type);

        Type assetCountType = new TypeToken<Map<String, Pair<Integer, Integer>>>(){}.getType();
        assetCountBySubCategory = UtilClass.gson.fromJson((String) prefs.get("assetCountBySubCategory"), assetCountType);

        StockTakingRequest stockTakingEventResponseJSON = new Gson().fromJson((String) prefs.get("eventResponse"), StockTakingRequest.class);

        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowTitleEnabled(true);
        getSupportActionBar().setTitle("Select Sub Category Event");
        getSupportActionBar().setWindowTitle(stockTakingEventResponseJSON.getSubType());
        if(Objects.nonNull(activityPrefs.get("unitName"))){
            getSupportActionBar().setSubtitle((String) activityPrefs.get("unitName"));
        }

        multipleButtonsAdapter = new MultipleButtonsAdapter(subCategoryFlattenData, DayCloseSubCategorySelection.this, assetCountBySubCategory);
        buttonRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        buttonRecyclerView.setAdapter(multipleButtonsAdapter);
        builder.dismiss();
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
