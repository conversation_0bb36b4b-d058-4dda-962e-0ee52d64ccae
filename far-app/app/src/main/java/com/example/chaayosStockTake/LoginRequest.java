package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import com.google.gson.annotations.SerializedName;

public class LoginRequest {
    @SerializedName("userId")
    private Integer userId;
    @SerializedName("password")
    private String passcode;
    @SerializedName("unitId")
    private Integer unitId;
    @SerializedName("terminalId")
    private Integer terminalId;
    @SerializedName("application")
    private String application;

    public LoginRequest(Integer userId, String passcode, Integer unitId, Integer terminalId, String application) {
        this.userId = userId;
        this.passcode = passcode;
        this.unitId = unitId;
        this.terminalId = terminalId;
        this.application = application;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getPasscode() {
        return passcode;
    }

    public void setPasscode(String passcode) {
        this.passcode = passcode;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(Integer terminalId) {
        this.terminalId = terminalId;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }
}
