package com.example.chaayosStockTake.domain;

import com.example.chaayosStockTake.StockTakingRequest;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StockTakeInitResponse {

    protected List<StockTakingRequest> stockEventDefinition = new ArrayList<>();
    protected List<String> subTypeList = new ArrayList<>();

    @SerializedName("stockTakeStatusSubCategoryMap")
    protected Map<String, String> stockTakeStatusSubCategoryMap = new HashMap<>();

    public List<StockTakingRequest> getStockEventDefinition() {
        return stockEventDefinition;
    }

    public void setStockEventDefinition(List<StockTakingRequest> stockEventDefinition) {
        this.stockEventDefinition = stockEventDefinition;
    }

    public List<String> getSubTypeList() {
        return subTypeList;
    }

    public void setSubTypeList(List<String> subTypeList) {
        this.subTypeList = subTypeList;
    }

    public Map<String, String> getStockTakeStatusSubCategoryMap() {
        return stockTakeStatusSubCategoryMap;
    }

    public void setStockTakeStatusSubCategoryMap(Map<String, String> stockTakeStatusSubCategoryMap) {
        this.stockTakeStatusSubCategoryMap = stockTakeStatusSubCategoryMap;
    }
}
