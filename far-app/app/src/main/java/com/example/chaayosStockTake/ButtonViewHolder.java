package com.example.chaayosStockTake;

import static android.content.Context.MODE_PRIVATE;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.Exceptions.SCMError;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class ButtonViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener{

    private ConstraintLayout constraintLayout;
    private TextView subCategoryName;

    private TextView createBy;

    private TextView subEventStatus;

    private Button startButton;

    private Context context;

    private String subCategory;

    public ButtonViewHolder(View itemView, Context context) {
        super(itemView);
        constraintLayout = itemView.findViewById(R.id.sub_category_layout);
        subCategoryName = itemView.findViewById(R.id.sub_category_name);
        createBy = itemView.findViewById(R.id.sub_category_event_created_by);
        subEventStatus = itemView.findViewById(R.id.sub_category_event_status);
        startButton = itemView.findViewById(R.id.sub_category_event_start_button);
        constraintLayout.setVisibility(View.VISIBLE);
        subCategoryName.setVisibility(View.VISIBLE);
        createBy.setVisibility(View.VISIBLE);
        subEventStatus.setVisibility(View.VISIBLE);
        startButton.setOnClickListener(this);
        this.context = context;
    }

    public ConstraintLayout getConstraintLayout() {
        return constraintLayout;
    }

    public void setConstraintLayout(ConstraintLayout constraintLayout) {
        this.constraintLayout = constraintLayout;
    }

    public TextView getSubCategoryName() {
        return subCategoryName;
    }

    public void setSubCategoryName(TextView subCategoryName) {
        this.subCategoryName = subCategoryName;
    }

    public TextView getCreateBy() {
        return createBy;
    }

    public void setCreateBy(TextView createBy) {
        this.createBy = createBy;
    }

    public TextView getSubEventStatus() {
        return subEventStatus;
    }

    public void setSubEventStatus(TextView subEventStatus) {
        this.subEventStatus = subEventStatus;
    }

    public Button getStartButton() {
        return startButton;
    }

    public void setStartButton(Button startButton) {
        this.startButton = startButton;
    }

    @Override
    public void onClick(View view) {
        String msg = this.subCategory + " Event ";
        if (subEventStatus.getText().toString().equalsIgnoreCase("Not Started")) {
            AlertDialog.Builder alertDialog = new AlertDialog.Builder(context);
            alertDialog.setTitle("Are You Sure.?");
            alertDialog.setMessage("Do You Want to Initiate the " + this.subCategory + " event");
            alertDialog.setNegativeButton("NO", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.cancel();
                }
            });
            alertDialog.setPositiveButton("YES", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    AlertDialog progressDialog = UtilClass.utilClass.getDialogProgressBar(context).create();
                    progressDialog.show();
                    OkHttpClient okHttpClient = new OkHttpClient.Builder()
                            .connectTimeout(2, TimeUnit.MINUTES)
                            .readTimeout(2, TimeUnit.MINUTES)
                            .writeTimeout(2, TimeUnit.MINUTES)
                            .build();
                    Retrofit retrofit = new Retrofit.Builder()
                            .baseUrl(BuildConfig.SCM_BASE)
                            .addConverterFactory(GsonConverterFactory.create())
                            .client(okHttpClient)
                            .build();

                    UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
                    SharedPreferences pref = context.getSharedPreferences("ActivityPREF", MODE_PRIVATE);
                    String token = pref.getString("jwtToken", null);
                    SharedPreferences StockTakingPrevList = context.getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                    StockTakingRequest stockTakingChildRequest = new Gson().fromJson((String) StockTakingPrevList.getString("eventResponse", null), StockTakingRequest.class);
                    stockTakingChildRequest.setParentId(stockTakingChildRequest.getEventId());
                    stockTakingChildRequest.setSubCategory(getSubCategory());
                    Integer userId = pref.getInt("id",-1);
                    stockTakingChildRequest.getInitiatedBy().setId(userId);
                    stockTakingChildRequest.setDeviceInfo(UtilClass.getDeviceInfo());
                    Call<StockTakingRequest> subCategoryEventCall = unitsApiService.initiateChildEvent(token, stockTakingChildRequest);
                    subCategoryEventCall.enqueue(new Callback<StockTakingRequest>() {
                        @Override
                        public void onResponse(Call<StockTakingRequest> call, Response<StockTakingRequest> response) {
                            if (!response.isSuccessful()) {
                                if (Objects.nonNull(response.errorBody())) {
                                    try {
                                        String errorBodyString = response.errorBody().string();
                                        SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                                        if (Objects.nonNull(scmError) && Objects.nonNull(scmError.getErrorMessage())) {
                                            if (scmError.getErrorMessage().startsWith("Event")) {
                                                AlertDialog.Builder builder = new AlertDialog.Builder(context);
                                                builder.setTitle("Event Already Created..!")
                                                        .setMessage(scmError.getErrorMessage())
                                                        .setPositiveButton("OK", (dialog, which) -> {
                                                            dialog.cancel();
                                                        });

                                                AlertDialog dialog = builder.create();
                                                dialog.show();
                                            } else {
                                                Toast.makeText(getApplicationContext(), scmError.getErrorMessage(), Toast.LENGTH_SHORT).show();
                                            }
                                        }
                                    } catch (Exception e) {
                                        Toast.makeText(getApplicationContext(), "API Response is Not Successful", Toast.LENGTH_SHORT).show();
                                    }
                                } else {
                                    Toast.makeText(getApplicationContext(), "New event could not be created!", Toast.LENGTH_SHORT).show();
                                }
                                progressDialog.dismiss();
                                return;
                            }
                            if (response.body() == null) {
                                Toast.makeText(getApplicationContext(), "Child Event Response is null", Toast.LENGTH_SHORT).show();
                                return;
                            }
                            Gson gson = new Gson();
                            StockTakingRequest result = response.body();
                            Toast.makeText(getApplicationContext(), "Sub Category Event Created successfully!", Toast.LENGTH_SHORT).show();
                            SharedPreferences sharedPref = context.getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                            SharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                            sharedPrefEditor.putString("SavedChildStockTakingEvent", new Gson().toJson(result));
                            sharedPrefEditor.apply();
                            Map<String,List<StockTakingScannedItem>> scannedItemMap = new HashMap<>();
                            Map<String,List<StockTakingScannedItem>> pendingAssetMap = new HashMap<>();

                            List<StockTakingScannedItem> asssetList = result.getAvailableAssets();
                            for(StockTakingScannedItem item  : asssetList){
                                if(Boolean.TRUE.equals(item.getNonScannable())){
                                    continue;
                                }
                                if(Boolean.TRUE.equals(item.isFound())){
                                    if(!scannedItemMap.containsKey(item.getSubCategory())){
                                        scannedItemMap.put(item.getSubCategory(),new ArrayList<>());
                                    }
                                    scannedItemMap.get(item.getSubCategory()).add(item);
                                }else{
                                    if(!pendingAssetMap.containsKey(item.getSubCategory())){
                                        pendingAssetMap.put(item.getSubCategory(),new ArrayList<>());
                                    }
                                    pendingAssetMap.get(item.getSubCategory()).add(item);
                                }
                            }

                            String pendingAssetsJson = gson.toJson(pendingAssetMap);
                            String scannedAssetsJson = gson.toJson(scannedItemMap);

                            sharedPrefEditor.putString("pendingAssetsJson",pendingAssetsJson);
                            sharedPrefEditor.putString("scannedAssets",scannedAssetsJson);
                            if(Objects.nonNull(asssetList)){
                                String assetListJson = gson.toJson(asssetList);
                                sharedPrefEditor.putString("AssetList",assetListJson);
                                sharedPrefEditor.apply();
                            }
                            String initiatedEventJson = UtilClass.gson.toJson(result, StockTakingRequest.class);
                            sharedPrefEditor.putString("eventResponse",initiatedEventJson);
                            sharedPrefEditor.apply();
                            Intent intent = new Intent(context, NonScannableStockEventSummary.class);
                            progressDialog.dismiss();
                            context.startActivity(intent);
                        }

                        @Override
                        public void onFailure(Call<StockTakingRequest> call, Throwable t) {
                            Toast.makeText(getApplicationContext(), "Error Occurred While generating Child Event!", Toast.LENGTH_SHORT).show();
                            progressDialog.dismiss();
                        }
                    });
                }
            });

            AlertDialog dialog = alertDialog.create();
            dialog.show();
        } else if (subEventStatus.getText().toString().equalsIgnoreCase("IN_PROCESS")) {
            Toast.makeText(getApplicationContext(), msg + " is already Started By " + createBy.getText().toString(),
                    Toast.LENGTH_SHORT).show();
            return;
        } else {
            Toast.makeText(getApplicationContext(), msg + " is already Completed By " + createBy.getText().toString(),
                    Toast.LENGTH_SHORT).show();
            return;
        }
    }

    private Context getApplicationContext() {
        return this.context;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }
}
