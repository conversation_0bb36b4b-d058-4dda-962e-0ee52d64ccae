package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import java.util.ArrayList;

public class PendingReceiving extends AppCompatActivity implements View.OnClickListener{

    private ArrayList<String> PRList;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pending_receiving);

        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Pending/Receiving");

        PRList = new ArrayList<>();
        PRList.add("One");
        PRList.add("Two");
        PRList.add("Three");

        RecyclerView pendingReceivingItemsListRecyclerView = findViewById(R.id.pending_receiving_list_recyclerView);
        pendingReceivingItemsListRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        ScannedListAdapter mAdapter = new ScannedListAdapter(getApplicationContext(),PRList);
        pendingReceivingItemsListRecyclerView.setAdapter(mAdapter);
    }
    @Override
    public void onClick(final View view) {
        RecyclerView pendingReceivingRecyclerView = findViewById(R.id.pending_receiving_list_recyclerView);
        int itemPosition = pendingReceivingRecyclerView.getChildLayoutPosition(view);
        String item = PRList.get(itemPosition);
        Toast.makeText(getApplicationContext(), item, Toast.LENGTH_LONG).show();
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }
}
