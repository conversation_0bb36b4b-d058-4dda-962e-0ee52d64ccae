/*
 * Created By Shanmukh
 */

package com.example.chaayosStockTake.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockTakeSumoDayCloseEventDTO {
    private Integer stockTakeSumoDayCloseEventId;
    private Integer unitId;
    private Integer dayCloseEventId;

    private String stockTakeType;
    private String eventStatus;
    private Integer eventCreatedBy;
    private Long eventCreatedAt;
    private String deviceInfo;
    private String eventCreatedByName;

    private List<StockTakeSumoDayCloseProductsDTO> stockTakeSumoDayCloseProductsDTOS = new ArrayList<>(0);

    public Integer getStockTakeSumoDayCloseEventId() {
        return stockTakeSumoDayCloseEventId;
    }

    public void setStockTakeSumoDayCloseEventId(Integer stockTakeSumoDayCloseEventId) {
        this.stockTakeSumoDayCloseEventId = stockTakeSumoDayCloseEventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getDayCloseEventId() {
        return dayCloseEventId;
    }

    public void setDayCloseEventId(Integer dayCloseEventId) {
        this.dayCloseEventId = dayCloseEventId;
    }

    public String getStockTakeType() {
        return stockTakeType;
    }

    public void setStockTakeType(String stockTakeType) {
        this.stockTakeType = stockTakeType;
    }

    public String getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(String eventStatus) {
        this.eventStatus = eventStatus;
    }

    public Integer getEventCreatedBy() {
        return eventCreatedBy;
    }

    public void setEventCreatedBy(Integer eventCreatedBy) {
        this.eventCreatedBy = eventCreatedBy;
    }


    public Long getEventCreatedAt() {
        return eventCreatedAt;
    }

    public void setEventCreatedAt(Long eventCreatedAt) {
        this.eventCreatedAt = eventCreatedAt;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getEventCreatedByName() {
        return eventCreatedByName;
    }

    public void setEventCreatedByName(String eventCreatedByName) {
        this.eventCreatedByName = eventCreatedByName;
    }

    public List<StockTakeSumoDayCloseProductsDTO> getStockTakeSumoDayCloseProductsDTOS() {
        return stockTakeSumoDayCloseProductsDTOS;
    }

    public void setStockTakeSumoDayCloseProductsDTOS(List<StockTakeSumoDayCloseProductsDTO> stockTakeSumoDayCloseProductsDTOS) {
        this.stockTakeSumoDayCloseProductsDTOS = stockTakeSumoDayCloseProductsDTOS;
    }
}
