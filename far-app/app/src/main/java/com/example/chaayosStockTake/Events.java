package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.util.Pair;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.example.chaayosStockTake.domain.StockTakeInitResponse;
import com.example.chaayosStockTake.util.AppConstants;
import com.example.chaayosStockTake.util.UtilClass;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class Events extends AppCompatActivity implements View.OnClickListener {
    CoordinatorLayout coordinatorLayout;

    private StockTakingRequest transferOutEvent;

    private Button transferOutButton;

    private AlertDialog.Builder builder;
    private AlertDialog progressDialog;

    private HashMap<String, List<StockTakingScannedItem>> productAssetMap = new HashMap<>();

    private Boolean isScanningRequired;

    private StockTakeInitResponse stockTakeInitResponse;

    private List<String> eventSubtypes;

    DrawerLayout drawerLayout;
    NavigationView navigationView;
    ActionBarDrawerToggle actionBarDrawerToggle;


    @SuppressLint({"RestrictedApi", "MissingInflatedId", "SetTextI18n", "HardwareIds"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_events);
        setDeviceInfo();
        Button refreshButton = findViewById(R.id.refresh);
        transferOutButton = findViewById(R.id.transfer_out);
        transferOutButton.setVisibility(View.GONE);
        transferOutButton.setOnClickListener(this);
        refreshButton.setOnClickListener(this);

        progressDialog = getDialogProgressBar().create();
        progressDialog.show();
        progressDialog.setCancelable(false);
        checkAclData();
        checkEnabledForSumoDayClose();

        checkForTransferOutEvent(false);


        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        SharedPreferences.Editor edt = pref.edit();
        edt.putString("version", "1.2.6");
        UtilClass.setAppVersion("1.2.6");
        edt.apply();
        checkAppVersion();
        TextView welcomeName = findViewById(R.id.welcome_name);
        String userName = "Welcome, " + pref.getString("name", null) + "!";
        welcomeName.setText(userName);
        coordinatorLayout = findViewById(R.id.coordinator_layout_events_activity);
        Map<String, ?> prefs = pref.getAll();
        assert getSupportActionBar() != null;
        if (Objects.nonNull(prefs.get("unitName"))) {
            getSupportActionBar().setSubtitle((String) prefs.get("unitName"));
        }
        verifyLogin();

        drawerLayout = findViewById(R.id.drawer_layout);
        navigationView = findViewById(R.id.stock_take_nav_view);
        actionBarDrawerToggle = new ActionBarDrawerToggle(Events.this, drawerLayout, R.string.open, R.string.close);
        drawerLayout.addDrawerListener(actionBarDrawerToggle);
        actionBarDrawerToggle.syncState();
        Menu menu = navigationView.getMenu();
        MenuItem switchAssetMenuItem = menu.findItem(R.id.switch_asset_button);
        boolean showSwitchAsset = UtilClass.utilClass.checkAcl("menu", "SWASTA");
        switchAssetMenuItem.setVisible(showSwitchAsset);
        MenuItem dayCloseMenuItem = menu.findItem(R.id.sumo_day_close);
        dayCloseMenuItem.setVisible(UtilClass.isUnitEnabledForStockTakeThroughApp());
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        TextView versionTextView = findViewById(R.id.versionTextView);
        String appVersion = UtilClass.getAppVersion();
        versionTextView.setText("Version : " + appVersion);

        TextView deviceInfoTextView = findViewById(R.id.deviceInfoTextView);
        String deviceInfo = UtilClass.getDeviceInfo();
        deviceInfoTextView.setText("Device : " + deviceInfo);
        navigationView.setNavigationItemSelectedListener(item -> {
            callOnClickMethods(item.getItemId());
            drawerLayout.closeDrawer(GravityCompat.START);
            return true;
        });

    }

    private void checkEnabledForSumoDayClose() {
        if (Objects.isNull(UtilClass.isUnitEnabledForStockTakeThroughApp())) {
            SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            boolean isEnabledForDayClose = pref.getBoolean("unitEnabledForStockTakeThroughApp", false);
            UtilClass.setUnitEnabledForStockTakeThroughApp(isEnabledForDayClose);
        }
    }

    private void checkAclData() {
        if (Objects.isNull(UtilClass.getAcl()) || UtilClass.getAcl().isEmpty()) {
            SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            String aclDataString = pref.getString("acl", null);
            Map<String, Map<String, Boolean>> aclData = new HashMap<>();
            Type type = new TypeToken<Map<String, Map<String, Boolean>>>(){}.getType();
            aclData = UtilClass.gson.fromJson(aclDataString, type);
            UtilClass.setAcl(aclData);
        }
    }

    private void checkAppVersion() {
        progressDialog.show();
        Retrofit retrofit = UtilClass.getRetrofitScm();
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<String> appVersionCall = unitsApiService.getAppVersion(token);
        appVersionCall.enqueue(new Callback<String>() {
            @Override
            public void onResponse(Call<String> call, Response<String> response) {
                if (!response.isSuccessful()) {
                    UtilClass.utilClass.parseScmError(response, Events.this, "getting App Version");
                    progressDialog.dismiss();
                    return;
                }
                String appVersion = response.body();
                if (!UtilClass.getAppVersion().equalsIgnoreCase(appVersion)) {
                    Intent intent = new Intent(getApplicationContext(), AppVersionNotMatched.class);
                    startActivity(intent);
                    finish();
                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(Call<String> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Error While getting App Version..!", Toast.LENGTH_SHORT).show();
                progressDialog.dismiss();
            }
        });
    }


    private void checkForTransferOutEvent(Boolean submit) {
        progressDialog.show();
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(2, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .writeTimeout(2, TimeUnit.MINUTES)
                .build();
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);
        Integer unitId = pref.getInt("unitId", -1);
        Call<StockTakeInitResponse> stockTakingEventsCall = unitsApiService.getStockTakingEvents(token, unitId,
                "INITIATED");
        stockTakingEventsCall.enqueue(new Callback<StockTakeInitResponse>() {
            @Override
            public void onResponse(Call<StockTakeInitResponse> call, Response<StockTakeInitResponse> response) {
                if (!response.isSuccessful()) {
                    UtilClass.utilClass.parseScmError(response, Events.this, "Getting Initiated Events ");
                    progressDialog.dismiss();
                    return;
                }
                if (response.body() == null) {
                    progressDialog.dismiss();
                    //finish();
                    return;
                }
                stockTakeInitResponse = response.body();
                List<StockTakingRequest> stockTakingRequests = stockTakeInitResponse.getStockEventDefinition();

                if (stockTakingRequests.size() > 0) {
                    productAssetMap = new HashMap<>();
                    transferOutButton.setVisibility(View.VISIBLE);
                    transferOutEvent = stockTakingRequests.get(0);
                    List<StockTakingScannedItem> assetList = transferOutEvent.getAvailableAssets();
                    Map<Integer, Pair<BigDecimal, BigDecimal>> productRequestQtyMap = transferOutEvent.getProductRequestQtyMap();

                    isScanningRequired = false;
                    if (transferOutEvent.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT)) {
                        for (StockTakingScannedItem item : assetList) {
                            if (!productRequestQtyMap.containsKey(item.getProductId())) {
                                continue;
                            }
                            if (!productAssetMap.containsKey(item.getProductName())) {
                                Pair<BigDecimal, BigDecimal> qtyPair = productRequestQtyMap.get(item.getProductId());
                                Integer pendingQty = qtyPair.first.subtract(qtyPair.second).intValue();
                                if (pendingQty > 0) {
                                    isScanningRequired = true;
                                }
                                List<StockTakingScannedItem> items = new ArrayList<>();
                                for (Integer i = 0; i < pendingQty; i++) {
                                    items.add(new StockTakingScannedItem(transferOutEvent.getEventId(), transferOutEvent.getUnitId(),
                                            item.getProductName() + i, false, transferOutEvent.getAuditedBy(), item.getProductName(), item.getSkuName(), item.getSubCategory(), item.getProductName()));
                                }
                                if (!items.isEmpty()) {
                                    productAssetMap.put(item.getProductName(), items);
                                }
                            }
                        }
                    } else if (transferOutEvent.getEventType().equalsIgnoreCase(AppConstants.ASSET_RECEIVING)){
                        if (Objects.nonNull(assetList) && !assetList.isEmpty()) {
                            isScanningRequired = true;
                        } else {
                            isScanningRequired = false;
                        }
                    }
                    if (Boolean.FALSE.equals(isScanningRequired)) {
                        transferOutButton.setText(transferOutEvent.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT) ? "Transfer Out Completed!!"
                                : "Asset Receiving Completed!!");
                    } else {
                        transferOutButton.setText(transferOutEvent.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT) ? "Transfer Out" : "Asset Receiving");
                    }

                    if (Boolean.TRUE.equals(submit)) {
                        Intent intent = new Intent(getApplicationContext(), StockTakingScanner.class);
                        Gson gson = new Gson();
                        String transferOutEventJson = gson.toJson(transferOutEvent);
                        String productAssetMapJson = gson.toJson(productAssetMap);
                        SharedPreferences sharedPreferences = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                        SharedPreferences.Editor editor = sharedPreferences.edit();
                        editor.putString("transferOut", transferOutEventJson);
                        editor.putString("productMap", productAssetMapJson);
                        editor.apply();
                        startActivity(intent);
                        progressDialog.dismiss();
                    } else {
                        progressDialog.dismiss();
                    }

                } else {
                    transferOutButton.setVisibility(View.GONE);
                    progressDialog.dismiss();
                }
            }

            @Override
            public void onFailure(Call<StockTakeInitResponse> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "getStockTakingInitializedEventsList: Check internet connection!", Toast.LENGTH_SHORT).show();
                finish();
                progressDialog.dismiss();
            }
        });
    }

    private void verifyLogin() {

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)
                .build();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .client(okHttpClient)
                .build();

        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<Object> auditorRequirementCall = unitsApiService.getStockTakeSubList(token);
        auditorRequirementCall.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if (response.code() == 401) {
                    clearPreferences();
                    startActivity(new Intent(getApplicationContext(), Login.class));
                    finish();
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Auditor Requirement could not be fetched.", Toast.LENGTH_SHORT).show();
            }
        });


    }

    @Override
    public void onClick(View v) {
        callOnClickMethods(v.getId());

    }

    private void callOnClickMethods(int id) {
        switch (id) {
            case R.id.stock_taking_button:
                if (!isConnected()) {
                    Snackbar.make(coordinatorLayout, "Check internet connection!", Snackbar.LENGTH_SHORT).show();
                } else {
                    SharedPreferences sharedPreferences = getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                    SharedPreferences.Editor editor = sharedPreferences.edit();
                    editor.remove("transferOut");
                    editor.remove("productMap");
                    editor.apply();
                    startActivity(new Intent(getApplicationContext(), StockTaking.class));
                }
                break;
            case R.id.pending_receiving_button:
                startActivity(new Intent(getApplicationContext(), PendingReceiving.class));
                break;
            case R.id.asset_lookup_button:
                startActivity(new Intent(getApplicationContext(), AssetLookup.class));
                break;
            case R.id.transfer_out:
                if (Boolean.FALSE.equals(isScanningRequired)) {
                    if (Objects.nonNull(transferOutEvent)) {
                        Toast.makeText(getApplicationContext(), "Your " + (transferOutEvent.getEventType().equalsIgnoreCase(AppConstants.TRANSFER_OUT) ?
                                " transfer out" : "Asset Receiving " ) + "scanning is completed. Go to Sumo app to complete your Asset Transfer", Toast.LENGTH_LONG).show();
                    } else {
                        Toast.makeText(getApplicationContext(), "Your scanning is completed. Go to Sumo app to complete the Process", Toast.LENGTH_LONG).show();
                    }
                } else {
                    checkForTransferOutEvent(true);
                }
                break;
            case R.id.refresh:
                checkForTransferOutEvent(false);
                break;
            case R.id.switch_asset_button:
                startActivity(new Intent(getApplicationContext(), SwitchAssetBasicDetails.class));
                break;
            case R.id.action_logout:
                logout();
                break;
            case R.id.sumo_day_close:
                startActivity(new Intent(getApplicationContext(), SumoDayClose.class));
                break;
            default:
                break;
        }
    }

    private boolean isConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        return connectivityManager.getActiveNetworkInfo() != null && connectivityManager.getActiveNetworkInfo().isConnected();
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return false;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle item selection
        if (actionBarDrawerToggle.onOptionsItemSelected(item)) {
            return true;
        } else {
            switch (item.getItemId()) {
                case R.id.action_logout:
                    logout();
                    return true;
                default:
                    return super.onOptionsItemSelected(item);
            }
        }
    }

    private void logout() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(Events.this);
        alertDialog.setTitle("Logging out...");
        alertDialog.setMessage("Are you sure you want to log out of your current session?");
        alertDialog.setPositiveButton("CANCEL", (dialog, which) -> dialog.cancel());
        alertDialog.setNegativeButton("LOGOUT", (dialog, which) -> {
            dialog.cancel();
            clearPreferences();
            startActivity(new Intent(getApplicationContext(), Login.class));
            finish();
        });
        alertDialog.show();
    }

    public AlertDialog.Builder getDialogProgressBar() {

        if (builder == null) {
            builder = new AlertDialog.Builder(this);

            builder.setTitle("Please Wait...");

            final ProgressBar progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(true);
            builder.setView(progressBar);
        }
        return builder;
    }

    private void clearPreferences() {
        SharedPreferences pref = getSharedPreferences("ActivityPREF", Context.MODE_PRIVATE);
        SharedPreferences.Editor edt = pref.edit();
        edt.putBoolean("activity_executed", false);
        edt.clear();
        edt.apply();
        UtilClass.getAcl().clear();
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        setDeviceInfo();
        UtilClass.setAppVersion("1.2.6");
        checkAclData();
        checkEnabledForSumoDayClose();
        checkAppVersion();
        checkForTransferOutEvent(false);
    }

    private void setDeviceInfo() {
        String buildIdentityAndroid;
        try {
            String androidID = "";
            androidID = Settings.Secure.getString(this.getContentResolver(), Settings.Secure.ANDROID_ID);
            buildIdentityAndroid = Build.MANUFACTURER + "#" + Build.MODEL + "#" + androidID;
            Log.i("BUILD_INFO", buildIdentityAndroid);
        } catch (Exception e) {
            buildIdentityAndroid = Build.MANUFACTURER + "#" + Build.MODEL;
            Log.i("BUILD_INFO", buildIdentityAndroid);
        }
        UtilClass.setDeviceInfo(buildIdentityAndroid);
    }
}
