package com.example.chaayosStockTake.domain;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class StockTakeSumoDayCloseProductsDTO {

    private Integer sumoDayCloseProductItemId;

    private Integer stockTakeSumoDayCloseEventId;

    private Integer productId;

    private String uom;

    private String productType;

    private String productName;

    private Long updatedTime;

    private BigDecimal enteredQuantity;

    private List<DayCloseProductPackagingMappingsDTO> dayCloseProductPackagingMappings = new ArrayList<>(0);

    public Integer getSumoDayCloseProductItemId() {
        return sumoDayCloseProductItemId;
    }

    public void setSumoDayCloseProductItemId(Integer sumoDayCloseProductItemId) {
        this.sumoDayCloseProductItemId = sumoDayCloseProductItemId;
    }

    public Integer getStockTakeSumoDayCloseEventId() {
        return stockTakeSumoDayCloseEventId;
    }

    public void setStockTakeSumoDayCloseEventId(Integer stockTakeSumoDayCloseEventId) {
        this.stockTakeSumoDayCloseEventId = stockTakeSumoDayCloseEventId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }

    public List<DayCloseProductPackagingMappingsDTO> getDayCloseProductPackagingMappings() {
        return dayCloseProductPackagingMappings;
    }

    public void setDayCloseProductPackagingMappings(List<DayCloseProductPackagingMappingsDTO> dayCloseProductPackagingMappings) {
        this.dayCloseProductPackagingMappings = dayCloseProductPackagingMappings;
    }

    public BigDecimal getEnteredQuantity() {
        return enteredQuantity;
    }

    public void setEnteredQuantity(BigDecimal enteredQuantity) {
        this.enteredQuantity = enteredQuantity;
    }
}
