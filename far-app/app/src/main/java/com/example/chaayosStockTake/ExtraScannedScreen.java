package com.example.chaayosStockTake;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.example.chaayosStockTake.util.UtilClass;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import org.w3c.dom.Text;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.internal.platform.android.StandardAndroidSocketAdapter;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class ExtraScannedScreen extends AppCompatActivity implements View.OnClickListener {

    private List<StockTakingScannedItem> extraScannedItems;
    private String stockTakingEventResponseJson;
    private String pendingAssetsJson;
    private String scannedAssetsJson;
    private static final int pic_id = 123;

    private Button camera_open_id;
    private ImageView click_image_id;

    private Bitmap currentImage;

    @SuppressLint({"RestrictedApi"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.extrascanneditems);
        Button addButton = findViewById(R.id.add_button);

        addButton.setOnClickListener(this);

        camera_open_id = findViewById(R.id.camera_button);
        click_image_id = findViewById(R.id.click_image);

        EditText commentEdit = findViewById(R.id.editText2);
        commentEdit.setSelection(0);

        EditText productNameEdit = findViewById(R.id.editText1);
        productNameEdit.setSelection(0);

        // Camera_open button is for open the camera and add the setOnClickListener in this button
        camera_open_id.setOnClickListener(v -> {
            // Create the camera_intent ACTION_IMAGE_CAPTURE it will open the camera for capture the image
            Intent camera_intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            // Start the activity with camera_intent, and request pic id
            startActivityForResult(camera_intent, pic_id);
        });
        Intent intent = getIntent();
        Button nextButton = findViewById(R.id.next_button);
        nextButton.setOnClickListener(this);
        Button backButton  = findViewById(R.id.back_button);
        backButton.setOnClickListener(this);
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        SharedPreferences previousPref = getSharedPreferences("StockTakingPrevList", Context.MODE_PRIVATE);
        Map<String,?> prefs = pref.getAll();
        Map<String,?> previousPrefs = previousPref.getAll();
        if (previousPrefs.containsKey("StockTakingEventResponse")) {
            Gson gson = new Gson();
            stockTakingEventResponseJson = (String) previousPrefs.get("StockTakingEventResponse");
            //stockTakingEventResponse = gson.fromJson(temp,StockTakingEvent.class);
        }

        assert getSupportActionBar() != null;
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Extra Items Action");
        if(Objects.nonNull( prefs.get("unitName"))){
            getSupportActionBar().setSubtitle((String) prefs.get("unitName"));
        }

        Gson gson = new Gson();
        if(previousPrefs.containsKey("pendingAssetsJson")){
            pendingAssetsJson = (String) previousPrefs.get("pendingAssetsJson");
            scannedAssetsJson = (String) previousPrefs.get("scannedAssets");

        }

        if(intent.hasExtra("extraScannedItems")){
            String extraScannedItemsJson = intent.getStringExtra("extraScannedItems");
            Type type = new TypeToken<List<StockTakingScannedItem>>(){}.getType();
            extraScannedItems = gson.fromJson(extraScannedItemsJson,type);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                extraScannedItems.sort(new Comparator<StockTakingScannedItem>() {
                    @Override
                    public int compare(StockTakingScannedItem o1, StockTakingScannedItem o2) {
                        return o1.getAssetName().compareTo(o2.getAssetName());
                    }
                });
            }
        }


        if(!extraScannedItems.isEmpty()){
            TableLayout tb = (TableLayout) findViewById(R.id.extraScanned);
            tb.setVisibility(View.VISIBLE);
            TableLayout parentTableLayout = findViewById(R.id.parentTable);
            parentTableLayout.setVisibility(View.VISIBLE);
            for(StockTakingScannedItem item : extraScannedItems){
                TableRow row = new TableRow(this);
               TableRow.LayoutParams params = new TableRow.LayoutParams(TableRow.LayoutParams.WRAP_CONTENT,
                        TableRow.LayoutParams.WRAP_CONTENT);
                params.setMargins(25,25,25,25);
                TextView unitNameView = new TextView(this);
                unitNameView.setLayoutParams(params);
                TextView unitIdView = new TextView(this);
                unitIdView.setLayoutParams(params);
                TextView assetIdView = new TextView(this);
                assetIdView.setLayoutParams(params);
                TextView assetNameView = new TextView(this);
                assetNameView.setLayoutParams(params);
                TextView tagValueView = new TextView(this);
                tagValueView.setLayoutParams(params);
                TableLayout.LayoutParams tableRowParams=
                        new TableLayout.LayoutParams(TableLayout.LayoutParams.WRAP_CONTENT,
                                TableRow.LayoutParams.WRAP_CONTENT,1.0f);
                row.setLayoutParams(tableRowParams);

                unitIdView.setText(String.valueOf(item.getUnitId()));
                unitIdView.setTextColor(Color.BLACK);
                unitNameView.setText(String.valueOf(item.getUnitName()));
                unitNameView.setTextColor(Color.BLACK);
                assetIdView.setText(String.valueOf(item.getAssetId()));
                assetIdView.setTextColor(Color.BLACK);
                assetNameView.setText(String.valueOf(item.getAssetName()));
                assetNameView.setTextColor(Color.BLACK);
                assetNameView.setWidth(200);
                tagValueView.setText(String.valueOf(item.getAssetTagValue()));
                tagValueView.setTextColor(Color.BLACK);
                row.addView(assetIdView,0);
                row.addView(assetNameView,1);
                row.addView(unitIdView,2);
                row.addView(unitNameView,3);
                row.addView(tagValueView,4);
                row.setBackgroundResource(R.color.cornerlightGreen);
                tb.addView(row);
            }
        }


    }


    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        // Match the request 'pic id with requestCode
        if (requestCode == pic_id) {
            if(Objects.nonNull(data) && Objects.nonNull(data.getExtras())){
                // BitMap is data structure of image file which store the image in memory
                Bitmap photo = (Bitmap) data.getExtras().get("data");
                currentImage = photo;
                // Set the image in imageview for display
                click_image_id.setVisibility(View.VISIBLE);
                click_image_id.setImageBitmap(photo);

            }
        }
    }

    private  void saveExtraFoundItem(){
        TextView commentView = findViewById(R.id.editText2);
        String comment = commentView.getText()!= null ?  commentView.getText().toString() : null;
        if(Objects.isNull(comment) || comment.equalsIgnoreCase("")){
            Toast.makeText(getApplicationContext(),"PLease Write Some Comment!!",Toast.LENGTH_SHORT).show();
            return;
        }
        TextView descriptionView = findViewById(R.id.editText1);
        String productName = descriptionView.getText()!=null ?  descriptionView.getText().toString() : null;
        if(Objects.isNull(productName) || productName.equalsIgnoreCase("")){
            Toast.makeText(getApplicationContext(),"PLease Write Product Name!!",Toast.LENGTH_SHORT).show();
            return;
        }
        StockTakingRequest stockTakingRequest = new Gson().fromJson(stockTakingEventResponseJson, StockTakingRequest.class);

        MultipartBody.Part filePart = null;
        if(Objects.isNull(currentImage)){
            Toast.makeText(getApplicationContext(),"PLease Click Image!!",Toast.LENGTH_SHORT).show();
            return;
        }
        if(currentImage!=null){
            File imageFile = UtilClass.utilClass.saveBitmap(currentImage, this);
        filePart = MultipartBody.Part.createFormData("file", imageFile.getName(),
                RequestBody.create(MediaType.parse("image/*"), imageFile));
        }
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(2, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .writeTimeout(2, TimeUnit.MINUTES)
                .build();
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .client(okHttpClient)
                .build();

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        SharedPreferences sharedPreferences = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = sharedPreferences.getString("jwtToken", null);

        Call<Object> assetLookupCall = unitsApiService.saveExtraItemData(token,filePart,comment,stockTakingRequest.getEventId(),
                productName);
        assetLookupCall.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                Toast.makeText(getApplicationContext(),"SuccesFully Saved Data!!",Toast.LENGTH_SHORT).show();
                click_image_id.setVisibility(View.GONE);
                currentImage = null;
                descriptionView.setText(null);
                commentView.setText(null);
                Button clickButton = findViewById(R.id.camera_button);
                clickButton.setVisibility(View.VISIBLE);
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Toast.makeText(getApplicationContext(),"Server Error",Toast.LENGTH_SHORT).show();
                return;
            }
        });


    }


    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    private void next(){
        Intent intent = new Intent(ExtraScannedScreen.this, StockTakingEventSummary.class);
        startActivity(intent);
    }


    @Override
    public void onClick(View v) {
        if(v.getId() == R.id.next_button){
            next();
        }else if (v.getId() == R.id.back_button){
                onBackPressed();
        }else if (v.getId() == R.id.add_button){
            saveExtraFoundItem();
        }
    }
}
