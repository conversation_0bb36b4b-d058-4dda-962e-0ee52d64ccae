package com.example.chaayosStockTake;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.BaseExpandableListAdapter;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class CustomizedExpandableListAdapter extends BaseExpandableListAdapter  {

    private Context context;
    private List<String> expandableTitleList;
    private Map<String, List<StockTakingScannedItem>> expandableDetailList;

    private Map<String, List<StockTakingScannedItem>> foundMap = new HashMap<>();

    private Map<String, List<StockTakingScannedItem>> lostMap = new HashMap<>();


    private Boolean isLost;

    private boolean showMarkLost;

    private boolean hideAssetTag;

    // constructor
    public CustomizedExpandableListAdapter(Context context, List<String> expandableListTitle,
                                           Map<String, List<StockTakingScannedItem>> expandableListDetail, Boolean isLost, boolean showMarkLost, boolean hideAssetTag) {
        this.context = context;
        this.expandableTitleList = expandableListTitle;
        this.expandableDetailList = getSortedExpandableDetailList(expandableListDetail);
        this.isLost = isLost;
        this.showMarkLost = showMarkLost;
        this.hideAssetTag = hideAssetTag;
    }

    private Map<String, List<StockTakingScannedItem>> getSortedExpandableDetailList(Map<String, List<StockTakingScannedItem>> expandableListDetail) {
        Map<String, List<StockTakingScannedItem>> result = new HashMap<>();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            expandableListDetail.forEach((type, stockTakingScannedItems) -> {
                result.put(type, stockTakingScannedItems);
            });
        }
        return result;
    }

    public void setExpandableDetailList(Map<String, List<StockTakingScannedItem>> expandableDetailList) {
        this.expandableDetailList = expandableDetailList;
        notifyDataSetChanged();
    }

    public CustomizedExpandableListAdapter(Context context, List<String> expandableListTitle,
                                           Map<String, List<StockTakingScannedItem>> expandableListDetail,
                                           Map<String, List<StockTakingScannedItem>> foundMap, Map<String, List<StockTakingScannedItem>> lostMap, Boolean isLost, boolean showMarkLost) {
        this.context = context;
        this.expandableTitleList = expandableListTitle;
        this.expandableDetailList = getSortedExpandableDetailList(expandableListDetail);
        this.isLost = isLost;
        this.foundMap = foundMap;
        this.lostMap = lostMap;
        this.showMarkLost = showMarkLost;
    }


    @Override
    public void notifyDataSetChanged() {
        super.notifyDataSetChanged();
    }

    @Override
    // Gets the data associated with the given child within the given group.
    public Object getChild(int lstPosn, int expanded_ListPosition) {
        return this.expandableDetailList.get(this.expandableTitleList.get(lstPosn)).get(expanded_ListPosition);
    }

    @Override
    // Gets the ID for the given child within the given group.
    // This ID must be unique across all children within the group. Hence we can pick the child uniquely
    public long getChildId(int listPosition, int expanded_ListPosition) {
        return expanded_ListPosition;
    }


    @Override
    // Gets a View that displays the data for the given child within the given group.
    public View getChildView(int lstPosn, final int expanded_ListPosition,
                             boolean isLastChild, View convertView, ViewGroup parent) {
        StockTakingScannedItem expandedListText = (StockTakingScannedItem) getChild(lstPosn, expanded_ListPosition);

        LayoutInflater layoutInflater = (LayoutInflater) this.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        convertView = layoutInflater.inflate(R.layout.list_item, null);

        CardView expandedListTextView = (CardView) convertView.findViewById(R.id.expandedListItem);
        StockTakingScannedItem item = (StockTakingScannedItem) getChild(lstPosn, expanded_ListPosition);
        if(Objects.nonNull(expandedListText.getImageUrl())) {
            String url = "https://d8xnaajozedwc.cloudfront.net/sku_image/" + expandedListText.getImageUrl();
            MyLovelyOnClickListener clickListener = new MyLovelyOnClickListener(url);
            expandedListTextView.setOnClickListener(clickListener);
        }

        CheckBox markAsLostCheckBox = convertView.findViewById(R.id.mark_lost_check_box);

        TextView assetName = (TextView) convertView.findViewById(R.id.asset_name_tv);
        assetName.setText(item.getAssetName());

        TextView assetTag = convertView.findViewById(R.id.asset_tag_tv);
        assetTag.setText(item.getAssetTagValue());
        if (hideAssetTag) {
            assetTag.setText("****"+item.getAssetTagValue().substring(4));
        }

        TextView assetValidity = convertView.findViewById(R.id.asset_tag_validity_tv);
        ConstraintLayout constraintLayout = convertView.findViewById(R.id.sub_layout);
        if (item.isFound()) {
            assetValidity.setText("FOUND");
            constraintLayout.setBackgroundResource(R.drawable.rounded_shape_green_fill);
            markAsLostCheckBox.setClickable(false);
        } else {
            assetValidity.setText("NOT FOUND");
            constraintLayout.setBackgroundResource(R.drawable.rounded_shape_red_fill);
            if (showMarkLost) {
                markAsLostCheckBox.setVisibility(View.VISIBLE);
                constraintLayout.setBackgroundResource(R.drawable.rounded_shape_yellow_fill);
                if (Objects.nonNull(expandedListText) && Boolean.TRUE.equals(expandedListText.getMarkedAsLost())) {
                    markAsLostCheckBox.setChecked(true);
                    assetValidity.setText("MARKED_LOST");
                    constraintLayout.setBackgroundResource(R.drawable.rounded_shape_red_fill);
                }
            }
        }

        markAsLostCheckBox.setOnClickListener(view -> {
            boolean checked = ((CheckBox) view).isChecked();
            if (checked) {
                assetValidity.setText("MARKED_LOST");
                constraintLayout.setBackgroundResource(R.drawable.rounded_shape_red_fill);
                expandedListText.setMarkedAsLost(Boolean.TRUE);
            } else {
                expandedListText.setMarkedAsLost(Boolean.FALSE);
                if (item.isFound()) {
                    assetValidity.setText("FOUND");
                    constraintLayout.setBackgroundResource(R.drawable.rounded_shape_green_fill);
                } else {
                    assetValidity.setText("NOT FOUND");
                    constraintLayout.setBackgroundResource(R.drawable.rounded_shape_yellow_fill);
                }
            }
        });
        return convertView;
    }

    private Bitmap getImageBitmap(String url) {
        Bitmap bm = null;
        try {
            URL aURL = new URL(url);
            URLConnection conn = aURL.openConnection();
            conn.connect();
            InputStream is = conn.getInputStream();
            BufferedInputStream bis = new BufferedInputStream(is);
            bm = BitmapFactory.decodeStream(bis);
            bis.close();
            is.close();
        } catch (IOException e) {
            Log.e("vdff", "Error getting bitmap", e);
        }
        return bm;
    }

    @Override
    // Gets the number of children in a specified group.
    public int getChildrenCount(int listPosition) {
        return this.expandableDetailList.get(this.expandableTitleList.get(listPosition)).size();
    }

    @Override
    // Gets the data associated with the given group.
    public Object getGroup(int listPosition) {
        return this.expandableTitleList.get(listPosition);
    }

    @Override
    // Gets the number of groups.
    public int getGroupCount() {
        return this.expandableTitleList.size();
    }

    @Override
    // Gets the ID for the group at the given position. This group ID must be unique across groups.
    public long getGroupId(int listPosition) {
        return listPosition;
    }

    @Override
    // Gets a View that displays the given group.
    // This View is only for the group--the Views for the group's children
    // will be fetched using getChildView()
    public View getGroupView(int listPosition, boolean isExpanded, View convertView, ViewGroup parent) {
        String listTitle = (String) getGroup(listPosition);
        LayoutInflater layoutInflater = (LayoutInflater) this.context.
                getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        convertView = layoutInflater.inflate(R.layout.list_group, null);
        TextView listTitleTextView = (TextView) convertView.findViewById(R.id.listTitle);
        if (isLost.equals(Boolean.TRUE)) {
            listTitleTextView.setBackgroundResource(R.drawable.rounded_shape_red_fill);
        } else {
            listTitleTextView.setBackgroundResource(R.drawable.rounded_shape_green_fill);
        }

        listTitleTextView.setTypeface(null, Typeface.BOLD);
        Integer size = 0;
        if (Objects.nonNull(expandableDetailList.get(listTitle))) {
            size = expandableDetailList.get(listTitle).size();
        }
        String groupText = listTitle + "(" + size + ")";
        if (foundMap.containsKey(listTitle) || lostMap.containsKey(listTitle)) {
            groupText = groupText + "     Found(" + (Objects.nonNull(foundMap.get(listTitle)) ? foundMap.get(listTitle).size() : "0") + ")";
            groupText = groupText + "     Not Found(" + (Objects.nonNull(lostMap.get(listTitle)) ? lostMap.get(listTitle).size() : "0") + ")";
        }
        listTitleTextView.setText(groupText);
        return convertView;
    }

    @Override
    // Indicates whether the child and group IDs are stable across changes to the underlying data.
    public boolean hasStableIds() {
        return false;
    }

    @Override
    // Whether the child at the specified position is selectable.
    public boolean isChildSelectable(int listPosition, int expandedListPosition) {
        return true;
    }


    public class MyLovelyOnClickListener implements View.OnClickListener {

        String imageUrl;

        Dialog builder;

        public MyLovelyOnClickListener(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        @Override
        public void onClick(View v) {
            if (v.getId() == R.id.expandedListItem) {
                showImage(v);
            }else{
                builder.dismiss();
            }


        }

        public void showImage(View view) {
            Log.d("inside", "onCLick");
            builder = new Dialog(context, android.R.style.Theme_Translucent_NoTitleBar_Fullscreen);
            builder.setCancelable(true);
            builder.requestWindowFeature(Window.FEATURE_NO_TITLE);
            builder.getWindow().setBackgroundDrawable(
                    new ColorDrawable(android.graphics.Color.TRANSPARENT));
            builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialogInterface) {

                }
            });

            ImageView imageView = new ImageView(context);
            imageView.setClickable(true);
            imageView.setOnClickListener(this);


            String url = this.imageUrl;
            Bitmap bitmap = getImageBitmap(url);
            if(Objects.nonNull(bitmap)){
            imageView.setImageBitmap(bitmap);

                builder.addContentView(imageView, new RelativeLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT));
                builder.show();
            }else {
                imageView.setImageResource(R.drawable.noimage);
                builder.addContentView(imageView, new RelativeLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT));
                builder.show();
                //builder.dismiss();
            }

        }

    };

}

