package com.example.chaayosStockTake.util;

import android.app.AlertDialog;
import android.content.Context;
import android.content.ContextWrapper;
import android.graphics.Bitmap;
import android.os.Build;
import android.util.Log;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Toast;

import com.example.chaayosStockTake.BuildConfig;
import com.example.chaayosStockTake.Exceptions.SCMError;
import com.example.chaayosStockTake.StockTakingScannedItem;
import com.google.gson.Gson;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class UtilClass {

    public static Gson gson;

    public static UtilClass utilClass;

    public static String deviceInfo;

    public static OkHttpClient okHttpClient;

    public static Retrofit retrofitMaster;
    public static Retrofit retrofitScm;

    public static String appVersion;
    public static Map<String, Map<String, Boolean>> acl = new HashMap<>();

    public static Boolean unitEnabledForStockTakeThroughApp;

    static {
        gson = new Gson();
        utilClass = new UtilClass();
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(2, TimeUnit.MINUTES)
                .readTimeout(2, TimeUnit.MINUTES)
                .writeTimeout(2, TimeUnit.MINUTES)
                .build();
        retrofitMaster = new Retrofit.Builder()
                .client(okHttpClient)
                .baseUrl(BuildConfig.BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        retrofitScm = new Retrofit.Builder()
                .client(okHttpClient)
                .baseUrl(BuildConfig.SCM_BASE)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        appVersion = "1.2.6";
        acl = new HashMap<>();
        unitEnabledForStockTakeThroughApp = null;
    }


    private AlertDialog.Builder pleaseWaitBuilder;

    public AlertDialog alertDialog;

    public Toast toast;

    public AlertDialog.Builder getDialogProgressBar(Context context) {

            pleaseWaitBuilder = new AlertDialog.Builder(context);

            pleaseWaitBuilder.setTitle("Please Wait...");

            ProgressBar progressBar = new ProgressBar(context, null, android.R.attr.progressBarStyleHorizontal);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            progressBar.setPadding(50, 50, 50, 0);
            progressBar.setLayoutParams(lp);
            progressBar.setIndeterminate(true);
            pleaseWaitBuilder.setView(progressBar);
        return pleaseWaitBuilder;
    }

    public AlertDialog getAlertDialog(Context context, String title, String msg) {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(context);
        alertDialog.setTitle(title);
        alertDialog.setMessage(msg);
        alertDialog.setPositiveButton("Ok", (dialog, which) -> dialog.cancel());
        return alertDialog.create();
    }

    public Toast getToast(Context context, String textMessage) {
        return Toast.makeText(context, textMessage, Toast.LENGTH_SHORT);
    }

    public List<String> getFilterTypes() {
        return Arrays.asList("CATEGORY","PRODUCT","SKU");
    }

    public List<StockTakingScannedItem> sortAssets(List<StockTakingScannedItem> assetList){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            assetList.sort(new Comparator<>() {
                @Override
                public int compare(StockTakingScannedItem o1, StockTakingScannedItem o2) {
                    return o1.getAssetName().compareTo(o2.getAssetName());
                }
            });
        }
        return assetList;
    }

    public static String getDeviceInfo() {
        return deviceInfo;
    }

    public static void setDeviceInfo(String deviceInfo) {
        UtilClass.deviceInfo = deviceInfo;
    }

    public static Retrofit getRetrofitMaster() {
        return retrofitMaster;
    }

    public static Retrofit getRetrofitScm() {
        return retrofitScm;
    }

    public void parseScmError(Response<?> response, Context context, String msg) {
        if (Objects.nonNull(response.errorBody())) {
            try {
                String errorBodyString = response.errorBody().string();
                SCMError scmError = gson.fromJson(errorBodyString, SCMError.class);
                if (Objects.nonNull(scmError) && (Objects.nonNull(scmError.getErrorMessage()) || Objects.nonNull(scmError.getErrorMsg()))) {
                    Toast.makeText(context, Objects.nonNull(scmError.getErrorMessage()) ?
                            scmError.getErrorMessage() : scmError.getErrorMsg(), Toast.LENGTH_SHORT).show();
                    return;
                }
            } catch (Exception e) {
                Toast.makeText(context, msg + " Api Response is Not Successful", Toast.LENGTH_SHORT).show();
            }
        } else {
            Toast.makeText(context, "Error " +msg , Toast.LENGTH_SHORT).show();
        }
    }

    public File saveBitmap(Bitmap bmp, Context context) {
        ContextWrapper cw = new ContextWrapper(context);
        File directory = cw.getDir("imageDir", Context.MODE_PRIVATE);
        File file = new File(directory, "UniqueFileName" + ".png");
        if(file.exists()){
            file.delete();
        }
        Log.d("path", file.toString());
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            bmp.compress(Bitmap.CompressFormat.PNG, 100, fos);
            fos.flush();
            fos.close();
        } catch (java.io.IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    public static String getAppVersion() {
        return appVersion;
    }

    public static void setAppVersion(String appVersion) {
        UtilClass.appVersion = appVersion;
    }

    public static Map<String, Map<String, Boolean>> getAcl() {
        return acl;
    }

    public static void setAcl(Map<String, Map<String, Boolean>> acl) {
        UtilClass.acl = acl;
    }

    public boolean checkAcl(String type, String aclString) {
        return Objects.nonNull(acl) && Objects.nonNull(acl.get(type)) && Objects.nonNull(acl.get(type).get(aclString)) && acl.get(type).get(aclString);
    }

    public void parseUnsuccessfulResponse(Response<?> response, Context context, String methodName) {
        if (Objects.nonNull(response.errorBody())) {
            try {
                String errorBodyString = response.errorBody().string();
                SCMError scmError = new Gson().fromJson(errorBodyString, SCMError.class);
                if (Objects.nonNull(scmError) && (Objects.nonNull(scmError.getErrorMessage()) || Objects.nonNull(scmError.getErrorMsg()))) {
                    AlertDialog alertDialog = getAlertDialog(context, "ERROR",
                            Objects.nonNull(scmError.getErrorMessage()) ? scmError.getErrorMessage() : scmError.getErrorMsg());
                    alertDialog.show();
                }
            } catch (Exception e) {
                Toast.makeText(context, "API Response is Not Successful while " + methodName, Toast.LENGTH_SHORT).show();
            }
        } else {
            Toast.makeText(context, "Error Occurred while " + methodName, Toast.LENGTH_SHORT).show();
        }
    }

    public static Date getCurrentTimestamp() {
        return new Date(getCurrentTimeIST().getMillis());
    }

    public static DateTime getCurrentTimeIST() {
        return new DateTime(DateTimeZone.forID("Asia/Kolkata"));
    }

    public static Boolean isUnitEnabledForStockTakeThroughApp() {
        return unitEnabledForStockTakeThroughApp;
    }

    public static void setUnitEnabledForStockTakeThroughApp(Boolean unitEnabledForStockTakeThroughApp) {
        UtilClass.unitEnabledForStockTakeThroughApp = unitEnabledForStockTakeThroughApp;
    }
}
