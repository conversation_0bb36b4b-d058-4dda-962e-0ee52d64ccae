package com.example.chaayosStockTake;

import androidx.multidex.MultiDexApplication;

import com.example.chaayosStockTake.util.UtilClass;
import com.google.firebase.FirebaseApp;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.messaging.FirebaseMessaging;

public class StockTakeApplication extends MultiDexApplication {

    @Override
    public void onCreate() {
        super.onCreate();
        if (BuildConfig.ENABLE_CRASHLYTICS_ANALYTICS) {
            FirebaseApp.initializeApp(this);
            FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true);
            FirebaseAnalytics.getInstance(this).setAnalyticsCollectionEnabled(true);

            FirebaseMessaging.getInstance().subscribeToTopic("Stock_Take_Message")
                    .addOnCompleteListener(task -> {
                        if (!task.isSuccessful()) {
                            UtilClass.utilClass.getToast(StockTakeApplication.this, "Failed Completing").show();
                        }
                    });
        } else {
            FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(false);
            FirebaseAnalytics.getInstance(StockTakeApplication.this).setAnalyticsCollectionEnabled(false);
        }
    }

}
