package com.example.chaayosStockTake.Adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.R;
import com.example.chaayosStockTake.domain.DayCloseProductPackagingMappingsDTO;
import com.example.chaayosStockTake.domain.StockTakeSumoDayCloseProductsDTO;
import com.example.chaayosStockTake.util.UtilClass;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ProductPackagingListViewAdapter extends RecyclerView.Adapter<ProductPackagingListViewAdapter.ProductPackagingViewHolder>{

    private List<DayCloseProductPackagingMappingsDTO> productPackagingList = new ArrayList<>();

    private StockTakeSumoDayCloseProductsDTO productDTO;

    private InputFilter decimalInputFilter;

    DayCloseProductsListViewAdapter.DayCloseProductViewHolder productHolder;

    private boolean isPackagingEditable;

    private Context context;

    private List<StockTakeSumoDayCloseProductsDTO> listOfProducts;

    private Button saveProductTypeButton;

    private String currentDisplayProductType;

    public ProductPackagingListViewAdapter(DayCloseProductsListViewAdapter.DayCloseProductViewHolder holder, StockTakeSumoDayCloseProductsDTO productsDTO,
                                           List<DayCloseProductPackagingMappingsDTO> dayCloseProductPackagingMappings, InputFilter decimalInputFilter,
                                           boolean isPackagingEditable, Context context, List<StockTakeSumoDayCloseProductsDTO> listOfProducts, Button saveProductTypeButton, String currentDisplayProductType) {
        this.productHolder = holder;
        this.productDTO = productsDTO;
        this.productPackagingList = dayCloseProductPackagingMappings;
        this.decimalInputFilter = decimalInputFilter;
        this.isPackagingEditable = isPackagingEditable;
        this.context = context;
        this.listOfProducts = listOfProducts;
        this.saveProductTypeButton = saveProductTypeButton;
        this.currentDisplayProductType = currentDisplayProductType;
    }

    public List<DayCloseProductPackagingMappingsDTO> getProductPackagingList() {
        return productPackagingList;
    }

    public void setProductPackagingList(List<DayCloseProductPackagingMappingsDTO> productPackagingList) {
        this.productPackagingList = productPackagingList;
    }

    public StockTakeSumoDayCloseProductsDTO getProductDTO() {
        return productDTO;
    }

    public void setProductDTO(StockTakeSumoDayCloseProductsDTO productDTO) {
        this.productDTO = productDTO;
    }

    @NonNull
    @Override
    public ProductPackagingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.day_close_product_packaging_item,parent,false);
        return new ProductPackagingViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ProductPackagingViewHolder holder, int position) {
        DayCloseProductPackagingMappingsDTO productPackaging = productPackagingList.get(position);
        if (Objects.nonNull(productPackaging)) {
            holder.packageName.setText(productPackaging.getPackagingName());
            holder.quantity.setText(Objects.nonNull(productPackaging.getQuantity()) ? productPackaging.getQuantity().toString() : null);
            holder.quantity.setFilters(new InputFilter[]{decimalInputFilter});
            holder.quantity.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    String quantityText = s.toString();
                    if (quantityText.startsWith(".")) {
                        quantityText = "0" + quantityText;
                    }
                    BigDecimal quantity = quantityText.equalsIgnoreCase("") ? null : new BigDecimal(quantityText);
                    productPackaging.setQuantity(quantity);
                    calculateEnteredQuantity();
                    setSaveButtonTitle();
                }
            });
            holder.clearQuantity.setOnClickListener(new View.OnClickListener() {
                @SuppressLint("SetTextI18n")
                @Override
                public void onClick(View v) {
                    if (isPackagingEditable) {
                        holder.quantity.setText(null);
                        BigDecimal quantity = BigDecimal.ZERO;
                        boolean enteredPackaging = false;
                        for (DayCloseProductPackagingMappingsDTO packaging : productPackagingList) {
                            if (Objects.nonNull(packaging.getQuantity())) {
                                enteredPackaging = true;
                                quantity = quantity.
                                        add((Objects.nonNull(packaging.getQuantity())
                                                ? packaging.getQuantity() : BigDecimal.ZERO).multiply(packaging.getConversionRatio()).setScale(6, RoundingMode.HALF_UP))
                                        .setScale(6, RoundingMode.UNNECESSARY);
                            }
                        }
                        if (enteredPackaging) {
                            productHolder.enteredQuantity.setText(quantity.toString());
                            productDTO.setUpdatedTime(UtilClass.getCurrentTimestamp().getTime());
                            productDTO.setEnteredQuantity(quantity);
                            productHolder.productBasicLayout.setBackgroundResource(R.drawable.rounded_shape_green_fill);
                        } else {
                            productHolder.enteredQuantity.setText(null);
                            productDTO.setEnteredQuantity(null);
                            productHolder.productBasicLayout.setBackgroundResource(0);
                        }
                        setSaveButtonTitle();
                    } else {
                        UtilClass.utilClass.getToast(context, "Can not Edit Quantity in Summary View...!").show();
                    }
                }
            });
            if (!isPackagingEditable) {
                holder.quantity.setEnabled(false);
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private void setSaveButtonTitle() {
        int totalEnteredProducts = 0;
        for (StockTakeSumoDayCloseProductsDTO productsDTO : listOfProducts) {
            if (Objects.nonNull(productsDTO.getEnteredQuantity())) {
                totalEnteredProducts++;
            }
        }
        if (!currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
            saveProductTypeButton.setText("SAVE " + "( " + totalEnteredProducts +"/" + listOfProducts.size() + " )");
        } else {
            saveProductTypeButton.setText("SUBMIT " + "( " + totalEnteredProducts +"/" + listOfProducts.size() + " )");
        }
    }

    @Override
    public int getItemCount() {
        return productPackagingList.size();
    }

    public void calculateEnteredQuantity() {
        BigDecimal enteredQuantity = BigDecimal.ZERO;
        boolean enteredPackaging = false;
        for (DayCloseProductPackagingMappingsDTO packaging : productPackagingList) {
            if (Objects.nonNull(packaging.getQuantity())) {
                enteredPackaging = true;
                enteredQuantity = enteredQuantity.
                        add((Objects.nonNull(packaging.getQuantity())
                                ? packaging.getQuantity() : BigDecimal.ZERO).multiply(packaging.getConversionRatio()).setScale(6, RoundingMode.HALF_UP))
                        .setScale(6, RoundingMode.UNNECESSARY);
            }
        }
        if (enteredPackaging) {
            productHolder.enteredQuantity.setText(enteredQuantity.toString());
            productDTO.setUpdatedTime(UtilClass.getCurrentTimestamp().getTime());
            productDTO.setEnteredQuantity(enteredQuantity);
            productHolder.productBasicLayout.setBackgroundResource(R.drawable.rounded_shape_green_fill);
        } else {
            productHolder.enteredQuantity.setText(null);
            productDTO.setEnteredQuantity(null);
            productHolder.productBasicLayout.setBackgroundResource(0);
        }
    }

    public static class ProductPackagingViewHolder extends RecyclerView.ViewHolder {
        TextView packageName;
        EditText quantity;
        TextView clearQuantity;
        public ProductPackagingViewHolder(@NonNull View itemView) {
            super(itemView);
            packageName = itemView.findViewById(R.id.packaging_name);
            quantity = itemView.findViewById(R.id.quantity);
            clearQuantity = itemView.findViewById(R.id.clear_quantity);
        }
    }
}
