package com.example.chaayosStockTake;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class ImageUploadButtonsAdapter extends RecyclerView.Adapter<ImageUploaderViewHolder> {
    private List<Bitmap> images;
    private final Context context;

    public ImageUploadButtonsAdapter(List<Bitmap> images, Context context) {
        this.images = images;
        this.context = context;
    }

    @Override
    public ImageUploaderViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.switch_asset_upload_image, parent, false);
        return new ImageUploaderViewHolder(view, this.context);
    }

    @Override
    public void onBindViewHolder(ImageUploaderViewHolder holder, int position) {
        holder.getImageView().setVisibility(View.VISIBLE);
        holder.getCancelButton().setVisibility(View.VISIBLE);
        holder.setCurrentPosition(position);
    }

    @Override
    public int getItemCount() {
        return images.size();
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }
}
