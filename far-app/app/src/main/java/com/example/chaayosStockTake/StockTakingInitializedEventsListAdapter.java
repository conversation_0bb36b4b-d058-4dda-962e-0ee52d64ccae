package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import java.util.ArrayList;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import static android.content.Context.MODE_PRIVATE;

public class StockTakingInitializedEventsListAdapter extends RecyclerView.Adapter<StockTakingInitializedEventsListAdapter.StockTakingEventViewHolder> {

    private ArrayList<StockTakingRequest> stockTakingInitializedEventsList;
    private Context mContext;

    public StockTakingInitializedEventsListAdapter(Context mContext, ArrayList<StockTakingRequest> stockTakingInitializedEventsList) {
        this.stockTakingInitializedEventsList = stockTakingInitializedEventsList;
        this.mContext = mContext;
    }

    @NonNull
    @Override
    public StockTakingEventViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.stock_taking_initialized_list_item, parent, false);
        return new StockTakingEventViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull StockTakingEventViewHolder holder, int position) {
        StockTakingRequest item = stockTakingInitializedEventsList.get(position);
        String eventIdValue = item.getEventId().toString();
        String eventStatusValue = item.getEventStatus();
        holder.eventId.setText(eventIdValue);
        holder.eventStatus.setText(eventStatusValue);
        holder.resumeEvent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SharedPreferences sh = mContext.getSharedPreferences("StockTakingPrevList", MODE_PRIVATE);
                String list = sh.getString("StockTakingPrevListData", null);
                Intent intent = new Intent(mContext, StockTakingScanner.class);
                if(list!=null){
                    Gson googleJson = new Gson();
                    ArrayList javaArrayListFromGSON = googleJson.fromJson(list, ArrayList.class);
                    Bundle args = new Bundle();
                    args.putSerializable("ARRAYLIST", javaArrayListFromGSON);
                    intent.putExtra("BUNDLE", args);
                }
                ((Activity)mContext).startActivityForResult(intent,2);
            }
        });
        holder.abandonEvent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlertDialog.Builder alertDialog = new AlertDialog.Builder(mContext);
                alertDialog.setTitle("Abandon Event");
                alertDialog.setMessage("Are you sure you want to abandon this Stock Taking Event?");
                alertDialog.setPositiveButton("CANCEL", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.cancel();
                    }
                });
                alertDialog.setNegativeButton("YES", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        StockTakingRequest abandonThisEvent = stockTakingInitializedEventsList.get(position);
                        abandonThisEvent.setEventStatus("ABANDONED");
                        Retrofit retrofit = new Retrofit.Builder()
                                .baseUrl(BuildConfig.SCM_BASE)
                                .addConverterFactory(GsonConverterFactory.create())
                                .build();

                        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
                        SharedPreferences pref = mContext.getSharedPreferences("ActivityPREF", MODE_PRIVATE);
                        String token = pref.getString("jwtToken", null);

                        Call<StockTakingRequest> abandonInitializedStockEventCall = unitsApiService.abandonEvent(token, abandonThisEvent);
                        abandonInitializedStockEventCall.enqueue(new Callback<StockTakingRequest>() {
                            @Override
                            public void onResponse(Call<StockTakingRequest> call, Response<StockTakingRequest> response) {
                                if (response.body() == null) {
                                    Toast.makeText(mContext, "Abandon response null", Toast.LENGTH_SHORT).show();
                                    return;
                                }
                                StockTakingRequest result = response.body();
                                Toast.makeText(mContext, "Event abandoned successfully!", Toast.LENGTH_SHORT).show();
                                stockTakingInitializedEventsList.remove(position);
                                notifyItemRemoved(position);
                            }

                            @Override
                            public void onFailure(Call<StockTakingRequest> call, Throwable t) {
                                Toast.makeText(mContext, "Abandon Response not received!", Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                });

                AlertDialog dialog = alertDialog.create();
                dialog.show();
            }
        });
    }

    @Override
    public int getItemCount() {
        return stockTakingInitializedEventsList.size();
    }

    public class StockTakingEventViewHolder extends RecyclerView.ViewHolder {
        TextView eventId;
        TextView eventStatus;
        LinearLayout parentLayout;
        ImageButton resumeEvent;
        ImageButton abandonEvent;

        public StockTakingEventViewHolder(@NonNull View itemView) {
            super(itemView);
            parentLayout = itemView.findViewById(R.id.parent_ST_layout);
            eventId = itemView.findViewById(R.id.event_id_tv);
            eventStatus = itemView.findViewById(R.id.event_status_tv);
            this.resumeEvent = itemView.findViewById(R.id.resume_ST_event_btn);
            this.abandonEvent = itemView.findViewById(R.id.cancel_ST_event_btn);
        }
    }
}
