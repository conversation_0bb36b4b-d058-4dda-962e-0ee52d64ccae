package com.example.chaayosStockTake;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Paint;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.InputFilter;
import android.text.Spanned;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.Adapters.DayCloseProductsListViewAdapter;
import com.example.chaayosStockTake.domain.DayCloseProductPackagingMappingsDTO;
import com.example.chaayosStockTake.domain.StockTakeDayCloseSaveRequest;
import com.example.chaayosStockTake.domain.StockTakeSumoDayCloseEventDTO;
import com.example.chaayosStockTake.domain.StockTakeSumoDayCloseProductsDTO;
import com.example.chaayosStockTake.util.AppConstants;
import com.example.chaayosStockTake.util.UtilClass;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;

public class SumoDayClose extends AppCompatActivity implements View.OnClickListener, AdapterView.OnItemSelectedListener {

    private AlertDialog progressDialog;

    private LinearLayout startSumoDayCloseLinearLayout;

    private LinearLayout resumeSumoDayCloseLinearLayout;

    private LinearLayout selectStockTakeTypeLinearLayout;

    private LinearLayout listOfProductsLinearLayout;

    private Button startSumoDayCloseButton;

    private Button cancelSumoDayCloseButton;

    private Button continueSumoDayCloseButton;

    private StockTakeSumoDayCloseEventDTO stockTakeSumoDayCloseEvent;

    private String selectedStockTakeType;

    private Map<String, List<StockTakeSumoDayCloseProductsDTO>> productsByProductType = new HashMap<>();

    private List<String> productTypes = new ArrayList<>();

    private String currentDisplayProductType;

    private RecyclerView listOfProductsRecyclerView;

    private DayCloseProductsListViewAdapter productsListViewAdapter;

    private Button previousProductTypeButton;

    private Button nextProductTypeButton;

    private Button saveProductTypeButton;

    private TextView sumoDayCloseEventId;

    private TextView sumoDayCloseEventInitiatedBy;

    private TextView sumoDayCloseStockTakeType;

    private TextView sumoDayCloseTextView;

    private SearchView productSearchView;

    private Spinner workStationTypeSpinner;

    private final List<String> orderList = Arrays.asList("FOOD", "HOT", "COLD", "BAKERY", "MERCHANDISE", "PACKAGING", "GNT");

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.sumo_day_close);
        assert getSupportActionBar() != null;
        getSupportActionBar().setTitle("Sumo Day Close");
        startSumoDayCloseLinearLayout = findViewById(R.id.start_sumo_day_close_linear_layout);
        resumeSumoDayCloseLinearLayout = findViewById(R.id.resume_sumo_day_close_linear_layout);
        selectStockTakeTypeLinearLayout = findViewById(R.id.select_stock_take_type);
        startSumoDayCloseButton = findViewById(R.id.start_sumo_dayClose_button);
        startSumoDayCloseButton.setOnClickListener(this);
        cancelSumoDayCloseButton = findViewById(R.id.cancel_sumo_day_close_button);
        cancelSumoDayCloseButton.setOnClickListener(this);
        continueSumoDayCloseButton = findViewById(R.id.continue_sumo_day_close_button);
        continueSumoDayCloseButton.setOnClickListener(this);
        listOfProductsLinearLayout = findViewById(R.id.list_of_products_linear_layout);
        listOfProductsRecyclerView = findViewById(R.id.list_of_products_recycler_view);
        previousProductTypeButton = findViewById(R.id.previous_product_type_button);
        previousProductTypeButton.setOnClickListener(this);
        nextProductTypeButton = findViewById(R.id.next_product_type_button);
        nextProductTypeButton.setOnClickListener(this);
        saveProductTypeButton = findViewById(R.id.save_product_type_button);
        saveProductTypeButton.setOnClickListener(this);
        sumoDayCloseEventId = findViewById(R.id.sumo_day_close_event_id);
        sumoDayCloseEventInitiatedBy = findViewById(R.id.sumo_day_close_event_initiated_by);
        sumoDayCloseStockTakeType = findViewById(R.id.sumo_day_close_stock_take_type);
        sumoDayCloseTextView = findViewById(R.id.sumo_day_close_text_view);
        sumoDayCloseTextView.setPaintFlags(sumoDayCloseTextView.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
        productSearchView = findViewById(R.id.search_list_of_products);
        Spinner stockTakeType = findViewById(R.id.stock_take_type_spinner);
        ArrayAdapter<String> stockTakeTypesArrayAdapter = new ArrayAdapter<String>(SumoDayClose.this, android.R.layout.simple_spinner_item, AppConstants.STOCK_TAKE_TYPES);
        stockTakeType.setAdapter(stockTakeTypesArrayAdapter);
        stockTakeType.setOnItemSelectedListener(SumoDayClose.this);
        selectedStockTakeType = AppConstants.STOCK_TAKE_TYPES.get(0);
        setCurrentDeviceInfo();
        progressDialog = UtilClass.utilClass.getDialogProgressBar(SumoDayClose.this).create();
        progressDialog.show();
        progressDialog.setCancelable(false);
        checkForInProgressStockTakeSumoDayCloseEvents();
    }

    private String setCurrentDeviceInfo() {
        String buildIdentityAndroid;
        try {
            String androidID = "";
            androidID = Settings.Secure.getString(this.getContentResolver(), Settings.Secure.ANDROID_ID);
            buildIdentityAndroid = Build.MANUFACTURER + "#" + Build.MODEL + "#" + androidID;
            Log.i("BUILD_INFO", buildIdentityAndroid);
        } catch (Exception e) {
            buildIdentityAndroid = Build.MANUFACTURER + "#" + Build.MODEL;
            Log.i("BUILD_INFO", buildIdentityAndroid);
        }
        UtilClass.setDeviceInfo(buildIdentityAndroid);
        return UtilClass.getDeviceInfo();
    }

    private void checkForInProgressStockTakeSumoDayCloseEvents() {
        progressDialog.show();
        Retrofit retrofit = UtilClass.getRetrofitScm();
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);
        Integer unitId = pref.getInt("unitId", -1);
        Integer userId = pref.getInt("id", -1);

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<StockTakeSumoDayCloseEventDTO> stockTakeSumoDayClose = unitsApiService.checkForStockTakeSumoDayClose(token, unitId, "IN_PROGRESS");
        stockTakeSumoDayClose.enqueue(new Callback<StockTakeSumoDayCloseEventDTO>() {
            @SuppressLint("SetTextI18n")
            @Override
            public void onResponse(@NonNull Call<StockTakeSumoDayCloseEventDTO> call, @NonNull Response<StockTakeSumoDayCloseEventDTO> response) {
                if (!response.isSuccessful()) {
                    UtilClass.utilClass.parseUnsuccessfulResponse(response, SumoDayClose.this, "checkForInProgressStockTakeSumoDayCloseEvents");
                    progressDialog.dismiss();
                    return;
                }
                if (response.body() == null) {
                    Toast.makeText(SumoDayClose.this, "Null response on checkForInProgressStockTakeSumoDayCloseEvents Call", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                    return;
                }
                StockTakeSumoDayCloseEventDTO stockTakeSumoDayCloseEventDTO = response.body();
                if (Objects.nonNull(stockTakeSumoDayCloseEventDTO.getStockTakeSumoDayCloseEventId())) {
                    startSumoDayCloseLinearLayout.setVisibility(View.VISIBLE);
                    selectStockTakeTypeLinearLayout.setVisibility(View.GONE);
                    resumeSumoDayCloseLinearLayout.setVisibility(View.VISIBLE);
                    stockTakeSumoDayCloseEvent = stockTakeSumoDayCloseEventDTO;
                    if (!stockTakeSumoDayCloseEvent.getEventCreatedBy().equals(userId)) {
                        cancelSumoDayCloseButton.setVisibility(View.GONE);
                        continueSumoDayCloseButton.setVisibility(View.GONE);
                    } else {
                        if (!UtilClass.getDeviceInfo().equalsIgnoreCase(stockTakeSumoDayCloseEvent.getDeviceInfo())) {
                            progressDialog.dismiss();
                            showDeviceInfoMismatchAlert();
                        }
                        cancelSumoDayCloseButton.setVisibility(View.VISIBLE);
                        continueSumoDayCloseButton.setVisibility(View.VISIBLE);
                    }
                    sumoDayCloseEventId.setText(stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId().toString());
                    sumoDayCloseEventInitiatedBy.setText(stockTakeSumoDayCloseEvent.getEventCreatedByName());
                    sumoDayCloseStockTakeType.setText(stockTakeSumoDayCloseEvent.getStockTakeType());
                } else {
                    resumeSumoDayCloseLinearLayout.setVisibility(View.GONE);
                    startSumoDayCloseLinearLayout.setVisibility(View.VISIBLE);
                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NonNull Call<StockTakeSumoDayCloseEventDTO> call, @NonNull Throwable t) {
                progressDialog.dismiss();
                Toast.makeText(SumoDayClose.this, " on Failure checkForInProgressStockTakeSumoDayCloseEvents Call..!", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void showDeviceInfoMismatchAlert() {
        AlertDialog.Builder alertDialog = null;
        String[] currentDeviceBuildInfo = UtilClass.getDeviceInfo().split("#");
        String currentDeviceName = currentDeviceBuildInfo[0] + " - " + currentDeviceBuildInfo[1];
        alertDialog = new AlertDialog.Builder(SumoDayClose.this);
        String[] buildInfo = stockTakeSumoDayCloseEvent.getDeviceInfo().split("#");
        String deviceName = buildInfo[0] + " - " + buildInfo[1];
        alertDialog.setTitle("Device Change Alert...!");
        alertDialog.setMessage("This Event is going on in " + deviceName + "\n Do You Continue this Event In This Device " + currentDeviceName + " ?");

            alertDialog.setPositiveButton("Yes", (dialog, which) -> {
                progressDialog.show();
                Retrofit scmRetrofit = UtilClass.getRetrofitScm();
                UnitsApiService unitsApiService = scmRetrofit.create(UnitsApiService.class);
                SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
                String token = pref.getString("jwtToken", null);

                Call<Boolean> updateEventDeviceInfoCall = unitsApiService.updateSumoDayCloseDeviceInfo(token,
                        UtilClass.getDeviceInfo(), stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
                updateEventDeviceInfoCall.enqueue(new Callback<Boolean>() {
                    @Override
                    public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                        if (!response.isSuccessful()) {
                            UtilClass.utilClass.parseUnsuccessfulResponse(response, SumoDayClose.this, "showDeviceInfoMismatchAlert");
                            progressDialog.dismiss();
                            finish();
                            return;
                        }
                        if (response.body() == null) {
                            Toast.makeText(SumoDayClose.this, "Null response on showDeviceInfoMismatchAlert Call", Toast.LENGTH_SHORT).show();
                            progressDialog.dismiss();
                            return;
                        }
                        Boolean result = response.body();
                        if (result) {
                            UtilClass.utilClass.getToast(SumoDayClose.this, "Device Info Updated Successfully..!").show();
                            makeProductsByProductView();
                        } else {
                            UtilClass.utilClass.getToast(SumoDayClose.this, "Can not Update the Device Info ...!").show();
                            finish();
                        }
                        progressDialog.dismiss();
                    }

                    @Override
                    public void onFailure(Call<Boolean> call, Throwable t) {
                        Toast.makeText(getApplicationContext(), "Error Occurred While Updating the Device Info ...!", Toast.LENGTH_SHORT).show();
                        progressDialog.dismiss();
                    }
                });
            });
            alertDialog.setNegativeButton("No", (dialog, which) -> dialog.cancel());
            alertDialog.show();
    }

    private void makeProductsByProductView() {
        startSumoDayCloseLinearLayout.setVisibility(View.GONE);
        listOfProductsLinearLayout.setVisibility(View.VISIBLE);
        if (Objects.nonNull(stockTakeSumoDayCloseEvent) &&
                Objects.nonNull(stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseProductsDTOS()) && !stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseProductsDTOS().isEmpty()) {
            Map<String, List<StockTakeSumoDayCloseProductsDTO>> result = new HashMap<>();
            for (StockTakeSumoDayCloseProductsDTO productsDTO : stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseProductsDTOS()) {
                List<StockTakeSumoDayCloseProductsDTO> productList;
                String productType;
                if (Objects.nonNull(productsDTO.getProductType())) {
                    productType = productsDTO.getProductType();
                } else {
                    productType = AppConstants.OTHERS;
                }
                if (!productTypes.contains(productType)) {
                    productTypes.add(productType);
                }
                if (result.containsKey(productType)) {
                    productList = result.get(productType);
                } else {
                    productList = new ArrayList<>();
                }
                productList.add(productsDTO);
                result.put(productType, productList);
            }
            productsByProductType = result;
            if (productTypes.contains(AppConstants.OTHERS)) {
                productTypes.remove(AppConstants.OTHERS);
                productTypes.add(AppConstants.OTHERS);
            }
            // sorting
            List<String> finalSortedList = new ArrayList<>();
            for (String orderListItem : orderList) {
                if (productTypes.contains(orderListItem)) {
                    finalSortedList.add(orderListItem);
                }
            }
            // adding Missing elements
            for (String orderListItem : productTypes) {
                if (!finalSortedList.contains(orderListItem)) {
                    finalSortedList.add(orderListItem);
                }
            }
            if (finalSortedList.contains(AppConstants.OTHERS)) {
                finalSortedList.remove(AppConstants.OTHERS);
                finalSortedList.add(AppConstants.OTHERS);
            }
            productTypes = finalSortedList;
            productTypes.add("SUMMARY");
            currentDisplayProductType = productTypes.get(0);
            listOfProductsRecyclerView.setLayoutManager(new LinearLayoutManager(this));
            productsListViewAdapter = new DayCloseProductsListViewAdapter(sortProducts(productsByProductType.get(currentDisplayProductType)), this,
                    getDecimalInputFilter(), new LinearLayoutManager(this), listOfProductsRecyclerView, true, saveProductTypeButton, currentDisplayProductType);
            productSearchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
                @Override
                public boolean onQueryTextSubmit(String query) {
                    return false;
                }

                @Override
                public boolean onQueryTextChange(String newText) {
                    productsListViewAdapter.getFilter().filter(newText);
                    return false;
                }
            });
            listOfProductsRecyclerView.setAdapter(productsListViewAdapter);
            listOfProductsRecyclerView.setVisibility(View.VISIBLE);
            previousProductTypeButton.setVisibility(View.GONE);
            setTitle();
            setSaveButtonTitle();
            workStationTypeSpinner = findViewById(R.id.work_station_type_spinner);
            ArrayAdapter<String> stockTakeTypesArrayAdapter = new ArrayAdapter<String>(SumoDayClose.this, android.R.layout.simple_spinner_item, productTypes);
            workStationTypeSpinner.setAdapter(stockTakeTypesArrayAdapter);
            workStationTypeSpinner.setOnItemSelectedListener(SumoDayClose.this);
        } else {
            UtilClass.utilClass.getToast(SumoDayClose.this, "No Product List Found to Display ..!").show();
        }
    }

    private List<StockTakeSumoDayCloseProductsDTO> sortProducts(List<StockTakeSumoDayCloseProductsDTO> stockTakeSumoDayCloseProductsDTOS) {
        boolean sortOnTime = false;
        for (StockTakeSumoDayCloseProductsDTO productsDTO : stockTakeSumoDayCloseProductsDTOS) {
            if (Objects.nonNull(productsDTO.getUpdatedTime())) {
                sortOnTime = true;
                break;
            }
        }
        if (sortOnTime) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Collections.sort(stockTakeSumoDayCloseProductsDTOS, Comparator.comparingLong(dto ->
                        dto.getUpdatedTime() != null ? dto.getUpdatedTime() : Long.MAX_VALUE));
            }
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Collections.sort(stockTakeSumoDayCloseProductsDTOS, Comparator.comparing(StockTakeSumoDayCloseProductsDTO::getProductName));
            }
        }
        return stockTakeSumoDayCloseProductsDTOS;
    }

    private void setTitle() {
        assert getSupportActionBar()!=null;
        getSupportActionBar().setTitle("Sumo Day Close - " + currentDisplayProductType);
    }
    @Override
    protected void onResume() {
        super.onResume();
        setCurrentDeviceInfo();
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.start_sumo_dayClose_button) {
            startSumoDayClose();
        } else if (v.getId() == R.id.cancel_sumo_day_close_button) {
            AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
            alertDialog.setTitle("Are You Sure.?");
            alertDialog.setMessage("Do You Want to Cancel the Event..?");
            alertDialog.setNegativeButton("NO", (dialog, which) -> {
                dialog.cancel();
            });
            alertDialog.setPositiveButton("Yes", (dialog, which) -> {
                dialog.cancel();
                cancelSumoDayClose();
            });
            alertDialog.show();
        } else if (v.getId() == R.id.continue_sumo_day_close_button) {
            continueSumoDayClose();
        } else if (v.getId() == R.id.previous_product_type_button) {
            previousProductTypeButton.setEnabled(false);
            saveProductQuantities(false, getListOfProducts(), "PREVIOUS");
            previousProductTypeButton.setEnabled(true);
        } else if (v.getId() == R.id.next_product_type_button) {
            nextProductTypeButton.setEnabled(false);
            saveProductQuantities(false, sortProducts(productsByProductType.get(currentDisplayProductType)), "NEXT");
            nextProductTypeButton.setEnabled(true);
        } else if (v.getId() == R.id.save_product_type_button) {
            saveProductTypeButton.setEnabled(false);
            if (saveProductTypeButton.getText().toString().contains("SUBMIT")) {
                List<StockTakeSumoDayCloseProductsDTO> allProductsList = new ArrayList<>();
                for (Map.Entry<String, List<StockTakeSumoDayCloseProductsDTO>> entry : productsByProductType.entrySet()) {
                    if (!entry.getKey().equalsIgnoreCase("SUMMARY")) {
                        allProductsList.addAll(entry.getValue());
                    }
                }
                saveProductQuantities(true, allProductsList, null);
            } else {
                saveProductQuantities(false, sortProducts(productsByProductType.get(currentDisplayProductType)), null);
            }
            saveProductTypeButton.setEnabled(true);
        }
    }

    private List<StockTakeSumoDayCloseProductsDTO> getListOfProducts() {
        if (currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
            return sortProducts(productsByProductType.get(productTypes.get(productTypes.size() - 2)));
        } else {
            return sortProducts(productsByProductType.get(currentDisplayProductType));
        }
    }

    private synchronized void saveProductQuantities(boolean isSubmit, List<StockTakeSumoDayCloseProductsDTO> needToSave, String nextOrPrevious) {
        List<StockTakeSumoDayCloseProductsDTO> finalListToSave = new ArrayList<>();
        List<StockTakeSumoDayCloseProductsDTO> quantityFilledList = new ArrayList<>();
        Set<Integer> allProductIds = new HashSet<>();
        for (StockTakeSumoDayCloseProductsDTO productsDTO : needToSave) {
            if (Objects.nonNull(productsDTO.getEnteredQuantity()) || Objects.nonNull(productsDTO.getSumoDayCloseProductItemId())) {
                finalListToSave.add(productsDTO);
            }
            boolean enteredPackaging = false;
            for (DayCloseProductPackagingMappingsDTO packaging : productsDTO.getDayCloseProductPackagingMappings()) {
                if (Objects.nonNull(packaging.getQuantity())) {
                    enteredPackaging = true;
                    break;
                }
            }
            if (enteredPackaging) {
                quantityFilledList.add(productsDTO);
            }
            allProductIds.add(productsDTO.getProductId());
        }
        if (!finalListToSave.isEmpty()) {
            int totalEnteredProducts = 0;
            for (StockTakeSumoDayCloseProductsDTO productsDTO : productsListViewAdapter.getOriginalProductsList()) {
                boolean enteredPackaging = false;
                for (DayCloseProductPackagingMappingsDTO packaging : productsDTO.getDayCloseProductPackagingMappings()) {
                    if (Objects.nonNull(packaging.getQuantity())) {
                        enteredPackaging = true;
                        break;
                    }
                }
                if (enteredPackaging) {
                    totalEnteredProducts++;
                }
            }
            if (isSubmit) {
                if (totalEnteredProducts != productsListViewAdapter.getOriginalProductsList().size()) {
                    UtilClass.utilClass.getToast(SumoDayClose.this, "Please Enter All Products Quantity to Submit...!").show();
                    progressDialog.dismiss();
                    return;
                }
            }
            progressDialog.show();
            Retrofit retrofit = UtilClass.getRetrofitScm();
            SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
            String token = pref.getString("jwtToken", null);
            Integer unitId = pref.getInt("unitId", -1);

            UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
            StockTakeDayCloseSaveRequest stockTakeDayCloseSaveRequest = new StockTakeDayCloseSaveRequest();
            stockTakeDayCloseSaveRequest.setProductList(finalListToSave);
            stockTakeDayCloseSaveRequest.setProductIds(allProductIds);
            Call<List<StockTakeSumoDayCloseProductsDTO>> saveSubmitCall = unitsApiService.saveSubmitDayCloseProducts(token, stockTakeDayCloseSaveRequest,
                    stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId(), isSubmit, unitId, Objects.isNull(UtilClass.getDeviceInfo()) ? setCurrentDeviceInfo() : UtilClass.getDeviceInfo());
            saveSubmitCall.enqueue(new Callback<List<StockTakeSumoDayCloseProductsDTO>>() {
                @Override
                public void onResponse(@NonNull Call<List<StockTakeSumoDayCloseProductsDTO>> call, @NonNull Response<List<StockTakeSumoDayCloseProductsDTO>> response) {
                    if (!response.isSuccessful()) {
                        UtilClass.utilClass.parseUnsuccessfulResponse(response, SumoDayClose.this, "saveProductQuantities");
                        progressDialog.dismiss();
                        return;
                    }
                    if (response.body() == null) {
                        Toast.makeText(SumoDayClose.this, "Null response on saveProductQuantities Call", Toast.LENGTH_SHORT).show();
                        progressDialog.dismiss();
                        return;
                    }
                    List<StockTakeSumoDayCloseProductsDTO> result = response.body();
                    if (result.isEmpty()) {
                        UtilClass.utilClass.getToast(SumoDayClose.this, "Something Went Wrong While Saving..!").show();
                    } else {
                        if (Objects.isNull(nextOrPrevious)) {
                            UtilClass.utilClass.getToast(SumoDayClose.this, isSubmit ? "Submitted Successfully..!" : "Saved Successfully").show();
                        }
                        if (!currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
                            if (Objects.isNull(nextOrPrevious)) {
                                productsByProductType.put(currentDisplayProductType, result);
                                productsListViewAdapter.setOriginalProductsList(sortProducts(productsByProductType.get(currentDisplayProductType)));
                                productsListViewAdapter.setPackagingEditable(true);
                                productsListViewAdapter.notifyDataSetChanged();
                            }
                        }
                        if (isSubmit) {
                            finish();
                        } else {
                            if (Objects.nonNull(nextOrPrevious)) {
                                productsByProductType.put(currentDisplayProductType, result);
                                if (nextOrPrevious.equalsIgnoreCase("PREVIOUS")) {
                                    goToPreviousProductType();
                                } else {
                                    goToNextProductType();
                                }
                            } else {
                                closeKeyboard();
                            }
                        }
                    }
                    progressDialog.dismiss();
                }

                @Override
                public void onFailure(@NonNull Call<List<StockTakeSumoDayCloseProductsDTO>> call, @NonNull Throwable t) {
                    progressDialog.dismiss();
                    Toast.makeText(SumoDayClose.this, " on Failure saveProductQuantities Call..!", Toast.LENGTH_SHORT).show();
                }
            });
        } else {
            if (Objects.nonNull(nextOrPrevious)) {
                if (nextOrPrevious.equalsIgnoreCase("PREVIOUS")) {
                    goToPreviousProductType();
                } else {
                    goToNextProductType();
                }
            } else {
                UtilClass.utilClass.getToast(SumoDayClose.this, "Please Enter Quantity Of At least 1 Product To  Save/Submit...!").show();
            }
        }
    }

    private InputFilter getDecimalInputFilter() {
        return new InputFilter() {

            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                String input = dest.subSequence(0, dstart) + source.toString() + dest.subSequence(dend, dest.length());
                if (input.startsWith(".")) {
                    input = "0" + input;
                }
                String[] parts = input.split("\\.");

                int MAX_DIGITS_BEFORE_DECIMAL = 16;
                if (parts[0].length() > MAX_DIGITS_BEFORE_DECIMAL - 1) {
                    return "";
                }

                int MAX_DECIMAL_DIGITS = 6;
                if (parts.length > 1 && parts[1].length() > MAX_DECIMAL_DIGITS) {
                    return "";
                }

                if (!input.matches("^\\d*\\.?\\d*$")) {
                    return "";
                }

                return null;
            }
        };
    }

    private void goToNextProductType() {
        progressDialog.show();
        int currentIndex = productTypes.indexOf(currentDisplayProductType);
        if (currentIndex == productTypes.size() - 2) {
            nextProductTypeButton.setVisibility(View.GONE);
            previousProductTypeButton.setVisibility(View.VISIBLE);
            currentDisplayProductType = productTypes.get(currentIndex + 1);
            if (currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
                List<StockTakeSumoDayCloseProductsDTO> allProductsList = new ArrayList<>();
                for (Map.Entry<String, List<StockTakeSumoDayCloseProductsDTO>> entry : productsByProductType.entrySet()) {
                    if (!entry.getKey().equalsIgnoreCase("SUMMARY")) {
                        allProductsList.addAll(entry.getValue());
                    }
                }
                productsListViewAdapter.setOriginalProductsList(sortProducts(allProductsList));
                productsListViewAdapter.notifyDataSetChanged();
                setTitle();
            }
        } else {
            currentDisplayProductType = productTypes.get(currentIndex + 1);
            productsListViewAdapter.setOriginalProductsList(sortProducts(productsByProductType.get(currentDisplayProductType)));
            productsListViewAdapter.notifyDataSetChanged();
            nextProductTypeButton.setVisibility(View.VISIBLE);
            previousProductTypeButton.setVisibility(View.VISIBLE);
            setTitle();
        }
        if (currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
            productsListViewAdapter.setPackagingEditable(false);
            changeSpinnerDisplayType(true);
        } else {
            productsListViewAdapter.setPackagingEditable(true);
            changeSpinnerDisplayType(false);
        }
        setSaveButtonTitle();
        productSearchView.setQuery("", false);
        closeKeyboard();
        progressDialog.dismiss();
    }

    private void changeSpinnerDisplayType(boolean setEmpty) {
        if (setEmpty) {
            workStationTypeSpinner.setSelection(productTypes.indexOf(currentDisplayProductType), false);
        } else {
            workStationTypeSpinner.setSelection(productTypes.indexOf(currentDisplayProductType));
        }
    }

    private void closeKeyboard() {
        View view = this.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private synchronized void goToPreviousProductType() {
        progressDialog.show();
        int currentIndex = productTypes.indexOf(currentDisplayProductType);
        if (currentIndex == 1) {
            previousProductTypeButton.setVisibility(View.GONE);
            nextProductTypeButton.setVisibility(View.VISIBLE);
        } else {
            previousProductTypeButton.setVisibility(View.VISIBLE);
            nextProductTypeButton.setVisibility(View.VISIBLE);
        }
        currentDisplayProductType = productTypes.get(currentIndex - 1);
        productsListViewAdapter.setOriginalProductsList(sortProducts(productsByProductType.get(currentDisplayProductType)));
        productsListViewAdapter.notifyDataSetChanged();
        setTitle();
        if (currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
            productsListViewAdapter.setPackagingEditable(false);
        } else {
            productsListViewAdapter.setPackagingEditable(true);
        }
        setSaveButtonTitle();
        productSearchView.setQuery("", false);
        closeKeyboard();
        progressDialog.dismiss();
        changeSpinnerDisplayType(false);
    }

    private synchronized void goToSelectedWorkStation(String workStationType) {
        progressDialog.show();
        int selectedIndex = productTypes.indexOf(workStationType);

        if (selectedIndex == productTypes.size() - 1) {
            nextProductTypeButton.setVisibility(View.GONE);
            previousProductTypeButton.setVisibility(View.VISIBLE);
            currentDisplayProductType = workStationType;
            if (currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
                List<StockTakeSumoDayCloseProductsDTO> allProductsList = new ArrayList<>();
                for (Map.Entry<String, List<StockTakeSumoDayCloseProductsDTO>> entry : productsByProductType.entrySet()) {
                    if (!entry.getKey().equalsIgnoreCase("SUMMARY")) {
                        allProductsList.addAll(entry.getValue());
                    }
                }
                productsListViewAdapter.setOriginalProductsList(sortProducts(allProductsList));
                productsListViewAdapter.notifyDataSetChanged();
                setTitle();
                callTitleSetter();
                progressDialog.dismiss();
                return;
            }
        } else {
            if (selectedIndex == 0) {
                previousProductTypeButton.setVisibility(View.GONE);
                nextProductTypeButton.setVisibility(View.VISIBLE);
            } else {
                previousProductTypeButton.setVisibility(View.VISIBLE);
                nextProductTypeButton.setVisibility(View.VISIBLE);
            }
        }
        currentDisplayProductType = workStationType;
        productsListViewAdapter.setOriginalProductsList(sortProducts(productsByProductType.get(currentDisplayProductType)));
        productsListViewAdapter.notifyDataSetChanged();
        callTitleSetter();
        progressDialog.dismiss();
    }

    private void callTitleSetter() {
        setTitle();
        if (currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
            productsListViewAdapter.setPackagingEditable(false);
        } else {
            productsListViewAdapter.setPackagingEditable(true);
        }
        setSaveButtonTitle();
        productSearchView.setQuery("", false);
        closeKeyboard();
        progressDialog.dismiss();
    }

    @SuppressLint("SetTextI18n")
    private void setSaveButtonTitle() {
        int totalEnteredProducts = 0;
        for (StockTakeSumoDayCloseProductsDTO productsDTO : productsListViewAdapter.getOriginalProductsList()) {
            boolean enteredPackaging = false;
            for (DayCloseProductPackagingMappingsDTO packaging : productsDTO.getDayCloseProductPackagingMappings()) {
                if (Objects.nonNull(packaging.getQuantity())) {
                    enteredPackaging = true;
                    break;
                }
            }
            if (enteredPackaging) {
                totalEnteredProducts++;
            }
        }
        if (!currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
            saveProductTypeButton.setText("SAVE " + "( " + totalEnteredProducts +"/" + productsListViewAdapter.getOriginalProductsList().size() + " )");
        } else {
            saveProductTypeButton.setText("SUBMIT " + "( " + totalEnteredProducts +"/" + productsListViewAdapter.getOriginalProductsList().size() + " )");
        }
    }

    private void cancelSumoDayClose() {
        progressDialog.show();
        Retrofit retrofit = UtilClass.getRetrofitScm();
        SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
        String token = pref.getString("jwtToken", null);

        UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
        Call<Boolean> cancelEventCall = unitsApiService.cancelStockTakeSumoDayCloseEvent(token, stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
        cancelEventCall.enqueue(new Callback<Boolean>() {
            @Override
            public void onResponse(Call<Boolean> call, Response<Boolean> response) {
                if (!response.isSuccessful()) {
                    UtilClass.utilClass.parseUnsuccessfulResponse(response, SumoDayClose.this, "cancelSumoDayClose");
                    progressDialog.dismiss();
                    finish();
                    return;
                }
                if (response.body() == null) {
                    Toast.makeText(SumoDayClose.this, "Null response on cancelSumoDayClose Call", Toast.LENGTH_SHORT).show();
                    progressDialog.dismiss();
                    return;
                }
                Boolean result = response.body();
                if (result) {
                    UtilClass.utilClass.getToast(SumoDayClose.this, "Event Cancelled Successfully..!").show();
                } else {
                    UtilClass.utilClass.getToast(SumoDayClose.this, "Can not Update Cancel the Event ...!").show();
                }
                finish();
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(Call<Boolean> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Error Occurred While cancelSumoDayClose ...!", Toast.LENGTH_SHORT).show();
                progressDialog.dismiss();
            }
        });
    }

    private void continueSumoDayClose() {
        if (!UtilClass.getDeviceInfo().equalsIgnoreCase(stockTakeSumoDayCloseEvent.getDeviceInfo())) {
            showDeviceInfoMismatchAlert();
        } else {
            makeProductsByProductView();
        }
    }

    private void startSumoDayClose() {
        if (Objects.nonNull(selectedStockTakeType)) {
            AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
            alertDialog.setTitle("Are You Sure.?");
            alertDialog.setMessage("are you sure you want to create a " + selectedStockTakeType + " Stock Take");
            alertDialog.setNegativeButton("NO", (dialog, which) -> {
                dialog.cancel();
            });
            alertDialog.setPositiveButton("Yes", (dialog, which) -> {
                dialog.cancel();
                progressDialog.show();
                Retrofit retrofit = UtilClass.getRetrofitScm();
                SharedPreferences pref = getSharedPreferences("ActivityPREF", MODE_PRIVATE);
                String token = pref.getString("jwtToken", null);
                Integer unitId = pref.getInt("unitId", -1);
                Integer userId = pref.getInt("id", -1);

                UnitsApiService unitsApiService = retrofit.create(UnitsApiService.class);
                StockTakeSumoDayCloseEventDTO stockTakeSumoDayCloseEventDTO = new StockTakeSumoDayCloseEventDTO();
                stockTakeSumoDayCloseEventDTO.setDeviceInfo(UtilClass.getDeviceInfo());
                stockTakeSumoDayCloseEventDTO.setEventCreatedBy(userId);
                stockTakeSumoDayCloseEventDTO.setUnitId(unitId);
                stockTakeSumoDayCloseEventDTO.setStockTakeType(selectedStockTakeType);
                Call<StockTakeSumoDayCloseEventDTO> stockTakeSumoDayClose = unitsApiService.startStockTakeSumoDayClose(token, stockTakeSumoDayCloseEventDTO);
                stockTakeSumoDayClose.enqueue(new Callback<StockTakeSumoDayCloseEventDTO>() {
                    @Override
                    public void onResponse(@NonNull Call<StockTakeSumoDayCloseEventDTO> call, @NonNull Response<StockTakeSumoDayCloseEventDTO> response) {
                        if (!response.isSuccessful()) {
                            UtilClass.utilClass.parseUnsuccessfulResponse(response, SumoDayClose.this, "startSumoDayClose");
                            progressDialog.dismiss();
                            return;
                        }
                        if (response.body() == null) {
                            Toast.makeText(SumoDayClose.this, "Null response on startSumoDayClose Call", Toast.LENGTH_SHORT).show();
                            progressDialog.dismiss();
                            return;
                        }
                        stockTakeSumoDayCloseEvent = response.body();
                        progressDialog.dismiss();
                        checkForInProgressStockTakeSumoDayCloseEvents();
                    }

                    @Override
                    public void onFailure(@NonNull Call<StockTakeSumoDayCloseEventDTO> call, @NonNull Throwable t) {
                        progressDialog.dismiss();
                        Toast.makeText(SumoDayClose.this, " on Failure startSumoDayClose Call..!", Toast.LENGTH_SHORT).show();
                    }
                });
            });
            alertDialog.show();
        } else {
            UtilClass.utilClass.getToast(SumoDayClose.this, "Please Select the Stock Take type..!").show();
        }
    }

    @Override
    public boolean onNavigateUp() {
        alertOnBackPress();
        return false;
    }

    @Override
    public void onBackPressed() {
        alertOnBackPress();
    }

    public void alertOnBackPress() {
        if (Objects.nonNull(currentDisplayProductType) && !currentDisplayProductType.equalsIgnoreCase("SUMMARY")) {
            saveProductQuantities(false, sortProducts(productsByProductType.get(currentDisplayProductType)), null);
        }
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
        alertDialog.setTitle("Are You Sure.?");
        alertDialog.setMessage("Do You Want to Go Back ? ");
        alertDialog.setNegativeButton("NO", (dialog, which) -> {
            dialog.cancel();
        });
        alertDialog.setPositiveButton("Yes", (dialog, which) -> {
            dialog.cancel();
            finish();
        });
        alertDialog.show();
    }

    @Override
    public void onItemSelected(AdapterView<?> adapterView, View view, int position, long id) {
        if (adapterView.getId() == R.id.stock_take_type_spinner) {
            selectedStockTakeType = AppConstants.STOCK_TAKE_TYPES.get(position);
        } else if (adapterView.getId() == R.id.work_station_type_spinner) {
            goToSelectedWorkStation(productTypes.get(position));
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }
}
