# Stock Take - Android app

Android application to perform Stock Taking operations.
## Pre-requisites

* Gradle
* Java JDK
* Android SDK
## How to make a build

In the root folder _(far-app)_ use the following command to build an APK.
1. To create a build with dev endpoints:
    * ./gradlew assembleDev
2. To create a build with local endpoints:
    * ./gradlew assembleLocal -PmPort="**_MASTER_PORT_NUMBER_**" -PsPort="**_SCM_PORT_NUMBER_**"
    
**NOTE :** _While building local build variant the endpoints gets set with the system's IP address automatically._

The APK will be generated in _**"far-app\app\build\outputs\apk"**_ directory.