package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.data.dao.BypassVendorContractDataDao;
import com.stpl.tech.scm.data.model.BypassVendorContractData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.domain.model.VendorContractStatus;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class BypassVendorContractDataDaoImpl extends SCMAbstractDaoImpl implements BypassVendorContractDataDao {

    @Override
    public BypassVendorContractData getByVendorIdAndStatusIn(Integer vendorId, List<String> status) {
        String queryString = "SELECT DISTINCT vcd FROM BypassVendorContractData vcd " +
                        "LEFT JOIN FETCH vcd.bypassVendorContractItemDataList vcid " +
                        "LEFT JOIN FETCH vcid.bypassVendorContractItemUnitDataList  " +
                        "WHERE vcd.vendorId =:vendorId AND vcd.status IN (:status) ORDER BY vcd.bypassContractId DESC";

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId", vendorId).setParameter("status", status).setMaxResults(1);
        List<BypassVendorContractData> data = query.getResultList();
        if(!CollectionUtils.isEmpty(data)) {
            return data.get(0);
        }
        return null;
    }

    @Override
    public boolean getContractStatusByVendorId(Integer vendorId) {
        String queryString = "SELECT DISTINCT vcd FROM BypassVendorContractData vcd " +
                "LEFT JOIN FETCH vcd.bypassVendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.bypassVendorContractItemUnitDataList  " +
                "WHERE vcd.vendorId =:vendorId ORDER BY vcd.bypassContractId DESC";

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId", vendorId).setMaxResults(1);
        List<BypassVendorContractData> data = query.getResultList();
        if(CollectionUtils.isEmpty(data)) {
            return false;
        }
        return data.get(0).getStatus().equals(VendorContractStatus.CREATED.name());
    }

    @Override
    public List<BypassVendorContractData> getContractSByDateAndStatus(Date startDate, Date endDate, String status) {
        StringBuilder str = new StringBuilder("SELECT DISTINCT vcd FROM BypassVendorContractData vcd" +
                                                " LEFT JOIN FETCH vcd.bypassVendorContractItemDataList vcid" +
                                                " LEFT JOIN FETCH vcid.bypassVendorContractItemUnitDataList" +
                                                " WHERE ");
        if(startDate != null) {
            str.append("vcd.startDate =:startDate ");
        }
        else {
            str.append("vcd.endDate =:endDate ");
        }
        str.append("AND vcd.status =:status ORDER BY vcd.bypassContractId DESC");
        Query query = manager.createQuery(str.toString());
        if(startDate != null) {
            query.setParameter("startDate", startDate);
        }
        else {
            query.setParameter("endDate", endDate);
        }
        query.setParameter("status", status);
        List<BypassVendorContractData> data = query.getResultList();
        return data;
    }

    @Override
    public BypassVendorContractData changeStatusOfAContract(Integer vendorId, String contractIn, String setStatus) {
        BypassVendorContractData data = getByVendorIdAndStatusIn(vendorId, List.of(contractIn));
        if(Objects.nonNull(data)) {
            data.setStatus(setStatus);
            manager.persist(data);
            return data;
        }
        return null;
    }

    @Override
    public BypassVendorContractData getByVendorIdAndStatusNotIn(Integer vendorId, List<String> status) {
        String queryString = "SELECT DISTINCT vcd FROM BypassVendorContractData vcd " +
                "LEFT JOIN FETCH vcd.bypassVendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.bypassVendorContractItemUnitDataList  " +
                "WHERE vcd.vendorId =:vendorId AND vcd.status NOT IN (:status) ORDER BY vcd.bypassContractId DESC";

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId", vendorId).setParameter("status", status).setMaxResults(1);

        List<BypassVendorContractData> data = query.getResultList();
        if(!data.isEmpty()) {
            return data.get(0);
        }
        return null;
    }

    @Override
    public BypassVendorContractData getByPassVendorContract(Integer vendorId) {
        String queryString = "SELECT DISTINCT vcd FROM BypassVendorContractData vcd " +
                "LEFT JOIN FETCH vcd.bypassVendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.bypassVendorContractItemUnitDataList  " +
                "WHERE vcd.vendorId =:vendorId ORDER BY vcd.bypassContractId DESC";

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId", vendorId).setMaxResults(1);

        List<BypassVendorContractData> data = query.getResultList();
        if(!data.isEmpty()) {
            return data.get(0);
        }
        return null;
    }

    @Override
    public BypassVendorContractData findById(Integer contractId) {
        String queryString = "SELECT DISTINCT vcd FROM BypassVendorContractData vcd " +
                "LEFT JOIN FETCH vcd.bypassVendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.bypassVendorContractItemUnitDataList  " +
                "WHERE vcd.bypassContractId =:contractId";

        Query query = manager.createQuery(queryString);
        query.setParameter("contractId", contractId).setMaxResults(1);

        List<BypassVendorContractData> data = query.getResultList();
        if(!data.isEmpty()) {
            return data.get(0);
        }
        return null;
    }

    @Override
    public VendorDetailData findByVendorId(Integer vendorId) {
        String queryString = (
                " select distinct v FROM VendorDetailData v " +
                        " left join fetch v.debitMappings " +
                        " left join fetch v.dispatchLocations as locationsAlias left join fetch locationsAlias.gstinDocument left join fetch locationsAlias.locationAddress " +
                        " left join fetch v.tdsDocument" +
                        " left join fetch v.accountDetails as  accountAlias left join fetch accountAlias.uploadedChequeDocumentID" +
                        " left join fetch v.companyDetails as companyAlias left join fetch companyAlias.cinDocument left join fetch companyAlias.arcDocument left join fetch companyAlias.panDocument" +
                        " left join fetch companyAlias.cstDocument left join fetch companyAlias.vatDocument  left join fetch companyAlias.serviceTaxDocument left join fetch companyAlias.msmeDocument " +
                        " left join fetch v.vendorAddress where v.vendorId =:vendorId");

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId", vendorId).setMaxResults(1);
        return (VendorDetailData) query.getResultList().get(0);
    }

    @Override
    public List<BypassVendorContractData> getAllInStatusAndApprover(String status, Integer approverId) {
        StringBuilder queryString = new StringBuilder("SELECT DISTINCT vcd FROM BypassVendorContractData vcd " +
                "LEFT JOIN FETCH vcd.bypassVendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.bypassVendorContractItemUnitDataList " +
                "WHERE vcd.status =:status ");

        boolean check = Objects.equals(approverId, SCMServiceConstants.SYSTEM_USER_2) || Objects.equals(approverId, SCMServiceConstants.KAPIL_GOLA_USER_ID);
        if(!check) {
            queryString.append("AND vcd.approvalRequestFrom =:approver ");
        }
        queryString.append("ORDER BY vcd.bypassContractId DESC");

        Query query = manager.createQuery(queryString.toString());
        query.setParameter("status", status);
        if(!check) {
            query.setParameter("approver", approverId.toString());
        }
        List<BypassVendorContractData> data = query.getResultList();
        return data;
    }
}
