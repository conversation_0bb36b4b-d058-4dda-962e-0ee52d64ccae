package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "RO_SCM_ITEM_EXPIRY_DRILLDOWN")
public class RoScmItemExpiryData {

    private Integer scmItemExpiryId;
    private ReferenceOrderScmItemData scmItemId;
    private String stockType;
    private Date expiryDate;
    private BigDecimal quantity;
    private Date fulfilmentDate;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SCM_ITEM_EXPIRY_ID", nullable = false, unique = true)
    public Integer getScmItemExpiryId() {
        return scmItemExpiryId;
    }

    public void setScmItemExpiryId(Integer scmItemExpiryId) {
        this.scmItemExpiryId = scmItemExpiryId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SCM_ITEM_ID", nullable = false)
    public ReferenceOrderScmItemData getScmItemId() {
        return scmItemId;
    }

    public void setScmItemId(ReferenceOrderScmItemData scmItemId) {
        this.scmItemId = scmItemId;
    }

    @Column(name = "STOCK_TYPE", nullable = true)
    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

    @Column(name = "EXPIRY_DATE", nullable = true)
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Column(name = "QUANTITY", nullable = false)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "FULFILMENT_DATE")
    public Date getFulfilmentDate() {
        return fulfilmentDate;
    }

    public void setFulfilmentDate(Date fulfilmentDate) {
        this.fulfilmentDate = fulfilmentDate;
    }
}
