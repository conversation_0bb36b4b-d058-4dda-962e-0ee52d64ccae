package com.stpl.tech.scm.data.model;

import org.joda.time.DateTime;

import javax.persistence.*;

import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "VARIANCE_ACKNOWLEDGEMENT")
public class VarianceAcknowledgementData {

    private Integer id;
    private Date businessDate;
    private Date generationTime;
    private Integer unitId;
    private BigDecimal inventoryCost;
    private BigDecimal varianceCost;
    private BigDecimal variancePercentage;
    private String frequency;
    private String acknowledged;
    private Integer acknowledgedBy;
    private Date acknowledgedTime;
    private SCMDayCloseEventData scmDayCloseEventData;
    private String acknowledgementRequired;
    private String acknowledgementType;
    private String comment;


    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "BUSINESS_DATE", nullable = true)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Column(name = "GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "INVENTORY_COST", nullable = false)
    public BigDecimal getInventoryCost() {
        return inventoryCost;
    }

    public void setInventoryCost(BigDecimal inventoryCost) {
        this.inventoryCost = inventoryCost;
    }

    @Column(name = "VARIANCE_COST", nullable = false)
    public BigDecimal getVarianceCost() {
        return varianceCost;
    }

    public void setVarianceCost(BigDecimal varianceCost) {
        this.varianceCost = varianceCost;
    }

    @Column(name = "FREQUENCY", nullable = true)
    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    @Column(name = "ACKNOWLEDGED", nullable = true)
    public String getAcknowledged() {
        return acknowledged;
    }

    public void setAcknowledged(String acknowledged) {this.acknowledged = acknowledged;}

    @Column(name = "VARIANCE_PERCENTAGE", nullable = true)
    public BigDecimal getVariancePercentage() { return variancePercentage;}

    public void setVariancePercentage(BigDecimal variancePercentage) {
        this.variancePercentage = variancePercentage;
    }

    @Column(name = "ACKNOWLEDGED_BY", nullable = true)
    public Integer getAcknowledgedBy() { return acknowledgedBy;}

    public void setAcknowledgedBy(Integer acknowledgedBy) {
        this.acknowledgedBy = acknowledgedBy;
    }

    @Column(name = "ACKNOWLEDGED_TIME", nullable = true)
    public Date getAcknowledgedTime() { return acknowledgedTime;}

    public void setAcknowledgedTime(Date acknowledgedTime) {
        this.acknowledgedTime = acknowledgedTime;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CURRENT_EVENT_ID", nullable = true)
    public SCMDayCloseEventData getCurrentDayCloseEvent() {
        return scmDayCloseEventData;
    }

    public void setCurrentDayCloseEvent(SCMDayCloseEventData scmDayCloseEventData) {
        this.scmDayCloseEventData = scmDayCloseEventData;
    }

    @Column(name = "ACKNOWLEDGEMENT_REQUIRED", nullable = true)
    public String getAcknowledgementRequired() {
        return acknowledgementRequired;
    }

    public void setAcknowledgementRequired(String acknowledgementRequired) {this.acknowledgementRequired = acknowledgementRequired;}

    @Column(name = "ACKNOWLEDGEMENT_TYPE", nullable = false)
    public String getAcknowledgementType(){return acknowledgementType;}

    public void setAcknowledgementType(String acknowledgementType) {this.acknowledgementType = acknowledgementType;}

    @Column(name = "COMMENT", nullable = true)
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {this.comment= comment;}


}
