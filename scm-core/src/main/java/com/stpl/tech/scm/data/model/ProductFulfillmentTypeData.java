package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 09-06-2016.
 */
@Entity
@Table(name = "PRODUCT_FULFILLMENT_TYPE_DATA")
public class ProductFulfillmentTypeData {

    private Integer id;
    private String fulfillmentType;
    private String status;
    private ProductDefinitionData productDefinitionData;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "FULFILLMENT_TYPE", nullable = false, length = 30)
    public String getFulfillmentType() {
        return fulfillmentType;
    }

    public void setFulfillmentType(String fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }

    @Column(name = "PRODUCT_FULFILLMENT_TYPE_STATUS", nullable = false, length = 1)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PRODUCT_DEFINITION_ID", nullable = false)
    public ProductDefinitionData getProductDefinitionData() {
        return productDefinitionData;
    }

    public void setProductDefinitionData(ProductDefinitionData productDefinitionData) {
        this.productDefinitionData = productDefinitionData;
    }
}
