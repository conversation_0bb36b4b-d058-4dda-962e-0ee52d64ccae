package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "PLAN_ORDER_MAPPING")
public class PlanOrderMappingData {

	private Integer id;
	private ProductionPlanEventData event;
	private RequestOrderData requestOrder;

	public PlanOrderMappingData() {
	}

	public PlanOrderMappingData(ProductionPlanEventData event, RequestOrderData requestOrder) {
		this.event = event;
		this.requestOrder = requestOrder;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "PLAN_ORDER_MAP_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PLAN_EVENT_ID", nullable = false)
	public ProductionPlanEventData getEvent() {
		return event;
	}

	public void setEvent(ProductionPlanEventData event) {
		this.event = event;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REQUEST_ORDER_ID", nullable = false)
	public RequestOrderData getRequestOrder() {
		return requestOrder;
	}

	public void setRequestOrder(RequestOrderData requestOrder) {
		this.requestOrder = requestOrder;
	}

}
