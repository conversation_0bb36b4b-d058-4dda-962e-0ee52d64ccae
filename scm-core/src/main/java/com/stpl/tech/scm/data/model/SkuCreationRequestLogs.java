package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.SwitchStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "SKU_CREATION_REQUEST_LOGS")
public class SkuCreationRequestLogs {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "SKU_CREATION_LOG_ID", unique = true, nullable = false)
    private Integer skuLogId;

    @JoinColumn(name = "USER_SKU_CREATION_ID", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UserSkuCreationRequestData userSkuCreationRequestData;

    @Column(name = "UPDATED_BY", nullable = false)
    private Integer updatedBy;

    @Column(name = "UPDATED_AT")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Column(name = "FROM_STATUS")
    @Enumerated(EnumType.STRING)
    private SwitchStatus fromStatus;

    @Column(name = "TO_STATUS")
    @Enumerated(EnumType.STRING)
    private SwitchStatus toStatus;

}


