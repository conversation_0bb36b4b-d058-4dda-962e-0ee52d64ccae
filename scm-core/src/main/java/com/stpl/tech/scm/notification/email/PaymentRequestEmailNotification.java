package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

public class PaymentRequestEmailNotification extends EmailNotification {
    private List<Integer> prIds;
    private String bankName;
    private Integer companyId;
    private EnvType envType;
    private List<String> toEmails;


    public PaymentRequestEmailNotification() {

    }

    public PaymentRequestEmailNotification(EnvType envType, List<Integer> prIds , String bankName , Integer companyId, List<String> toEmails ) {
        this.envType = envType;
        this.prIds  = prIds;
        this.bankName = bankName;
        this.companyId = companyId;
        this.toEmails = toEmails;


    }

    @Override
    public String[] getToEmails() {

        if (SCMUtil.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            return toEmails.toArray(new String[0]);
        }

    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Payment Sheet  For  PR IDs : " + prIds;
        ;
        if (SCMUtil.isDev(envType)) {
            subject = "[Dev]" + subject;
        }
        return subject;
    }

    public String body() throws EmailGenerationException {
        return "Please Find The Payment Sheet Attached For :" +
        "<br><b>PR Ids :</b> " + prIds + "<br>" + "<b>Bank Name :</b> " + bankName +
                "<br><b>Company Id :</b> " + companyId ;
    }


    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
