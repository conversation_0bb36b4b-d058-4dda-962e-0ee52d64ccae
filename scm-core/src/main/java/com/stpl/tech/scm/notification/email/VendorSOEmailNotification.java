package com.stpl.tech.scm.notification.email;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.notification.email.template.VendorSOEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorSOEmailNotification extends EmailNotification {

	private VendorSOEmailNotificationTemplate vendorSOEmailNotificationTemplate;
	private EnvType envType;
	private String[] emails;
	private String subjectOfEmail;
	private Boolean sendEmail;

	public VendorSOEmailNotification() {
	}

	public VendorSOEmailNotification(VendorSOEmailNotificationTemplate vendorSOEmailNotificationTemplate,
			EnvType envType, String[] emails, Boolean sendEmail) {
		this.vendorSOEmailNotificationTemplate = vendorSOEmailNotificationTemplate;
		this.envType = envType;
		this.emails = emails;
		this.sendEmail = sendEmail;
	}

	@Override
	public String[] getToEmails() {
		if (SCMUtil.isDev(envType)) {
//			return new String[]{"<EMAIL>"};
			Set<String> mails = new HashSet<>();
			Arrays.asList(emails).forEach(email -> mails.add(email));
			mails.add("<EMAIL>");
			String[] simpleArray = new String[mails.size()];
			return mails.toArray(simpleArray);
		} else {
			Set<String> mails = new HashSet<>();
			Arrays.asList(emails).forEach(email -> mails.add(email)); // adding vendor CC emails
			VendorDetail vendorDetail = vendorSOEmailNotificationTemplate.getVendorDetail();
			if (vendorDetail.getPrimaryEmail() != null) {
				mails.add(vendorDetail.getPrimaryEmail());
			}
			if (vendorDetail.getSecondaryEmail() != null) {
				mails.add(vendorDetail.getSecondaryEmail());
			}
			if (vendorSOEmailNotificationTemplate.getDispatchLocation().getContactEmail() != null) {
				mails.add(vendorSOEmailNotificationTemplate.getDispatchLocation().getContactEmail());
			}
			if(Objects.equals(vendorDetail.getIsEnterpriseVendor(), "Y")){
				mails.add("<EMAIL>");
			}
			if (sendEmail) {
				mails.add("<EMAIL>");
			}
			String[] simpleArray = new String[mails.size()];
			return mails.toArray(simpleArray);
		}
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		subjectOfEmail = String.format("Work Order From Chaayos to %s",
				vendorSOEmailNotificationTemplate.getVendorDetail().getEntityName());
		if (SCMUtil.isDev(envType)) {
			subjectOfEmail = " [DEV] : " + subjectOfEmail;
		}
		return subjectOfEmail;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return vendorSOEmailNotificationTemplate.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}
