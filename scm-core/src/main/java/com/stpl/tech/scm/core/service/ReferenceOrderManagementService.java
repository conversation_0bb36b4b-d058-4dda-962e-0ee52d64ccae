package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.impl.OrderScheduleClone;
import com.stpl.tech.scm.core.service.impl.RegularOrderUnitBrand;
import com.stpl.tech.scm.data.model.ExceptionDateEntry;
import com.stpl.tech.scm.data.model.ForecastReportResponse;
import com.stpl.tech.scm.data.model.ForecastReportScmResponse;
import com.stpl.tech.scm.data.model.SuggestiveOrderingStrategyMetadata;
import com.stpl.tech.scm.domain.model.EstimationShortData;
import com.stpl.tech.scm.domain.model.ExpiryProduct;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MenuProductSalesAverageDto;
import com.stpl.tech.scm.domain.model.RefCreateRequest;
import com.stpl.tech.scm.domain.model.ReferenceOrder;
import com.stpl.tech.scm.domain.model.ReferenceOrderResponse;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.ScmProductConsumptionAverage;
import org.apache.http.HttpResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Rahul Singh on 13-06-2016.
 */
public interface ReferenceOrderManagementService {

    public List<ReferenceOrder> getReferenceOrders(Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer referenceOrderId);

    public ReferenceOrder getReferenceOrder(int referenceOrderId);

    public Integer addReferenceOrder(ReferenceOrder referenceOrder) throws InventoryUpdateException, SumoException;

    public void  getEstimationData(int brandId, int unitId, Date targetDate, BigDecimal targetSale,BigDecimal dineInSale,BigDecimal deliverySale, String dates, Integer CategoryBuffer, Integer buffer, String productIds, String orderType);

    public List<Integer> getRequestIdsForEstimation(int unitId, List<Date> businessDate);

    public List<EstimationShortData> getEstimationQuantity(int unitId, String requestIds);

    List<IdCodeName> getExpiryProduct();

    public List<ExceptionDateEntry> generateExceptionDateEntries(List<ExceptionDateEntry> entry);

    public List<ExceptionDateEntry> getExceptionDateEntries(Date businessDate);

    public List<String> getDateWithoutException(Integer unitID,
                            Date date,Integer dayDiff,Integer calcDate);

    public BigDecimal getSalesPercentage(int unitId, int brandId, String businessDate );

    public BigDecimal getSalesAmount(int unitId, int brandId, String businessDate);

    void  cloneDayCloseDataForUnit(int newUnitId,int cloningUnitId);


    Pair<String, List<ForecastReportResponse>> getForecastReport(HttpResponse response, RefCreateRequest refCreateRequest) throws IOException;

    void convertForecastEstimationDetail(List<ForecastReportResponse> forecastReportResponseList, int unitId, List<String> days,
                                         List<EstimationShortData> remainingDayData, List<EstimationShortData> orderingDayData) ;

    public List<RegularOrderUnitBrand> getUnitOrderingSchedule(Integer unitId);

    public Boolean addUnitOrderingSchedule(List<RegularOrderUnitBrand> unitBrandList,Integer userId) throws SumoException;

    public Boolean updateUnitOrderingSchedule(List<RegularOrderUnitBrand> unitBrandList, Integer userId);

    public List<Integer> getFountain9Units(Integer unitId, Boolean isForceLookUp);

    public List<ExpiryProduct> getNewExpiryProducts(Integer unitId, Date lastDate);

    public ReferenceOrderResponse addNewReferenceOrder(ReferenceOrder referenceOrder) throws InventoryUpdateException, SumoException;

    public Boolean getFountain9ComparisonReport(List<Integer> unitIds);

    public List<RegularOrderUnitBrand> getAllOrderingSchedules();

    public String getMaxCap(int unitId, String brandName);

    public Boolean checkForF9Orders(Integer requestingUnitId, Integer fulfilmentUnitId, Date fulfilmentDate);

    public Boolean validateReferenceOrderTime(RegularOrderEvent orderEvent);

    public List<ForecastReportScmResponse> getForecastScmReport(HttpResponse httpScmResponse, RefCreateRequest refCreateRequest, String key, String requestedBy) throws IOException;

    public void saveSuggestions(List<ForecastReportResponse> value, List<ForecastReportScmResponse> forecastScmReportResponseList, int unitId);

    public Map<String, Integer> getFulfilmentUnits(Integer requestingUnitId);

    public OrderScheduleClone getAvailableUnitsForSchedule();

    public Boolean cloneUnitOrderingSchedule(OrderScheduleClone orderScheduleClone, Integer userId) throws SumoException;

    public Map<String, Map<String, Boolean>> getDayWiseBrandStatus(int unitId);

    Map<String, List<ScmProductConsumptionAverage>> getScmProductsConsumptionAverage(int unitId, String brandName, List<Date> orderDays, Map<String, BigDecimal> dayWiseOrderingPercentage) throws SumoException;

    Map<String, List<MenuProductSalesAverageDto>> getMenuProductsConsumptionAverage(int unitId, String brandName, List<LocalDate> orderDays) throws SumoException;

    ApiResponse getFreezeSuggestedQuantity(Integer loggedInUnit);

    SuggestiveOrderingStrategyMetadata checkForSuggestiveOrderingStrategyAvailable(Integer unitId);

}
