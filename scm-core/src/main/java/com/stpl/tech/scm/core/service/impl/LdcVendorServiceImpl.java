package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.mapper.LdcVendorMapper;
import com.stpl.tech.scm.core.service.LdcVendorService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.LdcVendorDao;
import com.stpl.tech.scm.data.dao.SCMVendorManagementDao;
import com.stpl.tech.scm.data.model.LdcVendorData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.domain.model.LdcVendorDomain;
import com.stpl.tech.util.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Log4j2
public class LdcVendorServiceImpl implements LdcVendorService {

    @Autowired
    LdcVendorDao ldcVendorDao;

    @Autowired
    private SCMVendorManagementDao vendorManagementDao;

    @Override
    public LdcVendorDomain addLdcForVendor(LdcVendorDomain ldcVendorDomain, Integer user) throws SumoException {
            LdcVendorData ldcVendorData = LdcVendorMapper.INSTANCE.getLdcVendorData(ldcVendorDomain);
            ldcVendorData.setVendorDetailData(vendorManagementDao.find(VendorDetailData.class, ldcVendorDomain.getVendorId()));
            ldcVendorData.setCreatedBy(user);
            ldcVendorData.setCreatedAt(SCMUtil.getCurrentTimestamp());
            ldcVendorData.setRemainingLimit(ldcVendorData.getLdcLimit());
            ldcVendorDao.save(ldcVendorData);
            ldcVendorDomain.setLdcId(ldcVendorData.getId());
           return ldcVendorDomain;
    }
    @Override
   public LdcVendorDomain deactiveLdcForVedndor(Long ldcId, Integer userId){
        Optional<LdcVendorData> ldcVendorData = ldcVendorDao.findById(ldcId);
        ldcVendorData.ifPresentOrElse(vendorData ->{
            vendorData.setStatus(AppConstants.IN_ACTIVE);
            vendorData.setUpdatedBy(userId);
            vendorData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
            ldcVendorDao.save(vendorData);
        },() -> {throw new RuntimeException("LDC Vendor Data not present for ldc id :"+ldcId);});

        return LdcVendorMapper.INSTANCE.getLdcVendorDomain(ldcVendorData.get());
    }

    @Override
    public Map<Long,LdcVendorDomain> getLdcForVendorId(Integer vendorId) throws SumoException {

       List<LdcVendorData> res =  ldcVendorDao.findByVendorDetailDataAndStatus(vendorManagementDao.find(VendorDetailData.class,vendorId),AppConstants.ACTIVE);
       return res.stream().map((e)->{
            LdcVendorDomain ldcVendorDomain = LdcVendorMapper.INSTANCE.getLdcVendorDomain(e);
            ldcVendorDomain.setVendorId(vendorId);
            ldcVendorDomain.setLdcId(e.getId());
            return ldcVendorDomain;
        }).collect(Collectors.toMap(LdcVendorDomain::getLdcId, Function.identity()));

    }

    @Override
    public LdcVendorDomain updateLdcVendorData(LdcVendorDomain ldcVendorDomain) throws SumoException{
         Optional<LdcVendorData> ldcVendorData = ldcVendorDao.findById(ldcVendorDomain.getLdcId());
         ldcVendorData.ifPresentOrElse((e)->{
                    e.setLdcTenureFrom(ldcVendorDomain.getLdcTenureFrom());
                    e.setLdcTenureTo(ldcVendorDomain.getLdcTenureTo());
                    e.setLdcCertificateNo(ldcVendorDomain.getLdcCertificateNo());
                    e.setLdcTdsSection(ldcVendorDomain.getLdcTdsSection());
                    e.setLdcTdsRate(ldcVendorDomain.getLdcTdsRate());
                    e.setLdcLimit(ldcVendorDomain.getLdcLimit());
                    ldcVendorDao.save(e);
         },()->{throw new RuntimeException("Ldc Vendor data not found for ldc id "+ldcVendorDomain.getLdcId());});
     return ldcVendorDomain;
    }

    @Override
   public Boolean updateLdcRemainingLimit(Long ldcId, Double deductedAmount) throws Exception {
        Optional<LdcVendorData> ldcVendorData = ldcVendorDao.findById(ldcId);
        ldcVendorData.ifPresentOrElse((e)->{
            if(deductedAmount > e.getRemainingLimit())  throw new RuntimeException("Deducted Amount cannot be greater than remaining limit");
            e.setRemainingLimit(e.getRemainingLimit() - deductedAmount);
            ldcVendorDao.save(e);
        },()-> {throw  new RuntimeException("Ldc Vendor data not found for ldc id "+ldcId);});
       return true;
   }


}