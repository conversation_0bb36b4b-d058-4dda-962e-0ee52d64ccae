package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "CATEGORY_CONVERSION")
public class CategoryConversionData {

    private Integer conversionId;
    private Integer oldCategory;
    private Integer newCategory;
    private String type;
    private Integer quantity;
    private Date conversionDate;
    private Integer convertedBy;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CONVERSION_ID", unique = true, nullable = false)
    public Integer getConversionId() {
        return conversionId;
    }

    public void setConversionId(Integer conversionId) {
        this.conversionId = conversionId;
    }

    @Column(name = "OLD_CATEGORY", nullable = false)
    public Integer getOldCategory() {
        return oldCategory;
    }

    public void setOldCategory(Integer oldCategory) {
        this.oldCategory = oldCategory;
    }

    @Column(name = "NEW_CATEGORY", nullable = false)
    public Integer getNewCategory() {
        return newCategory;
    }

    public void setNewCategory(Integer newCategory) {
        this.newCategory = newCategory;
    }

    @Column(name = "TYPE", nullable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "CONVERSION_QUANTITY", nullable = false)
    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Column(name = "CONVERSION_DATE", nullable = false)
    public Date getConversionDate() {
        return conversionDate;
    }

    public void setConversionDate(Date conversionDate) {
        this.conversionDate = conversionDate;
    }

    @Column(name = "CONVERTED_BY", nullable = false)
    public Integer getConvertedBy() {
        return convertedBy;
    }

    public void setConvertedBy(Integer convertedBy) {
        this.convertedBy = convertedBy;
    }
}
