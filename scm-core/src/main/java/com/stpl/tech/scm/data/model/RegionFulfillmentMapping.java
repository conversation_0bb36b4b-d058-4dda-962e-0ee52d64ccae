package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Table(name = "REGION_FULFILLMENT_MAPPING")
@Getter
@Setter
public class RegionFulfillmentMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "REGION_FULFILLMENT_MAPPING_ID")
    private Integer regionFulfillmentMappingId;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "REGION")
    private String region;

    @Column(name = "MAPPING_STATUS")
    private String mappingStatus;

    @Column(name = "COMPANY_ID")
    private Integer companyId;

    public Integer getRegionFulfillmentMappingId() {
        return this.regionFulfillmentMappingId;
    }

    public void setRegionFulfillmentMappingId(Integer regionFulfillmentMappingId) {
        this.regionFulfillmentMappingId = regionFulfillmentMappingId;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getRegion() {
        return this.region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }
}
