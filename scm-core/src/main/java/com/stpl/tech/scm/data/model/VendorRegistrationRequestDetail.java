package com.stpl.tech.scm.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "VENDOR_REGISTRATION_REQUEST_DETAIL")
public class VendorRegistrationRequestDetail {

	private Integer id;
	private String vendorName;
	private Date requestDate;
	private int requestById;
	private String requestByName;
	private int requestForId;
	private String requestForName;
	private String email;
	private String copyEmails;
	private Integer vendorId;
	private String requestStatus;
	private String registrationUrl;
	private String authKey;
	private String vendorType;
	private String panCardNumber;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "REQUEST_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "VENDOR_NAME", nullable = false)
	public String getVendorName() {
		return vendorName;
	}

	public void setVendorName(String vendor) {
		this.vendorName = vendor;
	}

	@Column(name = "REQUEST_DATE", nullable = false)
	public Date getRequestDate() {
		return requestDate;
	}

	public void setRequestDate(Date requestDate) {
		this.requestDate = requestDate;
	}

	@Column(name = "REQUESTED_BY_ID", nullable = false)
	public int getRequestById() {
		return requestById;
	}

	public void setRequestById(int requestBy) {
		this.requestById = requestBy;
	}

	@Column(name = "REQUESTED_BY_NAME", nullable = false)
	public String getRequestByName() {
		return requestByName;
	}

	public void setRequestByName(String requestByName) {
		this.requestByName = requestByName;
	}

	@Column(name = "REQUESTED_FOR_ID", nullable = false)
	public int getRequestForId() {
		return requestForId;
	}

	public void setRequestForId(int requestFor) {
		this.requestForId = requestFor;
	}

	@Column(name = "REQUESTED_FOR_NAME", nullable = false)
	public String getRequestForName() {
		return requestForName;
	}

	public void setRequestForName(String requestForName) {
		this.requestForName = requestForName;
	}

	@Column(name = "VENDOR_EMAIL", nullable = false)
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Column(name = "COPY_EMAILS", nullable = false)
	public String getCopyEmails() {
		return copyEmails;
	}

	public void setCopyEmails(String copyEmails) {
		this.copyEmails = copyEmails;
	}

	@Column(name = "VENDOR_LINK", nullable = true)
	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

	@Column(name = "REQUEST_STATUS", nullable = false)
	public String getRequestStatus() {
		return requestStatus;
	}

	public void setRequestStatus(String requestStatus) {
		this.requestStatus = requestStatus;
	}

	@Column(name = "REGISTRATION_URL", nullable = false)
	public String getRegistrationUrl() {
		return registrationUrl;
	}

	public void setRegistrationUrl(String registrationUrl) {
		this.registrationUrl = registrationUrl;
	}

	@Column(name = "AUTH_KEY", nullable = false)
	public String getAuthKey() {
		return authKey;
	}

	public void setAuthKey(String authKey) {
		this.authKey = authKey;
	}

	@Column(name = "VENDOR_TYPE", nullable = false)
	public String getVendorType() {
		return vendorType;
	}

	public void setVendorType(String vendorType) {
		this.vendorType = vendorType;
	}

	@Column(name = "PAN_CARD", nullable = false)
	public String getPanCardNumber() {
		return panCardNumber;
	}

	public void setPanCardNumber(String panCardNumber) {
		this.panCardNumber = panCardNumber;
	}
}
