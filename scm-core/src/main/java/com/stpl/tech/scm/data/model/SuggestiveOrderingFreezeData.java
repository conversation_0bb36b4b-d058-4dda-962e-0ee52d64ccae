package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "SUGGESTIVE_ORDERING_FREEZE")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SuggestiveOrderingFreezeData {

    @Id
    @Column(name = "FREEZE_ID", unique = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer freezeId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "UNIT_NAME")
    private String unitName;

    @Column(name = "UNIT_CITY")
    private String unitCity;

    @Column(name = "PRODUCT_ID")
    private Integer productId;

    @Column(name = "PRODUCT_NAME")
    private String productName;

    @Column(name = "STATUS")
    private String status;

}
