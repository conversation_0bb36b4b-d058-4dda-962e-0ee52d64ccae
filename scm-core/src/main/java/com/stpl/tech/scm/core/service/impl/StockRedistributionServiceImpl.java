package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.mapper.DomainDataMapper;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.service.StockRedistributionService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.StockRedistributionDao;
import com.stpl.tech.scm.data.dao.impl.MutexFactory;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.RiderInfoData;
import com.stpl.tech.scm.data.model.RiderRoutePlanData;
import com.stpl.tech.scm.data.model.RiderRoutePlanItemData;
import com.stpl.tech.scm.data.model.RiderRoutePlanStepData;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.RiderActionEnum;
import com.stpl.tech.scm.domain.model.RiderRoutePlanDataDto;
import com.stpl.tech.scm.domain.model.RiderRoutePlanItemDataDto;
import com.stpl.tech.scm.domain.model.RiderRoutePlanStatusEnum;
import com.stpl.tech.scm.domain.model.RiderRoutePlanStepDataDto;
import com.stpl.tech.scm.domain.model.RiderRouteStepStatusEnum;
import com.stpl.tech.scm.domain.model.SCMOrderPackaging;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.TransferOrderItem;
import com.stpl.tech.scm.domain.model.TransferOrderType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Log4j2
public class StockRedistributionServiceImpl implements StockRedistributionService {

    @Autowired
    private StockRedistributionDao stockRedistributionDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private StockManagementService stockManagementService;

    @Autowired
    private MutexFactory<String> factory;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private SCMProductManagementService scmProductManagementService;
    
    @Autowired
    private TransferOrderManagementService transferOrderManagementService;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RiderRoutePlanDataDto> getRiderRoutePlanByEmployeeId(Integer employeeId) throws SumoException {
        try {
            RiderInfoData riderInfoData = stockRedistributionDao.findRiderInfoByEmployeeId(employeeId);
            if (Objects.nonNull(riderInfoData)) {
                List<RiderRoutePlanData> riderRoutePlanDataList = stockRedistributionDao.getRoutePlanOfRiderByStatus(riderInfoData, Arrays.asList(RiderRoutePlanStatusEnum.CREATED,
                        RiderRoutePlanStatusEnum.IN_PROGRESS), true);
                return riderRoutePlanDataList.stream().map(DomainDataMapper.INSTANCE::riderRoutePlanDataToRiderRoutePlanDataDto)
                        .sorted(Comparator.comparing(RiderRoutePlanDataDto::getRiderRoutePlanDataId))
                        .map(this::setDataRelatedToRiderRoute).toList();
            } else {
                throw new SumoException("Something went wrong..!", "Rider Not Found with Employee Id : " + employeeId);
            }
        } catch (Exception e) {
            log.error("Exception Occurred while getRiderRoutePlanByEmployeeId for employee Id : {} ", employeeId, e);
            throw new SumoException("Something went wrong..!", e.getMessage());
        }
    }

    private RiderRoutePlanDataDto setDataRelatedToRiderRoute(RiderRoutePlanDataDto riderRoutePlanDataDto) {
        if (!CollectionUtils.isEmpty(riderRoutePlanDataDto.getRiderRoutePlanStepDataSet())) {
            Set<RiderRoutePlanStepDataDto> sortedStepDataSet = riderRoutePlanDataDto.getRiderRoutePlanStepDataSet().stream()
                    .filter(riderRoutePlanStepDataDto -> !CollectionUtils.isEmpty(riderRoutePlanStepDataDto.getRiderRoutePlanItemDataSet()))
                    .sorted(Comparator.comparing(RiderRoutePlanStepDataDto::getRiderRoutePlanStepDataId))
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            sortedStepDataSet.forEach(riderRoutePlanStepDataDto -> {
                Set<RiderRoutePlanItemDataDto> sortedItemDataList = riderRoutePlanStepDataDto.getRiderRoutePlanItemDataSet().stream()
                        .sorted(Comparator.comparing(RiderRoutePlanItemDataDto::getRiderRoutePlanItemDataId)) // Sort items by ID
                        .collect(Collectors.toCollection(LinkedHashSet::new));

                sortedItemDataList.forEach(riderRoutePlanItemDataDto -> {
                    riderRoutePlanItemDataDto.setProductName(
                            scmCache.getProductDefinition(riderRoutePlanItemDataDto.getProductId()).getProductName()
                    );
                    riderRoutePlanItemDataDto.setPackagingName(
                            scmCache.getPackagingDefinition(riderRoutePlanItemDataDto.getPackagingId()).getPackagingName()
                    );
                    riderRoutePlanItemDataDto.setExpiryDate(AppUtils.getBusinessDate());
                });
                riderRoutePlanStepDataDto.setRiderRoutePlanItemDataSet(sortedItemDataList);

            });
            riderRoutePlanDataDto.setRiderRoutePlanStepDataSet(sortedStepDataSet);
        }
        return riderRoutePlanDataDto;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateRiderRoutePlanItem(Integer riderRoutePlanItemDataId, BigDecimal finalQuantity, Integer loggedInUser, String comment) throws SumoException {
        try {
            log.info("Received Request to update RiderRoutePlanItemData of : {} with Quantity : {}",riderRoutePlanItemDataId, finalQuantity);
            RiderRoutePlanItemData riderRoutePlanItemData = stockRedistributionDao.find(RiderRoutePlanItemData.class, riderRoutePlanItemDataId);
            if (Objects.nonNull(riderRoutePlanItemData)) {
                RiderRoutePlanStepData riderRoutePlanStepData = riderRoutePlanItemData.getRiderRoutePlanStepData();
                RiderInfoData riderInfo = stockRedistributionDao.findRiderInfoByEmployeeId(loggedInUser);
                Map<Integer, BigDecimal> finalQuantitiesByProduct = Collections.singletonMap(riderRoutePlanItemData.getProductId(), finalQuantity);
                Map<Integer, BigDecimal> inventory;
                if (riderRoutePlanItemData.getRiderAction().equals(RiderActionEnum.DROP)) {
                    inventory = stockManagementService.getScmInventoryOfKeys(riderInfo.getUnitId(),
                            new ArrayList<>(finalQuantitiesByProduct.keySet()), PriceUpdateEntryType.PRODUCT, false);
                    validateWithLiveInventory(finalQuantitiesByProduct,inventory, true);
                } else {
                    // verifying inventory of current unit id not riders
                    inventory = getInventoryOfUnit(riderInfo, riderRoutePlanStepData, finalQuantitiesByProduct);
                    validateWithLiveInventory(finalQuantitiesByProduct, inventory, false);
                }
                updateStatusOfRouteAndRouteSteps(riderRoutePlanStepData);
                riderRoutePlanItemData.setFinalQuantity(finalQuantity);
                riderRoutePlanItemData.setComment(comment);
                stockRedistributionDao.update(riderRoutePlanItemData, true);
                return true;
            } else {
                throw new SumoException("Item Not Found ..!", "Can not find a product to update ..!");
            }
        } catch (Exception e) {
            log.error("Exception Occurred while updateRiderRoutePlanItem for riderRoutePlanItemDataId Id : {} ", riderRoutePlanItemDataId, e);
            throw new SumoException("Something went wrong..!", e.getMessage());
        }
    }

    private void updateStatusOfRouteAndRouteSteps(RiderRoutePlanStepData riderRoutePlanStepData) {
        if (!RiderRouteStepStatusEnum.IN_PROGRESS.equals(riderRoutePlanStepData.getRouteStepStatus())) {
            riderRoutePlanStepData.setRouteStepStatus(RiderRouteStepStatusEnum.IN_PROGRESS);
            riderRoutePlanStepData.setRiderReachedTime(AppUtils.getCurrentTimestamp());
            RiderRoutePlanData riderRoutePlanData = riderRoutePlanStepData.getRiderRoutePlanData();
            if (RiderRoutePlanStatusEnum.CREATED.equals(riderRoutePlanData.getRiderRoutePlanStatus())) {
                riderRoutePlanData.setRiderRoutePlanStatus(RiderRoutePlanStatusEnum.IN_PROGRESS);
                riderRoutePlanData.setRideStartTime(AppUtils.getCurrentTimestamp());
                stockRedistributionDao.update(riderRoutePlanStepData, true);
            }
            stockRedistributionDao.update(riderRoutePlanStepData, true);
        }
    }

    private void validateWithLiveInventory(Map<Integer, BigDecimal> finalQuantitiesByProduct, Map<Integer, BigDecimal> inventory, boolean isRiderUnit) throws SumoException {
        List<String> exceptionMessages = new ArrayList<>();
        for (Map.Entry<Integer, BigDecimal> entry : finalQuantitiesByProduct.entrySet()) {
            if (inventory.containsKey(entry.getKey())) {
                if (entry.getValue().compareTo(inventory.get(entry.getKey())) > 0) {
                    String msg  = (isRiderUnit ? "You" : "Unit" )+ " Do not Have Inventory Of this Product " + scmCache.getProductDefinition(entry.getKey()).getProductName() + "<br>" +
                                    "Available Inventory : " + inventory.get(entry.getKey()) + " <br>" +
                                    "Quantity You are trying to transfer : " + entry.getValue();
                    exceptionMessages.add(msg);
                }
            } else {
                if (entry.getValue().compareTo(BigDecimal.ZERO) != 0) {
                    String msg = (isRiderUnit ? "You" : "Unit" ) + " Do not Have Inventory Of this Product " + scmCache.getProductDefinition(entry.getKey()).getProductName();
                    exceptionMessages.add(msg);
                }
            }
        }
        if (!CollectionUtils.isEmpty(exceptionMessages)) {
            throw new SumoException("Inventory Not Available..!", "Errors : " + Arrays.asList(exceptionMessages.toArray()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean completeRouteStepOfRide(Integer riderRoutePlanStepDataId, RiderActionEnum riderAction, Integer loggedInUser, BigDecimal temperature) throws SumoException {
        try {
            log.info("Received Request to Complete route Step : {} of : {}",riderRoutePlanStepDataId, riderAction);
            RiderInfoData riderInfo = stockRedistributionDao.findRiderInfoByEmployeeId(loggedInUser);
            List<RiderRoutePlanStepData> riderRouteStepDetails = stockRedistributionDao.getRiderRouteStepDetails(riderRoutePlanStepDataId);
            if (!CollectionUtils.isEmpty(riderRouteStepDetails)) {
                RiderRoutePlanStepData riderRoutePlanStepData = riderRouteStepDetails.get(0);
                checkIfActionIsAlreadyCompleted(riderRoutePlanStepData, riderAction);
                updateStatusOfRouteAndRouteSteps(riderRoutePlanStepData);
                Map<Integer, BigDecimal> inventory;
                Map<Integer, BigDecimal> finalQuantitiesByProduct = riderRoutePlanStepData.getRiderRoutePlanItemDataSet().stream().filter(e -> e.getRiderAction().equals(riderAction))
                        .collect(Collectors.groupingBy(RiderRoutePlanItemData::getProductId, Collectors.reducing(BigDecimal.ZERO, RiderRoutePlanItemData::getFinalQuantity, SCMUtil::add)));
                if (RiderActionEnum.DROP.equals(riderAction)) {
                    riderRoutePlanStepData.setDropStatus(RiderActionEnum.DROP_COMPLETED);
                    riderRoutePlanStepData.setDropTime(AppUtils.getCurrentTimestamp());
                    riderRoutePlanStepData.setDropTemperature(temperature);
                    if (RiderActionEnum.NOT_REQUIRED.equals(riderRoutePlanStepData.getPickupStatus()) || RiderActionEnum.PICKUP_COMPLETED.equals(riderRoutePlanStepData.getPickupStatus())) {
                        riderRoutePlanStepData.setRouteStepStatus(RiderRouteStepStatusEnum.COMPLETED);
                        riderRoutePlanStepData.setRiderLeaveTime(AppUtils.getCurrentTimestamp());
                    }
                    // will revalidate inventory again
                    inventory = stockManagementService.getScmInventoryOfKeys(riderInfo.getUnitId(),
                            new ArrayList<>(finalQuantitiesByProduct.keySet()), PriceUpdateEntryType.PRODUCT, false);
                    validateWithLiveInventory(finalQuantitiesByProduct,inventory, true);
                } else if (RiderActionEnum.PICKUP.equals(riderAction)) {
                    riderRoutePlanStepData.setPickupStatus(RiderActionEnum.PICKUP_COMPLETED);
                    riderRoutePlanStepData.setPickupTime(AppUtils.getCurrentTimestamp());
                    riderRoutePlanStepData.setPickupTemperature(temperature);
                    if (RiderActionEnum.NOT_REQUIRED.equals(riderRoutePlanStepData.getDropStatus()) || RiderActionEnum.DROP_COMPLETED.equals(riderRoutePlanStepData.getDropStatus())) {
                        riderRoutePlanStepData.setRouteStepStatus(RiderRouteStepStatusEnum.COMPLETED);
                        riderRoutePlanStepData.setRiderLeaveTime(AppUtils.getCurrentTimestamp());
                    }
                    inventory = getInventoryOfUnit(riderInfo, riderRoutePlanStepData, finalQuantitiesByProduct);
                    validateWithLiveInventory(finalQuantitiesByProduct, inventory, false);
                }
                boolean isToRequired = getIsToRequired(riderRoutePlanStepData);
                if (isToRequired) {
                    Integer transferOrderId = createTransferAndReceiving(riderInfo, riderRoutePlanStepData, riderAction);
                    if (RiderActionEnum.DROP.equals(riderAction)) {
                        riderRoutePlanStepData.setDropOffToId(transferOrderId);
                    } else {
                        riderRoutePlanStepData.setPickedUpToId(transferOrderId);
                    }
                }
                stockRedistributionDao.update(riderRoutePlanStepData, true);
                return true;
            } else {
                throw new SumoException("Item Not Found ..!", "Can not find a Route to Submit ..!");
            }
        } catch (Exception e) {
            log.error("Exception Occurred while completeRouteStepOfRide for riderRoutePlanStepDataId Id : {} ", riderRoutePlanStepDataId, e);
            throw new SumoException("Something went wrong..!", e.getMessage());
        }
    }

    private boolean getIsToRequired(RiderRoutePlanStepData riderRoutePlanStepData) {
        for (RiderRoutePlanItemData riderRoutePlanItemData : riderRoutePlanStepData.getRiderRoutePlanItemDataSet()) {
            if (riderRoutePlanItemData.getFinalQuantity().compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
        }
        return false;
    }

    private Map<Integer, BigDecimal> getInventoryOfUnit(RiderInfoData riderInfo, RiderRoutePlanStepData riderRoutePlanStepData, Map<Integer, BigDecimal> finalQuantitiesByProduct) throws SumoException, URISyntaxException {
        Map<Integer, BigDecimal> inventory;
        UnitDetail unitDetail = scmCache.getUnitDetail(riderRoutePlanStepData.getCurrentStore());
        if (SCMUtil.isWareHouseOrKitchen(unitDetail)) {
            List<Integer> skuIdsToVerifyInventory = new ArrayList<>();
            Map<Integer, List<SkuDefinition>> skuByProduct = scmProductManagementService.viewAllSkuByProduct();
            for (Map.Entry<Integer, BigDecimal> entry : finalQuantitiesByProduct.entrySet()) {
                ProductDefinition productDefinition = scmCache.getProductDefinition(entry.getKey());
                List<SkuDefinition> skuDefinitions = skuByProduct.getOrDefault(entry.getKey(), new ArrayList<>()).stream()
                        .filter(skuDefinition -> skuDefinition.getSkuStatus().equals(SwitchStatus.ACTIVE)).toList();
                if (CollectionUtils.isEmpty(skuDefinitions)) {
                    throw new SumoException("No Active Sku Found ..!", "No Active Sku Found For Product " + productDefinition.getProductName() + " ( " + productDefinition.getProductId() + " )");
                }
                skuIdsToVerifyInventory.add(skuDefinitions.get(0).getSkuId());
            }
            Map<Integer, BigDecimal> skuInventory = stockManagementService.getScmInventoryOfKeys(riderRoutePlanStepData.getCurrentStore(),
                    skuIdsToVerifyInventory, PriceUpdateEntryType.SKU, false);
            inventory = convertSkuToProductInventory(skuInventory);
        } else {
            inventory = stockManagementService.getLiveStock(riderRoutePlanStepData.getCurrentStore());
        }
        return inventory;
    }

    private Map<Integer, BigDecimal> convertSkuToProductInventory(Map<Integer, BigDecimal> skuInventory) {
        Map<Integer, BigDecimal> result = new HashMap<>();
        skuInventory.forEach((skuId, quantity) -> {
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuId);
            result.put(skuDefinition.getLinkedProduct().getId(), quantity);
        });
        return result;
    }

    private void checkIfActionIsAlreadyCompleted(RiderRoutePlanStepData riderRoutePlanStepData, RiderActionEnum riderAction) throws SumoException {
        if (RiderActionEnum.DROP.equals(riderAction)) {
            if (Objects.nonNull(riderRoutePlanStepData.getDropOffToId())) {
                throw new SumoException("Action already Performed ..!", riderAction.name() + " is already completed.Please Refresh ..!");
            }
        } else {
            if (Objects.nonNull(riderRoutePlanStepData.getPickedUpToId())) {
                throw new SumoException("Action already Performed ..!", riderAction.name() + " is already completed.Please Refresh ..!");
            }
        }
    }

    private Integer createTransferAndReceiving(RiderInfoData riderInfo, RiderRoutePlanStepData riderRoutePlanStepData, RiderActionEnum riderAction)
            throws SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, ParseException {
        TransferOrder transferOrder = createToObject(riderInfo, riderRoutePlanStepData, riderAction);
        log.info("Initialize Mutex TO Creation for Generation Unit {}, For Unit {} and RO ID {}",
                transferOrder.getGeneratedForUnitId().getName(), transferOrder.getGeneratedForUnitId().getName(),
                transferOrder.getRequestOrderId());
        long mutextWaitTime = System.currentTimeMillis();
        long totalTimeStart = System.currentTimeMillis();
        Integer transferOrderId;
        log.info("Before Mutex TO Creation for Generation Unit {}, For Unit {} and RO ID {}, TO_CREATION_TIME_CALCULATION_LOCK_START_TIME = {}",
                transferOrder.getGeneratedForUnitId().getName(), transferOrder.getGeneratedForUnitId().getName(),
                transferOrder.getRequestOrderId(), System.currentTimeMillis() - mutextWaitTime);
        synchronized (factory.getMutex(transferOrder.getGenerationUnitId().getId() + "_TO_ID")) {
            log.info("After Mutex TO Creation for Generation Unit {}, For Unit {} and RO ID {}, TO_CREATION_TIME_CALCULATION_LOCK_WAIT_TIME = {}",
                    transferOrder.getGeneratedForUnitId().getName(), transferOrder.getGeneratedForUnitId().getName(),
                    transferOrder.getRequestOrderId(), System.currentTimeMillis() - mutextWaitTime);
            long toActualStartTime = System.currentTimeMillis();
            int sourceCompanyId = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId()).getCompany()
                    .getId();
            int receivingCompanyId = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId()).getCompany()
                    .getId();
            boolean isDifferentCompany = sourceCompanyId != receivingCompanyId;
            String invoiceId = transferOrderManagementService.getInvoiceId(transferOrder.getGenerationUnitId().getId(),
                    transferOrder.getGeneratedForUnitId().getId(), isDifferentCompany);
            transferOrderId = transferOrderManagementService.createTransferOrder(transferOrder, false, invoiceId);
            log.info("Completed Mutex TO Creation for Generation Unit {}, For Unit {} and RO ID {}, TO_CREATION_TIME_CALCULATION_ONLY_TO_TIME {}",
                    transferOrder.getGeneratedForUnitId().getName(), transferOrder.getGeneratedForUnitId().getName(),
                    transferOrder.getRequestOrderId(), System.currentTimeMillis() - toActualStartTime);
            log.info("Transfer-order Actual Time consumed for request Order id {} is {} ms ",
                    transferOrder.getRequestOrderId(), System.currentTimeMillis() - toActualStartTime);
        }
        long totalTimeend = System.currentTimeMillis();
        log.info("Transfer-order API Time consumed for request Order id {} is {} ms ", transferOrder.getRequestOrderId(), totalTimeend - totalTimeStart);
        // now i have the TO , will do the GR for that TO automatically
        log.info("Trying To Auto GR for transfer order Id ::::{}",transferOrderId);
        GoodsReceivedData grData =  transferOrderManagementService.getGrByTO(transferOrderId);
        grData.setAutoGenerated(AppUtils.setStatus(true));
        stockRedistributionDao.update(grData, true);
        GoodsReceived goodsReceivedDetail = goodsReceiveManagementService.getGoodsReceivedDetail(grData.getId());
        goodsReceivedDetail.setReceivedBy(SCMUtil.getSystemUser());
        goodsReceivedDetail.setComment(SCMServiceConstants.STOCK_REDISTRIBUTION);
        for(GoodsReceivedItem item : goodsReceivedDetail.getGoodsReceivedItems()){
            item.setReceivedQuantity(item.getTransferredQuantity());
            for(SCMOrderPackaging packaging : item.getPackagingDetails()){
                packaging.setReceivedQuantity(packaging.getTransferredQuantity());
                packaging.setNumberOfUnitsReceived(packaging.getReceivedQuantity()/packaging.getConversionRatio());
            }
        }
        goodsReceiveManagementService.settleGoodsReceivedDetail(goodsReceivedDetail);
        return transferOrderId;
    }

    private TransferOrder createToObject(RiderInfoData riderInfo, RiderRoutePlanStepData riderRoutePlanStepData, RiderActionEnum riderAction) throws SumoException {
        TransferOrder transferOrder = new TransferOrder();
        transferOrder.setComment(SCMServiceConstants.STOCK_REDISTRIBUTION);
        transferOrder.setGeneratedBy(new IdCodeName(riderInfo.getEmployeeId(), "", masterDataCache.getEmployee(riderInfo.getEmployeeId())));
        if (riderAction.equals(RiderActionEnum.DROP)) {
            transferOrder.setGenerationUnitId(new IdCodeName(riderInfo.getUnitId(), "", scmCache.getUnitDetail(riderInfo.getUnitId()).getUnitName()));
            transferOrder.setGeneratedForUnitId(new IdCodeName(riderRoutePlanStepData.getCurrentStore(), "", scmCache.getUnitDetail(riderRoutePlanStepData.getCurrentStore()).getUnitName()));
        } else {
            transferOrder.setGenerationUnitId(new IdCodeName(riderRoutePlanStepData.getCurrentStore(), "", scmCache.getUnitDetail(riderRoutePlanStepData.getCurrentStore()).getUnitName()));
            transferOrder.setGeneratedForUnitId(new IdCodeName(riderInfo.getUnitId(), "", scmCache.getUnitDetail(riderInfo.getUnitId()).getUnitName()));
        }
        transferOrder.setStatus(SCMOrderStatus.CREATED);
        transferOrder.setToType(TransferOrderType.REGULAR_TRANSFER);
        transferOrder.setLastUpdatedBy(new IdCodeName(riderInfo.getEmployeeId(), "", masterDataCache.getEmployee(riderInfo.getEmployeeId())));

        Map<Integer, List<SkuDefinition>> skuByProduct = scmProductManagementService.viewAllSkuByProduct();

        List<TransferOrderItem> transferOrderItems = new ArrayList<>();
        for (RiderRoutePlanItemData item : riderRoutePlanStepData.getRiderRoutePlanItemDataSet()) {
            if (item.getRiderAction().equals(riderAction) && item.getFinalQuantity().compareTo(BigDecimal.ZERO) > 0) {
                TransferOrderItem transferOrderItem = new TransferOrderItem();
                ProductDefinition productDefinition = scmCache.getProductDefinition(item.getProductId());
                transferOrderItem.setTransferredQuantity(item.getFinalQuantity().floatValue());
                // need to get the SKu id of the Product
                List<SkuDefinition> skuDefinitions =  getSkuDefinitions(skuByProduct, item, productDefinition);
                SkuDefinition selectedSku = skuDefinitions.get(0);
                transferOrderItem.setSkuId(selectedSku.getSkuId());
                transferOrderItem.setSkuName(selectedSku.getSkuName());
                transferOrderItem.setProductId(item.getProductId());
                transferOrderItem.setUnitOfMeasure(productDefinition.getUnitOfMeasure());
                transferOrderItem.setUnitPrice(selectedSku.getUnitPrice().doubleValue());
                transferOrderItem.setNegotiatedUnitPrice(selectedSku.getNegotiatedUnitPrice().doubleValue());
                // create the packaging details
                List<SkuPackagingMapping> loosePackagingOfSku = selectedSku.getSkuPackagings().stream().filter(e -> scmCache.getPackagingDefinition(e.getPackagingId()).getPackagingType().equals(PackagingType.LOOSE)).toList();
                if (CollectionUtils.isEmpty(skuDefinitions)) {
                    throw new SumoException("No Loose Packaging Found for SKU ..!", "No Loose Packaging Found for SKU " + selectedSku.getSkuName() + " ( " + selectedSku.getSkuId() + " )");
                }
                SkuPackagingMapping skuPackagingMapping = loosePackagingOfSku.get(0);
                List<SCMOrderPackaging> packagingDetails = new ArrayList<>();
                SCMOrderPackaging scmOrderPackaging = new SCMOrderPackaging();
                scmOrderPackaging.setTransferredQuantity(item.getFinalQuantity().floatValue());
                scmOrderPackaging.setNumberOfUnitsPacked(item.getFinalQuantity().floatValue());
                scmOrderPackaging.setPackagingDefinitionData(scmCache.getPackagingDefinition(skuPackagingMapping.getPackagingId()));
                packagingDetails.add(scmOrderPackaging);
                transferOrderItem.setPackagingDetails(packagingDetails);
                transferOrderItems.add(transferOrderItem);
            }
        }
        transferOrder.setTransferOrderItems(transferOrderItems);
        return transferOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void checkAndMarkRideAsCompleted(Integer riderRoutePlanDataId) throws SumoException {
        try {
            log.info("Checking to mark Ride as completed For riderRoutePlanDataId : {}",riderRoutePlanDataId);
            List<RiderRoutePlanData> riderRoutePlanDataList = stockRedistributionDao.getRouteDetailsByRoutePlanId(riderRoutePlanDataId);
            if (!CollectionUtils.isEmpty(riderRoutePlanDataList)) {
                RiderRoutePlanData riderRoutePlanData = riderRoutePlanDataList.get(0);
                List<RiderRoutePlanStepData> pendingRouteSteps = riderRoutePlanData.getRiderRoutePlanStepDataSet().stream().filter(e ->
                        (!RiderRouteStepStatusEnum.COMPLETED.equals(e.getRouteStepStatus()) && !RiderRouteStepStatusEnum.SKIPPED.equals(e.getRouteStepStatus()))).toList();
                if (CollectionUtils.isEmpty(pendingRouteSteps)) {
                    riderRoutePlanData.setRiderRoutePlanStatus(RiderRoutePlanStatusEnum.COMPLETED);
                    riderRoutePlanData.setRideEndTime(AppUtils.getCurrentTimestamp());
                }
                // TODO before marking the ride as completed check the riders unit inventory and if any stock left need to deliver it to the nearest hub in the route , if stock value < 500 can drop at current unit if not go to the nearest hub

                stockRedistributionDao.update(riderRoutePlanData, true);
            } else {
                throw new SumoException("Route Plan Not Found ..!", "Can not find a Route to Submit ..!");
            }
        } catch (Exception e) {
            log.error("Exception Occurred while checkAndMarkRideAsCompleted for riderRoutePlanDataId Id : {} ", riderRoutePlanDataId, e);
            throw new SumoException("Something went wrong..!", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, Pair<String, BigDecimal>> getRiderUnitInventory(Integer loggedInUser) throws SumoException {
       try {
           log.info("Received a Request to get the Inventory of a rider Unit with employee id : {}", loggedInUser);
           RiderInfoData riderInfo = stockRedistributionDao.findRiderInfoByEmployeeId(loggedInUser);
           return stockManagementService.fetchUnitProductInventory(riderInfo.getUnitId(), PriceUpdateEntryType.PRODUCT.name())
                   .stream()
                   .collect(Collectors.groupingBy(
                           CostDetail::getKeyId, // Group by keyId
                           Collectors.collectingAndThen(
                                   Collectors.reducing(
                                           new Pair<>("", BigDecimal.ZERO),
                                           data -> new Pair<>(scmCache.getProductDefinition(data.getKeyId()).getProductName(), data.getQuantity()),
                                           (pair1, pair2) -> new Pair<>(pair2.getKey(), SCMUtil.add(pair1.getValue(), pair2.getValue()))
                                   ),
                                   Function.identity()
                           )
                   ));
       } catch (Exception e) {
           log.error("Exception Occurred while getRiderUnitInventory for Employee Id : {} ", loggedInUser, e);
           throw new SumoException("Something went wrong..!", e.getMessage());
       }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setPackagingInfoToRouteItems() throws SumoException {
        try {
            log.info("Received Request to set setPackagingInfoToRouteItems ::");
            List<RiderRoutePlanData> pendingRides = stockRedistributionDao.getRoutePlanOfRiderByStatus(null,
                    Collections.singletonList(RiderRoutePlanStatusEnum.CREATED), true);
            Map<Integer, List<SkuDefinition>> skuByProduct = scmProductManagementService.viewAllSkuByProduct();
            List<RiderRoutePlanItemData> needToUpdateList = new ArrayList<>();
            for (RiderRoutePlanData riderRoutePlanData : pendingRides) {
                for (RiderRoutePlanStepData riderRoutePlanStepData : riderRoutePlanData.getRiderRoutePlanStepDataSet()) {
                    for (RiderRoutePlanItemData itemData : riderRoutePlanStepData.getRiderRoutePlanItemDataSet()) {
                        if (Objects.isNull(itemData.getPackagingId())) {
                            ProductDefinition productDefinition = scmCache.getProductDefinition(itemData.getProductId());
                            List<SkuDefinition> skuDefinitions = getSkuDefinitions(skuByProduct, itemData, productDefinition);
                            List<SkuPackagingMapping> loosePackagingOfSku = skuDefinitions.get(0).getSkuPackagings().stream().filter(e -> scmCache.getPackagingDefinition(e.getPackagingId()).getPackagingType().equals(PackagingType.LOOSE)).toList();
                            if (CollectionUtils.isEmpty(skuDefinitions)) {
                                throw new SumoException("No Loose Packaging Found for SKU ..!", "No Loose Packaging Found for SKU " + skuDefinitions.get(0).getSkuName() + " ( " + skuDefinitions.get(0).getSkuId() + " )");
                            }
                            itemData.setPackagingId(loosePackagingOfSku.get(0).getPackagingId());
                            needToUpdateList.add(itemData);
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(needToUpdateList)) {
                stockRedistributionDao.update(needToUpdateList, true);
            }
            return true;
        } catch (Exception e) {
            log.error("Exception Occurred while setPackagingInfoToRouteItems", e);
            throw new SumoException("Something went wrong..!", e.getMessage());
        }
    }

    private List<SkuDefinition> getSkuDefinitions(Map<Integer, List<SkuDefinition>> skuByProduct, RiderRoutePlanItemData itemData, ProductDefinition productDefinition) throws SumoException {
        List<SkuDefinition> skuDefinitions = skuByProduct.getOrDefault(itemData.getProductId(), new ArrayList<>()).stream()
                .filter(skuDefinition -> skuDefinition.getSkuStatus().equals(SwitchStatus.ACTIVE)).toList();
        if (CollectionUtils.isEmpty(skuDefinitions)) {
            throw new SumoException("No Active Sku Found ..!", "No Active Sku Found For Product " + productDefinition.getProductName() + " ( " + productDefinition.getProductId() + " )");
        }
        return skuDefinitions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void expirePendingRoutePlans() {
        try {
            List<RiderRoutePlanData> needToExpireRoutePlans = stockRedistributionDao.getRoutePlanOfRiderByStatus(null,
                    Arrays.asList(RiderRoutePlanStatusEnum.CREATED, RiderRoutePlanStatusEnum.IN_PROGRESS), false);
            if (!CollectionUtils.isEmpty(needToExpireRoutePlans)) {
                for (RiderRoutePlanData riderRoutePlanData : needToExpireRoutePlans) {
                    riderRoutePlanData.setRiderRoutePlanStatus(RiderRoutePlanStatusEnum.EXPIRED);
                }
                stockRedistributionDao.update(needToExpireRoutePlans, true);
            }
        } catch (Exception e) {
            log.error("Exception Occurred while expirePendingRoutePlans", e);
        }
    }
}
