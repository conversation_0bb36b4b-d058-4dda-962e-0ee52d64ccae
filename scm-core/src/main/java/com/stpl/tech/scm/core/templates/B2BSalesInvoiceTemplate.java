package com.stpl.tech.scm.core.templates;

import com.fasterxml.jackson.databind.util.CompactStringObjectMap;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.NumberToWord;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class B2BSalesInvoiceTemplate extends AbstractVelocityTemplate {

    private SalesPerformaInvoice salesPerformaInvoice;
    private String basePath;
    private Company company;
    private Unit unitData;
    private String totalAmountInWords;
    private String barCodeLink;


    public B2BSalesInvoiceTemplate() {
    }

    public B2BSalesInvoiceTemplate(SalesPerformaInvoice salesPerformaInvoice, String basePath, Company company, Unit unitData, String totalAmountInWords, String barCodeLink) {
        this.salesPerformaInvoice = salesPerformaInvoice;
        this.basePath = basePath;
        this.company=company;
        this.unitData=unitData;
        this.totalAmountInWords=totalAmountInWords;
        this.barCodeLink = barCodeLink;
    }

    @Override
    public String getTemplatePath() {
        return "templates/B2BInvoiceTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + salesPerformaInvoice.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("invoice", salesPerformaInvoice);
        stringObjectMap.put("totalAmountInWords", totalAmountInWords);
        stringObjectMap.put("unitData",unitData);
        stringObjectMap.put("company",company);
        stringObjectMap.put("dateTool", new DateTool());
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("barCodeLink", barCodeLink);
        return stringObjectMap;
    }
}
