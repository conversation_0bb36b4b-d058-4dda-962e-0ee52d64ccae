package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.AssetDefinitionDataLog;
import com.stpl.tech.scm.data.model.AssetDepreciationMappingData;
import com.stpl.tech.scm.data.model.AssetRecoveryData;
import com.stpl.tech.scm.data.model.AssetRecoveryDefinitionData;
import com.stpl.tech.scm.data.model.AssetRecoveryDetailData;
import com.stpl.tech.scm.data.model.GatepassData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.StockEventAssetMappingDefinitionData;
import com.stpl.tech.scm.data.model.StockEventDefinitionData;
import com.stpl.tech.scm.domain.model.*;
import org.apache.commons.math3.util.Pair;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Abhishek Sirohi on 07-05-2016.
 */
public interface SCMAssetManagementService {

    public List<AssetDefinition> addNewAssets(List<AssetDefinition> assetDefinitions)throws DataUpdationException, SumoException;

    public AssetDefinition addNewAsset(AssetDefinition assetDefinition)throws DataUpdationException, SumoException;

    public AssetDefinition createBackDatedAsset(AssetDefinition assetDefinition) throws  SumoException;

    public AssetDefinition viewAsset(int assetId);

    public boolean printAssetTagValue(int assetId, int printedBy) throws SumoException;

    public AssetDefinitionSlimObject viewAssetByTagValue(String tagValue);

    public List<AssetDefinition> viewAllAssets();

    public List<AssetDefinition> viewAllAssetsFromUnit(int unitId);

    public List<AssetDefinition> viewAllAssetsFromUnitWithStatus(int unitId, String assetStatus);

    public List<AssetDefinition> viewAllTransferableAssetsFromUnit(int unitId, boolean removeAssetWithPriceZero);

    public List<FixedAssetCompactDefinition> viewAllFixedAssetsFromUnit(int unitId);

    public List<AssetDefinition> getAllAssetWithGRItemId(int grItemId);

    public AssetDefinition updateAsset(AssetDefinition assetDefinition) throws SumoException;

    public AssetDefinition completeAssetGeneration(AssetDefinition assetDefinition) throws SumoException;

    public StockTakeInitResponse initiateNewStockEvent(StockEventDefinition stockEventDefinition, String isSplit) throws SumoException;

    public StockEventDefinition initiateChildStockEvent(StockEventDefinition stockEventDefinition) throws SumoException;

    List<StockEventDefinitionData> getParentOrChildEvent(Integer eventId, Integer parentId, String subCategory);

    List<StockEventAssetMappingDefinitionData> findAssetsByEventId(Integer eventId);

    List<StockEventDefinitionData> getLastStockTakeEventByAsset(Integer assetId, String eventStatus);

    StockEventDefinitionData getLatestNSOEventByUnit(int unitId, String eventStatus, String subtype);

    public List<String> getSubTypeListForApp(Integer unitId) throws SumoException;

    public Boolean checkFaDaycloseEnabled(Integer unitId);

    Map<String, Pair<Date,Long>> getPendingDayCloseEvents(Integer unitId);

    public StockEventDefinition viewEvent(int eventId);

    public StockEventDefinition updateStockEvent(StockEventDefinition stockEventDefinition,Boolean childEventCheck) throws  SumoException;

    public boolean transferAssetAgainstGR(GoodsReceivedData goodsReceivedData, List<RejectedGrItem> rejectedGrItems,Integer eventId) throws SumoException, InventoryUpdateException;

    public boolean transferAssetAgainstGatepass(
            List<GatepassItemAssetMapping> gatepassItemAssetMappings, GatepassData gatepassData) throws SumoException;

    public AssetDefinition updateAssetStatus(AssetDefinitionData assetDefinition, AssetStatusType assetStatusType) throws SumoException;

    public List<AssetDefinition> massAssetPutToUse() throws SumoException;

    public AssetDefinition updateAssetStatus(int assetId, String assetStatus) throws SumoException;


    public AssetDefinitionDataLog logCurrentStatus(AssetDefinitionData assetDefinitionData) throws SumoException;

    public List<ProfileAttributeMapping> getProfileAttributeMappingsByProfile(int profileId, String status);

    public StockTakeInitResponse getAllEventByUnit(int unitId, String status ,Integer roId, Integer userId) throws SumoException;

    public StockEventAssetMappingDefinitionRequest verifyAndCreateEventAssetMapping(
            StockEventAssetMappingDefinitionRequest stockEventAssetMappingDefinitionRequest) throws  SumoException;

    public List<GatepassItemAssetMapping> getGatepassItemAssetMappingsForGatepassItemId(int gatepassItemAssetMappingId);

    public boolean transferAssetOnGatepassReturn(Gatepass gatepass) throws SumoException ;

    public List<EntityAssetMapping> createEntityAssetMapping(List<EntityAssetMapping> entityAssetMappings) throws SumoException;

    public List<EntityAssetMapping> getAssociatedEntityAssetMapping(int entityId, int entitySubId, String entityType);

    public boolean transferAssetAgainstInvoice(SalesPerformaInvoice salesPerformaInvoice) throws SumoException;


    public List<AssetDepreciationMappingData> createAssetDepreciationMapping(
            AssetDefinitionData assetDefinitionData, Date startDate, Date endDate) throws SumoException ;

    public List<AssetDepreciationMappingData> createAssetDepreciationBetween(Date startDate, Date endDate ) throws SumoException;

    public BigDecimal getCurrentValueOfAsset(int assetId) throws ParseException;


    public void depreciationCalculationOnLastDayOfMonth() throws  SumoException;

    public List<String> getStockTakeList();

    public Map<String, Boolean> getStockTakeSubMap();

    public List<AssetRecoveryDefinition> getAllAssetsInRecoveryFromUnit(int unitId, String recoveryStatus);

    public AssetRecoveryDefinition convertToRecoveryDefinition(AssetRecoveryDefinitionData data);

    public AssetRecovery convertToRecoveryDefinition(AssetRecoveryData data) throws SumoException;

    public AssetRecoveryDetail convertToAssetRecoveryDetail(AssetRecoveryDetailData data);

    AssetRecoveryDetailData convertToAssetRecoveryDetailData(AssetRecoveryDetail data);

    public boolean recoverAssetList(List<AssetRecoveryDefinition> assetRecoveryDefinitions) throws SumoException;

    public boolean initiateAssetRecovery(List<AssetRecoveryDefinition> assetRecoveryDefinitions) throws SumoException;

    public List<String> assetStatusTypeList();

    public BigDecimal getDepreciationOnUnitByDate(int unitId , Date businessDate);

    public BigDecimal getLostAssetAmountOnUnitByDate(int unitId, Date businessDate);

    public BigDecimal getDamageAssetAmountOnUnitByDate(int unitId, Date businessDate);

    public BigDecimal getDepreciationOnUnitByMonth(int unitId , Date monthStartDate);

    public BigDecimal getLostAssetAmountOnUnitByMonth(int unitId, Date monthStartDate);

    public BigDecimal getDamageAssetAmountOnUnitByMonth(int unitId, Date monthStartDate);

    public BigDecimal getDepreciationOnUnit(int unitId, Date businessDate,StockTakeType type);

    public Map<Integer, BigDecimal> getDepreciationOnUnitForCategories(int unitId, Date businessDate);

    public BigDecimal getLostAssetAmountOnUnit(int unitId, Date monthStartDate,StockTakeType type);

    public BigDecimal getDamageAssetAmountOnUnit(int unitId, Date monthStartDate,StockTakeType type);

    public List<AssetDefinition> viewAllTransferableAssetsFromUnitByProducts(int unitId, List<Integer> productIds, boolean removeAssetWithPriceZero);

    public List<AssetDefinitionSlimObject> viewAllAssetsSlimFromUnitByName(int unitId, String name);

    public List<AssetDefinitionSlimObject> viewAllAssetsSlimFromUnit(int unitId);

    public List<String> getEmailHeirarchy(Integer unitId, boolean unitEmailInclude);

    public AssetDefinitionData updateOwnerAndUnitAndTransferData(AssetDefinitionData assetDefinitionData, String unitType, Integer unitId, boolean changeOwner, AssetStatusType nextStatus) throws SumoException;

    public RejectedGr getRejectedGr(List<RejectedGrItem> rejectedGrItems);

    public Boolean setInTransit(List<Integer> assetIds , Boolean status);

    Integer transferAssets(List<AssetDefinitionData> assetDefinitionDataList, Integer unitId, Integer userId, Integer receivingUnitId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException;

    public Map<Integer, List<AssetDefinition>> getMonkIngredientsAssets(Integer productId , Integer unitId) throws DataNotFoundException, SumoException;

    public List<Integer> autoTransferOfMonkIngredients(Map<Integer,BigDecimal> skuQuantityMap,List<Integer> assetIds, Integer unitId, Integer userId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException, ParseException;

    public Map<Integer, BigDecimal> getMonkIngredientsStock(List<Integer> productIds, Integer unitId);

    public Boolean printBulkAssetTagValue(List<Integer> assetIds, Integer printedBy) throws SumoException;

    public Boolean markNotFoundInAudit(String assetTag, Integer updatedBy);

    public Map<Integer,BigDecimal> getNonAssetStockInAssetInventory();

    public Boolean convertNonAssetStockInAssetInventory(List<AssetDefinitionData> assetDefinitionDataList) throws SumoException;

    public Boolean transferAndConvertNonAssetsInAssetInventory(Map<Integer,BigDecimal> productQtyMap,  Integer userId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException, ParseException;

    public Boolean saveNonScannableAssetMappings(List<Integer> productIds);

    public List<Integer> getNonScannableAssetMappings();

    public AssetDefinition convertAssetDefinitionDataToAssetDefinition(AssetDefinitionData assetDefinitionData, boolean setAttributes);

    public Boolean pauseFAStockTakeEvent(StockEventAssetMappingDefinitionRequest request) throws SumoException;

    Boolean submitFoundAssetEvent(Integer eventId) throws SumoException;

    public Boolean submitFAStockTakeEvent(StockEventAssetMappingDefinitionRequest request) throws SumoException ;

    public Boolean updateDeviceInfo(Integer eventId, String deviceInfo) throws SumoException ;

    public  Integer getFaTransferSubmitEvent(Integer unitId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException;

    public  Boolean saveExcessAssetFoundData(MultipartFile file , String comment , String userDescription , Integer eventId) throws SumoException;

    public Boolean sendFAStockTakeReport(Integer eventId, Boolean submit);

    public Map<String, List<AssetRecovery>> getAssetsPendingRecovery(Integer unitId, String recoveryStatus, String assetName, Integer assetId, String startDate, String endDate) throws SumoException;

    public Boolean submitRecovery(Integer recoveryId, List<AssetRecoveryDetail> recoveryList) throws SumoException;

    public Boolean editAssetName(Integer assetId, String newName, Integer userId) throws SumoException;

    String getStockTakeAppVersion(boolean reloadCache);

    Boolean saveInvalidTag(Integer eventId, String tagValue) throws SumoException;

    Boolean convertFaToConsumable(List<Integer> assetIds, Integer userId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException, ParseException;

    Boolean convertConsumableToFa(List<Integer> skuIds, Integer userId) throws SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException;

    public Boolean updateAssetToCache(Integer assetId) throws SumoException;

    AssetDefinition getAndSaveLostAssetData(String assetTagValue, Integer eventId) throws SumoException;

    Boolean startUnitClosureRegularEventAndDayClose(List<Integer> unitIds) throws SumoException;

    Boolean checkLiveInvWithDayCloseClosing(Integer unitId) throws URISyntaxException;

}
