package com.stpl.tech.scm.notification.email;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.LostAssetEmailObject;
import com.stpl.tech.scm.notification.email.template.LostAssetNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
public class LostAssetNotification extends EmailNotification {

    @Autowired
    private MasterDataCache masterDataCache;

    private LostAssetEmailObject lostAsset;
    private LostAssetNotificationTemplate template;
    private List<String> emails;
    private EnvType envType;
    private Boolean isInsuranceRecovery;

    @Override
    public String[] getToEmails() {
        return !SCMUtil.isProd(envType) ? new String[] { "<EMAIL>" } : (String[])this.emails.toArray();
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {

        String subject =
                " Recovery for Asset ID "+ lostAsset.getAssetId() + " & Tag " + lostAsset.getAssetTag() + " at " +
                masterDataCache.getUnitBasicDetail(lostAsset.getUnitId()).getName() +
                " ("+ lostAsset.getUnitId().toString()  + ") on "
                + SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
        if(isInsuranceRecovery){
            subject = "Insurance" + subject;
        }else{
            subject = "Employee" + subject;
        }
        subject ="Lost Asset " + subject;

        if (!SCMUtil.isProd(envType)) {
            subject = "[DEV] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
