package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.data.transport.model.CriticalProductFF;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataUnitLevel;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataWarehouseLevel;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.MathTool;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class FullfillmentReportEmailNotificationTemplate extends AbstractVelocityTemplate {

    ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelLastDay;
    ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelLastThiryDay;

    List<CriticalProductFF> criticalProductSummary;

    String basePath;

    public FullfillmentReportEmailNotificationTemplate(){

    }

    public FullfillmentReportEmailNotificationTemplate(ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelLastDay,
                                                       ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelLastThiryDay, String basePath,
                                                       List<CriticalProductFF> criticalProductSummary){
        this.fullfillmentDataWarehouseLevelLastDay= fullfillmentDataWarehouseLevelLastDay;
        this.fullfillmentDataWarehouseLevelLastThiryDay = fullfillmentDataWarehouseLevelLastThiryDay;
        this.basePath = basePath;
        this.criticalProductSummary = criticalProductSummary;
    }
    @Override
    public String getTemplatePath() {
        return "templates/FullfillmentReportTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath+"/fullfillment-report/fullfillment-report-temp-"+System.currentTimeMillis()+".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String,Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("ffDetailLastDay",fullfillmentDataWarehouseLevelLastDay);
        stringObjectMap.put("ffDetailLastThD",fullfillmentDataWarehouseLevelLastThiryDay);
        stringObjectMap.put("criticalProducts1",criticalProductSummary.stream().filter(e -> Objects.equals(e.getProductLevel(),"LEVEL_1")).sorted((a, b)->Double.compare(a.getMtdFF(),b.getMtdFF())).collect(Collectors.toList()));
        stringObjectMap.put("criticalProducts2",criticalProductSummary.stream().filter(e -> Objects.equals(e.getProductLevel(),"LEVEL_2")).sorted((a,b)->Double.compare(a.getMtdFF(),b.getMtdFF())).collect(Collectors.toList()));
        stringObjectMap.put("criticalProducts3", criticalProductSummary.stream().filter(e -> Objects.equals(e.getProductLevel(),"LEVEL_3")).sorted((a,b)->Double.compare(a.getMtdFF(),b.getMtdFF())).collect(Collectors.toList()));
        stringObjectMap.put("mathTool",new MathTool());

        DateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        Date endDate = AppUtils.getPreviousDate(AppUtils.getCurrentTimestamp());
        int month = AppUtils.getMonth(endDate);
        int year  = AppUtils.getYear(endDate);
        Date startDate = AppUtils.getStartDateOfMonth(year,month);
        stringObjectMap.put("prevDay",dateFormat.format(endDate));
        stringObjectMap.put("thisMonth",dateFormat.format(startDate)+" - "+dateFormat.format(endDate));
        return stringObjectMap;
    }
}
