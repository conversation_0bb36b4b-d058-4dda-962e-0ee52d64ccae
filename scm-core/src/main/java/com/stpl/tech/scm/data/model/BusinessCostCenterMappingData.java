package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "BUSINESS_COST_CENTER_MAPPING_DATA")
public class BusinessCostCenterMappingData {
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "MAPPING_ID")
	private Integer mappingId;
	@Column(name = "COST_CENTER_ID")
	private Integer costCenterId;
	@Column(name = "BUSINESS_COST_CENTER_ID")
	private Integer businessCostCenterId;
	@Column(name = "MAPPING_STATUS")
	private String mappingStatus;


}
