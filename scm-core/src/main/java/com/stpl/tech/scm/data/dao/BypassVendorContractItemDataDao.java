package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.BypassVendorContractItemData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface BypassVendorContractItemDataDao extends JpaRepository<BypassVendorContractItemData,Integer> {
    List<BypassVendorContractItemData> findByVendorIdAndStatus(Integer vendorId, String approved);

    BypassVendorContractItemData findByVendorIdAndSkuIdAndSkuPackagingIdAndDispatchLocationAndDeliveryLocation(Integer vendorId, Integer skuId, Integer skuPackagingId, String dispatch, String delivery);

    BypassVendorContractItemData findByVendorIdAndSkuIdAndSkuPackagingIdAndSkuPriceDataIdAndDispatchLocationAndDeliveryLocation(Integer vendorId, Integer skuId, Integer skuPackagingId, Integer skuPriceDataId, String dispatch, String delivery);

    @Query("SELECT DISTINCT vcid FROM BypassVendorContractItemData vcid " +
            "LEFT JOIN FETCH vcid.bypassVendorContractItemUnitDataList " +
            "WHERE vcid.skuPriceDataId IN (:skuPriceDataIds) ORDER BY vcid.bypassContractItemId DESC")
    List<BypassVendorContractItemData> findItemsAndUnitsBySkuPriceDataIds(@Param("skuPriceDataIds") Set<Integer> skuPriceDataIds);

    @Query("SELECT COUNT(*), SUM(CASE WHEN STATUS=?2 THEN 1 ELSE 0 END)" +
            " FROM BypassVendorContractItemData WHERE bypassContract.bypassContractId=?1")
    List<Object[]> getTotalRemovedOfByPassContractId(Integer byPassContractId, String removed);
}
