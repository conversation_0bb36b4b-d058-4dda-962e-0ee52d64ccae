package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class F9ComparisonEmailNotificationTemplate extends AbstractVelocityTemplate {

    private List<Date> dates;
    private Unit unit;
    private String basePath;

    public F9ComparisonEmailNotificationTemplate() {
    }

    public F9ComparisonEmailNotificationTemplate(List<Date> dates, Unit unit, String basePath) {
        this.dates = dates;
        this.unit = unit;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "templates/F9ComparisonEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/cafe/f9comparison/" + unit.getName() + "/" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("startDate",dates.get(0));
        stringObjectMap.put("endDate",dates.get(dates.size() - 1));
        return stringObjectMap;
    }

    public Unit getUnit() {
        return unit;
    }

    public List<Date> getDates() {
        return dates;
    }
}
