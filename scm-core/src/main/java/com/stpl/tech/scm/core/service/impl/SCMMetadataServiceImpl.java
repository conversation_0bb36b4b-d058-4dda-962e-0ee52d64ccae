/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.util.AssetHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.AuditChangeLogDao;
import com.stpl.tech.scm.data.dao.AuditChangeLogHistoryDao;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.SCMProductManagementDao;
import com.stpl.tech.scm.data.redis.dao.AssetDefinitionDao;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.AttributeDefinitionData;
import com.stpl.tech.scm.data.model.AttributeValueData;
import com.stpl.tech.scm.data.model.AuditLogData;
import com.stpl.tech.scm.data.model.CapexTemplateData;
import com.stpl.tech.scm.data.model.CategoryAttributeMappingData;
import com.stpl.tech.scm.data.model.CategoryAttributeValueData;
import com.stpl.tech.scm.data.model.CategoryDefinitionData;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.InventoryListTypeData;
import com.stpl.tech.scm.data.model.ListDetailData;
import com.stpl.tech.scm.data.model.MilkBreadBypassData;
import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.PaymentDeviationData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.ProductDefinitionLogs;
import com.stpl.tech.scm.data.model.ProductPackagingMappingData;
import com.stpl.tech.scm.data.model.ProfileAttributeMappingData;
import com.stpl.tech.scm.data.model.ProfileDefinitionData;
import com.stpl.tech.scm.data.model.RegionFulfillmentMapping;
import com.stpl.tech.scm.data.model.SkuAttributeValueData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingMappingData;
import com.stpl.tech.scm.data.model.SubCategoryDefinitionData;
import com.stpl.tech.scm.data.model.UnitCategoryData;
import com.stpl.tech.scm.data.model.UnitDetailData;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.ZipCodeDistanceMapping;
import com.stpl.tech.scm.data.mongo.AuditChangeLog;
import com.stpl.tech.scm.data.mongo.AuditChangeLogHistory;
import com.stpl.tech.scm.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.DeactivateValidateResponse;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ListDetail;
import com.stpl.tech.scm.domain.model.MapResponse;
import com.stpl.tech.scm.domain.model.MilkBreadBypass;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PaymentDeviation;
import com.stpl.tech.scm.domain.model.PendingMilkBread;
import com.stpl.tech.scm.domain.model.PendingMilkBreadItem;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProfileAttributeMapping;
import com.stpl.tech.scm.domain.model.ProfileDefinition;
import com.stpl.tech.scm.domain.model.SCMUnitCategory;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.notification.email.ChangesDiffNotification;
import com.stpl.tech.scm.notification.email.template.ChangesDiffNotificationTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import lombok.SneakyThrows;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.stpl.tech.scm.data.converter.SCMDataConverter.convert;
import static java.util.stream.Collectors.joining;

/**
 * Created by Rahul Singh on 04-05-2016.
 */

@Service
public class SCMMetadataServiceImpl implements SCMMetadataService {

	private static final Logger LOG = LoggerFactory.getLogger(SCMMetadataServiceImpl.class);


	@Autowired
	private SCMMetadataDao scmMetadataDao;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private AuditChangeLogDao auditChangeLogDao;

	@Autowired
	private AuditChangeLogHistoryDao auditChangeLogHistoryDao;

	@Autowired
	private EnvProperties props;

	@Autowired
	private SCMNotificationService scmNotificationService;

	@Autowired
	private AssetDefinitionDao assetDefinitionDao;

	@Autowired
	private SCMAssetManagementDao scmAssetManagementDao;

	@Autowired
	private SCMProductManagementDao scmProductManagementDao;


	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<AttributeDefinition> getAllAttributeDefinitions() {
		List<AttributeDefinition> attributeDefinitionList = new ArrayList<AttributeDefinition>();
		for (AttributeDefinitionData data : scmMetadataDao.findAll(AttributeDefinitionData.class)) {
			attributeDefinitionList.add(SCMDataConverter.convert(data));
		}
		return attributeDefinitionList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CategoryDefinition> getAllCategoryDefinitions() {
		List<CategoryDefinition> categoryDefinitions = new ArrayList<CategoryDefinition>();
		for (CategoryDefinitionData data : scmMetadataDao.findAll(CategoryDefinitionData.class)) {
			categoryDefinitions.add(SCMDataConverter.convert(data));
		}
		return categoryDefinitions;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SubCategoryDefinition> getAllSubCategoryDefinitions() {
		List<SubCategoryDefinition> subCategoryDefinitions = new ArrayList<SubCategoryDefinition>();
		for (SubCategoryDefinitionData data : scmMetadataDao.findAll(SubCategoryDefinitionData.class)) {
			subCategoryDefinitions.add(SCMDataConverter.convert(data));
		}
		return subCategoryDefinitions;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PackagingDefinition> getAllPackagingDefinitions() {
		List<PackagingDefinition> packagingDefinitions = new ArrayList<PackagingDefinition>();
		for (PackagingDefinitionData data : scmMetadataDao.findAll(PackagingDefinitionData.class)) {
			packagingDefinitions.add(SCMDataConverter.convert(data));
		}
		return packagingDefinitions;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<AssetDefinition> getAssetDefinitions() {
		List<AssetDefinition> assetDefinitions = new ArrayList<AssetDefinition>();
		for(AssetDefinitionData data : scmMetadataDao.findAll(AssetDefinitionData.class)) {
			IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
					masterDataCache.getEmployees().get(data.getCreatedBy()));
			IdCodeName lastTagPrintedBy = data.getLastTagPrintedBy() != null ? SCMUtil.generateIdCodeName(data.getLastTagPrintedBy(), "",
					masterDataCache.getEmployees().get(data.getLastTagPrintedBy())) : null;
			IdCodeName lastTransferedBy = data.getLastTransferedBy() != null ?  SCMUtil.generateIdCodeName(data.getLastTransferedBy(), "",
					masterDataCache.getEmployees().get(data.getLastTransferedBy())) : null;
			BigDecimal currentValue = AssetHelper.getCurrentValueOfAsset(data);
//			IdCodeName subCategory = SCMUtil.generateIdCodeName(productDefinitionData.getSubCategoryDefinition().getId(),
//					productDefinitionData.getSubCategoryDefinition().getCode(),
//					productDefinitionData.getSubCategoryDefinition().getName());
			assetDefinitions.add(SCMDataConverter.convert(data, createdBy, lastTagPrintedBy, lastTransferedBy, currentValue, null));
		}
		return assetDefinitions;
	}





	@Override
	@SneakyThrows
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductDefinition> getAllProductDefinitions() {
		List<ProductDefinition> productDefinitions = new ArrayList<ProductDefinition>();
		Map<Integer, String> employees = masterDataCache.getEmployees();
		for (ProductDefinitionData data : scmMetadataDao.findAll(ProductDefinitionData.class)) {
			IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
					employees.get(data.getCreatedBy()));
			productDefinitions.add(SCMDataConverter.convert(data, createdBy));
		}
		return productDefinitions;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, List<ProfileAttributeMapping>> getProfileAttributeMappings() {
		Map<Integer, List<ProfileAttributeMapping>> profileAttributeMappings = new TreeMap<Integer, List<ProfileAttributeMapping>>();
		for(ProfileAttributeMappingData data: scmMetadataDao.findAll(ProfileAttributeMappingData.class) ) {
			IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
					masterDataCache.getEmployees().get(data.getCreatedBy()));
			IdCodeName updatedBy = SCMUtil.generateIdCodeName(data.getUpdatedBy(), "",
					masterDataCache.getEmployees().get(data.getUpdatedBy()));
			List<ProfileAttributeMapping> list = profileAttributeMappings.get(data.getProfileDefinitionData().getProfileId());
			if(list == null){
				list = new ArrayList<ProfileAttributeMapping>();
			}
			list.add(SCMDataConverter.convert(data, createdBy, updatedBy));
			profileAttributeMappings.put(data.getProfileDefinitionData().getProfileId(), list);
		}
		return profileAttributeMappings;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProfileDefinition> getAllProfileDefinitions() {
		List<ProfileDefinition> profileDefinitions = new ArrayList<ProfileDefinition>();
		for (ProfileDefinitionData data: scmMetadataDao.findAll(ProfileDefinitionData.class)) {
			IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
					masterDataCache.getEmployees().get(data.getCreatedBy()));
			profileDefinitions.add(SCMDataConverter.convert(data, createdBy));
		}
		return profileDefinitions;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SkuDefinition> getAllSkuDefinitions() {
		List<SkuDefinition> skuDefinitions = new ArrayList<SkuDefinition>();
		Map<Integer, String> employees = masterDataCache.getEmployees();
		for (SkuDefinitionData data : scmMetadataDao.findAllSkus()) {
			IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
					employees.get(data.getCreatedBy()));
			skuDefinitions.add(SCMDataConverter.convert(data, createdBy));
		}
		return skuDefinitions;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitDetail> getAllUnitDetails() {
		List<UnitDetail> unitDetails = new ArrayList<UnitDetail>();
		for (UnitDetailData data : scmMetadataDao.findAll(UnitDetailData.class)) {
			unitDetails.add(SCMDataConverter.convert(data));
		}
		return unitDetails;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SCMUnitCategory> getAllUnitCategories() {
		List<SCMUnitCategory> scmUnitCategories = new ArrayList<SCMUnitCategory>();
		for (UnitCategoryData data : scmMetadataDao.findAll(UnitCategoryData.class)) {
			scmUnitCategories.add(SCMDataConverter.convert(data));
		}
		return scmUnitCategories;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CategoryAttributeMapping> getAllCategoryAttributeMappings() {
		List<CategoryAttributeMapping> categoryAttributeMappings = new ArrayList<CategoryAttributeMapping>();
		for (CategoryAttributeMappingData data : scmMetadataDao.findAll(CategoryAttributeMappingData.class)) {
			categoryAttributeMappings.add(SCMDataConverter.convert(data));
		}
		return categoryAttributeMappings;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<AttributeValue> getAllAttributeValues() {
		List<AttributeValue> attributeValues = new ArrayList<AttributeValue>();
		for (AttributeValueData avd : scmMetadataDao.findAll(AttributeValueData.class)) {
			attributeValues.add(SCMDataConverter.convert(avd));
		}
		return attributeValues;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CategoryAttributeValue> getAllCategoryAttributeValues() {
		List<CategoryAttributeValue> categoryAttributeValues = new ArrayList<CategoryAttributeValue>();
		for (CategoryAttributeValueData cavd : scmMetadataDao.findAll(CategoryAttributeValueData.class)) {
			categoryAttributeValues.add(SCMDataConverter.convert(cavd));
		}
		return categoryAttributeValues;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductPackagingMapping> getAllProductPackginMappings() {
		List<ProductPackagingMapping> productPackagingMappings = new ArrayList<ProductPackagingMapping>();
		for (ProductPackagingMappingData ppmd : scmMetadataDao.findAll(ProductPackagingMappingData.class)) {
			productPackagingMappings.add(SCMDataConverter.convert(ppmd));
		}
		return productPackagingMappings;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SkuPackagingMapping> getAllSkuPackagingMappings() {
		List<SkuPackagingMapping> skuPackagingMappings = new ArrayList<SkuPackagingMapping>();
		for (SkuPackagingMappingData spmd : scmMetadataDao.findAll(SkuPackagingMappingData.class)) {
			skuPackagingMappings.add(SCMDataConverter.convert(spmd));
		}
		return skuPackagingMappings;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SkuAttributeValue> getAllSkuAttributeValues() {
		List<SkuAttributeValue> skuAttributeValues = new ArrayList<SkuAttributeValue>();
		for (SkuAttributeValueData savd : scmMetadataDao.findAll(SkuAttributeValueData.class)) {
			skuAttributeValues.add(SCMDataConverter.convert(savd));
		}
		return skuAttributeValues;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<VendorDetail> getAllVendorDetails() {
		//List<VendorDetail> vendorDetails = convert(scmMetadataDao.findAll(VendorDetailData.class));
		List<VendorDetail> vendorDetails = convert(scmMetadataDao.findAllVendorDetailData());
		return vendorDetails;
	}

	@Override
	public List<VendorDetail> convertToVendorDetail(List<VendorDetailData> vendorDetailDataList) {
		return convert(vendorDetailDataList);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PaymentDeviation> getAllPaymentDeviations() {
		return SCMDataConverter.convert(scmMetadataDao.findAll(PaymentDeviationData.class));
	}


	@Override
	public VendorDetail addVendorToCache(VendorDetailData vendorDetailData){
		List<VendorDetailData> vendorDetailDataList = new ArrayList<>(Arrays.asList(vendorDetailData));
		List<VendorDetail> vendorDetails = convert(vendorDetailDataList);
		for(VendorDetail vendorDetail : vendorDetails){
			scmCache.getVendorDetails().put(vendorDetail.getVendorId(),vendorDetail);
			return vendorDetail;
		}
		return  null;
	}

	private List<VendorDetail> convert(List<VendorDetailData> list) {
		if (list == null) {
			return new ArrayList<>();
		}
		return list.stream().map(o -> {
			String updatedBy = masterDataCache.getEmployee(o.getUpdatedBy());
			String requestedBy = masterDataCache.getEmployee(o.getRequestedBy());
			VendorDetail detail = SCMDataConverter.convertVendor(o, updatedBy, requestedBy);
			if (Objects.nonNull(o.getUnblockedTillDate())) {
				detail.setUnblockedTillDate(o.getUnblockedTillDate());
			}
			if (Objects.nonNull(o.getLastBlockedBy())) {
				detail.setLastBlockedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(o.getLastBlockedBy()), o.getLastBlockedBy()));
			}
			if (Objects.nonNull(o.getLastBlockedDate())) {
				detail.setLastBlockedDate(o.getLastBlockedDate());
			}
			if (Objects.nonNull(o.getLastUnBlockedDate())) {
				detail.setLastUnBlockedDate(o.getLastUnBlockedDate());
			}
			if (Objects.nonNull(o.getLastUnBlockedBy())) {
				detail.setLastUnBlockedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(o.getLastUnBlockedBy()), o.getLastUnBlockedBy()));
			}
			if (Objects.nonNull(o.getByPassContract())) {
				detail.setByPassContract(o.getByPassContract());
			}
			if (Objects.nonNull(o.getIsEnterpriseVendor())) {
				detail.setIsEnterpriseVendor(o.getIsEnterpriseVendor());
			}
			detail.setIsCCVendor(o.getIsCCVendor());
			detail.setIsEcomParty(o.getIsEcomParty());
			return detail;
		}).collect(Collectors.toList());
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.core.service.SCMMetadataService#getAllLocations()
	 */
	@Override
	public Collection<Location> getAllLocations() {
		return masterDataCache.getAllLocations().values();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.core.service.SCMMetadataService#getAllLocations()
	 */
	@Override
	public Collection<State> getAllStates() {
		return masterDataCache.getAllStates().values();
	}

	@Override
	public List<IdCodeName> getAllInventoryLists() {
		List<InventoryListTypeData> types = scmMetadataDao.findAll(InventoryListTypeData.class);
		return types.stream().map(inventoryListTypeData -> {
			IdCodeName list = new IdCodeName();
			list.setCode(inventoryListTypeData.getListCode());
			list.setId(inventoryListTypeData.getId());
			list.setName(inventoryListTypeData.getListName());
			return list;
		}).collect(Collectors.toList());
	}

	@Override
	public Map<String, Integer> getFulfillmentTypeMap() {
		Map<String, Integer> m = new HashMap<>();
		List<FulfillmentUnitMappingData> l = scmMetadataDao.findAll(FulfillmentUnitMappingData.class);
		for (FulfillmentUnitMappingData f : l) {
			String key = SCMUtil.getkey(f.getRequestingUnitId(), f.getFulfillmentType());
			m.put(key, f.getFulfillingUnitId());
		}
		return m;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, Map<Integer, ProductRecipeKey>> getProductProfileMapping() {
		return scmMetadataDao.getProductProfileMapping();
	}

	@Override
	public Map<Integer, Set<Integer>> getUnitToAvailableProductMap() {
		List<Integer> units = scmMetadataDao.getUnits();
		List<Pair<Integer, Integer>> l = scmMetadataDao.getUnitToProductList(units);
		Map<Integer, Set<Integer>> map = new HashMap<>();
		for (Pair<Integer, Integer> p : l) {
			Set<Integer> s = map.get(p.getKey());
			if (s == null) {
				s = new HashSet<Integer>();
				map.put(p.getKey(), s);
			}
			s.add(p.getValue());
		}
		return map;
	}

	@Override
	public Map<Integer, Set<Integer>> getUnitToAvailableSKUMap() {
		List<Integer> units = scmMetadataDao.getUnits();
		List<Pair<Integer, Integer>> l = scmMetadataDao.getUnitToSKUList(units);
		Map<Integer, Set<Integer>> map = new HashMap<>();
		for (Pair<Integer, Integer> p : l) {
			Set<Integer> s = map.get(p.getKey());
			if (s == null) {
				s = new HashSet<Integer>();
				map.put(p.getKey(), s);
			}
			s.add(p.getValue());
		}
		return map;
	}

	@Override
	public Map<String, BigDecimal> getUnitDistanceMapping() {
		Map<String, BigDecimal> map = new HashMap<>();
		List<UnitDistanceMappingData> list = scmMetadataDao.getUnitDistanceMapping();
		for (UnitDistanceMappingData data : list) {
			map.put(createMappingKey(data.getSourceUnitId(), data.getDestinationUnitId()), data.getDistance());
		}
		return map;
	}

	@Override
	public Map<String, BigDecimal> getZipCodeDistanceMapping() {
		Map<String, BigDecimal> map = new HashMap<>();
		List<ZipCodeDistanceMapping> list = scmMetadataDao.getZipCodeDistanceMapping();
		for (ZipCodeDistanceMapping data : list) {
			map.put(data.getSourceZipCode()+""+data.getDestinationZipCode(), data.getDistance());
		}
		return map;
	}
	private String createMappingKey(int u1, int u2) {
		return u1 + "" + u2;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, List<ListDetail>> getListDetails() {
		Map<String, List<ListDetail>> detailMap = new HashMap<String, List<ListDetail>>();
		List<ListDetailData> listDetailData = scmMetadataDao.findAll(ListDetailData.class);
		for(ListDetailData list : listDetailData) {
			List<ListDetail> listMap = new ArrayList<ListDetail>();
			if(detailMap.isEmpty()) {
				listMap.add(SCMDataConverter.convert(list));
				detailMap.put(list.getType(), listMap);
			}
			else {
				if(detailMap.containsKey(list.getType())){
					listMap = (List<ListDetail>) detailMap.get(list.getType());
					listMap.add(SCMDataConverter.convert(list));
					detailMap.put(list.getType(), listMap);
				}
				else {
					listMap.add(SCMDataConverter.convert(list));
					detailMap.put(list.getType(), listMap);
				}
			}

		}
		return detailMap;
	}

		@Override
		@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
		public List<CapexTemplateData> getAllCapexTemplates() {
			List<CapexTemplateData> capexTemplateData = new ArrayList<CapexTemplateData>();
			for (CapexTemplateData avd : scmMetadataDao.findAll(CapexTemplateData.class)) {
				capexTemplateData.add(avd);
			}
			return capexTemplateData;
		}

	    @Override
		public Boolean saveAuditLog(Integer keyId , String keyType ,Integer changedBy , Object newObject , String changeType) throws SumoException {

		try {
			AuditChangeLog auditChangeLog = new AuditChangeLog();
			auditChangeLog = auditChangeLogDao.findByKeyId(keyId, keyType);
			if (Objects.nonNull(auditChangeLog)) {
				LOG.info("Found Already Mongo Document ");
				auditChangeLog.setNewObject(newObject);
				auditChangeLog.setUpdatedOn(SCMUtil.getCurrentTimeIST());
				auditChangeLog.setChangeType(changeType);
				auditChangeLog.setPreviousVersionId(auditChangeLog.getVersion());
			} else {
				LOG.info("Creating New Mongo Document");
				auditChangeLog = new AuditChangeLog();
				auditChangeLog.setKeyId(keyId);
				auditChangeLog.setKeyType(keyType);
				auditChangeLog.setChangeType(changeType);
				if (Objects.nonNull(changedBy)) {
					auditChangeLog.setChangedBy(changedBy);
				}
				auditChangeLog.setCreatedOn(SCMUtil.getCurrentTimeIST());
				auditChangeLog.setUpdatedOn(SCMUtil.getCurrentTimeIST());
				auditChangeLog.setNewObject(newObject);
			}
			auditChangeLogDao.save(auditChangeLog);
			AuditChangeLogHistory auditHistory = new AuditChangeLogHistory();
			auditHistory.setAuditChangeLog(auditChangeLog);
			auditChangeLogHistoryDao.save(auditHistory);
			return  true;
		}catch (Exception e){
			throw new SumoException("Error While Saving Audit Log In Mongo",e.getMessage());
		}


		}

	@Override
	public Boolean sendDiffEmail(Object oldObject , Object newObject , String updatedBy , String className , Integer id ,
								 List<String> toEmails, String placeHolder , Boolean isNew ) throws IllegalAccessException, EmailGenerationException {
		try {
			Map<String,Pair<Object,Object>> diffs = findDiff(oldObject,newObject);
			ChangesDiffNotificationTemplate template = new ChangesDiffNotificationTemplate(diffs,updatedBy,className,id,
					props.getBasePath() ,isNew);
			ChangesDiffNotification changesDiffNotification = new ChangesDiffNotification(className,props.getEnvType(),
					updatedBy,toEmails,template,placeHolder , isNew);
			changesDiffNotification.sendEmail();
		}catch (Exception e){
			LOG.info("Error While Finding Diff And Sending Diff Email For {} with ID : {}",className,id,e);
			return false;
		}
		return true;

	}

	private  Pair<String,String> findArrayDiff(Object[] oldArray , Object[] newArray) throws IllegalAccessException {
		String arrayDiffOld = "[<br>";
		String arrayDiffNew = "[<br>";
		Integer oldSize = oldArray.length;
		Integer newSize = newArray.length;
		Integer i =0;
		while(i < newSize && i < oldSize){
			Object oldEle = oldArray[i];
			Object newEle = newArray[i];
			if(!traverseDepthFirst(oldEle,"").equals(traverseDepthFirst(newEle,""))){
				Pair<String,String> nestedDiffs = getNestedDiff(oldEle,newEle);
				String oldMsg = "{<br>" +nestedDiffs.getKey() + "}" + ",<br>";
				String newMsg = "{<br>" + nestedDiffs.getValue() + "}" +",<br>";
				arrayDiffOld = arrayDiffOld.concat(oldMsg);
				arrayDiffNew = arrayDiffNew.concat(newMsg);
			}
			i++;
		}
		while(i < oldSize){
			String tempMsg = "";
			tempMsg = traverseDepthFirst(oldArray[i],tempMsg);
			arrayDiffOld = arrayDiffOld.concat("{<br>" +tempMsg + "}" +",<br>");
			i++;
		}
		while (i<newSize){
			String tempMsg = "";
			tempMsg = traverseDepthFirst(newArray[i],tempMsg);
			arrayDiffNew= arrayDiffNew.concat("{<br>" + tempMsg + "}" + ",<br>");
			i++;
		}
		arrayDiffOld =arrayDiffOld.concat("<br>]");
		arrayDiffNew =arrayDiffNew.concat("<br>]");
		return new Pair<>(arrayDiffOld.trim(),arrayDiffNew.trim());
	}



	@Override
	public Map<String,Pair<Object,Object>>  findDiff(Object oldObject , Object newObject) throws IllegalAccessException {
		//Map Key Is Field Name , Pair Key is Old Field Value , Pair Value is New Field Value
		Map<String,Pair<Object,Object>> objectDiffs = new HashMap<>();
		try{
			LOG.info("####Trying To Find Diffs On Object Of Class : {}",oldObject.getClass());
			if(!oldObject.getClass().equals(newObject.getClass())){
				LOG.info("Couldn't Find DIff Old And New Object Is not Of same Type");
				return objectDiffs;
			}
			Field[] fields = oldObject.getClass().getDeclaredFields();
			for(Field field : fields){
				try {
					if (field.getName().equals("serialVersionUID")) {
						continue;
					}
					field.setAccessible(true);
					if (field.getType().isArray() || field.getType().isAssignableFrom(Collection.class) ||
							field.getType().equals(List.class)) {
						LOG.info("######Field Is OF Array Type");
						if(Objects.isNull(field.get(oldObject))){
							if(Objects.isNull(field.get(newObject))){
								continue;
							}
							LOG.info("Old Array Is Null");
							oldObject = new ArrayList<Object>();
						}
						Object[] oldArray = ((Collection<Object>) field.get(oldObject)).toArray();
						Object[] newArray = ((Collection<Object>) field.get(newObject)).toArray();

						if (!Arrays.equals(oldArray, newArray)) {
							LOG.info("Field : {} is changed For Class : {}", field.getName(), oldObject.getClass());
							Pair<String, String> arrayDiffs = findArrayDiff(oldArray, newArray);
							if(arrayDiffs.getKey().equalsIgnoreCase(arrayDiffs.getValue())){
								continue;
							}
							objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(arrayDiffs.getKey().trim(),
									arrayDiffs.getValue().trim()));
						} else {
							LOG.info("#####Both Arrays Are Same");
							continue;
						}
					} else if (Objects.isNull(field.get(oldObject))) {
						if (Objects.nonNull(field.get(newObject))) {
							String msg = "";
							LOG.info("Field : {} is changed For Class : {}", field.getName(), oldObject.getClass());
							if (field.get(newObject).getClass().getPackage().getName().startsWith("com.stpl.tech") &&
									!field.get(newObject).getClass().isEnum()) {
								String oldObjMsg = "";
								String newObjMsg = "";
								oldObjMsg = traverseDepthFirst(field.get(oldObject), oldObjMsg);
								newObjMsg = traverseDepthFirst(field.get(newObject), newObjMsg);
								if(Objects.nonNull(oldObjMsg) && Objects.nonNull(newObjMsg) && oldObjMsg.equalsIgnoreCase(newObjMsg)){
									continue;
								}
								objectDiffs.put(splitCamelCase(field.getName()), new Pair<>("", newObjMsg));
							} else {
								if (field.getType().equals(Date.class)) {
									String oldDate = "";
									String newDate = "";
									if (Objects.nonNull(field.get(newObject))) {
										newDate = AppUtils.dateInddthMMMFormat((Date) field.get(newObject));
									}
									if (!oldDate.equals(newDate)) {
										objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(oldDate, newDate));
									}
								} else {
									objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(Objects.nonNull(field.get(oldObject)) ? field.get(oldObject).toString() : "",
											Objects.nonNull(field.get(newObject)) ? field.get(newObject).toString() : ""));
								}
							}
						} else {
							continue;
						}

					} else if (!field.get(oldObject).equals(field.get(newObject))) {
						LOG.info("Field : {} is changed For Class : {}", field.getName(), oldObject.getClass());
						String msg = "";
						if (field.get(oldObject).getClass().getPackage().getName().startsWith("com.stpl.tech") &&
								!field.get(oldObject).getClass().isEnum()) {
							String oldObjectMsg = "";
							String newObjMsg = "";
							oldObjectMsg = traverseDepthFirst(field.get(oldObject), oldObjectMsg);
							newObjMsg = traverseDepthFirst(field.get(newObject), newObjMsg);
							if (Objects.isNull(field.get(newObject))) {
								objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(oldObjectMsg, ""));
							} else if (!oldObjectMsg.equals(newObjMsg)) {
								Pair<String, String> nestedDiffs = getNestedDiff(field.get(oldObject), field.get(newObject));
								if(Objects.nonNull(nestedDiffs.getKey()) && Objects.nonNull(nestedDiffs.getValue()) &&
										nestedDiffs.getKey().equalsIgnoreCase(nestedDiffs.getValue())){
									continue;
								}
								objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(nestedDiffs.getKey(), nestedDiffs.getValue()));
							}
						} else {
							if (field.getType().equals(Date.class)) {
								String oldDate = AppUtils.dateInddthMMMFormat((Date) field.get(oldObject));
								String newDate = "";
								if (Objects.nonNull(field.get(newObject))) {
									newDate = AppUtils.dateInddthMMMFormat((Date) field.get(newObject));
								}
								if (!oldDate.equals(newDate)) {
									objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(oldDate, newDate));
								}
							} else {
								objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(Objects.nonNull(field.get(oldObject)) ? field.get(oldObject).toString() : "",
										Objects.nonNull(field.get(newObject)) ? field.get(newObject).toString() : ""));
							}
						}

					}
				}catch (Exception e){
					LOG.info("Error While Finding Diff For Field : {}",field.getName() , e);
					objectDiffs.put(splitCamelCase(field.getName()),new Pair<>("Error While Finding Diff",
							"Error While Finding Diff"));
				}
			}
		}catch (Exception e){
			LOG.info("Error While Finding Diffs On Objects" , e);
		}

		return objectDiffs;
	}

	private Pair<String , String> getNestedDiff(Object oldObj , Object newObj) throws IllegalAccessException {
		Map<String,Pair<Object,Object>> nestedDiffs = findDiff(oldObj,newObj);
		String oldObjectDiff = "";
		String newObjectDiff = "";
		for(String field :  nestedDiffs.keySet()){
			oldObjectDiff =  oldObjectDiff.concat( splitCamelCase(field) + " : " + nestedDiffs.get(field).getKey() + "<br>");
			newObjectDiff =  newObjectDiff.concat( splitCamelCase(field) + " : " + nestedDiffs.get(field).getValue() + "<br>");
		}
		return new Pair<>(oldObjectDiff,newObjectDiff);

	}

	private String traverseDepthFirst(Object obj ,String msg) throws IllegalAccessException {
		if (obj == null) {
			return msg;
		}
		for (Field field : obj.getClass().getDeclaredFields()) {
			field.setAccessible(true);
			if(field.getName().equals("serialVersionUID")){
				continue;
			}
			String tempMsg = "";
			if(Objects.nonNull(field.get(obj))  && field.get(obj).getClass().getPackage().getName().startsWith("com.stpl.tech") &&
					!field.get(obj).getClass().isEnum()){
				tempMsg =  splitCamelCase(field.getName()) + ": " + traverseDepthFirst(field.get(obj),"") + "<br>";
			}else{
				tempMsg = splitCamelCase(field.getName()) + ": " + field.get(obj) + "<br>";
			}
			msg = msg.concat(tempMsg);
		}
		return msg;
	}


	private String splitCamelCase(String s) {
		s =  s.replaceAll(
				String.format("%s|%s|%s",
						"(?<=[A-Z])(?=[A-Z][a-z])",
						"(?<=[^A-Z])(?=[A-Z])",
						"(?<=[A-Za-z])(?=[^A-Za-z])"
				),
				" "
		);
		return s.substring(0, 1).toUpperCase() + s.substring(1);
	}

	private String encodeValue(String value) throws UnsupportedEncodingException {
		return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
	}


	@Override
	public MapResponse findDistance(String source , String destination) throws IOException {
		OkHttpClient client = new OkHttpClient().newBuilder()
				.build();
		MediaType mediaType = MediaType.parse("text/plain");
		RequestBody body = RequestBody.create(mediaType, "");
		Map<String, String> requestParams = new HashMap<>();
		requestParams.put("origins", source);
		requestParams.put("destinations", destination);
		requestParams.put("key", props.getGoogleApiKey());

		String encodedURL = requestParams.keySet().stream()
				.map(key -> {
					try {
						return key + "=" + encodeValue(requestParams.get(key));
					} catch (UnsupportedEncodingException e) {
						e.printStackTrace();
					}
					return key;
				})
				.collect(joining("&", "https://maps.googleapis.com/maps/api/distancematrix/json?", ""));
		Request request = new Request.Builder()
				.url(encodedURL)
				.method("POST", body)
				.build();
		Response response = client.newCall(request).execute();
		GsonBuilder builder = new GsonBuilder();
		Gson gson = builder.create();
		MapResponse mapResponse = gson.fromJson(response.body().string(),MapResponse.class);
		return mapResponse;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Map<String ,Integer> getRegionFulfillmentMapping() {
		Map<String ,Integer> regionMapping = new HashMap<>();
		List<RegionFulfillmentMapping> regionFulfillmentMappingList = scmMetadataDao.getAllActiveRegionMappings();
		for(RegionFulfillmentMapping regionFulfillmentMapping : regionFulfillmentMappingList){
			String key = SCMUtil.generateUniqueKey(regionFulfillmentMapping.getRegion(), regionFulfillmentMapping.getType(), String.valueOf(regionFulfillmentMapping.getCompanyId()));
			regionMapping.put(key,regionFulfillmentMapping.getUnitId());
		}
		return regionMapping;
	}

	DeactivateValidateResponse checkForPendingFaGrs(Integer unitId){
		String message  = "Can't change Handover Date : \n\n";
		Boolean isValid = true;

		//Checking For Unit In Active FA Purchase Orders
		List<Integer> poIds = scmMetadataDao.checkForInCompletePurchaseOrder(null,unitId,true);
		LOG.info("{} Active FA Purchase Order is Found For Unit Id : {}",poIds.size(),unitId);
		if(Objects.nonNull(poIds) && !poIds.isEmpty()){
			message+= "This Unit is Present in " + "This Active FA Purchase Orders : \n\n";
			message+= poIds;
			message+="\n\n";
			isValid = false;
		}

		//Checking For Unit In Active Internal FA Grs
		List<Integer> internalGrIds = scmMetadataDao.checkForInCompleteInternalGr(null,unitId,true);
		LOG.info("{} Pending FA Internal is Found For Unit Id : {}",internalGrIds.size(),unitId);
		if(Objects.nonNull(internalGrIds) && !internalGrIds.isEmpty()){
			message+= "This Unit is Present in " + " This Active FA Internal GRs : \n\n";
			message+= internalGrIds;
			message+="\n\n";
			isValid = false;
		}
		return setValidationResponse("FA_GR",unitId,message,isValid);

	}

	private DeactivateValidateResponse setValidationResponse(String type , Integer id , String msg , Boolean canBeDeactivated){
		DeactivateValidateResponse response = new DeactivateValidateResponse();
		response.setType(type);
		response.setId(id);
		response.setMessage(msg);
		response.setCanBeDeActivated(canBeDeactivated);
		return response;
	}


	private DeactivateValidateResponse validateUnitForDeactivation(Integer unitId){
		String message  = "";
		Boolean isValid = true;

		List<Integer> assetIds = scmMetadataDao.getAllActiveAssetsForUnit(unitId);
		if(Objects.nonNull(assetIds) && !assetIds.isEmpty()){
			LOG.info("{} Asset Inventory Found For Unit {} " ,assetIds.size() , unitId);
			message+= "This Unit Have " + assetIds.size() + " Assets In Asset Inventory";
			message+= "\n\n";
			isValid = false;
		}

		//Checking For Unit In Purchase Orders
		List<Integer> poIds = scmMetadataDao.checkForInCompletePurchaseOrder(null,unitId,false);
		LOG.info("{} Active Purchase Order is Found For Unit Id : {}",poIds.size(),unitId);
		if(Objects.nonNull(poIds) && !poIds.isEmpty()){
			message+= "This Unit is Present in " + poIds.size() + " Active Purchase Orders";
			message+="\n\n";
			isValid = false;
		}

		//Checking  For Unit In Transfer Orders
		List<Integer> toIds = scmMetadataDao.checkForInCompleteTO(null,unitId);
		LOG.info("{} Active Transfer Order is Found For Unit Id : {}",toIds.size(),unitId);
		if(Objects.nonNull(toIds) && !toIds.isEmpty()){
			message+= "This Unit Is Present in This " + toIds.size() +  "  Active Transfer Orders";
			message+= "\n\n";
			isValid = false;
		}

		//checking for Unit In Internal GR
		List<Integer> internalGrIds = scmMetadataDao.checkForInCompleteInternalGr(null,unitId , false);
		LOG.info("{} Active Internal GR is Found For Unit Id : {}",internalGrIds.size(),unitId);
		if(Objects.nonNull(internalGrIds) && !internalGrIds.isEmpty()){
			message+= "This Unit Is Present in This " + internalGrIds.size() +  "  Active Internal GR";
			message+= "\n\n";
			isValid = false;
		}


		//Checking For Unit in Grs For Pending Payment
		List<Integer> grIds = scmMetadataDao.checkInCompleteGrForPayment(null,unitId);
		LOG.info("{} Grs with pending payment  is Found For Unit Id : {}",grIds.size(),unitId);
		if(Objects.nonNull(grIds) && !grIds.isEmpty()){
			message+= "This Unit Is Present in " + grIds.size() +  " Grs For Pending Payment ";
			message+= "\n\n";
			isValid = false;
		}

		//checking For Unit in Incomplete Payment Request (Exception sent for payment State)
		List<Integer> prIds = scmMetadataDao.checkInCompletePayment(null,unitId);
		LOG.info("{}  pending payment Requests are Found For Sku Id : {}",prIds.size(),unitId);
		if(Objects.nonNull(prIds) && !prIds.isEmpty()){
			message+= "This Unit  Is Present in " + prIds.size()  +  " Prs For Pending Payment";
			message+= "\n\n";
			isValid = false;
		}

		UnitDetail unitDetail = scmCache.getUnitDetail(unitId);
		Set<Integer> activeKeyIds = scmMetadataDao.checkActiveStocks(unitDetail);
		LOG.info("Active Stock found of :{} products for:{}",activeKeyIds.size(),unitId);
		if(Objects.nonNull(activeKeyIds) && !activeKeyIds.isEmpty()){
			message+= "This Unit has active stock for " +activeKeyIds.size()+ " Products" ;
			message+= "\n\n";
			isValid = false;
		}

		//GatePass
			Set<Integer> gatePassIds = scmMetadataDao.checkActiveGatePass(unitId);
		    LOG.info("{} active gatePasses found for :{}",gatePassIds.size(),unitId);
			if(Objects.nonNull(gatePassIds) && !gatePassIds.isEmpty()){
				message += "This Unit has active gatepass for " +gatePassIds.size();
				message+="\n\n";
				isValid=false;
			}




		return setValidationResponse("UNIT",unitId,message,isValid);
	}


	private DeactivateValidateResponse validateProductForDeactivation(Integer productId) throws URISyntaxException {
		String msg = "";
		Boolean isValid = true;
        Map<String,String> uriVariables = new HashMap<>();
		uriVariables.put("productId",productId.toString());
		String endPoint =  props.getMasterServiceBasePath() + "/master-service/rest/v1/recipe/find-recipe-containing-productId";

		List masterResponse =   WebServiceHelper.exchangeWithAuth(endPoint, props.getAuthToken(), HttpMethod.GET, List.class, null, uriVariables);
		ObjectMapper mapper = new ObjectMapper();
		List<RecipeDetail> finalList = mapper.convertValue(masterResponse, new TypeReference<List<RecipeDetail>>() {});

		List<String> recipeNames = finalList.stream().map(recipeDetail -> recipeDetail.getName()).
				collect(Collectors.toList());
		if(Objects.nonNull(recipeNames) && !recipeNames.isEmpty()){
			msg+= "<b>This Product is Found In Following Recipes : <b> <br>";
			msg+= recipeNames;
			msg+= "<br><br>";
			isValid  = false;
		}

		List<Integer> skuIds = scmMetadataDao.getAllActiveLinkedSKUs(productId);
		if(Objects.nonNull(skuIds) && !skuIds.isEmpty()){
			msg+= "<b>This Product has Following Active SKU's :  <b> <br> ";
			for(Integer skuId : skuIds){
				msg += scmCache.getSkuDefinition(skuId).getSkuName() + ", ";
			}
			msg+= "<br>";
			isValid = false;
		}

		return setValidationResponse("PRODUCT",productId,msg,isValid);
	}


	private DeactivateValidateResponse validateSkuForDeactivation(Integer skuId){
		String message = "";
		Boolean isValid = true;

		//Checking for Active Sku Stock In Units
		List<Integer> units = scmMetadataDao.getUnitsForNonZeroSkuInventory(skuId);
		LOG.info("Active Stock For Sku Id : {} is Found In {} Units",skuId,units.size());
		if(Objects.nonNull(units) && !units.isEmpty()){
			message+= "<b>This Sku have Active Stock In These Units : </b>" ;
			for(Integer unitId : units){
				message+= masterDataCache.getUnit(unitId).getName() + ", ";
			}
			message+= "<br>";
			isValid = false;
		}

		//Checking For Sku In Purchase Orders
		List<Integer> poIds = scmMetadataDao.checkForInCompletePurchaseOrder(skuId,null,false);
		LOG.info("{} Active Purchase Order is Found For Sku Id : {}",poIds.size(),skuId);
		if(Objects.nonNull(poIds) && !poIds.isEmpty()){
			message+= "<b>This Sku is Present in This Active Purchase Orders : </b>";
			message+= poIds;
			message+="<br>";
			isValid = false;
		}

		//Checking  For Sku In Transfer Orders
		List<Integer> toIds = scmMetadataDao.checkForInCompleteTO(skuId,null);
		LOG.info("{} Active Transfer Order is Found For Sku Id : {}",toIds.size(),skuId);
		if(Objects.nonNull(toIds) && !toIds.isEmpty()){
			message+= "<b>This Sku Is Present in This Active Transfer Orders : </b>";
			message+= toIds;
			message+= "<br>";
			isValid = false;
		}

		//checking for Sku In Internal GR
		List<Integer> internalGrIds = scmMetadataDao.checkForInCompleteInternalGr(skuId,null, false);
		LOG.info("{} Active Internal GR is Found For Sku Id : {}",internalGrIds.size(),skuId);
		if(Objects.nonNull(internalGrIds) && !internalGrIds.isEmpty()){
			message+= "<b>This Sku Is Present in This Active Internal GR : </b><br>";
			message+= internalGrIds;
			message+= "<br><br>";
			isValid = false;
		}

		//Checking For Sku in Grs For Pending Payment
		List<Integer> grIds = scmMetadataDao.checkInCompleteGrForPayment(skuId,null);
		LOG.info("{} Grs with pending payment  s Found For Sku Id : {}",grIds.size(),skuId);
		if(Objects.nonNull(grIds) && !grIds.isEmpty()){
			message+= "<b>This Sku Is Present in This Grs For Pending Payment : </b>";
			message+= grIds;
			message+= "<br>";
			isValid = false;
		}

		//checking For Sku in Incomplete Payment Request (Exception sent for payment State)
		List<Integer> prIds = scmMetadataDao.checkInCompletePayment(skuId,null);
		LOG.info("{}  pending payment Requests are Found For Sku Id : {}",prIds.size(),skuId);
		if(Objects.nonNull(prIds) && !prIds.isEmpty()){
			message+= "<b>This Sku Is Present in This Prs For Pending Payment : </b>";
			message+= prIds;
			message+= "<br>";
			isValid = false;
		}
		//Checking For Sku In Production booking mapping
		List<Integer> productIds = scmMetadataDao.checkForProductionBookingMapping(skuId);
		LOG.info("{} Active Production Booking Mapping is Found For Sku Id : {}",productIds.size(),skuId);
		if(Objects.nonNull(productIds) && !productIds.isEmpty()){
			message+= "<b>This Sku Is Present in  Production Booking Mapping For This Products  : </b>";
			for(Integer productId : productIds){
				message+= scmCache.getProductDefinition(productId).getProductName() + ", ";
			}
			message+= "<br>";
			isValid = false;
		}

		return setValidationResponse("SKU",skuId,message,isValid);
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public DeactivateValidateResponse validateForDeactivation(Integer id , String type) throws URISyntaxException {
		switch (type){
			case "SKU":
				return validateSkuForDeactivation(id);
			case "PRODUCT":
				return validateProductForDeactivation(id);
			case "UNIT":
				return validateUnitForDeactivation(id);
			case "FA_GR":
				return checkForPendingFaGrs(id);
			default:
				return null;

		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Map<String,List<Integer>> getMaintenanceWHMappings(){
		Map<String,List<Integer>> maintenanceZoneMap = new HashMap<>();

		scmMetadataDao.getAllMaintenanceWHMappings().ifPresent(mappings ->{
			mappings.forEach(mapping ->{
				maintenanceZoneMap.computeIfAbsent(mapping.getZone(),key -> new ArrayList<>()).
						add(mapping.getUnitId());
			});
		});
		return maintenanceZoneMap;

	}




	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void dumpAuditLogsToMysql(){
		   AuditLogData lastEntry = scmMetadataDao.getLastAuditEntry();
		   List<AuditChangeLogHistory> historyLogs = null;
		   if(Objects.nonNull(lastEntry)){
			   historyLogs = auditChangeLogHistoryDao.findByAuditChangeLog_UpdatedOnGreaterThan(new DateTime(lastEntry.getUpdatedOn().getTime()).plusSeconds(1));
		   }else{
			   historyLogs =  auditChangeLogHistoryDao.findAll();
		   }

		   for(AuditChangeLogHistory historyLog : historyLogs){
			   if(historyLog.getAuditChangeLog().getKeyType().equals(AuditChangeLogTypes.PRODUCT.value())){
				   Integer objectId = null;
				   try {
					   ProductDefinitionLogs temp = saveProductDefinationLogs((ProductDefinition) historyLog.getAuditChangeLog().getNewObject());
					   if(Objects.nonNull(temp)){
						   objectId = temp.getLogId();
					   }else{
						   continue;
					   }
				   } catch (SumoException e) {
					   LOG.error("Error While Saving Product Logs :::::",e);
				   }
				   try {
					   saveAuditLogsinMySql(historyLog.getAuditChangeLog(),objectId);
				   } catch (SumoException e) {
					   LOG.error("Error While Saving Audit  Logs in Mysql :::::",e);
				   }
			   }
		   }



	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Map<Integer, PendingMilkBread> getPendingMilkBread() {
		Stopwatch stopwatch = Stopwatch.createUnstarted();
		stopwatch.start();
		Map<Integer, PendingMilkBread> pendingMilkBreadMap = new HashMap<>();
		masterDataCache.getAllUnits().forEach(unitBasicDetail -> {
			PendingMilkBread pendingMilkBread = new PendingMilkBread(Boolean.TRUE);
			pendingMilkBreadMap.put(unitBasicDetail.getId(), pendingMilkBread);
		});
		try {
			List<Integer> milkBreadProductIds = Collections.singletonList(100234);
			List<Object[]> pendingRosListObjects = scmMetadataDao.getPendingMilkBreadRos(SCMUtil.getBusinessDate(), milkBreadProductIds, null, null);
			if (Objects.nonNull(pendingRosListObjects) && !pendingRosListObjects.isEmpty()) {
				List<PendingMilkBreadItem> pendingMilkBreadItems = new ArrayList<>();
				pendingRosListObjects.forEach(pendingRo -> {
					pendingMilkBreadItems.add(PendingMilkBreadItem.builder().requestUnitId((Integer) pendingRo[0]).
							roIds(getRoIds((String) pendingRo[1])).
							vendorIds(getVendorIds((String) pendingRo[2])).build());
				});
				pendingMilkBreadItems.forEach(pendingMilkBreadItem -> {
					PendingMilkBread pendingMilkBread;
					if (pendingMilkBreadMap.containsKey(pendingMilkBreadItem.getRequestUnitId())) {
						pendingMilkBread = pendingMilkBreadMap.get(pendingMilkBreadItem.getRequestUnitId());
					} else {
						pendingMilkBread = new PendingMilkBread(Boolean.FALSE);
					}
					pendingMilkBread.getVendorIds().addAll(pendingMilkBreadItem.getVendorIds());
					pendingMilkBread.getRoIds().addAll(pendingMilkBreadItem.getRoIds());
					pendingMilkBreadMap.put(pendingMilkBreadItem.getRequestUnitId(), pendingMilkBread);
				});
				Set<Integer> specializedVendorIds = new HashSet<>();
				Set<Integer> unitIds = new HashSet<>();
				pendingMilkBreadMap.forEach((unitId, pendingMilkBread) -> {
					specializedVendorIds.addAll(pendingMilkBread.getVendorIds().stream().map(Integer::parseInt).collect(Collectors.toSet()));
					if (!pendingMilkBread.getRoIds().isEmpty()) {
						unitIds.add(unitId);
					}
				});
				List<UnitVendorMappingData> unitVendorMappingData = getAllUnitVendorsWithVendorIds(specializedVendorIds, unitIds);
				Map<Integer, List<UnitVendorMappingData>> unitVendorMap = new HashMap<>();
				unitVendorMappingData.forEach(mapping -> {
					if (specializedVendorIds.contains(mapping.getVendorId()) && Objects.nonNull(pendingMilkBreadMap.get(mapping.getUnitId()))
							&& pendingMilkBreadMap.get(mapping.getUnitId()).getVendorIds().contains(Integer.toString(mapping.getVendorId()))) {
						List<UnitVendorMappingData> vendorMappingData;
						if (unitVendorMap.containsKey(mapping.getUnitId())) {
							vendorMappingData = unitVendorMap.get(mapping.getUnitId());
						} else {
							vendorMappingData = new ArrayList<>();
						}
						vendorMappingData.add(mapping);
						unitVendorMap.put(mapping.getUnitId(), vendorMappingData);
					}
				});
				pendingMilkBreadMap.forEach((unitId, pendingMilkBread) -> {
					if (Objects.nonNull(pendingMilkBread.getRoIds()) && !pendingMilkBread.getRoIds().isEmpty()) {
						List<UnitVendorMappingData> vendorMappingData = unitVendorMap.get(unitId);
						if (Objects.nonNull(vendorMappingData) && !vendorMappingData.isEmpty()) {
							if (checkDeliveryTime(vendorMappingData)) {
								Date maxAllowedTime = getMaxAllowedTime(vendorMappingData);
								Date finalTime = AppUtils.addHoursToDate(maxAllowedTime, props.getSpecialOrderBufferTime());
								pendingMilkBread.setMaxLimitTime(finalTime);
								pendingMilkBread.setReceivingDone(Boolean.FALSE);
							} else {
								pendingMilkBread.setReceivingDone(Boolean.TRUE);
							}
						} else {
							pendingMilkBread.setReceivingDone(Boolean.TRUE);
						}
					} else {
						pendingMilkBread.setReceivingDone(Boolean.TRUE);
					}
					pendingMilkBreadMap.put(unitId, pendingMilkBread);
				});
			}
		} catch (Exception e) {
			LOG.error("Exception Occurred While Making the Milk Bread cache ::: ",e);
		}
		LOG.info("Cache of Pending Milk Bread Completed in :: {} ms",stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
		return pendingMilkBreadMap;
	}

	private Boolean checkDeliveryTime(List<UnitVendorMappingData> vendorMappingData) {
		for (UnitVendorMappingData unitVendorMappingData : vendorMappingData) {
			if (Objects.isNull(unitVendorMappingData.getDeliveryPromiseTime())) {
				return Boolean.FALSE;
			}
		}
		return Boolean.TRUE;
	}

	private List<UnitVendorMappingData> getAllUnitVendorsWithVendorIds(Set<Integer> specializedVendorIds, Set<Integer> unitIds) {
		List<UnitVendorMappingData> result = new ArrayList<>();
		try {
			result = scmMetadataDao.getAllUnitVendorsWithVendorIdsAndUnitIds(specializedVendorIds,unitIds);
		}
		catch (Exception e) {
			LOG.error("Exception Occurred while getting all unit vendors with vendor ids and Unit Ids ::: ",e);
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markMilkBreadComplete(List<Integer> unitIds, Integer userId) {
		unitIds.forEach(unitId -> {
			PendingMilkBread pendingMilkBread = scmCache.getPendingMilkBread(unitId);
			if (Objects.nonNull(pendingMilkBread)) {
				if (!pendingMilkBread.getReceivingDone() && !pendingMilkBread.getRoIds().isEmpty()) {
					try {
						MilkBreadBypassData milkBreadBypassData = new MilkBreadBypassData();
						milkBreadBypassData.setUnitId(unitId);
						milkBreadBypassData.setBypassReason("DEVELOPER_DASH_BOARD");
						milkBreadBypassData.setComment("DEVELOPER_DASH_BOARD");
						milkBreadBypassData.setRoIDs(pendingMilkBread.getRoIds().stream().map(Object::toString).collect(Collectors.joining(", ")));
						milkBreadBypassData.setMaxAllowedTime(pendingMilkBread.getMaxLimitTime());
						milkBreadBypassData.setBypassedBy(masterDataCache.getEmployee(userId) + " [" + userId + "]");
						milkBreadBypassData.setBypassedTime(AppUtils.getCurrentTimestamp());
						scmMetadataDao.add(milkBreadBypassData, true);
					} catch (Exception e) {
						LOG.error("Exception Occurred while updating the milk bread cache for unit from Dev DashBoard : {} ::",unitId,e);
					}
				}
				scmCache.setPendingMilkBread(unitId, new PendingMilkBread(Boolean.TRUE));
			}
		});
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markMilkBreadCompleteForUnit(MilkBreadBypass milkBreadBypass) {
		PendingMilkBread pendingMilkBread = scmCache.getPendingMilkBread(milkBreadBypass.getUnitId());
		try {
			MilkBreadBypassData milkBreadBypassData = new MilkBreadBypassData();
			milkBreadBypassData.setUnitId(milkBreadBypass.getUnitId());
			milkBreadBypassData.setBypassReason(milkBreadBypass.getBypassReason());
			milkBreadBypassData.setComment(milkBreadBypass.getComment());
			milkBreadBypassData.setRoIDs(milkBreadBypass.getRoIds());
			milkBreadBypassData.setMaxAllowedTime(pendingMilkBread.getMaxLimitTime());
			milkBreadBypassData.setBypassedBy(masterDataCache.getEmployee(Integer.parseInt(milkBreadBypass.getBypassedBy())) + " [" + milkBreadBypass.getBypassedBy() + "]");
			milkBreadBypassData.setBypassedTime(AppUtils.getCurrentTimestamp());
			scmMetadataDao.add(milkBreadBypassData, true);
		} catch (Exception e) {
			LOG.error("Exception Occurred while updating the milk bread cache for unit : {} ::",milkBreadBypass.getUnitId(),e);
		}
		scmCache.setPendingMilkBread(milkBreadBypass.getUnitId(), new PendingMilkBread(Boolean.TRUE));
	}

	private List<Integer> getRoIds(String roIds) {
		if (Objects.nonNull(roIds)) {
			List<String> stringRoIds = Arrays.asList(roIds.split(","));
			return stringRoIds.stream().map(Integer::parseInt).collect(Collectors.toList());
		}
		return new ArrayList<>();
	}

	private Date getMaxAllowedTime(List<UnitVendorMappingData> vendorMappingData) {
		List<Date> allowedTimes = new ArrayList<>();
		vendorMappingData.forEach(unitVendorMappingData -> {
			String[] time = unitVendorMappingData.getDeliveryPromiseTime().split(":");
			Date deliveryPromiseTime = AppUtils.getUpdatedTimeInDate(Integer.parseInt(time[0]),0,0,AppUtils.getCurrentTimestamp());
			allowedTimes.add(deliveryPromiseTime);
		});
		Collections.sort(allowedTimes);
		return allowedTimes.get(allowedTimes.size() - 1);
	}

	private List<String> getVendorIds(String pendingRo) {
		if (Objects.nonNull(pendingRo)) {
			return Arrays.asList(pendingRo.split(","));
		}
		return new ArrayList<>();
	}

	private ProductDefinitionLogs saveProductDefinationLogs(ProductDefinition productDefinition) throws SumoException {
		ProductDefinitionLogs productLog = SCMDataConverter.convertToLogData(productDefinition,scmCache.getCategoryDefinitions().get(productDefinition.getCategoryDefinition().getId())
				,null );
		try{
			productLog =   scmMetadataDao.add(productLog,true);
		}catch (Exception e){
			LOG.error("error while saving ::::",e);
		}
		return productLog;
	}

	private Boolean saveAuditLogsinMySql(AuditChangeLog auditChangeLog , Integer objectId) throws SumoException {
		AuditLogData auditLogSqlData = new AuditLogData();
		auditLogSqlData.setKeyId(auditChangeLog.getKeyId());
		auditLogSqlData.setKeyType(auditChangeLog.getKeyType());
		auditLogSqlData.setChangedBy(auditChangeLog.getChangedBy());
		auditLogSqlData.setChangeType(auditChangeLog.getChangeType());
		auditLogSqlData.setVersion(Math.toIntExact(auditChangeLog.getVersion()));
		auditLogSqlData.setCreatedOn(auditChangeLog.getCreatedOn().toDate());
		auditLogSqlData.setUpdatedOn(auditChangeLog.getUpdatedOn().toDate());
		auditLogSqlData.setObjectId(objectId);

		scmMetadataDao.add(auditLogSqlData,false);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void saveAssetDefinitionToRedis(Integer assetId) {
		List<AssetDefinitionData> assetList = new ArrayList<>();
		if(Objects.isNull(assetId)) {
			assetList.addAll(scmMetadataDao.findAll(AssetDefinitionData.class));
		} else {
			assetList.add(scmMetadataDao.find(AssetDefinitionData.class, assetId));
		}
		for (AssetDefinitionData adData : assetList) {
			try{
				IdCodeName createdBy = SCMUtil.generateIdCodeName(adData.getCreatedBy(), "",
						masterDataCache.getEmployees().get(adData.getCreatedBy()));
				IdCodeName lastTagPrintedBy = adData.getLastTagPrintedBy() != null
						? SCMUtil.generateIdCodeName(adData.getLastTagPrintedBy(), "",
						masterDataCache.getEmployees().get(adData.getLastTagPrintedBy()))
						: null;
				IdCodeName lastTransferBy = adData.getLastTransferedBy() != null
						? SCMUtil.generateIdCodeName(adData.getLastTransferedBy(), "",
						masterDataCache.getEmployees().get(adData.getLastTransferedBy()))
						: null;
				BigDecimal currentValue = AssetHelper.getCurrentValueOfAsset(adData);

				ProductDefinitionData productDefinitionData = scmAssetManagementDao.find(ProductDefinitionData.class, adData.getProduct().getProductId());
				SubCategoryDefinitionData subCategoryDefinitionData = scmProductManagementDao.find(SubCategoryDefinitionData.class, productDefinitionData.getSubCategoryDefinition().getId());
				AssetDefinition assetDetails = SCMDataConverter.convert(adData,createdBy,lastTagPrintedBy,lastTransferBy,currentValue,null);
				assetDetails.setSubCategoryDefinition(new IdCodeName(subCategoryDefinitionData.getId(), "", subCategoryDefinitionData.getName()));
				if (subCategoryDefinitionData.getLifeTimeCategoryMonths() == null) {
					assetDetails.setLifeTimeCategoryMonths(0);
				} else {
					assetDetails.setLifeTimeCategoryMonths(subCategoryDefinitionData.getLifeTimeCategoryMonths());
				}
				assetDefinitionDao.save(assetDetails);
			}catch (Exception e){
				scmNotificationService.sendAssetCacheFailureNotification(adData,e.getMessage());
				LOG.error("ERROR IN ASSET CACHE :::{} ::: {} ",adData.getAssetId(),e.getMessage());
			}
		}
	}


}
