package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "DEBIT_NOTE_DETAIL")
public class DebitNoteDetailData {

    private Integer debitNoteDetailId;
    private String debitNoteStatus;
    private String invoiceNumber;
    private String busyReferenceNumber;
    private BigDecimal amount;
    private BigDecimal totalTaxes;
    private BigDecimal totalAmount;
    private String creditNoteReceived;
    private Date creditNoteReceivingTime;
    private Date generationTime;
    private Integer generatedBy;
    private Integer lastUpdatedBy;
    private Date updateTime;
    private PaymentRequestData paymentRequest;
    private Integer debitNoteDocId;
    private BigDecimal advanceAmount;
    private Integer advancePaymentId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DEBIT_NOTE_DETAIL_ID", nullable = false, unique = true)
    public Integer getDebitNoteDetailId() {
        return debitNoteDetailId;
    }

    public void setDebitNoteDetailId(Integer debitNoteDetailId) {
        this.debitNoteDetailId = debitNoteDetailId;
    }

    @Column(name = "DEBIT_NOTE_STATUS", nullable = true)
    public String getDebitNoteStatus() {
        return debitNoteStatus;
    }

    public void setDebitNoteStatus(String debitNoteStatus) {
        this.debitNoteStatus = debitNoteStatus;
    }

    @Column(name = "INVOICE_NUMBER", nullable = false)
    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    @Column(name = "AMOUNT", nullable = false)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "TOTAL_TAX", nullable = false)
    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    public void setTotalTaxes(BigDecimal totalTaxes) {
        this.totalTaxes = totalTaxes;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = false)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Column(name = "CREDIT_NOTE_RECEIVED", nullable = false, length = 1)
    public String getCreditNoteReceived() {
        return creditNoteReceived;
    }

    public void setCreditNoteReceived(String creditNoteReceived) {
        this.creditNoteReceived = creditNoteReceived;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAYMENT_REQUEST_ID", nullable = false)
    public PaymentRequestData getPaymentRequest() {
        return paymentRequest;
    }

    public void setPaymentRequest(PaymentRequestData paymentRequest) {
        this.paymentRequest = paymentRequest;
    }

    @Column(name = "CREDIT_NOTE_RECEIVING_TIME")
    public Date getCreditNoteReceivingTime() {
        return creditNoteReceivingTime;
    }

    public void setCreditNoteReceivingTime(Date creditNoteReceivingTime) {
        this.creditNoteReceivingTime = creditNoteReceivingTime;
    }

    @Column(name = "GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "GENERATED_BY", nullable = false)
    public Integer getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(Integer generatedBy) {
        this.generatedBy = generatedBy;
    }

    @Column(name = "LAST_UPDATED_BY")
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "UPDATE_TIME")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "BUSY_REFERENCE_NUMBER")
    public String getBusyReferenceNumber() {
        return busyReferenceNumber;
    }

    public void setBusyReferenceNumber(String busyReferenceNumber) {
        this.busyReferenceNumber = busyReferenceNumber;
    }

    @Column(name = "UPLOAD_DOC_ID")
    public Integer getDebitNoteDocId() {
        return debitNoteDocId;
    }

    public void setDebitNoteDocId(Integer debitNoteDocId) {
        this.debitNoteDocId = debitNoteDocId;
    }

    @Column(name = "ADVANCE_AMOUNT")
    public BigDecimal getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(BigDecimal advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    @Column(name = "ADVANCE_PAYMENT_ID")
    public Integer getAdvancePaymentId() {
        return advancePaymentId;
    }

    public void setAdvancePaymentId(Integer advancePaymentId) {
        this.advancePaymentId = advancePaymentId;
    }
}
