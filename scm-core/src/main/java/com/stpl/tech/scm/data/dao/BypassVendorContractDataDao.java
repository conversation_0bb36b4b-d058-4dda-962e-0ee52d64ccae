package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.BypassVendorContractData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface BypassVendorContractDataDao extends SCMAbstractDao {

    BypassVendorContractData getByVendorIdAndStatusIn(Integer vendorId, List<String> status);

    boolean getContractStatusByVendorId(Integer vendorId);

    List<BypassVendorContractData> getContractSByDateAndStatus(Date startDate, Date endDate,  String status);

    BypassVendorContractData changeStatusOfAContract(Integer vendorId, String contractIn, String setStatus);

    BypassVendorContractData getByVendorIdAndStatusNotIn(Integer vendorId, List<String> status);

    BypassVendorContractData getByPassVendorContract(Integer vendorId);

    BypassVendorContractData findById(Integer contractId);

    VendorDetailData findByVendorId(Integer vendorId);
    List<BypassVendorContractData> getAllInStatusAndApprover(String status, Integer approverId);
}
