package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.core.util.model.MeanTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "STRATEGY_MEAN_METADATA")
public class StrategyMeanMetaData {

    @Id
    @Column(name = "STRATEGY_MEAN_ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SUGGESTIVE_ORDERING_STRATEGY_ID", nullable = false)
    private SuggestiveOrderingStrategyMetadata suggestiveOrderingStrategy;

    @Column(name = "MEAN_TYPE")
    @Enumerated(EnumType.STRING)
    private MeanTypeEnum meanTypeEnum;

    @Column(name = "START_WEEK")
    private Integer startWeek;

    @Column(name = "END_WEEK")
    private Integer endWeek;

    @Column(name = "PERCENTAGE")
    private BigDecimal percentage;

    @Column(name = "STATUS")
    private String status;

}
