/**
 *
 */
package com.stpl.tech.scm.core.service.impl;

import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.OtpType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitBusinessType;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SkuMappingService;
import com.stpl.tech.scm.core.templates.VendorContractTemplate;
import com.stpl.tech.scm.core.util.MultiPartFileHelper;
import com.stpl.tech.scm.core.util.SCMConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.SkuDataAndTaxData;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.dao.SkuPriceDataDao;
import com.stpl.tech.scm.data.dao.VendorContractDataDao;
import com.stpl.tech.scm.data.dao.VendorContractItemDataDao;
import com.stpl.tech.scm.data.dao.VendorContractItemUnitDataDao;
import com.stpl.tech.scm.data.dao.VendorContractLogDao;
import com.stpl.tech.scm.data.model.BusinessDetailData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.InventoryListTypeData;
import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.PageRequestDetail;
import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.data.model.PurchaseProfile;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.data.model.SkuPriceData;
import com.stpl.tech.scm.data.model.SkuPriceHistory;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.data.model.UnitSkuMapping;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.VendorContractData;
import com.stpl.tech.scm.data.model.VendorContractItemData;
import com.stpl.tech.scm.data.model.VendorContractItemUnitData;
import com.stpl.tech.scm.data.model.VendorContractLogs;
import com.stpl.tech.scm.data.model.VendorContractSoInfo;
import com.stpl.tech.scm.data.model.VendorSkuMapping;
import com.stpl.tech.scm.data.model.WorkOrderApprovalMetaData;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.scm.data.transport.model.VendorOTPValidationDomain;
import com.stpl.tech.scm.domain.model.BusinessType;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.LogType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PackagingPriceData;
import com.stpl.tech.scm.domain.model.PageRequestType;
import com.stpl.tech.scm.domain.model.PreviousPricingDataVO;
import com.stpl.tech.scm.domain.model.PriceStatus;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SkuData;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.SkuPriceUpdateDetail;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.VendorContractItemDataVO;
import com.stpl.tech.scm.domain.model.VendorContractStatus;
import com.stpl.tech.scm.domain.model.VendorContractV2Dto;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.domain.model.VendorType;
import com.stpl.tech.scm.domain.model.WorkOrder;
import com.stpl.tech.scm.domain.model.WorkOrderApprovalMetaDataDto;
import com.stpl.tech.scm.domain.model.WorkOrderType;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;
import com.stpl.tech.scm.notification.email.UserContractEmailNotification;
import com.stpl.tech.scm.notification.email.VendorContractEmailNotification;
import com.stpl.tech.scm.notification.email.VendorContractEmailOTPNotification;
import com.stpl.tech.scm.notification.email.template.VendorContractEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorContractEmailOTPNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.notification.AttachmentData;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SkuMappingServiceImpl implements SkuMappingService {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseStockManagementServiceImpl.class);

    @Autowired
    private SkuMappingDao dao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private TaxDataCache taxDataCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private MappingCache mappingCache;

    @Autowired
    private SCMMetadataService scmMetadataService;

    @Autowired
    private SCMMetadataDao scmMetadataDao;

    @Autowired
    private SCMProductManagementService scmProductManagementService;
    @Autowired
    private EnvProperties props;
    @Autowired
    private FileArchiveService fileArchiveService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SMSClientProviderService providerService;

    @Autowired
    private SCMConstants scmConstants;

    @Autowired
    private VendorContractItemDataDao itemDataDao;

    @Autowired
    private VendorContractItemUnitDataDao unitDataDao;

    @Autowired
    private VendorContractDataDao dataDao;

    @Autowired
    private PaymentRequestManagementServiceImpl paymentRequestManagementService;

    @Autowired
    private SkuPriceDataDao skuPriceDataDao;

    @Autowired
    private VendorContractLogDao statusLogDao;

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.data.latest.service.SkuMappingService#updatePrices(com.
     * stpl.tech.scm.domain.model.SkuPriceUpdate)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePrices(SkuPriceUpdate data) throws SumoException {

        if(dao.updatePrices(data)) {
            if (data.getDetail().getUpdated() != null && data.getDetail().getUpdated().getDate() != null
                    && AppUtils.isCurrentBusinessDate(data.getDetail().getUpdated().getDate()) == 0) {
                dao.updateSkuPricesFromCurrentDay(data);
                SkuPriceData newPriceData = dao.find(SkuPriceData.class,data.getDetail().getKeyId());
                try{
                    scmMetadataService.saveAuditLog(data.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), data.getEmployeeId(), ((Object) newPriceData),
                            AuditChangeLogTypes.UPDATE_ENTRY.value());
                }catch (Exception e){
                    LOG.info("Error While saving Audit Log Data In Mongo",e);
                }


            }
        } else {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addPrice(SkuPriceUpdate data) {
        return dao.addPrice(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePricesV2(SkuPriceUpdate data) throws SumoException {
        try {
            if(Objects.isNull(data.getDetail().getKeyId())) {
                throw new SumoException("SkuPriceData id should not be null");
            }
            Optional<SkuPriceData> priceData = skuPriceDataDao.findById(data.getDetail().getKeyId());
            if(Objects.isNull(priceData)) {
                throw new SumoException("SkuPriceData not found of id " + data.getDetail().getKeyId());
            }
            SkuPriceData skuPriceData = priceData.get();
            skuPriceData.setPrice(data.getDetail().getUpdated().getValue());
            skuPriceData.setIsPriceChangeRequested(AppConstants.YES);
            skuPriceData = skuPriceDataDao.save(skuPriceData);
            newSkuPriceHistory(skuPriceData, data.getEmployeeName(), data.getEmployeeId());
            return true;
        } catch (SumoException exp) {
            throw exp;
        } catch (Exception exp) {
            LOG.error("Error : ", exp);
            throw new SumoException("Error while updating sku price data of skuPriceId " + data.getDetail().getKeyId() + exp);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addPriceV2(SkuPriceUpdate data) throws SumoException {
        try {
            Set<Integer> dispatchLocations = new HashSet<>();
            for(IdCodeName idCode : data.getDetail().getDispatchLocations()) {
                dispatchLocations.add(idCode.getId());
            }
            List<SkuPriceData> checkSkuPriceData = skuPriceDataDao.findByVendorIdAndSkuIdInAndPackagingIdInAndDeliveryLocationIdInAndDispatchLocationIdIn(
                    data.getDetail().getVendor().getId(), Set.of(data.getDetail().getSku().getId()), Set.of(data.getDetail().getPkg().getId()),
                    Set.of(data.getDetail().getDelivery().getId()), dispatchLocations
            );

            if(!CollectionUtils.isEmpty(checkSkuPriceData)) {
                throw new SumoException("Error : trying to add duplicate locations ", checkSkuPriceData.stream().map(SkuPriceData::getDispatchLocation).collect(Collectors.joining("<br>")));
            }

            List<SkuPriceData> skuPriceDataList = new ArrayList<>();
            SkuPriceData current = new SkuPriceData();
            current.setPackagingId(data.getDetail().getPkg().getId());
            current.setSkuId(data.getDetail().getSku().getId());
            current.setVendorId(data.getDetail().getVendor().getId());
            current.setDeliveryLocation(data.getDetail().getDelivery().getName().toUpperCase());
            current.setDeliveryLocationId(data.getDetail().getDelivery().getId());
            current.setPrice(data.getDetail().getCurrent().getValue());
            current.setStatus(AppConstants.ACTIVE);
            current.setLeadTime(data.getDetail().getLeadTime());
            current.setIsPriceChangeRequested(AppConstants.YES);

            for(IdCodeName idCode : data.getDetail().getDispatchLocations()) {
                SkuPriceData newSku = new SkuPriceData(current);
                newSku.setDispatchLocation(idCode.getCode().toUpperCase());
                newSku.setDispatchLocationId(idCode.getId());
                skuPriceDataList.add(newSku);
            }
            skuPriceDataList = skuPriceDataDao.saveAll(skuPriceDataList);
            for(SkuPriceData skuPriceData : skuPriceDataList) {
                newSkuPriceHistory(skuPriceData, data.getEmployeeName(), data.getEmployeeId());
            }
            return true;
        } catch (Exception exp) {
            LOG.error("Error : ", exp);
            throw new SumoException("Error while updating sku price data ", exp.getMessage());
        }
    }

    @SneakyThrows
    public void newSkuPriceHistory(SkuPriceData data, String name, Integer id) {
        SkuPriceHistory history = new SkuPriceHistory();
        history.setSkuPriceDataId(data.getSkuPriceKeyId());
        history.setCurrentPrice(data.getPrice());
        history.setRecordStatus(PriceStatus.APPLIED.name());
        history.setChangeType(PriceStatus.MANUAL_UPDATION.name());
        history.setCreatedAt(AppUtils.getCurrentTimestamp());
        history.setCreatedBy(dao.getName(name, id));
        dao.add(history, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<VendorDetail> searchVendorsForUnit(int unitId) {
        Map<Integer, VendorDetail> vendorDetailList = new HashMap<>();
        Map<Integer, VendorDetail> vendors = scmCache.getVendorDetails();
        List<UnitSkuVendorMapping> vendorSkuMappings = dao.searchActiveVendorMappingsForUnit(unitId);
        if (!vendorSkuMappings.isEmpty()) {
            //vendorDetailList = new HashMap<>();
            for (UnitSkuVendorMapping unitSkuVendorMapping : vendorSkuMappings) {
                Integer vendorId = unitSkuVendorMapping.getVendorId();
                if (vendors.containsKey(vendorId) && vendorDetailList.get(vendorId) == null) {
                    vendorDetailList.put(vendorId, vendors.get(vendorId).trim());
                }
            }
        }
        return vendorDetailList.values();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<IdCodeName> searchVendorsForUnitTrimmed(int unitId) {
        Map<Integer, IdCodeName> vendorDetailList = new HashMap<>();
        Map<Integer, VendorDetail> vendors = scmCache.getVendorDetails();
        List<UnitSkuVendorMapping> vendorSkuMappings = dao.searchActiveVendorMappingsForUnit(unitId);
        if (!vendorSkuMappings.isEmpty()) {
            //vendorDetailList = new HashMap<>();
            for (UnitSkuVendorMapping unitSkuVendorMapping : vendorSkuMappings) {
                Integer vendorId = unitSkuVendorMapping.getVendorId();
                if (vendors.containsKey(vendorId) && vendorDetailList.get(vendorId) == null) {
                    VendorDetail vd = vendors.get(vendorId);
                    if (VendorStatus.ACTIVE.equals(vd.getStatus())) {
                        vendorDetailList.put(vendorId,
                            SCMUtil.generateIdCodeName(vd.getVendorId(), "", vd.getEntityName()));
                    }
                }
            }
        }
        return vendorDetailList.values();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> getBusinessTypes() {
        List<BusinessDetailData> businessDetailData = dao.findAll(BusinessDetailData.class);
        List<IdCodeName> businessTypes = new ArrayList<>();
        if (!businessDetailData.isEmpty()) {
            for (BusinessDetailData detailData : businessDetailData) {

                businessTypes.add(SCMUtil.generateIdCodeName(detailData.getBusinessId(), "", detailData.getBusinessName()));
            }
        }
        return businessTypes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<SkuData> getSkusMappedToVendor(int unitId, int vendorId) {
        Map<Integer, SkuData> skus = new HashMap<>();
        List<UnitSkuMapping> skuMappings = dao.searchSkuMappingsForUnitAndVendor(unitId, vendorId);
        if (!skuMappings.isEmpty()) {
            for (UnitSkuMapping skuMapping : skuMappings) {
                Integer skuId = skuMapping.getSkuId();
                SkuDefinition sku = SCMUtil.clone(scmCache.getSkuDefinition(skuId), SkuDefinition.class);
                List<SkuPackagingMapping> packagings = scmCache.getSkuPackagingMappings(skuId);
                sku.getSkuPackagings().addAll(packagings);
                skus.put(skuId, convert(sku));
            }
        }
        return skus.values();
    }

    private SkuData convert(SkuDefinition sku) {
        SkuData skuData = new SkuData();
        skuData.setId(sku.getSkuId());
        skuData.setName(sku.getSkuName());
        skuData.setHsn(sku.getLinkedProduct().getCode());
        skuData.setUom(sku.getUnitOfMeasure());
        skuData.setProductId(sku.getLinkedProduct().getId());
        skuData.setLoose(sku.isSupportsLooseOrdering());
        List<PackagingPriceData> prices = sku.getSkuPackagings().stream().map(pkg -> {
            PackagingPriceData price = new PackagingPriceData();
            PackagingDefinition pkgDef = scmCache.getPackagingDefinition(pkg.getPackagingId());
            price.setId(pkg.getPackagingId());
            price.setName(pkgDef.getPackagingName());
            price.setCode(pkgDef.getPackagingCode());
            price.setRatio(pkgDef.getConversionRatio());
            price.setUom(pkgDef.getUnitOfMeasure());
            return price;
        }).collect(Collectors.toList());
        skuData.getPackagings().addAll(prices);
        Integer pid = sku.getLinkedProduct().getId();
        ProductDefinition pd = scmCache.getProductDefinition(pid);
        skuData.setCategory(pd.getCategoryDefinition().getId());
        return skuData;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.data.latest.service.SkuMappingService#searchPrices(int,
     * java.lang.String)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> searchPricesBySku(int skuId, Integer deliveryLocationId) {
        return dao.searchPricesBySku(skuId, deliveryLocationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> searchPricesByVendor(int vendorId, Integer locationId) {
        return dao.searchPricesByVendorDeliveryLocation(vendorId, locationId,true, false);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * searchSkuMappingsForUnit(int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchSkuMappingsForUnit(int unitId) {
        return dao.searchSkuMappingsForUnit(unitId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * searchUnitMappingsForSku(int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchUnitMappingsForSku(int skuId) {
        return dao.searchUnitMappingsForSku(skuId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * updateSkuMappingsForUnit(int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds) {
        boolean status = dao.updateSkuMappingsForUnit(employeeId, name, unitId, skuIds);
        if (status) {
            scmCache.refreshAvailableProductMapping();
        }
        return status;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds, Map<Integer, String> mappedInventoryWithSkuId, Map<Integer, String> productionUnitMapped,
                                            Map<Integer, String> mappedPackagingIds , Map<Integer,String> mappedTaxCodes,
                                            Map<Integer,Date> mappedVoDiscontinuedFrom, Map<Integer,Date> mappedRoDiscontinuedFrom) {

        boolean status = dao.updateSkuMappingsForUnit(employeeId, name, unitId, skuIds);
        if (mappedInventoryWithSkuId != null) {
            List<UnitSkuMapping> unitSkuMappings = dao.getUnitSkuMappingForUnitId(unitId, skuIds);
            List<InventoryListTypeData> inventoryListTypeData = dao.findAll(InventoryListTypeData.class);
            Map<String, Integer> inventoryListName = getInventoryListIdByName(inventoryListTypeData);
            List<ProductionUnitData>   productionUnitData =dao.findAll(ProductionUnitData.class);
            Map<String,Integer> productionUnitMap=getProductionUnitId(productionUnitData);
            List<PackagingDefinitionData> packagingDefinitionDataList = dao.findAll(PackagingDefinitionData.class);
            Map<String,Integer> packagingDefinitionDataMap = getPackagingListIdByName(packagingDefinitionDataList);
            mappedInventoryWithSkuId.forEach((skuID, inventoryName) -> {
                for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                    if ((unitSkuMapping.getSkuId() == skuID) && (inventoryListName.containsKey(inventoryName) && (unitSkuMapping.getInventoryList() != inventoryListName.get(inventoryName)))) {
                        unitSkuMapping.setInventoryList(inventoryListName.get(inventoryName));
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
            productionUnitMapped.forEach((skuID, unitName) -> {
                if (unitName != null) {
                    for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                        if ((unitSkuMapping.getSkuId() == skuID)) {
                            if (unitName != null) {
                                if(productionUnitMap.containsKey(unitName)) {
                                    unitSkuMapping.setProductionUnit(productionUnitMap.get(unitName));
                                    dao.update(unitSkuMapping, true);
                                    mappingCache.removeProductionLine(unitId,skuID);
                                    break;
                                }
                            }
                        }
                    }
                }
            });
            mappedPackagingIds.forEach((skuID, packagingName) -> {
                if (packagingName != null) {
                    for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                        if ((unitSkuMapping.getSkuId() == skuID) && packagingDefinitionDataMap.containsKey(packagingName) &&
                                (Objects.nonNull(packagingDefinitionDataMap.get(packagingName)) &&
                                        !packagingDefinitionDataMap.get(packagingName).equals(unitSkuMapping.getPackagingId()))) {
                            unitSkuMapping.setPackagingId(packagingDefinitionDataMap.get(packagingName));
                            dao.update(unitSkuMapping, true);
                            break;
                        }
                    }
                }
            });
            mappedTaxCodes.forEach((skuID,taxCode) ->{
                if(taxCode !=null){
                    for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                        if ((unitSkuMapping.getSkuId() == skuID) && !taxCode.equals(unitSkuMapping.getTaxCode()) ) {
                            unitSkuMapping.setTaxCode(taxCode);
                            dao.update(unitSkuMapping, true);
                            break;
                        }
                    }
                }
            });
            mappedVoDiscontinuedFrom.forEach((skuID,voDate) ->{
                for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                    if ((unitSkuMapping.getSkuId() == skuID)) {
                        if (Objects.nonNull(voDate) && Objects.nonNull(unitSkuMapping.getVoDisContinuedFrom())
                                && AppUtils.getDate(voDate).compareTo(unitSkuMapping.getVoDisContinuedFrom()) == 0) {
                            break;
                        }
                        if (Objects.isNull(voDate) && Objects.isNull(unitSkuMapping.getVoDisContinuedFrom())) {
                            break;
                        }
                        if(Objects.nonNull(voDate)) {
                            unitSkuMapping.setVoDisContinuedFrom(AppUtils.getDate(voDate));
                        } else {
                            unitSkuMapping.setVoDisContinuedFrom(null);
                        }
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
            mappedRoDiscontinuedFrom.forEach((skuID,roDate) ->{
                for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                    if ((unitSkuMapping.getSkuId() == skuID)) {
                        if (Objects.nonNull(roDate) && Objects.nonNull(unitSkuMapping.getRoDisContinuedFrom())
                                && AppUtils.getDate(roDate).compareTo(unitSkuMapping.getRoDisContinuedFrom()) == 0) {
                            break;
                        }
                        if (Objects.isNull(roDate) && Objects.isNull(unitSkuMapping.getRoDisContinuedFrom())) {
                            break;
                        }
                        if(Objects.nonNull(roDate)) {
                            unitSkuMapping.setRoDisContinuedFrom(AppUtils.getDate(roDate));
                        } else {
                            unitSkuMapping.setRoDisContinuedFrom(null);
                        }
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
        }
        if (status) {
            scmCache.refreshAvailableProductMapping();
        }
        return status;
    }

    private Map<String, Integer> getInventoryListIdByName(List<InventoryListTypeData> inventoryListTypeDatas) {
        Map<String, Integer> inventoryListName = new HashMap<>();
        for (InventoryListTypeData inventoryListTypeData : inventoryListTypeDatas) {
            inventoryListName.put(inventoryListTypeData.getListName(), inventoryListTypeData.getId());
        }
        return inventoryListName;
    }

    private Map<String, Integer> getPackagingListIdByName(List<PackagingDefinitionData> packagingDefinitionDataList) {
        Map<String, Integer> packagingDefinitionDataMap = new HashMap<>();
        for (PackagingDefinitionData packagingDefinitionData : packagingDefinitionDataList) {
            packagingDefinitionDataMap.put(packagingDefinitionData.getPackagingName(), packagingDefinitionData.getPackagingId());
        }
        return packagingDefinitionDataMap;
    }

    private Map<String,Integer> getProductionUnitId(List<ProductionUnitData> productionUnitData){
        Map<String, Integer> productionUnitMap = new HashMap<>();
        for(ProductionUnitData productionData:productionUnitData){
            productionUnitMap.put(productionData.getProductionUnitName(),productionData.getId());
        }
        return productionUnitMap;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * updateUnitMappingsForSku(int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds) {
        boolean status = dao.updateUnitMappingsForSku(employeeId, name, skuId, unitIds);
        if (status) {
            scmCache.refreshAvailableProductMapping();
        }
        return status;

    }


    private Boolean updateSiblingSkusInventoryList(Integer skuId , Map<Integer , Integer> unitInventoryListMap ){
        Integer productId = scmCache.getSkuDefinition(skuId).getLinkedProduct().getId();
        List<Integer> skuIds = scmProductManagementService.viewAllSkuByProduct(productId).get(productId).stream().map(sku -> sku.getSkuId()).
                collect(Collectors.toList());
        List<UnitSkuMapping> unitSkuMappings = dao.getActiveUnitSkuMappings(skuIds, new ArrayList<>(unitInventoryListMap.keySet()));
        unitSkuMappings.forEach(unitSkuMapping -> {
            unitSkuMapping.setInventoryList(unitInventoryListMap.get(unitSkuMapping.getUnitId()));
        });
        dao.update(unitSkuMappings,true);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds, Map<Integer, String> inventoryListWithMappedUnitId,
                                            Map<Integer, String> productionUnitMapped, Map<Integer, String> mappedPackagingIds , Map<Integer,String> mappedTaxCodes,
                                            Map<Integer,Date> mappedVoDiscontinuedFrom, Map<Integer,Date> mappedRoDiscontinuedFrom) {
        boolean status = dao.updateUnitMappingsForSku(employeeId, name, skuId, unitIds);
        if (inventoryListWithMappedUnitId != null) {
            List<UnitSkuMapping> unitSkuMappings = dao.getUnitSkuMappingForSKUS(skuId, unitIds);

            List<InventoryListTypeData> inventoryListTypeData = dao.findAll(InventoryListTypeData.class);
            Map<String, Integer> inventoryListName = getInventoryListIdByName(inventoryListTypeData);

            List<ProductionUnitData>   productionUnitData =dao.findAll(ProductionUnitData.class);
            Map<String,Integer> productionUnitMap=getProductionUnitId(productionUnitData);

            List<PackagingDefinitionData> packagingDefinitionDataList = dao.findAll(PackagingDefinitionData.class);
            Map<String,Integer> packagingDefinitionDataMap = getPackagingListIdByName(packagingDefinitionDataList);

            Map<Integer,Integer> unitInventoryListMap = new HashMap<>();

            inventoryListWithMappedUnitId.forEach((unitID, inventoryName) -> {
                for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                    if ((unitSkuMapping.getUnitId() == unitID) && (inventoryListName.containsKey(inventoryName) && (unitSkuMapping.getInventoryList() != inventoryListName.get(inventoryName)))) {
                        unitSkuMapping.setInventoryList(inventoryListName.get(inventoryName));
                        unitInventoryListMap.put(unitID,inventoryListName.get(inventoryName));
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
            if(!unitInventoryListMap.isEmpty()){
                updateSiblingSkusInventoryList(skuId,unitInventoryListMap);
            }
            productionUnitMapped.forEach((unitID, unitName) -> {
                if (unitName != null) {
                    for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                        if ((unitSkuMapping.getUnitId() == unitID) && productionUnitMap.containsKey(unitName) &&
                                (Objects.nonNull(productionUnitMap.get(unitName)) && !productionUnitMap.get(unitName).equals(unitSkuMapping.getProductionUnit()))) {
                            unitSkuMapping.setProductionUnit(productionUnitMap.get(unitName));
                            dao.update(unitSkuMapping, true);
                            mappingCache.removeProductionLine(unitID,unitSkuMapping.getSkuId());
                            break;
                        }
                    }
                }
            });
            mappedPackagingIds.forEach((unitID, packagingName) -> {
                if (packagingName != null) {
                    for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                        if ((unitSkuMapping.getUnitId() == unitID) && packagingDefinitionDataMap.containsKey(packagingName) &&
                                (Objects.nonNull(packagingDefinitionDataMap.get(packagingName)) &&
                                        !packagingDefinitionDataMap.get(packagingName).equals(unitSkuMapping.getPackagingId()))) {
                            unitSkuMapping.setPackagingId(packagingDefinitionDataMap.get(packagingName));
                            dao.update(unitSkuMapping, true);
                            break;
                        }
                    }
                }
            });
            mappedTaxCodes.forEach((unitId,taxCode) ->{
                if(taxCode !=null){
                    for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                        if ((unitSkuMapping.getUnitId() == unitId && !taxCode.equals(unitSkuMapping.getTaxCode()))) {
                            unitSkuMapping.setTaxCode(taxCode);
                            dao.update(unitSkuMapping, true);
                            break;
                        }
                    }
                }
            });
            mappedVoDiscontinuedFrom.forEach((unitId,voDate) ->{
                for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                    if ((unitSkuMapping.getUnitId() == unitId)) {
                        if (Objects.isNull(voDate) && Objects.isNull(unitSkuMapping.getVoDisContinuedFrom())) {
                            break;
                        }
                        if (Objects.nonNull(voDate) && Objects.nonNull(unitSkuMapping.getVoDisContinuedFrom())
                                && AppUtils.getDate(voDate).compareTo(unitSkuMapping.getVoDisContinuedFrom()) == 0) {
                            break;
                        }
                        if (Objects.nonNull(voDate)) {
                            unitSkuMapping.setVoDisContinuedFrom(AppUtils.getDate(voDate));
                        } else {
                            unitSkuMapping.setVoDisContinuedFrom(null);
                        }
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
            mappedRoDiscontinuedFrom.forEach((unitId,roDate) ->{
                for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                    if ((unitSkuMapping.getUnitId() == unitId)) {
                        if (Objects.isNull(roDate) && Objects.isNull(unitSkuMapping.getRoDisContinuedFrom())) {
                            break;
                        }
                        if (Objects.nonNull(roDate) && Objects.nonNull(unitSkuMapping.getRoDisContinuedFrom())
                                && AppUtils.getDate(roDate).compareTo(unitSkuMapping.getRoDisContinuedFrom()) == 0) {
                            break;
                        }
                        if(Objects.nonNull(roDate)) {
                            unitSkuMapping.setRoDisContinuedFrom(AppUtils.getDate(roDate));
                        } else {
                            unitSkuMapping.setRoDisContinuedFrom(null);
                        }
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
        }
        if (status) {
            scmCache.refreshAvailableProductMapping();
        }
        return status;

    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * searchSkuMappingsForVendor(int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchSkuMappingsForVendor(int vendorId) {
        return dao.searchSkuMappingsForVendor(vendorId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchVendorMappingsForSku(int skuId) {
        return dao.searchVendorMappingsForSku(skuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchVendorMappingsForBusiness(int businessId) {
        return dao.searchVendorMappingsForBusiness(businessId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * updateSkuMappingsForVendor(int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateVendorSkuMapping(int employeeId, String name, int vendorId, int skuId, String status) {
        return dao.updateVendorSkuMapping(employeeId, name, vendorId, skuId, status);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#allActiveUnits()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> allUnits() {
        return dao.allActiveUnits();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#allActiveSKU()
     */
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<IdCodeNameStatus> allSKU() {
        return dao.allActiveSKU();
    }

//    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true)
//    @Override
//    public List<unitSkuMapping>

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.data.latest.service.SkuMappingService#allActiveVendors( )
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> allVendors() {
        return dao.allActiveVendors();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> allVendorsWithBusinessType() {
        List<VendorDetail> allVendors = new ArrayList<>(scmCache.getVendorDetails().values());
        if (CollectionUtils.isEmpty(allVendors)) {
            return new ArrayList<IdCodeNameStatus>();
        }

        List<IdCodeNameStatus> listOfAllVendorsWithBusinessTypeAsNotService = new ArrayList<>();
        for (VendorDetail vendor : allVendors) {
            if (vendor.getCompanyDetails() == null || BusinessType.SERVICES.equals(vendor.getCompanyDetails().getBusinessType()) || VendorType.CUSTOMER.equals(vendor.getType())) {
                continue;
            }
            if (vendor.getStatus().equals(VendorStatus.ACTIVE)) {
                IdCodeNameStatus o = new IdCodeNameStatus();
                o.setId(vendor.getVendorId());
                o.setStatus(vendor.getStatus().value());
                o.setName(vendor.getEntityName());
                o.setByPassContract(vendor.getByPassContract());
                o.setCategory(vendor.getType().value());
                listOfAllVendorsWithBusinessTypeAsNotService.add(o);
            }
        }
        return listOfAllVendorsWithBusinessTypeAsNotService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuProfiles(Map<Integer, String> skuListWithInventoryListId, int unitId, String profiles) {
        List<InventoryListTypeData> inventoryListTypeData = dao.findAll(InventoryListTypeData.class);
        Map<String, Integer> inventoryListName = getInventoryListIdByName(inventoryListTypeData);
        return dao.updateSkuProfiles(skuListWithInventoryListId, unitId, profiles, inventoryListName);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.SkuMappingService#addSkuMappingsForVendor(
     * int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addSkuMappingsForVendor(int employeeId, String employeeName, int vendorId, List<IdCodeName> skuIds) {
        return dao.addSkuMappingsForVendor(employeeId, employeeName, vendorId, skuIds);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addSkuMappingsForVendorAlias(IdCodeName request) throws SumoException {
        int vendorId = Integer.parseInt(request.getName());
        int vendorSkuMappingId = dao.getVendorSkuMappingId(request.getId(), vendorId);
        VendorSkuMapping vendorSkuMapping = dao.find(VendorSkuMapping.class, vendorSkuMappingId);
        if (vendorSkuMapping != null) {
            vendorSkuMapping.setAlias(request.getCode());
            vendorSkuMapping = dao.update(vendorSkuMapping, true);
            if (vendorSkuMapping == null) {
                throw new SumoException("Error setting sku alias");
            }
        }
        return true;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.SkuMappingService#addVendorMappingsForSku(
     * int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addVendorMappingsForSku(int employeeId, String employeeName, int skuId, List<Integer> vendorIds) {
        return dao.addVendorMappingsForSku(employeeId, employeeName, skuId, vendorIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addVendorMappingsForBusiness(int employeeId, String employeeName, int businessId, List<Integer> vendorIds) {
        return dao.addVendorMappingsForBusiness(employeeId, employeeName, businessId, vendorIds);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SkuMappingService#cancelPriceUpdate(com.
     * stpl.tech.scm.domain.model.SkuPriceUpdate)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelPriceUpdate(SkuPriceUpdate data) {
        return dao.cancelPriceUpdate(data);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SkuMappingService#updatePriceStatus(com.
     * stpl.tech.scm.domain.model.SkuPriceUpdate)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePriceStatus(SkuPriceUpdate data) {
        return dao.updatePriceStatus(data);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SkuMappingService#
     * searchSkuMappingsForVendorAndUnit(int, int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitVendorSkuMapping> searchSkuMappingsForVendorAndUnit(int unitId, int vendorId) {
        return dao.searchSkuMappingsForVendorAndUnit(unitId, vendorId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SkuMappingService#
     * updateSkuMappingsForVendorAndUnit(com.stpl.tech.scm.domain.model.
     * UpdateUnitVendorSkuMapping)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<UnitVendorSkuMapping> updateSkuMappingsForVendorAndUnit(UpdateUnitVendorSkuMapping data) {
        return dao.updateSkuMappingsForVendorAndUnit(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<unitSkuMappingDetail> getSkusProfileForUnit(int unit, List<Integer> sku) {
        return dao.getSkusProfileForUnit(unit, sku);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuDataAndTaxData> getSkuAndTaxData(int vendorDispatchId, int unitId, int vendorId , Boolean toPickFromUnitMapping) {
        List<SkuDataAndTaxData> skuDataAndTaxDataList = new ArrayList<>();
        VendorDetail vendor = scmCache.getVendorDetail(vendorId);
        UnitBasicDetail unitDetail = masterDataCache.getUnitBasicDetail(unitId);
        if (vendor != null && unitDetail != null) {
            Optional<String> deliveryLocation = Optional.ofNullable(unitDetail.getLocationCode());
            Optional<VendorDispatchLocation> dispatchLocation = vendor.getDispatchLocations().stream()
                .filter(vendorDispatchLocation -> vendorDispatchLocation.getDispatchId().equals(vendorDispatchId))
                .findFirst();
            if (dispatchLocation.isPresent() && deliveryLocation.isPresent()) {
                VendorDispatchLocation location = dispatchLocation.get();
                String locationCode = location.getLocationId();
                Location locationDetail = masterDataCache.getAllLocations().get(locationCode);
                int stateId = locationDetail.getState().getId();

                Location dispatchLoc = masterDataCache.getAllLocations().get( locationDetail.getCode() );
                Location deliveryLoc = masterDataCache.getAllLocations().get( deliveryLocation.get() );
                List<SkuPriceDetail> skuPriceList = dao.searchSkuPricesForVendorAndUnit(unitId, vendorId,
                    dispatchLoc.getId(), deliveryLoc.getId());
                List<Integer> skuIds = skuPriceList.stream().map(skuPriceDetail -> skuPriceDetail.getSku().getId()).collect(Collectors.toList());
                Map<Integer,Map<Integer,String>> skuPackagingTaxMap = new HashMap<>();
                    if(toPickFromUnitMapping.equals(Boolean.TRUE) && Objects.nonNull(skuIds) && !skuIds.isEmpty()){
                        dao.findAllUnitSkuPackagingTaxMappingByUnit(skuIds,unitId).forEach(mapping ->{
                            Integer skuId = mapping.getSkuId();
                            Integer packagingId = mapping.getPackagingId();
                            if(!skuPackagingTaxMap.containsKey(skuId)){
                                skuPackagingTaxMap.put(skuId,new HashMap<>());
                            }
                            if(!skuPackagingTaxMap.get(skuId).containsKey(packagingId)){
                                skuPackagingTaxMap.get(skuId).put(packagingId,mapping.getTaxCode());
                            }

                        });
                    }
                if (skuPriceList != null && !skuPriceList.isEmpty()) {
                    Map<Integer, String> skuAliasMap = new HashMap<>();
                    skuPriceList.forEach(skuPriceDetail -> {
                        if (!skuAliasMap.containsKey(skuPriceDetail.getSku().getId()) && skuPriceDetail.getSku().getCode() != null) {
                            skuAliasMap.put(skuPriceDetail.getSku().getId(), skuPriceDetail.getSku().getCode());
                        }
                    });
                    Map<Integer, List<PackagingPriceData>> skuPkgPrices = new HashMap<>();
                    skuPriceList.forEach(skuPriceDetail -> {
                        Integer skuId = skuPriceDetail.getSku().getId();
                        List<PackagingPriceData> priceList = skuPkgPrices.get(skuId);
                        if (priceList == null) {
                            priceList = new ArrayList<>();
                        }
                        priceList.add(convert(skuPriceDetail));
                        skuPkgPrices.put(skuId, priceList);
                    });
                    List<SkuData> skuDataList = new ArrayList<>();
                    skuPkgPrices.keySet().forEach(skuId -> {
                        SkuDefinition sku = scmCache.getActiveSkuDefinition(skuId);
                        if (sku != null) {
                            SkuData skuData = convertToSkuData(sku, skuPkgPrices.get(skuId) ,toPickFromUnitMapping , unitId);
                            if (skuAliasMap.containsKey(skuData.getId())) {
                                skuData.setAlias(skuAliasMap.get(skuData.getId()));
                            }
                            skuDataList.add(skuData);
                        }
                    });
                    skuDataList.forEach(skuData -> {
                        if (skuData.getHsn() == null) {
                            return;
                        }
                        TaxData taxData = taxDataCache.getTaxData(stateId, skuData.getHsn());
                        Map<Integer,TaxData> packagingTaxMap = new HashMap<>();
                        if(toPickFromUnitMapping.equals(Boolean.TRUE)) {
                            Map<Integer,String> tempMap = skuPackagingTaxMap.getOrDefault(skuData.getId(),new HashMap<>());
                            tempMap.keySet().forEach(packagingId ->{
                                packagingTaxMap.put(packagingId, taxDataCache.getTaxData(stateId, tempMap.get(packagingId)));
                            });
                        }
                        skuDataAndTaxDataList.add(new SkuDataAndTaxData(taxData, skuData,packagingTaxMap));
                    });
                }
            }
        }
        List<Integer> skuIds = skuDataAndTaxDataList.stream().mapToInt(SkuDataAndTaxData::getId).boxed().collect(Collectors.toList());
        List<UnitSkuMapping> unitSkuMappings = dao.getActiveUnitSkuMappings(skuIds, Collections.singletonList(unitId));
        Map<Integer, Date> voDates = getVoDatesMap(unitSkuMappings);
        LOG.info("Before checking the VO discontinued Date SKU List is : {}",skuDataAndTaxDataList.size());
        return getFinalSkuData(skuDataAndTaxDataList, voDates);
    }

    private List<SkuDataAndTaxData> getFinalSkuData(List<SkuDataAndTaxData> skuDataAndTaxDataList, Map<Integer,Date> voDates) {
        List<SkuDataAndTaxData> result = new ArrayList<>();
        skuDataAndTaxDataList.forEach(skuDataAndTaxData -> {
            if (verifyDiscontinuedSku(skuDataAndTaxData, voDates)) {
                result.add(skuDataAndTaxData);
            }
        });
        LOG.info("After checking the VO discontinued Date SKU List is : {}",result.size());
        return result;
    }

    private Map<Integer,Date> getVoDatesMap(List<UnitSkuMapping> unitSkuMappings) {
        Map<Integer, Date> result = new HashMap<>();
        unitSkuMappings.forEach(unitSkuMapping -> {
            if (Objects.nonNull(unitSkuMapping.getVoDisContinuedFrom())) {
                if (!result.containsKey(unitSkuMapping.getSkuId())) {
                    result.put(unitSkuMapping.getSkuId(), unitSkuMapping.getVoDisContinuedFrom());
                } else {
                    Date alreadyStored = result.get(unitSkuMapping.getSkuId());
                    if (alreadyStored.compareTo(unitSkuMapping.getVoDisContinuedFrom()) < 0) {
                        result.put(unitSkuMapping.getSkuId(), unitSkuMapping.getVoDisContinuedFrom());
                    }
                }
            }
        });
        return result;
    }

    private Boolean verifyDiscontinuedSku(SkuDataAndTaxData skuDataAndTaxData, Map<Integer, Date> voDates) {
        try {
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuDataAndTaxData.getSkuData().getId());
            Date voDiscontinuedDate = skuDefinition.getVoDisContinuedFrom();
            if (Objects.nonNull(voDiscontinuedDate)) {
                return AppUtils.getCurrentTimestamp().compareTo(voDiscontinuedDate) < 0;
            } else {
                LOG.info("Trying to get the unit sku mapping :: for sku :: {}",skuDataAndTaxData.getSkuData().getName());
                voDiscontinuedDate = voDates.get(skuDataAndTaxData.getSkuData().getId());
                if (Objects.nonNull(voDiscontinuedDate)) {
                    return AppUtils.getCurrentTimestamp().compareTo(voDiscontinuedDate) < 0;
                }
                return true;
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While verifying Discontinued SKU ::: ",e);
        }
        return false;
    }

    @Override
    public String getHsnCodeFromUnitSkuMapping(SkuDefinition sku , Integer unitId){
        UnitSkuMapping skuMapping = dao.findSkuMappingBySkuAndUnit(sku.getSkuId(), unitId);
        if(Objects.nonNull(skuMapping) && Objects.nonNull(skuMapping.getTaxCode())){
            return skuMapping.getTaxCode();
        }else{
            return sku.getLinkedProduct().getCode();
        }
    }

    private SkuData convertToSkuData(SkuDefinition skuDefinition, List<PackagingPriceData> prices , Boolean toPickFromUnitMapping , Integer unitId) {
        SkuData data = new SkuData();
        data.setName(skuDefinition.getSkuName());
        data.setUom(skuDefinition.getUnitOfMeasure());
        data.setLoose(skuDefinition.isSupportsLooseOrdering());
        if(toPickFromUnitMapping.equals(Boolean.TRUE) && Objects.nonNull(unitId)){
            data.setHsn(getHsnCodeFromUnitSkuMapping(skuDefinition,unitId));
        }else{
            data.setHsn(skuDefinition.getLinkedProduct().getCode());
        }
        data.setId(skuDefinition.getSkuId());
        data.getPackagings().clear();
        data.getPackagings().addAll(prices);
        Integer pid = skuDefinition.getLinkedProduct().getId();
        ProductDefinition pd = scmCache.getProductDefinition(pid);
        data.setSubCategory(pd.getSubCategoryDefinition().getId());
        data.setProductId(pid);
        data.setCategory(pd.getCategoryDefinition().getId());
        return data;
    }

    private PackagingPriceData convert(SkuPriceDetail skuPriceDetail) {
        PackagingPriceData data = new PackagingPriceData();
        data.setId(skuPriceDetail.getPkg().getId());
        data.setCode(skuPriceDetail.getPkg().getCode());
        data.setName(skuPriceDetail.getPkg().getName());
        data.setPrice(skuPriceDetail.getCurrent().getValue()); // pkg price
        data.setRatio(skuPriceDetail.getPkg().getRatio());
        data.setUom(skuPriceDetail.getPkg().getUom());
        data.setLeadTime(skuPriceDetail.getLeadTime());
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> getSkuPackagingPriceForVendorsForUnit(int unitId, Set<Integer> vendorIds,
                                                                      Integer deliveryLocationId) {
        return dao.getSkuPackagingPriceForVendorsForUnit(unitId, vendorIds, deliveryLocationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> getSkuPackagingPriceForUnit(int unitId, Integer deliveryLocationId) {
        return dao.getSkuPackagingPriceForUnit(unitId, deliveryLocationId);
    }

    @Override
    public List<IdCodeNameStatus> unitsByBusinessType(final UnitBusinessType businessType) {
        List<IdCodeNameStatus> units = masterDataCache.getUnits().values().stream()
            .filter(unit -> unit.getUnitBusinessType().equals(businessType))
            .filter(unit -> unit.getStatus().equals(UnitStatus.ACTIVE))
            .map(unit -> {
                IdCodeNameStatus o = new IdCodeNameStatus();
                o.setId(unit.getId());
                o.setName(unit.getName() + "(" + unit.getCompany().getName() + ")");
                o.setStatus(unit.getStatus().name());
                return o;
            })
            .collect(Collectors.toList());
        return units;
    }

    @Override
    public List<Integer> getSubCategories(List<Integer> profiles) {
        List<PurchaseProfile> mappings = dao.getPurchaseMappings(profiles);
        if (mappings != null && !mappings.isEmpty()) {
            return mappings.stream()
                .mapToInt(PurchaseProfile::getSubCategory)
                .boxed().collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<String> getUnitDistanceMapping(int firstUnitId, int secondUnitId) {
        List<String> distance = dao.getDistanceOfUnits(firstUnitId, secondUnitId);
        return distance;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateMappingsForUnit(int firstUnitId, Integer firstMappingId, BigDecimal firstDistance, int secondUnitId,
                                         Integer secondMappingId, BigDecimal secondDistance) throws SumoException {
        return dao.updateUnitDistanceMappingData(firstUnitId, firstMappingId, firstDistance, secondUnitId, secondMappingId, secondDistance,true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuLeadTime(int vendorId,int leadTime) {
        if(leadTime!=0) {
            dao.updateSkuLeadTime(vendorId,leadTime);
            return true;
        } else{
            return  false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateLeadTime(IdIndex data) {
        SkuPriceData skuPriceData = dao.find(SkuPriceData.class,data.getId());
        if(skuPriceData!=null) {
            skuPriceData.setLeadTime(data.getIndex());
            dao.update(skuPriceData, false);
            return true;
        } else{
            return  false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer,Integer> getSibLingSkusInventoryListId(List<Integer> unitIds , Integer skuId){
        Map<Integer,Integer> skuUnitInventoryListMap = new HashMap<>();
        Integer productId = scmCache.getSkuDefinition(skuId).getLinkedProduct().getId();
        List<Integer> siblingSkuIds = scmProductManagementService.viewAllSkuByProduct(productId).get(productId).stream().map(sku -> sku.getSkuId()).
                collect(Collectors.toList());
        List<Integer> skipUnitIds = new ArrayList<>();
        List<UnitSkuMapping> unitSkuMappings = dao.getActiveUnitSkuMappings(siblingSkuIds, unitIds);
        unitSkuMappings.forEach(unitSkuMapping -> {
            if(skipUnitIds.contains(unitSkuMapping.getUnitId())){
                //do nothing
            }else{
                if(!skuUnitInventoryListMap.containsKey(unitSkuMapping.getUnitId())){
                    skuUnitInventoryListMap.put(unitSkuMapping.getUnitId(),unitSkuMapping.getInventoryList());
                }else{
                    if(!skuUnitInventoryListMap.get(unitSkuMapping.getUnitId()).equals(unitSkuMapping.getInventoryList())){
                        skuUnitInventoryListMap.remove(unitSkuMapping.getUnitId());
                        skipUnitIds.add(unitSkuMapping.getUnitId());
                    }
                }
            }


        });
        return skuUnitInventoryListMap;


    }

    @Override
    public Map<Integer, SkuPackagingTaxMapping> getAllUnitSkuPackagingTaxMappingByStatus(Integer skuId, Integer packagingId,List<String> statuses) {
        return dao.findAllUnitSkuPackagingTaxMappingByStatus(skuId, packagingId,statuses).stream().collect(Collectors.toMap(SkuPackagingTaxMapping::getUnitId, Function.identity()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateUnitSkuPackagingTaxMapping(Integer skuId, Integer packagingId, Map<Integer, String> unitToTaxMap, Integer employeeId) {
        dao.updateUnitSkuPackagingTaxMapping(employeeId, skuId, packagingId, unitToTaxMap);
        if (Objects.nonNull(unitToTaxMap) && unitToTaxMap.size() > 0) {
            List<SkuPackagingTaxMapping> skuPackagingTaxMappingList = dao.findAllUnitSkuPackagingTaxMappingByStatus(skuId, packagingId, Arrays.asList(AppConstants.ACTIVE));
            List<SkuPackagingTaxMapping> updatedMappings = new ArrayList<>();

            skuPackagingTaxMappingList.forEach((mapping) -> {
                Integer unitId = mapping.getUnitId();
                if (unitToTaxMap.containsKey(unitId) && !unitToTaxMap.get(unitId).equals(mapping.getTaxCode())) {
                    mapping.setTaxCode(unitToTaxMap.get(unitId));
                    mapping.setUpdatedBy(employeeId);
                    mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
                    updatedMappings.add(mapping);
                }

            });
            dao.update(updatedMappings,true);
        }

    return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean converUnitDistanceToZipCodeDistance() throws SumoException {
       List<UnitDistanceMappingData> res =  scmMetadataDao.getUnitDistanceMapping();
       for(UnitDistanceMappingData u : res){
           dao.saveZipCodeDistance(u.getSourceUnitId(),u.getDestinationUnitId(),u.getDistance(),u.getDistance());
       }
       return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean generateSkuPriceUpdateRequest(List<SkuPriceUpdate> skuPriceUpdates) throws SumoException {
        for (SkuPriceUpdate data : skuPriceUpdates) {
            if(dao.updatePrices(data)) {
                LOG.info("Request Generated for SKU {} ",data.getDetail().getSku().getName());
                try{
                    scmMetadataService.saveAuditLog(data.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), data.getEmployeeId(), ((Object) data),
                            AuditChangeLogTypes.UPDATE_ENTRY.value());
                }catch (Exception e){
                    LOG.info("Error While saving Audit Log Data In Mongo",e);
                }
            }
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean processPriceRequestForVendor(SkuPriceUpdateDetail data) {
        Set<Integer> processedId = new HashSet<>();
        data.getApprovedNew().forEach(val -> {
            try {
                dao.processPriceRequestForVendor(val, PriceStatus.PENDING, PriceStatus.CREATED, processedId);
                scmMetadataService.saveAuditLog(val.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), val.getEmployeeId(), ((Object) val),
                        AuditChangeLogTypes.UPDATE_ENTRY.value());
            } catch (Exception e) {
                LOG.info("Error While saving Audit Log Data In Mongo", e);
            }
        });
        data.getApproved().forEach(val -> {
            try {
                dao.processPriceRequestForVendor(val, PriceStatus.REACTIVATION_REQUESTED, PriceStatus.APPLIED, processedId);
                scmMetadataService.saveAuditLog(val.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), val.getEmployeeId(), ((Object) val),
                        AuditChangeLogTypes.UPDATE_ENTRY.value());
            } catch (Exception e) {
                LOG.info("Error While saving Audit Log Data In Mongo", e);
            }
        });
        data.getRejectedNew().forEach(val -> {
            try {
                dao.processPriceRequestForVendor(val, PriceStatus.REJECTED, PriceStatus.CREATED, processedId);
                scmMetadataService.saveAuditLog(val.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), val.getEmployeeId(), ((Object) val),
                        AuditChangeLogTypes.UPDATE_ENTRY.value());
            } catch (Exception e) {
                LOG.info("Error While saving Audit Log Data In Mongo", e);
            }
        });
        data.getRejected().forEach(val -> {
            try {
                dao.processPriceRequestForVendor(val, PriceStatus.DEACTIVATION_REQUESTED, PriceStatus.APPLIED, processedId);
                scmMetadataService.saveAuditLog(val.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), val.getEmployeeId(), ((Object) val),
                        AuditChangeLogTypes.UPDATE_ENTRY.value());
            } catch (Exception e) {
                LOG.info("Error While saving Audit Log Data In Mongo", e);
            }
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> previewVendorPriceChange(Integer vendorId) {
        return dao.vendorPriceChangeAsPerStatus(vendorId,List.of(PriceStatus.PENDING.name(),PriceStatus.REACTIVATION_REQUESTED.name()));
    }


    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveVendorPriceChangeV2(WorkOrderData woData, Integer loggedInUser) throws SumoException, TemplateRenderingException, EmailGenerationException {
        WorkOrderData oldWoData = null;
        Integer vendorId = woData.getContractData().getVendorId();
        List<SkuPriceData> skuPriceDataList = skuPriceDataDao.findByVendorId(vendorId);
        Map<String, SkuPriceData> skuIdMap = new HashMap<>();
        if( !CollectionUtils.isEmpty(skuPriceDataList) ) {
            skuIdMap = skuPriceDataList.stream().collect(Collectors.toMap(
                                    item -> SCMUtil.generateUniqueKey(String.valueOf(item.getSkuId()), String.valueOf(item.getPackagingId()), item.getDispatchLocationId().toString(), item.getDeliveryLocationId().toString()),
                                    Function.identity()));
        }

        VendorContractData previousContract = dataDao.findPreviousVendorContractItemsFromEachWO(woData, WorkOrderType.DEFAULT.equals(woData.getWorkOrderType()) );

        for(VendorContractItemData item : woData.getVendorContractItemDataList()) {
            String KEY = SCMUtil.generateUniqueKey(String.valueOf(item.getSkuId()), String.valueOf(item.getSkuPackagingId()), item.getDispatchLocationId().toString(), item.getDeliveryLocationId().toString());
            if(VendorContractStatus.REMOVED.name().equals(item.getStatus())) {
                continue;
            }
            Location dispatchLocation = masterDataCache.getLocationbyId(item.getDispatchLocationId());
            Location deliveryLocation = masterDataCache.getLocationbyId(item.getDeliveryLocationId());
            String taxCode = scmCache.getProductDefinition(scmCache.getSkuDefinition(item.getSkuId()).getLinkedProduct().getId()).getTaxCode();
            TaxData taxData = taxDataCache.getTaxData(dispatchLocation.getState().getId(), taxCode);
            BigDecimal taxPercentage = BigDecimal.ZERO;
            if (dispatchLocation.getState().getCode().equalsIgnoreCase(deliveryLocation.getState().getCode())) {
                taxPercentage = AppUtils.add(taxData.getState().getCgst(), taxData.getState().getSgst());
            } else {
                taxPercentage = AppUtils.add(taxData.getState().getIgst(),BigDecimal.ZERO);
            }
            SkuPriceData skuPriceData = skuIdMap.get(KEY);
            if (skuPriceData != null) {
                if(skuPriceData.getPrice().compareTo(item.getUpdatedPrice()) == 0) {
                    item.setSkuPriceState(VendorContractItemData.SkuPriceState.REPEATED_ITEM);
                } else {
                    item.setSkuPriceState(VendorContractItemData.SkuPriceState.PRICE_UPDATE);
                }
            } else {
                item.setSkuPriceState(VendorContractItemData.SkuPriceState.NEW_ITEM);
            }
            item.setTaxCode(taxCode);
            item.setTaxPercentage(taxPercentage);
            itemDataDao.save(item);
        }

        Collection<IdCodeName> list = scmConstants.getVendorContractTemplate().values();
        Integer templeteId = 2;
        for(IdCodeName i : list) {
            if(woData.getWorkOrderType().equals(WorkOrderType.DEFAULT)) {
                if(i.getCode().equalsIgnoreCase(SCMConstants.TemplateCode.MASTER_VENDOR_CONTRACT.code)) {
                    templeteId = i.getId();
                    break;
                }
            } else {
                if(i.getCode().equalsIgnoreCase(SCMConstants.TemplateCode.WORK_ORDER.code)) {
                    templeteId = i.getId();
                    break;
                }
            }
            templeteId = i.getId();
        }
        DocumentDetail dd = generateContractUsingTemplateV2(woData.getWorkOrderId(), templeteId);

        woData.getWoApprovalMetaData().setUnsignedDocumentId( dd.getDocumentId() );
        woData.getWoApprovalMetaData().setAuthSignedDocumentId( dd.getDocumentId() );
        woData.getWoApprovalMetaData().setTemplateId(templeteId);
        woData = dataDao.update(woData, false);
        if(AppConstants.YES.equals(woData.getIsByPassed())) {
            if( AppUtils.getBusinessDate().compareTo(AppUtils.getDate(woData.getStartDate()))>=0 ) {
                dao.applyWorkOrder(woData, loggedInUser);
                return;
            }
        } else {
            triggerVendorContractMailV2( woData.getWorkOrderId(), woData );
        }
        EmployeeBasicDetail createdByDetails = masterDataCache.getEmployeeBasicDetail(woData.getCreatedBy());
        if( StringUtils.hasText(createdByDetails.getEmailId()) ) {
            new UserContractEmailNotification("Vendor Price Approver Approved", VendorContractStatus.APPROVER_BY_PASSED, woData.getWorkOrderId(), scmCache.getVendorDetail(woData.getContractData().getVendorId()).getEntityName(), true, masterDataCache.getEmployeeBasicDetail(loggedInUser).getName(), createdByDetails.getEmailId(), props.getEnvType() ).sendEmail();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorContractV2Dto> getVendorContractV2(Integer vendorId, String status, Date startDate, Date endDate, Integer vendorContractId) {
        List<VendorContractV2Dto> vendorContracts = new ArrayList<>();
        List<VendorContractData> contractDataList = dao.getVendorContractV2(vendorId, status, startDate, endDate, vendorContractId);

        for(VendorContractData contract : contractDataList) {
            VendorContractV2Dto dto = new VendorContractV2Dto();
            dto.setContractId(contract.getContractId());
            dto.setVendorId(contract.getVendorId());
            dto.setVendorUserName( SCMUtil.getNameWithId(scmCache.getVendorDetail(contract.getVendorId()).getEntityName(), contract.getVendorId()) );
            dto.setStatus(contract.getStatus());
            dto.setCreatedByName( SCMUtil.getNameWithId(masterDataCache.getEmployee(contract.getCreatedBy()), contract.getCreatedBy()) );
            dto.setStartDate(contract.getStartDate());
            dto.setEndDate(contract.getEndDate());

            WorkOrderData woData = SCMUtil.getRecentWorkOrderByContract( contract.getWorkOrderDataSet() );
            dto.setIsByPassed( woData.getIsByPassed() );
            vendorContracts.add(dto);
        }
        vendorContracts.sort(Comparator.comparing(VendorContractV2Dto::getContractId).reversed());
        return vendorContracts;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ApiResponse getWorkOrdersByContractId(Integer contractId) throws SumoException {
        VendorContractData vendorContractData = dataDao.findById( contractId );
        if( Objects.isNull(vendorContractData) ) {
            throw new SumoException("Vendor Contract Not Found for contractId : " + contractId);
        }

        Set<WorkOrderData> workOrderDataList = vendorContractData.getWorkOrderDataSet();
        List<WorkOrder> workOrders = new ArrayList<>();

        for(WorkOrderData woData : workOrderDataList) {
            WorkOrder workOrder = new WorkOrder();
            workOrder.setWorkOrderId(woData.getWorkOrderId());
            workOrder.setGeneratedWorkOrderNumber( woData.getGeneratedWorkOrderNumber() );
            workOrder.setWorkOrderStatus(woData.getWorkOrderStatus());
            workOrder.setWorkOrderType(woData.getWorkOrderType());
            workOrder.setCreatedByName( SCMUtil.getNameWithId(masterDataCache.getEmployee(woData.getCreatedBy()), woData.getCreatedBy()) );
            workOrder.setIsByPassed( woData.getIsByPassed() );
            workOrder.setStartDate(woData.getStartDate());
            workOrder.setEndDate(woData.getEndDate());
            workOrder.setWorkOrderDocId( woData.getWoApprovalMetaData().getWorkOrderDocId() );
            workOrder.setUnsignedDocumentId( woData.getWoApprovalMetaData().getUnsignedDocumentId() );
            workOrder.setVendorSignedDocId( woData.getWoApprovalMetaData().getVendorSignedDocumentId() );
            workOrder.setVendorApprovalLink( dao.getPageRequestLink(woData.getWorkOrderId(), PageRequestType.VENDOR_CONTRACT.name()) );
            workOrders.add(workOrder);
        }
        workOrders.sort(Comparator.comparing(WorkOrder::getWorkOrderId));
        return new ApiResponse(workOrders);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ApiResponse getItemsByWoId(Integer workOrderId) throws SumoException {
        WorkOrderData workOrderData = dataDao.findWoById(workOrderId);
        if( Objects.isNull(workOrderData) ) {
            throw new SumoException("Work Order Not Found for workOrderId : " + workOrderId);
        }

        Set<VendorContractItemData> vendorContractItemDataList = workOrderData.getVendorContractItemDataList();
        List<VendorContractItemDataVO> vendorContractItems = new ArrayList<>();
        for(VendorContractItemData itemData : vendorContractItemDataList) {
            VendorContractItemDataVO item = new VendorContractItemDataVO();

            item.setContractItemId(itemData.getContractItemId());
            IdCodeName sku = new IdCodeName(itemData.getSkuId(), null, scmCache.getSkuDefinition(itemData.getSkuId()).getSkuName());
            item.setSkuId(sku);

            item.setPackagingData( SCMDataConverter.convertToPackagingData(scmCache.getPackagingDefinition(itemData.getSkuPackagingId())) );

            item.setDispatchLocation( masterDataCache.getLocationbyId(itemData.getDispatchLocationId()).getName().toUpperCase() );
            item.setDeliveryLocation( masterDataCache.getLocationbyId(itemData.getDeliveryLocationId()).getName().toUpperCase() );

            item.setUpdatedPrice(itemData.getUpdatedPrice());
            item.setSkuPriceState( itemData.getSkuPriceState() != null ? itemData.getSkuPriceState().name() : null );
            vendorContractItems.add(item);
        }
        vendorContractItems.sort(Comparator.comparing(VendorContractItemDataVO::getContractItemId));
        return new ApiResponse(vendorContractItems);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelVendorContractV2(Integer workOrderId, Integer loggedInUser) throws SumoException {
        return dao.cancelVendorContractV2(WorkOrder.builder().workOrderId(workOrderId).workOrderStatus(VendorContractStatus.CANCELLED).build(), loggedInUser);
    }

    public List<VendorContractItemData> getByPassApprovedItems(Set<VendorContractItemData> itemDataSet) {
        List<VendorContractItemData> list = new ArrayList<>();
        itemDataSet.forEach(item -> {
            if(item.getStatus().equals(VendorContractStatus.APPROVED.name())) {
                list.add(item);
            }
        });
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail generateContractUsingTemplateV2(Integer workOrderId, Integer templateId) throws SumoException, TemplateRenderingException {
        Integer createdBy = SCMUtil.getLoggedInUser();
        IdCodeName templateDetail = scmConstants.getVendorContractTemplate().get(templateId);
        StringBuilder fileName = new StringBuilder(templateDetail.getCode() + "_" + workOrderId + ".pdf");
        WorkOrderData workOrderData = dataDao.findWoById(workOrderId);

        WorkOrderApprovalMetaDataDto woMetaData = new WorkOrderApprovalMetaDataDto();
        if (Objects.isNull(workOrderData)) {
            throw new SumoException("Work Order Not Found, for work-order Id : " + workOrderId);
        }
        VendorContractData vendorContract = workOrderData.getContractData();
        WorkOrderApprovalMetaData workOrderApprovalMetaData = workOrderData.getWoApprovalMetaData();

        if (Objects.nonNull(workOrderApprovalMetaData.getVendorSignId())) {
            DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class, workOrderApprovalMetaData.getVendorSignId());
            woMetaData.setVendorSignUrl( documentDetailData.getFileUrl() );
            woMetaData.setVendorIpAddress( workOrderApprovalMetaData.getVendorIpAddress() );
        } else if(workOrderData.getWorkOrderStatus().equals(VendorContractStatus.VENDOR_APPROVED) || workOrderData.getWorkOrderStatus().equals(VendorContractStatus.VENDOR_REJECTED)) {
            woMetaData.setVendorIpAddress( workOrderApprovalMetaData.getVendorIpAddress() );
            woMetaData.setVendorSignedDate( SCMUtil.parseDate(getALogFromVendorContractLogs( workOrderData.getWorkOrderId(), LogType.WORK_ORDER, VendorContractStatus.PENDING_VENDOR_APPROVAL.name(), workOrderData.getWorkOrderStatus().name() ).getUpdatedTime()) );
            if( Objects.isNull( woMetaData.getVendorSignedDate() ) ) {
                woMetaData.setVendorSignedDate( SCMUtil.parseDate(AppUtils.getCurrentTimestamp()) );
            }
        }

        if (Objects.nonNull(workOrderApprovalMetaData.getAuthSignId())) {
            DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class, workOrderApprovalMetaData.getAuthSignId());
            woMetaData.setApproverSignUrl( documentDetailData.getFileUrl() );
        } else if ( workOrderData.getWorkOrderStatus().equals(VendorContractStatus.APPROVER_BY_PASSED)
                || workOrderData.getWorkOrderStatus().equals(VendorContractStatus.PENDING_VENDOR_APPROVAL)
                || workOrderData.getWorkOrderStatus().equals(VendorContractStatus.VENDOR_APPROVED)
                || workOrderData.getWorkOrderStatus().equals(VendorContractStatus.VENDOR_REJECTED)
        ){
            String fromStatus = getFromStatus(workOrderData.getWorkOrderStatus());
            woMetaData.setApproverSignedDate( SCMUtil.parseDate(getALogFromVendorContractLogs( workOrderData.getWorkOrderId(), LogType.WORK_ORDER, fromStatus, workOrderData.getWorkOrderStatus().name() ).getUpdatedTime()) );
            if( Objects.isNull( woMetaData.getApproverSignedDate() ) ) {
                woMetaData.setApproverSignedDate( SCMUtil.parseDate(AppUtils.getCurrentTimestamp()) );
            }
        }
        woMetaData.setApproverIpAddress( workOrderApprovalMetaData.getAuthIpAddress() );
        String vendorContractString = vendorContract.getContractId().toString();
        List<VendorContractItemData> vendorContractItemDataList = getByPassApprovedItems(workOrderData.getVendorContractItemDataList());


        VendorContractTemplate vendorContractTemplate = new VendorContractTemplate( workOrderData, SCMDataConverter.convertToItems(vendorContractItemDataList, scmCache, masterDataCache),
                scmCache.getVendorDetail(vendorContract.getVendorId()),
                masterDataCache.getEmployeeBasicDetail( workOrderData.getApprovalRequestId() )
                ,props.getBasePath(), templateDetail.getCode(), woMetaData, vendorContractString, SCMConstants.getCompanyNameByCompanyId(null));

        String invoiceHTMLTemplate = vendorContractTemplate.getContent();
        String path = props.getBasePath() + "/vendor-contract/" + fileName;
        try (OutputStream outputStream = Files.newOutputStream(Paths.get(path))) {
            HtmlConverter.convertToPdf(invoiceHTMLTemplate, outputStream);
            outputStream.flush();
            File file1 = new File(path);
            FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getProdDocumentS3Bucket(), "VENDOR_CONTRACTS/" + workOrderId, fileName.toString(), file1, false);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(MimeType.PDF);
            documentDetail.setUploadType(DocUploadType.VENDOR_CONTRACT);
            documentDetail.setFileType(FileType.CONTRACT);
            documentDetail.setDocumentLink(workOrderId.toString());
            documentDetail.setS3Key(fileDetail.getKey());
            documentDetail.setFileUrl(props.getProdDocument() + "VENDOR_CONTRACTS/" + workOrderId + "/" + fileDetail.getKey().split("/")[2]);
            documentDetail.setS3Bucket(fileDetail.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(createdBy, "", masterDataCache.getEmployee(createdBy)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data1 = dao.add(SCMDataConverter.convert(documentDetail), true);
            return SCMDataConverter.convert(data1);
        } catch (Exception e) {
            LOG.error("Error when trying to create vendor contract :::::: {}", workOrderId, e);
            throw new SumoException("Error creating vendor contract");
        }
    }

    private String getFromStatus(VendorContractStatus workOrderStatus) {

        if(workOrderStatus.equals( VendorContractStatus.APPROVER_BY_PASSED ) || workOrderStatus.equals(VendorContractStatus.PENDING_VENDOR_APPROVAL)) {
            return VendorContractStatus.CREATED.name();
        } else if( workOrderStatus.equals(VendorContractStatus.VENDOR_REJECTED) || workOrderStatus.equals(VendorContractStatus.VENDOR_APPROVED) ) {
            return VendorContractStatus.PENDING_VENDOR_APPROVAL.name();
        }
        return null;
    }

    private VendorContractLogs getALogFromVendorContractLogs(Integer logTypeId, LogType logType, String fromState, String toState) {
        VendorContractLogs log = statusLogDao.findTop1ByLogTypeIdAndLogTypeAndFromStateAndToStateOrderByLogIdDesc(logTypeId, logType, fromState, toState);
        if( Objects.isNull(log) ) {
            log = new VendorContractLogs();
        }
        return log;
    }

    private String generateAuthKey() {
        try {
            return PasswordImpl.encryptUrlCodec(AppUtils.getCurrentTimeISTString());
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getContractDocument(Integer documentId) throws SumoException {
        DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class,documentId);
        if (Objects.isNull(documentDetailData)) {
            throw new SumoException("Document Not Found");
        }
        return documentDetailData.getFileUrl();
     }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean triggerVendorContractMailV2(Integer workOrderId, WorkOrderData woData) {
        Integer loggedInUser = RequestContext.getContext().getLoggedInUserId();
        if( Objects.isNull(woData) ) {
            woData = dataDao.findWoById(workOrderId);
        }
        VendorContractData vendorContract = woData.getContractData();
        WorkOrderApprovalMetaData woApprovalMetaData = woData.getWoApprovalMetaData();

        if (Objects.isNull(woApprovalMetaData.getUnsignedDocumentId())) {
            throw new SumoException("Contract Document Not Found");
        }
        dao.cancelAllContractMailRequest(woData.getWorkOrderId(), PageRequestType.VENDOR_CONTRACT.name());
        PageRequestDetail contractRequest = dao.add(PageRequestDetail.builder()
                .eventId(woData.getWorkOrderId())
                .eventType(PageRequestType.VENDOR_CONTRACT.name())
                .createdBy(masterDataCache.getEmployee(loggedInUser) + " [" + loggedInUser + "]")
                .recordStatus(VendorStatus.INITIATED.name())
                .registrationUrl(props.getVendorContractUrl())
                .requestDate(AppUtils.getBusinessDate())
                .authKey(generateAuthKey())
                .build(), true);

        if (Objects.isNull(contractRequest)) {
            throw new SumoException("Contract Event Creation Failed");
        }
        String link = SCMUtil.getRegistrationLink(contractRequest.getRegistrationUrl(), contractRequest.getAuthKey());
        DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class, woApprovalMetaData.getAuthSignedDocumentId());

        VendorDetail vendorDetail = scmCache.getVendorDetail(vendorContract.getVendorId());
        String templateName = woData.getWorkOrderType().equals(WorkOrderType.DEFAULT)
                ? "VendorContractEmailTemplate.html" : "WorkOrderEmailTemplate.html";

        VendorContractEmailNotificationTemplate emailTemplate = new VendorContractEmailNotificationTemplate(
                vendorDetail.getEntityName(),
                props.getBasePath(),
                link,
                templateName,
                contractRequest.getAuthKey(),
                SCMUtil.getWorkOrderApprovalApi(props.getScmBaseUrl())
        );

        String subject = vendorDetail.getEntityName().toUpperCase() +
                (woData.getWorkOrderType().equals(WorkOrderType.DEFAULT) ? " Vendor Contract " : " Work Order ") +
                "Proposal by STPL";

        VendorContractEmailNotification emailNotification = new VendorContractEmailNotification(
                emailTemplate,
                props.getEnvType(),
                new String[]{vendorDetail.getPrimaryEmail()},
                subject
        );

        List<AttachmentData> attachments = new ArrayList<>();
        String fileUrl = documentDetailData.getFileUrl();
        AttachmentData attachmentData = SCMUtil.getAttachmentDoc(fileUrl);
        if( Objects.isNull(attachmentData) ) {
            throw new SumoException("Failed to extract document to send to vendor");
        }
        attachments.add( attachmentData );
        emailNotification.sendRawMail(attachments);

        woApprovalMetaData.setMailTime(AppUtils.getCurrentTimestamp());
        dao.update(woData, true);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void applyContract() {
        dao.applyContractV2();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void expiryContract() {
        dao.expiryContractV2();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean vendorActionOnWOMail(String token, HttpServletRequest httpRequest, VendorContractStatus action) throws Exception {
        try {
            token = URLDecoder.decode(token, "UTF-8");
            PageRequestDetail request = null;
            request = dao.findPRDByToken(token);
            if (request == null || request.getRecordStatus().equals(VendorContractStatus.COMPLETED.name())) {
                LOG.info("Token not found in PageRequestDetail: {}", token);
                return null;
            }
            WorkOrderData woData = dataDao.findWoById(request.getEventId());
            if (woData == null) {
                LOG.error("Work Order Not Found for workOrderId: {}", request.getEventId());
                return false;
            }
            if (VendorContractStatus.VENDOR_REJECTED.equals(woData.getWorkOrderStatus()) ||
                    VendorContractStatus.CANCELLED.equals(woData.getWorkOrderStatus()) || VendorContractStatus.VENDOR_APPROVED.equals(woData.getWorkOrderStatus())) {
                LOG.error("Work Order Not Valid, Work Order has been rejected or cancelled or vendor already approved");
                return false;
            }
            try {
                VendorContractData vendorContract = woData.getContractData();
                WorkOrderApprovalMetaData woApprovalMetaData = woData.getWoApprovalMetaData();
                if (woApprovalMetaData == null || woApprovalMetaData.getAuthSignedDocumentId() == null) {
                    LOG.error("Work order Auth Signed Document Not Found for workOrderId: {}", woData.getWorkOrderId());
                    return false;
                }
                woApprovalMetaData.setVendorIpAddress( SCMUtil.getUserIpAddress(httpRequest) );
                if( VendorContractStatus.VENDOR_APPROVED.equals(action) ) {
                    dao.updateContractStatusLog(
                            woData.getWorkOrderStatus().name(),
                            action.name(),
                            -1,
                            woData.getWorkOrderId(),
                            LogType.WORK_ORDER,
                            "Vendor has taken action on work order by mail"
                    );
                    woData.setWorkOrderStatus(action);
                }
                woData = dao.update(woData, true);
                if (VendorContractStatus.VENDOR_REJECTED.equals(action)) {
                    return checkIsWoRejectByVendor(woData, vendorContract.getVendorId(), -1);
                }

                DocumentDetail documentDetail = generateContractUsingTemplateV2(woData.getWorkOrderId(), woApprovalMetaData.getTemplateId());
                woApprovalMetaData.setVendorSignedDocumentId(documentDetail.getDocumentId());
                validateTokenAndApplyWorkOrder(woData, token, vendorContract.getVendorId(), -1);
                return true;
            } catch (Exception e) {
                LOG.error("Error while processing vendor action logic for workOrderId: {}, error: {}", woData.getWorkOrderId(), e.getMessage(), e);
                throw new SumoException(e);
            }
        } catch (Exception e) {
            LOG.error("Error decoding token or fetching request: {}, error: {}", token, e.getMessage(), e);
            throw new SumoException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean vendorAcceptanceV2(WorkOrder workOrder, Integer loggedInUser, HttpServletRequest request) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException {
        try {
            WorkOrderData woData = dataDao.findWoById(workOrder.getWorkOrderId());
            if( Objects.isNull(woData) ) {
                throw new SumoException("Work order Not Found for workOrderId : " + workOrder.getWorkOrderId());
            }
            VendorContractData vendorContract = woData.getContractData();
            WorkOrderApprovalMetaData woApprovalMetaData = woData.getWoApprovalMetaData();
            if (Objects.isNull(woApprovalMetaData.getAuthSignedDocumentId())) {
                throw new SumoException("Work order Auth signed Document Not Found");
            }
            woApprovalMetaData.setVendorIpAddress( StringUtils.hasText(workOrder.getVendorIpAddress()) ? workOrder.getVendorIpAddress() : SCMUtil.getUserIpAddress(request) );
            woApprovalMetaData.setVendorSignId(workOrder.getVendorDigitalSignId());
            woApprovalMetaData.setVendorUserName(workOrder.getVendorName());
            woApprovalMetaData.setVendorDesignation(workOrder.getVendorDesignation());
            dao.updateContractStatusLog(woData.getWorkOrderStatus().name(), workOrder.getWorkOrderStatus().name(), loggedInUser, woData.getWorkOrderId(), LogType.WORK_ORDER, "Vendor has taken action on work order");
            if( woData.getWorkOrderType().equals(WorkOrderType.DEFAULT) ) {
                dao.updateContractStatusLog(vendorContract.getStatus(), workOrder.getWorkOrderStatus().name(), loggedInUser, vendorContract.getContractId(), LogType.CONTRACT, null);
                vendorContract.setStatus(workOrder.getWorkOrderStatus().name());
                vendorContract = dao.update(vendorContract, false);
            }
            if( VendorContractStatus.VENDOR_APPROVED.equals(workOrder.getWorkOrderStatus()) ) {
                woData.setWorkOrderStatus(workOrder.getWorkOrderStatus());
            }
            woData = dao.update(woData, true);
            if(workOrder.getWorkOrderStatus().equals(VendorContractStatus.VENDOR_REJECTED)) {
                return checkIsWoRejectByVendor(woData, vendorContract.getVendorId(), loggedInUser);
            }
            DocumentDetail documentDetail = generateContractUsingTemplateV2(woData.getWorkOrderId(), woApprovalMetaData.getTemplateId());
            woApprovalMetaData.setVendorSignedDocumentId(documentDetail.getDocumentId());
            woData = dao.update(woData, true);
            validateTokenAndApplyWorkOrder(woData, workOrder.getToken(), vendorContract.getVendorId(), loggedInUser);
            return true;
        } catch (Exception e) {
            LOG.error("Error while making vendor action from mail : ", e);
            throw new SumoException(e);
        }
    }

    private boolean checkIsWoRejectByVendor(WorkOrderData woData, Integer vendorId, int loggedInUserId) throws EmailGenerationException, SumoException, TemplateRenderingException {
        return dao.cancelVendorContractV2( WorkOrder.builder().workOrderId(woData.getWorkOrderId()).workOrderStatus(VendorContractStatus.VENDOR_REJECTED).build(), loggedInUserId);
    }

    private void validateTokenAndApplyWorkOrder(WorkOrderData woData, String token, Integer vendorId, Integer loggedInUser) throws VendorRegistrationException, EmailGenerationException {
        if(Objects.nonNull(token)) {
            PageRequestDetail pageRequestDetail = dao.findPRDByToken(token);
            if( pageRequestDetail.getRecordStatus().equalsIgnoreCase(VendorStatus.INITIATED.name()) ) {
                pageRequestDetail.setRecordStatus(VendorContractStatus.COMPLETED.name());
                dao.update(pageRequestDetail, true);
            }
        }
        if( AppUtils.getBusinessDate().compareTo(AppUtils.getDate(woData.getStartDate()))>=0 ) {
            dao.applyWorkOrder(woData, loggedInUser);
            return;
        }
        if(woData.getWorkOrderType().equals(WorkOrderType.DEFAULT)) {
            VendorContractData contract = woData.getContractData();
            dao.updateContractStatusLog(contract.getStatus(), woData.getWorkOrderStatus().name(), loggedInUser, contract.getContractId(), LogType.CONTRACT, "Vendor has taken action on CONTRACT");
            contract.setStatus(woData.getWorkOrderStatus().name());
            dao.update(contract, false);
        }
        EmployeeBasicDetail createdByDetails = masterDataCache.getEmployeeBasicDetail(woData.getCreatedBy());
        if( StringUtils.hasText(createdByDetails.getEmailId()) ) {
            new UserContractEmailNotification("Vendor Price VENDOR APPROVED", VendorContractStatus.VENDOR_APPROVED, woData.getWorkOrderId(), scmCache.getVendorDetail(vendorId).getEntityName(), true, masterDataCache.getEmployeeBasicDetail(woData.getApprovalRequestId()).getName(), createdByDetails.getEmailId(), props.getEnvType()).sendEmail();
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean triggerEmailOtpForVendor(Integer vendorId) throws SumoException {
        String contactNumber = scmCache.getVendorDetail(vendorId).getPrimaryContact();
        try {
            String otp = notificationService.getOTPMapperInstance().generateOTP(false, OtpType.VENDOR_CONTRACT, contactNumber, props.getEnvType());
            StringBuilder message = new StringBuilder(String.format("OTP for Vendor Contract Approval : %s\n" +
                    "For User Id : %s\n" +
                    "\n" +
                    "-SUNSHINE TEAHOUSE PVT LTD" , otp, AppConstants.SYSTEM_EMPLOYEE_ID));
//            StringBuilder message = new StringBuilder(String.format("OTP for Vendor Contract Approval : %s   For User Id : %s   -SUNSHINE TEAHOUSE PVT LTD" , otp, AppConstants.SYSTEM_EMPLOYEE_ID));
            VendorContractEmailOTPNotificationTemplate emailTemplate = new VendorContractEmailOTPNotificationTemplate(scmCache.getVendorDetail(vendorId), otp,props.getBasePath());
            VendorContractEmailOTPNotification emailNotification = new VendorContractEmailOTPNotification(emailTemplate,props.getEnvType(),scmCache.getVendorDetail(vendorId).getPrimaryEmail());
            emailNotification.sendEmail();
            return notificationService.sendNotification("VENDOR_CONTRACT", message.toString(), contactNumber,
                    providerService.getSMSClient(SMSType.OTP, ApplicationName.SCM_SERVICE), false, null);
        } catch (Exception e) {
            LOG.error("##### Error while sending otp ########## ", e);
            throw new SumoException("Email OTP Failed");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean triggerEmailOtpForEmployee(Integer employeeId) throws SumoException {
        String contactNumber = masterDataCache.getEmployeeBasicDetail(employeeId).getContactNumber();
        try {
            String otp = notificationService.getOTPMapperInstance().generateOTP(false,
                    OtpType.VENDOR_CONTRACT, contactNumber, props.getEnvType());
            StringBuilder message = new StringBuilder(String.format("Hi,\n" +
                    "OTP for Vendor Contract Approval : %s\n" +
                    "For User Id : %s\n" +
                    "\n" +
                    "-SUNSHINE TEAHOUSE PVT LTD" , otp, employeeId));
            return notificationService.sendNotification("VENDOR_CONTRACT", message.toString(), contactNumber,
                    providerService.getSMSClient(SMSType.OTP, ApplicationName.SCM_SERVICE), true, null);
        } catch (Exception e) {
            LOG.error("##### Error while sending otp ##########", e);
            throw new SumoException("Email OTP Failed");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean validateVendorOtp(VendorOTPValidationDomain otp) throws SumoException {
        StringBuilder contactNumber = new StringBuilder();
        if (Objects.nonNull(otp.getVendorId())){
            VendorDetail vendorDetail = scmCache.getVendorDetail(otp.getVendorId());
            contactNumber.append(vendorDetail.getPrimaryContact());
        } else if (Objects.nonNull(otp.getEmployeeId())) {
            EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(otp.getEmployeeId());
            contactNumber.append(employeeBasicDetail.getContactNumber());
        }
        if (!StringUtils.isEmpty(contactNumber.toString()) && !StringUtils.isEmpty(otp.getOtpValue())) {
            boolean result = otp.getOtpValue()
                    .equals(notificationService.getOTPMapperInstance().getOTP(OtpType.VENDOR_CONTRACT, contactNumber.toString()));
            if (result) {
                notificationService.getOTPMapperInstance().removeOTP(OtpType.VENDOR_CONTRACT, contactNumber.toString());
                return true;
            }
            throw new SumoException("VENDOR_CONTRACT_EXCEPTION","Otp Verification  Failed, Please enter valid opt.");
        } else {
            throw new SumoException("VENDOR_CONTRACT_EXCEPTION","Contact number or otp is empty !");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public WorkOrder validateRequestV2(String token) throws UnsupportedEncodingException, VendorRegistrationException, SumoException {
        token = URLDecoder.decode(token, "UTF-8");
        PageRequestDetail request = dao.findByToken(token);
        if (request != null) {
            WorkOrderData woData = dataDao.findWoById(request.getEventId());
            if (Objects.isNull(woData)) {
                throw new SumoException("WorkOrder Not Found");
            }
            if (VendorContractStatus.APPROVER_REJECTED.equals(woData.getWorkOrderStatus()) ||
                    VendorContractStatus.CANCELLED.equals(woData.getWorkOrderStatus())) {
                throw new SumoException("Work Order Not Valid, Work Order has been rejected or cancelled");
            }

            WorkOrder wo = new WorkOrder();
            wo.setWorkOrderId(woData.getWorkOrderId());
            Integer vendorId = woData.getContractData().getVendorId();
            wo.setVendorId( vendorId );
            wo.setVendorName( scmCache.getVendorDetail(vendorId).getEntityName() );
            wo.setUnsignedDocumentId( woData.getWoApprovalMetaData().getAuthSignedDocumentId() );
            return wo;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public Integer saveDigitalSignature(MultipartFile file, Integer woId) throws SumoException {
        String fileName = MultiPartFileHelper.getVendorSignatureUploadFileName(FileType.OTHERS, woId, MimeType.PNG);
        FileDetail s3File = null;

        s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "VENDOR_CONTRACTS", fileName, file);
        DocumentDetail documentDetail = new DocumentDetail();
        documentDetail.setUploadTypeId(woId);
        documentDetail.setMimeType(MimeType.PNG);
        documentDetail.setUploadType(DocUploadType.VENDOR_CONTRACT);
        documentDetail.setFileType(FileType.CONTRACT);
        documentDetail.setDocumentLink(fileName);
        documentDetail.setS3Key(s3File.getKey());
        documentDetail.setFileUrl(s3File.getUrl());
        documentDetail.setS3Bucket(s3File.getBucket());
        documentDetail
                .setUpdatedBy(SCMUtil.generateIdCodeName(SCMServiceConstants.SYSTEM_USER, "SYSTEM", "SYSTEM"));
        documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
        DocumentDetailData data = dao.add(SCMDataConverter.convert(documentDetail), true);
        return data.getDocumentId();

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public VendorContractSoInfo validateSoContractRequest(String token) throws UnsupportedEncodingException, VendorRegistrationException {
        token = URLDecoder.decode(token, "UTF-8");
        PageRequestDetail request = dao.findByToken(token);
        if (request != null) {
            return dao.find(VendorContractSoInfo.class,request.getEventId());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM",propagation = Propagation.REQUIRED)
    public Map<Integer,List<SkuDefinition>> getSkuWrtCategory() {
        return scmCache.getSkuDefinitions().values().stream()
                .filter(skuDefinition -> SwitchStatus.ACTIVE.equals(skuDefinition.getSkuStatus()))
                .collect(Collectors.groupingBy(
                        skuDefinition -> scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId()).getCategoryDefinition().getId(),
                        Collectors.mapping(skuDefinition -> skuDefinition, Collectors.toList())
                ));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public WorkOrder getSkuPrices(Integer vendorId, Integer userId) throws SumoException {
        try {
            List<VendorContractItemDataVO> itemDataVOS = new ArrayList<>();
            WorkOrder workOrder = new WorkOrder();

            VendorContractData vendorContractData = dataDao.getByVendorIdAndStatusNotIn(vendorId, VendorContractStatus.getRejectionStatuses());
            WorkOrderData woData = null;

            if(Objects.nonNull(vendorContractData) && !CollectionUtils.isEmpty(vendorContractData.getWorkOrderDataSet())) {
                woData = SCMUtil.getRecentWorkOrderByContract( vendorContractData.getWorkOrderDataSet() );
            }
            if ( Objects.nonNull(woData) &&  !VendorContractStatus.getRejectionStatuses().contains(woData.getWorkOrderStatus().name()) && !VendorContractStatus.APPLIED.equals(woData.getWorkOrderStatus())) {
                if(!woData.getCreatedBy().equals(userId)) {
                    throw new SumoException("Already a PRICE REQUEST is in " + woData.getWorkOrderStatus().name() + " status! with Vendor : <b>" + scmCache.getVendorDetail(vendorId).getEntityName() + "</b>",
                            "<b>Price Request Id : " + woData.getWorkOrderId() + "</b><b> <br>Price Requested By : " + masterDataCache.getEmployee(woData.getCreatedBy()) + "</b>");
                }
                workOrder = SCMDataConverter.convertToDto(woData);

                itemDataVOS = new ArrayList<>();
                if (!CollectionUtils.isEmpty(woData.getVendorContractItemDataList())) {
                    for(VendorContractItemData dataItem : woData.getVendorContractItemDataList()) {
                        List<VendorContractItemDataVO> items = SCMDataConverter.convertToItems(List.of(dataItem), scmCache, masterDataCache);
                        List<Integer> unitIds = dataItem.getVendorContractItemUnitDataList()
                                .stream().filter(unit -> VendorContractStatus.ACTIVE.name().equals(unit.getStatus()))
                                .map(VendorContractItemUnitData::getUnitId)
                                .collect(Collectors.toList());
                        items.get(0).setUnitIds(unitIds);
                        itemDataVOS.addAll(items);
                    }
                    itemDataVOS = getIsStatusActiveFromSkuPriceData(itemDataVOS, vendorId);
                }
            } else {
                List<VendorContractStatus> rejectionStatus = List.of(VendorContractStatus.CANCELLED, VendorContractStatus.VENDOR_REJECTED, VendorContractStatus.APPLIED, VendorContractStatus.REMOVED_ALL,
                                                                        VendorContractStatus.APPROVER_REJECTED, VendorContractStatus.EXPIRED, VendorContractStatus.DEACTIVATED);
                if(Objects.nonNull(woData) && rejectionStatus.contains(woData.getWorkOrderStatus())) {
                    workOrder.setWorkOrderStatus(woData.getWorkOrderStatus());
                    workOrder.setContractId(woData.getContractData().getContractId());
                    workOrder.setApprovalRequestId( Integer.parseInt(woData.getContractData().getApprovalRequestFrom()) );
                    workOrder.setStartDate( woData.getContractData().getStartDate() );
                    workOrder.setEndDate( woData.getContractData().getEndDate() );
                }
                List<SkuPriceDetail> skuPriceDetailList = dao.searchPricesByVendorAndStatus(vendorId, VendorContractStatus.ACTIVE.name());
                if (Objects.nonNull(skuPriceDetailList)) {
                    List<VendorContractItemDataVO> skus = convert(skuPriceDetailList, vendorId);
                    itemDataVOS.addAll(skus);
                }
            }

            workOrder.getVendorContractItemDataVOS().addAll(itemDataVOS);
            return workOrder;
        } catch (SumoException sumoExp) {
            throw sumoExp;
        } catch (Exception exp) {
            LOG.error("Error in getSkuPrices --> ", exp);
            throw new SumoException("Exception Occurred While getting sku prices", exp.getMessage());
        }
    }

    private List<VendorContractItemDataVO> getIsStatusActiveFromSkuPriceData(List<VendorContractItemDataVO> itemDataVOs, Integer vendorId) {
        Map<String, VendorContractItemDataVO> contractItemMap = new HashMap<>();
        Set<Integer> skuIds = new HashSet<>();
        Set<Integer> skuPackagingIds = new HashSet<>();
        Set<Integer> dispatchLocationIds = new HashSet<>();
        Set<Integer> deliveryLocationIds = new HashSet<>();

        for(VendorContractItemDataVO item : itemDataVOs) {
            String KEY = SCMUtil.generateUniqueKey(item.getSkuId().getId().toString(), item.getSkuPackagingId().getId().toString(), item.getDispatchLocationId().toString(), item.getDeliveryLocationId().toString());
            if(!contractItemMap.containsKey(KEY)) {
                contractItemMap.put(KEY, item);
            }
            skuIds.add(item.getSkuId().getId());
            skuPackagingIds.add(item.getSkuPackagingId().getId());
            dispatchLocationIds.add(item.getDispatchLocationId());
            deliveryLocationIds.add(item.getDeliveryLocationId());
        }

        List<SkuPriceData> skuPriceDataList = skuPriceDataDao.findByVendorIdAndSkuIdInAndPackagingIdInAndDeliveryLocationIdInAndDispatchLocationIdIn(vendorId, skuIds, skuPackagingIds, deliveryLocationIds, dispatchLocationIds);

        for(SkuPriceData priceData : skuPriceDataList) {
            String KEY = SCMUtil.generateUniqueKey(String.valueOf(priceData.getSkuId()), String.valueOf(priceData.getPackagingId()), priceData.getDispatchLocationId().toString(), priceData.getDeliveryLocationId().toString());
            if(!contractItemMap.containsKey(KEY) || !priceData.getStatus().equals(VendorContractStatus.ACTIVE.name())) {
                continue;
            }
            contractItemMap.get(KEY).setIsStatusActive(AppConstants.YES);
        }
        return new ArrayList<>(contractItemMap.values());
    }

    private List<VendorContractItemDataVO> convert(List<SkuPriceDetail> skuPriceDetailList, Integer vendorId) {
        List<VendorContractItemDataVO> itemDataVOS = new ArrayList<>();

        List<String> compositeKeys = skuPriceDetailList.stream()
                .map(item -> SCMUtil.generateUniqueKey(
                        item.getSku().getId().toString(),
                        item.getPkg().getId().toString(),
                        item.getDispatch().getId().toString(),
                        item.getDelivery().getId().toString()))
                .collect(Collectors.toList());

        List<VendorContractItemData> itemDataList = itemDataDao.findItemsByCompositeKeys(compositeKeys);

        Map<String, VendorContractItemData> contractItemDataMap = itemDataList.stream()
                .collect(Collectors.toMap(
                        item -> SCMUtil.generateUniqueKey(item.getSkuId().toString(), item.getSkuPackagingId().toString(), item.getDispatchLocationId().toString(), item.getDeliveryLocationId().toString()),
                        item -> item,
                        (existingEntry, duplicateEntry) -> existingEntry
                ));

        for (SkuPriceDetail skuPriceDetail : skuPriceDetailList) {
            // can also check by skuPriceDataId...
            String KEY = SCMUtil.generateUniqueKey(skuPriceDetail.getSku().getId().toString(), skuPriceDetail.getPkg().getId().toString(), skuPriceDetail.getDispatch().getId().toString(), skuPriceDetail.getDelivery().getId().toString());
            VendorContractItemData itemData = contractItemDataMap.get(KEY);
            VendorContractItemDataVO itemDataVO = new VendorContractItemDataVO();
            if(Objects.nonNull(itemData)) {
                List<Integer> unitList = itemData.getVendorContractItemUnitDataList().stream()
                                .filter(unit -> unit.getStatus().equals(VendorContractStatus.ACTIVE.name()))
                                .map(VendorContractItemUnitData::getUnitId)
                                .toList();
                itemDataVO.setUnitIds(unitList);
            }

            itemDataVO.setKeyId(skuPriceDetail.getKeyId());
            itemDataVO.setSkuId(skuPriceDetail.getSku());
            itemDataVO.setSkuPackagingId(skuPriceDetail.getPkg());
            itemDataVO.setStatus(skuPriceDetail.getStatus());
            itemDataVO.setDispatchLocation(skuPriceDetail.getDispatch().getName().toUpperCase());
            itemDataVO.setDispatchLocationId(skuPriceDetail.getDispatch().getId());
            itemDataVO.setDeliveryLocation(skuPriceDetail.getDelivery().getName().toUpperCase());
            itemDataVO.setDeliveryLocationId(skuPriceDetail.getDelivery().getId());
            itemDataVO.setCurrentPrice(skuPriceDetail.getCurrent().getValue());
            itemDataVO.setUpdatedPrice(skuPriceDetail.getUpdated().getValue());
            itemDataVO.setIsStatusActive(AppConstants.YES);
            itemDataVOS.add(itemDataVO);
        }
        return itemDataVOS;
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public WorkOrderData initiateVendorContract(WorkOrder workOrder, Integer loggedInUser, WorkOrderData workOrderData) throws SumoException {
        try {
            VendorContractData data = new VendorContractData();
            data.setVendorId(workOrder.getVendorId());
            data.setCreatedBy(loggedInUser);
            data.setApprovalRequestFrom(workOrder.getApprovalRequestId().toString());
            data.setCreatedAt(AppUtils.getCurrentTimestamp());
            data.setStatus(VendorContractStatus.CREATED.name());
            data.setDocumentId(workOrder.getWorkOrderDocId());
            data.setStartDate(AppUtils.getDate(workOrder.getStartDate()));
            data.setEndDate(AppUtils.getDate(workOrder.getEndDate()));
            data = dataDao.update(data, false);

            workOrderData = convertToWorkOrderData(workOrder, data, VendorContractStatus.CREATED, WorkOrderType.DEFAULT, loggedInUser);
            return dataDao.add(workOrderData, false);
        } catch (Exception e) {
            LOG.error("Unable to initiate the contract of vendorId {}", workOrder.getVendorId(), e);
            throw new SumoException(e.getMessage());
        }
    }

    private WorkOrderData convertToWorkOrderData(WorkOrder workOrder, VendorContractData data, VendorContractStatus status, WorkOrderType woType, Integer loggedInUser) {
        WorkOrderData workOrderData = new WorkOrderData();
        workOrderData.setContractData(data);
        workOrderData.setWorkOrderStatus(status);
        int size = data.getWorkOrderDataSet() != null ? data.getWorkOrderDataSet().size() : 0;
        workOrderData.setGeneratedWorkOrderNumber(data.getContractId() + "_" + size);
        workOrderData.setWorkOrderType(woType);
        workOrderData.setStartDate(workOrder.getStartDate());
        workOrderData.setEndDate(data.getEndDate());
        workOrderData.setApprovalRequestId(workOrder.getApprovalRequestId());
        workOrderData.setCreatedBy( loggedInUser );
        workOrderData.setCreatedAt(AppUtils.getCurrentTimestamp());

        WorkOrderApprovalMetaData woApprovalDetailData = workOrderData.getWoApprovalMetaData();
        if (woApprovalDetailData == null) {
            woApprovalDetailData = new WorkOrderApprovalMetaData();
            woApprovalDetailData.setWorkOrderData(workOrderData);
        }
        woApprovalDetailData.setWorkOrderDocId(data.getDocumentId());
        workOrderData.setWoApprovalMetaData(woApprovalDetailData);

        return workOrderData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean saveVendorContract(WorkOrder workOrder, Integer loggedInUser) throws SumoException {
        Set<VendorContractItemDataVO> vendorContractItemDataVO = workOrder.getVendorContractItemDataVOS();
        Integer vendorId = workOrder.getVendorId();
        try {
            VendorContractData data = null;
            WorkOrderData workOrderData = null;
            VendorContractStatus oldStatus = null;

            if(Objects.nonNull(workOrder.getWorkOrderId())) {
                // update existing WO...
                workOrderData = dataDao.findWoById(workOrder.getWorkOrderId());
                if(Objects.isNull(workOrderData)) {
                    throw new SumoException("Unable to find the Work Order for ID : " + workOrder.getWorkOrderId());
                }
                if( workOrderData.getWorkOrderStatus().equals(VendorContractStatus.CREATED) ) {
                    throw new SumoException("Unable to update Work Order : " + workOrder.getWorkOrderId() + ". Already in " + workOrderData.getWorkOrderStatus().name() + " status");
                }
                data = workOrderData.getContractData();
                oldStatus = VendorContractStatus.PARTIAL_REJECTED;
            } else if(Objects.nonNull(workOrder.getContractId())) {
                // create new WO...
                data = dataDao.findById(workOrder.getContractId());
                workOrderData = dataDao.findWoByContractId(data.getContractId());
                List<VendorContractStatus> statusList = List.of(VendorContractStatus.CREATED);
                if( Objects.nonNull(workOrderData) && statusList.contains(workOrderData.getWorkOrderStatus()) ) {
                    throw new SumoException("Unable to create Work Order for Contract ID : " + workOrder.getContractId() + ". Already in " + workOrderData.getWorkOrderStatus().name() + " status");
                }
                workOrderData = convertToWorkOrderData(workOrder, data, VendorContractStatus.CREATED, WorkOrderType.PRICE_CHANGE, loggedInUser);
                dataDao.add(workOrderData, true);
            } else {
                // create new Contract and DEFAULT_WORK_ORDER...
                workOrderData = initiateVendorContract(workOrder, loggedInUser, workOrderData);
                data = workOrderData.getContractData();
            }

            // to save all details at last...
            List<VendorContractItemData> vendorContractItemDataList = new ArrayList<>();
            List<VendorContractItemUnitData> contractItemUnitDataList = new ArrayList<>();

            for (VendorContractItemDataVO itemDataVO : vendorContractItemDataVO) {
                VendorContractItemData itemData = null;

                if (Objects.isNull(itemDataVO.getContractItemId())) {
                    itemData = new VendorContractItemData();
                    itemData.setWorkOrderData(workOrderData);
                    itemData.setSkuId(itemDataVO.getSkuId().getId());
                    itemData.setDeliveryLocationId(itemDataVO.getDeliveryLocationId());
                    itemData.setDispatchLocationId(itemDataVO.getDispatchLocationId());
                    itemData.setSkuPackagingId(itemDataVO.getSkuPackagingId().getId());
                    itemData.setCurrentPrice( itemDataVO.getCurrentPrice() );
                } else {
                    itemData = workOrderData.getVendorContractItemDataList().stream()
                                    .filter(item -> Objects.equals(item.getContractItemId(), itemDataVO.getContractItemId()))
                                    .findFirst()
                                    .orElse(null);
                    if(Objects.isNull(itemData)) {
                        continue;
                    }
                }
                if(itemData.getUpdatedPrice() != null && itemDataVO.getUpdatedPrice() != null &&  itemData.getUpdatedPrice().compareTo(itemDataVO.getUpdatedPrice()) != 0 ) {
                    dao.updateContractStatusLog(itemData.getUpdatedPrice().toString(), itemDataVO.getUpdatedPrice().toString(), loggedInUser, itemData.getContractItemId(), LogType.CONTRACT_ITEM_PRICE_CHANGE, "Item price has been changed");
                }
                itemData.setUpdatedPrice(itemDataVO.getUpdatedPrice());
                if( itemData.getStatus() != null ) {
                    dao.updateContractStatusLog(itemData.getStatus(), itemDataVO.getStatus(), loggedInUser, itemData.getContractItemId(), LogType.CONTRACT_ITEM_STATUS, "Item Status has been changed");
                }
                itemData.setStatus(itemDataVO.getStatus());
                vendorContractItemDataList.add(itemData);

                addUnitsByVendorContractItem(itemData.getVendorContractItemUnitDataList(), itemDataVO.getUnitIds(), itemData, contractItemUnitDataList);
            }

            if(!CollectionUtils.isEmpty(vendorContractItemDataList)) {
                itemDataDao.saveAll(vendorContractItemDataList);
            }
            if(!CollectionUtils.isEmpty(contractItemUnitDataList)) {
                unitDataDao.saveAll(contractItemUnitDataList);
            }

            checkAndUpdateTheStatusOfTheContract(workOrderData, loggedInUser);

            if( !workOrderData.getWorkOrderStatus().equals(VendorContractStatus.REMOVED_ALL) ) {
                String fromStatus = oldStatus == null ? null : oldStatus.name();
                if(workOrderData.getWorkOrderType().equals(WorkOrderType.DEFAULT)) {
                    data.setStatus(VendorContractStatus.CREATED.name());
                    dao.updateContractStatusLog(fromStatus, VendorContractStatus.CREATED.name(), loggedInUser, data.getContractId(), LogType.CONTRACT, null);
                }
                dao.updateContractStatusLog(fromStatus, VendorContractStatus.CREATED.name(), loggedInUser, workOrderData.getWorkOrderId(), LogType.WORK_ORDER, null);
                workOrderData.setWorkOrderStatus(VendorContractStatus.CREATED);
            }

            dataDao.update(data, false);
            dataDao.add(workOrderData, true);

            if(!workOrderData.getWorkOrderStatus().equals(VendorContractStatus.REMOVED_ALL)) {
                EmployeeBasicDetail createdByDetails = masterDataCache.getEmployeeBasicDetail(data.getCreatedBy());
                EmployeeBasicDetail approverDetails = masterDataCache.getEmployeeBasicDetail(Integer.valueOf(data.getApprovalRequestFrom()));
                if(!StringUtils.isEmpty(createdByDetails.getEmailId())) {
                    new UserContractEmailNotification("Vendor Price SKU's Updated.", VendorContractStatus.CREATED, data.getContractId(), scmCache.getVendorDetail(data.getVendorId()).getEntityName(), true, approverDetails.getName(), createdByDetails.getEmailId(), props.getEnvType()).sendEmail();
                }
                if(!StringUtils.isEmpty(approverDetails.getEmailId())) {
                    new UserContractEmailNotification("Vendor Price SKU's Updated.", VendorContractStatus.CREATED, data.getContractId(), scmCache.getVendorDetail(data.getVendorId()).getEntityName(), false, createdByDetails.getName(), approverDetails.getEmailId(), props.getEnvType()).sendEmail();
                }
            }
            return true;
        } catch (Exception e) {
            LOG.error("Unable to save data for vendorId ", e);
            throw new SumoException("Error while saving data for vendorId : " + vendorId);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public void addUnitsByVendorContractItem(Set<VendorContractItemUnitData> itemUnitsList, List<Integer> unitIdList, VendorContractItemData itemData, List<VendorContractItemUnitData> contractItemUnitDataList) {
        if (Objects.isNull(itemUnitsList)) {
            itemUnitsList = new HashSet<>();
        }
        if (Objects.isNull(unitIdList)) {
            unitIdList = new ArrayList<>();
        }
        // Check for the unitItems present in the table make required changes.
        for (VendorContractItemUnitData itemUnit : itemUnitsList) {
            Integer unitId = itemUnit.getUnitId();
            if (unitIdList.contains(unitId)) {
                itemUnit.setStatus(VendorContractStatus.ACTIVE.name());
                unitIdList.remove(unitId);
            } else {
                itemUnit.setStatus(VendorContractStatus.IN_ACTIVE.name());
            }
        }

        // create new entries which are not present in the table.
        for (Integer i : unitIdList) {
            VendorContractItemUnitData unitItemData = new VendorContractItemUnitData();
            unitItemData.setContractItem(itemData);
            unitItemData.setUnitId(i);
            unitItemData.setStatus(VendorContractStatus.ACTIVE.name());
            itemUnitsList.add(unitItemData);
        }
        contractItemUnitDataList.addAll(itemUnitsList);
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public void checkAndUpdateTheStatusOfTheContract(WorkOrderData workOrderData, Integer loggedInUser) {
        List<Object[]> values = itemDataDao.getTotalRemovedOfContractId(workOrderData.getWorkOrderId(), VendorContractStatus.REMOVED.name());
        long sizeOfAllItemData = (long) values.get(0)[0];
        long sizeOfRemovedData = values.get(0)[1] != null ? ((Number) values.get(0)[1]).longValue() : 0L;

        if (sizeOfAllItemData == sizeOfRemovedData && sizeOfAllItemData > 0) {
            dao.updateContractStatusLog(workOrderData.getWorkOrderStatus().name(), VendorContractStatus.REMOVED_ALL.name(), loggedInUser, workOrderData.getWorkOrderId(), LogType.WORK_ORDER, "user removed all items, so work order set to this status");
            workOrderData.setWorkOrderStatus(VendorContractStatus.REMOVED_ALL);
            if(WorkOrderType.DEFAULT.equals(workOrderData.getWorkOrderType())) {
                dao.updateContractStatusLog(workOrderData.getContractData().getStatus(), VendorContractStatus.REMOVED_ALL.name(), loggedInUser, workOrderData.getContractData().getContractId(), LogType.CONTRACT, "user removed all items, so contract set to this status");
                workOrderData.getContractData().setStatus(VendorContractStatus.REMOVED_ALL.name());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public List<WorkOrder> getWorkOrders(Integer userId) {
        List<WorkOrderData> dataList = dataDao.getAllInStatusAndApprover(VendorContractStatus.CREATED, userId);
        List<WorkOrder> resultList = new ArrayList<>();
        for(WorkOrderData data : dataList) {
            WorkOrder workOrder = new WorkOrder();
            workOrder.setVendorId(data.getContractData().getVendorId());
            workOrder.setWorkOrderId(data.getWorkOrderId());
            workOrder.setContractId(data.getContractData().getContractId());

            workOrder.setStartDate(data.getStartDate());
            workOrder.setEndDate(data.getEndDate());

            workOrder.setCreatedAt(data.getCreatedAt());
            workOrder.setCreatedByName(masterDataCache.getEmployee(data.getCreatedBy()) + " [" + data.getCreatedBy() + "]");
            workOrder.setVendorName(scmCache.getVendorDetail(workOrder.getVendorId()).getEntityName() + " [" + workOrder.getVendorId() + "]" );
            workOrder.setWorkOrderType(data.getWorkOrderType());
            resultList.add(workOrder);
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public WorkOrder findSkuPriceMapping(Integer woId, String status) throws SumoException {
        WorkOrder workOrder = new WorkOrder();
        WorkOrderData woData = dataDao.findWoById(woId);

        if (Objects.nonNull(woData) && !CollectionUtils.isEmpty(woData.getVendorContractItemDataList())) {
            workOrder.setWorkOrderStatus(woData.getWorkOrderStatus());
            workOrder.setWorkOrderId(woData.getWorkOrderId());
            workOrder.setStartDate(woData.getStartDate());
            workOrder.setEndDate(woData.getEndDate());
            workOrder.setWorkOrderType(woData.getWorkOrderType());
            workOrder.setWorkOrderDocId(woData.getWoApprovalMetaData().getWorkOrderDocId());

            VendorContractData vendorContractData = woData.getContractData();
            workOrder.setVendorName( scmCache.getVendorDetail(vendorContractData.getVendorId()).getEntityName() + " [" + vendorContractData.getVendorId() + "]" );
            workOrder.setVendorId(vendorContractData.getVendorId());
            workOrder.setContractId(vendorContractData.getContractId());
            workOrder.setIsByPassed( SCMUtil.getFirstWorkOrderByContract(vendorContractData.getWorkOrderDataSet()).getIsByPassed() );

            for (VendorContractItemData itemData : woData.getVendorContractItemDataList()) {
                if( !itemData.getStatus().equals(status) ) {
                    continue;
                }
                VendorContractItemDataVO itemDataVO = new VendorContractItemDataVO();

                IdCodeName sku = new IdCodeName();
                sku.setId(itemData.getSkuId());
                sku.setCode(null);
                sku.setName(scmCache.getSkuDefinition(itemData.getSkuId()).getSkuName());
                itemDataVO.setSkuId(sku);

                IdCodeName skuPackaging = new IdCodeName();
                PackagingDefinition packagingDefinition = scmCache.getPackagingDefinition(itemData.getSkuPackagingId());
                skuPackaging.setId(packagingDefinition.getPackagingId());
                skuPackaging.setCode(packagingDefinition.getPackagingCode());
                skuPackaging.setName(packagingDefinition.getPackagingName());
                itemDataVO.setSkuPackagingId(skuPackaging);

                itemDataVO.setContractItemId(itemData.getContractItemId());
                itemDataVO.setCurrentPrice(itemData.getCurrentPrice());
                itemDataVO.setStatus(itemData.getStatus());
                itemDataVO.setUpdatedPrice(itemData.getUpdatedPrice());
                itemDataVO.setCurrentPrice(itemData.getCurrentPrice());

                Location dispatchLocation = masterDataCache.getLocationbyId(itemData.getDispatchLocationId());
                Location deliveryLocation = masterDataCache.getLocationbyId(itemData.getDeliveryLocationId());
                itemDataVO.setDeliveryLocation(deliveryLocation.getName().toUpperCase());
                itemDataVO.setDispatchLocation(dispatchLocation.getName().toUpperCase());
                itemDataVO.setDeliveryLocationId(itemData.getDeliveryLocationId());
                itemDataVO.setDispatchLocationId(itemData.getDispatchLocationId());

                itemDataVO.setRejectionReason(itemData.getRejectionReason());
                itemDataVO.setSelectedRejectionReason(itemData.getSelectedRejectionReason());
                List<Integer> unitIds = itemData.getVendorContractItemUnitDataList()
                        .stream().filter(unit -> VendorContractStatus.ACTIVE.name().equals(unit.getStatus()))
                        .map(VendorContractItemUnitData::getUnitId)
                        .collect(Collectors.toList());
                itemDataVO.setUnitIds(unitIds);
                workOrder.getVendorContractItemDataVOS().add(itemDataVO);
            }
            if(CollectionUtils.isEmpty(workOrder.getVendorContractItemDataVOS())) {
                if( !VendorContractStatus.CREATED.equals(workOrder.getWorkOrderStatus()) ) {
                    LOG.info("No mappings found for Work Order Id:{}", woId);
                    throw new SumoException("No mappings found for WorkOrder ID: " + woId);
                }
                // wo is in created but no items found in created status,
                // that says all items are in approved status.
            }
        }
        return workOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean submitPriceApprovals(WorkOrder workOrder, Integer loggedInUser, HttpServletRequest request) throws SumoException {
        try {
            Set<VendorContractItemDataVO> itemDataVOS = workOrder.getVendorContractItemDataVOS();
            WorkOrderData woData = dataDao.findWoById(workOrder.getWorkOrderId());
            if( Objects.isNull(woData) ) {
                throw new SumoException("Unable to find the Work Order for ID : " + workOrder.getWorkOrderId());
            }
            VendorContractData vendorContractData = woData.getContractData();

            WorkOrderData existingWorkOrderData = SCMUtil.getFirstWorkOrderByContract(vendorContractData.getWorkOrderDataSet());
            if( Objects.nonNull(workOrder.getIsByPassed()) && Objects.nonNull(existingWorkOrderData.getIsByPassed()) && !workOrder.getIsByPassed().equalsIgnoreCase(existingWorkOrderData.getIsByPassed()) ) {
                String msg = null;
                if(existingWorkOrderData.getIsByPassed().equalsIgnoreCase(AppConstants.NO)) {
                    msg = "Parent contract is not bypassed. Please check and retry again.";
                } else {
                    msg = "Parent contract is bypassed. Please check and retry again.";
                }
                throw new SumoException(msg);
            }

            Set<VendorContractItemData> itemDataList = woData.getVendorContractItemDataList();
            Map<Integer, VendorContractItemData> vendorContractItemDataMap = itemDataList.stream()
                    .collect(Collectors.toMap(
                            VendorContractItemData::getContractItemId,
                            item -> item
                    ));

            for (VendorContractItemDataVO itemDataVO : itemDataVOS) {
                VendorContractItemData data = vendorContractItemDataMap.get(itemDataVO.getContractItemId());
                if(data.getStatus().equals(VendorContractStatus.APPROVED.name()) || data.getStatus().equals(VendorContractStatus.REJECTED.name())) {
                    continue;
                }
                if(data.getSelectedRejectionReason() != null && itemDataVO.getSelectedRejectionReason() != null &&  !data.getSelectedRejectionReason().equalsIgnoreCase(itemDataVO.getSelectedRejectionReason())) {
                    dao.updateContractStatusLog(data.getStatus(), itemDataVO.getStatus(), loggedInUser, data.getContractItemId(), LogType.CONTRACT_ITEM_STATUS, "Item Status has been changed");
                    dao.updateContractStatusLog(data.getSelectedRejectionReason(), itemDataVO.getSelectedRejectionReason(), loggedInUser, data.getContractItemId(), LogType.CONTRACT_ITEM_REJECTION_REASON, "Selected Rejection reason has been changed");
                }
                data.setStatus(itemDataVO.getStatus());
                data.setSelectedRejectionReason(itemDataVO.getSelectedRejectionReason());
                if(data.getRejectionReason() != null && itemDataVO.getRejectionReason() != null &&  !data.getRejectionReason().equalsIgnoreCase(itemDataVO.getRejectionReason())) {
                    dao.updateContractStatusLog(data.getRejectionReason(), itemDataVO.getRejectionReason(), loggedInUser, data.getContractItemId(), LogType.CONTRACT_ITEM_REJECTION_REASON, "Rejection reason has been changed");
                }
                data.setRejectionReason(itemDataVO.getRejectionReason());
            }

            itemDataDao.saveAll(vendorContractItemDataMap.values());

            sendMailAndCheckByPass(woData, workOrder, request);
            return true;
        } catch (Exception e) {
            LOG.error("Unable to save price approvals ", e);
            throw new SumoException("Unable to save price approvals", e.getMessage());
        }
    }

    private void sendMailAndCheckByPass(WorkOrderData woData, WorkOrder workOrder, HttpServletRequest request) throws Exception {
        Integer loggedInUser = RequestContext.getContext().getLoggedInUserId();
        VendorContractData contractData = woData.getContractData();
        if( Objects.isNull(contractData) ) {
            throw new SumoException("Unable to find the contract for ID : " + workOrder.getWorkOrderId());
        }

        EmployeeBasicDetail createdByDetails = masterDataCache.getEmployeeBasicDetail(woData.getCreatedBy());
        if(Objects.nonNull(workOrder.getIsByPassed())) {
            woData.setIsByPassed(workOrder.getIsByPassed());
            VendorContractStatus oldStatus = woData.getWorkOrderStatus();
            VendorContractStatus emailStatus = null;
            if(AppConstants.YES.equals(workOrder.getIsByPassed())) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(9999, Calendar.DECEMBER, 31);
                woData.setEndDate(calendar.getTime());
                woData.setWorkOrderStatus(VendorContractStatus.APPROVER_BY_PASSED);
                emailStatus = VendorContractStatus.APPROVER_BY_PASSED;
            } else {
                woData.setWorkOrderStatus(VendorContractStatus.PENDING_VENDOR_APPROVAL);
                emailStatus = VendorContractStatus.PENDING_VENDOR_APPROVAL;
            }

            // make change in contract, if required by checking type
            WorkOrderApprovalMetaData woApprovalMetaData = woData.getWoApprovalMetaData();
            woApprovalMetaData.setAuthIpAddress( SCMUtil.getUserIpAddress(request) );
            if( WorkOrderType.DEFAULT.equals(woData.getWorkOrderType()) ) {
                woApprovalMetaData.setAuthSignId( workOrder.getAuthDigitalSignId() );
                dao.updateContractStatusLog(contractData.getStatus(), woData.getWorkOrderStatus().name(), loggedInUser, contractData.getContractId(), LogType.CONTRACT, "Contract status changed");
                contractData.setEndDate(woData.getEndDate());
                contractData.setStatus(woData.getWorkOrderStatus().name());
                dataDao.update(contractData, false);
            }

            dao.updateContractStatusLog(oldStatus.name(), woData.getWorkOrderStatus().name(), loggedInUser, woData.getWorkOrderId(), LogType.WORK_ORDER, null);
            dataDao.update(woData, false);
            saveVendorPriceChangeV2(woData, loggedInUser);
            try {
                if(StringUtils.hasText(createdByDetails.getEmailId())) {
                    new UserContractEmailNotification("Vendor Price Update from Approver.", emailStatus, woData.getWorkOrderId(), scmCache.getVendorDetail(contractData.getVendorId()).getEntityName(), true, masterDataCache.getEmployeeBasicDetail(loggedInUser).getName(), createdByDetails.getEmailId(), props.getEnvType()).sendEmail();
                }
            } catch (Exception e) {
                LOG.error("Unable to send email for bypassed work order", e);
            }
        } else if(VendorContractStatus.CREATED.equals(woData.getWorkOrderStatus())) {
            // any sku in work-order has been reject, then it comes into else if.
            dao.updateContractStatusLog(woData.getWorkOrderStatus().name(), VendorContractStatus.PARTIAL_REJECTED.name(), loggedInUser, woData.getWorkOrderId(), LogType.WORK_ORDER, "Approver Rejected few skus");
            woData.setWorkOrderStatus(VendorContractStatus.PARTIAL_REJECTED);

            try {
                if(StringUtils.hasText(createdByDetails.getEmailId())) {
                    new UserContractEmailNotification("Vendor Price Update from Approver.", VendorContractStatus.PARTIAL_REJECTED, woData.getWorkOrderId(), scmCache.getVendorDetail(contractData.getVendorId()).getEntityName(), true, masterDataCache.getEmployeeBasicDetail(loggedInUser).getName(), createdByDetails.getEmailId(), props.getEnvType()).sendEmail();
                }
            } catch (Exception e) {
                LOG.error("Unable to send email for Work Order : ", e);
            }

            // make change in contract, if required by checking status
            if( WorkOrderType.DEFAULT.equals(woData.getWorkOrderType()) ) {
                dao.updateContractStatusLog(contractData.getStatus(), woData.getWorkOrderStatus().name(), loggedInUser, contractData.getContractId(), LogType.CONTRACT, "Contract status changed");
                contractData.setStatus(woData.getWorkOrderStatus().name());
                dataDao.update(contractData, false);
            }
            dataDao.update(woData, false);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public Map<String, String> getContractStatus(Integer vendorId) {
        // check for user can create Contract/Work-order for vendorId.
        Map<String, String> map = new HashMap<>();
        VendorDetail vd = scmCache.getVendorDetail(vendorId);
        map.put("vendorEmail", vd.getPrimaryEmail());
        if( !StringUtils.hasText(vd.getPrimaryEmail()) ) {
            map.put("message", "Vendor does not have primary email, so cannot create contract/work-order. Please update vendor email.");
            map.put("flag", "false");
            return map;
        }
        WorkOrderData data = dataDao.getRecentWorkOrderForVendor(vendorId);
        if( Objects.isNull(data) ) {
            map.put("flag", "true");
        } else if( VendorContractStatus.getRejectionStatuses().contains(data.getWorkOrderStatus().name()) ) {
            map.put("flag", "true");
        } else if( VendorContractStatus.PARTIAL_REJECTED.equals(data.getWorkOrderStatus())) {
            map.put("flag", "true");
        } else if( data.getWorkOrderStatus().equals(VendorContractStatus.APPLIED) ) {
            map.put("flag", "true");
        } else {
            map.put("flag", "false");
            map.put("message", "One contract is in pending, so please finish it first !");
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public Integer uploadDocument(String mimeType, Integer userId, MultipartFile file) {
        try {
            // TODO --> change formatting.....
            FileType type = FileType.valueOf("OTHERS");
            DocUploadType docType = DocUploadType.valueOf("VENDOR_CONTRACT");
            MimeType mimeTypeNew = MimeType.valueOf(mimeType);

            String fileName = "BYPASS_VENDOR_DOCUMENT" + SCMUtil.getCurrentTimeISTStringWithNoColons();
            String baseDir = "BYPASS_VENDOR_UPLOADED_DOCUMENT" + File.separator + SCMUtil.getCurrentYear() + File.separator + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
            DocumentDetail documentUploaded = paymentRequestManagementService.uploadDocument(type, mimeTypeNew, docType, userId, file, fileName, baseDir);
            if (Objects.isNull(documentUploaded)) {
                return null;
            }
            return documentUploaded.getDocumentId();
        } catch (Exception e) {
            LOG.info("Error while uploading the document " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<PreviousPricingDataVO> getPreviousPricesOfSkuByLocation(Integer skuId, Integer deliveryLocationId, Integer packagingId) {

        try {
            List<UnitBasicDetail> list = masterDataCache.getAllUnits();
            List<Integer> listOfUnitIds = list.stream().filter(ubd -> VendorContractStatus.ACTIVE.name().equals(ubd.getStatus().value())).filter(ubd -> ubd.getLocation() != null).filter(ubd -> ubd.getLocation().getId() == deliveryLocationId).map(UnitBasicDetail::getId).collect(Collectors.toList());
            return dao.findPreviousPricesOfSkuByLocation(skuId, listOfUnitIds, packagingId);
        } catch (Exception exp) {
            LOG.error("ERROR while running the function getPreviousPricesOfSkuByLocation() --> ", exp);
        }
        return null;
    }

    @Override
    public Map<Integer, List<Pair<Integer, String>>> getUnitsByDeliveryLocationIds(List<Integer> locationIds) {
        try {
            List<UnitBasicDetail> listOfUnits = masterDataCache.getAllUnits();
            Map<Integer, List<Pair<Integer, String>>> map = new HashMap<>();
            for (UnitBasicDetail ubd : listOfUnits) {
                if(ubd.getStatus().equals(UnitStatus.IN_ACTIVE)) {
                    continue;
                }
                Integer locationId = ubd.getLocation().getId();
                if (locationIds.contains(locationId)) {
                    if (!map.containsKey(locationId)) {
                        map.put(locationId, new ArrayList<>());
                    }
                    map.get(locationId).add(new Pair<>(ubd.getId(), ubd.getName()));
                }
            }
            return map;

        } catch (Exception exp) {
            LOG.error("Error while calling function : getUnitsByDeliveryLocationIds ", exp);
        }
        return null;
    }


    private void updateAllLogs() {
        List<VendorContractLogs> logs = dataDao.findAll(VendorContractLogs.class);
        List<VendorContractLogs> updatedLogs = new ArrayList<>();
        for (VendorContractLogs log : logs) {
            try {
                if( Objects.isNull(log.getLogType()) ) {
                    log.setLogType( LogType.CONTRACT );
                    log.setLogMessage( "updated LogType in this Log by System" );
                    updatedLogs.add(log);
                }
            } catch (Exception exp) {
                LOG.error("Error while updating log and logType for logId : {}", log.getLogId(), exp);
            }
        }
        dao.update(updatedLogs, false);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public void createDefaultWOsForExistingContracts() {
        LOG.info("Creating Default Work order");
        updateAllLogs();

        // get all contracts to create work orders
        List<VendorContractData> contracts= dataDao.findAll(VendorContractData.class);

        for(VendorContractData contract : contracts) {

            try {
                LOG.info("Creating WO for contract ID : {}", contract.getContractId());
                WorkOrderData workOrderData = getWorkOrderData(contract);
                dataDao.update(contract, false);

                List<VendorContractItemData> itemDataList = new ArrayList<>();
                for( VendorContractItemData itemData : contract.getVendorContractItemDataList() ) {
                    modifyItemData(itemData, workOrderData);
                    itemDataList.add(itemData);
                }
                dataDao.update(itemDataList, false);
                LOG.info("Created WO for contract ID : {}", contract.getContractId());

            } catch (Exception exp) {
                LOG.error("Unable to create WO for contract ID : {}", contract.getContractId(), exp);
            }

        }
    }

    private WorkOrderData getWorkOrderData(VendorContractData data) throws SumoException {
        WorkOrderData workOrderData = new WorkOrderData();
        workOrderData.setContractData(data);
        workOrderData.setGeneratedWorkOrderNumber( data.getContractId() + "_0" );
        workOrderData.setWorkOrderType(WorkOrderType.DEFAULT);
        workOrderData.setStartDate(data.getStartDate());
        workOrderData.setEndDate(data.getEndDate());
        workOrderData.setWorkOrderStatus( getStatus(data) );
        if( Objects.nonNull(data.getApprovalRequestFrom()) ) {
            workOrderData.setApprovalRequestId( Integer.parseInt(data.getApprovalRequestFrom()) );
        }
        workOrderData.setIsByPassed( data.getIsByPassed() );
        workOrderData.setCreatedBy( data.getCreatedBy() );
        workOrderData.setCreatedAt( data.getCreatedAt() );
        workOrderData.setWoApprovalMetaData( getWorkOrderApprovalMetaData(data, workOrderData) );
        workOrderData = dataDao.add(workOrderData, true);
        dao.updateContractStatusLog(null, workOrderData.getWorkOrderStatus().name(), AppConstants.SYSTEM_EMPLOYEE_ID, workOrderData.getWorkOrderId(), LogType.WORK_ORDER, "New default workOrder created by SYSTEM for contract [" + data.getContractId() + "]");
        return workOrderData;
    }

    private VendorContractStatus getStatus(VendorContractData data) {
        if(VendorContractStatus.INITIATED.name().equalsIgnoreCase(data.getStatus())) {
            data.setStatus( VendorContractStatus.PARTIAL_REJECTED.name() );
            dao.updateContractStatusLog(VendorContractStatus.INITIATED.name(), VendorContractStatus.PARTIAL_REJECTED.name(), AppConstants.SYSTEM_EMPLOYEE_ID, data.getContractId(), LogType.CONTRACT, "Status change while creating a default workOrder for contract [" + data.getContractId() + "]");
            return VendorContractStatus.PARTIAL_REJECTED;
        }
        if( VendorContractStatus.APPROVER_APPROVED.name().equalsIgnoreCase(data.getStatus()) ) {
            data.setStatus( VendorContractStatus.VENDOR_APPROVED.name() );
            dao.updateContractStatusLog(VendorContractStatus.APPROVER_APPROVED.name(), VendorContractStatus.VENDOR_APPROVED.name(), AppConstants.SYSTEM_EMPLOYEE_ID, data.getContractId(), LogType.CONTRACT, "Status change while creating a default workOrder for contract [" + data.getContractId() + "]");
            return VendorContractStatus.VENDOR_APPROVED;
        }
        if( VendorContractStatus.APPROVER_REJECTED.name().equals(data.getStatus()) ) {
            data.setStatus( VendorContractStatus.CANCELLED.name() );
            dao.updateContractStatusLog(VendorContractStatus.APPROVER_REJECTED.name(), VendorContractStatus.CANCELLED.name(), AppConstants.SYSTEM_EMPLOYEE_ID, data.getContractId(), LogType.CONTRACT, "Status change while creating a default workOrder for contract [" + data.getContractId() + "]");
            return VendorContractStatus.CANCELLED;
        }
        return VendorContractStatus.valueOf( data.getStatus() );
    }

    private WorkOrderApprovalMetaData getWorkOrderApprovalMetaData(VendorContractData data, WorkOrderData workOrderData) {
        WorkOrderApprovalMetaData woMetaData = new WorkOrderApprovalMetaData();
        woMetaData.setWorkOrderData(workOrderData);
        woMetaData.setWorkOrderDocId( data.getDocumentId() );
        woMetaData.setVendorUserName( data.getVendorUserName() );
        woMetaData.setVendorDesignation( data.getVendorUserDesignation() );
        woMetaData.setVendorIpAddress( data.getIpAddress() );
        woMetaData.setAuthIpAddress( data.getAuthIpAddress() );
        woMetaData.setVendorSignedDocumentId( data.getSignedDocumentId() );
        woMetaData.setAuthSignedDocumentId( data.getAuthSignedDocumentId() != null ? data.getAuthSignedDocumentId() : data.getUnsignedDocumentId() );
        woMetaData.setVendorSignId( data.getDigitalSignID() );
        woMetaData.setAuthSignId( data.getAuthDigitalSignID() );
        woMetaData.setUnsignedDocumentId( data.getUnsignedDocumentId() );
        woMetaData.setTemplateId( data.getTemplateId() );
        woMetaData.setMailTime( data.getMailTime() );

        return woMetaData;
    }

    private void modifyItemData(VendorContractItemData itemData, WorkOrderData workOrderData) {
        itemData.setSkuPriceState( getSkuPriceState(itemData) );
        itemData.setWorkOrderData(workOrderData);
    }

    private VendorContractItemData.SkuPriceState getSkuPriceState(VendorContractItemData itemData) {
        if( Objects.isNull(itemData.getIsNewItem()) && Objects.isNull(itemData.getIsNew()) ) {
            return null;
        }
        if(AppConstants.YES.equalsIgnoreCase(itemData.getIsNew()) && AppConstants.YES.equalsIgnoreCase(itemData.getIsNewItem())) {
            return VendorContractItemData.SkuPriceState.PRICE_UPDATE;
        }
        if( AppConstants.YES.equalsIgnoreCase(itemData.getIsNewItem()) ) {
            return VendorContractItemData.SkuPriceState.NEW_ITEM;
        }
        return VendorContractItemData.SkuPriceState.REPEATED_ITEM;
    }


}
