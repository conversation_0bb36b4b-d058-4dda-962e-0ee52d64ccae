package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.CapexRequestDetailData;
import com.stpl.tech.scm.data.model.CostCenterData;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.BusinessCostCenterMappingData;
import com.stpl.tech.scm.data.model.EmployeeCostCenterMappingData;

import com.stpl.tech.scm.data.model.ListDetailData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderItemData;
import com.stpl.tech.scm.data.model.ServiceOrderStatusEventData;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemData;
import com.stpl.tech.scm.data.model.VendorCostCenterCostElementMapping;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.transport.model.UnitCapexDataSummary;
import com.stpl.tech.scm.domain.model.BusinessCostCenterMappingDomain;
import com.stpl.tech.scm.domain.model.CapexStatus;
import com.stpl.tech.scm.domain.model.InvoiceDocType;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import com.stpl.tech.scm.domain.model.VendorContractSoStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
public class ServiceOrderManagementDaoImpl extends SCMAbstractDaoImpl implements ServiceOrderManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceOrderManagementDaoImpl.class);
    @Autowired
    private EnvProperties env;

    @Override
    public List<ServiceOrderData> findServiceOrders(Integer vendorId, Integer dispatchId,
                                                    List<ServiceOrderStatus> statusList, List<Integer> costElements, boolean showAll ) {
        return findServiceOrders(null,vendorId, dispatchId, null, statusList, null, null, costElements, showAll,
                false,null,null);
    }

    @Override
    public List<ServiceOrderData> findServiceOrders(Integer bccId,Integer vendorId, Integer serviceOrderId,
                                                    List<ServiceOrderStatus> statusList, Date startDate, Date endDate, List<Integer> costElements, boolean showAll ,
                                                    boolean isShort , Integer costCenterId,String type) {
        return findServiceOrders(bccId,vendorId, null, serviceOrderId, statusList, startDate, endDate, costElements, showAll,isShort,costCenterId,type);
    }

    @Override
    public List<ServiceOrderData> findSOsByDepartment(Integer bccId,List<ServiceOrderStatus> statusList, Date startDate, Date endDate, Integer departmentId){
        List<ServiceOrderData> serviceOrderDataList= new ArrayList<>();
        List<String> statuses = statusList.stream().map(ServiceOrderStatus::name).collect(Collectors.toList());
        ListDetailData lda = new ListDetailData();
        lda.setId(departmentId);
        try {
            StringBuilder queryString = new StringBuilder(
                    "SELECT DISTINCT r FROM ServiceOrderData r, ServiceOrderItemData s , CostElementData c left join fetch r.soInvoiceDocument " +
                            " left join fetch r.serviceOrderItemDataList "
                            + " WHERE r.id = s.serviceOrderId AND s.costElementId = c.costElementId AND r.status in (:statusList) " +
                            " AND s.businessCostCenterId = :bccId " +
                            " AND r.generationTime >= :startDate AND r.generationTime <= :endDate " +
                            " And c.department = (:departmentId)");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("statusList",statuses).setParameter("bccId",bccId).setParameter("startDate",startDate).
                    setParameter("endDate",endDate).setParameter("departmentId",lda);
            List<ServiceOrderData> sod =  query.getResultList();
            return sod;

        }catch (Exception e){
            LOG.error("Error In Getting So's For Department Ids ::::" ,e);
            return serviceOrderDataList;
        }


    }

    public List<ServiceOrderData> findServiceOrders(Integer bccId,Integer vendorId, Integer dispatchId, Integer serviceOrderId,
                                                    List<ServiceOrderStatus> statusList, Date startDate, Date endDate, List<Integer> costElements,
                                                    boolean showAll , boolean isShort,Integer costCenterId,String type) {

        List<ServiceOrderData> serviceOrderDataList = null;
        List<String> statuses = statusList.stream().map(ServiceOrderStatus::name).collect(Collectors.toList());

        if (serviceOrderId != null) {
            StringBuilder queryString = new StringBuilder(
                    "SELECT DISTINCT r FROM ServiceOrderData r, ServiceOrderItemData s left join fetch r.soInvoiceDocument " +
                            " left join fetch r.serviceOrderItemDataList "
                            + " WHERE r.id = s.serviceOrderId AND r.id = :serviceOrderId");
            if(type !=null){
                queryString.append(" AND r.type = :type");
            }
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("serviceOrderId", serviceOrderId);
            if(type != null) {
                query.setParameter("type", type);
            }
            return query.getResultList();
        }

        if (statuses != null && !statuses.isEmpty()) {
            StringBuilder queryString = new StringBuilder(
                    "SELECT DISTINCT r FROM ServiceOrderData r , ServiceOrderItemData s");

            if(isShort){
                queryString.append(" left join fetch r.soInvoiceDocument ");
            }
            queryString.append(" WHERE r.status in (:statusList) ");
            queryString.append(" AND r.id = s.serviceOrderId ");

            if (startDate != null && endDate != null) {
                queryString.append(" AND r.generationTime >= :startDate AND r.generationTime <= :endDate");
            }

            if (vendorId != null) {
                queryString.append(" AND r.vendorId = :vendorId");
            }

            if (dispatchId != null) {
                queryString.append(" AND r.dispatchLocationId = :dispatchLocationId");
            }

            if(bccId!=null ){
                queryString.append(" AND s.businessCostCenterId = :bccId");
            }
            if(costCenterId !=null){
                queryString.append(" AND r.costCenterId = :costCenterId ");
            }

            if(type !=null){
                queryString.append(" AND r.type = :type");
            }

            if (!showAll && costElements != null && !costElements.isEmpty()) {
                queryString.append(" AND s.costElementId IN (:costElements)");
            }

            queryString.append(" ORDER BY r.id DESC");

            Query query = manager.createQuery(queryString.toString());

            query.setParameter("statusList", statuses);
            if (startDate != null && endDate != null) {
                query.setParameter("startDate", startDate);
                query.setParameter("endDate", endDate);
            }

            if (vendorId != null) {
                query.setParameter("vendorId", vendorId);
            }

            if (bccId !=null) {
                query.setParameter("bccId", bccId);
            }

            if(costCenterId !=null){
                query.setParameter("costCenterId",costCenterId);
            }
            if(type !=null){
                query.setParameter("type",type);
            }

            if (dispatchId != null) {
                query.setParameter("dispatchLocationId", dispatchId);
            }

            if (!showAll && costElements != null && !costElements.isEmpty()) {
                query.setParameter("costElements", costElements);
            }

            serviceOrderDataList = query.getResultList();
        }
        return serviceOrderDataList;
    }

    @Override
    public List<Integer> findCostCentersForEmployee(Integer empId) {
        Query query = manager.createQuery(
                "SELECT E.costCenterId FROM EmployeeCostCenterMappingData E WHERE E.employeeId = :empId AND E.mappingStatus = :mappingStatus");
        query.setParameter("empId", empId);
        query.setParameter("mappingStatus", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public List<Integer> findBusinessCostCentersForEmployee(Integer costCenterId) {
        Query query = manager.createQuery(
                "SELECT E.businessCostCenterId FROM BusinessCostCenterMappingData E WHERE E.costCenterId = :costCenterId AND E.mappingStatus = :mappingStatus");
        query.setParameter("costCenterId", costCenterId);
        query.setParameter("mappingStatus", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public EmployeeCostCenterMappingData findCostCentersEmployeeActiveMapping(Integer empId , Integer costCenterId) {
        try {
            Query query = manager.createQuery(
                    "SELECT E FROM EmployeeCostCenterMappingData E WHERE E.employeeId = :empId AND " +
                            "  E.costCenterId = :costCenterId AND E.mappingStatus = :mappingStatus");
            query.setParameter("empId", empId);
            query.setParameter("costCenterId",costCenterId);
            query.setParameter("mappingStatus", AppConstants.ACTIVE);
            return (EmployeeCostCenterMappingData) query.getSingleResult();
        }catch (NoResultException nre) {
            LOG.info("No Mapping Found For Cost Center : {} , EmployeeId : {} ", costCenterId, empId);
            return null;
        }
    }

    @Override
    public List<ServiceReceivedItemData> getServiceReceivedItemData(Integer soId) {
        Query query = manager.createQuery(
                "SELECT sri FROM ServiceReceivedItemData sri, ServiceReceivedData srd " +
                        "WHERE sri.serviceReceivedDataId = srd.serviceReceivedId AND sri.serviceOrderId = :soId AND" +
                        " srd.serviceReceiveStatus <> 'CANCELLED' ");
        query.setParameter("soId", soId);
        return query.getResultList();
    }

    @Override
    public List<Integer> findCostElementsForEmployee(Integer empId) {
        Query query = manager.createQuery(
                "SELECT D.costElementId FROM EmployeeCostCenterMappingData E ,CostCenterData C, CostElementCostCenterMapping D "
                        + " WHERE E.employeeId = :empId AND E.mappingStatus = :mappingStatus AND D.mappingStatus = :costElementMappingStatus" +
                        " AND C.costCenterId = E.costCenterId AND D.costCenterId = C.costCenterId");
        query.setParameter("empId", empId);
        query.setParameter("mappingStatus", AppConstants.ACTIVE);
        query.setParameter("costElementMappingStatus", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public List<Integer> findCostElementsForDepartment(Integer departmentId) {
        ListDetailData department = new ListDetailData();
        department.setId(departmentId);
        Query query = manager.createQuery(
                "SELECT c.costElementId FROM CostElementData c "
                        + " WHERE  c.department = :department ");
        query.setParameter("department", department);
        return query.getResultList();
    }

    @Override
    public List<CostCenterData> findCostCenters(List<Integer> costCenterIds) {
        Query query = manager.createQuery(
                "FROM CostCenterData E WHERE E.costCenterStatus = :costCenterStatus AND E.costCenterId IN :costCenterIds ");
        query.setParameter("costCenterIds", costCenterIds);
        query.setParameter("costCenterStatus", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public List<BusinessCostCenterData> findBusinessCostCenters(List<Integer> costCenterIds) {
        Query query = manager.createQuery(
                "FROM BusinessCostCenterData E WHERE E.status = :costCenterStatus AND E.id IN :costCenterIds ");
        query.setParameter("costCenterIds", costCenterIds);
        query.setParameter("costCenterStatus", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public String getCostElementStatus(Integer costId) {
        String reasonData = new String();
        List<String> serviceOrderStatus = new ArrayList<String>();
        serviceOrderStatus.add(ServiceOrderStatus.APPROVED.value());
        serviceOrderStatus.add(ServiceOrderStatus.CREATED.value());
        serviceOrderStatus.add(ServiceOrderStatus.IN_PROGRESS.value());
        Query query = manager.createQuery(
                "FROM ServiceOrderData E WHERE E.status IN :serviceOrderStatus");
        query.setParameter("serviceOrderStatus", serviceOrderStatus);
        List<ServiceOrderData> serviceOrders = query.getResultList();
        for (ServiceOrderData data : serviceOrders) {
            for (ServiceOrderItemData itemData : data.getServiceOrderItemDataList()) {
                if (itemData.getCostElementId() == costId) {
                    reasonData = " Service Orders are not closed for specific cost element, ";
                    break;
                }
            }
        }

        Query querySec = manager.createQuery(
                "FROM ServiceReceivedData E WHERE E.serviceReceiveStatus = :serviceReceivingStatus");
        querySec.setParameter("serviceReceivingStatus", "CREATED");
        List<ServiceReceivedData> serviceReceivings = querySec.getResultList();
        for (ServiceReceivedData receivingdata : serviceReceivings) {
            for (ServiceReceivedItemData receivingitemData : receivingdata.getServiceItemList()) {
                if (receivingitemData.getCostElementId() == costId) {
                    reasonData = " Service Receivings are not Cancelled for specific cost element ";
                    break;
                }
            }
        }

        return reasonData;
    }

    @Override
    public List<Integer> findVendorIds(Integer costCenterId) {
        Query query = manager.createQuery("select vendorId from VendorCostCenterCostElementMapping where costCenterId = :costCenterId");
        query.setParameter("costCenterId", costCenterId);
        List<Integer> vendorIds = query.getResultList();
        return vendorIds;
    }

    @Override
    public List<VendorDetailData> findAllVendors(List<Integer> vendorIds) {
        Query query = manager.createQuery("FROM VendorDetailData where id IN :vendorIds");
        query.setParameter("vendorIds", vendorIds);
        List<VendorDetailData> vendorList = query.getResultList();
        return vendorList;
    }

    @Override
    public List<Integer> findCostElementIds(Integer costCenterId, Integer vendorId) {
        Query query = manager.createQuery(
                "select costElementId from VendorCostCenterCostElementMapping where costCenterId =:costCenterId and vendorId =:vendorId and mappingStatus = :status");
        query.setParameter("costCenterId", costCenterId);
        query.setParameter("vendorId", vendorId);
        query.setParameter("status", ProductStatus.ACTIVE.toString());
        List<Integer> costElementsId = query.getResultList();
        return costElementsId;
    }

    @Override
    public List<CostElementData> findCostElements(List<Integer> costElementIds) {
        Query query = manager.createQuery("FROM CostElementData where id IN :costElementIds and costElementStatus = :status");
        query.setParameter("costElementIds", costElementIds);
        query.setParameter("status", ProductStatus.ACTIVE.toString());
        List<CostElementData> costElementList = query.getResultList();
        return costElementList;
    }

    @Override
    public VendorCostCenterCostElementMapping findCostElement(Integer costCenterId, Integer vendorId, Integer costElementId) {
        Query query = manager.createQuery("FROM VendorCostCenterCostElementMapping c where c.costCenterId =:costCenterId and vendorId =:vendorId and c.costElementId =:costElementId and c.mappingStatus =:status");
        query.setParameter("costCenterId", costCenterId);
        query.setParameter("vendorId", vendorId);
        query.setParameter("costElementId", costElementId);
        query.setParameter("status", ProductStatus.ACTIVE.toString());
        VendorCostCenterCostElementMapping costElement = (VendorCostCenterCostElementMapping) query.getSingleResult();
        return costElement;
    }

    @Override
    public List<ServiceOrderData> getTagDataList(Integer costCenterId, String tagName) {
        Query query = manager.createQuery("FROM ServiceOrderData c where c.costCenterId =:costCenterId and c.tagName =:tagName");
        query.setParameter("costCenterId", costCenterId);
        query.setParameter("tagName", tagName);
        List<ServiceOrderData> serviceOrder = query.getResultList();
        return serviceOrder;
    }

    @Override
    public List<String> getTagNamesList(Integer costCenterId){
        try {
            Query query = manager.createNativeQuery("SELECT s.TAG_NAME FROM SERVICE_ORDER s where s.COST_CENTER_ID =:costCenterId and s.TAG_NAME IS NOT NULL");
            query.setParameter("costCenterId", costCenterId);
            List<String> tagNamesList = query.getResultList();
            return tagNamesList;
        }
        catch (Exception e){
            LOG.error("Error while fetching data from database");
            return new ArrayList<>();
        }
    }

    @Override
    public boolean updateStatusInMappings(Integer costId) {
        List<String> mappings = new ArrayList<String>();
        mappings.add("CostElementVendorMappingData");
        mappings.add("CostElementCostCenterMapping");
        mappings.add("VendorCostCenterCostElementMapping");

        try {
            for (String mapping : mappings) {
                StringBuilder queryStr = new StringBuilder("Update " + mapping + " c set c.mappingStatus = :status where c.costElementId = :costId");
                Query query = manager.createQuery(queryStr.toString());
                query.setParameter("status", ProductStatus.IN_ACTIVE.toString());
                query.setParameter("costId", costId);
                query.executeUpdate();
            }
        } catch (Exception e) {
            LOG.error("Error in Changing Status", e);
        }
        return true;
    }

    @Override
    public ServiceOrderStatusEventData findServiceOrderStatus(Integer serviceOrderId) {
        Query query = manager.createQuery("FROM ServiceOrderStatusEventData c where c.serviceOrderId =:serviceOrderId");
        query.setParameter("serviceOrderId", serviceOrderId);
        ServiceOrderStatusEventData serviceOrderStatus = (ServiceOrderStatusEventData) query.getSingleResult();
        return serviceOrderStatus;
    }

    @Override
    public boolean findBudgetDetail(Integer unitId) {
        Query query = manager.createQuery("FROM CapexRequestDetailData c where c.unitId =:unitId and c.status =:status");
        query.setParameter("unitId", unitId);
        query.setParameter("status", CapexStatus.APPROVED.name());
        List<CapexRequestDetailData> capexRequestDetail = new ArrayList<CapexRequestDetailData>();
        try {
            capexRequestDetail = query.getResultList();
        } catch (Exception e) {
            return false;
        }
        if (!capexRequestDetail.isEmpty() && capexRequestDetail != null) {
            return true;
        }
        return false;
    }

    @Override
    public List<UnitCapexDataSummary> findBudgetDetails(Set<Integer> unitIds , Date validDate) {
      //  Query query = manager.createQuery("select c.unitId FROM CapexRequestDetailData c where c.unitId in :unitIds and c.status =:status and c.lastUpdateTime < :validDate");
        Query query = manager.createQuery("SELECT new com.stpl.tech.scm.data.transport.model.UnitCapexDataSummary(CRD.id , CBD.unitId ,CBD.budgetType,sum(CBD.budgetAmount) as totalBudgetAmount, sum(CBD.runningAmount) as totalRunningAmount, sum(CBD.receivingAmount) as totalReceivedAmount ,sum(CBD.remainingAmount) as totalRemainingAmount,CRD.generationTime ,CRD.lastUpdateTime, CRD.generatedBy, CRD.lastUpdatedBy,CRD.status)  FROM CapexRequestDetailData CRD INNER JOIN CapexBudgetDetailData CBD ON CRD.id = CBD.capexRequestId WHERE CRD.unitId IN (:unitIds) and CRD.status =:status and CRD.lastUpdateTime < :validDate group by CBD.capexRequestId");
        query.setParameter("unitIds", unitIds);
        query.setParameter("status", CapexStatus.APPROVED.name());
        query.setParameter("validDate",validDate);

        try {
            return (List<UnitCapexDataSummary>)query.getResultList();
        } catch (Exception e) {
            LOG.info("Error While Getting Budget Details For Unit Ids : {} ::::::: ",unitIds,e);
            throw  new RuntimeException(e.getMessage());
         }
    }

    @Override
    public List<Integer> getExistingSrInFinancialYear(PaymentRequest paymentRequest) {
        Query query = manager.createQuery(
                        " SELECT p.id FROM PaymentRequestData p WHERE p.vendorId =:vendorId and p.invoiceNumber =:invoiceNumber and p.currentStatus NOT IN(:statusList)" +
                                " and p.grDocType = :grDocType order by p.creationTime desc")
                .setParameter("vendorId", paymentRequest.getVendorId().getId()).setParameter("invoiceNumber", paymentRequest.getInvoiceNumber())
                .setParameter("statusList", Arrays.asList(PaymentRequestStatus.REJECTED.value(),PaymentRequestStatus.CANCELLED.value())).setParameter("grDocType", InvoiceDocType.INVOICE.name());
        return query.getResultList();
    }

    @Override
    public List<ServiceOrderData> getSosForAdvance(Integer vendorId) {
        Query query = manager.createQuery("FROM ServiceOrderData E where E.vendorId = :vendorId and E.status =:status");
        query.setParameter("vendorId", vendorId);
        query.setParameter("status", ServiceOrderStatus.APPROVED.value());
        return query.getResultList();
    }

    @Override
    public BusinessCostCenterMappingData findBusinessCostCentersByCostCenter(Integer id, Integer costCenterId) {
        try {
            Query query = manager.createQuery("FROM BusinessCostCenterMappingData E where E.businessCostCenterId = :businessCostCenterId and E.costCenterId =:costCenterId order by 1 desc");
            query.setParameter("businessCostCenterId", id);
            query.setParameter("costCenterId", costCenterId);
            query.setMaxResults(1);
            return (BusinessCostCenterMappingData) query.getSingleResult() != null ? (BusinessCostCenterMappingData) query.getSingleResult() : null;
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public BigDecimal findUnitAmount(Integer unitId, String departmentName) {
        Query query = manager.createQuery("FROM CapexBudgetDetailData c where c.unitId =:unitId and c.departmentName =:departmentName");
        query.setParameter("unitId", unitId);
        query.setParameter("departmentName", departmentName);
        CapexBudgetDetailData capexBudgetDetailData = new CapexBudgetDetailData();
        try {
            capexBudgetDetailData = (CapexBudgetDetailData) query.getSingleResult();
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
        return capexBudgetDetailData.getRemainingAmount();
    }

    @Override
    public CapexBudgetDetailData findBudgetUnit(Integer unitId, String departmentName) {
        Query queryF = manager.createQuery("FROM CapexRequestDetailData c where c.unitId =:unitId and c.status =:status");
        queryF.setParameter("unitId", unitId);
        queryF.setParameter("status", CapexStatus.APPROVED.name());
        CapexRequestDetailData capexRequestDetailData = (CapexRequestDetailData) queryF.getSingleResult();

        Query query = manager.createQuery("FROM CapexBudgetDetailData c where c.unitId =:unitId and c.departmentName =:departmentName and c.capexRequestId =:capexRequestId");
        query.setParameter("unitId", unitId);
        query.setParameter("departmentName", departmentName);
        query.setParameter("capexRequestId", capexRequestDetailData.getId());
        CapexBudgetDetailData capexBudgetDetailData = new CapexBudgetDetailData();
        try {
            capexBudgetDetailData = (CapexBudgetDetailData) query.getSingleResult();
            return capexBudgetDetailData;
        } catch (Exception e) {
            LOG.error("Error in Finding Budget Detail.", e);
            return null;
        }
    }

    @Override
    public CapexAuditDetailData findCapexAuditData(int unitId) {
        Integer capexRequestId;
        try {
            Query query = manager.createQuery("select id FROM CapexRequestDetailData c where c.unitId =:unitId and c.status =:status");
            query.setParameter("unitId", unitId);
            query.setParameter("status", CapexStatus.APPROVED.name());
            capexRequestId = (Integer) query.getSingleResult();
        }
        catch (Exception e) {
            LOG.error("Exception occurred while finding Capex Request Data for unit Id : {}  ::: ",unitId,e);
            return null;
        }

        /*
         * Query querySec = manager.
         * createQuery("FROM CapexAuditDetailData c where c.capexRequestId =:capexRequestId and c.status not in (:status) ORDER BY c.id DESC"
         * ); querySec.setParameter("capexRequestId", capexRequestId); List<String>
         * status = Arrays.asList(CapexStatus.CREATED.value(),
         * CapexStatus.PENDING_APPROVAL.value(),
         * CapexStatus.PENDING_APPROVAL_L1.value(),
         * CapexStatus.PENDING_APPROVAL_L2.value(),
         * CapexStatus.PENDING_APPROVAL_L3.value(), CapexStatus.REJECTED_L1.value(),
         * CapexStatus.REJECTED_L2.value(), CapexStatus.REJECTED_L3.value(),
         * CapexStatus.CANCELLED.value()); querySec.setParameter("status", status);
         * querySec.setMaxResults(1);
         */
        try {
            Query querySec = manager.createQuery("FROM CapexAuditDetailData c where c.capexRequestId =:capexRequestId and c.status =:status");
            querySec.setParameter("capexRequestId", capexRequestId);
            querySec.setParameter("status", CapexStatus.APPROVED.name());
            CapexAuditDetailData capexAuditDetailData = (CapexAuditDetailData) querySec.getSingleResult();
            return capexAuditDetailData;
        }
        catch (Exception e) {
            LOG.error("Exception occurred while finding Capex Audit Detail Data for unit id : {} ::: ",unitId,e);
            return null;
        }
    }

    @Override
    public BigDecimal getReceivedQuantity(Integer soItemId) {

        List<String> notInCheckList = new ArrayList<>();
        notInCheckList.add(ServiceOrderStatus.PROVISIONAL.value());
        notInCheckList.add(ServiceOrderStatus.CANCELLED.value());

//        Query query = manager.createQuery("FROM ServiceReceivedItemData c where c.serviceOrderItemId =:itemId");
        Query query = manager.createQuery("SELECT sri FROM ServiceReceivedItemData sri, ServiceReceivedData srd " +
                "WHERE sri.serviceReceivedDataId = srd.serviceReceivedId AND sri.serviceOrderItemId = :soItemId AND" +
                        " srd.serviceReceiveStatus NOT IN :notInCheckList");

        query.setParameter("soItemId", soItemId).setParameter("notInCheckList", notInCheckList);
        List<ServiceReceivedItemData> serviceItems = query.getResultList();
        BigDecimal receivedQuantity = new BigDecimal(0);
        for (ServiceReceivedItemData itemData : serviceItems) {
            receivedQuantity = receivedQuantity.add(itemData.getReceivedQuantity());
        }
        return receivedQuantity;
    }

    @Override
    public List<Integer> getServiceReceivedDataIds(Integer soId, String status) {

        Query query = manager.createNativeQuery("SELECT srd.SERVICE_RECEIVED_ID FROM SERVICE_ORDER_TO_SERVICE_RECEIVED_MAPPING sosr " +
                "INNER JOIN SERVICE_RECEIVED_DATA srd ON sosr.SERVICE_RECEIVED_ID = srd.SERVICE_RECEIVED_ID " +
                "WHERE sosr.SERVICE_ORDER_ID = :soId AND srd.SERVICE_RECEIVE_STATUS = :status");

        query.setParameter("soId", soId).setParameter("status", status);
        List<Integer> ids = query.getResultList();
        return ids;
    }

    @Override
    public Map<String, BigDecimal> getServiceReceivedData(Date endDate, Integer unitId) {
        Date startDate = AppUtils.getFirstDayOfMonth(endDate);

        String s = "SELECT sr.BUDGET_CATEGORY, SUM(sr.TOTAL_AMOUNT) FROM SERVICE_RECEIVED_ITEM sr "
                + "INNER JOIN SERVICE_RECEIVED_DATA sd ON sr.SERVICE_RECEIVED_ID = sd.SERVICE_RECEIVED_ID "
                + "INNER JOIN SERVICE_ORDER so ON so.SERVICE_ORDER_ID = sr.SERVICE_ORDER_ID "
                + "INNER JOIN BUSINESS_COST_CENTER_DATA bcc ON bcc.BCC_ID = sr.BUSINESS_COST_CENTER_ID "
                + "WHERE sd.SERVICE_RECEIVE_STATUS = :status AND sd.BUSINESS_DATE BETWEEN :startDate AND :endDate "
                + "AND so.TYPE = :soType AND bcc.BCC_CODE = :unitId GROUP BY sr.BUDGET_CATEGORY";
        Query query = manager.createNativeQuery(s);
        query.setParameter("startDate", startDate).setParameter("endDate", endDate).setParameter("status",
                CalculationStatus.CREATED.name()).setParameter("soType", "OPEX").setParameter("unitId", unitId + "");
        List<Object[]> results = query.getResultList();
        Map<String, BigDecimal> map = new HashMap<>();
        if (results != null && results.size() > 0) {
            for (Object[] o : results) {
                String budget = o[0] == null ? ExpenseField.ServiceRecordCategory.NONE.name() : o[0].toString();
                map.put(budget, (BigDecimal) o[1]);
            }
        }
        return map;
    }

    @Override
    public List<Integer> getServiceOrderIdsForFinalizedUpdation(Date endDate, Integer unitId) {
        Date startDate = AppUtils.getFirstDayOfMonth(endDate);

        String s = "SELECT DISTINCT so.SERVICE_ORDER_ID FROM SERVICE_RECEIVED_ITEM sr "
                + "INNER JOIN SERVICE_RECEIVED_DATA sd ON sr.SERVICE_RECEIVED_ID = sd.SERVICE_RECEIVED_ID "
                + "INNER JOIN SERVICE_ORDER so ON so.SERVICE_ORDER_ID = sr.SERVICE_ORDER_ID "
                + "INNER JOIN BUSINESS_COST_CENTER_DATA bcc ON bcc.BCC_ID = sr.BUSINESS_COST_CENTER_ID "
                + "WHERE sd.SERVICE_RECEIVE_STATUS = :status AND sd.BUSINESS_DATE BETWEEN :startDate AND :endDate "
                + "AND so.TYPE = :soType AND bcc.BCC_CODE = :unitId ";
        Query query = manager.createNativeQuery(s);
        query.setParameter("startDate", startDate).setParameter("endDate", endDate).setParameter("status",
                CalculationStatus.CREATED.name()).setParameter("soType", "OPEX").setParameter("unitId", unitId + "");
        return query.getResultList();
    }

    public void setAccountableForPnlServiceOrder(List<Integer> ids, boolean status) {
        Query query = manager.createQuery("update ServiceOrderData so set so.accountedForInPnl=:status where so.id IN (:ids)");
        query.setParameter("ids", ids);
        query.setParameter("status", AppConstants.getValue(status));
        query.executeUpdate();

    }

    public Date getStartDateOfCapexBudget(Integer capexRequestId){
        try{
            Query query=manager.createQuery("FROM CapexAuditDetailData a WHERE a.capexRequestId = :capexRequestId and a.approvedDate IS NOT NULL ORDER BY a.id asc");
            query.setParameter("capexRequestId",capexRequestId);
            query.setMaxResults(1);
            CapexAuditDetailData result = (CapexAuditDetailData) query.getSingleResult();
            if (Objects.isNull(result)) {
                return AppUtils.getCurrentTimestamp();
            }
            else {
                return result.getApprovedDate();
            }
        }
        catch(Exception e){
            LOG.error("Error in getting date from Capex_Audit_Detail For Capex Request Id : {} :::::: ",capexRequestId,e);
        }
        return AppUtils.getCurrentTimestamp();
    }

    @Override
    public Map<Integer,BigDecimal> getPaidAmount(List<Integer> SoIds) {
        Map<Integer,BigDecimal> soToPaidAmount = new HashMap<>();
        try{
            Query query=manager.createNativeQuery("SELECT sri.SERVICE_ORDER_ITEM_ID , sum(pii.TOTAL_AMOUNT) " +
                    "FROM SERVICE_RECEIVED_ITEM sri inner join SERVICE_RECEIVED_DATA srd " +
                    "on sri.SERVICE_RECEIVED_ID=srd.SERVICE_RECEIVED_ID inner join PAYMENT_REQUEST pr on srd.PAYMENT_REQUEST_ID=pr.PAYMENT_REQUEST_ID " +
                    "inner join PAYMENT_INVOICE_ITEM pii on sri.SERVICE_RECEIVED_ITEM_ID=pii.SERVICE_RECEIVED_ITEM_ID " +
                    "where pr.CURRENT_STATUS IN :statuses AND sri.SERVICE_ORDER_ID IN (:SoIds) group by sri.SERVICE_ORDER_ITEM_ID" +
                    ";");
            query.setParameter("SoIds",SoIds).setParameter("statuses", Arrays.asList("PAID","SENT_FOR_PAYMENT","CLOSED"));
            List<Object[]> totalAmountList =  query.getResultList();
            if(Objects.nonNull(totalAmountList)){
                totalAmountList.forEach(item ->{
                    soToPaidAmount.put((Integer) item[0], (BigDecimal) item[1]);
                });
            }
            return soToPaidAmount;
        }
        catch(Exception e){
            LOG.error("Error in getting Paid Amount from Payment Invoice Item for SO Ids :  {} ::::",SoIds,e);
        }
        return soToPaidAmount ;
    }

    @Override

    public Date getStartDate(Integer capexRequestId){
        try{
            Query query=manager.createQuery("FROM CapexAuditDetailData a WHERE a.capexRequestId = :capexRequestId and a.approvedDate IS NOT NULL ORDER BY a.id asc");
            query.setParameter("capexRequestId",capexRequestId);
            query.setMaxResults(1);
            List<CapexAuditDetailData> result = (List<CapexAuditDetailData>) query.getResultList();
            if (result.isEmpty()) {
                return AppUtils.getCurrentTimestamp();
            }
            else {
                return result.get(0).getApprovedDate();
            }
        }
        catch(Exception e){
            LOG.error("Error in getting date from Capex_Audit_Detail :::",e);
        }
        return AppUtils.getCurrentTimestamp();
    }

    public List<String> showRequiredDocuments(List<Integer> costElementIds){
        Query query = manager.createQuery("SELECT a.documentName FROM AdditionalDocumentsMaster a inner join CostElementDocumentMapping c on " +
                "a.id = c.documentId where c.costElementId in :ids group by c.documentId");
        query.setParameter("ids", costElementIds);
        List<String> documentName = query.getResultList();
        return documentName;
    }

    @Override
    public List<Integer> findVendorIdsForCostElements(Integer costCenterId,Integer costElementId) {
        try {
            Query query = this.manager.createQuery("select distinct(vendorId) from VendorCostCenterCostElementMapping where costCenterId =:costCenterId and costElementId =:costElementId and mappingStatus = :status");
            query.setParameter("costCenterId", costCenterId);
            query.setParameter("costElementId", costElementId);
            query.setParameter("status", ProductStatus.ACTIVE.toString());
            List<Integer> vendorIds = query.getResultList();
            return vendorIds;
        }catch (Exception e){
            LOG.error("Error while getting vendors for cost center Id  :{}  cost element Id :  {} ::::",costCenterId,costElementId);
            throw new RuntimeException(e.getMessage());
        }


    }

    @Override
    public Map<Integer,BigDecimal> getSOLevelPaidAmount(List<Integer> SoIds) {
        Map<Integer,BigDecimal> soIdAndPaidAmount = new HashMap<>();
        try{
            Query query=manager.createNativeQuery("SELECT \n" +
                    "    sri.SERVICE_ORDER_ID,\n" +
                    "    sum(pii.TOTAL_AMOUNT) as TOTAL_AMOUNT\n" +
                    "FROM SERVICE_RECEIVED_ITEM sri\n" +
                    "INNER JOIN SERVICE_RECEIVED_DATA srd on sri.SERVICE_RECEIVED_ID=srd.SERVICE_RECEIVED_ID \n" +
                    "INNER JOIN PAYMENT_REQUEST pr on srd.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID\n" +
                    "INNER JOIN PAYMENT_INVOICE pi on pi.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID\n" +
                    "INNER JOIN PAYMENT_INVOICE_ITEM pii on pi.PAYMENT_INVOICE_ID = pii.PAYMENT_INVOICE_ID and sri.SERVICE_RECEIVED_ITEM_ID = pii.SERVICE_RECEIVED_ITEM_ID\n" +
                    "WHERE pr.CURRENT_STATUS IN (:statuses) and  sri.SERVICE_ORDER_ID in(:SoIds) group by sri.SERVICE_ORDER_ID;");
            query.setParameter("SoIds",SoIds).setParameter("statuses", Arrays.asList("PAID","SENT_FOR_PAYMENT","CLOSED","FORCE_CLOSED"));
            List<Object[]> totalAmountList =  query.getResultList();
            if(Objects.nonNull(totalAmountList)){
                totalAmountList.forEach(item ->{
                    soIdAndPaidAmount.put((Integer) item[0], (BigDecimal) item[1]);
                });
            }
            return soIdAndPaidAmount;
        }
        catch(Exception e){
            LOG.error("Error in getting Paid Amount from Payment Invoice Item for SO Ids :  {} ::::",SoIds,e);
        }
        return soIdAndPaidAmount;
    }


    @Override
    public Date getVendorSOApprovalDate(Integer soId){

        try {
            Query query = manager.createQuery("Select lastUpdateTime from VendorContractSoInfo where soId = :soId and status = :status");
            query.setParameter("soId", soId);
            query.setParameter("status", VendorContractSoStatus.APPROVED.name());
            return (Date) query.getSingleResult();
        }catch (Exception e){
            LOG.error("######### Error while getting vendor so approval date for soId : {}, message : {}  ##############",soId,e.getMessage());
             return null;
        }
    }

    @Override
    public List<BusinessCostCenterMappingDomain> findBusinessCostCentersData(Integer costCenterId) {
        Query query = manager.createNativeQuery(
                "SELECT bcmd.*, bcc.BCC_NAME,ccd.COST_CENTER_NAME FROM BUSINESS_COST_CENTER_MAPPING_DATA bcmd inner join BUSINESS_COST_CENTER_DATA bcc on bcmd.BUSINESS_COST_CENTER_ID = bcc.BCC_ID \n" +
                        "inner join COST_CENTER_DATA ccd on bcmd.COST_CENTER_ID = ccd.COST_CENTER_ID where bcmd.COST_CENTER_ID = :costCenterId");
        query.setParameter("costCenterId", costCenterId);
        List<Object[]> res = query.getResultList();
        List<BusinessCostCenterMappingDomain> result = new ArrayList<>();
        for(Object[] o : res){
            result.add(
            BusinessCostCenterMappingDomain.builder().mappingId((Integer)o[0]).costCenterId((Integer) o[1]).businessCostCenterId((Integer) o[2])
                    .mappingStatus((String) o[3]).bccName((String) o[4]).ccName((String) o[5]).build());

        }
        return result;
    }


    @Override
    public Date getLastOperationDate(Integer unitId){
        try {
            Query query = manager.createNativeQuery("SELECT OPERATION_STOP_DATE FROM "+ (AppUtils.isProd(env.getEnvType()) ? "KETTLE_MASTER.UNIT_CLOSURE_EVENT" : "KETTLE_MASTER_STAGE.UNIT_CLOSURE_EVENT")+" where UNIT_ID= :unitId and CLOSURE_STATUS in ('PROCESSING','CLOSED') order by CREATION_TIME desc limit 1;");
            query.setParameter("unitId",unitId);
            return (Date) query.getSingleResult();
        }catch (Exception e){
            LOG.error("######### Error while getting last operation date, error_msg : {}  ##############",e.getMessage());
            return null;
        }
    }

}
