package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.PaymentRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Table(name = "PAYMENT_REQUEST_METADATA")
public class PaymentRequestMetaData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    Long id;
    @OneToOne
    @JoinColumn(name="PAYMENT_REQUEST_ID")
    PaymentRequestData paymentRequest;
    @Column(name="IS_RCM")
    String isRCM;
    @Column(name = "GST_RATE")
    Double gstRate;
    @Column(name = "IS_GST_AVAILED")
    String isGstAvailed;
    @ManyToOne
    @JoinColumn(name="TDS_LEDGER")
    TdsLedgerRate tdsLedger;

    @Column(name="TDS_PERCENTAGE")
    Double tdsPercentage;
    @ManyToOne
    @JoinColumn(name="LDC_VENDOR_RATE_ID")
    LdcVendorData ldcVendorData;
    @ManyToOne
    @JoinColumn(name="RECIPIENT_STATE_ID")
    GstOfStpl recipientState;
    @ManyToOne
    @JoinColumn(name = "SUPPLIER_STATE_ID")
    GstStateMetaData supplierState;

    @Lob
    @Column(name="LOGGED_MESSAGES")
    String loggedMessages;
}
