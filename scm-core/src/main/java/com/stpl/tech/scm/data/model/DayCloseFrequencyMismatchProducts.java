/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "DAY_CLOSE_FREQUENCY_MISMATCH_PRODUCTS")
public class DayCloseFrequencyMismatchProducts {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DAY_CLOSE_FREQUENCY_MISMATCH_ID")
    private Integer dayCloseFrequencyMismatchId;

    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "PRODUCT_ID")
    private Integer productId;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "START_ID")
    private Integer startId;

    @Column(name = "END_ID")
    private Integer endId;

    @Column(name = "STOCK_TYPE")
    private String stockType;

    public Integer getDayCloseFrequencyMismatchId() {
        return this.dayCloseFrequencyMismatchId;
    }

    public void setDayCloseFrequencyMismatchId(Integer dayCloseFrequencyMismatchId) {
        this.dayCloseFrequencyMismatchId = dayCloseFrequencyMismatchId;
    }

    public Integer getEventId() {
        return this.eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getProductId() {
        return this.productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getStartId() {
        return this.startId;
    }

    public void setStartId(Integer startId) {
        this.startId = startId;
    }

    public Integer getEndId() {
        return this.endId;
    }

    public void setEndId(Integer endId) {
        this.endId = endId;
    }

    public String getStockType() {
        return this.stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }
}
