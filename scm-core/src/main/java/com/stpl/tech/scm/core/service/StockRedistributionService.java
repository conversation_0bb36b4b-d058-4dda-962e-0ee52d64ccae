package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.RiderActionEnum;
import com.stpl.tech.scm.domain.model.RiderRoutePlanDataDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface StockRedistributionService {
    List<RiderRoutePlanDataDto> getRiderRoutePlanByEmployeeId(Integer employeeId) throws SumoException;

    boolean updateRiderRoutePlanItem(Integer riderRoutePlanItemDataId, BigDecimal finalQuantity, Integer loggedInUser, String comment) throws SumoException;

    boolean completeRouteStepOfRide(Integer riderRoutePlanStepDataId, RiderActionEnum riderAction, Integer loggedInUser, BigDecimal temperature) throws SumoException;

    void checkAndMarkRideAsCompleted(Integer riderRoutePlanDataId) throws SumoException;

    Map<Integer, Pair<String, BigDecimal>> getRiderUnitInventory(Integer loggedInUser) throws SumoException;

    boolean setPackagingInfoToRouteItems() throws SumoException;

    void expirePendingRoutePlans();
}
