package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.data.enums.NotificationKeyType;
import com.stpl.tech.scm.data.enums.SchedulerStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.util.Date;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "EMAIL_NOTIFICATION_STATUS")
public class EmailNotificationStatus{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    Long id;
    @Column(name = "SCHEDULED_TIME")
    Date scheduledTime;
    @Column(name = "SCHEDULER_STATUS")
    @Enumerated(EnumType.STRING)
    SchedulerStatus schedulerStatus;
    @Column(name = "NOTIFICATION_KEY_TYPE")
    @Enumerated(EnumType.STRING)
    NotificationKeyType notificationKeyType;
    @Column(name = "MESSAGE_ID", unique = true)
    String messageId;
    @Lob
    @Column(name="METADATA")
    String metadata;
}
