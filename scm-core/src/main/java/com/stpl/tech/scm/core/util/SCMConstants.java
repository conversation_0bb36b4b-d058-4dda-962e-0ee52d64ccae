package com.stpl.tech.scm.core.util;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.config.SpringContextHolder;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.util.domain.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class SCMConstants {

    private static SCMCache scmCache;
    private static MasterDataCache masterDataCache;

    private static void getCache() {
        if (scmCache == null) {
            scmCache = SpringContextHolder.getBean(SCMCache.class);
        }
    }

    private static void getMasterCache() {
        if (masterDataCache == null) {
            masterDataCache = SpringContextHolder.getBean(MasterDataCache.class);
        }
    }

    public enum TemplateCode {
        VENDOR_CONTRACT_1("VendorContract_1"), MASTER_VENDOR_CONTRACT("MasterVendorContractTemplate"), WORK_ORDER("WorkOrderTemplate");

        public String code; // html file names
        TemplateCode(String code) {
            this.code = code;
        }

    }

    private Map<Integer, IdCodeName> template = new HashMap<>();
    public Map<Integer, IdCodeName> getVendorContractTemplate() {
        if (template.isEmpty()) {
            template = Map.of(
                    1, new IdCodeName(1,TemplateCode.VENDOR_CONTRACT_1.code,"365"),
                    2, new IdCodeName(2, TemplateCode.MASTER_VENDOR_CONTRACT.code, TemplateCode.MASTER_VENDOR_CONTRACT.name()),
                    3, new IdCodeName(3, TemplateCode.WORK_ORDER.code, TemplateCode.WORK_ORDER.name())
            );
        }
        return template;
    }

    public static String getCompanyNameByCompanyId(Integer companyId) {
        if(companyId == null) {
            Integer unitId = RequestContext.getContext().getUnitId();
            if(unitId != null) {
                getMasterCache();
                companyId = masterDataCache.getUnit(unitId).getCompany().getId();
            }
        }

        // TODO make it dynamic
        String companyName = "SUNSHINE TEAHOUSE PVT LTD.";

        return companyName;

    }

}
