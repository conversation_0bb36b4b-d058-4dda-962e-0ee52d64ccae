package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

public class CreditNoteGenerationTemplate extends AbstractVelocityTemplate {

    private EnvProperties props;
    private SalesPerformaInvoice invoice;
    private DocumentDetailData documentDetailData;

    public CreditNoteGenerationTemplate(DocumentDetailData documentDetailData, SalesPerformaInvoice invoice, EnvProperties props) {
        this.props = props;
        this.documentDetailData = documentDetailData;
        this.invoice = invoice;
    }

    @Override
    public String getTemplatePath() {
        return "templates/CreditNoteGenerationTemplate.html";
    }

    @Override
    public String getFilepath() {
        return props.getBasePath() + "/CreditNote/" + documentDetailData.getDocumentId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> data = new HashMap<>();
        data.put("props",props);
        data.put("documentData",documentDetailData);
        data.put("invoice",invoice);
        return data;
    }
}