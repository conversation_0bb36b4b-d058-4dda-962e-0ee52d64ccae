package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "MENU_SCM_KEY_DATA")
public class MenuScmKeyData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "KEY_ID", unique = true, nullable = false)
    private Integer keyId;
    @Column(name = "REFERENCE_ORDER_KEY_ITEM_ID", nullable = false)
    private Integer referenceOrderKeyItemId;
    @Column(name = "REFERENCE_ORDER_KEY_ITEM_TYPE", nullable = false)
    private String referenceOrderKeyItemType;
    @Column(name = "DATE", nullable = false)
    private Date date;
    @Column(name = "DATE_TYPE", nullable = false)
    private String dateType;
    @Column(name = "QUANTITY", nullable = false)
    private BigDecimal quantity;

}
