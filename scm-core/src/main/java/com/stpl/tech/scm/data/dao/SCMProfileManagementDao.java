package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.*;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 07-05-2016.
 */
public interface SCMProfileManagementDao extends SCMAbstractDao {

    public List<EntityAttributeValueMappingData> getEntityAttributeValueMappingForEntityAndType(int entityId, String entityType);

    public ProfileDefinitionData getProfileByName(String profileName);


}
