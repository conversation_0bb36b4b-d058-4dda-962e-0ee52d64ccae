package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.UnitDetailData;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.DeactivateValidateResponse;
import com.stpl.tech.scm.domain.model.NsoEventAssetsResponse;
import com.stpl.tech.scm.domain.model.UnitDetail;

import java.net.URISyntaxException;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 07-05-2016.
 */
public interface SCMUnitManagementService {

    public UnitDetail viewUnit(int unitId);

    public List<UnitDetail> viewAllUnits();

    public UnitDetailData addUnitDetail(UnitDetail unitDetail) throws SumoException;

    public boolean updateUnit(UnitDetail unitDetail);

    public DeactivateValidateResponse deactivateUnit(int unitId) throws SumoException, URISyntaxException;

    public boolean activateUnit(int unitId) throws SumoException;

    public List<UnitDetail> viewUnitsForAdhocRequest(int unitId);

    public boolean addUnit(int unitId) throws SumoException;

	public List<FulfillmentUnitMappingData> getUnitToFulfillmentTypeMapping(int unitId);

	public List<UnitDistanceMappingData> getUnitDistanceMapping(int sourceUnitId, int destinationUnitId);

	public List<UnitDistanceMappingData> updateUnitDistanceMapping(List<UnitDistanceMappingData> list) throws SumoException;

    public boolean updateSCMUnit(Unit unit)throws SumoException;

    public List<BusinessCostCenter> getBusinessCostCentersByCompanyId(int companyId, String accountType, String businessCostCentreCode) throws SumoException;

    List<BusinessCostCenter> getBusinessCostCentersByType(String businessCostCentreType);

    List<BusinessCostCenterData> getBusinessCostCentresByBccCode(Integer unitId);

    public NsoEventAssetsResponse getAssetsToHandover(int unitId);

}
