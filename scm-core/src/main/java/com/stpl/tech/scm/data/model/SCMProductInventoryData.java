/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "STOCK_INVENTORY")
public class SCMProductInventoryData {

	private Integer stockingId;
	private String eventType;
	private int productId;
	private int unitId;
	private Date businessDate;
	private Date generationTime;
	private int generatedBy;
	private BigDecimal openingStock;
	private BigDecimal expectedClosing;
	private BigDecimal actualClosing;
	private BigDecimal originalClosing;
	private BigDecimal originalExpectedClosing;
	private BigDecimal averagePrice;
	private BigDecimal varianceTillNow;
	private BigDecimal extraVariance;
	private BigDecimal variance;
	private BigDecimal originalVariance;
	private BigDecimal varianceCost;
	private BigDecimal originalVarianceCost;
	private BigDecimal variancePrice;
	private BigDecimal expectedExpiryWastage;
	private BigDecimal expectedWastageByNow;
	private BigDecimal expectedExpiryWastageAfterConsumption;
	private BigDecimal actualExpiryWastage;
	private BigDecimal deviationOfExpiryWastage;
	private BigDecimal nextDayExpectedExpiryWastage;
	private BigDecimal nextDayExpectedWastageByNow;
	private String unitOfMeasure;
	private String status;
	private String comment;
	private Integer lastDayCloseEvent;
	private SCMDayCloseEventData currentDayCloseEvent;
    private String stockType;
    private String taxType;
    private BigDecimal taxPercentage;
    private BigDecimal varianceTax;
    private BigDecimal originalVarianceTax;
	private Integer categoryId;
	private Integer subCategoryId;

	public SCMProductInventoryData() {
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "STOCKING_ID", unique = true, nullable = false)
	public Integer getStockingId() {
		return stockingId;
	}

	public void setStockingId(Integer stockingId) {
		this.stockingId = stockingId;
	}

	@Column(name = "EVENT_TYPE", nullable = false)
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "BUSINESS_DATE", nullable = true)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "GENERATION_TIME", nullable = false)
	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public int getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(int generatedBy) {
		this.generatedBy = generatedBy;
	}

	@Column(name = "OPENING_STOCK", nullable = true)
	public BigDecimal getOpeningStock() {
		return openingStock;
	}

	public void setOpeningStock(BigDecimal openingStock) {
		this.openingStock = openingStock;
	}

	@Column(name = "EXPECTED_CLOSING_VALUE", nullable = true)
	public BigDecimal getExpectedClosing() {
		return expectedClosing;
	}

	public void setExpectedClosing(BigDecimal expectedClosing) {
		this.expectedClosing = expectedClosing;
	}

	@Column(name = "CLOSING_STOCK", nullable = true)
	public BigDecimal getActualClosing() {
		return actualClosing;
	}

	public void setActualClosing(BigDecimal actualClosing) {
		this.actualClosing = actualClosing;
	}

	@Column(name = "ORIGINAL_CLOSING_STOCK", nullable = true)
	public BigDecimal getOriginalClosing() {
		return originalClosing;
	}

	public void setOriginalClosing(BigDecimal originalClosing) {
		this.originalClosing = originalClosing;
	}

	@Column(name = "ORIGINAL_EXPECTED_CLOSING", nullable = true)
	public BigDecimal getOriginalExpectedClosing() {
		return originalExpectedClosing;
	}

	public void setOriginalExpectedClosing(BigDecimal originalExpectedClosing) {
		this.originalExpectedClosing = originalExpectedClosing;
	}

	@Column(name = "AVERAGE_PRICE", nullable = true)
	public BigDecimal getAveragePrice() {
		return averagePrice;
	}

	public void setAveragePrice(BigDecimal averagePrice) {
		this.averagePrice = averagePrice;
	}

	@Column(name = "VARIANCE_TILL_NOW", nullable = true)
	public BigDecimal getVarianceTillNow() {
		return varianceTillNow;
	}

	public void setVarianceTillNow(BigDecimal varianceTillNow) {
		this.varianceTillNow = varianceTillNow;
	}

	//only saved when Expected Closing is Negative
	@Column(name = "EXTRA_VARIANCE", nullable = true)
	public BigDecimal getExtraVariance() {
		return extraVariance;
	}

	public void setExtraVariance(BigDecimal extraVariance) {
		this.extraVariance = extraVariance;
	}

	@Column(name = "VARIANCE", nullable = true)
	public BigDecimal getVariance() {
		return variance;
	}

	public void setVariance(BigDecimal variance) {
		this.variance = variance;
	}

	@Column(name = "ORIGINAL_VARIANCE", nullable = true)
	public BigDecimal getOriginalVariance() {
		return originalVariance;
	}

	public void setOriginalVariance(BigDecimal originalVariance) {
		this.originalVariance = originalVariance;
	}

	@Column(name = "VARIANCE_COST", nullable = true)
    public BigDecimal getVarianceCost() {
        return varianceCost;
    }

    public void setVarianceCost(BigDecimal varianceCost) {
        this.varianceCost = varianceCost;
    }

	@Column(name = "ORIGINAL_VARIANCE_COST", nullable = true)
	public BigDecimal getOriginalVarianceCost() {
		return originalVarianceCost;
	}

	public void setOriginalVarianceCost(BigDecimal originalVarianceCost) {
		this.originalVarianceCost = originalVarianceCost;
	}

	@Column(name = "VARIANCE_PRICE", nullable = true)
    public BigDecimal getVariancePrice() {
        return variancePrice;
    }

    public void setVariancePrice(BigDecimal variancePrice) {
        this.variancePrice = variancePrice;
    }

	@Column(name = "EXPECTED_EXPIRY_WASTAGE", nullable = true)
	public BigDecimal getExpectedExpiryWastage() {
		return expectedExpiryWastage;
	}

	public void setExpectedExpiryWastage(BigDecimal expectedExpiryWastage) {
		this.expectedExpiryWastage = expectedExpiryWastage;
	}

	@Column(name = "EXPECTED_EXPIRY_WASTAGE_BY_NOW", nullable = true)
	public BigDecimal getExpectedWastageByNow() {
		return expectedWastageByNow;
	}

	public void setExpectedWastageByNow(BigDecimal expectedWastageByNow) {
		this.expectedWastageByNow = expectedWastageByNow;
	}

	@Column(name = "EXPECTED_EXPIRY_WASTAGE_AFTER_CONSUMPTION", nullable = true)
	public BigDecimal getExpectedExpiryWastageAfterConsumption() {
		return expectedExpiryWastageAfterConsumption;
	}

	public void setExpectedExpiryWastageAfterConsumption(BigDecimal expectedExpiryWastageAfterConsumption) {
		this.expectedExpiryWastageAfterConsumption = expectedExpiryWastageAfterConsumption;
	}

	@Column(name = "ACTUAL_EXPIRY_WASTAGE", nullable = true)
	public BigDecimal getActualExpiryWastage() {
		return actualExpiryWastage;
	}

	public void setActualExpiryWastage(BigDecimal actualExpiryWastage) {
		this.actualExpiryWastage = actualExpiryWastage;
	}

	@Column(name = "DEVIATION_OF_EXPIRY_WASTAGE", nullable = true)
	public BigDecimal getDeviationOfExpiryWastage() {
		return deviationOfExpiryWastage;
	}

	public void setDeviationOfExpiryWastage(BigDecimal deviationOfExpiryWastage) {
		this.deviationOfExpiryWastage = deviationOfExpiryWastage;
	}

	@Column(name = "NEXT_DAY_EXPECTED_EXPIRY_WASTAGE", nullable = true)
	public BigDecimal getNextDayExpectedExpiryWastage() {
		return nextDayExpectedExpiryWastage;
	}

	public void setNextDayExpectedExpiryWastage(BigDecimal nextDayExpectedExpiryWastage) {
		this.nextDayExpectedExpiryWastage = nextDayExpectedExpiryWastage;
	}

	@Column(name = "NEXT_DAY_EXPECTED_EXPIRY_WASTAGE_BY_NOW", nullable = true)
	public BigDecimal getNextDayExpectedWastageByNow() {
		return nextDayExpectedWastageByNow;
	}

	public void setNextDayExpectedWastageByNow(BigDecimal nextDayExpectedWastageByNow) {
		this.nextDayExpectedWastageByNow = nextDayExpectedWastageByNow;
	}

	@Column(name = "UOM", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "COMMENT", nullable = true)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Column(name = "LAST_EVENT_ID", nullable = true)
	public Integer getLastDayCloseEvent() {
		return lastDayCloseEvent;
	}

	public void setLastDayCloseEvent(Integer lastDayCloseEvent) {
		this.lastDayCloseEvent = lastDayCloseEvent;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CURRENT_EVENT_ID", nullable = false)
	public SCMDayCloseEventData getCurrentDayCloseEvent() {
		return currentDayCloseEvent;
	}

	public void setCurrentDayCloseEvent(SCMDayCloseEventData currentDayCloseEvent) {
		this.currentDayCloseEvent = currentDayCloseEvent;
	}

    @Column(name = "STOCK_TYPE", nullable = false)
    public String getStockType() {
        return stockType;
    }

	public void setStockType(String stockType) {
        this.stockType = stockType;
    }

	@Column(name = "TAX_TYPE", nullable = false)
	public String getTaxType() {
		return taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

	@Column(name = "TAX_PERCENTAGE", nullable = false)
	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

	@Column(name = "TAX_VARIANCE", nullable = false)
	public BigDecimal getVarianceTax() {
		return varianceTax;
	}

	public void setVarianceTax(BigDecimal varianceTax) {
		this.varianceTax = varianceTax;
	}

	@Column(name = "ORIGINAL_TAX_VARIANCE", nullable = false)
	public BigDecimal getOriginalVarianceTax() {
		return originalVarianceTax;
	}

	public void setOriginalVarianceTax(BigDecimal originalVarianceTax) {
		this.originalVarianceTax = originalVarianceTax;
	}

	@Column(name = "CATEGORY_ID", nullable = false)
	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	@Column(name = "SUB_CATEGORY_ID", nullable = false)
	public Integer getSubCategoryId() {
		return subCategoryId;
	}

	public void setSubCategoryId(Integer subCategoryId) {
		this.subCategoryId = subCategoryId;
	}
}
