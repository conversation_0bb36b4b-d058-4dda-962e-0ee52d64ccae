package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.data.dao.SuggestiveOrderingStrategyDao;
import com.stpl.tech.scm.data.model.SuggestiveOrderingStrategyMetadata;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;

@Repository
public class SuggestiveOrderingStrategyDaoImpl extends SCMAbstractDaoImpl implements SuggestiveOrderingStrategyDao {

    @Override
    public SuggestiveOrderingStrategyMetadata findByUnitIdAndStatus(Integer unitId, SwitchStatus status) {
       String queryString = "SELECT DISTINCT sm FROM SuggestiveOrderingStrategyMetadata sm " +
                "LEFT JOIN FETCH sm.strategyMeanMetaDataSet " +
                "WHERE sm.unitId =:unitId AND sm.status =:status";

        Query query = manager.createQuery(queryString);
        query.setParameter("unitId", unitId).setParameter("status", status);

         try {
            return (SuggestiveOrderingStrategyMetadata) query.getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }
}
