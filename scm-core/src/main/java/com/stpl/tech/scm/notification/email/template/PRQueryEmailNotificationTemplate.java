/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.PaymentRequestQuery;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.List;
import java.util.Map;

public class PRQueryEmailNotificationTemplate extends AbstractVelocityTemplate {

    private List<PaymentRequestQuery> prQueries;
    private Map<String, Object> mapOfData;
    private String basePath;

    public PRQueryEmailNotificationTemplate() {
    }

    public PRQueryEmailNotificationTemplate(List<PaymentRequestQuery> prQueries, Map<String, Object> mapOfData, String basePath) {
        this.prQueries = prQueries;
        this.mapOfData = mapOfData;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "templates/PrQueryTemplate.html";
    }


    @Override
    public String getFilepath() {
        return basePath + "/prQuery/" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        mapOfData.put("prQueries", prQueries);
        return mapOfData;
    }
}
