package com.stpl.tech.scm.core.templates;

import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceDetails;
import com.stpl.tech.scm.domain.model.SalesPerformaCorrectedType;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class CorrectionCreditTemplate extends AbstractVelocityTemplate {

    private SalesPerformaInvoice invoice;
    private CorrectedSalesInvoiceDetails correctedInvoice;
    private String basePath;
    private BigDecimal netAmount;
    private BigDecimal totalTax;
    private Company company;
    private Unit unitData;
    private String barCodeLink;
    private String businessDate;
    private String creditNoteNo;


    public CorrectionCreditTemplate() {
    }

    public CorrectionCreditTemplate(SalesPerformaInvoice salesPerformaInvoice, CorrectedSalesInvoiceDetails correctedInvoice, String basePath,
                                    BigDecimal netAmount, BigDecimal totalTax, Company company, Unit unitData, String barCodeLink, String businessDate, String creditNoteNo) {
        this.invoice = salesPerformaInvoice;
        this.correctedInvoice = correctedInvoice;
        this.basePath = basePath;
        this.netAmount = netAmount;
        this.totalTax = totalTax;
        this.company=company;
        this.unitData=unitData;
        this.barCodeLink = barCodeLink;
        this.businessDate = businessDate;
        this.creditNoteNo = creditNoteNo;
    }


    @Override
    public String getTemplatePath() {
        return "templates/CorrectionCreditTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + SalesPerformaCorrectedType.CREDIT_NOTE.value()+"/"+ correctedInvoice.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("invoice", invoice);
        stringObjectMap.put("correctedInvoice", correctedInvoice);
        stringObjectMap.put("unitData",unitData);
        stringObjectMap.put("netAmount",netAmount);
        stringObjectMap.put("totalTax",totalTax);
        stringObjectMap.put("company",company);
        stringObjectMap.put("barCodeLink",barCodeLink);
        stringObjectMap.put("businessDate",businessDate);
        stringObjectMap.put("creditNoteNo",creditNoteNo);
        stringObjectMap.put("dateTool", new DateTool());
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("basePath", basePath);
        return stringObjectMap;
    }
}
