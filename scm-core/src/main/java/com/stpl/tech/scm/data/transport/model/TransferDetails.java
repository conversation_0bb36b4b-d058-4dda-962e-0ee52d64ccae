package com.stpl.tech.scm.data.transport.model;

public class TransferDetails {
    private String TaxSch;
    private String SupTyp;
    private String IgstOnIntra = "N";
    private String RegRev = "N";
    private Integer EcmGstin;

    public String getTaxSch() {
        return TaxSch;
    }

    public void setTaxSch(String taxSch) {
        TaxSch = taxSch;
    }

    public String getSupTyp() {
        return SupTyp;
    }

    public void setSupTyp(String supTyp) {
        SupTyp = supTyp;
    }

    public String getIgstOnIntra() {
        return IgstOnIntra;
    }

    public void setIgstOnIntra(String igstOnIntra) {
        IgstOnIntra = igstOnIntra;
    }

    public String getRegRev() {
        return RegRev;
    }

    public void setRegRev(String regRev) {
        RegRev = regRev;
    }

    public Integer getEcmGstin() {
        return EcmGstin;
    }

    public void setEcmGstin(Integer ecmGstin) {
        EcmGstin = ecmGstin;
    }
}
