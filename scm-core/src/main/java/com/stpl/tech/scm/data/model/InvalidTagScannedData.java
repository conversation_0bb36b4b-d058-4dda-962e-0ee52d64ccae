package com.stpl.tech.scm.data.model;

import org.joda.time.DateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import java.sql.Timestamp;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "INVALID_TAG_SCANNED")
public class InvalidTagScannedData {

    protected Integer id;
    protected Integer eventId;
    protected String tagValue;
    protected Date scanTime;

    public InvalidTagScannedData() {
    }

    public InvalidTagScannedData(Integer eventId, String tagValue, Date scanTime) {
        this.eventId = eventId;
        this.tagValue = tagValue;
        this.scanTime = scanTime;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "EVENT_ID", nullable = false)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "TAG_VALUE", nullable = false)
    public String getTagValue() {
        return tagValue;
    }

    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    @Column(name = "SCAN_TIME", nullable = false)
    public Date getScanTime() {
        return scanTime;
    }

    public void setScanTime(Date scanTime) {
        this.scanTime = scanTime;
    }
}
