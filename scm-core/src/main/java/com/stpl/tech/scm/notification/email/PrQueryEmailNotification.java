/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.PRQueryEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.Set;

public class PrQueryEmailNotification extends EmailNotification {

    private PRQueryEmailNotificationTemplate notificationTemplate;
    private EnvType envType;
    private Set<String> emails;

    public PrQueryEmailNotification() {
    }

    public PrQueryEmailNotification(PRQueryEmailNotificationTemplate notificationTemplate, EnvType envType, Set<String> emails) {
        this.notificationTemplate = notificationTemplate;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        String[] simpleArray = new String[emails.size()];
        return SCMUtil.isDev(envType) ? new String[]{"<EMAIL>"} : emails.toArray(simpleArray);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Payment Request Query PR ID : " + notificationTemplate.getData().get("prId");
        if ( notificationTemplate.getData().get("isQueried").equals(Boolean.FALSE)) {
            subject += " - Resolved";
        }
        if (SCMUtil.isDev(envType)) {
            subject = "Dev " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return notificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
