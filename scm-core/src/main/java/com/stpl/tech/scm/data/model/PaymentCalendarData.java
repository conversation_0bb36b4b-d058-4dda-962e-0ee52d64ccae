package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "PAYMENT_CALENDAR")
public class PaymentCalendarData {

    private Integer id;
    private String name;
    private Date paymentDate;
    private Date prCreationDate;
    private Date invoiceDate;
    private Integer cycleTag;
    private Integer companyId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_CALENDAR_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "CYCLE_NAME", nullable = false, length = 100, unique = true)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "PAYMENT_DATE", nullable = false)
    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    @Column(name = "PR_CREATION_DATE", nullable = false)
    public Date getPrCreationDate() {
        return prCreationDate;
    }

    public void setPrCreationDate(Date prCreationDate) {
        this.prCreationDate = prCreationDate;
    }

    @Column(name = "INVOICE_DATE", nullable = false)
    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    @Column(name = "CYCLE_TAG", nullable = false)
    public Integer getCycleTag() {
        return cycleTag;
    }

    public void setCycleTag(Integer cycleTag) {
        this.cycleTag = cycleTag;
    }
    
	@Column(name = "COMPANY_ID")
	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}
	
}
