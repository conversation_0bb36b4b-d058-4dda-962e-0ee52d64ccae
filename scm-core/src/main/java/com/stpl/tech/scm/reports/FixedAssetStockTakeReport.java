package com.stpl.tech.scm.reports;

import com.stpl.tech.scm.core.exception.StockTakeException;
import com.stpl.tech.util.EnvType;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import java.io.IOException;
import java.util.Date;

public interface FixedAssetStockTakeReport  {

    public void renderSummary() throws StockTakeException;

    public void renderNotFound() throws StockTakeException;

    public void renderScanned() throws StockTakeException;

    public void renderFoundExtra() throws StockTakeException;

    public void renderRecoveredAssets() throws StockTakeException;

    public void generateReport(String filePath, byte[] content) throws IOException;

    public int getEventId();

    public String getEventSubtype();

    public String getEmailId();

    public String getUnitName();

    public boolean isGenerated();

    public String getFileName();

    public String getMimeType();

    public WorkbookContext getWorkbook();

    public String getFilePath();

    public Date getBusinessDate();

    public EnvType getEnv();
}
