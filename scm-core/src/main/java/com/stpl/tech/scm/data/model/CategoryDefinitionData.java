/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * CategoryDefinitionData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CATEGORY_DEFINITION", uniqueConstraints = @UniqueConstraint(columnNames = "CATEGORY_NAME"))
public class CategoryDefinitionData implements java.io.Serializable {

    private Integer categoryId;
    private String categoryName;
    private String categoryCode;
    private String categoryDescription;
    private String categoryStatus;
    private List<SubCategoryDefinitionData> subCategoryDefinitionData;

    public CategoryDefinitionData() {
    }

    public CategoryDefinitionData(String categoryName, String categoryCode, String categoryStatus) {
        this.categoryName = categoryName;
        this.categoryCode = categoryCode;
        this.categoryStatus = categoryStatus;
    }

    public CategoryDefinitionData(String categoryName, String categoryCode, String categoryDescription,
                                  String categoryStatus) {
        this.categoryName = categoryName;
        this.categoryCode = categoryCode;
        this.categoryDescription = categoryDescription;
        this.categoryStatus = categoryStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "CATEGORY_ID", unique = true, nullable = false)
    public Integer getCategoryId() {
        return this.categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    @Column(name = "CATEGORY_NAME", unique = true, nullable = false)
    public String getCategoryName() {
        return this.categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    @Column(name = "CATEGORY_CODE", nullable = false, length = 50)
    public String getCategoryCode() {
        return this.categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    @Column(name = "CATEGORY_DESCRIPTION", length = 1000)
    public String getCategoryDescription() {
        return this.categoryDescription;
    }

    public void setCategoryDescription(String categoryDescription) {
        this.categoryDescription = categoryDescription;
    }

    @Column(name = "CATEGORY_STATUS", nullable = false, length = 15)
    public String getCategoryStatus() {
        return this.categoryStatus;
    }

    public void setCategoryStatus(String categoryStatus) {
        this.categoryStatus = categoryStatus;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "LINKED_CATEGORY_ID", nullable = true)
    public List<SubCategoryDefinitionData> getSubCategoryDefinitionData() {
        return subCategoryDefinitionData;
    }

    public void setSubCategoryDefinitionData(List<SubCategoryDefinitionData> subCategoryDefinitionData) {
        this.subCategoryDefinitionData = subCategoryDefinitionData;
    }
}
