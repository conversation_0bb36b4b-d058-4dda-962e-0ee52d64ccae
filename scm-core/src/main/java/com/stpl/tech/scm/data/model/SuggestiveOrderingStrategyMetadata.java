package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.core.util.model.StrategyType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Set;

@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "SUGGESTIVE_ORDERING_STRATEGY_METADATA")
public class SuggestiveOrderingStrategyMetadata {

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "STRATEGY_TYPE")
    @Enumerated(EnumType.STRING)
    private StrategyType strategyType;

    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    private SwitchStatus status;

    @Column(name = "NO_OF_WEEKS")
    private Integer noOfWeeks;

    @Column(name = "SAFETY_WEEKS")
    private Integer safetyWeeks;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "suggestiveOrderingStrategy")
    private Set<StrategyMeanMetaData> strategyMeanMetaDataSet;

}
