package com.stpl.tech.scm.data.model;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "PURCHASE_ORDER_LOG")
public class PurchaseOrderLog {
    private int id ;
    private BigDecimal previousPackagingQty ;
    private BigDecimal acceptedPackagingQty ;
    private int poId ;
    private Date lastUpdateTime ;
    private int lastUpdatedBy ;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PURCHASE_ORDER_LOG_ID" , nullable = false, unique = true)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    @Column(name = "PREVIOUS_PACKAGING_QTY")
    public BigDecimal getPreviousPackagingQty() {
        return previousPackagingQty;
    }

    public void setPreviousPackagingQty(BigDecimal previousPackagingQty) {
        this.previousPackagingQty = previousPackagingQty;
    }
    @Column(name = "ACCEPTED_PACKAGING_QTY")
    public BigDecimal getAcceptedPackagingQty() {
        return acceptedPackagingQty;
    }

    public void setAcceptedPackagingQty(BigDecimal acceptedPackagingQty) {
        this.acceptedPackagingQty = acceptedPackagingQty;
    }
    @Column(name = "PURCHASE_ORDER_ITEM_DATA_ID")
    public int getPoId() {
        return poId;
    }

    public void setPoId(int poId) {
        this.poId = poId;
    }
    @Column(name = "LAST_UPDATE_TIME")
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
    @Column(name = "LAST_UPDATED_BY")
    public int getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(int lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

}