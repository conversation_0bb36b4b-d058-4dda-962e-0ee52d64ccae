package com.stpl.tech.scm.data.transport.model;

import java.math.BigDecimal;

public class ValueDetails {

    private BigDecimal AssVal;
    private BigDecimal IgstVal;
    private BigDecimal CgstVal;
    private BigDecimal SgstVal;
    private BigDecimal CesVal;
    private BigDecimal StCesVal;
    private BigDecimal Discount;
    private BigDecimal OthChrg;
    private BigDecimal RndOffAmt;
    private BigDecimal TotInvVal;

    public BigDecimal getAssVal() {
        return AssVal;
    }

    public void setAssVal(BigDecimal assVal) {
        AssVal = assVal;
    }

    public BigDecimal getIgstVal() {
        return IgstVal;
    }

    public void setIgstVal(BigDecimal igstVal) {
        IgstVal = igstVal;
    }

    public BigDecimal getCgstVal() {
        return CgstVal;
    }

    public void setCgstVal(BigDecimal cgstVal) {
        CgstVal = cgstVal;
    }

    public BigDecimal getSgstVal() {
        return SgstVal;
    }

    public void setSgstVal(BigDecimal sgstVal) {
        SgstVal = sgstVal;
    }

    public BigDecimal getCesVal() {
        return CesVal;
    }

    public void setCesVal(BigDecimal cesVal) {
        CesVal = cesVal;
    }

    public BigDecimal getStCesVal() {
        return StCesVal;
    }

    public void setStCesVal(BigDecimal stCesVal) {
        StCesVal = stCesVal;
    }

    public BigDecimal getDiscount() {
        return Discount;
    }

    public void setDiscount(BigDecimal discount) {
        Discount = discount;
    }

    public BigDecimal getOthChrg() {
        return OthChrg;
    }

    public void setOthChrg(BigDecimal othChrg) {
        OthChrg = othChrg;
    }

    public BigDecimal getRndOffAmt() {
        return RndOffAmt;
    }

    public void setRndOffAmt(BigDecimal rndOffAmt) {
        RndOffAmt = rndOffAmt;
    }

    public BigDecimal getTotInvVal() {
        return TotInvVal;
    }

    public void setTotInvVal(BigDecimal totInvVal) {
        TotInvVal = totInvVal;
    }
}
