package com.stpl.tech.scm.core.service.impl;

import java.util.List;

public class RegularOrderUnitBrand {

    private Integer id;
    private Integer unitId;
    private Integer brandId;
    private String manual;
    private Boolean functional;
    private List<UnitOrderSchedule> unitOrderSchedules;

    public RegularOrderUnitBrand() {
    }

    public RegularOrderUnitBrand(Integer id, Integer unitId, Integer brandId) {
        this.id = id;
        this.unitId = unitId;
        this.brandId = brandId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getManual() {
        return manual;
    }

    public void setManual(String manual) {
        this.manual = manual;
    }

    public Boolean getFunctional() {
        return functional;
    }

    public void setFunctional(Boolean functional) {
        this.functional = functional;
    }

    public List<UnitOrderSchedule> getUnitOrderSchedules() {
        return unitOrderSchedules;
    }

    public void setUnitOrderSchedules(List<UnitOrderSchedule> unitOrderSchedules) {
        this.unitOrderSchedules = unitOrderSchedules;
    }
}
