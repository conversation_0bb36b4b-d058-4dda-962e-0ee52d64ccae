package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "REGULAR_ORDER_UNIT_BRAND_DATA")
public class RegularOrderUnitBrandData {

    private Integer id;
    private Integer unitId;
    private Integer brandId;
    private String maxCap;
    private String isFunctional;
    private String isManual = "Y";
    private Integer createdBy;
    private Date createdAt;
    private Integer lastUpdatedBy;
    private Date lastUpdatedTime;
    private List<UnitOrderScheduleData> unitOrderScheduleData = new ArrayList<>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UNIT_BRAND_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "BRAND_ID", nullable = false)
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "CAPPING_STOCK_MULTIPLIER", nullable = true)
    public String getMaxCap() {
        return maxCap;
    }

    public void setMaxCap(String maxCap) {
        this.maxCap = maxCap;
    }

    @Column(name = "IS_FUNCTIONAL", nullable = false)
    public String getIsFunctional() {
        return isFunctional;
    }

    public void setIsFunctional(String isFunctional) {
        this.isFunctional = isFunctional;
    }

    @Column(name = "IS_MANUAL", nullable = false)
    public String getIsManual() {
        return isManual;
    }

    public void setIsManual(String isManual) {
        this.isManual = isManual;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATED_AT", nullable = true)
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = true)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "LAST_UPDATED_TIME", nullable = true)
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "regularOrderUnitBrandData")
    public List<UnitOrderScheduleData> getUnitOrderScheduleData() {
        return unitOrderScheduleData;
    }

    public void setUnitOrderScheduleData(List<UnitOrderScheduleData> unitOrderScheduleData) {
        this.unitOrderScheduleData = unitOrderScheduleData;
    }
}
