/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.*;

/**
 * ProductRecipeCost generated by hbm2java
 */
@SuppressWarnings("serial")
@MappedSuperclass
public class CostDetailAuditDrillDown implements java.io.Serializable {

	private Integer costDetailAuditDataDrilldownId;
	private int costDetailAuditDataId;
	private int keyId;
	private String keyType;
	private BigDecimal quantity;
	private BigDecimal price;
	private String uom;
	private Date addTime;
	private Date expiryDate;
	private String cancellation;

	/**
	 * @return the productRecipeCostId
	 */
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COST_DETAIL_DATA_AUDIT_DRILLDOWN_ID", unique = true, nullable = false)
	public Integer getCostDetailAuditDataDrilldownId() {
		return costDetailAuditDataDrilldownId;
	}

	/**
	 * @param costDetailDataId
	 *            the costDetailDataId to set
	 */
	public void setCostDetailAuditDataDrilldownId(Integer costDetailAuditDataDrilldownId) {
		this.costDetailAuditDataDrilldownId = costDetailAuditDataDrilldownId;
	}

	@Column(name = "COST_DETAIL_DATA_AUDIT_ID", nullable = false)
	public int getCostDetailAuditDataId() {
		return costDetailAuditDataId;
	}

	public void setCostDetailAuditDataId(int costDetailAuditDataId) {
		this.costDetailAuditDataId = costDetailAuditDataId;
	}
	
	@Column(name = "KEY_ID")
	public int getKeyId() {
		return keyId;
	}

	public void setKeyId(int keyId) {
		this.keyId = keyId;
	}
	@Column(name = "KEY_TYPE")
	public String getKeyType() {
		return keyType;
	}

	public void setKeyType(String keyType) {
		this.keyType = keyType;
	}
	@Column(name = "QUANTITY", precision = 16)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	@Column(name = "PRICE", precision = 16)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal cost) {
		this.price = cost;
	}
	
	@Column(name = "UNIT_OF_MEASURE", nullable = false)
	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Column(name = "IS_CANCELLATION", nullable = false)
	public String getCancellation() {
		return cancellation;
	}

	public void setCancellation(String cancellation) {
		this.cancellation = cancellation;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXPIRY_DATE", nullable = false, length = 10)
	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

}