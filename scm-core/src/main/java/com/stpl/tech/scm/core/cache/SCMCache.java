/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.cache;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.service.RedisCacheService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.redis.dao.AssetDefinitionDao;
import com.stpl.tech.scm.data.model.CapexTemplateData;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ListDetail;
import com.stpl.tech.scm.domain.model.MonkWastageDetail;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.PendingMilkBread;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.ProfileAttributeMapping;
import com.stpl.tech.scm.domain.model.ProfileDefinition;
import com.stpl.tech.scm.domain.model.SCMUnitCategory;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 04-05-2016.
 */

@Service
public class SCMCache {

	private static final Logger LOG = LoggerFactory.getLogger(SCMCache.class);

	@Autowired
	private RedisCacheService redisCacheService;

	private Map<Integer, AttributeDefinition> attributeDefinitions = new TreeMap<Integer, AttributeDefinition>();
	private Map<Integer, CategoryDefinition> categoryDefinitions = new TreeMap<Integer, CategoryDefinition>();
	private Map<Integer, SubCategoryDefinition> subCategoryDefinitions = new TreeMap<Integer, SubCategoryDefinition>();
	private Map<Integer, PackagingDefinition> packagingDefinitions = new TreeMap<Integer, PackagingDefinition>();
	private Map<UnitOfMeasure, PackagingDefinition> loosePackagingDefinitions = new TreeMap<UnitOfMeasure, PackagingDefinition>();
	private Map<Integer, CategoryAttributeMapping> categoryAttributeMappings = new TreeMap<Integer, CategoryAttributeMapping>();
	private Map<Integer, ProductDefinition> productDefinitions = new TreeMap<Integer, ProductDefinition>();
	private Map<Integer, ProfileDefinition> profileDefinitions = new TreeMap<Integer, ProfileDefinition>();
  	private Map<Integer, List<ProfileAttributeMapping>> profileAttributeMappings = new TreeMap<Integer, List<ProfileAttributeMapping>>();
	private Map<Integer, ProductPriceData> productPrices = new TreeMap<Integer, ProductPriceData>();
	private Map<Integer, SkuDefinition> skuDefinitions = new TreeMap<Integer, SkuDefinition>();
	private Map<Integer, List<SkuDefinition>> skusByProductId = new TreeMap<Integer, List<SkuDefinition>>();
	private Map<Integer, UnitDetail> unitDetails = new TreeMap<Integer, UnitDetail>();
	private Map<Integer, SCMUnitCategory> unitCategories = new TreeMap<Integer, SCMUnitCategory>();
	private Map<Integer, AttributeValue> attributeValues = new TreeMap<Integer, AttributeValue>();
	private Map<Integer, CategoryAttributeValue> categoryAttributeValues = new TreeMap<Integer, CategoryAttributeValue>();
	private Map<Integer, ProductPackagingMapping> productPackagingMappings = new TreeMap<Integer, ProductPackagingMapping>();
	private Map<Integer, SkuPackagingMapping> skuPackagingMappings = new TreeMap<Integer, SkuPackagingMapping>();
	private Map<Integer, SkuAttributeValue> skuAttributeValues = new TreeMap<Integer, SkuAttributeValue>();
	private Map<Integer, VendorDetail> vendorDetails = new TreeMap<Integer, VendorDetail>();
	private Map<String, Location> locationDetails = new TreeMap<String, Location>();
	private Map<String, State> stateDetails = new TreeMap<String, State>();
	private Map<Integer, IdCodeName> inventoryLists = new TreeMap<>();
	private Map<String, Integer> fulfillmentUnitMapping = new TreeMap<>();
	private Map<Integer, Set<Integer>> availableProductMapping = new TreeMap<>();
	private Map<Integer, Set<Integer>> availableSKUMapping = new TreeMap<>();
	private Map<String, BigDecimal> unitDistanceMapping = new TreeMap<>();
	private Map<String, BigDecimal> zipCodeDistanceMapping = new TreeMap<>();
	private Map<Integer, List<String>> unitWiseMonkWastageIdentity = new HashMap<>();
	private Map<String, List<ListDetail>> listDetail = new HashMap<String, List<ListDetail>>();
	private Map<String, CapexTemplateData> capexTemplate = new HashMap<String, CapexTemplateData>();
	private Map<Integer, Map<Integer, ProductRecipeKey>> unitProductProfileMapping = new HashMap<Integer, Map<Integer,ProductRecipeKey>>();
	private Map<String,Integer> regionFulfillmentMapping = new HashMap<>();
	private Map<Integer, PendingMilkBread> pendingMilkBread = new HashMap<>();
	private String stockTakeAppVersion = "1.2.2";
	private List<AssetDefinition> assetDefinitionUnitMapping = new ArrayList<>();

	@Autowired
	private SCMMetadataService scmMetadataService;

	@Autowired
	private SCMAssetManagementService scmAssetManagementService;

	@Autowired
	private PaymentRequestManagementService paymentRequestManagementService;

	@Autowired
	private SCMProductManagementService scmProductManagementService;

	public SCMCache() {

	}

	public void clearCache() {
		attributeDefinitions = new TreeMap<Integer, AttributeDefinition>();
		categoryDefinitions = new TreeMap<Integer, CategoryDefinition>();
		subCategoryDefinitions = new TreeMap<Integer, SubCategoryDefinition>();
		packagingDefinitions = new TreeMap<Integer, PackagingDefinition>();
		loosePackagingDefinitions = new HashMap<UnitOfMeasure, PackagingDefinition>();
		categoryAttributeMappings = new TreeMap<Integer, CategoryAttributeMapping>();
		productDefinitions = new TreeMap<Integer, ProductDefinition>();
		profileDefinitions = new TreeMap<Integer, ProfileDefinition>();
		profileAttributeMappings = new TreeMap<Integer, List<ProfileAttributeMapping>>();
		productPrices = new HashMap<>();
		skuDefinitions = new TreeMap<Integer, SkuDefinition>();
		unitDetails = new TreeMap<Integer, UnitDetail>();
		unitCategories = new TreeMap<Integer, SCMUnitCategory>();
		attributeValues = new TreeMap<Integer, AttributeValue>();
		categoryAttributeValues = new TreeMap<Integer, CategoryAttributeValue>();
		productPackagingMappings = new TreeMap<Integer, ProductPackagingMapping>();
		skuPackagingMappings = new TreeMap<Integer, SkuPackagingMapping>();
		skuAttributeValues = new TreeMap<Integer, SkuAttributeValue>();
		vendorDetails = new TreeMap<Integer, VendorDetail>();
		locationDetails = new TreeMap<>();
		stateDetails = new TreeMap<>();
		inventoryLists = new TreeMap<>();
		fulfillmentUnitMapping = new TreeMap<>();
		availableProductMapping = new TreeMap<>();
		availableSKUMapping = new TreeMap<>();
		unitDistanceMapping = new TreeMap<>();
		unitWiseMonkWastageIdentity = new HashMap<>();
		listDetail = new HashMap<>();
		capexTemplate = new HashMap<>();
		regionFulfillmentMapping = new HashMap<>();
		pendingMilkBread = new HashMap<>();
		stockTakeAppVersion = "1.2.2";
		skusByProductId = new TreeMap<>();
	}

	@PostConstruct
	public void loadCache() {
		LOG.info("\nCreating ############# AttributeDefinition cache" );
		for (AttributeDefinition ad : scmMetadataService.getAllAttributeDefinitions()) {
			attributeDefinitions.put(ad.getAttributeId(), ad);
		}
		LOG.info("\nCreating ############# CategoryDefinition cache" );
		for (CategoryDefinition cd : scmMetadataService.getAllCategoryDefinitions()) {
			categoryDefinitions.put(cd.getCategoryId(), cd);
		}
		LOG.info("\nCreating ############# SubCategoryDefinition cache" );
		for (SubCategoryDefinition scd : scmMetadataService.getAllSubCategoryDefinitions()) {
			subCategoryDefinitions.put(scd.getSubCategoryId(), scd);
		}
		LOG.info("\nCreating ############# PackagingDefinition cache" );
		for (PackagingDefinition pd : scmMetadataService.getAllPackagingDefinitions()) {
			packagingDefinitions.put(pd.getPackagingId(), pd);
			if (pd.getPackagingType().equals(PackagingType.LOOSE)) {
				loosePackagingDefinitions.put(UnitOfMeasure.valueOf(pd.getPackagingCode()), pd);
			}
		}
		LOG.info("\nCreating ############# CategoryAttributeMapping cache" );
		for (CategoryAttributeMapping cam : scmMetadataService.getAllCategoryAttributeMappings()) {
			categoryAttributeMappings.put(cam.getCategoryAttributeMappingId(), cam);
		}
		LOG.info("\nCreating ############# ProfileDefinition cache" );
		for (ProfileDefinition pd : scmMetadataService.getAllProfileDefinitions()){
			profileDefinitions.put(pd.getProfileId(), pd);
		}
		LOG.info("\nCreating ############# ProductDefinition cache" );
		for (ProductDefinition pd : scmMetadataService.getAllProductDefinitions()) {
			productDefinitions.put(pd.getProductId(), pd);
			productPrices.put(pd.getProductId(), SCMDataConverter.convertToPrice(pd));
		}
		LOG.info("\nCreating ############# SkuDefinition cache" );
		for (SkuDefinition sd : scmMetadataService.getAllSkuDefinitions()) {
			skuDefinitions.put(sd.getSkuId(), sd);
		}
		LOG.info("\nCreating ############# UnitDetail cache" );
		for (UnitDetail ud : scmMetadataService.getAllUnitDetails()) {
			unitDetails.put(ud.getUnitId(), ud);
		}
		LOG.info("\nCreating ############# SCMUnitCategory cache" );
		for (SCMUnitCategory uc : scmMetadataService.getAllUnitCategories()) {
			unitCategories.put(uc.getCategoryId(), uc);
		}
		LOG.info("\nCreating ############# AttributeValue cache" );
		for (AttributeValue av : scmMetadataService.getAllAttributeValues()) {
			attributeValues.put(av.getAttributeValueId(), av);
		}
		LOG.info("\nCreating ############# CategoryAttributeValue cache" );
		for (CategoryAttributeValue cav : scmMetadataService.getAllCategoryAttributeValues()) {
			categoryAttributeValues.put(cav.getCategoryAttributeValueId(), cav);
		}
		LOG.info("\nCreating ############# ProductPackagingMapping cache" );
		for (ProductPackagingMapping ppm : scmMetadataService.getAllProductPackginMappings()) {
			productPackagingMappings.put(ppm.getProductPackagingMappingId(), ppm);
		}
		LOG.info("\nCreating ############# SkuPackagingMapping cache" );
		for (SkuPackagingMapping spm : scmMetadataService.getAllSkuPackagingMappings()) {
			skuPackagingMappings.put(spm.getSkuPackagingMappingId(), spm);
		}
		LOG.info("\nCreating ############# SkuAttributeValue cache" );
		for (SkuAttributeValue sav : scmMetadataService.getAllSkuAttributeValues()) {
			skuAttributeValues.put(sav.getSkuAttributeValueId(), sav);
		}
		LOG.info("\nCreating ############# VendorDetail cache" );
		for (VendorDetail vd : scmMetadataService.getAllVendorDetails()) {
			vendorDetails.put(vd.getVendorId(), vd);
		}
		LOG.info("\nCreating ############# Location cache" );
		for (Location vd : scmMetadataService.getAllLocations()) {
			locationDetails.put(vd.getCode(), vd);
		}
		LOG.info("\nCreating ############# State cache" );
		for (State vd : scmMetadataService.getAllStates()) {
			stateDetails.put(vd.getCode(), vd);
		}
		LOG.info("\nCreating ############# InventoryList cache" );
		for (IdCodeName list : scmMetadataService.getAllInventoryLists()) {
			inventoryLists.put(list.getId(), list);
		}
		LOG.info("\nCreating ############# InventoryList cache" );
		for (IdCodeName list : scmMetadataService.getAllInventoryLists()) {
			inventoryLists.put(list.getId(), list);
		}
		LOG.info("\nCreating ############# FulfillmentMapping cache" );
		refreshFulfillmentMapping();
		LOG.info("\nCreating ############# AvailableProductMapping cache" );
		refreshProductRecipeKeyMapping();
		LOG.info("\nCreating ############# ProductRecipeMapping cache" );
		refreshAvailableProductMapping();
		LOG.info("\nCreating ############# UnitDistanceMapping cache" );
		refreshUnitDistanceMapping();
		LOG.info("\nCreating ############# ZipCodeDistanceMapping cache" );
		refreshZipCodeDistanceMapping();
		LOG.info("\nCreating ############# ProfileAttributeMapping cache" );
		refreshProfileAttributeMapping();
		refreshListDetails();
		LOG.info("\nCreating ############# Capex Template cache" );
		for (CapexTemplateData cp : scmMetadataService.getAllCapexTemplates()) {
			capexTemplate.put(cp.getType(), cp);
		}
		LOG.info("\nCreating ############# RegionFulFillmentMapping cache");
		refreshRegionFulfillmentMapping();
		LOG.info("\nCreating ############# Pending Milk Bread cache");
		refreshPendingMilkBread();
		LOG.info("\nUpdating ############# Vendor Block cache" );
		paymentRequestManagementService.refreshVendorAdvancePaymentsCache(null);
		LOG.info("\nUpdating ############# Stock Take App Version" );
		refreshStockTakeAppVersion();
		// AppUtils.printHeapSize();
		LOG.info("\nCreating ############# Asset Definition cache" );
		refreshAssetDefinitionUnitMapping();
		LOG.info("\nCreating ############# Skus By ProductId cache" );
		refreshSkusByProductIdCache();
	}

	public void refreshAssetDefinitionUnitMapping() {
		redisCacheService.initialAssetCacheLoad();
	}

	public void forceRefreshAssetCache() {
		redisCacheService.reloadAssetCache();
	}

	public List<AssetDefinition> getAllAssets() {
		return redisCacheService.getAllAssets();
	}

	public List<AssetDefinition> getAssetsForUnit(Integer unitId) {
		 return redisCacheService.getAssetsByUnitId(unitId);
	}

	public AssetDefinition getAssetByAssetId(Integer assetId) {
		return redisCacheService.getAssetByAssetId(assetId);
	}

	public void updateAssetToCache(AssetDefinition assetDefinition){
		redisCacheService.updateAssetToCache(assetDefinition);
	}

	public void refreshStockTakeAppVersion() {
		stockTakeAppVersion = "1.2.2";
		stockTakeAppVersion = scmAssetManagementService.getStockTakeAppVersion(true);
	}

	public void refreshListDetails() {
		listDetail = new HashMap<String, List<ListDetail>>();
		for(Entry<String, List<ListDetail>> entry : scmMetadataService.getListDetails().entrySet()) {
			listDetail.put(entry.getKey(), (List<ListDetail>) entry.getValue());
		}
	}

	private void refreshProfileAttributeMapping() {
		profileAttributeMappings = new TreeMap<Integer, List<ProfileAttributeMapping>>();
		for(Map.Entry<Integer, List<ProfileAttributeMapping>> entry: scmMetadataService.getProfileAttributeMappings().entrySet()) {
			Integer key = entry.getKey();
			List<ProfileAttributeMapping> mappings = entry.getValue();
			profileAttributeMappings.put(key, mappings);
		}
	}

	public void refreshRegionFulfillmentMapping()  {
		regionFulfillmentMapping = new HashMap<>();
		regionFulfillmentMapping = scmMetadataService.getRegionFulfillmentMapping();
	}

	public void refreshPendingMilkBread()  {
		pendingMilkBread = new HashMap<>();
		pendingMilkBread = scmMetadataService.getPendingMilkBread();
	}

	public void refreshUnitDistanceMapping() {
		unitDistanceMapping = new TreeMap<>();
		Map<String, BigDecimal> map = scmMetadataService.getUnitDistanceMapping();
		for (String s : map.keySet()) {
			unitDistanceMapping.put(s, map.get(s));
		}
	}

	public void refreshZipCodeDistanceMapping() {
		zipCodeDistanceMapping = new TreeMap<>();
		Map<String, BigDecimal> map = scmMetadataService.getZipCodeDistanceMapping();
		for (String s : map.keySet()) {
			zipCodeDistanceMapping.put(s, map.get(s));
		}
	}

	public void refreshAvailableProductMapping() {
		availableProductMapping = new TreeMap<>();

		Map<Integer, Set<Integer>> m = scmMetadataService.getUnitToAvailableProductMap();
		for (Integer i : m.keySet()) {
			availableProductMapping.put(i, m.get(i));
		}

		availableSKUMapping = new TreeMap<>();
		m = scmMetadataService.getUnitToAvailableSKUMap();
		for (Integer i : m.keySet()) {
			availableSKUMapping.put(i, m.get(i));
		}
	}

	public void  refreshFulfillmentMapping() {
		fulfillmentUnitMapping = new TreeMap<>();
		Map<String, Integer> m = scmMetadataService.getFulfillmentTypeMap();
		for (String s : m.keySet()) {
			fulfillmentUnitMapping.put(s, m.get(s));
		}
	}

	public void refreshProductRecipeKeyMapping() {
		unitProductProfileMapping = new TreeMap<>();
		unitProductProfileMapping = scmMetadataService.getProductProfileMapping();
	}

	public void refreshCategory(){
		for (CategoryDefinition cd : scmMetadataService.getAllCategoryDefinitions()) {
			categoryDefinitions.put(cd.getCategoryId(), cd);
		}

		for (SubCategoryDefinition scd : scmMetadataService.getAllSubCategoryDefinitions()) {
			subCategoryDefinitions.put(scd.getSubCategoryId(), scd);
		}
	}

	public Map<Integer, AttributeDefinition> getAttributeDefinitions() {
		return attributeDefinitions;
	}

	public Map<Integer, CategoryDefinition> getCategoryDefinitions() {
		return categoryDefinitions;
	}

	public Map<Integer, PackagingDefinition> getPackagingDefinitions() {
		return packagingDefinitions;
	}

	public Map<Integer, ProductDefinition> getProductDefinitions() {
		return productDefinitions;
	}

	public ProductDefinition getProductDefinition(int productId) {
		return productDefinitions.get(productId);
	}

	public Map<Integer, ProfileDefinition> getProfileDefinitions() {
		return profileDefinitions;
	}

	public Map<Integer, List<ProfileAttributeMapping>> getProfileAttributeMappings() {
		return profileAttributeMappings;
	}

	public ProfileDefinition getProfileDefinition(int productId) {
		return profileDefinitions.get(productId);
	}

	public List<ProfileAttributeMapping> getProfileAttributeMappings(int profileId) {
		return profileAttributeMappings.get(profileId);
	}

    /**
	 * @return the productPrices
	 */
	public Map<Integer, ProductPriceData> getProductPrices() {
		return productPrices;
	}

	public Map<Integer, SkuDefinition> getSkuDefinitions() {
		return skuDefinitions;
	}

	public Map<Integer, UnitDetail> getUnitDetails() {
		return unitDetails;
	}

	public Map<Integer, CategoryAttributeMapping> getCategoryAttributeMappings() {
		return categoryAttributeMappings;
	}

	public Map<Integer, AttributeValue> getAttributeValues() {
		return attributeValues;
	}

	public Map<Integer, CategoryAttributeValue> getCategoryAttributeValues() {
		return categoryAttributeValues;
	}

	public Map<Integer, ProductPackagingMapping> getProductPackagingMappings() {
		return productPackagingMappings;
	}

	public Map<Integer, SkuPackagingMapping> getSkuPackagingMappings() {
		return skuPackagingMappings;
	}

	public Map<Integer, SkuAttributeValue> getSkuAttributeValues() {
		return skuAttributeValues;
	}

	public Map<Integer, SubCategoryDefinition> getSubCategoryDefinitions() {
		return subCategoryDefinitions;
	}

	public Map<Integer, SCMUnitCategory> getUnitCategories() {
		return unitCategories;
	}

	public Map<Integer, VendorDetail> getVendorDetails() {
		return vendorDetails;
	}

	public Map<String, Location> getlocationDetails() {
		return locationDetails;
	}

	public Map<String, State> getStateDetails() {
		return stateDetails;
	}

	public VendorDetail getVendorDetail(int vendorId) {
		return vendorDetails.get(vendorId);
	}

	public IdCodeName getLocationDetail(String locationCode) {
		Location loc = locationDetails.get(locationCode);
		IdCodeName data = new IdCodeName();
		if (loc != null) {
			data.setCode(loc.getCode());
			data.setId(loc.getId());
			data.setName(loc.getName());
		}
		return data;
	}

	public Location fetchLocationDetail(String locationCode) {
		return locationDetails.get(locationCode);
	}

	public IdCodeName getStateDetail(String stateCode) {
		State loc = stateDetails.get(stateCode);
		IdCodeName data = new IdCodeName();
		data.setCode(loc.getCode());
		data.setId(loc.getId());
		data.setName(loc.getName());
		return data;
	}

	public void setAttributeDefinitions(Map<Integer, AttributeDefinition> attributeDefinitions) {
		this.attributeDefinitions = attributeDefinitions;
	}

	public void setCategoryDefinitions(Map<Integer, CategoryDefinition> categoryDefinitions) {
		this.categoryDefinitions = categoryDefinitions;
	}

	public void setSubCategoryDefinitions(Map<Integer, SubCategoryDefinition> subCategoryDefinitions) {
		this.subCategoryDefinitions = subCategoryDefinitions;
	}

	public void setPackagingDefinitions(Map<Integer, PackagingDefinition> packagingDefinitions) {
		this.packagingDefinitions = packagingDefinitions;
	}

	public void setCategoryAttributeMappings(Map<Integer, CategoryAttributeMapping> categoryAttributeMappings) {
		this.categoryAttributeMappings = categoryAttributeMappings;
	}

	public void setProductDefinitions(Map<Integer, ProductDefinition> productDefinitions) {
		this.productDefinitions = productDefinitions;
	}

	public void setProfileDefinitions(Map<Integer, ProfileDefinition> profileDefinitions) {
		this.profileDefinitions = profileDefinitions;
	}


	public void setProfileAttributeMappings(Map<Integer, List<ProfileAttributeMapping>> profileAttributeMappings) {
		this.profileAttributeMappings = profileAttributeMappings;
	}


    /**
	 * @param productPrices
	 *            the productPrices to set
	 */
	public void setProductPrices(Map<Integer, ProductPriceData> productPrices) {
		this.productPrices = productPrices;
	}

	public void setSkuDefinitions(Map<Integer, SkuDefinition> skuDefinitions) {
		this.skuDefinitions = skuDefinitions;
	}

	public void setUnitDetails(Map<Integer, UnitDetail> unitDetails) {
		this.unitDetails = unitDetails;
	}

	public void setUnitCategories(Map<Integer, SCMUnitCategory> unitCategories) {
		this.unitCategories = unitCategories;
	}

	public void setAttributeValues(Map<Integer, AttributeValue> attributeValues) {
		this.attributeValues = attributeValues;
	}

	public void setCategoryAttributeValues(Map<Integer, CategoryAttributeValue> categoryAttributeValues) {
		this.categoryAttributeValues = categoryAttributeValues;
	}

	public void setProductPackagingMappings(Map<Integer, ProductPackagingMapping> productPackagingMappings) {
		this.productPackagingMappings = productPackagingMappings;
	}

	public void setSkuPackagingMappings(Map<Integer, SkuPackagingMapping> skuPackagingMappings) {
		this.skuPackagingMappings = skuPackagingMappings;
	}

	public void setSkuAttributeValues(Map<Integer, SkuAttributeValue> skuAttributeValues) {
		this.skuAttributeValues = skuAttributeValues;
	}

	public void setVendorDetails(Map<Integer, VendorDetail> vendorDetails) {
		this.vendorDetails = vendorDetails;
	}

	public void setLocationDetails(Map<String, Location> locationDetails) {
		this.locationDetails = locationDetails;
	}

	public void setStateDetails(Map<String, State> stateDetails) {
		this.stateDetails = stateDetails;
	}

	/**
	 * @param packagingId
	 * @return
	 */
	public PackagingDefinition getPackagingDefinition(int packagingId) {
		return packagingDefinitions.get(packagingId);
	}

	/**
	 * @param uom
	 * @return
	 */
	public PackagingDefinition getLoosePackagingDefinition(UnitOfMeasure uom) {
		return loosePackagingDefinitions.get(uom);
	}

	/**
	 * @param skuId
	 * @return
	 */
	public SkuDefinition getSkuDefinition(int skuId) {
		return skuDefinitions.get(skuId);
	}

	//key is region-warehouse/kitchen
	public Integer getRegionFulfillmentMapping(String key) {
		return regionFulfillmentMapping.get(key);
	}

	public void setRegionFulfillmentMapping(String key , Integer unitId) {
		this.regionFulfillmentMapping.put(key,unitId);
	}

	public UnitDetail getUnitDetail(int unitId) {
		return unitDetails.get(unitId);
	}

	public Map<Integer, IdCodeName> getAllInventoryLists() {
		return inventoryLists;
	}

	public SkuDefinition getActiveSkuDefinition(Integer skuId) {
		SkuDefinition sku = getSkuDefinition(skuId);
		return sku.getSkuStatus().equals(SwitchStatus.ACTIVE) ? sku : null;
	}

	public List<SkuPackagingMapping> getSkuPackagingMappings(int skuId) {
		List<SkuPackagingMapping> pkgMappings = new ArrayList<>(getSkuPackagingMappings().values());
		return pkgMappings.stream().filter(
				mapping -> mapping.getMappingStatus().equals(SwitchStatus.ACTIVE) && mapping.getSkuId() == skuId)
				.collect(Collectors.toList());

	}

	public Integer getFulfillmentUnit(FulfillmentType type, int unitId) {
		return fulfillmentUnitMapping.get(SCMUtil.getkey(unitId, type.name()));
	}

	public Set<Integer> getAvailableProductForUnit(int unitId) {
		return availableProductMapping.getOrDefault(unitId,new HashSet<>());
	}

	public Set<Integer> getAvailableSKUForUnit(int unitId) {
		return availableSKUMapping.get(unitId);
	}

	public BigDecimal getUnitDistanceMapping(int u1, int u2) {
		BigDecimal d = null;
		d = unitDistanceMapping.get(u1 + "" + u2);
		if (d == null) {
			d = unitDistanceMapping.get(u2 + "" + u1);
		}
		return d;
	}

	public BigDecimal getZipCodeDistanceMapping(String z1, String z2) {
		BigDecimal d = null;
		d = zipCodeDistanceMapping.get(z1 + "" + z2);
		if (d == null) {
			d = zipCodeDistanceMapping.get(z2 + "" + z1);
		}
		return d;
	}
	public String addToUnitWiseWastageIdentity(WastageEvent wastage){
		String hash = getHashForWastage(wastage);
		List<String> wastageHashes = unitWiseMonkWastageIdentity.get(wastage.getUnitId());
		// clear out wastage tokens if greater than 20 in size
		if(wastageHashes==null || wastageHashes.size() > 20){
			wastageHashes = new LinkedList<>();
		}

		//try to add a new hash, if hash is not present against the unit
		if(!wastageHashes.contains(hash)){
			wastageHashes.add(hash);
			unitWiseMonkWastageIdentity.put(wastage.getUnitId(), wastageHashes);
			return hash;
		}
		return null;
	}

	private String getHashForWastage(WastageEvent wastage){
		StringBuffer buffer = new StringBuffer();
		int index = 0;
		for(WastageData wastageData: wastage.getItems()){
			String wastageQty = wastageData.getQuantity().setScale(4, BigDecimal.ROUND_HALF_UP).toString();
			if(index>0){
				buffer.append("_");
			}
			buffer.append(wastageData.getKeyId()).append("_").append(wastageQty);
			if(wastageData.getWasteDrillDown()!=null && !wastageData.getWasteDrillDown().isEmpty()){
				wastageData.getWasteDrillDown().stream()
						.collect(Collectors.groupingByConcurrent(
								MonkWastageDetail::getChaiMonk,
								Collectors.summingDouble(MonkWastageDetail::getQuantity)
						)).forEach((monk, qty) -> buffer.append("_").append(monk).append("_").append(qty));
			}
			index++;
		}
		return buffer.toString();
	}

	public Map<String, List<ListDetail>> getListDetail(String baseType) {
		Map<String, List<ListDetail>> detailMap = new HashMap<String, List<ListDetail>>();
		for(Entry<String, List<ListDetail>> listmap : listDetail.entrySet()) {
			for(ListDetail list: listmap.getValue()) {
				if (list.getBaseType().equalsIgnoreCase(baseType)) {
					List<ListDetail> listMap = new ArrayList<ListDetail>();
					if (detailMap.isEmpty()) {
						listMap.add(list);
						detailMap.put(list.getType(), listMap);
					} else {
						if (detailMap.containsKey(list.getType())) {
							listMap = (List<ListDetail>) detailMap.get(list.getType());
							listMap.add(list);
							detailMap.put(list.getType(), listMap);
						} else {
							listMap.add(list);
							detailMap.put(list.getType(), listMap);
						}
					}
				}
			}


		}
		return detailMap;
//		return listDetail.entrySet().stream().forEach(stringListEntry -> stringListEntry.getValue().stream().filter(listDetail1 ->
//			listDetail1.getBaseType().equals(baseType)).collect(Collectors.toList()));



	}

	public void setListDetail(Map<String, List<ListDetail>> listDetail) {
		this.listDetail = listDetail;
	}

	public CapexTemplateData getCapexTemplate(String type) {
		return capexTemplate.get(type);
	}


	public ProductRecipeKey getRecipeProfile(Integer unitId, Integer productId) throws DataNotFoundException {
		if (unitProductProfileMapping.containsKey(unitId)
				&& unitProductProfileMapping.get(unitId).containsKey(productId)) {
			return unitProductProfileMapping.get(unitId).get(productId);
		} else {
			throw new DataNotFoundException(
					String.format("Missing Recipe profile for unit : %d and product : %d", unitId, productId));
		}
	}

	public PendingMilkBread getPendingMilkBread(Integer unitId) {
		if (Objects.nonNull(unitId)) {
			return pendingMilkBread.getOrDefault(unitId, null);
		}
		return null;
	}

	public void setPendingMilkBread(Integer unitId,PendingMilkBread milkBread ) {
		pendingMilkBread.put(unitId,milkBread);
	}

	public String getStockTakeAppVersion() {
		return stockTakeAppVersion;
	}

	public void refreshSkusByProductIdCache() {
		skusByProductId.clear();
		skusByProductId = scmProductManagementService.viewAllSkusByProductId();
	}

	public void setSkusByProductId(Map<Integer, List<SkuDefinition>> skusByProductId) {
		this.skusByProductId = skusByProductId;
	}

	public Map<Integer, List<SkuDefinition>> getSkusByProductId() {
		return skusByProductId;
	}

	public void setSkuByProductId(Integer productId, SkuDefinition skuDefinition) {
		if(skuDefinition != null) {
			skusByProductId.computeIfAbsent(productId, skus -> new ArrayList<>()).add(skuDefinition);
		} else {
			skusByProductId.put(productId, new ArrayList<>());
		}
	}

}
