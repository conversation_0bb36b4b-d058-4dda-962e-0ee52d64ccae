package com.stpl.tech.scm.core.service.impl;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.tax.model.AdditionalTax;
import com.stpl.tech.master.tax.model.TaxApplicability;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.cache.SCMStateTransitionCache;
import com.stpl.tech.scm.core.cache.SCMStateTransitionObject;
import com.stpl.tech.scm.core.exception.PurchaseOrderCreationException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.service.PurchaseOrderManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.util.PdfHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.AutoPOCreationResponse;
import com.stpl.tech.scm.core.util.model.ConsumptionView;
import com.stpl.tech.scm.core.util.model.PurchaseOrderCreateVO;
import com.stpl.tech.scm.core.util.model.UsedPOModel;
import com.stpl.tech.scm.core.util.model.UsedSKUModel;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.GoodsReceiveManagementDao;
import com.stpl.tech.scm.data.dao.PurchaseOrderManagementDao;
import com.stpl.tech.scm.data.dao.SCMProductManagementDao;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.BudgetAuditDetailData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.ItemTaxDetailData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderExtendedStatusLogData;
import com.stpl.tech.scm.data.model.PurchaseOrderItemData;
import com.stpl.tech.scm.data.model.PurchaseOrderLog;
import com.stpl.tech.scm.data.model.PurchaseOrderNotificationData;
import com.stpl.tech.scm.data.model.PurchaseOrderStatusEventData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.domain.model.AdvancePaymentStatus;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.CapexBudgetDetail;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.OtherTaxDetail;
import com.stpl.tech.scm.domain.model.POCreationType;
import com.stpl.tech.scm.domain.model.PercentageDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.PurchaseOrder;
import com.stpl.tech.scm.domain.model.PurchaseOrderDetails;
import com.stpl.tech.scm.domain.model.PurchaseOrderExtendedStatus;
import com.stpl.tech.scm.domain.model.PurchaseOrderExtendedStatusLog;
import com.stpl.tech.scm.domain.model.PurchaseOrderItem;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorGRItem;
import com.stpl.tech.scm.domain.state.SCMTransitionData;
import com.stpl.tech.scm.domain.state.SCMTransitionStatus;
import com.stpl.tech.scm.notification.email.POExpiredEmailNotification;
import com.stpl.tech.scm.notification.email.template.POExpiredEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorClosedPOEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorPOEmailNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@Service
public class PurchaseOrderManagementServiceImpl implements PurchaseOrderManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(PurchaseOrderManagementServiceImpl.class);
    @Autowired
    private SCMMetadataDao scmMetadataDao;

    @Autowired
    private PurchaseOrderManagementDao dao;

    @Autowired
    private SkuMappingDao skuMappingDao;

    @Autowired
    private SCMProductManagementDao scmProductManagementDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private TaxDataCache taxCache;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private ServiceOrderManagementDao serviceOrderManagementDao;

    @Autowired
    private GoodsReceiveManagementDao goodsReceiveManagementDao;

    @Autowired
    private PaymentRequestManagementService paymentRequestManagementService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer createPurchaseOrder(PurchaseOrderCreateVO createVO) throws PurchaseOrderCreationException, SumoException {
        int unitId = createVO.getDeliveryUnitId();
        Date currentDate = AppUtils.getCurrentDate() ;
        Date handOverDate = masterDataCache.getUnit(unitId).getHandoverDate() ;

        Boolean isNSO = Objects.nonNull(handOverDate) && (SCMUtil.isBefore(currentDate,AppUtils.getDateAfterDays(handOverDate , 3)) || AppUtils.isSameDate(currentDate,AppUtils.getDateAfterDays(handOverDate , 3))) ;
        if(isNSO)
        {
            List<Integer> unitIds = new ArrayList<>();
            unitIds.add(unitId) ;
            List<Integer> isPendingFaGr = scmMetadataDao.checkForFaReceiveing(unitIds) ;
            if(!isPendingFaGr.isEmpty())
            {
                throw new SumoException("Error while creating PO","Fixed Assets Receiving is still pending") ;
            }
        }

        LOG.info("Creating Purchase Order for Vendor :::::: {}", createVO.getVendorId());
        PurchaseOrderData purchaseOrderData = new PurchaseOrderData();
        List<PurchaseOrderItemData> itemDataList = new ArrayList<>();
        int userId = createVO.getUserId();
        int dispatchId = createVO.getDispatchId();
        int deliveryUnitId = createVO.getDeliveryUnitId();
        String fulfilmentDate = createVO.getFulfilmentDate();
        String comment = createVO.getComment();
        int vendorId = createVO.getVendorId();
        POCreationType creationType = createVO.getCreationType();
        List<PurchaseOrderItem> items = createVO.getItems();
        String orderType = createVO.getOrderType();

        Integer poId = null;
        Date estimatedFulfilmentDate = SCMUtil.parseDate(fulfilmentDate);
        VendorDetail vendor = scmCache.getVendorDetail(vendorId);
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendor, dispatchId);
        Unit deliveryUnit = masterDataCache.getUnit(deliveryUnitId);
        if (dispatchLocation.isPresent()) {
            boolean setIgst = !deliveryUnit.getLocation().getState().getCode()
                    .equals(dispatchLocation.get().getAddress().getStateCode());
            LOG.info("PO - deliveryUnit.getId() : " + deliveryUnit.getId());
            LOG.info("PO - deliveryUnit.getLocation().getState().getCode() : "
                    + deliveryUnit.getLocation().getState().getCode());
            LOG.info("PO - dispatchLocation.get().getDispatchId() : " + dispatchLocation.get().getDispatchId());
            LOG.info("PO - dispatchLocation.get().getAddress().getStateCode() : "
                    + dispatchLocation.get().getAddress().getStateCode());
            LOG.info("PO - setIgst : " + setIgst);
            BigDecimal billAmount = BigDecimal.ZERO;
            BigDecimal billTaxes = BigDecimal.ZERO;
            // Create PurchaseOrder object
            // Check the total amount of PO
            // if less than 25k make an auto approved PO and send vendor notification
            // otherwise create PO in created state and wait until approval has been done.
            if (items.size() > 0) {
                for (PurchaseOrderItem item : items) {
                    if (item.getSkuId() != 0) {
                        PurchaseOrderItemData itemData = new PurchaseOrderItemData();
                        itemData.setSkuId(item.getSkuId());
                        itemData.setUnitOfMeasure(item.getUnitOfMeasure());
                        itemData.setSkuName(item.getSkuName());
                        itemData.setHsnCode(item.getHsn());
                        itemData.setAmountPaid(BigDecimal.ZERO);
                        itemData.setUnitPrice(item.getUnitPrice());
                        itemData.setNegotiatedUnitPrice(
                                item.getNegotiatedUnitPrice() != null ? item.getNegotiatedUnitPrice()
                                        : item.getUnitPrice());
                        itemData.setPackagingQuantity(item.getPackagingQty());

                        BigDecimal totalQty = SCMUtil.multiply(item.getConversionRatio(),
                                SCMUtil.convertToBigDecimal(item.getPackagingQty()));

                        itemData.setRequestedQuantity(totalQty);
                        itemData.setRequestedAbsoluteQuantity(totalQty);

                        itemData.setPackagingId(item.getPackagingId());
                        itemData.setConversionRatio(item.getConversionRatio());
                        itemData.setPackagingName(item.getPackagingName());
                        itemData.setExemptItem(item.isExemptItem() ? SCMServiceConstants.SCM_CONSTANT_YES
                                : SCMServiceConstants.SCM_CONSTANT_NO);

                        BigDecimal totalTax;
                        if (!setIgst) {
                            itemData.setCgstPercentage(item.getCgst().getPercentage());
                            itemData.setCgstValue(item.getCgst().getValue());
                            itemData.setSgstPercentage(item.getCgst().getPercentage());
                            itemData.setSgstValue(item.getCgst().getValue());
                            totalTax = SCMUtil.add(itemData.getSgstValue(), itemData.getSgstValue());
                        } else {
                            itemData.setIgstPercentage(item.getIgst().getPercentage());
                            itemData.setIgstValue(item.getIgst().getValue());
                            totalTax = itemData.getIgstValue();
                        }

                        if (item.getOtherTaxes() != null && !item.getOtherTaxes().isEmpty()) {
                            List<ItemTaxDetailData> otherTaxes = item.getOtherTaxes().stream()
                                    .map(otherTaxDetail -> SCMDataConverter.convert(otherTaxDetail))
                                    .collect(Collectors.toList());

                            BigDecimal otherTax = BigDecimal.valueOf(
                                    otherTaxes.stream().mapToDouble(value -> value.getTaxValue().doubleValue()).sum());
                            itemData.setOtherTaxes(otherTax);
                            totalTax = totalTax.add(otherTax); // add other taxes to total tax
                            itemData.setOtherTaxesApplied(otherTaxes);
                        }
                        itemData.setTotalTax(totalTax);
                        BigDecimal totalPrice = item.getTotalCost();
                        itemData.setTotalCost(totalPrice);
                        BigDecimal itemCost = SCMUtil.add(totalPrice, totalTax);
                        itemData.setAmountPaid(itemCost);
                        billAmount = SCMUtil.add(billAmount, itemCost);
                        billTaxes = SCMUtil.add(billTaxes, totalTax);
                        if (item.getType() != null) {
                            itemData.setType(item.getType());
                        }
                        itemDataList.add(itemData);
                    }
                }

                if (itemDataList.size() > 0) {
                    purchaseOrderData.setDeliveryLocationId(deliveryUnitId);
                    purchaseOrderData.setCompanyId(deliveryUnit.getCompany().getId());
                    purchaseOrderData.setDispatchLocationId(dispatchId);
                    purchaseOrderData.setBillAmount(SCMUtil.subtract(billAmount, billTaxes));
                    purchaseOrderData.setPaidAmount(billAmount);
                    purchaseOrderData.setTotalTaxes(billTaxes);
                    purchaseOrderData.setLeadTime(createVO.getLeadTime());
                    purchaseOrderData.setExpiryStatus(PurchaseOrderExtendedStatus.UN_EXPIRED.value());
                    purchaseOrderData.setStatus(PurchaseOrderStatus.CREATED.name());
                    // Auto approve if the total sum is less than 25000
                    if (SCMUtil.convertToBigDecimal(purchaseOrderData.getBillAmount()).compareTo(props.getPOLimit()) < 1
                            || POCreationType.SYSTEM.equals(createVO.getCreationType())) {
                        purchaseOrderData.setStatus(PurchaseOrderStatus.APPROVED.name());
                        purchaseOrderData.setApprovedBy(SCMServiceConstants.SYSTEM_USER);
                        purchaseOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                        // setting expiry date as order is approved
                        purchaseOrderData.setExpiryDate(AppUtils.getDayBeforeOrAfterDay(SCMUtil.getCurrentTimestamp(),createVO.getLeadTime()));
                        purchaseOrderData.setLastUpdatedBy(SCMServiceConstants.SYSTEM_USER);
                    }
                    if (createVO.getStopNotify() != null && createVO.getStopNotify()) {
                        purchaseOrderData.setVendorNotified(SCMServiceConstants.SCM_CONSTANT_YES);
                    } else {
                        purchaseOrderData.setVendorNotified(SCMServiceConstants.SCM_CONSTANT_NO);
                    }
                    purchaseOrderData.setFulfillmentDate(estimatedFulfilmentDate);
                    purchaseOrderData.setCreationType(creationType.name());
                    purchaseOrderData.setGeneratedBy(userId);
                    purchaseOrderData.setGenerationTime(SCMUtil.getCurrentTimestamp());
                    purchaseOrderData.setGeneratedForVendor(vendorId);
                    purchaseOrderData.setComment(comment);
                    purchaseOrderData.setReceiptNumber(generatePOId(vendor, deliveryUnit));
                    purchaseOrderData.setOrderType(orderType);
                    if (createVO.getType() != null) {
                        purchaseOrderData.setType(createVO.getType());
                    }
                    purchaseOrderData = dao.add(purchaseOrderData, true);
                } else {
                    LOG.error("Didn't find items for Purchase Order");
                    throw new PurchaseOrderCreationException("Didn't find items for Purchase Order");
                }
            }
        } else {
            LOG.error("No dispatch location found in vendor with the given dispatchId {}", dispatchId);
            throw new PurchaseOrderCreationException("No dispatch location found in vendor with the given dispatchId");
        }

        // On successful persistence
        if (purchaseOrderData != null && purchaseOrderData.getId() != null) {
            poId = purchaseOrderData.getId();
            for (PurchaseOrderItemData itemData : itemDataList) {
                itemData.setPurchaseOrderData(purchaseOrderData);
                itemData = dao.add(itemData, false);

                if (itemData.getOtherTaxesApplied() != null && !itemData.getOtherTaxesApplied().isEmpty()) {
                    final PurchaseOrderItemData finalItemData = itemData;
                    itemData.getOtherTaxesApplied().stream().map(itemTaxDetailData -> {
                        itemTaxDetailData.setPurchaseItem(finalItemData);
                        try {
                            return dao.add(itemTaxDetailData, false);
                        } catch (SumoException e) {
                            LOG.error("Error while adding Item Tax Detail in Purchase Order ", e);
                        }
                        return null;
                    }).collect(Collectors.toList());
                }
            }
            dao.flush();
            purchaseOrderData.setPurchaseOrderItemDatas(itemDataList);
            String approvedBy = purchaseOrderData.getApprovedBy()==null ? "" :  masterDataCache.getEmployee(purchaseOrderData.getApprovedBy());
            EmployeeBasicDetail employeeDetail = masterDataCache.getEmployeeBasicDetail(userId);
            VendorPOEmailNotificationTemplate emailTemplate = new VendorPOEmailNotificationTemplate(purchaseOrderData,
                    deliveryUnit, vendor, dispatchLocation.get(), props.getBasePath(),
                    masterDataCache.getEmployee(userId), masterDataCache.getCompany(purchaseOrderData.getCompanyId()),
                    props.getEnvType(), "",approvedBy,employeeDetail,null,purchaseOrderData.getStatus());
            try {
                Pair<File, DocumentDetailData> poInvoice = createInvoice(emailTemplate,
                        purchaseOrderData.getGeneratedBy(), purchaseOrderData.getId());
                if (purchaseOrderData.getStatus().equals(PurchaseOrderStatus.APPROVED.name())
                        && creationType.equals(POCreationType.MANUAL)) {
                    PurchaseOrderNotificationData sentNotification = notificationService
                            .sendPONotification(purchaseOrderData, vendor, emailTemplate, poInvoice.getFirst());
                    if (sentNotification != null) {
                        purchaseOrderData.setVendorNotified(SCMServiceConstants.SCM_CONSTANT_YES);
                        purchaseOrderData.setPoInvoiceDocument(poInvoice.getSecond());
                        dao.update(purchaseOrderData, true);// updating purchase order with
                        // invoice
                    }
                } else if (purchaseOrderData.getStatus().equals(PurchaseOrderStatus.CREATED.name())) {
                    // send notification to group that po is pending for approval
                    notificationService.poPendingForApprovalNotification(purchaseOrderData);
                }
            } catch (Exception e) {
                LOG.error("Encountered error while uploading file to S3 service ::::", e);
                throw new PurchaseOrderCreationException(
                        "Purchase Order creation failed since upload of S3 document failed");
            }
        }
        // return
        // SCMDataConverter.convert(purchaseOrderData,scmCache,masterDataCache,true);
        if (purchaseOrderData.getType()!= null && purchaseOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
            if (!updateBudgetDetailsForPo(purchaseOrderData, true ,null)) {
                throw new SumoException("Error in Budget Updation", "Exception occurred while updating Budget for PO...!");
            }
        }
        return poId;
    }

    private boolean updateBudgetDetailsForPo(PurchaseOrderData purchaseOrderData, boolean isCreatedOrClosed  , BigDecimal diffInPaidAmount  ) {
        try {
            LOG.info("paid amount is : {} ",purchaseOrderData.getPaidAmount());
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(purchaseOrderData.getDeliveryLocationId());
            String isFAOrGoods = purchaseOrderData.getOrderType().equalsIgnoreCase(AppConstants.FIXED_ASSET_ORDER) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(purchaseOrderData.getDeliveryLocationId(), isFAOrGoods);
            List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(), BudgetAuditActions.RUNNING_AMOUNT.value());
            for (String action : actions) {
                BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
                budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
                budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
                budgetAuditDetail.setActionBy(purchaseOrderData.getGeneratedBy());
                if (isCreatedOrClosed ) {
                    if(Objects.nonNull(diffInPaidAmount))
                    {
                        budgetAuditDetail.setAction(BudgetAuditActions.EDITED.value());
                    }
                    else {
                        budgetAuditDetail.setAction(BudgetAuditActions.CREATED.value());
                    }
                }
                else {
                    if(Objects.nonNull(diffInPaidAmount))
                    {
                        budgetAuditDetail.setAction(BudgetAuditActions.EDITED.value());
                    }
                    else {
                        budgetAuditDetail.setAction(BudgetAuditActions.CLOSED.value());
                    }
                }
                budgetAuditDetail.setKeyType(BudgetAuditActions.PO_ID.value());
                budgetAuditDetail.setKeyValue(purchaseOrderData.getId());
                budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
                if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
                    if (isCreatedOrClosed) {
                        if(Objects.nonNull(diffInPaidAmount))
                        {
                            budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().subtract(diffInPaidAmount));
                        }
                        else {
                            budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().subtract(purchaseOrderData.getPaidAmount()));
                        }
                            budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                    }
                    else {
                        if(Objects.nonNull(diffInPaidAmount))
                        {
                            budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(diffInPaidAmount));
                        }
                        else {
                            budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(purchaseOrderData.getPaidAmount()));
                        }
                        budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                    }
                } else {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                    if (isCreatedOrClosed) {
                        if(Objects.nonNull(diffInPaidAmount))
                        {
                            budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().add(diffInPaidAmount));
                            budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                        }
                        else {
                            budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().add(purchaseOrderData.getPaidAmount()));
                            budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                        }

                    }
                    else {
                        if(Objects.nonNull(diffInPaidAmount))
                        {
                            budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(diffInPaidAmount));
                            budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                        }
                        else {
                            budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(purchaseOrderData.getPaidAmount()));
                            budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                        }

                    }
                }
                serviceOrderManagementDao.add(budgetAuditDetail, true);
            }
            if (isCreatedOrClosed) {
                if(Objects.nonNull(diffInPaidAmount))
                {
                    capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().subtract(diffInPaidAmount));
                    capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().add(diffInPaidAmount));
                }
                else {
                    capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().subtract(purchaseOrderData.getPaidAmount()));
                    capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().add(purchaseOrderData.getPaidAmount()));
                }

            }
            else {
                if(Objects.nonNull(diffInPaidAmount))
                {
                    capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(diffInPaidAmount));
                    capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(diffInPaidAmount));
                }
                else {
                    capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(purchaseOrderData.getPaidAmount()));
                    capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(purchaseOrderData.getPaidAmount()));
                }
            }
            CapexBudgetDetailData finalData =serviceOrderManagementDao.update(capexBudgetData, true);
            if (!validateBudgetAmounts(finalData)) {
                LOG.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        }
        catch (Exception e){
            LOG.error("Exception Occurred while updating budget for PO ::: ",e);
            return false;
        }
    }

    @Override
    public Boolean validateBudgetAmounts(CapexBudgetDetailData capexBudgetDetailData) {
        LOG.info("Checking for negative values in the budget..!");
        if (capexBudgetDetailData.getRemainingAmount().compareTo(BigDecimal.ZERO) < 0) {
            LOG.info("Remaining amount goes as Negative Value..!");
            return false;
        }
        else if (capexBudgetDetailData.getRunningAmount().compareTo(BigDecimal.ZERO) < 0) {
            LOG.info("Running amount goes as Negative Value..!");
            return false;
        }
        else if (capexBudgetDetailData.getReceivingAmount().compareTo(BigDecimal.ZERO) < 0) {
            LOG.info("Receiving amount goes as Negative Value..!");
            return false;
        }
        else if (capexBudgetDetailData.getPaidAmount().compareTo(BigDecimal.ZERO) < 0) {
            LOG.info("Paid amount goes as Negative Value..!");
            return false;
        }
        else if (capexBudgetDetailData.getExtraReceiving().compareTo(BigDecimal.ZERO) < 0) {
            LOG.info("Extra Receiving amount goes as Negative Value..!");
            return false;
        }
        else {
            return true;
        }
    }

//    private Integer getMaxLeadTime(List<PurchaseOrderItem> purchaseOrderItem){
//       int maxLeadTime = 0;
//        for(PurchaseOrderItem po:purchaseOrderItem){
//            if( maxLeadTime < po.getLeadTime())
//                maxLeadTime = po.getLeadTime();
//        }
//        return maxLeadTime;
//    }
//
//    private List<PurchaseOrderItem> convert(List<PurchaseOrderItemData> data){
//        List<PurchaseOrderItem> list = new ArrayList<>();
//        for(PurchaseOrderItemData purchaseOrderItemData:data){
//            list.add(SCMDataConverter.convert(purchaseOrderItemData));
//        }
//        return list;
//    }


    private String generatePOId(VendorDetail vendor, Unit deliveryUnit) {
        int stateId = deliveryUnit.getLocation().getState().getId();
        Integer financialYear = AppUtils.getFinancialYear();
        if (financialYear <= 2024) {
            int stateInvoiceId = dao.getNextStateInvoiceId(stateId, "PURCHASE_ORDER_ID");
            return "PO" + "-" + deliveryUnit.getLocation().getState().getCode() + "-" + vendor.getVendorId() + "-"
                    + stateInvoiceId;
        } else {
            int stateInvoiceId = dao.getNextStateInvoiceId(stateId, "PURCHASE_ORDER_ID", financialYear);
            return "PO" + "-" + deliveryUnit.getLocation().getState().getCode() + "-" + vendor.getVendorId() + "-" + (financialYear%100) + "-"
                    + stateInvoiceId;
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class , value = "SCMDataSourceTM" , readOnly = false , propagation = Propagation.REQUIRED)
    public Boolean updateApprovedPurchaseOrder(PurchaseOrderDetails purchaseOrderDetails) throws SumoException {
        try {
            PurchaseOrderData purchaseOrderData = dao.find(PurchaseOrderData.class, purchaseOrderDetails.getPoId());

                BigDecimal diffInPaidAmount = SCMUtil.subtract(purchaseOrderData.getPaidAmount(),SCMUtil.add(purchaseOrderDetails.getTotalTax(), purchaseOrderDetails.getTotalBillAmount())) ;
            purchaseOrderData.setTotalTaxes(purchaseOrderDetails.getTotalTax());
            purchaseOrderData.setBillAmount(purchaseOrderDetails.getTotalBillAmount());

            purchaseOrderData.setPaidAmount(SCMUtil.add(purchaseOrderDetails.getTotalTax(), purchaseOrderDetails.getTotalBillAmount()));
            if(purchaseOrderData.getType().equals("CAPEX"))
            {

                int isNewAmountGreater = diffInPaidAmount.compareTo(new BigDecimal(0)) ;
                        if(!updateBudgetDetailsForPo( purchaseOrderData , isNewAmountGreater >= 0 ?false : true ,isNewAmountGreater > 0 ?diffInPaidAmount : SCMUtil.multiply(diffInPaidAmount,new BigDecimal(-1)))){
                            throw new SumoException("Error in Budget Updation", "Exception occurred while updating Budget for PO...!");
                        };

            }
            dao.update(purchaseOrderData, true);
            PurchaseOrderItemData purchaseOrderItemData = dao.find(PurchaseOrderItemData.class, purchaseOrderDetails.getPoDetailId());
            BigDecimal prevPackaginQty = purchaseOrderItemData.getPackagingQuantity() ;
            purchaseOrderItemData.setPackagingQuantity(purchaseOrderDetails.getPackagingQuantity());
            purchaseOrderItemData.setTotalTax(purchaseOrderDetails.getTax());
            purchaseOrderItemData.setTotalCost(purchaseOrderDetails.getAmount());
            purchaseOrderItemData.setAmountPaid(SCMUtil.add(purchaseOrderDetails.getTax(), purchaseOrderDetails.getAmount()));
            purchaseOrderItemData.setCgstValue(purchaseOrderDetails.getCgstValue());
            if(Objects.nonNull(purchaseOrderDetails.getIgstValue()))
            purchaseOrderItemData.setIgstValue(purchaseOrderDetails.getIgstValue());
            purchaseOrderItemData.setSgstValue(purchaseOrderDetails.getSgstValue());
            purchaseOrderItemData.setRequestedQuantity((SCMUtil.multiply(purchaseOrderDetails.getPackagingQuantity(),purchaseOrderItemData.getConversionRatio()))) ;
            dao.update(purchaseOrderItemData, true);
            PurchaseOrderLog purchaseOrderLog = new PurchaseOrderLog() ;
            purchaseOrderLog.setPoId(purchaseOrderDetails.getPoDetailId());
            purchaseOrderLog.setAcceptedPackagingQty(purchaseOrderDetails.getPackagingQuantity());
            purchaseOrderLog.setPreviousPackagingQty(prevPackaginQty);
            purchaseOrderLog.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
            purchaseOrderLog.setLastUpdatedBy(purchaseOrderDetails.getUpdatedBy());
            dao.add(purchaseOrderLog,true) ;
            return true ;
        }catch (SumoException e)
        {
            LOG.error("Exception Occurred while updating  PO ::: ",e);
            throw e;
        }
        catch (Exception e){
            LOG.error("Exception Occurred while updating  PO ::: ",e);
            return false;
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PurchaseOrder> getClonableOrders(int vendorId, int deliveryUnitId, int dispatchId) {

        Optional<List<PurchaseOrderData>> purchaseOrderDataList = Optional.ofNullable(
                dao.findClonablePurchaseOrders(vendorId, deliveryUnitId, dispatchId));
        List<PurchaseOrder> orders = null;
        if (purchaseOrderDataList.isPresent()) {
            orders = purchaseOrderDataList.get().stream().map(
                    purchaseOrderData -> SCMDataConverter.convert(purchaseOrderData, scmCache, masterDataCache, false, null))
                    .collect(Collectors.<PurchaseOrder>toList());
        }
        return orders;
    }

    public Pair<File, DocumentDetailData> createInvoice(VendorPOEmailNotificationTemplate template, Integer generatedBy,
                                                        Integer poId) throws TemplateRenderingException, IOException, DocumentException, SumoException {
        String templateString = template.getContent();
        File poFile = PdfHelper.createPdf(templateString, props.getBasePath(), template.getName());
        FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "purchaseOrders", poFile);
        DocumentDetailData documentDetailData = new DocumentDetailData();
        documentDetailData.setS3Bucket(fileDetail.getBucket());
        documentDetailData.setS3Key(fileDetail.getKey());
        documentDetailData.setDocumentLink(poFile.getName());
        documentDetailData.setFileUrl(fileDetail.getUrl());
        documentDetailData.setDocumentUploadType(DocUploadType.PURCHASE_ORDER.name());
        documentDetailData.setMimeType(MimeType.PDF.name());
        documentDetailData.setFileType(FileType.OTHERS.name());
        documentDetailData.setUpdatedBy(generatedBy);
        documentDetailData.setDocumentUploadTypeId(poId);
        documentDetailData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        documentDetailData = dao.add(documentDetailData, true);
        return Pair.of(poFile, documentDetailData);
    }

    public Pair<File, DocumentDetailData> createInvoiceForClosedPo(VendorClosedPOEmailNotificationTemplate template, Integer generatedBy,
                                                                   Integer poId) throws TemplateRenderingException, IOException, DocumentException, SumoException {
        String templateString = template.getContent();
        File poFile = PdfHelper.createPdf(templateString, props.getBasePath(), template.getName());
        FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "purchaseOrders", poFile);
        DocumentDetailData documentDetailData = new DocumentDetailData();
        documentDetailData.setS3Bucket(fileDetail.getBucket());
        documentDetailData.setS3Key(fileDetail.getKey());
        documentDetailData.setDocumentLink(poFile.getName());
        documentDetailData.setFileUrl(fileDetail.getUrl());
        documentDetailData.setDocumentUploadType(DocUploadType.PURCHASE_ORDER.name());
        documentDetailData.setMimeType(MimeType.PDF.name());
        documentDetailData.setFileType(FileType.OTHERS.name());
        documentDetailData.setUpdatedBy(generatedBy);
        documentDetailData.setDocumentUploadTypeId(poId);
        documentDetailData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        documentDetailData = dao.add(documentDetailData, true);
        return Pair.of(poFile, documentDetailData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PurchaseOrder> findOrdersByStatus(Integer vendorId, Integer dispatchId, List<Integer> skus,
                                                  int deliveryUnitId, List<PurchaseOrderStatus> statusList, Date startDate, Date endDate, Integer PurchaseOrderId, PurchaseOrderStatus status,PurchaseOrderExtendedStatus expiryStatus) {
        if (startDate == null && endDate == null) {
            startDate = SCMUtil.getCurrentBusinessDate();
            endDate = startDate;
        }
        endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);

        List<PurchaseOrderData> purchaseOrderDataList = dao.findPurchaseOrdersByStatus(vendorId,
                dispatchId, deliveryUnitId, skus, statusList, startDate, endDate, PurchaseOrderId, status,expiryStatus);
        if (purchaseOrderDataList != null) {
            List<PurchaseOrder> purchaseOrders =new ArrayList<>();
            for (PurchaseOrderData purchaseOrderData : purchaseOrderDataList) {
                List<VendorAdvancePayment> vendorAdvancePayments = new ArrayList<>();
                if (Objects.nonNull(purchaseOrderData.getAdvancePaymentDatas()) && !purchaseOrderData.getAdvancePaymentDatas().isEmpty()) {
                    for (AdvancePaymentData advancePaymentData : purchaseOrderData.getAdvancePaymentDatas()) {
                        if (!advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.CANCELLED.value())) {
                            VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(purchaseOrderData.getGeneratedForVendor())
                                    .advanceStatus("ALL").advanceType(SCMServiceConstants.PO_ADVANCE).poId(purchaseOrderData.getId()).build();
                            VendorAdvancePayment advance = null;
                            try {
                                advance = paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, true,advancePaymentData);
                                vendorAdvancePayments.add(advance);
                            } catch (Exception e) {
                                LOG.info("Exception during Vendor Advance ..!",e);
                            }
                        }
                    }
                }
                PurchaseOrder purchaseOrder = SCMDataConverter.convert(purchaseOrderData, scmCache, masterDataCache, false, null);
                if (!vendorAdvancePayments.isEmpty()) {
                    purchaseOrder.setVendorAdvancePayments(vendorAdvancePayments);
                }
                purchaseOrders.add(purchaseOrder);
            }
            purchaseOrders = purchaseOrders.stream().sorted((o1, o2) -> o2.getId().compareTo(o1.getId())).collect(Collectors.<PurchaseOrder>toList());
            if(!purchaseOrders.isEmpty()){
                for(PurchaseOrder p : purchaseOrders){
                    for(PurchaseOrderItem item : p.getOrderItems()){
                        Integer dispatchLocId = masterDataCache.getAllLocations().get( p.getDispatchLocation().getLocationId() ).getId();
                        int skuPriceDataId = dao.getSkuPriceDataId(
                                item.getSkuId(),
                                item.getPackagingId(),
                                p.getGeneratedForVendor().getId(),
                                dispatchLocId,
                                p.getDeliveryUnitId().getId() ).get(0);
                                List<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>> skuPriceHistory = dao.getSkuPriceHistory(skuPriceDataId);
                                if(skuPriceHistory!= null &&!skuPriceHistory.isEmpty()){
                                    item.setPriceHistory(skuPriceHistory);
                                }
                    }
                }
            }
            return purchaseOrders;
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PurchaseOrder> getPosForAdvance(Integer vendorId) throws SumoException {
        VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(vendorId).advanceStatus("ALL").
                advanceType(SCMServiceConstants.STAND_ALONE_ADVANCE).build();
        VendorAdvancePayment advancePayment = null;
        try {
            advancePayment = paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, false, null);
        } catch (SumoException e) {
            throw new SumoException("Pending Stand Alone Advance..!",
                    "Please Settle all the Stand Alone Advance Payments related to the Vendor " + scmCache.getVendorDetail(vendorId).getEntityName());
        }
        List<PurchaseOrder> purchaseOrders = new ArrayList<>();
        if (Objects.isNull(advancePayment)) {
            List<PurchaseOrderData> purchaseOrderDataList = dao.getPosForAdvance(vendorId);
            if (Objects.nonNull(purchaseOrderDataList) && !purchaseOrderDataList.isEmpty()) {
                purchaseOrders = purchaseOrderDataList.stream().map(
                                purchaseOrderData -> SCMDataConverter.convert(purchaseOrderData, scmCache, masterDataCache, false, null))
                        .sorted((o1, o2) -> o2.getId().compareTo(o1.getId())).collect(Collectors.<PurchaseOrder>toList());
            }
        } else {
            throw new SumoException("Pending Stand Alone Advance..!",
                    "Please Settle all the Stand Alone Advance Payments related to the Vendor " + scmCache.getVendorDetail(vendorId).getEntityName());
        }
        return purchaseOrders;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean approvePurchaseOrder(Integer poId, Integer userId)
            throws DocumentException, TemplateRenderingException, IOException {
        PurchaseOrderData purchaseOrderData = dao.find(PurchaseOrderData.class, poId);
        try {
            if (purchaseOrderData != null) {
                Unit unit = masterDataCache.getUnit(purchaseOrderData.getDeliveryLocationId());
                VendorDetail vendorDetail = scmCache.getVendorDetail(purchaseOrderData.getGeneratedForVendor());
                String createdBy = masterDataCache.getEmployee(purchaseOrderData.getGeneratedBy());

                Optional<VendorDispatchLocation> vendorLocation = vendorDetail.getDispatchLocations().stream()
                        .filter(dispatchLocation -> dispatchLocation.getDispatchId()
                                .equals(purchaseOrderData.getDispatchLocationId()))
                        .findFirst();
                if (vendorLocation.isPresent()) {
                    EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(purchaseOrderData.getGeneratedBy());
                    VendorPOEmailNotificationTemplate emailTemplate = new VendorPOEmailNotificationTemplate(
                            purchaseOrderData, unit, vendorDetail, vendorLocation.get(), props.getBasePath(), createdBy,
                            masterDataCache.getCompany(purchaseOrderData.getCompanyId()), props.getEnvType(), "", masterDataCache.getEmployee(userId),employeeBasicDetail,
                            null, purchaseOrderData.getStatus());
                    Pair<File, DocumentDetailData> poInvoice = createInvoice(emailTemplate,
                            purchaseOrderData.getGeneratedBy(), purchaseOrderData.getId());
                    if (poInvoice != null && poInvoice.getFirst().getName() != null) {
                        PurchaseOrderNotificationData notification = notificationService.sendPONotification(
                                purchaseOrderData, vendorDetail, emailTemplate, poInvoice.getFirst());
                        PurchaseOrderStatus toStatus = PurchaseOrderStatus.APPROVED;
                        if (notification != null
                                && validatePOStateTransition(purchaseOrderData.getStatus(), toStatus)) {
                            boolean updated = generatePOStatusEvent(purchaseOrderData, toStatus, userId);
                            if (updated) {
                                purchaseOrderData.setStatus(toStatus.name());
                                purchaseOrderData.setLastUpdatedBy(userId);
                                purchaseOrderData.setApprovedBy(userId);
                                purchaseOrderData.setPoInvoiceDocument(poInvoice.getSecond());
                                purchaseOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                                // setting expiry date as order is approved
                                purchaseOrderData.setExpiryDate(AppUtils.getDayBeforeOrAfterDay(SCMUtil.getCurrentTimestamp(), purchaseOrderData.getLeadTime()));
//                                purchaseOrderData.setExpiryDate(AppUtils.getDayBeforeOrAfterDay(SCMUtil.getCurrentTimestamp(), vendorDetail.getLeadTime()));
                                purchaseOrderData.setVendorNotified(SCMServiceConstants.SCM_CONSTANT_YES);
                                dao.update(purchaseOrderData, true);
                                return true;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Encountered error while approving Purchase Order {} by UserId {}", poId, userId, e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean rejectPurchaseOrder(Integer poId, Integer userId) throws SumoException {
        return changeStatus(poId, userId, PurchaseOrderStatus.REJECTED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelPurchaseOrder(Integer poId, Integer userId) throws SumoException {
        return changeStatusAndNotify(poId, userId, PurchaseOrderStatus.CANCELLED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean generatePOStatusEvent(PurchaseOrderData purchaseOrder, PurchaseOrderStatus toStatus, int userId) throws SumoException {
        PurchaseOrderStatus fromStatus = PurchaseOrderStatus.valueOf(purchaseOrder.getStatus());
        if (validatePOStateTransition(fromStatus, toStatus)) {
            PurchaseOrderStatusEventData eventData = new PurchaseOrderStatusEventData();
            eventData.setFromStatus(fromStatus.name());
            eventData.setToStatus(toStatus.name());
            eventData.setPurchaseOrderId(purchaseOrder.getId());
            eventData.setUpdatedBy(userId);
            eventData.setUpdateTime(SCMUtil.getCurrentTimestamp());
            eventData.setTransitionStatus(SCMTransitionStatus.SUCCESS.name());
            eventData = dao.add(eventData, true);
            return eventData.getStatusEventId() != null;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean closePurchaseOrder(Integer poId, Integer userId) throws SumoException {
        return changeStatusAndNotify(poId, userId, PurchaseOrderStatus.CLOSED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean checkIfFulfilled(PurchaseOrderData po) {
        boolean flag = true;
        List<PurchaseOrderItemData> items = po.getPurchaseOrderItemDatas();
        for (PurchaseOrderItemData item : items) {
            if (item.getRequestedQuantity() != null && item.getRequestedQuantity()
                    .compareTo(SCMUtil.convertToBigDecimal(item.getReceivedQuantity())) != 0) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> checkExtraGrEligibility(Integer unitId, Integer vendorId, Integer dispatchId,
                                                 List<Integer> poIds, List<Integer> skus) {
        List<PurchaseOrderItemData> purchaseItems = dao.checkExtraGrEligibility(unitId, vendorId,
                dispatchId, poIds, skus);
        if (purchaseItems != null) {
            Map<Integer, Double> requestedMap = purchaseItems.stream()
                    .collect(Collectors.groupingBy(PurchaseOrderItemData::getSkuId,
                            Collectors.summingDouble(value -> value.getRequestedQuantity().doubleValue())));
            Map<Integer, Double> receivedMap = purchaseItems.stream()
                    .collect(Collectors.groupingBy(PurchaseOrderItemData::getSkuId, Collectors.summingDouble(
                            value -> SCMUtil.convertToBigDecimal(value.getReceivedQuantity()).doubleValue())));

            return skus.stream().filter(skuId -> receivedMap.get(skuId) == requestedMap.get(skuId))
                    .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Pair<UsedPOModel, Map<String, VendorGRItem>> createPurchaseOrder(PurchaseOrderCreateVO vo,
                                                                            Map<String, VendorGRItem> extraGrItems) throws PurchaseOrderCreationException, SumoException {
        // First create Purchase Order
        // Second create gr to po item map for pkg qty
        Integer poId = createPurchaseOrder(vo);
        PurchaseOrderData po = dao.find(PurchaseOrderData.class, poId);
        if (po != null) {
            List<UsedSKUModel> skuModelList = new ArrayList<>();
            List<PurchaseOrderItemData> poItems = po.getPurchaseOrderItemDatas();
            poItems.forEach(poItem -> {
                String key = SCMUtil.getSkuPkgKey(poItem.getSkuId(), poItem.getPackagingId());
                VendorGRItem grItem = extraGrItems.get(key);
                if (grItem != null) {
                    grItem.getUsedPOItems().put(poItem.getId(), poItem.getPackagingQuantity());
                    UsedSKUModel sku = new UsedSKUModel();
                    sku.setId(poItem.getSkuId());
                    sku.setPkg(poItem.getPackagingId());
                    sku.setQty(poItem.getRequestedAbsoluteQuantity());
                    skuModelList.add(sku);
                }
            });

            UsedPOModel poModel = new UsedPOModel();
            poModel.setId(poId);
            poModel.setSkus(skuModelList);
            return Pair.of(poModel, extraGrItems);
        }
        throw new PurchaseOrderCreationException(
                "Excess GR process failed while creating purchase order. Please check");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AutoPOCreationResponse createPurchaseOrder(PurchaseOrderCreateVO vo,
                                                      List<RequestOrderItem> requestOrderItems) throws PurchaseOrderCreationException, SumoException {
        AutoPOCreationResponse response = new AutoPOCreationResponse();
        Map<Integer, Set<Integer>> productSkuMap = new HashMap<Integer, Set<Integer>>();
        Map<Integer, Integer> productDefaultSkuMap = new HashMap<Integer, Integer>();
        UnitBasicDetail unitDetail = masterDataCache.getUnitBasicDetail(vo.getDeliveryUnitId());
        Unit unit = masterDataCache.getUnit(vo.getDeliveryUnitId());
        VendorDispatchLocation locationDetail = null;
        List<VendorDispatchLocation> locations = scmCache.getVendorDetail(vo.getVendorId()).getDispatchLocations();
        if (locations != null && !locations.isEmpty()) {
            for (VendorDispatchLocation l : locations) {
                if (l.getDispatchId() == vo.getDispatchId()) {
                    locationDetail = l;
                    break;
                }
            }
        }
        Location dispatchLoc = masterDataCache.getAllLocations().get(locationDetail.getAddress().getLocationId());
        Location deliveryLoc = masterDataCache.getAllLocations().get(unit.getLocation().getCode());
        List<SkuPriceDetail> skuPriceList = skuMappingDao.searchSkuPricesForVendorAndUnit(vo.getDeliveryUnitId(),
                vo.getVendorId(), dispatchLoc.getId(), deliveryLoc.getId());
        vo.setItems(new ArrayList<>());
        for (RequestOrderItem item : requestOrderItems) {
            productSkuMap.put(item.getProductId(), new HashSet<>());
            List<SkuDefinitionData> skus = scmProductManagementDao.getSkuAgainstProduct(item.getProductId());
            if (skus != null && skus.size() > 0) {
                for (SkuDefinitionData sku : skus) {
                    if (AppUtils.isActive(sku.getSkuStatus()) && AppConstants.getValue(sku.getIsDefault())) {
                        productDefaultSkuMap.put(item.getProductId(), sku.getSkuId());
                    }
                    if (AppUtils.isActive(sku.getSkuStatus())) {
                        productSkuMap.get(item.getProductId()).add(sku.getSkuId());
                    }
                }
            }
            Map<Integer, Map<Integer, SkuPriceDetail>> productSkuPriceMap = new HashMap<Integer, Map<Integer, SkuPriceDetail>>();
            if (skuPriceList != null && skuPriceList.size() > 0) {
                for (SkuPriceDetail price : skuPriceList) {
                    if (!productSkuPriceMap.containsKey(item.getProductId())) {
                        productSkuPriceMap.put(item.getProductId(), new HashMap<>());
                    }
                    if (productSkuMap.get(item.getProductId()).contains(price.getSku().getId())
                            && price.getPkg().getId() == item.getPackagingId()) {

                        productSkuPriceMap.get(item.getProductId()).put(price.getSku().getId(), price);
                    }
                }
            }
            if (!productSkuMap.containsKey(item.getProductId())) {
                response.getErrors().add(String.format("Cannot find SKU's for the product %s [%d]",
                        item.getProductName(), item.getProductId()));
            }
            if (!productDefaultSkuMap.containsKey(item.getProductId())) {
                response.getErrors().add(String.format("Cannot find Default SKU's for the product %s [%d]",
                        item.getProductName(), item.getProductId()));
            }
            if (!productSkuPriceMap.containsKey(item.getProductId())
                    || productSkuPriceMap.get(item.getProductId()).size() == 0) {
                response.getErrors().add(String.format(
                        "Cannot find Any SKU's prices for the product %s [%d] and delivery unit id : %d and vendor id : %d"
                                + " and dispatch location : %s and delivery location : %s",
                        item.getProductName(), item.getProductId(), vo.getDeliveryUnitId(), vo.getVendorId(),
                        locationDetail.getLocationId(), unitDetail.getLocation().getName()));
            }
            if (productSkuPriceMap.containsKey(item.getProductId())
                    && productSkuPriceMap.get(item.getProductId()).size() > 1 && !productSkuPriceMap
                    .get(item.getProductId()).containsKey(productDefaultSkuMap.get(item.getProductId()))) {
                response.getErrors().add(String.format(
                        "Multiple SKU prices for the product %s [%d] and delivery unit id : %d and vendor id : %d"
                                + " and dispatch location : %s and delivery location : %s but has no price for default sku id: %d",
                        item.getProductName(), item.getProductId(), vo.getDeliveryUnitId(), vo.getVendorId(),
                        locationDetail.getLocationId(), unitDetail.getLocationCode(),
                        productDefaultSkuMap.get(item.getProductId())));
            }
            SkuPriceDetail finalPrice = null;
            if (productSkuPriceMap.containsKey(item.getProductId())
                    && productSkuPriceMap.get(item.getProductId()).size() == 1) {
                for (Integer key : productSkuPriceMap.get(item.getProductId()).keySet()) {
                    finalPrice = productSkuPriceMap.get(item.getProductId()).get(key);
                    break;
                }
            } else if (productSkuPriceMap.containsKey(item.getProductId())
                    && productSkuPriceMap.get(item.getProductId()).size() > 1) {
                finalPrice = productSkuPriceMap.get(item.getProductId())
                        .get(productDefaultSkuMap.get(item.getProductId()));
            }
            if (finalPrice == null) {
                response.getErrors()
                        .add(String.format(
                                "Could not find price product %s [%d] and delivery unit id : %d and vendor id : %d"
                                        + " and dispatch location : %s and delivery location : %s",
                                item.getProductName(), item.getProductId(), vo.getDeliveryUnitId(), vo.getVendorId(),
                                locationDetail.getLocationId(), unitDetail.getLocationCode()));
            } else {
                State state = masterDataCache.getAllStates().get(locationDetail.getAddress().getStateCode());
                LOG.info("RO - PO - deliveryUnit.getId()" + unit.getId());
                LOG.info("RO - PO - deliveryUnit.getLocation().getState().getCode()" + unit.getLocation().getState().getCode());
                LOG.info("RO - PO - locationDetail.getAddress().getStateCode()" + locationDetail.getAddress().getStateCode());
                boolean setIgst = !unit.getLocation().getState().getCode()
                        .equals(locationDetail.getAddress().getStateCode());
                PurchaseOrderItem poItem = createPoItem(item, finalPrice, setIgst, state.getId());
                LOG.info("RO - PO - setIgst" + setIgst);
                vo.getItems().add(poItem);
            }
        }

        // set sku's for products in the requestOrderItems
        // setPrices for packaging definition in requestOrderItems
        // calculate taxes for POItems
        if (response.getErrors().size() == 0) {
            vo.setStopNotify(true);
            int poId = createPurchaseOrder(vo);
            response.setPoId(poId);

        }
        return response;

    }

    private PurchaseOrderItem createPoItem(RequestOrderItem item, SkuPriceDetail finalPrice, boolean setIgst,
                                           int stateId) {
        PurchaseOrderItem pi = new PurchaseOrderItem();
        ProductDefinition product = scmCache.getProductDefinition(item.getProductId());
        TaxData tax = taxCache.getTaxData(stateId, product.getTaxCode());
        TaxCategory taxCategory = taxCache.getTaxCategory(product.getTaxCode());
        // Price Data
        pi.setSkuId(finalPrice.getSku().getId());
        pi.setSkuName(finalPrice.getSku().getName());
        pi.setUnitPrice(finalPrice.getCurrent().getValue());
        pi.setNegotiatedUnitPrice(finalPrice.getCurrent().getValue());
        pi.setPackagingId(finalPrice.getPkg().getId());
        pi.setPackagingName(finalPrice.getPkg().getName());
        // Product Level data
        pi.setHsn(product.getTaxCode());

        // Item Level Data
        pi.setPackagingQty(item.getPackagingQuantity());
        pi.setUnitOfMeasure(item.getUnitOfMeasure());
        pi.setConversionRatio(item.getConversionRatio());

        // Quantity and Amount Calculation
        pi.setRequestedQuantity(AppUtils.multiply(pi.getPackagingQty(), pi.getConversionRatio()));
        pi.setRequestedAbsoluteQuantity(AppUtils.multiply(pi.getPackagingQty(), pi.getConversionRatio()));
        pi.setTotalCost(AppUtils.multiply(pi.getPackagingQty(), pi.getUnitPrice()));

        // Tax Calculation

        if (!taxCategory.isExempted()) {
            BigDecimal totalTax = BigDecimal.ZERO;
            if (!setIgst) {
                pi.setCgst(new PercentageDetail(tax.getState().getCgst(),
                        AppUtils.percentageOf(tax.getState().getCgst(), pi.getTotalCost())));
                pi.setSgst(new PercentageDetail(tax.getState().getSgst(),
                        AppUtils.percentageOf(tax.getState().getSgst(), pi.getTotalCost())));
                totalTax = AppUtils.add(totalTax, pi.getCgst().getValue());
                totalTax = AppUtils.add(totalTax, pi.getSgst().getValue());
            } else {
                pi.setIgst(new PercentageDetail(tax.getState().getIgst(),
                        AppUtils.percentageOf(tax.getState().getIgst(), pi.getTotalCost())));
                totalTax = AppUtils.add(totalTax, pi.getIgst().getValue());
            }
            List<OtherTaxDetail> others = new ArrayList<>();
            if (tax.getOthers() != null && tax.getOthers().size() > 0) {
                for (AdditionalTax o : tax.getOthers()) {
                    OtherTaxDetail other = new OtherTaxDetail();
                    other.setPercentage(o.getTax());
                    other.setTaxCategory(o.getType());
                    other.setTaxName(o.getType());
                    if (TaxApplicability.ON_SALE.equals(o.getApplicability())) {
                        other.setValue(AppUtils.percentageOf(o.getTax(), pi.getTotalCost()));
                    } else {
                        other.setValue(AppUtils.percentageOf(o.getTax(), totalTax));
                    }
                }

            }

            pi.setOtherTaxes(others);
        } else {
            if (!setIgst) {
                pi.setCgst(new PercentageDetail(BigDecimal.ZERO, BigDecimal.ZERO));
                pi.setSgst(new PercentageDetail(BigDecimal.ZERO, BigDecimal.ZERO));
            } else {
                pi.setIgst(new PercentageDetail(BigDecimal.ZERO, BigDecimal.ZERO));
            }
            List<OtherTaxDetail> others = new ArrayList<>();
            pi.setOtherTaxes(others);
        }
        return pi;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ConsumptionView> getConsumptionForPurchase(int daysInPast, String skus, int unitId) {
        List<ConsumptionView> result = Collections.emptyList();
        if (skus != null && !skus.trim().isEmpty()) {
            List<Integer> skuIds = Arrays.stream(skus.split(",")).mapToInt(Integer::parseInt)
                    .boxed().collect(Collectors.toList());
            List<Object[]> resultSet = dao.getConsumptionForPurchase(daysInPast, skuIds, unitId);
            if (resultSet != null && !resultSet.isEmpty()) {
                result = resultSet.stream()
                        .filter(objects -> {
                            if (objects[2] != null) {
                                return ((BigDecimal) objects[2]).compareTo(BigDecimal.ZERO) > 0;
                            } else {
                                return false;
                            }
                        }).map(objects -> new ConsumptionView(objects[0], objects[1], objects[2], scmCache))
                        .collect(Collectors.toList());
            }
        }
        return result;
    }

    private boolean validatePOStateTransition(PurchaseOrderStatus fromStatus, PurchaseOrderStatus toStatus) {
        SCMTransitionData data = new SCMTransitionData();
        data.setFromStateCode(fromStatus.name());
        data.setToStateCode(toStatus.name());
        SCMStateTransitionCache.getInstance().setTransitionState(SCMStateTransitionObject.PURCHASE_ORDER, data);
        return SCMTransitionStatus.SUCCESS.equals(data.getStatus());
    }

    private boolean validatePOStateTransition(String fromStatus, PurchaseOrderStatus toStatus) {
        return validatePOStateTransition(PurchaseOrderStatus.fromValue(fromStatus), toStatus);
    }

    private boolean changeStatus(Integer poId, Integer userId, PurchaseOrderStatus status) throws SumoException {
        PurchaseOrderData purchaseOrderData = dao.find(PurchaseOrderData.class, poId);
        if (purchaseOrderData != null && validatePOStateTransition(purchaseOrderData.getStatus(), status)) {
            if (generatePOStatusEvent(purchaseOrderData, status, userId)) {
                purchaseOrderData.setStatus(status.name());
                purchaseOrderData.setLastUpdatedBy(userId);
                purchaseOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                if (status.equals(PurchaseOrderStatus.CLOSED)) {
                    purchaseOrderData.setForceClosed(SCMServiceConstants.SCM_CONSTANT_YES);
                }
                dao.update(purchaseOrderData, true);
//                 notificationService.sendPOClosureNotification(purchaseOrderData,status,false);
                if (purchaseOrderData.getType()!= null && purchaseOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    if (!updateBudgetDetailsForPoRejectionCancellation(purchaseOrderData, true)) {
                        throw new SumoException("Error in Budget Updation", "Exception occurred while updating Budget for PO...!");
                    }
                }
                try {
                    Unit deliveryUnit = masterDataCache.getUnit(purchaseOrderData.getDeliveryLocationId());
                    VendorDetail vendor = scmCache.getVendorDetail(purchaseOrderData.getGeneratedForVendor());
                    Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendor,
                            purchaseOrderData.getDispatchLocationId());
                    String approvedBy = purchaseOrderData.getApprovedBy()==null ? "" :  masterDataCache.getEmployee(purchaseOrderData.getApprovedBy());
                    String lastUpdatedBy = purchaseOrderData.getLastUpdatedBy() == null ? "" : masterDataCache.getEmployee(userId);
                    EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(userId);
                    VendorPOEmailNotificationTemplate emailTemplate = new VendorPOEmailNotificationTemplate(purchaseOrderData,
                            deliveryUnit, vendor, dispatchLocation.get(), props.getBasePath(), masterDataCache.getEmployee(userId),
                            masterDataCache.getCompany(purchaseOrderData.getCompanyId()), props.getEnvType(),
                            "This Purchase Order stands Rejected", approvedBy,employeeBasicDetail,lastUpdatedBy,status.name());

                    try {
                        Pair<File, DocumentDetailData> poInvoice = createInvoice(emailTemplate, purchaseOrderData.getGeneratedBy(),
                                purchaseOrderData.getId());
                        notificationService.sendRejectedPONotification(purchaseOrderData, vendor, emailTemplate, poInvoice.getFirst());
                    } catch (TemplateRenderingException | IOException | DocumentException e) {
                        LOG.error("Send Rejected Po Email Notification Failure", e);
                        throw new SumoException("Send Cancelled Po Email Notification Failure", e);
                    }
                }
                catch (Exception e) {
                    LOG.error("Error Occurred while sending Rejected PO mail :: ",e);
                }
                return true;
            }
        }
        return false;
    }

    private boolean updateBudgetDetailsForPoRejectionCancellation(PurchaseOrderData purchaseOrderData ,Boolean isRejected) {
        try {
            LOG.info("paid amount is : {} ",purchaseOrderData.getPaidAmount());
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(purchaseOrderData.getDeliveryLocationId());
            String isFAOrGoods = purchaseOrderData.getOrderType().equalsIgnoreCase(AppConstants.FIXED_ASSET_ORDER) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(purchaseOrderData.getDeliveryLocationId(), isFAOrGoods);
            List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(), BudgetAuditActions.RUNNING_AMOUNT.value());
            for (String action : actions) {
                BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
                budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
                budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
                budgetAuditDetail.setActionBy(purchaseOrderData.getGeneratedBy());
                if(isRejected) {
                    budgetAuditDetail.setAction(BudgetAuditActions.REJECTED.value());
                }
                else{
                    budgetAuditDetail.setAction(BudgetAuditActions.CANCELLED.value());
                }
                budgetAuditDetail.setKeyType(BudgetAuditActions.PO_ID.value());
                budgetAuditDetail.setKeyValue(purchaseOrderData.getId());
                budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
                if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
                    budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(purchaseOrderData.getPaidAmount()));
                    budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                } else {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                    budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(purchaseOrderData.getPaidAmount()));
                    budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                }
                serviceOrderManagementDao.add(budgetAuditDetail, true);
            }
            capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(purchaseOrderData.getPaidAmount()));
            capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(purchaseOrderData.getPaidAmount()));
            CapexBudgetDetailData finalData =serviceOrderManagementDao.update(capexBudgetData, true);
            if (!validateBudgetAmounts(finalData)) {
                LOG.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        }
        catch (Exception e){
            LOG.error("Exception Occurred while updating budget for PO ::: ",e);
            return false;
        }
    }

    private boolean changeStatusAndNotify(Integer poId, Integer userId, PurchaseOrderStatus status)
            throws SumoException {
    	try {
        PurchaseOrderData purchaseOrderData = dao.find(PurchaseOrderData.class, poId);
        validateVendorAdvance(purchaseOrderData);
        if (status.equals(PurchaseOrderStatus.CLOSED) && (purchaseOrderData.getStatus()
                .equalsIgnoreCase(String.valueOf(PurchaseOrderStatus.APPROVED))
                || purchaseOrderData.getStatus().equalsIgnoreCase(String.valueOf(PurchaseOrderStatus.IN_PROGRESS)))) {
            sendCLosedPoEmailNotification(purchaseOrderData, userId);
        }
        if (status.equals(PurchaseOrderStatus.CANCELLED)) {
            if (purchaseOrderData.getStatus().equalsIgnoreCase(String.valueOf(PurchaseOrderStatus.APPROVED))
                    || purchaseOrderData.getStatus().equalsIgnoreCase(String.valueOf(PurchaseOrderStatus.IN_PROGRESS))) {
                sendCancelledPoEmailNotification(purchaseOrderData, userId);
            }
            if (purchaseOrderData.getStatus().equalsIgnoreCase(String.valueOf(PurchaseOrderStatus.CREATED))) {
                if (purchaseOrderData.getType()!= null && purchaseOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    if (!updateBudgetDetailsForPoRejectionCancellation(purchaseOrderData, false )) {
                        throw new SumoException("Error in Budget Updation", "Cannot Update Budget While Cancelling the Purchase Order..!");
                    }
                }
            }
        }
        LOG.info("Purchase Order is: " + status);
        if (purchaseOrderData != null && validatePOStateTransition(purchaseOrderData.getStatus(), status)) {
            if (generatePOStatusEvent(purchaseOrderData, status, userId)) {
                purchaseOrderData.setStatus(status.name());
                purchaseOrderData.setExpiryStatus(PurchaseOrderExtendedStatus.TERMINATED.value());
                purchaseOrderData.setLastUpdatedBy(userId);
                purchaseOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                if (status.equals(PurchaseOrderStatus.CLOSED)) {
                    purchaseOrderData.setForceClosed(SCMServiceConstants.SCM_CONSTANT_YES);
                }
                dao.update(purchaseOrderData, true);
                // notificationService.sendPOClosureNotification(purchaseOrderData,status,false);
                return true;
            }
        }
    	}catch(NullPointerException npe) {
    		LOG.error("Null Pointer Exception", npe);
    	}
        return false;
    }

    private void validateVendorAdvance(PurchaseOrderData purchaseOrderData) throws SumoException {
        StringBuilder msg = new StringBuilder("");
        if (Objects.nonNull(purchaseOrderData.getAdvancePaymentDatas()) && !purchaseOrderData.getAdvancePaymentDatas().isEmpty()) {
            for (AdvancePaymentData advancePaymentData : purchaseOrderData.getAdvancePaymentDatas()) {
                if (advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.INITIATED.value())) {
                    msg.append("Pending Vendor Advance of Rs : " + advancePaymentData.getPrAmount() + " ( "+advancePaymentData.getAdvancePaymentId() + " )<br>" );
                }
            }
        }
        if (!msg.toString().equalsIgnoreCase("")) {
            throw new SumoException("Please Settle the Vendor Advance Related to this PO", msg.toString() + "<br>" +
                    "Please Settle the vendor Advance to CANCEL/CLOSE the Purchase Order..!");
        }
    }

    @Override
    public void sendCLosedPoEmailNotification(PurchaseOrderData purchaseOrderData, Integer userId)
            throws SumoException {
        Unit deliveryUnit = masterDataCache.getUnit(purchaseOrderData.getDeliveryLocationId());
        VendorDetail vendor = scmCache.getVendorDetail(purchaseOrderData.getGeneratedForVendor());
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendor,
                purchaseOrderData.getDispatchLocationId());
        PurchaseOrderData newPurchaseOrderData = getCLosedPUrchaseData(purchaseOrderData);
        if (purchaseOrderData.getType()!= null && purchaseOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
            if (!updateBudgetDetailsForPo(newPurchaseOrderData, false ,null)) {
                throw new SumoException("Exception occurred while updating Budget for PO");
            }
        }
        VendorClosedPOEmailNotificationTemplate emailTemplate = new VendorClosedPOEmailNotificationTemplate(
                newPurchaseOrderData, deliveryUnit, vendor, dispatchLocation.get(), props.getBasePath(),
                masterDataCache.getEmployee(userId), masterDataCache.getCompany(purchaseOrderData.getCompanyId()),
                props.getEnvType(),
                "This Purchase Order stands Cancelled for the pending quantity mentioned below and No production/Delivery to be entertained against it");

        try {
            Pair<File, DocumentDetailData> poInvoice = createInvoiceForClosedPo(emailTemplate,
                    purchaseOrderData.getGeneratedBy(), purchaseOrderData.getId());
            PurchaseOrderNotificationData sentNotification = notificationService
                    .sendClosedPONotification(purchaseOrderData, vendor, emailTemplate, poInvoice.getFirst());
        } catch (TemplateRenderingException | IOException | DocumentException e) {
            LOG.error("Send CLosed Po Email Notification Failure", e);
            throw new SumoException("Send CLosed Po Email Notification Failure", e);
        }
    }

    private PurchaseOrderData getCLosedPUrchaseData(PurchaseOrderData purchaseOrderData) {
        BigDecimal billAmount = new BigDecimal(0);
        BigDecimal totalTaxes = new BigDecimal(0);
        BigDecimal paidAmount = new BigDecimal(0);

        for (PurchaseOrderItemData purchaseItem : purchaseOrderData.getPurchaseOrderItemDatas()) {
            BigDecimal pendingQaunt = new BigDecimal(0);
            if (purchaseItem.getReceivedQuantity() != null) {
                pendingQaunt = purchaseItem.getRequestedQuantity().subtract(purchaseItem.getReceivedQuantity());
            } else {
                pendingQaunt = purchaseItem.getRequestedQuantity();
                purchaseItem.setReceivedQuantity(BigDecimal.ZERO);
            }
            BigDecimal totalCost = purchaseItem.getUnitPrice().multiply(pendingQaunt);

            if (pendingQaunt.compareTo(BigDecimal.ZERO) == 0) {
                purchaseItem.setTotalCost(new BigDecimal(0));
                purchaseItem.setCgstValue(new BigDecimal(0));
                purchaseItem.setSgstValue(new BigDecimal(0));
                purchaseItem.setIgstValue(new BigDecimal(0));
            } else {
                if (purchaseItem.getIgstValue() != null) {
                    BigDecimal igst = (totalCost.multiply(purchaseItem.getIgstPercentage()))
                            .divide(new BigDecimal(100));
                    purchaseItem.setIgstValue(igst);
                    totalTaxes = (igst.add(totalTaxes));
                } else {
                    BigDecimal cgst = (totalCost.multiply(Objects.nonNull(purchaseItem.getCgstPercentage()) ? purchaseItem.getCgstPercentage() : BigDecimal.ZERO))
                            .divide(new BigDecimal(100));
                    BigDecimal sgst = (totalCost.multiply(Objects.nonNull(purchaseItem.getSgstPercentage()) ? purchaseItem.getSgstPercentage() : BigDecimal.ZERO))
                            .divide(new BigDecimal(100));
                    purchaseItem.setCgstValue(cgst);
                    purchaseItem.setSgstValue(sgst);
                    totalTaxes = (cgst.add(sgst)).add(totalTaxes);
                }

                purchaseItem.setTotalCost(purchaseItem.getUnitPrice().multiply(pendingQaunt));
                billAmount = totalCost.add(billAmount);
            }
        }
        paidAmount = billAmount.add(totalTaxes);
        purchaseOrderData.setBillAmount(billAmount);
        purchaseOrderData.setTotalTaxes(totalTaxes);
        purchaseOrderData.setPaidAmount(paidAmount);

        return purchaseOrderData;
    }

    private void sendCancelledPoEmailNotification(PurchaseOrderData purchaseOrderData, Integer userId)
            throws SumoException {
        Unit deliveryUnit = masterDataCache.getUnit(purchaseOrderData.getDeliveryLocationId());
        if (purchaseOrderData.getType()!= null && purchaseOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
            if (!updateBudgetDetailsForPoRejectionCancellation(purchaseOrderData, false )) {
                throw new SumoException("Error in Budget Updation", "Cannot Update Budget While Cancelling the Purchase Order..!");
            }
        }
        VendorDetail vendor = scmCache.getVendorDetail(purchaseOrderData.getGeneratedForVendor());
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendor,
                purchaseOrderData.getDispatchLocationId());
        String approvedBy = purchaseOrderData.getApprovedBy()==null ? "" :  masterDataCache.getEmployee(purchaseOrderData.getApprovedBy());
        EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(userId);
        String lastUpdatedBy = purchaseOrderData.getLastUpdatedBy() == null ? "" : masterDataCache.getEmployee(userId);
        LOG.info("last updated is : {} ",lastUpdatedBy);
        VendorPOEmailNotificationTemplate emailTemplate = new VendorPOEmailNotificationTemplate(purchaseOrderData,
                deliveryUnit, vendor, dispatchLocation.get(), props.getBasePath(), masterDataCache.getEmployee(userId),
                masterDataCache.getCompany(purchaseOrderData.getCompanyId()), props.getEnvType(),
                "This Purchase Order stands Cancelled and No production/Delivery to be entertained against it", approvedBy,employeeBasicDetail,
                lastUpdatedBy,PurchaseOrderStatus.CANCELLED.name());

        try {
            Pair<File, DocumentDetailData> poInvoice = createInvoice(emailTemplate, purchaseOrderData.getGeneratedBy(),
                    purchaseOrderData.getId());
            PurchaseOrderNotificationData sentNotification = notificationService
                    .sendCancelledPONotification(purchaseOrderData, vendor, emailTemplate, poInvoice.getFirst());
        } catch (TemplateRenderingException | IOException | DocumentException e) {
            LOG.error("Send Cancelled Po Email Notification Failure", e);
            throw new SumoException("Send Cancelled Po Email Notification Failure", e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PurchaseOrderExtendedStatusLog extendPurchaseOrder(PurchaseOrderExtendedStatusLog purchaseOrderExtendedStatusLog) throws SumoException {
        VendorDetail vendor = scmCache.getVendorDetail(purchaseOrderExtendedStatusLog.getVendorId());
        PurchaseOrderData purchaseOrderData = scmProductManagementDao.find(PurchaseOrderData.class, purchaseOrderExtendedStatusLog.getPurchaseOrderId());
        if (purchaseOrderData != null) {
            PurchaseOrderExtendedStatusLogData purchaseOrderExtendedStatusLogData = new PurchaseOrderExtendedStatusLogData();
            purchaseOrderExtendedStatusLogData.setFromStatus(purchaseOrderData.getExpiryStatus());
            purchaseOrderExtendedStatusLogData.setToStatus(PurchaseOrderExtendedStatus.EXTENDED.value());
            purchaseOrderExtendedStatusLogData.setDescription(purchaseOrderExtendedStatusLog.getDescription());
            purchaseOrderExtendedStatusLogData.setExtensionReason(purchaseOrderExtendedStatusLog.getExtensionReason());
            purchaseOrderExtendedStatusLogData.setUpdatedBy(purchaseOrderExtendedStatusLog.getUpdatedBy());
            purchaseOrderExtendedStatusLogData.setUpdateTime(AppUtils.getCurrentTimestamp());
            purchaseOrderExtendedStatusLogData.setPurchaseOrderId(purchaseOrderData.getId());

            //UPDATING PURCHASE ORDER DATA  EXPIRY DATE
            purchaseOrderData.setExpiryDate(purchaseOrderExtendedStatusLog.getUpdatedExpiryDate());
            purchaseOrderData.setExpiryStatus(PurchaseOrderExtendedStatus.EXTENDED.value());
            dao.update(purchaseOrderData, false);

            return SCMDataConverter.convert(dao.add(purchaseOrderExtendedStatusLogData, false));
        }

        return new PurchaseOrderExtendedStatusLog();
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updatePoStatus() throws SumoException,EmailGenerationException {
        //getting all orders which needs to be mark as expired
        List<PurchaseOrderData> toBeExpireOrders = dao.getAllToBeExpirePurchaseOrder();
        for (PurchaseOrderData orders : toBeExpireOrders) {
            if((orders.getStatus().equals(PurchaseOrderStatus.APPROVED.value())) || orders.getStatus().equals(PurchaseOrderStatus.IN_PROGRESS.value())) {
               if(orders.getExpiryStatus().equals(PurchaseOrderExtendedStatus.EXTENDED.value())){
                   if (orders.getAdvancePaymentDatas().isEmpty()) {
                       orders.setExpiryStatus(PurchaseOrderExtendedStatus.TERMINATED.value());
                       createAutoLogsForPurchaseOrder(orders);
                       orders.setStatus(PurchaseOrderStatus.CLOSED.value());
                       sendCLosedPoEmailNotification(orders, SCMServiceConstants.SYSTEM_USER);
                   }
               }
               else{
                   if (orders.getAdvancePaymentDatas().isEmpty()) {
                       orders.setExpiryStatus(PurchaseOrderExtendedStatus.EXPIRED.value());
                   }
               }
                dao.update(orders, false);
            }
        }
        // getting already expired orders whose 7 days has passed and they needs to be closed
        List<PurchaseOrderData> expiredOrders = dao.getAllExpiredAndNeedToClosePurchaseOrders();
        for (PurchaseOrderData purchaseOrderData : expiredOrders) {
            if (purchaseOrderData.getAdvancePaymentDatas().isEmpty()) {
                createAutoLogsForPurchaseOrder(purchaseOrderData);
                purchaseOrderData.setStatus(PurchaseOrderStatus.CLOSED.value());
                sendCLosedPoEmailNotification(purchaseOrderData, SCMServiceConstants.SYSTEM_USER);
                dao.update(purchaseOrderData, false);
            }
        }

        // getting expiring orders after 5 days and they needs to be extended
        List<PurchaseOrderData> expiringOrders = dao.getAllExpiredAndExpiringPurchaseOrders(SCMUtil.getDayBeforeOrAfterDay(SCMUtil.getCurrentBusinessDate(),+5));
        for (PurchaseOrderData purchaseOrderData : expiringOrders) {
            if((purchaseOrderData.getStatus().equals(PurchaseOrderStatus.APPROVED.value())) || purchaseOrderData.getStatus().equals(PurchaseOrderStatus.IN_PROGRESS.value())) {
                if (AppUtils.getDaysDiff(SCMUtil.getCurrentBusinessDate(),purchaseOrderData.getExpiryDate())==5) {
                    Unit deliveryUnit = masterDataCache.getUnit(purchaseOrderData.getDeliveryLocationId());
                    POExpiredEmailNotificationTemplate poExpiredEmailNotificationTemplate = new POExpiredEmailNotificationTemplate(
                            scmCache.getVendorDetail(purchaseOrderData.getGeneratedForVendor()), purchaseOrderData, deliveryUnit,
                            props.getBasePath());
                    String[] emails = {"<EMAIL>"};
                    POExpiredEmailNotification poExpiredEmailNotification = new POExpiredEmailNotification(
                            poExpiredEmailNotificationTemplate, props.getEnvType(), emails);
                    poExpiredEmailNotification.sendEmail();
                }
            }
        }
    }

    @Override
    public CapexBudgetDetail getDepartmentBudgetData(Integer unitId, String isFixedAssetOrGoods) {
        try {
            CapexBudgetDetailData capexBudgetDetailData = serviceOrderManagementDao.findBudgetUnit(unitId, isFixedAssetOrGoods);
            if (capexBudgetDetailData == null) {
                return null;
            } else {
                CapexBudgetDetail capexData = new CapexBudgetDetail();
                capexData.setDepartmentName(capexBudgetDetailData.getDepartmentName());
                capexData.setBudgetAmount(capexBudgetDetailData.getBudgetAmount());
                capexData.setOriginalAmount(capexBudgetDetailData.getOriginalAmount());
                capexData.setRemainingAmount(capexBudgetDetailData.getRemainingAmount());
                capexData.setReceivingAmount(capexBudgetDetailData.getReceivingAmount());
                capexData.setRunningAmount(capexBudgetDetailData.getRunningAmount());
                capexData.setPaidAmount(capexBudgetDetailData.getPaidAmount());
                capexData.setInitialAmount(capexBudgetDetailData.getInitialAmount());
                return capexData;
            }
        }
        catch (Exception e){
            LOG.error("Error occurred while fetching for budget details for unit Id : {} of Department {} ::: ",unitId,isFixedAssetOrGoods,e);
            return null;
        }
    }

    @Override
    public void createAutoLogsForPurchaseOrder(PurchaseOrderData purchaseOrderData) throws SumoException {
        //log for status change in po
        PurchaseOrderExtendedStatusLogData purchaseOrderExtendedStatusLogData = new PurchaseOrderExtendedStatusLogData();
        purchaseOrderExtendedStatusLogData.setFromStatus(purchaseOrderData.getStatus());
        purchaseOrderExtendedStatusLogData.setToStatus(PurchaseOrderStatus.CLOSED.value());
        purchaseOrderExtendedStatusLogData.setDescription(SCMServiceConstants.SYSTEM_USER_EXPIRY_REASON);
        purchaseOrderExtendedStatusLogData.setExtensionReason(SCMServiceConstants.SYSTEM_USER_EXPIRY_REASON);
        purchaseOrderExtendedStatusLogData.setUpdatedBy(SCMServiceConstants.SYSTEM_USER);
        purchaseOrderExtendedStatusLogData.setUpdateTime(AppUtils.getCurrentTimestamp());
        purchaseOrderExtendedStatusLogData.setPurchaseOrderId(purchaseOrderData.getId());
        dao.add(purchaseOrderExtendedStatusLogData,false);
    }


}
