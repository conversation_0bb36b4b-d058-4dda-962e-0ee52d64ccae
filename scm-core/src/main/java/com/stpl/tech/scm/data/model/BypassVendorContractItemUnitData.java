package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "BYPASS_VENDOR_CONTRACT_ITEM_UNIT")
public class BypassVendorContractItemUnitData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BYPASS_CONTRACT_ITEM_UNIT_ID", unique = true)
    private Integer bypassContractItemUnitId;

    @JoinColumn(name = "BYPASS_CONTRACT_ITEM_ID", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private BypassVendorContractItemData bypassContractItem;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "STATUS", nullable = false)
    private String Status;
}
