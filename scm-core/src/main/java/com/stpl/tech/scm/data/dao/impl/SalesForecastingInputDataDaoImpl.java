package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.data.dao.SalesForecastingInputDataDao;
import com.stpl.tech.scm.data.model.SalesForecastingInputData;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class SalesForecastingInputDataDaoImpl extends SCMAbstractDaoImpl implements SalesForecastingInputDataDao {
    @Override
    public List findByUnitIdAndDateIn(int unitId, List<Date> dates) {
        List<SalesForecastingInputData> result = new ArrayList<>();
        if(dates == null || dates.isEmpty()){
            return result;
        }
        String queryString = "FROM SalesForecastingInputData s WHERE s.unitId = :unitId AND s.dateOfOrdering IN :dates";
        Query query = manager.createQuery(queryString, SalesForecastingInputData.class);
        query.setParameter("unitId", unitId);
        query.setParameter("dates", dates);

        result= query.getResultList();
        return result;
    }
}
