package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Version;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "REVERSE_PRODUCTION_BOOKING")
public class ReverseProductionBookingData {

	private Integer bookingId;
	private Integer productId;
	private String productName;
	private Integer skuId;
	private String unitOfMeasure;
	private BigDecimal quantity;
	private int unitId;
	private BigDecimal unitPrice;
	private BigDecimal totalCost;
	private Date generationTime;
	private Date cancellationTime;
	private Date closureTime;
	private int generatedBy;
	private Integer cancelledBy;
	private String bookingStatus;
	private Integer closureEventId;
	private Date expiryDate;
	private String profile;
	private String autoBooking;
	private Integer wastageId;
	private BigDecimal inventoryPrice;
	private List<ReverseBookingConsumptionData> consumption = new ArrayList<>();
	private Integer rowVersion;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "BOOKING_ID", nullable = false, unique = true)
	public Integer getBookingId() {
		return bookingId;
	}

	public void setBookingId(Integer bookingId) {
		this.bookingId = bookingId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	@Column(name = "PRODUCT_NAME", nullable = false)
	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Column(name = "SKU_ID", nullable = false)
	public Integer getSkuId() {
		return skuId;
	}

	public void setSkuId(Integer skuId) {
		this.skuId = skuId;
	}

	@Column(name = "UOM", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@Column(name = "QUANTITY", nullable = false)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "UNIT_PRICE", nullable = true)
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	@Column(name = "TOTAL_COST", nullable = true)
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	@Column(name = "GENERATION_TIME", nullable = false)
	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}

	@Column(name = "CANCELLATION_TIME", nullable = true)
	public Date getCancellationTime() {
		return cancellationTime;
	}

	public void setCancellationTime(Date cancellationTime) {
		this.cancellationTime = cancellationTime;
	}

	@Column(name = "CLOSURE_TIME", nullable = true)
	public Date getClosureTime() {
		return closureTime;
	}

	public void setClosureTime(Date closureTime) {
		this.closureTime = closureTime;
	}

	@Column(name = "GENERATED_BY", nullable = true)
	public int getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(int generatedBy) {
		this.generatedBy = generatedBy;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public Integer getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(Integer cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	@Column(name = "BOOKING_STATUS", nullable = true)
	public String getBookingStatus() {
		return bookingStatus;
	}

	public void setBookingStatus(String bookingStatus) {
		this.bookingStatus = bookingStatus;
	}

	@Column(name = "CLOSURE_EVENT_ID", nullable = true)
	public Integer getClosureEventId() {
		return closureEventId;
	}

	public void setClosureEventId(Integer closureEventId) {
		this.closureEventId = closureEventId;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "booking")
	public List<ReverseBookingConsumptionData> getConsumption() {
		return consumption;
	}

	public void setConsumption(List<ReverseBookingConsumptionData> consumption) {
		this.consumption = consumption;
	}

	@Column(name = "EXPIRY_DATE", nullable = true)
	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	@Column(name = "PROFILE", nullable = true)
	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	@Column(name="AUTO_BOOKING",nullable = false,length =1 )
	public String getAutoBooking() {
		return autoBooking;
	}

	public void setAutoBooking(String autoBooking) {
		this.autoBooking = autoBooking;
	}

	@Column(name = "WASTAGE_ID", nullable = true)
	public Integer getWastageId() {
		return wastageId;
	}

	public void setWastageId(Integer wastageId) {
		this.wastageId = wastageId;
	}

	// PRICE ON WHICH ACTUAL INVENTORY GOT CONSUMED ON FIFO BASIS(CREATED TO CALCULATE THE DIFFERENCE OF ACTUAL INVENTORY CONSUMED PRICE AND FIFO BASIS PRICE)
	@Column(name = "INVENTORY_PRICE", nullable = true)
	public BigDecimal getInventoryPrice() {
		return inventoryPrice;
	}

	public void setInventoryPrice(BigDecimal inventoryPrice) {
		this.inventoryPrice = inventoryPrice;
	}

	@Version
	@Column(name="ROW_VERSION")
	public Integer getRowVersion() {
		return rowVersion;
	}

	public void setRowVersion(Integer rowVersion) {
		this.rowVersion = rowVersion;
	}
}
