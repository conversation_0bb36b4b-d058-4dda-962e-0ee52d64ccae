package com.stpl.tech.scm.core.util;

import com.google.common.base.Stopwatch;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.data.transport.model.*;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Log4j2
public class EWayHelper {


    public static EWayWrapper convertToEWayData(List<EwayBillData> events, String gstin,
                                                VehicleDispatchData vehicleDispatchData, MasterDataCache masterDataCache) {
        EWayWrapper data = new EWayWrapper();
        data.setGstin(gstin);
        data.setVersion("1.0.0621");
        List<EWayBillWrapper> bills = new ArrayList<>();
        for (EwayBillData event : events) {
            if (AppUtils.getStatus(event.getEwayRequired())
                    || vehicleDispatchData.getForceEway().equals(SCMServiceConstants.SCM_CONSTANT_YES)) {
                bills.add(createEWayBill(event, vehicleDispatchData, masterDataCache));
            }
        }
        data.setBillLists(bills);
        return data;
    }

    private static EWayBillWrapper createEWayBill(EwayBillData event, VehicleDispatchData vehicleDispatchData,
                                                  MasterDataCache masterDataCache) {
        EWayBillWrapper b = new EWayBillWrapper();
        TransferOrderData to = event.getTransferOrder();
        Unit sourceUnit = masterDataCache.getUnit(to.getGenerationUnitId());
        Unit receivingUnit = masterDataCache.getUnit(to.getGeneratedForUnitId());

        b.setUserGstin(sourceUnit.getTin());// user is taken as source unit
        b.setSupplyType(SupplyType.OUTWARD.getCode());
        if (to.getTransferType().equals(OrderTransferType.INVOICE.name())) {
            b.setSubSupplyType(SubSupplyType.SUPPLY.getCode());
            b.setDocType(DocumentType.INVOICE.getCode());
        } else {
            b.setSubSupplyType(SubSupplyType.FOR_OWN_USE.getCode());
            b.setDocType(DocumentType.CHALLAN.getCode());
        }


        b.setDocNo(to.getGeneratedInvoiceId());
        b.setDocDate(SCMUtil.createEWayBillDateFormat(to.getGenerationTime()));

        // FROM
        b.setFromGstin(sourceUnit.getTin());
        b.setFromTrdName(sourceUnit.getCompany().getName());
        b.setFromAddr1(getAddress1(sourceUnit.getAddress()));
        b.setFromAddr2(getAddress2(sourceUnit.getAddress()));
        b.setFromPlace(sourceUnit.getLocation().getName());
        b.setFromPincode(Integer.parseInt(sourceUnit.getAddress().getZipCode()));
        b.setFromStateCode(Integer.parseInt(sourceUnit.getLocation().getState().getCode()));
        b.setActualFromStateCode(Integer.parseInt(sourceUnit.getLocation().getState().getCode()));

        // TO
        b.setToGstin(receivingUnit.getTin());
        b.setToTrdName(receivingUnit.getCompany().getName());
        b.setToAddr1(getAddress1(receivingUnit.getAddress()));
        b.setToAddr2(getAddress2(receivingUnit.getAddress()));
        b.setToPlace(receivingUnit.getLocation().getName());
        b.setToPincode(Integer.parseInt(receivingUnit.getAddress().getZipCode()));
        b.setToStateCode(Integer.parseInt(receivingUnit.getLocation().getState().getCode()));
        b.setActualToStateCode(Integer.parseInt(receivingUnit.getLocation().getState().getCode()));

        b.setTotalValue(to.getTotalAmount().setScale(2, BigDecimal.ROUND_HALF_UP));

        for (TransferOrderTaxDetail tax : to.getOrderTaxes()) {
            if (SCMServiceConstants.IGST.equals(tax.getTaxCode())) {
                b.setIgstValue(b.getIgstValue().add(tax.getTotalTax()));
            } else if (SCMServiceConstants.CGST.equals(tax.getTaxCode())) {
                b.setCgstValue(b.getCgstValue().add(tax.getTotalTax()));
            } else if (SCMServiceConstants.SGST.equals(tax.getTaxCode())) {
                b.setSgstValue(b.getSgstValue().add(tax.getTotalTax()));
            }
        }

        b.setTransMode(TransportMode.valueOf(event.getVehicle().getTransportMode()).getCode());
        b.setTransDistance(event.getDistance().intValue());
        b.setTransporterName("");

        if (!vehicleDispatchData.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
            b.setTransDocNo(vehicleDispatchData.getDocketNumber());
            b.setTransporterId(vehicleDispatchData.getTransporterGstin());
            b.setVehicleNo("");
        } else {
            b.setTransDocNo("");
            b.setTransporterId("");
            b.setVehicleNo(vehicleDispatchData.getVehicleNumber());
        }
        b.setTransDocDate(SCMUtil.createEWayBillDateFormat(SCMUtil.getCurrentTimestamp()));
        b.setVehicleType("R");
        b.setTotInvValue(b.getTotalValue().add(b.getIgstValue()).add(b.getCgstValue())
                .add(b.getSgstValue().add(b.getCessValue())));

        // add items
        addEWayBillItems(event, b);
        b.setMainHsnCode("");
        if (Objects.nonNull(b.getItemList()) && !b.getItemList().isEmpty()) {
            b.setMainHsnCode(b.getItemList().get(0).getHsnCode());
        }
        return b;
    }

    private static void addEWayBillItems(EwayBillData event, EWayBillWrapper b) {
        List<EWayItemWrapper> eWayItems = new ArrayList<>();
        int i = 1;
        for (TransferOrderItemData item : event.getTransferOrder().getTransferOrderItemDatas()) {
            eWayItems.add(createEWayItem(item, i));
            i++;
        }
        b.setItemList(eWayItems);
    }

    private static EWayItemWrapper createEWayItem(TransferOrderItemData item, int index) {
        EWayItemWrapper i = new EWayItemWrapper();
        i.setItemNo(index);
        i.setProductName(item.getSkuName());
        i.setProductDesc(item.getSkuName());
        i.setHsnCode(item.getTaxCode());
        i.setQtyUnit(getTransferUOM(item.getUnitOfMeasure()));
        i.setQuantity(item.getTransferredQuantity().setScale(2, BigDecimal.ROUND_HALF_UP));
        if (TransportUOM.MILLI_LITRES.getCode().equals(getTransferUOM(item.getUnitOfMeasure()))) {
            i.setQuantity(
                    item.getTransferredQuantity().multiply(new BigDecimal(1000)).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        i.setTaxableAmount(item.getCalculatedAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
        for (TransferOrderItemTaxDetail tax : item.getOrderItemTaxes()) {
            if (SCMServiceConstants.IGST.equals(tax.getTaxCode())) {
                i.setIgstRate(tax.getTaxPercentage());
            } else if (SCMServiceConstants.CGST.equals(tax.getTaxCode())) {
                i.setCgstRate(tax.getTaxPercentage());
            } else if (SCMServiceConstants.SGST.equals(tax.getTaxCode())) {
                i.setSgstRate(tax.getTaxPercentage());
            }
        }
        return i;
    }

    private static String getTransferUOM(String unitOfMeasure) {
        UnitOfMeasure u = UnitOfMeasure.valueOf(unitOfMeasure);
        switch (u) {
            case L:
                return TransportUOM.MILLI_LITRES.getCode();
            case KG:
                return TransportUOM.KILO_GRAMS.getCode();
            case PC:
                return TransportUOM.PIECES.getCode();
            case SACHET:
                return TransportUOM.NUMBERS_OR_UNITS.getCode();
            case M:
                return TransportUOM.METERS.getCode();
            case SQ_FT:
                return TransportUOM.SQUARE_FEET.getCode();
            case NOS:
                return TransportUOM.NUMBERS_OR_UNITS.getCode();
            case KM:
                return TransportUOM.KILO_METERS.getCode();
            case FT:
                return null;
            case SQ_MTR:
                return TransportUOM.SQUARE_METERS.getCode();
            case PKT:
                return TransportUOM.NUMBERS_OR_UNITS.getCode();
            default:
                return null;
        }
    }

    public static String getAddress2(Address a) {
        return (!AppUtils.isBlank(a.getCity()) ? a.getCity() + " " : "")
                + (!AppUtils.isBlank(a.getState()) ? a.getState() + " " : "");
    }

    public static String getAddress1(Address a) {
        return (!AppUtils.isBlank(a.getLine1()) ? a.getLine1() + " " : "")
                + (!AppUtils.isBlank(a.getLine2()) ? a.getLine2() + " " : "")
                + (!AppUtils.isBlank(a.getLine3()) ? a.getLine3() + " " : "");
    }

    public static String getAddress(Address a) {
        return getAddress1(a) + "" + getAddress2(a);
    }

    public static Map<Integer, List<TransferOrderData>> createUnitToTransferOrderMap(
            List<TransferOrderData> transferOrders) {

        Map<Integer, List<TransferOrderData>> map = new HashMap<>();

        if (transferOrders == null || transferOrders.isEmpty()) {
            return map;
        }

        for (TransferOrderData to : transferOrders) {
            List<TransferOrderData> l = map.get(to.getGenerationUnitId());
            if (l == null) {
                l = new ArrayList<>();
                l.add(to);
                map.put(to.getGenerationUnitId(), l);
            } else {
                l.add(to);
            }
        }

        return map;
    }

    public static Map<Integer, ConsignmentData> createConsignementMap(List<ConsignmentData> consignmentList) {
        Map<Integer, ConsignmentData> map = new HashMap<>();
        if (consignmentList == null || consignmentList.isEmpty()) {
            return map;
        }
        for (ConsignmentData cd : consignmentList) {
            map.put(cd.getConsignmentUnitId(), cd);
        }
        return map;
    }

    public static String getAddress(AddressDetail a) {
        return getAddress1(a) + getAddress2(a) + "PIN: " + (!AppUtils.isBlank(a.getZipcode()) ? a.getZipcode() + " " : "");
    }

    public static String getAddress1(AddressDetail a) {
        return (!AppUtils.isBlank(a.getLine1()) ? a.getLine1() + " " : "")
                + (!AppUtils.isBlank(a.getLine2()) ? a.getLine2() + " " : "")
                + (!AppUtils.isBlank(a.getLine3()) ? a.getLine3() + " " : "");
    }

    public static String getAddress2(AddressDetail a) {
        return (!AppUtils.isBlank(a.getCity()) ? a.getCity() + " " : "")
                + (!AppUtils.isBlank(a.getState()) ? a.getState() + " " : "")
                + (!AppUtils.isBlank(a.getCountry()) ? a.getCountry() + " " : "");
    }

    public static List<EPortalWrapper> covertToEPortalData(SalesPerformaInvoice invoice, MasterDataCache masterDataCache, SCMCache scmCache, Integer distance) {
        Unit transferUnit = masterDataCache.getUnit(invoice.getSendingUnit().getId());
        VendorDetail toVendor = scmCache.getVendorDetail(invoice.getVendor().getId());

        AddressDetail dispatchLocation = toVendor.getDispatchLocations().stream()
                .filter(location -> location.getDispatchId().equals(invoice.getDispatchLocation().getId()))
                .map(VendorDispatchLocation::getAddress)
                .findFirst().orElse(toVendor.getVendorAddress());
        List<EPortalWrapper> ePortalWrappers = new ArrayList<>();
        int count = 1;
        BigDecimal cess = BigDecimal.ZERO;
        BigDecimal cgst = BigDecimal.ZERO;
        BigDecimal sgst = BigDecimal.ZERO;
        BigDecimal igst = BigDecimal.ZERO;
        String additionalChargesHsn = "";

        EPortalWrapper ePortalWrapper = new EPortalWrapper();
        ePortalWrapper.setVersion("1.1");

//	    Transfer Details
        TransferDetails transferDetails = new TransferDetails();
        transferDetails.setTaxSch(DocumentType.GOODSERVICESTAX.getCode());
        transferDetails.setSupTyp(SupplyType.BUSINESS2BUSINESS.getCode());
        transferDetails.setIgstOnIntra(transferDetails.getIgstOnIntra());
        transferDetails.setRegRev(transferDetails.getRegRev());
//        transferDetails.setEcmGstin(null);

        ePortalWrapper.setTranDtls(transferDetails);

//	    Document Details
        DocumentDetails documentDetails = new DocumentDetails();
        if (SalesPerformaType.RETURN_TO_VENDOR.equals(invoice.getType())) {
            documentDetails.setTyp(DocumentType.DEBIT_NOTE.getCode());
            documentDetails.setNo(invoice.getGeneratedDebitNoteId());
        }else if (SalesPerformaType.B2B_RETURN.equals(invoice.getType())) {
            documentDetails.setTyp(DocumentType.CREDIT_NOTE.getCode());
            documentDetails.setNo(invoice.getGeneratedCreditNoteId());
        }else {
            documentDetails.setTyp(DocumentType.INVOICE.getCode());
            documentDetails.setNo(invoice.getUploadDocId());
        }

        documentDetails.setDt((SCMUtil.createEWayBillDateFormat(invoice.getDispatchDate())));
        ePortalWrapper.setDocDtls(documentDetails);

//       Seller Details
        SellerDetails sellerDetails = new SellerDetails();

        sellerDetails.setGstin(transferUnit.getTin());
        sellerDetails.setLglNm(invoice.getSendingCompany().getName());
        sellerDetails.setAddr1(getAddress1(transferUnit.getAddress()).length() < 100 ? getAddress1(transferUnit.getAddress()) :
                getAddress1(transferUnit.getAddress()).substring(0, 100));
        sellerDetails.setAddr2(getAddress2(transferUnit.getAddress()));
        sellerDetails.setLoc(transferUnit.getLocation().getName());
        sellerDetails.setPin(Integer.parseInt(transferUnit.getAddress().getZipCode()));
        sellerDetails.setStcd((invoice.getFrom().getCode()));
        sellerDetails.setPh(transferUnit.getManagerContact());
        sellerDetails.setEm(transferUnit.getChannel());

        ePortalWrapper.setSellerDtls(sellerDetails);

//      Buyer Details
        BuyerDetails buyerDetails = new BuyerDetails();
        buyerDetails.setGstin(invoice.getDispatchLocation().getCode());
        buyerDetails.setLglNm(invoice.getVendor().getName());
        buyerDetails.setAddr1(getAddress1(dispatchLocation).length() < 100 ? getAddress1(dispatchLocation) :
                getAddress1(dispatchLocation).substring(0, 100));
        buyerDetails.setAddr2(getAddress2(dispatchLocation));
        buyerDetails.setLoc(invoice.getTo().getName());
        buyerDetails.setPin(Integer.parseInt(dispatchLocation.getZipcode()));
        buyerDetails.setPos((invoice.getTo().getCode()));
        buyerDetails.setStcd((invoice.getTo().getCode()));
        buyerDetails.setPh(toVendor.getPrimaryContact());
        buyerDetails.setEm(toVendor.getPrimaryEmail());
        
        ePortalWrapper.setBuyerDtls(buyerDetails);

//        Item List Details
        List<EPortalitemDetails> ItemList = new ArrayList<>();
        for (SalesPerformaInvoiceItem item : invoice.getItems()) {
            EPortalitemDetails ePortalitemDetails = new EPortalitemDetails();
            ePortalitemDetails.setSlNo(String.valueOf(count++));
            ePortalitemDetails.setPrdDesc(item.getSku().getName());
            ePortalitemDetails.setHsnCd(item.getSku().getCode());
            additionalChargesHsn = item.getSku().getCode();
            ePortalitemDetails.setQty(item.getQty().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setIsServc(ePortalitemDetails.getIsServc());
            ePortalitemDetails.setFreeQty(new BigDecimal(0));
            ePortalitemDetails.setUnit(getTransferUOM(item.getUom()));
            ePortalitemDetails.setUnitPrice(item.getPkgPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setTotAmt(item.getSellAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setDiscount(new BigDecimal(0));
            ePortalitemDetails.setPreTaxVal(new BigDecimal(0));
            ePortalitemDetails.setAssAmt(item.getSellAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setIgstAmt(new BigDecimal(0));
            ePortalitemDetails.setCgstAmt(new BigDecimal(0));
            ePortalitemDetails.setSgstAmt(new BigDecimal(0));
            ePortalitemDetails.setCesAmt(new BigDecimal(0));
            ePortalitemDetails.setCesRt(new BigDecimal(0));
            BigDecimal totalItemTaxRate = new BigDecimal(0);
            for (SalesPerformaItemTax tax : item.getTaxes()) {
                BigDecimal taxRate = tax.getPercent().setScale(2, BigDecimal.ROUND_HALF_UP);
                totalItemTaxRate = SCMUtil.add(totalItemTaxRate, taxRate);
                switch (tax.getType()) {
                    case CGST:
                        BigDecimal itemCgst = tax.getValue();
                        cgst = SCMUtil.add(cgst, tax.getValue());
                        ePortalitemDetails.setCgstAmt(itemCgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                        break;
                    case SGST:
                        BigDecimal itemsgst = tax.getValue();
                        sgst = SCMUtil.add(sgst, tax.getValue());
                        ePortalitemDetails.setSgstAmt(itemsgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                        break;
                    case IGST:
                        BigDecimal itemIgst = tax.getValue();
                        igst = SCMUtil.add(igst, tax.getValue());
                        ePortalitemDetails.setIgstAmt(itemIgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                        break;
                    case CESS1:
                        BigDecimal itemCess = tax.getValue();
                        cess = SCMUtil.add(cess, tax.getValue());
                        ePortalitemDetails.setCesRt(taxRate);
                        ePortalitemDetails.setCesAmt(itemCess.setScale(2, BigDecimal.ROUND_HALF_UP));
                        break;
                }
            }
            ePortalitemDetails.setOthChrg(new BigDecimal(0));
            ePortalitemDetails.setTotItemVal(SCMUtil.add(item.getSellAmount(), item.getTotalTax()).setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setGstRt(totalItemTaxRate);
            ePortalitemDetails.setStateCesAmt(new BigDecimal(0));
            ePortalitemDetails.setStateCesRt(new BigDecimal(0));
            ePortalitemDetails.setStateCesNonAdvlAmt(new BigDecimal(0));
            ItemList.add(ePortalitemDetails);
        }
//		Additional Charges At Zero Tax
        if (invoice.getAdditionalCharges().setScale(6, BigDecimal.ROUND_HALF_UP)
                .compareTo(BigDecimal.ZERO.setScale(6, BigDecimal.ROUND_HALF_UP)) > 0) {
            EPortalitemDetails ePortalitemDetails = new EPortalitemDetails();
            ePortalitemDetails.setSlNo(String.valueOf(count++));
            ePortalitemDetails.setPrdDesc("Additional Charges");
            ePortalitemDetails.setHsnCd(additionalChargesHsn);
            ePortalitemDetails.setQty(BigDecimal.ONE);
            ePortalitemDetails.setFreeQty(new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setUnit(getTransferUOM(UnitOfMeasure.PC.name()));
            ePortalitemDetails.setUnitPrice(new BigDecimal(0));
            ePortalitemDetails.setTotAmt(invoice.getAdditionalCharges().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setDiscount(new BigDecimal(0));
            ePortalitemDetails.setPreTaxVal(new BigDecimal(0));
            ePortalitemDetails.setAssAmt(invoice.getAdditionalCharges().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setCgstAmt(new BigDecimal(0));
            ePortalitemDetails.setSgstAmt(new BigDecimal(0));
            ePortalitemDetails.setIgstAmt(new BigDecimal(0));
            ePortalitemDetails.setCesAmt(new BigDecimal(0));
            ePortalitemDetails.setCesRt(new BigDecimal(0));
            ePortalitemDetails.setGstRt(new BigDecimal(0));
            ePortalitemDetails.setStateCesNonAdvlAmt(new BigDecimal(0));
            ePortalitemDetails.setOthChrg(new BigDecimal(0));
            ePortalitemDetails.setTotItemVal(invoice.getAdditionalCharges().setScale(2, BigDecimal.ROUND_HALF_UP));
            ItemList.add(ePortalitemDetails);
        }
        ePortalWrapper.setItemList(ItemList);

//       Value Details
        ValueDetails valueDetails = new ValueDetails();
        valueDetails.setCesVal(cess.setScale(2, BigDecimal.ROUND_HALF_UP));
        valueDetails.setCgstVal(cgst.setScale(2, BigDecimal.ROUND_HALF_UP));
        valueDetails.setSgstVal(sgst.setScale(2, BigDecimal.ROUND_HALF_UP));
        valueDetails.setIgstVal(igst.setScale(2, BigDecimal.ROUND_HALF_UP));
        valueDetails.setStCesVal(new BigDecimal(0));
        valueDetails.setDiscount(new BigDecimal(0));
        valueDetails.setRndOffAmt(new BigDecimal(0));

        BigDecimal totalValue = SCMUtil.add(invoice.getTotalSellingCost(), invoice.getAdditionalCharges())
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal invoiceValue = SCMUtil.add(invoice.getTotalAmount(), invoice.getAdditionalCharges())
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        valueDetails.setAssVal(totalValue);
        valueDetails.setTotInvVal(invoiceValue);

        ePortalWrapper.setValDtls(valueDetails);

//		Eway Bill Details
        EwayBillDetails ewayBillDetails = new EwayBillDetails();
        if (!invoice.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
            ewayBillDetails.setTransId(invoice.getVehicle().getRegistrationNumber());
            ewayBillDetails.setVehNo("");
        } else {
            ewayBillDetails.setTransId(null);
            ewayBillDetails.setVehNo(invoice.getVehicle().getRegistrationNumber());
        }

        int index = Objects.nonNull(invoice.getUploadDocId()) ?  invoice.getUploadDocId().lastIndexOf("/") : -1;
        String trnsDocNo = invoice.getUploadDocId();
        if (Objects.nonNull(invoice.getUploadDocId()) && index != -1) {
            trnsDocNo = invoice.getUploadDocId().substring(0, index) + invoice.getUploadDocId().substring(index + 1);
        }

        ewayBillDetails.setTransDocNo(trnsDocNo);

        ewayBillDetails.setTransName(invoice.getVehicle().getTransportMode());
        ewayBillDetails.setTransDocDt((SCMUtil.createEWayBillDateFormat(invoice.getDispatchDate())));
        ewayBillDetails.setTransMode(TransportMode.valueOf(invoice.getVehicle().getTransportMode()).getCode().toString());
        ewayBillDetails.setVehType("R");
        ewayBillDetails.setDistance(distance);

        ePortalWrapper.setEwbDtls(ewayBillDetails);

//		Reference Details
        ReferenceDetails referenceDetails = new ReferenceDetails();
        referenceDetails.setInvRm(null);
        ePortalWrapper.setRefDtls(null);
        ePortalWrappers.add(ePortalWrapper);
        return ePortalWrappers;
    }

    public static List<EPortalWrapper> covertToCorrectionEPortalData(SalesPerformaInvoice invoice,CorrectedSalesInvoiceDetails correctedInvoice, MasterDataCache masterDataCache, SCMCache scmCache, Integer distance, String type) {
        Unit transferUnit = masterDataCache.getUnit(invoice.getSendingUnit().getId());
        VendorDetail toVendor = scmCache.getVendorDetail(invoice.getVendor().getId());

        AddressDetail dispatchLocation = toVendor.getDispatchLocations().stream()
                .filter(location -> location.getDispatchId().equals(invoice.getDispatchLocation().getId()))
                .map(VendorDispatchLocation::getAddress)
                .findFirst().orElse(toVendor.getVendorAddress());
        List<EPortalWrapper> ePortalWrappers = new ArrayList<>();
        int count = 1;
        BigDecimal cess = BigDecimal.ZERO;
        BigDecimal cgst = BigDecimal.ZERO;
        BigDecimal sgst = BigDecimal.ZERO;
        BigDecimal igst = BigDecimal.ZERO;
        String additionalChargesHsn = "";

        EPortalWrapper ePortalWrapper = new EPortalWrapper();
        ePortalWrapper.setVersion("1.1");

//	    Transfer Details
        TransferDetails transferDetails = new TransferDetails();
        transferDetails.setTaxSch(DocumentType.GOODSERVICESTAX.getCode());
        transferDetails.setSupTyp(SupplyType.BUSINESS2BUSINESS.getCode());
        transferDetails.setIgstOnIntra(transferDetails.getIgstOnIntra());
        transferDetails.setRegRev(transferDetails.getRegRev());
        transferDetails.setEcmGstin(null);

        ePortalWrapper.setTranDtls(transferDetails);

//	    Document Details
        DocumentDetails documentDetails = new DocumentDetails();
        documentDetails.setTyp(DocumentType.valueOf(type).getCode());
        if(correctedInvoice.getType().equals(SalesPerformaCorrectedType.DEBIT_NOTE.value())){
            documentDetails.setNo(correctedInvoice.getGeneratedDebitNoteId());
        }else if(correctedInvoice.getType().equals(SalesPerformaCorrectedType.CREDIT_NOTE.value())){
            documentDetails.setNo(correctedInvoice.getGeneratedCreditNoteId());
        }
        documentDetails.setDt((SCMUtil.createEWayBillDateFormat(invoice.getDispatchDate())));
        ePortalWrapper.setDocDtls(documentDetails);

//       Seller Details
        SellerDetails sellerDetails = new SellerDetails();
        sellerDetails.setGstin(transferUnit.getTin());
        sellerDetails.setLglNm(invoice.getSendingCompany().getName());
        sellerDetails.setAddr1(getAddress1(transferUnit.getAddress()).length() < 100 ? getAddress1(transferUnit.getAddress()) :
                getAddress1(transferUnit.getAddress()).substring(0, 100));
        sellerDetails.setAddr2(getAddress2(transferUnit.getAddress()));
        sellerDetails.setLoc(transferUnit.getLocation().getName());
        sellerDetails.setPin(Integer.parseInt(transferUnit.getAddress().getZipCode()));
        sellerDetails.setStcd((invoice.getFrom().getCode()));
        sellerDetails.setPh(transferUnit.getManagerContact());
        sellerDetails.setEm(transferUnit.getChannel());

        ePortalWrapper.setSellerDtls(sellerDetails);

//      Buyer Details
        BuyerDetails buyerDetails = new BuyerDetails();

        buyerDetails.setGstin(invoice.getDispatchLocation().getCode());
        buyerDetails.setLglNm(invoice.getVendor().getName());
        buyerDetails.setAddr1(getAddress1(dispatchLocation).length() < 100 ? getAddress1(dispatchLocation) :
                getAddress1(dispatchLocation).substring(0, 100));
        buyerDetails.setAddr2(getAddress2(dispatchLocation));
        buyerDetails.setLoc(invoice.getTo().getName());
        buyerDetails.setPin(Integer.parseInt(dispatchLocation.getZipcode()));
        buyerDetails.setPos((invoice.getTo().getCode()));
        buyerDetails.setStcd((invoice.getTo().getCode()));
        buyerDetails.setPh(toVendor.getPrimaryContact());
        buyerDetails.setEm(toVendor.getPrimaryEmail());

        ePortalWrapper.setBuyerDtls(buyerDetails);

//        Item List Details

        List<EPortalitemDetails> ItemList = new ArrayList<>();
        for (CorrectedSalesInvoiceItemDetails item : correctedInvoice.getSalesPerformaCorrectedItems()) {

            List<SalesPerformaInvoiceItem> refItems =  invoice.getItems().stream().filter(
                    invoiceItem -> invoiceItem.getSku().getId().equals(item.getSkuId())).collect(Collectors.toList());
            SalesPerformaInvoiceItem refItem = refItems.get(0);

            EPortalitemDetails ePortalitemDetails = new EPortalitemDetails();
            ePortalitemDetails.setSlNo(String.valueOf(count++));
            SkuDefinition sku = scmCache.getSkuDefinition(item.getSkuId());
            ePortalitemDetails.setPrdDesc(sku.getSkuName());
            ePortalitemDetails.setHsnCd(sku.getSkuCode());
            additionalChargesHsn = sku.getSkuCode();
            ePortalitemDetails.setQty(item.getPkgQty().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setIsServc(ePortalitemDetails.getIsServc());
            ePortalitemDetails.setFreeQty(new BigDecimal(0));
            ePortalitemDetails.setUnit(getTransferUOM(sku.getUnitOfMeasure()));
            ePortalitemDetails.setUnitPrice(item.getRevisedPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setTotAmt(item.getRevisedAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setDiscount(new BigDecimal(0));
            ePortalitemDetails.setPreTaxVal(new BigDecimal(0));
            ePortalitemDetails.setAssAmt(item.getRevisedAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setIgstAmt(new BigDecimal(0));
            ePortalitemDetails.setCgstAmt(new BigDecimal(0));
            ePortalitemDetails.setSgstAmt(new BigDecimal(0));
            ePortalitemDetails.setCesAmt(new BigDecimal(0));
            ePortalitemDetails.setCesRt(new BigDecimal(0));
            BigDecimal totalItemTaxRate = new BigDecimal(0);
            for (SalesPerformaItemTax tax : refItem.getTaxes()) {
                BigDecimal taxRate = tax.getPercent().setScale(2, BigDecimal.ROUND_HALF_UP);
                totalItemTaxRate = SCMUtil.add(totalItemTaxRate, taxRate);
                switch (tax.getType()) {
                    case CGST:
                        BigDecimal itemCgst = tax.getValue();
                        cgst = SCMUtil.add(cgst, tax.getValue());
                        ePortalitemDetails.setCgstAmt(itemCgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                        break;
                    case SGST:
                        BigDecimal itemsgst = tax.getValue();
                        sgst = SCMUtil.add(sgst, tax.getValue());
                        ePortalitemDetails.setSgstAmt(itemsgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                        break;
                    case IGST:
                        BigDecimal itemIgst = tax.getValue();
                        igst = SCMUtil.add(igst, tax.getValue());
                        ePortalitemDetails.setIgstAmt(itemIgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                        break;
                    case CESS1:
                        BigDecimal itemCess = tax.getValue();
                        cess = SCMUtil.add(cess, tax.getValue());
                        ePortalitemDetails.setCesRt(taxRate);
                        ePortalitemDetails.setCesAmt(itemCess.setScale(2, BigDecimal.ROUND_HALF_UP));
                        break;
                }
            }
            ePortalitemDetails.setOthChrg(new BigDecimal(0));
            ePortalitemDetails.setTotItemVal(SCMUtil.add(item.getRevisedAmount(), item.getRevisedTax()).setScale(2, BigDecimal.ROUND_HALF_UP));
            ePortalitemDetails.setGstRt(totalItemTaxRate);
            ePortalitemDetails.setStateCesAmt(new BigDecimal(0));
            ePortalitemDetails.setStateCesRt(new BigDecimal(0));
            ePortalitemDetails.setStateCesNonAdvlAmt(new BigDecimal(0));
            ItemList.add(ePortalitemDetails);
        }
        ePortalWrapper.setItemList(ItemList);

//       Value Details
        ValueDetails valueDetails = new ValueDetails();
        valueDetails.setCesVal(cess.setScale(2, BigDecimal.ROUND_HALF_UP));
        valueDetails.setCgstVal(cgst.setScale(2, BigDecimal.ROUND_HALF_UP));
        valueDetails.setSgstVal(sgst.setScale(2, BigDecimal.ROUND_HALF_UP));
        valueDetails.setIgstVal(igst.setScale(2, BigDecimal.ROUND_HALF_UP));
        valueDetails.setStCesVal(new BigDecimal(0));
        valueDetails.setDiscount(new BigDecimal(0));
        valueDetails.setRndOffAmt(new BigDecimal(0));

        BigDecimal totalValue = invoice.getTotalSellingCost().setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal invoiceValue = invoice.getTotalAmount().setScale(2, BigDecimal.ROUND_HALF_UP);
        valueDetails.setAssVal(totalValue);
        valueDetails.setTotInvVal(invoiceValue);

        ePortalWrapper.setValDtls(valueDetails);

//		Eway Bill Details
        EwayBillDetails ewayBillDetails = new EwayBillDetails();
        if (!invoice.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
            ewayBillDetails.setTransId(invoice.getVehicle().getRegistrationNumber());
            ewayBillDetails.setVehNo("");
        } else {
            ewayBillDetails.setTransId(null);
            ewayBillDetails.setVehNo(invoice.getVehicle().getRegistrationNumber());
        }
        int index = invoice.getUploadDocId().lastIndexOf("/");
        String trnsDocNo = invoice.getUploadDocId();
        if (Objects.nonNull(invoice.getUploadDocId()) && index != -1) {
            trnsDocNo = invoice.getUploadDocId().substring(0, index) + invoice.getUploadDocId().substring(index + 1);
        }
        ewayBillDetails.setTransDocNo(trnsDocNo);
        ewayBillDetails.setTransName(invoice.getVehicle().getTransportMode());
        ewayBillDetails.setTransDocDt((SCMUtil.createEWayBillDateFormat(invoice.getDispatchDate())));
        ewayBillDetails.setTransMode(TransportMode.valueOf(invoice.getVehicle().getTransportMode()).getCode().toString());
        ewayBillDetails.setVehType("R");
        ewayBillDetails.setDistance(distance);

        ePortalWrapper.setEwbDtls(ewayBillDetails);

//		Reference Details
        ReferenceDetails referenceDetails = new ReferenceDetails();
        referenceDetails.setInvRm(null);
        ePortalWrapper.setRefDtls(null);
        ePortalWrappers.add(ePortalWrapper);
        return ePortalWrappers;
    }



    public static List<EPortalWrapper> covertToEPortalData(List<TransferOrder> transferOrderList, MasterDataCache masterDataCache, SCMCache scmCache) throws SumoException {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        List<EPortalWrapper> ePortalWrappers = new ArrayList<>();

            for (TransferOrder transferOrder : transferOrderList) {
                Unit transferUnit = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId());
                Unit receivingUnit = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId());

                String srcZipCode = transferUnit.getAddress().getZipCode();
                String desZipCode = receivingUnit.getAddress().getZipCode();
                BigDecimal distance = scmCache.getZipCodeDistanceMapping(srcZipCode, desZipCode);

                if(Objects.isNull(distance)){
                    throw new SumoException("Distance Mapping Not Found","Distance Mapping Not Found Between " + transferUnit.getName() +" PinCode : "+srcZipCode  +" and  " + receivingUnit.getName()+" PinCode : "+desZipCode);
                }

                Address dispatchLocation = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId()).getAddress();

                int count = 1;
                BigDecimal cess = BigDecimal.ZERO;
                BigDecimal cgst = BigDecimal.ZERO;
                BigDecimal sgst = BigDecimal.ZERO;
                BigDecimal igst = BigDecimal.ZERO;
                String additionalChargesHsn = "";

                EPortalWrapper ePortalWrapper = new EPortalWrapper();
                ePortalWrapper.setVersion("1.1");

//	    Transfer Details
                TransferDetails transferDetails = new TransferDetails();
                transferDetails.setTaxSch(DocumentType.GOODSERVICESTAX.getCode());
                transferDetails.setSupTyp(SupplyType.BUSINESS2BUSINESS.getCode());
                transferDetails.setIgstOnIntra(transferDetails.getIgstOnIntra());
                transferDetails.setRegRev(transferDetails.getRegRev());
                transferDetails.setEcmGstin(null);
//        transferDetails.setEcmGstin(null);

                ePortalWrapper.setTranDtls(transferDetails);

//	    Document Details
                DocumentDetails documentDetails = new DocumentDetails();
                documentDetails.setTyp(DocumentType.INVOICE.getCode());
                String docNo = transferOrder.getInvoiceId().substring(0,1).equalsIgnoreCase("0") ?
                        transferOrder.getInvoiceId().substring(1) : transferOrder.getInvoiceId();
                documentDetails.setNo(docNo);
                documentDetails.setDt((SCMUtil.createEWayBillDateFormat(transferOrder.getGenerationTime())));

                ePortalWrapper.setDocDtls(documentDetails);

//       Seller Details
                SellerDetails sellerDetails = new SellerDetails();
                sellerDetails.setGstin(transferUnit.getTin());
                sellerDetails.setLglNm(transferOrder.getSourceCompany().getName());
                sellerDetails.setAddr1(getAddress1(transferUnit.getAddress()).length() < 100 ? getAddress1(transferUnit.getAddress()) :
                        getAddress1(transferUnit.getAddress()).substring(0, 100));
                sellerDetails.setAddr2(getAddress2(transferUnit.getAddress()));
                sellerDetails.setLoc(transferUnit.getLocation().getName());
                sellerDetails.setPin(Integer.parseInt(transferUnit.getAddress().getZipCode()));
                sellerDetails.setStcd((transferOrder.getFromState().getCode()));
                sellerDetails.setPh(transferUnit.getManagerContact());
                sellerDetails.setEm(transferUnit.getUnitEmail());

                ePortalWrapper.setSellerDtls(sellerDetails);

//      Buyer Details
                BuyerDetails buyerDetails = new BuyerDetails();
                buyerDetails.setGstin(receivingUnit.getTin());
                buyerDetails.setLglNm(transferOrder.getReceivingCompany().getName());
                buyerDetails.setAddr1(getAddress1(dispatchLocation).length() < 100 ? getAddress1(dispatchLocation) :
                        getAddress1(dispatchLocation).substring(0, 100));
                buyerDetails.setAddr2(getAddress2(dispatchLocation));
                buyerDetails.setLoc(receivingUnit.getLocation().getName());
                buyerDetails.setPin(Integer.parseInt(dispatchLocation.getZipCode()));
                buyerDetails.setPos(transferOrder.getToState().getCode());
                buyerDetails.setStcd(transferOrder.getToState().getCode());
                buyerDetails.setPh(receivingUnit.getManagerContact());
                buyerDetails.setEm(receivingUnit.getUnitEmail());

                ePortalWrapper.setBuyerDtls(buyerDetails);

//        Item List Details
                List<EPortalitemDetails> ItemList = new ArrayList<>();
                BigDecimal totalTransferAmount = BigDecimal.ZERO;
                BigDecimal totalTax = BigDecimal.ZERO;
                for (TransferOrderItem item : transferOrder.getTransferOrderItems()) {
                    if(item.getNegotiatedUnitPrice() <= 0){
                        continue;
                    }
//                    try {
//                        BigInteger hsn = BigInteger.valueOf(Integer.parseInt(item.getCode()));
//                        Integer codeLength = item.getCode().length();
//                        if (!(codeLength == 4 || codeLength == 6 || codeLength == 8)) {
//                            throw new Exception("Invalid hsn");
//                        }
//                    } catch (Exception e) {
//                        continue;
//                    }
                    EPortalitemDetails ePortalitemDetails = new EPortalitemDetails();
                    ePortalitemDetails.setSlNo(String.valueOf(count++));
                    if(item.getSkuName().equals("Ld")){
                        ePortalitemDetails.setPrdDesc(item.getSkuName()+"_");
                    }else {
                        ePortalitemDetails.setPrdDesc(item.getSkuName());
                    }
                    String hsnCode = item.getCode();
                    if(Objects.nonNull(hsnCode) && hsnCode.contains("_Brand")){
                        hsnCode = hsnCode.replace("_Brand","");
                    }
                    ePortalitemDetails.setHsnCd(hsnCode);

                    additionalChargesHsn = item.getCode();
                    ePortalitemDetails.setQty(BigDecimal.valueOf(item.getTransferredQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    ePortalitemDetails.setIsServc(ePortalitemDetails.getIsServc());
                    ePortalitemDetails.setFreeQty(new BigDecimal(0));
                    ePortalitemDetails.setUnit(getTransferUOM(item.getUom()));
                    ePortalitemDetails.setUnitPrice(BigDecimal.valueOf(item.getNegotiatedUnitPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    ePortalitemDetails.setTotAmt(item.getTotal().setScale(2, BigDecimal.ROUND_HALF_UP));
                    ePortalitemDetails.setDiscount(new BigDecimal(0));
                    ePortalitemDetails.setPreTaxVal(new BigDecimal(0));
                    ePortalitemDetails.setAssAmt(item.getTotal().setScale(2, BigDecimal.ROUND_HALF_UP));
                    ePortalitemDetails.setIgstAmt(new BigDecimal(0));
                    ePortalitemDetails.setCgstAmt(new BigDecimal(0));
                    ePortalitemDetails.setSgstAmt(new BigDecimal(0));
                    ePortalitemDetails.setCesAmt(new BigDecimal(0));
                    ePortalitemDetails.setCesRt(new BigDecimal(0));
                    BigDecimal totalItemTaxRate = new BigDecimal(0);
                    BigDecimal gstItemTaxRate = new BigDecimal(0);
                    BigDecimal totalItemTax = new BigDecimal(0);
                    for (TaxDetail tax : item.getTaxes()) {
                        BigDecimal taxRate = tax.getPercentage().setScale(2, BigDecimal.ROUND_HALF_UP);
                        totalItemTaxRate = SCMUtil.add(totalItemTaxRate, taxRate);
                        if(Objects.nonNull(tax.getCode()) && !tax.getCode().equals("CESS1")){
                            gstItemTaxRate = SCMUtil.add(gstItemTaxRate, taxRate);
                        }
                        switch (tax.getCode()) {
                            case "CGST":
                                BigDecimal itemCgst = tax.getValue();
                                cgst = SCMUtil.add(cgst, tax.getValue());
                                ePortalitemDetails.setCgstAmt(itemCgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                                break;
                            case "SGST":
                                BigDecimal itemsgst = tax.getValue();
                                sgst = SCMUtil.add(sgst, tax.getValue());
                                ePortalitemDetails.setSgstAmt(itemsgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                                break;
                            case "IGST":
                                BigDecimal itemIgst = tax.getValue();
                                igst = SCMUtil.add(igst, tax.getValue());
                                ePortalitemDetails.setIgstAmt(itemIgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                                break;
                            case "CESS1":
                                BigDecimal itemCess = tax.getValue();
                                cess = SCMUtil.add(cess, tax.getValue());
                                ePortalitemDetails.setCesRt(taxRate);
                                ePortalitemDetails.setCesAmt(itemCess.setScale(2, BigDecimal.ROUND_HALF_UP));
                                break;
                        }
                        totalTax = AppUtils.add(totalTax, tax.getValue());
                        totalItemTax = AppUtils.add(totalItemTax, tax.getValue());
                    }
                    ePortalitemDetails.setOthChrg(new BigDecimal(0));
                    ePortalitemDetails.setTotItemVal(SCMUtil.add(item.getTotal(), totalItemTax).setScale(2, BigDecimal.ROUND_HALF_UP));
                    ePortalitemDetails.setGstRt(gstItemTaxRate);
                    ePortalitemDetails.setStateCesAmt(new BigDecimal(0));
                    ePortalitemDetails.setStateCesRt(new BigDecimal(0));
                    ePortalitemDetails.setStateCesNonAdvlAmt(new BigDecimal(0));
                    ItemList.add(ePortalitemDetails);

                    totalTransferAmount = AppUtils.add(totalTransferAmount,
                            AppUtils.multiplyWithScale10(new BigDecimal(item.getTransferredQuantity()),
                                    (item.getPrice() == null
                                            ? SCMUtil.convertToBigDecimal(item.getNegotiatedUnitPrice())
                                            : item.getPrice())));
//                    totalTax = AppUtils.add(totalTax, item.getTax());

                }
//		Additional Charges At Zero Tax
/*		if(invoice.getAdditionalCharges().setScale(6,BigDecimal.ROUND_HALF_UP)
				.compareTo(BigDecimal.ZERO.setScale(6, BigDecimal.ROUND_HALF_UP)) > 0) {
			EPortalitemDetails ePortalitemDetails = new EPortalitemDetails();
			ePortalitemDetails.setSlNo(String.valueOf(count++));
			ePortalitemDetails.setPrdDesc("Additional Charges");
			ePortalitemDetails.setHsnCd(additionalChargesHsn);
			ePortalitemDetails.setQty(BigDecimal.ONE);
			ePortalitemDetails.setFreeQty(new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP));
			ePortalitemDetails.setUnit(getTransferUOM(UnitOfMeasure.PC.name()));
			ePortalitemDetails.setUnitPrice(new BigDecimal(0));
			ePortalitemDetails.setTotAmt(invoice.getAdditionalCharges().setScale(2, BigDecimal.ROUND_HALF_UP));
			ePortalitemDetails.setDiscount(new BigDecimal(0));
			ePortalitemDetails.setPreTaxVal(new BigDecimal(0));
			ePortalitemDetails.setAssAmt(invoice.getAdditionalCharges().setScale(2, BigDecimal.ROUND_HALF_UP));
			ePortalitemDetails.setCgstAmt(new BigDecimal(0));
			ePortalitemDetails.setSgstAmt(new BigDecimal(0));
			ePortalitemDetails.setIgstAmt(new BigDecimal(0));
			ePortalitemDetails.setCesAmt(new BigDecimal(0));
			ePortalitemDetails.setCesRt(new BigDecimal(0));
			ePortalitemDetails.setGstRt(new BigDecimal(0));
			ePortalitemDetails.setStateCesNonAdvlAmt(new BigDecimal(0));
			ePortalitemDetails.setOthChrg(new BigDecimal(0));
			ePortalitemDetails.setTotItemVal(invoice.getAdditionalCharges().setScale(2, BigDecimal.ROUND_HALF_UP));
			ItemList.add(ePortalitemDetails);
		}*/
                ePortalWrapper.setItemList(ItemList);

//       Value Details
                ValueDetails valueDetails = new ValueDetails();
                valueDetails.setCesVal(cess.setScale(2, BigDecimal.ROUND_HALF_UP));
                valueDetails.setCgstVal(cgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                valueDetails.setSgstVal(sgst.setScale(2, BigDecimal.ROUND_HALF_UP));
                valueDetails.setIgstVal(igst.setScale(2, BigDecimal.ROUND_HALF_UP));
                valueDetails.setStCesVal(new BigDecimal(0));
                valueDetails.setDiscount(new BigDecimal(0));
                valueDetails.setRndOffAmt(new BigDecimal(0));
                if(Objects.isNull(transferOrder.getTotalCost())){
                    transferOrder.setTotalCost(Double.valueOf(0));
                }
                if(Objects.isNull(transferOrder.getTotalAmount())){
                    transferOrder.setTotalAmount(Float.valueOf(0));
                }
//                BigDecimal totalValue = SCMUtil.add(BigDecimal.valueOf(transferOrder.getTotalAmount()), BigDecimal.ZERO)


                BigDecimal totalValue = SCMUtil.add(totalTransferAmount, BigDecimal.ZERO)
                        .setScale(2, BigDecimal.ROUND_HALF_UP);
//                BigDecimal invoiceValue = SCMUtil.add(BigDecimal.valueOf(transferOrder.getTotalAmount()), transferOrder.getTax())

                BigDecimal invoiceValue = SCMUtil.add(totalTransferAmount, totalTax)
                        .setScale(2, BigDecimal.ROUND_HALF_UP);

                valueDetails.setAssVal(totalValue);
                valueDetails.setTotInvVal(invoiceValue);

                ePortalWrapper.setValDtls(valueDetails);

//		Eway Bill Details
                EwayBillDetails ewayBillDetails = new EwayBillDetails();
                ewayBillDetails.setDistance(distance.intValue());

                ePortalWrapper.setEwbDtls(ewayBillDetails);

//		Reference Details
                ReferenceDetails referenceDetails = new ReferenceDetails();
                referenceDetails.setInvRm(null);
                ePortalWrapper.setRefDtls(null);
                if(totalValue.intValue() > 0){
                    ePortalWrappers.add(ePortalWrapper);
                }

            }
        log.info("Step 1 : Download : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));
        return ePortalWrappers;
    }

    public static EWayWrapper convertToEWayData(SalesPerformaInvoice invoice, MasterDataCache masterDataCache, SCMCache scmCache) {
        Unit sendingUnit = masterDataCache.getUnit(invoice.getSendingUnit().getId());
        EWayWrapper wrapper = new EWayWrapper();
        wrapper.setGstin(sendingUnit.getTin());
        wrapper.setVersion("1.0.0621");
        List<EWayBillWrapper> bills = new ArrayList<>();
        EWayBillWrapper billWrapper = new EWayBillWrapper();

        List<EWayItemWrapper> itemList = new ArrayList<>();
        int count = 1;
        BigDecimal cess = BigDecimal.ZERO;
        BigDecimal cgst = BigDecimal.ZERO;
        BigDecimal sgst = BigDecimal.ZERO;
        BigDecimal igst = BigDecimal.ZERO;

        for (SalesPerformaInvoiceItem item : invoice.getItems()) {
            EWayItemWrapper itemWrapper = new EWayItemWrapper();
            itemWrapper.setItemNo(count++);
            itemWrapper.setHsnCode(item.getSku().getCode());
            itemWrapper.setProductName(item.getSku().getName());
            itemWrapper.setProductDesc(item.getSku().getName());
            itemWrapper.setQuantity(item.getQty());
            itemWrapper.setTaxableAmount(item.getSellAmount());
            itemWrapper.setQtyUnit(getTransferUOM(item.getUom()));
            for (SalesPerformaItemTax tax : item.getTaxes()) {
                BigDecimal taxRate = tax.getPercent().setScale(2, BigDecimal.ROUND_HALF_UP);
                switch (tax.getType()) {
                    case CGST:
                        cgst = SCMUtil.add(cgst, tax.getValue());
                        itemWrapper.setCgstRate(taxRate);
                        break;
                    case SGST:
                        sgst = SCMUtil.add(sgst, tax.getValue());
                        itemWrapper.setSgstRate(taxRate);
                        break;
                    case IGST:
                        igst = SCMUtil.add(igst, tax.getValue());
                        itemWrapper.setIgstRate(taxRate);
                        break;
                    case CESS1:
                        cess = SCMUtil.add(cess, tax.getValue());
                        itemWrapper.setCessRate(taxRate);
                        break;
                }

            }
            itemList.add(itemWrapper);
        }

        // add additonal charges at zero tax in the json
        if (invoice.getAdditionalCharges().setScale(6, BigDecimal.ROUND_HALF_UP)
                .compareTo(BigDecimal.ZERO.setScale(6, BigDecimal.ROUND_HALF_UP)) > 0) {
            EWayItemWrapper itemWrapper = new EWayItemWrapper();
            itemWrapper.setItemNo(count + 1);
            itemWrapper.setHsnCode("*********");
            itemWrapper.setProductName("Additional Charges");
            itemWrapper.setProductDesc("Additional Charges");
            itemWrapper.setQuantity(BigDecimal.ONE);
            itemWrapper.setTaxableAmount(invoice.getAdditionalCharges().setScale(2, BigDecimal.ROUND_HALF_UP));
            itemWrapper.setQtyUnit(getTransferUOM(UnitOfMeasure.PC.name()));
            itemList.add(itemWrapper);
        }
        billWrapper.setItemList(itemList);

        // FROM
        billWrapper.setFromGstin(sendingUnit.getTin());
        billWrapper.setFromTrdName(invoice.getSendingCompany().getName());
        billWrapper.setFromAddr1(getAddress1(sendingUnit.getAddress()));
        billWrapper.setFromAddr2(getAddress2(sendingUnit.getAddress()));
        billWrapper.setFromPlace(sendingUnit.getLocation().getName());
        billWrapper.setFromPincode(Integer.parseInt(sendingUnit.getAddress().getZipCode()));
        billWrapper.setFromStateCode(Integer.parseInt(invoice.getFrom().getCode()));
        billWrapper.setActualFromStateCode(Integer.parseInt(invoice.getFrom().getCode()));

        // TO
        VendorDetail toVendor = scmCache.getVendorDetail(invoice.getVendor().getId());
        AddressDetail dispatchLocation = toVendor.getDispatchLocations().stream()
                .filter(location -> location.getDispatchId().equals(invoice.getDispatchLocation().getId()))
                .map(VendorDispatchLocation::getAddress)
                .findFirst().orElse(toVendor.getVendorAddress());

        billWrapper.setToGstin(invoice.getDispatchLocation().getCode());
        billWrapper.setToTrdName(invoice.getVendor().getName());
        billWrapper.setToAddr1(getAddress1(dispatchLocation));
        billWrapper.setToAddr2(getAddress2(dispatchLocation));
        billWrapper.setToPlace(invoice.getTo().getName());
        billWrapper.setToPincode(Integer.parseInt(dispatchLocation.getZipcode()));
        billWrapper.setToStateCode(Integer.parseInt(invoice.getTo().getCode()));
        billWrapper.setActualToStateCode(Integer.parseInt(invoice.getTo().getCode()));

        billWrapper.setCessValue(cess.setScale(2, BigDecimal.ROUND_HALF_UP));
        billWrapper.setCgstValue(cgst.setScale(2, BigDecimal.ROUND_HALF_UP));
        billWrapper.setSgstValue(sgst.setScale(2, BigDecimal.ROUND_HALF_UP));
        billWrapper.setIgstValue(igst.setScale(2, BigDecimal.ROUND_HALF_UP));

        billWrapper.setDocDate(SCMUtil.createEWayBillDateFormat(invoice.getDispatchDate()));
        billWrapper.setDocNo(invoice.getId().toString());

        BigDecimal totalValue = SCMUtil.add(invoice.getTotalSellingCost(), invoice.getAdditionalCharges())
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal invoiceValue = SCMUtil.add(invoice.getTotalAmount(), invoice.getAdditionalCharges())
                .setScale(2, BigDecimal.ROUND_HALF_UP);

        billWrapper.setTotalValue(totalValue);
        billWrapper.setTotInvValue(invoiceValue);


        if (!invoice.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
            billWrapper.setTransDocNo(invoice.getDocketNumber());
            billWrapper.setTransporterId(invoice.getVehicle().getRegistrationNumber());
            billWrapper.setVehicleNo("");
        } else {
            billWrapper.setTransDocNo("");
            billWrapper.setTransporterId("");
            billWrapper.setVehicleNo(invoice.getVehicle().getRegistrationNumber());
        }
        billWrapper.setTransMode(TransportMode.valueOf(invoice.getVehicle().getTransportMode()).getCode());
        billWrapper.setVehicleType("R");
        billWrapper.setTransDistance(17);
        billWrapper.setUserGstin(sendingUnit.getTin());// user is taken as source unit
        billWrapper.setSupplyType(SupplyType.OUTWARD.getCode());
        billWrapper.setSubSupplyType(SubSupplyType.SUPPLY.getCode());
        billWrapper.setDocType(DocumentType.INVOICE.getCode());
        billWrapper.setMainHsnCode("");
        if (Objects.nonNull(billWrapper.getItemList()) && !billWrapper.getItemList().isEmpty()) {
            billWrapper.setMainHsnCode(billWrapper.getItemList().get(0).getHsnCode());
        }
        bills.add(billWrapper);
        wrapper.setBillLists(bills);
        return wrapper;
    }

}
