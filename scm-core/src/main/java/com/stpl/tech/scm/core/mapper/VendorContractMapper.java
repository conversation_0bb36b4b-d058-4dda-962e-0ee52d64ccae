/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.core.mapper;

import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.data.model.VendorContractItem;
import com.stpl.tech.scm.domain.model.VendorContractItemVO;
import com.stpl.tech.scm.domain.model.VendorContractVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VendorContractMapper {

    VendorContractMapper INSTANCE = Mappers.getMapper(VendorContractMapper.class);

    VendorContractVO toDomain(VendorContractInfo vendorContractInfo);
    List<VendorContractVO> toDomainList(List<VendorContractInfo> vendorContractInfo);

    VendorContractInfo toDTO(VendorContractVO vendorContractVO);
    List<VendorContractInfo> toDTO(List<VendorContractVO> vendorContractVOS);

}
