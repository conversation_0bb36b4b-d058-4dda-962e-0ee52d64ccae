package com.stpl.tech.scm.core.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.stpl.tech.scm.domain.model.*;
import org.springframework.web.multipart.MultipartFile;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexRequestDetailData;

public interface CapexManagementService {

	//List<CapexRequestDetail> getCapexRequestData();

	List<CapexAuditDetail> getCapexAuditList(Integer unitId, String version, String status);

	CapexRequestDetailData createCapexData(CapexRequestDetail capexRequestData) throws IOException, SumoException;

	boolean uploadCapexSheet(MultipartFile file, String uploadedBy) throws IOException, SumoException;

	CapexRequestDetail validateCapexSheet(MultipartFile file) throws IOException, SumoException;

	boolean approveCapexBudget(CapexAuditDetail capexAuditData, Integer userId) throws SumoException;

	boolean validateUnitForCapex(Integer unitId, String type);

	CapexAuditDetailData getCapexFileVersion(CapexAuditDetail capexAuditDetail) throws SumoException;

	CapexRequestDetailData getCapexRequest(Integer capexRequestId);

	boolean changeCapexStatus(Integer capexRequestId, String status, String userId, String comment) throws SumoException;

	List<DepartmentBudgetVO> getDepartmentData(MultipartFile file) throws IOException, SumoException;

	List<DepartmentBudgetVO> showDepartmentBudgetForApproval(Integer capexAuditId);

	boolean approvalStatusBudget(Integer auditId, String status, String type, String userId, Boolean isRequiredForL3Approval);

	Set<String> getCapexVersionList();

	List<CapexBudgetDetail> fetchBudgetDetails(Integer unitId, Integer capexRequestId);

	boolean uploadClosureSheets(MultipartFile file, String sheetName, String unitId,
			String capexRequestId);

	String getClosureComment(Integer capexRequestId);

	String initiateClosureState(Integer capexRequestId, String status, String userId, String comment) throws SumoException;

    List<BudgetComparisionDetail> getBudgetComparision (Integer unitId, Integer capexRequestId, Integer currentCapexId);

	List<ServiceOrderShort> getSOsByDepartment (Integer unitId , Integer capexRequestId, Integer departmentId , Integer bcc );

	ServiceOrderSummary getSoByCapexId(Integer capexRequestId, Integer vendorId);
	List<PurchaseOrder> getPoByCapexId(Integer capexRequestId, Integer vendorId);
	Map<Integer,Boolean> getCapexValidationBySoPo(List<Integer> soPoId, String type);

	List<ServiceOrder> getServiceOrdersByCapexAndDept(Integer capexId, Integer deptId);


    List<PurchaseOrder> getPurchaseOrderByCapexIdAndDepartment(Integer capexId, Integer deptId) throws SumoException;


	List<PoItemLevelSummary> getPoItemLevelSummary(Integer capexId, Integer deptId) throws SumoException;

	BigDecimal findPurchaseAndServiceOrderFromCapexId(Integer capexRequestId, Class<?> type) throws SumoException;
	BigDecimal getTotalUploadedAmountForCapex(Integer capexId);
}
