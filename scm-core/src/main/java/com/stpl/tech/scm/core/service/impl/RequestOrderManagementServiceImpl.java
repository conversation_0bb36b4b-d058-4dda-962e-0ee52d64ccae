package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.kettle.core.data.budget.vo.ConsumableBudgetRequest;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.master.core.UnitClosureStateEnum;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitBusinessType;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.tax.model.AdditionalTax;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.core.SCMCategoryFieldEnum;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.core.service.PurchaseOrderManagementService;
import com.stpl.tech.scm.core.service.ReferenceOrderManagementService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.service.WarehouseStockManagementService;
import com.stpl.tech.scm.core.util.AssetHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.AutoPOCreationResponse;
import com.stpl.tech.scm.core.util.model.PurchaseOrderCreateVO;
import com.stpl.tech.scm.core.util.webservice.KettleServiceClientEndpoints;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.ProductionBookingDao;
import com.stpl.tech.scm.data.dao.ProductionPlanManagementDao;
import com.stpl.tech.scm.data.dao.RequestOrderManagementDao;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;
import com.stpl.tech.scm.data.dao.SCMVendorManagementDao;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.dao.impl.RequestOrderManagementDaoImpl;
import com.stpl.tech.scm.data.model.BudgetAuditDetailData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.MultiPackagingAdjustmentsData;
import com.stpl.tech.scm.data.model.PlanOrderItemData;
import com.stpl.tech.scm.data.model.PlanOrderItemPrepData;
import com.stpl.tech.scm.data.model.PlanOrderItemPrepItemData;
import com.stpl.tech.scm.data.model.PlanOrderMappingData;
import com.stpl.tech.scm.data.model.ProductionPlanEventData;
import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.data.model.ReferenceOrderData;
import com.stpl.tech.scm.data.model.ReferenceOrderScmItemData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.data.model.UnitSkuMapping;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.CapexStatus;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MultiPackagingAdjustments;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.OrderTransferType;
import com.stpl.tech.scm.domain.model.OrdersDetailsShort;
import com.stpl.tech.scm.domain.model.POCreationType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.PlanOrderItemPrep;
import com.stpl.tech.scm.domain.model.PlanOrderItemPrepItem;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductionItemType;
import com.stpl.tech.scm.domain.model.ProductionPlanEvent;
import com.stpl.tech.scm.domain.model.ProductionPlanningSummary;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.RequestOrderResponse;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.TaxDetail;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.TransferOrderItem;
import com.stpl.tech.scm.domain.model.TransferOrderType;
import com.stpl.tech.scm.domain.model.UnitPlanItemRequest;
import com.stpl.tech.scm.domain.model.UnitWiseSummary;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorGrType;
import com.stpl.tech.scm.notification.email.CafeROEmailNotification;
import com.stpl.tech.scm.notification.email.template.CafeRoEmailNotificationTemplate;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.NoResultException;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 13-06-2016.
 */

@Service
public class RequestOrderManagementServiceImpl implements RequestOrderManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(RequestOrderManagementServiceImpl.class);

    @Autowired
    private RequestOrderManagementDao requestOrderManagementDao;
    @Autowired
    private SCMMetadataDao scmMetadataDao;

    @Autowired
    ProductionPlanManagementDao productionPlanManagementDao;

    @Autowired
    MappingCache mappingCache;

    @Autowired
    EnvProperties envProperties;

    @Autowired
    SCMNotificationService notificationService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private WarehouseStockManagementService warehouseService;

    @Autowired
    private PurchaseOrderManagementService purchaseService;

    @Autowired
    private StockManagementService stockManagementService;

    @Autowired
    private SCMVendorManagementDao vendorManagementDao;

    @Autowired
    private TaxDataCache taxCache;

    @Autowired
    private EnvProperties props;

    @Autowired
    private SCMProductManagementService scmProductManagementService;

    @Autowired
    private ProductionBookingDao productionBookingDao;

    @Autowired
    private ReferenceOrderManagementService referenceOrderManagementService;

    @Autowired
    private ServiceOrderManagementDao serviceOrderManagementDao;

    @Autowired
    private SkuMappingDao skuMappingDao;

    @Autowired
    private TransferOrderManagementService transferOrderManagementService;

    @Autowired
    private SCMAssetManagementService scmAssetManagementService;


    @Override
    public Map<Integer, Pair<BigDecimal, BigDecimal>> getCurrentPriceAtFulfillingUnit(Map<Integer,Integer> productIdsToSkuSelectedMap, Integer fulfullingUnitId,
                                                                                      Integer requestingUnitid, Boolean isFA) throws SumoException {
        List<Integer> productIds = new ArrayList<>(productIdsToSkuSelectedMap.keySet());
        Map<Integer, List<SkuDefinition>> productToSkuMap = scmProductManagementService.viewAllActiveSkuByUnitId(fulfullingUnitId,productIds);
        for(Integer productId : productToSkuMap.keySet()){
            if(!productIdsToSkuSelectedMap.get(productId).equals(Integer.valueOf(-1))) {
                SkuDefinition selectedSku = scmCache.getSkuDefinition(productIdsToSkuSelectedMap.get(productId));
                productToSkuMap.put(productId,new ArrayList<>(Arrays.asList(selectedSku)));
            }
        }
        Map<Integer, Integer> pickedSkuMap = new HashMap<>();
        List<Integer> skuIds = new ArrayList<>();
        Unit unit = masterDataCache.getUnit(fulfullingUnitId);
        Map<Integer, Pair<BigDecimal, BigDecimal>> productPriceMap = new HashMap<>();

        //setting Available Sku List For Fulfilling Unit
        productToSkuMap.keySet().forEach(productId -> {
            productToSkuMap.get(productId).forEach(sku -> {
                /*if (sku.isIsDefault()) {
                    defaultSkuMap.put(productId, sku.getSkuId());
                    //skuIds.add(sku.getSkuId());
                }*/
                skuIds.add(sku.getSkuId());
            });
        });
        LOG.info("###################### Found sku ids for  , skuIds = {} ",skuIds);
        Map<Integer, BigDecimal> currentPrices = new HashMap<>();

        if (isFA.equals(Boolean.FALSE)) {
            //Get Current Price For Default Sku At Fulfulling Unit
            LOG.info("###################### Trying to find in cost detail , skuIds = {} ",skuIds);
            currentPrices = warehouseService.getCurrentPrices(PriceUpdateEntryType.SKU, fulfullingUnitId, skuIds, true).stream()
                    .collect(Collectors.toMap(CostDetailData::getKeyId, CostDetailData::getPrice, (price1, price2) -> {
                        return price2;
                    }));
            for(Integer skuId : currentPrices.keySet()){
                LOG.info("###################### found in cost detail , skuId = {} ",skuId);
                SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuId);
                pickedSkuMap.put(skuDefinition.getLinkedProduct().getId(),skuId);
            }

        } else {
            LOG.info("###################### Trying to find in asset definition , productIds = {} ",productIds);
            List<AssetDefinition> assets = scmAssetManagementService.viewAllTransferableAssetsFromUnitByProducts(fulfullingUnitId, productIds, true);
            for (AssetDefinition assetDefinition : assets) {
                LOG.info("###################### found in asset definition , productIds = {} ",productIds);
                currentPrices.put(assetDefinition.getSkuId(), BigDecimal.valueOf(assetDefinition.getCurrentValueWithoutTax()));
                SkuDefinition skuDefinition = scmCache.getSkuDefinition(assetDefinition.getSkuId());
                pickedSkuMap.put(skuDefinition.getLinkedProduct().getId(),assetDefinition.getSkuId());
            }
        }


        //If Current Price Not Found Then FallBack To Vendor Mapping Price For Fulfulling Location .
        //If Vendor Price Too Is Not Found Then FallBack To Non Zero Negotiated Unit Price Of Product Definition..
        Map<Integer, BigDecimal> finalCurrentPrices = currentPrices;
        skuIds.forEach(skuId -> {
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuId);
            if(pickedSkuMap.containsKey(skuDefinition.getLinkedProduct().getId())){
                return;
            }

            if (!finalCurrentPrices.containsKey(skuId)) {
                LOG.info("###################### Trying to find in vendor  , skuId = {} ",skuId);
                Integer deliveryLocationId = masterDataCache.getAllLocations().get( unit.getLocation().getCode()).getId();
                List<SkuPriceDetail> skuPriceDetailList = skuMappingDao.searchPricesBySku(skuId, deliveryLocationId).stream().filter(skuPriceDetail -> skuPriceDetail.getStatus().equals(AppConstants.ACTIVE)).
                        collect(Collectors.toList());
                if (skuPriceDetailList.size() > 0) {
                    LOG.info("###################### found in vendor , skuId = {} ",skuId);
                    finalCurrentPrices.put(skuId, skuPriceDetailList.get(0).getCurrent().getValue());
                    skuDefinition = scmCache.getSkuDefinition(skuId);
                    pickedSkuMap.put(skuDefinition.getLinkedProduct().getId(),skuId);
                } else {
                    LOG.info("###################### Trying to find in product definition , skuId = {} ",skuId);
                    ProductDefinition productDefinition = scmCache.getProductDefinition(scmCache.getSkuDefinition(skuId).getLinkedProduct().getId());
                    if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(productDefinition.getNegotiatedUnitPrice())) < 0) {
                        LOG.info("###################### found in product definition , skuId = {} ",skuId);
                        finalCurrentPrices.put(skuId, BigDecimal.valueOf(productDefinition.getNegotiatedUnitPrice()));
                        skuDefinition = scmCache.getSkuDefinition(skuId);
                        pickedSkuMap.put(skuDefinition.getLinkedProduct().getId(),skuId);
                    }
                }
            } else {
                skuDefinition = scmCache.getSkuDefinition(skuId);
                pickedSkuMap.put(skuDefinition.getLinkedProduct().getId(),skuId);
            }
        });

        skuIds.forEach(skuId -> {
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuId);
            if(pickedSkuMap.containsKey(skuDefinition.getLinkedProduct().getId())){
                return;
            }
            if (!finalCurrentPrices.containsKey(skuId)) {
                LOG.info("###################### Trying to find in last transfer , skuId = {} ",skuId);
                BigDecimal negotiatedUnitPrice = requestOrderManagementDao.getLastTransferPriceBySku(fulfullingUnitId,skuId);
                if(Objects.nonNull(negotiatedUnitPrice) && negotiatedUnitPrice.compareTo(BigDecimal.ZERO) >0){
                    LOG.info("###################### found in last transfer , skuId = {} ",skuId);
                    finalCurrentPrices.put(skuId, negotiatedUnitPrice);
                    skuDefinition = scmCache.getSkuDefinition(skuId);
                pickedSkuMap.put(skuDefinition.getLinkedProduct().getId(), skuId);
            }
            }
        });

        //Sku Price Map To Product Price Map
        //In Case No price Found For Product Then Set Value As -1

        productIds.forEach(productId -> {
            if (pickedSkuMap.containsKey(productId)) {
                Integer pickedSkuId = pickedSkuMap.get(productId);
                if (finalCurrentPrices.containsKey(pickedSkuId)) {
                    BigDecimal tax = getTaxForProduct(productId, finalCurrentPrices.get(pickedSkuId), fulfullingUnitId, requestingUnitid);
                    productPriceMap.put(productId, new Pair<>(finalCurrentPrices.get(pickedSkuId), tax));
                } else {
                    productPriceMap.put(productId, new Pair<>(BigDecimal.valueOf(-1), BigDecimal.ZERO));
                }
            } else {
                productPriceMap.put(productId, new Pair<>(BigDecimal.valueOf(-1), BigDecimal.ZERO));
            }
        });

        return productPriceMap;
    }

    private BigDecimal getTaxForProduct(Integer productId, BigDecimal price, Integer fulfullingUnitId, Integer requestingUnitId) {
        State fromState = masterDataCache.getUnit(requestingUnitId).getLocation().getState();
        State toState = masterDataCache.getUnit(fulfullingUnitId).getLocation().getState();
        String code = scmCache.getProductDefinition(productId).getTaxCode();
        TaxData taxData = taxCache.getTaxData(fromState.getId(), code);
        BigDecimal tax = BigDecimal.ZERO;
        if (fromState.getId() == toState.getId()) {
            if (taxData.getState() != null && taxData.getState().getSgst().compareTo(BigDecimal.ZERO) > 0) {
                tax = tax.add(getTaxByType(price, taxData, "SGST"));
            }
            if (taxData.getState() != null && taxData.getState().getCgst().compareTo(BigDecimal.ZERO) > 0) {
                tax = tax.add(getTaxByType(price, taxData, "CGST"));
            }
        } else {
            if (taxData.getState() != null && taxData.getState().getIgst().compareTo(BigDecimal.ZERO) > 0) {
                tax = tax.add(getTaxByType(price, taxData, "IGST"));
            }

        }

        return tax;

    }

    private BigDecimal getTaxByType(BigDecimal price, TaxData taxData, String taxType) {
        BigDecimal total = AppUtils.multiplyWithScale10(price, BigDecimal.ONE);
        BigDecimal taxPercentage = getTaxPercentage(taxType, taxData);
        BigDecimal totalTax = AppUtils.multiplyWithScale10(total,
                AppUtils.divideWithScale10(taxPercentage, new BigDecimal(100)));
        if (taxData.getOthers() != null && taxData.getOthers().size() > 0) {
            for (AdditionalTax tax : taxData.getOthers()) {
                if (tax.getTax().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal otherTax = AppUtils.multiplyWithScale10(total,
                            AppUtils.divideWithScale10(tax.getTax(), new BigDecimal(100)));
                    totalTax = totalTax.add(otherTax);
                }
            }
        }
        return totalTax;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RequestOrder> getRequestOrders(Integer fulfillingUnitId, Integer requestingUnitId, Date startDate,
                                               Date endDate, SCMOrderStatus status, Integer requestOrderId, String searchTag) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getRequestOrders(fulfillingUnitId,
                requestingUnitId, startDate, endDate, status, requestOrderId, searchTag);
        return createRequestOrderObject(requestOrderDataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public RequestOrder getRequestOrder(int requestOrderId) {
        RequestOrderData requestOrderData = requestOrderManagementDao.find(RequestOrderData.class, requestOrderId);
        return SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache, masterDataCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RequestOrder> getRequestOrdersByIds(List<Integer> roIds) {
        if (roIds.size() > 1) {
            return requestOrderManagementDao.getRequestOrdersByIds(null, null, null, null, roIds).stream().
                    map(ro -> SCMDataConverter.convertFullRequestOrder(ro, scmCache, masterDataCache)).collect(Collectors.toList());
        } else {
            LOG.info("ROIds Size is 0");
            return new ArrayList<>();
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RequestOrder> getRequestOrdersFromReferenceOrder(int referenceOrderId) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao
                .getRequestOrdersFromReferenceOrder(referenceOrderId);
        return createRequestOrderObject(requestOrderDataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<RequestOrder> createRequestOrdersFromReferenceOrder(ReferenceOrderData referenceOrderData, RegularOrderEvent orderEvent)
            throws InventoryUpdateException, SumoException {
        List<RequestOrder> requestOrders = new ArrayList<RequestOrder>();
        List<RequestOrder> mailRequestOrders = new ArrayList<>();
        if (referenceOrderData.getFulfillmentUnitId() != null) {
            RequestOrderData requestOrderData = createRequestOrderDataObject(referenceOrderData,
                    referenceOrderData.getReferenceOrderScmItemDatas(), referenceOrderData.getFulfillmentUnitId());
            requestOrders.add(SCMDataConverter.convert(requestOrderData, scmCache, masterDataCache));
        } else {
            Map<FulfillmentType, List<ReferenceOrderScmItemData>> fulfillmentTypeListMap = new HashMap<FulfillmentType, List<ReferenceOrderScmItemData>>();
            for (ReferenceOrderScmItemData referenceOrderScmItemData : referenceOrderData
                    .getReferenceOrderScmItemDatas()) {
                List<ReferenceOrderScmItemData> referenceOrderScmItemDatas = fulfillmentTypeListMap
                        .get(FulfillmentType.valueOf(referenceOrderScmItemData.getFulfillmentType()));
                if (referenceOrderScmItemDatas == null) {
                    referenceOrderScmItemDatas = new ArrayList<>();
                }
                referenceOrderScmItemDatas.add(referenceOrderScmItemData);
                fulfillmentTypeListMap.put(FulfillmentType.valueOf(referenceOrderScmItemData.getFulfillmentType()),
                        referenceOrderScmItemDatas);
            }
            for (FulfillmentType fulfillmentType : fulfillmentTypeListMap.keySet()) {
                FulfillmentUnitMappingData fulfillmentUnitMappingData = requestOrderManagementDao
                        .getFulFillmentUnitByType(fulfillmentType, referenceOrderData.getRequestUnitId());
                RequestOrderData requestOrderData = createRequestOrderDataObject(referenceOrderData,
                        fulfillmentTypeListMap.get(fulfillmentType), fulfillmentUnitMappingData.getFulfillingUnitId());
                requestOrders.add(SCMDataConverter.convert(requestOrderData, scmCache, masterDataCache));
                mailRequestOrders.add(SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache, masterDataCache));
            }

            if (SCMUtil.getStatus(referenceOrderData.getRaiseBy()) || referenceOrderData.getRefOrderSource().equalsIgnoreCase("SUGGESTIVE_ORDERING")) {
                Unit unit = masterDataCache.getUnit(mailRequestOrders.get(0).getRequestUnit().getId());
                List<String> mails = new ArrayList<>();
                if (unit.getUnitEmail() != null) {
                    mails.add(unit.getUnitEmail());
                }
                if (unit.getManagerEmail() != null) {
                    mails.add(unit.getManagerEmail());
                }
                if (Objects.nonNull(unit.getUnitManager()) && Objects.nonNull(unit.getUnitManager().getEmployeeEmail())) {
                    mails.add(unit.getUnitManager().getEmployeeEmail());
                }

                try {
                    CafeRoEmailNotificationTemplate cafeRoEmailNotificationTemplate = new CafeRoEmailNotificationTemplate(
                            mailRequestOrders, unit, envProperties.getBasePath(), referenceOrderData, orderEvent, masterDataCache.getEmployee(referenceOrderData.getGeneratedBy()), null, null);
                    CafeROEmailNotification notification = new CafeROEmailNotification(cafeRoEmailNotificationTemplate,
                            envProperties.getEnvType(), mails, referenceOrderData, null);
                    generateRegularOrderExcel(mailRequestOrders, notification);
                } catch (Exception e) {
                    LOG.error("Error sending vendor notification email: " + unit.getName(), e);
                }

                for (RequestOrder requestOrdersDetails : mailRequestOrders) {
                    requestOrderNotification(requestOrdersDetails);
                }
            }
        }
        return requestOrders;
    }

    @Override
    public CellStyle getHeaderStyle(XSSFWorkbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(font);
        headerStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        return headerStyle;
    }

    public void generateRegularOrderExcel(List<RequestOrder> mailRequestOrders, CafeROEmailNotification notification) {
        Workbook workbook = new XSSFWorkbook();
        String fileName = "Regular_Orders_" + AppUtils.getCurrentTimeISTStringWithNoColons();
        try {
            for (int j = 0; j < mailRequestOrders.size(); j++) {
                Sheet sheet = workbook.createSheet();
                String name = mailRequestOrders.get(j).getFulfillmentUnit().getName() + "_" + mailRequestOrders.get(j).getId();
                workbook.setSheetName(j, name);
                int rowCount = 0;
                int columnCount = 0;
                Row row = sheet.createRow(rowCount++);
                List<String> fieldNames = new ArrayList<>();
                fieldNames.add("Product Id");
                fieldNames.add("Product Name");
                fieldNames.add("Requestd Quantity");
                fieldNames.add("UOM");
                for (String fieldName : fieldNames) {
                    Cell cell = row.createCell(columnCount++);
                    cell.setCellValue(fieldName);
                    cell.setCellStyle(getHeaderStyle((XSSFWorkbook) workbook));
                }
                List<RequestOrderItem> list = mailRequestOrders.get(j).getRequestOrderItems();
                for (int k = 0; k < list.size(); k++) {
                    Row rows = sheet.createRow(rowCount++);
                    for (int i = 0; i < columnCount; i++) {
                        Cell cell = rows.createCell(i);
                        if (i == 0) {
                            cell.setCellValue(list.get(k).getProductId());
                        } else if (i == 1) {
                            cell.setCellValue(list.get(k).getProductName());
                        } else if (i == 2) {
                            cell.setCellValue(list.get(k).getRequestedAbsoluteQuantity());
                        } else {
                            cell.setCellValue(list.get(k).getUnitOfMeasure());
                        }
                    }
                }
//                for (int i = 0; i < columnCount; i++) {
//                    sheet.autoSizeColumn(i);
//                }
            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            try {
                workbook.write(bos);
            } catch (IOException e1) {
                LOG.error("Error Occurred While writing into the Workbook... ::: ", e1);
            }

            File fileToUpload = new File(props.getBasePath() + fileName);
            byte[] barray = null;
            try {
                barray = bos.toByteArray();
            } catch (Exception e) {
                LOG.error("Error While Creating File");
            }
            try {
                LOG.info("Trying to sent  email Of Regular Orders ");
                List<AttachmentData> attachments = new ArrayList<>();
                AttachmentData roSheet = null;
                roSheet = new AttachmentData(barray, fileName,
                        AppConstants.EXCEL_MIME_TYPE);
                attachments.add(roSheet);
                notification.sendRawMail(attachments);
                fileToUpload.delete();
            } catch (Exception e) {
                LOG.info("error sending email ::: ", e);
            }
        } catch (Exception e) {
            LOG.error("error While Generating Excel ::: ", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RequestOrder> getPendingRequestOrders(Integer unitId, Date date) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao
                .getPendingAcknowledgedRequestOrders(date, unitId);
        List<RequestOrder> requestOrders = new ArrayList<RequestOrder>();
        for (RequestOrderData requestOrderData : requestOrderDataList) {
            RequestOrder requestOrder = SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache,
                    masterDataCache);
            requestOrders.add(requestOrder);
        }
        return requestOrders;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<RequestOrderResponse> createMultipleRequestOrder(RequestOrder requestOrder) throws SumoException {
        Map<Integer, Pair<UnitVendorMappingData, List<RequestOrderItem>>> vendorItems = new HashMap<>();
        List<RequestOrderResponse> response = new ArrayList<>();
        List<UnitVendorMappingData> mappings = vendorManagementDao.getUnitVendorMappingByUnitId(requestOrder.getRequestUnit().getId());
        if (requestOrder.isSpecialOrder()) {
            for (RequestOrderItem item : requestOrder.getRequestOrderItems()) {
                if (!vendorItems.containsKey(item.getVendor().getId())) {
                    UnitVendorMappingData foundMapping = null;
                    for (UnitVendorMappingData mapping : mappings) {
                        if (item.getVendor() != null && mapping.getVendorId() == item.getVendor().getId()) {
                            foundMapping = mapping;
                            break;
                        }
                    }
                    if (validateMapping(item.getVendor().getId(), requestOrder.getRequestUnit().getId(), foundMapping)) {
                        vendorItems.put(item.getVendor().getId(), new Pair<>(foundMapping, new ArrayList<>()));
                    }
                }
                vendorItems.get(item.getVendor().getId()).getValue().add(item);
            }
            IdCodeName originalFulfillmentUnit = requestOrder.getFulfillmentUnit();
            Date originalFulfillmentDate = requestOrder.getFulfillmentDate();
            for (Integer vendorId : vendorItems.keySet()) {
                //get FullFillment Unit Id mapping based on mapping
                /*
                 * run validations based on the mapping type
                 * if mapping type == SELF, just create the RO with same fullfillment unit.
                 * if mapping type == KITCHEN OR WAREHOUSE then set the fullfillment unit same as the fullfillment unit of unit for the fullfillment type and then create RO, Also create a specialized Vendor PO for the same
                 * set notification time for the RO
                 * set notification types for the RO
                 *
                 */
                UnitVendorMappingData mapping = vendorItems.get(vendorId).getKey();
                requestOrder.setRequestOrderItems(vendorItems.get(vendorId).getValue());
                requestOrder.setVendorId(vendorId);
                IdCodeName fulfillmentUnit = getFulfillmentUnit(mapping, requestOrder.getRequestUnit().getId());
                if (fulfillmentUnit != null) {
                    requestOrder.setFulfillmentUnit(fulfillmentUnit);
                } else {
                    requestOrder.setFulfillmentUnit(originalFulfillmentUnit);
                }
                requestOrder.setFulfillmentDate(originalFulfillmentDate);
                Date date = getFulfillmentDate(mapping, originalFulfillmentDate);
                Date notificationTime = getNotificationTime(mapping, date);
                if (notificationTime != null) {
                    requestOrder.setNotificationTime(notificationTime);
                    requestOrder.setNotificationTypes(getNotificationTypes(mapping));
                } else {
                    requestOrder.setNotificationTime(null);
                    requestOrder.setNotificationTypes(null);
                }
                if (!FulfillmentType.SELF.name().equals(mapping.getFulfillmentType())) {
                    // Create purchase order and if its successful then create RO
                    AutoPOCreationResponse po = purchaseService.createPurchaseOrder(
                            createPORequest(requestOrder, mapping, originalFulfillmentDate),
                            vendorItems.get(vendorId).getValue());
                    if (po.getErrors() != null && po.getErrors().size() > 0) {

                        throw new SumoException(String.format(
                                "Cannot create Purchase Order for vendor : %s and unit %s  due to the following errors : \n %s",
                                requestOrder.getRequestOrderItems().get(0).getVendor().getName(),
                                requestOrder.getFulfillmentUnit().getName(), String.join(",", po.getErrors())));

                    }
                    response.addAll(createRequestOrder(requestOrder));
                } else {
                    response.addAll(createRequestOrder(requestOrder));

                }
            }
        } else {
            throw new SumoException(
                    "Cannot create Multiple Vendor Request for orders that are not marked as specialized");
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean autoAcknowledgeAndTransferSpecializedROs(List<Integer> roIds) throws SumoException {
        LOG.info("Trying To Auto Acknowledge Special RO Orders ::::");
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getRequestOrdersByIds(null, null, null, null, roIds);
        requestOrderDataList = requestOrderDataList.stream().filter(requestOrderData -> requestOrderData.getStatus().equalsIgnoreCase(SCMOrderStatus.CREATED.value()
        )).map(requestOrderData -> {
            requestOrderData.setStatus(SCMOrderStatus.ACKNOWLEDGED.value());
            requestOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
            requestOrderData.setLastUpdatedBy(AppConstants.SYSTEM_EMPLOYEE_ID);
            return requestOrderData;
        }).collect(Collectors.toList());
        requestOrderManagementDao.addAll(requestOrderDataList);

        transferOrderManagementService.createAutoTransferForSpecializedRO(requestOrderDataList,
                AppConstants.SYSTEM_EMPLOYEE_ID);
        return true;
    }

    @Override
    public Map<Integer, Boolean> getProductDiscontinuedStatus(List<Integer> productIds, Integer fullfilmentUnitId, Date fulfilmentDate) {
        Map<Integer, Boolean> result = new HashMap<>();
        productIds.forEach(productId -> {
            result.put(productId, false);
        });
        try {
            Map<Integer, List<SkuDefinition>> skuByProduct = scmProductManagementService.viewAllSkuByProduct();
            List<Integer> skuIds = new ArrayList<>();
            for (List<SkuDefinition> skuDefinitions : skuByProduct.values()) {
                for (SkuDefinition skuDefinition : skuDefinitions) {
                    if (skuDefinition.getSkuStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)) {
                        skuIds.add(skuDefinition.getSkuId());
                    }
                }
            }
            List<UnitSkuMapping> unitSkuMappings = skuMappingDao.getActiveUnitSkuMappings(skuIds, Collections.singletonList(fullfilmentUnitId));
            Map<Integer, Pair<Date, Date>> discontinuedDates = new HashMap<>();
            unitSkuMappings.forEach(unitSkuMapping -> {
                SkuDefinition skuDefinition = scmCache.getSkuDefinition(unitSkuMapping.getSkuId());
                if (skuIds.contains(unitSkuMapping.getSkuId())) {
                    discontinuedDates.put(unitSkuMapping.getSkuId(), new Pair<>(skuDefinition.getRoDisContinuedFrom(), unitSkuMapping.getRoDisContinuedFrom()));
                }
            });
            productIds.forEach(productId -> {
                if (Objects.nonNull(skuByProduct.get(productId))) {
                    Boolean discontinuedStatus = getDiscontinuedStatus(skuByProduct.get(productId), fulfilmentDate, discontinuedDates);
                    result.put(productId, discontinuedStatus);
                }
            });
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting the RO discontinued Status :: ",e);
        }
        return result;
    }

    private Boolean getDiscontinuedStatus(List<SkuDefinition> skuDefinitions, Date fulfilmentDate, Map<Integer, Pair<Date, Date>> discontinuedDates) {
        Boolean result = false;
        List<Pair<Date, Date>> currentProductDates = new ArrayList<>();
        for (SkuDefinition skuDefinition : skuDefinitions) {
            if (discontinuedDates.containsKey(skuDefinition.getSkuId())) {
                currentProductDates.add(discontinuedDates.get(skuDefinition.getSkuId()));
            }
        }
        for (Pair<Date, Date> dates : currentProductDates) {
            if (Objects.isNull(dates.getKey())) {
                if (Objects.isNull(dates.getValue())) {
                    return true;
                } else {
                    result = fulfilmentDate.compareTo(dates.getValue()) < 0;
                    if (result) {
                        return true;
                    }
                }
            } else {
                result = fulfilmentDate.compareTo(dates.getKey()) < 0;
                if (result) {
                    return true;
                }
            }
        }
        return result;
    }

    private PurchaseOrderCreateVO createPORequest(RequestOrder requestOrder, UnitVendorMappingData mapping,
                                                  Date originalFulfillmentDate) {
        PurchaseOrderCreateVO po = new PurchaseOrderCreateVO();
        po.setComment("AUTO_GENERATED FOR RO");
        po.setCreationType(POCreationType.SYSTEM);
        po.setDeliveryUnitId(requestOrder.getFulfillmentUnit().getId());
        po.setDispatchId(mapping.getDispatchLocationId());
        Date date = getFulfillmentDate(mapping, originalFulfillmentDate);
        po.setFulfilmentDate(AppUtils.getSQLFormattedDate(date));
        po.setOrderType(VendorGrType.REGULAR_ORDER.name());
        po.setVendorId(requestOrder.getVendorId());
        po.setUserId(AppConstants.SYSTEM_EMPLOYEE_ID);
        po.setType("OPEX");
        return po;
    }

    private boolean validateMapping(int vendorId, int unitId, UnitVendorMappingData foundMapping) throws SumoException {
        VendorDetail vendor = scmCache.getVendorDetail(vendorId);
        UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitId);
        if (foundMapping == null) {
            throw new SumoException(String.format("Cannot find unit to vendor mapping for unit %s and vendor %s",
                    unit.getName(), vendor.getEntityName()));

        }
        if (foundMapping != null && (foundMapping.getFulfillmentType() == null
                || foundMapping.getFulfillmentType().trim().length() == 0)) {
            throw new SumoException(
                    String.format("fullfillment type for unit to vendor mapping for unit %s and vendor %s is missing",
                            unit.getName(), vendor.getEntityName()));
        }
        return true;
    }

    private String getNotificationTypes(UnitVendorMappingData mapping) {
        if (AppConstants.getValue(mapping.getSmsNotification())
                || AppConstants.getValue(mapping.getEmailNotification())) {
            StringBuffer buffer = new StringBuffer();
            if (AppConstants.getValue(mapping.getSmsNotification())) {
                buffer.append(NotificationType.SMS.name());
            }
            if (AppConstants.getValue(mapping.getSmsNotification())
                    && AppConstants.getValue(mapping.getEmailNotification())) {
                buffer.append(",");
            }
            if (AppConstants.getValue(mapping.getEmailNotification())) {
                buffer.append(NotificationType.EMAIL.name());
            }
            return buffer.toString();
        } else {
            return null;
        }
    }

    private Date getNotificationTime(UnitVendorMappingData mapping, Date fulfillmentDate) {
        if (AppConstants.getValue(mapping.getSmsNotification())
                || AppConstants.getValue(mapping.getEmailNotification())) {
            Date notificationDate = AppUtils.getDayBeforeOrAfterDay(fulfillmentDate,
                    -1 * (mapping.getNotifyDaysBefore() == null ? 0 : mapping.getNotifyDaysBefore()));
            String[] split = mapping.getNotificationTime().split(":");
            int hours = Integer.valueOf(split[0]);
            int mins = Integer.valueOf(split[1]);
            int millis = ((hours * 60 * 60) + (mins * 60)) * 1000;
            Date newDate = DateUtils.addMilliseconds(notificationDate, millis);
            if (newDate.before(AppUtils.getCurrentTimestamp())) {
                return null;
            } else {
                return newDate;
            }
        }
        return null;
    }

    public static void main(String[] args) {
        Date date = AppUtils.getCurrentBusinessDate();
        String time = "06:05";
        String[] split = time.split(":");
        int hours = Integer.valueOf(split[0]);
        int mins = Integer.valueOf(split[1]);
        int millis = ((hours * 60 * 60) + (mins * 60)) * 1000;
        Date newDate = DateUtils.addMilliseconds(date, millis);
        System.out.println(date);
        System.out.println(hours);
        System.out.println(mins);
        System.out.println(millis);
        System.out.println(newDate);

    }

    private IdCodeName getFulfillmentUnit(UnitVendorMappingData mapping, int requestingUnit) {
        if (!FulfillmentType.SELF.name().equals(mapping.getFulfillmentType())) {
            Integer fullFillmentUnit = scmCache
                    .getFulfillmentUnit(FulfillmentType.valueOf(mapping.getFulfillmentType()), requestingUnit);
            if (fullFillmentUnit != null) {
                UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(fullFillmentUnit);
                return new IdCodeName(unit.getId(), unit.getName(), unit.getName());
            }
        }
        return null;
    }

    private Date getFulfillmentDate(UnitVendorMappingData mapping, Date fulfillmentDate) {
        if (FulfillmentType.SELF.name().equals(mapping.getFulfillmentType())) {
            return fulfillmentDate;
        } else {
            Date date = AppUtils.getDayBeforeOrAfterDay(fulfillmentDate,
                    -1 * (mapping.getFulfillmentLeadDays() == null ? 0 : mapping.getFulfillmentLeadDays()));
            if (date.before(AppUtils.getCurrentBusinessDate())) {
                return AppUtils.getCurrentBusinessDate();
            } else {
                return date;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<RequestOrderResponse> createRequestOrder(RequestOrder requestOrder) throws SumoException {

        Unit unit = masterDataCache.getUnit(requestOrder.getRequestUnit().getId());
        if(unit.getClosure() !=null && unit.getClosure().equals(UnitClosureStateEnum.PROCESSING.name()) &&
                (Boolean.TRUE.equals(requestOrder.isSpecialOrder()) || Boolean.TRUE.equals(requestOrder.getAssetOrder()) || Boolean.TRUE.equals(requestOrder.getBulkOrder()) || Boolean.TRUE.equals(requestOrder.getSpecializedUrgentOrder()) ) ) {
            throw new SumoException("Unit Closure in processing (Only ad-hoc ordering is allowed), unit id : "+unit.getId());
        }

        List<RequestOrderResponse> orderResponses = new ArrayList<>();
        Date currentDate = AppUtils.getCurrentDate() ;
        Date handOverDate = masterDataCache.getUnit(requestOrder.getRequestUnit().getId()).getHandoverDate() ;
        Boolean isNSO = Objects.nonNull(handOverDate) && (SCMUtil.isBefore(currentDate,AppUtils.getDateAfterDays(handOverDate , 3)) ||
                AppUtils.isSameDate(currentDate,AppUtils.getDateAfterDays(handOverDate , 3))) ;
        if(isNSO)
        {
            int unitId = requestOrder.getRequestUnit().getId() ;
            List<Integer> unitIds = new ArrayList<>();
            unitIds.add(unitId) ;
           List<Integer> isPendingFaGrIds = scmMetadataDao.checkForFaReceiveing(unitIds) ;
            if(!isPendingFaGrIds.isEmpty())
            {
                throw new SumoException("Error","Fixed Assets Receiving is still pending") ;
            }
        }

        if (requestOrder.isApplyBudget() != null && requestOrder.isApplyBudget()) {
            if (requestOrder.getAssetOrder()) {
                orderResponses = applyBudgetConstraintFixedAsset(requestOrder);
            } else {
                orderResponses = applyBudgetConstraintConsumables(requestOrder);
            }
            if (orderResponses != null && orderResponses.size() > 0) {
                if (!orderResponses.get(0).isBudgetAvailable()) {
                    requestOrder.setApplyBudget(false);
                    requestOrder.setBudgetReason(AppConstants.BUDGET_UNAVAILABLE);
                } else {
                    return orderResponses;
                }
            }
        }

        requestOrder.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        requestOrder.setGenerationTime(SCMUtil.getCurrentTimestamp());
        requestOrder.setLastUpdatedBy(requestOrder.getGeneratedBy());
        requestOrder.setRequestCompany(SCMDataConverter
                .convertToIdCodeName(masterDataCache.getUnit(requestOrder.getRequestUnit().getId()).getCompany()));
        requestOrder.setFulfillmentCompany(SCMDataConverter
                .convertToIdCodeName(masterDataCache.getUnit(requestOrder.getFulfillmentUnit().getId()).getCompany()));

        if (validateSpecializedRequestOrder(requestOrder)) {
            RequestOrderData requestOrderData = SCMDataConverter.convert(requestOrder, null);
            if (Objects.nonNull(requestOrder.getTotalAmount())) {
                requestOrderData.setTotalAmount(BigDecimal.valueOf(requestOrder.getTotalAmount()));
            }
            requestOrderData = setTransferType(requestOrderData);
            requestOrderData.setSpecializedUrgentOrder(SCMUtil.setStatus(requestOrder.getSpecializedUrgentOrder()));
            requestOrderData = (RequestOrderData) requestOrderManagementDao.add(requestOrderData, true);
            if (requestOrderData != null) {
                List<RequestOrderItemData> requestOrderItemDataList = new ArrayList<RequestOrderItemData>();
                for (RequestOrderItem requestOrderItem : requestOrder.getRequestOrderItems()) {
                    RequestOrderItemData requestOrderItemData = SCMDataConverter.convert(requestOrderItem,
                            requestOrderData);
                    if (requestOrderItemData.getNegotiatedUnitPrice() != null
                            && requestOrderItemData.getRequestedQuantity() != null) {
                        requestOrderItemData.setCalculatedAmount(requestOrderItemData.getNegotiatedUnitPrice()
                                .multiply(requestOrderItemData.getRequestedQuantity()));
                    }
                    calculateTax(requestOrderData, requestOrderItemData, requestOrderItem);
                    requestOrderItemData.setTaxCode(requestOrderItem.getCode());
                    requestOrderItemData.setTaxAmount(requestOrderItem.getTax());
                    requestOrderItemData.setPackagingId(requestOrderItem.getPackagingId());
                    requestOrderItemData = requestOrderManagementDao.add(requestOrderItemData, false);
                    requestOrderManagementDao.setTaxDetail(masterDataCache.getUnit(requestOrder.getFulfillmentUnit().getId()).getLocation().getState().getId(), requestOrderItemData, requestOrderItem);
                    requestOrderItemDataList.add(requestOrderItemData);
                }
                if (Objects.nonNull(requestOrder.getType()) && requestOrder.getType().equals(CapexStatus.CAPEX.value())) {
                    updateBudgetDetails(requestOrderData.getRequestUnitId(), requestOrderData.getTotalAmount(), requestOrderData.getId()
                            , BudgetAuditActions.RO_ID, requestOrderData.getAssetOrder(), requestOrderData.getGeneratedBy(), false);
                }
                requestOrderManagementDao.flush();
                requestOrderData.setRequestOrderItemDatas(requestOrderItemDataList);
                if (requestOrder.getAssetOrder() != null && requestOrder.getAssetOrder()) {
                    notificationService.sendAssetOrderNotification(SCMDataConverter.convertFullRequestOrder(
                            requestOrderManagementDao.find(RequestOrderData.class, requestOrderData.getId()), scmCache,
                            masterDataCache));
                }
//                if (Objects.nonNull(requestOrder.getAssetOrder()) && !requestOrder.getAssetOrder() &&
//                        Objects.nonNull(requestOrder.isSpecialOrder()) && !requestOrder.isSpecialOrder()) {
//                    sendAdhocOrderEmailNotification(requestOrderData);
//                }
                RequestOrderResponse response = new RequestOrderResponse();
                response.setOrderId(requestOrderData.getId());
                response.setBudgetExceeded(false);
                orderResponses = new ArrayList<>();
                orderResponses.add(response);
                List<Integer> requestOrderId= new ArrayList<>();
                if(Objects.nonNull(response.getOrderId())){
                    requestOrderId.add(response.getOrderId());
                    if(Objects.nonNull(requestOrder.getSpecializedUrgentOrder()) && requestOrder.getSpecializedUrgentOrder().equals(Boolean.TRUE)){
                        if(autoAcknowledgeAndTransferSpecializedROs(requestOrderId)){
                            response.setUrgentSpecializedOrder(requestOrder.getSpecializedUrgentOrder());
                            return orderResponses;
                        }
                        else {
                            throw new SumoException(String.format("Exception while making urgent specialized order with :: %s",response.getOrderId()));
                        }
                    }
                }
                return orderResponses;
            }
        }
        return orderResponses;
    }

    @Override
    public Boolean updateBudgetDetails(Integer requestUnitId, BigDecimal totalAmount, Integer id, BudgetAuditActions actionType,
                                       String isAssetOrder, Integer generatedBy, Boolean isCancelled) {
        try {
            LOG.info("Total  Amount For Budget is :::::: {} ", totalAmount);
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(requestUnitId);
            String departmentName = Boolean.valueOf(AppUtils.getStatus(isAssetOrder)).equals(Boolean.TRUE) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(requestUnitId, departmentName);
            List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(), BudgetAuditActions.RUNNING_AMOUNT.value());
            for (String action : actions) {
                BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
                budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
                budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
                budgetAuditDetail.setActionBy(generatedBy);
                if (isCancelled.equals(Boolean.FALSE)) {
                    budgetAuditDetail.setAction(BudgetAuditActions.CREATED.value());
                } else {
                    budgetAuditDetail.setAction(BudgetAuditActions.CANCELLED.value());
                }
                budgetAuditDetail.setKeyType(actionType.value());
                budgetAuditDetail.setKeyValue(id);
                budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
                if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
                    if (isCancelled.equals(Boolean.FALSE)) {
                        budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().subtract(totalAmount));
                        String budgetAction = totalAmount.compareTo(BigDecimal.ZERO) >= 0 ? BudgetAuditActions.REDUCTION.value() :
                                BudgetAuditActions.ADDITION.value();
                        budgetAuditDetail.setActionType(budgetAction);
                    } else {
                        budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(totalAmount));
                        budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                    }
                } else {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                    if (isCancelled.equals(Boolean.FALSE)) {
                        String budgetAction = totalAmount.compareTo(BigDecimal.ZERO) < 0 ? BudgetAuditActions.REDUCTION.value() :
                                BudgetAuditActions.ADDITION.value();
                        budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().add(totalAmount));
                        budgetAuditDetail.setActionType(budgetAction);
                    } else {
                        budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(totalAmount));
                        budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                    }
                }
                serviceOrderManagementDao.add(budgetAuditDetail, true);
            }
            if (isCancelled.equals(Boolean.FALSE)) {
                capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().subtract(totalAmount));
                capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().add(totalAmount));
            } else {
                capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(totalAmount));
                capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(totalAmount));
            }
            CapexBudgetDetailData finalData = serviceOrderManagementDao.update(capexBudgetData, true);
            if (!purchaseService.validateBudgetAmounts(finalData)) {
                LOG.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        } catch (Exception e) {
            LOG.error("Exception Occurred while updating budget for Request Order ::: ", e);
            return false;
        }
    }


    private void sendAdhocOrderEmailNotification(RequestOrderData requestOrderData) {
        try {
            LOG.info("This is an Adhoc Order..!");
            Boolean isFountain9Enabled = getFountain9Enabled(requestOrderData.getRequestUnitId());
            if (isFountain9Enabled) {
                RequestOrderData lastRoData = requestOrderManagementDao.getLastAdhocF9Order(requestOrderData);
                Map<Integer, BigDecimal> lastRoMap = new HashMap<>();
                if (Objects.nonNull(lastRoData)) {
                    LOG.info("Got last RO Id is : {}", lastRoData.getId());
                    lastRoMap = makeLastROProductMap(lastRoData);
                }
                List<RequestOrder> mailRequestOrders = new ArrayList<>();
                mailRequestOrders.add(SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache, masterDataCache));
                Unit unit = masterDataCache.getUnit(mailRequestOrders.get(0).getRequestUnit().getId());
                List<String> mails = new ArrayList<>();
                if (unit.getUnitEmail() != null) {
                    mails.add(unit.getUnitEmail());
                }
                if (unit.getManagerEmail() != null) {
                    mails.add(unit.getManagerEmail());
                }
                if (unit.getUnitManager().getEmployeeEmail() != null) {
                    mails.add(unit.getUnitManager().getEmployeeEmail());
                }
                CafeRoEmailNotificationTemplate cafeRoEmailNotificationTemplate = new CafeRoEmailNotificationTemplate(
                        mailRequestOrders, unit, envProperties.getBasePath(), null, null, masterDataCache.getEmployee(requestOrderData.getGeneratedBy()),
                        lastRoMap, lastRoData);
                CafeROEmailNotification notification = new CafeROEmailNotification(cafeRoEmailNotificationTemplate,
                        envProperties.getEnvType(), mails, null, requestOrderData);
                generateRegularOrderExcel(mailRequestOrders, notification);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While Sending Adhoc Order Mail :: ", e);
        }
    }

    private Map<Integer, BigDecimal> makeLastROProductMap(RequestOrderData lastRoData) {
        Map<Integer, BigDecimal> result = new HashMap<>();
        for (RequestOrderItemData itemData : lastRoData.getRequestOrderItemDatas()) {
            result.put(itemData.getProductId(), itemData.getRequestedAbsoluteQuantity());
        }
        return result;
    }

    private Boolean getFountain9Enabled(Integer requestUnitId) {
        List<Integer> unitsIds = referenceOrderManagementService.getFountain9Units(requestUnitId, false);
        return unitsIds.contains(requestUnitId);
    }


    private void calculateTax(RequestOrderData requestOrderData, RequestOrderItemData requestOrderItemData, RequestOrderItem requestOrderItem) {
        State fromState = masterDataCache.getUnit(requestOrderData.getRequestUnitId()).getLocation().getState();
        State toState = masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId()).getLocation().getState();
        String code = scmCache.getProductDefinition(requestOrderItemData.getProductId()).getTaxCode();
        requestOrderItem.setCode(code);
        TaxData taxData = taxCache.getTaxData(fromState.getId(), code);
        if (fromState.getId() == toState.getId()) {
            if (taxData.getState() != null && taxData.getState().getSgst().compareTo(BigDecimal.ZERO) > 0) {
                setTaxes(requestOrderItemData, requestOrderItem, taxData, "SGST");
            }
            if (taxData.getState() != null && taxData.getState().getCgst().compareTo(BigDecimal.ZERO) > 0) {
                setTaxes(requestOrderItemData, requestOrderItem, taxData, "CGST");
            }
        } else {
            if (taxData.getState() != null && taxData.getState().getIgst().compareTo(BigDecimal.ZERO) > 0) {
                setTaxes(requestOrderItemData, requestOrderItem, taxData, "IGST");
            }

        }


    }

    private void setTaxes(RequestOrderItemData item, RequestOrderItem requestOrderItem, TaxData taxData, String taxType) {
        requestOrderItem.setTotal(AppUtils.multiplyWithScale10(item.getNegotiatedUnitPrice(), item.getRequestedQuantity()));
        BigDecimal taxPercentage = getTaxPercentage(taxType, taxData);
        if (requestOrderItem.getTaxes() != null && requestOrderItem.getTaxes().size() > 0) {
            requestOrderItem.getTaxes().add(convert(requestOrderItem, taxPercentage, taxType, "GST"));
        } else {
            requestOrderItem.setTaxes(new ArrayList(Arrays.asList(convert(requestOrderItem, taxPercentage, taxType, "GST"))));
        }
        if (taxData.getOthers() != null && taxData.getOthers().size() > 0) {
            for (AdditionalTax tax : taxData.getOthers()) {
                if (tax.getTax().compareTo(BigDecimal.ZERO) > 0) {
                    requestOrderItem.getTaxes().add(convert(requestOrderItem, tax.getTax(), tax.getType(), tax.getType()));
                }
            }
        }
    }


    private TaxDetail convert(RequestOrderItem item, BigDecimal percentage, String code,
                              String type) {
        TaxDetail d = new TaxDetail();
        d.setCode(code);
        d.setPercentage(percentage);
        BigDecimal tax = AppUtils.multiplyWithScale10(item.getTotal(),
                AppUtils.divideWithScale10(percentage, new BigDecimal(100)));
        d.setTaxable(item.getTotal());
        d.setTotal(item.getTotal());
        d.setType(type);
        d.setValue(tax);
        item.setTax(AppUtils.add(item.getTax(), tax));
        return d;
    }

    private BigDecimal getTaxPercentage(String taxType, TaxData taxData) {
        BigDecimal percentage = BigDecimal.ZERO;
        switch (taxType) {
            case "IGST":
                percentage = percentage.add(taxData.getState().getIgst());
                break;
            case "SGST":
                percentage = percentage.add(taxData.getState().getSgst());
                break;
            case "CGST":
                percentage = percentage.add(taxData.getState().getCgst());
                break;
        }
        return percentage;
    }

    private RequestOrderData setTransferType(RequestOrderData requestOrderData) {
        Unit requestingUnit = masterDataCache.getUnit(requestOrderData.getRequestUnitId());
        OrderTransferType transferType = requestingUnit.getUnitBusinessType().equals(UnitBusinessType.FIFO) ?
                OrderTransferType.INVOICE : OrderTransferType.TRANSFER;
        requestOrderData.setTransferType(transferType.name());
        return requestOrderData;
    }

    private List<RequestOrderResponse> applyBudgetConstraintConsumables(RequestOrder requestOrder) {

        ConsumablesAggregate requestedConsumables = new ConsumablesAggregate();
        HashMap<Integer, List<String>> categoryMap = new HashMap<>();
        BigDecimal consumable = BigDecimal.ZERO;
        BigDecimal consumableMarketing = BigDecimal.ZERO;
        BigDecimal consumableOthers = BigDecimal.ZERO;

        ConsumablesAggregate aggregate = stockManagementService
                .getConsumablesAggregateForMonth(requestOrder.getRequestUnit().getId());

        for (RequestOrderItem item : requestOrder.getRequestOrderItems()) {
            BigDecimal calculatedAmount = BigDecimal.ZERO;
            if (item.getNegotiatedUnitPrice() != null) {
                calculatedAmount = SCMUtil
                        .convertToBigDecimal(item.getNegotiatedUnitPrice() * item.getRequestedQuantity());
            }

            int subCategoryId = scmCache.getProductDefinition(item.getProductId()).getSubCategoryDefinition().getId();

            switch (subCategoryId) {
                case SCMServiceConstants.SUB_CATEGORY_UTILITY:
                case SCMServiceConstants.SUB_CATEGORY_STATIONERY:
                case SCMServiceConstants.SUB_CATEGORY_UNIFORM:
                case SCMServiceConstants.SUB_CATEGORY_EQUIPMENT:
                case SCMServiceConstants.SUB_CATEGORY_CUTLERY:
                    consumable = AppUtils.add(consumable, calculatedAmount);
                    break;
                case SCMServiceConstants.SUB_CATEGORY_MARKETING:
                    consumableMarketing = AppUtils.add(consumableMarketing, calculatedAmount);
                    break;
                default:
                    consumableOthers = AppUtils.add(consumableOthers, calculatedAmount);
                    subCategoryId = -1;
                    break;
            }
            if (categoryMap.get(subCategoryId) == null) {
                categoryMap.put(subCategoryId, new ArrayList<>());
            }
            categoryMap.get(subCategoryId).add(item.getProductName());
        }

        requestedConsumables.setConsumable(consumable);
        requestedConsumables.setConsumableMarketing(consumableMarketing);
        requestedConsumables.setConsumableOthers(consumableOthers);

        ConsumableBudgetRequest request = new ConsumableBudgetRequest();
        request.setCurrentAggregate(aggregate);
        request.setRequestedAggregate(requestedConsumables);
        request.setUnitId(requestOrder.getRequestUnit().getId());
        request.setCreatedBy(requestOrder.getGeneratedBy().getId());
        request.setFixedAsset(false);
        Map<String, Map<String, Double>> responseMap = getBudgetConstraintDetails(request);

        if (responseMap.size() > 0) {
            return getResponse(responseMap, categoryMap);
        }
        return null;
    }

    private List<RequestOrderResponse> applyBudgetConstraintFixedAsset(RequestOrder requestOrder) {

        ConsumablesAggregate requestedConsumables = new ConsumablesAggregate();
        HashMap<Integer, List<String>> categoryMap = new HashMap<>();
        BigDecimal fixedAssets = BigDecimal.ZERO;

        ConsumablesAggregate aggregate = stockManagementService
                .getFixedAssetAggregateForMonth(requestOrder.getRequestUnit().getId());

        for (RequestOrderItem item : requestOrder.getRequestOrderItems()) {
            ProductDefinition definition = scmCache.getProductDefinition(item.getProductId());
            if (definition.isParticipatesInPnl()) {
                BigDecimal calculatedAmount = BigDecimal.ZERO;
                if (item.getNegotiatedUnitPrice() != null) {
                    calculatedAmount = SCMUtil
                            .convertToBigDecimal(item.getNegotiatedUnitPrice() * item.getRequestedQuantity());
                }
                if (categoryMap.get(SCMServiceConstants.CATEGORY_FIXED_ASSETS) == null) {
                    categoryMap.put(SCMServiceConstants.CATEGORY_FIXED_ASSETS, new ArrayList<>());
                }
                if (definition.getCategoryDefinition().getId() == SCMServiceConstants.CATEGORY_FIXED_ASSETS) {
                    fixedAssets = AppUtils.add(fixedAssets, calculatedAmount);
                    categoryMap.get(SCMServiceConstants.CATEGORY_FIXED_ASSETS).add(item.getProductName());
                }
            }
        }

        requestedConsumables.setFixedAssets(fixedAssets);

        ConsumableBudgetRequest request = new ConsumableBudgetRequest();
        request.setCurrentAggregate(aggregate);
        request.setRequestedAggregate(requestedConsumables);
        request.setUnitId(requestOrder.getRequestUnit().getId());
        request.setCreatedBy(requestOrder.getGeneratedBy().getId());
        request.setFixedAsset(true);
        Map<String, Map<String, Double>> responseMap = getBudgetConstraintDetails(request);

        if (responseMap.size() > 0) {
            return getResponse(responseMap, categoryMap);
        }
        return null;
    }

    private List<RequestOrderResponse> getResponse(Map<String, Map<String, Double>> resultMap,
                                                   HashMap<Integer, List<String>> categoryMap) {
        List<RequestOrderResponse> responses = new ArrayList<>();

        if (resultMap.get(AppConstants.BUDGET_UNAVAILABLE) != null) {
            RequestOrderResponse response = new RequestOrderResponse();
            response.setBudgetAvailable(false);
            responses.add(response);
            return responses;
        }

        for (Map.Entry<String, Map<String, Double>> entry : resultMap.entrySet()) {
            RequestOrderResponse response = new RequestOrderResponse();
            response.setBudgetExceeded(true);
            response.setBudgetAvailable(true);
            response.setBudgetAmount(SCMUtil.convertToBigDecimal(entry.getValue().get("budgetAmount")));
            response.setCurrentAmount(SCMUtil.convertToBigDecimal(entry.getValue().get("currentAmount")));
            response.setRequestedAmount(SCMUtil.convertToBigDecimal(entry.getValue().get("requestedAmount")));

            if (SCMCategoryFieldEnum.SUB_CATEGORY_UTILITY.getKey().equals(entry.getKey())) {
                response.getProducts().addAll(categoryMap.get(SCMCategoryFieldEnum.SUB_CATEGORY_UTILITY.getValue()));
            } else if (SCMCategoryFieldEnum.SUB_CATEGORY_STATIONERY.getKey().equals(entry.getKey())) {
                response.getProducts().addAll(categoryMap.get(SCMCategoryFieldEnum.SUB_CATEGORY_STATIONERY.getValue()));
            } else if (SCMCategoryFieldEnum.SUB_CATEGORY_UNIFORM.getKey().equals(entry.getKey())) {
                response.getProducts().addAll(categoryMap.get(SCMCategoryFieldEnum.SUB_CATEGORY_UNIFORM.getValue()));
            } else if (SCMCategoryFieldEnum.SUB_CATEGORY_EQUIPMENT.getKey().equals(entry.getKey())) {
                response.getProducts().addAll(categoryMap.get(SCMCategoryFieldEnum.SUB_CATEGORY_EQUIPMENT.getValue()));
            } else if (SCMCategoryFieldEnum.SUB_CATEGORY_CUTLERY.getKey().equals(entry.getKey())) {
                response.getProducts().addAll(categoryMap.get(SCMCategoryFieldEnum.SUB_CATEGORY_CUTLERY.getValue()));
            } else if (SCMCategoryFieldEnum.SUB_CATEGORY_MARKETING.getKey().equals(entry.getKey())) {
                response.getProducts().addAll(categoryMap.get(SCMCategoryFieldEnum.SUB_CATEGORY_MARKETING.getValue()));
            } else if (SCMCategoryFieldEnum.SUB_CATEGORY_OTHERS.getKey().equals(entry.getKey())) {
                response.getProducts().addAll(categoryMap.get(SCMCategoryFieldEnum.SUB_CATEGORY_OTHERS.getValue()));
            } else if (SCMCategoryFieldEnum.CATEGORY_FIXED_ASSETS.getKey().equals(entry.getKey())) {
                response.getProducts().addAll(categoryMap.get(SCMCategoryFieldEnum.CATEGORY_FIXED_ASSETS.getValue()));
            }
            responses.add(response);
        }
        return responses;
    }

    @SuppressWarnings("unchecked")
    private Map<String, Map<String, Double>> getBudgetConstraintDetails(ConsumableBudgetRequest request) {
        Map<String, Map<String, Double>> resultMap = new HashMap<>();

        try {
            resultMap = WebServiceHelper.postRequestWithAuthInternalNoTimeout(getBudgetConstarintEndPoint(), request,
                    Map.class, props.getAuthToken());
            if (resultMap == null) {
                resultMap = new HashMap<>();
            }
            LOG.info("result of api call for budget details : " + resultMap);
        } catch (IOException e) {
            LOG.info("Error while rest api api call of budget restriction : " + e);
        }
        return resultMap;
    }

    private String getBudgetConstarintEndPoint() {
        return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.APPLY_BUDGET_CONSTRAINT;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer cancelRequestOrder(int requestOrderId, Integer updatedBy) throws SumoException {
        RequestOrderData requestOrderData = requestOrderManagementDao.find(RequestOrderData.class, requestOrderId);
        if (requestOrderData != null) {
            if(Objects.nonNull(requestOrderData.getIsNotified()) && requestOrderData.getIsNotified().equals(AppUtils.setStatus(true))){
                throw new SumoException("Can't Cancel RO !!","This Ro Is Notified To Vendor !!");
            }
            if (requestOrderData.getStatus().equals(SCMOrderStatus.CREATED.value())
                    || requestOrderData.getStatus().equals(SCMOrderStatus.INITIATED.value())) {
                requestOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                requestOrderData.setStatus(SCMOrderStatus.CANCELLED.value());
                requestOrderData.setLastUpdatedBy(updatedBy);
                if (Objects.nonNull(requestOrderData.getType()) && requestOrderData.getType().equals(CapexStatus.CAPEX.value())) {
                    updateBudgetDetails(requestOrderData.getRequestUnitId(), requestOrderData.getTotalAmount(), requestOrderId, BudgetAuditActions.RO_ID,
                            requestOrderData.getAssetOrder(), requestOrderData.getGeneratedBy(), true);
                }
                requestOrderData = (RequestOrderData) requestOrderManagementDao.update(requestOrderData, true);
                if (requestOrderData != null) {
                    return requestOrderData.getId();
                } else {
                    LOG.error("Error cancelling request order with id: {}", requestOrderId);
                    return null;
                }
            } else {
                LOG.error("Request order with id: {} has been acknowledged hence can't be cancelled!", requestOrderId);
            }
        } else {
            LOG.error("Request order with id: {} not found!", requestOrderId);
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<RequestOrder> getPendingRequestOrders(String date, int fulfilmentUnit) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getPendingRequestOrders(date,
                fulfilmentUnit);
        return getRequestOrders(requestOrderDataList, true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<OrdersDetailsShort> getPendingRoShort(Integer unitId, Date date) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao
                .getPendingAcknowledgedRequestOrders(date, unitId);
        List<OrdersDetailsShort> ordersDetailsShorts = new ArrayList();
        for (RequestOrderData requestOrderData : requestOrderDataList) {
            ordersDetailsShorts.add(SCMDataConverter.convertShort(requestOrderData, masterDataCache));
            ;
        }
        return ordersDetailsShorts;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<OrdersDetailsShort> getPendingRequestOrdersShort(String date, int fulfilmentUnit, boolean fetchAcknowledged) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getPendingRequestOrders(date,
                fulfilmentUnit);
        /*GNT FUNCTION */
        Set<Integer> gntData = getGNTProductId(requestOrderDataList);
        List<OrdersDetailsShort> ordersDetailsShorts = new ArrayList();
        if (!fetchAcknowledged) {
            for (RequestOrderData requestOrderData : requestOrderDataList) {
                if (!requestOrderData.getStatus().equals(SCMOrderStatus.ACKNOWLEDGED.value())) {
                    ordersDetailsShorts.add(SCMDataConverter.convertShort(requestOrderData, masterDataCache));
                }
            }
        } else {
            for (RequestOrderData requestOrderData : requestOrderDataList) {
                ordersDetailsShorts.add(SCMDataConverter.convertShort(requestOrderData, masterDataCache));
            }
        }
        addGNTToOrderDetail(ordersDetailsShorts, gntData);
        return ordersDetailsShorts;
    }

    private void addGNTToOrderDetail(List<OrdersDetailsShort> ordersDetailsShorts, Set<Integer> gntData) {
        for (OrdersDetailsShort r : ordersDetailsShorts) {
            for (Integer data : gntData) {
                if (r.getId().equals(data)) {
                    r.setGNTFlag(true);
                }
            }
        }
    }

    private Set<Integer> getGNTProductId(List<RequestOrderData> requestOrderDataList) {
        Set<Integer> gntData = new HashSet<>();
        for (RequestOrderData r : requestOrderDataList) {
            List<RequestOrderItemData> orderItem = (List<RequestOrderItemData>) r.getRequestOrderItemDatas();
            for (RequestOrderItemData data : orderItem) {
                String s = data.getProductName();
                if (s.contains("GnT") || s.contains("GNT")) { //change to AppConstant when you commit code
                    LOG.info("string id  is {} and name {}", s, data.getProductId());
                    gntData.add(r.getId());
                    break;
                }
            }
        }
        return gntData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean acknowledgeOrders(List<RequestOrder> requestOrders, IdCodeName updatedBy) {
        try {
            for (RequestOrder requestOrder : requestOrders) {
                requestOrder.setStatus(SCMOrderStatus.ACKNOWLEDGED);
                requestOrder.setLastUpdatedBy(updatedBy);
                requestOrder.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                ReferenceOrderData refOrderData = null;
                if (requestOrder.getReferenceOrderId() != 0) {
                    refOrderData = requestOrderManagementDao.find(ReferenceOrderData.class,
                            requestOrder.getReferenceOrderId());
                }
                requestOrderManagementDao.update(SCMDataConverter.convert(requestOrder, refOrderData), true);
            }
        } catch (Exception e) {
            LOG.error("Encountered error while updating status of request orders:::", e);
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ProductionPlanEvent startPlanning(Date fulfillmentDate, List<Integer> orderList, IdCodeName lastUpdatedBy,
                                             int unitId) throws SumoException, DataNotFoundException, DataUpdationException {
        List<String> errors = new ArrayList<>();
        try {
            ProductionPlanEventData event = createProductionPlanEvent(fulfillmentDate, orderList, lastUpdatedBy,
                    unitId);
            mapRequestOrders(event, orderList, lastUpdatedBy);
            calculateProductionItemsData(event, errors);
            SCMDataConverter.convert(event, scmCache, masterDataCache);
            ProductionPlanEvent productionPlanEvent = SCMDataConverter.convert(event, scmCache, masterDataCache);
            addProductionName(productionPlanEvent); //add productionUnitName
            return productionPlanEvent;
        } catch (Exception e) {
            if (errors.size() > 0) {
                String message = "Error while Planning " + "\n"
                        + Arrays.toString(errors.toArray());
                LOG.error("Exception occured while planning ",e);
                throw new SumoException(message);
            } else {
                LOG.error("Exception occured while planning log :: ", e);
                throw new SumoException("Error While Planning " + e);
            }
        }
    }

    private void addProductionName(ProductionPlanEvent ppe) {
        List<PlanOrderItem> planOrderItem = ppe.getRequestItems();
        for (PlanOrderItem items : planOrderItem) {
            if (items.getRecipeRequire() && items.getProductionUnit() != null && items.getProductionUnit() > 0) {
                ProductionUnitData data = mappingCache.findProductionLine(items.getProductionUnit());
                items.setProductionName(data.getProductionUnitName());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PlanOrderItemPrep getPlanItemsForSemiFinishedProduct(RequestOrderItem item, Boolean isSkuRequired) throws SumoException, DataNotFoundException {

        ProductDefinition productDefinition = scmCache.getProductDefinition(item.getProductId());
        if (productDefinition == null) {
            throw new SumoException("Error getting product recipe.",
                    "Product Id " + item.getProductId() + " doesn't exist in system.");
        } else if (SCMServiceConstants.CATEGORY_SEMI_FINISHED != productDefinition.getCategoryDefinition().getId()) {
            throw new SumoException("Error getting product recipe.",
                    "Product Id " + item.getProductId() + " isn't a Semi Finished product.");
        } else {
            RecipeDetail recipe = recipeCache.getScmRecipe(scmCache.getRecipeProfile(item.getUnitId(), productDefinition.getProductId()));
//            RecipeDetail recipe = recipeCache.getScmRecipe(productDefinition.getProductId());
            if (recipe == null) {
                throw new SumoException("Error getting product recipe.",
                        "Recipe Missing for the product:" + productDefinition.getProductName());
            } else {
                PlanOrderItemPrep planOrderItemPrep = new PlanOrderItemPrep();
                planOrderItemPrep.setPlanOrderItem(SCMUtil.generateIdCodeName(item.getId(), "", item.getProductName()));
                planOrderItemPrep.setRecipeId(recipe.getRecipeId());
                planOrderItemPrep.setPreparationQuantity(SCMUtil.convertToBigDecimal(item.getRequestedQuantity()));
                planOrderItemPrep.setRecipeNotes(recipe.getNotes());
                planOrderItemPrep.setImagesURL(recipe.getImagesURL());
                Map<Integer, SkuDefinition> productToSkuMap = Objects.nonNull(isSkuRequired) && isSkuRequired.equals(Boolean.TRUE) ? productionBookingDao.findMapping(productDefinition.getProductId(), item.getUnitId()).stream().filter(productionBookingMappingData ->
                                productionBookingMappingData.getMappingStatus().equals(AppConstants.ACTIVE))
                        .map(bookingMapping -> scmCache.getSkuDefinition(bookingMapping.getLinkedSkuId())).
                        collect(Collectors.toMap(sku -> sku.getLinkedProduct().getId(), Function.identity(), (sku1, sku2) -> {
                            LOG.info("duplicate sku found in recipre!");
                            return sku1;
                        })) : new HashMap<>();
                for (IngredientProductDetail pd : recipe.getIngredient().getComponents()) {
                    PlanOrderItemPrepItem planOrderItemPrepItem = getPlaneOrderItemPrepItems(pd, item.getUnitId(),
                            SCMUtil.convertToBigDecimal(item.getRequestedQuantity()), recipe.getNotes(), productToSkuMap);
                    planOrderItemPrep.getPlanOrderItemPrepItems().add(planOrderItemPrepItem);
                }
                return planOrderItemPrep;
            }
        }
    }

    @Override
    public Map<Integer, Integer> getProductionBookingProductSkuMap(Integer productId, Integer unitId) {
        return productionBookingDao.findMapping(productId, unitId).stream().filter(productionBookingMappingData ->
                        productionBookingMappingData.getMappingStatus().equals(AppConstants.ACTIVE))
                .map(bookingMapping -> scmCache.getSkuDefinition(bookingMapping.getLinkedSkuId())).
                collect(Collectors.toMap(sku -> sku.getLinkedProduct().getId(), sku -> sku.getSkuId(), (sku1, sku2) -> {
                    LOG.info("duplicate sku found in recipre!");
                    return sku1;
                }));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public PlanOrderItemPrep submitPlanOrderItemPreparation(PlanOrderItemPrep item, int unitId) throws SumoException, DataNotFoundException {
        PlanOrderItemData planOrderItemData = requestOrderManagementDao.find(PlanOrderItemData.class,
                item.getPlanOrderItem().getId());
        if (planOrderItemData == null) {
            throw new SumoException("Plan submission failure.",
                    "Could not find the Plan order item for id:" + item.getPlanOrderItem().getId());
        } else {
            if (scmCache.getProductDefinition(planOrderItemData.getProductId()).getCategoryDefinition()
                    .getId() != SCMServiceConstants.CATEGORY_SEMI_FINISHED) {
                throw new SumoException("Plan submission failure.",
                        "Product : " + planOrderItemData.getProductName() + " is not Semi Finished product.");
            } else {
                PlanOrderItemPrepData planOrderItemPrepData = new PlanOrderItemPrepData();
                planOrderItemPrepData.setPlanOrderItemData(planOrderItemData);
                planOrderItemPrepData.setPreparationQuantity(item.getPreparationQuantity());
                planOrderItemPrepData.setRecipeId(item.getRecipeId());
                planOrderItemPrepData.setRequestedBy(item.getRequestedBy().getId());
                planOrderItemPrepData.setRequestingTime(SCMUtil.getCurrentTimestamp());
                planOrderItemPrepData = requestOrderManagementDao.add(planOrderItemPrepData, true);
                for (PlanOrderItemPrepItem planOrderItemPrepItem : item.getPlanOrderItemPrepItems()) {
                    persistChildren(planOrderItemPrepData, planOrderItemPrepItem, null);
                    // planOrderItemPrepData.getPlanOrderItemPrepItemData().add(planOrderItemPrepItemData);
                }
                planOrderItemData.setPrintCount(
                        planOrderItemData.getPrintCount() == null ? 1 : planOrderItemData.getPrintCount() + 1);
                requestOrderManagementDao.update(planOrderItemData, false);
                requestOrderManagementDao.flush();
                IdCodeName requestedBy = SCMUtil.generateIdCodeName(planOrderItemPrepData.getRequestedBy(), "",
                        masterDataCache.getEmployee(planOrderItemPrepData.getRequestedBy()));
                planOrderItemPrepData = requestOrderManagementDao.find(PlanOrderItemPrepData.class,
                        planOrderItemPrepData.getId());
                return SCMDataConverter.convert(planOrderItemPrepData, requestedBy, unitId, recipeCache, scmCache);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PlanOrderItemPrep> getPlanOrderItemPreparations(Integer itemId, Integer unitId) throws SumoException, DataNotFoundException {
        PlanOrderItemData planOrderItemData = requestOrderManagementDao.find(PlanOrderItemData.class, itemId);
        if (planOrderItemData == null) {
            throw new SumoException("Plan fetch failure.", "Could not find the Plan order item for id:" + itemId);
        } else {
            List<PlanOrderItemPrepData> planOrderItemPrepDataList = requestOrderManagementDao
                    .findPlanOrderItemPrepByPlanOrderItemId(itemId);
            List<PlanOrderItemPrep> planOrderItemPreps = new ArrayList<>();
            planOrderItemPrepDataList.forEach(planOrderItemPrepData -> {
                IdCodeName requestedBy = SCMUtil.generateIdCodeName(planOrderItemPrepData.getRequestedBy(), "",
                        masterDataCache.getEmployee(planOrderItemPrepData.getRequestedBy()));
                try {
                    planOrderItemPreps.add(SCMDataConverter.convert(planOrderItemPrepData, requestedBy, unitId, recipeCache, scmCache));
                } catch (DataNotFoundException e) {
                    e.printStackTrace();
                }
            });
            return planOrderItemPreps;
        }
    }

    private PlanOrderItemPrepItem getPlaneOrderItemPrepItems(IngredientProductDetail pd, Integer unitId, BigDecimal qty,
                                                             String recipeNotes, Map<Integer, SkuDefinition> productToSkuMap) throws DataNotFoundException {


        BigDecimal quantity = SCMUtil.convertToBigDecimal(qty).multiply(pd.getQuantity());
        //quantity = AppUtils.getQuantityByYield(quantity, pd.getYield());
        PlanOrderItemPrepItem planOrderItemPrepItem = new PlanOrderItemPrepItem();
        planOrderItemPrepItem.setProductId(pd.getProduct().getProductId());
        planOrderItemPrepItem.setProductName(productToSkuMap.containsKey(pd.getProduct().getProductId()) ?
                productToSkuMap.get(pd.getProduct().getProductId()).getSkuName() : pd.getProduct().getName());
        planOrderItemPrepItem.setQuantity(quantity);
        planOrderItemPrepItem.setUnitOfMeasure(pd.getUom().value());
        if (pd.getInstructions() != null && pd.getInstructions().size() > 0) {
            String instructions = pd.getInstructions().stream().map(iterationIngredientInstructions -> {
                return iterationIngredientInstructions.getInstruction();
            }).collect(Collectors.joining(", "));
            planOrderItemPrepItem.setInstructions(instructions);
        }

        if (scmCache.getProductDefinition(pd.getProduct().getProductId()).isAutoProduction() && pd.getShowRecipe() == null) {
            pd.setShowRecipe(true);
        }
        LOG.info("checking show recipe value {}", pd.getShowRecipe());
        if (scmCache.getProductDefinition(pd.getProduct().getProductId()).isAutoProduction() && pd.getShowRecipe()) {
            RecipeDetail ingredientRecipe = recipeCache.getScmRecipe(scmCache.getRecipeProfile(unitId, pd.getProduct().getProductId()));
            if (ingredientRecipe != null) {
                if (ingredientRecipe.getNotes() != null) {
                    planOrderItemPrepItem.setRecipeNotes(ingredientRecipe.getNotes());
                }
                for (IngredientProductDetail pdx : ingredientRecipe.getIngredient().getComponents()) {
                    PlanOrderItemPrepItem planOrderItemPrepItemx = getPlaneOrderItemPrepItems(pdx, unitId,
                            planOrderItemPrepItem.getQuantity(), ingredientRecipe.getNotes(), productToSkuMap);
                    planOrderItemPrepItem.getPlanOrderItemPrepItems().add(planOrderItemPrepItemx);
                }
            }
        }
        return planOrderItemPrepItem;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCalculatedProductionItemsData(int eventId, List<PlanOrderItem> list) throws DataUpdationException, SumoException, DataNotFoundException {
        ProductionPlanEventData eventData = productionPlanManagementDao.find(ProductionPlanEventData.class, eventId);

        Map<String, Map<Integer, PlanOrderItemData>> itemMap = new HashMap<>();
        itemMap.put(ProductionItemType.REQUESTED.name(), new HashMap<>());
        itemMap.put(ProductionItemType.ADDITIONAL.name(), new HashMap<>());
        itemMap.put(ProductionItemType.SKU.name(), new HashMap<>());

        List<PlanOrderItem> updatedRequestedOrderItemList = new ArrayList<>();

        // UPDATING LIST WITH UPDATED BUFFERED QUANTITIES WITH TYPE REQUESTED
        for (PlanOrderItem updatedItem : list) {
            if (updatedItem.getItemType().equals(ProductionItemType.REQUESTED.name())) {
                updatedRequestedOrderItemList.add(updatedItem);
            }
        }

        // FIND AND UPDATE ALL THE REQUEST ORDER ITEM WITH THE EXPIRY DATE WITH LOGIC AS IN PRODUCTION BOOKING FOR ONLY REQUESTED ITEMS WITH RECIPE FLAG Y
        for (PlanOrderItem item : updatedRequestedOrderItemList) {
            if (item.getRecipeRequire()) {
                ProductDefinition product = scmCache.getProductDefinition(item.getProductId());
                List<RequestOrderItemData> requestOrderItemData = productionPlanManagementDao.findRoItemByPlanEvent(eventData.getId(), item.getProductId());
                if (requestOrderItemData != null && !requestOrderItemData.isEmpty()) {
                    for (RequestOrderItemData roItem : requestOrderItemData) {
                        roItem.setExpiryDate(AppUtils.createExpiryDate(item.getExpiryDate(), product.getShelfLifeInDays()));
                        requestOrderManagementDao.update(roItem, false);
                    }
                }
            }
        }

        // CALCULATING AND UPDATING THE PLAN EVENT ITEM AS PER REQUEST ORDER ITEM IN RO AFTER APPLYING BUFFERED
        for (PlanOrderItem item : updatedRequestedOrderItemList) {
            addItemDataToMap(item.getProductId(), item.getTotalQuantity(), ProductionItemType.REQUESTED.name(),
                    itemMap, eventData.getUnitId());
            calculateItemsByRecipe(item.getProductId(), eventData.getUnitId(), item.getTotalQuantity(),
                    ProductionItemType.REQUESTED.name(), itemMap);
        }

        //UPDATE AND SAVE BUFFERED QUANTITIES
        Set<Integer> productIds = new HashSet<Integer>();
        for (String key : itemMap.keySet()) {
            for (PlanOrderItemData itemData : itemMap.get(key).values()) {
                PlanOrderItemData previousItemData = productionPlanManagementDao.findItemByProductAndEvent(itemData.getProductId(), eventData.getId(), itemData.getItemType());
                if (previousItemData != null) {
                    BigDecimal updateQuantity = itemData.getRequestedQuantity().setScale(5, BigDecimal.ROUND_UP);
                    BigDecimal bufferedQuantity = SCMUtil.subtract(updateQuantity, previousItemData.getRequestedQuantity());
                    BigDecimal bufferedPercentage = SCMUtil.multiplyWithScale10(SCMUtil.divideWithScale10(bufferedQuantity, previousItemData.getRequestedQuantity()), BigDecimal.valueOf(100));
                    previousItemData.setBufferedPercentage(bufferedPercentage);
                    previousItemData.setBufferedQuantity(bufferedQuantity);
                    previousItemData.setTotalQuantity(itemData.getRequestedQuantity().setScale(6, BigDecimal.ROUND_UP));
                    productionPlanManagementDao.update(previousItemData, false);
                } else {
                    throw new SumoException("cannot update product {}", itemData.getProductName());
                }
                productIds.add(itemData.getProductId());
            }
        }
        // Inventory Calculation
        Map<Integer, BigDecimal> inventoryMap = warehouseService.getInventorySummaryForProducts(productIds,
                eventData.getUnitId());
        for (PlanOrderItemData itemData : eventData.getOrderItemData()) {
            itemData.setAvailableQuantity(inventoryMap.get(itemData.getProductId()));
        }

        productionPlanManagementDao.flush();

        // price calculations
        for (PlanOrderItemData itemData : eventData.getOrderItemData()) {
            ProductDefinition productDefinition = scmCache.getProductDefinition(itemData.getProductId());
            itemData.setUnitPrice(BigDecimal.valueOf(productDefinition.getUnitPrice()));
            itemData.setAmount(
                    BigDecimal.valueOf(productDefinition.getUnitPrice()).multiply(itemData.getTotalQuantity()));
        }
        return true;
    }

    private void calculateProductionItemsData(ProductionPlanEventData eventData, List<String> errors) throws DataUpdationException, SumoException, DataNotFoundException {
        Map<String, Map<Integer, PlanOrderItemData>> itemMap = new HashMap<>();
        itemMap.put(ProductionItemType.REQUESTED.name(), new HashMap<>());
        itemMap.put(ProductionItemType.ADDITIONAL.name(), new HashMap<>());
        itemMap.put(ProductionItemType.SKU.name(), new HashMap<>());
        String mappingErrorMsg = null;
        for (PlanOrderMappingData roData : eventData.getOrderMappingData()) {
            String error = "";
            for (RequestOrderItemData item : roData.getRequestOrder().getRequestOrderItemDatas()) {
                try {
                    addItemDataToMap(item.getProductId(), item.getRequestedQuantity(), ProductionItemType.REQUESTED.name(),
                            itemMap, eventData.getUnitId());
                } catch (Exception e) {
                    mappingErrorMsg = "It Seems These Products mapped Sku is IN_ACTIVE Or Some Mapping Is missing.";
                    error = error.concat("Problem in Product ID  :" + scmCache.getProductDefinition(item.getProductId()).getProductName());
                }
                if (Objects.isNull(mappingErrorMsg)) {
                    try {
                        calculateItemsByRecipe(item.getProductId(), eventData.getUnitId(), item.getRequestedQuantity(),
                                ProductionItemType.REQUESTED.name(), itemMap);
                    } catch (Exception e) {
                        error = error.concat(" Recipe Missing for the product:" + scmCache.getProductDefinition(item.getProductId()).getProductName());
                    }
                }

            }
            if (!error.equals("")) {
                errors.add("IN RO : " + roData.getRequestOrder().getId() + " " + error);
            }
        }
        if (errors.size() > 0) {
            String message = "Error while Planning " + (Objects.nonNull(mappingErrorMsg) ? mappingErrorMsg : "") + "\n"
                    + Arrays.toString(errors.toArray());
            throw new SumoException(message);
        }

        Set<Integer> productIds = new HashSet<Integer>();
        for (String key : itemMap.keySet()) {
            for (PlanOrderItemData itemData : itemMap.get(key).values()) {
                itemData.setEventId(eventData.getId());
                itemData = productionPlanManagementDao.add(itemData, false);
                eventData.getOrderItemData().add(itemData);
                productIds.add(itemData.getProductId());
            }
        }
        productionPlanManagementDao.flush();

        // Inventory Calculation
        Map<Integer, BigDecimal> inventoryMap = warehouseService.getInventorySummaryForProducts(productIds,
                eventData.getUnitId());
        for (PlanOrderItemData itemData : eventData.getOrderItemData()) {
            itemData.setAvailableQuantity(inventoryMap.get(itemData.getProductId()));
        }

        // price calculations
        for (PlanOrderItemData itemData : eventData.getOrderItemData()) {
            ProductDefinition productDefinition = scmCache.getProductDefinition(itemData.getProductId());
            itemData.setUnitPrice(BigDecimal.valueOf(productDefinition.getUnitPrice()));
            itemData.setAmount(
                    BigDecimal.valueOf(productDefinition.getUnitPrice()).multiply(itemData.getRequestedQuantity()));
        }

    }

    private void calculateItemsByRecipe(int productId, int unitId, BigDecimal parentQuantity, String itemType,
                                        Map<String, Map<Integer, PlanOrderItemData>> itemMap) throws DataUpdationException, DataNotFoundException {
        RecipeDetail recipe = recipeCache.getScmRecipe(scmCache.getRecipeProfile(unitId, productId));
        ProductDefinition product = scmCache.getProductDefinition(productId);
        if (recipe == null) {
            if (product.isRecipeRequired()) {
                throw new DataUpdationException("Recipe Missing for the product:" + product.getProductName());
            }
            return;
        }
        for (IngredientProductDetail pd : recipe.getIngredient().getComponents()) {
            ProductDefinition def = scmCache.getProductDefinition(pd.getProduct().getProductId());
            BigDecimal quantity = parentQuantity.multiply(pd.getQuantity());
          //  quantity = AppUtils.getQuantityByYield(quantity, pd.getYield());
            if (def != null && def.isRecipeRequired() && def.isAutoProduction()) {
                addItemDataToMap(pd.getProduct().getProductId(), quantity, ProductionItemType.ADDITIONAL.name(),
                        itemMap, unitId);
                calculateItemsByRecipe(pd.getProduct().getProductId(), unitId, quantity, ProductionItemType.ADDITIONAL.name(),
                        itemMap);
            } else {
                addItemDataToMap(pd.getProduct().getProductId(), quantity, ProductionItemType.SKU.name(), itemMap, unitId);
            }
        }
    }

    private void addItemDataToMap(int productId, BigDecimal quantity, String itemType,
                                  Map<String, Map<Integer, PlanOrderItemData>> itemMap, int unitId) {
        PlanOrderItemData itemData = null;

        if (itemMap.get(itemType).containsKey(productId)) {
            itemData = itemMap.get(itemType).get(productId);
        } else {
            itemData = createNewPlanOrderItem(productId, itemType, unitId);
        }
        itemData.setTotalQuantity(itemData.getRequestedQuantity().add(quantity));
        itemData.setRequestedQuantity(itemData.getRequestedQuantity().add(quantity));

        itemMap.get(itemType).put(productId, itemData);
    }

    private PlanOrderItemData createNewPlanOrderItem(int productId, String itemType, int unitId) {
        PlanOrderItemData itemData = new PlanOrderItemData();
        ProductDefinition productDef = scmCache.getProductDefinition(productId);
        itemData.setProductId(productId);
        itemData.setProductName(productDef.getProductName());
        itemData.setUnitOfMeasure(productDef.getUnitOfMeasure());
        itemData.setCategory(productDef.getCategoryDefinition().getCode());
        itemData.setItemType(itemType);
        itemData.setAvailableQuantity(BigDecimal.ZERO);
        itemData.setAmount(BigDecimal.ZERO);
        itemData.setUnitPrice(BigDecimal.ZERO);
        itemData.setRequestedQuantity(BigDecimal.ZERO);
        itemData.setBufferedPercentage(BigDecimal.ZERO);
        itemData.setBufferedQuantity(BigDecimal.ZERO);
        itemData.setTotalQuantity(BigDecimal.ZERO);
        itemData.setExcessQuantity(BigDecimal.ZERO);
        if (productDef.isRecipeRequired()) {//if recipeRequire==True
            itemData.setIsRecipeRequired(SCMServiceConstants.SCM_CONSTANT_YES);
            LOG.info("PLAN ORDER ITEM DATA FOR THE PRODUCT {}", productId);
            int skuid = mappingCache.findSKUID(productId);
            LOG.info("PLAN ORDER ITEM DATA FOR THE SKU ID {}", skuid);
            Integer productionLine = mappingCache.findProductionLine(unitId, skuid);
            if (!productionLine.equals(-1)) {
                itemData.setProductionUnit(productionLine);
            }
        } else {
            itemData.setIsRecipeRequired(SCMServiceConstants.SCM_CONSTANT_NO);
        }
        return itemData;
    }

    private void mapRequestOrders(ProductionPlanEventData event, List<Integer> orderList, IdCodeName lastUpdatedBy) throws SumoException {
        for (Integer requestOrderId : orderList) {
            RequestOrderData requestOrder = requestOrderManagementDao.find(RequestOrderData.class, requestOrderId);
            PlanOrderMappingData map = new PlanOrderMappingData(event, requestOrder);
            event.getOrderMappingData().add(productionPlanManagementDao.add(map, false));
        }
        productionPlanManagementDao.flush();
    }

    private ProductionPlanEventData createProductionPlanEvent(Date fulfillmentDate, List<Integer> orderList,
                                                              IdCodeName lastUpdatedBy, int unitId) throws SumoException {
        ProductionPlanEventData event = new ProductionPlanEventData();
        event.setFulfillmentDate(SCMUtil.getDate(fulfillmentDate));
        event.setUnitId(unitId);
        event.setGenerationTime(SCMUtil.getCurrentTimestamp());
        event.setGeneratedBy(lastUpdatedBy.getId());
        event.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        event.setLastUpdatedBy(lastUpdatedBy.getId());
        event.setStatus(SCMOrderStatus.INITIATED.name());
        event.setIsUpdated(SCMServiceConstants.SCM_CONSTANT_NO);
        productionPlanManagementDao.add(event, true);
        return event;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean acknowledgeEventOrders(int eventId) {
        ProductionPlanEventData eventData = productionPlanManagementDao.find(ProductionPlanEventData.class, eventId);
        if (eventData != null) {
            for (PlanOrderMappingData mapData : eventData.getOrderMappingData()) {
                RequestOrderData ro = mapData.getRequestOrder();
                setOrderStatus(ro);
                ro.setLastUpdatedBy(eventData.getLastUpdatedBy());
                ro.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                requestOrderManagementDao.update(ro, false);
            }
            eventData.setStatus(SCMOrderStatus.CREATED.name());
            productionPlanManagementDao.update(eventData, false);
            productionPlanManagementDao.flush();
            return true;
        }
        return false;
    }

    private void setOrderStatus(RequestOrderData ro) {

//        OrderTransferType transferType = ro.getTransferType() != null
//                ? OrderTransferType.valueOf(ro.getTransferType())
//                : OrderTransferType.TRANSFER;
        Unit unit = masterDataCache.getUnit(ro.getFulfillmentUnitId());
        ro.setStatus(ro.getTransferType().equals(OrderTransferType.INVOICE.value()) && !SCMUtil.isKitchen(unit.getFamily())
                ? SCMOrderStatus.SETTLED.name()
                : SCMOrderStatus.ACKNOWLEDGED.name());

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductionPlanEvent> getPlansByFulfilmentDate(Date start, Date end, int fulfillmentUnit) {
        return productionPlanManagementDao.getPlansByFulfilmentDate(start, end, fulfillmentUnit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RequestOrder> getSpecializedROForFulfillmentDate(Date fulfillmentDate, boolean isReceiving) {
        List<RequestOrder> requestOrders = requestOrderManagementDao
                .getSpecializedROForFulfillmentDate(fulfillmentDate, isReceiving)
                .stream().map(requestOrderData -> SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache, masterDataCache))
                .collect(Collectors.toList());
        return requestOrders;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RequestOrder> getSpecializedROForNotification(NotificationType type, Date startTime, Date endTime) {
        List<RequestOrder> list = new ArrayList<>();
        List<RequestOrderData> requestOrders = requestOrderManagementDao.getSpecializedROForNotification(type,
                startTime, endTime);
        if (requestOrders != null) {
            for (RequestOrderData requestOrderData : requestOrders) {
                list.add(SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache, masterDataCache));
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<RequestOrder>> getSpecialOrdersForDate(Date date) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getSpecialOrdersForDate(date);
        Map<Integer, List<RequestOrder>> unitOrdersMap = new HashMap<>();
        for (Integer unitId : masterDataCache.getUnitsBasicDetails().keySet()) {
            List<RequestOrder> requestOrders = new ArrayList<>();
            for (RequestOrderData requestOrderData : requestOrderDataList) {
                if (requestOrderData.getRequestUnitId() == unitId) {
                    requestOrders
                            .add(SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache, masterDataCache));
                }
            }
            unitOrdersMap.put(unitId, requestOrders);
        }
        return unitOrdersMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<TransferOrder> getTransferOrdersFromRequestOrders(List<Integer> clubRoIds, Map<Integer, Map<Integer, GoodsReceivedItem>> skuToPackagingMapping, Date fulfilmentDate
            , IdCodeName generationUnit, IdCodeName generatedBy) throws SumoException {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getRequestOrdersByIds(null, null, null, null, clubRoIds);
        Map<Integer, List<RequestOrderData>> unitIdsToRO = createUnitToROMap(requestOrderDataList);
        List<RequestOrderData> clubbedRequestOrders = new ArrayList<>();
        Integer fulfillmentCompanyId = Integer.valueOf(requestOrderDataList.get(0).getFulfillmentCompanyId());
        for (Integer unitId : unitIdsToRO.keySet()) {
            if (unitIdsToRO.get(unitId).size() > 1) {
                Integer requestCompanyId = Integer.valueOf(unitIdsToRO.get(unitId).get(0).getRequestCompanyId());
                clubbedRequestOrders.add(clubRequestOrders(unitIdsToRO.get(unitId), new IdCodeName(unitId, "", "")
                        , generationUnit, generatedBy, fulfilmentDate, requestCompanyId, fulfillmentCompanyId));
            } else {
                clubbedRequestOrders.add(unitIdsToRO.get(unitId).get(0));
            }
        }


        return createClubbedTOs(clubbedRequestOrders, skuToPackagingMapping, generatedBy);
    }


    private List<TransferOrder> createClubbedTOs(List<RequestOrderData> requestOrderDataList, Map<Integer, Map<Integer, GoodsReceivedItem>> skuToPackagingMapping,
                                                 IdCodeName generatedBy) {
        List<TransferOrder> transferOrderList = new ArrayList<>();
        for (RequestOrderData requestOrderData : requestOrderDataList) {
            TransferOrder transferOrder = new TransferOrder();
            transferOrder.setGeneratedForUnitId(new IdCodeName(requestOrderData.getRequestUnitId(), "", ""));
            transferOrder.setGenerationUnitId(new IdCodeName(requestOrderData.getFulfillmentUnitId(), "", ""));
            transferOrder.setGeneratedBy(generatedBy);
            transferOrder.setLastUpdatedBy(generatedBy);
            transferOrder.setStatus(SCMOrderStatus.CREATED);
            transferOrder.setRequestOrderId(requestOrderData.getId());
            transferOrder.setSourceCompany(new IdCodeName(requestOrderData.getFulfillmentCompanyId(), "", ""));
            transferOrder.setReceivingCompany(new IdCodeName(requestOrderData.getRequestCompanyId(), "", ""));
            transferOrder.setTransferOrderItems(generateTOItems(requestOrderData, skuToPackagingMapping));
            transferOrder.setToType(TransferOrderType.REGULAR_TRANSFER);
            if (requestOrderData.getAssetOrder().equals("Y")) {
                transferOrder.setToType(TransferOrderType.FIXED_ASSET_TRANSFER);
            }
            transferOrderList.add(transferOrder);
        }

        return transferOrderList;
    }

    private List<TransferOrderItem> generateTOItems(RequestOrderData requestOrderData, Map<Integer, Map<Integer, GoodsReceivedItem>> skuToPackagingMapping) {
        List<TransferOrderItem> transferOrderItemList = new ArrayList<>();
        for (RequestOrderItemData requestOrderItemData : requestOrderData.getRequestOrderItemDatas()) {
            GoodsReceivedItem skuPackagingData = skuToPackagingMapping.get(requestOrderData.getRequestUnitId()).get(requestOrderItemData.getProductId());
            TransferOrderItem transferOrderItem = new TransferOrderItem();
            transferOrderItem.setProductId(skuPackagingData.getProductId());
            transferOrderItem.setSkuId(skuPackagingData.getSkuId());
            transferOrderItem.setSkuName(skuPackagingData.getSkuName());
            if (Objects.nonNull(skuPackagingData.getNegotiatedUnitPrice())) {
                transferOrderItem.setNegotiatedUnitPrice(skuPackagingData.getNegotiatedUnitPrice().doubleValue());
            }
            transferOrderItem.setRequestOrderItemId(requestOrderItemData.getId());
            if (Objects.nonNull(requestOrderItemData.getRequestedAbsoluteQuantity())) {
                transferOrderItem.setRequestedAbsoluteQuantity(requestOrderItemData.getRequestedAbsoluteQuantity().floatValue());
            }
            if (Objects.nonNull(requestOrderItemData.getRequestedQuantity())) {
                transferOrderItem.setRequestedQuantity(requestOrderItemData.getRequestedQuantity().floatValue());
            }
            transferOrderItem.setPackagingDetails(skuPackagingData.getPackagingDetails());
            transferOrderItem.setUnitOfMeasure(skuPackagingData.getUnitOfMeasure());
            if (Objects.nonNull(skuPackagingData.getUnitPrice())) {
                transferOrderItem.setUnitPrice(skuPackagingData.getUnitPrice().doubleValue());
            }
            transferOrderItem.setTransferredQuantity(skuPackagingData.getTransferredQuantity());
            transferOrderItem.setExcessQuantity(requestOrderItemData.getExcessQuantity());
            transferOrderItem.setRoItemExpiryDate(requestOrderItemData.getExpiryDate());
            transferOrderItemList.add(transferOrderItem);
        }

        return transferOrderItemList;
    }


    private Map<Integer, List<RequestOrderData>> createUnitToROMap(List<RequestOrderData> requestOrderDataList) {
        Map<Integer, List<RequestOrderData>> unitIdsToRO = new HashMap<>();
        for (RequestOrderData requestOrderData : requestOrderDataList) {
            if (!unitIdsToRO.containsKey(requestOrderData.getRequestUnitId())) {
                unitIdsToRO.put(requestOrderData.getRequestUnitId(), new ArrayList<>());
            }
            unitIdsToRO.get(requestOrderData.getRequestUnitId()).add(requestOrderData);
        }
        return unitIdsToRO;


    }

    @Override
    public RequestOrderData clubRequestOrders(List<RequestOrderData> requestOrderDataList, IdCodeName generationUnitId, IdCodeName generatedForUnitId,
                                              IdCodeName generatedBy, Date fulfilmentDate, Integer requestCompanyId,
                                              Integer fulfillmentCompanyId) throws SumoException {
        RequestOrderData clubbedRO = new RequestOrderData();

        clubbedRO.setStatus(SCMOrderStatus.CREATED.toString());
        clubbedRO.setIsSpecialOrder(SCMServiceConstants.SCM_CONSTANT_NO);
        clubbedRO.setRequestUnitId(generationUnitId.getId());
        clubbedRO.setFulfillmentUnitId(generatedForUnitId.getId());
        clubbedRO.setLastUpdatedBy(generatedBy.getId());
        clubbedRO.setGeneratedBy(generatedBy.getId());
        clubbedRO.setGenerationTime(SCMUtil.getCurrentTimestamp());
        clubbedRO.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        clubbedRO.setFulfillmentDate(fulfilmentDate);
        clubbedRO.setRequestCompanyId(requestCompanyId);
        clubbedRO.setFulfillmentCompanyId(fulfillmentCompanyId);
        clubbedRO.setRaiseBy("N");
        setTransferType(clubbedRO);

        BigDecimal totalROAmount = BigDecimal.ZERO;
        Map<Integer, RequestOrderItemData> requestOrderItemMap = new HashMap<>();
        List<RequestOrderData> clubbedROs = new ArrayList<>();
        String type = null;
        for (RequestOrderData requestOrderData : requestOrderDataList) {
            List<RequestOrderItemData> requestOrderItemDataList = requestOrderData.getRequestOrderItemDatas();
            type = requestOrderData.getType();
            for (RequestOrderItemData item : requestOrderItemDataList) {
                RequestOrderItemData roItem = requestOrderItemMap.get(item.getProductId());
                if (roItem != null) {
                    BigDecimal reqAbsQty = roItem.getRequestedAbsoluteQuantity()
                            .add(item.getRequestedAbsoluteQuantity());
                    BigDecimal reqQty = roItem.getRequestedQuantity().add(item.getRequestedQuantity());
                    BigDecimal excessQty = roItem.getExcessQuantity().add(item.getExcessQuantity());
                    roItem.setRequestedAbsoluteQuantity(reqAbsQty);
                    roItem.setExcessQuantity(excessQty);
                    roItem.setRequestedQuantity(reqQty);
                    roItem.setOriginalQuantity(reqAbsQty);
                    if (Objects.nonNull(item.getTaxAmount())) {
                        roItem.setTaxAmount(roItem.getTaxAmount().add(item.getTaxAmount()));
                    }
                    if (roItem.getNegotiatedUnitPrice() != null) {
                        BigDecimal roItemAmount = roItem.getNegotiatedUnitPrice()
                                .multiply(roItem.getRequestedQuantity());
                        roItemAmount = roItemAmount.add(roItem.getTaxAmount());
                        roItem.setCalculatedAmount(roItemAmount);
                    }

                } else {
                    roItem = cloneROItemData(item);
                }
                requestOrderItemMap.put(item.getProductId(), roItem);
            }
            clubbedROs.add(requestOrderData);
        }

        clubbedRO = requestOrderManagementDao.add(clubbedRO, true);

        List<RequestOrderItemData> finalOrderItems = new ArrayList<>();
        for (RequestOrderItemData requestOrderItemData : requestOrderItemMap.values()) {
            requestOrderItemData.setRequestOrderData(clubbedRO);
            finalOrderItems.add(requestOrderManagementDao.add(requestOrderItemData, false));
            totalROAmount = totalROAmount.add(SCMUtil.convertToBigDecimal(requestOrderItemData.getCalculatedAmount()));
        }

        // update total amount of clubbed RO into the master RO and create
        // Request Order
        clubbedRO.setRequestOrderItemDatas(finalOrderItems);
        clubbedRO.setTotalAmount(totalROAmount);
        clubbedRO.setType(type);
        clubbedRO = requestOrderManagementDao.update(clubbedRO, true);

        // update the status and child RO for the ROs that are being clubbed
        // together
        for (RequestOrderData requestOrderData : clubbedROs) {
            requestOrderData.setStatus(SCMOrderStatus.CANCELLED.toString());
            requestOrderData.setChildRO(clubbedRO);
            requestOrderManagementDao.update(requestOrderData, true);
        }
        return clubbedRO;
    }

    private RequestOrderItemData cloneROItemData(RequestOrderItemData item) {
        RequestOrderItemData roItem = new RequestOrderItemData();

        roItem.setId(null);
        roItem.setRequestedAbsoluteQuantity(item.getRequestedAbsoluteQuantity());
        roItem.setRequestedQuantity(item.getRequestedQuantity());
        roItem.setNegotiatedUnitPrice(item.getNegotiatedUnitPrice());
        roItem.setProductId(item.getProductId());
        roItem.setProductName(item.getProductName());
        roItem.setTransferredQuantity(item.getTransferredQuantity());
        roItem.setUnitOfMeasure(item.getUnitOfMeasure());
        roItem.setUnitPrice(item.getUnitPrice());
        roItem.setReceivedQuantity(item.getReceivedQuantity());
        roItem.setExcessQuantity(SCMUtil.convertToBigDecimal(item.getExcessQuantity()));
        roItem.setOriginalQuantity(item.getOriginalQuantity());
        if (item.getExpiryDate() != null) {
            roItem.setExpiryDate(item.getExpiryDate());
        }
        roItem.setVendorId(item.getVendorId());
        roItem.setTaxAmount(Objects.nonNull(item.getTaxAmount()) ? item.getTaxAmount() : BigDecimal.ZERO);
        roItem.setCalculatedAmount(Objects.nonNull(item.getCalculatedAmount()) ? item.getCalculatedAmount() : BigDecimal.ZERO);
        roItem.setCalculatedAmount(roItem.getCalculatedAmount().add(roItem.getTaxAmount()));
        roItem.setTaxCode(item.getTaxCode());

        return roItem;
    }

    private List<RequestOrder> getRequestOrders(List<RequestOrderData> requestOrderDataList, boolean convertFull) {
        List<RequestOrder> requestOrders = new ArrayList<RequestOrder>();
        for (RequestOrderData requestOrderData : requestOrderDataList) {

            if (!convertFull) {
                requestOrders.add(SCMDataConverter.convert(requestOrderData, scmCache, masterDataCache));
            } else {
                requestOrders
                        .add(SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache, masterDataCache));
            }
        }
        return requestOrders;
    }

    private List<RequestOrder> createRequestOrderObject(List<RequestOrderData> requestOrderDataList) {
        List<RequestOrder> requestOrders = new ArrayList<RequestOrder>();
        for (RequestOrderData requestOrderData : requestOrderDataList) {
            requestOrders.add(SCMDataConverter.convert(requestOrderData, scmCache, masterDataCache));
        }
        return requestOrders;
    }

    private RequestOrderData createRequestOrderDataObject(ReferenceOrderData referenceOrderData,
                                                          List<ReferenceOrderScmItemData> items, int fulfillmentUnitId) throws InventoryUpdateException, SumoException {

        /*
         * List<Integer> keyIds = items.stream().mapToInt(value ->
         * value.getProductId()).boxed().collect(Collectors.toList());
         *
         * List<CostDetailData> currentPrices =
         * warehouseService.getCurrentPrices(PriceUpdateEntryType.PRODUCT,
         * fulfillmentUnitId,keyIds, true);
         *
         * if(currentPrices==null || currentPrices.isEmpty()){ throw new
         * InventoryUpdateException("Current prices not found for the products in reference order"
         * ); }
         *
         * Map<Integer,CostDetailData> priceMap =
         * currentPrices.stream().collect(Collectors.toMap(CostDetailData::getKeyId,
         * Function.identity()));
         */

        RequestOrderData requestOrderData = new RequestOrderData();
        requestOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        requestOrderData.setRequestUnitId(referenceOrderData.getRequestUnitId());
        requestOrderData.setIsSpecialOrder(SCMUtil.setStatus(false));
        // requestOrderData.setId();
        requestOrderData.setReferenceOrderData(referenceOrderData);
        requestOrderData.setComment(referenceOrderData.getComment());
        requestOrderData.setFulfillmentDate(referenceOrderData.getFulfillmentDate());
        requestOrderData.setFulfillmentUnitId(fulfillmentUnitId);
        requestOrderData.setGeneratedBy(referenceOrderData.getGeneratedBy());
        requestOrderData.setLastUpdatedBy(referenceOrderData.getGeneratedBy());
        requestOrderData.setGenerationTime(SCMUtil.getCurrentTimestamp());
        requestOrderData.setStatus(SCMOrderStatus.CREATED.value());
        requestOrderData.setNumberOfDays(referenceOrderData.getNumberOfDays() == null ? 1 : referenceOrderData.getNumberOfDays());
        requestOrderData.setRaiseBy(referenceOrderData.getRaiseBy() == null ? "N" : referenceOrderData.getRaiseBy());

        requestOrderData.setRequestCompanyId(
                masterDataCache.getUnit(referenceOrderData.getRequestUnitId()).getCompany().getId());
        requestOrderData.setFulfillmentCompanyId(
                masterDataCache.getUnit(fulfillmentUnitId).getCompany().getId());

        requestOrderData = setTransferType(requestOrderData); // setting Transfer Type on ROs
        requestOrderData = requestOrderManagementDao.add(requestOrderData, true);
        List<RequestOrderItemData> requestOrderItemDataList = new ArrayList<RequestOrderItemData>();
        for (ReferenceOrderScmItemData referenceOrderScmItemData : items) {
            if (referenceOrderScmItemData.getRequestedAbsoluteQuantity().compareTo(BigDecimal.ZERO) > 0) {
                RequestOrderItemData requestOrderItemData = new RequestOrderItemData();
                requestOrderItemData.setTransferredQuantity(referenceOrderScmItemData.getTransferredQuantity());
                requestOrderItemData.setRequestedQuantity(referenceOrderScmItemData.getRequestedQuantity());
                requestOrderItemData.setRequestedAbsoluteQuantity(referenceOrderScmItemData.getRequestedAbsoluteQuantity());
                requestOrderItemData.setUnitOfMeasure(referenceOrderScmItemData.getUnitOfMeasure());
                // requestOrderItemData.setId();
                requestOrderItemData.setProductId(referenceOrderScmItemData.getProductId());
                requestOrderItemData.setProductName(referenceOrderScmItemData.getProductName());
                requestOrderItemData.setReceivedQuantity(referenceOrderScmItemData.getReceivedQuantity());
                requestOrderItemData.setRequestOrderData(requestOrderData);
                requestOrderItemData.setExcessQuantity(BigDecimal.ZERO);
                requestOrderItemData.setOriginalQuantity(referenceOrderScmItemData.getRequestedQuantity());
                if (Objects.nonNull(referenceOrderScmItemData.getReason())) {
                    requestOrderItemData.setReason(referenceOrderScmItemData.getReason());
                }
                /*
                 * CostDetailData price = priceMap.get(requestOrderItemData.getProductId()); if
                 * (price==null){ throw new
                 * InventoryUpdateException(String.format("Current price not found for %d"
                 * ,requestOrderItemData.getProductId())); }
                 * requestOrderItemData.setCalculatedAmount(SCMUtil.multiplyWithScale10(
                 * requestOrderItemData.getRequestedQuantity(),price.getPrice()));
                 */
                if (Objects.nonNull(referenceOrderScmItemData.getSuggestedQuantity())) {
                    requestOrderItemData.setSuggestedQuantity(referenceOrderScmItemData.getSuggestedQuantity());
                    requestOrderItemData.setDiffQuantity(referenceOrderScmItemData.getRequestedAbsoluteQuantity().subtract(requestOrderItemData.getSuggestedQuantity()));
                }
                if (Objects.nonNull(referenceOrderScmItemData.getPredictedQuantity())) {
                    requestOrderItemData.setPredictedQuantity(referenceOrderScmItemData.getPredictedQuantity());
                }
                RequestOrderItem requestOrderItem = new RequestOrderItem();
                calculateTax(requestOrderData, requestOrderItemData, requestOrderItem);
                requestOrderItemData = requestOrderManagementDao.add(requestOrderItemData, false);
                requestOrderManagementDao.setTaxDetail(masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId()).getLocation().getState().getId(), requestOrderItemData, requestOrderItem);

                requestOrderItemDataList.add(requestOrderItemData);
            }
        }
        requestOrderData.setRequestOrderItemDatas(requestOrderItemDataList);
        requestOrderManagementDao.flush();
        return requestOrderData;
    }

    private boolean validateSpecializedRequestOrder(RequestOrder requestOrder) {
        boolean isValid = true;
        if (requestOrder.isSpecialOrder()) {
            for (RequestOrderItem requestOrderItem : requestOrder.getRequestOrderItems()) {
                if (requestOrderItem.getVendor() == null) {
                    isValid = false;
                } else if (requestOrderItem.getVendor().getId() == null) {
                    isValid = false;
                }
            }
        }
        return isValid;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductionPlanEvent getPlanningEvent(int eventId) {
        ProductionPlanEventData data = productionPlanManagementDao.find(ProductionPlanEventData.class, eventId);
        return SCMDataConverter.convert(data, scmCache, masterDataCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductionPlanEvent> getPlanningEventBulk(List<Integer> planIds) {
        List<ProductionPlanEvent> result = new ArrayList<>();
        for (Integer id : planIds) {
            ProductionPlanEventData data = productionPlanManagementDao.find(ProductionPlanEventData.class, id);
            result.add(SCMDataConverter.convert(data, scmCache, masterDataCache));
        }
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RequestOrder> getRequestOrdersForInvoice(Integer sendingUnit, Integer receivingUnit) {
        List<RequestOrder> requestOrders = null;
        List<RequestOrderData> orderDataList = requestOrderManagementDao.getRequestOrdersForInvoice(sendingUnit, receivingUnit);
        if (orderDataList != null && !orderDataList.isEmpty()) {
            requestOrders = orderDataList.stream()
                    .map(requestOrderData -> SCMDataConverter.convertFullRequestOrder(requestOrderData, scmCache, masterDataCache))
                    .collect(Collectors.toList());
        }
        return requestOrders;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateTag(int roId, String tag) {
        boolean flag = false;
        try {
            RequestOrderData ro = requestOrderManagementDao.find(RequestOrderData.class, roId);
            ro.setTag(tag);
            requestOrderManagementDao.update(ro, true);
            flag = true;
        } catch (NoResultException nre) {
            LOG.error("No request order with ID {} found", roId);
        } catch (Exception e) {
            LOG.error("Exception for updating request order {} with the search tag {}", roId, tag, e);
        }
        return flag;
    }

    private void persistChildren(PlanOrderItemPrepData planOrderItemPrepData,
                                 PlanOrderItemPrepItem planOrderItemPrepItem, Integer parentItemId) throws SumoException {
        PlanOrderItemPrepItemData planOrderItemPrepItemData = new PlanOrderItemPrepItemData();
        planOrderItemPrepItemData.setPlanOrderItemPrepData(planOrderItemPrepData);
        planOrderItemPrepItemData.setProductId(planOrderItemPrepItem.getProductId());
        planOrderItemPrepItemData.setProductName(planOrderItemPrepItem.getProductName());
        planOrderItemPrepItemData.setQuantity(planOrderItemPrepItem.getQuantity());
        planOrderItemPrepItemData.setUnitOfMeasure(planOrderItemPrepItem.getUnitOfMeasure());
        planOrderItemPrepItemData.setInstructions(planOrderItemPrepItem.getInstructions());
        if (parentItemId != null) {
            planOrderItemPrepItemData.setParentOrderItem(parentItemId);
        }
        planOrderItemPrepItemData = requestOrderManagementDao.add(planOrderItemPrepItemData, true);
        for (PlanOrderItemPrepItem planOrderItemPrepItemx : planOrderItemPrepItem.getPlanOrderItemPrepItems()) {
            persistChildren(planOrderItemPrepData, planOrderItemPrepItemx, planOrderItemPrepItemData.getId());
        }
        planOrderItemPrepData.getPlanOrderItemPrepItemData().add(planOrderItemPrepItemData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void markRequestOrderNotified(List<Integer> requestOrdersNotified) {
        requestOrderManagementDao.markRequestOrderNotified(requestOrdersNotified);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductionPlanningSummary getProductionPlanningSummary(String date, String region, Integer fulfillmentUnit) {
        ProductionPlanningSummary summary = new ProductionPlanningSummary();
        List<UnitBasicDetail> unitDetail = masterDataCache.getAllUnits();
        Set<Integer> uniqueUnitId = new HashSet<>();
        Set<String> distinctCity = new TreeSet<>();
        List<UnitBasicDetail> activeUnit = new ArrayList<>();
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getPendingRequestOrders(date,
                fulfillmentUnit);
        for (RequestOrderData data : requestOrderDataList) {
            uniqueUnitId.add(data.getRequestUnitId());
        }
        int activeCount = 0;
        for (UnitBasicDetail unitData : unitDetail) {
            if (unitData.getRegion().equals(region) && unitData.getStatus().value().equals(AppConstants.ACTIVE) && unitData.isLive() &&
                    unitData.getCategory().value().equals(AppConstants.CAFE)) {
                distinctCity.add(unitData.getCity());
                if (!uniqueUnitId.contains(unitData.getId())) {
                    activeUnit.add(unitData);
                }
                activeCount += 1;
            }
        }
        summary.setTotalActiveUnit(activeCount);
        LOG.info("total active Cafe unit {}", activeCount); //active
        summary.setTotalRoReceived(uniqueUnitId.size());
        summary.setPendingRo(summary.getTotalActiveUnit() - summary.getTotalRoReceived());
        LOG.info("total RO received   {}", uniqueUnitId.size());
        summary.setCityRegionWise(distinctCity);
        summary.setAllActiveUnit(activeUnit);
        return summary;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean excessPlanning(List<PlanOrderItem> list, Integer planId) throws IOException, SumoException {
        ProductionPlanEventData eventData = productionPlanManagementDao.find(ProductionPlanEventData.class, planId);
        List<Integer> productIds = new ArrayList<>();
        for (PlanOrderItem item : list) {
            productIds.add(item.getProductId());
        }

        //CREATING THE MAP OF ALL REQUEST ORDER ITEMS FOR PLAN AND SORTED BY REQUESTED QUANTITIES AND AVERAGE SALES.
        List<UnitPlanItemRequest> items = productionPlanManagementDao.findRoItemByPlanItem(productIds, eventData.getId(), eventData.getFulfillmentDate());
        Map<Integer, List<UnitPlanItemRequest>> itemMap = new HashMap<>();
        if (items != null) {
            for (UnitPlanItemRequest item : items) {
                if (!itemMap.containsKey(item.getRoProductId())) {
                    itemMap.put(item.getRoProductId(), new ArrayList<>());
                }
                if (itemMap.containsKey(item.getRoProductId())) {
                    itemMap.get(item.getRoProductId()).add(item);
                }
            }
        } else {
            String message = "Cannot Find Sales Data For Products!";
            throw new SumoException(message);
        }


        // IF MAP IS NOT EMPTY UPDATE REQUEST ORDER ITEM WITH EXCESS QUANTITIES IN ROUND ROBIN BY 1 IN EACH CYCLE
        if (itemMap != null && !itemMap.isEmpty()) {
            for (PlanOrderItem item : list) {
                List<UnitPlanItemRequest> unitPlanItemRequests = itemMap.get(item.getProductId());
                BigDecimal excessQuantity = item.getExcessQuantity();
                BigDecimal distributedQty = BigDecimal.ZERO;
                Boolean flag = true;
                if (unitPlanItemRequests != null && !unitPlanItemRequests.isEmpty()) {
                    PackagingDefinition itemPackagingDefinition = new PackagingDefinition();
                    int skuId = mappingCache.findSKUID(item.getProductId());
                    List<SkuPackagingMapping> skuPackaging = scmCache.getSkuPackagingMappings(skuId);
                    for (SkuPackagingMapping mapping : skuPackaging) {
                        if (mapping.isIsDefault()) {
                            itemPackagingDefinition = scmCache.getPackagingDefinition(mapping.getPackagingId());
                        }
                    }
//                    BigDecimal differenceExcessQuantity = excessQuantity.remainder(SCMUtil.convertToBigDecimal(itemPackagingDefinition.getConversionRatio()));
                    BigDecimal excessPackagingQuantity = SCMUtil.multiplyWithScale10(new BigDecimal(1), SCMUtil.convertToBigDecimal(itemPackagingDefinition.getConversionRatio()));
                    while (flag) {
                        BigDecimal lastDistribution = distributedQty;
                        for (UnitPlanItemRequest itemList : unitPlanItemRequests) {
                            RequestOrderItemData requestOrderItemData = requestOrderManagementDao.find(RequestOrderItemData.class, itemList.getRoItemId());
                            RequestOrderData requestOrderData = requestOrderManagementDao.find(RequestOrderData.class, itemList.getRoId());
                            if (distributedQty.compareTo(excessQuantity) < 0 && requestOrderItemData != null) {
                                if (itemList.getAverageSales().compareTo(BigDecimal.ZERO) != 0
                                        && !OrderTransferType.valueOf(requestOrderData.getTransferType()).equals(OrderTransferType.INVOICE)
                                        && requestOrderData.getStatus().equals(SCMOrderStatus.ACKNOWLEDGED.name())) {
//                                    excessPackagingQuantity = excessPackagingQuantity.compareTo(differenceExcessQuantity) > 0 ? excessPackagingQuantity : differenceExcessQuantity;
                                    if (requestOrderData.getReferenceOrderData() == null) {
                                        BigDecimal thresholdValue = SCMUtil.multiplyWithScale10(new BigDecimal(20), AppUtils.divideWithScale10(requestOrderItemData.getOriginalQuantity(), new BigDecimal(100)));
                                        if (requestOrderItemData.getExcessQuantity().compareTo(thresholdValue) < 0 && excessPackagingQuantity.compareTo(SCMUtil.subtract(thresholdValue, requestOrderItemData.getExcessQuantity())) < 0) {
                                            distributedQty = SCMUtil.add(distributedQty, excessPackagingQuantity);
                                            updateRoItemDataWithExcessQuantity(requestOrderItemData, excessPackagingQuantity);
                                        }
                                    } else {
                                        distributedQty = SCMUtil.add(distributedQty, excessPackagingQuantity);
                                        updateRoItemDataWithExcessQuantity(requestOrderItemData, excessPackagingQuantity);
                                    }
                                }
                            } else {
                                flag = false;
                                break;
                            }
                        }
                        for (UnitPlanItemRequest itemList : unitPlanItemRequests) {
                            RequestOrderItemData requestOrderItemData = requestOrderManagementDao.find(RequestOrderItemData.class, itemList.getRoItemId());
                            RequestOrderData requestOrderData = requestOrderManagementDao.find(RequestOrderData.class, itemList.getRoId());
                            if (distributedQty.compareTo(excessQuantity) < 0 && requestOrderItemData != null) {
                                if (itemList.getAverageSales().compareTo(BigDecimal.ZERO) == 0
                                        && !OrderTransferType.valueOf(requestOrderData.getTransferType()).equals(OrderTransferType.INVOICE)
                                        && requestOrderData.getStatus().equals(SCMOrderStatus.ACKNOWLEDGED.name())) {
//                                    excessPackagingQuantity = excessPackagingQuantity.compareTo(differenceExcessQuantity) > 0 ? excessPackagingQuantity : differenceExcessQuantity;
                                    if (requestOrderData.getReferenceOrderData() == null) {
                                        BigDecimal thresholdValue = SCMUtil.multiplyWithScale10(new BigDecimal(20), AppUtils.divideWithScale10(requestOrderItemData.getOriginalQuantity(), new BigDecimal(100)));
                                        if (requestOrderItemData.getExcessQuantity().compareTo(thresholdValue) < 0 && excessPackagingQuantity.compareTo(SCMUtil.subtract(thresholdValue, requestOrderItemData.getExcessQuantity())) <= 0) {
                                            distributedQty = SCMUtil.add(distributedQty, excessPackagingQuantity);
                                            updateRoItemDataWithExcessQuantity(requestOrderItemData, excessPackagingQuantity);
                                        }
                                    } else {
                                        distributedQty = SCMUtil.add(distributedQty, excessPackagingQuantity);
                                        updateRoItemDataWithExcessQuantity(requestOrderItemData, excessPackagingQuantity);
                                    }
                                }
                            } else {
                                flag = false;
                                break;
                            }
                        }
//                       If Distributed Quantity remains same after one cycle distribution check for distribution qty and end while loop.
                        if (distributedQty.compareTo(BigDecimal.ZERO) == 0 || distributedQty.compareTo(lastDistribution) == 0) {
                            flag = false;
                            break;
                        }
                    }
                }
                item.setExcessQuantity(distributedQty);
            }
        }

        // AFTER UPDATING REQUEST ORDER ITEMS SAVE EXCESS QUANTITIES IN PLAN ORDER ITEM DATA
        for (PlanOrderItem item : list) {
            PlanOrderItemData planOrderItemData = productionPlanManagementDao.find(PlanOrderItemData.class, item.getId());
            if (item.getExcessQuantity() != null) {
                planOrderItemData.setExcessQuantity(item.getExcessQuantity());
            }
            productionPlanManagementDao.update(planOrderItemData, false);
        }
        eventData.setIsUpdated(SCMServiceConstants.SCM_CONSTANT_YES);
        productionPlanManagementDao.update(eventData, true);
        return true;
    }

    private void updateRoItemDataWithExcessQuantity(RequestOrderItemData requestOrderItemData, BigDecimal excessPackagingQuantity) {
        RequestOrderData requestOrder = requestOrderManagementDao.find(RequestOrderData.class, requestOrderItemData.getRequestOrderData().getId());
        if (requestOrder != null) {
            requestOrderItemData.setExcessQuantity(SCMUtil.add(requestOrderItemData.getExcessQuantity(), excessPackagingQuantity));
            requestOrderItemData.setRequestedQuantity(SCMUtil.add(requestOrderItemData.getRequestedQuantity(), excessPackagingQuantity));
            if (requestOrderItemData.getNegotiatedUnitPrice() != null) {
                requestOrderItemData.setCalculatedAmount(requestOrderItemData.getNegotiatedUnitPrice()
                        .multiply(requestOrderItemData.getRequestedQuantity()));
                RequestOrderItem requestOrderItem = new RequestOrderItem();
                calculateTax(requestOrder, requestOrderItemData, requestOrderItem);
                requestOrderItemData.setTaxAmount(requestOrderItem.getTax());
            }
            requestOrderManagementDao.update(requestOrderItemData, false);
            BigDecimal total = BigDecimal.ZERO;
            for (RequestOrderItemData orderItemData : requestOrder.getRequestOrderItemDatas()) {
                if (orderItemData.getNegotiatedUnitPrice() != null) {
                    total = SCMUtil.add(total, SCMUtil.multiplyWithScale10(orderItemData.getRequestedQuantity(), orderItemData.getNegotiatedUnitPrice()));
                }
            }
            requestOrder.setTotalAmount(total);
            requestOrderManagementDao.update(requestOrder, false);
        } else {
            LOG.info("CANNOT FIND REQUEST ORDER FOR RO ID{}", requestOrder.getId());
        }
    }

    public void requestOrderNotification(RequestOrder requestOrder) {
        UnitBasicDetail receivingUnit = masterDataCache.getUnitBasicDetail(requestOrder.getRequestUnit().getId());
        UnitBasicDetail transferredUnit = masterDataCache.getUnitBasicDetail(requestOrder.getFulfillmentUnit().getId());
        StringBuilder message = new StringBuilder("REQUEST ORDER RAISED FROM WAREHOUSE/KITCHEN NOTIFICATION\n");
        message.append(
                "Request Order Id : " + requestOrder.getId() +
                        " Created From : " + transferredUnit.getName() +
                        " To : " + receivingUnit.getName() + "\n");
        message.append(
                "At : " + SCMUtil.getCurrentTimestamp() +
                        " Generated By : " + masterDataCache.getEmployee(requestOrder.getGeneratedBy().getId()) +
                        " Fulfillment Date : " + AppUtils.getFormattedDate(requestOrder.getFulfillmentDate()) + "\n");
        message.append("\n");
        try {
            //Send Notification To Area Manager
            EmployeeBasicDetail e1 = masterDataCache.getEmployeeBasicDetail(receivingUnit.getUnitManagerId());
            if (e1 != null && e1.getSlackChannel() != null && e1.getSlackChannel().trim().length() > 0) {
                LOG.info("Slack to AM {} ", e1.getSlackChannel());
                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "REQUEST ORDER RAISED FROM WAREHOUSE/KITCHEN NOTIFICATION", e1.getSlackChannel(), null, message.toString());
            }

            //Send Notification TO Cafe Manager
            EmployeeBasicDetail e2 = masterDataCache.getEmployeeBasicDetail(receivingUnit.getCafeManagerId());
            if (e2 != null && e1.getSlackChannel() != null && e2.getSlackChannel().trim().length() > 0) {
                LOG.info("Slack to DAM {} ", e2.getSlackChannel());
                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "REQUEST ORDER RAISED FROM WAREHOUSE/KITCHEN NOTIFICATION", e2.getSlackChannel(), null, message.toString());
            }
        } catch (Exception e) {
            LOG.info(":::::: Could Not Send Slack Notification To Manager And Area Manager :::::::");
        }

        System.out.println(message);
        SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "REQUEST ORDER RAISED FROM WAREHOUSE/KITCHEN NOTIFICATION", null,
                SlackNotification.REGULAR_ORDERING.getChannel(props.getEnvType()), message.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitWiseSummary> getUnitWiseGntAndChaayos(String date, int fulfilmentUnit, ProductionPlanningSummary productionPlanningSummary, String region) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getPendingRequestOrders(date,
                fulfilmentUnit);
        Set<Integer> gntData = getGNTProductId(requestOrderDataList);
        List<OrdersDetailsShort> ordersDetailsShorts = new ArrayList();
        for (RequestOrderData requestOrderData : requestOrderDataList) {
            ordersDetailsShorts.add(SCMDataConverter.convertShort(requestOrderData, masterDataCache));
        }
        addGNTToOrderDetail(ordersDetailsShorts, gntData);
        List<UnitWiseSummary> data = new ArrayList<>();
        List<UnitBasicDetail> unitDetail = masterDataCache.getAllUnits();
        for (UnitBasicDetail unitData : unitDetail) {
            if (unitData.getRegion().equals(region) && unitData.getStatus().value().equals(AppConstants.ACTIVE) && unitData.isLive() &&
                    unitData.getCategory().value().equals(AppConstants.CAFE)) {
                UnitWiseSummary uwd = new UnitWiseSummary();
                uwd.setUnitBasicDetails(unitData);
                uwd.setChaayosFlag(false);
                uwd.setGntFlag(false);
                data.add(uwd);
            }
        }
        for (UnitWiseSummary t : data) {
            for (OrdersDetailsShort orderShort : ordersDetailsShorts) {
                if (t.getUnitBasicDetails().getId() == orderShort.getRequestUnit().getId() && (orderShort.getGNTFlag() != null && orderShort.getGNTFlag() == true)) {
                    LOG.info("unit Id {}  and GNT ", t.getUnitBasicDetails().getId());
                    t.setGntFlag(true);
                }
                if (t.getUnitBasicDetails().getId() == orderShort.getRequestUnit().getId() && (orderShort.getGNTFlag() == null || orderShort.getGNTFlag() == false)) {
                    LOG.info("unit Id {} and Chaayos ", t.getUnitBasicDetails().getId());
                    t.setChaayosFlag(true);
                }
            }
        }
        int allGntCount = 0;
        int allChaayosCount = 0;
        for (UnitWiseSummary t : data) {
            if (t.getGntFlag()) {
                allGntCount += 1;
            }
            if (t.getChaayosFlag()) {
                allChaayosCount += 1;
            }
        }
        productionPlanningSummary.setAllChaayosCount(allChaayosCount);
        productionPlanningSummary.setAllGntCount(allGntCount);
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean isDifferentCompany(Integer requestOrderId) {
        return requestOrderManagementDao.isDifferentCompany(requestOrderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitPlanItemRequest> getRoItemsByPlanId(Integer planId) {
        ProductionPlanEventData eventData = productionPlanManagementDao.find(ProductionPlanEventData.class, planId);
        List<UnitPlanItemRequest> result = new ArrayList<>();

        for (PlanOrderMappingData mapping : eventData.getOrderMappingData()) {
            for (RequestOrderItemData itemData : mapping.getRequestOrder().getRequestOrderItemDatas()) {
                UnitPlanItemRequest entry = new UnitPlanItemRequest();
                entry.setUnitId(itemData.getRequestOrderData().getRequestUnitId());
                entry.setUnitName(masterDataCache.getUnit(entry.getUnitId()).getName());
                entry.setRoId(itemData.getRequestOrderData().getId());
                entry.setRoItemId(itemData.getId());
                entry.setRequestedQuantity(itemData.getRequestedQuantity());
                entry.setRequestedAbsoluteQuantity(itemData.getRequestedAbsoluteQuantity());
                entry.setRoProductId(itemData.getProductId());
                if (Objects.nonNull(itemData.getUnitOfMeasure())) {
                    entry.setUom(itemData.getUnitOfMeasure());
                }
                if (itemData.getAdjustedQuantity() != null) {
                    entry.setAdjustedQuantity(itemData.getAdjustedQuantity());
                }
                if (itemData.getAdjustedReason() != null) {
                    entry.setAdjustedReason(itemData.getAdjustedReason());
                }

                if (itemData.getProductionBookingCompleted() != null) {
                    entry.setProductionBookingCompleted(itemData.getProductionBookingCompleted());
                } else {
                    entry.setProductionBookingCompleted(AppConstants.NO);
                }

                if (itemData.getRequestOrderData().getStatus() != null) {
                    entry.setStatus(itemData.getRequestOrderData().getStatus());
                    Boolean transferredStatus = checkTransferStatus(itemData.getRequestOrderData().getStatus(), itemData.getRequestOrderData().getChildRO(), entry);
                    entry.setTransferred(transferredStatus);
                } else {
                    entry.setTransferred(false);
                }
                updatePackagingAdjustments(entry, itemData);
                result.add(entry);
            }
        }
        return result;
    }

    private void updatePackagingAdjustments(UnitPlanItemRequest entry, RequestOrderItemData itemData) {
        List<MultiPackagingAdjustments> packagingAdjustmentsList = new ArrayList<>();
        for (MultiPackagingAdjustmentsData adjustmentsData : itemData.getPackagingAdjustmentsData()) {
            MultiPackagingAdjustments packagingAdjustments = new MultiPackagingAdjustments(adjustmentsData.getItemPackagingId(), adjustmentsData.getPackagingId(),
                    adjustmentsData.getIsChecked().equalsIgnoreCase(AppConstants.YES), adjustmentsData.getQuantity());
            packagingAdjustmentsList.add(packagingAdjustments);
        }
        entry.setMultiPackagingAdjustments(packagingAdjustmentsList);
    }

    private Boolean checkTransferStatus(String status, RequestOrderData childRO, UnitPlanItemRequest entry) {
        if (status.equalsIgnoreCase(SCMOrderStatus.TRANSFERRED.value())) {
            entry.setStatus("TRANSFERRED(R)");
            return true;
        } else if (status.equalsIgnoreCase(SCMOrderStatus.SETTLED.value())) {
            entry.setStatus(status);
            return true;
        } else if (status.equalsIgnoreCase(SCMOrderStatus.CANCELLED.value()) && childRO != null) {
            entry.setStatus("TRANSFERRED(B)");
            return true;
        } else {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateAdjustedQuantities(List<UnitPlanItemRequest> list, String updatedBy) {
        try {
            for (UnitPlanItemRequest item : list) {
                updateRoItemDataWithAdjustedQuantity(item, updatedBy);
            }
            return true;
        } catch (Exception e) {
            LOG.error("Error Occurred While Updating Ro Item Adjusted Quantity and Reason :: ", e);
            return false;
        }
    }

    private void updateRoItemDataWithAdjustedQuantity(UnitPlanItemRequest item, String updatedBy) {
        RequestOrderItemData requestOrderItemData = requestOrderManagementDao.find(RequestOrderItemData.class, item.getRoItemId());
        RequestOrderData requestOrder = requestOrderManagementDao.find(RequestOrderData.class, requestOrderItemData.getRequestOrderData().getId());
        if (requestOrder != null) {
            requestOrderItemData.setRequestedQuantity(SCMUtil.add(requestOrderItemData.getRequestedAbsoluteQuantity(), item.getAdjustedQuantity()));
            requestOrderItemData.setAdjustedQuantity(item.getAdjustedQuantity());
            requestOrderItemData.setAdjustedReason(item.getAdjustedReason());
            if (requestOrderItemData.getNegotiatedUnitPrice() != null) {
                requestOrderItemData.setCalculatedAmount(requestOrderItemData.getNegotiatedUnitPrice()
                        .multiply(requestOrderItemData.getRequestedQuantity()));
                RequestOrderItem requestOrderItem = new RequestOrderItem();
                calculateTax(requestOrder, requestOrderItemData, requestOrderItem);
                requestOrderItemData.setTaxAmount(requestOrderItem.getTax());
            }
            updateMultiPackagings(item.getMultiPackagingAdjustments(), requestOrderItemData, updatedBy);
            requestOrderManagementDao.update(requestOrderItemData, false);
            BigDecimal total = BigDecimal.ZERO;
            for (RequestOrderItemData orderItemData : requestOrder.getRequestOrderItemDatas()) {
                if (orderItemData.getNegotiatedUnitPrice() != null) {
                    total = SCMUtil.add(total, SCMUtil.multiplyWithScale10(orderItemData.getRequestedQuantity(), orderItemData.getNegotiatedUnitPrice()));
                }
            }
            requestOrder.setTotalAmount(total);
            requestOrderManagementDao.update(requestOrder, false);
        } else {
            LOG.info("CANNOT FIND REQUEST ORDER FOR RO ITEM ID : {} ", requestOrderItemData.getId());
        }
    }

    private void updateMultiPackagings(List<MultiPackagingAdjustments> multiPackagingAdjustments, RequestOrderItemData requestOrderItemData, String updatedBy) {
        try {
            for (MultiPackagingAdjustmentsData adjustmentsData : requestOrderItemData.getPackagingAdjustmentsData()) {
                boolean found = false;
                for (MultiPackagingAdjustments packagingAdjustments : multiPackagingAdjustments) {
                    if (Objects.nonNull(packagingAdjustments.getItemPackagingId()) && packagingAdjustments.getItemPackagingId().equals(adjustmentsData.getItemPackagingId())) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    if (adjustmentsData.getIsChecked().equalsIgnoreCase(AppConstants.YES) || adjustmentsData.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                        LOG.info("Adjustment with Item packaging id : {} and Packaging Id : {} of RO Item : {} is not found", adjustmentsData.getItemPackagingId(),
                                adjustmentsData.getPackagingId(), adjustmentsData.getRequestOrderItemData().getId());
                        adjustmentsData.setIsChecked(AppConstants.NO);
                        adjustmentsData.setQuantity(BigDecimal.ZERO);
                        adjustmentsData.setLastUpdatedBy(updatedBy);
                        adjustmentsData.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                        requestOrderManagementDao.update(adjustmentsData, true);
                    }
                }
            }
            for (MultiPackagingAdjustments packagingAdjustments : multiPackagingAdjustments) {
                if (Objects.nonNull(packagingAdjustments.getItemPackagingId())) {
                    MultiPackagingAdjustmentsData adjustmentsData = requestOrderItemData.getPackagingAdjustmentsData().stream().filter(e ->
                            e.getItemPackagingId().equals(packagingAdjustments.getItemPackagingId())).findFirst().get();
                    adjustmentsData.setIsChecked(packagingAdjustments.getItemCheckPack() ? AppConstants.YES : AppConstants.NO);
                    adjustmentsData.setQuantity(packagingAdjustments.getItemQuantity());
                    adjustmentsData.setRequestOrderItemData(requestOrderItemData);
                    adjustmentsData.setLastUpdatedBy(updatedBy);
                    adjustmentsData.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                    requestOrderManagementDao.update(adjustmentsData, true);
                } else {
                    MultiPackagingAdjustmentsData adjustmentsData = new MultiPackagingAdjustmentsData(packagingAdjustments.getPackagingId(),
                            packagingAdjustments.getItemCheckPack() ? AppConstants.YES : AppConstants.NO, packagingAdjustments.getItemQuantity());
                    adjustmentsData.setRequestOrderItemData(requestOrderItemData);
                    adjustmentsData.setLastUpdatedBy(updatedBy);
                    adjustmentsData.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                    requestOrderManagementDao.add(adjustmentsData, true);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while updating the adjustments with packagings :: ", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, BigDecimal> getLastWeekAverageQty(List<Integer> productIds, Integer unitId,Boolean isSpecial) {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getLastWeekOrders(unitId,isSpecial);
        Map<Integer, BigDecimal> averageQtyMap = new HashMap<>();
        Map<Integer, Map<String, BigDecimal>> productToFullfilmentDateQtyMap = new HashMap<>();
        requestOrderDataList.forEach(ro -> {
            ro.getRequestOrderItemDatas().forEach(roItem -> {
                if (!productToFullfilmentDateQtyMap.containsKey(roItem.getProductId())) {
                    productToFullfilmentDateQtyMap.put(roItem.getProductId(), new HashMap<>());
                }
                if (!productToFullfilmentDateQtyMap.get(roItem.getProductId()).containsKey(ro.getFulfillmentDate().toString())) {
                    productToFullfilmentDateQtyMap.get(roItem.getProductId()).put(ro.getFulfillmentDate().toString(), BigDecimal.ZERO);
                }
                BigDecimal qty = SCMUtil.add(productToFullfilmentDateQtyMap.get(roItem.getProductId()).get(ro.getFulfillmentDate().toString()),
                        roItem.getRequestedQuantity());
                productToFullfilmentDateQtyMap.get(roItem.getProductId()).put(ro.getFulfillmentDate().toString(), qty);
            });
        });

        productIds.forEach(productId -> {
            if (!productToFullfilmentDateQtyMap.containsKey(productId)) {
                averageQtyMap.put(productId, BigDecimal.ZERO);
            } else {
                Map<String, BigDecimal> dayWiseQty = productToFullfilmentDateQtyMap.get(productId);
                Integer numberOfDays = dayWiseQty.size();
                BigDecimal sumOverDays = BigDecimal.ZERO;
                Set<String> days = dayWiseQty.keySet();
                for (String day : days) {
                    sumOverDays = SCMUtil.add(sumOverDays, dayWiseQty.get(day));
                }
                BigDecimal average = SCMUtil.divideWithScale10(sumOverDays, BigDecimal.valueOf(numberOfDays));
                averageQtyMap.put(productId, average);
            }
        });

        return averageQtyMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public RequestOrderData createROFromGR(GoodsReceived goodsReceived , Integer vendorId) throws SumoException {
        RequestOrderData requestOrderData = new RequestOrderData();
        requestOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        requestOrderData.setRequestUnitId(goodsReceived.getGenerationUnitId().getId());
        requestOrderData.setIsSpecialOrder(SCMUtil.setStatus(true));
        requestOrderData.setComment("Auto RO For Excess Qty During Payment Request Of Special Order");
        requestOrderData.setFulfillmentDate(SCMUtil.getCurrentBusinessDate());
        requestOrderData.setFulfillmentUnitId(goodsReceived.getGenerationUnitId().getId());
        requestOrderData.setGeneratedBy(AppConstants.SYSTEM_EMPLOYEE_ID);
        requestOrderData.setLastUpdatedBy(AppConstants.SYSTEM_EMPLOYEE_ID);
        requestOrderData.setGenerationTime(SCMUtil.getCurrentTimestamp());
        requestOrderData.setRaiseBy(AppConstants.NO);
        requestOrderData.setStatus(SCMOrderStatus.CREATED.value());
        requestOrderData.setVendorId(vendorId);




        requestOrderData.setRequestCompanyId(
                masterDataCache.getUnit(goodsReceived.getGenerationUnitId().getId()).getCompany().getId());
        requestOrderData.setFulfillmentCompanyId(
                masterDataCache.getUnit(goodsReceived.getGenerationUnitId().getId()).getCompany().getId());

        requestOrderData = setTransferType(requestOrderData); // setting Transfer Type on ROs
        requestOrderData = requestOrderManagementDao.add(requestOrderData, true);
        List<RequestOrderItemData> requestOrderItemDataList = new ArrayList<RequestOrderItemData>();
        for (GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()) {
            RequestOrderItemData requestOrderItemData = new RequestOrderItemData();
            requestOrderItemData.setTransferredQuantity(BigDecimal.valueOf(goodsReceivedItem.getTransferredQuantity()));
            requestOrderItemData.setRequestedQuantity(BigDecimal.valueOf(goodsReceivedItem.getTransferredQuantity()));
            requestOrderItemData.setRequestedAbsoluteQuantity(BigDecimal.valueOf(goodsReceivedItem.getTransferredQuantity()));
            requestOrderItemData.setUnitOfMeasure(goodsReceivedItem.getUnitOfMeasure());
            requestOrderItemData.setProductId(goodsReceivedItem.getProductId());
            requestOrderItemData.setProductName(scmCache.getProductDefinition(goodsReceivedItem.getProductId()).getProductName());
            requestOrderItemData.setReceivedQuantity(BigDecimal.ZERO);
            requestOrderItemData.setRequestOrderData(requestOrderData);
            requestOrderItemData.setExcessQuantity(BigDecimal.ZERO);
            requestOrderItemData.setOriginalQuantity(requestOrderItemData.getRequestedQuantity());
            requestOrderItemData.setVendorId(vendorId);
            RequestOrderItem requestOrderItem = new RequestOrderItem();
            calculateTax(requestOrderData, requestOrderItemData, requestOrderItem);
            requestOrderItemData = requestOrderManagementDao.add(requestOrderItemData, false);
            requestOrderManagementDao.setTaxDetail(masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId()).getLocation().getState().getId(), requestOrderItemData, requestOrderItem);

            requestOrderItemDataList.add(requestOrderItemData);
        }

        requestOrderData.setRequestOrderItemDatas(requestOrderItemDataList);
        requestOrderManagementDao.flush();
        return  requestOrderData;
    }


}
