package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.EnvType;

import java.util.List;
import java.util.Date;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 07-05-2016.
 */
public interface SCMAssetManagementDao extends SCMAbstractDao {

    List<AssetDefinitionData> getAllAssetFromUnit(int unitId);

    List<FixedAssetCompactDefinition> getAllFixedAssetFromUnit(int unitId , List<String> statusList);

    List<AssetDefinitionData> viewAllAssetsSlimFromUnitByName(int unitId, String name);

    List<AssetDefinitionData> getAllAssetFromUnitWithStatus(int unitId, String assetStatus);

    List<AssetDefinitionData> getAllAssetWithGRItemId(int grItemId);

    AssetTransferMappingData getLatestAssetDepreciationMapping(int assetId);

    AssetDefinitionData getAssetDefinitionDataByAssetId(int assetId);

    String getMaximumAssetTagValue();

    String getMaxToNoteUniqueSequence();

    AssetDefinitionData getAssetByTagValue(String tagValue);

    List<ProfileAttributeMappingData> getProfileAttributeMappingByProfileId(int profileId, String status);

    List<StockEventDefinitionData> getEventByUnit(int unitId, String eventStatus ,Integer roId);

    List<StockEventDefinitionData> getNsoCompletedEvent(int unitId);

    List<StockEventDefinitionData> getParentOrChildEvent(Integer eventId, Integer parentId, String subCategory);

    StockEventDefinitionData getLatestNSOEventByUnit(int unitId, String eventStatus ,String subtype);

    List<GatepassItemAssetMappingData> getAssociatedAssetMappingForGatepassItemId(int gatePassItemId);

    AssetDefinitionDataLog getLatestAssetDefinitionDataLog(int assetId);

    List<EntityAssetMappingData> getAssociatedEntityAssetMapping(int entityId, int entitySubId, String entityType);

    List<AssetTransferMappingData> getAssetTransferMapping(int assetId, Date startDate, Date endDate);

    List<AssetDefinitionData> getAssetForDepreciation(Date actualEndDate);

    AssetTransferMappingData getLastEntryWithStatus(int assetId, String status);

    List<AssetRecoveryDefinitionData> getAllAssetInRecoveryFromUnit(int unitId, String recoveryStatus);

    List<AssetRecoveryData> getAssetsInRecoveryFromUnit(Integer recoveryId, Integer unitId, String recoveryStatus, Integer assetId, Date startDate, Date endDate);

    List<AssetDepreciationMappingData> getDepreciationRegisteredFromToDateOnUnit(int unitId, Date startDate, Date endDate);

    List<AssetTransferMappingData> getAssetTransferMappingBetweenDateWithStatusAndOwner(int ownerId, Date startDate, Date endDate, String assetStatus);

    List<AssetDefinitionData> getAssetDefinitionDataWithOwnerAndStatus(int ownerId, List<String> assetStatusList);

    List<AssetTransferMappingData> getAssetTransferMappingWithOwnerAndStatus(int ownerId,Date businessDate, List<String> assetStatusList);

    StockEventDefinitionData getStockEventById(int eventId);

    List<AssetScrappedMappingData> getAssetScrappedMappingByUnitAndBusinessDate(int unitId, Date businessDate);

    List<AssetScrappedMappingData> getAssetScrappedMappingByUnitAndBusinessDateRange(int unitId, Date startDate, Date endDate);

    List<AssetDefinitionData> getAllAssetFromUnitByProductIds(int unitId , List<Integer> productIds);

    public List<AssetDefinitionData> getAssetsByIds(List<Integer> assetIds);

    public List<Integer> checkIfTransferInProgress(List<Integer> assetIds);

    public GoodsReceivedData getGrForTO(Integer toId);

    public List<AssetDefinitionData> getAllNonAssetStockInAssetInventory(List<Integer> productIds);

    public List<NonScannableAssetProducts> findNonScannableMapping(List<Integer> productIds , Boolean findAll);

    public List<StockEventAssetMappingDefinitionData> findAssetsByEventId(Integer eventId);
    public StockEventDefinitionData findEventbyAssetId(Integer assetId, Integer unitId);
    public List<StockEventAssetMappingDefinitionData> findAssetsByEventIdAndProductId(Integer eventId , Integer productId) ;

    public List<FaTransferData> fetchFaTransferData(Integer eventId);

    public List<FaStockEventExtraScannedItems> findExtraScannedAssetsByEventId(Integer eventId);

    public String getLatestStockTakeVersion();

    public List<AssetScanReportObject> getScannedAssetsByEventId(Integer eventId, EnvType envType);

    public List<AssetScanReportObject> getLostAssetsByEventId(Integer eventId, EnvType envType);

    public List<ExcessAssetReportObject> getExcessAssetsByEventId(Integer eventId, EnvType envType);

    public StockTakeReportSummaryObject getSummaryByEventId(Integer eventId, EnvType envType);


    public AssetDefinitionData getAssetFromUnitAndTagValue(Integer unitId, String tagValue);

    List<StockEventDefinitionData> getInitiatedToEvent(int unitId);

    public List<DocumentDetailData> getSwitchAssetDocs(List<Integer> docIds);

    public List<StockEventDefinitionData> getLastStockTakeEventByAsset(Integer assetId, String eventStatus);

    StockEventDefinitionData getEventFromGr(Integer grId, String status);

    List<FaGrData> getFaGrDataForEvent(Integer eventId);

    List<StockEventDefinitionData> getInitiatedEventForUnit(int unitId);

    public List<AssetRecoveryDetailData> getAssetRecoveryDetail(Integer recoveryId, String recoveryStatus);

    AssetDefinitionDataLog getFilteredLatestAssetDefinitionDataLog(int assetId,List<AssetStatusType> statuses) throws SumoException;

    StockEventDefinitionData getPendingFoundAssetEvent(Integer unitId);

    List<StockEventAssetMappingDefinitionData> findAssetsByEventIdAndAssetId(Integer eventId, Integer assetId);

    List<AssetRecoveryData> getAllAssetInRecoveryFromEventId(int eventId);

    Boolean checkUnitClosureRegularEvent(Integer unitId) throws SumoException;

    List<StockEventDefinitionData> getEventByUnitAndStatusList(int unitId, List<String> eventStatus, Integer roId);
}
