/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;
import com.stpl.tech.scm.data.model.AuditLogData;
import com.stpl.tech.scm.data.model.MaintenanceWhZoneMapping;
import com.stpl.tech.scm.data.model.PlanOrderItemData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.RegionFulfillmentMapping;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.UOMConversionMapping;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.ZipCodeDistanceMapping;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Rahul Singh on 04-05-2016.
 */

@Repository
public class SCMMetadataDaoImpl extends SCMAbstractDaoImpl implements SCMMetadataDao {

    private static final Logger LOG = LoggerFactory.getLogger(SCMMetadataDaoImpl.class);

    @Override
    public List<VendorDetailData> findAllVendorDetailData() {
        Query query = manager.createQuery(
                " select distinct v FROM VendorDetailData v " +
                        " left join fetch v.debitMappings " +
                        " left join fetch v.dispatchLocations as locationsAlias left join fetch locationsAlias.gstinDocument left join fetch locationsAlias.locationAddress " +
                        " left join fetch v.tdsDocument  " +
                        "left join fetch v.accountDetails as  accountAlias left join fetch accountAlias.uploadedChequeDocumentID" +
                        " left join fetch v.companyDetails as companyAlias left join fetch companyAlias.cinDocument left join fetch companyAlias.companyAddress " +
                        " left join fetch companyAlias.arcDocument left join fetch companyAlias.panDocument" +
                        " left join fetch companyAlias.cstDocument left join fetch companyAlias.vatDocument  left join fetch companyAlias.serviceTaxDocument left join fetch companyAlias.msmeDocument " +
                        " left join fetch v.vendorAddress ");
        return query.getResultList();
    }

    @Override
    public List<SkuDefinitionData> findAllSkus(){
        Query query = manager.createQuery(
                " select distinct s FROM SkuDefinitionData s " +
                        " left join fetch s.linkedProduct where s.skuStatus not in (:statusList)" );
        query.setParameter("statusList", List.of(SwitchStatus.INITIATED.name(), SwitchStatus.CANCELLED.name(), SwitchStatus.REJECTED.name()));
        return query.getResultList();
    }


    @Override
    public List<ProductDefinitionData> findAllProductDefinitions() {
        Query query = manager.createQuery(
                "SELECT DISTINCT p FROM ProductDefinitionData p " +
                        "LEFT JOIN FETCH p.categoryDefinition c " +
                        "LEFT JOIN FETCH p.subCategoryDefinition sc " +
                        "LEFT JOIN FETCH p.profileDefinitionData prof " +
                        "LEFT JOIN FETCH p.skuDefinitionDataList sku " +
                        "LEFT JOIN FETCH p.derivedMappingDataList d "
        );
        return query.getResultList();
    }

    @Override
    public List<UnitVendorMappingData> getVendorMappings(int unitId) {
        Query query = manager.createQuery(
                "FROM UnitVendorMappingData p WHERE p.unitId = :unitId and p.mappingStatus = :mappingStatus");
        query.setParameter("unitId", unitId);
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name());
        return query.getResultList();
    }

    @Override
    public List<Pair<Integer, Integer>> getUnitToProductList(List<Integer> units) {
        List<Pair<Integer, Integer>> l = new ArrayList<Pair<Integer, Integer>>();
        Query query = manager
                .createQuery("SELECT p.unitId, s.linkedProduct.productId FROM UnitSkuMapping p , SkuDefinitionData s "
                        + "WHERE s.skuId = p.skuId AND p.mappingStatus = :mappingStatus AND p.unitId IN :units AND s.skuStatus = :skuStatus");
        query.setParameter("units", units);
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name());
        query.setParameter("skuStatus", SwitchStatus.ACTIVE.name());
        List<Object[]> results = query.getResultList();
        results.stream().forEach(p -> l.add(new Pair<Integer, Integer>((Integer) p[0], (Integer) p[1])));
        return l;
    }

    @Override
    public List<Pair<Integer, Integer>> getUnitToSKUList(List<Integer> units) {
        List<Pair<Integer, Integer>> l = new ArrayList<Pair<Integer, Integer>>();
        Query query = manager.createQuery("SELECT p.unitId, s.skuId FROM UnitSkuMapping p , SkuDefinitionData s "
                + "WHERE s.skuId = p.skuId AND p.mappingStatus = :mappingStatus AND p.unitId IN :units AND s.skuStatus = :skuStatus");
        query.setParameter("units", units);
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name());
        query.setParameter("skuStatus", SwitchStatus.ACTIVE.name());
        List<Object[]> results = query.getResultList();
        results.stream().forEach(p -> l.add(new Pair<Integer, Integer>((Integer) p[0], (Integer) p[1])));
        return l;
    }

    @Override
    public List<Integer> getUnits() {
        Query query = manager.createQuery("SELECT U.unitId FROM UnitDetailData U WHERE  U.unitStatus = :unitStatus");
        query.setParameter("unitStatus", SwitchStatus.ACTIVE.name());
        return query.getResultList();
    }

    @Override
    public List<UnitDistanceMappingData> getUnitDistanceMapping() {
        return findAll(UnitDistanceMappingData.class);
    }

    @Override
    public List<ZipCodeDistanceMapping> getZipCodeDistanceMapping() {
        return findAll(ZipCodeDistanceMapping.class);
    }

    public Map<Integer, Map<Integer, ProductRecipeKey>> getProductProfileMapping() {
        Map<Integer, Map<Integer, ProductRecipeKey>> productProfileMap = new HashMap<>();
        Query query = manager
                .createQuery("SELECT p.unitId, s.linkedProduct.productId, p.profile FROM UnitSkuMapping p , SkuDefinitionData s "
                        + "WHERE s.skuId = p.skuId AND p.mappingStatus = :mappingStatus AND s.skuStatus = :skuStatus");
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name());
        query.setParameter("skuStatus", SwitchStatus.ACTIVE.name());
        List<Object[]> results = query.getResultList();
        results.stream().forEach(p -> {
            int unitId = (Integer) p[0];
            int productId = (Integer) p[1];
            String profile = (String) p[2];
            if (!productProfileMap.containsKey(unitId)) {
                productProfileMap.put(unitId, new HashMap<>());
            }
            if (!productProfileMap.get(unitId).containsKey(productId)) {
                productProfileMap.get(unitId).put(productId, new ProductRecipeKey(productId, null, profile));
            }
        });
        return productProfileMap;
    }

    @Override
    public PlanOrderItemData getPlanOrderData(Integer productId, Integer unitId) {
        Query query = manager.createQuery("select item FROM PlanOrderItemData item, ProductionPlanEventData plan  WHERE plan.id = item.eventId and plan.unitId =:unitId and item.productId =:productId ORDER BY plan.id DESC");
        query.setParameter("productId", productId);
        query.setParameter("unitId", unitId);
        try {
            return (PlanOrderItemData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        } catch (NonUniqueResultException e) {
            return (PlanOrderItemData) query.getResultList().get(0);
        }

    }

    @Override
    public List<RegionFulfillmentMapping> getAllActiveRegionMappings() {
        Query query = manager.createQuery(
                "FROM  RegionFulfillmentMapping r WHERE r.mappingStatus = :status ");
        query.setParameter("status", SwitchStatus.ACTIVE.value());
        return query.getResultList();
    }

    @Override
    public List<Integer> getUnitsForNonZeroSkuInventory(Integer skuId) {
        Query query = manager.createQuery("select DISTINCT s.unitId FROM StockEntryEventData s ,SCMDayCloseEventData c where s.updateEventId = c.eventId and  s.skuId = :skuId " +
                " and c.status = :status" +
                " and s.currentStock > :stock");
        query.setParameter("skuId", skuId);
        query.setParameter("status", StockEventStatus.INITIATED.value());
        query.setParameter("stock", BigDecimal.valueOf(0.00));

        List<Integer> unitIds = query.getResultList();
        return unitIds;

    }

    @Override
    public List<Integer> checkForInCompletePurchaseOrder(Integer skuId, Integer unitId, Boolean isAsset ) {
        List<String> expiredStatuses = new ArrayList<>(Arrays.asList(PurchaseOrderExtendedStatus.EXPIRED.value(),
                PurchaseOrderExtendedStatus.TERMINATED.value()));
        List<String> acceptedStatruses = new ArrayList<>(Arrays.asList(PurchaseOrderStatus.REJECTED.value(), PurchaseOrderStatus.CANCELLED.value(), PurchaseOrderStatus.CLOSED.value()));
        StringBuilder queryString = new StringBuilder("select distinct po.id FROM PurchaseOrderData po ,PurchaseOrderItemData item where po.id = item.purchaseOrderData.id and ");
        if (Objects.nonNull(skuId)) {
            queryString.append(" item.skuId = :skuId ");
        }
        if (Objects.nonNull(unitId)) {
            queryString.append(" po.deliveryLocationId = :unitId");
        }
        queryString.append(" and (po.status not in :statuses  and (po.leadTime is not null and po.expiryStatus  not in :expiryStatuses and  FROM_UNIXTIME(UNIX_TIMESTAMP(po.fulfillmentDate)+ (2*po.leadTime*86400)) >=  :currentDate)) ");



        if (isAsset.equals(Boolean.TRUE)) {
            queryString.append(" and po.orderType = :assetType ");
        }

        Query query = manager.createQuery(queryString.toString());
        if (Objects.nonNull(skuId)) {
            query.setParameter("skuId", skuId);
        }
        if (Objects.nonNull(unitId)) {
            query.setParameter("unitId", unitId);
        }
        if (isAsset.equals(Boolean.TRUE)) {
            query.setParameter("assetType", VendorGrType.FIXED_ASSET_ORDER.name());
        }

        query.setParameter("statuses", acceptedStatruses);
        query.setParameter("expiryStatuses", expiredStatuses);
        query.setParameter("currentDate", SCMUtil.getCurrentTimestamp());


        List<Integer> poIds = query.getResultList();
        return poIds;
    }

    @Override
    public List<Integer> checkForInCompleteTO(Integer skuId, Integer unitId) {
        List<String> acceptedStatruses = new ArrayList<>(Arrays.asList(SCMOrderStatus.CANCELLED.value(), SCMOrderStatus.SETTLED.value()));
        StringBuilder queryString = new StringBuilder("select distinct to.id FROM TransferOrderData to , TransferOrderItemData item where to.id = item.transferOrderData.id and  ");
        if (Objects.nonNull(skuId)) {
            queryString.append(" item.skuId = :skuId ");
        }
        if (Objects.nonNull(unitId)) {
            queryString.append(" ( to.generatedForUnitId = :unitId or to.generationUnitId  = :unitId )  ");
        }
        queryString.append(" and to.status not in :statuses");
        Query query = manager.createQuery(queryString.toString());
        if (Objects.nonNull(skuId)) {
            query.setParameter("skuId", skuId);
        }
        if (Objects.nonNull(unitId)) {
            query.setParameter("unitId", unitId);
        }
        query.setParameter("statuses", acceptedStatruses);

        List<Integer> toIds = query.getResultList();
        return toIds;
    }

    @Override
    public List<Integer> checkForInCompleteInternalGr(Integer skuId, Integer unitId, Boolean isAsset) {
        List<String> acceptedStatruses = new ArrayList<>(Arrays.asList(SCMOrderStatus.CANCELLED.value(), SCMOrderStatus.SETTLED.value()));
        List<String> toTypes = new ArrayList<>(Arrays.asList(TransferOrderType.FIXED_ASSET_TRANSFER.value(), TransferOrderType.RENOVATION_ASSET_TRANSFER.value()));
        StringBuilder queryString = new StringBuilder("select distinct gr.id  FROM GoodsReceivedData gr , GoodsReceivedItemData item where gr.id = item.goodsReceivedData.id and  ");
        if (Objects.nonNull(skuId)) {
            queryString.append(" item.skuId = :skuId ");
        }
        if (Objects.nonNull(unitId)) {
            queryString.append(" ( gr.generatedForUnitId = :unitId ");
            if (isAsset.equals(Boolean.FALSE)) {
                queryString.append(" or gr.generationUnitId  = :unitId ");
            }
            queryString.append(" )");
        }
        queryString.append(" and gr.status not in :statuses");

        if (isAsset.equals(Boolean.TRUE)) {
            queryString.append(" and gr.transferOrderType in :toTypes ");
        }
        Query query = manager.createQuery(queryString.toString());
        if (Objects.nonNull(skuId)) {
            query.setParameter("skuId", skuId);
        }
        if (Objects.nonNull(unitId)) {
            query.setParameter("unitId", unitId);
        }
        query.setParameter("statuses", acceptedStatruses);
        if (isAsset.equals(Boolean.TRUE)) {
            query.setParameter("toTypes", toTypes);
        }

        List<Integer> grIds = query.getResultList();
        return grIds;
    }
      @Override
      public List<Integer> checkForFaReceiveing(List<Integer> unitId ) {

          List<String> acceptedStatuses = new ArrayList<>(Arrays.asList(SCMOrderStatus.CANCELLED.value(), SCMOrderStatus.SETTLED.value()));
          List<String> toTypes = new ArrayList<>(Arrays.asList(TransferOrderType.FIXED_ASSET_TRANSFER.value(), TransferOrderType.RENOVATION_ASSET_TRANSFER.value()));
          List<Integer> unitIds = new ArrayList<>();
          try{
              StringBuilder queryString = new StringBuilder("select distinct gr.generatedForUnitId  FROM GoodsReceivedData gr , GoodsReceivedItemData item where gr.id = item.goodsReceivedData.id and  ");
              queryString.append(" ( gr.generatedForUnitId In  (:unitId) ");
              queryString.append(" )");
              queryString.append(" and gr.status not in :statuses");
              queryString.append(" and gr.transferOrderType in :toTypes ");
              queryString.append(" and  ") ;
              queryString.append("  FROM_UNIXTIME(UNIX_TIMESTAMP(gr.generationTime)+ 3*86400) < :currentDate") ;
              Query query = manager.createQuery(queryString.toString());
              if (Objects.nonNull(unitId)) {
                  query.setParameter("unitId", unitId);
              }
              query.setParameter("statuses", acceptedStatuses);
              query.setParameter("toTypes", toTypes);
              Date currentDate = new Date() ;
              query.setParameter("currentDate",currentDate);
              unitIds = query.getResultList();
          }catch (Exception e){
              LOG.error("Error while Fetching Pending FA GRs For Unit Id  :::::::::: {} :::: {}",unitId,e);
          }
         return unitIds ;
      }

    @Override
    public List<Object[]> getPendingMilkBreadRos(Date businessDate, List<Integer> milkBreadProductIds, Integer requestUnitId, Set<Integer> roIds) {
        try {
            StringBuilder queryString = new StringBuilder("SELECT ro.REQUEST_UNIT_ID, group_concat(distinct ro.REQUEST_ORDER_ID), group_concat(distinct ro.VENDOR_ID) FROM REQUEST_ORDER ro " +
                    "INNER JOIN REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID " +
                    "WHERE ro.IS_SPECIAL_ORDER =:specialOrder AND ro.FULFILLMENT_DATE =:businessDate AND ro.REQUEST_ORDER_STATUS NOT IN(:statusList) " +
                    "AND ro.IS_NOTIFIED =:isNotified AND roi.PRODUCT_ID IN(:milkBreadProductIds)");
            if (Objects.nonNull(requestUnitId)) {
                queryString.append("AND ro.REQUEST_UNIT_ID =:requestUnitId ");
            }
            if (Objects.nonNull(roIds)) {
                queryString.append("AND ro.REQUEST_ORDER_ID IN(:roIds) ");
            }
            queryString.append("GROUP BY ro.REQUEST_UNIT_ID");
            Query query = manager.createNativeQuery(queryString.toString());
            query.setParameter("specialOrder", AppConstants.YES);
            query.setParameter("businessDate", businessDate);
            query.setParameter("statusList", Arrays.asList(SCMOrderStatus.CANCELLED.toString(), SCMOrderStatus.SETTLED.toString()));
            query.setParameter("isNotified", AppConstants.YES);
            query.setParameter("milkBreadProductIds", milkBreadProductIds);
            if (Objects.nonNull(requestUnitId)) {
                query.setParameter("requestUnitId", requestUnitId);
            }
            if (Objects.nonNull(roIds)) {
                query.setParameter("roIds", roIds);
            }
            List<Object[]> result = (List<Object[]>) query.getResultList();
            return result;
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting the Pending Milk Bread Ros :: ",e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<UnitVendorMappingData> getAllUnitVendorsWithVendorIdsAndUnitIds(Set<Integer> specializedVendorIds, Set<Integer> unitIds) {
        Query query = manager.createQuery("FROM UnitVendorMappingData p WHERE p.unitId IN(:unitIds) and p.vendorId IN(:specializedVendorIds)");
        query.setParameter("unitIds",unitIds).setParameter("specializedVendorIds",specializedVendorIds);
        return (List<UnitVendorMappingData>) query.getResultList();
    }


    @Override
    public List<Integer> checkForProductionBookingMapping(Integer skuId) {
        Query query = manager.createQuery("select distinct b.productId FROM ProductionBookingMappingData b where  b.linkedSkuId = :skuId " +
                " and b.mappingStatus = :status");
        query.setParameter("skuId", skuId);
        query.setParameter("status", StatusType.ACTIVE.value());

        List<Integer> productIds = query.getResultList();
        return productIds;
    }

    @Override
    public List<Integer> checkInCompleteGrForPayment(Integer skuId, Integer unitId) {
        StringBuilder queryString = new StringBuilder("select distinct g.goodsReceivedId  FROM VendorGoodsReceivedData g , VendorGoodsReceivedItemData item WHERE g.goodsReceivedId = item.goodsReceivedData.goodsReceivedId  and ");
        if (Objects.nonNull(skuId)) {
            queryString.append(" item.skuId =:skuId");
        }
        if (Objects.nonNull(unitId)) {
            queryString.append(" g.deliveryUnitId = :unitId");
        }
        queryString.append(" and g.grStatus = :grStatus and g.toBePaid = :toBePaid  and g.invalidGR=:invalidGR and g.paymentRequestData IS NULL ");
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("invalidGR", SCMServiceConstants.SCM_CONSTANT_NO)
                .setParameter("toBePaid", SCMServiceConstants.SCM_CONSTANT_YES)
                .setParameter("grStatus", PurchaseOrderStatus.CREATED.value());
        if (Objects.nonNull(skuId)) {
            query.setParameter("skuId", skuId);
        }
        if (Objects.nonNull(unitId)) {
            query.setParameter("unitId", unitId);
        }

        return query.getResultList();

    }

    @Override
    public List<Integer> checkInCompletePayment(Integer skuId, Integer unitId) {
        List<String> acceptedStatuses = new ArrayList<>(Arrays.asList(PaymentRequestStatus.REJECTED.value(), PaymentRequestStatus.SENT_FOR_PAYMENT.value(),
                PaymentRequestStatus.PAID.value(), PaymentRequestStatus.CANCELLED.value(), PaymentRequestStatus.FORCE_CLOSED.value()));
        StringBuilder queryString = new StringBuilder("select distinct pr.id  FROM VendorGoodsReceivedData gr ,VendorGoodsReceivedItemData  item, PaymentRequestData pr WHERE gr.goodsReceivedId = item.goodsReceivedData.goodsReceivedId and ");
        if (Objects.nonNull(skuId)) {
            queryString.append(" item.skuId =:skuId ");
        }
        if (Objects.nonNull(unitId)) {
            queryString.append(" pr.requestingUnit = :unitId ");
        }
        queryString.append(" and  gr.paymentRequestData.id = pr.id   and gr.grStatus = :grStatus and pr.currentStatus not in :acceptedStatuses");
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("acceptedStatuses", acceptedStatuses)
                .setParameter("grStatus", PurchaseOrderStatus.CREATED.value());
        if (Objects.nonNull(skuId)) {
            query.setParameter("skuId", skuId);
        }
        if (Objects.nonNull(unitId)) {
            query.setParameter("unitId", unitId);
        }

        return query.getResultList();

    }

    @Override
    public List<Integer> getAllActiveLinkedSKUs(Integer productId) {
        Query query = manager.createQuery("select sku.skuId from SkuDefinitionData  sku where  sku.linkedProduct.productId = :productId and sku.skuStatus = :status");
        query.setParameter("productId", productId);
        query.setParameter("status", StatusType.ACTIVE.value());

        return query.getResultList();
    }

    @Override
    public List<Integer> getAllActiveAssetsForUnit(Integer unitId) {
        List<String> ignoredStatuses = new ArrayList<>(Arrays.asList(AssetStatusType.DUPLICATE.value(), AssetStatusType.DECAPITALIZED.value(),
                AssetStatusType.SCRAPPED.value(),AssetStatusType.LOST_ADJUSTED.value() , AssetStatusType.SETTLED.value()));
        Query query = manager.createQuery("select asset.assetId from AssetDefinitionData asset  where asset.unitId = :unitId and " +
                " asset.assetStatus not in :ignoredStatuses ");
        query.setParameter("unitId", unitId);
        query.setParameter("ignoredStatuses", ignoredStatuses);
        return query.getResultList();
    }

    @Override
    public Optional<List<MaintenanceWhZoneMapping>> getAllMaintenanceWHMappings() {
        Query query = manager.createQuery("From MaintenanceWhZoneMapping m where m.mappingStatus = :status ");
        query.setParameter("status", StatusType.ACTIVE.value());

        return Optional.ofNullable(query.getResultList());
    }

    @Override
    public AuditLogData getLastAuditEntry() {
        Query query = manager.createQuery("FROM AuditLogData ORDER BY 1 DESC ");
        query.setMaxResults(1);
        try {
            return (AuditLogData) query.getSingleResult();
        } catch (Exception e) {
            LOG.info("No Audit Log Result Found :::");
            return null;
        }

    }
    @Override
    public Set<Integer> checkActiveStocks(UnitDetail unitDetail) {
     Boolean isWhKitchen =    SCMUtil.isWareHouseOrKitchen(unitDetail);
        Set<Integer> keyIds = new HashSet<>();
        try {
            Query query = null;
            if(Boolean.TRUE.equals(isWhKitchen)){
                query = manager.createQuery("SELECT keyId FROM CostDetailDataWh C where C.unitId =:unitId and C.latest =:latest and C.quantity>:quantity");
            }else{
                query = manager.createQuery("SELECT keyId FROM CostDetailDataCafe C where C.unitId =:unitId and C.latest =:latest and C.quantity>:quantity");
            }
            query.setParameter("unitId", unitDetail.getUnitId());
            query.setParameter("latest", AppConstants.YES);
            query.setParameter("quantity", BigDecimal.valueOf(0));
            keyIds = new HashSet<>(query.getResultList());
        } catch (Exception e) {
            LOG.error("Error while getting active units data ", e);
        }
        return keyIds;

    }


    @Override
    public Set<Integer> checkActiveGatePass(Integer unitId) {
        Set<Integer> gatePassIds = new HashSet<>();
        try{
            Query query = manager.createQuery("FROM GatepassData G where G.sendingUnit = :unitId " +
                    "and G.returnable = :returnable and (G.returnStatus <> :returnStatus  or G.returnStatus IS NULL) AND G.status <> :statuus AND G.status <> :status" +
                    " AND G.assetGatePass=:assetGatePass");
            query.setParameter("unitId",unitId);
            query.setParameter("returnable",AppConstants.YES);
            query.setParameter("returnStatus","COMPLETED");
            query.setParameter("status","CANCELLED");
            query.setParameter("statuus","CLOSED");
            query.setParameter("assetGatePass", AppConstants.YES);
            gatePassIds = new HashSet<>(query.getResultList());
        } catch (Exception e) {
           LOG.error("Error while getting active gatePass for unitId :{}",unitId);
        }
        return gatePassIds;
    }

    @Override
    public List<UOMConversionMapping> getUOMConversionMapping() {
        List<UOMConversionMapping> recipeDetailList = new ArrayList<>();
        try{
            Query query = manager.createQuery("FROM UOMConversionMapping UOM");
            recipeDetailList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting UOMConversionMapping ");
        }

        return recipeDetailList;
    }


}
