package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.RiderInfoData;
import com.stpl.tech.scm.data.model.RiderRoutePlanData;
import com.stpl.tech.scm.data.model.RiderRoutePlanStepData;
import com.stpl.tech.scm.domain.model.RiderRoutePlanStatusEnum;

import java.util.List;

public interface StockRedistributionDao extends SCMAbstractDao{
    RiderInfoData findRiderInfoByEmployeeId(Integer employeeId);

    List<RiderRoutePlanData> getRoutePlanOfRiderByStatus(RiderInfoData riderInfoData, List<RiderRoutePlanStatusEnum> asList, boolean fetchAllData);

    List<RiderRoutePlanStepData> getRiderRouteStepDetails(Integer riderRoutePlanStepDataId);

    List<RiderRoutePlanData> getRouteDetailsByRoutePlanId(Integer riderRoutePlanDataId);
}
