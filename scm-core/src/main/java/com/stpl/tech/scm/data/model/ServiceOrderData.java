package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "SERVICE_ORDER")
public class ServiceOrderData {

	private Integer id;
	private Date generationTime;
	private Date lastUpdateTime;
	private Integer costCenterId;
	private Integer vendorId;
	private Integer generatedBy;
	private Integer lastUpdatedBy;
	private BigDecimal totalCost;
	private BigDecimal totalAmount;
	private String receiptNumber;
	private String status;
	private String comment;
	private BigDecimal totalTaxes;
	private Integer approvedBy;
	private String vendorNotified = "N";
	private String forceClosed;
	private Integer dispatchLocationId;
	private DocumentDetailData soInvoiceDocument;
	private String type;
	private String tagName;
	private String accountedForInPnl;
	private Integer uploadedDocumentId;
	private List<AdvancePaymentData> advancePaymentDatas = new ArrayList<>(0);
	private Integer approvalOfHod;

	private List<ServiceOrderItemData> serviceOrderItemDataList = new ArrayList<>(0);
	// private List<PurchaseOrderNotificationData> notificationList = new
	// ArrayList<>(0);
	// private List<PurchaseOrderVendorGRMappingData> grMappingList = new
	// ArrayList<>(0);

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SERVICE_ORDER_ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "GENERATION_TIME", nullable = false)
	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}

	@Column(name = "VENDOR_ID", nullable = false)
	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public Integer getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(Integer generatedBy) {
		this.generatedBy = generatedBy;
	}

	@Column(name = "TOTAL_COST", nullable = true)
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal billAmount) {
		this.totalCost = billAmount;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = true)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal paidAmount) {
		this.totalAmount = paidAmount;
	}

	@Column(name = "ORDER_RECEIPT_NUMBER", nullable = true)
	public String getReceiptNumber() {
		return receiptNumber;
	}

	public void setReceiptNumber(String receiptNumber) {
		this.receiptNumber = receiptNumber;
	}

	@Column(name = "SERVICE_ORDER_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "COMMENT_TEXT", nullable = true, length = 1000)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceOrderId")
	public List<ServiceOrderItemData> getServiceOrderItemDataList() {
		return serviceOrderItemDataList;
	}

	public void setServiceOrderItemDataList(List<ServiceOrderItemData> serviceOrderItemDataList) {
		this.serviceOrderItemDataList = serviceOrderItemDataList;
	}

	@Column(name = "LAST_UPDATED_BY", nullable = true)
	public Integer getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(Integer lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	@Column(name = "TOTAL_TAXES", nullable = true)
	public BigDecimal getTotalTaxes() {
		return totalTaxes;
	}

	public void setTotalTaxes(BigDecimal totalTaxes) {
		this.totalTaxes = totalTaxes;
	}

	@Column(name = "APPROVED_BY", nullable = true)
	public Integer getApprovedBy() {
		return approvedBy;
	}

	public void setApprovedBy(Integer approvedBy) {
		this.approvedBy = approvedBy;
	}

	@Column(name = "VENDOR_NOTIFIED", nullable = true)
	public String getVendorNotified() {
		return vendorNotified;
	}

	public void setVendorNotified(String vendorNotified) {
		this.vendorNotified = vendorNotified;
	}

	@Column(name = "FORCE_CLOSED", nullable = true)
	public String getForceClosed() {
		return forceClosed;
	}

	public void setForceClosed(String forceClosed) {
		this.forceClosed = forceClosed;
	}

	@Column(name = "DISPATCH_LOCATION_ID", nullable = true)
	public Integer getDispatchLocationId() {
		return dispatchLocationId;
	}

	public void setDispatchLocationId(Integer dispatchLocationId) {
		this.dispatchLocationId = dispatchLocationId;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SO_INVOICE_ID")
	public DocumentDetailData getSoInvoiceDocument() {
		return soInvoiceDocument;
	}

	public void setSoInvoiceDocument(DocumentDetailData soInvoiceDocument) {
		this.soInvoiceDocument = soInvoiceDocument;
	}

	/*
	 * @OneToMany(fetch = FetchType.LAZY, mappedBy="purchaseOrderId") public
	 * List<PurchaseOrderNotificationData> getNotificationList() { return
	 * notificationList; }
	 *
	 * public void setNotificationList(List<PurchaseOrderNotificationData>
	 * notificationList) { this.notificationList = notificationList; }
	 *
	 * @OneToMany(fetch = FetchType.LAZY, mappedBy="purchaseOrderData") public
	 * List<PurchaseOrderVendorGRMappingData> getGrMappingList() { return
	 * grMappingList; }
	 *
	 * public void setGrMappingList(List<PurchaseOrderVendorGRMappingData>
	 * mappingList) { this.grMappingList = mappingList; }
	 */

	@Column(name = "COST_CENTER_ID", nullable = true)
	public Integer getCostCenterId() {
		return costCenterId;
	}

	public void setCostCenterId(Integer costCenterId) {
		this.costCenterId = costCenterId;
	}

	@Column(name = "TYPE", nullable = true)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "TAG_NAME", nullable = true)
	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	@Column(name = "ACCOUNTED_FOR_IN_PNL", nullable = true)
	public String getAccountedForInPnl() {
		return accountedForInPnl;
	}

	public void setAccountedForInPnl(String accountedForInPnl) {
		this.accountedForInPnl = accountedForInPnl;
	}

	@Column(name = "UPLOADED_DOCUMENT_ID", nullable = true)
	public Integer getUploadedDocumentId() {
		return uploadedDocumentId;
	}

	public void setUploadedDocumentId(Integer uploadedDocumentId) {
		this.uploadedDocumentId = uploadedDocumentId;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceOrderData")
	public List<AdvancePaymentData> getAdvancePaymentDatas() {
		return advancePaymentDatas;
	}

	public void setAdvancePaymentDatas(List<AdvancePaymentData> advancePaymentDatas) {
		this.advancePaymentDatas = advancePaymentDatas;
	}

	@Column(name = "APPROVAL_OF_HOD", nullable = true)
	public Integer getApprovalOfHod() {
		return approvalOfHod;
	}

	public void setApprovalOfHod(Integer approvalOfHod) {
		this.approvalOfHod = approvalOfHod;
	}
}
