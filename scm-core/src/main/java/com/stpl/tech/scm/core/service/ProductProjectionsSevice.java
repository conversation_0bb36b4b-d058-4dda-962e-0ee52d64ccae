package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.ForecastReportResponse;
import com.stpl.tech.scm.data.model.ProductProjectionsDetailsData;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnitDetail;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnits;
import org.apache.http.HttpResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ProductProjectionsSevice {

    List<ForecastReportResponse> getForecastReport(HttpResponse response) throws IOException;

    List<ProductProjectionsUnitDetail> getUnitInputData(Date startDate, Date endDate, Integer unitId);

    List<ProductProjectionsUnitDetail> readUploadFile(MultipartFile file) throws IOException,SumoException;

    List<ProductProjectionsUnitDetail> getMenuOutput(List<ForecastReportResponse> forecastReportResponseList, ProductProjectionsUnitDetail unitDetail, ProductProjectionsUnitDetail inactiveUnit);

    List<ProductProjectionsUnitDetail> getScmOutput(List<ProductProjectionsUnitDetail> sortedMenuList, Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers);

    List<ProductProjectionsUnitDetail> getSemiFinishedOutput(List<ProductProjectionsUnitDetail> semiFinishedScmList);

    ProductProjectionsDetailsData createProductProjectionsEntry(Date startDate, Date endDate, String generatedBy, boolean isUploaded) throws SumoException;

    void updateProjectionsData(String downloadedPath, ProductProjectionsDetailsData projectionsDetailsData);

    Map<Integer,List<ForecastReportResponse>> getUnitWiseResponseList(List<ForecastReportResponse> forecastReportResponseList);

    HashMap<String,Object> generateProductProjections(ProductProjectionsUnits projectionsData, Boolean isUnitsUploaded) throws SumoException, IOException;
}
