package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "VENDOR_CONTRACT_ITEM_DATA")
public class VendorContractItemData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CONTRACT_ITEM_ID",unique = true)
    private Integer contractItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "WORK_ORDER_ID")
    private WorkOrderData workOrderData;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CONTRACT_ID")
    private VendorContractData contractData;

    // no need of vendor here
    @Column(name = "VENDOR_ID")
    private Integer vendorId;

    @Column(name = "SKU_ID")
    private Integer skuId;

    @Column(name = "SKU_PACKAGING_ID")
    private Integer skuPackagingId;

    // need to remove names
    @Column(name = "DISPATCH_LOCATION")
    private String dispatchLocation;

    // need to remove names
    @Column(name = "DELIVERY_LOCATION")
    private String deliveryLocation;

    @Column(name = "DISPATCH_LOCATION_ID")
    private Integer dispatchLocationId;

    @Column(name = "DELIVERY_LOCATION_ID")
    private Integer deliveryLocationId;

    @Column(name = "CURRENT_PRICE")
    private  BigDecimal currentPrice;

    @Column(name = "UPDATED_PRICE")
    private BigDecimal updatedPrice;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "SELECTED_REJECTION_REASON")
    private String selectedRejectionReason;

    @Column(name = "PREVIOUS_REJECTION_REASON")
    private String previousRejectionReason;

    @Column(name = "CURRENT_REJECTION_REASON")
    private String rejectionReason;

    // no need of this
    @Column(name = "SKU_PRICE_DATA_ID")
    private Integer skuPriceDataId;

    @Column(name = "TAX_CODE")
    private String taxCode;

    @Column(name = "TAX_PERCENTAGE")
    private BigDecimal taxPercentage;

    @Column(name = "IS_NEW", nullable = false)
    private String isNew;

    @Column(name = "IS_NEW_ITEM")
    private String isNewItem;

    @Enumerated(EnumType.STRING)
    @Column(name = "SKU_PRICE_STATE")
    private SkuPriceState skuPriceState;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "contractItem")
    private Set<VendorContractItemUnitData> vendorContractItemUnitDataList;


    public enum SkuPriceState {
        NEW_ITEM, PRICE_UPDATE, REPEATED_ITEM
    }

}
