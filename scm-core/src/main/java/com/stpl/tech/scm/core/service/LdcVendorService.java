package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.LdcVendorDomain;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface LdcVendorService {

    LdcVendorDomain addLdcForVendor(LdcVendorDomain ldcVendorDomain, Integer user) throws SumoException;

    LdcVendorDomain deactiveLdcForVedndor(Long ldcId, Integer userId) throws SumoException;

    Map<Long,LdcVendorDomain> getLdcForVendorId(Integer vendorId) throws SumoException;

    LdcVendorDomain updateLdcVendorData(LdcVendorDomain ldcVendorDomain) throws SumoException;

    default List<LdcVendorDomain> getValidLdcData(Double checkAmount, Date checkDate,Integer vendorId) throws SumoException {
        System.out.println("No default implementation");
        return null;
    }

    Boolean updateLdcRemainingLimit(Long ldcId, Double deductedAmount) throws Exception;

    ;
}
