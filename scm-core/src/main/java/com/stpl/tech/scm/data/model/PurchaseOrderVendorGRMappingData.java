package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 07-06-2017.
 */
@Entity
@Table(name = "PURCHASE_ORDER_TO_GOODS_RECEIVED_MAPPING")
public class PurchaseOrderVendorGRMappingData {

    private Integer id;
    private PurchaseOrderData purchaseOrderData;
    private VendorGoodsReceivedData vendorGoodsReceivedData;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MAPPING_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne(targetEntity = PurchaseOrderData.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "PURCHASE_ORDER_ID", nullable = false)
    public PurchaseOrderData getPurchaseOrderData() {
        return purchaseOrderData;
    }

    public void setPurchaseOrderData(PurchaseOrderData purchaseOrderData) {
        this.purchaseOrderData = purchaseOrderData;
    }

    @ManyToOne(targetEntity = VendorGoodsReceivedData.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "VENDOR_GOODS_RECEIVED_ID", nullable = false)
    public VendorGoodsReceivedData getVendorGoodsReceivedData() {
        return vendorGoodsReceivedData;
    }

    public void setVendorGoodsReceivedData(VendorGoodsReceivedData vendorGoodsReceivedData) {
        this.vendorGoodsReceivedData = vendorGoodsReceivedData;
    }
}
