package com.stpl.tech.scm.data.model;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "REFERENCE_ORDER_ORDERING_DAYS")
@Data
public class ReferenceOrderOrderingDays {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "RO_ORDERING_DAYS_ID", nullable = false, unique = true)
    private Integer roOrderingDaysId;

    @Column(name = "REFERENCE_ORDER_ID",nullable = false)
    private Integer referenceOrderId;

    @Column(name = "DAY_TYPE",nullable = false)
    private String dayType;

    @Column(name = "DATE_OF_ORDERING")
    private Date dateOfOrdering;

    @Column(name = "CATEGORY_ID",nullable = false)
    private BigDecimal orderingPercentage;
}
