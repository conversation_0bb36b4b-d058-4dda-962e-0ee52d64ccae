package com.stpl.tech.scm.core.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.VehicleMasterService;
import com.stpl.tech.scm.data.dao.VehicleMasterDao;
import com.stpl.tech.scm.data.model.VehicleData;
import com.stpl.tech.scm.domain.model.Vehicle;

@Service
public class VehicleMasterServiceImpl implements VehicleMasterService {

	private static final Logger LOG = LoggerFactory.getLogger(TransportManagementServiceImpl.class);
	
	@Autowired
	VehicleMasterDao vehicleMasterDao;

	@Override
	public List<Vehicle> getVehicleList() {
		List<Vehicle> getVehicleList = vehicleMasterDao.getVehicleData();
		return getVehicleList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean saveVehicleDetails(Vehicle vehicle) throws SumoException {
		VehicleData vehicleData = new VehicleData();
		vehicleData = setVehicleData(vehicle);
		vehicleMasterDao.addVehicleData(vehicleData);
		return true;
	}

	private VehicleData setVehicleData(Vehicle vehicle) {
		VehicleData vehicleData = new VehicleData();
		vehicleData.setRegistrationNumber(vehicle.getRegistrationNumber());
		vehicleData.setName(vehicle.getName());
		vehicleData.setModel(vehicle.getModel());
		vehicleData.setMake(vehicle.getMake());
		vehicleData.setMultiDispatch(vehicle.isMultiDispatch() == true ? "Y" : "N");
		vehicleData.setStatus(vehicle.getStatus());
		vehicleData.setTransportMode(vehicle.getTransportMode());
		return vehicleData;
	}

	@Override
	public Vehicle getSingleVehicleData(Integer vehicleId) {
		VehicleData vehicleData = vehicleMasterDao.getSingleVehicleData(vehicleId);
		Vehicle vehicle = setSingleVehicleData(vehicleData);
		return vehicle;
	}
	
	private Vehicle setSingleVehicleData(VehicleData vehicle) {
		Vehicle vehicleData = new Vehicle();
		vehicleData.setRegistrationNumber(vehicle.getRegistrationNumber());
		vehicleData.setName(vehicle.getName());
		vehicleData.setModel(vehicle.getModel());
		vehicleData.setMake(vehicle.getMake());
		vehicleData.setMultiDispatch(vehicle.getMultiDispatch().equalsIgnoreCase("Y") ? true : false);
		vehicleData.setStatus(vehicle.getStatus());
		vehicleData.setTransportMode(vehicle.getTransportMode());
		return vehicleData;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean saveUpdatedVehicleDetails(Vehicle vehicle) throws SumoException {
		VehicleData vehicleData = new VehicleData();
		vehicleData = setVehicleUpdatedData(vehicle);
		vehicleMasterDao.updateVehicleData(vehicleData);
		return true;
	}

	private VehicleData setVehicleUpdatedData(Vehicle vehicle) {
		VehicleData vehicleData = new VehicleData();
		vehicleData.setVehicleId(vehicle.getVehicleId());
		vehicleData.setRegistrationNumber(vehicle.getRegistrationNumber());
		vehicleData.setName(vehicle.getName());
		vehicleData.setModel(vehicle.getModel());
		vehicleData.setMake(vehicle.getMake());
		vehicleData.setMultiDispatch(vehicle.isMultiDispatch() == true ? "Y" : "N");
		vehicleData.setStatus(vehicle.getStatus());
		vehicleData.setTransportMode(vehicle.getTransportMode());
		return vehicleData;
	}
}
