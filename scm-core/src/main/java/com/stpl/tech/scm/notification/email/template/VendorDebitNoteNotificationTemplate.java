package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class VendorDebitNoteNotificationTemplate extends AbstractVelocityTemplate {


    private SalesPerformaDetailData salesPerformaDetailData;
    private VendorDetail vendor;
    private Integer vendorId;
    private String basePath;

    public VendorDebitNoteNotificationTemplate() {

    }

    public VendorDebitNoteNotificationTemplate(SalesPerformaDetailData salesPerformaInvoice, String basePath , Integer vendorId , VendorDetail vendor) {
        this.salesPerformaDetailData = salesPerformaInvoice;
        this.vendorId = vendorId;
        this.vendor = vendor;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorDebitNoteTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor/debitNote/" + vendorId
                + "_" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        BigDecimal totalAmount = SCMUtil.add(salesPerformaDetailData.getTotalAmount(),salesPerformaDetailData.getAdditionalCharges()) ;
        stringObjectMap.put("invoice", salesPerformaDetailData);
        stringObjectMap.put("vendor", vendor);
        stringObjectMap.put("totalAmount", totalAmount);
        return stringObjectMap;
    }
}
