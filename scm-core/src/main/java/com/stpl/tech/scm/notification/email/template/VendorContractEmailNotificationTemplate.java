package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.VendorContractData;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
public class VendorContractEmailNotificationTemplate extends AbstractVelocityTemplate {
    private String vendorName;
    private String basePath;
    private String vendorUrl;
    private String templateName;
    private String token;
    private String actionApiUrl;

    @Override
    public String getTemplatePath() {
        return "templates/"+templateName;
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor-contract/" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendorName", vendorName);
        stringObjectMap.put("vendorUrl", vendorUrl);
        stringObjectMap.put("token", token);
        stringObjectMap.put("actionApiUrl", actionApiUrl);
        return stringObjectMap;
    }

}
