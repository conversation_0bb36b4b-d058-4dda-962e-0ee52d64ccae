package com.stpl.tech.scm.data.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.model.BookingDetail;
import com.stpl.tech.scm.core.util.model.ReverseBookingDetail;
import com.stpl.tech.scm.data.model.ProductionBookingData;
import com.stpl.tech.scm.data.model.ProductionBookingMappingData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingMappingData;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.ReverseProductionBooking;

public interface ProductionBookingDao extends SCMAbstractDao {

//	public ProductionBooking calculateConsumption(int unitId, int productId, BigDecimal quantity) throws SumoException;

	public ProductionBooking calculateConsumption(int unitId, int productId, BigDecimal quantity) throws SumoException, DataNotFoundException;

	public BookingDetail addBooking(ProductionBooking booking) throws SumoException;

	ReverseBookingDetail addReverseBooking(ReverseProductionBooking booking) throws SumoException;

	public List<ProductionBooking> getBookings(Integer unitId, Date startDate, Date endDate, boolean isReverse);

	public ProductionBookingData cancelBooking(Integer bookingId, Integer empId);

	public void updateBookingPrice(ProductionBooking booking);

	public ProductionBooking getLastBooking(Integer unitId, boolean isReverse);

	void updateReverseBookingPrice(ReverseProductionBooking booking, ProductionBooking productionBooking);

	public ProductionBookingMappingData productionUnitMappingItems(int productId, int unitId, int linkedSkuId, String profile) throws SumoException;

    public List<ProductionBookingMappingData> findMapping(Integer productId, Integer unitId);

	List<ReverseProductionBookingMappingData> findReverseMapping(Integer productId, Integer unitId);

	public boolean inactiveProductFromProductionBooking(Integer productId, String profile);

	public boolean inactiveMapping(int productId, int unitId);

	public  Boolean deleteCostDetailEntryForFixedAsset(Integer bookingId , Integer unitId);

    ReverseProductionBookingData cancelReverseBooking(Integer bookingId, Integer empId);
}
