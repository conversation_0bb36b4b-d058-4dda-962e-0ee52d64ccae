/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.reports;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.scm.data.model.SCMProductInventoryData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.core.service.impl.BudgetHelper;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.scm.core.exception.StockTakeException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.reports.modal.CafeUnsettledTOModal;
import com.stpl.tech.scm.reports.modal.CafeWastageModal;
import com.stpl.tech.scm.reports.modal.VarianceModal;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;
import com.stpl.tech.util.endpoint.Endpoints;

public class CafeVarianceReport extends ReportData implements VarianceReport {
	private final static Logger LOG = LoggerFactory.getLogger(CafeVarianceReport.class);
	private WorkbookContext workbookCtx;
	private StockTakeType varianceType;
	private int unitId;
	private String unitName;
	private String filePath;
	private Date businessDate;
	private EnvType envType;
	private String toEmail;
	private boolean generated = false;
	private String fileName;
	private String mimeType = AppConstants.EXCEL_MIME_TYPE;

	private StockManagementDao stockManagementDao;
	private EnvProperties props;
	private Map<Integer, SCMProductInventoryData> dailyInventoryData;

	public CafeVarianceReport(WorkbookContextFactory ctxFactory, StockManagementDao stockManagementDao, String basePath,
							  EnvType envType, StockTakeType stockType, Integer unitId, String unitName, String email, Date businessDate,
							  EnvProperties props, Map<Integer, SCMProductInventoryData> dailyInventoryData) {
		this.varianceType = stockType;
		this.unitName = unitName;
		this.unitId = unitId;
		this.filePath = basePath + "variance_reports" + File.separator;
		this.fileName = this.varianceType.toString() + "_Variance_Report_ " + this.unitName
				+ SCMUtil.getCurrentTimeISTStringWithNoColons();
		this.workbookCtx = ctxFactory.createWorkbook();
		this.stockManagementDao = stockManagementDao;
		this.toEmail = email;
		this.envType = envType;
		this.businessDate = businessDate;
		this.props = props;
		this.dailyInventoryData = dailyInventoryData;
	}

	@Override
	public void renderVariance() throws StockTakeException {

		List<VarianceModal> data = stockManagementDao.getCafeVarianceStock(unitId, businessDate, varianceType);

		if (data == null || data.isEmpty()) {
			throw new StockTakeException("Stock Take Returned No Data for Unit: " + unitName + " Business Date: "
					+ businessDate + " Stock Take Type: " + varianceType);
		}

		// sort in descending order based on variance cost
		data.sort((a, b) -> b.getVarianceCost().compareTo(a.getVarianceCost()));

		SheetContext sheetCtx = workbookCtx.createSheet("Variance Report");

		sheetCtx.nextRow().mergeCells(10).text(this.unitName); // heading
		sheetCtx.nextRow().text("Product ID").text("Product Name").setColumnWidth(SCMUtil.NAME_COLUMN_WIDTH)
				.text("Unit Of Measure").text("Category").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Sub Category").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Variance Type")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Opening Stock").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Transferred").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Received")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Wastage").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Consumption").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Closing Stock")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Variance").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Unit Cost").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Value")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Expected Expiry Wastage")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Expected Expiry Wastage After Consumption")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Actual Expiry Wastage")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Deviation Of Expiry Wastage")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH);

		for (VarianceModal stockForUnit : data) {
			sheetCtx.nextRow().number(stockForUnit.getId()).text(stockForUnit.getName()).text(stockForUnit.getUom())
					.text(stockForUnit.getCategory()).text(stockForUnit.getSubCategory())
					.text(stockForUnit.getTypeOfVariance())
					.number(stockForUnit.getOpeningStock() != null ? stockForUnit.getOpeningStock() : BigDecimal.ZERO)
					.number(stockForUnit.getTransferred() != null ? stockForUnit.getTransferred() : BigDecimal.ZERO)
					.number(stockForUnit.getReceived() != null ? stockForUnit.getReceived() : BigDecimal.ZERO)
					.number(stockForUnit.getWasted() != null ? stockForUnit.getWasted() : BigDecimal.ZERO)
					.number(stockForUnit.getConsumption() != null ? stockForUnit.getConsumption() : BigDecimal.ZERO)
					.number(stockForUnit.getClosingStock() != null ? stockForUnit.getClosingStock() : BigDecimal.ZERO)
					.number(stockForUnit.getVariance() != null ? stockForUnit.getVariance() : BigDecimal.ZERO)
					.number(stockForUnit.getUnitCost() != null ? stockForUnit.getUnitCost() : BigDecimal.ZERO)
					.number(stockForUnit.getVarianceCost() != null ? stockForUnit.getVarianceCost() : BigDecimal.ZERO)

					.number(this.dailyInventoryData != null && this.dailyInventoryData.get(stockForUnit.getId()) != null &&
							this.dailyInventoryData.get(stockForUnit.getId()).getExpectedExpiryWastage() != null ? this.dailyInventoryData.get(stockForUnit.getId()).getExpectedExpiryWastage() : BigDecimal.ZERO)
					.number(this.dailyInventoryData != null && this.dailyInventoryData.get(stockForUnit.getId()) != null &&
							this.dailyInventoryData.get(stockForUnit.getId()).getExpectedExpiryWastageAfterConsumption() != null ? this.dailyInventoryData.get(stockForUnit.getId()).getExpectedExpiryWastageAfterConsumption() : BigDecimal.ZERO)
					.number(this.dailyInventoryData != null && this.dailyInventoryData.get(stockForUnit.getId()) != null &&
							this.dailyInventoryData.get(stockForUnit.getId()).getActualExpiryWastage() != null ? this.dailyInventoryData.get(stockForUnit.getId()).getActualExpiryWastage() : BigDecimal.ZERO)
					.number(this.dailyInventoryData != null && this.dailyInventoryData.get(stockForUnit.getId()) != null &&
							this.dailyInventoryData.get(stockForUnit.getId()).getDeviationOfExpiryWastage() != null ? this.dailyInventoryData.get(stockForUnit.getId()).getDeviationOfExpiryWastage() : BigDecimal.ZERO);
		}

	}

	@Override
	public void renderNegativeWriteOff() {
		return;
	}

	@Override
	public void renderWastage() {

		List<CafeWastageModal> data = stockManagementDao.getCafeWastage(unitId, varianceType, businessDate);

		// sort in descending order based on variance cost
		data.sort((a, b) -> b.getValue().compareTo(a.getValue()));

		SheetContext sheetCtx = workbookCtx.createSheet("Wastage Report");

		Style headerStyle = SCMUtil.getHeaderStyle(workbookCtx);

		sheetCtx.nextRow().setTextStyle(headerStyle).text("Wastage ID").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Product Name")
				.setColumnWidth(SCMUtil.NAME_COLUMN_WIDTH).text("Unit Of Measure")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Category").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Sub Category").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Generated By")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Comment").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Quantity").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Unit Cost")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Value").setColumnWidth(SCMUtil.COLUMN_WIDTH);

		for (CafeWastageModal wastage : data) {
			sheetCtx.nextRow().number(wastage.getWastageId())
					.text(wastage.getProductName() != null ? wastage.getProductName() : "")
					.text(wastage.getUom() != null ? wastage.getUom() : "")
					.text(wastage.getCategory() != null ? wastage.getCategory() : "")
					.text(wastage.getSubCategory() != null ? wastage.getSubCategory() : "")
					.text(wastage.getGeneratedBy() != null ? wastage.getGeneratedBy() : "")
					.text(wastage.getComment() != null ? wastage.getComment() : "")
					.number(wastage.getQuantity() != null ? wastage.getQuantity() : BigDecimal.ZERO)
					.number(wastage.getUnitCost() != null ? wastage.getUnitCost() : BigDecimal.ZERO)
					.number(wastage.getValue() != null ? wastage.getValue() : BigDecimal.ZERO);
		}
	}

	@Override
	public void renderUnsettledTO() {
		List<CafeUnsettledTOModal> data = stockManagementDao.getCafeUnsettledTO(unitId, varianceType, businessDate);
		SheetContext sheetCtx = workbookCtx.createSheet("Unsettled TO Report");
		Style headerStyle = SCMUtil.getHeaderStyle(workbookCtx);
		if(data == null || data.isEmpty()) {
			LOG.info("No Unsettled TO for unit {}, businessDate {}", unitId, businessDate);
		}
		sheetCtx.nextRow().setTextStyle(headerStyle).text("Generation Date").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("TO Number")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("TO Item Number").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("SKU Name").setColumnWidth(SCMUtil.NAME_COLUMN_WIDTH).text("Unit Of Measure")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Category").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Sub Category").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Receiving Unit")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Transferred").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Received").setColumnWidth(SCMUtil.COLUMN_WIDTH);

		for (CafeUnsettledTOModal item : data) {
			sheetCtx.nextRow().date(item.getTransferDate()).number(item.getTransferOrderNo())
					.number(item.getTransferOrderItemNo()).text(item.getSkuName() != null ? item.getSkuName() : "")
					.text(item.getUom() != null ? item.getUom() : "")
					.text(item.getCategory() != null ? item.getCategory() : "")
					.text(item.getSubCategory() != null ? item.getSubCategory() : "")
					.text(item.getReceivingUnitName() != null ? item.getReceivingUnitName() : "")
					.number(item.getTransferred() != null ? item.getTransferred() : BigDecimal.ZERO)
					.number(item.getReceived() != null ? item.getReceived() : BigDecimal.ZERO);
		}
	}

	@Override
	public boolean renderSummaryView() {
		SCMDayCloseEventData event = stockManagementDao.getLastDayCloseEventOfTypeOnBusinessDate(this.unitId, this.varianceType,
				StockEventType.STOCK_TAKE, businessDate);
		LOG.info("render Summary view for unit {}, business Date {}", unitId, businessDate);
		if (event == null) {
			LOG.info("SCMDayCloseEventData not found for unit {}, business Date {}", unitId, businessDate);
			return false;
		}
		/*
		 * List<VarianceSummaryModal> list =
		 * stockManagementDao.getDailySummary(event.getEventId()); SheetContext sheetCtx
		 * = workbookCtx.createSheet("Summary"); VarianceSummaryUtil summaryUtil = new
		 * VarianceSummaryUtil(); summaryUtil.writeSummaryView(this.unitName, sheetCtx,
		 * list);
		 */

		// TODO @Vivek Render PNL View Here
		List<UnitExpenditureDetail> expenditureDetails = getAllMTDPnlForUnitForMonth(unitId, event.getBusinessDate());
		if(expenditureDetails == null || expenditureDetails.isEmpty()) {
			LOG.info("No Data for Expenditure for unit {}, business Date {}", unitId, businessDate);
		}
		BudgetHelper.createPnlDetailSheet(expenditureDetails, workbookCtx.toNativeWorkbook());

		return true;
	}

	/*
	 * private List<UnitExpenditureDetail> getAllMTDPnlForUnitForMonth(int unitId,
	 * Date businessDate) { List<UnitExpenditureDetail> resultSet = new
	 * ArrayList<>(); String endPoint = getAllMTDPnlForUnitForMonthURL(); String
	 * token = props.getAuthToken(); HashMap<String, String> dataMap = new
	 * HashMap<>(); dataMap.put("unitId", String.valueOf(unitId));
	 * dataMap.put("tillDate", SCMUtil.getSQLFormattedDate(businessDate));
	 *
	 * List<?> list = WebServiceHelper.postWithAuth(endPoint, token, dataMap,
	 * List.class); for (Object o : list) {
	 * resultSet.add(JSONSerializer.toJSON(JSONSerializer.toJSON(o),
	 * UnitExpenditureDetail.class)); } return resultSet; }
	 */

	@SuppressWarnings("unchecked")
	public List<UnitExpenditureDetail> getAllMTDPnlForUnitForMonth(int unitId, Date businessDate) {
		List<UnitExpenditureDetail> resultSet = new ArrayList<UnitExpenditureDetail>();
		try {
			String url = getAllMTDPnlForUnitForMonthURL();
			Map<String, String> uriVariables = new HashMap<>();
			HashMap<String, String> dataMap = new HashMap<>();
			dataMap.put("unitId", String.valueOf(unitId));
			dataMap.put("tillDate", SCMUtil.getSQLFormattedDate(businessDate));
			List<?> list = WebServiceHelper.exchangeWithAuth(url, props.getAuthToken(), HttpMethod.POST, List.class,
					JSONSerializer.toJSON(dataMap), uriVariables);
			GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
			Gson gson = gSonBuilder.create();
			list.forEach(p -> {
				resultSet.add(gson.fromJson(gson.toJson(p), UnitExpenditureDetail.class));
			});

		} catch (URISyntaxException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return new ArrayList<UnitExpenditureDetail>(resultSet);
	}

	private String getAllMTDPnlForUnitForMonthURL() {
		return props.getKettleServiceBasePath() + Endpoints.GET_MTD_PNL_DETAILS;
	}

	@Override
	public void generateReport(String filePath, byte[] content) throws IOException {
		String writtenPath = SCMUtil.write(content, filePath, this.unitName, this.varianceType.toString(),
				this.fileName, LOG);
		if (writtenPath != null) {
			this.filePath = writtenPath;
			this.generated = true;
		}
	}

	@Override
	public String getFilePath() {
		return this.filePath;
	}

	@Override
	public Date getBusinessDate() {
		return this.businessDate;
	}

	@Override
	public EnvType getEnv() {
		return this.envType;
	}

	@Override
	public String getEmailId() {
		return this.toEmail;
	}

	@Override
	public String getUnitName() {
		return this.unitName;
	}

	@Override
	public boolean isGenerated() {
		return this.generated;
	}

	@Override
	public String getFileName() {
		return this.fileName;
	}

	@Override
	public String getMimeType() {
		return this.mimeType;
	}

	@Override
	public WorkbookContext getWorkbook() {
		return workbookCtx;
	}

}
