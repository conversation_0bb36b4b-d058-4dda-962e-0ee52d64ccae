package com.stpl.tech.scm.data.model;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.ManyToOne;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;

import java.math.BigDecimal;

@Entity
@Table(name = "SALES_PERFORMA_INVOICE_ITEM_CORRECTED")
public class SalesPerformaInvoiceItemCorrected {

    private Integer itemId;
    private Integer skuId;
    private String skuName;
    private BigDecimal price;
    private BigDecimal revisedPrice;
    private BigDecimal correctedPrice;
    private BigDecimal pkgQty;
    private BigDecimal revisedPkgQty;
    private BigDecimal correctedPkgQty;
    private BigDecimal amount;
    private BigDecimal revisedAmount;
    private BigDecimal tax;
    private BigDecimal revisedTax;

    private SalesPerformaInvoiceCorrected salesPerformaInvoiceCorrected;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ITEM_ID", unique = true, nullable = false)
    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    @Column(name = "SKU_ID",nullable = true)
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "SKU_NAME",nullable = true)
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "PRICE",nullable = true)
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "REVISED_PRICE",nullable = true)
    public BigDecimal getRevisedPrice() {return revisedPrice;}

    public void setRevisedPrice(BigDecimal revisedPrice) {this.revisedPrice = revisedPrice;}

    @Column(name = "CORRECTED_PRICE",nullable = true)
    public BigDecimal getCorrectedPrice() {return correctedPrice;}

    public void setCorrectedPrice(BigDecimal correctedPrice) {this.correctedPrice = correctedPrice;}

    @Column(name = "PKG_QTY",nullable = true)
    public BigDecimal getPkgQty() {
        return pkgQty;
    }

    public void setPkgQty(BigDecimal pkgQty) {
        this.pkgQty = pkgQty;
    }

    @Column(name = "REVISED_PKG_QTY",nullable = true)
    public BigDecimal getRevisedPkgQty() {return revisedPkgQty;}

    public void setRevisedPkgQty(BigDecimal revisedPkgQty) {this.revisedPkgQty = revisedPkgQty;}

    @Column(name = "CORRECTED_PKG_QTY",nullable = true)
    public BigDecimal getCorrectedPkgQty() {return correctedPkgQty;}

    public void setCorrectedPkgQty(BigDecimal correctedPkgQty) {this.correctedPkgQty = correctedPkgQty;}

    @Column(name = "AMOUNT",nullable = true)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "REVISED_AMOUNT",nullable = true)
    public BigDecimal getRevisedAmount() {
        return revisedAmount;
    }
    public void setRevisedAmount(BigDecimal revisedAmount) {this.revisedAmount = revisedAmount;}

    @Column(name = "TAX",nullable = true)
    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    @Column(name = "REVISED_TAX",nullable = true)
    public BigDecimal getRevisedTax() {
        return revisedTax;
    }
    public void setRevisedTax(BigDecimal revisedTax) {
        this.revisedTax = revisedTax;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="ID", nullable = true)
    public SalesPerformaInvoiceCorrected getSalesPerformaInvoiceCorrected() {
        return salesPerformaInvoiceCorrected;
    }

    public void setSalesPerformaInvoiceCorrected(SalesPerformaInvoiceCorrected salesPerformaInvoiceCorrected) {
        this.salesPerformaInvoiceCorrected = salesPerformaInvoiceCorrected;
    }


}
