/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * OrderItem generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ESTIMATE_REQUEST_DATA")
public class EstimateRequestData implements java.io.Serializable {

    private Integer id;
    private Integer requestId;
    private Date businessDate;
    private Integer dayOfWeek;
    private Integer unitId;
    private Integer brandId;
    private Integer productId;
    private String dimension;
    private BigDecimal avgUpt;
    private BigDecimal netSales;
    private BigDecimal totalSale;
    private BigDecimal avgPrice;
    private BigDecimal suggestedQuantity;
    private BigDecimal categoryBufferSuggestedQuantity;
    private Integer roundSuggestedtQuantity;
    private BigDecimal suggestedSales;
    private String dataType;
    private String categoryBufferApplied;
    private Integer incrementedQunatity;


    public EstimateRequestData() {
    }

    public EstimateRequestData(Integer id, Integer requestId, Date businessDate, Integer dayOfWeek, Integer unitId, Integer brandId, Integer productId, String dimension, BigDecimal avgUpt, BigDecimal netSales, BigDecimal totalSale, BigDecimal avgPrice, BigDecimal suggestedQuantity, BigDecimal categoryBufferSuggestedQuantity, Integer roundSuggestedtQuantity, BigDecimal suggestedSales, String dataType) {
        this.id = id;
        this.requestId = requestId;
        this.businessDate = businessDate;
        this.dayOfWeek = dayOfWeek;
        this.unitId = unitId;
        this.brandId = brandId;
        this.productId = productId;
        this.dimension = dimension;
        this.avgUpt = avgUpt;
        this.netSales = netSales;
        this.totalSale = totalSale;
        this.avgPrice = avgPrice;
        this.suggestedQuantity = suggestedQuantity;
        this.categoryBufferSuggestedQuantity = categoryBufferSuggestedQuantity;
        this.roundSuggestedtQuantity = roundSuggestedtQuantity;
        this.suggestedSales = suggestedSales;
        this.dataType = dataType;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "REQUEST_ID")
    public Integer getRequestId() {
        return requestId;
    }

    public void setRequestId(Integer requestId) {
        this.requestId = requestId;
    }

    @Column(name = "BUSINESS_DATE")
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Column(name = "DAY_OF_WEEK")
    public Integer getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(Integer dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }


    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }


    @Column(name = "DIMENSION")
    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }


    @Column(name = "AVG_UPT")
    public BigDecimal getAvgUpt() {
        return avgUpt;
    }

    public void setAvgUpt(BigDecimal avgUpt) {
        this.avgUpt = avgUpt;
    }

    @Column(name = "NET_SALES")
    public BigDecimal getNetSales() {
        return netSales;
    }

    public void setNetSales(BigDecimal netSales) {
        this.netSales = netSales;
    }

    @Column(name = "TOTAL_SALE")
    public BigDecimal getTotalSale() {
        return totalSale;
    }

    public void setTotalSale(BigDecimal totalSale) {
        this.totalSale = totalSale;
    }

    @Column(name = "AVG_PRICE")
    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }

    @Column(name = "SUGGESTED_QUANTITY")
    public BigDecimal getSuggestedQuantity() {
        return suggestedQuantity;
    }

    public void setSuggestedQuantity(BigDecimal suggestedQuantity) {
        this.suggestedQuantity = suggestedQuantity;
    }

    @Column(name = "CATEGORY_BUFFER_SUGGESTED_QUANTITY")
    public BigDecimal getCategoryBufferSuggestedQuantity() {
        return categoryBufferSuggestedQuantity;
    }

    public void setCategoryBufferSuggestedQuantity(BigDecimal categoryBufferSuggestedQuantity) {
        this.categoryBufferSuggestedQuantity = categoryBufferSuggestedQuantity;
    }

    @Column(name = "ROUND_SUGST_QUANTITY")
    public Integer getRoundSuggestedtQuantity() {
        return roundSuggestedtQuantity;
    }

    public void setRoundSuggestedtQuantity(Integer roundSuggestedtQuantity) {
        this.roundSuggestedtQuantity = roundSuggestedtQuantity;
    }

    @Column(name = "SUGGESTED_SALES")
    public BigDecimal getSuggestedSales() {
        return suggestedSales;
    }

    public void setSuggestedSales(BigDecimal suggestedSales) {
        this.suggestedSales = suggestedSales;
    }

    @Column(name = "DATA_TYPE")
    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    @Column(name = "CATEGORY_BUFFER_APPLIED")
    public String getCategoryBufferApplied() {
        return categoryBufferApplied;
    }

    public void setCategoryBufferApplied(String categoryBufferApplied) {
        this.categoryBufferApplied = categoryBufferApplied;
    }

    @Column(name = "QUANTITY_INCREMENT")
    public Integer getIncrementedQunatity() {
        return incrementedQunatity;
    }

    public void setIncrementedQunatity(Integer incrementedQunatity) {
        this.incrementedQunatity = incrementedQunatity;
    }
}
