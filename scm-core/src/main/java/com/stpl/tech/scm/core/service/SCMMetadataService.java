/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.CapexTemplateData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.DeactivateValidateResponse;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ListDetail;
import com.stpl.tech.scm.domain.model.MapResponse;
import com.stpl.tech.scm.domain.model.MilkBreadBypass;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PaymentDeviation;
import com.stpl.tech.scm.domain.model.PendingMilkBread;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProfileAttributeMapping;
import com.stpl.tech.scm.domain.model.ProfileDefinition;
import com.stpl.tech.scm.domain.model.SCMUnitCategory;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.UomConversionMappingDto;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.EmailGenerationException;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Rahul Singh on 04-05-2016.
 */
public interface SCMMetadataService {

    public List<AttributeDefinition> getAllAttributeDefinitions();

    public List<CategoryDefinition> getAllCategoryDefinitions();

    public List<SubCategoryDefinition> getAllSubCategoryDefinitions();

    public List<PackagingDefinition> getAllPackagingDefinitions();

    List<ProductDefinitionData> getAllProducts();

    List<SkuDefinitionData> getAllSkus();

    List<VendorDetailData> getAllVendors();


    public List<ProfileDefinition> getAllProfileDefinitions();

    public Map<Integer, SkuDefinition> getAllSkuDefinitions(List<SkuDefinitionData> skusList);

    public List<UnitDetail> getAllUnitDetails();

    public List<SCMUnitCategory> getAllUnitCategories();

    public List<CategoryAttributeMapping> getAllCategoryAttributeMappings();

    public List<AttributeValue> getAllAttributeValues();

    public List<CategoryAttributeValue> getAllCategoryAttributeValues();

    public List<ProductPackagingMapping> getAllProductPackginMappings();

    public List<SkuPackagingMapping> getAllSkuPackagingMappings();

    public List<SkuAttributeValue> getAllSkuAttributeValues();

    public List<VendorDetail> getAllVendorDetails();

    Map<Integer, VendorDetail> converterForVendorCache(List<VendorDetailData> list) ;

    public Collection<Location> getAllLocations();

	public Collection<State> getAllStates();

    public List<IdCodeName> getAllInventoryLists();

    public List<PaymentDeviation> getAllPaymentDeviations();

	public Map<String, Integer> getFulfillmentTypeMap();

	public Map<Integer, Set<Integer>> getUnitToAvailableProductMap();

	public Map<Integer, Set<Integer>> getUnitToAvailableSKUMap();

	public Map<String, BigDecimal> getUnitDistanceMapping();

    List<ProductDefinition> getAllProductDefinitions(List<ProductDefinitionData> productDefinitionDataList);

    public Map<Integer, List<ProfileAttributeMapping>> getProfileAttributeMappings();

	public List<AssetDefinition> getAssetDefinitions();

   public Map<String, BigDecimal> getZipCodeDistanceMapping();
    public Map<String, List<ListDetail>> getListDetails();

	public List<CapexTemplateData> getAllCapexTemplates();

	public Map<Integer, Map<Integer,ProductRecipeKey>> getProductProfileMapping();

    public Boolean saveAuditLog(Integer keyId , String keyType ,Integer changedBy , Object newObject , String changeType) throws SumoException;

    public Map<String, Pair<Object,Object>>  findDiff(Object oldObject , Object newObject) throws IllegalAccessException;

    public Boolean sendDiffEmail(Object oldObject , Object newObject , String updatedBy , String className , Integer id ,
                                 List<String> toEmails, String placeHolder , Boolean isNew) throws IllegalAccessException, EmailGenerationException;
    public MapResponse findDistance(String source , String destination) throws IOException;

    public Map<String ,Integer> getRegionFulfillmentMapping();

    public DeactivateValidateResponse validateForDeactivation(Integer id , String type) throws URISyntaxException;

    public Map<String,List<Integer>> getMaintenanceWHMappings();

    public void dumpAuditLogsToMysql();

    public Map<Integer, PendingMilkBread> getPendingMilkBread();

    public void markMilkBreadComplete(List<Integer> unitIds, Integer userId);

    public void markMilkBreadCompleteForUnit(MilkBreadBypass milkBreadBypass);

    public VendorDetail addVendorToCache(VendorDetailData vendorDetailData);

    public void saveAssetDefinitionToRedis(Integer assetId);

    List<VendorDetail> convertToVendorDetail(List<VendorDetailData> vendorDetailDataList);

}
