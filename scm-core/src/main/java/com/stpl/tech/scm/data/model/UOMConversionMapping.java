package com.stpl.tech.scm.data.model;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UOM_CONVERSION_MAPPING")
@Data
public class UOMConversionMapping {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PRODUCT_ID")
    private String productId;

    @Column(name = "FROM_UOM")
    private String fromUom;

    @Column(name = "TO_UOM")
    private String toUom;

    @Column(name = "CONVERSION_RATIO")
    private BigDecimal conversionRatio;

}
