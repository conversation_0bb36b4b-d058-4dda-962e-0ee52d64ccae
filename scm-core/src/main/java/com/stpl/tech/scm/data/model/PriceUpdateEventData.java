package com.stpl.tech.scm.data.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "PRICE_UPDATE_EVENT")
public class PriceUpdateEventData {

	private Integer id;
	private Integer createdBy;
	private String createdByName;
	private Date creationTime;
	private Integer finalizedBy;
	private Date finalizationTime;
	private String finalizedByName;
	private String eventType;
	private String eventActionType;
	private String eventStatus;
	private Integer noOfRecords;
	private Integer noOfErrors;
	private String dataFilePath;
	private List<PriceUpdateEntryData> entries = new ArrayList<PriceUpdateEntryData>();

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "EVENT_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "CREATED_BY_NAME", nullable = false)
	public String getCreatedByName() {
		return createdByName;
	}

	public void setCreatedByName(String createdByName) {
		this.createdByName = createdByName;
	}

	@Column(name = "CREATION_TIME", nullable = false)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "FINALIZED_BY", nullable = true)
	public Integer getFinalizedBy() {
		return finalizedBy;
	}

	public void setFinalizedBy(Integer finalizedBy) {
		this.finalizedBy = finalizedBy;
	}

	@Column(name = "FINALIZATION_TIME", nullable = true)
	public Date getFinalizationTime() {
		return finalizationTime;
	}

	public void setFinalizationTime(Date finalizationTime) {
		this.finalizationTime = finalizationTime;
	}

	@Column(name = "FINALIZED_BY_NAME", nullable = true)
	public String getFinalizedByName() {
		return finalizedByName;
	}

	public void setFinalizedByName(String finalizedByName) {
		this.finalizedByName = finalizedByName;
	}

	@Column(name = "EVENT_TYPE", nullable = false)
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	@Column(name = "EVENT_ACTION_TYPE", nullable = false)
	public String getEventActionType() {
		return eventActionType;
	}

	public void setEventActionType(String eventActionType) {
		this.eventActionType = eventActionType;
	}

	@Column(name = "EVENT_STATUS", nullable = false)
	public String getEventStatus() {
		return eventStatus;
	}

	public void setEventStatus(String eventStatus) {
		this.eventStatus = eventStatus;
	}

	@Column(name = "NO_OF_RECORDS", nullable = false)
	public Integer getNoOfRecords() {
		return noOfRecords;
	}

	public void setNoOfRecords(Integer noOfRecords) {
		this.noOfRecords = noOfRecords;
	}

	@Column(name = "NO_OF_ERRORS", nullable = false)
	public Integer getNoOfErrors() {
		return noOfErrors;
	}

	public void setNoOfErrors(Integer noOfErrors) {
		this.noOfErrors = noOfErrors;
	}

	@Column(name = "DATA_FILE_PATH", nullable = false)
	public String getDataFilePath() {
		return dataFilePath;
	}

	public void setDataFilePath(String dataFilePath) {
		this.dataFilePath = dataFilePath;
	}

	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	@JoinColumn(name = "EVENT_ID", nullable = true)
	public List<PriceUpdateEntryData> getEntries() {
		return entries;
	}

	public void setEntries(List<PriceUpdateEntryData> entries) {
		this.entries = entries;
	}

}
