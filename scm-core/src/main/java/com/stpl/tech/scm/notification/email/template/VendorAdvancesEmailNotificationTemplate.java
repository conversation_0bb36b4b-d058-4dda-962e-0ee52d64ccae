/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.MathTool;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VendorAdvancesEmailNotificationTemplate  extends AbstractVelocityTemplate {

    Map<String,List<VendorAdvancePayment>> vendorAdvancePayments;
    Map<String, BigDecimal> advanceData;
    private Map<Integer, VendorDetail> vendorDetailMap;
    private String basePath;

    public VendorAdvancesEmailNotificationTemplate() {
    }

    public VendorAdvancesEmailNotificationTemplate(Map<String, List<VendorAdvancePayment>> vendorAdvancePayments, Map<String, BigDecimal> advanceData,
                                                   Map<Integer, VendorDetail> vendorDetailMap, String basePath) {
        this.vendorAdvancePayments = vendorAdvancePayments;
        this.advanceData = advanceData;
        this.vendorDetailMap = vendorDetailMap;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorAdvanceTemplate.html";
    }


    @Override
    public String getFilepath() {
        return basePath + "/vendorAdvances/" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendorAdvancePayments", vendorAdvancePayments);
        stringObjectMap.put("advanceData", advanceData);
        stringObjectMap.put("vendorDetailMap", vendorDetailMap);
        stringObjectMap.put("mathTool", new MathTool());
        return stringObjectMap;
    }
}
