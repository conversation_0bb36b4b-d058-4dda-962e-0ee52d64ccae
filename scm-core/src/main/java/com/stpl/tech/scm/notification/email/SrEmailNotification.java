package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.notification.email.template.SrEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class SrEmailNotification extends EmailNotification {

    private SrEmailNotificationTemplate srEmailNotificationTemplate;
    private EnvType envType;
    private String[] emails;
    private String subjectOfEmail;

    public SrEmailNotification() {
    }

    public SrEmailNotification(SrEmailNotificationTemplate srEmailNotificationTemplate, EnvType envType, String[] emails) {
        this.srEmailNotificationTemplate = srEmailNotificationTemplate;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        if (SCMUtil.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            return new String[] { "<EMAIL>" };
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subjectOfEmail = srEmailNotificationTemplate.getSubjectOfEmail();
        if (SCMUtil.isDev(envType)) {
            subjectOfEmail = " [DEV] : " + subjectOfEmail;
        }
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return srEmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
