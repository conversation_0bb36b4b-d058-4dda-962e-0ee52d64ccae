package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.VendorContractEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
public class VendorContractEmailNotification extends EmailNotification {

    private VendorContractEmailNotificationTemplate vendorContractEmailNotificationTemplate;
    private EnvType envType;
    private String[] vendorMail;
    private String subject;

    @Override
    public String[] getToEmails() {
        if (SCMUtil.isDev(envType)) {
            return new String[] { SCMServiceConstants.TECHNOLOGY_EMAIL };
        } else {
            return this.vendorMail;
        }
    }

    @Override
    public String getFromEmail() {
        return SCMServiceConstants.REPORTING_EMAIL;
    }

    @Override
    public String subject() {
        return (SCMUtil.isDev(envType) ? " [DEV] " : "") + subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return vendorContractEmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
