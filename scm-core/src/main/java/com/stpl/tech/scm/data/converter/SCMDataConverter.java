/*
 * SUNSHINE TEAHOUSE public LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse public Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse public Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse public Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse public Limited.
 */

package com.stpl.tech.scm.data.converter;

import com.google.common.base.Joiner;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.data.model.UnitClosureState;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.tax.model.TaxationDetailDao;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.mapper.LdcVendorMapper;
import com.stpl.tech.scm.core.mapper.PrMetaDataMapper;
import com.stpl.tech.scm.core.util.EWayHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.AddressDetailData;
import com.stpl.tech.scm.data.model.ApprovalDetailData;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.AssetDefinitionDataLog;
import com.stpl.tech.scm.data.model.AssetDepreciationMappingData;
import com.stpl.tech.scm.data.model.AssetDepreciationSummaryData;
import com.stpl.tech.scm.data.model.AssetRecoveryData;
import com.stpl.tech.scm.data.model.AssetRecoveryDefinitionData;
import com.stpl.tech.scm.data.model.AssetRecoveryDetailData;
import com.stpl.tech.scm.data.model.AssetScrappedMappingData;
import com.stpl.tech.scm.data.model.AssetTransferMappingData;
import com.stpl.tech.scm.data.model.AttributeDefinitionData;
import com.stpl.tech.scm.data.model.AttributeValueData;
import com.stpl.tech.scm.data.model.BookingConsumptionData;
import com.stpl.tech.scm.data.model.BookingConsumptionItemDrilldown;
import com.stpl.tech.scm.data.model.BulkTransferEventData;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.CapexRequestDetailData;
import com.stpl.tech.scm.data.model.CategoryAttributeMappingData;
import com.stpl.tech.scm.data.model.CategoryAttributeValueData;
import com.stpl.tech.scm.data.model.CategoryDefinitionData;
import com.stpl.tech.scm.data.model.ConsignmentData;
import com.stpl.tech.scm.data.model.CostCenterData;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.DayCloseInventoryDrillDown;
import com.stpl.tech.scm.data.model.DebitNoteDetailData;
import com.stpl.tech.scm.data.model.DerivedMappingData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.EntityAssetMappingData;
import com.stpl.tech.scm.data.model.EntityAttributeValueMappingData;
import com.stpl.tech.scm.data.model.EwayBillData;
import com.stpl.tech.scm.data.model.ExternalTransferDetailData;
import com.stpl.tech.scm.data.model.GatepassData;
import com.stpl.tech.scm.data.model.GatepassItemAssetMappingData;
import com.stpl.tech.scm.data.model.GatepassItemData;
import com.stpl.tech.scm.data.model.GatepassItemDrilldownDetail;
import com.stpl.tech.scm.data.model.GatepassTaxDetail;
import com.stpl.tech.scm.data.model.GatepassVendorMappingData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemDrilldown;
import com.stpl.tech.scm.data.model.InvoiceDeviationMappingData;
import com.stpl.tech.scm.data.model.ItemTaxDetailData;
import com.stpl.tech.scm.data.model.ListDatas;
import com.stpl.tech.scm.data.model.ListDetailData;
import com.stpl.tech.scm.data.model.ListTypeData;
import com.stpl.tech.scm.data.model.MonkWastageDetailData;
import com.stpl.tech.scm.data.model.OutwardRegisterData;
import com.stpl.tech.scm.data.model.PRPaymentDetailData;
import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.PaymentCalendarData;
import com.stpl.tech.scm.data.model.PaymentDeviationData;
import com.stpl.tech.scm.data.model.PaymentInvoiceData;
import com.stpl.tech.scm.data.model.PaymentInvoiceItemData;
import com.stpl.tech.scm.data.model.PaymentInvoiceItemTaxData;
import com.stpl.tech.scm.data.model.PaymentRequestData;
import com.stpl.tech.scm.data.model.PaymentRequestItemMappingData;
import com.stpl.tech.scm.data.model.PaymentRequestLogData;
import com.stpl.tech.scm.data.model.PaymentRequestMetaData;
import com.stpl.tech.scm.data.model.PaymentRequestStatusLogData;
import com.stpl.tech.scm.data.model.PlanOrderItemData;
import com.stpl.tech.scm.data.model.PlanOrderItemPrepData;
import com.stpl.tech.scm.data.model.PlanOrderItemPrepItemData;
import com.stpl.tech.scm.data.model.PlanOrderMappingData;
import com.stpl.tech.scm.data.model.PriceUpdateEntryData;
import com.stpl.tech.scm.data.model.PriceUpdateEntryDrillDown;
import com.stpl.tech.scm.data.model.PriceUpdateEntryError;
import com.stpl.tech.scm.data.model.PriceUpdateEventData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.ProductDefinitionLogs;
import com.stpl.tech.scm.data.model.ProductFulfillmentTypeData;
import com.stpl.tech.scm.data.model.ProductPackagingMappingData;
import com.stpl.tech.scm.data.model.ProductionBookingData;
import com.stpl.tech.scm.data.model.ProductionPlanEventData;
import com.stpl.tech.scm.data.model.ProfileAttributeMappingData;
import com.stpl.tech.scm.data.model.ProfileDefinitionData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderExtendedStatusLogData;
import com.stpl.tech.scm.data.model.PurchaseOrderItemData;
import com.stpl.tech.scm.data.model.PurchaseOrderNotificationData;
import com.stpl.tech.scm.data.model.ROMenuItemVariantData;
import com.stpl.tech.scm.data.model.ReferenceOrderData;
import com.stpl.tech.scm.data.model.ReferenceOrderMenuItemData;
import com.stpl.tech.scm.data.model.ReferenceOrderScmItemData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.data.model.RequestOrderItemTaxDetail;
import com.stpl.tech.scm.data.model.ReverseBookingConsumptionData;
import com.stpl.tech.scm.data.model.ReverseBookingConsumptionItemDrilldown;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.RoScmItemExpiryData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SCMOrderPackagingData;
import com.stpl.tech.scm.data.model.SCMProductConsumptionData;
import com.stpl.tech.scm.data.model.SCMProductInventoryData;
import com.stpl.tech.scm.data.model.SCMWastageData;
import com.stpl.tech.scm.data.model.SCMWastageEventData;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCorrected;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteDetail;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteItemDetail;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceItemCorrected;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceItemData;
import com.stpl.tech.scm.data.model.SalesPerformaItemDrilldown;
import com.stpl.tech.scm.data.model.SalesPerformaItemTaxDetail;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderItemData;
import com.stpl.tech.scm.data.model.ServiceOrderServiceReceiveMappingData;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemDrilldownData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemTaxData;
import com.stpl.tech.scm.data.model.SkuAttributeValueData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingMappingData;
import com.stpl.tech.scm.data.model.SkuPriceData;
import com.stpl.tech.scm.data.model.StockEventAssetMappingDefinitionData;
import com.stpl.tech.scm.data.model.StockEventDefinitionData;
import com.stpl.tech.scm.data.model.SubCategoryDefinitionData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.data.model.TransferOrderItemDrilldown;
import com.stpl.tech.scm.data.model.TransferOrderItemTaxDetail;
import com.stpl.tech.scm.data.model.TransferOrderNoteData;
import com.stpl.tech.scm.data.model.TransferOrderTaxDetail;
import com.stpl.tech.scm.data.model.UnitCategoryData;
import com.stpl.tech.scm.data.model.UnitDetailData;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.data.model.UserProductCreationRequestData;
import com.stpl.tech.scm.data.model.UserSkuCreationRequestData;
import com.stpl.tech.scm.data.model.VarianceAcknowledgementData;
import com.stpl.tech.scm.data.model.VehicleData;
import com.stpl.tech.scm.data.model.VehicleDispatchData;
import com.stpl.tech.scm.data.model.VendorAccountDetailData;
import com.stpl.tech.scm.data.model.VendorCompanyDebitMapping;
import com.stpl.tech.scm.data.model.VendorCompanyDetailData;
import com.stpl.tech.scm.data.model.VendorContractItem;
import com.stpl.tech.scm.data.model.VendorContractItemData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorDispatchLocationDetailData;
import com.stpl.tech.scm.data.model.VendorEditedDetail;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedItemData;
import com.stpl.tech.scm.data.model.VendorRegistrationRequestDetail;
import com.stpl.tech.scm.data.model.WastageDataDrilldown;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.data.transport.model.TransportMode;
import com.stpl.tech.scm.domain.model.AddressDetail;
import com.stpl.tech.scm.domain.model.AddressType;
import com.stpl.tech.scm.domain.model.ApprovalDetail;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.AssetDefinitionSlimObject;
import com.stpl.tech.scm.domain.model.AssetRecovery;
import com.stpl.tech.scm.domain.model.AssetRecoveryDefinition;
import com.stpl.tech.scm.domain.model.AssetRecoveryDetail;
import com.stpl.tech.scm.domain.model.AssetRecoveryStatusType;
import com.stpl.tech.scm.domain.model.AssetStatusType;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeType;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.BankAccountType;
import com.stpl.tech.scm.domain.model.BookingConsumption;
import com.stpl.tech.scm.domain.model.BookingStatus;
import com.stpl.tech.scm.domain.model.BulkTransferEvent;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.BusinessType;
import com.stpl.tech.scm.domain.model.CapexRequestDetail;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.Consignment;
import com.stpl.tech.scm.domain.model.ConstitutionalEntityType;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceDetails;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceItemDetails;
import com.stpl.tech.scm.domain.model.CostCenter;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.CostElement;
import com.stpl.tech.scm.domain.model.CreditDebitNoteDetail;
import com.stpl.tech.scm.domain.model.CreditDebitNoteItemDetail;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.scm.domain.model.DebitNoteDetail;
import com.stpl.tech.scm.domain.model.DerivedMapping;
import com.stpl.tech.scm.domain.model.DispatchStatus;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.EWayBill;
import com.stpl.tech.scm.domain.model.EntityAssetMapping;
import com.stpl.tech.scm.domain.model.EntityAttributeValueMapping;
import com.stpl.tech.scm.domain.model.ExternalTransferDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.Gatepass;
import com.stpl.tech.scm.domain.model.GatepassItem;
import com.stpl.tech.scm.domain.model.GatepassItemAssetMapping;
import com.stpl.tech.scm.domain.model.GatepassItemDrilldown;
import com.stpl.tech.scm.domain.model.GatepassOperationType;
import com.stpl.tech.scm.domain.model.GatepassReturnStatus;
import com.stpl.tech.scm.domain.model.GatepassStatus;
import com.stpl.tech.scm.domain.model.GatepassTax;
import com.stpl.tech.scm.domain.model.GatepassTransType;
import com.stpl.tech.scm.domain.model.GatepassVendorMapping;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.GstApplicationStatus;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.InvoiceBillingType;
import com.stpl.tech.scm.domain.model.InvoiceDeviationMapping;
import com.stpl.tech.scm.domain.model.InvoiceDocType;
import com.stpl.tech.scm.domain.model.LifeTimeType;
import com.stpl.tech.scm.domain.model.ListData;
import com.stpl.tech.scm.domain.model.ListDetail;
import com.stpl.tech.scm.domain.model.ListType;
import com.stpl.tech.scm.domain.model.LocationType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.MonkWastageDetail;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.ObjectFactory;
import com.stpl.tech.scm.domain.model.OrderTransferType;
import com.stpl.tech.scm.domain.model.OrdersDetailsShort;
import com.stpl.tech.scm.domain.model.OtherTaxDetail;
import com.stpl.tech.scm.domain.model.OutwardRegister;
import com.stpl.tech.scm.domain.model.POCreationType;
import com.stpl.tech.scm.domain.model.PRPaymentDetail;
import com.stpl.tech.scm.domain.model.PackagingData;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.PaymentCalendar;
import com.stpl.tech.scm.domain.model.PaymentDeviation;
import com.stpl.tech.scm.domain.model.PaymentDeviationLevel;
import com.stpl.tech.scm.domain.model.PaymentDeviationType;
import com.stpl.tech.scm.domain.model.PaymentInvoice;
import com.stpl.tech.scm.domain.model.PaymentInvoiceItem;
import com.stpl.tech.scm.domain.model.PaymentInvoiceItemTax;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.PaymentRequestItemMapping;
import com.stpl.tech.scm.domain.model.PaymentRequestLog;
import com.stpl.tech.scm.domain.model.PaymentRequestMetaDataDomain;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.PaymentRequestStatusLog;
import com.stpl.tech.scm.domain.model.PaymentRequestType;
import com.stpl.tech.scm.domain.model.PaymentType;
import com.stpl.tech.scm.domain.model.PercentageDetail;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.PlanOrderItemPrep;
import com.stpl.tech.scm.domain.model.PlanOrderItemPrepItem;
import com.stpl.tech.scm.domain.model.PriceUpdateDrillDown;
import com.stpl.tech.scm.domain.model.PriceUpdateEntry;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.PriceUpdateEvent;
import com.stpl.tech.scm.domain.model.PriceUpdateEventActionType;
import com.stpl.tech.scm.domain.model.PriceUpdateEventStatus;
import com.stpl.tech.scm.domain.model.PriceUpdateEventType;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductFulfillmentType;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.ProductStockForUnit;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.ProductionItemType;
import com.stpl.tech.scm.domain.model.ProductionPlanEvent;
import com.stpl.tech.scm.domain.model.ProfileAttributeMapping;
import com.stpl.tech.scm.domain.model.ProfileDefinition;
import com.stpl.tech.scm.domain.model.PurchaseOrder;
import com.stpl.tech.scm.domain.model.PurchaseOrderExtendedStatusLog;
import com.stpl.tech.scm.domain.model.PurchaseOrderItem;
import com.stpl.tech.scm.domain.model.PurchaseOrderNotification;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.ReferenceOrder;
import com.stpl.tech.scm.domain.model.ReferenceOrderMenuItem;
import com.stpl.tech.scm.domain.model.ReferenceOrderMenuVariant;
import com.stpl.tech.scm.domain.model.ReferenceOrderScmItem;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.ReverseBookingConsumption;
import com.stpl.tech.scm.domain.model.ReverseProductionBooking;
import com.stpl.tech.scm.domain.model.SCMOrderPackaging;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SCMUnitCategory;
import com.stpl.tech.scm.domain.model.SREmailShort;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoiceItem;
import com.stpl.tech.scm.domain.model.SalesPerformaItemTax;
import com.stpl.tech.scm.domain.model.SalesPerformaStatus;
import com.stpl.tech.scm.domain.model.SalesPerformaType;
import com.stpl.tech.scm.domain.model.ServiceOrder;
import com.stpl.tech.scm.domain.model.ServiceOrderItem;
import com.stpl.tech.scm.domain.model.ServiceOrderShort;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceReceive;
import com.stpl.tech.scm.domain.model.ServiceReceiveItem;
import com.stpl.tech.scm.domain.model.ServiceReceiveShort;
import com.stpl.tech.scm.domain.model.ServiceReceiveTaxDetail;
import com.stpl.tech.scm.domain.model.ServiceReceivedItemDrilldown;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuBasicDetail;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuPrice;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.StockEventAssetMappingDefinition;
import com.stpl.tech.scm.domain.model.StockEventAssetMappingDefinitionRequest;
import com.stpl.tech.scm.domain.model.StockEventDefinition;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventStatusType;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.TagType;
import com.stpl.tech.scm.domain.model.TaxCategoryType;
import com.stpl.tech.scm.domain.model.TaxDetail;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.TransferOrderItem;
import com.stpl.tech.scm.domain.model.TransferOrderNoteType;
import com.stpl.tech.scm.domain.model.TransferOrderType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.UnitRoMappingDetailData;
import com.stpl.tech.scm.domain.model.UnitVendorMapping;
import com.stpl.tech.scm.domain.model.ValidateStateOutput;
import com.stpl.tech.scm.domain.model.VarianceAcknowledgementDetail;
import com.stpl.tech.scm.domain.model.VarianceType;
import com.stpl.tech.scm.domain.model.Vehicle;
import com.stpl.tech.scm.domain.model.VehicleDispatch;
import com.stpl.tech.scm.domain.model.VendorAccountDetail;
import com.stpl.tech.scm.domain.model.VendorAccountType;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorBasicDetail;
import com.stpl.tech.scm.domain.model.VendorCompanyDetail;
import com.stpl.tech.scm.domain.model.VendorContractItemDataVO;
import com.stpl.tech.scm.domain.model.VendorContractItemVO;
import com.stpl.tech.scm.domain.model.VendorDebitBalanceVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorEditedData;
import com.stpl.tech.scm.domain.model.VendorEditedFieldStatus;
import com.stpl.tech.scm.domain.model.VendorEditedFieldType;
import com.stpl.tech.scm.domain.model.VendorGR;
import com.stpl.tech.scm.domain.model.VendorGRItem;
import com.stpl.tech.scm.domain.model.VendorGrType;
import com.stpl.tech.scm.domain.model.VendorRegistrationRequest;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.domain.model.VendorType;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.model.WastageEventType;
import com.stpl.tech.scm.domain.model.WorkOrder;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.stream.Collectors;


/**
 * Created by Rahul Singh on 04-05-2016.
 */
public class SCMDataConverter {

    private static final ObjectFactory factory = new ObjectFactory();

    private static final Logger LOG = LoggerFactory.getLogger(SCMDataConverter.class);

    /**
     * @param pd
     * @return
     */
    public static ProductPriceData convertToPrice(ProductDefinition pd) {
        ProductPriceData data = new ProductPriceData();
        data.setName(pd.getProductName());
        data.setProductId(pd.getProductId());
        data.setStatus(pd.getProductStatus());
        data.setUom(pd.getUnitOfMeasure());
        data.setPrice(SCMUtil.convertToBigDecimal(pd.getNegotiatedUnitPrice()));
        return data;
    }

    public static ProductPriceData convert(PriceUpdateEntry entry) {
        ProductPriceData data = new ProductPriceData();
        data.setName(entry.getKeyName());
        data.setPrice(entry.getApprovedUnitPrice());
        data.setProductId(entry.getKeyId());
        data.setStatus(ProductStatus.ACTIVE);
        data.setUom(entry.getUnitOfMeasure());
        return data;
    }

    public static AttributeDefinition convert(AttributeDefinitionData attributeDefinitionData) {
        AttributeDefinition attributeDefinition = factory.createAttributeDefinition();
        attributeDefinition.setAttributeCode(attributeDefinitionData.getAttributeCode());
        attributeDefinition.setAttributeDescription(attributeDefinitionData.getAttributeDescription());
        attributeDefinition.setAttributeId(attributeDefinitionData.getAttributeId());
        attributeDefinition.setAttributeName(attributeDefinitionData.getAttributeName());
        attributeDefinition.setAttributeShortCode(attributeDefinitionData.getAttributeShortCode());
        attributeDefinition.setAttributeStatus(SwitchStatus.valueOf(attributeDefinitionData.getAttributeStatus()));
        attributeDefinition.setAttributeType(AttributeType.fromValue(attributeDefinitionData.getAttributeType()));
        return attributeDefinition;
    }

    public static AttributeDefinitionData convert(AttributeDefinition attributeDefinition) {
        AttributeDefinitionData attributeDefinitionData = new AttributeDefinitionData();
        attributeDefinitionData.setAttributeType(attributeDefinition.getAttributeType().value());
        attributeDefinitionData.setAttributeShortCode(attributeDefinition.getAttributeShortCode());
        attributeDefinitionData.setAttributeName(attributeDefinition.getAttributeName());
        attributeDefinitionData.setAttributeDescription(attributeDefinition.getAttributeDescription());
        attributeDefinitionData.setAttributeCode(attributeDefinition.getAttributeCode());
        attributeDefinitionData.setAttributeId(attributeDefinition.getAttributeId());
        attributeDefinitionData.setAttributeStatus(attributeDefinition.getAttributeStatus().value());
        return attributeDefinitionData;
    }

    public static CategoryDefinitionData convert(CategoryDefinition categoryDefinition) {
        CategoryDefinitionData categoryDefinitionData = new CategoryDefinitionData();
        categoryDefinitionData.setCategoryCode(categoryDefinition.getCategoryCode());
        categoryDefinitionData.setCategoryDescription(categoryDefinition.getCategoryDescription());
        categoryDefinitionData.setCategoryId(categoryDefinition.getCategoryId());
        categoryDefinitionData.setCategoryName(categoryDefinition.getCategoryName());
        categoryDefinitionData.setCategoryStatus(categoryDefinition.getCategoryStatus().name());
        List<SubCategoryDefinitionData> subCategoryDefinitionDataList = new ArrayList<SubCategoryDefinitionData>();
        for (SubCategoryDefinition subCategoryDefinition : categoryDefinition.getSubCategories()) {
            subCategoryDefinitionDataList.add(convert(subCategoryDefinition));
            categoryDefinition.setCategoryCode(categoryDefinitionData.getCategoryCode());
            categoryDefinition.setCategoryDescription(categoryDefinitionData.getCategoryDescription());
            categoryDefinition.setCategoryId(categoryDefinitionData.getCategoryId());
            categoryDefinition.setCategoryName(categoryDefinitionData.getCategoryName());
        }
        categoryDefinitionData.setSubCategoryDefinitionData(subCategoryDefinitionDataList);
        return categoryDefinitionData;
    }

    public static CategoryDefinition convert(CategoryDefinitionData categoryDefinitionData) {
        CategoryDefinition categoryDefinition = factory.createCategoryDefinition();
        categoryDefinition.setCategoryCode(categoryDefinitionData.getCategoryCode());
        categoryDefinition.setCategoryDescription(categoryDefinitionData.getCategoryDescription());
        categoryDefinition.setCategoryId(categoryDefinitionData.getCategoryId());
        categoryDefinition.setCategoryName(categoryDefinitionData.getCategoryName());
        categoryDefinition.setCategoryStatus(SwitchStatus.valueOf(categoryDefinitionData.getCategoryStatus()));
        for (SubCategoryDefinitionData subCategoryDefinitionData : categoryDefinitionData
                .getSubCategoryDefinitionData()) {
            categoryDefinition.getSubCategories().add(convert(subCategoryDefinitionData));
        }
        return categoryDefinition;
    }

    public static SubCategoryDefinition convert(SubCategoryDefinitionData subCategoryDefinitionData) {
        SubCategoryDefinition subCategoryDefinition = factory.createSubCategoryDefinition();
        subCategoryDefinition.setSubCategoryCode(subCategoryDefinitionData.getCode());
        subCategoryDefinition.setSubCategoryDescription(subCategoryDefinitionData.getDescription());
        subCategoryDefinition.setSubCategoryId(subCategoryDefinitionData.getId());
        subCategoryDefinition.setSubCategoryName(subCategoryDefinitionData.getName());
        subCategoryDefinition.setSubCategoryStatus(SwitchStatus.valueOf(subCategoryDefinitionData.getStatus()));
        subCategoryDefinition.setShelfLifeRange(subCategoryDefinitionData.getShelfLifeRange());
        subCategoryDefinition.setShelfLifeInDays(subCategoryDefinitionData.getShelfLifeInDays());
        return subCategoryDefinition;
    }

    public static SubCategoryDefinitionData convert(SubCategoryDefinition subCategoryDefinition) {
        SubCategoryDefinitionData subCategoryDefinitionData = new SubCategoryDefinitionData();
        subCategoryDefinitionData.setId(subCategoryDefinition.getSubCategoryId());
        subCategoryDefinitionData.setName(subCategoryDefinition.getSubCategoryName());
        subCategoryDefinitionData.setCode(subCategoryDefinition.getSubCategoryCode());
        subCategoryDefinitionData.setDescription(subCategoryDefinition.getSubCategoryDescription());
        subCategoryDefinitionData.setStatus(subCategoryDefinition.getSubCategoryStatus().value());
        subCategoryDefinitionData.setShelfLifeRange(subCategoryDefinition.getShelfLifeRange());
        subCategoryDefinitionData.setShelfLifeInDays(subCategoryDefinition.getShelfLifeInDays());
        return subCategoryDefinitionData;
    }

    public static AttributeValue convert(AttributeValueData attributeValueData) {
        AttributeValue attributeValue = factory.createAttributeValue();
        attributeValue.setAttributeDefinitionId(attributeValueData.getAttributeDefinition().getAttributeId());
        attributeValue.setAttributeValue(attributeValueData.getAttributeValue());
        attributeValue.setAttributeValueId(attributeValueData.getAttributeValueId());
        attributeValue.setAttributeValueShortCode(attributeValueData.getAttributeValueShortCode());
        attributeValue.setAttributeValueStatus(SwitchStatus.valueOf(attributeValueData.getAttributeValueStatus()));
        return attributeValue;
    }

    public static AttributeValueData convert(AttributeValue attributeValue, AttributeDefinition attributeDefinition) {
        AttributeValueData attributeValueData = new AttributeValueData();
        attributeValueData.setAttributeValueShortCode(attributeValue.getAttributeValueShortCode());
        attributeValueData.setAttributeValue(attributeValue.getAttributeValue());
        attributeValueData.setAttributeDefinition(convert(attributeDefinition));
        attributeValueData.setAttributeValueId(attributeValue.getAttributeValueId());
        attributeValueData.setAttributeValueStatus(attributeValue.getAttributeValueStatus().value());
        return attributeValueData;
    }

    public static PackagingDefinition convert(PackagingDefinitionData packagingDefinitionData) {
        PackagingDefinition packagingDefinition = factory.createPackagingDefinition();
        packagingDefinition.setConversionRatio(packagingDefinitionData.getConversionRatio().floatValue());
        packagingDefinition.setPackagingCode(packagingDefinitionData.getPackagingCode());
        packagingDefinition.setPackagingId(packagingDefinitionData.getPackagingId());
        packagingDefinition.setPackagingName(packagingDefinitionData.getPackagingName());
        packagingDefinition.setPackagingStatus(SwitchStatus.valueOf(packagingDefinitionData.getPackagingStatus()));
        packagingDefinition.setPackagingType(PackagingType.valueOf(packagingDefinitionData.getPackagingType()));
        packagingDefinition.setUnitOfMeasure(packagingDefinitionData.getUnitOfMeasure());
        if (packagingDefinitionData.getSubPackaging() != null) {
            packagingDefinition.setSubPackagingId(packagingDefinitionData.getSubPackaging());
        }
        return packagingDefinition;
    }

    public static PackagingDefinitionData convert(PackagingDefinition packagingDefinition) {
        PackagingDefinitionData packagingDefinitionData = new PackagingDefinitionData();
        packagingDefinitionData
                .setConversionRatio(SCMUtil.convertToBigDecimal(packagingDefinition.getConversionRatio()));
        packagingDefinitionData.setPackagingCode(packagingDefinition.getPackagingCode());
        packagingDefinitionData.setPackagingId(packagingDefinition.getPackagingId());
        packagingDefinitionData.setPackagingName(packagingDefinition.getPackagingName());
        packagingDefinitionData.setPackagingStatus(packagingDefinition.getPackagingStatus().value());
        packagingDefinitionData.setPackagingType(packagingDefinition.getPackagingType().value());
        packagingDefinitionData.setUnitOfMeasure(packagingDefinition.getUnitOfMeasure());
        if (packagingDefinition.getSubPackagingId() != null) {
            packagingDefinitionData.setSubPackaging(packagingDefinition.getSubPackagingId());
        }
        return packagingDefinitionData;
    }

    public static ProfileDefinition convert(ProfileDefinitionData profileDefinitionData, IdCodeName createdBy) {
        ProfileDefinition profileDefinition = factory.createProfileDefinition();
        profileDefinition.setProfileDescription(profileDefinitionData.getProfileDescription());
        profileDefinition.setCreationDate(profileDefinitionData.getCreationDate());
        profileDefinition.setCreatedBy(createdBy);
        profileDefinition.setProfileCode(profileDefinitionData.getProfileCode());
        profileDefinition.setProfileId(profileDefinitionData.getProfileId());
        profileDefinition.setProfileName(profileDefinitionData.getProfileName());
        profileDefinition.setProfileStatus(profileDefinitionData.getProfileStatus());
        profileDefinition.setUniqueNumberAvailable(AppUtils.getStatus(profileDefinitionData.getUniqueNumberAvailable()));
        profileDefinition.setUniqueFieldName(profileDefinitionData.getUniqueFieldName());
        return profileDefinition;
    }

    public static ProfileAttributeMapping convert(ProfileAttributeMappingData profileAttributeMappingData, IdCodeName createdBy, IdCodeName updatedBy) {
        ProfileAttributeMapping profileAttributeMapping = factory.createProfileAttributeMapping();

        profileAttributeMapping.setCreationDate(profileAttributeMappingData.getCreationDate());
        profileAttributeMapping.setUpdationDate(profileAttributeMappingData.getUpdationDate());
        profileAttributeMapping.setAttributeId(profileAttributeMappingData.getAttributeDefinition().getAttributeId());
        profileAttributeMapping.setCreatedBy(createdBy);
        profileAttributeMapping.setDefinedAtAsset(AppUtils.getStatus(profileAttributeMappingData.getIsDefinedAtAsset()));
        profileAttributeMapping.setDefinedAtProduct(AppUtils.getStatus(profileAttributeMappingData.getIsDefinedAtProduct()));
        profileAttributeMapping.setDefinedAtSKU(AppUtils.getStatus(profileAttributeMappingData.getIsDefinedAtSKU()));
        profileAttributeMapping.setMandatoryAtAsset(AppUtils.getStatus(profileAttributeMappingData.getIsMandatoryAtAsset()));
        profileAttributeMapping.setMandatoryAtProduct(AppUtils.getStatus(profileAttributeMappingData.getIsMandatoryAtProduct()));
        profileAttributeMapping.setMandatoryAtSKU(AppUtils.getStatus(profileAttributeMappingData.getIsMandatoryAtSKU()));
        profileAttributeMapping.setOverridableAtProduct(AppUtils.getStatus(profileAttributeMappingData.getIsOverridableAtProduct()));
        profileAttributeMapping.setOverridableAtSKU(AppUtils.getStatus(profileAttributeMappingData.getIsOverridableAtSKU()));
        profileAttributeMapping.setParticipateInName(AppUtils.getStatus(profileAttributeMappingData.getParticipateInName()));
        profileAttributeMapping.setProfileId(profileAttributeMappingData.getProfileDefinitionData().getProfileId());
        profileAttributeMapping.setStatus(profileAttributeMappingData.getStatus());
        profileAttributeMapping.setUpdatedBy(updatedBy);
        profileAttributeMapping.setProfileAttributeMappingId(profileAttributeMappingData.getProfileAttributeMappingId());
        profileAttributeMapping.setAssociatedImage(profileAttributeMappingData.getAssociatedImage());
        profileAttributeMapping.setAttributeName(profileAttributeMappingData.getAttributeDefinition().getAttributeName());
        profileAttributeMapping.setStandAlone(AppUtils.getStatus(profileAttributeMappingData.getIsStandAlone()));
        return profileAttributeMapping;
    }

    public static ProductDefinition convert(ProductDefinitionData productDefinitionData, IdCodeName createdBy) {
        ProductDefinition productDefinition = factory.createProductDefinition();
        productDefinition.setUnitOfMeasure(productDefinitionData.getUnitOfMeasure());
        productDefinition.setVarianceType(productDefinitionData.getVarianceType() != null
                ? VarianceType.valueOf(productDefinitionData.getVarianceType())
                : null);
        productDefinition.setKitchenVarianceType(productDefinitionData.getKitchenVarianceType() != null
                ? VarianceType.valueOf(productDefinitionData.getKitchenVarianceType())
                : null);
        IdCodeName category = SCMUtil.generateIdCodeName(productDefinitionData.getCategoryDefinition().getCategoryId(),
                productDefinitionData.getCategoryDefinition().getCategoryCode(),
                productDefinitionData.getCategoryDefinition().getCategoryName());
        productDefinition.setCategoryDefinition(category);
        IdCodeName subCategory = SCMUtil.generateIdCodeName(productDefinitionData.getSubCategoryDefinition().getId(),
                productDefinitionData.getSubCategoryDefinition().getCode(),
                productDefinitionData.getSubCategoryDefinition().getName());
        if (productDefinitionData.getSubCategoryDefinition() != null) {
            productDefinition.setSubCategoryDefinition(subCategory);
        }
        productDefinition.setCreatedBy(createdBy);
        productDefinition.setCreationDate(productDefinitionData.getCreationDate());
        if (StringUtils.isNotEmpty(productDefinitionData.getFulfillmentType())) {
            FulfillmentType productFulfillmentType = FulfillmentType
                    .valueOf(productDefinitionData.getFulfillmentType());
            productDefinition.setFulfillmentType(productFulfillmentType);
            if (productFulfillmentType.equals(FulfillmentType.DERIVED)) {
                productDefinition.setDefaultFulfillmentType(
                        FulfillmentType.valueOf(productDefinitionData.getDefaultFulfillmentType()));

                // set derivedMappings for the product
                if (productDefinitionData.getDerivedMappingDataList() != null) {
                    List<DerivedMapping> derivedMappings = productDefinitionData.getDerivedMappingDataList().stream()
                            .map(derivedMappingData -> convert(derivedMappingData)).collect(Collectors.toList());
                    productDefinition.getDerivedMappings().addAll(derivedMappings);
                }

            }
        }
        productDefinition.setAvailableAtCafe(
                SCMServiceConstants.SCM_CONSTANT_YES.equals(productDefinitionData.getAvailableForCafe()));
        productDefinition.setInterCafeTransfer(
            SCMServiceConstants.SCM_CONSTANT_YES.equals(productDefinitionData.getInterCafeTransfer()));
        productDefinition.setAvailableForCafeInventory(
                SCMServiceConstants.SCM_CONSTANT_YES.equals(productDefinitionData.getAvailableForCafeInventory()));
        productDefinition.setHasCase(AppUtils.getStatus(productDefinitionData.getHasCase()));
        productDefinition.setHasInner(AppUtils.getStatus(productDefinitionData.getHasInner()));
        productDefinition.setBulkGRAllowed(AppUtils.getStatus(productDefinitionData.getIsBulkGRAllowed()));
        productDefinition.setProductCode(productDefinitionData.getProductCode());
        productDefinition.setProductDescription(productDefinitionData.getProductDescription());
        productDefinition.setProductId(productDefinitionData.getProductId());
        productDefinition.setProductName(productDefinitionData.getProductName());
        productDefinition.setProductStatus(ProductStatus.fromValue(productDefinitionData.getProductStatus()));
        productDefinition.setShelfLifeInDays(productDefinitionData.getShelfLifeInDays());
        productDefinition
                .setStockKeepingFrequency(StockTakeType.valueOf(productDefinitionData.getStockKeepingFrequency()));
        productDefinition
                .setSupportsLooseOrdering(AppUtils.getStatus(productDefinitionData.getSupportsLooseOrdering()));
        productDefinition.setVariantLevelOrdering(SCMUtil.getStatus(productDefinitionData.getVariantLevelOrdering()));
        productDefinition.setProductImage(productDefinitionData.getProductImage());
        productDefinition
                .setSupportsSpecialOrdering(SCMUtil.getStatus(productDefinitionData.getSupportsSpecializedOrdering()));
        productDefinition.setTaxCode(productDefinitionData.getTaxCategoryCode());
        productDefinition.setParticipatesInRecipe(SCMUtil.getStatus(productDefinitionData.getParticipatesInRecipe()));
        productDefinition.setParticipatesInCafeRecipe(SCMUtil.getStatus(productDefinitionData.getParticipatesInCafeRecipe()));
        productDefinition.setAssetOrdering(SCMUtil.getStatus(productDefinitionData.getAssetOrdering()));
        productDefinition.setNegotiatedUnitPrice(productDefinitionData.getNegotiatedUnitPrice() == null ? 0.00F
                : productDefinitionData.getNegotiatedUnitPrice().floatValue());
        productDefinition.setUnitPrice(productDefinitionData.getUnitPrice() == null ? 0.00F
                : productDefinitionData.getUnitPrice().floatValue());
        productDefinition.setAutoProduction(SCMUtil.getStatus(productDefinitionData.getAutoProduction()));
        productDefinition.setRecipeRequired(SCMUtil.getStatus(productDefinitionData.getRecipeRequired()));
        productDefinition.setParticipatesInPnl(SCMUtil.getStatus(productDefinitionData.getParticipatesInPnl()));
        if (productDefinitionData.getProfileDefinitionData() != null) {
            productDefinition.setProfileId(productDefinitionData.getProfileDefinitionData().getProfileId());
        }
        productDefinition.setDivisionId(productDefinitionData.getDivisionId());
        productDefinition.setDepartmentId(productDefinitionData.getDepartmentId());
        productDefinition.setClassificationId(productDefinitionData.getClassificationId());
        productDefinition.setSubClassificationId(productDefinitionData.getSubClassificationId());
        productDefinition.setBrandId(productDefinitionData.getBrandId());
        productDefinition.setCompanyId(productDefinitionData.getCompanyId());
        productDefinition.setApprovalDocumentId(productDefinitionData.getApprovalDocumentId());
        if (Objects.nonNull(productDefinitionData.getProductType())) {
            productDefinition.setProductType(productDefinitionData.getProductType());
        }
        if(Objects.nonNull(productDefinitionData.getCategoryLevel())){
            productDefinition.setCategoryLevel(productDefinitionData.getCategoryLevel());
        }
        return productDefinition;
    }

    public static ProductDefinition convert(UserProductCreationRequestData userProductCreationRequestData, IdCodeName createdBy) {
        ProductDefinition productDefinition = factory.createProductDefinition();
        productDefinition.setUnitOfMeasure(userProductCreationRequestData.getUnitOfMeasure());
        productDefinition.setVarianceType(userProductCreationRequestData.getVarianceType() != null
                ? VarianceType.valueOf(userProductCreationRequestData.getVarianceType())
                : null);
        productDefinition.setKitchenVarianceType(userProductCreationRequestData.getKitchenVarianceType() != null
                ? VarianceType.valueOf(userProductCreationRequestData.getKitchenVarianceType())
                : null);

        if(Objects.nonNull(userProductCreationRequestData.getCategoryDefinition())) {
            IdCodeName category = SCMUtil.generateIdCodeName(userProductCreationRequestData.getCategoryDefinition().getCategoryId(),
                    userProductCreationRequestData.getCategoryDefinition().getCategoryCode(),
                    userProductCreationRequestData.getCategoryDefinition().getCategoryName());
            productDefinition.setCategoryDefinition(category);
        }
        if(Objects.nonNull(userProductCreationRequestData.getSubCategoryDefinition())) {
            IdCodeName subCategory = SCMUtil.generateIdCodeName(userProductCreationRequestData.getSubCategoryDefinition().getId(),
                    userProductCreationRequestData.getSubCategoryDefinition().getCode(),
                    userProductCreationRequestData.getSubCategoryDefinition().getName());
            productDefinition.setSubCategoryDefinition(subCategory);
        }
        productDefinition.setCreatedBy(createdBy);
        productDefinition.setCreationDate(userProductCreationRequestData.getCreationDate());
        if (StringUtils.isNotEmpty(userProductCreationRequestData.getFulfillmentType())) {
            FulfillmentType productFulfillmentType = FulfillmentType
                    .valueOf(userProductCreationRequestData.getFulfillmentType());
            productDefinition.setFulfillmentType(productFulfillmentType);
            if (productFulfillmentType.equals(FulfillmentType.DERIVED)) {
                productDefinition.setDefaultFulfillmentType(
                        FulfillmentType.valueOf(userProductCreationRequestData.getDefaultFulfillmentType()));

                // set derivedMappings for the product
                if (userProductCreationRequestData.getDerivedMappingDataList() != null) {
                    List<DerivedMapping> derivedMappings = userProductCreationRequestData.getDerivedMappingDataList().stream()
                            .map(derivedMappingData -> convert(derivedMappingData)).collect(Collectors.toList());
                    productDefinition.getDerivedMappings().addAll(derivedMappings);
                }

            }
        }
        productDefinition.setAvailableAtCafe(
                SCMServiceConstants.SCM_CONSTANT_YES.equals(userProductCreationRequestData.getAvailableForCafe()));
        productDefinition.setInterCafeTransfer(
                SCMServiceConstants.SCM_CONSTANT_YES.equals(userProductCreationRequestData.getInterCafeTransfer()));
        productDefinition.setAvailableForCafeInventory(
                SCMServiceConstants.SCM_CONSTANT_YES.equals(userProductCreationRequestData.getAvailableForCafeInventory()));
        productDefinition.setHasCase(AppUtils.getStatus(userProductCreationRequestData.getHasCase()));
        productDefinition.setHasInner(AppUtils.getStatus(userProductCreationRequestData.getHasInner()));
        productDefinition.setBulkGRAllowed(AppUtils.getStatus(userProductCreationRequestData.getIsBulkGRAllowed()));
        productDefinition.setProductCode(userProductCreationRequestData.getProductCode());
        productDefinition.setProductDescription(userProductCreationRequestData.getProductDescription());
        productDefinition.setProductId(userProductCreationRequestData.getProductId());
        productDefinition.setProductName(userProductCreationRequestData.getProductName());
        productDefinition.setHodId(userProductCreationRequestData.getHodId());
        productDefinition.setProductStatus(ProductStatus.fromValue(userProductCreationRequestData.getProductStatus()));
        productDefinition.setShelfLifeInDays(userProductCreationRequestData.getShelfLifeInDays());
        productDefinition
                .setStockKeepingFrequency(StockTakeType.valueOf(userProductCreationRequestData.getStockKeepingFrequency()));
        productDefinition
                .setSupportsLooseOrdering(AppUtils.getStatus(userProductCreationRequestData.getSupportsLooseOrdering()));
        productDefinition.setVariantLevelOrdering(SCMUtil.getStatus(userProductCreationRequestData.getVariantLevelOrdering()));
        productDefinition.setProductImage(userProductCreationRequestData.getProductImage());
        productDefinition
                .setSupportsSpecialOrdering(SCMUtil.getStatus(userProductCreationRequestData.getSupportsSpecializedOrdering()));
        productDefinition.setTaxCode(userProductCreationRequestData.getTaxCategoryCode());
        productDefinition.setParticipatesInRecipe(SCMUtil.getStatus(userProductCreationRequestData.getParticipatesInRecipe()));
        productDefinition.setParticipatesInCafeRecipe(SCMUtil.getStatus(userProductCreationRequestData.getParticipatesInCafeRecipe()));
        productDefinition.setAssetOrdering(SCMUtil.getStatus(userProductCreationRequestData.getAssetOrdering()));
        productDefinition.setNegotiatedUnitPrice(userProductCreationRequestData.getNegotiatedUnitPrice() == null ? 0.00F
                : userProductCreationRequestData.getNegotiatedUnitPrice().floatValue());
        productDefinition.setUnitPrice(userProductCreationRequestData.getUnitPrice() == null ? 0.00F
                : userProductCreationRequestData.getUnitPrice().floatValue());
        productDefinition.setAutoProduction(SCMUtil.getStatus(userProductCreationRequestData.getAutoProduction()));
        productDefinition.setRecipeRequired(SCMUtil.getStatus(userProductCreationRequestData.getRecipeRequired()));
        productDefinition.setParticipatesInPnl(SCMUtil.getStatus(userProductCreationRequestData.getParticipatesInPnl()));
        if (userProductCreationRequestData.getProfileDefinitionData() != null) {
            productDefinition.setProfileId(userProductCreationRequestData.getProfileDefinitionData().getProfileId());
        }
        productDefinition.setDivisionId(userProductCreationRequestData.getDivisionId());
        productDefinition.setDepartmentId(userProductCreationRequestData.getDepartmentId());
        productDefinition.setClassificationId(userProductCreationRequestData.getClassificationId());
        productDefinition.setSubClassificationId(userProductCreationRequestData.getSubClassificationId());
        productDefinition.setBrandId(userProductCreationRequestData.getBrandId());
        productDefinition.setCompanyId(userProductCreationRequestData.getCompanyId());
        productDefinition.setApprovalDocumentId(userProductCreationRequestData.getApprovalDocumentId());
        if (Objects.nonNull(userProductCreationRequestData.getProductType())) {
            productDefinition.setProductType(userProductCreationRequestData.getProductType());
        }
        return productDefinition;
    }

    private static DerivedMapping convert(DerivedMappingData derivedMappingData) {
        DerivedMapping derivedMapping = new DerivedMapping();
        derivedMapping.setUnit(derivedMappingData.getUnitId());
        if (derivedMappingData.getFulfillmentType() != null) {
            derivedMapping.setType(FulfillmentType.valueOf(derivedMappingData.getFulfillmentType()));
        }
        return derivedMapping;
    }

    public static ProfileDefinitionData convert(ProfileDefinition profileDefinition) {
        ProfileDefinitionData profileDefinitionData = new ProfileDefinitionData();
        profileDefinitionData.setProfileId(profileDefinition.getProfileId());
        profileDefinitionData.setCreatedBy(profileDefinition.getCreatedBy().getId());
        profileDefinitionData.setCreationDate(profileDefinition.getCreationDate());
        profileDefinitionData.setProfileCode(profileDefinition.getProfileCode());
        profileDefinitionData.setProfileName(profileDefinition.getProfileName());
        profileDefinitionData.setProfileStatus(profileDefinition.getProfileStatus());
        profileDefinitionData.setProfileDescription(profileDefinition.getProfileDescription());
        profileDefinitionData.setUniqueFieldName(profileDefinition.getUniqueFieldName());
        profileDefinitionData.setUniqueNumberAvailable(SCMUtil.setStatus(profileDefinition.getUniqueNumberAvailable()));
        return profileDefinitionData;
    }

    public static ProfileAttributeMappingData convert(ProfileAttributeMapping profileAttributeMapping,
                                                      ProfileDefinitionData profileDefinitionData, AttributeDefinitionData attributeDefinitionData) {
        ProfileAttributeMappingData profileAttributeMappingData = new ProfileAttributeMappingData();
        profileAttributeMappingData.setProfileAttributeMappingId(profileAttributeMapping.getProfileAttributeMappingId());
        profileAttributeMappingData.setAttributeDefinition(attributeDefinitionData);
        profileAttributeMappingData.setCreatedBy(profileAttributeMapping.getCreatedBy().getId());
        profileAttributeMappingData.setCreationDate(profileAttributeMapping.getCreationDate());
        profileAttributeMappingData.setIsDefinedAtAsset(SCMUtil.setStatus(profileAttributeMapping.isDefinedAtAsset()));
        profileAttributeMappingData.setIsDefinedAtProduct(SCMUtil.setStatus(profileAttributeMapping.isDefinedAtProduct()));
        profileAttributeMappingData.setIsDefinedAtSKU(SCMUtil.setStatus(profileAttributeMapping.isDefinedAtSKU()));
        profileAttributeMappingData.setIsMandatoryAtAsset(SCMUtil.setStatus(profileAttributeMapping.isMandatoryAtAsset()));
        profileAttributeMappingData.setIsMandatoryAtProduct(SCMUtil.setStatus(profileAttributeMapping.isMandatoryAtProduct()));
        profileAttributeMappingData.setIsMandatoryAtSKU(SCMUtil.setStatus(profileAttributeMapping.isMandatoryAtSKU()));
        profileAttributeMappingData.setIsOverridableAtProduct(SCMUtil.setStatus(profileAttributeMapping.isOverridableAtProduct()));
        profileAttributeMappingData.setIsOverridableAtSKU(SCMUtil.setStatus(profileAttributeMapping.isOverridableAtSKU()));
        profileAttributeMappingData.setParticipateInName(SCMUtil.setStatus(profileAttributeMapping.isParticipateInName()));
        profileAttributeMappingData.setProfileDefinitionData(profileDefinitionData);
        profileAttributeMappingData.setStatus(profileAttributeMapping.getStatus());
        profileAttributeMappingData.setUpdatedBy(profileAttributeMapping.getUpdatedBy().getId());
        profileAttributeMappingData.setUpdationDate(profileAttributeMapping.getUpdationDate());
        profileAttributeMappingData.setAssociatedImage(profileAttributeMapping.getAssociatedImage());
        profileAttributeMappingData.setIsStandAlone(SCMUtil.setStatus(profileAttributeMapping.isStandAlone()));
        return profileAttributeMappingData;
    }

    public static AssetDefinitionData convert(ProductDefinitionData product, AssetDefinition assetDefinition) {
        AssetDefinitionData assetDefinitionData = new AssetDefinitionData();
        assetDefinitionData.setAssetStatus(assetDefinition.getAssetStatus().value());
        assetDefinitionData.setActualEndDate(assetDefinition.getActualEndDate());
        assetDefinitionData.setAmcLastDate(assetDefinition.getAmcLastDate());
        assetDefinitionData.setAssetId(assetDefinition.getAssetId());
        assetDefinitionData.setAssetImageUrl(assetDefinition.getAssetImageUrl());
        assetDefinitionData.setAssetName(assetDefinition.getAssetName());
        assetDefinitionData.setCreatedBy(assetDefinition.getCreatedBy().getId());
        assetDefinitionData.setCreationDate(assetDefinition.getCreationDate());
        if (assetDefinition.getDailyDepreciationRate() != null) {
            assetDefinitionData.setDailyDepreciationRate(new BigDecimal(Float.toString(assetDefinition.getDailyDepreciationRate())));
        }
        if (assetDefinition.getDepreciationRatePa() != null) {
            DecimalFormat df = new DecimalFormat("#.######");
            df.setRoundingMode(RoundingMode.CEILING);
            double d1 = assetDefinition.getDepreciationRatePa();
            double d2 = assetDefinition.getDepreciationResidue();
            assetDefinitionData.setDepreciationRatePa(new BigDecimal(df.format(d1)));
            assetDefinitionData.setDepreciationResidue(new BigDecimal(df.format(d2)));
        }

        assetDefinitionData.setDepreciationStrategy(assetDefinition.getDepreciationStrategy());
        assetDefinitionData.setExpectedEndDate(assetDefinition.getExpectedEndDate());
        assetDefinitionData.setFirstOwnerId(assetDefinition.getFirstOwnerId());
        assetDefinitionData.setFirstOwnerType(assetDefinition.getFirstOwnerType());
        assetDefinitionData.setGrId(assetDefinition.getGrId());
        assetDefinitionData.setGrItemId(assetDefinition.getGrItemId());
        assetDefinitionData.setHasAMC(SCMUtil.setStatus(assetDefinition.isHasAMC()));
        assetDefinitionData.setHasInsurance(SCMUtil.setStatus(assetDefinition.isHasInsurance()));
        assetDefinitionData.setHasWarranty(SCMUtil.setStatus(assetDefinition.isHasWarranty()));
        assetDefinitionData.setWarrantyLastDate(assetDefinition.getWarrantyLastDate());
        assetDefinitionData.setInsuranceLastDate(assetDefinition.getInsuranceLastDate());
        assetDefinitionData.setInventoryDate(assetDefinition.getInventoryDate());
        if (assetDefinition.getLastTagPrintDate() != null) {
            assetDefinitionData.setLastTagPrintDate(assetDefinition.getLastTagPrintDate());
            assetDefinitionData.setLastTagPrintedBy(assetDefinition.getLastTagPrintedBy().getId());
        }
        if (assetDefinition.getLastTransferId() != null) {
            assetDefinitionData.setLastTransferDate(assetDefinition.getLastTransferDate());
            assetDefinitionData.setLastTransferedBy(assetDefinition.getLastTransferedBy().getId());
            assetDefinitionData.setLastTransferId(assetDefinition.getLastTransferId());
            assetDefinitionData.setLastTransferType(assetDefinition.getLastTransferType());
        }
        if (assetDefinition.getLifeTimeType() != null) {
            assetDefinitionData.setLifeTimeInDays(assetDefinition.getLifeTimeInDays());
            assetDefinitionData.setLifeTimeType(assetDefinition.getLifeTimeType().value());
            assetDefinitionData.setLifeTimeValue(new BigDecimal(Float.toString(assetDefinition.getLifeTimeValue())));
        }
        assetDefinitionData.setOwnerId(assetDefinition.getOwnerId());
        assetDefinitionData.setOwnerType(assetDefinition.getOwnerType());
        assetDefinitionData.setPrice(new BigDecimal(Float.toString(assetDefinition.getPrice())));
        assetDefinitionData.setProcurementCost(new BigDecimal(Float.toString(assetDefinition.getProcurementCost())));
        assetDefinitionData.setProduct(product);
        assetDefinitionData.setProfileId(assetDefinition.getProfileId());
        assetDefinitionData.setQuantity(assetDefinition.getQuantity());
        if (assetDefinition.getRealizedDepreciation() != null) {
            assetDefinitionData.setRealizedDepreciation(new BigDecimal(Float.toString(assetDefinition.getRealizedDepreciation())));
            assetDefinitionData.setRealizedDepreciationDate(assetDefinition.getRealizedDepreciationDate());
        }
        assetDefinitionData.setSKUId(assetDefinition.getSkuId());
        assetDefinitionData.setStartDate(assetDefinition.getStartDate());
        assetDefinitionData.setTagPrintCount(assetDefinition.getTagPrintCount());
        if (assetDefinition.getTagType() != null) {
            assetDefinitionData.setTagType(assetDefinition.getTagType().value());
            assetDefinitionData.setTagValue(assetDefinition.getTagValue());
        }
        assetDefinitionData.setTax(new BigDecimal(Float.toString(assetDefinition.getTax())));
        assetDefinitionData.setUnitId(assetDefinition.getUnitId());
        assetDefinitionData.setUnitType(assetDefinition.getUnitType());
        assetDefinitionData.setVendorId(assetDefinition.getVendorId());
        assetDefinitionData.setVendorName(assetDefinition.getVendorName());
        /////////////////////
        assetDefinitionData.setGrossBlock(new BigDecimal(Float.toString(assetDefinition.getGrossBlock())));
        assetDefinitionData.setFixedValue(new BigDecimal(Float.toString(assetDefinition.getFixedValue())));
        assetDefinitionData.setRecoveryStatus(assetDefinition.getRecoveryStatus());
        if (assetDefinition.getRecoveryType() != null) {
            assetDefinitionData.setRecoveryType(assetDefinition.getRecoveryType());
        }
        if(assetDefinition.getRecoveryAmount()!=null){
            assetDefinitionData.setRecoveryAmount(new BigDecimal(assetDefinition.getRecoveryAmount()));
        }
        if (assetDefinition.getWriteOffType() != null) {
            assetDefinitionData.setWriteOffType(assetDefinition.getWriteOffType());
            assetDefinitionData.setWriteOffAmount(new BigDecimal(Float.toString(assetDefinition.getWriteOffAmount())));
        }
        assetDefinitionData.setTaxPercentage(new BigDecimal(assetDefinition.getTaxPercentage()));
        assetDefinitionData.setUniqueFieldName(assetDefinition.getUniqueFieldName());
        if(Objects.nonNull(assetDefinition.getInTransit())){
            assetDefinitionData.setIsInTransit(AppUtils.setStatus(assetDefinition.getInTransit()));
        }
        return assetDefinitionData;
    }

    public static TransferOrderNoteData convert(AssetDefinitionData assetDefinitionData, Unit receivingUnit, Unit sendingUnit,
                                                TransferOrderNoteType transferOrderNoteType,
                                                TransferOrderData originalTo, TransferOrderData returnTo, Long uniqueLong, GoodsReceivedData goodsReceivedDat) {
        TransferOrderNoteData transferOrderNoteData = new TransferOrderNoteData();
        // Different Data
        transferOrderNoteData.setDocumentType(transferOrderNoteType.value());
        if (transferOrderNoteType.equals(TransferOrderNoteType.CREDIT_NOTE)) {
            // receiving unit will create this
            transferOrderNoteData.setGstin(receivingUnit.getTin());
            transferOrderNoteData.setUnitId(sendingUnit.getId());
            transferOrderNoteData.setUnitName(sendingUnit.getName());
            String uniqueSequence = transferOrderNoteType.getShortCode() + "/"
                    + receivingUnit.getLocation().getState().getCode() + "/" +
                    AppUtils.getYear(originalTo.getGenerationTime()) + "/" + uniqueLong;
            transferOrderNoteData.setUniqueSequence(uniqueSequence);
        } else {
            // sending unit will create this
            transferOrderNoteData.setGstin(sendingUnit.getTin());
            transferOrderNoteData.setUnitId(receivingUnit.getId());
            transferOrderNoteData.setUnitName(receivingUnit.getName());
            String uniqueSequence = transferOrderNoteType.getShortCode() + "/"
                    + sendingUnit.getLocation().getState().getCode() + "/" +
                    AppUtils.getYear(originalTo.getGenerationTime()) + "/" + uniqueLong;
            transferOrderNoteData.setUniqueSequence(uniqueSequence);
        }
        // Same Data
        transferOrderNoteData.setApplicableTaxRate(BigDecimal.ZERO);
        transferOrderNoteData.setCessAmount(BigDecimal.ZERO);
        transferOrderNoteData.setPreGst(BigDecimal.ZERO);
        transferOrderNoteData.setOriginalInvoiceDate(originalTo.getGenerationTime());
        transferOrderNoteData.setOriginalInvoiceNo(originalTo.getGeneratedInvoiceId());
        transferOrderNoteData.setAssetId(assetDefinitionData.getAssetId());
		transferOrderNoteData.setDateOfTransfer(
				returnTo == null ? goodsReceivedDat.getGenerationTime() : returnTo.getGenerationTime());
        transferOrderNoteData.setPlaceOfSupply(receivingUnit.getLocation().getState().getName());
        for (TransferOrderItemData transferOrderItemData : originalTo.getTransferOrderItemDatas()) {
            if (transferOrderItemData.getAssociatedAssetId().equals(assetDefinitionData.getAssetId())) {
                transferOrderNoteData.setTaxableValue(transferOrderItemData.getUnitPrice());
                transferOrderNoteData.setTaxRate(assetDefinitionData.getTaxPercentage());
                BigDecimal amountWithTax = transferOrderItemData.getUnitPrice()
                        .multiply(BigDecimal.ONE.add(assetDefinitionData.getTaxPercentage().divide(new BigDecimal(100))));
                transferOrderNoteData.setAmountWithTax(amountWithTax);
            }
        }
        return transferOrderNoteData;
    }

    public static AssetDepreciationSummaryData convert(AssetDefinitionData assetDefinitionData,
                                                       AssetDepreciationMappingData assetDepreciationMappingData, boolean isWriteOff) {
        AssetDepreciationSummaryData assetDepreciationSummaryData = new AssetDepreciationSummaryData();
        assetDepreciationSummaryData.setAssetId(assetDefinitionData.getAssetId());
        assetDepreciationSummaryData.setEndDate(assetDepreciationMappingData.getEndDate());
        assetDepreciationSummaryData.setStartDate(assetDepreciationMappingData.getStartDate());
        assetDepreciationSummaryData.setAssetStatus(assetDefinitionData.getAssetStatus());
        assetDepreciationSummaryData.setIsWriteOff(SCMUtil.setStatus(isWriteOff));
        assetDepreciationSummaryData.setWriteOffAmount(assetDefinitionData.getWriteOffAmount());
        assetDepreciationSummaryData.setWriteOffType(assetDefinitionData.getWriteOffType());
        assetDepreciationSummaryData.setCurrentValue(
                assetDefinitionData.getGrossBlock().subtract(assetDefinitionData.getRealizedDepreciation()));
        return assetDepreciationSummaryData;
    }

    public static AssetDefinitionSlimObject convertToSlimObject(AssetDefinition assetDefinition, SCMCache scmCache) {
        AssetDefinitionSlimObject assetDefinitionSlimObject = factory.createAssetDefinitionSlimObject();
        assetDefinitionSlimObject.setAssetId(assetDefinition.getAssetId());
        assetDefinitionSlimObject.setAssetName(assetDefinition.getAssetName());
        assetDefinitionSlimObject.setAssetStatus(assetDefinition.getAssetStatus());
        assetDefinitionSlimObject.setCurrentValue(assetDefinition.getCurrentValue());
        assetDefinitionSlimObject.setCurrentValueWithoutTax(assetDefinition.getCurrentValueWithoutTax());
        assetDefinitionSlimObject.setEntityAttributeValueMappings(assetDefinition.getEntityAttributeValueMappings());
        assetDefinitionSlimObject.setOwnerId(assetDefinition.getOwnerId());
        assetDefinitionSlimObject.setProductId(assetDefinition.getProductId());
        assetDefinitionSlimObject.setOwnerType(assetDefinition.getOwnerType());
        assetDefinitionSlimObject.setProfileAttributeMappingList(assetDefinition.getProfileAttributeMappingList());
        assetDefinitionSlimObject.setProfileId(assetDefinition.getProfileId());
        assetDefinitionSlimObject.setSkuId(assetDefinition.getSkuId());
        assetDefinitionSlimObject.setTagType(assetDefinition.getTagType());
        assetDefinitionSlimObject.setTagValue(assetDefinition.getTagValue());
        assetDefinitionSlimObject.setUniqueFieldName(assetDefinition.getUniqueFieldName());
        assetDefinitionSlimObject.setUniqueFieldValue(assetDefinition.getUniqueFieldValue());
        assetDefinitionSlimObject.setUnitId(assetDefinition.getUnitId());
        assetDefinitionSlimObject.setUnitType(assetDefinition.getUnitType());
        assetDefinitionSlimObject.setUnitName(scmCache.getUnitDetails().get(assetDefinition.getUnitId()).getUnitName());
        try{
            assetDefinitionSlimObject.setOwnerName(scmCache.getUnitDetails().get(assetDefinition.getOwnerId()).getUnitName());
        }catch (Exception e){

        }
        assetDefinitionSlimObject.setSubCategoryName(scmCache.getProductDefinition(assetDefinition.getProductId()).getSubCategoryDefinition().getName());
        return assetDefinitionSlimObject;
    }

    public static AssetRecoveryDefinitionData convert(AssetDefinition assetDefinition,
                                                      Integer empId, Integer createdBy, Date creationDate, StockEventDefinitionData stockEventDefinitionData) {
        AssetRecoveryDefinitionData assetRecoveryDefinitionData = new AssetRecoveryDefinitionData();
        assetRecoveryDefinitionData.setUnitId(assetDefinition.getUnitId());
        assetRecoveryDefinitionData.setRecoveryUnitType(assetDefinition.getUnitType());
        assetRecoveryDefinitionData.setRecoveryType(assetDefinition.getRecoveryType());
        assetRecoveryDefinitionData.setRecoveryStatus(assetDefinition.getRecoveryStatus());
        assetRecoveryDefinitionData.setRecoveryEmpId(empId);
        if (assetDefinition.getRecoveryAmount() != null) {
            assetRecoveryDefinitionData.setRecoveryAmount(new BigDecimal(assetDefinition.getRecoveryAmount()));
        }
        assetRecoveryDefinitionData.setAssetStatus(assetDefinition.getAssetStatus().value());
        assetRecoveryDefinitionData.setAssetId(assetDefinition.getAssetId());
        assetRecoveryDefinitionData.setCreatedBy(createdBy);
        assetRecoveryDefinitionData.setCreationDate(creationDate);
        assetRecoveryDefinitionData.setRecoveryDate(null);
        assetRecoveryDefinitionData.setSalaryDeductionDate(null);
        assetRecoveryDefinitionData.setRecoveryUnit(assetDefinition.getUnitId());
        if (Objects.nonNull(stockEventDefinitionData)) {
            assetRecoveryDefinitionData.setEventId(stockEventDefinitionData.getEventId());
        }
        return assetRecoveryDefinitionData;
    }

    public static AssetRecoveryData convert(AssetDefinition assetDefinition,
                                                      Integer createdBy, Date creationDate, StockEventDefinitionData stockEventDefinitionData) {
        AssetRecoveryData assetRecoveryData = new AssetRecoveryData();
        assetRecoveryData.setAssetId(assetDefinition.getAssetId());
        assetRecoveryData.setEventId(stockEventDefinitionData.getEventId());
        assetRecoveryData.setUnitId(stockEventDefinitionData.getUnitId());
        assetRecoveryData.setAmountToRecover(assetDefinition.getTotalRecoverAmount());
        assetRecoveryData.setRecoveryStatus(AssetRecoveryStatusType.PENDING.value());
        assetRecoveryData.setEmployeeRecoveryStatus(AssetRecoveryStatusType.PENDING.value());
        assetRecoveryData.setInsuranceRecoveryStatus(AssetRecoveryStatusType.PENDING.value());
        assetRecoveryData.setCreatedBy(createdBy);
        assetRecoveryData.setCreationDate(creationDate);

        return assetRecoveryData;
    }

//    public static AssetRecoveryDefinitionData convert(AssetRecoveryDefinition assetRecoveryDefinition) {
//        AssetRecoveryDefinitionData assetRecoveryDefinitionData = new AssetRecoveryDefinitionData();
//
//        assetRecoveryDefinitionData.setSalaryDeductionDate(assetRecoveryDefinition.getSalaryDeductionDate());
//        assetRecoveryDefinitionData.setRecoveryDate(assetRecoveryDefinition.getRecoveryDate());
//        assetRecoveryDefinitionData.setCreationDate(assetRecoveryDefinition.getCreationDate());
//        assetRecoveryDefinitionData.setCreatedBy(assetRecoveryDefinition.getCreatedBy().getId());
//        assetRecoveryDefinitionData.setAssetId(assetRecoveryDefinition.getAssetId());
//        assetRecoveryDefinitionData.setAssetStatus(assetRecoveryDefinition.getAssetStatus().value());
//        assetRecoveryDefinitionData.setRecoveryAmount(new BigDecimal(assetRecoveryDefinition.getRecoveryAmount()));
//        assetRecoveryDefinitionData.setRecoveryEmpId(assetRecoveryDefinition.getRecoveryEmpId());
//        assetRecoveryDefinitionData.setRecoveryStatus(assetRecoveryDefinition.getRecoveryStatus().value());
//        assetRecoveryDefinitionData.setRecoveryType(assetRecoveryDefinition.getRecoveryType());
//        assetRecoveryDefinitionData.setRecoveryUnitType(assetRecoveryDefinition.getRecoveryUnitType());
//        assetRecoveryDefinitionData.setRecoveryUnit(assetRecoveryDefinition.getRecoveryUnit());
//        assetRecoveryDefinitionData.setUnitId(assetRecoveryDefinition.getUnitId());
//        assetRecoveryDefinitionData.setAssetRecoveryId(assetRecoveryDefinition.getAssetRecoveryId());
//        assetRecoveryDefinitionData.setEventId(assetRecoveryDefinition.getEventId());
//        if (assetRecoveryDefinition.getRecoveredBy() != null) {
//            assetRecoveryDefinitionData.setRecoveredAmount(new BigDecimal(assetRecoveryDefinition.getRecoveredAmount()));
//            assetRecoveryDefinitionData.setRecoveredBy(assetRecoveryDefinition.getRecoveredBy().getId());
//        }
//        return assetRecoveryDefinitionData;
//    }

    public static AssetRecoveryDefinition convert(AssetRecoveryDefinitionData assetRecoveryDefinitionData,
                                                  IdCodeName createdBy, IdCodeName recoveredBy,
                                                  IdCodeName recoveredFrom,
                                                  AssetDefinition assetDefinition, IdCodeName approvedBy,
                                                  Unit unit, IdCodeName auditedBy) {
        AssetRecoveryDefinition assetRecoveryDefinition = factory.createAssetRecovery();
        assetRecoveryDefinition.setAssetDefinition(assetDefinition);
        assetRecoveryDefinition.setAssetId(assetRecoveryDefinitionData.getAssetId());
        assetRecoveryDefinition.setAssetRecoveryId(assetRecoveryDefinitionData.getAssetRecoveryId());
        assetRecoveryDefinition.setAssetStatus(AssetStatusType.valueOf(assetRecoveryDefinitionData.getAssetStatus()));
        assetRecoveryDefinition.setCreatedBy(createdBy);
        assetRecoveryDefinition.setCreationDate(assetRecoveryDefinitionData.getCreationDate());
        assetRecoveryDefinition.setRecoveryEmp(recoveredFrom);
        if (assetRecoveryDefinitionData.getRecoveredAmount() != null) {
            assetRecoveryDefinition.setRecoveredAmount(assetRecoveryDefinitionData.getRecoveredAmount().floatValue());
            assetRecoveryDefinition.setRecoveredBy(recoveredBy);
        }
        if (assetRecoveryDefinitionData.getRecoveryAmount() != null) {
            assetRecoveryDefinition.setRecoveryAmount(assetRecoveryDefinitionData.getRecoveryAmount().floatValue());
        }
        assetRecoveryDefinition.setRecoveryDate(assetRecoveryDefinitionData.getRecoveryDate());
        assetRecoveryDefinition.setRecoveryEmpId(assetRecoveryDefinitionData.getRecoveryEmpId());
        assetRecoveryDefinition.setSalaryDeductionDate(assetRecoveryDefinitionData.getSalaryDeductionDate());
        assetRecoveryDefinition.setRecoveryUnit(assetRecoveryDefinitionData.getRecoveryUnit());
        assetRecoveryDefinition.setRecoveryUnitType(assetRecoveryDefinitionData.getRecoveryUnitType());
        assetRecoveryDefinition.setRecoveryStatus(AssetRecoveryStatusType.valueOf(assetRecoveryDefinitionData.getRecoveryStatus()));
        assetRecoveryDefinition.setRecoveryType(assetRecoveryDefinitionData.getRecoveryType());
        assetRecoveryDefinition.setUnitId(assetRecoveryDefinitionData.getUnitId());
        assetRecoveryDefinition.setUnitName(unit.getName());
        assetRecoveryDefinition.setApprovedBy(approvedBy);
        assetRecoveryDefinition.setApprovalDate(assetRecoveryDefinitionData.getApprovalDate());
        assetRecoveryDefinition.setEventId(assetRecoveryDefinitionData.getEventId());
        assetRecoveryDefinition.setAuditedBy(auditedBy);
        return assetRecoveryDefinition;
    }

    public static AssetRecovery convert(AssetRecoveryData assetRecoveryData,
                                        AssetDefinition assetDefinition,
                                        StockEventDefinition stockEventDefinition,
                                        List<AssetRecoveryDetail> list,
                                        IdCodeName unit,
                                        ApprovalDetail approval,
                                        MasterDataCache masterCache
                                        ) {
        AssetRecovery assetRecovery = new AssetRecovery();
        assetRecovery.setRecoveryId(assetRecoveryData.getRecoveryId());
        assetRecovery.setAssetId(assetRecoveryData.getAssetId());
        assetRecovery.setAssetDefinition(assetDefinition);
        assetRecovery.setEventId(assetRecoveryData.getEventId());
        assetRecovery.setStockEventDefinition(stockEventDefinition);
        assetRecovery.setUnit(unit);
        assetRecovery.setAssetRecoveryDetail(list);
        assetRecovery.setAmountToRecover(assetRecoveryData.getAmountToRecover());
        assetRecovery.setRecoveryStatus(assetRecoveryData.getRecoveryStatus());
        assetRecovery.setAmountRecovered(assetRecoveryData.getAmountRecovered());
        assetRecovery.setEmployeeRecoveryStatus(assetRecoveryData.getEmployeeRecoveryStatus());
        assetRecovery.setEmployeeRecoveryAmount(assetRecoveryData.getEmployeeRecoveryAmount());
        assetRecovery.setInsuranceRecoveryStatus(assetRecoveryData.getInsuranceRecoveryStatus());
        assetRecovery.setInsuranceRecoveryAmount(assetRecoveryData.getInsuranceRecoveryAmount());
        assetRecovery.setWriteOffAmount(assetRecoveryData.getWriteOffAmount());
        assetRecovery.setCreatedBy(SCMUtil.generateIdCodeName(assetRecoveryData.getCreatedBy(),"",masterCache.getEmployeeBasicDetail(assetRecoveryData.getCreatedBy()).getName()));
        if(Objects.nonNull(approval) && Objects.nonNull(approval.getApprovedBy())){
            assetRecovery.setApprovedBy(SCMUtil.generateIdCodeName(approval.getApprovedBy(),"",masterCache.getEmployeeBasicDetail(approval.getApprovedBy()).getName()));
        }
        assetRecovery.setCreationDate(assetRecoveryData.getCreationDate());

        return assetRecovery;
    }

    public static AssetRecoveryDetail convert(AssetRecoveryDetailData assetRecoveryDetailData, IdCodeName recoveredFrom){
        AssetRecoveryDetail assetRecoveryDetail = new AssetRecoveryDetail();
        assetRecoveryDetail.setRecoveryDetailId(assetRecoveryDetailData.getRecoveryDetailId());
        assetRecoveryDetail.setRecoveryId(assetRecoveryDetailData.getRecoveryId());
        assetRecoveryDetail.setRecoveryType(assetRecoveryDetailData.getRecoveryType());
        assetRecoveryDetail.setRecoveryStatus(assetRecoveryDetailData.getRecoveryStatus());
        assetRecoveryDetail.setAmountRecovered(assetRecoveryDetailData.getAmountRecovered());
        if(Objects.nonNull(recoveredFrom)){
            assetRecoveryDetail.setRecoveredFrom(recoveredFrom);
        }
        return assetRecoveryDetail;
    }

    public static AssetRecoveryDetailData convert(AssetRecoveryDetail assetRecoveryDetail){
        AssetRecoveryDetailData assetRecoveryDetailData = new AssetRecoveryDetailData();
        assetRecoveryDetailData.setRecoveryId(assetRecoveryDetail.getRecoveryId());
        assetRecoveryDetailData.setRecoveryType(assetRecoveryDetail.getRecoveryType());
        assetRecoveryDetailData.setRecoveryStatus(assetRecoveryDetail.getRecoveryStatus());
        assetRecoveryDetailData.setAmountRecovered(assetRecoveryDetail.getAmountRecovered());
        assetRecoveryDetailData.setRecoveredFrom(assetRecoveryDetail.getRecoveredFrom().getId() != null ? assetRecoveryDetail.getRecoveredFrom().getId().toString() : assetRecoveryDetail.getRecoveredFrom().getName());

        return assetRecoveryDetailData;
    }

//    public static AssetRecoveryDefinition convert(AssetRecoveryDefinitionData assetRecoveryDefinitionData,
//                                                  IdCodeName createdBy, IdCodeName recoveredBy, IdCodeName recoveredFrom) {
//        AssetRecoveryDefinition assetRecoveryDefinition = factory.createAssetRecovery();
//        assetRecoveryDefinition.setAssetId(assetRecoveryDefinitionData.getAssetId());
//        assetRecoveryDefinition.setAssetRecoveryId(assetRecoveryDefinitionData.getAssetRecoveryId());
//        assetRecoveryDefinition.setAssetStatus(AssetStatusType.valueOf(assetRecoveryDefinitionData.getAssetStatus()));
//        assetRecoveryDefinition.setCreatedBy(createdBy);
//        assetRecoveryDefinition.setCreationDate(assetRecoveryDefinitionData.getCreationDate());
//        assetRecoveryDefinition.setRecoveryEmp(recoveredFrom);
//        if (assetRecoveryDefinitionData.getRecoveredAmount() != null) {
//            assetRecoveryDefinition.setRecoveredAmount(assetRecoveryDefinitionData.getRecoveredAmount().floatValue());
//            assetRecoveryDefinition.setRecoveredBy(recoveredBy);
//        }
//        assetRecoveryDefinition.setRecoveryAmount(assetRecoveryDefinitionData.getRecoveryAmount().floatValue());
//        assetRecoveryDefinition.setRecoveryDate(assetRecoveryDefinitionData.getRecoveryDate());
//        assetRecoveryDefinition.setRecoveryEmpId(assetRecoveryDefinitionData.getRecoveryEmpId());
//        assetRecoveryDefinition.setSalaryDeductionDate(assetRecoveryDefinitionData.getSalaryDeductionDate());
//        assetRecoveryDefinition.setRecoveryUnit(assetRecoveryDefinitionData.getRecoveryUnit());
//        assetRecoveryDefinition.setRecoveryUnitType(assetRecoveryDefinitionData.getRecoveryUnitType());
//        assetRecoveryDefinition.setRecoveryStatus(AssetRecoveryStatusType.valueOf(assetRecoveryDefinitionData.getRecoveryStatus()));
//        assetRecoveryDefinition.setRecoveryType(assetRecoveryDefinitionData.getRecoveryType());
//        assetRecoveryDefinition.setUnitId(assetRecoveryDefinitionData.getUnitId());
//        return assetRecoveryDefinition;
//    }

    public static AssetDefinition convert(AssetDefinitionData assetDefinitionData, IdCodeName createdBy,
                                          IdCodeName lastTagPrintedBy, IdCodeName lastTransferBy,
                                          BigDecimal currentValue, IdCodeName subCategory) {
        AssetDefinition assetDefinition = factory.createAssetDefinition();
        try{

            assetDefinition.setCurrentValue(currentValue.floatValue());
            BigDecimal onePlusPercentage = BigDecimal.ONE.add(assetDefinitionData.getTaxPercentage().divide(new BigDecimal(100)));
            BigDecimal currentValueWithoutTax = currentValue.divide(onePlusPercentage, 6, RoundingMode.HALF_EVEN);
            //  float floatValue = currentValueWithoutTax.floatValue();
            assetDefinition.setCurrentValueWithoutTax(currentValueWithoutTax.floatValue());
            assetDefinition.setCreationDate(assetDefinitionData.getCreationDate());
            assetDefinition.setActualEndDate(assetDefinitionData.getActualEndDate());
            assetDefinition.setAmcLastDate(assetDefinitionData.getAmcLastDate());
            assetDefinition.setAssetId(assetDefinitionData.getAssetId());
            assetDefinition.setAssetImageUrl(assetDefinitionData.getAssetImageUrl());
            assetDefinition.setAssetName(assetDefinitionData.getAssetName());
            if (assetDefinitionData.getDailyDepreciationRate() != null) {
                assetDefinition.setDailyDepreciationRate(assetDefinitionData.getDailyDepreciationRate().floatValue());
                assetDefinition.setDepreciationRatePa(assetDefinitionData.getDepreciationRatePa().floatValue());
                assetDefinition.setDepreciationResidue(assetDefinitionData.getDepreciationResidue().floatValue());
            }

            assetDefinition.setCreatedBy(createdBy);
            assetDefinition.setDepreciationStrategy(assetDefinitionData.getDepreciationStrategy());
            assetDefinition.setExpectedEndDate(assetDefinitionData.getExpectedEndDate());
            assetDefinition.setFirstOwnerId(assetDefinitionData.getFirstOwnerId());
            assetDefinition.setFirstOwnerType(assetDefinitionData.getFirstOwnerType());
            assetDefinition.setGrId(assetDefinitionData.getGrId());
            assetDefinition.setGrItemId(assetDefinitionData.getGrItemId());
            assetDefinition.setHasAMC(AppUtils.getStatus(assetDefinitionData.getHasAMC()));
            assetDefinition.setHasInsurance(AppUtils.getStatus(assetDefinitionData.getHasInsurance()));
            assetDefinition.setAssetStatus(AssetStatusType.valueOf(assetDefinitionData.getAssetStatus()));
            assetDefinition.setHasWarranty(AppUtils.getStatus(assetDefinitionData.getHasWarranty()));
            assetDefinition.setInsuranceLastDate(assetDefinitionData.getInsuranceLastDate());
            assetDefinition.setInventoryDate(assetDefinitionData.getInventoryDate());
            assetDefinition.setLastTagPrintDate(assetDefinitionData.getLastTagPrintDate());
            if (assetDefinitionData.getLastTagPrintedBy() != null) {
                assetDefinition.setLastTagPrintedBy(lastTagPrintedBy);
            }

            assetDefinition.setLastTransferDate(assetDefinitionData.getLastTransferDate());
            assetDefinition.setLastTransferedBy(lastTransferBy);
            assetDefinition.setLastTransferId(assetDefinitionData.getLastTransferId());
            assetDefinition.setLastTransferType(assetDefinitionData.getLastTransferType());
            assetDefinition.setLifeTimeInDays(assetDefinitionData.getLifeTimeInDays());
            if (assetDefinitionData.getLifeTimeType() != null) {
                assetDefinition.setLifeTimeType(LifeTimeType.valueOf(assetDefinitionData.getLifeTimeType()));
                assetDefinition.setLifeTimeValue(assetDefinitionData.getLifeTimeValue().floatValue());
            }

            assetDefinition.setOwnerId(assetDefinitionData.getOwnerId());
            assetDefinition.setOwnerType(assetDefinitionData.getOwnerType());
            assetDefinition.setPrice(assetDefinitionData.getPrice().floatValue());
            assetDefinition.setProcurementCost(assetDefinitionData.getProcurementCost().floatValue());
            assetDefinition.setProductId(assetDefinitionData.getProduct().getProductId());
            assetDefinition.setProfileId(assetDefinitionData.getProfileId());
            assetDefinition.setQuantity(assetDefinitionData.getQuantity());
            if (assetDefinitionData.getRealizedDepreciation() != null) {
                assetDefinition.setRealizedDepreciation(assetDefinitionData.getRealizedDepreciation().floatValue());
                assetDefinition.setRealizedDepreciationDate(assetDefinitionData.getRealizedDepreciationDate());
            }

            //assetDefinition.setSKUId(assetDefinitionData.getSKUId());
            assetDefinition.setSkuId(assetDefinitionData.getSKUId());
            assetDefinition.setStartDate(assetDefinitionData.getStartDate());
            assetDefinition.setTagPrintCount(assetDefinitionData.getTagPrintCount());
            if (assetDefinitionData.getTagType() != null) {
                assetDefinition.setTagType(TagType.valueOf(assetDefinitionData.getTagType()));
                assetDefinition.setTagValue(assetDefinitionData.getTagValue());
            }

            assetDefinition.setTax(assetDefinitionData.getTax().floatValue());
            assetDefinition.setUnitId(assetDefinitionData.getUnitId());
            assetDefinition.setUnitType(assetDefinitionData.getUnitType());
            assetDefinition.setVendorId(assetDefinitionData.getVendorId());
            assetDefinition.setVendorName(assetDefinitionData.getVendorName());
            assetDefinition.setWarrantyLastDate(assetDefinitionData.getWarrantyLastDate());

            /////////////////////////////////////
            assetDefinition.setGrossBlock(assetDefinitionData.getGrossBlock().floatValue());
            assetDefinition.setFixedValue(assetDefinitionData.getFixedValue().floatValue());
            assetDefinition.setRecoveryStatus(assetDefinitionData.getRecoveryStatus());
            assetDefinition.setRecoveryType(assetDefinitionData.getRecoveryType());
            if (assetDefinitionData.getRecoveryAmount() != null) {

                assetDefinition.setRecoveryAmount(assetDefinitionData.getRecoveryAmount().floatValue());

            }
            if (assetDefinitionData.getWriteOffType() != null) {
                assetDefinition.setWriteOffType(assetDefinitionData.getWriteOffType());
                assetDefinition.setWriteOffAmount(assetDefinitionData.getWriteOffAmount().floatValue());
            }
            assetDefinition.setTaxPercentage(assetDefinitionData.getTaxPercentage().floatValue());
            assetDefinition.setUniqueFieldName(assetDefinitionData.getUniqueFieldName());
            assetDefinition.setUniqueFieldValue(assetDefinitionData.getUniqueFieldValue());
            assetDefinition.setSubCategoryDefinition(subCategory);
            assetDefinition.setInTransit(Objects.nonNull(assetDefinitionData.getIsInTransit()) ?
                    AppUtils.getStatus(assetDefinitionData.getIsInTransit()) : false);
            assetDefinition.setTotalRecoverAmount(assetDefinitionData.getTotalRecoverAmount());
        }catch (Exception e){
            LOG.error("error on asset :{}{}",assetDefinitionData.getAssetId(),e);
        }
        return assetDefinition;

    }

    public static AssetDepreciationMappingData convert(AssetDefinitionData assetDefinitionData, AssetTransferMappingData assetTransferMappingData) {
        AssetDepreciationMappingData assetDepreciationMappingData = new AssetDepreciationMappingData();
        assetDepreciationMappingData.setEndDate(assetTransferMappingData.getEndDate());
        assetDepreciationMappingData.setUnitId(assetTransferMappingData.getUnitId());
        assetDepreciationMappingData.setStartDate(assetTransferMappingData.getStartDate());
        assetDepreciationMappingData.setOwnerId(assetTransferMappingData.getOwnerId());
        assetDepreciationMappingData.setAssetStatus(assetTransferMappingData.getAssetStatus());
        assetDepreciationMappingData.setAssetDefinition(assetDefinitionData);
        assetDepreciationMappingData.setSubCategoryId(assetDefinitionData.getProduct().getSubCategoryDefinition().getId());
        return assetDepreciationMappingData;
    }

    public static AssetTransferMappingData convert(AssetDefinitionData assetDefinitionData, Date startDate) {
        AssetTransferMappingData assetTransferMappingData = new AssetTransferMappingData();
        assetTransferMappingData.setAssetId(assetDefinitionData.getAssetId());
        assetTransferMappingData.setAssetStatus(assetDefinitionData.getAssetStatus());
        //assetTransferMappingData.setEndDate(assetDefinitionData.getExpectedEndDate());
        assetTransferMappingData.setOwnerId(assetDefinitionData.getOwnerId());
        assetTransferMappingData.setStartDate(startDate);
        assetTransferMappingData.setUnitId(assetDefinitionData.getUnitId());
        return assetTransferMappingData;
    }

    public static AssetDefinitionDataLog convert(AssetDefinitionData assetDefinitionData) {
        AssetDefinitionDataLog assetDefinitionDataLog = new AssetDefinitionDataLog();
        assetDefinitionDataLog.setAssetId(assetDefinitionData.getAssetId());
        assetDefinitionDataLog.setAssetStatus(assetDefinitionData.getAssetStatus());
        assetDefinitionDataLog.setAssetName(assetDefinitionData.getAssetName());
        assetDefinitionDataLog.setDailyDepreciationRate(assetDefinitionData.getDailyDepreciationRate());
        assetDefinitionDataLog.setDepreciationRatePa(assetDefinitionData.getDepreciationRatePa());
        assetDefinitionDataLog.setDepreciationResidue(assetDefinitionData.getDepreciationResidue());
        assetDefinitionDataLog.setDepreciationStrategy(assetDefinitionData.getDepreciationStrategy());
        assetDefinitionDataLog.setLastTagPrintDate(assetDefinitionData.getLastTagPrintDate());
        assetDefinitionDataLog.setLastTagPrintedBy(assetDefinitionData.getLastTagPrintedBy());
        assetDefinitionDataLog.setLastTransferDate(assetDefinitionData.getLastTransferDate());
        assetDefinitionDataLog.setLastTransferedBy(assetDefinitionData.getLastTransferedBy());
        assetDefinitionDataLog.setLastTransferId(assetDefinitionData.getLastTransferId());
        assetDefinitionDataLog.setLastTransferType(assetDefinitionData.getLastTransferType());
        assetDefinitionDataLog.setOwnerId(assetDefinitionData.getOwnerId());
        assetDefinitionDataLog.setOwnerType(assetDefinitionData.getOwnerType());
        assetDefinitionDataLog.setRealizedDepreciation(assetDefinitionData.getRealizedDepreciation());
        assetDefinitionDataLog.setRealizedDepreciationDate(assetDefinitionData.getRealizedDepreciationDate());
        assetDefinitionDataLog.setTagPrintCount(assetDefinitionData.getTagPrintCount());
        assetDefinitionDataLog.setTagType(assetDefinitionData.getTagType());
        assetDefinitionDataLog.setTagValue(assetDefinitionData.getTagValue());
        assetDefinitionDataLog.setUnitId(assetDefinitionData.getUnitId());
        assetDefinitionDataLog.setUnitType(assetDefinitionData.getUnitType());
        assetDefinitionDataLog.setUpdatedBy(assetDefinitionData.getCreatedBy());
        return assetDefinitionDataLog;
    }

    public static StockEventDefinitionData convert(StockEventDefinition stockEventDefinition) {
        StockEventDefinitionData stockEventDefinitionData = new StockEventDefinitionData();
        if (stockEventDefinition.getAuditedBy() != null) {
            stockEventDefinitionData.setAuditedBy(stockEventDefinition.getAuditedBy().getId());
        }

        stockEventDefinitionData.setEventId(stockEventDefinition.getEventId());
        stockEventDefinitionData.setEventCreationDate(stockEventDefinition.getEventCreationDate());
        stockEventDefinitionData.setEventStatus(stockEventDefinition.getEventStatus().value());
        stockEventDefinitionData.setEventType(stockEventDefinition.getEventType());
        stockEventDefinitionData.setInitiatedBy(stockEventDefinition.getInitiatedBy().getId());
        stockEventDefinitionData.setUnitId(stockEventDefinition.getUnitId());
        stockEventDefinitionData.setUnitType(stockEventDefinition.getUnitType());
        stockEventDefinitionData.setSubType(stockEventDefinition.getSubType());
        stockEventDefinitionData.setParentId(stockEventDefinition.getParentId());
        stockEventDefinitionData.setSubCategory(stockEventDefinition.getSubCategory());
        stockEventDefinitionData.setSplit(stockEventDefinition.getIsSplit());
        stockEventDefinitionData.setDeviceInfo(stockEventDefinition.getDeviceInfo());
        return stockEventDefinitionData;
    }

    public static GatepassItemAssetMapping convert(GatepassItemAssetMappingData gatepassItemAssetMappingData) {
        GatepassItemAssetMapping gatepassItemAssetMapping = factory.getGatepassItemAssetMapping();
        gatepassItemAssetMapping.setGatePassType(GatepassOperationType.valueOf(gatepassItemAssetMappingData.getGatePassType()));
        gatepassItemAssetMapping.setGatePassItemId(gatepassItemAssetMappingData.getGatePassItemId());
        gatepassItemAssetMapping.setGatePassItemAssetId(gatepassItemAssetMappingData.getGatePassItemAssetId());
        gatepassItemAssetMapping.setAssetId(gatepassItemAssetMappingData.getAssetId());
        gatepassItemAssetMapping.setAssetTagValue(gatepassItemAssetMappingData.getAssetTagValue());
        gatepassItemAssetMapping.setGatePassId(gatepassItemAssetMappingData.getGatePassId());
        gatepassItemAssetMapping.setIsReturned(gatepassItemAssetMappingData.getIsReturned());
        return gatepassItemAssetMapping;
    }

    public static GatepassItemAssetMappingData convert(GatepassItemAssetMapping gatepassItemAssetMapping) {
        GatepassItemAssetMappingData gatepassItemAssetMappingData = new GatepassItemAssetMappingData();
        gatepassItemAssetMappingData.setAssetId(gatepassItemAssetMapping.getAssetId());
        gatepassItemAssetMappingData.setAssetTagValue(gatepassItemAssetMapping.getAssetTagValue());
        gatepassItemAssetMappingData.setGatePassId(gatepassItemAssetMapping.getGatePassId());
        gatepassItemAssetMappingData.setGatePassItemAssetId(gatepassItemAssetMapping.getGatePassItemAssetId());
        gatepassItemAssetMappingData.setGatePassItemId(gatepassItemAssetMapping.getGatePassItemId());
        gatepassItemAssetMappingData.setGatePassType(gatepassItemAssetMapping.getGatePassType().toString());
        return gatepassItemAssetMappingData;
    }

    public static StockEventDefinition convert(StockEventDefinitionData stockEventDefinitionData, IdCodeName createdBy, IdCodeName auditedBy) {
        StockEventDefinition stockEventDefinition = factory.createStockEventDefinition();
        stockEventDefinition.setEventCreationDate(stockEventDefinitionData.getEventCreationDate());
        if (stockEventDefinitionData.getAuditedBy() != null) {
            stockEventDefinition.setAuditedBy(auditedBy);
        }

        stockEventDefinition.setEventId(stockEventDefinitionData.getEventId());
        stockEventDefinition.setEventStatus(StockEventStatusType.valueOf(stockEventDefinitionData.getEventStatus()));
        stockEventDefinition.setEventType(stockEventDefinitionData.getEventType());
        stockEventDefinition.setInitiatedBy(createdBy);
        stockEventDefinition.setUnitId(stockEventDefinitionData.getUnitId());
        stockEventDefinition.setUnitType(stockEventDefinitionData.getUnitType());
        stockEventDefinition.setSubType(stockEventDefinitionData.getSubType());
        stockEventDefinition.setReceivingUnitID(stockEventDefinitionData.getReceivingUnitId());
        stockEventDefinition.setBudgetType(stockEventDefinitionData.getBudgetType());
        stockEventDefinition.setRoId(stockEventDefinitionData.getRoId());
        stockEventDefinition.setToId(stockEventDefinitionData.getToId());
        stockEventDefinition.setParentId(stockEventDefinitionData.getParentId());
        stockEventDefinition.setSubCategory(stockEventDefinitionData.getSubCategory());
        if (Objects.nonNull(stockEventDefinitionData.getSplit())) {
            stockEventDefinition.setIsSplit(stockEventDefinitionData.getSplit());
        }
        if (Objects.nonNull(stockEventDefinitionData.getDeviceInfo())) {
            stockEventDefinition.setDeviceInfo(stockEventDefinitionData.getDeviceInfo());
        }
        return stockEventDefinition;
    }

    public static AssetScrappedMappingData convert(int assetId, int unitId, BigDecimal scrappedAmount, Date scrappedDate) {
        AssetScrappedMappingData assetScrappedMappingData = new AssetScrappedMappingData();
        assetScrappedMappingData.setAssetId(assetId);
        assetScrappedMappingData.setScrappedAmount(scrappedAmount);
        assetScrappedMappingData.setScrappedDate(scrappedDate);
        assetScrappedMappingData.setUnitId(unitId);
        return assetScrappedMappingData;
    }

    public static StockEventAssetMappingDefinitionData convert(StockEventAssetMappingDefinition stockEventAssetMappingDefinition, AssetDefinition assetDefinition) {
        StockEventAssetMappingDefinitionData stockEventAssetMappingDefinitionData = new StockEventAssetMappingDefinitionData();
        stockEventAssetMappingDefinitionData.setAssetId(stockEventAssetMappingDefinition.getAssetId());
        stockEventAssetMappingDefinitionData.setAssetStatus(assetDefinition.getAssetStatus().value());
        if (stockEventAssetMappingDefinition.getAuditDate() != null) {
            stockEventAssetMappingDefinitionData.setAuditDate(stockEventAssetMappingDefinition.getAuditDate());
            stockEventAssetMappingDefinitionData.setAuditedBy(stockEventAssetMappingDefinition.getAuditedBy().getId());
            stockEventAssetMappingDefinitionData.setAuditStatus(stockEventAssetMappingDefinition.getAuditStatus());
        }

        stockEventAssetMappingDefinitionData.setCreationDate(stockEventAssetMappingDefinition.getCreationDate());
        stockEventAssetMappingDefinitionData.setEventAssetMappingId(stockEventAssetMappingDefinition.getEventAssetMappingId());
        stockEventAssetMappingDefinitionData.setEventId(stockEventAssetMappingDefinition.getEventId());
        stockEventAssetMappingDefinitionData.setUnitId(stockEventAssetMappingDefinition.getUnitId());
        stockEventAssetMappingDefinitionData.setManualChecked(AppUtils.setStatus(stockEventAssetMappingDefinition.getManualChecked()));
        stockEventAssetMappingDefinitionData.setCreationDate(SCMUtil.getCurrentTimestamp());
        return stockEventAssetMappingDefinitionData;
    }

    public static StockEventAssetMappingDefinition convert(StockEventAssetMappingDefinitionData stockEventAssetMappingDefinitionData,
            Map<Integer,AssetDefinition> assetMap ,IdCodeName auditedBy ,SCMCache scmCache) {
        StockEventAssetMappingDefinition stockEventAssetMappingDefinition = factory.createStockEventAssetMappingDefinition();
        stockEventAssetMappingDefinition.setCreationDate(stockEventAssetMappingDefinitionData.getCreationDate());
        stockEventAssetMappingDefinition.setAssetId(stockEventAssetMappingDefinitionData.getAssetId());
        if(Objects.nonNull(assetMap)){
            AssetDefinition asset = assetMap.get(stockEventAssetMappingDefinition.getAssetId());
            ProductDefinition productDefinition = scmCache.getProductDefinition(asset.getProductId());
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(asset.getSkuId());
            stockEventAssetMappingDefinition.setAssetName(asset.getAssetName());
            stockEventAssetMappingDefinition.setAssetTagValue(asset.getTagValue());
            stockEventAssetMappingDefinition.setAssetStatus(asset.getAssetStatus());
            stockEventAssetMappingDefinition.setSubCategory(asset.getSubCategoryDefinition().getName());
            stockEventAssetMappingDefinition.setPrice(BigDecimal.valueOf(asset.getCurrentValueWithoutTax()));
            stockEventAssetMappingDefinition.setTax(BigDecimal.valueOf(asset.getTaxPercentage()));
            stockEventAssetMappingDefinition.setImageUrl(scmCache.getSkuDefinition(asset.getSkuId()).getSkuImage());
            stockEventAssetMappingDefinition.setProductId(productDefinition.getProductId());
            stockEventAssetMappingDefinition.setProductName(productDefinition.getProductName());
            stockEventAssetMappingDefinition.setSkuName(skuDefinition.getSkuName());
        }
        stockEventAssetMappingDefinition.setAssetStatus(AssetStatusType.valueOf(stockEventAssetMappingDefinitionData.getAssetStatus()));
        if (stockEventAssetMappingDefinitionData.getAuditDate() != null) {
            stockEventAssetMappingDefinition.setAuditDate(stockEventAssetMappingDefinitionData.getAuditDate());
            stockEventAssetMappingDefinition.setAuditedBy(auditedBy);
            stockEventAssetMappingDefinition.setAuditStatus(stockEventAssetMappingDefinitionData.getAuditStatus());
        }

        stockEventAssetMappingDefinition.setEventAssetMappingId(stockEventAssetMappingDefinitionData.getEventAssetMappingId());
        stockEventAssetMappingDefinition.setEventId(stockEventAssetMappingDefinitionData.getEventId());
        stockEventAssetMappingDefinition.setUnitId(stockEventAssetMappingDefinitionData.getUnitId());
        stockEventAssetMappingDefinition.setNonScannable(AppUtils.getStatus(stockEventAssetMappingDefinitionData.getManualChecked()));
        stockEventAssetMappingDefinition.setManualChecked(AppUtils.getStatus(stockEventAssetMappingDefinitionData.getManualChecked()));
        if(!stockEventAssetMappingDefinitionData.getAssetStatus().equalsIgnoreCase(AssetStatusType.PENDING_LOST.value())){
            stockEventAssetMappingDefinition.setFound(true);
        }else{
            stockEventAssetMappingDefinition.setFound(false);
        }

        return stockEventAssetMappingDefinition;

    }

    public static EntityAttributeValueMappingData convert(EntityAttributeValueMapping entityAttributeValueMapping) {
        EntityAttributeValueMappingData entityAttributeValueMappingData = new EntityAttributeValueMappingData();
        entityAttributeValueMappingData.setAttributeId(entityAttributeValueMapping.getAttributeId());
        entityAttributeValueMappingData.setAttributeValueId(entityAttributeValueMapping.getAttributeValueId());
        entityAttributeValueMappingData.setCreatedBy(entityAttributeValueMapping.getCreatedBy().getId());
        entityAttributeValueMappingData.setCreationDate(entityAttributeValueMapping.getCreationDate());
        entityAttributeValueMappingData.setEntityId(entityAttributeValueMapping.getEntityId());
        entityAttributeValueMappingData.setEntityType(entityAttributeValueMapping.getEntityType());
        entityAttributeValueMappingData.setProfileId(entityAttributeValueMapping.getProfileId());
        entityAttributeValueMappingData.setProfileAttributeMappingId(entityAttributeValueMapping.getProfileAttributeMappingId());
        entityAttributeValueMappingData.setStatus(entityAttributeValueMapping.getStatus());
        return entityAttributeValueMappingData;
    }

    public static EntityAttributeValueMapping convert(EntityAttributeValueMappingData entityAttributeValueMappingData,
                                                      IdCodeName createdBy, String attributeName) {
        EntityAttributeValueMapping entityAttributeValueMapping = factory.createEntityAttributeValueMapping();
        entityAttributeValueMapping.setAttributeName(attributeName);
        entityAttributeValueMapping.setCreationDate(entityAttributeValueMappingData.getCreationDate());
        entityAttributeValueMapping.setAttributeId(entityAttributeValueMappingData.getAttributeId());
        entityAttributeValueMapping.setAttributeValueId(entityAttributeValueMappingData.getAttributeValueId());
        entityAttributeValueMapping.setCreatedBy(createdBy);
        entityAttributeValueMapping.setEntityAttributeValueMappingId(entityAttributeValueMappingData.getEntityAttributeValueMappingId());
        entityAttributeValueMapping.setEntityId(entityAttributeValueMappingData.getEntityId());
        entityAttributeValueMapping.setEntityType(entityAttributeValueMappingData.getEntityType());
        entityAttributeValueMapping.setProfileAttributeMappingId(entityAttributeValueMappingData.getProfileAttributeMappingId());
        entityAttributeValueMapping.setProfileId(entityAttributeValueMappingData.getProfileId());
        entityAttributeValueMapping.setStatus(entityAttributeValueMappingData.getStatus());
        return entityAttributeValueMapping;
    }

    public static ProductDefinitionLogs convertToLogData(ProductDefinition productDefinition,
                                                CategoryDefinition categoryDefinition, SubCategoryDefinition subCategoryDefinition) {
        ProductDefinitionLogs productDefinitionLogs = new ProductDefinitionLogs();
        productDefinitionLogs.setCategoryId(convert(categoryDefinition).getCategoryId());
        if (subCategoryDefinition != null) {
            productDefinitionLogs.setSubCategoryId(convert(subCategoryDefinition).getId());
        }
        productDefinitionLogs.setCreatedBy(productDefinition.getCreatedBy().getId());
        productDefinitionLogs.setCreationDate(productDefinition.getCreationDate());
        productDefinitionLogs.setHasCase(SCMUtil.setStatus(productDefinition.isHasCase()));
        productDefinitionLogs.setHasInner(SCMUtil.setStatus(productDefinition.isHasInner()));
        productDefinitionLogs.setRecipeRequired(SCMUtil.setStatus(productDefinition.isRecipeRequired()));
        productDefinitionLogs.setIsBulkGrAllowed(SCMUtil.setStatus(productDefinition.isBulkGRAllowed()));
        productDefinitionLogs.setProductCode(productDefinition.getProductCode());
        productDefinitionLogs.setProductDescription(productDefinition.getProductDescription());
        productDefinitionLogs.setProductId(productDefinition.getProductId());
        productDefinitionLogs.setProductName(productDefinition.getProductName());
        productDefinitionLogs.setProductStatus(productDefinition.getProductStatus().name());
        productDefinitionLogs.setShelfLifeInDays(productDefinition.getShelfLifeInDays());
        productDefinitionLogs.setStockKeepingFrequency(productDefinition.getStockKeepingFrequency().name());
        productDefinitionLogs.setSupportsLooseOrdering(SCMUtil.setStatus(productDefinition.isSupportsLooseOrdering()));
        productDefinitionLogs.setUnitOfMeasure(productDefinition.getUnitOfMeasure());
        productDefinitionLogs.setProductImage(productDefinition.getProductImage());
        productDefinitionLogs.setVariantLevelOrdering(SCMUtil.setStatus(productDefinition.isVariantLevelOrdering()));
        productDefinitionLogs.setVarianceType(productDefinition.getVarianceType().name());
        if (Objects.nonNull(productDefinition.getKitchenVarianceType())) {
            productDefinitionLogs.setKitchenVarianceType(productDefinition.getKitchenVarianceType().name());
        }
        productDefinitionLogs
                .setSupportsSpecializedOrdering(SCMUtil.setStatus(productDefinition.isSupportsSpecialOrdering()));
        productDefinitionLogs.setTaxCategoryCode(productDefinition.getTaxCode());
        productDefinitionLogs
                .setAvailableAtCafe(productDefinition.isAvailableAtCafe() ? SCMServiceConstants.SCM_CONSTANT_YES
                        : SCMServiceConstants.SCM_CONSTANT_NO);
        productDefinitionLogs.setInterCafeTransfer(productDefinition.isInterCafeTransfer() ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
        productDefinitionLogs.setAvailableForCafeInventory(
                productDefinition.isAvailableForCafeInventory() ? SCMServiceConstants.SCM_CONSTANT_YES
                        : SCMServiceConstants.SCM_CONSTANT_NO);

        productDefinitionLogs.setFulfillmentType(productDefinition.getFulfillmentType().name());
        if (FulfillmentType.DERIVED.equals(productDefinition.getFulfillmentType())) {
            productDefinitionLogs.setDefaultFulfillmentType(productDefinition.getDefaultFulfillmentType().name());
        }
        productDefinitionLogs.setParticipatesInRecipe(SCMUtil.setStatus(productDefinition.isParticipatesInRecipe()));
        productDefinitionLogs.setParticipatesInCafeRecipe(SCMUtil.setStatus(productDefinition.isParticipatesInCafeRecipe()));
        productDefinitionLogs.setAssetOrdering(SCMUtil.setStatus(productDefinition.getAssetOrdering()));
        productDefinitionLogs
                .setNegotiatedUnitPrice(SCMUtil.convertToBigDecimal(productDefinition.getNegotiatedUnitPrice()));
        productDefinitionLogs.setUnitPrice(SCMUtil.convertToBigDecimal(productDefinition.getUnitPrice()));
        productDefinitionLogs.setAutoProduction(SCMUtil.setStatus(productDefinition.isAutoProduction()));
        productDefinitionLogs.setParticipatesInPnl(SCMUtil.setStatus(productDefinition.isParticipatesInPnl()));
        return productDefinitionLogs;

    }

    public static ProductDefinitionData convert(ProductDefinition productDefinition,
                                                CategoryDefinition categoryDefinition, SubCategoryDefinition subCategoryDefinition) {
        ProductDefinitionData productDefinitionData = new ProductDefinitionData();
        productDefinitionData.setCategoryDefinition(convert(categoryDefinition));
        if (subCategoryDefinition != null) {
            productDefinitionData.setSubCategoryDefinition(convert(subCategoryDefinition));
        }
        productDefinitionData.setCreatedBy(productDefinition.getCreatedBy().getId());
        productDefinitionData.setCreationDate(productDefinition.getCreationDate());
        productDefinitionData.setHasCase(SCMUtil.setStatus(productDefinition.isHasCase()));
        productDefinitionData.setHasInner(SCMUtil.setStatus(productDefinition.isHasInner()));
        productDefinitionData.setRecipeRequired(SCMUtil.setStatus(productDefinition.isRecipeRequired()));
        productDefinitionData.setIsBulkGRAllowed(SCMUtil.setStatus(productDefinition.isBulkGRAllowed()));
        productDefinitionData.setProductCode(productDefinition.getProductCode());
        productDefinitionData.setProductDescription(productDefinition.getProductDescription());
        productDefinitionData.setProductId(productDefinition.getProductId());
        productDefinitionData.setProductName(productDefinition.getProductName());
        productDefinitionData.setProductStatus(productDefinition.getProductStatus().name());
        productDefinitionData.setShelfLifeInDays(productDefinition.getShelfLifeInDays());
        productDefinitionData.setStockKeepingFrequency(productDefinition.getStockKeepingFrequency().name());
        productDefinitionData.setSupportsLooseOrdering(SCMUtil.setStatus(productDefinition.isSupportsLooseOrdering()));
        productDefinitionData.setUnitOfMeasure(productDefinition.getUnitOfMeasure());
        productDefinitionData.setProductImage(productDefinition.getProductImage());
        productDefinitionData.setVariantLevelOrdering(SCMUtil.setStatus(productDefinition.isVariantLevelOrdering()));
        productDefinitionData.setVarianceType(productDefinition.getVarianceType().name());
        if (Objects.nonNull(productDefinition.getKitchenVarianceType())) {
            productDefinitionData.setKitchenVarianceType(productDefinition.getKitchenVarianceType().name());
        }
        productDefinitionData
                .setSupportsSpecializedOrdering(SCMUtil.setStatus(productDefinition.isSupportsSpecialOrdering()));
        productDefinitionData.setTaxCategoryCode(productDefinition.getTaxCode());
        productDefinitionData
                .setAvailableForCafe(productDefinition.isAvailableAtCafe() ? SCMServiceConstants.SCM_CONSTANT_YES
                        : SCMServiceConstants.SCM_CONSTANT_NO);
        productDefinitionData.setInterCafeTransfer(productDefinition.isInterCafeTransfer() ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
        productDefinitionData.setAvailableForCafeInventory(
                productDefinition.isAvailableForCafeInventory() ? SCMServiceConstants.SCM_CONSTANT_YES
                        : SCMServiceConstants.SCM_CONSTANT_NO);

        productDefinitionData.setFulfillmentType(productDefinition.getFulfillmentType().name());
        if (FulfillmentType.DERIVED.equals(productDefinition.getFulfillmentType())) {
            productDefinitionData.setDefaultFulfillmentType(productDefinition.getDefaultFulfillmentType().name());
        }
        productDefinitionData.setParticipatesInRecipe(SCMUtil.setStatus(productDefinition.isParticipatesInRecipe()));
        productDefinitionData.setParticipatesInCafeRecipe(SCMUtil.setStatus(productDefinition.isParticipatesInCafeRecipe()));
        productDefinitionData.setAssetOrdering(SCMUtil.setStatus(productDefinition.getAssetOrdering()));
        productDefinitionData
                .setNegotiatedUnitPrice(SCMUtil.convertToBigDecimal(productDefinition.getNegotiatedUnitPrice()));
        productDefinitionData.setUnitPrice(SCMUtil.convertToBigDecimal(productDefinition.getUnitPrice()));
        productDefinitionData.setAutoProduction(SCMUtil.setStatus(productDefinition.isAutoProduction()));
        productDefinitionData.setParticipatesInPnl(SCMUtil.setStatus(productDefinition.isParticipatesInPnl()));
        productDefinitionData.setBrandId(productDefinition.getBrandId());
        productDefinitionData.setCompanyId(productDefinition.getCompanyId());
        productDefinitionData.setApprovalDocumentId(productDefinition.getApprovalDocumentId());
        if (Objects.nonNull(productDefinition.getProductType())) {
            productDefinitionData.setProductType(productDefinition.getProductType());
        }
        return productDefinitionData;
    }

    public static UserProductCreationRequestData converter(ProductDefinition productDefinition,
                                                         CategoryDefinition categoryDefinition, SubCategoryDefinition subCategoryDefinition) {
        UserProductCreationRequestData productDefinitionData = new UserProductCreationRequestData();
        productDefinitionData.setCategoryDefinition(convert(categoryDefinition));
        if (subCategoryDefinition != null) {
            productDefinitionData.setSubCategoryDefinition(convert(subCategoryDefinition));
        }
        productDefinitionData.setCreatedBy(productDefinition.getCreatedBy().getId());
        productDefinitionData.setCreationDate(productDefinition.getCreationDate());
        productDefinitionData.setHasCase(SCMUtil.setStatus(productDefinition.isHasCase()));
        productDefinitionData.setHasInner(SCMUtil.setStatus(productDefinition.isHasInner()));
        productDefinitionData.setRecipeRequired(SCMUtil.setStatus(productDefinition.isRecipeRequired()));
        productDefinitionData.setIsBulkGRAllowed(SCMUtil.setStatus(productDefinition.isBulkGRAllowed()));
        productDefinitionData.setProductCode(productDefinition.getProductCode());
        productDefinitionData.setProductDescription(productDefinition.getProductDescription());
        productDefinitionData.setProductId(productDefinition.getProductId());
        productDefinitionData.setProductName(productDefinition.getProductName());
        productDefinitionData.setProductStatus(productDefinition.getProductStatus().name());
        productDefinitionData.setShelfLifeInDays(productDefinition.getShelfLifeInDays());
        productDefinitionData.setStockKeepingFrequency(productDefinition.getStockKeepingFrequency().name());
        productDefinitionData.setSupportsLooseOrdering(SCMUtil.setStatus(productDefinition.isSupportsLooseOrdering()));
        productDefinitionData.setUnitOfMeasure(productDefinition.getUnitOfMeasure());
        productDefinitionData.setProductImage(productDefinition.getProductImage());
        productDefinitionData.setVariantLevelOrdering(SCMUtil.setStatus(productDefinition.isVariantLevelOrdering()));
        productDefinitionData.setVarianceType(productDefinition.getVarianceType().name());
        productDefinitionData.setHodId(productDefinition.getHodId());
        productDefinitionData.setHodId(productDefinition.getHodId());
        if (Objects.nonNull(productDefinition.getKitchenVarianceType())) {
            productDefinitionData.setKitchenVarianceType(productDefinition.getKitchenVarianceType().name());
        }
        productDefinitionData
                .setSupportsSpecializedOrdering(SCMUtil.setStatus(productDefinition.isSupportsSpecialOrdering()));
        productDefinitionData.setTaxCategoryCode(productDefinition.getTaxCode());
        productDefinitionData
                .setAvailableForCafe(productDefinition.isAvailableAtCafe() ? SCMServiceConstants.SCM_CONSTANT_YES
                        : SCMServiceConstants.SCM_CONSTANT_NO);
        productDefinitionData.setInterCafeTransfer(productDefinition.isInterCafeTransfer() ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
        productDefinitionData.setAvailableForCafeInventory(
                productDefinition.isAvailableForCafeInventory() ? SCMServiceConstants.SCM_CONSTANT_YES
                        : SCMServiceConstants.SCM_CONSTANT_NO);

        productDefinitionData.setFulfillmentType(productDefinition.getFulfillmentType().name());
        if (FulfillmentType.DERIVED.equals(productDefinition.getFulfillmentType())) {
            productDefinitionData.setDefaultFulfillmentType(productDefinition.getDefaultFulfillmentType().name());
        }
        productDefinitionData.setParticipatesInRecipe(SCMUtil.setStatus(productDefinition.isParticipatesInRecipe()));
        productDefinitionData.setParticipatesInCafeRecipe(SCMUtil.setStatus(productDefinition.isParticipatesInCafeRecipe()));
        productDefinitionData.setAssetOrdering(SCMUtil.setStatus(productDefinition.getAssetOrdering()));
        productDefinitionData
                .setNegotiatedUnitPrice(SCMUtil.convertToBigDecimal(productDefinition.getNegotiatedUnitPrice()));
        productDefinitionData.setUnitPrice(SCMUtil.convertToBigDecimal(productDefinition.getUnitPrice()));
        productDefinitionData.setAutoProduction(SCMUtil.setStatus(productDefinition.isAutoProduction()));
        productDefinitionData.setParticipatesInPnl(SCMUtil.setStatus(productDefinition.isParticipatesInPnl()));
        productDefinitionData.setDivisionId(productDefinition.getDivisionId());
        productDefinitionData.setDepartmentId(productDefinition.getDepartmentId());
        productDefinitionData.setClassificationId(productDefinition.getClassificationId());
        productDefinitionData.setSubClassificationId(productDefinition.getSubClassificationId());
        productDefinitionData.setBrandId(productDefinition.getBrandId());
        productDefinitionData.setCompanyId(productDefinition.getCompanyId());
        productDefinitionData.setApprovalDocumentId(productDefinition.getApprovalDocumentId());
        if (Objects.nonNull(productDefinition.getProductType())) {
            productDefinitionData.setProductType(productDefinition.getProductType());
        }
        return productDefinitionData;
    }

    public static List<ProductFulfillmentTypeData> convert(List<ProductFulfillmentType> productFulfillmentTypes,
                                                           ProductDefinitionData productDefinitionData) {
        List<ProductFulfillmentTypeData> productFulfillmentTypeDatas = new ArrayList<ProductFulfillmentTypeData>();
        for (ProductFulfillmentType productFulfillmentType : productFulfillmentTypes) {
            ProductFulfillmentTypeData productFulfillmentTypeData = new ProductFulfillmentTypeData();
            productFulfillmentTypeData.setId(productFulfillmentType.getId());
            productFulfillmentTypeData.setFulfillmentType(productFulfillmentType.getFulfillmentType().value());
            productFulfillmentTypeData.setStatus(productFulfillmentType.getStatus().value());
            productFulfillmentTypeData.setProductDefinitionData(productDefinitionData);
            productFulfillmentTypeDatas.add(productFulfillmentTypeData);
        }
        return productFulfillmentTypeDatas;
    }

    public static List<ProductFulfillmentType> convertProductFulfillmentType(
            List<ProductFulfillmentTypeData> productFulfillmentTypeDatas) {
        List<ProductFulfillmentType> productFulfillmentTypes = new ArrayList<ProductFulfillmentType>();
        for (ProductFulfillmentTypeData productFulfillmentTypeData : productFulfillmentTypeDatas) {
            ProductFulfillmentType productFulfillmentType = new ProductFulfillmentType();
            productFulfillmentType
                    .setFulfillmentType(FulfillmentType.valueOf(productFulfillmentTypeData.getFulfillmentType()));
            productFulfillmentType.setId(productFulfillmentTypeData.getId());
            productFulfillmentType.setStatus(SwitchStatus.valueOf(productFulfillmentTypeData.getStatus()));
            productFulfillmentType.setProductId(productFulfillmentTypeData.getProductDefinitionData().getProductId());
            productFulfillmentTypes.add(productFulfillmentType);
        }
        return productFulfillmentTypes;
    }

    public static SkuDefinition convert(SkuDefinitionData skuDefinitionData, IdCodeName createdBy) {
        SkuDefinition skuDefinition = factory.createSkuDefinition();
        skuDefinition.setCreatedBy(createdBy);
        skuDefinition.setCreationDate(skuDefinitionData.getCreationDate());
        skuDefinition.setHasCase(SCMUtil.getStatus(skuDefinitionData.getHasCase()));
        skuDefinition.setHasInner(SCMUtil.getStatus(skuDefinitionData.getHasInner()));
        IdCodeName product = SCMUtil.generateIdCodeName(skuDefinitionData.getLinkedProduct().getProductId(),
                skuDefinitionData.getLinkedProduct().getTaxCategoryCode(),
                skuDefinitionData.getLinkedProduct().getProductName());
        skuDefinition.setLinkedProduct(product);
        skuDefinition.setShelfLifeInDays(skuDefinitionData.getShelfLifeInDays());
        skuDefinition.setSkuDescription(skuDefinitionData.getSkuDescription());
        skuDefinition.setSkuId(skuDefinitionData.getSkuId());
        skuDefinition.setSkuName(skuDefinitionData.getSkuName());
        skuDefinition.setSkuStatus(SwitchStatus.fromValue(skuDefinitionData.getSkuStatus()));
        skuDefinition.setSupportsLooseOrdering(SCMUtil.getStatus(skuDefinitionData.getSupportsLooseOrdering()));
        skuDefinition.setUnitOfMeasure(skuDefinitionData.getUnitOfMeasure());
        skuDefinition.setPriceLastUpdated(skuDefinitionData.getPriceLastUpdated());
        skuDefinition.setTorqusSkuName(skuDefinitionData.getTorqusSKU());
        skuDefinition.setIsDefault(SCMUtil.getStatus(skuDefinitionData.getIsDefault()));
        skuDefinition.setInventoryList(skuDefinitionData.getInventoryList());
        skuDefinition.setUnitPrice(SCMUtil.convertToBigDecimal(skuDefinitionData.getUnitPrice()).floatValue());
        skuDefinition.setNegotiatedUnitPrice(
                SCMUtil.convertToBigDecimal(skuDefinitionData.getNegotiatedUnitPrice()).floatValue());
        skuDefinition.setUnitPrice(SCMUtil.convertToBigDecimal(skuDefinitionData.getUnitPrice()).floatValue());
        skuDefinition.setSkuCode(skuDefinitionData.getSkuCode());
        if (skuDefinitionData.getSkuImage() != null) {
            skuDefinition.setSkuImage(skuDefinitionData.getSkuImage());
        }
        skuDefinition.setTaxCode(skuDefinitionData.getTaxCategoryCode());
        if (Objects.nonNull(skuDefinitionData.getVoDisContinuedFrom())) {
            skuDefinition.setVoDisContinuedFrom(skuDefinitionData.getVoDisContinuedFrom());
        }
        if (Objects.nonNull(skuDefinitionData.getRoDisContinuedFrom())) {
            skuDefinition.setRoDisContinuedFrom(skuDefinitionData.getRoDisContinuedFrom());
        }
        if (Objects.nonNull(skuDefinitionData.getIsBranded())) {
            skuDefinition.setIsBranded(SCMUtil.getStatus(skuDefinitionData.getIsBranded()));
        }
        skuDefinition.setBrand(skuDefinitionData.getBrand());

        return skuDefinition;
    }

    public static SkuDefinition convert(UserSkuCreationRequestData requestData, IdCodeName createdBy) {
        SkuDefinition skuDefinition = factory.createSkuDefinition();
        skuDefinition.setCreatedBy(createdBy);
        skuDefinition.setCreationDate(requestData.getCreationDate());
        skuDefinition.setHasCase(SCMUtil.getStatus(requestData.getHasCase()));
        skuDefinition.setHasInner(SCMUtil.getStatus(requestData.getHasInner()));
        IdCodeName product = SCMUtil.generateIdCodeName(requestData.getLinkedProduct().getProductId(),
                requestData.getLinkedProduct().getTaxCategoryCode(),
                requestData.getLinkedProduct().getProductName());
        skuDefinition.setLinkedProduct(product);
        skuDefinition.setShelfLifeInDays(requestData.getShelfLifeInDays());
        skuDefinition.setSkuDescription(requestData.getSkuDescription());
        skuDefinition.setSkuId(requestData.getUserSkuCreationId());
        skuDefinition.setSkuName(requestData.getSkuName());
        skuDefinition.setSkuStatus(requestData.getSkuStatus());
        skuDefinition.setSupportsLooseOrdering(SCMUtil.getStatus(requestData.getSupportsLooseOrdering()));
        skuDefinition.setUnitOfMeasure(requestData.getUnitOfMeasure());
        skuDefinition.setPriceLastUpdated(requestData.getPriceLastUpdated());
        skuDefinition.setTorqusSkuName(requestData.getTorqusSKU());
        skuDefinition.setIsDefault(SCMUtil.getStatus(requestData.getIsDefault()));
        skuDefinition.setInventoryList(requestData.getInventoryList());
        skuDefinition.setUnitPrice(SCMUtil.convertToBigDecimal(requestData.getUnitPrice()).floatValue());
        skuDefinition.setNegotiatedUnitPrice(
                SCMUtil.convertToBigDecimal(requestData.getNegotiatedUnitPrice()).floatValue());
        skuDefinition.setUnitPrice(SCMUtil.convertToBigDecimal(requestData.getUnitPrice()).floatValue());
        skuDefinition.setSkuCode(requestData.getSkuCode());
        if (requestData.getSkuImageId() != null) {
            skuDefinition.setSkuImage(requestData.getSkuImageId().toString());
        }
        skuDefinition.setTaxCode(requestData.getTaxCategoryCode());
        if (Objects.nonNull(requestData.getVoDisContinuedFrom())) {
            skuDefinition.setVoDisContinuedFrom(requestData.getVoDisContinuedFrom());
        }
        if (Objects.nonNull(requestData.getRoDisContinuedFrom())) {
            skuDefinition.setRoDisContinuedFrom(requestData.getRoDisContinuedFrom());
        }
        if (Objects.nonNull(requestData.getIsBranded())) {
            skuDefinition.setIsBranded(SCMUtil.getStatus(requestData.getIsBranded()));
        }
        skuDefinition.setBrand(requestData.getBrand());
        skuDefinition.setApprovalDocId(requestData.getApprovalDocId());
        skuDefinition.setSkuImage(requestData.getSkuImageId().toString());

        return skuDefinition;
    }

    public static SkuDefinitionData convert(SkuDefinition skuDefinition, ProductDefinition productDefinition,
                                            CategoryDefinition categoryDefinition, SubCategoryDefinition subCategoryDefinition, List<CostElementData> costElementData) {
        SkuDefinitionData skuDefinitionData = new SkuDefinitionData();
        skuDefinitionData.setUnitOfMeasure(skuDefinition.getUnitOfMeasure());
        skuDefinitionData.setCreatedBy(skuDefinition.getCreatedBy().getId());
        skuDefinitionData.setCreationDate(skuDefinition.getCreationDate());
        skuDefinitionData.setHasCase(SCMUtil.setStatus(skuDefinition.isHasCase()));
        skuDefinitionData.setHasInner(SCMUtil.setStatus(skuDefinition.isHasInner()));
        skuDefinitionData.setLinkedProduct(convert(productDefinition, categoryDefinition, subCategoryDefinition));
        skuDefinitionData.setShelfLifeInDays(skuDefinition.getShelfLifeInDays());
        skuDefinitionData.setSkuDescription(skuDefinition.getSkuDescription());
        skuDefinitionData.setSkuId(skuDefinition.getSkuId());
        skuDefinitionData.setSkuName(skuDefinition.getSkuName());
        skuDefinitionData.setSkuStatus(skuDefinition.getSkuStatus().value());
        skuDefinitionData.setSupportsLooseOrdering(SCMUtil.setStatus(skuDefinition.isSupportsLooseOrdering()));
        skuDefinitionData.setTorqusSKU(skuDefinition.getTorqusSkuName());
        skuDefinitionData.setIsDefault(SCMUtil.setStatus(skuDefinition.isIsDefault()));
        skuDefinitionData.setSkuCode(skuDefinition.getSkuCode());
        skuDefinitionData.setTaxCategoryCode(skuDefinition.getTaxCode());
        if(Objects.nonNull(skuDefinition.isIsBranded())){
            skuDefinitionData.setIsBranded(SCMUtil.setStatus(skuDefinition.isIsBranded()));
        }
        skuDefinitionData.setBrand(skuDefinition.getBrand());

        return skuDefinitionData;
    }

    public static UnitDetail convert(UnitDetailData unitDetailData) {
        UnitDetail unitDetail = factory.createUnitDetail();
        unitDetail.setUnitCategoryId(unitDetailData.getUnitCategory().getId());
        unitDetail.setUnitEmail(unitDetailData.getUnitEmail());
        unitDetail.setUnitId(unitDetailData.getUnitId());
        unitDetail.setUnitName(unitDetailData.getUnitName());
        unitDetail.setUnitStatus(SwitchStatus.fromValue(unitDetailData.getUnitStatus()));
        unitDetail.setCompanyId(unitDetailData.getCompanyId());
        unitDetail.setUnitRegion(unitDetailData.getUnitRegion());
        unitDetail.setShortCode(unitDetailData.getShortCode());
        unitDetail.setHubSpokeType(unitDetailData.getHubSpokeType());
        return unitDetail;
    }

    public static UnitDetailData convert(UnitDetail unitDetail, SCMUnitCategory scmUnitCategory) {
        UnitDetailData unitDetailData = new UnitDetailData();
        unitDetailData.setUnitCategory(convert(scmUnitCategory));
        unitDetailData.setUnitEmail(unitDetail.getUnitEmail());
        unitDetailData.setUnitId(unitDetail.getUnitId());
        unitDetailData.setUnitName(unitDetail.getUnitName());
        unitDetailData.setUnitStatus(unitDetail.getUnitStatus().value());
        unitDetailData.setTin(unitDetail.getTin());
        unitDetailData.setCompanyId(unitDetail.getCompanyId());
        unitDetailData.setUnitRegion(unitDetail.getUnitRegion());
        unitDetailData.setShortCode(unitDetail.getShortCode());
        return unitDetailData;
    }

    public static SCMUnitCategory convert(UnitCategoryData unitCategoryData) {
        SCMUnitCategory scmUnitCategory = factory.createSCMUnitCategory();
        scmUnitCategory.setCategoryCode(unitCategoryData.getCode());
        scmUnitCategory.setCategoryDescription(unitCategoryData.getDescription());
        scmUnitCategory.setCategoryId(unitCategoryData.getId());
        scmUnitCategory.setCategoryName(unitCategoryData.getName());
        scmUnitCategory.setCategoryStatus(SwitchStatus.valueOf(unitCategoryData.getStatus()));
        return scmUnitCategory;
    }

    public static UnitCategoryData convert(SCMUnitCategory scmUnitCategory) {
        UnitCategoryData unitCategoryData = new UnitCategoryData();
        unitCategoryData.setStatus(scmUnitCategory.getCategoryStatus().value());
        unitCategoryData.setCode(scmUnitCategory.getCategoryCode());
        unitCategoryData.setName(scmUnitCategory.getCategoryName());
        unitCategoryData.setId(scmUnitCategory.getCategoryId());
        unitCategoryData.setDescription(scmUnitCategory.getCategoryDescription());
        return unitCategoryData;
    }

    public static CategoryAttributeMapping convert(CategoryAttributeMappingData categoryAttributeMappingData) {
        CategoryAttributeMapping categoryAttributeMapping = new CategoryAttributeMapping();
        IdCodeName attribute = SCMUtil.generateIdCodeName(
                categoryAttributeMappingData.getAttributeDefinition().getAttributeId(),
                categoryAttributeMappingData.getAttributeDefinition().getAttributeCode(),
                categoryAttributeMappingData.getAttributeDefinition().getAttributeName());
        categoryAttributeMapping.setAttributeDefinition(attribute);
        IdCodeName category = SCMUtil.generateIdCodeName(
                categoryAttributeMappingData.getCategoryDefinition().getCategoryId(),
                categoryAttributeMappingData.getCategoryDefinition().getCategoryCode(),
                categoryAttributeMappingData.getCategoryDefinition().getCategoryName());
        categoryAttributeMapping.setCategoryDefinition(category);
        categoryAttributeMapping
                .setCategoryAttributeMappingId(categoryAttributeMappingData.getCategoryAttributeMappingId());
        categoryAttributeMapping.setMandatory(SCMUtil.getStatus(categoryAttributeMappingData.getIsMandatory()));
        categoryAttributeMapping.setUsedInNaming(SCMUtil.getStatus(categoryAttributeMappingData.getIsUsedInNaming()));
        categoryAttributeMapping.setMappingOrder(categoryAttributeMappingData.getMappingOrder());
        categoryAttributeMapping
                .setMappingStatus(SwitchStatus.valueOf(categoryAttributeMappingData.getMappingStatus()));
        return categoryAttributeMapping;
    }

    public static CategoryAttributeMappingData convert(CategoryAttributeMapping categoryAttributeMapping,
                                                       AttributeDefinition attributeDefinition, CategoryDefinition categoryDefinition) {
        CategoryAttributeMappingData categoryAttributeMappingData = new CategoryAttributeMappingData();
        categoryAttributeMappingData.setMappingOrder(categoryAttributeMapping.getMappingOrder());
        categoryAttributeMappingData.setIsUsedInNaming(SCMUtil.setStatus(categoryAttributeMapping.isUsedInNaming()));
        categoryAttributeMappingData.setIsMandatory(SCMUtil.setStatus(categoryAttributeMapping.isMandatory()));
        categoryAttributeMappingData
                .setAttributeDefinition(attributeDefinition != null ? convert(attributeDefinition) : null);
        categoryAttributeMappingData
                .setCategoryAttributeMappingId(categoryAttributeMapping.getCategoryAttributeMappingId());
        categoryAttributeMappingData
                .setCategoryDefinition(categoryDefinition != null ? convert(categoryDefinition) : null);
        categoryAttributeMappingData.setMappingStatus(categoryAttributeMapping.getMappingStatus().value());
        return categoryAttributeMappingData;
    }

    public static CategoryAttributeValue convert(CategoryAttributeValueData categoryAttributeValueData) {
        CategoryAttributeValue categoryAttributeValue = factory.createCategoryAttributeValue();
        categoryAttributeValue.setCategoryAttributeValueId(categoryAttributeValueData.getCategoryAttributeValueId());
        categoryAttributeValue.setCategoryAttributeMappingId(
                categoryAttributeValueData.getCategoryAttributeMapping().getCategoryAttributeMappingId());
        IdCodeName attributeValue = factory.createIdCodeName();
        attributeValue.setId(categoryAttributeValueData.getAttributeValue().getAttributeValueId());
        attributeValue.setCode(categoryAttributeValueData.getAttributeValue().getAttributeValueShortCode());
        attributeValue.setName(categoryAttributeValueData.getAttributeValue().getAttributeValue());
        categoryAttributeValue.setAttributeValue(attributeValue);
        categoryAttributeValue.setMappingStatus(SwitchStatus.valueOf(categoryAttributeValueData.getMappingStatus()));
        return categoryAttributeValue;
    }

    public static CategoryAttributeValueData convert(CategoryAttributeValue categoryAttributeValue,
                                                     AttributeValue attributeValue, AttributeDefinition attributeDefinition,
                                                     CategoryAttributeMapping categoryAttributeMapping, CategoryDefinition categoryDefinition) {
        CategoryAttributeValueData categoryAttributeValueData = new CategoryAttributeValueData();
        categoryAttributeValueData.setAttributeValue(convert(attributeValue, attributeDefinition));
        categoryAttributeValueData.setCategoryAttributeMapping(
                convert(categoryAttributeMapping, attributeDefinition, categoryDefinition));
        categoryAttributeValueData.setCategoryAttributeValueId(categoryAttributeValue.getCategoryAttributeValueId());
        categoryAttributeValueData.setMappingStatus(categoryAttributeValue.getMappingStatus().value());
        return categoryAttributeValueData;
    }

    public static ProductPackagingMapping convert(ProductPackagingMappingData productPackagingMappingData) {
        ProductPackagingMapping productPackagingMapping = factory.createProductPackagingMapping();
        productPackagingMapping.setMappingStatus(SwitchStatus.valueOf(productPackagingMappingData.getMappingStatus()));
        productPackagingMapping.setPackagingId(productPackagingMappingData.getPackagingId());
        productPackagingMapping.setProductId(productPackagingMappingData.getProductId());
        productPackagingMapping
                .setProductPackagingMappingId(productPackagingMappingData.getProductPackagingMappingId());
        productPackagingMapping.setIsDefault(SCMUtil.getStatus(productPackagingMappingData.getIsDefault()));
        return productPackagingMapping;
    }

    public static ProductPackagingMappingData convert(ProductPackagingMapping productPackagingMapping) {
        ProductPackagingMappingData productPackagingMappingData = new ProductPackagingMappingData();
        productPackagingMappingData.setMappingStatus(productPackagingMapping.getMappingStatus().value());
        productPackagingMappingData
                .setProductPackagingMappingId(productPackagingMapping.getProductPackagingMappingId());
        productPackagingMappingData.setProductId(productPackagingMapping.getProductId());
        productPackagingMappingData.setPackagingId(productPackagingMapping.getPackagingId());
        productPackagingMappingData.setIsDefault(SCMUtil.setStatus(productPackagingMapping.isIsDefault()));
        return productPackagingMappingData;
    }

    public static SkuPackagingMapping convert(SkuPackagingMappingData skuPackagingMappingData) {
        SkuPackagingMapping skuPackagingMapping = factory.createSkuPackagingMapping();
        skuPackagingMapping.setMappingStatus(SwitchStatus.valueOf(skuPackagingMappingData.getMappingStatus()));
        skuPackagingMapping.setPackagingId(skuPackagingMappingData.getPackagingId());
        skuPackagingMapping.setSkuId(skuPackagingMappingData.getSkuId());
        skuPackagingMapping.setSkuPackagingMappingId(skuPackagingMappingData.getSkuPackagingMappingId());
        skuPackagingMapping.setIsDefault(SCMUtil.getStatus(skuPackagingMappingData.getIsDefault()));
        return skuPackagingMapping;
    }

    public static SkuPackagingMappingData convert(SkuPackagingMapping skuPackagingMapping) {
        SkuPackagingMappingData skuPackagingMappingData = new SkuPackagingMappingData();
        skuPackagingMappingData.setMappingStatus(skuPackagingMapping.getMappingStatus().value());
        skuPackagingMappingData.setPackagingId(skuPackagingMapping.getPackagingId());
        skuPackagingMappingData.setSkuId(skuPackagingMapping.getSkuId());
        skuPackagingMappingData.setSkuPackagingMappingId(skuPackagingMapping.getSkuPackagingMappingId());
        skuPackagingMappingData.setIsDefault(SCMUtil.setStatus(skuPackagingMapping.isIsDefault()));
        return skuPackagingMappingData;
    }

    public static SkuAttributeValue convert(SkuAttributeValueData skuAttributeValueData) {
        SkuAttributeValue skuAttributeValue = new SkuAttributeValue();
        skuAttributeValue.setAttributeId(skuAttributeValueData.getAttributeId());
        skuAttributeValue.setAttributeValueId(skuAttributeValueData.getAttributeValueId());
        skuAttributeValue.setMappingStatus(SwitchStatus.valueOf(skuAttributeValueData.getMappingStatus()));
        skuAttributeValue.setSkuAttributeValueId(skuAttributeValueData.getSkuAttributeValueId());
        skuAttributeValue.setSkuId(skuAttributeValueData.getSkuId());
        return skuAttributeValue;
    }

    public static SkuAttributeValueData convert(SkuAttributeValue skuAttributeValue) {
        SkuAttributeValueData skuAttributeValueData = new SkuAttributeValueData();
        skuAttributeValueData.setSkuId(skuAttributeValue.getSkuId());
        skuAttributeValueData.setSkuAttributeValueId(skuAttributeValue.getSkuAttributeValueId());
        skuAttributeValueData.setAttributeValueId(skuAttributeValue.getAttributeValueId());
        skuAttributeValueData.setAttributeId(skuAttributeValue.getAttributeId());
        skuAttributeValueData.setMappingStatus(skuAttributeValue.getMappingStatus().value());
        return skuAttributeValueData;
    }

    public static RequestOrder convert(RequestOrderData requestOrderData, SCMCache scmCache,
                                       MasterDataCache masterDataCache) {
        IdCodeName generatedBy = SCMUtil.generateIdCodeName(requestOrderData.getGeneratedBy(), "",
                masterDataCache.getEmployees().get(requestOrderData.getGeneratedBy()));
        IdCodeName lastUpdatedBy = null;
        if (requestOrderData.getLastUpdatedBy() != null) {
            lastUpdatedBy = SCMUtil.generateIdCodeName(requestOrderData.getLastUpdatedBy(), "",
                    masterDataCache.getEmployees().get(requestOrderData.getLastUpdatedBy()));
        }
        IdCodeName fulfillmentUnit = SCMUtil.generateIdCodeName(requestOrderData.getFulfillmentUnitId(),
                String.valueOf(masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId()).getCompany().getId()),
                masterDataCache.getUnitBasicDetail(requestOrderData.getFulfillmentUnitId()).getName());
        IdCodeName requestingUnit = SCMUtil.generateIdCodeName(requestOrderData.getRequestUnitId(),
                String.valueOf(masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId()).getCompany().getId()),
                masterDataCache.getUnitBasicDetail(requestOrderData.getRequestUnitId()).getName());

        IdCodeName fulfillmentCompany = convertToIdCodeName(
                masterDataCache.getCompany(requestOrderData.getFulfillmentCompanyId()));
        IdCodeName requestCompany = convertToIdCodeName(
                masterDataCache.getCompany(requestOrderData.getRequestCompanyId()));

        RequestOrder requestOrder = new RequestOrder();
        requestOrder.setFulfillmentCompany(fulfillmentCompany);
        requestOrder.setRequestCompany(requestCompany);
        requestOrder.setFulfillmentDate(requestOrderData.getFulfillmentDate());
        requestOrder.setFulfillmentUnit(fulfillmentUnit);
        requestOrder.setGeneratedBy(generatedBy);
        requestOrder.setId(requestOrderData.getId());
        requestOrder.setTransferType(OrderTransferType.valueOf(requestOrderData.getTransferType()));
        requestOrder.setSearchTag(requestOrderData.getTag());
        requestOrder.setGenerationTime(requestOrderData.getGenerationTime());
        requestOrder.setVendorId(requestOrderData.getVendorId());
        requestOrder.setVendorName(requestOrderData.getVendorName());
        requestOrder.setNotified(AppConstants.getValue(requestOrderData.getIsNotified()));
        requestOrder.setNotificationTime(requestOrderData.getNotificationTime());
        requestOrder.setNotificationTypes(requestOrderData.getNotificationTypes());
        if (requestOrderData.getBudgetApplied() != null) {
            requestOrder.setApplyBudget(AppUtils.getStatus(requestOrderData.getBudgetApplied()));
        }
        if (requestOrderData.getBudgetReason() != null) {
            requestOrder.setBudgetReason(requestOrderData.getBudgetReason());
        }
        if (requestOrderData.getIsSpecialOrder() != null) {
            requestOrder.setSpecialOrder(SCMUtil.getStatus(requestOrderData.getIsSpecialOrder()));
        }
        requestOrder.setAssetOrder(
                SCMUtil.getStatus(requestOrderData.getAssetOrder() == null ? "N" : requestOrderData.getAssetOrder()));
        if (Objects.nonNull(requestOrderData.getAlternateF9Order())) {
            requestOrder.setAlternateF9Order(requestOrderData.getAlternateF9Order());
        }
        if (requestOrderData.getReferenceOrderData() != null) {
            requestOrder.setReferenceOrderId(requestOrderData.getReferenceOrderData().getId());
            requestOrder.setReferenceOrderType(requestOrderData.getReferenceOrderData().getRefOrderSource());
        }
        if (Objects.nonNull(requestOrderData.getBulkOrder())) {
            requestOrder.setBulkOrder(requestOrderData.getBulkOrder().equalsIgnoreCase(AppConstants.YES));
        }
        requestOrder.setRequestUnit(requestingUnit);
        requestOrder.setStatus(SCMOrderStatus.valueOf(requestOrderData.getStatus()));
        requestOrder.setComment(requestOrderData.getComment());
        requestOrder.setLastUpdateTime(requestOrderData.getLastUpdateTime());
        if (lastUpdatedBy != null) {
            requestOrder.setLastUpdatedBy(lastUpdatedBy);
        }
        requestOrder.setNumberOfDays(requestOrderData.getNumberOfDays() == null? 1 : requestOrderData.getNumberOfDays());
        requestOrder.setRaiseBy(SCMUtil.getStatus(requestOrderData.getRaiseBy() == null ? "N" : requestOrderData.getRaiseBy()));
        return requestOrder;
    }

    public static OrdersDetailsShort convertShort(RequestOrderData requestOrderData,
                                            MasterDataCache masterDataCache) {
        IdCodeName generatedBy = SCMUtil.generateIdCodeName(requestOrderData.getGeneratedBy(), "",
            masterDataCache.getEmployees().get(requestOrderData.getGeneratedBy()));
        IdCodeName requestingUnit = SCMUtil.generateIdCodeName(requestOrderData.getRequestUnitId(),
            String.valueOf(masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId()).getCompany().getId()),
            masterDataCache.getUnitBasicDetail(requestOrderData.getRequestUnitId()).getName());
        OrdersDetailsShort ordersDetailsShort = new OrdersDetailsShort();
        ordersDetailsShort.setId(requestOrderData.getId());
        ordersDetailsShort.setRequestUnit(requestingUnit);
        ordersDetailsShort.setGeneratedBy(generatedBy);
        ordersDetailsShort.setLastUpdateTime(requestOrderData.getLastUpdateTime());
        ordersDetailsShort.setFulfillmentDate(requestOrderData.getFulfillmentDate());
        ordersDetailsShort.setSpecialOrder(SCMUtil.getStatus(requestOrderData.getIsSpecialOrder()));
        ordersDetailsShort.setGenerationTime(requestOrderData.getGenerationTime());
        if (requestOrderData.getTransferType() != null) {
            ordersDetailsShort.setTransferType(OrderTransferType.valueOf(requestOrderData.getTransferType()));
        } else {
            ordersDetailsShort.setTransferType(OrderTransferType.TRANSFER);
        }
        ordersDetailsShort.setRaisedBy(requestOrderData.getRaiseBy());
        ordersDetailsShort.setAssetOrder(
            SCMUtil.getStatus(requestOrderData.getAssetOrder() == null ? "N" : requestOrderData.getAssetOrder()));
        ordersDetailsShort.setStatus(SCMOrderStatus.valueOf(requestOrderData.getStatus()));
        if(requestOrderData.getReferenceOrderData()!=null){
            ordersDetailsShort.setRequestOrderType(true);
        }else {
            ordersDetailsShort.setRequestOrderType(false);
        }
        ordersDetailsShort.setType(requestOrderData.getType());

        return ordersDetailsShort;
    }

    public static IdCodeName convertToIdCodeName(Company company) {
        return company != null ? new IdCodeName(company.getId(), company.getShortCode(), company.getName()) : null;
    }

    public static RequestOrder convertFullRequestOrder(RequestOrderData requestOrderData, SCMCache scmCache,
                                                       MasterDataCache masterDataCache) {

        IdCodeName generatedBy = SCMUtil.generateIdCodeName(requestOrderData.getGeneratedBy(), "",
                masterDataCache.getEmployees().get(requestOrderData.getGeneratedBy()));
        IdCodeName lastUpdatedBy = null;
        if (requestOrderData.getLastUpdatedBy() != null) {
            lastUpdatedBy = SCMUtil.generateIdCodeName(requestOrderData.getLastUpdatedBy(), "",
                    masterDataCache.getEmployees().get(requestOrderData.getLastUpdatedBy()));
        }
        IdCodeName requestingUnit = SCMUtil.generateIdCodeName(requestOrderData.getRequestUnitId(),
                String.valueOf(masterDataCache.getUnit(requestOrderData.getRequestUnitId()).getCompany().getId()),
                masterDataCache.getUnit(requestOrderData.getRequestUnitId()).getName());
        IdCodeName fulfillmentUnit = SCMUtil.generateIdCodeName(requestOrderData.getFulfillmentUnitId(),
                String.valueOf(masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId()).getCompany().getId()),
                masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId()).getName());
        IdCodeName fulfillmentCompany = convertToIdCodeName(
                masterDataCache.getCompany(requestOrderData.getFulfillmentCompanyId()));
        IdCodeName requestCompany = convertToIdCodeName(
                masterDataCache.getCompany(requestOrderData.getRequestCompanyId()));
        Map<Integer, VendorDetail> vendors = scmCache.getVendorDetails();
        RequestOrder requestOrder = new RequestOrder();
        requestOrder.setFulfillmentCompany(fulfillmentCompany);
        requestOrder.setRequestCompany(requestCompany);
        requestOrder.setFulfillmentDate(requestOrderData.getFulfillmentDate());
        requestOrder.setFulfillmentUnit(fulfillmentUnit);
        requestOrder.setGeneratedBy(generatedBy);
        requestOrder.setSearchTag(requestOrderData.getTag());
        if (requestOrderData.getIsSpecialOrder() != null) {
            requestOrder.setSpecialOrder(SCMUtil.getStatus(requestOrderData.getIsSpecialOrder()));
        }
        requestOrder.setAssetOrder(
                SCMUtil.getStatus(requestOrderData.getAssetOrder() == null ? "N" : requestOrderData.getAssetOrder()));
        if (Objects.nonNull(requestOrderData.getAlternateF9Order())) {
            requestOrder.setAlternateF9Order(requestOrderData.getAlternateF9Order());
        }
        if (lastUpdatedBy != null) {
            requestOrder.setLastUpdatedBy(lastUpdatedBy);
        }
        requestOrder.setId(requestOrderData.getId());
        requestOrder.setGenerationTime(requestOrderData.getGenerationTime());
        if (requestOrderData.getReferenceOrderData() != null) {
            requestOrder.setReferenceOrderId(requestOrderData.getReferenceOrderData().getId());
            requestOrder.setReferenceOrderType(requestOrderData.getReferenceOrderData().getRefOrderSource());
        }
        if (Objects.nonNull(requestOrderData.getBulkOrder())) {
            requestOrder.setBulkOrder(requestOrderData.getBulkOrder().equalsIgnoreCase(AppConstants.YES));
        }
        requestOrder.setRequestUnit(requestingUnit);
        requestOrder.setStatus(SCMOrderStatus.valueOf(requestOrderData.getStatus()));
        requestOrder.setComment(requestOrderData.getComment());
        requestOrder.setLastUpdateTime(requestOrderData.getLastUpdateTime());
        requestOrder.setVendorId(requestOrderData.getVendorId());
        requestOrder.setVendorName(requestOrderData.getVendorName());
        requestOrder.setNotified(AppConstants.getValue(requestOrderData.getIsNotified()));
        requestOrder.setNotificationTime(requestOrderData.getNotificationTime());
        requestOrder.setNotificationTypes(requestOrderData.getNotificationTypes());

        if (requestOrderData.getTransferType() != null) {
            requestOrder.setTransferType(OrderTransferType.valueOf(requestOrderData.getTransferType()));
        } else {
            requestOrder.setTransferType(OrderTransferType.TRANSFER);
        }
        Map<Integer, Pair<BigDecimal,BigDecimal>> predictedSuggestedQty = getPredictedSuggestedQtyMap(requestOrderData);
        for (RequestOrderItemData requestOrderItemData : requestOrderData.getRequestOrderItemDatas()) {
            String productCode = scmCache.getProductDefinition(requestOrderItemData.getProductId()).getProductCode();
            if (requestOrderItemData.getVendorId() != null) {
                VendorDetail vd = vendors.get(requestOrderItemData.getVendorId());
                if (vd != null) {
                    IdCodeName vendor = SCMUtil.generateIdCodeName(vd.getVendorId(), "", vd.getEntityName());
                    requestOrder.getRequestOrderItems().add(convert(requestOrderItemData, vendor, productCode));
                } else {
                    requestOrder.getRequestOrderItems().add(convert(requestOrderItemData, null, productCode));
                }
            } else {
                requestOrder.getRequestOrderItems().add(convert(requestOrderItemData, null, productCode));
            }
        }

        Set<RequestOrderData> parentROs = requestOrderData.getParentRequestOrderDatas();
        if (!parentROs.isEmpty()) {
            requestOrder.getParentROs()
                    .addAll(parentROs.stream().map(value -> value.getId()).collect(Collectors.toSet()));
        }
        return requestOrder;
    }

    private static Map<Integer,Pair<BigDecimal,BigDecimal>> getPredictedSuggestedQtyMap(RequestOrderData requestOrderData) {
        Map<Integer,Pair<BigDecimal,BigDecimal>> result = new HashMap<>();
        if (Objects.nonNull(requestOrderData.getReferenceOrderData())) {
            for (ReferenceOrderScmItemData scmItemData : requestOrderData.getReferenceOrderData().getReferenceOrderScmItemDatas()) {
                result.put(scmItemData.getProductId(), new Pair<>(scmItemData.getPredictedQuantity(), scmItemData.getSuggestedQuantity()));
            }
        }
        return result;
    }

    public static RequestOrderItem convert(RequestOrderItemData requestOrderItemData, IdCodeName vendor,
                                           String productCode) {
        RequestOrderItem requestOrderItem = new RequestOrderItem();
        requestOrderItem.setId(requestOrderItemData.getId());
        requestOrderItem.setProductId(requestOrderItemData.getProductId());
        requestOrderItem.setProductName(requestOrderItemData.getProductName());
        List<TaxDetail> taxList = new ArrayList<>();
        for (RequestOrderItemTaxDetail tax : requestOrderItemData.getOrderItemTaxes()) {
            TaxDetail taxDetail = new TaxDetail();
            taxDetail.setCode(tax.getTaxCode());
            taxDetail.setPercentage(tax.getTaxPercentage());
            taxDetail.setTaxable(tax.getTaxableAmount());
            taxDetail.setTotal(tax.getTotalAmount());
            taxDetail.setType(tax.getTaxType());
            taxDetail.setValue(tax.getTotalTax());
            taxList.add(taxDetail);
        }
        requestOrderItem.setTaxes(taxList);
        if (requestOrderItemData.getReceivedQuantity() != null) {
            requestOrderItem.setReceivedQuantity(requestOrderItemData.getReceivedQuantity().floatValue());
        }
        requestOrderItem.setRequestedAbsoluteQuantity(requestOrderItemData.getRequestedAbsoluteQuantity().floatValue());
        requestOrderItem.setTax(requestOrderItemData.getTaxAmount());
        requestOrderItem.setCode(requestOrderItemData.getTaxCode());
        requestOrderItem.setRequestedQuantity(requestOrderItemData.getRequestedQuantity().floatValue());
        requestOrderItem.setOriginalQuantity(requestOrderItemData.getOriginalQuantity());
        requestOrderItem.setExcessQuantity(requestOrderItemData.getExcessQuantity() == null? BigDecimal.ZERO : requestOrderItemData.getExcessQuantity());
        if (requestOrderItemData.getTransferredQuantity() != null) {
            requestOrderItem.setTransferredQuantity(requestOrderItemData.getTransferredQuantity().floatValue());
        }
        requestOrderItem.setUnitOfMeasure(requestOrderItemData.getUnitOfMeasure());
        if (vendor != null) {
            requestOrderItem.setVendor(vendor);
        } else if (requestOrderItemData.getVendorId() != null) {
            requestOrderItem.setVendor(SCMUtil.generateIdCodeName(requestOrderItemData.getVendorId(), "", ""));
        }
        if (requestOrderItemData.getNegotiatedUnitPrice() != null) {
            requestOrderItem.setNegotiatedUnitPrice(requestOrderItemData.getNegotiatedUnitPrice().floatValue());
        }
        if (requestOrderItemData.getUnitPrice() != null) {
            requestOrderItem.setUnitPrice(requestOrderItemData.getUnitPrice().floatValue());
        }
        if(requestOrderItemData .getExpiryDate() != null){
            requestOrderItem.setExpiryDate(requestOrderItemData.getExpiryDate());
        }
        requestOrderItem.setProductCode(productCode);
        requestOrderItem.setProductionBookingCompleted(SCMUtil.getStatus(Objects.isNull(requestOrderItemData.getProductionBookingCompleted()) ? "N" : requestOrderItemData.getProductionBookingCompleted()));
        requestOrderItem.setProductionBookingQuantity(requestOrderItemData.getProductionBookingQuantity());
        if (Objects.nonNull(requestOrderItemData.getReason())) {
            requestOrderItem.setReason(requestOrderItemData.getReason());
        }
        if (Objects.nonNull(requestOrderItemData.getComment())) {
            requestOrderItem.setComment(requestOrderItemData.getComment());
        }
        if (Objects.nonNull(requestOrderItemData.getSuggestedQuantity())) {
            requestOrderItem.setSuggestedQuantity(requestOrderItemData.getSuggestedQuantity());
        }
        if (Objects.nonNull(requestOrderItemData.getPredictedQuantity())) {
            requestOrderItem.setPredictedQuantity(requestOrderItemData.getPredictedQuantity());
        }
        if (Objects.nonNull(requestOrderItemData.getDiffQuantity())) {
            requestOrderItem.setDiffQuantity(requestOrderItemData.getDiffQuantity());
        }

        return requestOrderItem;
    }

    public static RequestOrderData convert(RequestOrder requestOrder, ReferenceOrderData referenceOrderData) {
        RequestOrderData requestOrderData = new RequestOrderData();
        requestOrderData.setComment(requestOrder.getComment());
        requestOrderData.setStatus(requestOrder.getStatus().value());
        requestOrderData.setId(requestOrder.getId());
        requestOrderData.setFulfillmentDate(SCMUtil.getDate(requestOrder.getFulfillmentDate()));
        requestOrderData.setFulfillmentUnitId(requestOrder.getFulfillmentUnit().getId());
        requestOrderData.setFulfillmentCompanyId(requestOrder.getFulfillmentCompany().getId());
        requestOrderData.setRequestCompanyId(requestOrder.getRequestCompany().getId());
        requestOrderData.setGeneratedBy(requestOrder.getGeneratedBy().getId());
        requestOrderData.setTag(requestOrder.getSearchTag());
        requestOrderData.setVendorId(requestOrder.getVendorId());
        requestOrderData.setVendorName(requestOrder.getVendorName());
        requestOrderData.setIsNotified(AppConstants.getValue(requestOrder.isNotified()));
        requestOrderData.setNotificationTime(requestOrder.getNotificationTime());
        requestOrderData.setNotificationTypes(requestOrder.getNotificationTypes());
        requestOrderData.setNumberOfDays(requestOrder.getNumberOfDays() == null ? 1 : requestOrder.getNumberOfDays());
        requestOrderData.setRaiseBy(SCMUtil.setStatus(requestOrder.getRaiseBy() == null ? false : requestOrder.getRaiseBy()));
        requestOrderData.setAssetOrder(
                SCMUtil.setStatus(requestOrder.getAssetOrder() == null ? false : requestOrder.getAssetOrder()));
        if (Objects.nonNull(requestOrder.getAlternateF9Order())) {
            requestOrderData.setAlternateF9Order(requestOrder.getAlternateF9Order());
        }
        if (requestOrder.isSpecialOrder() != null) {
            requestOrderData.setIsSpecialOrder(SCMUtil.setStatus(requestOrder.isSpecialOrder()));
        }
        if (requestOrder.getLastUpdatedBy() != null) {
            requestOrderData.setLastUpdatedBy(requestOrder.getLastUpdatedBy().getId());
        }
        requestOrderData.setGenerationTime(requestOrder.getGenerationTime());
        requestOrderData.setLastUpdateTime(requestOrder.getLastUpdateTime());
        requestOrderData.setReferenceOrderData(referenceOrderData);
        float total = 0.0f;
        List<RequestOrderItemData> requestOrderItemDataList = new ArrayList<RequestOrderItemData>();
        for (RequestOrderItem requestOrderItem : requestOrder.getRequestOrderItems()) {
            requestOrderItemDataList.add(convert(requestOrderItem, null));
            if (requestOrderItem.getNegotiatedUnitPrice() != null) {
                total = total + (requestOrderItem.getRequestedQuantity() * requestOrderItem.getNegotiatedUnitPrice());
            }
        }
        requestOrderData.setTotalAmount(SCMUtil.convertToBigDecimal(total));
        requestOrderData.setRequestOrderItemDatas(requestOrderItemDataList);
        requestOrderData.setRequestUnitId(requestOrder.getRequestUnit().getId());
        if (requestOrder.isApplyBudget() != null) {
            requestOrderData.setBudgetApplied(AppUtils.setStatus(requestOrder.isApplyBudget()));
        }
        if (requestOrder.getBudgetReason() != null) {
            requestOrderData.setBudgetReason(requestOrder.getBudgetReason());
        }
        if (requestOrder.getTransferType() != null) {
            requestOrderData.setTransferType(requestOrder.getTransferType().name());
        }
        if (Objects.nonNull(requestOrder.getBulkOrder())) {
            requestOrderData.setBulkOrder(requestOrder.getBulkOrder() ? AppConstants.YES : AppConstants.NO);
        }
        requestOrderData.setType(requestOrder.getType());
        return requestOrderData;
    }

    public static RequestOrderItemData convert(RequestOrderItem requestOrderItem, RequestOrderData requestOrderData) {
        RequestOrderItemData requestOrderItemData = new RequestOrderItemData();
        requestOrderItemData.setId(requestOrderItem.getId());
        requestOrderItemData.setProductId(requestOrderItem.getProductId());
        requestOrderItemData.setProductName(requestOrderItem.getProductName());
        requestOrderItemData.setReceivedQuantity(SCMUtil.convertToBigDecimal(requestOrderItem.getReceivedQuantity()));
        requestOrderItemData.setRequestedAbsoluteQuantity(
                SCMUtil.convertToBigDecimal(requestOrderItem.getRequestedAbsoluteQuantity()));
        requestOrderItemData.setRequestedQuantity(SCMUtil.convertToBigDecimal(requestOrderItem.getRequestedQuantity()));
        requestOrderItemData
                .setTransferredQuantity(SCMUtil.convertToBigDecimal(requestOrderItem.getTransferredQuantity()));
        requestOrderItemData.setUnitOfMeasure(requestOrderItem.getUnitOfMeasure());
        requestOrderItemData.setOriginalQuantity(SCMUtil.convertToBigDecimal(requestOrderItem.getRequestedQuantity()));
        requestOrderItemData.setExcessQuantity(requestOrderItem.getExcessQuantity() == null? BigDecimal.ZERO : requestOrderItem.getExcessQuantity());
        requestOrderItemData.setRequestOrderData(requestOrderData);
        if (requestOrderItem.getNegotiatedUnitPrice() != null) {
            requestOrderItemData.setCalculatedAmount(SCMUtil.convertToBigDecimal(
                    requestOrderItem.getNegotiatedUnitPrice() * requestOrderItem.getRequestedQuantity()));
        }
        if (requestOrderItem.getVendor() != null) {
            requestOrderItemData.setVendorId(requestOrderItem.getVendor().getId());
        }
        if (requestOrderItem.getNegotiatedUnitPrice() != null) {
            requestOrderItemData
                    .setNegotiatedUnitPrice(SCMUtil.convertToBigDecimal(requestOrderItem.getNegotiatedUnitPrice()));
        }
        if (requestOrderItem.getUnitPrice() != null) {
            requestOrderItemData.setUnitPrice(SCMUtil.convertToBigDecimal(requestOrderItem.getNegotiatedUnitPrice()));
        }
        if(Objects.nonNull(requestOrderItem.getProductionBookingCompleted())){
            requestOrderItemData.setProductionBookingCompleted(AppUtils.setStatus(requestOrderItem.getProductionBookingCompleted()));
        }
        requestOrderItemData.setProductionBookingQuantity(requestOrderItem.getProductionBookingQuantity());
        if (Objects.nonNull(requestOrderItem.getReason())) {
            requestOrderItemData.setReason(requestOrderItem.getReason());
        }
        if (Objects.nonNull(requestOrderItem.getComment())) {
            requestOrderItemData.setComment(requestOrderItem.getComment());
        }
        return requestOrderItemData;
    }

    public static ReferenceOrder convert(ReferenceOrderData referenceOrderData, IdCodeName generatedBy,
                                         IdCodeName fulfillmentUnit, IdCodeName requestingUnit) {
        ReferenceOrder referenceOrder = factory.createReferenceOrder();
        referenceOrder.setId(referenceOrderData.getId());
        referenceOrder.setComment(referenceOrderData.getComment());
        referenceOrder.setFulfillmentDate(referenceOrderData.getFulfillmentDate());
        if (fulfillmentUnit != null) {
            referenceOrder.setFulfillmentUnit(fulfillmentUnit);
        }
        referenceOrder.setGeneratedBy(generatedBy);
        referenceOrder.setGenerationTime(referenceOrderData.getGenerationTime());
        referenceOrder.setRequestUnit(requestingUnit);
        referenceOrder.setStatus(SCMOrderStatus.valueOf(referenceOrderData.getStatus()));
        referenceOrder.setLastUpdateTime(referenceOrderData.getLastUpdateTime());
        referenceOrder.setInitiationTime(referenceOrderData.getInitiationTime());
        referenceOrder.getRequestOrderIds();
        return referenceOrder;
    }

    public static ReferenceOrder convertFullRefOrder(ReferenceOrderData referenceOrderData, IdCodeName generatedBy,
                                                     IdCodeName fulfillmentUnit, IdCodeName requestingUnit, List<RequestOrderData> requestOrderDatas) {
        ReferenceOrder referenceOrder = factory.createReferenceOrder();
        referenceOrder.setId(referenceOrderData.getId());
        referenceOrder.setComment(referenceOrderData.getComment());
        referenceOrder.setFulfillmentDate(referenceOrderData.getFulfillmentDate());
        if (fulfillmentUnit != null) {
            referenceOrder.setFulfillmentUnit(fulfillmentUnit);
        }
        referenceOrder.setGeneratedBy(generatedBy);
        referenceOrder.setGenerationTime(referenceOrderData.getGenerationTime());
        referenceOrder.setRequestUnit(requestingUnit);
        referenceOrder.setStatus(SCMOrderStatus.valueOf(referenceOrderData.getStatus()));
        referenceOrder.setLastUpdateTime(referenceOrderData.getLastUpdateTime());
        referenceOrder.setInitiationTime(referenceOrderData.getInitiationTime());
        List<ReferenceOrderMenuItem> referenceOrderMenuItems = new ArrayList<ReferenceOrderMenuItem>();
        for (ReferenceOrderMenuItemData referenceOrderMenuItemData : referenceOrderData
                .getReferenceOrderMenuItemDatas()) {
            referenceOrderMenuItems.add(convert(referenceOrderMenuItemData));
        }
        referenceOrder.getReferenceOrderMenuItems().addAll(referenceOrderMenuItems);
        List<ReferenceOrderScmItem> referenceOrderScmItems = new ArrayList<ReferenceOrderScmItem>();
        for (ReferenceOrderScmItemData referenceOrderScmItemData : referenceOrderData.getReferenceOrderScmItemDatas()) {
            referenceOrderScmItems.add(convert(referenceOrderScmItemData));
        }
        referenceOrder.getReferenceOrderScmItems().addAll(referenceOrderScmItems);
        List<Integer> requestOrderIds = new ArrayList<Integer>();
        for (RequestOrderData requestOrderData : requestOrderDatas) {
            requestOrderIds.add(requestOrderData.getId());
        }
        referenceOrder.getRequestOrderIds().addAll(requestOrderIds);
        return referenceOrder;
    }

    public static ReferenceOrderData convert(ReferenceOrder referenceOrder, boolean setChildEntities,
                                             SCMCache scmCache) {
        ReferenceOrderData referenceOrderData = new ReferenceOrderData();
        referenceOrderData.setStatus(referenceOrder.getStatus().value());
        referenceOrderData.setRefOrderSource(referenceOrder.getRefOrderSource());
        referenceOrderData.setGenerationTime(referenceOrder.getGenerationTime());
        referenceOrderData.setId(referenceOrder.getId());
        referenceOrderData.setComment(referenceOrder.getComment());
        referenceOrderData.setFulfillmentDate(SCMUtil.getDate(referenceOrder.getFulfillmentDate()));
        if (referenceOrder.getFulfillmentUnit() != null) {
            referenceOrderData.setFulfillmentUnitId(referenceOrder.getFulfillmentUnit().getId());
        }
        referenceOrderData.setGenerationTime(referenceOrder.getGenerationTime());
        if (setChildEntities) {
            List<ReferenceOrderMenuItemData> referenceOrderMenuItemDataList = new ArrayList<ReferenceOrderMenuItemData>();
            for (ReferenceOrderMenuItem referenceOrderMenuItem : referenceOrder.getReferenceOrderMenuItems()) {
                referenceOrderMenuItemDataList.add(convert(referenceOrderMenuItem, null, setChildEntities));
            }
            referenceOrderData.setReferenceOrderMenuItemDatas(referenceOrderMenuItemDataList);
            List<ReferenceOrderScmItemData> referenceOrderScmItemDataList = new ArrayList<ReferenceOrderScmItemData>();
            for (ReferenceOrderScmItem referenceOrderScmItem : referenceOrder.getReferenceOrderScmItems()) {
                FulfillmentType fulfillmentType = referenceOrderScmItem.getFulfillmentType();
                referenceOrderScmItemDataList.add(convert(referenceOrderScmItem, null, fulfillmentType, null));
            }
            referenceOrderData.setReferenceOrderScmItemDatas(referenceOrderScmItemDataList);
        }
        referenceOrderData.setRequestUnitId(referenceOrder.getRequestUnit().getId());
        referenceOrderData.setInitiationTime(referenceOrder.getInitiationTime());
        referenceOrderData.setLastUpdateTime(referenceOrder.getLastUpdateTime());
        referenceOrderData.setGeneratedBy(referenceOrder.getGeneratedBy().getId());
        referenceOrderData.setNumberOfDays(referenceOrder.getNumberOfDays() == null ? 1 : referenceOrder.getNumberOfDays());
        referenceOrderData.setRaiseBy(SCMUtil.setStatus(referenceOrder.getRaiseBy() == null ? false :referenceOrder.getRaiseBy()));
        if (Objects.nonNull(referenceOrder.getBrand())) {
            referenceOrderData.setBrand(referenceOrder.getBrand());
        }
        return referenceOrderData;
    }

    public static ReferenceOrderMenuItemData convert(ReferenceOrderMenuItem referenceOrderMenuItem,
                                                     ReferenceOrderData referenceOrderData, boolean setChildEntities) {
        ReferenceOrderMenuItemData referenceOrderMenuItemData = new ReferenceOrderMenuItemData();
        if (referenceOrderMenuItem.getTransferredQuantity() != null) {
            referenceOrderMenuItemData.setTransferredQuantity(
                    SCMUtil.convertToBigDecimal(referenceOrderMenuItem.getTransferredQuantity()));
        }
        referenceOrderMenuItemData
                .setRequestedQuantity(SCMUtil.convertToBigDecimal(referenceOrderMenuItem.getRequestedQuantity()));
        referenceOrderMenuItemData.setId(referenceOrderMenuItem.getId());
        referenceOrderMenuItemData.setProductId(referenceOrderMenuItem.getProductId());
        referenceOrderMenuItemData.setProductName(referenceOrderMenuItem.getProductName());
        referenceOrderMenuItemData.setDimension(referenceOrderMenuItem.getDimension());
        if (referenceOrderMenuItem.getReceivedQuantity() != null) {
            referenceOrderMenuItemData
                    .setReceivedQuantity(SCMUtil.convertToBigDecimal(referenceOrderMenuItem.getReceivedQuantity()));
        }
        referenceOrderMenuItemData.setRequestedAbsoluteQuantity(
                SCMUtil.convertToBigDecimal(referenceOrderMenuItem.getRequestedAbsoluteQuantity()));
        if (setChildEntities) {
            List<ROMenuItemVariantData> roMenuItemVariantDataList = new ArrayList<ROMenuItemVariantData>();
            for (ReferenceOrderMenuVariant referenceOrderMenuVariant : referenceOrderMenuItem.getVariants()) {
                roMenuItemVariantDataList.add(convert(referenceOrderMenuVariant, null));
            }
            referenceOrderMenuItemData.setVariants(roMenuItemVariantDataList);
        }
        referenceOrderMenuItemData.setReferenceOrderData(referenceOrderData);
        if (referenceOrderMenuItem.getQuantity() != null) {
            referenceOrderMenuItemData.setQuantity(SCMUtil.convertToBigDecimal(referenceOrderMenuItem.getQuantity()));
        }
        if (referenceOrderMenuItem.getDineInQuantity() != null) {
            referenceOrderMenuItemData
                    .setDineInQuantity(SCMUtil.convertToBigDecimal(referenceOrderMenuItem.getDineInQuantity()));
        }
        if (referenceOrderMenuItem.getDeliveryQuantity() != null) {
            referenceOrderMenuItemData
                    .setDeliveryQuantity(SCMUtil.convertToBigDecimal(referenceOrderMenuItem.getDeliveryQuantity()));
        }
        if (referenceOrderMenuItem.getTakeawayQuantity() != null) {
            referenceOrderMenuItemData
                    .setTakeawayQuantity(SCMUtil.convertToBigDecimal(referenceOrderMenuItem.getTakeawayQuantity()));
        }
        return referenceOrderMenuItemData;
    }

    public static ROMenuItemVariantData convert(ReferenceOrderMenuVariant referenceOrderMenuVariant,
                                                ReferenceOrderMenuItemData referenceOrderMenuItemData) {
        ROMenuItemVariantData roMenuItemVariantData = new ROMenuItemVariantData();
        roMenuItemVariantData
                .setConversionQuantity(SCMUtil.convertToBigDecimal(referenceOrderMenuVariant.getConversionQuantity()));
        roMenuItemVariantData.setId(referenceOrderMenuVariant.getId());
        roMenuItemVariantData.setName(referenceOrderMenuVariant.getName());
        roMenuItemVariantData
                .setOrderedQuantity(SCMUtil.convertToBigDecimal(referenceOrderMenuVariant.getOrderedQuantity()));
        roMenuItemVariantData.setReferenceOrderMenuItemData(referenceOrderMenuItemData);
        return roMenuItemVariantData;
    }

    public static ReferenceOrderMenuItem convert(ReferenceOrderMenuItemData referenceOrderMenuItemData) {
        ReferenceOrderMenuItem referenceOrderMenuItem = new ReferenceOrderMenuItem();
        referenceOrderMenuItem.setId(referenceOrderMenuItemData.getId());
        referenceOrderMenuItem.setProductName(referenceOrderMenuItemData.getProductName());
        referenceOrderMenuItem.setDimension(referenceOrderMenuItemData.getDimension());
        referenceOrderMenuItem.setProductId(referenceOrderMenuItemData.getProductId());
        if (referenceOrderMenuItemData.getReceivedQuantity() != null) {
            referenceOrderMenuItem.setReceivedQuantity(referenceOrderMenuItemData.getReceivedQuantity().floatValue());
        }
        referenceOrderMenuItem
                .setRequestedAbsoluteQuantity(referenceOrderMenuItemData.getRequestedAbsoluteQuantity().floatValue());
        referenceOrderMenuItem.setRequestedQuantity(referenceOrderMenuItemData.getRequestedQuantity().floatValue());
        if (referenceOrderMenuItemData.getTransferredQuantity() != null) {
            referenceOrderMenuItem
                    .setTransferredQuantity(referenceOrderMenuItemData.getTransferredQuantity().floatValue());
        }
        List<ReferenceOrderMenuVariant> referenceOrderMenuVariants = new ArrayList<ReferenceOrderMenuVariant>();
        for (ROMenuItemVariantData roMenuItemVariantData : referenceOrderMenuItemData.getVariants()) {
            referenceOrderMenuVariants.add(convert(roMenuItemVariantData));
        }
        referenceOrderMenuItem.getVariants().addAll(referenceOrderMenuVariants);
        if (referenceOrderMenuItemData.getQuantity() != null) {
            referenceOrderMenuItem.setQuantity(referenceOrderMenuItemData.getQuantity().floatValue());
        }
        if (referenceOrderMenuItemData.getDineInQuantity() != null) {
            referenceOrderMenuItem.setDineInQuantity(referenceOrderMenuItemData.getDineInQuantity().floatValue());
        }
        if (referenceOrderMenuItemData.getDeliveryQuantity() != null) {
            referenceOrderMenuItem.setDeliveryQuantity(referenceOrderMenuItemData.getDeliveryQuantity().floatValue());
        }
        if (referenceOrderMenuItemData.getTakeawayQuantity() != null) {
            referenceOrderMenuItem.setTakeawayQuantity(referenceOrderMenuItemData.getTakeawayQuantity().floatValue());
        }
        return referenceOrderMenuItem;
    }

    public static ReferenceOrderMenuVariant convert(ROMenuItemVariantData roMenuItemVariantData) {
        ReferenceOrderMenuVariant referenceOrderMenuVariant = new ReferenceOrderMenuVariant();
        referenceOrderMenuVariant.setName(roMenuItemVariantData.getName());
        referenceOrderMenuVariant.setId(roMenuItemVariantData.getId());
        referenceOrderMenuVariant.setConversionQuantity(roMenuItemVariantData.getConversionQuantity().floatValue());
        referenceOrderMenuVariant.setOrderedQuantity(roMenuItemVariantData.getOrderedQuantity().floatValue());
        return referenceOrderMenuVariant;
    }

    public static ReferenceOrderScmItemData convert(ReferenceOrderScmItem referenceOrderScmItem,
                                                    ReferenceOrderData referenceOrderData, FulfillmentType fulfillmentType,
                                                    Map<Integer, List<String>> expiryUsageLogs) {
        ReferenceOrderScmItemData referenceOrderScmItemData = new ReferenceOrderScmItemData();
        referenceOrderScmItemData
                .setTransferredQuantity(SCMUtil.convertToBigDecimal(referenceOrderScmItem.getTransferredQuantity()));
        referenceOrderScmItemData
                .setRequestedQuantity(SCMUtil.convertToBigDecimal(referenceOrderScmItem.getRequestedQuantity()));
        referenceOrderScmItemData.setId(referenceOrderScmItem.getId());
        referenceOrderScmItemData.setProductId(referenceOrderScmItem.getProductId());
        referenceOrderScmItemData.setProductName(referenceOrderScmItem.getProductName());
        referenceOrderScmItemData
                .setReceivedQuantity(SCMUtil.convertToBigDecimal(referenceOrderScmItem.getReceivedQuantity()));
        referenceOrderScmItemData.setRequestedAbsoluteQuantity(
                SCMUtil.convertToBigDecimal(referenceOrderScmItem.getRequestedAbsoluteQuantity()));

        referenceOrderScmItemData.setFulfillmentType(fulfillmentType.name());
        referenceOrderScmItemData.setUnitOfMeasure(referenceOrderScmItem.getUnitOfMeasure());
        referenceOrderScmItemData.setReferenceOrderData(referenceOrderData);
        referenceOrderScmItemData
                .setSuggestedQuantity(SCMUtil.convertToBigDecimal(referenceOrderScmItem.getSuggestedQuantity()));
        referenceOrderScmItemData.setSuggestedQuantityBeforeMoq(referenceOrderScmItem.getSuggestedQuantityBeforeMoq());
        if (Objects.nonNull(referenceOrderScmItem.getPredictedQuantity())) {
            referenceOrderScmItemData
                    .setPredictedQuantity(SCMUtil.convertToBigDecimal(referenceOrderScmItem.getPredictedQuantity()));
        }
        List<RoScmItemExpiryData> roScmItemExpiryDataList = new ArrayList<>();
        if (Objects.nonNull(referenceOrderScmItem.getExpiryDrillDown())) {
            Map<String, BigDecimal> expiryDrillDown = referenceOrderScmItem.getExpiryDrillDown();
            for (Map.Entry<String, BigDecimal> entry : expiryDrillDown.entrySet()) {
                try {
                    RoScmItemExpiryData roScmItemExpiryData = new RoScmItemExpiryData();
                    roScmItemExpiryData.setScmItemId(referenceOrderScmItemData);
                    roScmItemExpiryData.setExpiryDate(AppUtils.getDate(entry.getKey().split("_")[0], "yyyy-MM-dd"));
                    roScmItemExpiryData.setStockType(getStockType(entry.getKey().split("_")[1]));
                    roScmItemExpiryData.setQuantity(entry.getValue());
                    roScmItemExpiryDataList.add(roScmItemExpiryData);
                } catch (Exception e) {
                    LOG.error("Exception occurred while saving the reference order Scm Item Expiry DrillDown");
                }
            }
        }
        if (Objects.nonNull(referenceOrderScmItem.getAcknowledgedRoExpiryDrillDown()) && !referenceOrderScmItem.getAcknowledgedRoExpiryDrillDown().isEmpty()) {
            for (Map.Entry<String, Map<String, BigDecimal>> entry : referenceOrderScmItem.getAcknowledgedRoExpiryDrillDown().entrySet()) {
                for (Map.Entry<String, BigDecimal> innerEntry : entry.getValue().entrySet()) {
                    try {
                        RoScmItemExpiryData roScmItemExpiryData = new RoScmItemExpiryData();
                        roScmItemExpiryData.setScmItemId(referenceOrderScmItemData);
                        roScmItemExpiryData.setExpiryDate(AppUtils.getDate(innerEntry.getKey() , "yyyy-MM-dd"));
                        roScmItemExpiryData.setStockType(SCMServiceConstants.ACK_RO_IN_TRANSIT);
                        roScmItemExpiryData.setQuantity(innerEntry.getValue());
                        roScmItemExpiryData.setFulfilmentDate(AppUtils.getDate(entry.getKey() , "yyyy-MM-dd"));
                        roScmItemExpiryDataList.add(roScmItemExpiryData);
                    } catch (Exception e) {
                        LOG.error("Exception occurred while saving the reference order Scm Item Expiry DrillDown - ACK", e);
                    }
                }
            }
        }
        referenceOrderScmItemData.setRoScmItemExpiryData(roScmItemExpiryDataList);
        if (Objects.nonNull(referenceOrderScmItem.getReason())) {
            referenceOrderScmItemData.setReason(referenceOrderScmItem.getReason());
        }
        referenceOrderScmItemData.setAdjustedQuantity(referenceOrderScmItem.getAdjustedQuantity());
        if(Objects.nonNull(referenceOrderScmItem.getStockOutRaw()) && Objects.nonNull(referenceOrderScmItem.getCafeTotalHours())){
            referenceOrderScmItemData.setStockOutPercentage(AppUtils.multiply(AppUtils.divide(referenceOrderScmItem.getStockOutRaw(),
                    referenceOrderScmItem.getCafeTotalHours()),BigDecimal.valueOf(100)));
        }
        if(Objects.nonNull(referenceOrderScmItem.getWastageRaw()) && Objects.nonNull(referenceOrderScmItem.getTotalConsumption())){
            referenceOrderScmItemData.setWastagePercentage(AppUtils.multiply(AppUtils.divide(referenceOrderScmItem.getWastageRaw(),
                    referenceOrderScmItem.getTotalConsumption()),BigDecimal.valueOf(100)));
        }
        referenceOrderScmItemData.setStockOutRaw(referenceOrderScmItem.getStockOutRaw());
        referenceOrderScmItemData.setWastageRaw(referenceOrderScmItem.getWastageRaw());
        referenceOrderScmItemData.setTotalCafeHours(referenceOrderScmItem.getCafeTotalHours());
        referenceOrderScmItemData.setConsumption(referenceOrderScmItem.getOriginalConsumption());
        referenceOrderScmItemData.setComments(referenceOrderScmItem.getComments());
        if (Objects.nonNull(expiryUsageLogs) && expiryUsageLogs.containsKey(referenceOrderScmItem.getProductId())) {
            referenceOrderScmItemData.setExpiryUsageLogs(String.join("#$", expiryUsageLogs.getOrDefault(referenceOrderScmItem.getProductId(), new ArrayList<>())));
        }
        return referenceOrderScmItemData;
    }

    private static String getStockType(String type) {
        if (type.equalsIgnoreCase("inTransit")) {
            return "IN_TRANSIT";
        } else {
            return "IN_STOCK";
        }
    }

    public static ReferenceOrderScmItem convert(ReferenceOrderScmItemData referenceOrderScmItemData) {
        ReferenceOrderScmItem referenceOrderScmItem = new ReferenceOrderScmItem();
        referenceOrderScmItem.setId(referenceOrderScmItemData.getId());
        referenceOrderScmItem.setProductName(referenceOrderScmItemData.getProductName());
        referenceOrderScmItem.setProductId(referenceOrderScmItemData.getProductId());
        if (referenceOrderScmItemData.getReceivedQuantity() != null) {
            referenceOrderScmItem.setReceivedQuantity(referenceOrderScmItemData.getReceivedQuantity().floatValue());
        }
        referenceOrderScmItem
                .setRequestedAbsoluteQuantity(referenceOrderScmItemData.getRequestedAbsoluteQuantity().floatValue());
        referenceOrderScmItem.setRequestedQuantity(referenceOrderScmItemData.getRequestedQuantity().floatValue());
        if (referenceOrderScmItemData.getTransferredQuantity() != null) {
            referenceOrderScmItem
                    .setTransferredQuantity(referenceOrderScmItemData.getTransferredQuantity().floatValue());
        }
        referenceOrderScmItem.setUnitOfMeasure(referenceOrderScmItemData.getUnitOfMeasure());
        referenceOrderScmItem
                .setFulfillmentType(FulfillmentType.valueOf(referenceOrderScmItemData.getFulfillmentType()));
        referenceOrderScmItem.setSuggestedQuantity(referenceOrderScmItemData.getSuggestedQuantity() == null ? 0.0F
                : referenceOrderScmItemData.getSuggestedQuantity().floatValue());
        if (Objects.nonNull(referenceOrderScmItemData.getPredictedQuantity())) {
            referenceOrderScmItem.setPredictedQuantity(referenceOrderScmItemData.getPredictedQuantity().floatValue());
        }
        if (Objects.nonNull(referenceOrderScmItemData.getReason())) {
            referenceOrderScmItem.setReason(referenceOrderScmItemData.getReason());
        }
        return referenceOrderScmItem;
    }

    public static StockEventAssetMappingDefinition convert(AssetDefinition assetDefinition, StockEventAssetMappingDefinitionRequest request) {
        StockEventAssetMappingDefinition stockEventAssetMappingDefinition = new StockEventAssetMappingDefinition();
        stockEventAssetMappingDefinition.setUnitId(assetDefinition.getUnitId());
        stockEventAssetMappingDefinition.setEventId(request.getEventId());
        stockEventAssetMappingDefinition.setAssetStatus(assetDefinition.getAssetStatus());
        stockEventAssetMappingDefinition.setAssetId(assetDefinition.getAssetId());
        stockEventAssetMappingDefinition.setAssetTagValue(assetDefinition.getTagValue());
        return stockEventAssetMappingDefinition;
    }

    public static TransferOrderData convert(TransferOrder transferOrder, RequestOrderData requestOrderData,
                                            boolean setChildEntity, MasterDataCache cache) {
        TransferOrderData transferOrderData = new TransferOrderData();
		/*
			To handle cases where thansfer order is not recieved from frontend
		 */

		if(transferOrder.getToType() == null){
			transferOrder.setToType(TransferOrderType.REGULAR_TRANSFER);
		}
        if (transferOrder.getBudgetType() != null) {
            transferOrderData.setType(transferOrder.getBudgetType());
        }
		transferOrderData.setToType(transferOrder.getToType().value());
		transferOrderData.setStatus(transferOrder.getStatus().value());
		transferOrderData.setGeneratedBy(transferOrder.getGeneratedBy().getId());
		transferOrderData.setComment(transferOrder.getComment());
		transferOrderData.setGenerationTime(transferOrder.getGenerationTime());
		transferOrderData.setLastUpdatedBy(transferOrder.getLastUpdatedBy().getId());
		transferOrderData.setId(transferOrder.getId());
		transferOrderData.setComment(transferOrder.getComment());
		transferOrderData.setLastUpdateTime(transferOrder.getLastUpdateTime());
		transferOrderData.setInitiationTime(transferOrder.getInitiationTime());
		transferOrderData.setGeneratedInvoiceId(transferOrder.getInvoiceId());
		if (requestOrderData != null) {
			transferOrderData.setRequestOrderData(requestOrderData);
			transferOrderData.setGeneratedForUnitId(requestOrderData.getRequestUnitId());
			transferOrderData.setGenerationUnitId(requestOrderData.getFulfillmentUnitId());
			transferOrderData.setSourceCompanyId(requestOrderData.getFulfillmentCompanyId());
			transferOrderData.setReceivingCompanyId(requestOrderData.getRequestCompanyId());
		}
		if (requestOrderData == null) {
			transferOrderData.setGeneratedForUnitId(transferOrder.getGeneratedForUnitId().getId());
			transferOrderData.setGenerationUnitId(transferOrder.getGenerationUnitId().getId());
			transferOrderData.setSourceCompanyId(
					cache.getUnit(transferOrder.getGenerationUnitId().getId()).getCompany().getId());
			transferOrderData.setReceivingCompanyId(
					cache.getUnit(transferOrder.getGeneratedForUnitId().getId()).getCompany().getId());
		}

		transferOrderData.setExternal(transferOrder.isExternal() ? SCMServiceConstants.SCM_CONSTANT_YES
				: SCMServiceConstants.SCM_CONSTANT_NO);

		transferOrderData.setEwayApplicable(AppUtils.setStatus(transferOrder.isEwayApplicable()));

		if (setChildEntity) {
			List<TransferOrderItemData> transferOrderItemDatas = new ArrayList<TransferOrderItemData>();
			for (TransferOrderItem transferOrderItem : transferOrder.getTransferOrderItems()) {
				RequestOrderItemData roid = null;
				if (requestOrderData != null) {
					for (RequestOrderItemData requestOrderItemData : requestOrderData.getRequestOrderItemDatas()) {
						if (transferOrderItem.getRequestOrderItemId() == requestOrderItemData.getId()) {
							roid = requestOrderItemData;
						}
					}
				}
				transferOrderItemDatas.add(convert(transferOrderItem, roid, null, false));
			}
			transferOrderData.setTransferOrderItemDatas(transferOrderItemDatas);
		}
        transferOrderData.setBulkTransferEventId(transferOrder.getBulkTransferEventId());
        if(Objects.nonNull(transferOrder.geteInvoiceGenerated())){
            transferOrderData.seteInvoiceGenerated(AppUtils.setStatus(transferOrder.geteInvoiceGenerated()));
        }


		return transferOrderData;
	}

    public static BulkTransferEventData convert(BulkTransferEvent bulkTransferEvent){
        BulkTransferEventData bulkTransferEventData = new BulkTransferEventData();
        bulkTransferEventData.setBulkTransferEventId(bulkTransferEvent.getBulkTransferEventId());
        bulkTransferEventData.setRoCount(bulkTransferEvent.getRoCount());
        bulkTransferEventData.setToCount(bulkTransferEvent.getToCount());
        bulkTransferEventData.setGeneratedBy(bulkTransferEvent.getGeneratedBy().getId());
        bulkTransferEventData.setSourceCompanyId(bulkTransferEvent.getSourceCompany().getId());
        bulkTransferEventData.setGenerationUnitId(bulkTransferEvent.getGenerationUnit().getId());
        bulkTransferEventData.setInitiationTime(bulkTransferEvent.getInitiationTime());
        bulkTransferEventData.setCompletionTime(bulkTransferEvent.getCompletionTime());
        bulkTransferEventData.setStatus(bulkTransferEvent.getStatus());
        bulkTransferEventData.setIsInvoiceSet(AppUtils.setStatus(bulkTransferEvent.getInvoiceSet()));
        return bulkTransferEventData;
    }


	public static <T extends TaxationDetailDao> TaxDetail convert(T tax) {
		TaxDetail taxDetail = new TaxDetail();
		taxDetail.setCode(tax.getTaxCode());
		taxDetail.setType(tax.getTaxType());
		taxDetail.setValue(tax.getTotalTax());
		taxDetail.setPercentage(tax.getTaxPercentage());
		taxDetail.setTotal(tax.getTotalAmount());
		taxDetail.setTaxable(tax.getTaxableAmount());
		return taxDetail;
	}

	public static TransferOrderItemData convert(TransferOrderItem transferOrderItem,
			RequestOrderItemData requestOrderItemData, TransferOrderData transferOrderData, boolean setChildEntity) {
		TransferOrderItemData transferOrderItemData = new TransferOrderItemData();
		transferOrderItemData.setId(transferOrderItem.getId());
		if (setChildEntity) {
			List<SCMOrderPackagingData> scmOrderPackagingDatas = new ArrayList<SCMOrderPackagingData>();
			for (SCMOrderPackaging scmOrderPackaging : transferOrderItem.getPackagingDetails()) {
				scmOrderPackagingDatas.add(convert(scmOrderPackaging, null, null));
			}
			transferOrderItemData.setPackagingDetails(scmOrderPackagingDatas);
		}
		if (requestOrderItemData != null) {
			transferOrderItemData.setRequestOrderItemData(requestOrderItemData);
		}
		if(transferOrderItem.getProductionId() != null){
		    transferOrderItemData.setProductionId(transferOrderItem.getProductionId());
        }
		transferOrderItemData.setReceivedQuantity(SCMUtil.convertToBigDecimal(transferOrderItem.getReceivedQuantity()));
		transferOrderItemData
				.setRequestedQuantity(SCMUtil.convertToBigDecimal(transferOrderItem.getRequestedQuantity()));
        transferOrderItemData
            .setExcessQuantity(SCMUtil.convertToBigDecimal(transferOrderItem.getExcessQuantity()));
		transferOrderItemData.setRequestedAbsoluteQuantity(
				SCMUtil.convertToBigDecimal(transferOrderItem.getRequestedAbsoluteQuantity()));
		transferOrderItemData.setUnitOfMeasure(transferOrderItem.getUnitOfMeasure());
		transferOrderItemData.setSkuId(transferOrderItem.getSkuId());
		transferOrderItemData.setSkuName(transferOrderItem.getSkuName());
		transferOrderItemData.setTransferOrderData(transferOrderData);
		transferOrderItemData
				.setTransferredQuantity(SCMUtil.convertToBigDecimal(transferOrderItem.getTransferredQuantity()));
		transferOrderItemData.setAssociatedAssetId(transferOrderItem.getAssociatedAssetId());
		transferOrderItemData.setAssociatedAssetTagValue(transferOrderItem.getAssociatedAssetTagValue());
		return transferOrderItemData;
	}

	public static SCMOrderPackagingData convert(SCMOrderPackaging scmOrderPackaging,
			GoodsReceivedItemData goodsReceivedItemData, TransferOrderItemData transferOrderItemData) {
		SCMOrderPackagingData scmOrderPackagingData = new SCMOrderPackagingData();
		scmOrderPackagingData
				.setTransferredQuantity(SCMUtil.convertToBigDecimal(scmOrderPackaging.getTransferredQuantity()));
		scmOrderPackagingData.setId(scmOrderPackaging.getId());
		scmOrderPackagingData
				.setNumberOfUnitsPacked(SCMUtil.convertToBigDecimal(scmOrderPackaging.getNumberOfUnitsPacked()));
		scmOrderPackagingData
				.setNumberOfUnitsReceived(SCMUtil.convertToBigDecimal(scmOrderPackaging.getNumberOfUnitsReceived()));
		scmOrderPackagingData.setPackagingDefinitionData(convert(scmOrderPackaging.getPackagingDefinitionData()));
		scmOrderPackagingData.setReceivedQuantity(SCMUtil.convertToBigDecimal(scmOrderPackaging.getReceivedQuantity()));
		if (goodsReceivedItemData != null) {
			scmOrderPackagingData.setGoodsReceivedItemData(goodsReceivedItemData);
		}
		if (transferOrderItemData != null) {
			scmOrderPackagingData.setTransferOrderItemData(transferOrderItemData);
		}
		scmOrderPackagingData
				.setConversionRatio(scmOrderPackagingData.getPackagingDefinitionData().getConversionRatio());
		scmOrderPackagingData
				.setNumberOfUnitsRejected(SCMUtil.convertToBigDecimal(scmOrderPackaging.getNumberOfUnitsRejected()));
		scmOrderPackagingData.setRejectionReason(scmOrderPackaging.getRejectionReason());
		scmOrderPackagingData.setPricePerUnit(SCMUtil.convertToBigDecimal(scmOrderPackaging.getPricePerUnit()));
		return scmOrderPackagingData;
	}

	public static GoodsReceivedData convert(TransferOrderData transferOrderData, Integer generatedBy,
                                            RequestOrderData requestOrderData, PurchaseOrderData purchaseOrderData, boolean isAutoGen,
                                            boolean setChildEntity, SCMCache scmCache) {
		GoodsReceivedData goodsReceivedData = new GoodsReceivedData();
		goodsReceivedData.setStatus(transferOrderData.getStatus());
		goodsReceivedData.setLastUpdateTime(transferOrderData.getLastUpdateTime());
		goodsReceivedData.setGeneratedBy(generatedBy);
		goodsReceivedData.setTransferOrderData(transferOrderData);
		goodsReceivedData.setComment(null);
		goodsReceivedData.setGeneratedForUnitId(transferOrderData.getGeneratedForUnitId());
		goodsReceivedData.setReceivingCompanyId(transferOrderData.getReceivingCompanyId());
		goodsReceivedData.setGenerationTime(transferOrderData.getGenerationTime());
		goodsReceivedData.setGenerationUnitId(transferOrderData.getGenerationUnitId());
		goodsReceivedData.setSourceCompanyId(transferOrderData.getSourceCompanyId());
		goodsReceivedData.setReceivedBy(null);
		goodsReceivedData.setId(null);
		goodsReceivedData.setAutoGenerated(SCMUtil.setStatus(isAutoGen));
		if (requestOrderData != null) {
			goodsReceivedData.setRequestOrderData(requestOrderData);
		}
		/*
		 * if (purchaseOrderData != null) {
		 * goodsReceivedData.setPurchaseOrderData(purchaseOrderData); }
		 */
		goodsReceivedData.setTransferOrderData(transferOrderData);
		if (setChildEntity) {
			List<GoodsReceivedItemData> goodsReceivedItemDatas = new ArrayList<GoodsReceivedItemData>();
			for (TransferOrderItemData transferOrderItemData : transferOrderData.getTransferOrderItemDatas()) {
				goodsReceivedItemDatas.add(convert(transferOrderItemData, null, setChildEntity,scmCache));
			}
			goodsReceivedData.setGoodsReceivedItemDatas(goodsReceivedItemDatas);
		}
		goodsReceivedData.setTransferOrderType(transferOrderData.getToType());
		return goodsReceivedData;
	}

	public static GoodsReceivedItemData convert(TransferOrderItemData transferOrderItemData,
			GoodsReceivedData goodsReceivedData, boolean setChildEntity, SCMCache scmCache) {
		GoodsReceivedItemData goodsReceivedItemData = new GoodsReceivedItemData();
		goodsReceivedItemData.setId(null);
		if (transferOrderItemData.getNegotiatedUnitPrice() != null) {
			goodsReceivedItemData.setNegotiatedUnitPrice(transferOrderItemData.getNegotiatedUnitPrice());
		}
		if(transferOrderItemData.getTaxAmount() !=null){
		    goodsReceivedItemData.setTaxAmount(transferOrderItemData.getTaxAmount());
        }
		goodsReceivedItemData.setReceivedQuantity(null);
		goodsReceivedItemData.setRequestOrderItemData(transferOrderItemData.getRequestOrderItemData());
		goodsReceivedItemData.setGoodsReceivedData(goodsReceivedData);
		goodsReceivedItemData.setSkuId(transferOrderItemData.getSkuId());
		goodsReceivedItemData.setSkuName(transferOrderItemData.getSkuName());
		goodsReceivedItemData.setTransferOrderItemData(transferOrderItemData);
		goodsReceivedItemData.setTransferredQuantity(transferOrderItemData.getTransferredQuantity());
		goodsReceivedItemData.setUnitOfMeasure(transferOrderItemData.getUnitOfMeasure());
		goodsReceivedItemData.setUnitPrice(transferOrderItemData.getNegotiatedUnitPrice());
		goodsReceivedItemData.setNegotiatedUnitPrice(transferOrderItemData.getNegotiatedUnitPrice());
		goodsReceivedItemData.setExcessQuantity(transferOrderItemData.getExcessQuantity());
        SkuDefinition skuDefinition = scmCache.getSkuDefinition(goodsReceivedItemData.getSkuId());
        ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
        goodsReceivedItemData.setCategoryId(productDefinition.getCategoryDefinition().getId());
        goodsReceivedItemData.setSubCategoryId(productDefinition.getSubCategoryDefinition().getId());
		if (setChildEntity) {
			List<SCMOrderPackagingData> scmOrderPackagingDatas = new ArrayList<SCMOrderPackagingData>();
			for (SCMOrderPackagingData scmOrderPackagingData : transferOrderItemData.getPackagingDetails()) {
				scmOrderPackagingDatas.add(scmOrderPackagingData);
			}
			goodsReceivedItemData.setPackagingDetails(scmOrderPackagingDatas);

			goodsReceivedItemData.setItemDrilldowns(
					convertDrillDown(transferOrderItemData.getItemDrilldowns(), goodsReceivedItemData));
		}
		if (transferOrderItemData.getPurchaseOrderItemData() != null) {
			goodsReceivedItemData.setPurchaseOrderItemData(transferOrderItemData.getPurchaseOrderItemData());
		}
		goodsReceivedItemData.setAssociatedAssetId(transferOrderItemData.getAssociatedAssetId());
		goodsReceivedItemData.setAssociatedAssetTagValue(transferOrderItemData.getAssociatedAssetTagValue());
		return goodsReceivedItemData;
	}

	private static List<GoodsReceivedItemDrilldown> convertDrillDown(List<TransferOrderItemDrilldown> itemDrilldowns,
			GoodsReceivedItemData receivedItemData) {
		List<GoodsReceivedItemDrilldown> list = new ArrayList<>();
		itemDrilldowns.forEach(i -> list.add(convertToGoodsReceivedItemDrillDown(i, receivedItemData)));
		return list;
	}

	private static GoodsReceivedItemDrilldown convertToGoodsReceivedItemDrillDown(TransferOrderItemDrilldown i,
			GoodsReceivedItemData receivedItemData) {
		GoodsReceivedItemDrilldown gid = new GoodsReceivedItemDrilldown();
		gid.setAddTime(AppUtils.getCurrentTimestamp());
		gid.setExpiryDate(i.getExpiryDate());
		gid.setPrice(i.getPrice());
		gid.setQuantity(i.getQuantity());
		gid.setReceivedItemData(receivedItemData);
		return gid;
	}

	public static GoodsReceived convert(GoodsReceivedData goodsReceivedData, IdCodeName generatedBy,
			IdCodeName receivedBy, IdCodeName cancelledBy, IdCodeName generatedForUnit, IdCodeName generationUnit,
			boolean getChildEntity, SCMCache cache, MasterDataCache masterCache) {
		GoodsReceived goodsReceived = factory.createGoodsReceived();
		goodsReceived.setTransferOrderType(TransferOrderType.fromValue(goodsReceivedData.getTransferOrderType()));

		goodsReceived.setComment(goodsReceivedData.getComment());
		goodsReceived.setLastUpdateTime(goodsReceivedData.getLastUpdateTime());
		goodsReceived.setGeneratedBy(generatedBy);
		if (receivedBy != null) {
			goodsReceived.setReceivedBy(receivedBy);
		}
		if (cancelledBy != null) {
			goodsReceived.setCancelledBy(cancelledBy);
		}
		goodsReceived.setGeneratedForUnitId(generatedForUnit);
		goodsReceived.setReceivingCompany(
				convertToIdCodeName(masterCache.getCompany(goodsReceivedData.getReceivingCompanyId())));
		goodsReceived.setInventoryType(SCMUtil
				.isCafe(masterCache.getUnitBasicDetail(goodsReceived.getGeneratedForUnitId().getId()).getCategory())
						? PriceUpdateEntryType.PRODUCT
						: PriceUpdateEntryType.SKU);

		goodsReceived.setGenerationTime(goodsReceivedData.getGenerationTime());
		goodsReceived.setGenerationUnitId(generationUnit);
		goodsReceived
				.setSourceCompany(convertToIdCodeName(masterCache.getCompany(goodsReceivedData.getSourceCompanyId())));
		goodsReceived.setId(goodsReceivedData.getId());
		/*
		 * if (goodsReceivedData.getPurchaseOrderData() != null) {
		 * goodsReceived.setPurchaseOrderId(goodsReceivedData.
		 * getPurchaseOrderData().getId()); }
		 */
		if (goodsReceivedData.getRequestOrderData() != null) {
			goodsReceived.setRequestOrderId(goodsReceivedData.getRequestOrderData().getId());
		}
		goodsReceived.setStatus(SCMOrderStatus.valueOf(goodsReceivedData.getStatus()));
		if (goodsReceivedData.getTransferOrderData() != null) {
			goodsReceived.setTransferOrderId(goodsReceivedData.getTransferOrderData().getId());
		}
		if (getChildEntity) {
			List<GoodsReceivedItem> goodsReceivedItems = new ArrayList<GoodsReceivedItem>();
			for (GoodsReceivedItemData goodsReceivedItemData : goodsReceivedData.getGoodsReceivedItemDatas()) {
				goodsReceivedItems
						.add(convert(goodsReceivedItemData, getChildEntity, cache, goodsReceived.getInventoryType()));
			}
			goodsReceived.getGoodsReceivedItems().addAll(goodsReceivedItems);
		}
		goodsReceived.setAutoGenerated(AppUtils.getStatus(goodsReceivedData.isAutoGenerated()));
		goodsReceived.setRejectedGR(AppUtils.getStatus(goodsReceivedData.getIsRejectedGR()));
		goodsReceived.setRejectGRComment(goodsReceivedData.getRejectGRComment());
		goodsReceived.setParentGRComment(goodsReceivedData.getParentGRComment());
		if (goodsReceived.isAutoGenerated()) {
            if(Objects.nonNull(goodsReceivedData.getParentGR())){
                goodsReceived.setParentGR(goodsReceivedData.getParentGR().getId());
            }
		}
		if (goodsReceivedData.getRequestOrderData() != null) {
			goodsReceived.setSpecialOrder(goodsReceivedData.getRequestOrderData().getIsSpecialOrder()
					.equals(SCMServiceConstants.SCM_CONSTANT_YES));
		}
        if(Objects.nonNull(goodsReceivedData.getTotalAmount())){
            goodsReceived.setTotalAmount(goodsReceivedData.getTotalAmount().floatValue());
        }
		return goodsReceived;
	}

	public static GoodsReceivedItem convert(GoodsReceivedItemData goodsReceivedItemData, boolean getChildEntity,
			SCMCache cache, PriceUpdateEntryType keyType) {
		GoodsReceivedItem goodsReceivedItem = factory.createGoodsReceivedItem();
		goodsReceivedItem.setAssociatedAssetId(goodsReceivedItemData.getAssociatedAssetId());
		goodsReceivedItem.setAssociatedAssetTagValue(goodsReceivedItemData.getAssociatedAssetTagValue());
		goodsReceivedItem.setId(goodsReceivedItemData.getId());
		goodsReceivedItem.setKeyType(keyType);
		if (goodsReceivedItemData.getRequestOrderItemData() != null
				&& goodsReceivedItemData.getRequestOrderItemData().getVendorId() != null) {
			VendorDetail vendorDetail = cache
					.getVendorDetail(goodsReceivedItemData.getRequestOrderItemData().getVendorId());
			goodsReceivedItem.setVendor(SCMUtil.generateIdCodeName(vendorDetail.getVendorId(),
					vendorDetail.getEntityName(), vendorDetail.getEntityName()));
		}
		if (goodsReceivedItemData.getNegotiatedUnitPrice() != null) {
			goodsReceivedItem.setNegotiatedUnitPrice(goodsReceivedItemData.getNegotiatedUnitPrice().doubleValue());
		}
		if (goodsReceivedItemData.getTaxAmount() != null) {
			goodsReceivedItem.setTaxAmount(goodsReceivedItemData.getTaxAmount().floatValue());
		}
		if (goodsReceivedItemData.getPurchaseOrderItemData() != null) {
			goodsReceivedItem.setPurchaseOrderItemId(goodsReceivedItemData.getPurchaseOrderItemData().getId());
		}
		if (goodsReceivedItemData.getReceivedQuantity() != null) {
			goodsReceivedItem.setReceivedQuantity(goodsReceivedItemData.getReceivedQuantity().floatValue());
		}
		if (goodsReceivedItemData.getRequestOrderItemData() != null) {
			goodsReceivedItem.setRequestOrderItemId(goodsReceivedItemData.getRequestOrderItemData().getId());
		}
        if (goodsReceivedItemData.getExcessQuantity() != null) {
            goodsReceivedItem.setExcessQuantity(goodsReceivedItemData.getExcessQuantity().floatValue());
        }
		goodsReceivedItem.setSkuId(goodsReceivedItemData.getSkuId());
		goodsReceivedItem.setSkuName(goodsReceivedItemData.getSkuName());
		goodsReceivedItem
				.setProductId(cache.getSkuDefinition(goodsReceivedItemData.getSkuId()).getLinkedProduct().getId());
		if (goodsReceivedItemData.getTransferOrderItemData() != null) {
			goodsReceivedItem.setTransferOrderItemId(goodsReceivedItemData.getTransferOrderItemData().getId());
            if (goodsReceivedItemData.getTransferOrderItemData().getTaxAmount() != null) {
                goodsReceivedItem.setTaxAmount(goodsReceivedItemData.getTransferOrderItemData().getTaxAmount().floatValue());
            }
		}
		goodsReceivedItem.setUnitOfMeasure(goodsReceivedItemData.getUnitOfMeasure());
		if (goodsReceivedItemData.getUnitPrice() != null) {
			goodsReceivedItem.setUnitPrice(goodsReceivedItemData.getUnitPrice().doubleValue());
		}
		goodsReceivedItem.setTransferredQuantity(goodsReceivedItemData.getTransferredQuantity().floatValue());
		if (getChildEntity) {
			List<SCMOrderPackaging> scmOrderPackagings = new ArrayList<SCMOrderPackaging>();
			for (SCMOrderPackagingData scmOrderPackagingData : goodsReceivedItemData.getPackagingDetails()) {
				scmOrderPackagings.add(convert(scmOrderPackagingData));
			}
			goodsReceivedItem.getPackagingDetails().addAll(scmOrderPackagings);
			goodsReceivedItem.getDrillDowns().addAll(convertDrillDown(goodsReceivedItemData.getItemDrilldowns()));
		}
		goodsReceivedItem.setSkuCode(cache.getSkuDefinition(goodsReceivedItemData.getSkuId()).getSkuCode());
		goodsReceivedItem.setCategory(cache
				.getProductDefinition(
						cache.getSkuDefinition(goodsReceivedItemData.getSkuId()).getLinkedProduct().getId())
				.getCategoryDefinition().getName());
        goodsReceivedItem.setCategoryId(cache
                .getProductDefinition(
                        cache.getSkuDefinition(goodsReceivedItemData.getSkuId()).getLinkedProduct().getId())
                .getCategoryDefinition().getId());
		goodsReceivedItem.setSubCategory(cache
				.getProductDefinition(
						cache.getSkuDefinition(goodsReceivedItemData.getSkuId()).getLinkedProduct().getId())
				.getSubCategoryDefinition().getName());
        if(Objects.nonNull(goodsReceivedItemData.getCalculatedAmount())){
            goodsReceivedItem.setCalculatedAmount(goodsReceivedItemData.getCalculatedAmount().floatValue());
        }
        goodsReceivedItem.setSubCategoryId(cache
                .getProductDefinition(
                        cache.getSkuDefinition(goodsReceivedItemData.getSkuId()).getLinkedProduct().getId())
                .getSubCategoryDefinition().getId());
		return goodsReceivedItem;
	}

	private static Collection<? extends InventoryItemDrilldown> convertDrillDown(
			List<GoodsReceivedItemDrilldown> itemDrilldowns) {
		List<InventoryItemDrilldown> list = new ArrayList<>();
		itemDrilldowns.forEach(i -> list.add(convertToItemDrillDown(i)));
		return list;
	}

	private static List<InventoryItemDrilldown> convertToItemDrillDown(VendorGoodsReceivedItemData i) {
		InventoryItemDrilldown itd = new InventoryItemDrilldown();
		itd.setKeyId(i.getSkuId());
		itd.setPrice(i.getUnitPrice());
		itd.setKeyType(PriceUpdateEntryType.SKU.name());
		itd.setQuantity(i.getReceivedQty());
		itd.setExpiryDate(SCMUtil.parseDateIST("9999-12-31 01:00:00"));
		// Divided by conversation ratio to get Unit Price
		itd.setPrice(SCMUtil.divideWithScale10(i.getUnitPrice(), i.getConversionRatio()));
		return Arrays.asList(itd);
	}

	private static InventoryItemDrilldown convertToItemDrillDown(GoodsReceivedItemDrilldown i) {
		InventoryItemDrilldown itd = new InventoryItemDrilldown();
        if(Objects.nonNull(i.getGoodsReceivedItemDrilldownId())){
            itd.setKeyId(i.getGoodsReceivedItemDrilldownId());
        }
		itd.setPrice(i.getPrice());
		itd.setQuantity(i.getQuantity());
		itd.setExpiryDate(i.getExpiryDate());
		return itd;
	}

	public static SCMOrderPackaging convert(SCMOrderPackagingData scmOrderPackagingData) {
		SCMOrderPackaging scmOrderPackaging = factory.createSCMOrderPackaging();
		scmOrderPackaging.setId(scmOrderPackagingData.getId());
		if (scmOrderPackagingData.getNumberOfUnitsReceived() != null) {
			scmOrderPackaging.setNumberOfUnitsReceived(scmOrderPackagingData.getNumberOfUnitsReceived().floatValue());
		}
        if (scmOrderPackagingData.getRejectionReason() != null) {
            scmOrderPackaging.setRejectionReason(scmOrderPackagingData.getRejectionReason());
        }

		if (scmOrderPackagingData.getGoodsReceivedItemData() != null) {
			scmOrderPackaging.setGoodsReceivedItemId(scmOrderPackagingData.getGoodsReceivedItemData().getId());
		}

		scmOrderPackaging.setNumberOfUnitsPacked(scmOrderPackagingData.getNumberOfUnitsPacked().floatValue());
		scmOrderPackaging.setPackagingDefinitionData(convert(scmOrderPackagingData.getPackagingDefinitionData()));
		if (scmOrderPackagingData.getReceivedQuantity() != null) {
			scmOrderPackaging.setReceivedQuantity(scmOrderPackagingData.getReceivedQuantity().floatValue());
		}

		Optional.of(scmOrderPackagingData).map(SCMOrderPackagingData::getTransferOrderItemData)
				.ifPresent(p -> scmOrderPackaging.setTransferOrderItemId(p.getId()));

		Optional.of(scmOrderPackagingData).map(SCMOrderPackagingData::getTransferredQuantity)
				.ifPresent(p -> scmOrderPackaging.setTransferredQuantity(p.floatValue()));

		scmOrderPackaging.setConversionRatio(scmOrderPackaging.getPackagingDefinitionData().getConversionRatio());
		scmOrderPackaging
				.setPricePerUnit(SCMUtil.convertToBigDecimal(scmOrderPackagingData.getPricePerUnit()).floatValue());
		return scmOrderPackaging;
	}

	public static WastageEvent convert(SCMWastageEventData wastage, SCMCache cache, String empName) {
		WastageEvent scmWastageEvent = new WastageEvent();
		scmWastageEvent.setWastageId(wastage.getWastageId());
		scmWastageEvent.setBusinessDate(wastage.getBusinessDate());
		scmWastageEvent.setGenerationTime(wastage.getGenerationTime());
		scmWastageEvent.setUnitId(wastage.getUnitId());
		scmWastageEvent.setStatus(StockEventStatus.valueOf(wastage.getStatus()));
		scmWastageEvent.setGeneratedBy(wastage.getGeneratedBy());
		scmWastageEvent.setLinkedGrId(wastage.getLinkedGRId());
		scmWastageEvent.setLinkedKettleId(wastage.getLinkedRefId());
		scmWastageEvent.setLinkedKettleIdType(wastage.getLinkedRefIdType());
		scmWastageEvent.setKettleReason(wastage.getKettleReason());
		scmWastageEvent.setType(WastageEventType.valueOf(wastage.getType()));
		scmWastageEvent.setGrReason(wastage.getGrReason());
		scmWastageEvent.setInventoryType(
				WastageEventType.PRODUCT.equals(scmWastageEvent.getType()) ? PriceUpdateEntryType.PRODUCT
						: PriceUpdateEntryType.SKU);
		if (wastage.getItems() != null) {
			for (SCMWastageData data : wastage.getItems()) {
				scmWastageEvent.getItems().add(convert(data, cache,scmWastageEvent,empName));
			}
		}
		scmWastageEvent.setEmpName(empName);
		return scmWastageEvent;
	}

	/**
	 * @param wastage
	 * @param cache
	 * @return
	 */
	private static WastageData convert(SCMWastageData wastage, SCMCache cache,WastageEvent wastageEvent,String empName) {
		WastageData data = new WastageData();
        data.setWastageEventId(wastageEvent.getWastageId());
        if (Objects.nonNull(wastageEvent.getGenerationTime())) {
            data.setGenerationTime(wastageEvent.getGenerationTime());
        }
        if (Objects.nonNull(wastageEvent.getStatus())) {
            data.setStatus(wastageEvent.getStatus().value());
        }
        if (Objects.nonNull(empName)) {
            data.setEmployeeName(empName);
        }
		data.setProduct(convert(cache.getProductDefinition(wastage.getProduct().getProductId())));
		data.setQuantity(wastage.getQuantity());
		data.setProductId(data.getProduct().getProductId());
        if (wastage.getProductionId() != null) {
            data.setProductionId(wastage.getProductionId());
        }
		if (wastage.getSku() != null) {
			SkuBasicDetail skuDef = convert(cache.getSkuDefinition(wastage.getSku().getSkuId()));
			data.setSkuId(skuDef.getSkuId());
			data.setSku(skuDef);
		}
        if (wastage.getProduct().getProductId() != null) {
            ProductDefinition productDefinition = cache.getProductDefinition(wastage.getProduct().getProductId());
            data.setSubCategory(productDefinition.getSubCategoryDefinition().getName());
        }
		data.setId(wastage.getWastageItemId());
		data.setPrice(wastage.getPrice());
        BigDecimal totalAmount = BigDecimal.ZERO;
        data.setCost(wastage.getCost());
        totalAmount = totalAmount.add(wastage.getCost());
        if (Objects.nonNull(wastage.getTax())) {
            data.setTax(wastage.getTax());
            totalAmount = totalAmount.add(wastage.getTax());
        }
        data.setTotalAmount(totalAmount);
		data.setComment(wastage.getComment());
        if (Objects.nonNull(wastage.getEnteredComment())) {
            data.setEnteredComment(wastage.getEnteredComment());
        }
		for (WastageDataDrilldown wastageDrillDownData : wastage.getItems()) {
			data.getDrillDowns().add(SCMDataConverter.convert(wastageDrillDownData));
		}
		return data;
	}

    public static VendorDetail convertShortVendor(VendorDetailData vendorDetailData, String updatedBy, String requestedBy, MasterDataCache masterDataCache) {
        VendorDetail vendorDetail = factory.createVendorDetail();

        vendorDetail.setVendorId(vendorDetailData.getVendorId());
        vendorDetail.setEntityName(StringUtils.capitalize(vendorDetailData.getEntityName()));
        vendorDetail.setFirstName(StringUtils.capitalize(vendorDetailData.getFirstName()));
        vendorDetail.setLastName(StringUtils.capitalize(vendorDetailData.getLastName()));
        vendorDetail.setType(VendorType.valueOf(vendorDetailData.getType()));
        vendorDetail.setDisclaimerAccepted(AppConstants.getValue(vendorDetailData.getDisclaimerAccepted()));
        vendorDetail.setLeadTime(vendorDetailData.getLeadTime());
        if(vendorDetailData.getTds()!=null) {
            vendorDetail.setTds(SCMServiceConstants.SCM_CONSTANT_YES.equals(vendorDetailData.getTds()));
        }
        if(vendorDetailData.getTdsDocument()!=null)
        {
            vendorDetail.setTdsDocument(convert(vendorDetailData.getTdsDocument()));
        }
        if (vendorDetailData.getPrimaryContact() != null) {
            vendorDetail.setPrimaryContact(vendorDetailData.getPrimaryContact());
        }
        if (vendorDetailData.getPrimaryEmail() != null) {
            vendorDetail.setPrimaryEmail(vendorDetailData.getPrimaryEmail());
        }
        if (vendorDetailData.getSecondaryContact() != null) {
            vendorDetail.setSecondaryContact(vendorDetailData.getSecondaryContact());
        }
        if (vendorDetailData.getSecondaryEmail() != null) {
            vendorDetail.setSecondaryEmail(vendorDetailData.getSecondaryEmail());
        }
        if(vendorDetailData.getCompanyDetails() != null && vendorDetailData.getCompanyDetails().getCreditDays() != null) {
            vendorDetail.setCreditDays(vendorDetailData.getCompanyDetails().getCreditDays());
        }
        if(vendorDetailData.getCompanyDetails() != null && vendorDetailData.getCompanyDetails().getCompanyDetailId() != null) {
            vendorDetail.setCompanyId(vendorDetailData.getCompanyDetails().getCompanyDetailId());
        }
        vendorDetail.setStatus(VendorStatus.valueOf(vendorDetailData.getStatus()));
        vendorDetail.setUpdatedBy(SCMUtil.generateIdCodeName(vendorDetailData.getUpdatedBy(), "", updatedBy));
        vendorDetail.setUpdateTime(vendorDetailData.getUpdatedAt());
        vendorDetail.setRequestedBy(SCMUtil.generateIdCodeName(vendorDetailData.getRequestedBy(), "", requestedBy));
        if (Objects.nonNull(vendorDetailData.getVendorBlocked())) {
            vendorDetail.setVendorBlocked(vendorDetailData.getVendorBlocked());
        }
        if (Objects.nonNull(vendorDetailData.getVendorBlockedReason())) {
            vendorDetail.setBlockedReason(vendorDetailData.getVendorBlockedReason());
        }
        if (Objects.nonNull(vendorDetailData.getUnblockedTillDate())) {
            vendorDetail.setUnblockedTillDate(vendorDetailData.getUnblockedTillDate());
        }
        if (Objects.nonNull(vendorDetailData.getLastBlockedBy())) {
            vendorDetail.setLastBlockedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(vendorDetailData.getLastBlockedBy()), vendorDetailData.getLastBlockedBy()));
        }
        if (Objects.nonNull(vendorDetailData.getLastBlockedDate())) {
            vendorDetail.setLastBlockedDate(vendorDetailData.getLastBlockedDate());
        }
        if (Objects.nonNull(vendorDetailData.getLastUnBlockedDate())) {
            vendorDetail.setLastUnBlockedDate(vendorDetailData.getLastUnBlockedDate());
        }
        if (Objects.nonNull(vendorDetailData.getLastUnBlockedBy())) {
            vendorDetail.setLastUnBlockedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(vendorDetailData.getLastUnBlockedBy()), vendorDetailData.getLastUnBlockedBy()));
        }
        vendorDetail.setIsCCVendor(vendorDetailData.getIsCCVendor());
        vendorDetail.setIsEcomParty(vendorDetailData.getIsEcomParty());
        return vendorDetail;
    }


    public static VendorDetail convertShortVendor(VendorDetail vendorDetailData, String updatedBy, String requestedBy, MasterDataCache masterDataCache) {
        VendorDetail vendorDetail = factory.createVendorDetail();

        vendorDetail.setVendorId(vendorDetailData.getVendorId());
        vendorDetail.setEntityName(StringUtils.capitalize(vendorDetailData.getEntityName()));
        vendorDetail.setFirstName(StringUtils.capitalize(vendorDetailData.getFirstName()));
        vendorDetail.setLastName(StringUtils.capitalize(vendorDetailData.getLastName()));
        vendorDetail.setType(vendorDetailData.getType());
        vendorDetail.setDisclaimerAccepted(vendorDetailData.isDisclaimerAccepted());
        vendorDetail.setLeadTime(vendorDetailData.getLeadTime());
        if(vendorDetailData.getTds()!=null) {
            vendorDetail.setTds(SCMServiceConstants.SCM_CONSTANT_YES.equals(vendorDetailData.getTds()));
        }
        if(vendorDetailData.getTdsDocument()!=null)
        {
            vendorDetail.setTdsDocument(vendorDetailData.getTdsDocument());
        }
        if (vendorDetailData.getPrimaryContact() != null) {
            vendorDetail.setPrimaryContact(vendorDetailData.getPrimaryContact());
        }
        if (vendorDetailData.getPrimaryEmail() != null) {
            vendorDetail.setPrimaryEmail(vendorDetailData.getPrimaryEmail());
        }
        if (vendorDetailData.getSecondaryContact() != null) {
            vendorDetail.setSecondaryContact(vendorDetailData.getSecondaryContact());
        }
        if (vendorDetailData.getSecondaryEmail() != null) {
            vendorDetail.setSecondaryEmail(vendorDetailData.getSecondaryEmail());
        }
        vendorDetail.setCreditDays(vendorDetailData.getCreditDays());
        vendorDetail.setCompanyId(vendorDetailData.getCompanyId());
        vendorDetail.setStatus(vendorDetailData.getStatus());
        vendorDetail.setUpdatedBy(vendorDetailData.getUpdatedBy());
        vendorDetail.setUpdateTime(vendorDetailData.getUpdateTime());
        vendorDetail.setRequestedBy(vendorDetailData.getRequestedBy());
        if (Objects.nonNull(vendorDetailData.getVendorBlocked())) {
            vendorDetail.setVendorBlocked(vendorDetailData.getVendorBlocked());
        }
        vendorDetail.setBlockedReason(vendorDetailData.getBlockedReason());

        if (Objects.nonNull(vendorDetailData.getUnblockedTillDate())) {
            vendorDetail.setUnblockedTillDate(vendorDetailData.getUnblockedTillDate());
        }
        if (Objects.nonNull(vendorDetailData.getLastBlockedBy())) {
            vendorDetail.setLastBlockedBy(vendorDetailData.getLastBlockedBy());
        }
        if (Objects.nonNull(vendorDetailData.getLastBlockedDate())) {
            vendorDetail.setLastBlockedDate(vendorDetailData.getLastBlockedDate());
        }
        if (Objects.nonNull(vendorDetailData.getLastUnBlockedDate())) {
            vendorDetail.setLastUnBlockedDate(vendorDetailData.getLastUnBlockedDate());
        }
        if (Objects.nonNull(vendorDetailData.getLastUnBlockedBy())) {
            vendorDetail.setLastUnBlockedBy(vendorDetailData.getLastUnBlockedBy());
        }
        vendorDetail.setIsCCVendor(vendorDetailData.getIsCCVendor());
        vendorDetail.setIsEcomParty(vendorDetailData.getIsEcomParty());
        return vendorDetail;
    }


	public static VendorDetail convertVendor(VendorDetailData vendorDetailData, String updatedBy, String requestedBy) {
		VendorDetail vendorDetail = factory.createVendorDetail();

		vendorDetail.setVendorId(vendorDetailData.getVendorId());
		vendorDetail.setEntityName(StringUtils.capitalize(vendorDetailData.getEntityName()));
		vendorDetail.setFirstName(StringUtils.capitalize(vendorDetailData.getFirstName()));
		vendorDetail.setLastName(StringUtils.capitalize(vendorDetailData.getLastName()));
		vendorDetail.setType(VendorType.valueOf(vendorDetailData.getType()));
		vendorDetail.setVendorAddress(convert(vendorDetailData.getVendorAddress()));
		vendorDetail.setDisclaimerAccepted(AppConstants.getValue(vendorDetailData.getDisclaimerAccepted()));
		vendorDetail.setLeadTime(vendorDetailData.getLeadTime());

        if (vendorDetailData.getAccountDetails() != null) {
            vendorDetail.setAccountDetails(convert(vendorDetailData.getAccountDetails()));
        }
        if (vendorDetailData.getCompanyDetails() != null) {
            vendorDetail.setCreditPeriodDays(vendorDetailData.getCompanyDetails().getCreditDays());
        }

        List<VendorDebitBalanceVO> vos = new ArrayList<>();
        if (vendorDetailData.getDebitMappings() == null || vendorDetailData.getDebitMappings().size() == 0) {
            vendorDetail.setVos(vos);
        } else {
            for (VendorCompanyDebitMapping mapping : vendorDetailData.getDebitMappings()) {
                vos.add(convert(mapping));
            }
            vendorDetail.setVos(vos);
        }

        if (vendorDetailData.getCompanyDetails() != null) {
            vendorDetail.setCompanyDetails(convert(vendorDetailData.getCompanyDetails()));
        }

		if (vendorDetailData.getDispatchLocations() != null && !vendorDetailData.getDispatchLocations().isEmpty()) {
			Set<VendorDispatchLocation> locationSet = vendorDetailData.getDispatchLocations().stream()
					//.filter(location -> location.getStatus().equalsIgnoreCase(SwitchStatus.ACTIVE.name()))
					.map(vendorDispatchLocation -> convert(vendorDispatchLocation))
					.collect(Collectors.<VendorDispatchLocation>toSet());
			vendorDetail.getDispatchLocations().addAll(locationSet);
		}

        if (vendorDetailData.getPrimaryContact() != null) {
            vendorDetail.setPrimaryContact(vendorDetailData.getPrimaryContact());
        }
        if (vendorDetailData.getPrimaryEmail() != null) {
            vendorDetail.setPrimaryEmail(vendorDetailData.getPrimaryEmail());
        }
        if (vendorDetailData.getSecondaryContact() != null) {
            vendorDetail.setSecondaryContact(vendorDetailData.getSecondaryContact());
        }
        if (vendorDetailData.getSecondaryEmail() != null) {
            vendorDetail.setSecondaryEmail(vendorDetailData.getSecondaryEmail());
        }
        vendorDetail.setStatus(VendorStatus.valueOf(vendorDetailData.getStatus()));
        vendorDetail.setUpdatedBy(SCMUtil.generateIdCodeName(vendorDetailData.getUpdatedBy(), "", updatedBy));
        vendorDetail.setUpdateTime(vendorDetailData.getUpdatedAt());
        vendorDetail.setRequestedBy(SCMUtil.generateIdCodeName(vendorDetailData.getRequestedBy(), "", requestedBy));
        if (Objects.nonNull(vendorDetailData.getVendorBlocked())) {
            vendorDetail.setVendorBlocked(vendorDetailData.getVendorBlocked());
        }
        if (Objects.nonNull(vendorDetailData.getVendorBlockedReason())) {
            vendorDetail.setBlockedReason(vendorDetailData.getVendorBlockedReason());
        }
        if (Objects.nonNull(vendorDetailData.getUnblockedTillDate())) {
            vendorDetail.setUnblockedTillDate(vendorDetailData.getUnblockedTillDate());
        }
        vendorDetail.setIsCCVendor(vendorDetailData.getIsCCVendor());
        vendorDetail.setIsEcomParty(vendorDetailData.getIsEcomParty());
        vendorDetail.setCanCreateContract(vendorDetailData.getCanCreateContract());
        vendorDetail.setDocumentId(vendorDetailData.getDocumentId());
        return vendorDetail;
    }
    public static VendorEditedData convertVendorEditedDetailtoDomainData(List<VendorEditedDetail> vendorEditedDetail)
    {
        if(vendorEditedDetail==null)
            return null;
        VendorEditedData vendorEditedData=new VendorEditedData();

        for(VendorEditedDetail vendorEditedDetail1 :vendorEditedDetail)
        {
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ENTITY_NAME.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setEntityName(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.FIRST_NAME.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setFirstName(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.LAST_NAME.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setLastName(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.PRIMARY_CONTACT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setPrimaryContact(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.SECONDARY_CONTACT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setSecondaryContact(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.PRIMARY_EMAIL.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setPrimaryEmail(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.SECONDARY_EMAIL.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setSecondaryEmail(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.VENDOR_ADDRESS.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setVendorAddress(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.MSME_REGISTERED.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setMsmeRegistered(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.CIN.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCin(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.CIN_DOCUMENT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCinDocument(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.COMPANY_NAME.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCompanyName(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.COMPANY_TYPE.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCompanyType(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.PAN.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setPan(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.MSME_DOCUMENT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setMsmeDocument(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.VAT_DOCUMENT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setVatDocument(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.PAN_DOCUMENT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setPanDocument(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.REGISTERED_NAME.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setRegisteredName(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.COMPANRY_ADDRESS.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCompanyAddress(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ACCOUNT_CONTACT_NAME.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setAccountContactName(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ACCOUNT_CONTACT_EMAIL.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setAccountContactEmail(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ACCOUNT_NUMBAR.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setAccountNumber(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.IFSC_CODE.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setIfscCode(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ACCOUNT_KIND.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setAccountKind(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.CONTACT_NUMBER.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setAccountContact(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.CANCELED_CHEQUE.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCancelledCheque(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.MICRE_CODE.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setMicreCode(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.NOTIFICATION.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setNotificationType(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.GSTIN.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setGstin(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.GSTIN_DOCUMENT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setGstinDocument(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.TIN.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setTin(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.APPLY_TAX.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setApplyTax(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.DISPATCH_ADDRESS.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setAddress(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.EMAIL_ID.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setDispatchEmailID(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.LOCATION_NAME.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setAddress(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.CST.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCst(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.CST_DOCUMENT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCstDocument(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ACCOUNT_TYPE.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setAccountType(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ENTITY_TYPE.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setEntityType(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.COMPANY_ADDRESS.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setCompanyAddress(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.GST_STATUS.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setGstStatus(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ARC_DOCUMENT.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setArcDocument(true);
            }
            if(vendorEditedDetail1.getFieldName().equals(VendorEditedFieldType.ARC.name()) && vendorEditedDetail1.getStatus().equals(VendorEditedFieldStatus.UPDATED.name()))
            {
                vendorEditedData.setArc(true);
            }

        }
        return  vendorEditedData;
    }

    public static VendorAccountDetail convert(VendorAccountDetailData detailData) {
        VendorAccountDetail accountDetail = new VendorAccountDetail();

		accountDetail.setAccountType(VendorAccountType.valueOf(detailData.getAccountType()));
		accountDetail.setUpdatedBy(SCMUtil.generateIdCodeName(detailData.getUpdatedBy(), "", ""));
		accountDetail.setUpdateTime(detailData.getUpdatedAt());
		accountDetail.setAccountId(detailData.getAccountDetailId());
		accountDetail.setAccountContact(detailData.getContactNumber());
		accountDetail.setAccountContactEmail(detailData.getAccountContactEmail());
		accountDetail.setAccountContactName(StringUtils.capitalize(detailData.getAccountContactName()));
		accountDetail.setAccountNumber(detailData.getAccountNumber());
		accountDetail.setIfscCode(StringUtils.upperCase(detailData.getIfscCode()));
		accountDetail.setMicrCode(detailData.getMicrCode());
		accountDetail.setKindOfAccount(BankAccountType.valueOf(detailData.getAccountKind()));
		if (detailData.getUploadedChequeDocumentID() != null) {
			accountDetail.setCancelledCheque(convert(detailData.getUploadedChequeDocumentID()));
		}
		VendorDetailData vendorDetail = detailData.getVendorDetail();
		if (vendorDetail != null) {
			IdCodeName vendor = SCMUtil.generateIdCodeName(vendorDetail.getVendorId(), vendorDetail.getType(),
					vendorDetail.getEntityName());
			accountDetail.setVendorDetail(vendor);
		}
		accountDetail.setPaymentBlocked(SCMUtil.getStatus(detailData.getPaymentBlocked()));
        if (Objects.nonNull(detailData.getSection206())) {
            accountDetail.setSection206(detailData.getSection206());
        }
		return accountDetail;
	}

	public static DocumentDetail convert(DocumentDetailData detailData) {
		if (detailData == null) {
			return null;
		}
		DocumentDetail detail = new DocumentDetail();
		detail.setDocumentId(detailData.getDocumentId());
		detail.setFileUrl(detailData.getFileUrl());
		detail.setDocumentLink(detailData.getDocumentLink());
		detail.setUpdatedBy(SCMUtil.generateIdCodeName(detailData.getUpdatedBy(), "", ""));
		detail.setFileType(FileType.valueOf(detailData.getFileType()));
		detail.setMimeType(MimeType.valueOf(detailData.getMimeType()));
		detail.setUploadType(DocUploadType.valueOf(detailData.getDocumentUploadType()));
		detail.setUploadTypeId(detailData.getDocumentUploadTypeId());
		detail.setS3Bucket(detailData.getS3Bucket());
		detail.setS3Key(detailData.getS3Key());
		detail.setDocumentLink(detailData.getDocumentLink());
		detail.setUpdateTime(detailData.getUpdateTime());
		return detail;
	}

    public static VendorCompanyDetail convert(VendorCompanyDetailData companyDetails) {
        VendorCompanyDetail detail = new VendorCompanyDetail();

        detail.setUpdateTime(companyDetails.getUpdatedAt());
        detail.setUpdatedBy(SCMUtil.generateIdCodeName(companyDetails.getUpdatedBy(), "", ""));
        detail.setCompanyId(companyDetails.getCompanyDetailId());
        detail.setCin(companyDetails.getCIN() != null ? StringUtils.upperCase(companyDetails.getCIN()) : null);
        detail.setPan(companyDetails.getPAN() != null ? StringUtils.upperCase(companyDetails.getPAN()) : null);
        detail.setPanStatus(companyDetails.getPanStatus() != null ? companyDetails.getPanStatus() : null);
        detail.setCst(companyDetails.getCST() != null ? StringUtils.upperCase(companyDetails.getCST()) : null);
        detail.setCompanyName(StringUtils.capitalize(companyDetails.getCompanyName()));
        detail.setRegisteredName(StringUtils.capitalize(companyDetails.getCompanyName()));
        detail.setArc(companyDetails.getARC() != null ? StringUtils.upperCase(companyDetails.getARC()) : null);
        detail.setEntityType(ConstitutionalEntityType.valueOf(companyDetails.getCompanyType()));
        detail.setBusinessType(BusinessType.valueOf(companyDetails.getBusinessType()));
        detail.setCompanyAddress(convert(companyDetails.getCompanyAddress()));
        detail.setCreditDays(companyDetails.getCreditDays());
        detail.setExemptSupplier(SCMServiceConstants.SCM_CONSTANT_YES.equals(companyDetails.getExemptSupplier()));
        detail.setMsmeRegistered(SCMServiceConstants.SCM_CONSTANT_YES.equals(companyDetails.getMsmeRegistered()));
        VendorDetailData vendorDetailData = companyDetails.getVendorDetail();
        if (vendorDetailData != null) {
            IdCodeName vendor = SCMUtil.generateIdCodeName(vendorDetailData.getVendorId(), vendorDetailData.getType(),
                    vendorDetailData.getEntityName());
            detail.setVendorDetail(vendor);
        }

        if (companyDetails.getCstDocument() != null) {
            detail.setCstDocument(convert(companyDetails.getCstDocument()));
        }
        if (companyDetails.getPanDocument() != null) {
            detail.setPanDocument(convert(companyDetails.getPanDocument()));
        }
        if (companyDetails.getArcDocument() != null) {
            detail.setArcDocument(convert(companyDetails.getArcDocument()));
        }
        if (companyDetails.getCinDocument() != null) {
            detail.setCinDocument(convert(companyDetails.getCinDocument()));
        }
        if (companyDetails.getVatDocument() != null) {
            detail.setVatDocument(convert(companyDetails.getVatDocument()));
        }
        if (companyDetails.getServiceTaxDocument() != null) {
            detail.setServiceTaxDocument(convert(companyDetails.getServiceTaxDocument()));
        }
        if (companyDetails.getMsmeDocument() != null) {
            detail.setMsmeDocument(convert(companyDetails.getMsmeDocument()));
        }
        if (companyDetails.getMsmeExpirationDate() != null) {
            detail.setMsmeExpirationDate(companyDetails.getMsmeExpirationDate());
        }
        return detail;
    }

    public static VendorDispatchLocation convert(VendorDispatchLocationDetailData vendorDispatchLocation) {
        VendorDispatchLocation location = new VendorDispatchLocation();

        VendorDetailData vendorDetailData = vendorDispatchLocation.getVendorDetail();
        if (vendorDetailData != null) {
            IdCodeName vendor = SCMUtil.generateIdCodeName(vendorDetailData.getVendorId(), vendorDetailData.getType(),
                    vendorDetailData.getEntityName());
            location.setVendorDetail(vendor);
        }

        if (vendorDispatchLocation.getLocationAddress() != null) {
            location.setAddress(convert(vendorDispatchLocation.getLocationAddress()));
            location.setState(vendorDispatchLocation.getLocationAddress().getState());
            location.setCity(vendorDispatchLocation.getLocationAddress().getCity());
            location.setCountry(vendorDispatchLocation.getLocationAddress().getCountry());
            location.setLocationId(vendorDispatchLocation.getLocationAddress().getLocationId());
        }
        location.setContactEmail(vendorDispatchLocation.getEmailId());
        location.setGstStatus(GstApplicationStatus.valueOf(vendorDispatchLocation.getGstStatus()));
        location.setGstin(
                vendorDispatchLocation.getGSTIN() != null ? StringUtils.upperCase(vendorDispatchLocation.getGSTIN())
                        : null);
        if (vendorDispatchLocation.getGstinDocument() != null) {
            location.setGstinDocument(convert(vendorDispatchLocation.getGstinDocument()));
        }
        location.setTin(vendorDispatchLocation.getTIN() != null ? StringUtils.upperCase(vendorDispatchLocation.getTIN())
                : null);
        location.setApplyTax(SCMServiceConstants.SCM_CONSTANT_YES.equals(vendorDispatchLocation.getApplyTax()));
        location.setLocationName(StringUtils.capitalize(vendorDispatchLocation.getLocationName()));
        location.setDispatchId(vendorDispatchLocation.getDispatchLocationId());
        if (vendorDispatchLocation.getNotificationType() != null) {
            List<String> notifications = Arrays.asList(vendorDispatchLocation.getNotificationType().split(","));
            location.setNotificationType(notifications);
        }
        location.setUpdatedBy(SCMUtil.generateIdCodeName(vendorDispatchLocation.getUpdatedBy(), "", ""));
        location.setUpdateTime(vendorDispatchLocation.getUpdatedAt());
        location.setStatus(SwitchStatus.fromValue(vendorDispatchLocation.getStatus()));
        if(Objects.nonNull(vendorDispatchLocation.getLocationType())){
            location.setLocationType(LocationType.valueOf(vendorDispatchLocation.getLocationType()));
        }
        return location;
    }

	public static AddressDetail convert(AddressDetailData addressDetailData) {
		AddressDetail addressDetail = new AddressDetail();
		addressDetail.setAddressId(addressDetailData.getAddressId());
		addressDetail.setZipcode(addressDetailData.getZipcode());
		addressDetail.setLine1(StringUtils.capitalize(addressDetailData.getLine1()));
		addressDetail.setLine2(StringUtils.isNotEmpty(addressDetailData.getLine2())
				? StringUtils.capitalize(addressDetailData.getLine2())
				: null);
		addressDetail.setLine3(StringUtils.isNotEmpty(addressDetailData.getLine3())
				? StringUtils.capitalize(addressDetailData.getLine3())
				: null);
		addressDetail.setCity(StringUtils.isNotEmpty(addressDetailData.getCity())
				? StringUtils.capitalize(addressDetailData.getCity())
				: null);
		addressDetail.setCountry(StringUtils.isNotEmpty(addressDetailData.getCountry())
				? StringUtils.capitalize(addressDetailData.getCountry())
				: null);
		addressDetail.setState(addressDetailData.getState());
		addressDetail.setAddressContact(addressDetailData.getAddressContact());
		addressDetail.setAddressType(AddressType.valueOf(addressDetailData.getAddressType()));
		addressDetail.setLocationId(addressDetailData.getLocationId());
		addressDetail.setStateCode(addressDetailData.getStateCode());
		return addressDetail;
	}

    public static VendorDetailData convert(VendorDetail vendorDetail, boolean setChildEntities) {
        VendorDetailData vendorDetailData = new VendorDetailData();
        vendorDetailData.setVendorId(vendorDetail.getVendorId());
        vendorDetailData.setEntityName(StringUtils.capitalize(vendorDetail.getEntityName()));
        vendorDetailData.setFirstName(StringUtils.capitalize(vendorDetail.getFirstName()));
        vendorDetailData.setLastName(StringUtils.capitalize(vendorDetail.getLastName()));
        vendorDetailData.setStatus(vendorDetail.getStatus() != null ? vendorDetail.getStatus().name() : null);
        vendorDetailData.setType(vendorDetail.getType() != null ? vendorDetail.getType().name() : null);
        vendorDetailData
                .setRequestedBy(vendorDetail.getRequestedBy() != null ? vendorDetail.getRequestedBy().getId() : null);
        vendorDetailData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
        vendorDetailData.setUpdatedBy(vendorDetail.getUpdatedBy() != null ? vendorDetail.getUpdatedBy().getId() : null);
        vendorDetailData.setDisclaimerAccepted(AppConstants.getValue(vendorDetail.isDisclaimerAccepted()));
        vendorDetailData.setCanCreateContract(vendorDetail.getCanCreateContract());
        vendorDetailData.setDocumentId(vendorDetail.getDocumentId());

        if (StringUtils.isNotEmpty(vendorDetail.getSecondaryEmail())) {
            vendorDetailData.setSecondaryEmail(vendorDetail.getSecondaryEmail().toLowerCase());
        }
        if (StringUtils.isNotEmpty(vendorDetail.getSecondaryContact())) {
            vendorDetailData.setSecondaryContact(vendorDetail.getSecondaryContact());
        }
        if (StringUtils.isNotEmpty(vendorDetail.getPrimaryEmail())) {
            vendorDetailData.setPrimaryEmail(vendorDetail.getPrimaryEmail().toLowerCase());
        }
        if (StringUtils.isNotEmpty(vendorDetail.getPrimaryContact())) {
            vendorDetailData.setPrimaryContact(vendorDetail.getPrimaryContact());
        }

        if (vendorDetail.getVendorAddress() != null) {
            vendorDetailData.setVendorAddress(convert(vendorDetail.getVendorAddress()));
        }

        if (setChildEntities) {
            if (vendorDetail.getAccountDetails() != null) {
                vendorDetailData.setAccountDetails(convert(vendorDetail.getAccountDetails(), null));
            }
            if (vendorDetail.getCompanyDetails() != null) {
                vendorDetailData.setCompanyDetails(convert(vendorDetail.getCompanyDetails(), null));
            }
            if (vendorDetail.getDispatchLocations() != null) {
                Set<VendorDispatchLocationDetailData> locationDetailDataSet = vendorDetail.getDispatchLocations()
                        .stream().map(location -> {
                            return convert(location, null);
                        }).collect(Collectors.toSet());

                vendorDetailData.getDispatchLocations().addAll(locationDetailDataSet);
            }
        }
        vendorDetailData.setCanCreateContract(vendorDetail.getCanCreateContract());
        return vendorDetailData;
    }

    public static VendorDispatchLocationDetailData convert(VendorDispatchLocation location, VendorDetail vendorDetail) {
        VendorDispatchLocationDetailData locationDetailData = new VendorDispatchLocationDetailData();

        locationDetailData.setDispatchLocationId(location.getDispatchId());
        locationDetailData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
        locationDetailData.setUpdatedBy(
                location.getUpdatedBy() != null ? location.getUpdatedBy().getId() : SCMServiceConstants.SYSTEM_USER);

        locationDetailData.setGSTIN(location.getGstin());
        locationDetailData.setTIN(location.getTin());
        locationDetailData.setGstStatus(location.getGstStatus() != null ? location.getGstStatus().name()
                : GstApplicationStatus.UNREGISTERED.name());

        if (location.getGstinDocument() != null) {
            locationDetailData.setGstinDocument(convert(location.getGstinDocument()));
        }

        if (StringUtils.isNotEmpty(location.getContactEmail())) {
            locationDetailData.setEmailId(location.getContactEmail().toLowerCase());
        }
        locationDetailData.setApplyTax(
                location.isApplyTax() ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);

        if (location.getNotificationType() != null && !location.getNotificationType().isEmpty()) {
            String notifyBy = Joiner.on(",").join(location.getNotificationType());
            locationDetailData.setNotificationType(notifyBy);
        }

        locationDetailData.setLocationName(StringUtils.upperCase(location.getLocationName()));
        locationDetailData.setLocationAddress(convert(location.getAddress()));
        locationDetailData.setStatus(SwitchStatus.ACTIVE.name());
        if (vendorDetail != null) {
            locationDetailData.setVendorDetail(convert(vendorDetail, false));
        }
        if(Objects.nonNull(location.getLocationType())){
            locationDetailData.setLocationType(location.getLocationType().value());
        }

        return locationDetailData;
    }

    public static VendorCompanyDetailData convert(VendorCompanyDetail companyDetails, VendorDetail vendorDetail) {
        VendorCompanyDetailData companyDetailData = new VendorCompanyDetailData();

        companyDetailData.setCompanyDetailId(companyDetails.getCompanyId());
        companyDetailData.setCIN(companyDetails.getCin());
        companyDetailData.setCompanyAddress(convert(companyDetails.getCompanyAddress()));
        companyDetailData.setCompanyName(StringUtils.capitalize(companyDetails.getCompanyName()));
        companyDetailData.setPAN(companyDetails.getPan());
        companyDetailData.setPanStatus(companyDetails.getPanStatus());
        companyDetailData.setCST(companyDetails.getCst());
        companyDetailData.setRegisteredCompanyName(companyDetails.getRegisteredName());
        companyDetailData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
        companyDetailData.setARC(companyDetails.getArc());

        companyDetailData.setUpdatedBy(companyDetails.getUpdatedBy() != null ? companyDetails.getUpdatedBy().getId()
                : SCMServiceConstants.SYSTEM_USER);

        if (vendorDetail != null) {
            companyDetailData.setVendorDetail(convert(vendorDetail, false));
        }

        companyDetailData.setExemptSupplier(companyDetails.isExemptSupplier() ? SCMServiceConstants.SCM_CONSTANT_YES
                : SCMServiceConstants.SCM_CONSTANT_NO);

        companyDetailData.setMsmeRegistered(companyDetails.isMsmeRegistered() ? SCMServiceConstants.SCM_CONSTANT_YES
                : SCMServiceConstants.SCM_CONSTANT_NO);

        if (companyDetails.getCreditDays() > 0) {
            companyDetailData.setCreditDays(companyDetails.getCreditDays());
        }
        companyDetailData
                .setBusinessType(companyDetails.getBusinessType() != null ? companyDetails.getBusinessType().name()
                        : BusinessType.GOODS.name());
        companyDetailData.setCompanyType(companyDetails.getEntityType() != null ? companyDetails.getEntityType().name()
                : ConstitutionalEntityType.PRIVATE_LTD.name());

		if (companyDetails.getCstDocument() != null) {
			companyDetailData.setCstDocument(convert(companyDetails.getCstDocument()));
		}
		if (companyDetails.getPanDocument() != null) {
			companyDetailData.setPanDocument(convert(companyDetails.getPanDocument()));
		}
		if (companyDetails.getArcDocument() != null) {
			companyDetailData.setArcDocument(convert(companyDetails.getArcDocument()));
		}
		if (companyDetails.getCinDocument() != null) {
			companyDetailData.setCinDocument(convert(companyDetails.getCinDocument()));
		}
		if (companyDetails.getVatDocument() != null) {
			companyDetailData.setVatDocument(convert(companyDetails.getVatDocument()));
		}
		if (companyDetails.getServiceTaxDocument() != null) {
			companyDetailData.setServiceTaxDocument(convert(companyDetails.getServiceTaxDocument()));
		}
		if (companyDetails.getMsmeDocument() != null) {
			companyDetailData.setMsmeDocument(convert(companyDetails.getMsmeDocument()));
		}
        if(companyDetails.getMsmeExpirationDate() != null){
            companyDetailData.setMsmeExpirationDate(companyDetails.getMsmeExpirationDate());
        }
		return companyDetailData;
	}

	public static AddressDetailData convert(AddressDetail addressDetail) {

        AddressDetailData address = new AddressDetailData();

        address.setAddressId(addressDetail.getAddressId());
        if (StringUtils.isNotEmpty(addressDetail.getAddressContact())) {
            address.setAddressContact(addressDetail.getAddressContact());
        }
        address.setAddressType(addressDetail.getAddressType().name());
        address.setCity(addressDetail.getCity());
        address.setState(addressDetail.getState());
        address.setCountry(addressDetail.getCountry());
        address.setLine1(addressDetail.getLine1());
        address.setLine2(addressDetail.getLine2());
        address.setLine3(addressDetail.getLine3());
        address.setZipcode(addressDetail.getZipcode());
        address.setLocationId(addressDetail.getLocationId());
        address.setStateCode(addressDetail.getStateCode());
        return address;
    }

    public static VendorAccountDetailData convert(VendorAccountDetail accountDetails, VendorDetail vendorDetail) {

        VendorAccountDetailData accountDetailData = new VendorAccountDetailData();

        accountDetailData.setAccountDetailId(accountDetails.getAccountId());
        accountDetailData.setVendorDetail(vendorDetail != null ? convert(vendorDetail, false) : null);
        if (StringUtils.isNotEmpty(accountDetails.getAccountContactEmail())) {
            accountDetailData.setAccountContactEmail(accountDetails.getAccountContactEmail().toLowerCase());
        }
        accountDetailData.setAccountContactName(StringUtils.capitalize(accountDetails.getAccountContactName()));
        accountDetailData.setAccountDetailId(accountDetails.getAccountId());
        if (StringUtils.isNotEmpty(accountDetails.getAccountContact())) {
            accountDetailData.setContactNumber(accountDetails.getAccountContact());
        }
        accountDetailData.setIfscCode(accountDetails.getIfscCode());
        accountDetailData.setMicrCode(accountDetails.getMicrCode());
        accountDetailData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
        accountDetailData.setAccountType(accountDetails.getAccountType().name());
        accountDetailData.setAccountKind(accountDetails.getKindOfAccount().name());
        accountDetailData.setAccountNumber(accountDetails.getAccountNumber());
        accountDetailData.setUpdatedBy(accountDetails.getUpdatedBy() != null ? accountDetails.getUpdatedBy().getId()
                : SCMServiceConstants.SYSTEM_USER);

        if (accountDetails.getCancelledCheque() != null) {
            accountDetailData.setUploadedChequeDocumentID(convert(accountDetails.getCancelledCheque()));
        }
        accountDetailData.setPaymentBlocked(SCMUtil.setStatus(accountDetails.isPaymentBlocked()));
        return accountDetailData;

    }

	public static DocumentDetailData convert(DocumentDetail documentDetail) {

        DocumentDetailData documentDetailData = new DocumentDetailData();

        documentDetailData.setDocumentId(documentDetail.getDocumentId());
        documentDetailData.setUpdatedBy(documentDetail.getUpdatedBy().getId());
        documentDetailData.setDocumentLink(documentDetail.getDocumentLink());
        documentDetailData.setFileUrl(documentDetail.getFileUrl());
        documentDetailData.setDocumentUploadType(documentDetail.getUploadType().name());
        documentDetailData.setDocumentUploadTypeId(documentDetail.getUploadTypeId());
        documentDetailData.setFileType(documentDetail.getFileType().name());
        documentDetailData.setMimeType(documentDetail.getMimeType().name());
        documentDetailData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        documentDetailData.setS3Key(documentDetail.getS3Key());
        documentDetailData.setS3Bucket(documentDetail.getS3Bucket());

        return documentDetailData;
    }

    public static TransferOrder convert(TransferOrderData transferOrderData, boolean setChildEntity, SCMCache cache,
                                        MasterDataCache masterDataCache) {
        IdCodeName generatedBy = SCMUtil.generateIdCodeName(transferOrderData.getGeneratedBy(), "",
                masterDataCache.getEmployees().get(transferOrderData.getGeneratedBy()));
        IdCodeName lastUpdatedBy = null;
        if (transferOrderData.getLastUpdatedBy() != null) {
            lastUpdatedBy = SCMUtil.generateIdCodeName(transferOrderData.getLastUpdatedBy(), "",
                    masterDataCache.getEmployees().get(transferOrderData.getLastUpdatedBy()));
        }
        Unit fromUnit = masterDataCache.getUnit(transferOrderData.getGenerationUnitId());
        Unit toUnit = masterDataCache.getUnit(transferOrderData.getGeneratedForUnitId());
        TransferOrder transfer = convert(transferOrderData, generatedBy, lastUpdatedBy, toUnit, fromUnit, null,
                setChildEntity, cache, masterDataCache);
        if (transfer.isExternal() && transfer.getExternalTransferDetail().getUpdatedBy() != null) {
            IdCodeName detailUpdatedBy = transfer.getExternalTransferDetail().getUpdatedBy();
            detailUpdatedBy.setName(masterDataCache.getEmployee(detailUpdatedBy.getId()));
            transfer.getExternalTransferDetail().setUpdatedBy(detailUpdatedBy);
        }

        return transfer;
    }

    public static TransferOrder convert(TransferOrderData transferOrderData, IdCodeName generatedBy,
                                        IdCodeName lastUpdatedBy, Unit generatedForUnit, Unit generationUnit, GoodsReceivedData goodsReceivedData,
                                        boolean setChildEntity, SCMCache cache, MasterDataCache masterCache) {
        TransferOrder transferOrder = factory.createTransferOrder();

        transferOrder.setToType(TransferOrderType.fromValue(transferOrderData.getToType()));
        transferOrder.setLastUpdateTime(transferOrderData.getLastUpdateTime());
        transferOrder.setInitiationTime(transferOrderData.getInitiationTime());
        transferOrder.setComment(transferOrderData.getComment());
        transferOrder.setGenerationTime(transferOrderData.getGenerationTime());
        transferOrder.setGeneratedBy(generatedBy);
        if (lastUpdatedBy != null) {
            transferOrder.setLastUpdatedBy(lastUpdatedBy);
        }
        transferOrder.setGeneratedForUnitId(convert(generatedForUnit));
        transferOrder.setGenerationUnitId(convert(generationUnit));
        transferOrder
                .setSourceCompany(convertToIdCodeName(masterCache.getCompany(transferOrderData.getSourceCompanyId())));
        transferOrder.setReceivingCompany(
                convertToIdCodeName(masterCache.getCompany(transferOrderData.getReceivingCompanyId())));
        transferOrder.setFromState(generationUnit.getLocation().getState());
        transferOrder.setToState(generatedForUnit.getLocation().getState());
        transferOrder.setType(transferOrderData.getTransferType());
        transferOrder.setInventoryType(SCMUtil
                .isCafe(masterCache.getUnitBasicDetail(transferOrder.getGenerationUnitId().getId()).getCategory())
                ? PriceUpdateEntryType.PRODUCT
                : PriceUpdateEntryType.SKU);
        if (transferOrderData.getRequestOrderData() != null) {
            transferOrder.setRequestOrderTime(transferOrderData.getRequestOrderData().getGenerationTime());
            transferOrder.setRequestOrderId(transferOrderData.getRequestOrderData().getId());
        }
        if (goodsReceivedData != null) {
            transferOrder.setGoodsReceivedId(goodsReceivedData.getId());
        }
        transferOrder.setId(transferOrderData.getId());
        if (setChildEntity) {
            List<TransferOrderItem> transferOrderItems = new ArrayList<TransferOrderItem>();
            for (TransferOrderItemData transferOrderItemData : transferOrderData.getTransferOrderItemDatas()) {
                transferOrderItems.add(convertTO(transferOrderItemData, null, setChildEntity, cache,
                        transferOrder.getInventoryType()));
            }
            transferOrder.getTransferOrderItems().addAll(transferOrderItems);
        }
        transferOrder.setInvoiceId(transferOrderData.getGeneratedInvoiceId());
        transferOrder.setStatus(SCMOrderStatus.valueOf(transferOrderData.getStatus()));
        transferOrder.setTax(transferOrderData.getTaxAmount());
        if (transferOrderData.getTotalAmount() != null) {
            transferOrder.setTotalAmount(transferOrderData.getTotalAmount().floatValue());
        }
        if (transferOrderData.getOrderTaxes() != null) {
            for (TransferOrderTaxDetail detail : transferOrderData.getOrderTaxes()) {
                transferOrder.getTaxes().add(convert(detail));
            }
        }

        transferOrder.setExternal(SCMServiceConstants.SCM_CONSTANT_YES.equals(transferOrderData.getExternal()));
        transferOrder.setEwayApplicable(AppUtils.getStatus(transferOrderData.getEwayApplicable()));
        if (transferOrder.isExternal() && transferOrderData.getExternalTransferDetail() != null) {
            transferOrder
                    .setExternalTransferDetail(convertExtDetail(transferOrderData.getExternalTransferDetail(), cache));
        }
        if(Objects.nonNull(transferOrderData.getBulkTransferEventId())){
            transferOrder.setBulkTransferEventId(transferOrderData.getBulkTransferEventId());
        }
        if(Objects.nonNull(transferOrderData.geteInvoiceGenerated())){
            transferOrder.seteInvoiceGenerated(AppUtils.getStatus(transferOrderData.geteInvoiceGenerated()));
        }
        if(Objects.nonNull(transferOrderData.getPartialInvoiceIrn())){
            transferOrder.setPartialInvoiceIrn(transferOrderData.getPartialInvoiceIrn());
        }
        return transferOrder;
    }

   public static BulkTransferEvent convert(BulkTransferEventData bulkTransferEventData , MasterDataCache masterCache , SCMCache scmCache , Boolean setChild ){
       IdCodeName generatedBy = SCMUtil.generateIdCodeName(bulkTransferEventData.getGeneratedBy(), "",
               masterCache.getEmployees().get(bulkTransferEventData.getGeneratedBy()));
        BulkTransferEvent bulkTransferEvent = new BulkTransferEvent();
        bulkTransferEvent.setBulkTransferEventId(bulkTransferEventData.getBulkTransferEventId());
        bulkTransferEvent.setRoCount(bulkTransferEventData.getRoCount());
        bulkTransferEvent.setToCount(bulkTransferEventData.getToCount());
        bulkTransferEvent.setGeneratedBy(generatedBy);
        bulkTransferEvent.setSourceCompany(convertToIdCodeName(masterCache.getCompany(bulkTransferEventData.getSourceCompanyId())));
        bulkTransferEvent.setGenerationUnit(convert(masterCache.getUnit(bulkTransferEventData.getGenerationUnitId())));
        bulkTransferEvent.setInitiationTime(bulkTransferEventData.getInitiationTime());
        bulkTransferEvent.setCompletionTime(bulkTransferEventData.getCompletionTime());
        bulkTransferEvent.setStatus(bulkTransferEventData.getStatus());
        bulkTransferEvent.setType(bulkTransferEventData.getType());
        bulkTransferEvent.setInvoiceSet(SCMUtil.getStatus(bulkTransferEventData.getIsInvoiceSet()));
        if(setChild.equals(Boolean.TRUE)){
            List<TransferOrder> transferOrders = bulkTransferEventData.getTransferOrderDataList().stream().
                    map(transfer -> convert(transfer,false,scmCache,masterCache)).collect(Collectors.toList());
            bulkTransferEvent.setTransferOrderList(transferOrders);
        }
        return bulkTransferEvent;
    }

	public static ExternalTransferDetail convertExtDetail(ExternalTransferDetailData externalTransferDetail,
			SCMCache cache) {
		ExternalTransferDetail detail = new ExternalTransferDetail();
		detail.setStatus(SCMOrderStatus.valueOf(externalTransferDetail.getApprovalStatus()));
		detail.setLocationName(externalTransferDetail.getLocationName());
		detail.setVendorName(externalTransferDetail.getVendorName());
		detail.setVendorId(externalTransferDetail.getVendorId());
		detail.setDispatchId(externalTransferDetail.getDispatchId());
		detail.setId(externalTransferDetail.getId());
		if (externalTransferDetail.getLastUpdatedBy() != null) {
			detail.setUpdatedBy(SCMUtil.generateIdCodeName(externalTransferDetail.getLastUpdatedBy(), "", ""));
			detail.setUpdatedAt(externalTransferDetail.getLastUpdateTime());
		}
		VendorDetail vendor = cache.getVendorDetail(externalTransferDetail.getVendorId());
		Optional<VendorDispatchLocation> location = SCMUtil.getDispatchLocation(vendor,
				externalTransferDetail.getDispatchId());
		if (location.isPresent()) {
			VendorDispatchLocation vendorDispatchLocation = location.get();
			detail.setGstin(vendorDispatchLocation.getGstin());
			detail.setAddressDetail(vendorDispatchLocation.getAddress());
		}
		return detail;
	}

	/**
	 * @param unit
	 * @return
	 */
	private static IdCodeName convert(Unit unit) {
		if (unit == null) {
			return null;
		}
		IdCodeName data = new IdCodeName();
		data.setName(unit.getName());
		data.setId(unit.getId());
		return data;
	}

    public static TransferOrderItem convertTO(TransferOrderItemData transferOrderItemData,
                                              GoodsReceivedItemData goodsReceivedItemData, boolean setChildEntity, SCMCache cache,
                                              PriceUpdateEntryType type) {
        TransferOrderItem transferOrderItem = factory.createTransferOrderItem();
        transferOrderItem.setId(transferOrderItemData.getId());
        transferOrderItem.setKeyType(type);
        if (goodsReceivedItemData != null) {
            transferOrderItem.setGoodReceivedItemId(goodsReceivedItemData.getId());
        }
        if (transferOrderItemData.getNegotiatedUnitPrice() != null) {
            transferOrderItem.setNegotiatedUnitPrice(transferOrderItemData.getNegotiatedUnitPrice().doubleValue());
            transferOrderItem.setPrice(transferOrderItemData.getNegotiatedUnitPrice());
        }
        if (transferOrderItemData.getReceivedQuantity() != null) {
            transferOrderItem.setReceivedQuantity(transferOrderItemData.getReceivedQuantity().floatValue());
        }
        transferOrderItem.setExcessQuantity(transferOrderItemData.getExcessQuantity());
        transferOrderItem
                .setRequestedAbsoluteQuantity(transferOrderItemData.getRequestedAbsoluteQuantity().floatValue());
        transferOrderItem.setRequestedQuantity(transferOrderItemData.getRequestedQuantity().floatValue());
        if (transferOrderItemData.getRequestOrderItemData() != null) {
            transferOrderItem.setRequestOrderItemId(transferOrderItemData.getRequestOrderItemData().getId());
        }
        transferOrderItem.setSkuId(transferOrderItemData.getSkuId());
        transferOrderItem
                .setProductId(cache.getSkuDefinition(transferOrderItemData.getSkuId()).getLinkedProduct().getId());
        transferOrderItem.setSkuName(transferOrderItemData.getSkuName());
        if (transferOrderItemData.getTransferredQuantity() != null) {
            transferOrderItem.setTransferredQuantity(transferOrderItemData.getTransferredQuantity().floatValue());
        }
        transferOrderItem.setUnitOfMeasure(transferOrderItemData.getUnitOfMeasure());
        if (transferOrderItemData.getUnitPrice() != null) {
            transferOrderItem.setUnitPrice(transferOrderItemData.getUnitPrice().doubleValue());

        }
        transferOrderItem.setTotal(transferOrderItemData.getCalculatedAmount());
        if (setChildEntity) {
            List<SCMOrderPackaging> scmOrderPackagings = new ArrayList<SCMOrderPackaging>();
            for (SCMOrderPackagingData scmOrderPackagingData : transferOrderItemData.getPackagingDetails()) {
                scmOrderPackagings.add(convert(scmOrderPackagingData));
            }
            transferOrderItem.getPackagingDetails().addAll(scmOrderPackagings);
        }
        transferOrderItem.setCode(transferOrderItemData.getTaxCode());
        transferOrderItem.setTax(transferOrderItemData.getTaxAmount());
        transferOrderItem.setSkuCode(cache.getSkuDefinition(transferOrderItemData.getSkuId()).getSkuCode());
        for (TransferOrderItemTaxDetail detail : transferOrderItemData.getOrderItemTaxes()) {
            transferOrderItem.getTaxes().add(convert(detail));
        }
        transferOrderItem.setAssociatedAssetId(transferOrderItemData.getAssociatedAssetId());
        transferOrderItem.setAssociatedAssetTagValue(transferOrderItemData.getAssociatedAssetTagValue());
        return transferOrderItem;
    }

    public static ProductBasicDetail convert(ProductDefinition productDefinition) {
        ProductBasicDetail productBasicDetail = factory.createProductBasicDetail();

        productBasicDetail.setProductId(productDefinition.getProductId());
        productBasicDetail.setProductName(productDefinition.getProductName());
        productBasicDetail.setCategory(productDefinition.getCategoryDefinition());
        productBasicDetail.setSubCategory(productDefinition.getSubCategoryDefinition());
        productBasicDetail.setUnitOfMeasure(productDefinition.getUnitOfMeasure());
        productBasicDetail.setCode(productDefinition.getTaxCode());
        productBasicDetail.setPrice(new BigDecimal(productDefinition.getNegotiatedUnitPrice()));
        productBasicDetail.setAutoProduction(productDefinition.isAutoProduction());
        productBasicDetail.setRecipeRequired(productDefinition.isRecipeRequired());
        return productBasicDetail;
    }

    public static SkuBasicDetail convert(SkuDefinition skuDefinition) {
        SkuBasicDetail productBasicDetail = new SkuBasicDetail();
        productBasicDetail.setSkuId(skuDefinition.getSkuId());
        productBasicDetail.setSkuName(skuDefinition.getSkuName());
        productBasicDetail.setUnitOfMeasure(skuDefinition.getUnitOfMeasure());
        productBasicDetail.setPrice(new BigDecimal(skuDefinition.getNegotiatedUnitPrice()));
        return productBasicDetail;
    }

    public static ProductStockForUnit convertInventory(SCMProductInventoryData inventoryData,
                                                       SCMProductConsumptionData productConsumptionData, BigDecimal openingValue, ProductBasicDetail product) {

        ProductStockForUnit stockForUnit = new ProductStockForUnit();

        if (inventoryData != null) {
            stockForUnit.setOpening(SCMUtil.convertToBigDecimal(openingValue));
            stockForUnit.setVariance(SCMUtil.convertToBigDecimal(inventoryData.getVariance()));
            stockForUnit.setOriginalVariance(SCMUtil.convertToBigDecimal(inventoryData.getOriginalVariance()));
            stockForUnit.setUom(product.getUnitOfMeasure());
            stockForUnit.setProductId(product.getProductId());
            stockForUnit.setInventoryId(inventoryData.getStockingId());
            stockForUnit.setStockValue(SCMUtil.convertToBigDecimal(inventoryData.getActualClosing()));
            stockForUnit.setOriginalClosing(SCMUtil.convertToBigDecimal(inventoryData.getOriginalClosing()));
            stockForUnit.setExpectedValue(SCMUtil.convertToBigDecimal(inventoryData.getExpectedClosing()));
            stockForUnit.setKeyType(PriceUpdateEntryType.PRODUCT);

            BigDecimal transferred = BigDecimal.ZERO, received = BigDecimal.ZERO, wasted = BigDecimal.ZERO,
                    consumed = BigDecimal.ZERO;
            if (productConsumptionData != null) {
                transferred = SCMUtil.convertToBigDecimal(productConsumptionData.getTransferOut());
                received = SCMUtil.convertToBigDecimal(productConsumptionData.getReceived());
                wasted = SCMUtil.convertToBigDecimal(productConsumptionData.getWastage());
                consumed = SCMUtil.convertToBigDecimal(productConsumptionData.getConsumption());
            }

            stockForUnit.setTransferred(transferred);
            stockForUnit.setReceived(received);
            stockForUnit.setWasted(wasted);
            stockForUnit.setConsumption(consumed);
        }

        return stockForUnit;
    }

    public static ProductStockForUnit convertInventory(DayCloseInventoryDrillDown inventoryData) {
        ProductStockForUnit stock = new ProductStockForUnit();
        stock.setKeyType(PriceUpdateEntryType.SKU);
        stock.setInventoryId(inventoryData.getInventoryId());
        stock.setSkuId(inventoryData.getSkuId());
        stock.setProductId(inventoryData.getProductId());
        stock.setUom(inventoryData.getUnitOfMeasure());
        stock.setUnitPrice(SCMUtil.convertToBigDecimal(inventoryData.getSkuPrice()));
        stock.setStockValue(SCMUtil.convertToBigDecimal(inventoryData.getActual()));
        stock.setTransferred(SCMUtil.convertToBigDecimal(inventoryData.getTransferred()));
        stock.setExpectedValue(SCMUtil.convertToBigDecimal(inventoryData.getExpected()));
        stock.setReceived(SCMUtil.convertToBigDecimal(inventoryData.getReceived()));
        stock.setBooked(SCMUtil.convertToBigDecimal(inventoryData.getBooked()));
        stock.setWasted(SCMUtil.convertToBigDecimal(inventoryData.getWasted()));
        stock.setOpening(SCMUtil.convertToBigDecimal(inventoryData.getOpeningStock()));
        stock.setConsumption(SCMUtil.convertToBigDecimal(inventoryData.getConsumed()));
        stock.setVariance(SCMUtil.convertToBigDecimal(inventoryData.getVariance()));
        stock.setVarianceCost(SCMUtil.convertToBigDecimal(inventoryData.getVarianceCost()));
        stock.setReverseBooked(SCMUtil.convertToBigDecimal(inventoryData.getReverseBooked()));
        stock.setReverseConsumption(SCMUtil.convertToBigDecimal(inventoryData.getReverseConsumed()));
        return stock;
    }


    public static ProductStockForUnit convert(DayCloseInventoryDrillDown inventoryData, ProductDefinition definition) {
        ProductStockForUnit stock = new ProductStockForUnit();
        stock.setKeyType(PriceUpdateEntryType.SKU);
        stock.setInventoryId(inventoryData.getInventoryId());
        stock.setSkuId(inventoryData.getSkuId());
        stock.setProductId(inventoryData.getProductId());
        stock.setUom(inventoryData.getUnitOfMeasure());
        stock.setUnitPrice(SCMUtil.convertToBigDecimal(inventoryData.getSkuPrice()));
        stock.setStockValue(SCMUtil.convertToBigDecimal(inventoryData.getActual()));
        stock.setTransferred(SCMUtil.convertToBigDecimal(inventoryData.getTransferred()));
        stock.setExpectedValue(SCMUtil.convertToBigDecimal(inventoryData.getExpected()));
        if(inventoryData.getReceivedWithInitiatedGr() != null){
            stock.setReceivedWithInitiatedGr(inventoryData.getReceivedWithInitiatedGr());
        }
        stock.setReceived(SCMUtil.convertToBigDecimal(inventoryData.getReceived()));
        stock.setBooked(SCMUtil.convertToBigDecimal(inventoryData.getBooked()));
        stock.setReverseBooked(SCMUtil.convertToBigDecimal(inventoryData.getReverseBooked()));
        stock.setWasted(SCMUtil.convertToBigDecimal(inventoryData.getWasted()));
        stock.setOpening(SCMUtil.convertToBigDecimal(inventoryData.getOpeningStock()));
        stock.setConsumption(SCMUtil.convertToBigDecimal(inventoryData.getConsumed()));
        stock.setReverseConsumption(SCMUtil.convertToBigDecimal(inventoryData.getReverseConsumed()));
        stock.setVariance(SCMUtil.convertToBigDecimal(inventoryData.getVariance()));
        stock.setVarianceCost(SCMUtil.convertToBigDecimal(inventoryData.getVarianceCost()));
        stock.setCategoryDef(definition.getCategoryDefinition().getName());
        stock.setSubCategoryDef(definition.getSubCategoryDefinition().getName());
        stock.setCategoryId(definition.getCategoryDefinition().getId());
        return stock;
    }

    public static UnitVendorMapping convert(UnitVendorMappingData unitVendorMappingData, VendorDetail vendorDetail,
                                            UnitBasicDetail unitDetail) {
        UnitVendorMapping unitVendorMapping = new UnitVendorMapping();
        unitVendorMapping.setUnitVendorMappingId(unitVendorMappingData.getUnitVendorMappingId());
        unitVendorMapping
                .setVendor(SCMUtil.generateIdCodeName(vendorDetail.getVendorId(), "", vendorDetail.getEntityName()));
        unitVendorMapping.setUnit(SCMUtil.generateIdCodeName(unitDetail.getId(), "", unitDetail.getName()));
        unitVendorMapping.setMappingStatus(SwitchStatus.valueOf(unitVendorMappingData.getMappingStatus()));

        if (unitVendorMappingData.getFulfillmentType() != null
                && unitVendorMappingData.getFulfillmentType().length() > 0) {
            unitVendorMapping.setFulFillmentType(FulfillmentType.valueOf(unitVendorMappingData.getFulfillmentType()));
        }
        if (unitVendorMappingData.getSmsNotification() != null) {
            unitVendorMapping.setSmsNotification(AppUtils.getStatus(unitVendorMappingData.getSmsNotification()));
        }
        if (unitVendorMappingData.getEmailNotification() != null) {
            unitVendorMapping.setEmailNotification(AppUtils.getStatus(unitVendorMappingData.getEmailNotification()));
        }
        if (unitVendorMappingData.getNotifyDaysBefore() != null) {
            unitVendorMapping.setNoOfDays(unitVendorMappingData.getNotifyDaysBefore());
        }
        if (unitVendorMappingData.getFulfillmentLeadDays() != null) {
            unitVendorMapping.setFulfillmentLeadDays(unitVendorMappingData.getFulfillmentLeadDays());
        }
        if (unitVendorMappingData.getDispatchLocationId() != null) {
            unitVendorMapping.setDispatchLocationId(unitVendorMappingData.getDispatchLocationId());
        }
        if (unitVendorMappingData.getNotificationTime() != null
                && unitVendorMappingData.getNotificationTime().length() > 0) {
            unitVendorMapping.setNotificationTime(unitVendorMappingData.getNotificationTime());
        }
        if (Objects.nonNull(unitVendorMappingData.getDeliveryPromiseTime())) {
            unitVendorMapping.setDeliveryPromiseTime(unitVendorMappingData.getDeliveryPromiseTime());
        }
        return unitVendorMapping;
    }

    public static void convert(SkuDefinitionData data, PriceUpdateEntryData entry) {
        entry.setUnitOfMeasure(data.getUnitOfMeasure());
        entry.setKeyId(data.getSkuId());
        entry.setKeyName(data.getSkuName());
        entry.setKeyType(PriceUpdateEntryType.SKU.name());
        entry.setUnitPrice(data.getUnitPrice());
    }

    public static PriceUpdateEvent convert(PriceUpdateEventData eventData, boolean getEntries) {

        PriceUpdateEvent event = new PriceUpdateEvent();
        event.setFinalizationTime(eventData.getFinalizationTime());
        event.setFinalizedBy(eventData.getFinalizedBy());
        event.setFinalizedByName(eventData.getFinalizedByName());
        event.setEventId(eventData.getId());
        event.setEventStatus(PriceUpdateEventStatus.valueOf(eventData.getEventStatus()));
        event.setCreatedBy(eventData.getCreatedBy());
        event.setCreatedByName(eventData.getCreatedByName());
        event.setCreationTime(eventData.getCreationTime());
        event.setEventType(PriceUpdateEventType.valueOf(eventData.getEventType()));
        event.setDataFilePath(eventData.getDataFilePath());
        event.setEventActionType(PriceUpdateEventActionType.valueOf(eventData.getEventActionType()));
        event.setEventType(PriceUpdateEventType.valueOf(eventData.getEventType()));
        event.setNoOfErrors(eventData.getNoOfErrors());
        event.setNoOfRecords(eventData.getNoOfRecords());
        if (getEntries) {
            if (eventData.getEntries() != null && !eventData.getEntries().isEmpty()) {
                for (PriceUpdateEntryData entry : eventData.getEntries()) {
                    event.getEntries().add(convert(entry));
                }
            }
        }
        return event;
    }

    public static PriceUpdateEntry convert(PriceUpdateEntryData entryData) {
        PriceUpdateEntry entry = new PriceUpdateEntry();
        entry.setEditedUnitPrice(entryData.getEditedUnitPrice());
        entry.setEntryStatus(PriceUpdateEventStatus.valueOf(entryData.getEntryStatus()));
        entry.setId(entryData.getId());
        entry.setKeyId(entryData.getKeyId());
        entry.setKeyName(entryData.getKeyName());
        entry.setUnitOfMeasure(entryData.getUnitOfMeasure());
        entry.setUnitPrice(entryData.getUnitPrice());
        entry.setUpdatedUnitPrice(entryData.getUpdatedUnitPrice());
        entry.setKeyType(PriceUpdateEntryType.valueOf(entryData.getKeyType()));
        entry.setError(AppConstants.getValue(entryData.getHasError()));
        entry.setApprovedUnitPrice(entryData.getApprovedUnitPrice());
        entry.setKeyType(PriceUpdateEntryType.valueOf(entryData.getKeyType()));
        if (entryData.getDrillDowns() != null) {
            for (PriceUpdateEntryDrillDown drilldown : entryData.getDrillDowns()) {
                entry.getDrilldowns().add(convert(drilldown));
            }
        }
        if (entryData.getErrors() != null && entryData.getErrors().size() > 0) {
            for (PriceUpdateEntryError error : entryData.getErrors()) {
                entry.getErrors().add(error.getErrorMessage());
            }
        }
        return entry;
    }

    public static PriceUpdateDrillDown convert(PriceUpdateEntryDrillDown o) {
        PriceUpdateDrillDown d = new PriceUpdateDrillDown();
        d.setCost(o.getCost());
        d.setDrilldownCategory(o.getDrilldownCategory());
        d.setDrilldownType(o.getDrilldownType());
        d.setError(AppConstants.getValue(o.getHasError()));
        d.setId(o.getId());
        d.setKeyId(o.getKeyId());
        d.setKeyName(o.getKeyName());
        d.setKeyType(o.getKeyType());
        d.setQuantity(o.getQuantity());
        d.setUnitOfMeasure(o.getUnitOfMeasure());
        d.setUnitPrice(o.getUnitPrice());
        if (AppConstants.getValue(o.getHasError()) && o.getErrors() != null && o.getErrors().size() > 0) {
            for (PriceUpdateEntryError error : o.getErrors()) {
                d.getErrors().add(error.getErrorMessage());
            }
        }
        return d;
    }

    public static UnitDetail convert(UnitBasicDetail unitDetail) {
        UnitDetail unit = new UnitDetail();
        unit.setTin(unitDetail.getTin());
        unit.setUnitName(unitDetail.getName());
        unit.setUnitId(unitDetail.getId());
        unit.setUnitEmail(unitDetail.getEmail());
        unit.setUnitCategoryId(1); // for CAFE
        unit.setUnitStatus(SwitchStatus.valueOf(unitDetail.getStatus().name()));
        unit.setCompanyId(unitDetail.getCompanyId());
        unit.setUnitRegion(unitDetail.getRegion());
        return unit;
    }

    public static VendorRegistrationRequest convert(VendorRegistrationRequestDetail r) {
        VendorRegistrationRequest v = new VendorRegistrationRequest();
        v.setCopyEmails(r.getCopyEmails());
        v.setEmail(r.getEmail());
        v.setId(r.getId());
        v.setRequestById(r.getRequestById());
        v.setRequestByName(r.getRequestByName());
        v.setRequestDate(r.getRequestDate());
        v.setRequestForId(r.getRequestForId());
        v.setRequestForName(r.getRequestForName());
        v.setRequestStatus(VendorStatus.valueOf(r.getRequestStatus()));
        v.setVendorName(r.getVendorName());
        v.setVendorId(r.getVendorId());
        v.setVendorType(VendorType.valueOf(r.getVendorType()));
        v.setLink(SCMUtil.getRegistrationLink(r.getRegistrationUrl(), r.getAuthKey()));
        return v;
    }

    public static SkuPrice convertToSkuPriceData(SkuPriceData data) {
        SkuPrice price = factory.createSkuPrice();
        price.setDelivery(data.getDeliveryLocation());
        price.setDeliveryLocationId(data.getDeliveryLocationId());
        price.setDispatch(data.getDispatchLocation());
        price.setDispatchLocationId(data.getDispatchLocationId());
        price.setPkgId(data.getPackagingId());
        price.setKeyId(data.getSkuPriceKeyId());
        price.setPrice(data.getPrice());
        price.setSkuId(data.getSkuId());
        price.setStartDate(data.getStartDate());
        price.setStatus(data.getStatus());
        price.setVendorId(data.getVendorId());
        return price;

    }

    public static SkuPriceDetail convert(SCMCache cache, Map<Integer, Location> locationMap, SkuPriceData data,boolean getUpdated) {
        SkuPriceDetail price = factory.createSkuPriceDetail();
        Location deliveryLocation = locationMap.get(data.getDeliveryLocationId());
        Location dispatchLocation = locationMap.get(data.getDispatchLocationId());
        price.setDelivery(convertToIdCodeName(deliveryLocation));
        price.setDispatch(convertToIdCodeName(dispatchLocation));
        price.setPkg(convertToPackagingData(cache.getPackagingDefinition(data.getPackagingId())));
        price.setSku(convertToIdCodeName(cache.getSkuDefinition(data.getSkuId())));
        price.setVendor(convertToIdCodeName(cache.getVendorDetail(data.getVendorId())));
        price.setKeyId(data.getSkuPriceKeyId());
        price.setStatus(data.getStatus());
        price.getCurrent().setDate(data.getStartDate());
        price.getCurrent().setValue(data.getPrice());
        price.setLeadTime(data.getLeadTime());
        if (getUpdated && data.getUpdateList() != null && !data.getUpdateList().isEmpty()) {
            price.getUpdated().setValue(data.getUpdateList().get(0).getNegotiatedPrice());
            price.getUpdated().setDate(data.getUpdateList().get(0).getStartDate());
        }
        return price;
    }

    public static VendorContractItemVO convert(SCMCache cache, MasterDataCache masterDataCache, VendorContractItem data) {
        VendorContractItemVO price = factory.createVendorContractItemVO();
        price.setDelivery(convertToIdCodeName(masterDataCache.getAllLocations().get(data.getDeliveryLocation())));
        price.setDispatch(convertToIdCodeName(masterDataCache.getAllLocations().get(data.getDispatchLocation())));
        price.setPkg(convertToPackagingData(cache.getPackagingDefinition(data.getSkuPackagingId())));
        price.setSku(convertToIdCodeName(cache.getSkuDefinition(data.getSkuId())));
        price.setVendor(convertToIdCodeName(cache.getVendorDetail(data.getVendorId())));
        price.setKeyId(data.getSkuPriceDataId());
        price.getCurrent().setValue(data.getCurrentPrice());
        price.getUpdated().setValue(data.getNegotiatedPrice());
        return price;
    }

    public static List<VendorContractItemVO> convert(SCMCache cache, MasterDataCache masterDataCache, List<VendorContractItem> item) {
        List<VendorContractItemVO> newVO = new ArrayList<>();
        for (VendorContractItem data : item) {
            VendorContractItemVO price = factory.createVendorContractItemVO();
            price.setDelivery(convertToIdCodeName(masterDataCache.getAllLocations().get(data.getDeliveryLocation())));
            price.setDispatch(convertToIdCodeName(masterDataCache.getAllLocations().get(data.getDispatchLocation())));
            price.setPkg(convertToPackagingData(cache.getPackagingDefinition(data.getSkuPackagingId())));
            price.setSku(convertToIdCodeName(cache.getSkuDefinition(data.getSkuId())));
            price.setVendor(convertToIdCodeName(cache.getVendorDetail(data.getVendorId())));
            price.setKeyId(data.getSkuPriceDataId());
            price.getCurrent().setValue(data.getCurrentPrice());
            price.getUpdated().setValue(BigDecimal.valueOf(Math.round(data.getNegotiatedPrice().doubleValue() * 100.0) / 100.0));
            price.setIsNewProduct(data.getIsNew());
            price.setIsNewItem(data.getIsNewItem());
            price.setTaxPercentage(Objects.nonNull(data.getTaxPercentage()) ? String.valueOf(data.getTaxPercentage().floatValue()) : String.valueOf(BigDecimal.ZERO.floatValue()));
            newVO.add(price);
        }

        return newVO;
    }

    public static IdCodeName convertToIdCodeName(Location location) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(location.getId());
        idCodeName.setName(location.getName());
        idCodeName.setCode(location.getCode());
        return idCodeName;
    }

    public static IdCodeName convertToIdCodeName(State state) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(state.getId());
        idCodeName.setName(state.getName());
        idCodeName.setCode(state.getCode());
        return idCodeName;
    }

    /**
     * @param def
     * @return
     */
    public static IdCodeName convertToIdCodeName(VendorDetail def) {
        IdCodeName code = factory.createIdCodeName();
        code.setId(def.getVendorId());
        if (def.getCompanyDetails() != null) {
            code.setCode(def.getCompanyDetails().getCin());
        }
        code.setName(def.getEntityName());
        return code;
    }

    public static PackagingData convertToPackagingData(PackagingDefinition def) {
        PackagingData code = factory.createPackagingData();
        code.setCode(def.getPackagingCode());
        code.setId(def.getPackagingId());
        code.setName(def.getPackagingName());
        code.setRatio(def.getConversionRatio());
        code.setUom(def.getUnitOfMeasure());
        return code;
    }

    public static IdCodeName convertToIdCodeName(SkuDefinition def) {
        IdCodeName code = factory.createIdCodeName();
        code.setId(def.getSkuId());
        code.setName(def.getSkuName());
        return code;
    }

    public static PurchaseOrder convert(PurchaseOrderData purchaseOrderData, SCMCache scmCache,
                                        MasterDataCache masterCache, boolean setChild, VendorAdvancePayment advance) {
        PurchaseOrder purchaseOrder = new PurchaseOrder();

        purchaseOrder.setComment(purchaseOrderData.getComment());
        purchaseOrder.setId(purchaseOrderData.getId());
        purchaseOrder.setFulfillmentDate(purchaseOrderData.getFulfillmentDate());
        purchaseOrder.setReceiptNumber(purchaseOrderData.getReceiptNumber());
        purchaseOrder.setOrderType(purchaseOrderData.getOrderType());

        VendorDetail vendor = scmCache.getVendorDetail(purchaseOrderData.getGeneratedForVendor());

        Optional<VendorDispatchLocation> dispatchLocation = vendor.getDispatchLocations().stream()
                .filter(location -> location.getDispatchId().equals(purchaseOrderData.getDispatchLocationId()))
                .findAny();

        Unit unit = masterCache.getUnit(purchaseOrderData.getDeliveryLocationId());
        purchaseOrder.setDispatchLocation(dispatchLocation.isPresent() ? dispatchLocation.get() : null);
        purchaseOrder
                .setGeneratedForVendor(SCMUtil.generateIdCodeName(vendor.getVendorId(), "", vendor.getEntityName()));
        purchaseOrder.setDeliveryUnitId(SCMUtil.generateIdCodeName(unit.getId(), "", unit.getName()));
        purchaseOrder.setCompany(convertToIdCodeName(masterCache.getCompany(purchaseOrderData.getCompanyId())));
        if (purchaseOrderData.getApprovedBy() != null) {
            purchaseOrder.setApprovedBy(SCMUtil.generateIdCodeName(purchaseOrderData.getApprovedBy(), "",
                    masterCache.getEmployee(purchaseOrderData.getApprovedBy())));
        }

        if (purchaseOrderData.getGeneratedBy() != null) {
            purchaseOrder.setGeneratedBy(SCMUtil.generateIdCodeName(purchaseOrderData.getGeneratedBy(), "",
                    masterCache.getEmployee(purchaseOrderData.getGeneratedBy())));
        }

        if (purchaseOrderData.getLastUpdatedBy() != null) {
            purchaseOrder.setGeneratedBy(SCMUtil.generateIdCodeName(purchaseOrderData.getLastUpdatedBy(), "",
                    masterCache.getEmployee(purchaseOrderData.getLastUpdatedBy())));
        }

        purchaseOrder.setLastUpdateTime(purchaseOrderData.getLastUpdateTime());
        purchaseOrder.setBillAmount(purchaseOrderData.getBillAmount());
        purchaseOrder.setForceClosed(purchaseOrderData.getForceClosed() != null
                ? purchaseOrderData.getForceClosed().equals(SCMServiceConstants.SCM_CONSTANT_YES)
                : false);
        purchaseOrder.setVendorNotified(purchaseOrderData.getVendorNotified() != null
                ? purchaseOrderData.getVendorNotified().equals(SCMServiceConstants.SCM_CONSTANT_YES)
                : false);
        purchaseOrder.setInitiationTime(purchaseOrderData.getInitiationTime());
        purchaseOrder.setGenerationTime(purchaseOrderData.getGenerationTime());
        purchaseOrder.setTotalTaxes(purchaseOrderData.getTotalTaxes());
        purchaseOrder.setPaidAmount(purchaseOrderData.getPaidAmount());
        purchaseOrder.setStatus(PurchaseOrderStatus.valueOf(purchaseOrderData.getStatus()));
        purchaseOrder.setExpiryDate(purchaseOrderData.getExpiryDate());
        purchaseOrder.setExpiryStatus(purchaseOrderData.getExpiryStatus());
        purchaseOrder.setLeadTime(purchaseOrderData.getLeadTime());
        if (purchaseOrderData.getPoInvoiceDocument() != null) {
            purchaseOrder.setPoInvoice(convert(purchaseOrderData.getPoInvoiceDocument()));
        }
        if (purchaseOrderData.getNotificationList() != null) {
            purchaseOrder.getNotifications()
                    .addAll(purchaseOrderData.getNotificationList().stream()
                            .map(notificationData -> convert(notificationData, purchaseOrderData.getId()))
                            .collect(Collectors.toList()));
        }

        if (purchaseOrderData.getPurchaseOrderItemDatas() != null) {
            purchaseOrder.getOrderItems().addAll(purchaseOrderData.getPurchaseOrderItemDatas().stream()
                    .map(purchaseOrderItemData -> convert(purchaseOrderItemData)).collect(Collectors.toList()));
        }

        if (purchaseOrderData.getGrMappingList() != null && setChild) {
            purchaseOrder.getGoodsReceivedList()
                    .addAll(purchaseOrderData.getGrMappingList().stream()
                            .map(mapping -> convert(mapping.getVendorGoodsReceivedData(), scmCache, masterCache, false))
                            .collect(Collectors.toList()));
        }

        purchaseOrder.setCreationType(POCreationType.valueOf(purchaseOrderData.getCreationType()));
        if (purchaseOrderData.getType() != null) {
            purchaseOrder.setType(purchaseOrderData.getType());
        }
        if (Objects.nonNull(advance)) {
            purchaseOrder.setVendorAdvancePayment(advance);
        }
        return purchaseOrder;
    }

    public static VendorGR convert(VendorGoodsReceivedData vendorGoodsReceivedData, SCMCache scmCache,
                                   MasterDataCache masterCache, boolean setChild) {
        VendorGR gr = new VendorGR();
        UnitBasicDetail deliveryUnit = masterCache.getUnitBasicDetail(vendorGoodsReceivedData.getDeliveryUnitId());
        VendorDetail vendorDetail = scmCache.getVendorDetail(vendorGoodsReceivedData.getGeneratedForVendor());
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendorDetail,
                vendorGoodsReceivedData.getDispatchId());

        gr.setId(vendorGoodsReceivedData.getGoodsReceivedId());
        gr.setDeliveryUnitId(deliveryUnit);
        gr.setDispatchLocation(dispatchLocation.get());
        gr.setGeneratedForVendor(convertToBasicDetail(vendorDetail));
        gr.setBillAmount(vendorGoodsReceivedData.getTotalAmount());
        gr.setTotalTaxes(vendorGoodsReceivedData.getTotalTaxes());
        gr.setExtraCharges(vendorGoodsReceivedData.getExtraCharges());
        gr.setGrCreationType(vendorGoodsReceivedData.getCreationType());
        gr.setStatus(PurchaseOrderStatus.valueOf(vendorGoodsReceivedData.getGrStatus()));
        gr.setReceiptNumber(vendorGoodsReceivedData.getDocNumber());
        gr.setReceiptType(InvoiceDocType.valueOf(vendorGoodsReceivedData.getDocType()));
        gr.setGenerationTime(vendorGoodsReceivedData.getCreatedAt());
        gr.setInvalid(SCMUtil.getStatus(vendorGoodsReceivedData.getInvalidGR()));
        gr.setGeneratedBy(SCMUtil.generateIdCodeName(vendorGoodsReceivedData.getCreatedBy(), "",
                masterCache.getEmployee(vendorGoodsReceivedData.getCreatedBy())));
        gr.setUpdatedBy(SCMUtil.generateIdCodeName(vendorGoodsReceivedData.getUpdatedBy(), "", ""));
        if(vendorGoodsReceivedData.getApprovedBy()!=null){
            gr.setApprovedBy(SCMUtil.generateIdCodeName(vendorGoodsReceivedData.getApprovedBy(), " ",
                    masterCache.getEmployee(vendorGoodsReceivedData.getApprovedBy())));
        }
        gr.setUpdationTime(vendorGoodsReceivedData.getUpdatedAt());
        gr.setCompanyId(vendorGoodsReceivedData.getCompanyId() != null ? vendorGoodsReceivedData.getCompanyId() : 1000);
        gr.setGrDocumentDate(vendorGoodsReceivedData.getDocumentDate());
        if (vendorGoodsReceivedData.getAmountMatched() != null) {
            gr.setAmountMatched(
                    SCMServiceConstants.SCM_CONSTANT_YES.equals(vendorGoodsReceivedData.getAmountMatched()));
        }

        gr.getGrItems().addAll(vendorGoodsReceivedData.getGrItemList().stream()
                .map(grItem -> SCMDataConverter.convert(grItem, scmCache)).collect(Collectors.toList()));
        if (vendorGoodsReceivedData.getPoMappingList() != null && setChild) {
            gr.getPurchaseOrderList()
                    .addAll(vendorGoodsReceivedData.getPoMappingList().stream()
                            .map(po -> {
                                    if (po.getPurchaseOrderData().getStatus().equalsIgnoreCase(PurchaseOrderStatus.CLOSED.value())) {
                                        gr.setCanBeCancelled(AppConstants.NO);
                                    }
                                return convert(po.getPurchaseOrderData(), scmCache, masterCache, false, null);
                            })
                            .collect(Collectors.toList()));
        }
        if (vendorGoodsReceivedData.getGrDocument() != null) {
            gr.setGrDocument(convert(vendorGoodsReceivedData.getGrDocument()));
        }
        if (vendorGoodsReceivedData.getPaymentRequestData() != null) {
            gr.setPaymentRequestId(vendorGoodsReceivedData.getPaymentRequestData().getId());
        }
        gr.setToBePaid(SCMUtil.getStatus(vendorGoodsReceivedData.getToBePaid()));
        gr.setVendorGrType(VendorGrType.valueOf(vendorGoodsReceivedData.getVendorGRType()));
        if (vendorGoodsReceivedData.getType() != null) {
            gr.setType(vendorGoodsReceivedData.getType());
        }
        gr.setComment(vendorGoodsReceivedData.getComment());
        gr.setVendorInvoiceDocId(vendorGoodsReceivedData.getVendorInvoiceDocId());
        return gr;
    }

    private static VendorBasicDetail convertToBasicDetail(VendorDetail vendorDetail) {
        VendorBasicDetail vendorBasicDetail = new VendorBasicDetail();
        vendorBasicDetail.setId(vendorDetail.getVendorId());
        vendorBasicDetail.setName(vendorDetail.getEntityName());
        vendorBasicDetail.setAddressDetail(vendorDetail.getVendorAddress());
        return vendorBasicDetail;
    }

    public static VendorGRItem convert(VendorGoodsReceivedItemData itemData, SCMCache scmCache) {
        VendorGRItem grItem = new VendorGRItem();
        grItem.setId(itemData.getItemId());
        grItem.setUnitOfMeasure(itemData.getUnitOfMeasure());
        grItem.setAmountPaid(itemData.getTotalAmount());
        SkuDefinition sku = scmCache.getSkuDefinition(itemData.getSkuId());
        if (sku != null) {
            ProductDefinition product = scmCache.getProductDefinition(sku.getLinkedProduct().getId());
            if (product != null) {
                grItem.setCategory(product.getCategoryDefinition().getName());
                grItem.setSubCategory(product.getSubCategoryDefinition().getName());
            }
        }
        grItem.setTotalCost(itemData.getTotalPrice());
        grItem.setTotalTax(itemData.getTotalTax());
        grItem.setUnitPrice(itemData.getUnitPrice());
        grItem.setPackagingQty(itemData.getPackagingQuantity());
        grItem.setPackagingId(itemData.getPackagingId());
        grItem.setPackagingName(itemData.getPackagingName());
        grItem.setReceivedQuantity(itemData.getReceivedQty());
        grItem.setConversionRatio(itemData.getConversionRatio());
        grItem.setSkuName(itemData.getSkuName());
        grItem.setDrillDowns(convertToItemDrillDown(itemData));
        grItem.setSkuId(itemData.getSkuId());
        if (itemData.getExpiryDate() != null) {
            grItem.setExpiryDate(itemData.getExpiryDate());
        }
        grItem.getTaxes().addAll(itemData.getTaxes().stream().map(itemTaxDetailData -> convert(itemTaxDetailData))
                .collect(Collectors.<OtherTaxDetail>toList()));
        grItem.setHsn(itemData.getHsn());
        return grItem;
    }

    public static PurchaseOrderItem convert(PurchaseOrderItemData purchaseOrderItemData) {
        PurchaseOrderItem item = new PurchaseOrderItem();

        item.setId(purchaseOrderItemData.getId());
        item.setSkuId(purchaseOrderItemData.getSkuId());
        item.setAmountPaid(purchaseOrderItemData.getAmountPaid());
        item.setCgst(
                new PercentageDetail(purchaseOrderItemData.getCgstPercentage(), purchaseOrderItemData.getCgstValue()));
        if (Objects.nonNull(purchaseOrderItemData.getIgstPercentage()) && Objects.nonNull(purchaseOrderItemData.getIgstValue())) {
            item.setIgst(
                    new PercentageDetail(purchaseOrderItemData.getIgstPercentage(), purchaseOrderItemData.getIgstValue()));
        }
        item.setSgst(
                new PercentageDetail(purchaseOrderItemData.getSgstPercentage(), purchaseOrderItemData.getSgstValue()));
        item.setTotalCost(purchaseOrderItemData.getTotalCost());
        item.setTotalTax(purchaseOrderItemData.getTotalTax());
        item.setExemptItem(purchaseOrderItemData.getExemptItem() != null
                ? purchaseOrderItemData.getExemptItem().equals(SCMServiceConstants.SCM_CONSTANT_YES)
                : false);
        item.setHsn(purchaseOrderItemData.getHsnCode());
        item.setReceivedQuantity(purchaseOrderItemData.getReceivedQuantity());
        item.setRequestedQuantity(purchaseOrderItemData.getRequestedQuantity());
        item.setRequestedAbsoluteQuantity(purchaseOrderItemData.getRequestedAbsoluteQuantity());
        item.setTransferredQuantity(purchaseOrderItemData.getTransferredQuantity());
        item.setPackagingId(purchaseOrderItemData.getPackagingId());
        item.setPackagingName(purchaseOrderItemData.getPackagingName());
        item.setConversionRatio(purchaseOrderItemData.getConversionRatio());
        item.setPackagingQty(purchaseOrderItemData.getPackagingQuantity());
        item.setUnitPrice(purchaseOrderItemData.getUnitPrice());
        item.setNegotiatedUnitPrice(purchaseOrderItemData.getNegotiatedUnitPrice());
        item.setUnitOfMeasure(purchaseOrderItemData.getUnitOfMeasure());
        item.setSkuName(purchaseOrderItemData.getSkuName());
        if (Objects.nonNull(purchaseOrderItemData.getOtherTaxes())) {
            item.setOtherTax(purchaseOrderItemData.getOtherTaxes());
        }
        if (purchaseOrderItemData.getOtherTaxesApplied() != null && !purchaseOrderItemData.getOtherTaxesApplied().isEmpty()) {
            item.setOtherTaxes(purchaseOrderItemData.getOtherTaxesApplied().stream()
                    .map(itemTaxDetailData -> convert(itemTaxDetailData)).collect(Collectors.toList()));
        }
        return item;
    }

    public static OtherTaxDetail convert(ItemTaxDetailData itemTaxDetailData) {
        OtherTaxDetail otherTaxDetail = new OtherTaxDetail();
        otherTaxDetail.setTaxName(itemTaxDetailData.getTaxType());
        otherTaxDetail.setTaxCategory(itemTaxDetailData.getTaxType());
        otherTaxDetail.setValue(itemTaxDetailData.getTaxValue());
        otherTaxDetail.setPercentage(itemTaxDetailData.getTaxPercentage());
        return otherTaxDetail;
    }

    public static PurchaseOrderNotification convert(PurchaseOrderNotificationData notificationData, Integer poId) {
        PurchaseOrderNotification notification = new PurchaseOrderNotification();

        notification.setCarrier(notificationData.getNotificationCarrier());
        notification.setClient(notificationData.getServiceClient());
        notification.setId(notificationData.getNotificationId());
        notification.setContact(notificationData.getContact());
        notification.setSent(notificationData.getNotificationSent() != null
                ? notificationData.getNotificationSent().equals(SCMServiceConstants.SCM_CONSTANT_YES)
                : false);
        notification.setType(NotificationType.valueOf(notificationData.getVendorNotificationType()));
        notification.setMessage(notificationData.getMessage());
        notification.setDate(notificationData.getNotificationTime());
        notification.setPoId(poId);

        return notification;
    }

    public static ItemTaxDetailData convert(OtherTaxDetail otherTaxDetail) {
        ItemTaxDetailData itemTaxDetailData = new ItemTaxDetailData();
        itemTaxDetailData.setTaxPercentage(otherTaxDetail.getPercentage());
        itemTaxDetailData.setTaxType(otherTaxDetail.getTaxCategory());
        itemTaxDetailData.setTaxValue(otherTaxDetail.getValue());
        return itemTaxDetailData;
    }

    public static ProductionPlanEvent convert(ProductionPlanEventData data, SCMCache scmCache,
                                              MasterDataCache masterDataCache) {
        IdCodeName generatedBy = SCMUtil.generateIdCodeName(data.getGeneratedBy(), "",
                masterDataCache.getEmployees().get(data.getGeneratedBy()));
        IdCodeName lastUpdatedBy = null;
        if (data.getLastUpdatedBy() != null) {
            lastUpdatedBy = SCMUtil.generateIdCodeName(data.getLastUpdatedBy(), "",
                    masterDataCache.getEmployees().get(data.getLastUpdatedBy()));
        }

        ProductionPlanEvent event = new ProductionPlanEvent();
        event.setId(data.getId());
        event.setFulfillmentDate(data.getFulfillmentDate());
        event.setGeneratedBy(generatedBy);
        event.setGenerationTime(data.getGenerationTime());
        event.setLastUpdatedBy(lastUpdatedBy);
        event.setLastUpdateTime(data.getLastUpdateTime());
        event.setStatus(data.getStatus());
        event.setUnitId(data.getUnitId());
        if(data.getIsUpdated() != null) {
            event.setUpdated(SCMUtil.getStatus(data.getIsUpdated()));
        }
        Map<String,Map<Integer, UnitRoMappingDetailData>> mappingData = mapUnitAndRoMappingForEachProduct(data.getOrderMappingData(),masterDataCache);
        for (PlanOrderMappingData map : data.getOrderMappingData()) {
            event.getRequestOrders().add(map.getRequestOrder().getId());
        }
        for (PlanOrderItemData item : data.getOrderItemData()) {
            PlanOrderItem planOrderItem = convert(item, scmCache);
            if(ProductionItemType.REQUESTED.value().equals(item.getItemType())) {
                try {
                    planOrderItem.getUnitDetail().addAll(mappingData.get(item.getProductName()).values());
                }catch (Exception e){
                    LOG.info("Error in Add mappingData for Requested Order Item : {}",e);
                }
            }
            event.getRequestItems().add(planOrderItem);
        }
        return event;
    }

    private static Map<String,Map<Integer, UnitRoMappingDetailData>> mapUnitAndRoMappingForEachProduct(List<PlanOrderMappingData> planOrderMappingDataList,MasterDataCache masterDataCache){
        Map<String,Map<Integer, UnitRoMappingDetailData>> result = new HashMap<>();
        for(PlanOrderMappingData roData : planOrderMappingDataList ){
            Integer unitId = roData.getRequestOrder().getRequestUnitId();
            String unitName = masterDataCache.getUnit(unitId).getName();
            for (RequestOrderItemData item : roData.getRequestOrder().getRequestOrderItemDatas()){
                String productName = item.getProductName();
                if(!result.containsKey(productName)){
                    Map<Integer,UnitRoMappingDetailData> tempData = new HashMap<>();
                    UnitRoMappingDetailData unitRoMappingDetailData = new UnitRoMappingDetailData(unitId,unitName);
                    unitRoMappingDetailData.getRoIds().add(roData.getRequestOrder().getId());
                    tempData.put(unitId,unitRoMappingDetailData);
                    result.put(productName,tempData);
                }else{
                    if(!result.get(productName).containsKey(unitId)){
                        UnitRoMappingDetailData unitRoMappingDetailData = new UnitRoMappingDetailData(unitId,unitName);
                        unitRoMappingDetailData.getRoIds().add(roData.getRequestOrder().getId());
                        result.get(productName).put(unitId,unitRoMappingDetailData);
                    }else{
                        result.get(productName).get(unitId).getRoIds().add(roData.getRequestOrder().getId());
                    }
                }

            }
        }
        return result;
    }

    public static PlanOrderItem convert(PlanOrderItemData data, SCMCache scmCache) {
        PlanOrderItem item = new PlanOrderItem();
        ProductDefinition product = scmCache.getProductDefinition(data.getProductId());
        if (data.getId() != null) {
            item.setId(data.getId());
        }
        item.setAmount(data.getAmount());
         if(data.getAvailableQuantity().intValue()< 0){
             item.setAvailableQuantity(BigDecimal.ZERO);
         }else {
             item.setAvailableQuantity(data.getAvailableQuantity());
         }
        //item.setAvailableQuantity(BigDecimal.ZERO);
        item.setCategory(data.getCategory());
        item.setItemType(data.getItemType());
        item.setProductId(data.getProductId());
        item.setProductName(data.getProductName());
        item.setRequestedQuantity(data.getRequestedQuantity());
        item.setUnitOfMeasure(data.getUnitOfMeasure());
        item.setExcessQuantity(data.getExcessQuantity() == null? BigDecimal.ZERO : data.getExcessQuantity());
        item.setBufferedPercentage(data.getBufferedPercentage());
        item.setBufferedQuantity(data.getBufferedQuantity());
        item.setTotalQuantity(data.getTotalQuantity());
        item.setUnitPrice(data.getUnitPrice());
        item.setPrintCount(data.getPrintCount());
        if(data.getProductionUnit()!=null){
            item.setProductionUnit(data.getProductionUnit());
        }
        item.setCurrentDate(AppUtils.getCurrentDate());
        item.setRecipeRequire(SCMUtil.getStatus(data.getIsRecipeRequired()));
        if(SCMUtil.getStatus(data.getIsRecipeRequired())) {
            item.setExpiryDate(AppUtils.createExpiryDate(AppUtils.getCurrentTimestamp(), 0));
            item.getAvailableDates().addAll(
                AppUtils.createAvailableExpiryDate(AppUtils.getCurrentTimestamp(),0));
        }
        return item;
    }

    public static ProductionBooking convert(ProductionBookingData source, SCMCache scmCache,
                                            MasterDataCache masterDataCache) {
        ProductionBooking target = new ProductionBooking();
        target.setBookingId(source.getBookingId());
        target.setBookingStatus(BookingStatus.valueOf(source.getBookingStatus()));
        target.setCancellationTime(source.getCancellationTime());
        if (source.getCancelledBy() != null) {
            target.setCancelledBy(SCMUtil.generateIdCodeName(source.getCancelledBy(), "",
                    masterDataCache.getEmployee(source.getCancelledBy())));
        }
        target.setGeneratedBy(SCMUtil.generateIdCodeName(source.getGeneratedBy(), "",
                masterDataCache.getEmployee(source.getGeneratedBy())));
        target.setGenerationTime(source.getGenerationTime());
        target.setClosureTime(source.getClosureTime());
        target.setProductId(source.getProductId());
        target.setSkuId(source.getSkuId());
        target.setExpiryDate(source.getExpiryDate());
        target.setProductName(source.getProductName());
        target.setQuantity(source.getQuantity());
        target.setTotalCost(source.getTotalCost());
        target.setUnitId(source.getUnitId());
        target.setUnitOfMeasure(source.getUnitOfMeasure());
        target.setUnitPrice(source.getUnitPrice());

        for (BookingConsumptionData c : source.getConsumption()) {
            target.getBookingConsumption().add(convert(c, scmCache));
        }

        return target;
    }

    public static ReverseProductionBooking convert(ReverseProductionBookingData source, SCMCache scmCache,
                                                   MasterDataCache masterDataCache) {
        ReverseProductionBooking target = new ReverseProductionBooking();
        target.setBookingId(source.getBookingId());
        target.setBookingStatus(BookingStatus.valueOf(source.getBookingStatus()));
        target.setCancellationTime(source.getCancellationTime());
        if (source.getCancelledBy() != null) {
            target.setCancelledBy(SCMUtil.generateIdCodeName(source.getCancelledBy(), "",
                    masterDataCache.getEmployee(source.getCancelledBy())));
        }
        target.setGeneratedBy(SCMUtil.generateIdCodeName(source.getGeneratedBy(), "",
                masterDataCache.getEmployee(source.getGeneratedBy())));
        target.setGenerationTime(source.getGenerationTime());
        target.setClosureTime(source.getClosureTime());
        target.setProductId(source.getProductId());
        target.setSkuId(source.getSkuId());
        target.setExpiryDate(source.getExpiryDate());
        target.setProductName(source.getProductName());
        target.setQuantity(source.getQuantity());
        target.setTotalCost(source.getTotalCost());
        target.setUnitId(source.getUnitId());
        target.setUnitOfMeasure(source.getUnitOfMeasure());
        target.setUnitPrice(source.getUnitPrice());
        for (ReverseBookingConsumptionData c : source.getConsumption()) {
            target.getBookingConsumption().add(convert(c, scmCache));
        }
        return target;
    }

    public static PlanOrderItemPrep convert(PlanOrderItemPrepData planOrderItemPrepData, IdCodeName requestedBy, int unitId,
                                            RecipeCache cache, SCMCache scmCache) throws DataNotFoundException {
        PlanOrderItemPrep planOrderItemPrep = new PlanOrderItemPrep();
        planOrderItemPrep
                .setPlanOrderItem(SCMUtil.generateIdCodeName(planOrderItemPrepData.getPlanOrderItemData().getId(),
                        planOrderItemPrepData.getPlanOrderItemData().getUnitOfMeasure(),
                        planOrderItemPrepData.getPlanOrderItemData().getProductName()));
        planOrderItemPrep.setPreparationQuantity(planOrderItemPrepData.getPreparationQuantity());
        planOrderItemPrep.setRecipeId(planOrderItemPrepData.getRecipeId());
        planOrderItemPrep.setId(planOrderItemPrepData.getId());
        planOrderItemPrep.setRequestedBy(requestedBy);
        if (cache.getScmRecipe(scmCache.getRecipeProfile(unitId,planOrderItemPrepData.getPlanOrderItemData().getProductId())) != null) {
            planOrderItemPrep.setRecipeNotes(
                    cache.getScmRecipe(scmCache.getRecipeProfile(unitId,planOrderItemPrepData.getPlanOrderItemData().getProductId())).getNotes());
        }
        Map<Integer, List<PlanOrderItemPrepItem>> childrenMap = new HashMap<>();
        Map<Integer, PlanOrderItemPrepItem> itemMap = new HashMap<>();
        planOrderItemPrep.setRequestingTime(planOrderItemPrepData.getRequestingTime());
        for (PlanOrderItemPrepItemData planOrderItemPrepItemData : planOrderItemPrepData
                .getPlanOrderItemPrepItemData()) {
            PlanOrderItemPrepItem planOrderItemPrepItem = new PlanOrderItemPrepItem();
            planOrderItemPrepItem.setUnitOfMeasure(planOrderItemPrepItemData.getUnitOfMeasure());
            planOrderItemPrepItem.setQuantity(planOrderItemPrepItemData.getQuantity());
            planOrderItemPrepItem.setProductName(planOrderItemPrepItemData.getProductName());
            planOrderItemPrepItem.setProductId(planOrderItemPrepItemData.getProductId());
            planOrderItemPrepItem.setId(planOrderItemPrepItemData.getId());
            planOrderItemPrepItem.setPlanOrderItemPrepId(planOrderItemPrepItemData.getPlanOrderItemPrepData().getId());
            planOrderItemPrepItem.setInstructions(planOrderItemPrepItemData.getInstructions());
            if (cache.getScmRecipe(scmCache.getRecipeProfile(unitId,planOrderItemPrepItemData.getProductId())) != null) {
                planOrderItemPrepItem
                        .setRecipeNotes(cache.getScmRecipe(scmCache.getRecipeProfile(unitId,planOrderItemPrepItemData.getProductId())).getNotes());
            }
            if (cache.getScmRecipe(scmCache.getRecipeProfile(unitId,planOrderItemPrepData.getPlanOrderItemData().getProductId())) != null) {
                planOrderItemPrep.setImagesURL(
                        cache.getScmRecipe(scmCache.getRecipeProfile(unitId,planOrderItemPrepData.getPlanOrderItemData().getProductId())).getImagesURL());
            }
            if (planOrderItemPrepItemData.getParentOrderItem() != null) {
                List<PlanOrderItemPrepItem> items = childrenMap.get(planOrderItemPrepItemData.getParentOrderItem());
                if (items == null) {
                    items = new ArrayList<>();
                }
                items.add(planOrderItemPrepItem);
                childrenMap.put(planOrderItemPrepItemData.getParentOrderItem(), items);
            } else {
                itemMap.put(planOrderItemPrepItem.getId(), planOrderItemPrepItem);
                // planOrderItemPrep.getPlanOrderItemPrepItems().add(planOrderItemPrepItem);
            }
        }
        for (Integer key : childrenMap.keySet()) {
//            PlanOrderItemPrepItem planOrderItemPrepItem = itemMap.get(key);
//            planOrderItemPrepItem.getPlanOrderItemPrepItems().addAll(childrenMap.get(key));
//            itemMap.put(key, planOrderItemPrepItem);
            if (itemMap.containsKey(key)) {
                PlanOrderItemPrepItem planOrderItemPrepItem = itemMap.get(key);
                planOrderItemPrepItem.getPlanOrderItemPrepItems().addAll(childrenMap.get(key));
                itemMap.put(key, planOrderItemPrepItem);
            }
            else {
                LOG.info("Can not find Data for key : {}",key);
            }
        }
        itemMap.values().stream().forEach(planOrderItemPrepItem -> {
            planOrderItemPrep.getPlanOrderItemPrepItems().add(planOrderItemPrepItem);
        });
        return planOrderItemPrep;
    }

    private static BookingConsumption convert(BookingConsumptionData s, SCMCache scmCache) {
        BookingConsumption t = new BookingConsumption();
        t.setId(s.getId());
        IdCodeName product = scmCache.getSkuDefinition(s.getSkuId()).getLinkedProduct();
        t.setProductId(product.getId());
        t.setProductName(product.getName());
        t.setSkuId(s.getSkuId());
        t.setSkuName(s.getSkuName());
        t.setTotalCost(s.getTotalCost());
        t.setUnitOfMeasure(s.getUnitOfMeasure());
        t.setUnitPrice(s.getUnitPrice());
        t.setCalculatedQuantity(s.getCalculatedQuantity());
        return t;
    }

    private static ReverseBookingConsumption convert(ReverseBookingConsumptionData s, SCMCache scmCache) {
        ReverseBookingConsumption t = new ReverseBookingConsumption();
        t.setId(s.getId());
        IdCodeName product = scmCache.getSkuDefinition(s.getSkuId()).getLinkedProduct();
        t.setProductId(product.getId());
        t.setProductName(product.getName());
        t.setSkuId(s.getSkuId());
        t.setSkuName(s.getSkuName());
        t.setTotalCost(s.getTotalCost());
        t.setUnitOfMeasure(s.getUnitOfMeasure());
        t.setUnitPrice(s.getUnitPrice());
        t.setCalculatedQuantity(s.getCalculatedQuantity());
        return t;
    }

    public static CostDetail convert(CostDetailData costDetailData) {
        CostDetail costDetail = new CostDetail();
        costDetail.setCostDetailId(costDetailData.getCostDetailDataId());
        costDetail.setUnitId(costDetailData.getUnitId());
        costDetail.setPrice(costDetailData.getPrice());
        costDetail.setQuantity(costDetailData.getQuantity());
        costDetail.setKeyId(costDetailData.getKeyId());
        costDetail.setKeyType(PriceUpdateEntryType.valueOf(costDetailData.getKeyType()));
        costDetail.setUom(costDetailData.getUom());
        costDetail.setLatest(SCMUtil.getStatus(costDetailData.getLatest()));
        costDetail.setLastUpdatedTimes(costDetailData.getLastUpdatedTime());
        costDetail.setExpiryDate(costDetailData.getExpiryDate());
        costDetail.setCreationReason(costDetailData.getCreationReason());
        costDetail.setCreationItemId(costDetailData.getCreationItemId());
        return costDetail;
    }

    public static List<PaymentDeviation> convert(List<PaymentDeviationData> paymentDeviationData) {
        List<PaymentDeviation> paymentDeviations = new ArrayList<>();
        paymentDeviationData.stream().forEach(paymentDeviationData1 -> {
            PaymentDeviation paymentDeviation = new PaymentDeviation();
            paymentDeviation.setDeviationCode(paymentDeviationData1.getDeviationCode());
            paymentDeviation.setDeviationDetail(paymentDeviationData1.getDeviationDetail());
            paymentDeviation
                    .setDeviationLevel(PaymentDeviationLevel.fromValue(paymentDeviationData1.getDeviationLevel()));
            paymentDeviation.setDeviationType(PaymentDeviationType.fromValue(paymentDeviationData1.getDeviationType()));
            paymentDeviation.setPaymentDeviationId(paymentDeviationData1.getId());
            paymentDeviations.add(paymentDeviation);
        });
        return paymentDeviations;
    }

    public static PaymentDeviation convert(PaymentDeviationData paymentDeviationData) {
        PaymentDeviation paymentDeviation = new PaymentDeviation();
        paymentDeviation.setDeviationCode(paymentDeviationData.getDeviationCode());
        paymentDeviation.setDeviationDetail(paymentDeviationData.getDeviationDetail());
        paymentDeviation.setDeviationLevel(PaymentDeviationLevel.fromValue(paymentDeviationData.getDeviationLevel()));
        paymentDeviation.setDeviationType(PaymentDeviationType.fromValue(paymentDeviationData.getDeviationType()));
        paymentDeviation.setPaymentDeviationId(paymentDeviationData.getId());
        return paymentDeviation;
    }

    public static PaymentRequest convert(PaymentRequestData paymentRequestData, PaymentInvoiceData paymentInvoiceData,
                                         String blockedBy, String createdBy, String requestingUnit, String vendor,
                                         List<PaymentRequestItemMapping> paymentRequestItemMappings, String detailBy, BigDecimal debitBalance,
                                         Integer creditPeriod, List<VendorDebitBalanceVO> vendorDebitBalanceVOS, SCMCache scmCache, List<CostElementData> costElementData) {
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setPaymentRequestId(paymentRequestData.getId());
        paymentRequest.setInvoiceNumber(paymentRequestData.getInvoiceNumber());
        paymentRequest.setType(PaymentRequestType.fromValue(paymentRequestData.getType()));

        if (paymentInvoiceData != null) {
            paymentRequest.setPaymentInvoice(convert(paymentRequest.getType(), paymentInvoiceData, scmCache, costElementData));
        }
        paymentRequest.setAmountsMatch(SCMUtil.getStatus(paymentRequestData.getAmountsMatch()));
        if (paymentRequestData.getIsBlocked() != null) {
            paymentRequest.setBlocked(SCMUtil.getStatus(paymentRequestData.getIsBlocked()));
        }
        if (paymentRequestData.getBlockedBy() != null) {
            paymentRequest.setBlockedBy(SCMUtil.generateIdCodeName(paymentRequestData.getBlockedBy(), "", blockedBy));
        }
        paymentRequest.setCreatedBy(SCMUtil.generateIdCodeName(paymentRequestData.getCreatedBy(), "", createdBy));
        paymentRequest.setCreationTime(paymentRequestData.getCreationTime());
        paymentRequest.setCurrentStatus(PaymentRequestStatus.fromValue(paymentRequestData.getCurrentStatus()));
        paymentRequest.setDeviationCount(paymentRequestData.getDeviationCount());
        if (Objects.nonNull(paymentRequestData.getGrDocType())) {
            paymentRequest.setGrDocType(InvoiceDocType.fromValue(paymentRequestData.getGrDocType()));
        }
        paymentRequest.setLastUpdated(paymentRequestData.getLastUpdated());
        paymentRequest.setPaidAmount(paymentRequestData.getPaidAmount());
        if (paymentRequestData.getPaymentCalendarData() != null) {
            paymentRequest.setPaymentCycle(convert(paymentRequestData.getPaymentCalendarData(), false));
            paymentRequest.setPaymentDate(paymentRequestData.getPaymentCalendarData().getPaymentDate());
        }
        if (paymentRequestData.getPaymentDate() != null) {
            paymentRequest.setPaymentDate(paymentRequestData.getPaymentDate());
            paymentRequest.setVendorPaymentDate(paymentRequestData.getVendorPaymentDate());
        }
        paymentRequest
                .setCompanyId(paymentRequestData.getCompanyId() != null ? paymentRequestData.getCompanyId() : 1000);
        paymentRequest.setProposedAmount(paymentRequestData.getProposedAmount());
        paymentRequest.setRemarks(paymentRequestData.getRemarks());
        if (requestingUnit != null) {
            paymentRequest.setRequestingUnit(
                    SCMUtil.generateIdCodeName(paymentRequestData.getRequestingUnit(), "", requestingUnit));
        }

        paymentRequest.setVendorId(SCMUtil.generateIdCodeName(paymentRequestData.getVendorId(), "", vendor));
        if (paymentRequestItemMappings != null) {
            paymentRequest.getRequestItemMappings().addAll(paymentRequestItemMappings);
        }
        paymentRequest.setVendorDebitBalance(debitBalance);
        if (vendorDebitBalanceVOS != null) {
            paymentRequest.getVendorDebitBalanceVOS().addAll(vendorDebitBalanceVOS);
        }
        paymentRequest.setVendorCreditPeriod(creditPeriod);
        if (paymentRequestData.getPrPaymentDetailData() != null) {
            paymentRequest.setPaymentDetail(convert(paymentRequestData.getPrPaymentDetailData(), detailBy));
        }
        if (Objects.nonNull(paymentRequestData.getPaymentCard())) {
            paymentRequest.setPaymentCard(paymentRequestData.getPaymentCard());
        }
        if (Objects.nonNull(paymentRequestData.getCardPaymentTransactionNumber())) {
            paymentRequest.setCardPaymentTransactionNumber(paymentRequestData.getCardPaymentTransactionNumber());
        }
        if (Objects.nonNull(paymentRequestData.getCardPaymentProof())) {
            paymentRequest.setCardPaymentProof(paymentRequestData.getCardPaymentProof());
        }
        if (Objects.nonNull(paymentRequestData.getCardPaymentComment())) {
            paymentRequest.setCardPaymentComment(paymentRequestData.getCardPaymentComment());
        }
        paymentRequest.setSoContractBreachApprovalDoc(paymentRequestData.getSoContractBreachApprovalDoc());
        paymentRequest.setIsSoContractBreach(paymentRequestData.getIsSoContractBreach());
        return paymentRequest;
    }

    public static PRPaymentDetail convert(PRPaymentDetailData prPaymentDetailData, String createdBy) {
        PRPaymentDetail prPaymentDetail = new PRPaymentDetail();
        prPaymentDetail.setBeneficiaryAccountNumber(prPaymentDetailData.getBeneficiaryAccountNumber());
        prPaymentDetail.setBeneficiaryIfscCode(prPaymentDetailData.getBeneficiaryIFSCode());
        prPaymentDetail.setCreatedBy(SCMUtil.generateIdCodeName(prPaymentDetailData.getCreatedBy(), "", createdBy));
        if (prPaymentDetailData.getDebitAccountNumber() != null) {
            prPaymentDetail.setDebitAccount(prPaymentDetailData.getDebitAccountNumber());
        }
        if(prPaymentDetailData.getActualDate() != null){
            prPaymentDetail.setActualDate(prPaymentDetailData.getActualDate());
        }
        prPaymentDetail.setDebitBank(prPaymentDetailData.getDebitBank());
        prPaymentDetail.setId(prPaymentDetailData.getPaymentDetailId());
        prPaymentDetail.setPaidAmount(prPaymentDetailData.getPaidAmount());
        prPaymentDetail.setPaymentDate(prPaymentDetailData.getPaymentDate());
        prPaymentDetail.setVendorPaymentDate(prPaymentDetailData.getVendorPaymentDate());
        if (prPaymentDetailData.getPaymentType() != null) {
            prPaymentDetail.setPaymentType(PaymentType.fromValue(prPaymentDetailData.getPaymentType()));
        }
        prPaymentDetail.setProposedAmount(prPaymentDetailData.getProposedAmount());
        if (prPaymentDetailData.getRemarks() != null) {
            prPaymentDetail.setRemarks(prPaymentDetailData.getRemarks());
        }
        prPaymentDetail.setUtrNumber(prPaymentDetailData.getUtrNumber());
        prPaymentDetail.setVendorId(prPaymentDetailData.getVendorId());
        prPaymentDetail.setVendorName(prPaymentDetailData.getVendorName());
        return prPaymentDetail;
    }

    public static PaymentInvoice convert(PaymentRequestType type, PaymentInvoiceData paymentInvoiceData, SCMCache scmCache, List<CostElementData> costElementData) {
        PaymentInvoice paymentInvoice = new PaymentInvoice();
        paymentInvoice.setCalculatedInvoiceAmount(paymentInvoiceData.getCalculatedInvoiceAmount());
        paymentInvoice.setExtraCharges(paymentInvoiceData.getExtraCharges());
        paymentInvoice.setInvoiceAmount(paymentInvoiceData.getInvoiceAmount());
        if (paymentInvoiceData.getInvoiceDocumentHandle() != null) {
            paymentInvoice.setInvoiceDocumentHandle(paymentInvoiceData.getInvoiceDocumentHandle());
        }
        if (paymentInvoiceData.getInvoiceNumber() != null) {
            paymentInvoice.setInvoiceNumber(paymentInvoiceData.getInvoiceNumber());
        }
        paymentInvoice.setPaymentAmount(paymentInvoiceData.getPaymentAmount());
        paymentInvoice.setPaymentInvoiceId(paymentInvoiceData.getId());
        if (paymentInvoiceData.getInvoiceDate() != null) {
            paymentInvoice.setInvoiceDate(paymentInvoiceData.getInvoiceDate());
        }
        paymentInvoiceData.getPaymentInvoiceItemData().forEach(paymentInvoiceItemData -> {
            paymentInvoice.getPaymentInvoiceItems().add(convert(type, paymentInvoiceItemData, scmCache, costElementData));
        });
        return paymentInvoice;
    }

    public static PaymentInvoiceItem convert(PaymentRequestType type, PaymentInvoiceItemData paymentInvoiceItemData, SCMCache scmCache, List<CostElementData> costElementData) {
        PaymentInvoiceItem paymentInvoiceItem = new PaymentInvoiceItem();
        paymentInvoiceItem.setConversionRatio(paymentInvoiceItemData.getConversionRatio());
        paymentInvoiceItem.setHsn(paymentInvoiceItemData.getHsn());
        paymentInvoiceItem.setPackagingId(paymentInvoiceItemData.getPackagingId());
        paymentInvoiceItem.setPackagingName(paymentInvoiceItemData.getPackagingName());
        paymentInvoiceItem.setPackagingPrice(paymentInvoiceItemData.getPackagingPrice());
        paymentInvoiceItem.setPaymentInvoiceItemId(paymentInvoiceItemData.getId());
        paymentInvoiceItem.setQuantity(paymentInvoiceItemData.getQuantity());
        paymentInvoiceItem.setSkuId(paymentInvoiceItemData.getSkuId());
        paymentInvoiceItem.setSkuName(paymentInvoiceItemData.getSkuName());
        paymentInvoiceItem.setTotalAmount(paymentInvoiceItemData.getTotalAmount());
        paymentInvoiceItem.setTotalPrice(paymentInvoiceItemData.getTotalPrice());
        paymentInvoiceItem.setTotalTax(paymentInvoiceItemData.getTotalTax());
        paymentInvoiceItem.setUnitPrice(paymentInvoiceItemData.getUnitPrice());
        paymentInvoiceItem.setUom(paymentInvoiceItemData.getUom());
        paymentInvoiceItem.setServiceReceivedItemId(paymentInvoiceItemData.getServiceReceivedItemId());
        if(paymentInvoiceItemData.getSkuDate()!=null){
            paymentInvoiceItem.setSkuDate(paymentInvoiceItemData.getSkuDate());
        }
        if(paymentInvoiceItemData.getToSkuDate()!=null){
            paymentInvoiceItem.setToSkuDate(paymentInvoiceItemData.getToSkuDate());
        }
        if (type.equals(PaymentRequestType.SERVICE_RECEIVED)) {
            for (CostElementData costElement : costElementData) {
                if (costElement.getCostElementId().equals(paymentInvoiceItemData.getSkuId())) {
                    paymentInvoiceItem.setCostDescription(costElement.getDescription());
                    if(costElement.getSubCategory()!=null) {
                        paymentInvoiceItem.setBudgetCategory(costElement.getSubCategory().getBudgetCategory());
                        paymentInvoiceItem.setSection(costElement.getSubCategory().getSection());
                    }
                    if(costElement.getDepartment()!=null) {
                        paymentInvoiceItem.setDepartmentName(costElement.getDepartment().getName());
                    }
                    if(costElement.getCategory()!=null) {
                        paymentInvoiceItem.setCategory(costElement.getCategory().getName());
                    }
                }
            }

        }


        // set category and sub category only in case of GOODS_RECEIVED PR
        if (type.equals(PaymentRequestType.GOODS_RECEIVED)) {
            paymentInvoiceItem.setCategory(scmCache
                    .getProductDefinition(
                            scmCache.getSkuDefinition(paymentInvoiceItemData.getSkuId()).getLinkedProduct().getId())
                    .getCategoryDefinition().getName());
            paymentInvoiceItem.setCategoryId(scmCache
                    .getProductDefinition(
                            scmCache.getSkuDefinition(paymentInvoiceItemData.getSkuId()).getLinkedProduct().getId())
                    .getCategoryDefinition().getId());
            paymentInvoiceItem.setSubCategoryId(scmCache
                    .getProductDefinition(
                            scmCache.getSkuDefinition(paymentInvoiceItemData.getSkuId()).getLinkedProduct().getId())
                    .getSubCategoryDefinition().getId());
            paymentInvoiceItem.setSubCategory(scmCache
                    .getProductDefinition(
                            scmCache.getSkuDefinition(paymentInvoiceItemData.getSkuId()).getLinkedProduct().getId())
                    .getSubCategoryDefinition().getName());
        }
        paymentInvoiceItemData.getTaxes().forEach(paymentInvoiceItemTaxData -> {
            paymentInvoiceItem.getTaxes().add(convert(paymentInvoiceItemTaxData));
        });
        paymentInvoiceItem.setTdsRate(paymentInvoiceItemData.getTdsRate());
        return paymentInvoiceItem;
    }


    public static PaymentInvoiceItemTax convert(PaymentInvoiceItemTaxData paymentInvoiceItemTaxData) {
        PaymentInvoiceItemTax paymentInvoiceItemTax = new PaymentInvoiceItemTax();
        paymentInvoiceItemTax.setTaxDetailId(paymentInvoiceItemTaxData.getTaxDetailId());
        paymentInvoiceItemTax.setTaxPercentage(paymentInvoiceItemTaxData.getTaxPercentage());
        paymentInvoiceItemTax.setTaxType(paymentInvoiceItemTaxData.getTaxType());
        paymentInvoiceItemTax.setTaxValue(paymentInvoiceItemTaxData.getTaxValue());
        return paymentInvoiceItemTax;
    }
    public static PaymentInvoiceItemTaxData convert(PaymentInvoiceItemTax paymentInvoiceItemTax,PaymentInvoiceItemData pid) {
        PaymentInvoiceItemTaxData paymentInvoiceItemTaxData = new PaymentInvoiceItemTaxData();
        paymentInvoiceItemTaxData.setTaxDetailId(paymentInvoiceItemTax.getTaxDetailId());
        paymentInvoiceItemTaxData.setTaxPercentage(paymentInvoiceItemTax.getTaxPercentage());
        paymentInvoiceItemTaxData.setTaxType(paymentInvoiceItemTax.getTaxType());
        paymentInvoiceItemTaxData.setTaxValue(paymentInvoiceItemTax.getTaxValue());
        paymentInvoiceItemTaxData.setPaymentInvoiceItemData(pid);
        return paymentInvoiceItemTaxData;
    }

    public static PaymentRequestStatusLog convert(PaymentRequestStatusLogData paymentRequestStatusLogData,
                                                  IdCodeName updatedBy) {
        PaymentRequestStatusLog paymentRequestStatusLog = new PaymentRequestStatusLog();
        paymentRequestStatusLog
                .setFromStatus(PaymentRequestStatus.fromValue(paymentRequestStatusLogData.getFromStatus()));
        paymentRequestStatusLog.setId(paymentRequestStatusLogData.getId());
        paymentRequestStatusLog.setRemarks(paymentRequestStatusLogData.getRemarks());
        paymentRequestStatusLog.setToStatus(PaymentRequestStatus.fromValue(paymentRequestStatusLogData.getToStatus()));
        paymentRequestStatusLog.setUpdatedBy(updatedBy);
        paymentRequestStatusLog.setUpdateTime(paymentRequestStatusLogData.getUpdateTime());
        return paymentRequestStatusLog;
    }

    public static PaymentRequestLog convert(PaymentRequestLogData paymentRequestLogData) {
        PaymentRequestLog paymentRequestLog = new PaymentRequestLog();
        paymentRequestLog.setId(paymentRequestLogData.getId());
        paymentRequestLog.setLogData(paymentRequestLogData.getLogData());
        paymentRequestLog.setPaymentRequestId(paymentRequestLogData.getPaymentRequestId());
        paymentRequestLog.setUpdateTime(paymentRequestLogData.getUpdateTime());
        return paymentRequestLog;
    }

    public static PaymentRequestItemMapping convert(PaymentRequestItemMappingData paymentRequestItemMappingData,
                                                    String grStatus) {
        PaymentRequestItemMapping paymentRequestItemMapping = new PaymentRequestItemMapping();
        paymentRequestItemMapping.setId(paymentRequestItemMappingData.getId());
        paymentRequestItemMapping.setPaymentRequestId(paymentRequestItemMappingData.getPaymentRequestId());
        paymentRequestItemMapping.setPaymentRequestItemId(paymentRequestItemMappingData.getPaymentRequestItemId());
        paymentRequestItemMapping.setPaymentRequestType(
                PaymentRequestType.fromValue(paymentRequestItemMappingData.getPaymentRequestType()));
        if (paymentRequestItemMappingData.getLinkedPaymentRequestId() != null) {
            paymentRequestItemMapping
                    .setLinkedPaymentRequestId(paymentRequestItemMappingData.getLinkedPaymentRequestId());
        }
        paymentRequestItemMapping.setStatus(grStatus);
        return paymentRequestItemMapping;
    }

    public static InvoiceDeviationMapping convert(InvoiceDeviationMappingData invoiceDeviationMappingData,
                                                  String createdBy, String acceptedBy, String rejectedBy, String removedBy) {
        InvoiceDeviationMapping invoiceDeviationMapping = new InvoiceDeviationMapping();
        if (invoiceDeviationMappingData.getAcceptedBy() != null) {
            invoiceDeviationMapping.setAcceptedBy(
                    SCMUtil.generateIdCodeName(invoiceDeviationMappingData.getAcceptedBy(), "", acceptedBy));
        }
        if (invoiceDeviationMappingData.getActionRemark() != null) {
            invoiceDeviationMapping.setActionRemark(invoiceDeviationMappingData.getActionRemark());
        }
        if (invoiceDeviationMappingData.getActionTime() != null) {
            invoiceDeviationMapping.setActionTime(invoiceDeviationMappingData.getActionTime());
        }
        invoiceDeviationMapping
                .setCreatedBy(SCMUtil.generateIdCodeName(invoiceDeviationMappingData.getCreatedBy(), "", createdBy));
        invoiceDeviationMapping.setCurrentStatus(invoiceDeviationMappingData.getCurrentStatus());
        invoiceDeviationMapping.setDeviationItemId(invoiceDeviationMappingData.getDeviationItemId());
        invoiceDeviationMapping.setDeviationItemType(
                PaymentDeviationLevel.fromValue(invoiceDeviationMappingData.getDeviationItemType()));
        invoiceDeviationMapping.setDeviationRemark(invoiceDeviationMappingData.getDeviationRemark());
        invoiceDeviationMapping.setMappingId(invoiceDeviationMappingData.getId());
        invoiceDeviationMapping.setPaymentDeviation(convert(invoiceDeviationMappingData.getPaymentDeviationData()));
        if (invoiceDeviationMapping.getRejectedBy() != null) {
            invoiceDeviationMapping.setRejectedBy(
                    SCMUtil.generateIdCodeName(invoiceDeviationMappingData.getRejectedBy(), "", rejectedBy));
        }
        if (invoiceDeviationMapping.getRemovedBy() != null) {
            invoiceDeviationMapping.setRemovedBy(
                    SCMUtil.generateIdCodeName(invoiceDeviationMappingData.getRemovedBy(), "", removedBy));
        }
        return invoiceDeviationMapping;
    }

    public static DebitNoteDetail convert(DebitNoteDetailData debitNoteDetailData, String generatedBy,
                                          String lastUpdatedBy) {
        DebitNoteDetail debitNoteDetail = new DebitNoteDetail();
        if (debitNoteDetailData.getDebitNoteDetailId() != null) {
            debitNoteDetail.setDebitNoteId(debitNoteDetailData.getDebitNoteDetailId());
        }
        if (Objects.nonNull(debitNoteDetailData.getDebitNoteStatus())) {
            debitNoteDetail.setDebitNoteStatus(debitNoteDetailData.getDebitNoteStatus());
        }
        if (Objects.nonNull(debitNoteDetailData.getAdvanceAmount())) {
            debitNoteDetail.setAdvanceAmount(debitNoteDetailData.getAdvanceAmount());
        }
        if (Objects.nonNull(debitNoteDetailData.getAdvancePaymentId())) {
            debitNoteDetail.setAdvancePaymentId(debitNoteDetailData.getAdvancePaymentId());
        }
        debitNoteDetail.setAmount(debitNoteDetailData.getAmount());
        debitNoteDetail.setCreditNoteReceived(SCMUtil.getStatus(debitNoteDetailData.getCreditNoteReceived()));
        debitNoteDetail.setCreditNoteReceivingTime(debitNoteDetailData.getCreditNoteReceivingTime());
        debitNoteDetail.setGenerationTime(debitNoteDetailData.getGenerationTime());
        debitNoteDetail.setInvoiceNumber(debitNoteDetailData.getInvoiceNumber());
        debitNoteDetail.setPaymentRequestId(debitNoteDetailData.getPaymentRequest().getId());
        debitNoteDetail.setTotalAmount(debitNoteDetailData.getTotalAmount());
        debitNoteDetail.setTotalTaxes(debitNoteDetailData.getTotalTaxes());
        debitNoteDetail.setBusyReferenceNumber(debitNoteDetailData.getBusyReferenceNumber());
        if(debitNoteDetailData.getDebitNoteDocId() != null){
            debitNoteDetail.setDebitNoteDocId(debitNoteDetail.getDebitNoteDocId());
        }
        debitNoteDetail
                .setGeneratedBy(SCMUtil.generateIdCodeName(debitNoteDetailData.getGeneratedBy(), "", generatedBy));
        debitNoteDetail.setLastUpdatedBy(
                SCMUtil.generateIdCodeName(debitNoteDetailData.getLastUpdatedBy(), "", lastUpdatedBy));
        debitNoteDetail.setUpdateTime(debitNoteDetailData.getUpdateTime());
        return debitNoteDetail;
    }

    public static PaymentCalendar convert(PaymentCalendarData paymentCalendarData, boolean selected) {
        PaymentCalendar paymentCalendar = new PaymentCalendar();
        if (paymentCalendarData.getId() != null) {
            paymentCalendar.setId(paymentCalendarData.getId());
        }
        paymentCalendar.setInvoiceDate(paymentCalendarData.getInvoiceDate());
        paymentCalendar.setName(paymentCalendarData.getName());
        paymentCalendar.setPaymentDate(paymentCalendarData.getPaymentDate());
        paymentCalendar.setPrCreationDate(paymentCalendarData.getPrCreationDate());
        paymentCalendar.setCycleTag(paymentCalendarData.getCycleTag());
        paymentCalendar.setSelected(selected);
        return paymentCalendar;
    }

    public static VendorDebitBalanceVO convert(VendorCompanyDebitMapping mapping) {
        VendorDebitBalanceVO balanceVO = new VendorDebitBalanceVO();
        balanceVO.setCompanyId(mapping.getCompanyId());
        balanceVO.setCompanyName(mapping.getCompanyName());
        balanceVO.setVendorId(mapping.getDetailData().getVendorId());
        balanceVO.setDebitBalance(mapping.getDebitBalance());
        return balanceVO;
    }

    public static VendorCompanyDebitMapping convert(VendorDebitBalanceVO vo, VendorDetailData vendorDetailData) {
        VendorCompanyDebitMapping mapping = new VendorCompanyDebitMapping();
        mapping.setCompanyId(vo.getCompanyId());
        mapping.setCompanyName(vo.getCompanyName());
        mapping.setDetailData(vendorDetailData);
        mapping.setDebitBalance(vo.getDebitBalance());
        return mapping;
    }

    public static InventoryItemDrilldown convert(TransferOrderItemDrilldown to) {
        InventoryItemDrilldown data = new InventoryItemDrilldown();
        data.setKeyId(to.getOrderItem().getId());
        data.setKeyType(StockEventType.TRANSFER_OUT.name());
        data.setExpiryDate(to.getExpiryDate());
        data.setQuantity(to.getQuantity());
        data.setPrice(to.getPrice());
        return data;
    }

    public static GoodsReceivedItemDrilldown convert(TransferOrderItemDrilldown down, GoodsReceivedItemData data) {
        GoodsReceivedItemDrilldown toDrilldown = new GoodsReceivedItemDrilldown();
        toDrilldown.setReceivedItemData(data);
        toDrilldown.setQuantity(down.getQuantity());
        toDrilldown.setPrice(down.getPrice());
        toDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
        toDrilldown.setExpiryDate(down.getExpiryDate());
        return toDrilldown;
    }

    public static InventoryItemDrilldown convert(GoodsReceivedItemDrilldown itemDrilldown) {
        InventoryItemDrilldown data = new InventoryItemDrilldown();
        data.setKeyId(itemDrilldown.getReceivedItemData().getId());
        data.setKeyType(StockEventType.RECEIVED.name());
        data.setExpiryDate(itemDrilldown.getExpiryDate());
        data.setQuantity(itemDrilldown.getQuantity());
        data.setPrice(itemDrilldown.getPrice());
        data.setRejection(itemDrilldown.getRejection());
        return data;
    }

    public static InventoryItemDrilldown convert(WastageDataDrilldown itemDrilldown) {
        InventoryItemDrilldown data = new InventoryItemDrilldown();
        data.setKeyId(itemDrilldown.getWastageData().getWastageItemId());
        data.setKeyType(StockEventType.WASTAGE.name());
        data.setExpiryDate(itemDrilldown.getExpiryDate());
        data.setQuantity(itemDrilldown.getQuantity());
        data.setPrice(itemDrilldown.getPrice());
        return data;
    }

    public static InventoryItemDrilldown convert(BookingConsumptionItemDrilldown itemDrilldown) {
        InventoryItemDrilldown data = new InventoryItemDrilldown();
        data.setKeyId(itemDrilldown.getConsumptionData().getId());
        data.setKeyType(StockEventType.CONSUMPTION.name());
        data.setExpiryDate(itemDrilldown.getExpiryDate());
        data.setQuantity(itemDrilldown.getQuantity());
        data.setPrice(itemDrilldown.getPrice());
        return data;
    }

    public static InventoryItemDrilldown convert(ReverseBookingConsumptionItemDrilldown itemDrilldown) {
        InventoryItemDrilldown data = new InventoryItemDrilldown();
        data.setKeyId(itemDrilldown.getConsumptionData().getId());
        data.setKeyType(StockEventType.CONSUMPTION.name());
        data.setExpiryDate(itemDrilldown.getExpiryDate());
        data.setQuantity(itemDrilldown.getQuantity());
        data.setPrice(itemDrilldown.getPrice());
        return data;
    }

    public static IdCodeName convertToIdCodeName(VehicleData v) {
        IdCodeName i = new IdCodeName();
        i.setId(v.getVehicleId());
        i.setCode(v.getRegistrationNumber());
        i.setName(v.getName());
        return i;
    }

    public static VehicleDispatch convert(VehicleDispatchData dd, SCMCache scmCache, MasterDataCache masterDataCache,
                                          boolean getChilds) {
        if (dd == null) {
            return null;
        }
        VehicleDispatch d = new VehicleDispatch();
        d.setDispatchDate(dd.getDispatchDate());
        if (!dd.getVehicle().getTransportMode().equals(TransportMode.ROAD.name())) {
            d.setTransporterGstin(dd.getTransporterGstin());
            d.setDocketNumber(dd.getDocketNumber());
        }
        d.setVehicleNumber(dd.getVehicleNumber());
        d.setVehicle(convert(dd.getVehicle()));
        d.setDispatchId(dd.getDispatchId());
        d.setStatus(DispatchStatus.valueOf(dd.getStatus()));
        if (dd.getConsignmentList() != null) {
            for (ConsignmentData cd : dd.getConsignmentList()) {
                d.getConsignmentList().add(convert(cd, scmCache, masterDataCache, getChilds));
            }
        }
        return d;
    }

    public static Consignment convert(ConsignmentData cd, SCMCache scmCache, MasterDataCache masterDataCache,
                                      boolean getChilds) {
        Consignment c = new Consignment();
        c.setConsigmentId(cd.getConsigmentId());
        c.setStatus(cd.getStatus());
        for (EwayBillData ebd : cd.getEwaybills()) {
            c.getEwaybills().add(convert(ebd, scmCache, masterDataCache, getChilds));
        }
        c.setConsignmentType(cd.getConsignmentType());
        c.setDispatchId(cd.getDispatchId());
        c.setConsignmentUnit(convertToIdCodeName(scmCache.getUnitDetail(cd.getConsignmentUnitId())));
        return c;
    }

    public static IdCodeName convertToIdCodeName(UnitDetail unitDetail) {
        IdCodeName code = new IdCodeName();
        code.setId(unitDetail.getUnitId());
        code.setName(unitDetail.getUnitName());
        return code;
    }

    public static EWayBill convert(EwayBillData ebd, SCMCache scmCache, MasterDataCache masterDataCache,
                                   boolean getChilds) {
        EWayBill e = new EWayBill();
        e.setDistance(ebd.getDistance());
        e.setEwayBillNumber(ebd.getEwayBillNumber());
        e.setId(ebd.getId());
        e.setStatus(ebd.getStatus());
        e.setTransferOrder(convert(ebd.getTransferOrder(), getChilds, scmCache, masterDataCache));
        e.setVehicle(convert(ebd.getVehicle()));
        e.setEwayRequired(AppUtils.getStatus(ebd.getEwayRequired()));
        return e;
    }

    public static Vehicle convert(VehicleData vehicle) {
        Vehicle v = new Vehicle();
        v.setVehicleId(vehicle.getVehicleId());
        v.setMake(vehicle.getMake());
        v.setModel(vehicle.getModel());
        v.setName(vehicle.getName());
        v.setRegistrationNumber(vehicle.getRegistrationNumber());
        v.setStatus(vehicle.getStatus());
        v.setTransportMode(vehicle.getTransportMode());
        v.setMultiDispatch(AppConstants.getValue(vehicle.getMultiDispatch()));
        v.setSuggested(AppConstants.ALL);
        return v;
    }

    public static GoodsReceivedItemDrilldown convertToGoodsReceivedItemDrilldown(InventoryItemDrilldown dd,
                                                                                 GoodsReceivedItemData grItem,
                                                                                 SCMOrderPackaging pkd) {
        BigDecimal qty  =    AppUtils.multiply(dd.getQuantity() , BigDecimal.valueOf(pkd.getConversionRatio()));
        GoodsReceivedItemDrilldown drillDown = new GoodsReceivedItemDrilldown();
        drillDown.setReceivedItemData(grItem);
        drillDown.setQuantity(qty);
        drillDown.setPrice(dd.getPrice());
        drillDown.setAddTime(AppUtils.getCurrentTimestamp());
        drillDown.setExpiryDate(dd.getExpiryDate());
        return drillDown;
    }

    public static WastageDataDrilldown convertToWastageItemDrillDown(InventoryItemDrilldown dd,
                                                                     SCMWastageData wastageData) {
        WastageDataDrilldown wd = new WastageDataDrilldown();
        wd.setAddTime(AppUtils.getCurrentTimestamp());
        wd.setExpiryDate(dd.getExpiryDate());
        wd.setPrice(dd.getPrice());
        wd.setQuantity(dd.getQuantity());
        wd.setWastageData(wastageData);
        return wd;
    }

  /*  public static SalesPerformaInvoice convert(TransferOrder transferOrder , MasterDataCache masterDataCache){
        SalesPerformaInvoice invoice = new SalesPerformaInvoice();
        invoice.setId(transferOrder.getId());
        invoice.setAssetOrder(transferOrder.isAssetOrder());
        Unit unit = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId());
        //VendorDetail vendor = scmCache.getVendorDetail(detailData.getVendor());
        //IdCodeName vendorName = convertToIdCodeName(vendor);
        Optional<VendorDispatchLocation> location = vendor.getDispatchLocations().stream()
                .filter(loc -> loc.getDispatchId().equals(detailData.getDispatchLocation())).findFirst();
        invoice.setVendor(vendorName);
        VendorDispatchLocation loc = location.get();
        invoice.setDispatchLocation(new IdCodeName(loc.getDispatchId(), loc.getGstin(), loc.getCity()));
        //invoice.setVendorAddress(vendor.getVendorAddress());
        invoice.setType(SalesPerformaType.valueOf(detailData.getType()));
        invoice.setStatus(SalesPerformaStatus.valueOf(detailData.getStatus()));
        invoice.setInvoice(detailData.getInvoiceDocUrl());
        if (detailData.getPurchasedOrderNumber() != null) {
            invoice.setPurchasedOrderNumber(detailData.getPurchasedOrderNumber());
        }else{
            invoice.setPurchasedOrderNumber(" ");
        }
        if(detailData.getPurchasedOrderDate()!=null){
            invoice.setPurchaseOrderDate(detailData.getPurchasedOrderDate());
        }
        if(detailData.getPoDocumentId()!=null){
            invoice.setDocPOId(detailData.getPoDocumentId());
        }
        if(detailData.getIrnNo()!=null){
            invoice.setIrnNo(detailData.getIrnNo());
        }
        if(detailData.getCancelDocId()!=null){
            invoice.setCancelDocId(detailData.getCancelDocId());
        }
        if(detailData.getSignedQrCode()!=null){
            invoice.setSignedQrCode(detailData.getSignedQrCode());
        }
        if(detailData.getUploadDocId()!=null){
            invoice.setUploadDocId(detailData.getUploadDocId());
        }
        invoice.setUploadedAckNo(detailData.getUploadedAckNo());
        invoice.setUploadedEwayNo(detailData.getUploadedEwayNo());
        invoice.setDeliveredDocumentUrl(detailData.getDeliveredDocUrl());
        invoice.setEwayBill(detailData.geteWayDocUrl());
        invoice.setTotalCost(detailData.getTotalCost());
        invoice.setTotalSellingCost(detailData.getTotalSellingCost());
        invoice.setTotalTax(detailData.getTotalTax());
        invoice.setAdditionalCharges(detailData.getAdditionalCharges());
        invoice.setTotalAmount(detailData.getTotalAmount());
        invoice.setSendingUnit(new IdCodeName(detailData.getSendingUnit(), "", detailData.getSendingUnitName()));
        invoice.setNeedsApproval(detailData.getNeedsApproval().equals(AppConstants.YES));
        invoice.setComment(detailData.getComment());
        invoice.setDispatchDate(detailData.getDispatchDate());
        EmployeeBasicDetail createdByDetail = masterDataCache.getEmployeeBasicDetail(detailData.getCreatedBy());
        IdCodeName createdBy = new IdCodeName(createdByDetail.getId(), createdByDetail.getSlackChannel(),
                createdByDetail.getName());
        invoice.setCreatedBy(createdBy);
        invoice.setCreatedAt(detailData.getCreatedAt());
        if (detailData.getCancelledBy() != null) {
            EmployeeBasicDetail cancelledByDetail = masterDataCache.getEmployeeBasicDetail(detailData.getCancelledBy());
            IdCodeName cancelledBy = new IdCodeName(cancelledByDetail.getId(), cancelledByDetail.getSlackChannel(),
                    cancelledByDetail.getName());
            invoice.setCreatedBy(cancelledBy);
        }
        invoice.setCancelledAt(detailData.getCancelledAt());
        invoice.setClosureId(detailData.getClosureId());
        Vehicle vehicle = new Vehicle();
        vehicle.setName(detailData.getTransportName());
        vehicle.setTransportMode(detailData.getTransportMode());
        vehicle.setRegistrationNumber(detailData.getTransportId());
        invoice.setVehicle(vehicle);
        invoice.setDocketNumber(detailData.getDocketNumber());
        invoice.setVendorAddress(EWayHelper.getAddress(vendor.getVendorAddress()));
        invoice.setDispatchAddress(EWayHelper.getAddress(unit.getAddress()));
        invoice.setDeliveryAddress(EWayHelper.getAddress(loc.getAddress()));
        if(Objects.isNull(detailData.getBillingType()) && Objects.nonNull(detailData.getBillingLocation())){
            Optional<VendorDispatchLocation> billingLocation = vendor.getDispatchLocations().stream()
                    .filter(dispatchLocation -> dispatchLocation.getDispatchId().equals(detailData.getBillingLocation())).findFirst();
            if(billingLocation.isPresent()){
                invoice.setBillingAddress(EWayHelper.getAddress(billingLocation.get().getAddress()));
            }
        }

        if(detailData.getBillingType()!=null && detailData.getBillingType().equals(InvoiceBillingType.REGISTERED_ADDRESS.name())){
            invoice.setBillingAddress(EWayHelper.getAddress(vendor.getVendorAddress()));
        }
        if(detailData.getBillingType()!=null && detailData.getBillingType().equals(InvoiceBillingType.DELIVERY_ADDRESS.name())){
            invoice.setBillingAddress(EWayHelper.getAddress(loc.getAddress()));
        }
        if(detailData.getGeneratedBarcodeId()!=null){
            invoice.setGeneratedBarcodeId(detailData.getGeneratedBarcodeId());
        }

        Company company = masterDataCache.getCompany(detailData.getSendingCompany());
        invoice.setSendingCompany(new IdCodeName(company.getId(), company.getCin(), company.getName()));
        invoice.setInvoiceType(InvoiceDocType.valueOf(detailData.getInvoiceType()));
        List<SalesPerformaInvoiceItem> itemList = new ArrayList<>();
        for (SalesPerformaInvoiceItemData item : detailData.getInvoiceItems()) {
            SalesPerformaInvoiceItem salesPerformaInvoiceItem = SCMDataConverter.convertToItem(item);
            itemList.add(salesPerformaInvoiceItem);
        }
        invoice.getItems().addAll(itemList);
        Location from = unit.getLocation();
        AddressDetail toAdd = loc.getAddress();
        IdCodeName fromLocation = new IdCodeName(from.getId(), from.getState().getCode(), from.getState().getName());
        IdCodeName toLocation = new IdCodeName(toAdd.getAddressId(), toAdd.getStateCode(), toAdd.getState());
        invoice.setFrom(fromLocation);
        invoice.setTo(toLocation);
        invoice.setGeneratedId(detailData.getGeneratedId());
        invoice.setSendingCompanyFssai(unit.getFssai());
        return invoice;
    }*/

    public static SalesPerformaDetailData convert(SalesPerformaInvoice invoice, MasterDataCache masterDataCache) {
        SalesPerformaDetailData detailData = new SalesPerformaDetailData();
        detailData.setVendor(invoice.getVendor().getId());
        detailData.setDispatchLocation(invoice.getDispatchLocation().getId());
        detailData.setInvoiceType(invoice.getInvoiceType().name());
        detailData.setGeneratedId(invoice.getGeneratedId());
        detailData.setType(invoice.getType().name());
        Vehicle vehicle = invoice.getVehicle();
        if (vehicle != null) {
            detailData.setTransportMode(vehicle.getTransportMode());
            detailData.setTransportId(vehicle.getRegistrationNumber());
            detailData.setTransportName(vehicle.getName());
        }
        detailData.setStatus(invoice.getStatus().name());
        detailData.setTotalCost(invoice.getTotalCost());
        detailData.setTotalSellingCost(invoice.getTotalSellingCost());
        detailData.setTotalTax(invoice.getTotalTax());
        detailData.setTotalAmount(invoice.getTotalAmount());
        detailData.setAdditionalCharges(invoice.getAdditionalCharges());
        detailData.setSendingUnit(invoice.getSendingUnit().getId());
        detailData.setSendingUnitName(invoice.getSendingUnit().getName());
        detailData.setNeedsApproval(invoice.isNeedsApproval() ? AppConstants.YES : AppConstants.NO);
        detailData.setComment(invoice.getComment());
        detailData.setDispatchDate(invoice.getDispatchDate());
        detailData.setDocketNumber(invoice.getDocketNumber());
        Unit sendingUnit = masterDataCache.getUnit(invoice.getSendingUnit().getId());
        Company sendingCompany = sendingUnit.getCompany();
        detailData.setSendingCompany(sendingCompany.getId());
        detailData.setCreatedAt(SCMUtil.getCurrentTimestamp());
        detailData.setCreatedBy(invoice.getCreatedBy().getId());
        List<SalesPerformaInvoiceItemData> itemList = invoice.getItems().stream()
                .map(SCMDataConverter::convertInvoiceItem).collect(Collectors.toList());
        detailData.setInvoiceItems(itemList);
        return detailData;
    }

    public static SalesPerformaInvoiceItemData convertInvoiceItem(SalesPerformaInvoiceItem item) {
        SalesPerformaInvoiceItemData itemDetail = new SalesPerformaInvoiceItemData();
        itemDetail.setSkuId(item.getSku().getId());
        itemDetail.setSkuName(item.getSku().getName());
        itemDetail.setTaxCode(item.getSku().getCode());
        itemDetail.setUnitOfMeasure(item.getUom());
        itemDetail.setPackagingId(item.getPkg().getId());
        itemDetail.setPackagingName(item.getPkg().getName());
        itemDetail.setConversionRatio(item.getRatio());
        itemDetail.setPackagingQuantity(item.getPkgQty());
        itemDetail.setQuantity(SCMUtil.multiply(item.getPkgQty(), item.getRatio()));
        itemDetail.setCurrentPrice(item.getCurrPrice());
        itemDetail.setMappedPrice(item.getPkgPrice());
        itemDetail.setSellingPrice(item.getSellPrice());
        itemDetail.setSellingAmount(item.getSellAmount());
        itemDetail.setTotalTax(item.getTotalTax());
        itemDetail.setAlias(item.getAlias());
        final String hsn = itemDetail.getTaxCode();
        List<SalesPerformaItemTaxDetail> taxes = item.getTaxes().stream()
                .map(taxItem -> SCMDataConverter.convertInvoiceItemTax(taxItem, hsn)).collect(Collectors.toList());
        itemDetail.setTaxDetailList(taxes);
        return itemDetail;
    }

    public static SalesPerformaItemTaxDetail convertInvoiceItemTax(SalesPerformaItemTax tax, String hsn) {
        SalesPerformaItemTaxDetail taxDetail = new SalesPerformaItemTaxDetail();
        taxDetail.setTaxCode(hsn);
        taxDetail.setTaxType(tax.getType().name());
        taxDetail.setPercentage(tax.getPercent());
        taxDetail.setValue(tax.getValue());
        return taxDetail;
    }

        public static OutwardRegister convertOutwardRegisterData(OutwardRegisterData outwardRegisterData)  {
        OutwardRegister entity = new OutwardRegister();
        entity.setDateTime(outwardRegisterData.getDateTime());
        entity.setChallanNo(outwardRegisterData.getChallanNumber());
        entity.setAddressOfBuyer(outwardRegisterData.getAddressOfBuyer());
        entity.setDetailsOfArticle(outwardRegisterData.getDetailsOfArticle());
        entity.setQuantity(outwardRegisterData.getQuantity());
        entity.setAmount(outwardRegisterData.getAmount());
        entity.setNameOfDeliverer(outwardRegisterData.getNameOfDeliverer());
        entity.setVehicleNoType(outwardRegisterData.getVehicleNumberType());
        entity.setSignatureOfSecurity(outwardRegisterData.getSignatureOfSecurity());
        entity.setBusinessType(outwardRegisterData.getBusinessType());
        entity.setInvoiceId(outwardRegisterData.getInvoiceId());
        entity.setId(outwardRegisterData.getId());
        entity.setRemarks(outwardRegisterData.getRemarks());
        entity.setUnitId(outwardRegisterData.getUnitId());
        entity.setSubmissionDateTime(outwardRegisterData.getSubmissionDateTime());
        return entity;
    }
    public static SalesPerformaInvoice convert(SalesPerformaDetailData detailData, MasterDataCache masterDataCache,
                                               SCMCache scmCache) {
        SalesPerformaInvoice invoice = new SalesPerformaInvoice();
        invoice.setId(detailData.getInvoiceId());
        invoice.setAssetOrder(AppUtils.getStatus(detailData.getAssetOrder()));
        Unit unit = masterDataCache.getUnit(detailData.getSendingUnit());
        VendorDetail vendor = scmCache.getVendorDetail(detailData.getVendor());
        IdCodeName vendorName = convertToIdCodeName(vendor);
        Optional<VendorDispatchLocation> location = vendor.getDispatchLocations().stream()
                .filter(loc -> loc.getDispatchId().equals(detailData.getDispatchLocation())).findFirst();
        invoice.setVendor(vendorName);
        VendorDispatchLocation loc = location.get();
        invoice.setDispatchLocation(new IdCodeName(loc.getDispatchId(), loc.getGstin(), loc.getCity()));
        invoice.setVendorDispatchLocation(loc);

        //invoice.setVendorAddress(vendor.getVendorAddress());
        invoice.setType(SalesPerformaType.valueOf(detailData.getType()));
        invoice.setStatus(SalesPerformaStatus.valueOf(detailData.getStatus()));
        invoice.setInvoice(detailData.getInvoiceDocUrl());
        if (detailData.getPurchasedOrderNumber() != null) {
            invoice.setPurchasedOrderNumber(detailData.getPurchasedOrderNumber());
        }else{
            invoice.setPurchasedOrderNumber(" ");
        }
        if(detailData.getPurchasedOrderDate()!=null){
            invoice.setPurchaseOrderDate(detailData.getPurchasedOrderDate());
        }
        if(detailData.getPoDocumentId()!=null){
            invoice.setDocPOId(detailData.getPoDocumentId());
        }
        if(detailData.getIrnNo()!=null){
            invoice.setIrnNo(detailData.getIrnNo());
        }
        if(detailData.getCancelDocId()!=null){
            invoice.setCancelDocId(detailData.getCancelDocId());
        }
        if(detailData.getSignedQrCode()!=null){
            invoice.setSignedQrCode(detailData.getSignedQrCode());
        }
        if(detailData.getUploadDocId()!=null){
            invoice.setUploadDocId(detailData.getUploadDocId());
        }
        invoice.setUploadedAckNo(detailData.getUploadedAckNo());
        invoice.setUploadedEwayNo(detailData.getUploadedEwayNo());
        invoice.setDeliveredDocumentUrl(detailData.getDeliveredDocUrl());
        invoice.setEwayBill(detailData.geteWayDocUrl());
        invoice.setTotalCost(detailData.getTotalCost());
        invoice.setTotalSellingCost(detailData.getTotalSellingCost());
        invoice.setTotalTax(detailData.getTotalTax());
        invoice.setAdditionalCharges(detailData.getAdditionalCharges());
        invoice.setTotalAmount(detailData.getTotalAmount());
        invoice.setSendingUnit(new IdCodeName(detailData.getSendingUnit(), "", detailData.getSendingUnitName()));
        invoice.setNeedsApproval(detailData.getNeedsApproval().equals(AppConstants.YES));
        invoice.setComment(detailData.getComment());
        invoice.setDispatchDate(detailData.getDispatchDate());
        invoice.setGstInvoiceType(detailData.getGstInvoiceType());
        EmployeeBasicDetail createdByDetail = masterDataCache.getEmployeeBasicDetail(detailData.getCreatedBy());
        IdCodeName createdBy = new IdCodeName(createdByDetail.getId(), createdByDetail.getSlackChannel(),
                createdByDetail.getName());
        invoice.setCreatedBy(createdBy);
        invoice.setCreatedAt(detailData.getCreatedAt());
        if (detailData.getCancelledBy() != null) {
            EmployeeBasicDetail cancelledByDetail = masterDataCache.getEmployeeBasicDetail(detailData.getCancelledBy());
            IdCodeName cancelledBy = new IdCodeName(cancelledByDetail.getId(), cancelledByDetail.getSlackChannel(),
                    cancelledByDetail.getName());
            invoice.setCreatedBy(cancelledBy);
        }
        invoice.setCancelledAt(detailData.getCancelledAt());
        invoice.setClosureId(detailData.getClosureId());
        Vehicle vehicle = new Vehicle();
        vehicle.setName(detailData.getTransportName());
        vehicle.setTransportMode(detailData.getTransportMode());
        vehicle.setRegistrationNumber(detailData.getTransportId());
        invoice.setVehicle(vehicle);
        invoice.setDocketNumber(detailData.getDocketNumber());
        invoice.setVendorAddress(EWayHelper.getAddress(vendor.getVendorAddress()));
        invoice.setDispatchAddress(EWayHelper.getAddress(unit.getAddress()));
        invoice.setDeliveryAddress(EWayHelper.getAddress(loc.getAddress()));
        if(Objects.isNull(detailData.getBillingType()) && Objects.nonNull(detailData.getBillingLocation())){
            Optional<VendorDispatchLocation> billingLocation = vendor.getDispatchLocations().stream()
                    .filter(dispatchLocation -> dispatchLocation.getDispatchId().equals(detailData.getBillingLocation())).findFirst();
            if(billingLocation.isPresent()){
                invoice.setBillingAddress(EWayHelper.getAddress(billingLocation.get().getAddress()));
            }
        }

        if(detailData.getBillingType()!=null && detailData.getBillingType().equals(InvoiceBillingType.REGISTERED_ADDRESS.name())){
            invoice.setBillingAddress(EWayHelper.getAddress(vendor.getVendorAddress()));
        }
        if(detailData.getBillingType()!=null && detailData.getBillingType().equals(InvoiceBillingType.DELIVERY_ADDRESS.name())){
            invoice.setBillingAddress(EWayHelper.getAddress(loc.getAddress()));
        }
        if(detailData.getGeneratedBarcodeId()!=null){
            invoice.setGeneratedBarcodeId(detailData.getGeneratedBarcodeId());
        }

        Company company = masterDataCache.getCompany(detailData.getSendingCompany());
        invoice.setSendingCompany(new IdCodeName(company.getId(), company.getCin(), company.getName()));
        invoice.setInvoiceType(InvoiceDocType.valueOf(detailData.getInvoiceType()));
        List<SalesPerformaInvoiceItem> itemList = new ArrayList<>();
        for (SalesPerformaInvoiceItemData item : detailData.getInvoiceItems()) {
            SalesPerformaInvoiceItem salesPerformaInvoiceItem = SCMDataConverter.convertToItem(item);
            itemList.add(salesPerformaInvoiceItem);
        }
        invoice.getItems().addAll(itemList);
        Location from = unit.getLocation();
        AddressDetail toAdd = loc.getAddress();
        IdCodeName fromLocation = new IdCodeName(from.getId(), from.getState().getCode(), from.getState().getName());
        IdCodeName toLocation = new IdCodeName(toAdd.getAddressId(), toAdd.getStateCode(), toAdd.getState());
        invoice.setFrom(fromLocation);
        invoice.setTo(toLocation);
        invoice.setGeneratedId(detailData.getGeneratedId());
        invoice.setSendingCompanyFssai(unit.getFssai());
        invoice.setCreditNoteDocId(detailData.getCreditNoteDocId());
        invoice.setCreditNoteDocUrl(detailData.getCreditNoteDocUrl());
        invoice.setDebitNoteDocId(detailData.getDebitNoteDocId());
        invoice.setDebitNoteDocUrl(detailData.getDebitNoteDocUrl());
        invoice.setGeneratedCreditNoteId(detailData.getGeneratedCreditNoteId());
        invoice.setGeneratedDebitNoteId(detailData.getGeneratedDebitNoteId());
        invoice.setReferenceInvoiceNumber(detailData.getReferenceInvoiceNumber());
        return invoice;
    }

    public static SalesPerformaInvoiceItem convertToItem(SalesPerformaInvoiceItemData itemData) {

        SalesPerformaInvoiceItem item = new SalesPerformaInvoiceItem();
        item.setId(itemData.getItemId());
        item.setSku(new IdCodeName(itemData.getSkuId(), itemData.getTaxCode(), itemData.getSkuName()));
        item.setUom(itemData.getUnitOfMeasure());
        item.setPkg(new IdCodeName(itemData.getPackagingId(), "", itemData.getPackagingName()));
        item.setRatio(itemData.getConversionRatio());
        item.setQty(itemData.getQuantity());
        item.setPkgQty(itemData.getPackagingQuantity());
        item.setCurrPrice(itemData.getCurrentPrice());
        item.setPkgPrice(itemData.getMappedPrice());
        item.setSellPrice(itemData.getSellingPrice());
        item.setCurrAmount(itemData.getCurrentAmount());
        item.setPkgAmount(itemData.getMappedAmount());
        item.setSellAmount(itemData.getSellingAmount());
        item.setTotalTax(itemData.getTotalTax());
        item.setAlias(itemData.getAlias());

        if (itemData.getTaxDetailList() != null) {
            List<SalesPerformaItemTax> taxes = new ArrayList<>();
            for (SalesPerformaItemTaxDetail tax : itemData.getTaxDetailList()) {
                taxes.add(SCMDataConverter.convertInvoiceTaxItem(tax));
            }
            item.getTaxes().addAll(taxes);
        }

        if (itemData.getExpiryDrilldownList() != null) {
            List<InventoryItemDrilldown> drilldowns = new ArrayList<>();
            for (SalesPerformaItemDrilldown drilldown : itemData.getExpiryDrilldownList()) {
                drilldowns.add(SCMDataConverter.convertToDrilldown(drilldown));
            }
            item.getDrillDowns().addAll(drilldowns);
        }

        return item;
    }

    public static EntityAssetMapping convert(EntityAssetMappingData entityAssetMappingData) {
        EntityAssetMapping entityAssetMapping = factory.getEntityAssetMapping();
        entityAssetMapping.setEntityType(entityAssetMappingData.getEntityType());
        entityAssetMapping.setEntitySubId(entityAssetMappingData.getEntitySubId());
        entityAssetMapping.setEntityId(entityAssetMappingData.getEntityId());
        entityAssetMapping.setEntityCategory(entityAssetMappingData.getEntityCategory());
        entityAssetMapping.setAssetId(entityAssetMappingData.getAssetId());
        entityAssetMapping.setAssetTagValue(entityAssetMappingData.getAssetTagValue());
        entityAssetMapping.setEntityAssetMappingId(entityAssetMappingData.getEntityAssetMappingId());
        return entityAssetMapping;
    }

    public static EntityAssetMappingData convert(EntityAssetMapping entityAssetMapping) {
        EntityAssetMappingData entityAssetMappingData = new EntityAssetMappingData();
        entityAssetMappingData.setAssetId(entityAssetMapping.getAssetId());
        entityAssetMappingData.setAssetTagValue(entityAssetMapping.getAssetTagValue());
        entityAssetMappingData.setEntityAssetMappingId(entityAssetMapping.getEntityAssetMappingId());
        entityAssetMappingData.setEntityCategory(entityAssetMapping.getEntityCategory());
        entityAssetMappingData.setEntityId(entityAssetMapping.getEntityId());
        entityAssetMappingData.setEntitySubId(entityAssetMapping.getEntitySubId());
        entityAssetMappingData.setEntityType(entityAssetMapping.getEntityType());
        return entityAssetMappingData;
    }

    public static InventoryItemDrilldown convertToDrilldown(SalesPerformaItemDrilldown i) {
        InventoryItemDrilldown itd = new InventoryItemDrilldown();
        itd.setKeyType(PriceUpdateEntryType.SKU.name());
        itd.setKeyId(i.getDrilldownId());
        itd.setPrice(i.getPrice());
        itd.setQuantity(i.getQuantity());
        itd.setExpiryDate(i.getExpiryDate());
        return itd;
    }

    public static SalesPerformaItemTax convertInvoiceTaxItem(SalesPerformaItemTaxDetail taxDetail) {
        SalesPerformaItemTax tax = new SalesPerformaItemTax();
        tax.setCode(taxDetail.getTaxCode());
        tax.setType(TaxCategoryType.valueOf(taxDetail.getTaxType()));
        tax.setPercent(taxDetail.getPercentage());
        tax.setValue(taxDetail.getValue());
        return tax;
    }

    public static GatepassVendorMapping convert(GatepassVendorMappingData data, SCMCache scmCache,
                                                MasterDataCache masterDataCache) {
        GatepassVendorMapping detail = new GatepassVendorMapping();
        detail.setId(data.getId());
        detail.setVendor(scmCache.getVendorDetail(data.getVendorId()));
        detail.setOperationType(data.getOperationType());
        detail.setStatus(data.getStatus());
        detail.setCreatedBy(new IdCodeName(data.getCreatedBy(), "", masterDataCache.getEmployee(data.getCreatedBy())));
        detail.setUnit(new IdCodeName(data.getUnitId(), "", masterDataCache.getUnit(data.getUnitId()).getName()));
        detail.setCreatedAt(data.getCreatedAt());
        return detail;
    }

    public static Gatepass convert(GatepassData data, boolean setChildEntities, SCMCache scmCache,
                                   MasterDataCache masterDataCache,Boolean itemLevel) {
        Gatepass gatepass = new Gatepass();
        gatepass.setId(data.getId());
        gatepass.setVendor(
                new IdCodeName(data.getVendorId(), "", scmCache.getVendorDetail(data.getVendorId()).getEntityName()));
        gatepass.setOperationType(GatepassOperationType.valueOf(data.getOperationType()));
        if (data.getReturnable() != null) {
            gatepass.setReturnable(AppUtils.getStatus(data.getReturnable()));
        }
        if (data.getExpectedReturn() != null) {
            gatepass.setExpectedReturn(data.getExpectedReturn());
        }
        gatepass.setStatus(GatepassStatus.valueOf(data.getStatus()));
        if (data.getReturnStatus() != null) {
            gatepass.setReturnStatus(GatepassReturnStatus.valueOf(data.getReturnStatus()));
        }
        gatepass.setTotalCost(data.getTotalCost());
        gatepass.setTotalTax(data.getTotalTax());

        if (data.getAdditionalCharges() != null) {
            gatepass.setAdditionalCharges(data.getAdditionalCharges());
        }

        gatepass.setComment(data.getComment());
        gatepass.setReason(data.getReason());
        gatepass.setCreatedBy(
                new IdCodeName(data.getCreatedBy(), "", masterDataCache.getEmployee(data.getCreatedBy())));
        gatepass.setCreatedAt(data.getCreatedAt());
        gatepass.setSendingUnit(
                new IdCodeName(data.getSendingUnit(), "", masterDataCache.getUnit(data.getSendingUnit()).getName()));
        gatepass.setSendingCompany(new IdCodeName(data.getSendingCompany(), "", ""));
        gatepass.setNeedsApproval(data.getNeedsApproval());
        gatepass.setIssueDate(data.getIssueDate());
        gatepass.setDispatchLocation(new IdCodeName(data.getDispatchLocationId(), "", ""));
        if (data.getHasLoss() != null) {
            gatepass.setHasLoss(AppUtils.getStatus(data.getHasLoss()));
        }
        if (data.getCancelledAt() != null) {
            gatepass.setCancelledBy(
                    new IdCodeName(data.getCancelledBy(), "", masterDataCache.getEmployee(data.getCancelledBy())));
            gatepass.setCancelledAt(data.getCancelledAt());
        }
        if (Objects.nonNull(itemLevel) && itemLevel) {
            for (GatepassItemData itemData : data.getItemDatas()) {
                gatepass.getItemDatas().add(convert(itemData, setChildEntities, scmCache, masterDataCache));
            }
        }
        gatepass.setAssetGatePass(AppUtils.getStatus(data.getAssetGatePass()));
        return gatepass;
    }

    private static GatepassItem convert(GatepassItemData itemData, boolean setChildEntities, SCMCache scmCache,
                                        MasterDataCache masterDataCache) {
        GatepassItem item = new GatepassItem();
        item.setId(itemData.getId());
        item.setSku(
                new IdCodeName(itemData.getSkuId(), "", scmCache.getSkuDefinition(itemData.getSkuId()).getSkuName()));
        item.setUom(itemData.getUom());
        if (itemData.getPrice() != null) {
            item.setPrice(itemData.getPrice());
        }
        item.setTransType(GatepassTransType.valueOf(itemData.getTransType()));
        if (itemData.getQuantity() != null) {
            item.setQuantity(itemData.getQuantity());
        }
        if (itemData.getCost() != null) {
            item.setCost(itemData.getCost());
        }
        if (itemData.getTax() != null) {
            item.setTax(itemData.getTax());
        }
        if (itemData.getAmount() != null) {
            item.setAmount(itemData.getAmount());
        }
        if (itemData.getClosureId() != null) {
            item.setClosureId(itemData.getClosureId());
        }
        if (itemData.getCreatedAt() != null) {
            item.setCreatedBy(
                    new IdCodeName(itemData.getCreatedBy(), "", masterDataCache.getEmployee(itemData.getCreatedBy())));
            item.setCreatedAt(itemData.getCreatedAt());
        }
        if (setChildEntities) {
            for (GatepassItemDrilldownDetail drilldownDetail : itemData.getDrillDowns()) {
                item.getItemDrillDowns().add(convert(drilldownDetail, item));
            }
            for (GatepassTaxDetail taxDetail : itemData.getTaxDetails()) {
                item.getTaxes().add(convert(taxDetail));
            }
        }
        return item;
    }

    private static GatepassItemDrilldown convert(GatepassItemDrilldownDetail drilldownDetail, GatepassItem item) {
        GatepassItemDrilldown drilldown = new GatepassItemDrilldown();
        drilldown.setId(drilldownDetail.getId());
        drilldown.setQuantity(drilldownDetail.getQuantity());
        drilldown.setPrice(drilldownDetail.getPrice());
        drilldown.setExpiryDate(drilldownDetail.getExpiryDate());
        drilldown.setAddTime(drilldown.getAddTime());
        return drilldown;
    }

    private static GatepassTax convert(GatepassTaxDetail taxDetail) {
        GatepassTax tax = new GatepassTax();
        tax.setId(taxDetail.getId());
        tax.setTaxType(taxDetail.getTaxType());
        tax.setTaxCode(taxDetail.getTaxCode());
        tax.setTaxPercentage(taxDetail.getTaxPercentage());
        tax.setTaxAmount(taxDetail.getTaxAmount());
        return tax;
    }

    public static InventoryItemDrilldown convert(GatepassItemDrilldownDetail itemDrilldown, BigDecimal quantity) {
        InventoryItemDrilldown data = new InventoryItemDrilldown();
        data.setKeyId(itemDrilldown.getItemData().getId());
        data.setKeyType(StockEventType.RECEIVED.name());
        if (itemDrilldown.getExpiryDate() != null) {
            data.setExpiryDate(itemDrilldown.getExpiryDate());
        }
        data.setQuantity(quantity);
        data.setPrice(itemDrilldown.getPrice());
        data.setRejection(BigDecimal.ZERO);
        return data;
    }

    public static ApprovalDetail convertToApprovalDetail(ApprovalDetailData data, SCMCache scmCache, MasterDataCache masterDataCache){
        ApprovalDetail approval = new ApprovalDetail();
        if(Objects.nonNull(data)){
            approval.setApprovalRequestId(data.getApprovalRequestId());
            approval.setEventId(data.getEventId());
            approval.setAssetId(data.getAssetId());
            approval.setSkuId(data.getSkuId());
            approval.setUnitId(data.getUnitId());
            approval.setUnitName(masterDataCache.getUnitBasicDetail(data.getUnitId()).getName());
            approval.setType(data.getType());
            approval.setStatus(data.getStatus());
            approval.setSkuName(data.getSkuName());
            if(Objects.nonNull(scmCache.getSkuDefinition(data.getSkuId()).getSkuImage())){
                approval.setSkuImage(scmCache.getSkuDefinition(data.getSkuId()).getSkuImage());
            }else{
                approval.setSkuImage(null);
            }
            approval.setCost(data.getCost());
            approval.setRequestedBy(data.getRequestedBy());
            approval.setRequestedByName(masterDataCache.getEmployee(data.getRequestedBy()));
            approval.setRequestedTo(data.getRequestedTo());
            approval.setRequestDate(AppUtils.getDateString(data.getRequestDate(),"dd MMM yyyy").toString());
            approval.setApprovedBy(Objects.nonNull(data.getApprovedBy())? data.getApprovedBy():null);
            approval.setApprovalDate(Objects.nonNull(data.getApprovalDate())? AppUtils.getDateString(data.getApprovalDate(),"dd MMM yyyy").toString() :null);

        }
        return approval;
    }

    public static DayCloseEvent convert(SCMDayCloseEventData eventData, MasterDataCache masterDataCache) {
        DayCloseEvent event = null;
        if (eventData != null) {
            Unit unit = masterDataCache.getUnit(eventData.getUnitId());
            event = new DayCloseEvent();
            event.setId(eventData.getEventId());
            event.setBusinessDate(eventData.getBusinessDate());
            event.setUnit(SCMUtil.generateIdCodeName(unit.getId(), unit.getFamily().name(), unit.getName()));
            event.setStatus(StockEventStatus.valueOf(eventData.getStatus()));
            event.setType(StockEventType.valueOf(eventData.getDayCloseEventType()));
            event.setGenerationTime(eventData.getGenerationTime());
            event.setStockTakeType(eventData.getEventFrequencyType());
        }
        return event;
    }

    public static CostCenter convert(CostCenterData cd, MasterDataCache masterDataCache) {
        CostCenter cc = new CostCenter();
        cc.setCode(cd.getShortCode());
        cc.setDescription(cd.getDescription());
        cc.setId(cd.getCostCenterId());
        cc.setName(cd.getCostCenterName());
        cc.setCostCenterEmail(cd.getCostCenterEmail());

        /*
         * if (cd.getElements() != null && !cd.getElements().isEmpty()) { for
         * (CostElementData p : cd.getElements()) { CostElement ce = convert(p);
         * cc.getElements().add(ce); } }
         */
        EmployeeBasicDetail e = masterDataCache.getEmployeeBasicDetail(cd.getOwner());
        if (e != null) {
            cc.setOwner(new IdCodeName(e.getId(), null, e.getName()));
        }
        return cc;
    }

    public static CostElement convert(CostElementData p) {
        CostElement ce = new CostElement();
        ce.setCode(p.getAscCode());
        ce.setDescription(p.getDescription());
        ce.setId(p.getCostElementId());
        ce.setName(p.getCostElementName());
        ce.setTaxRate(p.getTaxRate());
        ce.setStatus(p.getCostElementStatus());
        ce.setUom(p.getUom());
        ce.setCapex(p.getCapex());
        ce.setIsPriceUpdate(p.getIsPriceUpdate());
        ce.setLowerPriceRange(p.getLowerPriceRange());
        ce.setUpperPriceRange(p.getUpperPriceRange());
        if (p.getCategory() != null) {
            ce.setCategory(convertListDetail(p.getCategory()));
        }
        if (p.getDepartment() != null) {
            ce.setDepartment(convertListDetail(p.getDepartment()));
        }
        if (p.getDivision() != null) {
            ce.setDivision(convertListDetail(p.getDivision()));
        }
        if (p.getSubCategory() != null) {
            ce.setSubCategory(convertType(p.getSubCategory()));
        }
        /*
         * for (CostElementPackagingMapping m : p.getPackagingList()) {
         * ce.getUoms().add(m.getPackaging().getPackagingCode()); }
         */
        return ce;
    }

    public static ServiceOrder convert(ServiceOrderData data, SCMCache scmCache, MasterDataCache masterDataCache,
                                       boolean setChild, List<VendorAdvancePayment> advances) {

        ServiceOrder serviceOrder = new ServiceOrder();
        serviceOrder.setComment(data.getComment());
        serviceOrder.setId(data.getId());
        serviceOrder.setReceiptNumber(data.getReceiptNumber());

        VendorDetail vendor = scmCache.getVendorDetail(data.getVendorId());
        Optional<VendorDispatchLocation> dispatchLocation = vendor.getDispatchLocations().stream()
                .filter(location -> location.getDispatchId().equals(data.getDispatchLocationId())).findAny();

        serviceOrder.setDispatchLocation(dispatchLocation.isPresent() ? dispatchLocation.get() : null);
        serviceOrder.setVendor(SCMUtil.generateIdCodeName(vendor.getVendorId(), "", vendor.getEntityName()));
        if (data.getApprovedBy() != null) {
            serviceOrder.setApprovedBy(SCMUtil.generateIdCodeName(data.getApprovedBy(), "",
                    masterDataCache.getEmployee(data.getApprovedBy())));
        }

        if (data.getGeneratedBy() != null) {
            serviceOrder.setGeneratedBy(SCMUtil.generateIdCodeName(data.getGeneratedBy(), "",
                    masterDataCache.getEmployee(data.getGeneratedBy())));
        }

        if (data.getLastUpdatedBy() != null) {
            serviceOrder.setLastUpdatedBy(SCMUtil.generateIdCodeName(data.getLastUpdatedBy(), "",
                    masterDataCache.getEmployee(data.getLastUpdatedBy())));
        }
        if (data.getTagName() != null) {
            serviceOrder.setTagName(data.getTagName());
        }

        serviceOrder.setLastUpdateTime(data.getLastUpdateTime());
        serviceOrder.setTotalAmount(data.getTotalAmount());
        serviceOrder.setTotalTaxes(data.getTotalTaxes());
        serviceOrder.setTotalCost(data.getTotalCost());
        serviceOrder.setCostCenterId(data.getCostCenterId());
        serviceOrder.setType(data.getType());
        serviceOrder.setForceClosed(
                data.getForceClosed() != null ? data.getForceClosed().equals(SCMServiceConstants.SCM_CONSTANT_YES)
                        : false);
        serviceOrder.setVendorNotified(
                data.getVendorNotified() != null ? data.getVendorNotified().equals(SCMServiceConstants.SCM_CONSTANT_YES)
                        : false);
        serviceOrder.setGenerationTime(data.getGenerationTime());
        serviceOrder.setTotalTaxes(data.getTotalTaxes());
        serviceOrder.setStatus(ServiceOrderStatus.valueOf(data.getStatus()));
        if (data.getServiceOrderItemDataList() != null) {

            for (ServiceOrderItemData serviceOrderItemData : data.getServiceOrderItemDataList()) {
                serviceOrder.getOrderItems().add(convert(serviceOrderItemData));
            }
        }
        serviceOrder.setSoInvoiceDocument(SCMDataConverter.convert(data.getSoInvoiceDocument()));
        serviceOrder.setAccountedForInPnl(AppConstants.getValue(data.getAccountedForInPnl()));
        if (Objects.nonNull(advances) && !advances.isEmpty()) {
            serviceOrder.setVendorAdvancePayments(advances);
        }
        return serviceOrder;
    }

    public static ServiceOrderShort convert(ServiceOrderData data, SCMCache scmCache,
                                            boolean setChild, MasterDataCache masterDataCache) {

        ServiceOrderShort serviceOrder = new ServiceOrderShort();
        serviceOrder.setGeneratedBy(data.getGeneratedBy());
        serviceOrder.setComment(data.getComment());
        serviceOrder.setId(data.getId());
        serviceOrder.setReceiptNumber(data.getReceiptNumber());

        serviceOrder.setEmployeeName(masterDataCache.getEmployeeBasicDetail(data.getGeneratedBy()).getName());

        VendorDetail vendor = scmCache.getVendorDetail(data.getVendorId());
        Optional<VendorDispatchLocation> dispatchLocation = vendor.getDispatchLocations().stream()
                .filter(location -> location.getDispatchId().equals(data.getDispatchLocationId())).findAny();


        if (data.getServiceOrderItemDataList() != null) {
            Set<Integer> h = new HashSet<>();
            for (ServiceOrderItemData serviceOrderItemData : data.getServiceOrderItemDataList()) {
                Integer key = serviceOrderItemData.getBusinessCostCenterId();
                if(!h.contains(key)){
                    serviceOrder.getOrderItems().add(convert(serviceOrderItemData));
                }
                    h.add(key);

            }
        }

        serviceOrder.setDispatchLocationCity(dispatchLocation.isPresent() ? dispatchLocation.get().getCity() : null);
        serviceOrder.setVendorName(vendor.getEntityName());
        serviceOrder.setLastUpdateTime(data.getLastUpdateTime());
        serviceOrder.setTotalAmount(data.getTotalAmount());
        serviceOrder.setTotalTaxes(data.getTotalTaxes());
        serviceOrder.setTotalCost(data.getTotalCost());
        serviceOrder.setCostCenterId(data.getCostCenterId());
        serviceOrder.setType(data.getType());
        serviceOrder.setGenerationTime(data.getGenerationTime());
        serviceOrder.setTotalTaxes(data.getTotalTaxes());
        serviceOrder.setStatus(ServiceOrderStatus.valueOf(data.getStatus()));
        serviceOrder.setAccountedForInPnl(AppConstants.getValue(data.getAccountedForInPnl()));
        serviceOrder.setSoInvoiceDocument(SCMDataConverter.convert(data.getSoInvoiceDocument()));

        return serviceOrder;
    }


    public static ServiceOrderItem convert(ServiceOrderItemData serviceOrderItemData) {
        ServiceOrderItem item = new ServiceOrderItem();
        item.setId(serviceOrderItemData.getId());
        item.setServiceOrderId(serviceOrderItemData.getServiceOrderId());
        item.setCostElementId(serviceOrderItemData.getCostElementId());
        item.setCostElementName(serviceOrderItemData.getCostElementName());
        item.setTotalCost(serviceOrderItemData.getTotalCost());
        item.setTotalTax(serviceOrderItemData.getTotalTax());
        item.setTaxRate(serviceOrderItemData.getTaxRate());
        item.setTdsRate(serviceOrderItemData.getTdsRate());
        item.setAscCode(serviceOrderItemData.getAscCode());
        item.setReceivedQuantity(serviceOrderItemData.getReceivedQuantity());
        item.setRequestedQuantity(serviceOrderItemData.getRequestedQuantity());
        item.setUnitPrice(serviceOrderItemData.getUnitPrice());
        if(serviceOrderItemData.getCostElementDate()!=null){
            item.setCostElementDate(serviceOrderItemData.getCostElementDate());
        }
        if(serviceOrderItemData.getCostElementToDate()!=null){
            item.setCostElementToDate(serviceOrderItemData.getCostElementToDate());
        }
        item.setUnitOfMeasure(serviceOrderItemData.getUnitOfMeasure());
        item.setServiceDescription(serviceOrderItemData.getServiceDescription());
        item.setAmountPaid(SCMUtil.add(serviceOrderItemData.getTotalCost(), serviceOrderItemData.getTotalTax()));
        item.setBusinessCostCenterId(serviceOrderItemData.getBusinessCostCenterId());
        item.setBusinessCostCenterName(serviceOrderItemData.getBusinessCostCenterName());
        item.setType(serviceOrderItemData.getType());
        return item;
    }

    public static ServiceOrderShort convertToShortFilterByCostElementIds(ServiceOrderData data, SCMCache scmCache,
                                             boolean setChild ,  List<Integer> costElementIds) {

        ServiceOrderShort serviceOrder = new ServiceOrderShort();
        serviceOrder.setComment(data.getComment());
        serviceOrder.setId(data.getId());
        serviceOrder.setReceiptNumber(data.getReceiptNumber());

        VendorDetail vendor = scmCache.getVendorDetail(data.getVendorId());
        Optional<VendorDispatchLocation> dispatchLocation = vendor.getDispatchLocations().stream()
                .filter(location -> location.getDispatchId().equals(data.getDispatchLocationId())).findAny();

        if (data.getServiceOrderItemDataList() != null) {
            for (ServiceOrderItemData serviceOrderItemData : data.getServiceOrderItemDataList()) {
                if(costElementIds.contains(serviceOrderItemData.getCostElementId())){
                    serviceOrder.getOrderItems().add(SCMDataConverter.convert(serviceOrderItemData));
                }
            }
        }

        serviceOrder.setDispatchLocationCity(dispatchLocation.isPresent() ? dispatchLocation.get().getCity() : null);
        serviceOrder.setVendorName(vendor.getEntityName());
        serviceOrder.setLastUpdateTime(data.getLastUpdateTime());
        serviceOrder.setTotalAmount(data.getTotalAmount());
        serviceOrder.setTotalTaxes(data.getTotalTaxes());
        serviceOrder.setTotalCost(data.getTotalCost());
        serviceOrder.setCostCenterId(data.getCostCenterId());
        serviceOrder.setType(data.getType());
        serviceOrder.setGenerationTime(data.getGenerationTime());
        serviceOrder.setTotalTaxes(data.getTotalTaxes());
        serviceOrder.setStatus(ServiceOrderStatus.valueOf(data.getStatus()));
        serviceOrder.setAccountedForInPnl(AppConstants.getValue(data.getAccountedForInPnl()));
        serviceOrder.setSoInvoiceDocument(SCMDataConverter.convert(data.getSoInvoiceDocument()));

        return serviceOrder;
    }

    public static ServiceReceive convert(ServiceReceivedData serviceReceivedData, SCMCache scmCache,
                                         MasterDataCache masterDataCache,Map<Integer,String> mapBusinessCenterIdCode, boolean setChild) {
        ServiceReceive sr = new ServiceReceive();
        VendorDetail vendorDetail = scmCache.getVendorDetail(serviceReceivedData.getVendorId());
        sr.setId(serviceReceivedData.getServiceReceivedId());
        sr.setVendor(convertToBasicDetail(vendorDetail));
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendorDetail,
                serviceReceivedData.getDispatchLocationId());
        if (dispatchLocation.isPresent()) {
            VendorDispatchLocation location = dispatchLocation.get();
            sr.setLocation(new IdCodeName(location.getDispatchId(), location.getGstin(), location.getCity()));
            sr.setDispatchAddress(location.getAddress());
        }
        Location deliveryLocation = masterDataCache.getLocationbyId(serviceReceivedData.getDeliveryLocationId());
        State deliveryState = masterDataCache.getStateById(serviceReceivedData.getDeliveryStateId());
        sr.setDeliveryLocation(new IdCodeName(deliveryLocation.getId(), deliveryLocation.getCode(), deliveryLocation.getName()));
        sr.setDeliveryState(new IdCodeName(deliveryState.getId(), deliveryState.getCode(), deliveryState.getName()));
        sr.setTotalAmount(serviceReceivedData.getTotalAmount());
        sr.setTotalTaxes(serviceReceivedData.getTotalTaxes());
        sr.setExtraCharges(serviceReceivedData.getExtraCharges());
        sr.setStatus(ServiceOrderStatus.valueOf(serviceReceivedData.getServiceReceiveStatus()));
        sr.setCreationTime(serviceReceivedData.getCreatedAt());
        sr.setCreatedBy(SCMUtil.generateIdCodeName(serviceReceivedData.getCreatedBy(), "",
                masterDataCache.getEmployee(serviceReceivedData.getCreatedBy())));
        sr.setUpdatedBy(SCMUtil.generateIdCodeName(serviceReceivedData.getUpdatedBy(), "", ""));
        sr.setUpdationTime(serviceReceivedData.getUpdatedAt());
        int companyId = serviceReceivedData.getCompanyId() != null ? serviceReceivedData.getCompanyId() : 1000;
        sr.setType(serviceReceivedData.getType());
        Company c = masterDataCache.getCompany(companyId);
        sr.setCompany(new IdCodeName(c.getId(), c.getServiceTaxNumber(), c.getName()));
        for (ServiceReceivedItemData item : serviceReceivedData.getServiceItemList()) {
            ServiceReceiveItem Item = SCMDataConverter.convert(item, scmCache);
            if(mapBusinessCenterIdCode != null)
            {
                Item.setBusinessCostCenterCode(mapBusinessCenterIdCode.get(Item.getBusinessCostCenterId()));
            }
            sr.getServiceReceiveItems().add(Item);
        }
        if (serviceReceivedData.getPaymentRequestData() != null) {
            sr.setPaymentRequestId(serviceReceivedData.getPaymentRequestData().getId());
        }
        sr.setToBePaid(SCMUtil.getStatus(serviceReceivedData.getToBePaid()));
        List<ServiceOrderServiceReceiveMappingData> serviceOrdersMappings = serviceReceivedData.getServiceOrderMappingList();
        List<ServiceOrder> serviceOrders = serviceOrdersMappings.stream()
                .map(ServiceOrderServiceReceiveMappingData::getServiceOrderData)
                .map(o -> convert(o, scmCache, masterDataCache, true, null))
                .collect(Collectors.toList());
        sr.setServiceOrderList(serviceOrders);
        return sr;
    }

    public static ServiceReceiveShort convertToServiceReceiveShort(ServiceReceivedData serviceReceivedData, SCMCache scmCache,
                                                                   MasterDataCache masterDataCache, boolean setChild, List<DocumentDetail> data) {
        ServiceReceiveShort sr = new ServiceReceiveShort();
        VendorDetail vendorDetail = scmCache.getVendorDetail(serviceReceivedData.getVendorId());
        sr.setId(serviceReceivedData.getServiceReceivedId());
        sr.setVendor(convertToBasicDetail(vendorDetail));
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendorDetail,
                serviceReceivedData.getDispatchLocationId());
        if (dispatchLocation.isPresent()) {
            VendorDispatchLocation location = dispatchLocation.get();
            sr.setLocation(new IdCodeName(location.getDispatchId(), location.getGstin(), location.getCity()));
        }
        sr.setTotalAmount(serviceReceivedData.getTotalAmount());
        sr.setTotalTaxes(serviceReceivedData.getTotalTaxes());
        sr.setExtraCharges(serviceReceivedData.getExtraCharges());
        sr.setStatus(ServiceOrderStatus.valueOf(serviceReceivedData.getServiceReceiveStatus()));
        sr.setCreationTime(serviceReceivedData.getCreatedAt());
        State deliveryState = masterDataCache.getStateById(serviceReceivedData.getDeliveryStateId());
        sr.setDeliveryState(new IdCodeName(deliveryState.getId(), deliveryState.getCode(), deliveryState.getName()));
        sr.setType(serviceReceivedData.getType());
        int companyId = serviceReceivedData.getCompanyId() != null ? serviceReceivedData.getCompanyId() : 1000;
        Company c = masterDataCache.getCompany(companyId);
        sr.setCompany(new IdCodeName(c.getId(), c.getServiceTaxNumber(), c.getName()));
        if (serviceReceivedData.getPaymentRequestData() != null) {
            sr.setPaymentRequestId(serviceReceivedData.getPaymentRequestData().getId());
        }
        List<ServiceOrderServiceReceiveMappingData> serviceOrdersMappings = serviceReceivedData.getServiceOrderMappingList();
        List<ServiceOrder> serviceOrders = serviceOrdersMappings.stream()
                .map(ServiceOrderServiceReceiveMappingData::getServiceOrderData)
                .map(o -> convert(o, scmCache, masterDataCache, true, null))
                .collect(Collectors.toList());
        sr.setServiceOrderList(serviceOrders);
        List<ServiceOrder> filterServiceOrders = new ArrayList<>();
        Set<Integer> h = new HashSet<>();

        for (ServiceOrder so: sr.getServiceOrderList()){
            List<ServiceOrderItem> fsoi = new ArrayList<>();
            Set<Integer> s = new HashSet<>();
            for(ServiceOrderItem oi: so.getOrderItems()){
                if(!s.contains(oi.getBusinessCostCenterId())){
                    fsoi.add(oi);
                }
                s.add(oi.getBusinessCostCenterId());
            }
            so.setOrderItems(fsoi);
            int key = so.getOrderItems().get(0).getBusinessCostCenterId();
            if(!h.contains(key)){
                filterServiceOrders.add(so);
            }
            h.add(key);
        }
        sr.setServiceOrderList(filterServiceOrders);
        sr.setServiceProofDoc(data);
        return sr;
    }

    public static ServiceReceiveItem convert(ServiceReceivedItemData itemData, SCMCache scmCache) {
        ServiceReceiveItem srItem = new ServiceReceiveItem();
        srItem.setId(itemData.getItemId());
        srItem.setCostElementId(itemData.getCostElementId());
        if(itemData.getRecievedcostElementDate()!=null){
            srItem.setCostElementDate(itemData.getRecievedcostElementDate());
        }
        if(itemData.getRecievedcostElementToDate()!=null){
            srItem.setCostElementToDate(itemData.getRecievedcostElementToDate());
        }
        srItem.setCostElementName(itemData.getCostElementName());
        srItem.setAscCode(itemData.getAscCode());
        srItem.setServiceDescription(itemData.getServiceDescription());
        srItem.setUnitOfMeasure(itemData.getUnitOfMeasure());
        srItem.setTotalAmount(itemData.getTotalAmount());
        srItem.setTotalCost(itemData.getTotalPrice());
        srItem.setTotalTax(itemData.getTotalTax());
        srItem.setTaxRate(itemData.getTaxRate());
        srItem.setUnitPrice(itemData.getUnitPrice());
        srItem.setRequestedQuantity(itemData.getReceivedQuantity());
        srItem.setReceivedQuantity(itemData.getReceivedQuantity());
        srItem.setAscCode(itemData.getAscCode());
        srItem.setServiceOrderId(itemData.getServiceOrderId());
        srItem.setServiceOrderItemId(itemData.getServiceOrderItemId());
        srItem.setBusinessCostCenterId(itemData.getBusinessCostCenterId());
        srItem.setBusinessCostCenterName(itemData.getBusinessCostCenterName());

        if (itemData.getTaxes() != null && !itemData.getTaxes().isEmpty()) {
            List<ServiceReceiveTaxDetail> taxDetailList = itemData.getTaxes()
                    .stream().map(taxData -> convert(taxData, itemData.getAscCode()))
                    .collect(Collectors.toList());
            srItem.setTaxes(taxDetailList);
        }
        if(Objects.nonNull(itemData.getServiceReceivedItemDrilldownData())){
            List<ServiceReceivedItemDrilldown> serviceReceivedItemDrilldownDataList = itemData.getServiceReceivedItemDrilldownData()
                    .stream().map(itemDetailsData -> SCMDataConverter.convert(itemDetailsData))
                    .collect(Collectors.toList());
            srItem.setServiceReceivedItemDrillDown(serviceReceivedItemDrilldownDataList);
        }
        return srItem;
    }

    public static ServiceReceiveTaxDetail convert(ServiceReceivedItemTaxData taxData, String ascCode) {
        ServiceReceiveTaxDetail taxDetail = new ServiceReceiveTaxDetail();
        taxDetail.setServiceReceiveTaxDetailId(taxData.getTaxDataId());
        taxDetail.setTaxName(taxData.getTaxType());
        taxDetail.setValue(taxData.getTaxValue());
        taxDetail.setPercentage(taxData.getTaxPercentage());
        taxDetail.setTaxCategory(ascCode);
        return taxDetail;
    }

    public static BusinessCostCenter convertToIdCodeName(BusinessCostCenterData data, MasterDataCache masterDataCache) {
        BusinessCostCenter center = new BusinessCostCenter();
        center.setId(data.getId());
        center.setCode(data.getCode());
        center.setName(data.getName());
        center.setStatus(data.getStatus());
        center.setType(data.getType());
        Location location = masterDataCache.getLocationbyId(data.getLocationId());
        center.setCompany(convertToIdCodeName(masterDataCache.getCompany(data.getCompanyId())));
        if (location != null) {
            center.setLocation(convertToIdCodeName(location));
            center.setState(convertToIdCodeName(location.getState()));
        }
        return center;
    }

    public static MonkWastageDetailData convertToMonkWastageDetail(MonkWastageDetail monkWastageDetail, SCMWastageData w) {
        MonkWastageDetailData detailData = new MonkWastageDetailData();

        detailData.setWastageData(w.getWastageItemId());
        detailData.setChaiMonk(monkWastageDetail.getChaiMonk());
        detailData.setMonkEvent(monkWastageDetail.getMonkEvent());
        detailData.setOrderId(monkWastageDetail.getOrderId());
        detailData.setTaskId(monkWastageDetail.getTaskId());
        detailData.setQuantity(BigDecimal.valueOf(monkWastageDetail.getQuantity()));

        return detailData;
    }

    public static ListDetail convert(ListDetailData list) {
        ListDetail listDetail = new ListDetail();
        listDetail.setListDetailId(list.getId());
        listDetail.setCode(list.getCode());
        listDetail.setName(list.getName());
        listDetail.setAlias(list.getAlias());
        listDetail.setDescription(list.getDescription());
        listDetail.setType(list.getType());
        listDetail.setStatus(list.getStatus());
        listDetail.setBaseType(list.getBaseType());
        listDetail.setEmail(list.getEmail());
        if (list.getListType() != null) {
            listDetail.setListType(convertListType(list.getListType()));
        }
        return listDetail;
    }

    private static List<ListType> convertListType(List<ListTypeData> listTypeData) {
        List<ListType> listType = new ArrayList<ListType>();
        for (ListTypeData list : listTypeData) {
            ListType listTypeNew = new ListType();
            listTypeNew.setListTypeId(list.getId());
            listTypeNew.setCode(list.getCode());
            listTypeNew.setName(list.getName());
            listTypeNew.setAlias(list.getAlias());
            listTypeNew.setDescription(list.getDescription());
            listTypeNew.setStatus(list.getStatus());
            if (list.getListData() != null) {
                listTypeNew.setListData(convertListData(list.getListData()));
            }
            listTypeNew.setBudgetCategory(list.getBudgetCategory());
            listType.add(listTypeNew);
        }
        return listType;
    }

    private static List<ListData> convertListData(List<ListDatas> listDatas) {
        List<ListData> listData = new ArrayList<ListData>();
        for (ListDatas list : listDatas) {
            ListData listDataNew = new ListData();
            listDataNew.setListDataId(list.getId());
            listDataNew.setCode(list.getCode());
            listDataNew.setName(list.getName());
            listDataNew.setAlias(list.getAlias());
            listDataNew.setDescription(list.getDescription());
            listDataNew.setStatus(list.getStatus());
            listData.add(listDataNew);
        }
        return listData;
    }

    public static ListDetailData convert(ListDetail list) {
        ListDetailData listDetail = new ListDetailData();
        listDetail.setId(list.getListDetailId());
        listDetail.setCode(list.getCode());
        listDetail.setName(list.getName());
        listDetail.setAlias(list.getAlias());
        listDetail.setDescription(list.getDescription());
        listDetail.setType(list.getType());
        listDetail.setStatus(list.getStatus());
        listDetail.setBaseType(list.getBaseType());
        listDetail.setIsAccountable(SCMUtil.setStatus(list.isAccountable()));
        listDetail.setEmail(list.getEmail());
//        listDetail.setBudgetCategory(list.getBudgetCategory());
        return listDetail;
    }

    public static ListTypeData convert(ListType list) {
        ListTypeData listTypeNew = new ListTypeData();
        listTypeNew.setId(list.getListTypeId());
        listTypeNew.setCode(list.getCode());
        listTypeNew.setName(list.getName());
        listTypeNew.setAlias(list.getAlias());
        listTypeNew.setDescription(list.getDescription());
        listTypeNew.setStatus(list.getStatus());
        listTypeNew.setBudgetCategory(list.getBudgetCategory());
        return listTypeNew;
    }

    public static ListDatas convert(ListData list) {
        ListDatas listDataNew = new ListDatas();
        listDataNew.setId(list.getListDataId());
        listDataNew.setCode(list.getCode());
        listDataNew.setName(list.getName());
        listDataNew.setAlias(list.getAlias());
        listDataNew.setDescription(list.getDescription());
        listDataNew.setStatus(list.getStatus());
        return listDataNew;
    }

    public static ListDetail convertListDetail(ListDetailData list) {
        ListDetail listDetail = new ListDetail();
        listDetail.setListDetailId(list.getId());
        listDetail.setCode(list.getCode());
        listDetail.setName(list.getName());
        listDetail.setAlias(list.getAlias());
        listDetail.setDescription(list.getDescription());
        listDetail.setType(list.getType());
        listDetail.setStatus(list.getStatus());
        listDetail.setAccountable(AppUtils.getStatus(list.getIsAccountable()));
//        listDetail.setBudgetCategory(list.getBudgetCategory());
        return listDetail;
    }

    public static ListType convertType(ListTypeData list) {
        ListType listTypeNew = new ListType();
        listTypeNew.setListTypeId(list.getId());
        listTypeNew.setCode(list.getCode());
        listTypeNew.setName(list.getName());
        listTypeNew.setAlias(list.getAlias());
        listTypeNew.setDescription(list.getDescription());
        listTypeNew.setStatus(list.getStatus());
        listTypeNew.setBudgetCategory(list.getBudgetCategory());
        return listTypeNew;
    }

    public static ListTypeData convertType(ListType list) {
        ListTypeData listTypeNew = new ListTypeData();
        listTypeNew.setId(list.getListTypeId());
        listTypeNew.setCode(list.getCode());
        listTypeNew.setName(list.getName());
        listTypeNew.setAlias(list.getAlias());
        listTypeNew.setDescription(list.getDescription());
        listTypeNew.setStatus(list.getStatus());
        listTypeNew.setListDetail(convert(list.getListDetail()));
        listTypeNew.setBudgetCategory(list.getBudgetCategory());
        return listTypeNew;
    }

    public static ListDatas convertData(ListData list) {
        ListDatas listDataNew = new ListDatas();
        listDataNew.setId(list.getListDataId());
        listDataNew.setCode(list.getCode());
        listDataNew.setName(list.getName());
        listDataNew.setAlias(list.getAlias());
        listDataNew.setDescription(list.getDescription());
        listDataNew.setStatus(list.getStatus());
        listDataNew.setListType(convert(list.getListType()));
        return listDataNew;
    }

    public static CapexRequestDetail convert(CapexRequestDetailData capexRequestData) {
        CapexRequestDetail capexData = new CapexRequestDetail();
        capexData.setId(capexRequestData.getId());
        capexData.setType(capexRequestData.getType());
        capexData.setUnitId(capexRequestData.getUnitId());
        capexData.setUnitName(capexRequestData.getUnitName());
        capexData.setAccessKey(capexRequestData.getAccessKey());
        capexData.setLastUpdatedBy(capexRequestData.getLastUpdatedBy());
        capexData.setLastUpdateTime(capexRequestData.getLastUpdateTime());
        capexData.setTemplateVersion(capexRequestData.getTemplateVersion());
        capexData.setStatus(capexRequestData.getStatus());
        return capexData;
    }

    public static PurchaseOrderExtendedStatusLog convert(PurchaseOrderExtendedStatusLogData request) {
        PurchaseOrderExtendedStatusLog purchaseOrderExtendedStatusLog = new PurchaseOrderExtendedStatusLog();
       purchaseOrderExtendedStatusLog.setPurchaseOrderExtendedStatusId(request.getId());
       purchaseOrderExtendedStatusLog.setDescription(request.getDescription());
       purchaseOrderExtendedStatusLog.setExtensionReason(request.getExtensionReason());
       purchaseOrderExtendedStatusLog.setFromStatus(request.getFromStatus());
       purchaseOrderExtendedStatusLog.setToStatus(request.getToStatus());
       purchaseOrderExtendedStatusLog.setPurchaseOrderId(request.getPurchaseOrderId());
       purchaseOrderExtendedStatusLog.setUpdatedBy(request.getUpdatedBy());
       purchaseOrderExtendedStatusLog.setUpdateTime(request.getUpdateTime());
        return purchaseOrderExtendedStatusLog;
    }

    public static ServiceReceivedItemDrilldownData convert(ServiceReceivedItemDrilldown serviceReceivedItemDrilldown){
        ServiceReceivedItemDrilldownData serviceReceivedItemDrilldownData = new ServiceReceivedItemDrilldownData();
        serviceReceivedItemDrilldownData.setDescription(serviceReceivedItemDrilldown.getDescription());
        serviceReceivedItemDrilldownData.setReceivedQuantity(serviceReceivedItemDrilldown.getReceivedQuantity());
        serviceReceivedItemDrilldownData.setNos(serviceReceivedItemDrilldown.getNos());
        if (Objects.nonNull(serviceReceivedItemDrilldown.getMultiplier())) {
            serviceReceivedItemDrilldownData.setMultiplier(serviceReceivedItemDrilldown.getMultiplier());
        }
        serviceReceivedItemDrilldownData.setSourceUom(serviceReceivedItemDrilldown.getSourceUom());
        serviceReceivedItemDrilldownData.setHeight(serviceReceivedItemDrilldown.getHeight());
        serviceReceivedItemDrilldownData.setLength(serviceReceivedItemDrilldown.getLength());
        serviceReceivedItemDrilldownData.setWidth(serviceReceivedItemDrilldown.getWidth());
        serviceReceivedItemDrilldownData.setIsExclusionEntry(serviceReceivedItemDrilldown.getIsExclusionEntry());

        return serviceReceivedItemDrilldownData;


    }

    public static ServiceReceivedItemDrilldown convert(ServiceReceivedItemDrilldownData serviceReceivedItemDrilldownData){
        ServiceReceivedItemDrilldown serviceReceivedItemDrilldown = new ServiceReceivedItemDrilldown();
        serviceReceivedItemDrilldown.setServiceReceivedItemDrilldownId(serviceReceivedItemDrilldownData.getServiceReceivedItemDrilldownId());
        serviceReceivedItemDrilldown.setServiceReceivedItemId(serviceReceivedItemDrilldownData.getServiceReceivedItemId());
        serviceReceivedItemDrilldown.setReceivedQuantity(serviceReceivedItemDrilldownData.getReceivedQuantity());
        serviceReceivedItemDrilldown.setWidth(serviceReceivedItemDrilldownData.getWidth());
        serviceReceivedItemDrilldown.setLength(serviceReceivedItemDrilldownData.getLength());
        serviceReceivedItemDrilldown.setHeight(serviceReceivedItemDrilldownData.getHeight());
        serviceReceivedItemDrilldown.setSourceUom(serviceReceivedItemDrilldownData.getSourceUom());
        serviceReceivedItemDrilldown.setNos(serviceReceivedItemDrilldownData.getNos());
        if (Objects.nonNull(serviceReceivedItemDrilldownData.getMultiplier())) {
            serviceReceivedItemDrilldown.setMultiplier(serviceReceivedItemDrilldownData.getMultiplier());
        }
        serviceReceivedItemDrilldown.setDescription(serviceReceivedItemDrilldownData.getDescription());
        serviceReceivedItemDrilldown.setIsExclusionEntry(serviceReceivedItemDrilldownData.getIsExclusionEntry());
        serviceReceivedItemDrilldown.setIsExclusionEntry(serviceReceivedItemDrilldownData.getIsExclusionEntry());

        return serviceReceivedItemDrilldown;

    }

    public static SREmailShort convert(ServiceReceivedItemData data, Set<Integer> srIds) {
        SREmailShort entry = new SREmailShort();
        entry.setItemId(data.getItemId());
        entry.setBusinessCostCenterName(data.getBusinessCostCenterName());
        entry.setCostElementName(data.getCostElementName());
        entry.setAscCode(data.getAscCode());
        entry.setServiceDescription(data.getServiceDescription());
        entry.setReceivedQuantity(data.getReceivedQuantity());
        entry.setUnitOfMeasure(data.getUnitOfMeasure());
        entry.setUnitPrice(data.getTotalPrice());
        entry.setTotalPrice(data.getTotalPrice());
        entry.setTotalTax(data.getTotalTax());
        entry.setTaxRate(data.getTaxRate());
        if (srIds.contains(data.getItemId())) {
            LOG.info("This the latest of Id : {}",data.getItemId());
            entry.setLatest(AppConstants.YES);
        }
        else {
            entry.setLatest(AppConstants.NO);
        }
        return entry;
    }

    public static OutwardRegisterData convertOutwardRegister(OutwardRegister outwardRegister)  {
        OutwardRegisterData entity = new OutwardRegisterData();

        entity.setDateTime(outwardRegister.getDateTime());
        entity.setChallanNumber(outwardRegister.getChallanNo());
        entity.setAddressOfBuyer(outwardRegister.getAddressOfBuyer());
        entity.setDetailsOfArticle(outwardRegister.getDetailsOfArticle());
        entity.setQuantity(outwardRegister.getQuantity());
        entity.setAmount(outwardRegister.getAmount());
        entity.setNameOfDeliverer(outwardRegister.getNameOfDeliverer());
        entity.setVehicleNumberType(outwardRegister.getVehicleNoType());
        entity.setSignatureOfSecurity(outwardRegister.getSignatureOfSecurity());
        entity.setBusinessType(outwardRegister.getBusinessType());
        entity.setInvoiceId(outwardRegister.getInvoiceId());
        entity.setId(outwardRegister.getId());
        entity.setRemarks(outwardRegister.getRemarks());
        entity.setUnitId(outwardRegister.getUnitId());
        entity.setSubmissionDateTime(SCMUtil.getCurrentTimestamp());
        return entity;
    }

    public static VarianceAcknowledgementDetail convert(VarianceAcknowledgementData varianceAcknowledgementData) {
        VarianceAcknowledgementDetail varianceAcknowledgementDetail = new VarianceAcknowledgementDetail();
        varianceAcknowledgementDetail.setId(varianceAcknowledgementData.getId());
        varianceAcknowledgementDetail.setBusinessDate(varianceAcknowledgementData.getBusinessDate());
        varianceAcknowledgementDetail.setAcknowledged(varianceAcknowledgementData.getAcknowledged());
        varianceAcknowledgementDetail.setVariancePercentage(varianceAcknowledgementData.getVariancePercentage());
        varianceAcknowledgementDetail.setGenerationTime(varianceAcknowledgementData.getGenerationTime());
        varianceAcknowledgementDetail.setVarianceCost(varianceAcknowledgementData.getVarianceCost());
        varianceAcknowledgementDetail.setInventoryCost(varianceAcknowledgementData.getInventoryCost());
        varianceAcknowledgementDetail.setUnitId(varianceAcknowledgementData.getUnitId());
        varianceAcknowledgementDetail.setFrequency(varianceAcknowledgementData.getFrequency());
        varianceAcknowledgementDetail.setAcknowledgedBy(varianceAcknowledgementData.getAcknowledgedBy());
        varianceAcknowledgementDetail.setAcknowledgedTime(varianceAcknowledgementData.getAcknowledgedTime());
        if(Objects.nonNull(varianceAcknowledgementData.getCurrentDayCloseEvent()) && Objects.nonNull(varianceAcknowledgementData.getCurrentDayCloseEvent().getEventId())){
            varianceAcknowledgementDetail.setScmDayCloseEventId(varianceAcknowledgementData.getCurrentDayCloseEvent().getEventId());
        }
        varianceAcknowledgementDetail.setAcknowledgementRequired(varianceAcknowledgementData.getAcknowledgementRequired());
        varianceAcknowledgementDetail.setAcknowledgementType(varianceAcknowledgementData.getAcknowledgementType());
        varianceAcknowledgementDetail.setComment(varianceAcknowledgementData.getComment());
        return varianceAcknowledgementDetail ;
    }

    public static CreditDebitNoteDetail convert(SalesPerformaInvoiceCreditDebitNoteDetail noteDetail) {
        CreditDebitNoteDetail detail = new CreditDebitNoteDetail();
        detail.setId(noteDetail.getId());
        detail.setInvoiceId(noteDetail.getInvoiceId());
        detail.setVendorId(noteDetail.getVendorId());
        detail.setStatus(noteDetail.getStatus());
        detail.setCreatedBy(noteDetail.getCreatedBy());
        detail.setGenerationTime(noteDetail.getGenerationTime());
        detail.setApprovalRequired(noteDetail.getApprovalRequired());
        detail.setApprovedBy(noteDetail.getApprovedBy());
        detail.setUpdatedAt(noteDetail.getUpdatedAt());
        detail.setCreditNoteId(noteDetail.getCreditNoteId());
        detail.setCreditNoteDocUrl(noteDetail.getCreditNoteDocUrl());
        detail.setCreditNoteDocId(noteDetail.getCreditNoteDocId());
        detail.setTotalAmount(noteDetail.getTotalAmount());
        detail.setTotalTax(noteDetail.getTotalTax());
        detail.setNetAmount(noteDetail.getNetAmount());
        List<CreditDebitNoteItemDetail> itemList = new ArrayList<>();
        for(SalesPerformaInvoiceCreditDebitNoteItemDetail item : noteDetail.getCreditDebitNoteItems()){
            CreditDebitNoteItemDetail creditDebitNoteItemDetail = SCMDataConverter.convert(item);
            itemList.add(creditDebitNoteItemDetail);
        }
        detail.setItemDetails(itemList);
        return detail;
    }

    public static CreditDebitNoteItemDetail convert(SalesPerformaInvoiceCreditDebitNoteItemDetail noteItemDetail) {
        CreditDebitNoteItemDetail itemDetail = new CreditDebitNoteItemDetail();
        itemDetail.setItemId(noteItemDetail.getItemId());
        itemDetail.setItemDesc(noteItemDetail.getItemDesc());
        itemDetail.setQty(noteItemDetail.getQty());
        itemDetail.setPrice(noteItemDetail.getPrice());
        itemDetail.setNetAmount(noteItemDetail.getNetAmount());
        itemDetail.setTaxPercent(noteItemDetail.getTaxPercent());
        itemDetail.setTaxAmount(noteItemDetail.getTaxAmount());
        itemDetail.setTotalAmount(noteItemDetail.getTotalAmount());
        return itemDetail;
    }
    static BiFunction<Object,String,Object> getPrivateFieldValue = (e, f) -> {
        try {
            Field field =  e.getClass().getDeclaredField(f);
            field.setAccessible(true);
            return  field.get(e);
        } catch (IllegalAccessException | NoSuchFieldException ex) {
            throw new RuntimeException(ex);
        }
    };
    public static ValidateStateOutput toConverter(Object obj,UnitClosureState unitClosureState){
       return  ValidateStateOutput.builder().taskId(unitClosureState.getStateId())
                .CurrentKeyStatus((String) getPrivateFieldValue.apply(obj,"status"))
                .initiatedBy((Integer) getPrivateFieldValue.apply(obj,"generatedBy"))
                .KeyType("TO_ID")
                .keyValue((Integer) getPrivateFieldValue.apply(obj,"id"))
                .isCheckPassed(false).build();
    }

    public static ValidateStateOutput poConverter(Object obj,UnitClosureState unitClosureState){
        return  ValidateStateOutput.builder().taskId(unitClosureState.getStateId())
                .CurrentKeyStatus((String) getPrivateFieldValue.apply(obj,"status"))
                .initiatedBy((Integer) getPrivateFieldValue.apply(obj,"generatedBy"))
                .KeyType("PO_ID")
                .keyValue((Integer) getPrivateFieldValue.apply(obj,"id"))
                .isCheckPassed(false).build();
    }

    public static ValidateStateOutput roConverter(Object obj,UnitClosureState unitClosureState){
        return  ValidateStateOutput.builder().taskId(unitClosureState.getStateId())
                .CurrentKeyStatus((String) getPrivateFieldValue.apply(obj,"status"))
                .initiatedBy((Integer) getPrivateFieldValue.apply(obj,"generatedBy"))
                .KeyType("RO_ID")
                .keyValue((Integer) getPrivateFieldValue.apply(obj,"id"))
                .isCheckPassed(false).build();
    }

    public static ValidateStateOutput grConverter(Object obj,UnitClosureState unitClosureState){
        return  ValidateStateOutput.builder().taskId(unitClosureState.getStateId())
                .CurrentKeyStatus((String) getPrivateFieldValue.apply(obj,"status"))
                .initiatedBy((Integer) getPrivateFieldValue.apply(obj,"generatedBy"))
                .KeyType("GR_ID")
                .keyValue((Integer) getPrivateFieldValue.apply(obj,"id"))
                .isCheckPassed(false).build();
    }

    public static ValidateStateOutput vendorGrConverter(Object obj,UnitClosureState unitClosureState){
        return  ValidateStateOutput.builder().taskId(unitClosureState.getStateId())
                .CurrentKeyStatus((String) getPrivateFieldValue.apply(obj,"grStatus"))
                .initiatedBy((Integer) getPrivateFieldValue.apply(obj,"createdBy"))
                .KeyType("VENDOR_GR_ID")
                .keyValue((Integer) getPrivateFieldValue.apply(obj,"goodsReceivedId"))
                .isCheckPassed(false).build();
    }

    public static ValidateStateOutput pendingPrConverter(Object obj,UnitClosureState unitClosureState){
        return  ValidateStateOutput.builder().taskId(unitClosureState.getStateId())
                .CurrentKeyStatus((String) getPrivateFieldValue.apply(obj,"currentStatus"))
                .initiatedBy((Integer) getPrivateFieldValue.apply(obj,"createdBy"))
                .KeyType("PR_ID")
                .keyValue((Integer) getPrivateFieldValue.apply(obj,"id"))
                .isCheckPassed(false).build();
    }

    public static ValidateStateOutput acitveAssetConverter(Object obj,UnitClosureState unitClosureState){
        return  ValidateStateOutput.builder().taskId(unitClosureState.getStateId())
                .CurrentKeyStatus((String) getPrivateFieldValue.apply(obj,"assetStatus"))
                .initiatedBy((Integer) getPrivateFieldValue.apply(obj,"createdBy"))
                .KeyType("ASSET_ID")
                .keyValue((Integer) getPrivateFieldValue.apply(obj,"assetId"))
                .isCheckPassed(false).build();
    }

    public static ValidateStateOutput soConverter(Object obj,UnitClosureState unitClosureState){
        return  ValidateStateOutput.builder().taskId(unitClosureState.getStateId())
                .CurrentKeyStatus((String) getPrivateFieldValue.apply(obj,"status"))
                .initiatedBy((Integer) getPrivateFieldValue.apply(obj,"generatedBy"))
                .KeyType("SO_ID")
                .keyValue((Integer) getPrivateFieldValue.apply(obj,"id"))
                .isCheckPassed(false).build();
    }



    public static SalesPerformaInvoiceItemCorrected convert(SalesPerformaInvoiceItem item, Integer invoiceId , SalesPerformaInvoiceCorrected correctedInvoice) {
        SalesPerformaInvoiceItemCorrected itemDetail = new SalesPerformaInvoiceItemCorrected();
        itemDetail.setSkuId(item.getSku().getId());
        itemDetail.setSkuName(item.getSku().getName());
        itemDetail.setPrice(item.getSellPrice());
        itemDetail.setRevisedPrice(item.getCorrectedSellPrice());
        itemDetail.setPkgQty(item.getPkgQty());
        itemDetail.setRevisedPkgQty(item.getCorrectedPkgQty());
        itemDetail.setAmount(item.getSellAmount());
        itemDetail.setRevisedAmount(item.getCorrectedSellAmount());
        itemDetail.setTax(item.getTotalTax());
        itemDetail.setRevisedTax(item.getCorrectedTax());
        if(item.getPkgQty().equals(item.getCorrectedPkgQty())){
           itemDetail.setCorrectedPkgQty(item.getPkgQty());
           itemDetail.setCorrectedPrice(SCMUtil.subtract(item.getSellPrice(),item.getCorrectedSellPrice()).abs());
        }else {
            BigDecimal amtDiff = SCMUtil.subtract(item.getSellAmount(),item.getCorrectedSellAmount()).abs();
            BigDecimal pkgQtyDiff = SCMUtil.subtract(item.getPkgQty(),item.getCorrectedPkgQty()).abs();
            BigDecimal correctedPrice = SCMUtil.divideWithScale(amtDiff,pkgQtyDiff,4);
            itemDetail.setCorrectedPkgQty(pkgQtyDiff);
            itemDetail.setCorrectedPrice(correctedPrice);
        }
        return itemDetail;
    }

    public static CorrectedSalesInvoiceDetails convert(SalesPerformaInvoiceCorrected revisedInvoice) {
        CorrectedSalesInvoiceDetails detail = new CorrectedSalesInvoiceDetails();
        detail.setId(revisedInvoice.getId());
        detail.setInvoiceId(revisedInvoice.getInvoiceId());
        detail.setInvoiceStatus(revisedInvoice.getInvoiceStatus());
        detail.setType(revisedInvoice.getType());
        detail.setDocId(revisedInvoice.getDocId());
        detail.setCreditNoteDocId(revisedInvoice.getCreditNoteDocId());
        detail.setCreditNoteDocUrl(revisedInvoice.getCreditNoteDocUrl());
        detail.setGeneratedCreditNoteId(revisedInvoice.getGeneratedCreditNoteId());
        detail.setDebitNoteDocId(revisedInvoice.getDebitNoteDocId());
        detail.setDebitNoteDocUrl(revisedInvoice.getDebitNoteDocUrl());
        detail.setGeneratedDebitNoteId(revisedInvoice.getGeneratedDebitNoteId());
        detail.setInvoiceId(revisedInvoice.getInvoiceId());
        detail.setIrnNo(revisedInvoice.getIrnNo());
        detail.setUploadedAckNo(revisedInvoice.getUploadedAckNo());
        detail.setUploadedEwayNo(revisedInvoice.getUploadedEwayNo());
        detail.setSignedQrCode(revisedInvoice.getSignedQrCode());
        detail.setBarcodeId(revisedInvoice.getBarcodeId());
        List<CorrectedSalesInvoiceItemDetails> items = new ArrayList<>();
        for (SalesPerformaInvoiceItemCorrected itemDetail : revisedInvoice.getSalesPerformaCorrectedItems()) {
            items.add(convert(itemDetail));
        }
        detail.setSalesPerformaCorrectedItems(items);
        return detail;
    }

    public static CorrectedSalesInvoiceItemDetails convert(SalesPerformaInvoiceItemCorrected revisedInvoiceItem) {
        CorrectedSalesInvoiceItemDetails csiid = new CorrectedSalesInvoiceItemDetails();
        csiid.setItemId(revisedInvoiceItem.getItemId());
        csiid.setSkuId(revisedInvoiceItem.getSkuId());
        csiid.setSkuName(revisedInvoiceItem.getSkuName());
        csiid.setPrice(revisedInvoiceItem.getPrice());
        csiid.setRevisedPrice(revisedInvoiceItem.getRevisedPrice());
        csiid.setCorrectedPrice(revisedInvoiceItem.getCorrectedPrice());
        csiid.setPkgQty(revisedInvoiceItem.getPkgQty());
        csiid.setRevisedPkgQty(revisedInvoiceItem.getRevisedPkgQty());
        csiid.setCorrectedPkgQty(revisedInvoiceItem.getCorrectedPkgQty());
        csiid.setTax(revisedInvoiceItem.getTax());
        csiid.setRevisedTax(revisedInvoiceItem.getRevisedTax());
        csiid.setAmount(revisedInvoiceItem.getAmount());
        csiid.setRevisedAmount(revisedInvoiceItem.getRevisedAmount());
        return csiid;
    }

    public static PaymentRequestMetaDataDomain convert(PaymentRequestMetaData paymentRequestMetaData) {
        if(paymentRequestMetaData == null) return null;
        PaymentRequestMetaDataDomain paymentRequestMetaDataDomain = new PaymentRequestMetaDataDomain();
        paymentRequestMetaDataDomain.setPaymentRequestId(paymentRequestMetaData.getPaymentRequest().getId());
        paymentRequestMetaDataDomain.setGstRate(paymentRequestMetaData.getGstRate());
        paymentRequestMetaDataDomain.setIsRcm(Objects.equals(paymentRequestMetaData.getIsRCM(),"Y"));
        paymentRequestMetaDataDomain.setIsGstAvailed(Objects.equals(paymentRequestMetaData.getIsGstAvailed(),"Y"));
        paymentRequestMetaDataDomain.setLdcVendorDomain(LdcVendorMapper.INSTANCE.getLdcVendorDomain(paymentRequestMetaData.getLdcVendorData()));
        paymentRequestMetaDataDomain.setTdsLedger(PrMetaDataMapper.INSTANCE.toTdsLegerRateDomain(paymentRequestMetaData.getTdsLedger()));
        paymentRequestMetaDataDomain.setRecipientState(PrMetaDataMapper.INSTANCE.toGstOfStplDomain(paymentRequestMetaData.getRecipientState()));
        paymentRequestMetaDataDomain.setSupplierState(PrMetaDataMapper.INSTANCE.toGstStateMetaDataDomain(paymentRequestMetaData.getSupplierState()));
        paymentRequestMetaDataDomain.setTdsPercentage(paymentRequestMetaData.getTdsPercentage());
        paymentRequestMetaDataDomain.setLoggedMessages(paymentRequestMetaData.getLoggedMessages());
        return  paymentRequestMetaDataDomain;
    }

    public static WorkOrder convertToDto(WorkOrderData entity) {
        if (entity == null) return null;

        WorkOrder dto = new WorkOrder();
        dto.setWorkOrderId(entity.getWorkOrderId());
        dto.setContractId(entity.getContractData().getContractId());
        dto.setGeneratedWorkOrderNumber(entity.getGeneratedWorkOrderNumber());
        dto.setWorkOrderType(entity.getWorkOrderType());
        dto.setWorkOrderStatus(entity.getWorkOrderStatus());
        dto.setStartDate(entity.getStartDate());
        dto.setEndDate(entity.getEndDate());
        dto.setApprovalRequestId(entity.getApprovalRequestId());
        dto.setIsByPassed(entity.getIsByPassed());
        dto.setWorkOrderDocId(entity.getWoApprovalMetaData().getWorkOrderDocId());

        return dto;
    }


    public static List<VendorContractItemDataVO> convertToItems(List<VendorContractItemData> vendorContractItemData, SCMCache scmCache, MasterDataCache masterDataCache) {
        List<VendorContractItemDataVO> itemDataVOS = new ArrayList<>();
        for(VendorContractItemData itemData : vendorContractItemData){
            VendorContractItemDataVO itemDataVO = new VendorContractItemDataVO();

            IdCodeName sku = new IdCodeName();
            sku.setId(itemData.getSkuId());
            sku.setCode(null);
            sku.setName(scmCache.getSkuDefinition(itemData.getSkuId()).getSkuName());
            itemDataVO.setSkuId(sku);

            itemDataVO.setContractItemId(itemData.getContractItemId());
            itemDataVO.setCurrentPrice(itemData.getCurrentPrice());

            IdCodeName skuPackaging = new IdCodeName();
            PackagingDefinition pkfDef = scmCache.getPackagingDefinition(itemData.getSkuPackagingId());
            skuPackaging.setId(itemData.getSkuPackagingId());
            skuPackaging.setCode(pkfDef.getPackagingCode());
            skuPackaging.setName(pkfDef.getPackagingName());
            itemDataVO.setSkuPackagingId(skuPackaging);

            itemDataVO.setWorkOrderId(itemData.getWorkOrderData().getWorkOrderId());

            itemDataVO.setPackagingData(convertToPackagingData(scmCache.getPackagingDefinition(itemData.getSkuPackagingId())));
            itemDataVO.setStatus(itemData.getStatus());
            itemDataVO.setUpdatedPrice(itemData.getUpdatedPrice());
            itemDataVO.setCurrentPrice(itemData.getCurrentPrice());
            itemDataVO.setVendorId(itemData.getVendorId());

            Location dispatchLocation = masterDataCache.getLocationbyId(itemData.getDispatchLocationId());
            Location deliveryLocation = masterDataCache.getLocationbyId(itemData.getDeliveryLocationId());
            itemDataVO.setDeliveryLocation(deliveryLocation.getName().toUpperCase());
            itemDataVO.setDispatchLocation(dispatchLocation.getName().toUpperCase());
            itemDataVO.setDeliveryLocationId(itemData.getDeliveryLocationId());
            itemDataVO.setDispatchLocationId(itemData.getDispatchLocationId());

            itemDataVO.setRejectionReason(itemData.getRejectionReason());
            itemDataVO.setSelectedRejectionReason(itemData.getSelectedRejectionReason());
            itemDataVO.setTaxPercentage(itemData.getTaxPercentage());
            itemDataVO.setSkuPriceState(itemData.getSkuPriceState() != null ? itemData.getSkuPriceState().name() : null);

            itemDataVOS.add(itemDataVO);
        }
        return itemDataVOS;
    }



}
