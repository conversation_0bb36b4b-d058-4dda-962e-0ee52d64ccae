/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * UnitProductMappingData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_PRODUCT_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = {"UNIT_ID", "PRODUCT_ID"}))
public class UnitProductMappingData implements java.io.Serializable {

    private Integer unitProductMappingId;
    private int unitId;
    private int productId;
    private int vendorId;
    private String mappingStatus;

    public UnitProductMappingData() {
    }

    public UnitProductMappingData(int unitId, int productId, int vendorId, String mappingStatus) {
        this.unitId = unitId;
        this.productId = productId;
        this.vendorId = vendorId;
        this.mappingStatus = mappingStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "UNIT_PRODUCT_MAPPING_ID", unique = true, nullable = false)
    public Integer getUnitProductMappingId() {
        return this.unitProductMappingId;
    }

    public void setUnitProductMappingId(Integer unitProductMappingId) {
        this.unitProductMappingId = unitProductMappingId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public int getUnitId() {
        return this.unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public int getProductId() {
        return this.productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public int getVendorId() {
        return this.vendorId;
    }

    public void setVendorId(int vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 15)
    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

}
