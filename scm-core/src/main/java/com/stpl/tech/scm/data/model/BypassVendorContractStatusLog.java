package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "BYPASS_VENDOR_CONTRACT_STATUS_LOG")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BypassVendorContractStatusLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BYPASS_VENDOR_CONTRACT_STATUS_LOG_ID",unique = true)
    private Integer bypassVendorContractStatusLogId;

    @Column(name = "FROM_STATUS")
    private String fromStatus;

    @Column(name = "TO_STATUS")
    private String toStatus;

    @Column(name = "UPDATED_BY", nullable = false)
    private Integer updatedBy;

    @Column(name = "UPDATED_TIME", nullable = false)
    private Date updatedTime;

    @Column(name = "BYPASS_CONTRACT_ID", nullable = false)
    private Integer bypassContractId;

}
