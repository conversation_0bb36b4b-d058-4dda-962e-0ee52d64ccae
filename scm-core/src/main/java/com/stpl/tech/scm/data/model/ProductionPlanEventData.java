package com.stpl.tech.scm.data.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "PRODUCTION_PLAN_EVENT")
public class ProductionPlanEventData implements Cloneable {

	private Integer id;
	private int unitId;
	private Date fulfillmentDate;
	private Date generationTime;
	private Date lastUpdateTime;
	private int generatedBy;
	private Integer lastUpdatedBy;
	private String status;
	private String isUpdated;

	private List<PlanOrderMappingData> orderMappingData = new ArrayList<PlanOrderMappingData>(0);
	private List<PlanOrderItemData> orderItemData = new ArrayList<PlanOrderItemData>(0);

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "PLAN_ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "GENERATION_TIME", nullable = false)
	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int requestUnitId) {
		this.unitId = requestUnitId;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public int getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(int generatedBy) {
		this.generatedBy = generatedBy;
	}

	@Column(name = "PLAN_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "FULFILLMENT_DATE", nullable = false)
	public Date getFulfillmentDate() {
		return fulfillmentDate;
	}

	public void setFulfillmentDate(Date fulfillmentDate) {
		this.fulfillmentDate = fulfillmentDate;
	}

	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "LAST_UPDATED_BY", nullable = true)
	public Integer getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(Integer lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "event")
	public List<PlanOrderMappingData> getOrderMappingData() {
		return orderMappingData;
	}

	public void setOrderMappingData(List<PlanOrderMappingData> requestOrders) {
		this.orderMappingData = requestOrders;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "eventId")
	public List<PlanOrderItemData> getOrderItemData() {
		return orderItemData;
	}

	public void setOrderItemData(List<PlanOrderItemData> orderItemData) {
		this.orderItemData = orderItemData;
	}

	@Column(name = "IS_UPDATED", nullable = true)
	public String getIsUpdated() {
		return isUpdated;
	}

	public void setIsUpdated(String isUpdated) {
		this.isUpdated = isUpdated;
	}
}
