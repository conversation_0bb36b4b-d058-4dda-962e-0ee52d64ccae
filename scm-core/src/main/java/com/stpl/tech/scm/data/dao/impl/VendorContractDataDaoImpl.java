package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.data.dao.VendorContractDataDao;
import com.stpl.tech.scm.data.model.VendorContractData;
import com.stpl.tech.scm.data.model.VendorContractLogs;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.domain.model.LogType;
import com.stpl.tech.scm.domain.model.VendorContractStatus;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class VendorContractDataDaoImpl extends SCMAbstractDaoImpl implements VendorContractDataDao {

    @Override
    public VendorContractData getByVendorIdAndStatusIn(Integer vendorId, List<String> status) {
        String queryString = "SELECT DISTINCT vcd FROM VendorContractData vcd " +
                        "LEFT JOIN FETCH vcd.workOrderDataSet wo " +
                        "LEFT JOIN FETCH wo.vendorContractItemDataList vcid " +
                        "LEFT JOIN FETCH vcid.vendorContractItemUnitDataList  " +
                        "WHERE vcd.vendorId =:vendorId AND vcd.status IN (:status) ORDER BY vcd.contractId DESC";

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId", vendorId).setParameter("status", status).setMaxResults(1);
        List<VendorContractData> data = query.getResultList();
        return (VendorContractData)returnResultList(data);
    }

    private static Object returnResultList(List<?> data) {
        return CollectionUtils.isEmpty(data) ? null : data.get(0);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<VendorContractData> getContractSByDateAndStatus(Date startDate, Date endDate, String status) {
        StringBuilder str = new StringBuilder("SELECT DISTINCT vcd FROM VendorContractData vcd" +
                                                " LEFT JOIN FETCH vcd.workOrderDataSet wo " +
                                                " LEFT JOIN FETCH wo.vendorContractItemDataList vcid" +
                                                " LEFT JOIN FETCH vcid.vendorContractItemUnitDataList" +
                                                " WHERE ");
        if(startDate != null) {
            str.append("vcd.startDate =:startDate ");
        }
        else {
            str.append("vcd.endDate =:endDate ");
        }
        str.append("AND vcd.status =:status ORDER BY vcd.contractId DESC");
        Query query = manager.createQuery(str.toString());
        if(startDate != null) {
            query.setParameter("startDate", startDate);
        }
        else {
            query.setParameter("endDate", endDate);
        }
        query.setParameter("status", status);
        return (List<VendorContractData>) query.getResultList();
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<WorkOrderData> getWorkOrdersByDateAndStatus(Date startDate, Date endDate, List<VendorContractStatus> statuses) {
        StringBuilder str = new StringBuilder("SELECT DISTINCT wo FROM WorkOrderData wo " +
                "LEFT JOIN FETCH wo.vendorContractItemDataList vcid " +
                "WHERE ");
        if(startDate != null) {
            str.append("wo.startDate =:startDate ");
        } else {
            str.append("wo.endDate =:endDate ");
        }
        str.append("AND wo.workOrderStatus IN (:statuses) ORDER BY wo.workOrderId DESC");
        Query query = manager.createQuery(str.toString());
        if(startDate != null) {
            query.setParameter("startDate", startDate);
        }
        else {
            query.setParameter("endDate", endDate);
        }
        query.setParameter("statuses", statuses);
        return (List<WorkOrderData>) query.getResultList();
    }

    @Override
    public VendorContractData changeStatusOfAContract(Integer vendorId, String contractIn, String setStatus) {
        VendorContractData data = getByVendorIdAndStatusIn(vendorId, List.of(contractIn));
        if(Objects.nonNull(data)) {
            data.setStatus(setStatus);
            manager.persist(data);
            return data;
        }
        return null;
    }

    @Override
    public VendorContractData getByVendorIdAndStatusNotIn(Integer vendorId, List<String> status) {
        String queryString = "SELECT DISTINCT vcd FROM VendorContractData vcd " +
                "LEFT JOIN FETCH vcd.workOrderDataSet wo " +
                "LEFT JOIN FETCH wo.vendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.vendorContractItemUnitDataList  " +
                "WHERE vcd.vendorId =:vendorId AND vcd.status NOT IN (:status) ORDER BY vcd.contractId DESC";

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId", vendorId).setParameter("status", status).setMaxResults(1);

        List<VendorContractData> data = query.getResultList();
        return (VendorContractData)returnResultList(data);
    }

    @Override
    public WorkOrderData getRecentWorkOrderForVendor(Integer vendorId) {
        String queryString = "SELECT wo FROM VendorContractData vcd " +
                "JOIN vcd.workOrderDataSet wo " +
                "WHERE vcd.vendorId = :vendorId " +
                "ORDER BY wo.workOrderId DESC";

        return manager.createQuery(queryString, WorkOrderData.class)
                .setParameter("vendorId", vendorId)
                .setMaxResults(1)
                .getResultStream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public WorkOrderData findWoByContractId(Integer contractId) {
        String queryString = "SELECT DISTINCT wo FROM WorkOrderData wo " +
                "LEFT JOIN FETCH wo.vendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.vendorContractItemUnitDataList  " +
                "WHERE wo.contractData.contractId =:contractId";

        Query query = manager.createQuery(queryString);
        query.setParameter("contractId", contractId).setMaxResults(1);

        List<WorkOrderData> data = query.getResultList();
        return (WorkOrderData)returnResultList(data);
    }

    @Override
    public VendorContractData findById(Integer contractId) {
        String queryString = "SELECT DISTINCT vcd FROM VendorContractData vcd " +
                "LEFT JOIN FETCH vcd.workOrderDataSet wo " +
                "LEFT JOIN FETCH wo.vendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.vendorContractItemUnitDataList  " +
                "WHERE vcd.contractId =:contractId";

        Query query = manager.createQuery(queryString);
        query.setParameter("contractId", contractId).setMaxResults(1);

        List<VendorContractData> data = query.getResultList();
        return (VendorContractData)returnResultList(data);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<WorkOrderData> getAllInStatusAndApprover(VendorContractStatus status, Integer approverId) {
        StringBuilder queryString = new StringBuilder("SELECT DISTINCT wo FROM WorkOrderData wo " +
                "LEFT JOIN FETCH wo.vendorContractItemDataList vcid " +
                "WHERE wo.workOrderStatus =:status ");

        boolean check = Objects.equals(approverId, SCMServiceConstants.SYSTEM_USER_2) || Objects.equals(approverId, SCMServiceConstants.KAPIL_GOLA_USER_ID);
        if(!check) {
            queryString.append("AND wo.approvalRequestId =:approver ");
        }
        queryString.append("ORDER BY wo.workOrderId DESC");

        Query query = manager.createQuery(queryString.toString());
        query.setParameter("status", status);
        if(!check) {
            query.setParameter("approver", approverId);
        }
        return (List<WorkOrderData>) query.getResultList();
    }

    @Override
    public WorkOrderData findWoById(Integer workOrderId) {
        String queryString = "SELECT DISTINCT wo FROM WorkOrderData wo " +
                "LEFT JOIN FETCH wo.vendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.vendorContractItemUnitDataList  " +
                "WHERE wo.workOrderId =:workOrderId";

        Query query = manager.createQuery(queryString);
        query.setParameter("workOrderId", workOrderId).setMaxResults(1);

        List<WorkOrderData> data = query.getResultList();
        return (WorkOrderData)returnResultList(data);
    }

    @Override
    public VendorContractData findPreviousVendorContractItemsFromEachWO(WorkOrderData woData, boolean excludeContractId) {
        String queryString = getQueryString(excludeContractId);

        Query query = manager.createQuery(queryString);
        query.setParameter("status", List.of(VendorContractStatus.APPLIED.name(), VendorContractStatus.EXPIRED.name()))
                .setParameter("vendorId", woData.getContractData().getVendorId());
        if(excludeContractId) {
            query.setParameter("contractId", woData.getContractData().getContractId());
        }
        query.setMaxResults(1);

        List<VendorContractData> data = query.getResultList();
        return (VendorContractData)returnResultList(data);
    }

    private static String getQueryString(boolean excludeContractId) {
        StringBuilder queryString = new StringBuilder("SELECT DISTINCT vcd FROM VendorContractData vcd " +
                "LEFT JOIN FETCH vcd.workOrderDataSet wo " +
                "LEFT JOIN FETCH wo.vendorContractItemDataList vcid " +
                "LEFT JOIN FETCH vcid.vendorContractItemUnitDataList  " +
                "WHERE vcd.vendorId =:vendorId ");
        if(excludeContractId) {
            queryString.append("AND vcd.contractId <> :contractId ");
        }
        queryString.append("AND vcd.status IN (:status) ORDER BY vcd.contractId DESC");
        return queryString.toString();
    }

}
