package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "PLAN_ORDER_ITEM_PREPARATION")
public class PlanOrderItemPrepData {

	private Integer id;
	private int recipeId;
	private int requestedBy;
	private BigDecimal preparationQuantity;
	private Date requestingTime;
	private PlanOrderItemData planOrderItemData;
	private List<PlanOrderItemPrepItemData> planOrderItemPrepItemData = new ArrayList<>(0);

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ITEM_PREPARATION_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "RECIPE_ID", nullable = false)
	public int getRecipeId() {
		return recipeId;
	}

	public void setRecipeId(int recipeId) {
		this.recipeId = recipeId;
	}

	@Column(name = "REQUESTED_BY", nullable = false)
	public int getRequestedBy() {
		return requestedBy;
	}

	public void setRequestedBy(int requestedBy) {
		this.requestedBy = requestedBy;
	}

	@Column(name = "PREPARATION_QUANTITY", nullable = false)
	public BigDecimal getPreparationQuantity() {
		return preparationQuantity;
	}

	public void setPreparationQuantity(BigDecimal preparationQuantity) {
		this.preparationQuantity = preparationQuantity;
	}

	@Column(name = "REQUESTING_TIME", nullable = false)
	public Date getRequestingTime() {
		return requestingTime;
	}

	public void setRequestingTime(Date requestingTime) {
		this.requestingTime = requestingTime;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PLAN_ITEM_ID", nullable = false)
	public PlanOrderItemData getPlanOrderItemData() {
		return planOrderItemData;
	}

	public void setPlanOrderItemData(PlanOrderItemData planOrderItemData) {
		this.planOrderItemData = planOrderItemData;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "planOrderItemPrepData")
	public List<PlanOrderItemPrepItemData> getPlanOrderItemPrepItemData() {
		return planOrderItemPrepItemData;
	}

	public void setPlanOrderItemPrepItemData(List<PlanOrderItemPrepItemData> planOrderItemPrepItemData) {
		this.planOrderItemPrepItemData = planOrderItemPrepItemData;
	}
}
