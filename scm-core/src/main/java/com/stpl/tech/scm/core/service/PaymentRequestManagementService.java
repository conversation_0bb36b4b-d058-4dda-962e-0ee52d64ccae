package com.stpl.tech.scm.core.service;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.AdvancePaymentStatusLog;
import com.stpl.tech.scm.data.model.CompanyBankMapping;
import com.stpl.tech.scm.domain.model.PrProcessMetaData;
import com.stpl.tech.scm.domain.model.BankVO;
import com.stpl.tech.scm.domain.model.DebitNoteDetail;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.HolidayActivateDeactivate;
import com.stpl.tech.scm.domain.model.HolidayDetails;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PRPaymentDetail;
import com.stpl.tech.scm.domain.model.PaymentCalendar;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.PaymentRequestStatusChangeVO;
import com.stpl.tech.scm.domain.model.PaymentRequestType;
import com.stpl.tech.scm.domain.model.PaymentSheetReturnVO;
import com.stpl.tech.scm.domain.model.PaymentSheetVO;
import com.stpl.tech.scm.domain.model.RejectedPayments;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorEditVO;
import com.stpl.tech.scm.domain.model.VendorPRVO;
import com.stpl.tech.util.EmailGenerationException;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
public interface PaymentRequestManagementService {

    public PaymentRequest createPaymentRequest(PaymentRequest paymentRequest , Integer milkInvoiceId) throws SumoException;

    AdvancePaymentData createLinkedAdvancePayment(AdvancePaymentData advancePaymentData, VendorAdvancePayment vendorAdvancePayment, Integer userId, PaymentRequest paymentRequest, List<AdvancePaymentData> finalAdpList) throws SumoException;

    public PaymentRequest updateInvoicePaymentRequest(PaymentRequest paymentRequest) throws SumoException;

    public List<PaymentRequest> getPaymentRequests(Integer vendorId, Integer unitId, Integer prId, Integer grId,Integer srId, String invoiceNumber,
                                                   Date startDate, Date endDate, String status, Date paymentDate, Integer companyId, String requestType) throws SumoException;

    public PaymentRequest viewPaymentRequest(Integer paymentRequestId) throws SumoException;

    List<PaymentRequestStatusChangeVO> changePaymentRequestStatusInBulk(List<PaymentRequestStatusChangeVO> changeRequestList)
            throws SumoException, EmailGenerationException;

    public PaymentRequestStatusChangeVO changePaymentRequestStatus(PaymentRequestStatusChangeVO changeRequest) throws SumoException, EmailGenerationException;

    public boolean approvePaymentRequest(PaymentRequest paymentRequest) throws SumoException, InventoryUpdateException, ParseException, DataNotFoundException, TransferOrderCreationException, EmailGenerationException;

    public boolean rejectPaymentRequest(PaymentRequest paymentRequest) throws SumoException, EmailGenerationException;

    public DebitNoteDetail addDebitNote(DebitNoteDetail debitNoteDetail) throws SumoException;

    public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
                                         MultipartFile file) throws IOException, DocumentException, SumoException;

    DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String fileName, String baseDir) throws IOException, DocumentException;

    public DocumentDetail uploadDebitNoteDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
                                                  MultipartFile file) throws IOException, DocumentException, SumoException;

    public DocumentDetail getDocument(Integer docId) throws SumoException;

    public List<Pair<String,String>> getMandatoryReqDoc(Integer prId) throws SumoException;

    void markAdjustedSoPoRejected(AdvancePaymentData vendorAdvancePayment, Integer userId, String value, AdvancePaymentData childAdvance) throws SumoException;

    public List<DebitNoteDetail> getDebitNotes(Integer vendorId, Integer unitId, Integer prId, Integer dnId, String invoiceNumber,
                                               Date startDate, Date endDate, Integer companyId, String status) throws SumoException;

    public DebitNoteDetail settleDebitNote(DebitNoteDetail detail) throws SumoException;

    public List<PaymentCalendar> getPaymentDatesForPaymentRequest(Integer paymentRequestId) throws SumoException;

    public PaymentSheetReturnVO getPaymentSheet(PaymentSheetVO paymentSheetVO) throws SumoException;

    public boolean unblockPaymentRequests(PaymentSheetVO paymentSheetVO) throws SumoException;

    public boolean blockPaymentRequests(PaymentSheetVO paymentSheetVO) throws SumoException;

    public boolean settlePaymentRequestSingle(PRPaymentDetail detail) throws SumoException, EmailGenerationException;

    public boolean startPaymentProcessAdhoc(PaymentRequestStatusChangeVO changeRequest) throws SumoException;

    public boolean forceSettlePaymentRequests(PaymentSheetVO request) throws SumoException;

    public List<PaymentRequest> uploadPaymentSheet(String bankName, MultipartFile file) throws IOException, SumoException;

    AdvancePaymentStatusLog logAdvancePaymentStatus(String fromStatus, String toStatus, AdvancePaymentData advancePaymentData, Integer loggedBy) throws SumoException;

    public boolean settlePaymentRequestBulk(List<PaymentRequest> paymentRequests, String selectedCompany, Boolean isForceUploaded, String utrUploadedBy) throws SumoException, EmailGenerationException;

    public boolean blockAllPRsForVendor(VendorEditVO request) throws SumoException;

    public boolean unBlockAllPRsForVendor(VendorEditVO request) throws SumoException;

    public List<PaymentRequest> getCompanyPaymentRequests(Integer companyId, Integer vendorId, Integer unitId, Integer prId,
			Integer grId, String invoiceNumber, Date startDate, Date endDate, String status, Date paymentDate,String type) throws SumoException;

    public List<PaymentRequest> getCompanyPaymentRequestsForProcess(PaymentRequestType type, Integer companyId, Integer vendorId, Integer unitId, Integer prId,
                                                                    String invoiceNumber, Date startDate, Date endDate, String status, String dateType) throws SumoException;

    public CompanyBankMapping getBanksOfComapny(int companyId, String bankCode) throws SumoException;

    public List<BankVO> getBanksOfComapny(int companyId) throws SumoException;

    public VendorPRVO getVendorPRVO(Integer vendorId) throws SumoException;

    void updateVendorBlockCache(AdvancePaymentData advancePaymentData);

    public List<RejectedPayments> getRejectedPrForGr(Integer unitId, Integer vendorId) throws SumoException;

    public boolean updatePrFilingNo(Integer prId, String filingNo) throws SumoException;

    public boolean addHoliday(HolidayDetails holidayDetails);

    public List<HolidayDetails> listOfHolidays(String holidayType, Integer year);

    public boolean activateDeActivateHoliday(HolidayActivateDeactivate request);

    public MultipartFile compressPdf(File destFile , MultipartFile file , MultipartFile compressedFile) throws IOException, DocumentException, SumoException;

    public byte[] compressImage(MultipartFile image , String mimeType) throws IOException;

    public VendorAdvancePayment getVendorAdvancePayment(VendorAdvancePayment vendorAdvanceRequest, Boolean isView, AdvancePaymentData advancePaymentData) throws SumoException;

    public PaymentRequest createVendorAdvancePayment(VendorAdvancePayment vendorAdvancePayment, Integer loggedInUnit) throws SumoException;

    Boolean validatePoSoStatus(VendorAdvancePayment vendorAdvanceRequest, Boolean checkAdjusted);

    Date getAdvanceSettlementTime(String type);

    public List<VendorAdvancePayment> getAllVendorAdvances(Date date, Date date1, Integer vendorId, String status, Integer advancePaymentId);

    public Boolean submitAdvanceRefundedDate(Integer advancePaymentId, String refundedDate, Integer receivedBy, Integer refundDocId) throws SumoException;

    public void refreshVendorAdvancePaymentsCache(Integer vendorId);

    void sendVendorAdvancesReminder();

    Boolean validatePrForQuery(Integer prId) throws SumoException;

    DocumentDetail uploadQueryDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException;

    void rejectQueriedPrs();

    DocumentDetail uploadAdvanceRefund(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName, Integer advancePaymentId) throws DocumentException, IOException;

    List<String> getEmployeePaymentCards(Integer userId);

    DocumentDetail uploadCardPaymentProof(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException;

    DocumentDetail uploadSoBreachApproval(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException;
}
