package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "EXPIRY_DATE_CORRECTION_DATA")
public class ExpiryDateCorrectionData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EXPIRY_DATE_CORRECTION_ID", nullable = false, unique = true)
    private Integer expiryDateCorrectionId;

    @Column(name = "EXPIRY_DATE_CORRECTION_TYPE", nullable = false)
    private String expiryDateCorrectionType;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "TRANSACTION_ID", nullable = false)
    private Integer transactionId;

    @Column(name = "KEY_ID", nullable = false)
    private Integer keyId;

    @Column(name = "KEY_TYPE", nullable = false)
    private String keyType;

    @Column(name = "ORIGINAL_EXPIRY_DATE")
    private Date originalExpiryDate;

    @Column(name = "UPDATED_EXPIRY_DATE")
    private Date updatedExpiryDate;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "CREATED_AT")
    private Date createdAt;
}
