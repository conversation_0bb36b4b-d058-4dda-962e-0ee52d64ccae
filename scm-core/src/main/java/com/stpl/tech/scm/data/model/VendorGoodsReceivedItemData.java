package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 12-05-2017.
 */
@Entity
@Table(name = "VENDOR_GR_ITEM_DETAIL")
public class VendorGoodsReceivedItemData {

    private Integer itemId;
    private Integer skuId;
    private String skuName;
    private String hsn;
    private Integer packagingId;
    private BigDecimal receivedQty;
    private BigDecimal packagingQuantity;
    private BigDecimal conversionRatio;
    private String unitOfMeasure;
    private BigDecimal totalAmount;
    private BigDecimal totalTax;
    private BigDecimal totalPrice;
    private BigDecimal unitPrice;
    private List<ItemTaxDetailData> taxes;
    private List<VendorGrPoItemMappingData> poItemList;
    private VendorGoodsReceivedData goodsReceivedData;
    private String packagingName;
    private Date expiryDate;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GOODS_RECEIVED_ITEM_ID", unique = true, nullable = false)
    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    @Column(name = "SKU_ID", nullable = false)
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "SKU_NAME", nullable = false)
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "PACKAGING_ID", nullable = false)
    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name = "PACKAGING_QUANTITY", nullable = false)
    public BigDecimal getPackagingQuantity() {
        return packagingQuantity;
    }

    public void setPackagingQuantity(BigDecimal packagingQuantity) {
        this.packagingQuantity = packagingQuantity;
    }

    @Column(name = "CONVERSION_RATIO", nullable = false)
    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = false)
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VENDOR_GR_ID")
    public VendorGoodsReceivedData getGoodsReceivedData() {
        return goodsReceivedData;
    }

    public void setGoodsReceivedData(VendorGoodsReceivedData goodsReceivedData) {
        this.goodsReceivedData = goodsReceivedData;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = false)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Column(name = "TOTAL_TAX", nullable = false)
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    @Column(name = "TOTAL_PRICE", nullable = false)
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "grItem")
    public List<VendorGrPoItemMappingData> getPoItemList() {
        return poItemList;
    }

    public void setPoItemList(List<VendorGrPoItemMappingData> poItemList) {
        this.poItemList = poItemList;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "grItem")
    public List<ItemTaxDetailData> getTaxes() {
        return taxes;
    }

    public void setTaxes(List<ItemTaxDetailData> taxes) {
        this.taxes = taxes;
    }

    @Column(name = "UNIT_PRICE", nullable = false)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Column(name = "PACKAGING_NAME", nullable = false)
    public String getPackagingName() {
        return packagingName;
    }

    public void setPackagingName(String packagingName) {
        this.packagingName = packagingName;
    }

    @Column(name = "RECEIVED_QUANTITY", nullable = false)
    public BigDecimal getReceivedQty() {
        return receivedQty;
    }

    public void setReceivedQty(BigDecimal receivedQty) {
        this.receivedQty = receivedQty;
    }

    @Column(name = "HSN_CODE", nullable = false)
    public String getHsn() {
        return hsn;
    }

    public void setHsn(String hsn) {
        this.hsn = hsn;
    }
    
    @Column(name = "EXPIRY_DATE", nullable = true)
	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}
    
    
}
