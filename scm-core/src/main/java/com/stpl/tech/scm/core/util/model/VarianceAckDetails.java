package com.stpl.tech.scm.core.util.model;


import com.stpl.tech.scm.reports.modal.VarianceModal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VarianceAckDetails {

    private List<VarianceModal> varianceModalList;
    private String businessDate;
    private String comment;
    private String acknowledgedBy;
}
