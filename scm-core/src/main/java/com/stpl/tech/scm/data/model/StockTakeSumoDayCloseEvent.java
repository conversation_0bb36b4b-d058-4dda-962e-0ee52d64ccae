/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "STOCK_TAKE_SUMO_DAY_CLOSE_EVENT")
public class StockTakeSumoDayCloseEvent {
    private Integer stockTakeSumoDayCloseEventId;
    private Integer unitId;
    private SCMDayCloseEventData dayCloseEvent;

    private SCMDayCloseEventData sumoDayCloseEvent;

    private String stockTakeType;
    private String eventStatus;
    private Integer eventCreatedBy;
    private Date eventCreatedAt;
    private Date eventSubmittedAt;

    private Date lastUpdatedTime;
    private String deviceInfo;

    private List<StockTakeSumoDayCloseProducts> stockTakeSumoDayCloseProducts = new ArrayList<>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID", unique = true, nullable = false)
    public Integer getStockTakeSumoDayCloseEventId() {
        return this.stockTakeSumoDayCloseEventId;
    }

    public void setStockTakeSumoDayCloseEventId(Integer stockTakeSumoDayCloseEventId) {
        this.stockTakeSumoDayCloseEventId = stockTakeSumoDayCloseEventId;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "KETTLE_DAY_CLOSE_EVENT", referencedColumnName = "EVENT_ID")
    public SCMDayCloseEventData getDayCloseEvent() {
        return dayCloseEvent;
    }

    public void setDayCloseEvent(SCMDayCloseEventData dayCloseEvent) {
        this.dayCloseEvent = dayCloseEvent;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SUMO_DAY_CLOSE_EVENT", referencedColumnName = "EVENT_ID")
    public SCMDayCloseEventData getSumoDayCloseEvent() {
        return sumoDayCloseEvent;
    }

    public void setSumoDayCloseEvent(SCMDayCloseEventData sumoDayCloseEvent) {
        this.sumoDayCloseEvent = sumoDayCloseEvent;
    }

    @Column(name = "STOCK_TAKE_TYPE")
    public String getStockTakeType() {
        return stockTakeType;
    }

    public void setStockTakeType(String stockTakeType) {
        this.stockTakeType = stockTakeType;
    }

    @Column(name = "EVENT_STATUS")
    public String getEventStatus() {
        return this.eventStatus;
    }

    public void setEventStatus(String eventStatus) {
        this.eventStatus = eventStatus;
    }

    @Column(name = "EVENT_CREATED_BY")
    public Integer getEventCreatedBy() {
        return this.eventCreatedBy;
    }

    public void setEventCreatedBy(Integer eventCreatedBy) {
        this.eventCreatedBy = eventCreatedBy;
    }

    @Column(name = "EVENT_CREATED_AT")
    public Date getEventCreatedAt() {
        return eventCreatedAt;
    }

    public void setEventCreatedAt(Date eventCreatedAt) {
        this.eventCreatedAt = eventCreatedAt;
    }

    @Column(name = "EVENT_SUBMITTED_AT")
    public Date getEventSubmittedAt() {
        return eventSubmittedAt;
    }

    public void setEventSubmittedAt(Date eventSubmittedAt) {
        this.eventSubmittedAt = eventSubmittedAt;
    }

    @Column(name = "LAST_UPDATED_TIME")
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    @Column(name = "DEVICE_INFO")
    public String getDeviceInfo() {
        return this.deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "stockTakeSumoDayCloseEventId")
    public List<StockTakeSumoDayCloseProducts> getStockTakeSumoDayCloseProducts() {
        return stockTakeSumoDayCloseProducts;
    }

    public void setStockTakeSumoDayCloseProducts(List<StockTakeSumoDayCloseProducts> stockTakeSumoDayCloseProducts) {
        this.stockTakeSumoDayCloseProducts = stockTakeSumoDayCloseProducts;
    }
}
