package com.stpl.tech.scm.notification.email.template;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.NumberToWord;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorPOEmailNotificationTemplate extends AbstractVelocityTemplate {

	private PurchaseOrderData purchaseOrderData;
	private Unit deliveryUnit;
	private VendorDetail vendorDetail;
	private VendorDispatchLocation dispatchLocation;
	private String createdBy;
	private String createdByEmail;
	private boolean hasOtherTaxes;
	private boolean hasIgst;
	private String basePath;
	private Company company;
	private EnvType envType;
	private String poType;
	private String approvedBy;
	private EmployeeBasicDetail employeeBasicDetail;
	private String lastUpdatedBy;
	private String updatedStatus;

	public VendorPOEmailNotificationTemplate() {

	}

	public VendorPOEmailNotificationTemplate(PurchaseOrderData purchaseOrderData, Unit deliveryUnit,
			VendorDetail vendorDetail, VendorDispatchLocation dispatchLocation, String basePath, String createdBy,
			Company company, EnvType envType, String poType, String approvedBy,EmployeeBasicDetail employeeBasicDetail,String lastUpdatedBy,String updatedStatus) {
		this.purchaseOrderData = purchaseOrderData;
		this.deliveryUnit = deliveryUnit;
		this.vendorDetail = vendorDetail;
		this.dispatchLocation = dispatchLocation;
		this.basePath = basePath;
		this.hasIgst = !deliveryUnit.getLocation().getState().getCode()
				.equals(dispatchLocation.getAddress().getStateCode());
		this.hasOtherTaxes = purchaseOrderData.getPurchaseOrderItemDatas().stream()
				.filter(item -> item.getOtherTaxes() != null).mapToDouble(value -> value.getOtherTaxes().doubleValue())
				.sum() > 0.0d;
		this.createdBy = createdBy;
		this.company = company;
		this.envType = envType;
		this.poType = poType;
		this.approvedBy = approvedBy;
		this.employeeBasicDetail = employeeBasicDetail;
		this.lastUpdatedBy = lastUpdatedBy;
		this.updatedStatus = updatedStatus;
	}

	@Override
	public String getTemplatePath() {
		return "templates/VendorPOEmailTemplate.html";
	}

	@Override
	public String getFilepath() {
		return basePath + "/vendor/purchaseOrders/" + vendorDetail.getVendorId() + "/"
				+ purchaseOrderData.getReceiptNumber() + ".html";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("vendorDetail", vendorDetail);
		stringObjectMap.put("dispatchLocation", dispatchLocation);
		stringObjectMap.put("purchaseOrderData", purchaseOrderData);
		stringObjectMap.put("hasIgst", hasIgst);
		stringObjectMap.put("hasOtherTaxes", hasOtherTaxes);
		stringObjectMap.put("deliveryUnit", deliveryUnit);
		stringObjectMap.put("dateTool", new DateTool());
		stringObjectMap.put("mathTool", new MathTool());
		stringObjectMap.put("createdBy", this.createdBy);
		stringObjectMap.put("approvedBy", this.approvedBy);
		stringObjectMap.put("amountInWords",
				NumberToWord.getInstance().convertNumberToWords(purchaseOrderData.getPaidAmount().intValue()));
		stringObjectMap.put("company", this.company);
		String link = SCMUtil.isDev(envType) ? "http://dev.kettle.chaayos.com:9595" : "http://orient.chaayos.com:9797";
		link = link + "/scm-service/disclaimer.html";
		stringObjectMap.put("disclaimerLink", link);
		stringObjectMap.put("potype", poType);
		stringObjectMap.put("employeeBasicDetail", employeeBasicDetail);
		stringObjectMap.put("lastUpdatedBy", lastUpdatedBy);
		stringObjectMap.put("updatedStatus", updatedStatus);
		return stringObjectMap;
	}

	public VendorDetail getVendorDetail() {
		return vendorDetail;
	}

	public PurchaseOrderData getPurchaseOrderData() {
		return purchaseOrderData;
	}

	public Unit getDeliveryUnit() {
		return deliveryUnit;
	}

	public VendorDispatchLocation getDispatchLocation() {
		return dispatchLocation;
	}

	public String getName() {
		return this.purchaseOrderData.getReceiptNumber();
	}

}
