/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.mongo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VendorComplianceResponseData {
    @SerializedName("ctb")
    private String constitutionOfBusiness;

    @SerializedName("ctj")
    private String centreJurisdiction;

    @SerializedName("ctjCd")
    private String centreJurisdictionCode;

    @SerializedName("cxdt")
    private String dateOfCancellation;

    @SerializedName("dty")
    private String taxpayerType;

    @SerializedName("einvoiceStatus")
    private String einvoiceStatus;

    @SerializedName("gstin")
    private String gstin;

    @SerializedName("lgnm")
    private String legalNameOfBusiness;

    @SerializedName("lstupddt")
    private String lastUpdatedDate;

    @SerializedName("nba")
    private List<String> natureOfBusinessActivity;

    @SerializedName("rgdt")
    private String dateOfRegistration;

    @SerializedName("stj")
    private String stateJurisdiction;

    @SerializedName("stjCd")
    private String stateJurisdictionCode;

    @SerializedName("sts")
    private String gstinStatus;

    @SerializedName("tradeNam")
    private String tradeName;

    @SerializedName("adadr")
    private List<BusinessAddress> additionalPlaceOfBusiness;

    @SerializedName("pradr")
    private BusinessAddress principalPlaceOfBusiness;

    @SerializedName("error_code")
    private String errorCode;

    @SerializedName("message")
    private String message;

    @SerializedName("EFiledlist")
    private List<EFiledList> eFiledlist;

    @SerializedName("pan")
    private String pan;

    @SerializedName("name")
    private String name;

    @SerializedName("pan_allotment_date")
    private Long panAllotmentDate;

    @SerializedName("financial_year")
    private String financialYear;

    @SerializedName("specified_person_us_206ab_&_206cca")
    private String specifiedPerson206ABAnd206CCA;

    @SerializedName("pan_status")
    private String panStatus;

    @SerializedName("note")
    private String note;

    @SerializedName("@entity")
    private String entity;
}
