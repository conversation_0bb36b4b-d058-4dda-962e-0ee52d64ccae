package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "DAY_CLOSE_COST_DETAIL_DATA_CAFE_DUMP")
public class DayCloseCostDetailDataCafeDump {

    private Integer cafeDumpDetailId;
    private Integer costDetailDataId;
    private Integer productId;
    private Integer unitId;
    private BigDecimal quantity;
    private BigDecimal price;
    private String uom;
    private String latest;
    private Date lastUpdatedTime;
    private Date expiryDate;
    private String creationReason;
    private Integer creationItemId;
    private Integer dayCloseEventId;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CAFE_DUMP_DETAIL_ID", unique = true, nullable = false)
    public Integer getCafeDumpDetailId() {
        return cafeDumpDetailId;
    }

    public void setCafeDumpDetailId(Integer cafeDumpDetailId) {
        this.cafeDumpDetailId = cafeDumpDetailId;
    }

    @Column(name = "COST_DETAIL_DATA_ID", nullable = false)
    public Integer getCostDetailDataId() {
        return costDetailDataId;
    }

    public void setCostDetailDataId(Integer costDetailDataId) {
        this.costDetailDataId = costDetailDataId;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "QUANTITY", precision = 16)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "PRICE", precision = 16)
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = true)
    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    @Column(name = "IS_LATEST", nullable = true)
    public String getLatest() {
        return latest;
    }

    public void setLatest(String latest) {
        this.latest = latest;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    @Column(name = "EXPIRY_DATE", nullable = true)
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Column(name = "CREATION_REASON", nullable = true)
    public String getCreationReason() {
        return creationReason;
    }

    public void setCreationReason(String creationReason) {
        this.creationReason = creationReason;
    }

    @Column(name = "CREATION_ITEM_ID", nullable = true)
    public Integer getCreationItemId() {
        return creationItemId;
    }

    public void setCreationItemId(Integer creationItemId) {
        this.creationItemId = creationItemId;
    }

    @Column(name = "DAY_CLOSE_EVENT_ID", nullable = false)
    public Integer getDayCloseEventId() {
        return dayCloseEventId;
    }

    public void setDayCloseEventId(Integer dayCloseEventId) {
        this.dayCloseEventId = dayCloseEventId;
    }
}
