package com.stpl.tech.scm.core.service.impl;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.UnitClosureState;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.ValidateStateOutput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.AttributeManagementService;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.AttributeManagementDao;
import com.stpl.tech.scm.data.model.AttributeDefinitionData;
import com.stpl.tech.scm.data.model.AttributeValueData;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeType;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.SwitchStatus;

/**
 * Created by Rahul Singh on 05-05-2016.
 */

@Service
public class AttributeManagementServiceImpl implements AttributeManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(AttributeManagementServiceImpl.class);

    @Autowired
    private AttributeManagementDao attributeManagementDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<AttributeType, List<AttributeDefinition>> viewAllAttributeDefinitions() {
        Map<AttributeType, List<AttributeDefinition>> attributeTypeListMap = new TreeMap<AttributeType, List<AttributeDefinition>>();
        for (AttributeDefinition ad : scmCache.getAttributeDefinitions().values()) {
            List<AttributeDefinition> attributeDefinitionList = attributeTypeListMap.get(ad.getAttributeType());
            if (attributeDefinitionList == null) {
                attributeDefinitionList = new ArrayList<AttributeDefinition>();
            }
            if (!attributeDefinitionList.contains(ad)) {
                attributeDefinitionList.add(ad);
            }
            attributeTypeListMap.put(ad.getAttributeType(), attributeDefinitionList);
        }
        return attributeTypeListMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<AttributeValue>> viewAllAttributeValues() {
        Map<Integer, List<AttributeValue>> attributeDefinitionListMap = new TreeMap<Integer, List<AttributeValue>>();
        for (AttributeValue av : scmCache.getAttributeValues().values()) {
            List<AttributeValue> attributeValues = attributeDefinitionListMap.get(av.getAttributeDefinitionId());
            if (attributeValues == null) {
                attributeValues = new ArrayList<AttributeValue>();
            }
            if (!attributeValues.contains(av)) {
                attributeValues.add(av);
            }
            attributeDefinitionListMap.put(av.getAttributeDefinitionId(), attributeValues);
        }
        return attributeDefinitionListMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<AttributeValueData> addNewAttributeValue(List<AttributeValue> attributeValues) throws SumoException {
        boolean flag = false;
        List<AttributeValueData> attributeValueDataList = new ArrayList<>();
        for (AttributeValue attributeValue : attributeValues) {
            AttributeValueData attributeValueData = new AttributeValueData();
            attributeValueData.setAttributeDefinition(attributeManagementDao.find(AttributeDefinitionData.class, attributeValue.getAttributeDefinitionId()));
            attributeValueData.setAttributeValueId(attributeValue.getAttributeValueId());
            attributeValueData.setAttributeValue(attributeValue.getAttributeValue());
            attributeValueData.setAttributeValueStatus(attributeValue.getAttributeValueStatus().value());
            attributeValueData.setAttributeValueShortCode(attributeValue.getAttributeValueShortCode());
            AttributeValueData data = attributeManagementDao.add(attributeValueData,false);
            attributeValueDataList.add(data);
            flag = updateAttributeValueCache(data);
        }
        attributeManagementDao.flush();
        return attributeValueDataList;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateAttributeValue(AttributeValue attributeValue) {
        AttributeValueData attributeValueData = attributeManagementDao.find(AttributeValueData.class, attributeValue.getAttributeValueId());
        if (attributeValueData != null) {
            attributeValueData.setAttributeDefinition(attributeManagementDao.find(AttributeDefinitionData.class, attributeValue.getAttributeDefinitionId()));
            attributeValueData.setAttributeValueId(attributeValue.getAttributeValueId());
            attributeValueData.setAttributeValue(attributeValue.getAttributeValue());
            attributeValueData.setAttributeValueStatus(attributeValue.getAttributeValueStatus().value());
            attributeValueData.setAttributeValueShortCode(attributeValue.getAttributeValueShortCode());
            return updateAttributeValueCache(attributeManagementDao.update(attributeValueData,false));
        }
        attributeManagementDao.flush();
        LOG.info("Attribute Value productId: {} not found!", attributeValue.getAttributeValueId());
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<AttributeValue>> getAllAttributeValuesByAttribute(int categoryId, boolean onlyActive) {
        Map<Integer, List<AttributeValue>> attributeToAttributeValueMap = new TreeMap<Integer, List<AttributeValue>>();
        for (CategoryAttributeValue cav : scmCache.getCategoryAttributeValues().values()) {
            CategoryAttributeMapping cam = scmCache.getCategoryAttributeMappings().get(cav.getCategoryAttributeMappingId());
            if (cam.getCategoryDefinition().getId() == categoryId) {
                List<AttributeValue> attributeValues = attributeToAttributeValueMap.get(cam.getAttributeDefinition().getId());
                if (attributeValues == null) {
                    attributeValues = new ArrayList<AttributeValue>();
                }
                if (!attributeValues.contains(scmCache.getAttributeValues().get(cav.getAttributeValue().getId()))) {
                    if (onlyActive) {
                        AttributeValue av = scmCache.getAttributeValues().get(cav.getAttributeValue().getId());
                        if (av.getAttributeValueStatus().equals(SwitchStatus.ACTIVE)) {
                            attributeValues.add(av);
                        }
                    } else {
                        attributeValues.add(scmCache.getAttributeValues().get(cav.getAttributeValue().getId()));
                    }
                    attributeToAttributeValueMap.put(cam.getAttributeDefinition().getId(), attributeValues);
                }
            }
        }
        return attributeToAttributeValueMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AttributeDefinition addAttribute(AttributeDefinition attributeDefinition) throws SumoException {
        AttributeDefinitionData attributeDefinitionData = SCMDataConverter.convert(attributeDefinition);
        attributeDefinitionData = attributeManagementDao.add(attributeDefinitionData, true);
        attributeDefinition = SCMDataConverter.convert(attributeDefinitionData);
        scmCache.getAttributeDefinitions().put(attributeDefinition.getAttributeId(), attributeDefinition);
        return attributeDefinition;
    }

    @Override
    public AttributeDefinition updateAttribute(AttributeDefinition attributeDefinition) throws SumoException {
        AttributeDefinitionData attributeDefinitionData = attributeManagementDao.find(AttributeDefinitionData.class, attributeDefinition.getAttributeId());
        if(attributeDefinitionData != null) {
            attributeDefinitionData = SCMDataConverter.convert(attributeDefinition);
            attributeDefinitionData = attributeManagementDao.update(attributeDefinitionData, true);
            attributeDefinition = SCMDataConverter.convert(attributeDefinitionData);
            scmCache.getAttributeDefinitions().put(attributeDefinition.getAttributeId(), attributeDefinition);
            return attributeDefinition;
        } else {
            throw new SumoException("Attribute updation failure", "Error while updating attribute.");
        }
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ValidateStateOutput> validateState(Long stateId, Integer unitId) {
        UnitClosureState unitClosureState = masterDataCache.getUnitClosureState(stateId);
        UnitCategory unitCategory = masterDataCache.getUnitBasicDetail(unitId).getCategory();
        String [] params = unitClosureState.getParameter().split("\\.");
        String className =  params[params.length-1];
        List<ValidateStateOutput>  outputs = new ArrayList<>();
        if (unitCategory==UnitCategory.CAFE && className.equalsIgnoreCase("CostDetailDataWh")){
            outputs.add(ValidateStateOutput.builder().isCheckPassed(true).build());
            return outputs;
        }
        if ((unitCategory == UnitCategory.WAREHOUSE || unitCategory == UnitCategory.KITCHEN)  && className.equalsIgnoreCase("CostDetailDataCafe")){
            outputs.add(ValidateStateOutput.builder().isCheckPassed(true).build());
            return outputs;
        }

        List response = attributeManagementDao.validateState(unitClosureState.getQuery(),unitId);

        if(className.equalsIgnoreCase("StockEventDefinitionData") && response.size()>=1){
            outputs.add(ValidateStateOutput.builder().isCheckPassed(true).build());
            return outputs;
        }
        if(className.equalsIgnoreCase("StockEventDefinitionData") && response.size()==0){
            outputs.add(ValidateStateOutput.builder().isCheckPassed(false).build());
            return outputs;
        }
        if((className.equalsIgnoreCase("CostDetailDataWh") || className.equalsIgnoreCase("CostDetailDataCafe"))  && response.size()>=1){
            outputs.add(ValidateStateOutput.builder().isCheckPassed(false).build());
            return outputs;
        }
        
        if(response.size() == 0) {
            outputs.add(ValidateStateOutput.builder().isCheckPassed(true).build());
          return outputs;
        }
        response.stream().forEach(e->{
            Object res = null;
            try {
                res = castObjectToClassName(e, unitClosureState.getParameter());
                outputs.add(convertToValidateStateOutput(res,className,unitClosureState));
            } catch (NoSuchFieldException | IllegalAccessException | ClassNotFoundException ex) {
                throw new RuntimeException(ex);
            }
        });
       return outputs;
    }

    private Object castObjectToClassName(Object obj,String className) throws ClassNotFoundException {
        Class<?> clazz = Class.forName(className);
        return clazz.cast(obj);
    }

    private ValidateStateOutput convertToValidateStateOutput(Object obj,String className,UnitClosureState unitClosureState) throws NoSuchFieldException, IllegalAccessException {
        if(className.equalsIgnoreCase("TransferOrderData")) return SCMDataConverter.toConverter(obj,unitClosureState);
        else if(className.equalsIgnoreCase("RequestOrderData")) return SCMDataConverter.roConverter(obj,unitClosureState);
        else if(className.equalsIgnoreCase("PurchaseOrderData")) return SCMDataConverter.poConverter(obj,unitClosureState);
        else if(className.equalsIgnoreCase("GoodsReceivedData")) return SCMDataConverter.grConverter(obj,unitClosureState);
        else if(className.equalsIgnoreCase("VendorGoodsReceivedData")) return SCMDataConverter.vendorGrConverter(obj,unitClosureState);
        else if(className.equalsIgnoreCase("PaymentRequestData")) return SCMDataConverter.pendingPrConverter(obj,unitClosureState);
        else if(className.equalsIgnoreCase("AssetDefinitionData")) return SCMDataConverter.acitveAssetConverter(obj,unitClosureState);
        else if(className.equalsIgnoreCase("ServiceOrderData")) return SCMDataConverter.soConverter(obj,unitClosureState);
        else return null;
    }

    private boolean updateAttributeValueCache(AttributeValueData attributeValueData) {
        if (attributeValueData != null) {
            scmCache.getAttributeValues().put(attributeValueData.getAttributeValueId(), SCMDataConverter.convert(attributeValueData));
            return true;
        }
        return false;
    }
}
