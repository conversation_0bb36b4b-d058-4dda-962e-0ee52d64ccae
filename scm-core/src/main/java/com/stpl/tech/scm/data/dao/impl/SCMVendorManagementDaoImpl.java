package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.data.dao.SCMVendorManagementDao;
import com.stpl.tech.scm.data.model.AgreementDetail;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.data.model.VendorCompanyDetailData;
import com.stpl.tech.scm.data.model.VendorComplianceData;
import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorLogData;
import com.stpl.tech.scm.data.model.VendorRegistrationRequestDetail;
import com.stpl.tech.scm.domain.model.ChangeType;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SkuPriceDataObject;
import com.stpl.tech.scm.domain.model.SkuPriceHistoryObject;
import com.stpl.tech.scm.domain.model.VendorComplianceDataDTO;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
@Service
public class SCMVendorManagementDaoImpl extends SCMAbstractDaoImpl implements SCMVendorManagementDao {

	private static final Logger LOG = LoggerFactory.getLogger(SCMVendorManagementDaoImpl.class);


	@Override
	public List<UnitVendorMappingData> getUnitVendorMappingByUnitId(int unitId) {
		Query query = manager.createQuery("FROM UnitVendorMappingData u WHERE u.unitId = :unitId");
		query.setParameter("unitId", unitId);
		return query.getResultList();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SCMVendorManagementDao#getAllVendor(java.lang.
	 * String)
	 */
	@Override
	public List<VendorDetailData> getAllVendor(String requestStatus) {
		Query query = manager.createQuery("FROM VendorDetailData u WHERE u.status = :status");
		query.setParameter("status", requestStatus);
		return query.getResultList();

	}

	@Override
	public List<VendorDetailData> getAllVendor() {
		Query query = manager.createQuery("FROM VendorDetailData u WHERE u.status in (:statusList)");
		List<String> statusList = Arrays.asList(VendorStatus.ACTIVE, VendorStatus.COMPLETED,
						VendorStatus.ACTIVE, VendorStatus.IN_ACTIVE, VendorStatus.IN_PROCESS)
				.stream().map(VendorStatus::name).collect(Collectors.toList());
		query.setParameter("statusList", statusList);
		return query.getResultList();
	}

	@Override
	public List<VendorRegistrationRequestDetail> findCompletedRegistrationRequests(Integer vendorId) {
		Query query = manager.createQuery("FROM VendorRegistrationRequestDetail" +
				" WHERE vendorId = :vendorId and requestStatus = :status");
		query.setParameter("vendorId", vendorId);
		query.setParameter("status", VendorStatus.COMPLETED.name());
		return query.getResultList();
	}

	@Override
	public Boolean checkIfVendorApprovedAtleastOnce(Integer vendorId) {
		Query query = manager.createQuery("FROM VendorLogData" +
				" WHERE vendorId = :vendorId and status = :status");
		query.setParameter("vendorId", vendorId).setParameter("status", VendorStatus.ACTIVE.value());
		List<VendorLogData> logDataList = query.getResultList();
		if (Objects.nonNull(logDataList) && logDataList.size() > 0) {
			return true;
		}
		return false;
	}

	@Override
	public List<AgreementDetail> getAllActiveAgreements(String type) {
		List<AgreementDetail> allActiveAgreements = new ArrayList<>();
		try {
			Query query = manager.createQuery("FROM AgreementDetail" +
					" WHERE type = :type AND status = :status ");
			query.setParameter("type", type).setParameter("status", AppConstants.ACTIVE);
			allActiveAgreements = query.getResultList();
		} catch (Exception e) {
			LOG.error("Error While Fetching Disclaimer Details For Type ::::::::: {} :::::{} ", type, e);
		}
		return allActiveAgreements;

	}

	@Override
	public List<Pair<Integer, Integer>> getskuPackagingId(SkuPriceDataObject reqObj) {
		StringBuilder queryString = new StringBuilder("SELECT s.skuPriceKeyId, s.packagingId " +
				"FROM SkuPriceData s");
		queryString.append(" WHERE s.vendorId = :vendorId");
		queryString.append(" AND s.skuId = :skuId");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("vendorId", reqObj.getVendorId());
		query.setParameter("skuId", reqObj.getSkuId());
		List<Object[]> result = query.getResultList();
		List<Pair<Integer, Integer>> convertedResult = new ArrayList<>();
		for (Object[] o : result) {
			convertedResult.add(new Pair(o[0], o[1]));
		}
		return convertedResult;
	}

	@Override
	public List<Integer> getSkuPriceDataId(SkuPriceDataObject reqObj) {
		StringBuilder queryString = new StringBuilder("SELECT s.skuPriceKeyId " +
				"FROM SkuPriceData s");
		queryString.append(" WHERE s.vendorId = :vendorId");
		queryString.append(" AND s.skuId = :skuId");
		if (!Objects.equals(reqObj.getPackagingId(), -1)) {
			queryString.append(" AND s.packagingId = :skuPackagingId");
		}
		if (!Objects.equals(reqObj.getDispatchLocation(), "")) {
			queryString.append(" AND s.dispatchLocation = :dispatchLocation");
		}
		if (!Objects.equals(reqObj.getDeliveryLocation(), "")) {
			queryString.append(" AND s.deliveryLocation = :deliveryLocation");
		}
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("vendorId", reqObj.getVendorId());
		query.setParameter("skuId", reqObj.getSkuId());
		if (!Objects.equals(reqObj.getPackagingId(), -1)) {
			query.setParameter("skuPackagingId", reqObj.getPackagingId());
		}
		if (!Objects.equals(reqObj.getDispatchLocation(), "")) {
			query.setParameter("dispatchLocation", reqObj.getDispatchLocation());
		}
		if (!Objects.equals(reqObj.getDeliveryLocation(), "")) {
			query.setParameter("deliveryLocation", reqObj.getDeliveryLocation());
		}

		return query.getResultList();
	}

	@Override
	public List<SkuPriceHistoryObject> getPriceHistory(List<Integer> skuPriceDataId) {
		List<SkuPriceHistoryObject> skuPriceHistory = new ArrayList<>();
		StringBuilder queryString = new StringBuilder("SELECT s.skuPriceDataId, s.negotiatedPrice, s.changeType, s.createdBy,  s.createdAt " +
				"FROM SkuPriceHistory s");
		queryString.append(" WHERE s.skuPriceDataId IN :skuPriceDataId AND s.changeType = :changeType ");
		queryString.append(" ORDER BY s.createdAt ASC");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("skuPriceDataId", skuPriceDataId);
		query.setParameter("changeType", ChangeType.PRICE_UPDATE.name());
		List<Object[]> result = query.getResultList();
		for (Object[] o : result) {
			skuPriceHistory.add(new SkuPriceHistoryObject((Integer) o[0], null, (BigDecimal) o[1], (String) o[2], (String) o[3], AppUtils.getDateString(AppUtils.covertDateIST(((Timestamp) o[4]).getTime()), "dd MMM yyyy HH:mm:ss")));
		}

		return skuPriceHistory;
	}

	@Override
	public List<PurchaseOrderData> getPendingPos(Integer vendorId) {
		Query query = manager.createQuery("FROM PurchaseOrderData a WHERE a.generatedForVendor =: vendorId AND a.status IN(:statusList)");
		query.setParameter("vendorId", vendorId);
		query.setParameter("statusList", Arrays.asList(PurchaseOrderStatus.APPROVED.value(), PurchaseOrderStatus.IN_PROGRESS.value()));
		return query.getResultList();
	}

	@Override
	public List<ServiceOrderData> getPendingSos(Integer vendorId) {
		Query query = manager.createQuery("FROM ServiceOrderData a WHERE a.vendorId =: vendorId AND a.status IN(:statusList)");
		query.setParameter("vendorId", vendorId);
		query.setParameter("statusList", Arrays.asList(PurchaseOrderStatus.APPROVED.value(), PurchaseOrderStatus.IN_PROGRESS.value()));
		return query.getResultList();
	}

	@Override
	public List<VendorComplianceData> findVendorCompliancesByComplianceType(List<String> complianceType, Set<String> complianceKeys) {
		StringBuilder queryString = new StringBuilder("FROM VendorComplianceData a WHERE a.year =:year and a.month =:month");
		if (Objects.nonNull(complianceType)) {
			queryString.append(" and a.complianceType IN(:complianceType)");
		}
		if (Objects.nonNull(complianceKeys)) {
			queryString.append(" and a.complianceKey IN(:complianceKeys)");
		}
		Query query = manager.createQuery(queryString.toString());
		if (Objects.nonNull(complianceType)) {
			query.setParameter("complianceType", complianceType);
		}
		query.setParameter("year", AppUtils.getYear(AppUtils.getCurrentTimestamp()));
		query.setParameter("month", AppUtils.getMonth(AppUtils.getCurrentTimestamp()));
		if (Objects.nonNull(complianceKeys)) {
			query.setParameter("complianceKeys", complianceKeys);
		}
		return query.getResultList();
	}

	@Override
	public List<VendorComplianceDataDTO> getLatestCompliances() {
		List<VendorComplianceDataDTO> result = new ArrayList<>();
		try {
			String queryString = "SELECT v.COMPLIANCE_TYPE, v.COMPLIANCE_KEY, MAX(v.VENDOR_COMPLIANCE_ID) FROM VENDOR_COMPLIANCE_DATA v " +
					"WHERE v.KEY_ID IS NOT NULL GROUP BY v.COMPLIANCE_TYPE, v.COMPLIANCE_KEY";
			Query query = manager.createNativeQuery(queryString);
			List<Object[]> list = query.getResultList();
			if (list != null && list.size() > 0) {
				for (Object[] detail : list) {
					VendorComplianceDataDTO dataDTO = new VendorComplianceDataDTO();
					dataDTO.setComplianceType((String) detail[0]);
					dataDTO.setComplianceKey((String) detail[1]);
					dataDTO.setVendorComplianceId((Integer) detail[2]);
					result.add(dataDTO);
				}
			}
		} catch (Exception e) {
			LOG.error("No Latest Compliances Found :::", e);
		}
		return result;
	}

	@Override
	public VendorContractInfo getVendorContractInfo(Integer vendorId) {
		try {
			Query query = manager.createQuery("from VendorContractInfo where vendorId =: vendorId and recordStatus != :recordStatus ORDER BY 1 DESC");
			query.setParameter("vendorId",vendorId);
			query.setParameter("recordStatus", SCMOrderStatus.CANCELLED.value());
			List<VendorContractInfo> result =  query.getResultList();
			return result.size()>0 ? result.get(0) : null;
		}catch (NoResultException e) {
			LOG.error("Vendor Contract info not found  ::: vendor ID : {}", vendorId);
			return null;
		}catch (Exception e) {
			LOG.error("Error while getting Vendor Contract info  ::: vendor ID : {}", vendorId);
			throw new RuntimeException(e.getMessage());
		}
	}

	@Override
	public List<VendorCompanyDetailData> getVendorCompanyDetailsWithPan(List<String> panNumbers) {
		try {
			Query query = manager.createQuery("FROM VendorCompanyDetailData a WHERE a.PAN IN(:panNumbers)");
			query.setParameter("panNumbers", panNumbers);
			return (List<VendorCompanyDetailData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Error while getVendorCompanyDetailsWithPan ::: ", e);
			return new ArrayList<>();
		}
	}

    @Override
    public List<VendorLogData> getAllLogsById(Integer vendorId) {
		Query query = manager.createQuery("FROM VendorLogData WHERE vendorId = :vendorId ORDER BY id DESC");
		query.setParameter("vendorId", vendorId);
		List<VendorLogData> logDataList = query.getResultList();
		if (!CollectionUtils.isEmpty(logDataList)) {
			return logDataList;
		}
		return new ArrayList<>();
    }
}
