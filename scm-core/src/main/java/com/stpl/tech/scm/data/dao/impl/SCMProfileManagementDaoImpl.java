package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.data.dao.SCMProductManagementDao;
import com.stpl.tech.scm.data.dao.SCMProfileManagementDao;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 07-05-2016.
 */
@Repository
public class SCMProfileManagementDaoImpl extends SCMAbstractDaoImpl implements SCMProfileManagementDao {


    @Override
    public List<EntityAttributeValueMappingData> getEntityAttributeValueMappingForEntityAndType(int entityId, String entityType) {
        Query query = manager.createQuery("FROM EntityAttributeValueMappingData e WHERE e.entityId = :entityId and e.entityType = :entityType");
        query.setParameter("entityId", entityId);
        query.setParameter("entityType",entityType);
        return query.getResultList();
    }

    @Override
    public ProfileDefinitionData getProfileByName(String profileName) {
        Query query = manager.createQuery("FROM ProfileDefinitionData e WHERE e.profileName = :profileName");
        query.setParameter("profileName", profileName);
        try{
            ProfileDefinitionData pdd = (ProfileDefinitionData) query.getSingleResult();
            return pdd;
        } catch(NoResultException e){
            e.printStackTrace();
        }

        return null;
    }
}
