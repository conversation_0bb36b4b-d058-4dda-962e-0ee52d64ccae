package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.RequestOrderManagementDao;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.PlanOrderItemPrepData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.data.model.RequestOrderItemTaxDetail;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.OrderTransferType;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.StatusType;
import com.stpl.tech.scm.domain.model.TaxDetail;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 13-06-2016.
 */

@Repository
public class RequestOrderManagementDaoImpl extends SCMAbstractDaoImpl implements RequestOrderManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(RequestOrderManagementDaoImpl.class);

    @Override
    public List<RequestOrderData> getRequestOrders(Integer fulfillingUnitId, Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer requestOrderId, String searchTag) {
        String queryString = "FROM RequestOrderData r WHERE ";
        if (requestOrderId != null) {
            queryString += "r.id = :requestOrderId ";
        } else {
            queryString += "r.generationTime >= :startDate and r.generationTime < :endDate ";
        }
        if (requestingUnitId != null) {
            queryString += "and r.requestUnitId = :requestingUnitId ";
        }
        if (fulfillingUnitId != null) {
            queryString += "and r.fulfillmentUnitId = :fulfillingUnitId ";
        }
        if (status != null) {
            queryString += "and r.status = :status ";
        }
        if(searchTag!=null){
            queryString += "and r.tag= :searchTag ";
        }
        queryString += "order by r.generationTime";
        Query query = manager.createQuery(queryString);
        if (requestOrderId != null) {
            query.setParameter("requestOrderId", requestOrderId);
        } else {
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
        }
        if (requestingUnitId != null) {
            query.setParameter("requestingUnitId", requestingUnitId);
        }
        if (fulfillingUnitId != null) {
            query.setParameter("fulfillingUnitId", fulfillingUnitId);
        }
        if (status != null) {
            query.setParameter("status", status.value());
        }
        if (searchTag != null) {
            query.setParameter("searchTag", searchTag);
        }
        return query.getResultList();
    }

    @Override
    public List<RequestOrderData> getRequestOrdersByIds(Integer fulfillingUnitId, Date startDate, Date endDate, SCMOrderStatus status, List<Integer> requestOrderIds) {
        String queryString = "select  distinct  r FROM RequestOrderData r  left join fetch r.requestOrderItemDatas WHERE ";
        if (requestOrderIds != null && requestOrderIds.size() >0 ) {
            queryString += "r.id in :requestOrderIds ";
        } else {
            queryString += "r.generationTime >= :startDate and r.generationTime < :endDate ";
        }

        if (fulfillingUnitId != null) {
            queryString += "and r.fulfillmentUnitId = :fulfillingUnitId ";
        }
        if (status != null) {
            queryString += "and r.status = :status ";
        }
        /*queryString += "order by r.generationTime";*/
        Query query = manager.createQuery(queryString);
        if (requestOrderIds != null && requestOrderIds.size() >0) {
            query.setParameter("requestOrderIds", requestOrderIds);
        } else {
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
        }

        if (fulfillingUnitId != null) {
            query.setParameter("fulfillingUnitId", fulfillingUnitId);
        }
        if (status != null) {
            query.setParameter("status", status.value());
        }
        return query.getResultList();
    }

    @Override
    public RequestOrderData getLastAdhocF9Order(RequestOrderData requestOrderData) {
        try {
            List<RequestOrderData> finalList = new ArrayList<>();
            Query query1 = manager.createQuery("FROM RequestOrderData r WHERE r.requestUnitId =:requestUnitId AND r.fulfillmentUnitId =:fulfillmentUnitId" +
                    " AND r.isSpecialOrder =:isSpecialOrder AND r.assetOrder =:assetOrder AND r.referenceOrderData IS NULL ORDER BY r.id DESC");
            query1.setParameter("requestUnitId",requestOrderData.getRequestUnitId()).setParameter("fulfillmentUnitId",requestOrderData.getFulfillmentUnitId()).
                    setParameter("isSpecialOrder",AppConstants.NO).setParameter("assetOrder",AppConstants.NO).setMaxResults(2);
            List<RequestOrderData> list1 = (List<RequestOrderData>) query1.getResultList();
            if (list1.size() == 2) {
                finalList.add(list1.get(1));
            }

            Query query2 = manager.createQuery("FROM RequestOrderData r WHERE r.requestUnitId =:requestUnitId AND r.fulfillmentUnitId =:fulfillmentUnitId" +
                    " AND r.isSpecialOrder =:isSpecialOrder AND r.assetOrder =:assetOrder AND r.referenceOrderData.refOrderSource =:refOrderSource ORDER BY r.id DESC");
            query2.setParameter("requestUnitId",requestOrderData.getRequestUnitId()).setParameter("fulfillmentUnitId",requestOrderData.getFulfillmentUnitId()).
                    setParameter("isSpecialOrder",AppConstants.NO).setParameter("assetOrder",AppConstants.NO).
                    setParameter("refOrderSource","FOUNTAIN9_DATA_SOURCE_ORDERING").setMaxResults(1);
            List<RequestOrderData> list2 = (List<RequestOrderData>) query2.getResultList();
            finalList.addAll(list2);
            List<RequestOrderData> data = finalList.stream().sorted(Comparator.comparing(RequestOrderData::getGenerationTime)).collect(Collectors.toList());
            return data.get(data.size() -1);
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding last Adhoc F9 Order",e);
        }
        return null;
    }

    @Override
    public List<RequestOrderData> getRequestOrdersFromReferenceOrder(int referenceOrderId) {
        Query query = manager.createQuery("SELECT r FROM RequestOrderData r WHERE r.referenceOrderData.id = :referenceOrderId");
        query.setParameter("referenceOrderId", referenceOrderId);
        return query.getResultList();
    }

    @Override
    public FulfillmentUnitMappingData getFulFillmentUnitByType(FulfillmentType fulfillmentType, int requestUnitId) {
        Query query = manager.createQuery("SELECT f FROM FulfillmentUnitMappingData f WHERE f.requestingUnitId = :requestingUnitId AND f.fulfillmentType = :fulfillmentType");
        query.setParameter("requestingUnitId", requestUnitId);
        query.setParameter("fulfillmentType", fulfillmentType.value());
        return (FulfillmentUnitMappingData) query.getSingleResult();
    }

    @Override
    public List<Integer> getALlReceivingUnits(FulfillmentType fulfillmentType, int fulfillingUnit) {
        Query query = manager.createQuery("SELECT f.requestingUnitId FROM FulfillmentUnitMappingData f WHERE f.fulfillingUnitId = :fulfillingUnit " +
                "AND f.fulfillmentType = :fulfillmentType AND f.status = :status");
        query.setParameter("fulfillingUnit", fulfillingUnit);
        query.setParameter("fulfillmentType", fulfillmentType.value());
        query.setParameter("status", StatusType.ACTIVE.value());
        return query.getResultList();
    }

    @Override
    public List<RequestOrderData> getPendingRequestOrders(Integer unitId) {
        String qString = "FROM RequestOrderData r WHERE r.status = :status ";
        if (unitId != null) {
            qString += "AND r.fulfillmentUnitId = :fulfillmentUnitId ";
        }
        qString += "ORDER BY r.fulfillmentDate";
        Query query = manager.createQuery(qString);
        query.setParameter("status", SCMOrderStatus.ACKNOWLEDGED.value());
        if (unitId != null) {
            query.setParameter("fulfillmentUnitId", unitId);
        }
        return query.getResultList();
    }

    @Override
    public List<RequestOrderData> getPendingAcknowledgedRequestOrders(Date date, int fulfillmentUnit) {
        String queryStatement = "SELECT E FROM RequestOrderData E " +
                "WHERE E.fulfillmentUnitId = :fulfillmentUnitId and E.fulfillmentDate = :date " +
                "and E.status in (:status)";

        String[] list = {SCMOrderStatus.ACKNOWLEDGED.toString()};
        List<String> statusList = Arrays.asList(list);
        Query query = manager.createQuery(queryStatement).setParameter("date", SCMUtil.getDate(date))
                .setParameter("status", statusList)
                .setParameter("fulfillmentUnitId", fulfillmentUnit);
        return (List<RequestOrderData>) query.getResultList();
    }


    @Override
    public List<RequestOrderData> getPendingRequestOrders(String date, int fulfillmentUnit) {
        String queryStatement = "SELECT E FROM RequestOrderData E " +
            "WHERE E.fulfillmentUnitId = :fulfillmentUnitId and E.fulfillmentDate = :date " +
            "and E.status in (:status)";

        String[] list = {SCMOrderStatus.CREATED.toString(), SCMOrderStatus.ACKNOWLEDGED.toString()};
        List<String> statusList = Arrays.asList(list);
        Query query = manager.createQuery(queryStatement).setParameter("date", SCMUtil.getDate(SCMUtil.parseDate(date)))
            .setParameter("status", statusList)
            .setParameter("fulfillmentUnitId", fulfillmentUnit);
        return (List<RequestOrderData>) query.getResultList();
    }

    @Override
    public List<RequestOrderData> getSpecializedROForFulfillmentDate(Date fulfillmentDate, boolean isReceiving){
        Query query = null;
        if(isReceiving){
            query = manager.createQuery("FROM RequestOrderData r WHERE r.isSpecialOrder = :isSpecialOrder AND r.status != :cancelled AND r.status = :settled" +
                " AND r.fulfillmentDate = :startTime");
        }else{
            query = manager.createQuery("FROM RequestOrderData r WHERE r.isSpecialOrder = :isSpecialOrder AND r.status != :cancelled AND r.status != :settled" +
                " AND r.fulfillmentDate = :startTime");
        }
        query.setParameter("isSpecialOrder",SCMUtil.setStatus(true));
        query.setParameter("cancelled",SCMOrderStatus.CANCELLED.value());
        query.setParameter("settled",SCMOrderStatus.SETTLED.value());
        query.setParameter("startTime", fulfillmentDate);
        return query.getResultList();
    }


	@Override
	public List<RequestOrderData> getSpecializedROForNotification(NotificationType type, Date startTime, Date endTime) {
		Query query = manager.createQuery(
				"FROM RequestOrderData r WHERE r.isSpecialOrder = :isSpecialOrder AND r.status != :cancelled AND r.status != :settled"
						+ " AND r.notificationTime is not null and r.notificationTime >= :startTime and r.notificationTime <= :endTime and r.isNotified = :notified and r.notificationTypes like :notification");
		query.setParameter("isSpecialOrder", SCMUtil.setStatus(true));
		query.setParameter("cancelled", SCMOrderStatus.CANCELLED.value());
		query.setParameter("settled", SCMOrderStatus.SETTLED.value());
		query.setParameter("startTime", startTime);
		query.setParameter("endTime", endTime);
		query.setParameter("notified", AppConstants.NO);
		query.setParameter("notification", "%" + type.name() + "%");
		return query.getResultList();
	}

    @Override
    public List<RequestOrderData> getSpecialOrdersForDate(Date date){
        Query query = manager.createQuery("FROM RequestOrderData r WHERE r.generationTime >= :generationTime AND r.generationTime < :endTime AND r.isSpecialOrder = :isSpecialOrder");
        query.setParameter("isSpecialOrder",SCMUtil.setStatus(true));
        query.setParameter("generationTime", date);
        query.setParameter("endTime", SCMUtil.getNextDate(date));
        return query.getResultList();
    }

    @Override
    public List<PlanOrderItemPrepData> findPlanOrderItemPrepByPlanOrderItemId(Integer itemId){
        Query query = manager.createQuery("FROM PlanOrderItemPrepData r WHERE r.planOrderItemData.id = :itemId");
        query.setParameter("itemId", itemId);
        return query.getResultList();
    }

    @Override
    public List<RequestOrderData> getRequestOrdersForInvoice(Integer sendingUnit, Integer receivingUnit) {
        Query query = manager.createQuery("FROM RequestOrderData r WHERE r.fulfillmentUnitId = :sendingUnit " +
                " AND r.requestUnitId = :receivingUnit AND r.status in (:transferred) AND r.transferType = :transferType" +
                " ORDER BY r.id DESC");
        query
                .setParameter("sendingUnit", sendingUnit)
                .setParameter("receivingUnit", receivingUnit)
                .setParameter("transferred", Arrays.asList(SCMOrderStatus.TRANSFERRED.name(), SCMOrderStatus.SETTLED.name()))
                .setParameter("transferType", OrderTransferType.INVOICE.name())
                .setMaxResults(10);
        return query.getResultList();
    }

	@Override
	public void markRequestOrderNotified(List<Integer> requestOrdersNotified) {
		Query query = manager.createQuery("update RequestOrderData r "
				+ " set isNotified = :notified WHERE r.id IN (:requestOrdersNotified) and  r.isNotified = :notNotified");
		query.setParameter("notified", AppConstants.YES).setParameter("requestOrdersNotified", requestOrdersNotified)
				.setParameter("notNotified", AppConstants.NO);
		query.executeUpdate();
	}

    @Override
    public void setTaxDetail(int stateId, RequestOrderItemData detail, RequestOrderItem item) {
        if (item.getTaxes() == null || item.getTaxes().isEmpty()) {
            return;
        }
        for (TaxDetail tax : item.getTaxes()) {
            RequestOrderItemTaxDetail taxDetail = new RequestOrderItemTaxDetail();
            taxDetail.setTaxCode(tax.getCode());
            taxDetail.setTaxType(tax.getType());
            taxDetail.setTotalTax(tax.getValue());
            taxDetail.setTaxPercentage(tax.getPercentage());
            taxDetail.setTotalAmount(tax.getTotal());
            taxDetail.setTaxableAmount(tax.getTaxable());
            taxDetail.setOrderItem(detail);
            manager.persist(taxDetail);
        }
        manager.flush();

    }

	@Override
	public boolean isDifferentCompany(Integer requestOrderId) {
		RequestOrderData ro = manager.find(RequestOrderData.class, requestOrderId);
		return ro.getFulfillmentCompanyId() != ro.getRequestCompanyId();
	}

    public List<RequestOrderData> getLastWeekOrders(Integer unitId,Boolean isSpecial){
        List<RequestOrderData> requestOrderDataList = new ArrayList<>();
        try {
            Date current = SCMUtil.getCurrentTimestamp();
            Calendar date = Calendar.getInstance();
            date.add(Calendar.DATE, -7);
            Date previousDate=date.getTime();
            Query query;
            if(isSpecial){
                query = manager.createQuery("FROM RequestOrderData where requestUnitId = :unitId and isSpecialOrder = :isSpecialOrder " +
                        "AND status <> :status " +
                        " AND generationTime  between :startDate and :endDate ");
                query.setParameter("isSpecialOrder",AppConstants.YES);
            }else {
                query = manager.createQuery("FROM RequestOrderData where requestUnitId = :unitId " +
                        "AND status <> :status " +
                        " AND generationTime  between :startDate and :endDate ");
            }
            query.setParameter("unitId",unitId).setParameter("status",SCMOrderStatus.CANCELLED.value())
                    .setParameter("startDate",previousDate).setParameter("endDate",current);
            requestOrderDataList =(List<RequestOrderData>)  query.getResultList();
        }catch (Exception e){
            LOG.error("Error While Getting Last Week Specialized Order For Unit  : {} ::::::", unitId,e);
        }
        return requestOrderDataList;
    }

    @Override
    public BigDecimal getLastTransferPriceBySku(Integer fulfullingUnitId, Integer skuId) {
        try{
            Query query= manager.createNativeQuery("SELECT toi.NEGOTIATED_UNIT_PRICE FROM TRANSFER_ORDER  t  INNER JOIN TRANSFER_ORDER_ITEM toi on t.TRANSFER_ORDER_ID=toi.TRANSFER_ORDER_ID" +
                    " where t.GENERATION_UNIT_ID = :generationUnitId and toi.SKU_ID = :skuId and toi.NEGOTIATED_UNIT_PRICE > :zeroPrice order by t.GENERATION_TIME desc limit 1");
            query.setParameter("generationUnitId",fulfullingUnitId);
            query.setParameter("skuId",skuId);
            query.setParameter("zeroPrice",BigDecimal.ZERO);
            List<BigDecimal> res = query.getResultList();
            if(res.isEmpty()) return BigDecimal.ZERO;
            return res.get(0);
        } catch (Exception e) {
            LOG.error("Error while fetching Negotiated Unit Price for :{}  ::::::",fulfullingUnitId,e);
        }
        return BigDecimal.valueOf(0);
    }

}
