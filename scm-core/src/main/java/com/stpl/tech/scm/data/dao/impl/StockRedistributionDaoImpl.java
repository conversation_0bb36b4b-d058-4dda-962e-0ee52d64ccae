package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.data.dao.StockRedistributionDao;
import com.stpl.tech.scm.data.model.RiderInfoData;
import com.stpl.tech.scm.data.model.RiderRoutePlanData;
import com.stpl.tech.scm.data.model.RiderRoutePlanStepData;
import com.stpl.tech.scm.domain.model.RiderRoutePlanStatusEnum;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
@Log4j2
public class StockRedistributionDaoImpl extends SCMAbstractDaoImpl implements StockRedistributionDao {
    @Override
    public RiderInfoData findRiderInfoByEmployeeId(Integer employeeId) {
        try {
            Query query = manager.createQuery("FROM RiderInfoData E WHERE E.employeeId = :employeeId")
                    .setParameter("employeeId", employeeId);
            return (RiderInfoData) query.getSingleResult();
        } catch (Exception e) {
            log.error("Error while findRiderInfoByEmployeeId for employee id : {} ", employeeId, e);
        }
        return null;
    }

    @Override
    public List<RiderRoutePlanData> getRoutePlanOfRiderByStatus(RiderInfoData riderInfoData, List<RiderRoutePlanStatusEnum> statusList, boolean fetchAllData) {
        try {
            StringBuilder queryString = new StringBuilder("SELECT DISTINCT rrpd FROM RiderRoutePlanData rrpd ");
            if (fetchAllData) {
               queryString.append("LEFT JOIN FETCH rrpd.riderRoutePlanStepDataSet rrpsd ")
                       .append("LEFT JOIN FETCH rrpsd.riderRoutePlanItemDataSet ");
            }
            queryString.append("WHERE rrpd.riderRoutePlanStatus IN (:riderRoutePlanStatusList) ");
            if (Objects.nonNull(riderInfoData)) {
                queryString.append("AND rrpd.riderInfoData.riderInfoDataId = :riderInfoDataId ");
            }
            queryString.append("ORDER BY rrpd.riderRoutePlanDataId");
            Query query = manager.createQuery(queryString.toString())
                    .setParameter("riderRoutePlanStatusList", statusList);
            if (Objects.nonNull(riderInfoData)) {
                 query.setParameter("riderInfoDataId", riderInfoData.getRiderInfoDataId());
            }
            return (List<RiderRoutePlanData>) query.getResultList();
        } catch (Exception e) {
            log.error("Error while getRoutePlanOfRiderByStatus ", e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<RiderRoutePlanStepData> getRiderRouteStepDetails(Integer riderRoutePlanStepDataId) {
        try {
            Query query = manager.createQuery("SELECT DISTINCT rrpsd FROM RiderRoutePlanStepData rrpsd " +
                            "LEFT JOIN FETCH rrpsd.riderRoutePlanItemDataSet " +
                            "WHERE rrpsd.riderRoutePlanStepDataId = :riderRoutePlanStepDataId")
                    .setParameter("riderRoutePlanStepDataId", riderRoutePlanStepDataId);
            return (List<RiderRoutePlanStepData>) query.getResultList();
        } catch (Exception e) {
            log.error("Error while getRiderRouteStepDetails for route Step Id : {} ", riderRoutePlanStepDataId , e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<RiderRoutePlanData> getRouteDetailsByRoutePlanId(Integer riderRoutePlanDataId) {
        try {
            Query query = manager.createQuery("SELECT DISTINCT rrpd FROM RiderRoutePlanData rrpd " +
                            "LEFT JOIN FETCH rrpd.riderRoutePlanStepDataSet rrpsd " +
                            "WHERE rrpd.riderRoutePlanDataId = :riderRoutePlanDataId")
                    .setParameter("riderRoutePlanDataId",riderRoutePlanDataId);
            return (List<RiderRoutePlanData>) query.getResultList();
        } catch (Exception e) {
            log.error("Error while getRouteDetailsByRoutePlanId for route plan Id : {} ", riderRoutePlanDataId , e);
        }
        return new ArrayList<>();
    }
}
