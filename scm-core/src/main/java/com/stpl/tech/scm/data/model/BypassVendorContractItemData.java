package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "BYPASS_VENDOR_CONTRACT_ITEM")
public class BypassVendorContractItemData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BYPASS_CONTRACT_ITEM_ID",unique = true)
    private Integer bypassContractItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BYPASS_CONTRACT_ID", nullable = false)
    private BypassVendorContractData bypassContract;

    @Column(name = "VENDOR_ID")
    private Integer vendorId;

    @Column(name = "SKU_ID")
    private Integer skuId;

    @Column(name = "SKU_PACKAGING_ID")
    private Integer skuPackagingId;

    @Column(name = "DISPATCH_LOCATION")
    private String dispatchLocation;

    @Column(name = "DELIVERY_LOCATION")
    private String deliveryLocation;

    @Column(name = "DISPATCH_LOCATION_ID")
    private Integer dispatchLocationId;

    @Column(name = "DELIVERY_LOCATION_ID")
    private Integer deliveryLocationId;

    @Column(name = "CURRENT_PRICE")
    private  BigDecimal currentPrice;
    @Column(name = "UPDATED_PRICE")
    private BigDecimal updatedPrice;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "SELECTED_REJECTION_REASON")
    private String selectedRejectionReason;

    @Column(name = "PREVIOUS_REJECTION_REASON")
    private String previousRejectionReason;

    @Column(name = "CURRENT_REJECTION_REASON")
    private String rejectionReason;

    @Column(name = "SKU_PRICE_DATA_ID")
    private Integer skuPriceDataId;

    @Column(name = "TAX_CODE")
    private String taxCode;

    @Column(name = "TAX_PERCENTAGE")
    private BigDecimal taxPercentage;

    @Column(name = "IS_NEW", nullable = false)
    private String isNew;

    @Column(name = "IS_NEW_ITEM")
    private String isNewItem;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "bypassContractItem")
    private Set<BypassVendorContractItemUnitData> bypassVendorContractItemUnitDataList;
}
