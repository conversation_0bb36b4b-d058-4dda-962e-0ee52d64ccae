/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SCMError;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.util.SCMConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.VendorContractDataDao;
import com.stpl.tech.scm.data.dao.VendorContractItemDataDao;
import com.stpl.tech.scm.data.dao.VendorContractLogDao;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.dao.SkuPriceDataDao;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.VendorContractData;
import com.stpl.tech.scm.data.model.VendorContractItemData;
import com.stpl.tech.scm.data.model.VendorContractItemUnitData;
import com.stpl.tech.scm.data.model.VendorContractLogs;
import com.stpl.tech.scm.data.model.InventoryListTypeData;
import com.stpl.tech.scm.data.model.PageRequestDetail;
import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.data.model.PurchaseProfile;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.data.model.SkuPriceData;
import com.stpl.tech.scm.data.model.SkuPriceHistory;
import com.stpl.tech.scm.data.model.UnitCategoryData;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.data.model.UnitSkuMapping;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.data.model.VendorContractItem;
import com.stpl.tech.scm.data.model.VendorSkuMapping;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.data.model.ZipCodeDistanceMapping;
import com.stpl.tech.scm.data.model.businessVendorMapping;
import com.stpl.tech.scm.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.scm.domain.model.LogType;
import com.stpl.tech.scm.domain.model.PageRequestType;
import com.stpl.tech.scm.domain.model.ChangeType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.PreviousPricingDataVO;
import com.stpl.tech.scm.domain.model.PriceStatus;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.StatusType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.VendorContract;
import com.stpl.tech.scm.domain.model.VendorContractStatus;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.domain.model.WorkOrder;
import com.stpl.tech.scm.domain.model.WorkOrderType;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;
import com.stpl.tech.scm.notification.email.UserContractEmailNotification;
import com.stpl.tech.scm.notification.email.VendorContractEmailNotification;
import com.stpl.tech.scm.notification.email.template.VendorContractEmailNotificationTemplate;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.AttachmentData;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Mohit
 */

@Repository
public class SkuMappingDaoImpl extends SCMAbstractDaoImpl implements SkuMappingDao {

	private static final Logger LOG = LoggerFactory.getLogger(SkuMappingDaoImpl.class);

	@Autowired
	private SCMCache cache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private EnvProperties props;

	@Autowired
	private SCMMetadataService scmMetadataService;

	@Autowired
	private SCMConstants scmConstants;

	@Autowired
	private VendorContractItemDataDao vendorContractItemDataDao;

	@Autowired
	private VendorContractDataDao vendorContractDataDao;

	@Autowired
	private SkuPriceDataDao skuPriceDataDao;

	@Autowired
	private VendorContractLogDao statusLogDao;
	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updatePrices(com.stpl.tech.scm.
	 * domain.model.SkuPriceUpdate)
	 */
	@Override
	public boolean updatePrices(SkuPriceUpdate data) throws SumoException {
		boolean updatePrice = false;
		boolean addPrice = false;
		if(data.getDetail().getKeyId() != null) {
			cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data, PriceStatus.APPLIED);
//		cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data, PriceStatus.PENDING);
//		cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data, PriceStatus.REACTIVATION_REQUESTED);
//		cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data, PriceStatus.DEACTIVATION_REQUESTED);
			checkApprovedPriceUpdate(ChangeType.PRICE_UPDATE, data, List.of(PriceStatus.APPROVED.name(),
					PriceStatus.REACTIVATION_ACCEPTED.name(), PriceStatus.DEACTIVATION_ACCEPTED.name()));
			SkuPriceData current = manager.find(SkuPriceData.class, data.getDetail().getKeyId());
			Query query = manager.createQuery(
					"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and changeType =:changeType order by 1 desc");
			query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
			query.setParameter("changeType", ChangeType.PRICE_UPDATE.name());
			query.setMaxResults(1);
			List<SkuPriceHistory> history = query.getResultList();
			SkuPriceHistory price = new SkuPriceHistory();
			if (!history.isEmpty() && history.get(0).getContractId() != null) {
				price.setContractId(history.get(0).getContractId());
			}
			price.setCreatedAt(AppUtils.getCurrentTimestamp());
			price.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
			price.setCurrentPrice(data.getDetail().getCurrent().getValue());
			price.setNegotiatedPrice(data.getDetail().getUpdated().getValue());
			price.setRecordStatus(PriceStatus.CREATED.name());
			price.setSkuPriceDataId(data.getDetail().getKeyId());
//		price.setStartDate(data.getDetail().getUpdated().getDate());
			price.setChangeType(ChangeType.PRICE_UPDATE.name());
			current.setIsPriceChangeRequested(AppConstants.YES);
			manager.persist(price);
			manager.flush();
			VendorContractItemData itemData = vendorContractItemDataDao.findByVendorIdAndSkuIdAndSkuPackagingIdAndDispatchLocationAndDeliveryLocation(data.getDetail().getVendor().getId(),
					data.getDetail().getSku().getId(),data.getDetail().getPkg().getId(),data.getDetail().getDispatch().getCode(),data.getDetail().getDelivery().getName());
			itemData.setSkuPriceDataId(data.getDetail().getKeyId());
			vendorContractItemDataDao.save(itemData);
			updatePrice = true;
		}else {
			addPrice = addPrice(data);
		}
		return addPrice && updatePrice;
	}

	private void checkApprovedPriceUpdate(ChangeType priceUpdate, SkuPriceUpdate data, List<String> status) throws SumoException {
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and changeType=:changeType order by 1 desc");
		query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
		query.setParameter("changeType",priceUpdate.name());
		query.setMaxResults(1);
		List<SkuPriceHistory> history = query.getResultList();
		if (!history.isEmpty() &&
				(PriceStatus.APPROVED.name().equalsIgnoreCase(history.get(0).getRecordStatus()) ||
				PriceStatus.REACTIVATION_ACCEPTED.name().equalsIgnoreCase(history.get(0).getRecordStatus()) ||
				PriceStatus.DEACTIVATION_ACCEPTED.name().equalsIgnoreCase(history.get(0).getRecordStatus()))) {
			throw new SumoException("Price for SKU "+data.getDetail().getSku().getName()+" Found in Approved State");
		}
	}

	@Override
	public void updateSkuPricesFromCurrentDay(SkuPriceUpdate data) {
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and spd.recordStatus = :status and spd.changeType = :changeType");
		query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
		query.setParameter("status", PriceStatus.CREATED.name());
		query.setParameter("changeType", ChangeType.PRICE_UPDATE.name());
		List<SkuPriceHistory> history = query.getResultList();
		BigDecimal price = null;
		if (history != null) {
			for (SkuPriceHistory p : history) {
				p.setUpdatedAt(AppUtils.getCurrentTimestamp());
				p.setUpdatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
				p.setRecordStatus(PriceStatus.APPLIED.name());
				p.setChangeType(ChangeType.PRICE_UPDATE.name());
				price = p.getNegotiatedPrice();
			}
		}
		SkuPriceData current = manager.find(SkuPriceData.class, data.getDetail().getKeyId());
		current.setPrice(price);
		manager.flush();
	}

	@Override
	public 	void processPriceRequestForVendor(SkuPriceUpdate val, PriceStatus key, PriceStatus currentStatus, Set<Integer> processedId) throws SumoException {
		checkApprovedPriceUpdate(ChangeType.PRICE_UPDATE, val, List.of(PriceStatus.APPROVED.name(),
				PriceStatus.REACTIVATION_ACCEPTED.name(),PriceStatus.DEACTIVATION_ACCEPTED.name()));
		if (processedId.contains(val.getDetail().getKeyId())){
			return;
		}
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and spd.recordStatus = :status and spd.changeType = :changeType order by 1 desc");
		query.setParameter("skuPriceDataId", val.getDetail().getKeyId());
		query.setParameter("status", currentStatus.name());
		query.setParameter("changeType", ChangeType.PRICE_UPDATE.name());
		query.setMaxResults(1);
		List<SkuPriceHistory> history = query.getResultList();
		if (history != null) {
			for (SkuPriceHistory p : history) {
				SkuPriceHistory price = new SkuPriceHistory();
				price.setCreatedAt(AppUtils.getCurrentTimestamp());
				price.setCreatedBy(getName(val.getEmployeeName(), val.getEmployeeId()));
				price.setCurrentPrice(p.getCurrentPrice());
				if (p.getNegotiatedPrice()!=null){
					price.setNegotiatedPrice(p.getNegotiatedPrice());
				} else {
					price.setNegotiatedPrice(p.getCurrentPrice());
				}
				price.setRecordStatus(key.name());
				price.setSkuPriceDataId(p.getSkuPriceDataId());
				price.setStartDate(p.getStartDate());
				price.setEndDate(p.getEndDate());
				if (p.getContractId()!=null) {
					price.setContractId(p.getContractId());
				}
				price.setChangeType(ChangeType.PRICE_UPDATE.name());
				manager.persist(price);
				SkuPriceData data = manager.find(SkuPriceData.class,p.getSkuPriceDataId());
				if (Objects.nonNull(data)) {
					data.setIsPriceChangeRequested(AppConstants.YES);
				} else {
					throw new SumoException("Price Data Not Found"+ p.getSkuPriceDataId());
				}
				processedId.add(val.getDetail().getKeyId());
			}
		}
		manager.flush();
	}

	@Override
	public boolean addPrice(SkuPriceUpdate data) {
		// cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data);

		SkuPriceData current = new SkuPriceData();
		current.setPackagingId(data.getDetail().getPkg().getId());
		current.setSkuId(data.getDetail().getSku().getId());
		current.setVendorId(data.getDetail().getVendor().getId());
		current.setDispatchLocation(data.getDetail().getDispatch().getCode());
		current.setDispatchLocationId(data.getDetail().getDispatch().getId());
		current.setDeliveryLocation(data.getDetail().getDelivery().getName());
		current.setDeliveryLocationId(data.getDetail().getDelivery().getId());
		current.setPrice(data.getDetail().getUpdated().getValue());
//		current.setStartDate(AppUtils.getCurrentTimestamp());
		current.setStatus(AppConstants.ACTIVE);
		current.setLeadTime(data.getDetail().getLeadTime());
		current.setIsPriceChangeRequested(AppConstants.YES);
		SkuPriceData data1 = manager.merge(current);
		VendorContractItemData itemData = vendorContractItemDataDao.findByVendorIdAndSkuIdAndSkuPackagingIdAndDispatchLocationAndDeliveryLocation(data.getDetail().getVendor().getId(),
				data.getDetail().getSku().getId(),data.getDetail().getPkg().getId(),data.getDetail().getDispatch().getCode(),data.getDetail().getDelivery().getName());
		itemData.setSkuPriceDataId(data1.getSkuPriceKeyId());
		vendorContractItemDataDao.save(itemData);
        try {
			scmMetadataService.saveAuditLog(current.getSkuPriceKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), data.getEmployeeId(), ((Object) current),
					AuditChangeLogTypes.NEW_ENTRY.value());
		}catch (Exception e){
			LOG.info("Error While Saving Audit Log Data In Mongo",e);
		}

		SkuPriceHistory price = new SkuPriceHistory();
		price.setCreatedAt(AppUtils.getCurrentTimestamp());
		price.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
		price.setCurrentPrice(data.getDetail().getCurrent().getValue());
		price.setNegotiatedPrice(data.getDetail().getUpdated().getValue());
		price.setRecordStatus(PriceStatus.CREATED.name());
		price.setSkuPriceDataId(data1.getSkuPriceKeyId());
//		price.setStartDate(AppUtils.getCurrentTimestamp());
		price.setChangeType(ChangeType.PRICE_UPDATE.name());
		manager.persist(price);
		manager.flush();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updatePrices(com.stpl.tech.scm.
	 * domain.model.SkuPriceUpdate)
	 */
	@Override
	public boolean cancelPriceUpdate(SkuPriceUpdate data) {
		cancelExistingPriceUpdates(ChangeType.PRICE_CANCEL, data, PriceStatus.CREATED);
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updatePrices(com.stpl.tech.scm.
	 * domain.model.SkuPriceUpdate)
	 */
	@Override
	public boolean updatePriceStatus(SkuPriceUpdate data) {
		SkuPriceData current = manager.find(SkuPriceData.class, data.getDetail().getKeyId());
		current.setStatus(data.getDetail().getStatus());
		SkuPriceHistory price = new SkuPriceHistory();
		price.setCreatedAt(AppUtils.getCurrentTimestamp());
		price.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
		price.setCurrentPrice(current.getPrice());
		price.setNegotiatedPrice(current.getPrice());
		price.setRecordStatus(AppConstants.ACTIVE.equals(data.getDetail().getStatus()) ? PriceStatus.ACTIVATED.name()
				: PriceStatus.DEACTIVATED.name());
		price.setSkuPriceDataId(data.getDetail().getKeyId());
		price.setStartDate(current.getStartDate());
		price.setChangeType(ChangeType.STATUS.name());
		manager.persist(price);
		manager.flush();
		return true;
	}

	private void cancelExistingPriceUpdates(ChangeType type, SkuPriceUpdate data, PriceStatus created) {
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and changeType =:changeType order by 1 desc");
		query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
		query.setParameter("changeType", type.name());
		query.setMaxResults(1);
		List<SkuPriceHistory> history = query.getResultList();
		if (history != null) {
			for (SkuPriceHistory p : history) {
				SkuPriceHistory price = new SkuPriceHistory();
				price.setCreatedAt(AppUtils.getCurrentTimestamp());
				price.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
				if (PriceStatus.REACTIVATION_REQUESTED.name().equalsIgnoreCase(p.getRecordStatus())) {
					createEventData(type, p, price);
				} else if (PriceStatus.DEACTIVATION_REQUESTED.name().equalsIgnoreCase(p.getRecordStatus())) {
					createEventData(type, p, price);
				} else if (PriceStatus.CREATED.name().equalsIgnoreCase(p.getRecordStatus())) {
					createEventData(type, p, price);
				}

			}
		}
		manager.flush();
	}

	private void createEventData(ChangeType type, SkuPriceHistory p, SkuPriceHistory price) {
		price.setRecordStatus(PriceStatus.CANCELLED.name());
		price.setChangeType(type.name());
		price.setCurrentPrice(p.getCurrentPrice());
		price.setNegotiatedPrice(p.getNegotiatedPrice());
		price.setStartDate(p.getStartDate());
		price.setEndDate(p.getEndDate());
		price.setContractId(p.getContractId());
		price.setSkuPriceDataId(p.getSkuPriceDataId());
		price.setChangeType(ChangeType.PRICE_UPDATE.name());
		manager.persist(price);
	}

	private void applyPriceUpdates(ChangeType type, SkuPriceUpdate data, PriceStatus created, VendorContractInfo item) {
		cancelPriceUpdate(data);
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and spd.recordStatus = :status");
		query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
		query.setParameter("status", PriceStatus.APPROVED.name());
		List<SkuPriceHistory> history = query.getResultList();
		if (history != null) {
			for (SkuPriceHistory p : history) {
				p.setUpdatedAt(AppUtils.getCurrentTimestamp());
				p.setUpdatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
				p.setRecordStatus(created.name());
				p.setChangeType(type.name());
				p.setStartDate(item.getStartDate());
				p.setEndDate(item.getEndDate());
				p.setContractId(item.getVendorContractId());

				// update sku price data
				SkuPriceData skuPriceData = manager.find(SkuPriceData.class,p.getSkuPriceDataId());
				if (PriceStatus.APPLIED.equals(created)){
					skuPriceData.setStartDate(item.getStartDate());
					skuPriceData.setEndDate(item.getEndDate());
					skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
					skuPriceData.setSkuPriceKeyId(p.getSkuPriceHistoryId());
					skuPriceData.setPrice(p.getNegotiatedPrice());
				} else {
					skuPriceData.setStatus(AppConstants.IN_ACTIVE);
				}
			}
		}
		manager.flush();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#searchPrices(int,
	 * java.lang.String)
	 */
	@Override
	public List<SkuPriceDetail> searchPricesBySku(int skuId, Integer deliveryLocation) {
		Query query = manager
				.createQuery("from SkuPriceData spd where spd.skuId = :skuId and spd.deliveryLocation = :location");
		query.setParameter("location", deliveryLocation);
		query.setParameter("skuId", skuId);

		return convert(query.getResultList(), true, false);
	}

	private List<SkuPriceDetail> convert(List<SkuPriceData> results, boolean getUpdated, boolean setUpdated) {
		List<SkuPriceDetail> list = new ArrayList<>();
		Map<Integer, Location> locationMap = masterDataCache.getAllLocationsData();
		results.stream().forEach((record) -> {
			SkuPriceDetail price = SCMDataConverter.convert(cache, locationMap, record,getUpdated);
			Query query = manager.createQuery("from SkuPriceHistory where skuPriceDataId = :skuPriceDataId and changeType = :changeType order by 1 desc ");
			query.setParameter("skuPriceDataId",record.getSkuPriceKeyId());
			query.setParameter("changeType",ChangeType.PRICE_UPDATE.name());
			query.setMaxResults(1);
			List<SkuPriceHistory> skuPriceHistories = query.getResultList();
			if (getUpdated && skuPriceHistories != null && !skuPriceHistories.isEmpty()
					&& (PriceStatus.CREATED.name().equalsIgnoreCase(skuPriceHistories.get(0).getRecordStatus()) ||
					PriceStatus.PENDING.name().equalsIgnoreCase(skuPriceHistories.get(0).getRecordStatus())
					)) {
				price.getUpdated().setValue(skuPriceHistories.get(0).getNegotiatedPrice());
				price.getUpdated().setDate(skuPriceHistories.get(0).getStartDate());
			} else {
				price.setUpdated(null);
			}
			if (setUpdated && Objects.nonNull(price.getUpdated().getValue())) {
				list.add(price);
			} else if (!setUpdated) {
				list.add(price);
			}
		});
		return list;
	}

	private List<SkuPriceDetail> convert(List<SkuPriceData> results) {
		List<SkuPriceDetail> list = new ArrayList<>();

		Map<Integer, Location> locationMap = masterDataCache.getAllLocationsData();

		results.stream().forEach((record) -> {
			SkuPriceDetail price = convertFromPriceDataToPriceDetail(record, locationMap);
			list.add(price);
		});
		return list;
	}

	private SkuPriceDetail convertFromPriceDataToPriceDetail(SkuPriceData data, Map<Integer, Location> locationMap) {
		SkuPriceDetail price = new SkuPriceDetail();
		price.setDelivery(SCMDataConverter.convertToIdCodeName(locationMap.get(data.getDeliveryLocationId())));
		price.setDispatch(SCMDataConverter.convertToIdCodeName(locationMap.get(data.getDispatchLocationId())));
		price.setPkg(SCMDataConverter.convertToPackagingData(cache.getPackagingDefinition(data.getPackagingId())));
		price.setSku(SCMDataConverter.convertToIdCodeName(cache.getSkuDefinition(data.getSkuId())));
		price.setVendor(SCMDataConverter.convertToIdCodeName(cache.getVendorDetail(data.getVendorId())));
		price.setKeyId(data.getSkuPriceKeyId());
		price.setStatus(data.getStatus());
		price.getCurrent().setDate(data.getStartDate());
		price.getCurrent().setValue(data.getPrice());
		price.setLeadTime(data.getLeadTime());
		return price;
	}

	@Override
	public List<SkuPriceDetail> searchPricesByVendorDeliveryLocation(int vendorId, Integer locationId, boolean isUpdated, boolean setUpdated) {
		StringBuilder ddl = new StringBuilder("From SkuPriceData spd where spd.vendorId = :vendorId ");
		if (Objects.nonNull(locationId)) {
			ddl.append("and spd.deliveryLocationId = :locationId");
		}
		Query query = manager.createQuery(ddl.toString());
		if (Objects.nonNull(locationId)) {
			query.setParameter("locationId", locationId);
		}
		query.setParameter("vendorId", vendorId);
		return convert(query.getResultList(), isUpdated, setUpdated);
	}

	@Override
	public List<SkuPriceDetail> searchPricesByVendorAndStatus(int vendorId, String status) {
		Query query = manager.createQuery("From SkuPriceData spd where spd.vendorId = :vendorId and spd.status =:status");
		query.setParameter("vendorId", vendorId).setParameter("status", status);
		return convert(query.getResultList());
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#searchSkuMappingsForUnit(int)
	 */
	@Override
	public List<IdCodeNameStatus> searchSkuMappingsForUnit(int unitId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
		"select DISTINCT usm.skuId, sdd.skuName, sdd.skuStatus, usm.mappingStatus, sdd.linkedProduct.categoryDefinition.categoryName, " +
			"sdd.linkedProduct.subCategoryDefinition.name, usm.profile, sdd.linkedProduct.recipeRequired, inv.listName," +
			"case when usm.productionUnit is NULL then 'NULL' else pd.productionUnitName end AS NAME, usm.packagingId  , usm.taxCode, usm.voDisContinuedFrom, usm.roDisContinuedFrom " +
			"from UnitSkuMapping usm, SkuDefinitionData sdd,InventoryListTypeData inv , ProductionUnitData  pd" +
			" where sdd.skuId = usm.skuId and usm.unitId = :unitId and usm.inventoryList = inv.id " +
			"and (usm.productionUnit is null or usm.productionUnit= pd.id ) ");
		query.setParameter("unitId", unitId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setMappingStatus((String) record[3]);
			o.setCategory((String) record[4]);
			o.setSubCategory((String) record[5]);
			if (record[6] == null) {
				o.setProfile("NULL");
			} else {
				o.setProfile((String) record[6]);}
			if(record[7] != null){
			o.setRecipeRequired(SCMUtil.getStatus((String) record[7]));}
			o.setInventoryList((String)record[8]);
			o.setProductionUnit((String) record[9]);
			o.setPackagingId((Integer) record[10]);
			o.setTaxCategoryCode((String) record[11]);
			if (Objects.nonNull(record[12])) {
				o.setVoDisContinuedFrom(AppUtils.getFormattedTime((Date) record[12], "yyyy-MM-dd"));
			}
			if (Objects.nonNull(record[13])) {
				o.setRoDisContinuedFrom(AppUtils.getFormattedTime((Date) record[13], "yyyy-MM-dd"));
			}
			list.add(o);
		});

		return list;
	}



	@Override
	public List<UnitSkuVendorMapping> searchActiveVendorMappingsForUnit(int unitId) {
		Query query = manager.createQuery("SELECT F FROM UnitSkuMapping E, UnitSkuVendorMapping F"
				+ " where F.unitSkuMapping.unitSkuMappingId=E.unitSkuMappingId and E.unitId = :unitId"
				+ " and E.mappingStatus = :status");
		query.setParameter("unitId", unitId).setParameter("status", SwitchStatus.ACTIVE.name());
		List<UnitSkuVendorMapping> vendorMappings = query.getResultList();
		return vendorMappings;
	}


    @Override
    public List<UnitSkuMapping> searchSkuMappingsForUnitAndVendor(int unitId, int vendorId) {
        Query query = manager.createQuery("SELECT DISTINCT E FROM UnitSkuMapping E, UnitSkuVendorMapping F"
                + " where F.unitSkuMapping.unitSkuMappingId=E.unitSkuMappingId and E.unitId = :unitId"
                + " and E.mappingStatus = :status and F.mappingStatus = :status and F.vendorId = :vendorId");
        query.setParameter("unitId", unitId)
                .setParameter("vendorId",vendorId)
                .setParameter("status", SwitchStatus.ACTIVE.name());
        List<UnitSkuMapping> skuMapping = query.getResultList();
        return skuMapping;
    }



	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#searchUnitMappingsForSku(int)
	 */
	@Override
	public List<IdCodeNameStatus> searchUnitMappingsForSku(int skuId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select distinct vd.unitId, vd.unitName,vd.unitStatus,vsm.mappingStatus,vd.unitCategory.name,inv.listName ," +
					"case when vsm.productionUnit is null then 'NULL' else pd.productionUnitName end as NAME , vsm.packagingId , vsm.taxCode " +
						", vsm.voDisContinuedFrom, vsm.roDisContinuedFrom" +
					" from UnitSkuMapping vsm, UnitDetailData vd,InventoryListTypeData inv ,ProductionUnitData pd" +
					" where vd.unitId = vsm.unitId and vsm.skuId = :skuId and vsm.inventoryList = inv.id" +
					" and (vsm.productionUnit is null or vsm.productionUnit=pd.id)");
		query.setParameter("skuId", skuId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setMappingStatus((String) record[3]);
			o.setCategory((String) record[4]);
			o.setInventoryList((String)record[5]);
			o.setProductionUnit((String) record[6]);
			o.setPackagingId((Integer) record[7]);
			o.setTaxCategoryCode((String) record[8]);
			if (Objects.nonNull(record[9])) {
				o.setVoDisContinuedFrom(AppUtils.getFormattedTime((Date) record[9], "yyyy-MM-dd"));
			}
			if (Objects.nonNull(record[10])) {
				o.setRoDisContinuedFrom(AppUtils.getFormattedTime((Date) record[10], "yyyy-MM-dd"));
			}
			list.add(o);
		});
		return list;
	}

	@Override
	public boolean updateSkuProfiles(Map<Integer,String> skuListWithInventoryListId, int unitId, String profile,Map<String,Integer> inventoryListName){
		updateSkuProfile(skuListWithInventoryListId.keySet().stream().collect(Collectors.toList()), unitId,profile);
		Set<Integer> skus = getAllSkusForUnit(unitId);

		skuListWithInventoryListId.forEach((skuID,inventoryName)->{
			if (!skus.contains(skuID)) {
				UnitSkuMapping mapping = new UnitSkuMapping();
				mapping.setUnitId(unitId);
				mapping.setSkuId(skuID);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setProfile(profile);
				mapping.setInventoryList(inventoryListName.get(inventoryName));
				manager.persist(mapping);
			}
		});
		manager.flush();
		scmCache.refreshProductRecipeKeyMapping();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForUnit(int,
	 * java.lang.String, int, java.util.List)
	 */
	@Override
	public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds) {
		activateUnitSkuMapping(employeeId, name, unitId, skuIds);
		deactivateUnitSkuMapping(employeeId, name, unitId, skuIds);
		Set<Integer> skus = getAllSkusForUnit(unitId);
		for (Integer skuId : skuIds) {
			if (!skus.contains(skuId)) {
				UnitSkuMapping mapping = new UnitSkuMapping();
				mapping.setUnitId(unitId);
				mapping.setSkuId(skuId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(name, employeeId));
				if(mapping.getProfile()==null)
				{
					mapping.setProfile("P0");
				}
				mapping.setInventoryList(1);
				//set default
				manager.persist(mapping);
			}
		}
		manager.flush();
		scmCache.refreshProductRecipeKeyMapping();
		return true;
	}

	@Override
	public List<UnitSkuMapping> getUnitSkuMappingForUnitId(int unitId,List<Integer> skuIds){
		if(skuIds.isEmpty()){
			return new ArrayList<>();
		}
		Query query=manager.createQuery("FROM UnitSkuMapping WHERE skuId IN ( :skuIds ) and unitId = :unitId");
		query.setParameter("skuIds",skuIds);
		query.setParameter("unitId",unitId);
		return query.getResultList();
	}

	@Override

	public List<UnitSkuMapping> getUnitSkuMappingForSKUS(int skuId,List<Integer> unitIds)
	{
		Query query=manager.createQuery("FROM UnitSkuMapping WHERE unitId IN ( :unitIds ) and skuId = :skuId");
		query.setParameter("skuId",skuId);
		query.setParameter("unitIds",unitIds);
		return query.getResultList();
	}

	@Override
	public List<UnitSkuMapping> getActiveUnitSkuMappings(List<Integer> skuIds ,List<Integer> unitIds){
		Query query=manager.createQuery("FROM UnitSkuMapping WHERE unitId IN ( :unitIds ) and skuId IN ( :skuIds ) " +
		" and mappingStatus = :status ");
		query.setParameter("skuIds",skuIds);
		query.setParameter("unitIds",unitIds);
		query.setParameter("status",StatusType.ACTIVE.value());
		return query.getResultList();
	}


	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateUnitMappingsForSku(int,
	 * java.lang.String, int, java.util.List)
	 */
	@Override
	public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds) {
		activateSkuUnitMapping(employeeId, name, skuId, unitIds);
		deactivateSkuUnitMapping(employeeId, name, skuId, unitIds);
		Set<Integer> units = getAllUnitsForSku(skuId);
		for (Integer unitId : unitIds) {
			if (!units.contains(unitId)) {
				UnitSkuMapping mapping = new UnitSkuMapping();
				mapping.setUnitId(unitId);
				mapping.setSkuId(skuId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(name, employeeId));
				if(mapping.getProfile()==null)
				{
					mapping.setProfile("P0");
				}
				mapping.setInventoryList(1);
				manager.persist(mapping);
			}
		}
		manager.flush();
		scmCache.refreshProductRecipeKeyMapping();
		return true;
	}

	private Set<Integer> getAllUnitsForSku(int skuId) {

		Set<Integer> units = new HashSet<>();
		Query query = manager.createQuery("select unitId from UnitSkuMapping where skuId = :skuId");
		query.setParameter("skuId", skuId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			units.addAll(list);
		}
		return units;
	}

	private Set<Integer> getAllSkusForUnit(int unitId) {

		Set<Integer> skus = new HashSet<>();
		Query query = manager.createQuery("select skuId from UnitSkuMapping where unitId = :unitId");
		query.setParameter("unitId", unitId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			skus.addAll(list);
		}
		return skus;
	}

	private boolean updateSkuProfile(List<Integer> skuIds, int unitId, String profiles) {
		if (skuIds.isEmpty()) {
			return true;
		}
		Query query = manager.createQuery(
				"update UnitSkuMapping set profile = :profile, updatedAt = :updateAt  where "
						+ " skuId IN ( :skuIds ) and unitId = :unitId and mappingStatus = :mappingStatus");
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("profile", profiles);
		query.setParameter("skuIds", skuIds);
		query.setParameter("unitId", unitId);
		query.setParameter("updateAt",AppUtils.getCurrentTimestamp());
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean activateUnitSkuMapping(int employeeId, String name, int unitId, List<Integer> skuIds) {
		if (skuIds.isEmpty()) {
			return true;
		}
		Query query = manager.createQuery(
				"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId IN ( :skuIds ) and unitId = :unitId and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuIds", skuIds);
		query.setParameter("unitId", unitId);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean deactivateUnitSkuMapping(int employeeId, String name, int unitId, List<Integer> skuIds) {
		Query query = null;
		if (skuIds.isEmpty()) {
			query = manager.createQuery(
					"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ "  unitId = :unitId and mappingStatus = :mappingStatus");
		} else {
			query = manager.createQuery(
					"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ " skuId NOT IN ( :skuIds ) and unitId = :unitId and mappingStatus = :mappingStatus");
			query.setParameter("skuIds", skuIds);
		}
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("unitId", unitId);
		query.executeUpdate();
		return true;
	}

	private boolean deactivateBusinessVendorMapping(int employeeId, String employeeName, int businessId, List<Integer> vendorIds) {
		Query query = null;
		if (vendorIds.isEmpty()) {
			query = manager.createQuery(
					"update businessVendorMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ "  businessId = :businessId and mappingStatus = :mappingStatus");
		} else {
			query = manager.createQuery(
					"update businessVendorMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ " vendorId NOT IN ( :vendorIds ) and businessId = :businessId and mappingStatus = :mappingStatus");
			query.setParameter("vendorIds", vendorIds);
		}
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("updatedBy", getName(employeeName, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("businessId", businessId);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean activateSkuUnitMapping(int employeeId, String name, int skuId, List<Integer> unitIds) {
		Query query = manager.createQuery(
				"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId = :skuId and unitId IN ( :unitIds) and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("unitIds", unitIds);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean deactivateSkuUnitMapping(int employeeId, String name, int skuId, List<Integer> unitIds) {
		Query query = manager.createQuery(
				"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId = :skuId and unitId NOT IN ( :unitIds) and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("unitIds", unitIds);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#searchSkuMappingsForVendor(int)
	 */
	@Override
	public List<IdCodeNameStatus> searchSkuMappingsForVendor(int vendorId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.skuId, vd.skuName,vd.skuStatus,vsm.mappingStatus, vd.linkedProduct.categoryDefinition.categoryName, " +
						"vd.linkedProduct.subCategoryDefinition.name, vsm.alias from VendorSkuMapping vsm, SkuDefinitionData vd where vd.skuId = vsm.skuId and vsm.vendorId = :vendorId");
		query.setParameter("vendorId", vendorId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setMappingStatus((String) record[3]);
			o.setCategory((String) record[4]);
			o.setSubCategory((String) record[5]);
			o.setAlias((String) record[6]);
			list.add(o);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#searchVendorMappingsForSku(int)
	 */
	@Override
	public List<IdCodeNameStatus> searchVendorMappingsForSku(int skuId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.vendorId, vd.entityName,vd.type,vd.status,vsm.mappingStatus from VendorSkuMapping vsm, VendorDetailData vd where vd.id = vsm.vendorId and vsm.skuId = :skuId");
		query.setParameter("skuId", skuId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setCategory((String) record[2]);
			o.setStatus((String) record[3]);
			o.setMappingStatus((String) record[4]);
			list.add(o);
		});
		return list;
	}

	@Override
	public List<IdCodeNameStatus> searchVendorMappingsForBusiness(int businessId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.vendorId, vd.entityName,vd.type,vd.status,vsm.mappingStatus from businessVendorMapping vsm, VendorDetailData vd where vd.id = vsm.vendorId and vsm.businessId = :businessId");
		query.setParameter("businessId", businessId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setCategory((String) record[2]);
			o.setStatus((String) record[3]);
			o.setMappingStatus((String) record[4]);
			list.add(o);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#allActiveUnits()
	 */
	@Override
	public List<IdCodeNameStatus> allActiveUnits() {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select ud.unitId, ud.unitName, ud.unitCategory, ud.unitStatus, ud.companyId, ud.unitRegion from UnitDetailData ud where ud.unitStatus = :status");
		query.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
            try{
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1] + "(" + masterDataCache.getCompany((Integer) record[4]).getShortCode() +")");
			o.setCode(((UnitCategoryData) record[2]).getCode());
			o.setCategory(((UnitCategoryData) record[2]).getName());
			o.setStatus((String) record[3]);
			o.setSubCategory((String) record[5]);
			list.add(o);
            }catch (Exception e){
                LOG.error("Error while fetching unit for {} ", record[4]);
            }
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#allActiveSKU()
	 */
	@Override
	public List<IdCodeNameStatus> allActiveSKU() {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select sdd.skuId, sdd.skuName,sdd.skuStatus, sdd.linkedProduct.categoryDefinition.categoryName, sdd.linkedProduct.subCategoryDefinition.name,inv.listName, sdd.linkedProduct.recipeRequired ,sdd.taxCategoryCode from SkuDefinitionData sdd,InventoryListTypeData inv where sdd.skuStatus = :status and sdd.inventoryList = inv.id ");
		query.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setCategory((String) record[3]);
			o.setSubCategory((String) record[4]);
			o.setInventoryList((String)record[5]);
			if(((String) record[6]).equals("Y")) {
				o.setRecipeRequired(true);
			}else {
				o.setRecipeRequired(false);
			}
			o.setTaxCategoryCode((String) record[7]);
			list.add(o);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#allActiveVendors()
	 */
	@Override
	public List<IdCodeNameStatus> allActiveVendors() {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery("select id, entityName, status,type,byPassContract from VendorDetailData where status = :status");
		query.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setCategory((String) record[3]);
			o.setByPassContract((String) record[4]);
			list.add(o);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	@Override
	public boolean updateVendorSkuMapping(int employeeId, String name, int vendorId, int skuId, String status) {
		Query query = manager.createQuery(
				"update VendorSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId = :skuId and vendorId = :vendorId");
		query.setParameter("status", status);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("vendorId", vendorId);
		query.executeUpdate();
		return true;
	}
	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean activateVendorSkuMapping(int employeeId, String name, int vendorId, List<Integer> skuIds) {
		Query query = manager.createQuery(
				"update VendorSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId IN ( :skuIds ) and vendorId = :vendorId and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuIds", skuIds);
		query.setParameter("vendorId", vendorId);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean activateSkuVendorMapping(int employeeId, String name, int skuId, List<Integer> vendorIds) {
		Query query = manager.createQuery(
				"update VendorSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId = :skuId and vendorId IN ( :vendorIds) and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("vendorIds", vendorIds);
		query.executeUpdate();
		return true;
	}

	private boolean activateBusinessVendorMapping(int employeeId, String name, int businessId, List<Integer> vendorIds) {
		if(!vendorIds.isEmpty()) {
			Query query = manager.createQuery(
					"update businessVendorMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ " businessId = :businessId and vendorId IN ( :vendorIds) and mappingStatus = :mappingStatus");
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
			query.setParameter("updatedBy", getName(name, employeeId));
			query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
			query.setParameter("businessId", businessId);
			query.setParameter("vendorIds", vendorIds);
			query.executeUpdate();
		}
		return true;
	}

	private Set<Integer> getAllVendorsForSku(int skuId) {

		Set<Integer> vendors = new HashSet<>();
		Query query = manager.createQuery("select vendorId from VendorSkuMapping where skuId = :skuId");
		query.setParameter("skuId", skuId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			vendors.addAll(list);
		}
		return vendors;
	}

	private Set<Integer> getAllVendorsForBusinessId(int businessId) {

		Set<Integer> vendors = new HashSet<>();
		Query query = manager.createQuery("select vendorId from businessVendorMapping where businessId = :businessId");
		query.setParameter("businessId", businessId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			vendors.addAll(list);
		}
		return vendors;
	}

	private Set<Integer> getAllSkusForVendor(int vendorId) {

		Set<Integer> skus = new HashSet<>();
		Query query = manager.createQuery("select skuId from VendorSkuMapping where vendorId = :vendorId");
		query.setParameter("vendorId", vendorId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			skus.addAll(list);
		}
		return skus;
	}

	@Override
	public String getName(String name, int id) {
		return name + " [" + id + "]";
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#addSkuMappingsForVendor(int,
	 * java.lang.String, int, java.util.List)
	 */
	@Override
	public boolean addSkuMappingsForVendor(int employeeId, String employeeName, int vendorId, List<IdCodeName> skuIds) {
		List<Integer> skuIdList = skuIds.stream().map(idCodeName -> {
			return idCodeName.getId();
		}).collect(Collectors.toList());
		activateVendorSkuMapping(employeeId, employeeName, vendorId, skuIdList);
		Set<Integer> skus = getAllSkusForVendor(vendorId);
		for (IdCodeName skuId : skuIds) {
			if (!skus.contains(skuId.getId())) {
				VendorSkuMapping mapping = new VendorSkuMapping();
				mapping.setVendorId(vendorId);
				mapping.setSkuId(skuId.getId());
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(employeeName, employeeId));
				if (skuId.getCode() != null) {
					mapping.setAlias(skuId.getCode());
				}
				manager.persist(mapping);
			}
		}
		manager.flush();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#addVendorMappingsForSku(int,
	 * java.lang.String, int, java.util.List)
	 */

	@Override
	public boolean addVendorMappingsForBusiness(int employeeId, String employeeName, int businessId, List<Integer> vendorIds) {
		activateBusinessVendorMapping(employeeId, employeeName, businessId, vendorIds);
		deactivateBusinessVendorMapping(employeeId, employeeName, businessId, vendorIds);
		Set<Integer> vendors = getAllVendorsForBusinessId(businessId);
		for (Integer vendorId : vendorIds) {
			if (!vendors.contains(vendorId)) {
				businessVendorMapping mapping = new businessVendorMapping();
				mapping.setVendorId(vendorId);
				mapping.setBusinessId(businessId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(employeeName, employeeId));
				manager.persist(mapping);
			}
		}
		manager.flush();
		return true;
	}

	@Override
	public boolean addVendorMappingsForSku(int employeeId, String employeeName, int skuId, List<Integer> vendorIds) {
		activateSkuVendorMapping(employeeId, employeeName, skuId, vendorIds);
		Set<Integer> vendors = getAllVendorsForSku(skuId);
		for (Integer vendorId : vendorIds) {
			if (!vendors.contains(vendorId)) {
				VendorSkuMapping mapping = new VendorSkuMapping();
				mapping.setVendorId(vendorId);
				mapping.setSkuId(skuId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(employeeName, employeeId));
				manager.persist(mapping);
			}
		}
		manager.flush();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#
	 * searchSkuMappingsForVendorAndUnit(int, int)
	 */
	@Override
	public List<UnitVendorSkuMapping> searchSkuMappingsForVendorAndUnit(int unitId, int vendorId) {
		List<UnitVendorSkuMapping> list = new ArrayList<UnitVendorSkuMapping>();
		VendorDetail vendor = cache.getVendorDetail(vendorId);
		IdCodeName data = SCMUtil.generateIdCodeName(vendor.getVendorId(), "", vendor.getEntityName());
		Query query = manager.createNativeQuery(
				"SELECT usm.UNIT_SKU_MAPPING_ID, usm.SKU_ID, sd.SKU_NAME, usvm.UNIT_SKU_VENDOR_MAPPING_ID, usvm.MAPPING_STATUS FROM "
						+ " UNIT_SKU_MAPPING usm INNER JOIN "
						+ " VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID INNER JOIN "
						+ " SKU_DEFINITION sd ON sd.SKU_ID = vsm.SKU_ID LEFT OUTER JOIN "
						+ " UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID AND usvm.VENDOR_ID = vsm.VENDOR_ID WHERE "
						+ " usm.UNIT_ID = :unitId AND usm.MAPPING_STATUS = :status "
						+ " and vsm.MAPPING_STATUS = :status AND vsm.VENDOR_ID = :vendorId");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("unitId", unitId);
		query.setParameter("vendorId", vendorId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			UnitVendorSkuMapping o = new UnitVendorSkuMapping();
			o.setUnitSkuMappingId((Integer) record[0]);
			o.setSku(SCMUtil.generateIdCodeName((Integer) record[1], "", (String) record[2]));
			o.setVendor(data);
			o.setKeyId((Integer) record[3]);
			String status = (String) record[4];
			if (status != null) {
				o.setStatus(SwitchStatus.valueOf(status));
			} else {
				o.setStatus(SwitchStatus.IN_ACTIVE);
			}
			list.add(o);
		});
		return list;
	}

	@Override
	public List<Integer> searchSkuMappingIdsForVendorAndUnit(int unitId, int vendorId) {
		List<Integer> list = new ArrayList<>();
		VendorDetail vendor = cache.getVendorDetail(vendorId);
		IdCodeName data = SCMUtil.generateIdCodeName(vendor.getVendorId(), "", vendor.getEntityName());
		Query query = manager.createNativeQuery("SELECT usm.SKU_ID FROM UNIT_SKU_MAPPING usm "
				+ "INNER JOIN VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID LEFT OUTER JOIN "
				+ "UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID AND usvm.VENDOR_ID = vsm.VENDOR_ID"
				+ "WHERE usm.UNIT_ID = :unitId AND usm.MAPPING_STATUS = :status "
				+ "AND vsm.MAPPING_STATUS = :status AND vsm.VENDOR_ID = :vendorId");
		query.setParameter("status", SwitchStatus.ACTIVE.name());
		query.setParameter("unitId", unitId);
		query.setParameter("vendorId", vendorId);
		list = query.getResultList();
		return list;
	}

	@Override
	public List<SkuPriceDetail> searchSkuPricesForVendorAndUnit(int unitId, int vendorId, Integer dispatchLocationId, Integer deliveryLocationId) {
		try {
			Query query = manager.createNativeQuery("SELECT usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION_ID,"
					+ " spd.DELIVERY_LOCATION_ID, spd.SKU_PRICE, vsm.SKU_ALIAS, spd.LEAD_TIME, spd.SKU_PRICE_DATA_ID FROM  UNIT_SKU_MAPPING usm "
					+ " INNER JOIN VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID "
					+ " INNER JOIN SKU_PRICE_DATA spd ON spd.SKU_ID = usm.SKU_ID AND vsm.VENDOR_ID = spd.VENDOR_ID "
					+ " INNER JOIN UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID "
					+ " AND usvm.VENDOR_ID = vsm.VENDOR_ID WHERE usm.UNIT_ID = :unitId "
					+ " AND usm.MAPPING_STATUS = :status AND vsm.MAPPING_STATUS = :status "
					+ " AND vsm.VENDOR_ID = :vendorId AND spd.STATUS = :status AND spd.DISPATCH_LOCATION_ID = :dispatchLocationId"
					+ " AND spd.DELIVERY_LOCATION_ID=:deliveryLocationId");

			query.setParameter("status", SwitchStatus.ACTIVE.name())
			.setParameter("unitId", unitId)
			.setParameter("vendorId", vendorId)
			.setParameter("dispatchLocationId",dispatchLocationId)
			.setParameter("deliveryLocationId",deliveryLocationId);
			List<Object[]> list = query.getResultList();

			/*usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION, spd.DELIVERY_LOCATION, spd.SKU_PRICE*/
			return createSkuPriceDetailList(list);
		} catch (Exception e) {
			LOG.error("Encountered error while getting prices for vendor and unit", e);
		}
		return null;
	}

	@Override
	public List<SkuPriceDetail> getSkuPackagingPriceForVendorsForUnit(int unitId, Set<Integer> vendorIds, Integer deliveryLocationId) {
		try {
			Query query = manager.createNativeQuery("SELECT usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION_ID,"
				+ " spd.DELIVERY_LOCATION_ID, spd.SKU_PRICE, vsm.SKU_ALIAS, spd.LEAD_TIME ,spd.SKU_PRICE_DATA_ID FROM  UNIT_SKU_MAPPING usm "
				+ " INNER JOIN VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID"
				+ " INNER JOIN SKU_PRICE_DATA spd ON spd.SKU_ID = usm.SKU_ID AND vsm.VENDOR_ID = spd.VENDOR_ID "
				+ " INNER JOIN UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID "
				+ " AND usvm.VENDOR_ID = vsm.VENDOR_ID WHERE usm.UNIT_ID = :unitId "
				+ " AND usm.MAPPING_STATUS = :status AND vsm.MAPPING_STATUS = :status and usvm.MAPPING_STATUS= :status"
				+ " AND vsm.VENDOR_ID IN :vendorIds AND spd.STATUS = :status"
				+ " AND spd.DELIVERY_LOCATION_ID=:deliveryLocationId");

			query.setParameter("status", SwitchStatus.ACTIVE.name())
				.setParameter("unitId", unitId)
				.setParameter("vendorIds", vendorIds)
				.setParameter("deliveryLocationId",deliveryLocationId);
			List<Object[]> list = query.getResultList();

			//usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION, spd.DELIVERY_LOCATION, spd.SKU_PRICE
			return createSkuPriceDetailList(list);
		} catch (Exception e) {
			LOG.error("Encountered error while getting prices for vendor and unit", e);
		}
		return null;
	}

	@Override
	public List<SkuPriceDetail> getSkuPackagingPriceForUnit(int unitId, Integer deliveryLocationId) {
		try {
			Query query = manager.createNativeQuery("SELECT usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION_ID,"
					+ " spd.DELIVERY_LOCATION_ID, spd.SKU_PRICE, vsm.SKU_ALIAS, spd.LEAD_TIME,spd.SKU_PRICE_DATA_ID FROM  UNIT_SKU_MAPPING usm "
					+ " INNER JOIN VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID"
					+ " INNER JOIN SKU_PRICE_DATA spd ON spd.SKU_ID = usm.SKU_ID "
					+ " INNER JOIN UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID "
					+ " AND usvm.VENDOR_ID = vsm.VENDOR_ID WHERE usm.UNIT_ID = :unitId "
					+ " AND usm.MAPPING_STATUS = :status AND vsm.MAPPING_STATUS = :status "
					+ " AND spd.STATUS = :status AND spd.DELIVERY_LOCATION_ID=:deliveryLocationId");

			query.setParameter("status", SwitchStatus.ACTIVE.name())
					.setParameter("unitId", unitId)
					.setParameter("deliveryLocationId",deliveryLocationId);
			List<Object[]> list = query.getResultList();

			//usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION, spd.DELIVERY_LOCATION, spd.SKU_PRICE
			return createSkuPriceDetailList(list);
		} catch (Exception e) {
			LOG.error("Encountered error while getting prices for vendor and unit", e);
		}
		return null;
	}
	@Override
	public List<unitSkuMappingDetail> getSkusProfileForUnit(int unitId, List<Integer> sku) {
		try {
			List<unitSkuMappingDetail> skuList = new ArrayList<>();
			Query query = manager.createNativeQuery("SELECT usm.UNIT_SKU_MAPPING_ID, usm.SKU_ID, usm.UNIT_ID, usm.MAPPING_STATUS, usm.PROFILE FROM  UNIT_SKU_MAPPING usm "
					+ "where usm.UNIT_ID =:unitId and usm.SKU_Id IN :skuId and usm.MAPPING_STATUS =:mappingStatus");

			query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name())
					.setParameter("unitId", unitId)
					.setParameter("skuId", sku);
			List<Object[]> list = query.getResultList();
			    list.stream().forEach((record) -> {
				unitSkuMappingDetail o = new unitSkuMappingDetail();
				o.setUnitSkuMappingId((Integer) record[0]);
				o.setSkuId((Integer) record[1]);
				o.setUnitId((Integer) record[2]);
				o.setMappingStatus((String) record[3]);
				o.setProfile((String) record[4]);
				skuList.add(o);
			});
			return skuList;
		} catch (Exception e) {
			LOG.error("Encountered error while getting profiles for vendor and unit", e);
		}
		return null;
	}

	private List<SkuPriceDetail> createSkuPriceDetailList(List<Object[]> list) throws TransferOrderCreationException {
		if (list != null && !list.isEmpty()) {
            List<SkuPriceDetail> skuPriceDetailList = new ArrayList<>();
            for(Object[] price : list){
                try{
                    SkuPriceDetail updatedPrice = new SkuPriceDetail();
                    updatedPrice.setDelivery(SCMDataConverter.convertToIdCodeName(masterDataCache.getLocationbyId( (Integer) price[4] )));
                    updatedPrice.setDispatch(SCMDataConverter.convertToIdCodeName(masterDataCache.getLocationbyId( (Integer) price[3] )));
                    updatedPrice.setPkg(SCMDataConverter.convertToPackagingData(cache.getPackagingDefinition((Integer) price[1])));
                    updatedPrice.setSku(SCMDataConverter.convertToIdCodeName(cache.getSkuDefinition((Integer) price[0])));
                    updatedPrice.setVendor(SCMDataConverter.convertToIdCodeName(cache.getVendorDetail((Integer) price[2])));
                    updatedPrice.setStatus(SwitchStatus.ACTIVE.name());
                    updatedPrice.setLeadTime((Integer) price[7]);
                    updatedPrice.getCurrent().setDate(SCMUtil.getCurrentTimestamp());
                    updatedPrice.getCurrent().setValue((BigDecimal) price[5]);
                    if(price[6] != null) {
						updatedPrice.getSku().setCode(price[6].toString());
					}
//					if (getSkuPriceHistoryForSku((Integer) price[8])){
//						skuPriceDetailList.add(updatedPrice);
//					}
					skuPriceDetailList.add(updatedPrice);
                }catch(Exception e){
                    String message = "Error while finding updated price for vendor sku price " + JSONSerializer.toJSON(price);
                    LOG.error(message, e);
                    throw new TransferOrderCreationException(new SCMError("Error while fetching price", message, 701));
                }
            }
            return skuPriceDetailList;
		}
		return null;
	}

	private boolean getSkuPriceHistoryForSku(Integer sku) {
		Query query = manager.createQuery("from SkuPriceHistory where skuPriceDataId=:skuPriceDataId " +
				"and changeType=:changeType order by 1 desc");
		query.setParameter("skuPriceDataId",sku);
		query.setParameter("changeType",ChangeType.PRICE_UPDATE.name());
		query.setMaxResults(1);
		if (!query.getResultList().isEmpty()) {
			//TODO Change check from status to vendor contract status when adopted fully
			SkuPriceHistory skuPriceHistory = (SkuPriceHistory) query.getResultList().get(0);
			Query query1 = manager.createQuery("FROM SkuPriceHistory where skuPriceDataId = :vendorSkuId and changeType =:changeType and recordStatus = :recordStatus order by 1 desc");
			query1.setParameter("vendorSkuId",skuPriceHistory.getSkuPriceDataId());
			query1.setParameter("recordStatus",PriceStatus.APPLIED.name());
			query1.setParameter("changeType",ChangeType.PRICE_UPDATE.name());
			query1.setMaxResults(1);
			List<SkuPriceHistory> history = query1.getResultList();
			if (!history.isEmpty() && history.get(0).getContractId()!=null) {
				VendorContractData vendorContractData = manager.find(VendorContractData.class, history.get(0).getContractId());
				return PriceStatus.APPLIED.name().equalsIgnoreCase(vendorContractData.getStatus());
			} else if (!history.isEmpty() && history.get(0).getContractId()==null){
				return true;
			}
			return false;
		}
		return false;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#
	 * updateSkuMappingsForVendorAndUnit(com.stpl.tech.scm.domain.model.
	 * UpdateUnitVendorSkuMapping)
	 */
	@Override
	public List<UnitVendorSkuMapping> updateSkuMappingsForVendorAndUnit(UpdateUnitVendorSkuMapping data) {
		UnitSkuMapping unitMapping = null;
		if (data.getMapping().getKeyId() == null) {
			// create new active mapping
			UnitSkuVendorMapping mapping = new UnitSkuVendorMapping();
			mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
			mapping.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
			mapping.setMappingStatus(AppConstants.ACTIVE);
			mapping.setVendorId(data.getMapping().getVendor().getId());
			unitMapping = manager.find(UnitSkuMapping.class, data.getMapping().getUnitSkuMappingId());
			mapping.setUnitSkuMapping(unitMapping);
			manager.persist(mapping);
		} else {
			UnitSkuVendorMapping mapping = manager.find(UnitSkuVendorMapping.class, data.getMapping().getKeyId());
			mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
			mapping.setUpdatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
			mapping.setMappingStatus(data.getMapping().getStatus().name());
			unitMapping = mapping.getUnitSkuMapping();
		}
		manager.flush();
		return searchSkuMappingsForVendorAndUnit(unitMapping.getUnitId(), data.getMapping().getVendor().getId());
	}

	@Override
	public List<PurchaseProfile> getPurchaseMappings(List<Integer> profiles) {
		Query query = manager.createQuery("FROM PurchaseProfile E WHERE E.roleId IN (:roles)");
		query.setParameter("roles",profiles);
		return query.getResultList();
	}

	@Override
	public boolean updateSkuLeadTime(int vendorId, int leadTime) {
		Query query = manager.createQuery(
				"update SkuPriceData SPD set SPD.leadTime =:leadTime  where  SPD.vendorId =:vendorId");
		query.setParameter("leadTime",leadTime);
		query.setParameter("vendorId",vendorId);
		query.executeUpdate();
		return true;
	}

	@Override
	public List<String> getDistanceOfUnits(int firstUnitId, int secondUnitId) {
		List<String> distances = new ArrayList<String>();
		int temp;
		for(int i = 0 ; i < 2 ; i++) {
			if( i == 1) {
				temp = firstUnitId;
				firstUnitId = secondUnitId;
				secondUnitId = temp;
			}
			try {
			Query query = manager.createQuery("SELECT E FROM UnitDistanceMappingData E where E.sourceUnitId=:firstUnitId AND E.destinationUnitId=:secondUnitId");
			query.setParameter("firstUnitId", firstUnitId).setParameter("secondUnitId", secondUnitId);
			 UnitDistanceMappingData data = (UnitDistanceMappingData) query.getSingleResult();
			 distances.add(data.getDistance().toString() +"-"+ data.getMappingId());
			}catch (Exception e) {
				distances.add(0 +"-"+ null);
			}
		}
		return distances;
	}

	@Override
	public boolean updateUnitDistanceMappingData(int firstUnitId, Integer firstMappingId, BigDecimal firstDistance,
												 int secondUnitId, Integer secondMappingId, BigDecimal secondDistance, boolean saveZipDis) throws SumoException {
		BigDecimal check = new BigDecimal(0);
		UnitDistanceMappingData data = new UnitDistanceMappingData();
		if(saveZipDis) {
			saveZipCodeDistance(firstUnitId, secondUnitId, firstDistance, secondDistance);
		}
		if(firstMappingId != null) {
			data.setMappingId(firstMappingId);
			data.setSourceUnitId(firstUnitId);
			data.setDestinationUnitId(secondUnitId);
			data.setDistance(firstDistance.equals(check) ?secondDistance : firstDistance);
			manager.merge(data);
			manager.flush();
		}else {
			data.setSourceUnitId(firstUnitId);
			data.setDestinationUnitId(secondUnitId);
			data.setDistance(firstDistance.equals(check) ?secondDistance : firstDistance);
			manager.persist(data);
		}
		UnitDistanceMappingData unitData = new UnitDistanceMappingData();
		if(secondMappingId != null) {
			unitData.setMappingId(secondMappingId);
			unitData.setSourceUnitId(secondUnitId);
			unitData.setDestinationUnitId(firstUnitId);
			unitData.setDistance(secondDistance.equals(check) ?firstDistance : secondDistance);
			manager.merge(unitData);
			manager.flush();
		} else {
			unitData.setSourceUnitId(secondUnitId);
			unitData.setDestinationUnitId(firstUnitId);
			unitData.setDistance(secondDistance.equals(check) ?firstDistance : secondDistance);
			manager.persist(unitData);
		}
		manager.flush();
		cache.refreshUnitDistanceMapping();
		return true;
	}

	@Override
	public void saveZipCodeDistance(int firstUnitId, int secondUnitId, BigDecimal firstDistance, BigDecimal secondDistance) throws SumoException {

		BigDecimal check = BigDecimal.ZERO;
		if(firstDistance.equals(check) && secondDistance.equals(check)){
			throw  new SumoException("for unit first distance and second distance both are zero");
		}
		String srcZipCode = masterDataCache.getUnit(firstUnitId).getAddress().getZipCode();
		String desZipCode = masterDataCache.getUnit(secondUnitId).getAddress().getZipCode();
		ZipCodeDistanceMapping res = findDistanceByZipCode(srcZipCode,desZipCode);
		BigDecimal dis = firstDistance.equals(check) ? secondDistance : firstDistance;

		if(res==null){
			ZipCodeDistanceMapping zipCodeDistanceMapping = new ZipCodeDistanceMapping();
			zipCodeDistanceMapping.setSourceZipCode(srcZipCode);
			zipCodeDistanceMapping.setDestinationZipCode(desZipCode);
			zipCodeDistanceMapping.setDistance(dis);
			manager.persist(zipCodeDistanceMapping);
		}else if (!res.getDistance().equals(dis)){
			res.setDistance(dis);
			manager.persist(res);
		}
		manager.flush();
		cache.refreshZipCodeDistanceMapping();
	}
	private ZipCodeDistanceMapping findDistanceByZipCode(String sourceZipCode, String destinationZipCode){
		Query query = manager.createQuery("FROM ZipCodeDistanceMapping as z where sourceZipCode=:srcZipCode and destinationZipCode=:desZipCode");
		query.setParameter("srcZipCode",sourceZipCode);
		query.setParameter("desZipCode",destinationZipCode);
		ZipCodeDistanceMapping res=null;
		try{
		 res	= (ZipCodeDistanceMapping)query.getSingleResult();
		}catch (NoResultException ignored){}
		if(res==null){
			query.setParameter("srcZipCode",destinationZipCode);
			query.setParameter("desZipCode",sourceZipCode);
			try{
				res	= (ZipCodeDistanceMapping)query.getSingleResult();
			}catch (NoResultException ignored){}
		}
		return res;
	}


	@Override
	public int getVendorSkuMappingId(int skuId,int vendorId){
		Query query = manager.createQuery("SELECT vendorSkuMappingId FROM VendorSkuMapping WHERE skuId=:skuId and vendorId=:vendorId  ");
		query.setParameter("skuId",skuId);
		query.setParameter("vendorId",vendorId);
		int vendorSkuMappingId=(Integer) query.getSingleResult();
		System.out.println("in dao impl"+vendorSkuMappingId);
		return vendorSkuMappingId;
	}

	@Override
	public ProductionUnitData findProductionLine(int productIonUnitId) {
		return manager.find(ProductionUnitData.class, productIonUnitId);
	}

	@Override
	public Integer findProductionLine(int unitId, int skuId) {
		Query query = manager.createQuery("SELECT usm.productionUnit FROM UnitSkuMapping usm WHERE usm.unitId=:unitId and usm.skuId=:skuId");
		query.setParameter("unitId", unitId);
		query.setParameter("skuId", skuId);
		try{
			Integer productionLine = (Integer) query.getSingleResult();
			LOG.info("production line is  {}", productionLine);
			if (productionLine == null) {
				productionLine = -1;
			}
			return productionLine;
		}catch(NoResultException e){
			LOG.info("No production Line ");
			return -1;
		}catch (NonUniqueResultException e){
			return (Integer) query.getResultList().get(0);
		}
	}

	@Override
	public int findSKUID(int productId) {
		Query query = manager.createQuery(
				"SELECT sdd.skuId FROM SkuDefinitionData sdd WHERE sdd.linkedProduct.productId=:productId and sdd.skuStatus = :skuStatus");
		query.setParameter("productId", productId);
		query.setParameter("skuStatus", ProductStatus.ACTIVE.name());
		try{
			return (int) query.getSingleResult();
		}catch (NonUniqueResultException e){
			return (int) query.getResultList().get(0);
		}
		//return (int) query.getSingleResult();
	}

	@Override
	public List<SkuDefinitionData> findAllSkuDefinition(int productId) {
		Query query=manager.createQuery("FROM SkuDefinitionData sdd  where sdd.linkedProduct.productId=:productId and sdd.skuStatus=:status ");
		query.setParameter("productId",productId);
		query.setParameter("status", AppConstants.ACTIVE);
		return (List<SkuDefinitionData>) query.getResultList();
	}

	@Override
	public UnitSkuMapping findSkuMappingBySkuAndUnit(Integer skuId,Integer unitId){
		Query query=manager.createQuery("FROM UnitSkuMapping usm  where usm.skuId = :skuId and usm.unitId = :unitId and usm.mappingStatus = :status ");
		query.setParameter("skuId",skuId).setParameter("unitId",unitId);
		query.setParameter("status", AppConstants.ACTIVE);
		return (UnitSkuMapping) query.getSingleResult();
	}

	@Override
	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMappingByStatus(Integer skuId,Integer packagingId,List<String> statuses){
		Query query=manager.createQuery("FROM SkuPackagingTaxMapping sptm  where sptm.skuId = :skuId and sptm.packagingId = :packagingId and sptm.mappingStatus in :statuses ");
		query.setParameter("skuId",skuId).setParameter("packagingId",packagingId).setParameter("statuses", statuses);
		List<SkuPackagingTaxMapping> resultList = query.getResultList();
		return Objects.nonNull(resultList) ? resultList : new ArrayList<>();
	}

	@Override
	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMappingByUnit(List<Integer> skuIds,Integer unitId){
		Query query=manager.createQuery("FROM SkuPackagingTaxMapping sptm  where sptm.skuId in :skuIds and sptm.unitId = :unitId and sptm.mappingStatus = :status ");
		query.setParameter("skuIds",skuIds).setParameter("unitId", unitId).setParameter("status",AppConstants.ACTIVE);
		List<SkuPackagingTaxMapping> resultList = query.getResultList();
		return Objects.nonNull(resultList) ? resultList : new ArrayList<>();
	}

	private Boolean activateUnitSkuPackagingTaxMapping(Integer employeeId, Integer skuId,Integer packagingId, List<Integer> unitIds) {
		if (unitIds.isEmpty()) {
			return true;
		}
		Query query = manager.createQuery(
				"update SkuPackagingTaxMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " unitId IN ( :unitIds ) and skuId = :skuId and packagingId = :packagingId and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", employeeId);
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("packagingId",packagingId);
		query.setParameter("skuId", skuId);
		query.setParameter("unitIds", unitIds);
		query.executeUpdate();
		return true;
	}

	private boolean deactivateUnitSkuPackagingTaxMapping(Integer employeeId, Integer skuId, Integer packagingId, List<Integer> unitIds) {
		Query query = null;
		if (unitIds.isEmpty()) {
			query = manager.createQuery(
					"update SkuPackagingTaxMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ "  skuId = :skuId  and packagingId = :packagingId  and mappingStatus = :mappingStatus");
		} else {
			query = manager.createQuery(
					"update SkuPackagingTaxMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ " unitId NOT IN ( :unitIds ) and skuId = :skuId and packagingId = :packagingId and mappingStatus = :mappingStatus");
			query.setParameter("unitIds", unitIds);
		}
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("updatedBy", employeeId);
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("packagingId", packagingId);
		query.executeUpdate();
		return true;
	}

	private Set<Integer> getAllUnitsForSku_PackagingId(Integer skuId, Integer packagingId) {

		Set<Integer> units = new HashSet<>();
		Query query = manager.createQuery("select unitId from SkuPackagingTaxMapping where skuId = :skuId and packagingId = :packagingId");
		query.setParameter("skuId", skuId).setParameter("packagingId",packagingId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			units.addAll(list);
		}
		return units;
	}

	@Override
	public Boolean updateUnitSkuPackagingTaxMapping(Integer employeeId, Integer skuId,Integer packagingId  , Map<Integer,String> unitTaxMap) {
		List<Integer> unitIds = new ArrayList<Integer>(unitTaxMap.keySet());
		activateUnitSkuPackagingTaxMapping(employeeId,skuId,packagingId,unitIds);
		deactivateUnitSkuPackagingTaxMapping(employeeId,skuId,packagingId,unitIds);
		Set<Integer> units = getAllUnitsForSku_PackagingId(skuId,packagingId);
		List<SkuPackagingTaxMapping> newMappings = new ArrayList<>();
		for (Integer unitId : unitIds) {
			if (!units.contains(unitId)) {
				SkuPackagingTaxMapping mapping= new SkuPackagingTaxMapping();
				mapping.setUnitId(unitId);
				mapping.setSkuId(skuId);
				mapping.setPackagingId(packagingId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(employeeId);
				mapping.setTaxCode(unitTaxMap.get(unitId));

				newMappings.add(mapping);
			}
		}
		if(!newMappings.isEmpty()){
			addAll(newMappings);
		}
		manager.flush();

		return true;
	}


	@Override
	public List<SkuPriceDetail> vendorPriceChangeAsPerStatus(Integer vendorId,List<String> status) {
		Query query = manager.createQuery("FROM SkuPriceData where isPriceChangeRequested = :isPriceChangeRequested and vendorId =:vendorId");
		query.setParameter("isPriceChangeRequested",AppConstants.YES);
		query.setParameter("vendorId",vendorId);
		List<SkuPriceData> data = query.getResultList();
		Map<Integer, SkuPriceData> vendorSkuId = data.stream()
				.collect(Collectors.toMap(SkuPriceData::getSkuPriceKeyId, sku -> sku));
		List<SkuPriceData> resultant = new ArrayList<>();
		for (Integer skuId : vendorSkuId.keySet()) {
			Query query1 = manager.createQuery("FROM SkuPriceHistory where skuPriceDataId = :vendorSkuId and changeType=:changeType order by 1 desc");
			query1.setParameter("vendorSkuId",skuId);
			query1.setParameter("changeType",ChangeType.PRICE_UPDATE.name());
			query1.setMaxResults(1);
			List<SkuPriceHistory> skuPriceHistories = query1.getResultList();
		skuPriceHistories.forEach(price -> {
			if (status.contains(price.getRecordStatus())) {
				SkuPriceData skuPriceData = vendorSkuId.get(price.getSkuPriceDataId());
				skuPriceData.getUpdateList().add(price);
				resultant.add(skuPriceData);
			}
			});
		}
		return convert(resultant, true, false);
	}

	public List<VendorContractData> getVendorContractV2(Integer vendorId, String status, Date startDate, Date endDate, Integer vendorContractId) {
		StringBuilder builder = new StringBuilder("FROM VendorContractData WHERE 1=1 ");
		if (Objects.nonNull(vendorId)) {
			builder.append( "AND vendorId =:vendorId ");
		}
		if (Objects.nonNull(status)) {
			builder.append("AND status IN(:status) ");
		}
		if (Objects.nonNull(startDate)) {
			builder.append("AND startDate >=:startDate ");
		}
		if (Objects.nonNull(endDate)) {
			builder.append("AND startDate <=:endDate ");
		}
		if (Objects.nonNull(vendorContractId)) {
			builder.append("AND contractId =:contractId ");
		}
		Query query = manager.createQuery(builder.toString());

		if (Objects.nonNull(status)) {
			query.setParameter("status",List.of(status));
		}
		if (Objects.nonNull(vendorId)) {
			query.setParameter("vendorId",vendorId);
		}
		if (Objects.nonNull(startDate)) {
			query.setParameter("startDate",startDate);
		}
		if (Objects.nonNull(endDate)) {
			query.setParameter("endDate",endDate);
		}
		if (Objects.nonNull(vendorContractId)) {
			query.setParameter("contractId",vendorContractId);
		}
		return (List<VendorContractData>) query.getResultList();
	}

	public boolean cancelVendorContractV2(WorkOrder workOrder, Integer loggedInUser) throws SumoException, EmailGenerationException {
		WorkOrderData woData = vendorContractDataDao.findWoById(workOrder.getWorkOrderId());
		if (Objects.isNull(woData)) {
			throw new SumoException("No Work order Present with work order Id  :: " + workOrder.getWorkOrderId());
		}
		if ( VendorContractStatus.getRejectionStatuses().contains(woData.getWorkOrderStatus().name()) ) {
			throw new SumoException("Contract in " + woData.getWorkOrderStatus().name() + " state !", "cannot able to cancel this");
		}

		updateContractStatusLog(woData.getWorkOrderStatus().name(), workOrder.getWorkOrderStatus().name(), loggedInUser, woData.getWorkOrderId(), LogType.WORK_ORDER, null);
		woData.setWorkOrderStatus(workOrder.getWorkOrderStatus());
		if(woData.getWorkOrderType().equals(WorkOrderType.DEFAULT)) {
			updateContractStatusLog(woData.getContractData().getStatus(), workOrder.getWorkOrderStatus().name(), loggedInUser, woData.getContractData().getContractId(), LogType.CONTRACT, null);
			woData.getContractData().setStatus(workOrder.getWorkOrderStatus().name());
		}

		// cancel all the items of work order
		try {
			cancelAllItems(woData.getVendorContractItemDataList(), woData.getWorkOrderStatus(), loggedInUser);
		} catch (Exception e) {
			LOG.error("Unable to cancel items of workOrder {}", woData.getWorkOrderId(), e);
		}

		cancelAllContractMailRequest(woData.getWorkOrderId(), PageRequestType.VENDOR_CONTRACT.name());
		cancelAllContractMailRequest(woData.getWorkOrderId(), PageRequestType.VENDOR_CONTRACT_EMPLOYEE.name());
		manager.flush();

		try {
			Integer userId = (loggedInUser != null && loggedInUser > 0) ? loggedInUser : woData.getCreatedBy();
			Integer sendMailToId = null;
			Integer secondMailId = null;
			if(Objects.equals(userId, woData.getCreatedBy())) {
				sendMailToId = woData.getApprovalRequestId();
			} else if( Objects.equals(userId, woData.getApprovalRequestId()) ){
				sendMailToId = woData.getCreatedBy();
			} else {
				sendMailToId = woData.getApprovalRequestId();
				secondMailId = woData.getCreatedBy();
			}

			String byName = Objects.nonNull(loggedInUser) && loggedInUser > 0 ? masterDataCache.getEmployeeBasicDetail(loggedInUser).getName() : null;
			if( Objects.nonNull(sendMailToId) && sendMailToId > 0 && Objects.nonNull(secondMailId) && secondMailId > 0 ) {
				EmployeeBasicDetail sendToDetails = masterDataCache.getEmployeeBasicDetail(sendMailToId);
				EmployeeBasicDetail secondMailDetails = masterDataCache.getEmployeeBasicDetail(secondMailId);
				if( StringUtils.hasText(sendToDetails.getEmailId()) ) {
					new UserContractEmailNotification("Vendor Price Rejection", woData.getWorkOrderStatus(), woData.getWorkOrderId(), scmCache.getVendorDetail(woData.getContractData().getVendorId()).getEntityName(), true, byName, secondMailDetails.getEmailId(), props.getEnvType()).sendEmail();
				}
				if( StringUtils.hasText(secondMailDetails.getEmailId()) ) {
					new UserContractEmailNotification("Vendor Price Rejection", woData.getWorkOrderStatus(), woData.getWorkOrderId(), scmCache.getVendorDetail(woData.getContractData().getVendorId()).getEntityName(), true, byName, sendToDetails.getEmailId(), props.getEnvType()).sendEmail();
				}
			} else if( Objects.nonNull(sendMailToId) && sendMailToId > 0 ) {
				EmployeeBasicDetail sendToDetails = masterDataCache.getEmployeeBasicDetail(sendMailToId);
				if( StringUtils.hasText(sendToDetails.getEmailId()) ) {
					new UserContractEmailNotification("Vendor Price Rejection", woData.getWorkOrderStatus(), woData.getWorkOrderId(), scmCache.getVendorDetail(woData.getContractData().getVendorId()).getEntityName(), true, byName, sendToDetails.getEmailId(), props.getEnvType()).sendEmail();
				}
			}
		} catch (Exception e) {
			LOG.error("Unable to send email for work order cancellation of workOrder ID {}", woData.getWorkOrderId(), e);
		}

		return true;
	}

	private void cancelAllItems(Set<VendorContractItemData> vendorContractItemDataList, VendorContractStatus workOrderStatus, Integer loggedInUser) {
		List<VendorContractItemData> list = new ArrayList<>();
		for(VendorContractItemData item : vendorContractItemDataList) {
			if(item.getStatus().equalsIgnoreCase(VendorContractStatus.REMOVED.name())) {
				continue;
			}
			updateContractStatusLog(item.getStatus(), workOrderStatus.name(), loggedInUser, item.getContractItemId(), LogType.CONTRACT_ITEM, "Item status has been rejected/cancelled" );
			item.setStatus(workOrderStatus.name());
			list.add(item);
		}
		update(list, false);
	}

	@Override
	public void updateContractStatusLog(String fromStatus, String toStatus, Integer updatedBy, Integer logTypeId, LogType logType, String logMsg) {
		VendorContractLogs contractStatusLog = new VendorContractLogs();
		contractStatusLog.setFromState(fromStatus);
		contractStatusLog.setToState(toStatus);
		if(Objects.isNull(updatedBy) || updatedBy <= 0) {
			updatedBy = AppConstants.SYSTEM_EMPLOYEE_ID;
		}
		contractStatusLog.setUpdatedBy(updatedBy);
		contractStatusLog.setLogType(logType);
		contractStatusLog.setLogTypeId(logTypeId);
		contractStatusLog.setLogMessage(logMsg);
		statusLogDao.save(contractStatusLog);
	}

	public void changeWosStatusOnContract(VendorContractData data, VendorContractStatus status, Integer userId) {
		List<WorkOrderData> workOrderDataList = new ArrayList<>();
		for(WorkOrderData wo : data.getWorkOrderDataSet()) {
			if(VendorContractStatus.getRejectionStatuses().contains(wo.getWorkOrderStatus().name())) {
				continue;
			}
			updateContractStatusLog(wo.getWorkOrderStatus().name(), status.name(), userId, wo.getWorkOrderId(), LogType.WORK_ORDER, "WorkOrder has been expired by system");
			wo.setWorkOrderStatus(status);
			workOrderDataList.add( wo );
		}
		if( !workOrderDataList.isEmpty() ) {
			vendorContractDataDao.update(workOrderDataList, false);
		}
	}

	private static SkuPriceHistory getSkuPriceHistory(VendorContractData data, VendorContractStatus status, VendorContractItemData item, SkuPriceData priceData) {
		SkuPriceHistory history = new SkuPriceHistory();
		history.setSkuPriceDataId(priceData.getSkuPriceKeyId());
		history.setRecordStatus(status.name());
		history.setContractId(data.getContractId());
		history.setNegotiatedPrice(item.getUpdatedPrice());
		history.setCurrentPrice(item.getCurrentPrice());
		history.setChangeType(ChangeType.PRICE_UPDATE.name());
		history.setCreatedAt(AppUtils.getCurrentTimestamp());
		history.setCreatedBy("SYSTEM [" + AppConstants.SYSTEM_EMPLOYEE_ID + "]");
		return history;
	}

	private SkuPriceData addNewSkuPriceData(VendorContractItemData item, Integer vendorId) {
		SkuPriceData skuPriceData = new SkuPriceData();
		skuPriceData.setSkuId(item.getSkuId());
		skuPriceData.setPackagingId(item.getSkuPackagingId());
		skuPriceData.setVendorId(vendorId);
		skuPriceData.setDeliveryLocationId(item.getDeliveryLocationId());
		skuPriceData.setDispatchLocationId(item.getDispatchLocationId());
		skuPriceData.setIsPriceChangeRequested(AppConstants.YES);
		skuPriceData.setStatus(VendorContractStatus.ACTIVE.name());
		skuPriceData.setPrice(item.getUpdatedPrice());
		skuPriceData.setLeadTime(15);
		return skuPriceDataDao.save(skuPriceData);
	}

	private void updateSkuPriceDataAndHistory(WorkOrderData workOrderData) {
		List<SkuPriceHistory> skuPriceHistoryList = new ArrayList<>();
		List<SkuPriceData> skuPriceDataList = new ArrayList<>();

		Map<String, SkuPriceData> skuPriceDataMap = new HashMap<>();
		skuPriceDataMap = workOrderData.getVendorContractItemDataList().stream()
				.filter(item -> VendorContractStatus.APPROVED.name().equals(item.getStatus()))
				.collect(Collectors.toMap(
						item -> SCMUtil.generateUniqueKey(item.getSkuId().toString(), item.getSkuPackagingId().toString(), item.getDeliveryLocationId().toString(), item.getDispatchLocationId().toString()),
						item -> new SkuPriceData(),
						(existing, replacement) -> existing
				));

		for(SkuPriceData skuPriceData : skuPriceDataDao.findByVendorId(workOrderData.getContractData().getVendorId())) {
			String KEY = SCMUtil.generateUniqueKey(String.valueOf(skuPriceData.getSkuId()), String.valueOf(skuPriceData.getPackagingId()), skuPriceData.getDeliveryLocationId().toString(), skuPriceData.getDispatchLocationId().toString());
			if(Objects.isNull(skuPriceData.getLeadTime())) {
				skuPriceData.setLeadTime(15);
			}
			if(skuPriceDataMap.containsKey(KEY)) {
				skuPriceDataMap.put(KEY, skuPriceData);
			}
		}
	
		for (VendorContractItemData item : workOrderData.getVendorContractItemDataList()) {
			if (item.getStatus().equals(VendorContractStatus.REMOVED.name())) {
				continue;
			}
			String KEY = SCMUtil.generateUniqueKey(item.getSkuId().toString(), item.getSkuPackagingId().toString(), item.getDeliveryLocationId().toString(), item.getDispatchLocationId().toString());
			SkuPriceData priceData = skuPriceDataMap.get(KEY);

			if (Objects.isNull(priceData) || Objects.isNull(priceData.getSkuPriceKeyId())) {
				priceData = addNewSkuPriceData(item, workOrderData.getContractData().getVendorId());
				skuPriceHistoryList.add( getSkuPriceHistory(workOrderData.getContractData(), VendorContractStatus.APPLIED, item, priceData) );
			} else {
				skuPriceHistoryList.add( getSkuPriceHistory(workOrderData.getContractData(), VendorContractStatus.APPLIED, item, priceData) );
				priceData.setStatus(VendorContractStatus.ACTIVE.name());
				priceData.setPrice(item.getUpdatedPrice());
				if(Objects.isNull(priceData.getLeadTime())) {
					priceData.setLeadTime(15);
				}
				skuPriceDataList.add(priceData);
			}
		}

		if(!skuPriceHistoryList.isEmpty()) {
			vendorContractDataDao.update(skuPriceHistoryList, false);
		}
		if(!skuPriceDataList.isEmpty()) {
			skuPriceDataDao.saveAll(skuPriceDataList);
		}

	}

	private void saveEvent(VendorContract vendorContract, VendorContractInfo vendorContractInfo, SkuPriceHistory item) {
		SkuPriceHistory price = new SkuPriceHistory();
		price.setCreatedAt(AppUtils.getCurrentTimestamp());
		price.setCreatedBy(getName(Objects.nonNull(vendorContract.getEmployeeName()) ? vendorContract.getEmployeeName() : AppConstants.SYSTEM_EMPLOYEE_NAME.toUpperCase(),
				Objects.nonNull(vendorContract.getEmployeeId()) ? vendorContract.getEmployeeId() : AppConstants.SYSTEM_EMPLOYEE_ID));
		price.setCurrentPrice(item.getCurrentPrice());
//					price.setNegotiatedPrice(item.getNegotiatedPrice());
		price.setSkuPriceDataId(item.getSkuPriceDataId());
		price.setContractId(vendorContract.getVendorContractId());
		price.setChangeType(ChangeType.PRICE_UPDATE.name());
		price.setStartDate(vendorContractInfo.getStartDate());
		price.setEndDate(vendorContractInfo.getEndDate());
		price.setRecordStatus(PriceStatus.DEACTIVATED.name());
		manager.persist(price);
		SkuPriceData skuPriceData = manager.find(SkuPriceData.class, item.getSkuPriceDataId());
		skuPriceData.setStartDate(vendorContractInfo.getStartDate());
		skuPriceData.setEndDate(vendorContractInfo.getEndDate());
		skuPriceData.setStatus(AppConstants.IN_ACTIVE);
		skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
		skuPriceData.setPrice(item.getCurrentPrice());
	}

	@Override
	public void applyContractV2() {
		List<WorkOrderData> workOrderDataList = vendorContractDataDao.getWorkOrdersByDateAndStatus(AppUtils.getBusinessDate(), null, List.of(VendorContractStatus.VENDOR_APPROVED, VendorContractStatus.APPROVER_BY_PASSED));
		if(CollectionUtils.isEmpty(workOrderDataList)) {
			return;
		}
		workOrderDataList.forEach(data -> {
			if( !VendorContractStatus.getRejectionStatuses().contains(data.getContractData().getStatus()) ) {
				try {
					applyWorkOrder(data, AppConstants.SYSTEM_EMPLOYEE_ID);
				} catch (Exception exp) {
					LOG.error("Exception in applyContractV2 ::  {}", exp.getMessage(), exp);
				}
			}
		});
	}

	@SneakyThrows
	public void makeMappingOnContract(WorkOrderData woData) {
		updateSkuPriceDataAndHistory(woData);

		Integer vendorId  = woData.getContractData().getVendorId();
		Set<VendorContractItemData> vendorContractItemDataList = woData.getVendorContractItemDataList();

		vendorToSkuMappingV2( vendorContractItemDataList, vendorId );
		unitToSkuMappingV2( vendorContractItemDataList, vendorId );
		EmployeeBasicDetail createdByDetails = masterDataCache.getEmployeeBasicDetail(woData.getCreatedBy());
		if( StringUtils.hasText(createdByDetails.getEmailId()) ) {
			new UserContractEmailNotification("Vendor Price APPLIED", VendorContractStatus.APPLIED, woData.getContractData().getContractId(), scmCache.getVendorDetail(woData.getContractData().getVendorId()).getEntityName(), true, createdByDetails.getName(), createdByDetails.getEmailId(), props.getEnvType()).sendEmail();
		}
	}

	@SneakyThrows
	private void vendorToSkuMappingV2(Set<VendorContractItemData> itemDataList, Integer vendorId) {
		try {
			Set<Integer> skuIds = itemDataList.stream()
					.filter(item -> !item.getStatus().equals(VendorContractStatus.REMOVED.name()))
					.map(VendorContractItemData::getSkuId)
					.collect(Collectors.toSet());
			if(CollectionUtils.isEmpty(skuIds)) {
				return;
			}
			Query query = manager.createQuery("FROM VendorSkuMapping where vendorId = :vendorId and skuId IN (:skuIds)");
			query.setParameter("vendorId", vendorId);
			query.setParameter("skuIds", skuIds);
			List<VendorSkuMapping> mappings = query.getResultList();

			List<VendorSkuMapping> changedMappingList = new ArrayList<>();

			for(VendorSkuMapping map : mappings) {
				if(map.getMappingStatus().equals(AppConstants.IN_ACTIVE)) {
					map.setMappingStatus(AppConstants.ACTIVE);
					map.setUpdatedAt(AppUtils.getCurrentTimestamp());
					map.setUpdatedBy(AppConstants.SYSTEM_EMPLOYEE_NAME + "[" + AppConstants.SYSTEM_EMPLOYEE_ID + "]");
				}
				skuIds.remove(Integer.valueOf(map.getSkuId()));
				changedMappingList.add(map);
			}
			for(Integer skuId: skuIds) {
				VendorSkuMapping mapping = new VendorSkuMapping();
				mapping.setSkuId(skuId);
				mapping.setVendorId(vendorId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(AppConstants.SYSTEM_EMPLOYEE_NAME + "[" + AppConstants.SYSTEM_EMPLOYEE_ID + "]");
				changedMappingList.add(mapping);
			}
			addAll(changedMappingList);
		} catch (Exception e) {
			LOG.error("Error in vendorToSkuMappingV2() for vendorId {} :: " , vendorId, e);
			throw new SumoException("error : ", e.getMessage());
		}
	}

	@SneakyThrows
	private void unitToSkuMappingV2(Set<VendorContractItemData> itemDataList, Integer vendorId) {
		try {
			Map<Integer, Set<Integer>> skuAndUnitsMap = itemDataList.stream()
					.filter(item -> !item.getStatus().equals(VendorContractStatus.REMOVED.name()))
					.collect(Collectors.toMap(
							VendorContractItemData::getSkuId,
							item -> item.getVendorContractItemUnitDataList().stream()
									.map(VendorContractItemUnitData::getUnitId)
									.collect(Collectors.toSet()),
							(existingUnits, newUnits) -> {
								existingUnits.addAll(newUnits);
								return existingUnits;
							}
					));

			Set<Integer> unitIds = skuAndUnitsMap.values().stream()
					.flatMap(Set::stream)
					.collect(Collectors.toSet());

			Query query = manager.createQuery(
					"FROM UnitSkuMapping where skuId IN (:skuIds) and unitId IN (:unitIds)");
			query.setParameter("unitIds", unitIds)
					.setParameter("skuIds", skuAndUnitsMap.keySet());
			List<UnitSkuMapping> unitSkuMappingList = query.getResultList();

			Map<Integer, Map<Integer, UnitSkuMapping>> skuUnitIdUnitSkuMapping = new HashMap<>();

			for(UnitSkuMapping mapping : unitSkuMappingList) {
				Integer skuId = mapping.getSkuId();
				Integer unitId = mapping.getUnitId();
				if(!skuUnitIdUnitSkuMapping.containsKey(skuId)) {
					skuUnitIdUnitSkuMapping.put(skuId, new HashMap<>());
				}
				if(!skuUnitIdUnitSkuMapping.get(skuId).containsKey(unitId)) {
					if(mapping.getMappingStatus().equals(AppConstants.IN_ACTIVE)) {
						mapping.setMappingStatus(AppConstants.ACTIVE);
						mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
						mapping.setUpdatedBy(AppConstants.SYSTEM_EMPLOYEE_NAME + "[" + AppConstants.SYSTEM_EMPLOYEE_ID + "]");
					}
					skuUnitIdUnitSkuMapping.get(skuId).put(unitId, mapping);
				}
			}

			for(Integer skuId : skuAndUnitsMap.keySet()) {
				if(!skuUnitIdUnitSkuMapping.containsKey(skuId)) {
					skuUnitIdUnitSkuMapping.put(skuId, new HashMap<>());
					for(Integer unitId : skuAndUnitsMap.get(skuId)) {
						skuUnitIdUnitSkuMapping.get(skuId).put(unitId, addNewUnitSkuMapping(skuId, unitId));
					}
				} else {
					for(Integer unitId : skuAndUnitsMap.get(skuId)) {
						if(!skuUnitIdUnitSkuMapping.get(skuId).containsKey(unitId)) {
							skuUnitIdUnitSkuMapping.get(skuId).put(unitId, addNewUnitSkuMapping(skuId, unitId));
						}
					}
				}
			}

			List<UnitSkuMapping> unitSkuMappingList1 = skuUnitIdUnitSkuMapping.values().stream()
					.flatMap(map -> map.values().stream())
					.toList();

			unitSkuMappingList1 = vendorContractDataDao.addAll(unitSkuMappingList1);

			List<UnitSkuVendorMapping> unitSkuVendorMappingList = new ArrayList<>();

			for(UnitSkuMapping unitSkuMapping : unitSkuMappingList1) {
				unitSkuVendorMappingList.add(unitSkuVendorMappingV2(unitSkuMapping, vendorId));
			}

			vendorContractDataDao.addAll(unitSkuVendorMappingList);
		} catch (Exception e) {
			LOG.error("Error in unitToSkuMappingV2() for vendorId" + vendorId, e);
			throw new SumoException("error : ", e.getMessage());
		}
	}

	public UnitSkuMapping addNewUnitSkuMapping(Integer skuId, Integer unitId) {
		UnitSkuMapping mapping = new UnitSkuMapping();
		mapping.setUnitId(unitId);
		mapping.setSkuId(skuId);
		mapping.setMappingStatus(AppConstants.ACTIVE);
		mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
			mapping.setCreatedBy(AppConstants.SYSTEM_EMPLOYEE_NAME + "[" + AppConstants.SYSTEM_EMPLOYEE_ID + "]");
		if (scmCache.getSkuDefinition(skuId).getInventoryList() == null) {
			mapping.setInventoryList(getInventoryListCode("ALL DAYS"));
		} else {
			mapping.setInventoryList(scmCache.getSkuDefinition(skuId).getInventoryList());
		}
		mapping.setPackagingId(getDefaultPackagingId(skuId));
//		mapping.setTaxCode(contract.getTaxCode());
		mapping.setProfile("P0");
		return mapping;
	}

	private UnitSkuVendorMapping unitSkuVendorMappingV2(UnitSkuMapping mapping, Integer vendorId) {
		UnitSkuVendorMapping unitSkuVendorMapping = null;
		for(UnitSkuVendorMapping map : mapping.getVendors()) {
			if(map.getVendorId() == vendorId) {
				unitSkuVendorMapping = map;
			}
		}
		if(Objects.isNull(unitSkuVendorMapping)) {
			unitSkuVendorMapping = new UnitSkuVendorMapping();
			unitSkuVendorMapping.setUnitSkuMapping(mapping);
			unitSkuVendorMapping.setVendorId(vendorId);
			unitSkuVendorMapping.setMappingStatus(VendorContractStatus.ACTIVE.name());
			unitSkuVendorMapping.setCreatedAt(AppUtils.getCurrentTimestamp());
			unitSkuVendorMapping.setCreatedBy(AppConstants.SYSTEM_EMPLOYEE_NAME + "[" + AppConstants.SYSTEM_EMPLOYEE_ID + "]");
			return unitSkuVendorMapping;
		} else if(unitSkuVendorMapping.getMappingStatus().equals(VendorContractStatus.IN_ACTIVE.name())) {
			unitSkuVendorMapping.setMappingStatus(VendorContractStatus.ACTIVE.name());
			unitSkuVendorMapping.setUpdatedBy(AppConstants.SYSTEM_EMPLOYEE_NAME + "[" + AppConstants.SYSTEM_EMPLOYEE_ID + "]");
			unitSkuVendorMapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
			return unitSkuVendorMapping;
		}
		return unitSkuVendorMapping;
	}


	private Integer getDefaultPackagingId(Integer skuId) {
		Integer defaultPackaging;
		List<SkuPackagingMapping> mappings = scmCache.getSkuPackagingMappings(skuId);
		for(SkuPackagingMapping mapping : mappings){
			if(mapping.isIsDefault()){
				defaultPackaging = mapping.getPackagingId();
				return defaultPackaging;
			}
		}
		return null;
	}

	private Integer getInventoryListCode(String inventoryListName) {
		Query query = manager.createQuery("From InventoryListTypeData where listName = :listName");
		query.setParameter("listName",inventoryListName);
		List<InventoryListTypeData> list = query.getResultList();
		if(!list.isEmpty()){
			Integer inventoryListCode =Integer.valueOf(list.get(0).getListCode());
			return inventoryListCode;
		}
		return null;
	}

	public void expiryContractV2() {
		List<VendorContractData> dataList = vendorContractDataDao.getContractSByDateAndStatus(null, AppUtils.getBusinessDate(), VendorContractStatus.APPLIED.name());
		if(CollectionUtils.isEmpty(dataList)) {
			return;
		}
		List<VendorContractData> contractDataList = new ArrayList<>();
		dataList.forEach(data -> {
			try {
				updateContractStatusLog(data.getStatus(), VendorContractStatus.EXPIRED.name(), AppConstants.SYSTEM_EMPLOYEE_ID, data.getContractId(), LogType.CONTRACT, "Contract has been expired by system");
				data.setStatus(VendorContractStatus.EXPIRED.name());
				contractDataList.add(data);
				changeWosStatusOnContract(data, VendorContractStatus.EXPIRED, AppConstants.SYSTEM_EMPLOYEE_ID);
				EmployeeBasicDetail createdByDetails = masterDataCache.getEmployeeBasicDetail(data.getCreatedBy());
				if( StringUtils.hasText(createdByDetails.getEmailId()) ) {
					new UserContractEmailNotification("Vendor Price EXPIRED", VendorContractStatus.EXPIRED, data.getContractId(), scmCache.getVendorDetail(data.getVendorId()).getEntityName(), true, createdByDetails.getName(), createdByDetails.getEmailId(), props.getEnvType()).sendEmail();
				}
			} catch (Exception exp) {
				LOG.error("Exception in expiryContractV2 :: ", exp);
			}
		});
		vendorContractDataDao.update(contractDataList, false);
	}

	@Override
	public PageRequestDetail findByToken(String token) throws VendorRegistrationException {
		try {
			Query query = manager.createQuery("FROM PageRequestDetail E"
					+ " WHERE E.authKey = :authKey order by E.requestDate desc");
			query.setParameter("authKey", token);
			PageRequestDetail result = (PageRequestDetail) query.getSingleResult();
			if (result != null) {
				if (VendorStatus.INITIATED.equals(VendorStatus.valueOf(result.getRecordStatus()))) {
					return result;
				} else {
					throw new VendorRegistrationException(
							"Not a valid request. Please contact Chaayos to resolve conflicts.");
				}
			}
		} catch (Exception e) {
			LOG.error("Exception occurred while fetching request", e);
			throw new VendorRegistrationException("Could not find request", e);
		}
		return null;
	}

	@Override
	public PageRequestDetail findPRDByToken(String token) {
		try {
			Query query = manager.createQuery("FROM PageRequestDetail E"
					+ " WHERE E.authKey = :authKey order by E.requestDate desc");
			query.setParameter("authKey", token);
			return  (PageRequestDetail) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Exception occurred while fetching page request", e);
		}
		return null;
	}

	@Override
	public PageRequestDetail findPageRequestByEventTypeAndRecordStatus(String vendorContractEmployee, Integer vendorContractId) {
		try {
			Query query = manager.createQuery("from PageRequestDetail where recordStatus = :recordStatus and eventId = :vendorContractId and eventType= :eventType order by id desc");
			query.setParameter("recordStatus", VendorContractStatus.COMPLETED.name());
			query.setParameter("vendorContractId", vendorContractId);
			query.setParameter("eventType", vendorContractEmployee);

			List<PageRequestDetail> pageRequestDetail = query.getResultList();
			if(!CollectionUtils.isEmpty(pageRequestDetail)) {
				return pageRequestDetail.get(0);
			}
			return null;
		} catch (NoResultException e ) {
			return null;
		}
	}

	@Override
	public void cancelAllContractMailRequest(Integer vendorContractId, String eventType) {
		try {
			Query query = manager.createQuery("from PageRequestDetail where recordStatus = :recordStatus and eventId = :vendorContractId and eventType= :eventType");
			query.setParameter("recordStatus", VendorStatus.INITIATED.name());
			query.setParameter("vendorContractId", vendorContractId);
			query.setParameter("eventType", eventType);

			List<PageRequestDetail> pageRequestDetail = query.getResultList();
			pageRequestDetail.forEach( request -> {
				request.setRecordStatus(VendorContractStatus.IN_ACTIVE.name());
			});
		} catch (NoResultException e ) {

		}
	}

	@Override
	public String getPageRequestLink(Integer contractId, String eventType) {
		try {
			Query query = manager.createQuery("from PageRequestDetail where eventId = :vendorContractId and eventType= :eventType order by id desc");
			query.setParameter("vendorContractId", contractId);
			query.setParameter("eventType", eventType);

			List<PageRequestDetail> pageRequestDetail = query.getResultList();
			if(CollectionUtils.isEmpty(pageRequestDetail)) {
				return null;
			}
			return SCMUtil.getRegistrationLink(pageRequestDetail.get(0).getRegistrationUrl(), pageRequestDetail.get(0).getAuthKey());
		} catch (Exception e ) {
			LOG.error("Error while trying to get Page request link : {}", e.getMessage());
		}
		return null;
	}

	@Override
	public SkuPriceData getSkuPriceData(int skuPriceDataId) {
		Query query = manager.createQuery("FROM SkuPriceData where skuPriceKeyId = :skuPriceDataId order by 1 desc");
		query.setParameter("skuPriceDataId",skuPriceDataId);
		List<SkuPriceData> skuPriceDataList = query.getResultList();
		if(skuPriceDataList.size() > 0){
			SkuPriceData skuPriceData = skuPriceDataList.get(0);
			return skuPriceData;
		}
		return null;
	}

	@Override
	public List<PreviousPricingDataVO> findPreviousPricesOfSkuByLocation(Integer skuId, List<Integer> listOfUnitIds, Integer packagingId) {
		try {
			Query query = manager.createNativeQuery("SELECT " +
					"poid.TOTAL_COST, " +
					"poid.REQUESTED_QUANTITY, "+
					"poid.UNIT_PRICE, " +
					"ROUND(poid.UNIT_PRICE/poid.PACKAGING_CONVERSION_RATIO, 2), " +
					"poid.PACKAGING_NAME, " +
					"v.ENTITY_NAME, " +
					"po.GENERATION_TIME " +
					"FROM PURCHASE_ORDER po " +
					"INNER JOIN PURCHASE_ORDER_ITEM_DETAIL poid ON po.PURCHASE_ORDER_ID = poid.PURCHASE_ORDER_ID " +
					"INNER JOIN VENDOR_DETAIL_DATA v ON po.GENERATED_FOR_VENDOR_ID = v.VENDOR_ID " +
					"WHERE poid.SKU_ID = :skuId AND po.PURCHASE_ORDER_STATUS = :status " +
					"AND po.DELIVERY_LOCATION_ID IN (:listOfUnitIds) ORDER BY poid.PURCHASE_ORDER_ITEM_ID DESC LIMIT 10 ");

			query.setParameter("skuId", skuId);
			query.setParameter("status", "CLOSED");
			query.setParameter("listOfUnitIds", listOfUnitIds);

			List<Object[]> l = query.getResultList();
			List<PreviousPricingDataVO> previousPricingDataVOList = new ArrayList<>();
			double conversionRatio = scmCache.getPackagingDefinition(packagingId).getConversionRatio();

			for(Object[] o : l) {
				double o3 = ((BigDecimal) o[3]).doubleValue();
				double priceForRequiredPackaging = o3 * conversionRatio;
				previousPricingDataVOList.add(new PreviousPricingDataVO(
						((BigDecimal) o[0]).doubleValue(), ((BigDecimal) o[1]).doubleValue(),
						((BigDecimal) o[2]).doubleValue(), (String) o[4], priceForRequiredPackaging, (String) o[5], (Date) o[6])
				);
			}
			return previousPricingDataVOList;

		} catch (Exception e) {
			LOG.error("Error : ", e);
		}
		return null;
	}

	@SneakyThrows
	public void applyWorkOrder(WorkOrderData woData, Integer user) {
		try {
			if(woData.getWorkOrderType().equals(WorkOrderType.DEFAULT)) {
				VendorContractData previousActiveContract = vendorContractDataDao.changeStatusOfAContract(woData.getContractData().getVendorId(), VendorContractStatus.APPLIED.name(), VendorContractStatus.DEACTIVATED.name());
				if(Objects.nonNull(previousActiveContract)) {
					updateContractStatusLog(VendorContractStatus.APPLIED.name(), previousActiveContract.getStatus(), user, previousActiveContract.getContractId(), LogType.CONTRACT, "Vendor contract has been deactivated");
					changeWosStatusOnContract(previousActiveContract, VendorContractStatus.DEACTIVATED, user);
					EmployeeBasicDetail createdByDetails = masterDataCache.getEmployeeBasicDetail(previousActiveContract.getCreatedBy());
					if( StringUtils.hasText(createdByDetails.getEmailId()) ) {
						new UserContractEmailNotification("Vendor Price has been DEACTIVATED", VendorContractStatus.DEACTIVATED, previousActiveContract.getContractId(), scmCache.getVendorDetail(previousActiveContract.getVendorId()).getEntityName(), true, createdByDetails.getName(), createdByDetails.getEmailId(), props.getEnvType()).sendEmail();
					}
				}
			}

			updateContractStatusLog(woData.getWorkOrderStatus().name(), VendorContractStatus.APPLIED.name(), user, woData.getWorkOrderId(), LogType.WORK_ORDER, null);
			woData.setWorkOrderStatus(VendorContractStatus.APPLIED);
			if( WorkOrderType.DEFAULT.equals(woData.getWorkOrderType()) ) {
				VendorContractData contractData = woData.getContractData();
				updateContractStatusLog(contractData.getStatus(), VendorContractStatus.APPLIED.name(), user, contractData.getContractId(), LogType.CONTRACT, null);
				contractData.setStatus(VendorContractStatus.APPLIED.name());
				vendorContractDataDao.update(contractData, false);
			}
			vendorContractDataDao.update(woData, false);
			makeMappingOnContract(woData);

			// sending email for applied contract
			try {
				VendorDetail vendorDetail = scmCache.getVendorDetail(woData.getContractData().getVendorId());

				DocumentDetailData documentDetailData = find(DocumentDetailData.class, woData.getWoApprovalMetaData().getVendorSignedDocumentId() != null ? woData.getWoApprovalMetaData().getVendorSignedDocumentId() : woData.getWoApprovalMetaData().getAuthSignedDocumentId());
				VendorContractEmailNotificationTemplate emailTemplate = new VendorContractEmailNotificationTemplate( vendorDetail.getEntityName(),
						props.getBasePath(), null, "VendorContractEmailAppliedTemplate.html", null, null);
				String subject = vendorDetail.getEntityName().toUpperCase() + (woData.getWorkOrderType().equals(WorkOrderType.DEFAULT) ? " Vendor Contract " : " Work Order ") + "Proposal by STPL";
				VendorContractEmailNotification emailNotification = new VendorContractEmailNotification(emailTemplate,props.getEnvType(),
						SCMUtil.getArray(masterDataCache.getEmployeeBasicDetail(woData.getApprovalRequestId()).getEmailId(), vendorDetail.getPrimaryEmail()),
						subject);

				List<AttachmentData> attachments = new ArrayList<>();
				String fileUrl = documentDetailData.getFileUrl();
				AttachmentData attachmentData = SCMUtil.getAttachmentDoc(fileUrl);
				attachments.add( attachmentData );
				emailNotification.sendRawMail(attachments);
			} catch (Exception e) {
				LOG.error("Error in sending applied contract email for work order id {} : ", woData.getWorkOrderId(), e);
			}
		} catch (Exception e) {
			LOG.error("Error in applyWorkOrder() for work order id {} : ", woData.getWorkOrderId(), e);
			throw new SumoException("error : ", e.getMessage());
		}
	}


}
