package com.stpl.tech.scm.data.model;


import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "PLAN_ORDER_ITEM")
public class PlanOrderItemData {

    private Integer id;
    private int productId;
    private String productName;
    private BigDecimal requestedQuantity;
    private BigDecimal availableQuantity;
    private String unitOfMeasure;
    private String category;
    private String itemType;// requested/additional
    private BigDecimal unitPrice;
    private BigDecimal amount;
    private int eventId;
    private Integer printCount;
    private Integer productionUnit;
    private BigDecimal bufferedPercentage;
    private BigDecimal bufferedQuantity;
    private BigDecimal totalQuantity;
    private String isRecipeRequired;
    private BigDecimal excessQuantity;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PLAN_ITEM_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME", nullable = false)
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "REQUESTED_QUANTITY", nullable = false)
    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    public void setRequestedQuantity(BigDecimal requestedQuantity) {
        this.requestedQuantity = requestedQuantity;
    }

    @Column(name = "AVAILABLE_QUANTITY", nullable = false)
    public BigDecimal getAvailableQuantity() {
        return availableQuantity;
    }

    public void setAvailableQuantity(BigDecimal requestedAbsoluteQuantity) {
        this.availableQuantity = requestedAbsoluteQuantity;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = false, length = 20)
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @Column(name = "UNIT_PRICE", nullable = true)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal negotiatedUnitPrice) {
        this.unitPrice = negotiatedUnitPrice;
    }

    @Column(name = "CALCULATED_AMOUNT", nullable = true)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal calculatedAmount) {
        this.amount = calculatedAmount;
    }

    @Column(name = "CATEGORY", nullable = false)
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    @Column(name = "ITEM_TYPE", nullable = false)
    public String getItemType() {
        return itemType;
    }

    public void setItemType(String type) {
        this.itemType = type;
    }

    @Column(name = "PLAN_EVENT_ID", nullable = false)
    public int getEventId() {
        return eventId;
    }

    public void setEventId(int eventId) {
        this.eventId = eventId;
    }

    @Column(name = "PRINT_COUNT")
    public Integer getPrintCount() {
        return printCount;
    }

    public void setPrintCount(Integer printCount) {
        this.printCount = printCount;
    }

    @Column(name="PRODUCTION_UNIT")
    public Integer getProductionUnit() {
        return productionUnit;
    }

    public void setProductionUnit(Integer productionUnit) {
        this.productionUnit = productionUnit;
    }

    @Column(name = "BUFFERED_PERCENTAGE", nullable = false)
    public BigDecimal getBufferedPercentage() {
        return bufferedPercentage;
    }

    public void setBufferedPercentage(BigDecimal bufferedPercentage) {
        this.bufferedPercentage = bufferedPercentage;
    }

    @Column(name = "BUFFERED_QUANTITY", nullable = false)
    public BigDecimal getBufferedQuantity() {
        return bufferedQuantity;
    }

    public void setBufferedQuantity(BigDecimal bufferedQuantity) {
        this.bufferedQuantity = bufferedQuantity;
    }

    @Column(name = "TOTAL_QUANTITY", nullable = false)
    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    @Column(name = "RECIPE_REQUIRE", nullable = false,length = 1)
    public String getIsRecipeRequired() {
        return isRecipeRequired;
    }

    public void setIsRecipeRequired(String isRecipeRequired) {
        this.isRecipeRequired = isRecipeRequired;
    }


    @Column(name = "EXCESS_QUANTITY", nullable = false)
    public BigDecimal getExcessQuantity() {
        return excessQuantity;
    }

    public void setExcessQuantity(BigDecimal excessQuantity) {
        this.excessQuantity = excessQuantity;
    }
}
