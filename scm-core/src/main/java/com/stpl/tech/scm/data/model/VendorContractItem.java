package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "VENDOR_CONTRACT_ITEM_OLD")
public class VendorContractItem {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CONTRACT_ITEM_ID", unique = true, nullable = false)
    private Integer contractItemId;
    @Column(name = "VENDOR_CONTRACT_ID", nullable = false)
    private Integer vendorContractId;
    @Column(name = "VENDOR_ID", nullable = false)
    private Integer vendorId;
    @Column(name = "SKU_ID", nullable = false)
    private Integer skuId;
    @Column(name = "SKU_PACKAGING_ID", nullable = false)
    private Integer skuPackagingId;
    @Column(name = "SKU_PRICE_DATE_ID", nullable = false)
    private int skuPriceDataId;
    @Column(name = "DISPATCH_LOCATION", nullable = false)
    private String dispatchLocation;
    @Column(name = "DELIVERY_LOCATION", nullable = false)
    private String deliveryLocation;
    @Column(name = "CURRENT_PRICE")
    private BigDecimal currentPrice;
    @Column(name = "NEGOTIATED_PRICE")
    private BigDecimal negotiatedPrice;
    @Column(name = "TAX_CODE")
    private String taxCode;
    @Column(name = "TAX_PERCENTAGE")
    private BigDecimal taxPercentage;
    @Temporal(TemporalType.DATE)
    @Column(name = "START_DATE", nullable = false)
    private Date startDate;
    @Temporal(TemporalType.DATE)
    @Column(name = "END_DATE", nullable = false)
    private Date endDate;
    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;
    @Column(name = "IS_NEW", nullable = false)
    private String isNew;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false)
    private Date creationTime;

    @Column(name = "IS_NEW_ITEM",nullable = false)
    private String isNewItem;

}
