package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.RiderRoutePlanStatusEnum;
import com.stpl.tech.scm.domain.model.RiderRouteTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

@Entity
@Getter
@Setter
@Table(name = "DAY_WISE_ORDER_DATA")
@AllArgsConstructor
@NoArgsConstructor
public class DayWiseOrderData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DAY_WISE_ORDER_DATA_ID")
    private Integer dayWiseOrderDataId;

    @Column(name = "RO_ORDERING_DAYS_ID")
    private Integer roOrderingDaysId;

    @Column(name = "PRODUCT_ID")
    private Integer productId;

    @Column(name = "AVERAGE_CONSUMPTION_FOR_THE_DAY")
    private BigDecimal averageConsumption;

    @Column(name = "SUGGESTED_QUANTITY_TO_ORDER")
    private BigDecimal suggestedQuantityToOrder;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ORDER_DATE")
    private Date orderDate;

    @Column(name = "CONSUMPTION_DATA", columnDefinition = "TEXT")
    private String consumptionData;

}
