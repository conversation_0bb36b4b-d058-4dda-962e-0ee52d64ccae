/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "VENDOR_COMPLIANCE_DATA")
public class VendorComplianceData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "VENDOR_COMPLIANCE_ID",nullable = false,unique = true)
    private Integer vendorComplianceId;

    @Column(name = "YEAR")
    private Integer year;

    @Column(name = "MONTH")
    private Integer month;

    @Column(name = "COMPLIANCE_KEY")
    private String complianceKey;

    @Column(name = "COMPLIANCE_TYPE")
    private String complianceType;

    @Column(name = "FINANCIAL_YEAR")
    private String financialYear;

    @Column(name = "RETRY_API")
    private String retryApi;

    @Column(name = "KEY_ID")
    private Integer keyId;

    @Column(name = "LOGGED_MESSAGE")
    private String loggedMessage;

    @Column(name = "LAST_VALIDATION_TRY_AT")
    private Date lastValidationTryAt;

    public Integer getVendorComplianceId() {
        return this.vendorComplianceId;
    }

    public void setVendorComplianceId(Integer vendorComplianceId) {
        this.vendorComplianceId = vendorComplianceId;
    }

    public Integer getYear() {
        return this.year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return this.month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public String getComplianceKey() {
        return complianceKey;
    }

    public void setComplianceKey(String complianceKey) {
        this.complianceKey = complianceKey;
    }

    public String getComplianceType() {
        return this.complianceType;
    }

    public void setComplianceType(String complianceType) {
        this.complianceType = complianceType;
    }

    public String getFinancialYear() {
        return financialYear;
    }

    public void setFinancialYear(String financialYear) {
        this.financialYear = financialYear;
    }

    public String getRetryApi() {
        return retryApi;
    }

    public void setRetryApi(String retryApi) {
        this.retryApi = retryApi;
    }

    public Integer getKeyId() {
        return this.keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    public String getLoggedMessage() {
        return loggedMessage;
    }

    public void setLoggedMessage(String loggedMessage) {
        this.loggedMessage = loggedMessage;
    }

    public Date getLastValidationTryAt() {
        return lastValidationTryAt;
    }

    public void setLastValidationTryAt(Date lastValidationTryAt) {
        this.lastValidationTryAt = lastValidationTryAt;
    }
}
