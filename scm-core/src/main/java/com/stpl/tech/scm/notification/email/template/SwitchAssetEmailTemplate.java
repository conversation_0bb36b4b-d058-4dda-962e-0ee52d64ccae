package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.data.transport.model.FullfillmentDataUnitLevel;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataWarehouseLevel;
import com.stpl.tech.scm.data.transport.model.SwitchAssetEventData;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.MathTool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SwitchAssetEmailTemplate extends AbstractVelocityTemplate {
   SwitchAssetEventData switchAssetEventData;

    String basePath;

    public SwitchAssetEmailTemplate(){

    }

    public SwitchAssetEmailTemplate(SwitchAssetEventData switchAssetEventData,String basePath){
        this.switchAssetEventData = switchAssetEventData;
        this.basePath = basePath;
    }
    @Override
    public String getTemplatePath() {
        return "templates/SwitchAssetTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath+"/switch-asset/switch-asset-temp-"+System.currentTimeMillis()+".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String,Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("detail",switchAssetEventData);
        return stringObjectMap;
    }

}
