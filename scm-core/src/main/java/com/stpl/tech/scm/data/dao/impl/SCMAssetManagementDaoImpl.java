package com.stpl.tech.scm.data.dao.impl;

import com.google.api.client.util.DateTime;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.stpl.tech.util.EnvType.*;

/**
 * Created by Abhishek Sirohi on 07-05-2016.
 */
@Repository
public class SCMAssetManagementDaoImpl extends SCMAbstractDaoImpl implements SCMAssetManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(SCMAssetManagementDaoImpl.class);

    @Autowired
    private SCMCache scmCache;

    @Override
    public List<AssetDefinitionData> getAllAssetFromUnitWithStatus(int unitId, String assetStatus) {
        Query query = manager.createQuery("FROM AssetDefinitionData a " +
                "WHERE a.unitId = :unitId and a.assetStatus = :assetStatus");
        query.setParameter("unitId", unitId);
        query.setParameter("assetStatus", assetStatus);
        return query.getResultList();
    }


    @Override
    public List<AssetDefinitionData> viewAllAssetsSlimFromUnitByName(int unitId, String name) {
        Query query = manager.createQuery("FROM AssetDefinitionData a " +
            "WHERE a.unitId = :unitId and a.assetName LIKE :assetName");
        query.setParameter("unitId", unitId);
        query.setParameter("assetName", "%"+name+"%");
        return query.getResultList();
    }

    @Override
    public List<AssetDefinitionData> getAllAssetFromUnit(int unitId) {
        Query query = manager.createQuery(" select  distinct  a FROM AssetDefinitionData a left join  fetch a.product as productAlias " +
                "left join fetch productAlias.subCategoryDefinition WHERE a.unitId = :unitId");
       // Query query = manager.createQuery("FROM AssetDefinitionData a  WHERE a.unitId = :unitId");
        query.setParameter("unitId", unitId);
        return query.getResultList();
    }

    @Override
    public List<FixedAssetCompactDefinition> getAllFixedAssetFromUnit(int unitId , List<String> statusList) {
        List<FixedAssetCompactDefinition> response = new ArrayList<>();
        Query query = manager.createNativeQuery(
                " SELECT DISTINCT  A.ASSET_ID, A.ASSET_NAME, A.ASSET_STATUS, " +
                " A.TAG_VALUE, A.SKU_ID, A.PRODUCT_ID, S.SUB_CATEGORY_NAME, P.CLASSIFICATION_ID " +
                " FROM ASSET_DEFINITION A LEFT JOIN PRODUCT_DEFINITION AS P " +
                " ON A.PRODUCT_ID = P.PRODUCT_ID " +
                " LEFT JOIN SUB_CATEGORY_DEFINITION S " +
                " ON P.SUB_CATEGORY_ID = S.SUB_CATEGORY_ID " +
                " WHERE A.UNIT_ID = :unitId " +
                " AND (A.IS_IN_TRANSIT != :isInTransit OR A.IS_IN_TRANSIT IS NULL) " +
                " AND A.ASSET_STATUS IN :statusList");
        query.setParameter("unitId", unitId);
        query.setParameter("isInTransit", 'Y');
        query.setParameter("statusList", statusList);
        List<Object[]> result = query.getResultList();
        for(Object[] o: result){
            response.add(new FixedAssetCompactDefinition((Integer)o[0], (String)o[1], (String)o[2],
                    (String)o[3], (Integer)o[4], (Integer)o[5], (String) o[6], (Integer) o[7]));
        }
        return response;
    }

    @Override
    public List<AssetDefinitionData> getAllAssetFromUnitByProductIds(int unitId , List<Integer> productIds) {
        Query query = manager.createQuery(" select  distinct  a FROM AssetDefinitionData a left join  fetch a.product as productAlias " +
                "left join fetch productAlias.subCategoryDefinition WHERE a.unitId = :unitId and  a.product.productId in :productIds");
        // Query query = manager.createQuery("FROM AssetDefinitionData a  WHERE a.unitId = :unitId");
        query.setParameter("unitId", unitId);
        query.setParameter("productIds",productIds);
        return query.getResultList();
    }

    @Override
    public List<AssetDefinitionData> getAllAssetWithGRItemId(int grItemId) {
        Query query = manager.createQuery("FROM AssetDefinitionData a WHERE a.grItemId = :grItemId");
        query.setParameter("grItemId", grItemId);
        return query.getResultList();
    }

    @Override
    public AssetTransferMappingData getLatestAssetDepreciationMapping(int assetId) {
        Query query = manager.createQuery("FROM AssetTransferMappingData a WHERE a.assetId = :assetId ORDER BY a.assetTransferMappingId DESC ");
        query.setParameter("assetId", assetId);
        List<AssetTransferMappingData> list = query.getResultList();
        if (list != null & !list.isEmpty()) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public AssetDefinitionData getAssetDefinitionDataByAssetId(int assetId) {
        Query query = manager.createQuery("FROM AssetDefinitionData a where a.assetId = :assetId");
        query.setParameter("assetId",assetId);
        try{
            AssetDefinitionData assetDefinitionData = (AssetDefinitionData)query.getSingleResult();
            return assetDefinitionData;
        } catch( NoResultException e){
            LOG.info("Could not found asset with assetId : " + assetId);
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String getMaximumAssetTagValue() {
        Query query = manager.createQuery("FROM AssetDefinitionData a ORDER BY a.tagValue DESC");

        List<AssetDefinitionData> result = query.getResultList();
        if (result.size() == 0) {
            return null;
        }
        AssetDefinitionData asset = result.get(0);
        return asset.getTagValue();
    }

    @Override
    public String getMaxToNoteUniqueSequence() {
        Query query = manager.createQuery("FROM TransferOrderNoteData a ORDER BY a.transferOrderNoteId DESC");
        List<TransferOrderNoteData> result = query.getResultList();
        if (result.size() == 0) {
            return null;
        }
        return result.get(0).getUniqueSequence();
    }

    @Override
    public AssetDefinitionData getAssetByTagValue(String tagValue) {
        Query query = manager.createQuery("FROM AssetDefinitionData a WHERE  a.tagValue =:tagValue");
        query.setParameter("tagValue", tagValue);
        try {
            AssetDefinitionData assetDefinitionData = (AssetDefinitionData) query.getSingleResult();
            return assetDefinitionData;
        } catch (NoResultException e) {
            LOG.info("Could not found asset with tagValue " + tagValue + " " + e.getStackTrace());
            return null;
        }
    }

    @Override
    public List<ProfileAttributeMappingData> getProfileAttributeMappingByProfileId(int profileId, String status) {
        Query query = manager.createQuery("FROM ProfileAttributeMappingData p where p.profileId = :profileId and p.status = :status");
        query.setParameter("profileId", profileId);
        query.setParameter("status", status);
        return query.getResultList();
    }

    @Override
    public StockEventDefinitionData getLatestNSOEventByUnit(int unitId, String eventStatus ,String subtype) {
        try{
            StringBuilder queryString = new StringBuilder("FROM StockEventDefinitionData a WHERE " +
                    "a.unitId = :unitId and " +
                    "a.eventStatus = :eventStatus and " +
                    "a.subType = :subtype " +
                    "order by eventCreationDate DESC");
            Query query = manager.createQuery(queryString.toString()).setMaxResults(1);

            query.setParameter("unitId", unitId);
            query.setParameter("eventStatus", eventStatus);
            query.setParameter("subtype",subtype);

            return (StockEventDefinitionData) query.getSingleResult();
        }
        catch (Exception e)
        {
            LOG.info("Error occur while getting NSO event");
        }
        return null ;
    }

    @Override
    public List<StockEventDefinitionData> getEventByUnit(int unitId, String eventStatus , Integer roId) {
        StringBuilder queryString = new StringBuilder("FROM StockEventDefinitionData a WHERE a.unitId = :unitId and a.eventStatus = :eventStatus ");
        if(Objects.nonNull(roId)){
            queryString.append("and a.roId = :roId ");
        }
//        if(Objects.nonNull(userId)){
//            queryString.append("and a.initiatedBy = :userId ");
//        }
        queryString.append("and a.parentId is null ");
        Query query = manager.createQuery(queryString.toString());

        query.setParameter("unitId", unitId);
        query.setParameter("eventStatus", eventStatus);
        if(roId != null){
            query.setParameter("roId",roId);
        }
//        if(userId != null){
//            query.setParameter("userId",userId);
//        }
        return query.getResultList();
    }
    @Override
    public List<StockEventDefinitionData> getNsoCompletedEvent(int unitId){
         Query query  = manager.createQuery("FROM StockEventDefinitionData a WHERE a.unitId = :unitId and a.eventStatus = 'COMPLETED' and a.subType = 'NSO' order by 1 desc");
         query.setParameter("unitId",unitId);

         return  (List<StockEventDefinitionData>) query.getResultList();
    }

    @Override
    public List<StockEventDefinitionData> getLastStockTakeEventByAsset(Integer assetId, String eventStatus){
         Query query  = manager.createQuery("SELECT sed FROM StockEventDefinitionData sed left join StockEventAssetMappingDefinitionData seam" +
                 " ON sed.eventId = seam.eventId WHERE" +
                 " sed.eventStatus = :eventStatus and" +
                 " seam.assetId = :assetId" +
                 " order by sed.eventId desc").setMaxResults(1);
         query.setParameter("eventStatus",eventStatus);
         query.setParameter("assetId",assetId);

         return  (List<StockEventDefinitionData>) query.getResultList();
    }


    @Override
    public List<StockEventDefinitionData> getParentOrChildEvent(Integer eventId, Integer parentId, String subCategory) {
        List<StockEventDefinitionData> result = new ArrayList<>();
        StringBuilder queryString = new StringBuilder("FROM StockEventDefinitionData a WHERE " );
        if(Objects.nonNull(eventId)){
            queryString.append("a.eventId = :eventId and ");
        }
        if(Objects.nonNull(parentId)){
            queryString.append("a.parentId = :parentId and ");
        }
        if(Objects.nonNull(subCategory)){
            queryString.append("a.subCategory = :subCategory and ");
        }

        queryString.append("a.eventStatus NOT IN (:eventStatus) ");
        Query query = manager.createQuery(queryString.toString());

        if(Objects.nonNull(eventId)){
            query.setParameter("eventId", eventId);
        }
        if(Objects.nonNull(parentId)){
            query.setParameter("parentId", parentId);
        }
        if(Objects.nonNull(subCategory)){
            query.setParameter("subCategory", subCategory);
        }

        query.setParameter("eventStatus", Arrays.asList(StockEventStatusType.ABANDONED.value()));

        result =  (List<StockEventDefinitionData>) query.getResultList();
        return result;
    }

    @Override
    public List<GatepassItemAssetMappingData> getAssociatedAssetMappingForGatepassItemId(int gatePassItemId) {
        Query query = manager.createQuery("FROM GatepassItemAssetMappingData a WHERE a.gatePassItemId = :gatePassItemId");
        query.setParameter("gatePassItemId", gatePassItemId);
        return query.getResultList();
    }

    @Override
    public AssetDefinitionDataLog getLatestAssetDefinitionDataLog(int assetId) {
        Query query = manager.createQuery("FROM AssetDefinitionDataLog a WHERE a.assetId =:assetId ORDER BY a.assetDefinitionLogId DESC");
        query.setParameter("assetId", assetId);
        query.setMaxResults(1);
        Object result = null;
        try{
            result = query.getSingleResult();
            AssetDefinitionDataLog asset = (AssetDefinitionDataLog) result;
            return asset;
        } catch(NoResultException e){
            LOG.info("Could not found Asset Definition LOG for assetId " + assetId);
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public List<EntityAssetMappingData> getAssociatedEntityAssetMapping(int entityId, int entitySubId, String entityType) {
        Query query = manager.createQuery("FROM EntityAssetMappingData a WHERE a.entityId = :entityId and a.entitySubId = :entitySubId and a.entityType = :entityType");
        query.setParameter("entityId", entityId);
        query.setParameter("entitySubId", entitySubId);
        query.setParameter("entityType", entityType);
        return query.getResultList();
    }

    @Override
    public List<AssetTransferMappingData> getAssetTransferMapping(int assetId, Date startDate, Date endDate) {
        Query query = manager.createQuery("FROM AssetTransferMappingData a WHERE a.assetId = :assetId and " +
                "((DATE(a.startDate) <= DATE(:startDate) and DATE(a.endDate) >= DATE(:endDate))" +
                " or (DATE(a.startDate) <= DATE(:startDate) and DATE(a.endDate) <= DATE(:endDate) and DATE(a.endDate) >= DATE(:startDate))" +
                " or (DATE(a.startDate) >= DATE(:startDate) and DATE(a.endDate) <= DATE(:endDate))" +
                " or (DATE(a.startDate) >= DATE(:startDate) and DATE(a.endDate) >= DATE(:endDate) and DATE(a.startDate) <= DATE(:endDate))" +
                " or (DATE(a.startDate) <= DATE(:endDate) and a.endDate IS NULL))");
        query.setParameter("assetId", assetId);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        return query.getResultList();
    }

    @Override
    public List<AssetDefinitionData> getAssetForDepreciation(Date actualEndDate) {
        Query query = manager.createQuery(
                "FROM AssetDefinitionData a WHERE  a.startDate IS NOT NULL and " +
                        "( a.actualEndDate IS NUll OR (DATE(a.actualEndDate) >= DATE(:actualEndDate)))");
        query.setParameter("actualEndDate", actualEndDate);
        return query.getResultList();
    }

    @Override
    public AssetTransferMappingData getLastEntryWithStatus(int assetId, String status) {
        Query query = manager.createQuery("FROM AssetTransferMappingData a WHERE a.assetId = :assetId and a.assetStatus = :status");
        query.setParameter("assetId", assetId);
        query.setParameter("status", status);
        List<AssetTransferMappingData> result = query.getResultList();
        if (result != null && !result.isEmpty()) {
            return result.get(0);
        }
        return null;
    }

    @Override
    public List<AssetRecoveryDefinitionData> getAllAssetInRecoveryFromUnit(int unitId, String recoveryStatus) {
        Query query = manager.createQuery("FROM AssetRecoveryDefinitionData a WHERE a.unitId =:unitId and a.recoveryStatus =:recoveryStatus");
        query.setParameter("unitId", unitId);
        query.setParameter("recoveryStatus", recoveryStatus);
        return query.getResultList();
    }

    @Override
    public List<AssetRecoveryData> getAssetsInRecoveryFromUnit(Integer recoveryId, Integer unitId, String recoveryStatus, Integer assetId, Date startDate, Date endDate) {
        StringBuilder queryString = new StringBuilder("FROM AssetRecoveryData a WHERE a.creationDate <=:endDate");
        if(Objects.nonNull(recoveryId)){
            queryString.append(" and a.recoveryId =:recoveryId");
        }
        if(Objects.nonNull(unitId)){
            queryString.append(" and a.unitId =:unitId");
        }
        if(Objects.nonNull(recoveryStatus)){
            queryString.append(" and a.recoveryStatus =:recoveryStatus");
        }
        if(Objects.nonNull(assetId)){
            queryString.append(" and a.assetId =:assetId");
        }
        if(Objects.nonNull(startDate)){
            queryString.append(" and a.creationDate >=:startDate");
        }
        Query query = manager.createQuery(queryString.toString());
        if(Objects.isNull(endDate)){
            query.setParameter("endDate", AppUtils.getDate(SCMUtil.getCurrentTimestamp()));
        }else{
            query.setParameter("endDate", endDate);
        }
        if(Objects.nonNull(recoveryId)){
            query.setParameter("recoveryId", recoveryId);
        }
        if(Objects.nonNull(unitId)){
            query.setParameter("unitId", unitId);
        }
        if(Objects.nonNull(recoveryStatus)){
            query.setParameter("recoveryStatus", recoveryStatus);
        }
        if(Objects.nonNull(assetId)){
            query.setParameter("assetId", assetId);
        }
        if(Objects.nonNull(startDate)){
            query.setParameter("startDate", startDate);
        }
        return (List<AssetRecoveryData>) query.getResultList();
    }

    @Override
    public List<AssetDepreciationMappingData> getDepreciationRegisteredFromToDateOnUnit(int ownerId, Date startDate, Date endDate) {
        Query query = manager.createQuery("FROM AssetDepreciationMappingData a where  " +
                "a.ownerId = :ownerId and (DATE(a.startDate) >= DATE(:startDate) and DATE(a.endDate) <= DATE(:endDate))");
        query.setParameter("ownerId", ownerId);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        return query.getResultList();
    }

    @Override
    public List<AssetTransferMappingData> getAssetTransferMappingBetweenDateWithStatusAndOwner(int ownerId, Date startDate, Date endDate, String assetStatus) {
        Query query = manager.createQuery("FROM AssetTransferMappingData a where a.ownerId = :ownerId and " +
                " (DATE(a.startDate) >= DATE(:startDate) and DATE(a.endDate) <= DATE(:endDate)) and a.assetStatus = :assetStatus");
        query.setParameter("ownerId", ownerId);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate",endDate );
        query.setParameter("assetStatus",assetStatus );
        return query.getResultList();
    }

    @Override
    public List<AssetDefinitionData> getAssetDefinitionDataWithOwnerAndStatus(int ownerId, List<String> assetStatusList) {
        Query query = manager.createQuery("FROM AssetDefinitionData a where a.ownerId = :ownerId and " +
                " a.assetStatus IN (:assetStatusList)");
        query.setParameter("ownerId",ownerId);
        query.setParameter("assetStatusList",assetStatusList);
        return query.getResultList();
    }

    @Override
    public List<AssetTransferMappingData> getAssetTransferMappingWithOwnerAndStatus(int ownerId,Date businessDate ,List<String> assetStatusList) {
        Query query = manager.createQuery("FROM AssetTransferMappingData a where a.ownerId = :ownerId and " +
                "((DATE(a.startDate) <= DATE(:businessDate) AND a.endDate IS NUll ) OR " +
                " (DATE(a.startDate) <= DATE(:businessDate) AND DATE(a.endDate) > DATE(:businessDate) ) ) and  a.assetStatus IN (:assetStatusList)");
        query.setParameter("ownerId",ownerId);
        query.setParameter("businessDate",businessDate);
        query.setParameter("assetStatusList",assetStatusList);
        List<AssetTransferMappingData> list = query.getResultList();
       // LOG.info(list.size() + "");
        return list;
    }

    @Override
    public StockEventDefinitionData getStockEventById(int eventId) {
        Query query = manager.createQuery("FROM StockEventDefinitionData a WHERE a.eventId = :eventId");
        query.setParameter("eventId", eventId);
        List<StockEventDefinitionData> list = query.getResultList();
        if(list == null || list.size() == 0){
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<AssetScrappedMappingData> getAssetScrappedMappingByUnitAndBusinessDate(int unitId, Date businessDate) {
        Query query = manager.createQuery("FROM AssetScrappedMappingData a where a.unitId = :unitId and " +
                "(DATE(a.scrappedDate) = DATE(:businessDate))");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDate", businessDate);
        return query.getResultList();
    }

    @Override
    public List<AssetScrappedMappingData> getAssetScrappedMappingByUnitAndBusinessDateRange(int unitId, Date startDate , Date endDate) {
        Query query = manager.createQuery("FROM AssetScrappedMappingData a where a.unitId = :unitId and " +
                "(DATE(a.scrappedDate) >= DATE(:startDate) and DATE(a.scrappedDate) <= DATE(:endDate))");
        query.setParameter("unitId", unitId);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        return query.getResultList();
    }

    @Override
    public List<AssetDefinitionData> getAssetsByIds(List<Integer> assetIds){
        Query query = manager.createQuery("FROM AssetDefinitionData a where a.assetId in :assetIds ");
        query.setParameter("assetIds",assetIds);

        return query.getResultList();
    }

    @Override
    public List<Integer> checkIfTransferInProgress(List<Integer> assetIds) {
        Query query = manager.createQuery("SELECT a.assetId FROM AssetDefinitionData a " +
                "WHERE a.assetId in :assetIds  and a.isInTransit = :assetStatus ");
        query.setParameter("assetIds", assetIds);
        query.setParameter("assetStatus", SCMUtil.YES);
        List<Integer> assetDefinitionDataList = query.getResultList();
        return assetDefinitionDataList;
    }

    @Override
    public GoodsReceivedData getGrForTO(Integer toId){
        Query query = manager.createQuery("FROM GoodsReceivedData gr left join fetch gr.goodsReceivedItemDatas " +
                " WHERE gr.transferOrderData.id = :toId ");
        query.setParameter("toId",toId);
        return (GoodsReceivedData) query.getSingleResult();
    }

    @Override
    public List<AssetDefinitionData> getAllNonAssetStockInAssetInventory(List<Integer> productIds){
       List<AssetDefinitionData> assetDefinitionDataList = new ArrayList<>();
       List<Integer> allowedProductIds = new ArrayList<>(Arrays.asList(100561,
               100695,
               100898,
               100899,
               100900));
       try{
           StringBuilder queryString = new StringBuilder(" select a FROM AssetDefinitionData a , ProductDefinitionData p where a.product.productId = p.productId " +
                   " and p.assetOrdering = :NO and a.unitId = :unitId  and  a.product.productId in (:productIds) and (a.isWriteOff is null or  a.isWriteOff = :NO ) ");
           Query query = manager.createQuery(queryString.toString());
           query.setParameter("unitId",26188);
           query.setParameter("NO",SCMUtil.NO);
           query.setParameter("productIds",productIds);
           if(Objects.nonNull(productIds)){
               query.setParameter("productIds",productIds);
           }else{
               query.setParameter("productIds",allowedProductIds);
           }
           assetDefinitionDataList = query.getResultList();
       }catch (Exception e){
           LOG.error("Error While Fetching Non Asset Stock In Asset Inventory :::::: ",e);
       }
        return  assetDefinitionDataList;

    }

    @Override
    public List<NonScannableAssetProducts> findNonScannableMapping(List<Integer> productIds , Boolean findAll) {
        try {
            StringBuilder queryString = new StringBuilder(" from NonScannableAssetProducts p where  p.mappingStatus = :status ");
            if(Boolean.FALSE.equals(findAll)){
                queryString.append(" and p.productId in :productIds ");
            }
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("status", AppConstants.ACTIVE);
            if(Boolean.FALSE.equals(findAll)){
                query.setParameter("productIds",productIds);
            }
            return  query.getResultList();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }


    @Override
    public List<StockEventAssetMappingDefinitionData> findAssetsByEventId(Integer eventId){
        List<StockEventAssetMappingDefinitionData> stockEventAssetMappingDefinitionDataList = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder(" from StockEventAssetMappingDefinitionData s where  s.eventId = :eventId ");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("eventId", eventId);
            stockEventAssetMappingDefinitionDataList =   query.getResultList();
        } catch (Exception e) {
           LOG.info("Error While Fetching Assets Mapped To Stock Taking Event : {}",eventId);
        }
        return stockEventAssetMappingDefinitionDataList;
    }

    @Override
    public StockEventDefinitionData findEventbyAssetId(Integer assetId, Integer unitId){
        List<StockEventAssetMappingDefinitionData> stockEventAssetMappingDefinitionDataList = new ArrayList<>();
        StockEventDefinitionData stockEventDefinitionData = null;
        try {
            StringBuilder queryString = new StringBuilder("FROM StockEventAssetMappingDefinitionData s WHERE " +
                    "s.assetId = :assetId AND s.unitId = :unitId " +
                    "ORDER BY s.eventAssetMappingId DESC");
            Query query = manager.createQuery(queryString.toString()).setMaxResults(1);
            query.setParameter("assetId", assetId);
            query.setParameter("unitId", unitId);

            stockEventAssetMappingDefinitionDataList =   query.getResultList();
            if(Objects.nonNull(stockEventAssetMappingDefinitionDataList) && !stockEventAssetMappingDefinitionDataList.isEmpty()){
                Integer eventId = stockEventAssetMappingDefinitionDataList.get(0).getEventId();
                List<StockEventDefinitionData> events = getParentOrChildEvent(eventId,null,null);
                stockEventDefinitionData = events.get(0);
            }
        } catch (Exception e) {
           LOG.info("Error While Fetching Event Mapped to Asset : {}",assetId);
        }
        return stockEventDefinitionData;
    }

    @Override
    public List<FaStockEventExtraScannedItems> findExtraScannedAssetsByEventId(Integer eventId){
        List<FaStockEventExtraScannedItems> extraScannedItems = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder(" from FaStockEventExtraScannedItems e where  e.eventId = :eventId and e.assetId is not null ");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("eventId", eventId);
            //query.setParameter("null",null);
            extraScannedItems =   query.getResultList();
        } catch (Exception e) {
            LOG.info("Error While Fetching Extra Scanned Items For Stock Taking Event : {}",eventId);
        }
        return extraScannedItems;
    }
    @Override
    public  List<StockEventAssetMappingDefinitionData> findAssetsByEventIdAndProductId(Integer eventId , Integer productId)
    {
        List<StockEventAssetMappingDefinitionData> stockEventAssetMappingDefinitionDataList = new ArrayList<>();
        try{
            StringBuilder queryString = new StringBuilder(" select s from StockEventAssetMappingDefinitionData s , AssetDefinitionData a where " +
                    "s.assetId = a.assetId AND s.eventId = :eventId and a.product.productId = :productId ") ;
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("eventId" , eventId) ;
            query.setParameter("productId" , productId) ;
            stockEventAssetMappingDefinitionDataList = query.getResultList() ;

        }catch (Exception e) {
            LOG.info("Error While Fetching Assets Mapped To Stock Taking Event : {} , :::::",eventId,e);
        }
        return stockEventAssetMappingDefinitionDataList ;
    }
    @Override
    public List<FaTransferData> fetchFaTransferData(Integer eventId){
        List<FaTransferData> faTransferDataList = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder(" from FaTransferData f where  f.eventId = :eventId ");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("eventId", eventId);
            faTransferDataList =   query.getResultList();
        } catch (Exception e) {
            LOG.info("Error While Fetching Assets Mapped To Stock Taking Event : {}",eventId);
        }
        return faTransferDataList;

    }

    @Override
    public String getLatestStockTakeVersion(){
        try {
            StringBuilder queryString = new StringBuilder("Select s.versionNo from StockTakeAppVersions s order by s.id desc ");
            Query query = manager.createQuery(queryString.toString());
            query.setMaxResults(1);
            return (String) query.getSingleResult();
        } catch (Exception e) {
            LOG.info("Error While Fetching Latest Stock Take Version!!");
            return null;
        }
    }

    @Override
    public List<AssetScanReportObject> getScannedAssetsByEventId(Integer eventId, EnvType envType) {
        List<AssetScanReportObject> assetScanReportObjects = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder("SELECT" +
                    "    f.ASSET_ID," +
                    "    a.ASSET_NAME," +
                    "    a.TAG_VALUE as ASSET_TAG," +
                    "    a.ASSET_STATUS," +
                    "    scd.SUB_CATEGORY_NAME," +
                    "    a.PRODUCT_ID," +
                    "    a.SKU_ID," +
                    "    a.CREATION_DATE," +
                    "    a.LAST_TRANSFER_DATE" +
                    " FROM" +
                    "    STOCK_EVENT_ASSET_MAPPING f" +
                    "    INNER JOIN ASSET_DEFINITION a on f.ASSET_ID = a.ASSET_ID" +
                    "    INNER JOIN PRODUCT_DEFINITION pd on a.PRODUCT_ID = pd.PRODUCT_ID" +
                    "    INNER JOIN SUB_CATEGORY_DEFINITION scd on pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID" +
                    " WHERE" +
                    "    EVENT_ID = :eventId and" +
                    "    f.ASSET_STATUS <> 'PENDING_LOST'");
            Query query = manager.createNativeQuery(queryString.toString());
            query.setParameter("eventId", eventId);
            List<Object[]> resultList = query.getResultList();
            for (Object[] o : resultList) {
                assetScanReportObjects.add(new AssetScanReportObject((Integer) o[0], (String) o[1], (String) o[2],
                        (String) o[3], (String) o[4],null, (Integer) o[5], (Integer) o[6], (Date) o[7],
                        (Date) o[8], null, null));
            }

            return assetScanReportObjects;

        }catch (Exception e){
            LOG.info("Error While Fetching stocks for event ::::: {}!!",eventId,e);
            return null;
        }
    }

    @Override
    public List<AssetScanReportObject> getLostAssetsByEventId(Integer eventId, EnvType envType) {
        List<AssetScanReportObject> assetScanReportObjects = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder("SELECT" +
                    "    f.ASSET_ID," +
                    "    a.ASSET_NAME," +
                    "    a.TAG_VALUE as ASSET_TAG," +
                    "    a.ASSET_STATUS," +
                    "    scd.SUB_CATEGORY_NAME," +
                    "    a.PROCUREMENT_COST," +
                    "    a.PRODUCT_ID," +
                    "    a.SKU_ID," +
                    "    a.CREATION_DATE," +
                    "    a.LAST_TRANSFER_DATE" +
                    " FROM" +
                    "    STOCK_EVENT_ASSET_MAPPING f" +
                    "    INNER JOIN ASSET_DEFINITION a on f.ASSET_ID = a.ASSET_ID" +
                    "    INNER JOIN PRODUCT_DEFINITION pd on a.PRODUCT_ID = pd.PRODUCT_ID" +
                    "    INNER JOIN SUB_CATEGORY_DEFINITION scd on pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID" +
                    " WHERE" +
                    "    EVENT_ID = :eventId and" +
                    "    f.ASSET_STATUS = 'MARKED_LOST'");
            Query query = manager.createNativeQuery(queryString.toString());
            query.setParameter("eventId", eventId);
            List<Object[]> resultList = query.getResultList();
            for (Object[] o : resultList) {
                assetScanReportObjects.add(new AssetScanReportObject((Integer) o[0], (String) o[1], (String) o[2],
                        (String) o[3],(String) o[4], ((BigDecimal) o[5]).intValue(), (Integer) o[6], (Integer) o[7], (Date) o[8],
                        (Date) o[9], null, null));
            }

            return assetScanReportObjects;

        }catch (Exception e){
            LOG.info("Error While Fetching stocks for event ::::: {}!!",eventId,e);
            return null;
        }
    }

    @Override
    public List<ExcessAssetReportObject> getExcessAssetsByEventId(Integer eventId, EnvType envType) {
        List<ExcessAssetReportObject> excessAssetReportObjects = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder("SELECT" +
                    "    CASE" +
                    "        WHEN f.ASSET_ID is NULL THEN 'QR_MISSING'" +
                    "        ELSE 'QR_FOUND'" +
                    "    END as EXCESS_TYPE," +
                    "    f.ASSET_ID," +
                    "    CASE" +
                    "        WHEN f.ASSET_ID is NULL THEN f.USER_DESCRIPTION" +
                    "        ELSE a.ASSET_NAME" +
                    "    END as ASSET_NAME," +
                    "    a.TAG_VALUE as ASSET_TAG," +
                    "    a.ASSET_STATUS," +
                    "    scd.SUB_CATEGORY_NAME," +
                    "    a.PROCUREMENT_COST," +
                    "    a.PRODUCT_ID," +
                    "    a.SKU_ID," +
                    "    ud.UNIT_ID," +
                    "    ud.UNIT_NAME," +
                    "    a.CREATION_DATE," +
                    "    a.LAST_TRANSFER_DATE," +
                    "    f.COMMENT," +
                    "    ddd.DOCUMENT_URL as PIC_URL" +
                    " FROM" +
                    "    FA_STOCK_EVENT_EXTRA_SCANNED_ITEMS f" +
                    "    LEFT OUTER JOIN ASSET_DEFINITION a on f.ASSET_ID = a.ASSET_ID" +
                    "    LEFT OUTER JOIN PRODUCT_DEFINITION pd on a.PRODUCT_ID = pd.PRODUCT_ID" +
                    "    LEFT OUTER JOIN SUB_CATEGORY_DEFINITION scd on pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID" +
                    "    LEFT OUTER JOIN ");
                    if(envType.equals(DEV) || envType.equals(LOCAL)){
                        queryString.append("KETTLE_MASTER_DEV.UNIT_DETAIL");
                    }
                    else if(envType.equals(STAGE)){
                        queryString.append("KETTLE_MASTER_STAGE.UNIT_DETAIL");
                    }
                    else if(envType.equals(PROD) ||envType.equals(SPROD)){
                        queryString.append("KETTLE_MASTER.UNIT_DETAIL");
                    }
                    queryString.append(" ud on f.OWNER_UNIT_ID = ud.UNIT_ID" +
                    "    LEFT OUTER JOIN DOCUMENT_DETAIL_DATA ddd on f.DOC_ID = ddd.DOCUMENT_ID" +
                    " WHERE" +
                    "    EVENT_ID = :eventId and" +
                    "    f.IS_SETTLED != 'Y'");
            Query query = manager.createNativeQuery(queryString.toString());
            query.setParameter("eventId", eventId);
            List<Object[]> resultList = query.getResultList();
            for (Object[] o : resultList) {
                excessAssetReportObjects.add(new ExcessAssetReportObject((String) o[0], (Integer) o[1], (String) o[2],
                        (String) o[3], (String) o[4], (String) o[5], (Integer) o[6], (Integer) o[7], (Integer) o[8], (Integer) o[9]
                        , (String) o[10], (Date) o[11], (Date) o[12], (DateTime) o[13], (String) o[14], (String) o[15]));
            }

            return excessAssetReportObjects;

        }catch (Exception e){
            LOG.info("Error While Fetching stocks for event ::::: {}!!",eventId,e);
            return null;
        }
    }

    @Override
    public StockTakeReportSummaryObject getSummaryByEventId(Integer eventId, EnvType envType){

        StockTakeReportSummaryObject stockTakeReportSummaryObject = new StockTakeReportSummaryObject();
        try {
            StringBuilder queryString = new StringBuilder("SELECT" +
                    "    se.EVENT_ID," +
                    "    CONCAT(se.EVENT_TYPE, '_', se.SUB_TYPE) as EVENT_TYPE," +
                    "    se.EVENT_STATUS," +
                    "    se.EVENT_CREATION_DATE," +
                    "    se.LAST_UPDATION_TIME," +
                    "    se.UNIT_ID," +
                    "    ud.UNIT_NAME," +
                    "    se.UNIT_TYPE," +
                    "    ed1.EMP_NAME as INITIATED_BY," +
                    "    ed2.EMP_NAME as RECIPIENT," +
                    "    COUNT(am.STOCK_EVENT_ASSET_MAPPING_ID) as ASSET_COUNT_am," +
                    "    SUM(IF((am.ASSET_STATUS <> 'PENDING_LOST'), 1, 0)) AS SCANNED_SUCCESS," +
                    "    SUM(IF((am.ASSET_STATUS = 'PENDING_LOST'), 1, 0)) AS NOT_SCANNED," +
                    "    SUM(IF((am.ASSET_STATUS = 'PENDING_LOST'), ad.PROCUREMENT_COST, 0)) AS NOT_SCANNED_COST" +
                    " FROM" +
                    "    STOCK_EVENT_DEFINITION se" +
                    "    INNER JOIN STOCK_EVENT_ASSET_MAPPING am on se.EVENT_ID = am.EVENT_ID" +
                    "    INNER JOIN ");
                    if(envType.equals(DEV) || envType.equals(LOCAL)){
                        queryString.append("KETTLE_MASTER_DEV.UNIT_DETAIL ");
                    }
                    else if(envType.equals(STAGE)){
                        queryString.append("KETTLE_MASTER_STAGE.UNIT_DETAIL ");
                    }
                    else if(envType.equals(PROD) ||envType.equals(SPROD)){
                        queryString.append("KETTLE_MASTER.UNIT_DETAIL ");
                    }
                    queryString.append("ud on se.UNIT_ID = ud.UNIT_ID INNER JOIN ");
                    if(envType.equals(DEV) || envType.equals(LOCAL)){
                        queryString.append("KETTLE_MASTER_DEV.EMPLOYEE_DETAIL ");
                    }
                    else if(envType.equals(STAGE)){
                        queryString.append("KETTLE_MASTER_STAGE.EMPLOYEE_DETAIL ");
                    }
                    else if(envType.equals(PROD) ||envType.equals(SPROD)){
                        queryString.append("KETTLE_MASTER.EMPLOYEE_DETAIL ");
                    }
                    queryString.append("ed1 on se.EVENT_INITIATOR_ID = ed1.EMP_ID" +
                    "    INNER JOIN ASSET_DEFINITION ad on am.ASSET_ID = ad.ASSET_ID" +
                    "    LEFT OUTER JOIN ");
                    if (envType.equals(DEV) || envType.equals(LOCAL)){
                        queryString.append("KETTLE_MASTER_DEV.EMPLOYEE_DETAIL");
                    }
                    else if(envType.equals(STAGE)){
                        queryString.append("KETTLE_MASTER_STAGE.EMPLOYEE_DETAIL");
                    }
                    else if(envType.equals(PROD) ||envType.equals(SPROD)){
                        queryString.append("KETTLE_MASTER.EMPLOYEE_DETAIL");
                    }
                    queryString.append(" ed2 on se.AUDITOR_ID = ed2.EMPLOYEE_CODE" +
                    " WHERE se.EVENT_ID = :eventId");
            Query query = manager.createNativeQuery(queryString.toString());
            query.setParameter("eventId", eventId);
            List<Object[]> resultList = query.getResultList();
            for (Object[] o : resultList) {
                stockTakeReportSummaryObject = new StockTakeReportSummaryObject(
                        (Integer) o[0],
                        (String) o[1],
                        (String) o[2],
                        (Date) o[3],
                        (Date) o[4],
                        (Integer) o[5],
                        (String) o[6],
                        (String) o[7],
                        (String) o[8],
                        (String) o[9],
                        (Date) null,
                        (Number) null,
                        (Number) o[10],
                        (Number) o[11],
                        (Number) o[12],
                        (Number) o[13],
                        (Number) null,
                        (Number) null,
                        (Number) null);
            }

            return stockTakeReportSummaryObject;

        }catch (Exception e){
            LOG.info("Error While Fetching stocks for event ::::: {}!!",eventId,e);
            return null;
        }
    }


    @Override
   public AssetDefinitionData getAssetFromUnitAndTagValue(Integer unitId, String tagValue){
       Query query = manager.createQuery("FROM AssetDefinitionData a WHERE  a.tagValue =:tagValue and a.unitId=:unitId");
       query.setParameter("tagValue", tagValue);
       query.setParameter("unitId",unitId);
       try {
                return (AssetDefinitionData) query.getSingleResult();
       } catch (NoResultException e) {
           LOG.info("Could not found asset with tagValue : " + tagValue + " and unitId  : "+ unitId + e.getStackTrace());
           return null;
       }
   }
    @Override
    public List<StockEventDefinitionData> getInitiatedToEvent(int unitId) {

        Query query = manager.createQuery("FROM StockEventDefinitionData a WHERE a.unitId = :unitId  and a.eventType in ('TRANSFER_OUT','ASSET_RECEIVING') and a.eventStatus in ('INITIATED') ");
        query.setParameter("unitId", unitId);
        List<StockEventDefinitionData> res;
        try {
            res = (List<StockEventDefinitionData>)query.getResultList();
        }catch (NoResultException ex){
            return new ArrayList<>();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
        return res;
    }
    @Override
    public List<DocumentDetailData> getSwitchAssetDocs(List<Integer> docIds) {
       try {
           Query query = manager.createQuery("SELECT d FROM DocumentDetailData d where d.id IN :ids");
           query.setParameter("ids", docIds);
           return (List<DocumentDetailData>) query.getResultList();
       }catch(Exception e){
           LOG.error("###### Error while getting switch asset documents ##########, {}",e.getMessage());
          e.printStackTrace();
          throw new RuntimeException(e);
       }
    }

    @Override
    public StockEventDefinitionData getEventFromGr(Integer grId,String status){
       try {
           Query query = manager.createQuery("FROM StockEventDefinitionData a WHERE a.grId = :grId and a.eventStatus = :status");
           query.setParameter("grId",grId);
           query.setParameter("status",status);
              return  (StockEventDefinitionData) query.getSingleResult();
       }catch (NoResultException e) {
       LOG.error("{}",e.getMessage());
       return null;
       }catch(Exception e){
             LOG.error("###### Error in getEventFromGr ######, {}",e.getMessage());
             e.printStackTrace();
             throw new RuntimeException(e);
       }

    }
    @Override
    public List<FaGrData> getFaGrDataForEvent(Integer eventId){
        try{
            Query query = manager.createQuery("FROM FaGrData f WHERE f.eventId = :eventId");
            query.setParameter("eventId",eventId);
           return  (List<FaGrData>)query.getResultList();
        }catch (NoResultException e){
            LOG.error("{}",e.getMessage());
            return null;
        }
        catch (Exception e){
            LOG.error("###### Error in getFaTransferDataForEvent ######, {}",e.getMessage());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<StockEventDefinitionData> getInitiatedEventForUnit(int unitId) {

        Query query = manager.createQuery("FROM StockEventDefinitionData a WHERE a.unitId = :unitId and  a.eventType in ('TRANSFER_OUT','ASSET_RECEIVING') and a.eventStatus in ('INITIATED')  order by 1 desc");
        query.setParameter("unitId", unitId);
        List<StockEventDefinitionData> res;
        try {
            res = (List<StockEventDefinitionData>)query.getResultList();
        }catch (NoResultException ex){
            return new ArrayList<>();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
        return res;
    }

    @Override
    public List<AssetRecoveryDetailData> getAssetRecoveryDetail(Integer recoveryId, String recoveryStatus){
        List<AssetRecoveryDetailData> list= new ArrayList<>();
        StringBuilder queryString = new StringBuilder("FROM AssetRecoveryDetailData r WHERE r.recoveryId = :recoveryId");
        if(Objects.nonNull(recoveryStatus)){
            queryString.append(" and r.recoveryStatus = :recoveryStatus");
        }
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("recoveryId",recoveryId);
        if(Objects.nonNull(recoveryStatus)){
            query.setParameter("recoveryStatus",recoveryStatus);
        }
        list = (List<AssetRecoveryDetailData>) query.getSingleResult();
        return list;
    }

    @Override
    public AssetDefinitionDataLog getFilteredLatestAssetDefinitionDataLog(int assetId, List<AssetStatusType> statuses) throws SumoException {
        Query query = manager.createQuery("FROM AssetDefinitionDataLog a WHERE a.assetId =:assetId and a.assetStatus not in (:assetStatus) ORDER BY a.assetDefinitionLogId DESC");
        query.setParameter("assetId", assetId);
        query.setParameter("assetStatus",statuses.stream().map(e-> e.value()).collect(Collectors.toList()));
        query.setMaxResults(1);
        Object result = null;
        try{
            result = query.getSingleResult();
            AssetDefinitionDataLog asset = (AssetDefinitionDataLog) result;
            return asset;
        } catch(NoResultException e){
            LOG.info("Could not found Asset Definition LOG for assetId " + assetId);
            throw new SumoException(e);
        }
    }

    @Override
    public StockEventDefinitionData getPendingFoundAssetEvent(final Integer unitId){
        Query query = manager.createQuery("FROM StockEventDefinitionData a WHERE a.unitId = :unitId and a.subType in ('FOUND_ASSET') and a.eventStatus in ('IN_PROCESS','PENDING_FOUND_APPROVAL')");
        query.setParameter("unitId", unitId);
        query.setMaxResults(1);
        StockEventDefinitionData res;
        try {
            res = (StockEventDefinitionData)query.getSingleResult();
        }catch (NoResultException ex){
            return null;
        }catch (Exception e){
            throw new RuntimeException(e);
        }
        return res;
    }
    @Override
    public List<StockEventAssetMappingDefinitionData> findAssetsByEventIdAndAssetId(Integer eventId,Integer assetId){
        List<StockEventAssetMappingDefinitionData> stockEventAssetMappingDefinitionDataList = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder(" from StockEventAssetMappingDefinitionData s where  s.eventId = :eventId and s.assetId = :assetId");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("eventId", eventId);
            query.setParameter("assetId",assetId);
            stockEventAssetMappingDefinitionDataList =   query.getResultList();
        } catch (Exception e) {
            LOG.info("Error While Fetching Assets Mapped To Stock Taking Event : {}",eventId);
        }
        return stockEventAssetMappingDefinitionDataList;
    }

    @Override
    public List<AssetRecoveryData> getAllAssetInRecoveryFromEventId(int eventId) {
       try{
        Query query = manager.createQuery("FROM AssetRecoveryData a WHERE a.eventId = :eventId");
        query.setParameter("eventId", eventId);
        return (List<AssetRecoveryData>) query.getResultList();
       }catch (Exception e){
           LOG.error("Error while getting recovery for event id : {}, error : {}",eventId,e.getMessage());
           return new ArrayList<>();
       }
    }

    @Override
    public Boolean checkUnitClosureRegularEvent(Integer unitId) throws SumoException {
     try{
         Query query = manager.createQuery("from StockEventDefinitionData a where a.unitId = :unitId and a.subType = :subType and a.initiatedBy = :initiatedBy and a.status in (:statuses)");
         query.setParameter("unitId",unitId);
         query.setParameter("subType",StockTakeSubType.REGULAR.value());
         query.setParameter("initiatedBy",SCMServiceConstants.SYSTEM_USER);
         query.setParameter("statuses",Arrays.asList(StockEventStatusType.IN_PROCESS.value(),StockEventStatusType.COMPLETED.value()));
         return query.getResultList().size() <= 1;
     }catch (Exception e){
        throw  new SumoException(e);
     }
    }
    @Override
    public List<StockEventDefinitionData> getEventByUnitAndStatusList(int unitId, List<String> eventStatus, Integer roId) {
        StringBuilder queryString = new StringBuilder("FROM StockEventDefinitionData a WHERE a.eventType = : eventType and   a.unitId = :unitId and a.eventStatus in (:eventStatus) ");
        if(Objects.nonNull(roId)){
            queryString.append("and a.roId = :roId ");
        }
//        if(Objects.nonNull(userId)){
//            queryString.append("and a.initiatedBy = :userId ");
//        }
        queryString.append("and a.parentId is null ");
        Query query = manager.createQuery(queryString.toString());

        query.setParameter("unitId", unitId);
        query.setParameter("eventStatus", eventStatus);
        query.setParameter("eventType",StockEventType.STOCK_TAKE.value());
        if(roId != null){
            query.setParameter("roId",roId);
        }
//        if(userId != null){
//            query.setParameter("userId",userId);
//        }
        return query.getResultList();
    }
}
