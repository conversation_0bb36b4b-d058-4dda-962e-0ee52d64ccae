package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "PAYMENT_REQUEST_LOG")
public class PaymentRequestLogData {

    private Integer id;
    private String logData;
    private Date updateTime;
    private Integer paymentRequestId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_REQUEST_LOG_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "LOG_DATA", nullable = false)
    public String getLogData() {
        return logData;
    }

    public void setLogData(String logData) {
        this.logData = logData;
    }

    @Column(name = "UPDATE_TIME", nullable = false)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "PAYMENT_REQUEST_ID")
    public Integer getPaymentRequestId() {
        return paymentRequestId;
    }

    public void setPaymentRequestId(Integer paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }
}
