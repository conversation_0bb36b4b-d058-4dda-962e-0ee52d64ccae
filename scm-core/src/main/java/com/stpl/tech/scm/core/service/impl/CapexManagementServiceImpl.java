package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.excel.impl.GenericExcelManagementServiceImpl;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.data.model.ExcelRequestData;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.CapexManagementService;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.ServiceOrderManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.CapexManagementDao;
import com.stpl.tech.scm.data.dao.PurchaseOrderManagementDao;
import com.stpl.tech.scm.data.dao.RequestOrderManagementDao;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.dao.TransferOrderManagementDao;
import com.stpl.tech.scm.data.model.BudgetAuditDetailData;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetAuditDetail;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.CapexRequestDetailData;
import com.stpl.tech.scm.data.model.CapexRequestStatusLog;
import com.stpl.tech.scm.data.model.CapexTemplateData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.BudgetComparisionDetail;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.CapexAuditDetail;
import com.stpl.tech.scm.domain.model.CapexBudgetDetail;
import com.stpl.tech.scm.domain.model.CapexRequestDetail;
import com.stpl.tech.scm.domain.model.CapexStatus;
import com.stpl.tech.scm.domain.model.DepartmentBudgetVO;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.OrderTypes;
import com.stpl.tech.scm.domain.model.OrderTypesSummary;
import com.stpl.tech.scm.domain.model.PoItemLevelSummary;
import com.stpl.tech.scm.domain.model.PurchaseOrder;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.ServiceOrder;
import com.stpl.tech.scm.domain.model.ServiceOrderItem;
import com.stpl.tech.scm.domain.model.ServiceOrderShort;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceOrderSummary;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.notification.email.CapexEmailNotification;
import com.stpl.tech.scm.notification.email.template.CapexEmailNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class CapexManagementServiceImpl implements CapexManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(CapexManagementServiceImpl.class);

	@Autowired
	CapexManagementDao capexManagementDao;

	@Autowired
	ServiceOrderManagementDao serviceOrderManagementDao;
	@Autowired
	PurchaseOrderManagementDao purchaseOrderManagementDao;

	@Autowired
	private RequestOrderManagementDao requestOrderManagementDao;

	@Autowired
	private TransferOrderManagementDao transferOrderManagementDao;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private EnvProperties props;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private ServiceOrderManagementService serviceOrderManagementService;

	@Autowired
	private GenericExcelManagementServiceImpl genericExcelManagementService;



	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<CapexAuditDetail> getCapexAuditList(Integer unitId, String version, String status) {
		List<Integer> capexRequestIds = capexManagementDao.findIds(unitId, version, status);
		if(capexRequestIds.size() > 0) {
            List<CapexAuditDetail> capexAuditData = capexManagementDao.findList(capexRequestIds, version, status);
			try {
				for (CapexAuditDetail capexAuditDetail : capexAuditData) {
					BigDecimal uploadedAmount = BigDecimal.ZERO;
					List<BudgetComparisionDetail> budgetComparisonDetails = getBudgetComparision(capexAuditDetail.getUnitId(),capexAuditDetail.getCapexRequestId(),capexAuditDetail.getId());
					if (Objects.nonNull(budgetComparisonDetails) && budgetComparisonDetails.size() > 0) {
						for (BudgetComparisionDetail budgetComparisionDetail : budgetComparisonDetails) {
							uploadedAmount = uploadedAmount.add(budgetComparisionDetail.getCurrentOriginalAmount());
						}
					}
					capexAuditDetail.setUploadedAmount(uploadedAmount);
				}
			}
			catch (Exception e) {
				LOG.error("Error Occurred While Setting the Current Uploaded Amount...! :: ",e);
			}
            return capexAuditData;
        }
        else{
		    LOG.info("No capex details found for Unit Id : {}",unitId);
		    return new ArrayList<>();
        }
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean validateUnitForCapex(Integer unitId, String type) {
		boolean check = false;
		List<CapexRequestDetailData> capexRequestDetailData = capexManagementDao.getUnitVerify(unitId);
		if (capexRequestDetailData.isEmpty()) {
			return false;
		}
		for (CapexRequestDetailData capexRequestData : capexRequestDetailData) {
			if (type.equalsIgnoreCase("New Cafe")) {
				if (!capexRequestData.getStatus().equalsIgnoreCase(CapexStatus.ARCHIVED.name())) {
					check = true;
					break;
				}
			} else {
				if (!capexRequestData.getStatus().equalsIgnoreCase(CapexStatus.ARCHIVED.name())
						&& !capexRequestData.getStatus().equalsIgnoreCase(CapexStatus.CLOSED_L3.name())) {
					check = true;
					break;
				}
			}
		}
		return check;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public CapexRequestDetailData createCapexData(CapexRequestDetail capexRequestData)
			throws IOException, SumoException {
		CapexRequestDetailData capexRequest = new CapexRequestDetailData();
		capexRequest.setUnitId(capexRequestData.getUnitId());
		capexRequest.setUnitName(capexRequestData.getUnitName());
		capexRequest.setType(capexRequestData.getType());
		capexRequest.setGeneratedBy(capexRequestData.getGeneratedBy());
		capexRequest.setGenerationTime(SCMUtil.getCurrentTimestamp());
		capexRequest.setLastUpdatedBy(capexRequestData.getGeneratedBy());
		capexRequest.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
		capexRequest.setStatus(CapexStatus.CREATED.toString());
		capexRequest.setAccessKey(UUID.randomUUID().toString());
		capexRequest.setTemplateVersion("1.0");
		capexRequest = capexManagementDao.add(capexRequest, false);
		CapexAuditDetailData auditDetail = new CapexAuditDetailData();
		auditDetail.setVersion("1.0");
		auditDetail.setCapexRequestId(capexRequest.getId());
		auditDetail.setAccessKey(capexRequest.getAccessKey());
		auditDetail.setDownloadedBy(capexRequestData.getGeneratedBy().toString());
		auditDetail.setStatus(CapexStatus.CREATED.toString());
		capexManagementDao.add(auditDetail, true);
		StringBuilder msg = new StringBuilder(":::::::::::: New Capex Is Created ::::::::::\n");
		sendSlackNotificationForCapexEvent(msg.toString(),capexRequestData.getUnitId(), capexRequestData.getUnitName(), capexRequestData.getType(),
				capexRequestData.getId(), auditDetail.getVersion(), "-", CapexStatus.CREATED.value(),
				capexRequestData.getGeneratedBy());
		return capexRequest;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public CapexAuditDetailData getCapexFileVersion(CapexAuditDetail capexData) throws SumoException {
		Float newVersion = (float) 1.0;
		CapexAuditDetailData previousAuditDetail = capexManagementDao.find(CapexAuditDetailData.class,
				capexData.getId());
		previousAuditDetail.setStatus(CapexStatus.EDITED.toString());
		capexManagementDao.update(previousAuditDetail, true);

		CapexRequestDetailData capexRequestDetailData = capexManagementDao.find(CapexRequestDetailData.class,
				capexData.getCapexRequestId());
		capexRequestDetailData.setStatus(CapexStatus.CREATED.toString());
		capexRequestDetailData.setLastUpdatedBy(Integer.parseInt(capexData.getUploadedBy()));
		capexRequestDetailData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
		capexManagementDao.update(capexRequestDetailData, true);

		CapexRequestStatusLog capexStatusLog = capexManagementDao.findCapexStatusLog(capexData.getId(),capexData.getCapexRequestId());
		capexStatusLog.setFromStatus(capexStatusLog.getToStatus());
		capexStatusLog.setToStatus(CapexStatus.CREATED.toString());
		capexStatusLog.setUpdatedBy(Integer.parseInt(capexData.getUploadedBy()));
		capexStatusLog.setUpdateTime(SCMUtil.getCurrentTimestamp());
		capexStatusLog.setStatus(CapexStatus.IN_ACTIVE.toString());
		capexManagementDao.update(capexStatusLog, true);

		CapexAuditDetailData auditDetail = new CapexAuditDetailData();
		auditDetail.setVersion(String.valueOf(Float.valueOf(capexData.getVersion()) + newVersion));
		auditDetail.setCapexRequestId(capexData.getCapexRequestId());
		auditDetail.setAccessKey(UUID.randomUUID().toString());
		auditDetail.setDownloadedBy(capexData.getDownloadedBy());
		auditDetail.setStatus(CapexStatus.CREATED.toString());
		auditDetail.setVersionCreationTime(SCMUtil.getCurrentTimestamp());
		auditDetail = capexManagementDao.add(auditDetail, true);
		LOG.info("Sending Slack Notification for Capex Edit for Capex Request Id : {} ", capexData.getCapexRequestId());
		StringBuilder msg = new StringBuilder(":::::::::::: Capex Edit Is Initiated ::::::::::\n");
		sendSlackNotificationForCapexEvent(msg.toString(),capexRequestDetailData.getUnitId(), capexRequestDetailData.getUnitName(), capexRequestDetailData.getType(),
				capexRequestDetailData.getId(), capexData.getVersion(), capexStatusLog.getFromStatus(), capexStatusLog.getToStatus(),
				capexStatusLog.getUpdatedBy());
		return auditDetail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean uploadCapexSheet(MultipartFile file, String uploadedBy) throws IOException, SumoException {
		InputStream excelFile = file.getInputStream();
		XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
		XSSFSheet sheet = workbook.getSheetAt(0);
		XSSFSheet sheet1 = workbook.getSheetAt(1);
		int lastRowNumber = sheet1.getLastRowNum();
		XSSFRow row1 = sheet.getRow(0);
		XSSFCell cell1 = row1.getCell(1);
		XSSFRow versionRow = sheet.getRow(7);
		XSSFCell versionCell = versionRow.getCell(1);
		versionCell.setCellType(CellType.STRING);
		XSSFRow unitNameRow = sheet.getRow(2);
		CapexTemplateData capexTemplateBucket = scmCache
				.getCapexTemplate(sheet.getRow(3).getCell(1).getStringCellValue());
		if (!versionCell.getStringCellValue().equalsIgnoreCase("1.0")) {
			Float previousVers = (float) (Float.valueOf(versionCell.getStringCellValue()) - 1.0);
			List<CapexBudgetAuditDetail> budgetAuditDetails = new ArrayList<CapexBudgetAuditDetail>();
			for (int i = 4; i < lastRowNumber; i++) {
				CapexBudgetAuditDetail capexBudgetAuditDetail = new CapexBudgetAuditDetail();
				XSSFRow row41 = sheet1.getRow(i);
				capexBudgetAuditDetail.setDepartmentId((int) row41.getCell(1).getNumericCellValue());
				capexBudgetAuditDetail.setDepartmentName(row41.getCell(2).getStringCellValue());
				capexBudgetAuditDetail.setAmount(new BigDecimal(row41.getCell(3).getNumericCellValue()));
				budgetAuditDetails.add(capexBudgetAuditDetail);
			}
			CapexAuditDetailData capexPrevAuditData = capexManagementDao
					.findCapexDataForVersion((int) cell1.getNumericCellValue(), String.valueOf(previousVers));
			List<CapexBudgetDetailData> capexBudgetAuditDetail = capexManagementDao
					.findBudgetAudit((int) sheet.getRow(1).getCell(1).getNumericCellValue(), (int) cell1.getNumericCellValue());

			for (CapexBudgetAuditDetail budgetAudit : budgetAuditDetails) {// from // budgetAudit
				CapexBudgetDetailData budgetDetailData = getCapexBudgetDetailData(budgetAudit.getDepartmentId(), // to
																													// //
																													// budgetDetailData
						capexBudgetAuditDetail);
				if (budgetDetailData != null) {
					BigDecimal usedAmount = budgetDetailData.getBudgetAmount()
							.subtract(budgetDetailData.getRemainingAmount());
					if (usedAmount.compareTo(budgetAudit.getAmount()) > 0) {
						throw new SumoException("Budget In Sufficient ", "For Department : " + budgetDetailData.getDepartmentName() + " [" + budgetDetailData.getDepartmentId() + "] <br>" +
								"Used Amount is : " + usedAmount + " and Uploaded Budget Amount is : " + budgetAudit.getAmount());
					}
				}
			}
		}

		CapexAuditDetailData capexAuditData = capexManagementDao
				.findCapexDataForVersion((int) cell1.getNumericCellValue(), versionCell.getStringCellValue());
		List<CapexBudgetAuditDetail> budgetAuditDetails = new ArrayList<CapexBudgetAuditDetail>();
		for (int i = 4; i < lastRowNumber; i++) {
			CapexBudgetAuditDetail capexBudgetAuditDetail = new CapexBudgetAuditDetail();
			XSSFRow row41 = sheet1.getRow(i);
			capexBudgetAuditDetail.setDepartmentId((int) row41.getCell(1).getNumericCellValue());
			capexBudgetAuditDetail.setDepartmentName(row41.getCell(2).getStringCellValue());
			capexBudgetAuditDetail.setAmount(new BigDecimal(row41.getCell(3).getNumericCellValue()));
			capexBudgetAuditDetail.setCapexAuditDetailId(capexAuditData);
			budgetAuditDetails.add(capexBudgetAuditDetail);
		}
		String fileName = getCapexUploadFileName(unitNameRow.getCell(1).getStringCellValue(),
				versionCell.getStringCellValue(), (int) cell1.getNumericCellValue(), MimeType.XLSX);
		XSSFRow row6 = sheet.getRow(5);
		capexAuditData.setDownloadedBy(String.valueOf((int) row6.getCell(1).getNumericCellValue()));
		capexAuditData.setUploadedBy(uploadedBy);
		capexAuditData.setStatus(CapexStatus.PENDING_APPROVAL_L1.toString());
		capexAuditData.setDownloadedPath(props.getS3Bucket() + "/" + capexTemplateBucket.getBucket() + "/" + fileName);
		capexAuditData.setUploadedPath(props.getS3Bucket() + "/" + capexTemplateBucket.getBucket() + "/" + fileName);
		capexAuditData.setCapexBudgetAuditDetail(budgetAuditDetails);
		capexManagementDao.update(capexAuditData, true);
		CapexRequestStatusLog capexStatusLog = new CapexRequestStatusLog();
		capexStatusLog.setFromStatus(CapexStatus.CREATED.toString());
		capexStatusLog.setToStatus(CapexStatus.PENDING_APPROVAL_L1.toString());
		capexStatusLog.setUpdatedBy((int) row6.getCell(1).getNumericCellValue());
		capexStatusLog.setUpdateTime(SCMUtil.getCurrentTimestamp());
		capexStatusLog.setCapexAuditId(capexAuditData.getId());
		capexStatusLog.setCapexRequestId(capexAuditData.getCapexRequestId());
		capexStatusLog.setStatus(CapexStatus.ACTIVE.toString());
		capexManagementDao.add(capexStatusLog, true);
		CapexRequestDetailData capexRequestDetail = capexManagementDao.find(CapexRequestDetailData.class,
				capexAuditData.getCapexRequestId());
		capexRequestDetail.setStatus(CapexStatus.PENDING_APPROVAL.toString());
		capexManagementDao.update(capexRequestDetail, true);
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		try {
			workbook.write(bos);
		} catch (IOException e1) {
			LOG.error("Error in Saving File in S3", e1);
		}
		byte[] barray = bos.toByteArray();
		InputStream is = new ByteArrayInputStream(barray);
		MultipartFile multipartFile = null;
		try {
			multipartFile = new MockMultipartFile(capexTemplateBucket.getKey(), is);
		} catch (Exception e) {
			LOG.error("Error in Saving File in S3", e);
		}
		LOG.info("Sending Slack Ntification For Capex Upload for {}", capexRequestDetail.getUnitName());
		StringBuilder msg = new StringBuilder(":::::::::::: New  Capex Is Uploaded ::::::::::\n");
		sendSlackNotificationForCapexEvent(msg.toString(),capexRequestDetail.getUnitId(), capexRequestDetail.getUnitName(), capexRequestDetail.getType(),
				capexRequestDetail.getId(), capexAuditData.getVersion(), capexStatusLog.getFromStatus(), capexStatusLog.getFromStatus(),
				capexStatusLog.getUpdatedBy());
		saveCapexFile(fileName, multipartFile, capexTemplateBucket.getBucket());
		return true;
	}

	private void sendSlackNotificationForCapexEvent(String titleMsg,Integer unitId, String unitName, String type, Integer capexRequestId, String version, String previousStatus,
													String newStatus, Integer userId) {
		try {
			StringBuilder msg = new StringBuilder(titleMsg);
			msg.append("UNIT ID :::: " + unitName + "(" + unitId + ")\n");
			msg.append("TYPE :::: " + type + "\n");
			msg.append("VERSION :::: " + version + "\n");
			msg.append("CAPEX REQUEST ID ::::" + capexRequestId + "\n");
			msg.append("PREVIOUS STATUS :::: " + previousStatus + "\n");
			msg.append("NEW STATUS :::: " + newStatus + "\n");
			if (Objects.nonNull(userId)) {
				msg.append("UPDATED BY :::: " + userId + "\n");
			}
			msg.append("UPDATION TIME :::: " + AppUtils.getCurrentTimestamp() + "\n");
			SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
					SlackNotification.SUPPLY_CHAIN, msg.toString());
		} catch (Exception e) {
			LOG.error("Error While Sending Slack Notification For Unit Id : {} , Capex Request Id : {} ,Previous Status : {} , New Status : {}",
					unitId, capexRequestId, previousStatus, newStatus);
		}

	}

	private CapexBudgetDetailData getCapexBudgetDetailData(int departmentId,
														   List<CapexBudgetDetailData> capexBudgetDetail) {
		try {
			return capexBudgetDetail.stream().filter(capex -> capex.getDepartmentId() == departmentId).findFirst()
					.get();
		} catch (Exception e) {
			return null;
		}
	}

	private void saveCapexFile(String fileName, MultipartFile multipartFile, String capexTemplateBucket) {
		FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), capexTemplateBucket, fileName,
				multipartFile, true);
	}

	public static String getCapexUploadFileName(String unitName, String version, Integer capexId,
			MimeType fileExtension) {
		return "Capex_Request" + "-" + unitName + "-" + version + "-" + capexId + "."
				+ fileExtension.name().toLowerCase();
	}

	@Override
	public CapexRequestDetail validateCapexSheet(MultipartFile file) throws IOException, SumoException {
		CapexRequestDetail capexRequestDetail = new CapexRequestDetail();
		InputStream excelFile = file.getInputStream();
		XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
		int numberOfSheets =  workbook.getNumberOfSheets();

		XSSFSheet sheet = workbook.getSheetAt(0);
		XSSFRow row9 = sheet.getRow(8);
		if(row9!=null){
			XSSFCell cell9 = row9.getCell(1);
			String value = cell9.getStringCellValue();
			if (value.equalsIgnoreCase("Yes")) {
				if(numberOfSheets != 3){
					throw new SumoException("Invalid File Format", "Please Upload the Complete Summary Capex Sheet in Correct Format");
				}
			}
		}


		XSSFRow row1 = sheet.getRow(0);
		XSSFCell cell1 = row1.getCell(1);
		capexRequestDetail.setId((int) cell1.getNumericCellValue());
		XSSFRow row6 = sheet.getRow(6);
		XSSFCell cell61 = row6.getCell(1);
		capexRequestDetail.setAccessKey(cell61.getStringCellValue());
		return capexRequestDetail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean approveCapexBudget(CapexAuditDetail capexAuditData, Integer userId) throws SumoException {
		CapexAuditDetailData capexdata = capexManagementDao.find(CapexAuditDetailData.class, capexAuditData.getId());
		String previousState = capexdata.getStatus();
		List<CapexBudgetDetailData> capexBudgetAuditDetail = capexManagementDao
				.findBudgetAudit(capexAuditData.getUnitId(), capexAuditData.getCapexRequestId());
		List<CapexBudgetDetailData> updatedBudgetData = new ArrayList<>();
		if (capexBudgetAuditDetail == null || capexBudgetAuditDetail.isEmpty()) {
			for (CapexBudgetAuditDetail budgetAudit : capexdata.getCapexBudgetAuditDetail()) {
				int budgetId = saveCapexBudgetDetailData(budgetAudit, capexAuditData , updatedBudgetData);
				updateBudgetAuditDetails(budgetAudit, capexdata, budgetId);
			}
		} else {
			/*
			 * for (CapexBudgetAuditDetail budgetAudit :
			 * capexdata.getCapexBudgetAuditDetail()) {// from // budgetAudit
			 * CapexBudgetDetailData budgetDetailData =
			 * getCapexBudgetDetailData(budgetAudit.getDepartmentId(), // to //
			 * budgetDetailData capexBudgetAuditDetail); if(budgetDetailData != null) {
			 * BigDecimal usedAmount = budgetDetailData.getBudgetAmount()
			 * .subtract(budgetDetailData.getRemainingAmount()); if
			 * (usedAmount.compareTo(budgetAudit.getAmount()) > 0) { return false; } } }
			 */

			for (CapexBudgetAuditDetail budgetAudit : capexdata.getCapexBudgetAuditDetail()) {// from // budgetAudit
				CapexBudgetDetailData budgetDetailData = getCapexBudgetDetailData(budgetAudit.getDepartmentId(), // to // budgetDetailData
						capexBudgetAuditDetail);
				if (budgetDetailData == null) {
					int budgetId = saveCapexBudgetDetailData(budgetAudit, capexAuditData, null);
					updateBudgetAuditDetails(budgetAudit, capexdata, budgetId);
					budgetDetailData = capexManagementDao.find(CapexBudgetDetailData.class, budgetId);
					if(budgetDetailData == null){
						throw new SumoException("Cannot Find Entry In CAPEX BUDGET DETAIL For Department Id " + budgetAudit.getDepartmentId());
					}
				}
				BigDecimal usedAmount = budgetDetailData.getBudgetAmount()
						.subtract(budgetDetailData.getRemainingAmount());
				if (usedAmount.compareTo(budgetAudit.getAmount()) <= 0) {
					boolean check = false;
					BigDecimal concessionAmount = (budgetAudit.getAmount().multiply(SCMServiceConstants.CAPEX_BUDGET_CONCESSION))
							.divide(new BigDecimal(100));
					BigDecimal newAmount = budgetAudit.getAmount().add(concessionAmount);
					if (budgetAudit.getAmount().compareTo(budgetDetailData.getBudgetAmount()) < 0) {
						check = true;
						updateBudgetAuditDetail(budgetDetailData, capexdata, newAmount, check, budgetAudit);

					} else {
						updateBudgetAuditDetail(budgetDetailData, capexdata, newAmount, check, budgetAudit);
					}
					budgetDetailData.setBudgetAmount(newAmount);
					budgetDetailData.setOriginalAmount(budgetAudit.getAmount());
					budgetDetailData.setRemainingAmount(newAmount.subtract(budgetDetailData.getRunningAmount()));
					budgetDetailData.setCapexAuditId(capexdata.getId());
					budgetDetailData.setCapexAuditBudgetId(budgetAudit.getId());
					capexManagementDao.update(budgetDetailData, false);
					updatedBudgetData.add(budgetDetailData);
				} else {
					LOG.error("Error in Approving Budget as Amount of Department" + budgetDetailData.getDepartmentName()
							+ " is not valid");
					return false;
				}
			}
		}
		capexdata.setStatus(CapexStatus.APPROVED.toString());
		capexdata.setApprovedDate(SCMUtil.getCurrentTimestamp());
		capexManagementDao.update(capexdata, true);
		updateCapexStatusLog(capexAuditData);
		try {
			String approvedBy = masterDataCache.getEmployee(userId) + " ["+userId+" ]";
			String uploadedBy = masterDataCache.getEmployee(Integer.parseInt(capexAuditData.getUploadedBy())) + " ["+capexAuditData.getUploadedBy()+" ]";
			CapexAuditDetailData capexAuditDetailData = capexManagementDao.find(CapexAuditDetailData.class, capexAuditData.getId());
			CapexRequestDetailData capexRequestDetailData = capexManagementDao.find(CapexRequestDetailData.class , capexAuditData.getCapexRequestId());
			String unitName = capexRequestDetailData.getUnitName() + " [" + capexRequestDetailData.getUnitId() + "]";
			String subject = "CAPEX - L3 " + CapexStatus.APPROVED.toString() + " for unit " + capexRequestDetailData.getUnitName() +
					" [" + capexRequestDetailData.getUnitId() + "] by " + masterDataCache.getEmployee(userId);
			BigDecimal totalAmount = updatedBudgetData.stream().map(CapexBudgetDetailData::getBudgetAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
			CapexEmailNotificationTemplate emailTemplate = new CapexEmailNotificationTemplate(approvedBy,props.getBasePath(),props.getEnvType(),subject,
					null,unitName,"APPROVED",totalAmount, updatedBudgetData,uploadedBy,capexAuditDetailData,String.valueOf(userId),capexRequestDetailData);
			CapexEmailNotification capexEmailNotification = new CapexEmailNotification(emailTemplate,props.getEnvType(),new String[]{"<EMAIL>"});
			try {
				LOG.info("Sending Email for Capex ");
				StringBuilder msg = new StringBuilder(":::::::::::: Capex Status Is Approved (L3) ::::::::::\n");
				sendSlackNotificationForCapexEvent(msg.toString(),capexRequestDetailData.getUnitId(),capexRequestDetailData.getUnitName(),capexRequestDetailData.getType(),
						capexRequestDetailData.getId(), capexAuditData.getVersion(),previousState ,capexRequestDetailData.getStatus(),
						userId);
				capexEmailNotification.sendEmail();
			} catch (Exception e) {
				LOG.error("Error Occurred While Sending Capex Budget approval Mail  ::: ",e);
			}
		} catch (Exception e) {
			LOG.error("Exception Occurred while sending email notification during approving budget :: ",e);
		}
		return true;
	}

	private void updateCapexStatusLog(CapexAuditDetail capexAuditData) {
		CapexRequestStatusLog capexStatusLog = capexManagementDao.findCapexStatusLog(capexAuditData.getId(),
				capexAuditData.getCapexRequestId());
		capexStatusLog.setFromStatus(capexStatusLog.getToStatus());
		capexStatusLog.setToStatus(CapexStatus.APPROVED.toString());
		capexStatusLog.setUpdatedBy(Integer.parseInt(capexAuditData.getUploadedBy()));
		capexStatusLog.setUpdateTime(SCMUtil.getCurrentTimestamp());
		capexManagementDao.update(capexStatusLog, true);
		CapexRequestDetailData capexRequestDetailData = capexManagementDao.find(CapexRequestDetailData.class,
				capexAuditData.getCapexRequestId());
		capexRequestDetailData.setStatus(CapexStatus.APPROVED.toString());
		capexRequestDetailData.setLastUpdatedBy(Integer.parseInt(capexAuditData.getUploadedBy()));
		capexRequestDetailData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
		capexManagementDao.update(capexRequestDetailData, true);
	}

	private void updateBudgetAuditDetail(CapexBudgetDetailData budgetDetailData, CapexAuditDetailData capexdata,
			BigDecimal amount, boolean check, CapexBudgetAuditDetail budgetAudit) throws SumoException {
		List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(),
				BudgetAuditActions.ORIGINAL_AMOUNT.value(), BudgetAuditActions.BUDGET_AMOUNT.value());
		for (String action : actions) {
			BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
			budgetAuditData.setCapexAuditId(capexdata.getId());
			budgetAuditData.setCapexBudgetDetailId(budgetDetailData.getId());
			budgetAuditData.setActionType(BudgetAuditActions.APPROVED.value());
			budgetAuditData.setKeyType(BudgetAuditActions.CAPEX_AUDIT_ID.value());
			budgetAuditData.setKeyValue(capexdata.getId());
			budgetAuditData.setActionBy(Integer.parseInt(capexdata.getUploadedBy()));
			budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
			if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
				budgetAuditData.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
				budgetAuditData.setPreviousValue(budgetDetailData.getRemainingAmount());
				if (check) {
					budgetAuditData.setFinalValue(amount.subtract(budgetDetailData.getRunningAmount()));
					budgetAuditData.setAction(BudgetAuditActions.REDUCTION.value());
				} else {
					budgetAuditData.setFinalValue(amount.subtract(budgetDetailData.getRunningAmount()));
					budgetAuditData.setAction(BudgetAuditActions.ADDITION.value());
				}
			} else if (action.equalsIgnoreCase(BudgetAuditActions.ORIGINAL_AMOUNT.value())) {
				budgetAuditData.setAmountType(BudgetAuditActions.ORIGINAL_AMOUNT.value());
				budgetAuditData.setPreviousValue(budgetDetailData.getOriginalAmount());
				if (check) {
					budgetAuditData
							.setFinalValue(budgetAudit.getAmount());
					budgetAuditData.setAction(BudgetAuditActions.REDUCTION.value());
				} else {
					budgetAuditData
							.setFinalValue(budgetAudit.getAmount());
					budgetAuditData.setAction(BudgetAuditActions.ADDITION.value());
				}
			} else if (action.equalsIgnoreCase(BudgetAuditActions.BUDGET_AMOUNT.value())) {
				budgetAuditData.setAmountType(BudgetAuditActions.BUDGET_AMOUNT.value());
				budgetAuditData.setPreviousValue(budgetDetailData.getBudgetAmount());
				if (check) {
					budgetAuditData.setFinalValue(amount);
					budgetAuditData.setAction(BudgetAuditActions.REDUCTION.value());
				} else {
					budgetAuditData.setFinalValue(amount);
					budgetAuditData.setAction(BudgetAuditActions.ADDITION.value());
				}
			}
			capexManagementDao.add(budgetAuditData, true);
		}

	}

	private void updateBudgetAuditDetails(CapexBudgetAuditDetail capexbudgetAudit, CapexAuditDetailData capexAuditData,
			Integer budgetId) throws SumoException {
		List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(),
				BudgetAuditActions.ORIGINAL_AMOUNT.value(), BudgetAuditActions.BUDGET_AMOUNT.value());
		for (String action : actions) {
			BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
			budgetAuditData.setCapexAuditId(capexAuditData.getId());
			budgetAuditData.setCapexBudgetDetailId(budgetId);
			budgetAuditData.setPreviousValue(BigDecimal.ZERO);
			budgetAuditData.setActionType(BudgetAuditActions.APPROVED.value());
			budgetAuditData.setAction(BudgetAuditActions.ADDITION.value());
			budgetAuditData.setKeyType(BudgetAuditActions.CAPEX_AUDIT_ID.value());
			budgetAuditData.setKeyValue(capexAuditData.getId());
			budgetAuditData.setActionBy(Integer.parseInt(capexAuditData.getUploadedBy()));
			budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
			BigDecimal newAmount = (capexbudgetAudit.getAmount()
					.multiply(SCMServiceConstants.CAPEX_BUDGET_CONCESSION)).divide(new BigDecimal(100));
			if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
				budgetAuditData.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
				budgetAuditData.setFinalValue(capexbudgetAudit.getAmount().add(newAmount));
			} else if (action.equalsIgnoreCase(BudgetAuditActions.ORIGINAL_AMOUNT.value())) {
				budgetAuditData.setAmountType(BudgetAuditActions.ORIGINAL_AMOUNT.value());
				budgetAuditData.setFinalValue(capexbudgetAudit.getAmount());
			} else if (action.equalsIgnoreCase(BudgetAuditActions.BUDGET_AMOUNT.value())) {
				budgetAuditData.setAmountType(BudgetAuditActions.BUDGET_AMOUNT.value());
				budgetAuditData.setFinalValue(capexbudgetAudit.getAmount().add(newAmount));
			}
			capexManagementDao.add(budgetAuditData, true);
		}

	}

	private int saveCapexBudgetDetailData(CapexBudgetAuditDetail budgetAudit, CapexAuditDetail capexAuditData, List<CapexBudgetDetailData> updatedBudgetData)
			throws SumoException {
		CapexBudgetDetailData capexBudgetDetailData = new CapexBudgetDetailData();
		capexBudgetDetailData.setUnitId(capexAuditData.getUnitId());
		capexBudgetDetailData.setBudgetType(capexAuditData.getType());
		capexBudgetDetailData.setCapexRequestId(capexAuditData.getCapexRequestId());
		capexBudgetDetailData.setCapexAuditId(capexAuditData.getId());
		capexBudgetDetailData.setCapexAuditBudgetId(budgetAudit.getId());
		capexBudgetDetailData.setDepartmentId(budgetAudit.getDepartmentId());
		capexBudgetDetailData.setDepartmentName(budgetAudit.getDepartmentName());
		BigDecimal concessionAmount = (budgetAudit.getAmount().multiply(SCMServiceConstants.CAPEX_BUDGET_CONCESSION))
				.divide(new BigDecimal(100));
		capexBudgetDetailData.setBudgetAmount(budgetAudit.getAmount().add(concessionAmount));
		capexBudgetDetailData.setOriginalAmount(budgetAudit.getAmount());
		capexBudgetDetailData.setInitialAmount(budgetAudit.getAmount());
		capexBudgetDetailData.setRemainingAmount(budgetAudit.getAmount().add(concessionAmount));
		capexBudgetDetailData.setRunningAmount(BigDecimal.ZERO);
		capexBudgetDetailData.setReceivingAmount(BigDecimal.ZERO);
		capexBudgetDetailData.setPaidAmount(BigDecimal.ZERO);
		capexBudgetDetailData.setExtraReceiving(BigDecimal.ZERO);
		capexBudgetDetailData.setStatus(AppConstants.ACTIVE);
		capexBudgetDetailData = capexManagementDao.add(capexBudgetDetailData, true);
		if (Objects.nonNull(updatedBudgetData)) {
			updatedBudgetData.add(capexBudgetDetailData);
		}
		return capexBudgetDetailData.getId();
	}

	@Override
	public CapexRequestDetailData getCapexRequest(Integer capexRequestId) {
		CapexRequestDetailData capexRequest = capexManagementDao.find(CapexRequestDetailData.class, capexRequestId);
		return capexRequest;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean changeCapexStatus(Integer capexId, String status, String userId, String comment) throws SumoException {
		CapexAuditDetailData capexAuditData = capexManagementDao.find(CapexAuditDetailData.class, capexId);
		capexAuditData.setStatus(status);
		capexManagementDao.update(capexAuditData, true);

		CapexRequestDetailData capexRequestDetailData = capexManagementDao.find(CapexRequestDetailData.class,
				capexAuditData.getCapexRequestId());
		capexRequestDetailData.setStatus(status);
		capexRequestDetailData.setLastUpdatedBy(Integer.parseInt(userId));
		capexRequestDetailData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
		if(status.equalsIgnoreCase(CapexStatus.DOCUMENT_UPLOADED.value())) {
			capexRequestDetailData.setComment(comment);
		}
		capexManagementDao.update(capexRequestDetailData, true);

		CapexRequestStatusLog capexStatusLog = capexManagementDao.findCapexStatusLog(capexAuditData.getId(),
				capexAuditData.getCapexRequestId());
		capexStatusLog.setFromStatus(capexStatusLog.getToStatus());
		capexStatusLog.setToStatus(status);
		if (status.equalsIgnoreCase(CapexStatus.CLOSED_L3.value())) {
			capexStatusLog.setStatus(CapexStatus.IN_ACTIVE.toString());
		} else {
			capexStatusLog.setStatus(CapexStatus.ACTIVE.toString());
		}
		capexStatusLog.setUpdatedBy(Integer.parseInt(userId));
		capexStatusLog.setUpdateTime(SCMUtil.getCurrentTimestamp());
		capexManagementDao.update(capexStatusLog, true);
		StringBuilder msg = new StringBuilder(":::::::::::: Capex Status Is Changed ::::::::::\n");
		sendSlackNotificationForCapexEvent(msg.toString(),capexRequestDetailData.getUnitId(),capexRequestDetailData.getUnitName(),capexRequestDetailData.getType(),
				capexRequestDetailData.getId(),capexAuditData.getVersion(),capexStatusLog.getFromStatus(),capexStatusLog.getToStatus(),Integer.parseInt(userId));
		return true;
	}

	private String checksBeforeCapexClosure(CapexRequestDetailData capexRequestDetailData) {
		List<BusinessCostCenter> bccs = getBusinessCostCentersData();
		BusinessCostCenter bcc = bccs.stream()
				.filter(b -> b.getCode().equalsIgnoreCase(capexRequestDetailData.getUnitId().toString())).findFirst().orElse(null);
		String data = capexManagementDao.findSOSrPrInOpenedStates(bcc.getId());
		return data;
	}

	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<BusinessCostCenter> getBusinessCostCentersData() {
		List<BusinessCostCenter> list = new ArrayList<>();
		for (BusinessCostCenterData d : capexManagementDao.findAll(BusinessCostCenterData.class)) {
			if (AppConstants.ACTIVE.equals(d.getStatus())) {
				list.add(SCMDataConverter.convertToIdCodeName(d, masterDataCache));
			}
		}
		return list;
	}

	@Override
	public List<DepartmentBudgetVO> getDepartmentData(MultipartFile file) throws IOException, SumoException {
		InputStream excelFile = file.getInputStream();
		XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
		XSSFSheet sheet1 = workbook.getSheetAt(1);
		int lastRowNumber = sheet1.getLastRowNum();
		List<DepartmentBudgetVO> departmentList = new ArrayList<DepartmentBudgetVO>();
		try{
			FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
			evaluator.evaluateAll();
		}catch (Exception e){
			throw new SumoException("Invalid File","Error while evaluating formulas error : "+e.getMessage());
		}
		for (int i = 4; i < lastRowNumber; i++) {
			DepartmentBudgetVO departmentVo = new DepartmentBudgetVO();
			XSSFRow row41 = sheet1.getRow(i);
			departmentVo.setDeptId((int) row41.getCell(1).getNumericCellValue());
			departmentVo.setDeptName(row41.getCell(2).getStringCellValue());
			departmentVo.setAmount(new BigDecimal(row41.getCell(3).getNumericCellValue()));
			departmentList.add(departmentVo);
		}
		return departmentList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<DepartmentBudgetVO> showDepartmentBudgetForApproval(Integer capexAuditId) {
		CapexAuditDetailData capexAUditData = capexManagementDao.find(CapexAuditDetailData.class, capexAuditId);
		List<DepartmentBudgetVO> departmentList = new ArrayList<DepartmentBudgetVO>();
		for(CapexBudgetAuditDetail capexBudget : capexAUditData.getCapexBudgetAuditDetail()) {
			DepartmentBudgetVO departmentVo = new DepartmentBudgetVO();
			departmentVo.setDeptId(capexBudget.getDepartmentId());
			departmentVo.setDeptName(capexBudget.getDepartmentName());
			departmentVo.setAmount(capexBudget.getAmount());
			departmentList.add(departmentVo);
		}
		return departmentList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean approvalStatusBudget(Integer auditId, String status, String type, String userId, Boolean isRequiredForL2L3Approval) {
		String newStatus = new String();
		String previousStatus = "";
		List<DepartmentBudgetVO> budgets = new ArrayList<>();
		try {
			budgets = showDepartmentBudgetForApproval(auditId);
		}
		catch (Exception e) {
			LOG.error("Exception occurred while getting budgets ::: ",e);
		}
		if(status.equalsIgnoreCase(CapexStatus.APPROVED.toString())) {
			if(isRequiredForL2L3Approval) {if(type.equalsIgnoreCase("L1")) {
				newStatus = CapexStatus.PENDING_APPROVAL_L2.toString();
			}
			else {
				newStatus = CapexStatus.PENDING_APPROVAL_L3.toString();}
			}
		}
		else {
			if(isRequiredForL2L3Approval) {if(type.equalsIgnoreCase("L1")) {
				newStatus = CapexStatus.REJECTED_L1.toString();
			}
			else if (type.equalsIgnoreCase("L2")) {
				newStatus = CapexStatus.REJECTED_L2.toString();} else {
					newStatus = CapexStatus.REJECTED_L3.toString();
				}
			}
			else{
				if (type.equalsIgnoreCase("L1")) {
					newStatus = CapexStatus.REJECTED_L1.toString();
				}
			}
		}
		try {
			CapexAuditDetailData capexAuditData = capexManagementDao.find(CapexAuditDetailData.class, auditId);
			previousStatus = capexAuditData.getStatus();
			capexAuditData.setStatus(newStatus);
			capexManagementDao.update(capexAuditData, true);
			CapexRequestStatusLog capexStatusLog = capexManagementDao.findCapexStatusLog(capexAuditData.getId(),capexAuditData.getCapexRequestId());
			capexStatusLog.setFromStatus(capexStatusLog.getToStatus());
			capexStatusLog.setToStatus(newStatus);
			capexStatusLog.setUpdatedBy(Integer.parseInt(userId));
			capexStatusLog.setUpdateTime(SCMUtil.getCurrentTimestamp());
			capexManagementDao.update(capexStatusLog, true);
			String approvedBy = masterDataCache.getEmployee(Integer.parseInt(userId)) + " ["+userId+" ]";
			String uploadedBy = masterDataCache.getEmployee(Integer.parseInt(capexAuditData.getUploadedBy())) + " ["+capexAuditData.getUploadedBy()+" ]";
			CapexRequestDetailData capexRequestDetailData = capexManagementDao.find(CapexRequestDetailData.class , capexAuditData.getCapexRequestId());
			String unitName = capexRequestDetailData.getUnitName() + " [" + capexRequestDetailData.getUnitId() + "]";
			String subject = "CAPEX " + type + " " + status + " for unit " + capexRequestDetailData.getUnitName() + " [" + capexRequestDetailData.getUnitId() + "] " +
					"by " + masterDataCache.getEmployee(Integer.parseInt(userId));
			BigDecimal totalAmount = budgets.stream().map(DepartmentBudgetVO::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
			CapexEmailNotificationTemplate emailTemplate = new CapexEmailNotificationTemplate(approvedBy,props.getBasePath(),props.getEnvType(),subject,budgets,
					unitName,status,totalAmount,new ArrayList<>(),uploadedBy,capexAuditData,userId,capexRequestDetailData);
			CapexEmailNotification capexEmailNotification = new CapexEmailNotification(emailTemplate,props.getEnvType(),new String[]{"<EMAIL>"});
			try {
				LOG.info("Sending Email for Capex ");
				capexEmailNotification.sendEmail();
				StringBuilder msg = new StringBuilder(":::::::::::: Capex  Is " + status + "(" + type + ") ::::::::::\n");
				sendSlackNotificationForCapexEvent(msg.toString(),capexRequestDetailData.getId(),capexRequestDetailData.getUnitName(),capexRequestDetailData.getType(),
						capexRequestDetailData.getId(),capexAuditData.getVersion(),previousStatus,newStatus,Integer.parseInt(userId));
			} catch (Exception e) {
				LOG.error("Error Occurred While Generating Service Received Mail  ::: ",e);
			}
		} catch (Exception e) {
			LOG.error("Error in Changing Status.", e);
			return false;
		}
		return true;
	}

	@Override
	public Set<String> getCapexVersionList() {
		List<CapexAuditDetailData> capexAuditDetailData = capexManagementDao.findAll(CapexAuditDetailData.class);
		Set<String> versionList = new HashSet<String>();
		for(CapexAuditDetailData capexData : capexAuditDetailData) {
			versionList.add(capexData.getVersion());
		}
		return versionList;
	}

	@Override
	public List<CapexBudgetDetail> fetchBudgetDetails(Integer unitId, Integer capexRequestId, Boolean isExternal) {
		List<CapexBudgetDetail> capexBudgetDetails = capexManagementDao.findBudgetDetails(unitId, capexRequestId);
		Map<Integer, List<T>> getToSummaryDetails = new HashMap<>();
		Map<Integer, List<T>> getRoSummaryDetails = new HashMap<>();
		if(CollectionUtils.isNotEmpty(capexBudgetDetails) && (Objects.nonNull(isExternal) && !isExternal)) {
			getToSummaryDetails = capexManagementDao.findToAndRoForCapexAndDepartment(OrderTypes.TO, capexRequestId);
			getRoSummaryDetails = capexManagementDao.findToAndRoForCapexAndDepartment(OrderTypes.RO, capexRequestId);
		}
	    for(CapexBudgetDetail c : capexBudgetDetails){
			if(c.getOriginalAmount().compareTo(BigDecimal.valueOf(0)) == 1)  {
				if (Objects.isNull(isExternal) || isExternal) {
					setSoPoSummary(c, capexRequestId, c.getDepartmentId());
				} else {
					if(getRoSummaryDetails.containsKey(c.getDepartmentId())) {
						c.setTotalRO(getRoSummaryDetails.get(c.getDepartmentId()).size());
					}
					if(getToSummaryDetails.containsKey(c.getDepartmentId())) {
						c.setTotalTO(getToSummaryDetails.get(c.getDepartmentId()).size());
					}
				}
			}
		}
		return capexBudgetDetails;
	}

	@Override
	public boolean uploadClosureSheets(MultipartFile file, String sheetName, String unitId,
			String capexRequestId) {
		String fileName = sheetName +"-"+ unitId +"-"+ capexRequestId+"."+MimeType.PDF;
		FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), props.getCapexClosureBucket(), fileName, file, true);
		return true;
	}

	@Override
	public String getClosureComment(Integer capexRequestId) {
		CapexRequestDetailData capexRequestDetail = capexManagementDao.find(CapexRequestDetailData.class, capexRequestId);
		return capexRequestDetail.getComment();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String initiateClosureState(Integer capexRequestId, String status, String userId, String comment) throws SumoException {
		CapexAuditDetailData capexAuditData = capexManagementDao.find(CapexAuditDetailData.class, capexRequestId);
		CapexRequestDetailData capexRequestDetailData = capexManagementDao.find(CapexRequestDetailData.class,
				capexAuditData.getCapexRequestId());
		String data = checksBeforeCapexClosure(capexRequestDetailData);
		if(data.isEmpty()) {
			changeCapexStatus(capexRequestId, status, userId, comment);
			StringBuilder msg = new StringBuilder(":::::::::::: Capex  Is Inititated For Closure  ::::::::::\n");
			sendSlackNotificationForCapexEvent(msg.toString(),capexRequestDetailData.getId(),capexRequestDetailData.getUnitName(),capexRequestDetailData.getType(),
					capexRequestDetailData.getId(),capexAuditData.getVersion(),"-",capexRequestDetailData.getStatus(),Integer.parseInt(userId));
		}
		return data;
	}

	@Override
	public List<BudgetComparisionDetail> getBudgetComparision(Integer unitId, Integer capexRequestId, Integer currentCapexId) {
		try {
			List<BudgetComparisionDetail> result = new ArrayList<>();
			List<CapexBudgetAuditDetail> budgetAuditDetails = capexManagementDao.findCapexBudgetAuditDetailData(currentCapexId);
			List<CapexBudgetDetailData> capexBudgetDetailDataList = capexManagementDao.findCapexBudgetDetailData(unitId, capexRequestId);
			if(budgetAuditDetails.size() == 0) {
				if (capexBudgetDetailDataList.size() == 0) {
					LOG.info("Both audit and budget details are not found");
					return null;
				} else {
					for (CapexBudgetDetailData data : capexBudgetDetailDataList) {
						BudgetComparisionDetail entry = new BudgetComparisionDetail();
						entry.setDepartmentName(data.getDepartmentName());
						entry.setDepartmentId(data.getDepartmentId());
						entry.setLastApprovedOriginalAmount(data.getOriginalAmount());
						entry.setLastApprovedBudgetAmount(data.getBudgetAmount());
						result.add(entry);
					}
					LOG.info("No audit details are found");
					return result.isEmpty() ?null:result;
				}
			}

			for (CapexBudgetAuditDetail budgetAuditDetail : budgetAuditDetails) {
				BudgetComparisionDetail entry = new BudgetComparisionDetail();
				entry.setDepartmentName(budgetAuditDetail.getDepartmentName());
				entry.setDepartmentId(budgetAuditDetail.getDepartmentId());
				entry.setCurrentOriginalAmount(budgetAuditDetail.getAmount());
				Optional<CapexBudgetDetailData> data = capexBudgetDetailDataList.stream().
						filter(item -> item.getDepartmentName().equalsIgnoreCase(entry.getDepartmentName())).findFirst();
				if (data.isPresent()) {
					entry.setLastApprovedOriginalAmount(data.get().getOriginalAmount());
					entry.setLastApprovedBudgetAmount(data.get().getBudgetAmount());
				}
				else{
					LOG.info("Budget Data not present for {}",budgetAuditDetail.getDepartmentName());
				}
				result.add(entry);
			}
            return result.isEmpty() ?null:result;
		}
		catch (Exception e){
			LOG.error("Error Occurred while comparing budget!!!!",e);
		}

		return null;
	}

	private Integer getBusinessCostCenterForUnit(Integer unitId) {
		Integer bccForUnit= -1;
		for (BusinessCostCenterData d : serviceOrderManagementDao.findAll(BusinessCostCenterData.class)) {
			if (AppConstants.ACTIVE.equals(d.getStatus())) {
			   if(d.getCode().equalsIgnoreCase(unitId.toString())){
				   bccForUnit = d.getId();
				   break;
			   }
			}
		}

		return bccForUnit;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ServiceOrderShort> getSOsByDepartment (Integer unitId , Integer capexRequestId, Integer departmentId , Integer bcc){
		if(unitId==null){
				unitId = capexManagementDao.getUnitFromCapexId(capexRequestId);
		}
		Date lastApprovedDate = serviceOrderManagementDao.getStartDateOfCapexBudget(capexRequestId);
		List<Integer> costElementIds = serviceOrderManagementDao.findCostElementsForDepartment(departmentId);
		Integer bccId = Objects.isNull(bcc) ? getBusinessCostCenterForUnit(unitId) : bcc;
		List<ServiceOrderStatus> statusList = Arrays.asList(ServiceOrderStatus.CREATED, ServiceOrderStatus.PENDING_APPROVAL_L1,
				ServiceOrderStatus.PENDING_APPROVAL_L2, ServiceOrderStatus.PENDING_APPROVAL_L3,
				ServiceOrderStatus.PENDING_APPROVAL_L4, ServiceOrderStatus.PENDING_APPROVAL_L5,
				ServiceOrderStatus.PENDING_APPROVAL_L6, ServiceOrderStatus.APPROVED, ServiceOrderStatus.CLOSED, ServiceOrderStatus.IN_PROGRESS);

		List<ServiceOrderShort> soList = serviceOrderManagementDao.findServiceOrders(bccId, null, null, statusList, lastApprovedDate, AppUtils.getCurrentTimestamp()
				, costElementIds, false, true,null,null).stream().map(serviceOrderData ->
				SCMDataConverter.convertToShortFilterByCostElementIds(serviceOrderData, scmCache, true, costElementIds)).collect(Collectors.toList());

		List<Integer> soIds = soList.stream().map(ServiceOrderShort::getId).collect(Collectors.toList());

		Map<Integer, BigDecimal> soTotalPaidAmountMap = serviceOrderManagementDao.getPaidAmount(soIds);

		for (ServiceOrderShort sos : soList) {
			for (ServiceOrderItem soi : sos.getOrderItems()) {
				soi.setPaidAmount(soTotalPaidAmountMap.getOrDefault(soi.getId(), BigDecimal.ZERO));
			}
		}

		return soList;
	}

	public void setSoPoSummary(CapexBudgetDetail capexBudgetDetail,Integer capexRequestId, Integer departmentId){

		List<ServiceOrderData> soLists = capexManagementDao.findServiceOrderFromCapexIdAndDepartment(capexRequestId,departmentId,null,null);
		List<PurchaseOrderData> poLists = new ArrayList<>();
		if(departmentId==37 || departmentId==38){
		poLists = capexManagementDao.findPurchaseOrderFromCapexIdAndDepartment(capexRequestId,departmentId,null,null);
		}
		Integer soVendorCount = capexManagementDao.soVendorCount(capexRequestId,departmentId);
		Integer poVendorCount = capexManagementDao.poVendorCount(capexRequestId,departmentId);
		int pendingSo =0;
		for(ServiceOrderData so:soLists){
			if(so.getStatus().equals(ServiceOrderStatus.PENDING_APPROVAL_L1.value()) || so.getStatus().equals(ServiceOrderStatus.PENDING_APPROVAL_L2.value())
					||so.getStatus().equals(ServiceOrderStatus.PENDING_APPROVAL_L3.value())||so.getStatus().equals(ServiceOrderStatus.PENDING_APPROVAL_L4.value())||
					so.getStatus().equals(ServiceOrderStatus.PENDING_APPROVAL_L5.value()) || so.getStatus().equals(ServiceOrderStatus.PENDING_APPROVAL_L6.value())
					||so.getStatus().equals(ServiceOrderStatus.PENDING_HOD_APPROVAL.value()) || so.getStatus().equals(ServiceOrderStatus.FIN_APPROVAL_L1.value())  ){
				pendingSo++;
			}
		}
		int pendingPo =0;
		for(PurchaseOrderData po : poLists){
			if(po.getStatus().equals(PurchaseOrderStatus.CREATED.value())){ pendingPo++; }
		}
		capexBudgetDetail.setTotalSO(soLists.size());
		capexBudgetDetail.setSoPendingApproval(pendingSo);
		capexBudgetDetail.setTotalPO(poLists.size());
		capexBudgetDetail.setPoPendingApproval(pendingPo);
		capexBudgetDetail.setVendorCount(soVendorCount+poVendorCount);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ServiceOrderSummary getSoByCapexId(Integer capexRequestId, Integer vendorId) {
		List<ServiceOrderStatus> statusList = Arrays.asList(ServiceOrderStatus.CREATED, ServiceOrderStatus.PENDING_APPROVAL_L1,
				ServiceOrderStatus.PENDING_APPROVAL_L2, ServiceOrderStatus.PENDING_APPROVAL_L3,
				ServiceOrderStatus.PENDING_APPROVAL_L4, ServiceOrderStatus.PENDING_APPROVAL_L5,
				ServiceOrderStatus.PENDING_APPROVAL_L6,ServiceOrderStatus.FIN_APPROVAL_L1, ServiceOrderStatus.PENDING_HOD_APPROVAL);
		List<ServiceOrderData> soLists = capexManagementDao.findServiceOrderFromCapexIdAndDepartment(capexRequestId,null,vendorId,statusList);
		 return  serviceOrderManagementService.convertServiceOrdersShort(soLists);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PurchaseOrder> getPoByCapexId(Integer capexRequestId, Integer vendorId) {
		List<PurchaseOrderStatus> statusList = Arrays.asList(PurchaseOrderStatus.CREATED);
        List<PurchaseOrderData> poList = capexManagementDao.findPurchaseOrderFromCapexIdAndDepartment(capexRequestId,null,vendorId,statusList);

		List<PurchaseOrder> purchaseOrders = poList.stream().map(
						purchaseOrderData -> {
							PurchaseOrder purchaseOrder = SCMDataConverter.convert(purchaseOrderData, scmCache, masterDataCache, false, null);
							return purchaseOrder;
						}).collect(Collectors.<PurchaseOrder>toList());

		return purchaseOrders;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer,Boolean> getCapexValidationBySoPo(List<Integer> soPoIds, String type){
		Date dateForCheck =  AppUtils.addDays( AppUtils.getCurrentDate(),-60);
		Set<Integer> unitIds = masterDataCache.getAllUnits().stream().filter(u -> u.getStatus() == UnitStatus.ACTIVE && (u.getCategory() == UnitCategory.CAFE || u.getCategory() == UnitCategory.KITCHEN || u.getCategory() == UnitCategory.WAREHOUSE))
				.map(UnitBasicDetail::getId).collect(Collectors.toSet());

		Set<Integer> checkUnits = unitIds.stream().filter(unitId ->{
			Unit unit = masterDataCache.getUnit(unitId);
			if(Objects.isNull(unit.getHandoverDate())){
				return true;
			}
			if(unit.getHandoverDate().before(dateForCheck)){
				return true;
			}
			return false;
		}).collect(Collectors.toSet());
        Map<Integer,Boolean> res = new HashMap<>();
		soPoIds.forEach(e-> res.put(e,capexManagementDao.isSoPoFromExpiredCapex(e,dateForCheck,type,checkUnits)));
        return res;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ServiceOrder> getServiceOrdersByCapexAndDept(Integer capexId, Integer deptId){
		List<ServiceOrderData> soLists = capexManagementDao.findServiceOrderFromCapexIdAndDepartment(capexId,deptId,null,null);
		Map<Integer, BigDecimal> soTotalPaidAmountMap = serviceOrderManagementDao.getSOLevelPaidAmount(soLists.stream().map(ServiceOrderData::getId).collect(Collectors.toList()));
		 List<ServiceOrder> sos =  serviceOrderManagementService.convertServiceOrders(soLists);
		for (ServiceOrder so : sos) {
				so.setPrAmount(soTotalPaidAmountMap.getOrDefault(so.getId(), BigDecimal.ZERO));

		}
		return sos.size()>0 ? sos : new ArrayList<>();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PurchaseOrder> getPurchaseOrderByCapexIdAndDepartment(Integer capexId, Integer deptId) throws SumoException {
		List<PurchaseOrderData> poList = capexManagementDao.findPurchaseOrderFromCapexIdAndDepartment(capexId,deptId,null,null);
		List<PurchaseOrder> purchaseOrders = poList.stream().map(purchaseOrderData -> {
					PurchaseOrder purchaseOrder = SCMDataConverter.convert(purchaseOrderData, scmCache, masterDataCache, false, null);
					return purchaseOrder;
				}).collect(Collectors.<PurchaseOrder>toList());

		List<PurchaseOrder> poClubs = purchaseOrders.stream().filter(e-> {
			try {
				return purchaseOrderManagementDao.isPoClubInGr(e.getId());
			} catch (SumoException ex) {
				throw new RuntimeException(ex);
			}
		}).collect(Collectors.toList());

		poClubs.forEach(e-> {e.setPrAmount(BigDecimal.valueOf(-1));});

		List<PurchaseOrder> singlePo = purchaseOrders.stream().filter(e-> {
			try {
				return !purchaseOrderManagementDao.isPoClubInGr(e.getId());
			} catch (SumoException ex) {
				throw new RuntimeException(ex);
			}
		}).collect(Collectors.toList());

       Map<Integer,BigDecimal> poToPaidAmount = purchaseOrderManagementDao.getPoLevelPaidAmount(singlePo.stream().map(PurchaseOrder::getId).collect(Collectors.toList()));

	   singlePo.forEach(e -> {
		   e.setPrAmount(poToPaidAmount.getOrDefault(e.getId(), BigDecimal.valueOf(0)));
	   });

	   singlePo.addAll(poClubs);

	   Map<Integer,BigDecimal> poToAdvancePayment  = purchaseOrderManagementDao.getPoAdvancePaymentData(singlePo.stream().map(PurchaseOrder::getId).collect(Collectors.toList()));

		singlePo.forEach(e -> {
			e.setAdvanceAmount(poToAdvancePayment.getOrDefault(e.getId(), BigDecimal.valueOf(0)));
		});
	   return singlePo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PoItemLevelSummary> getPoItemLevelSummary(Integer capexId, Integer deptId) throws SumoException {
		List<PurchaseOrderData> poList = capexManagementDao.findPurchaseOrderFromCapexIdAndDepartment(capexId,deptId,null,null);
		List<PurchaseOrder> purchaseOrders = poList.stream().map(purchaseOrderData -> {
			PurchaseOrder purchaseOrder = SCMDataConverter.convert(purchaseOrderData, scmCache, masterDataCache, false, null);
			return purchaseOrder;
		}).collect(Collectors.<PurchaseOrder>toList());
		return purchaseOrderManagementDao.getPoItemLevelSummary(purchaseOrders.stream().map(PurchaseOrder::getId).collect(Collectors.toList()));
	}

	@Override
	public <T> List<T> getToRoItemLevelSummary(Integer capexId, Integer departmentId, String type) {
		if(OrderTypes.TO.name().equals(type))  {
			List<TransferOrderData> data = capexManagementDao.findToAndRoForCapexAndDepartment(OrderTypes.TO, capexId, departmentId);
			return getTransferOrder(data);
		} else {
			List<RequestOrderData> data = capexManagementDao.findToAndRoForCapexAndDepartment(OrderTypes.RO, capexId, departmentId);
			return getRequestOrder(data);
		}
	}

	private <T> List<T> getTransferOrder(List<TransferOrderData> data) {
		List<TransferOrder> toList = new ArrayList<>();
		for(TransferOrderData toData : data) {
			TransferOrder to = SCMDataConverter.convertTransferOrderDataBasicDetails(toData, scmCache, masterDataCache);
			toList.add(to);
		}
		return (List<T>) toList;
	}

	private <T> List<T> getRequestOrder(List<RequestOrderData> data) {
		List<RequestOrder> roList = new ArrayList<>();
		for(RequestOrderData roData : data) {
			RequestOrder ro = SCMDataConverter.convertRequestOrderBAsicDetails(roData, scmCache, masterDataCache);
			roList.add(ro);
		}
		return (List<T>) roList;
	}

	@Override
	public View downloadToRoItemLevelSummary(Integer capexId, Integer departmentId, String type) throws Exception {
		OrderTypes orderType = OrderTypes.TO.name().equals(type) ? OrderTypes.TO : OrderTypes.RO;

		if(OrderTypes.TO.equals(orderType)) {
			List<TransferOrderItemData> data =  capexManagementDao.findToAndRoItemsForCapexAndDepartment(orderType, capexId, departmentId);
			return toExcelHeaderAndBody(data);
		} else {
			List<RequestOrderItemData> data =  capexManagementDao.findToAndRoItemsForCapexAndDepartment(orderType, capexId, departmentId);
			return roExcelHeaderAndBody(data);
		}
	}

	private View toExcelHeaderAndBody(List<TransferOrderItemData> dataList) throws Exception {
		ExcelRequestData excelData = new ExcelRequestData();
		excelData.setFileName("TransferOrderData");

		List<String> headerNames = new ArrayList<>();
		headerNames.add("TO Id");
		headerNames.add("TO Item Id");
		headerNames.add("Product Id");
		headerNames.add("Product Name");
		headerNames.add("Requested Quantity");
		headerNames.add("Requested Absolute Quantity");
		headerNames.add("Transferred Quantity");
		headerNames.add("Received Quantity");
		headerNames.add("Unit Price");
		headerNames.add("Negotiated Unit Price");
		headerNames.add("Calculated Amount");
		headerNames.add("Tax Code");
		headerNames.add("Tax Amount");
		headerNames.add("Status");
		excelData.setHeaderNames(headerNames);

		List<Object[]> body = new ArrayList<>();
		for(TransferOrderItemData data: dataList) {
			Object[] newObjectData = new Object[14];
			newObjectData[0] = data.getTransferOrderData().getId();
			newObjectData[1] = data.getId();
			newObjectData[2] = data.getSkuId();
			newObjectData[3] = data.getSkuName();
			newObjectData[4] = data.getRequestedQuantity();
			newObjectData[5] = data.getRequestedAbsoluteQuantity();
			newObjectData[6] = data.getTransferredQuantity();
			newObjectData[7] = data.getReceivedQuantity();
			newObjectData[8] = data.getUnitPrice();
			newObjectData[9] = data.getNegotiatedUnitPrice();
			newObjectData[10] = data.getCalculatedAmount();
			newObjectData[11] = data.getTaxCode();
			newObjectData[12] = data.getTaxAmount();
			newObjectData[13] = data.getTransferOrderData().getStatus();
			body.add(newObjectData);
		}
		excelData.setBody(body);
		return genericExcelManagementService.downloadExcelFromRequestData(excelData);
	}

	private View roExcelHeaderAndBody(List<RequestOrderItemData> dataList) throws Exception {
		ExcelRequestData excelData = new ExcelRequestData();
		excelData.setFileName("RequestOrderData");

		List<String> headerNames = new ArrayList<>();
		headerNames.add("RO Id");
		headerNames.add("RO Item Id");
		headerNames.add("Product Id");
		headerNames.add("Product Name");
		headerNames.add("Requested Quantity");
		headerNames.add("Requested Absolute Quantity");
		headerNames.add("Transferred Quantity");
		headerNames.add("Received Quantity");
		headerNames.add("Unit Price");
		headerNames.add("Negotiated Unit Price");
		headerNames.add("Calculated Amount");
		headerNames.add("Tax Code");
		headerNames.add("Tax Amount");
		headerNames.add("Status");
		excelData.setHeaderNames(headerNames);

		List<Object[]> body = new ArrayList<>();
		for(RequestOrderItemData data: dataList) {
			Object[] newObjectData = new Object[14];
			newObjectData[0] = data.getRequestOrderData().getId();
			newObjectData[1] = data.getId();
			newObjectData[2] = data.getProductId();
			newObjectData[3] = data.getProductName();
			newObjectData[4] = data.getRequestedQuantity();
			newObjectData[5] = data.getRequestedAbsoluteQuantity();
			newObjectData[6] = data.getTransferredQuantity();
			newObjectData[7] = data.getReceivedQuantity();
			newObjectData[8] = data.getUnitPrice();
			newObjectData[9] = data.getNegotiatedUnitPrice();
			newObjectData[10] = data.getCalculatedAmount();
			newObjectData[11] = data.getTaxCode();
			newObjectData[12] = data.getTaxAmount();
			newObjectData[13] = data.getRequestOrderData().getStatus();
			body.add(newObjectData);
		}
		excelData.setBody(body);
		return genericExcelManagementService.downloadExcelFromRequestData(excelData);
	}


	@Override
	public OrderTypesSummary findAmountOrderSummary(Integer capexId, boolean isExternal) throws SumoException {
		OrderTypesSummary orderTypesSummary = new OrderTypesSummary();
		if(isExternal) {
			orderTypesSummary.setTotalSoAmount(findOrderAmountFromCapexId(capexId, OrderTypes.SO).setScale(2, RoundingMode.HALF_DOWN));
			orderTypesSummary.setTotalPoAmount(findOrderAmountFromCapexId(capexId, OrderTypes.PO).setScale(2, RoundingMode.HALF_DOWN));
			orderTypesSummary.setTotalUploadedAmount(getTotalUploadedAmountForCapex(capexId).setScale(2, RoundingMode.HALF_DOWN));
		} else {
			orderTypesSummary.setTotalToAmount(findOrderAmountFromCapexId(capexId, OrderTypes.TO).setScale(2, RoundingMode.HALF_DOWN));
			orderTypesSummary.setTotalRoAmount(findOrderAmountFromCapexId(capexId, OrderTypes.RO).setScale(2, RoundingMode.HALF_DOWN));
		}
		return orderTypesSummary;
	}

	public BigDecimal findOrderAmountFromCapexId(Integer capexRequestId, OrderTypes type) throws SumoException {
		List<String> statusList = new ArrayList<>();
		if(type.equals(OrderTypes.SO)) {
			statusList = List.of(
					ServiceOrderStatus.REJECTED_L1.name(), ServiceOrderStatus.REJECTED_L2.name(),
					ServiceOrderStatus.REJECTED_L3.name(), ServiceOrderStatus.REJECTED_L4.name(),
					ServiceOrderStatus.FIN_REJECTED_L1.name(), ServiceOrderStatus.REJECTED_VENDOR_APPROVAL.name(),
					ServiceOrderStatus.REJECTED.name(), ServiceOrderStatus.CANCELLED.name()
			);
		} else if(type.equals(OrderTypes.PO) || type.equals(OrderTypes.TO) || type.equals(OrderTypes.RO)) {
			statusList = List.of(PurchaseOrderStatus.CANCELLED.name(), PurchaseOrderStatus.REJECTED.name());
		} else {
			throw new SumoException("Invalid class type : " + type);
		}
		return capexManagementDao.findOrderAmountFromCapexId(capexRequestId, statusList, type);
	}

	public BigDecimal getTotalUploadedAmountForCapex(Integer capexId) {
		CapexAuditDetail auditDetail = capexManagementDao.findAuditDetail(capexId);
		if(Objects.isNull(auditDetail)) {
			return BigDecimal.ZERO;
		}
		try {
				BigDecimal uploadedAmount = BigDecimal.ZERO;
				List<BudgetComparisionDetail> budgetComparisonDetails = getBudgetComparision(auditDetail.getUnitId(), capexId, auditDetail.getId());
				if (Objects.nonNull(budgetComparisonDetails) && budgetComparisonDetails.size() > 0) {
					for (BudgetComparisionDetail budgetComparisionDetail : budgetComparisonDetails) {
						uploadedAmount = uploadedAmount.add(budgetComparisionDetail.getCurrentOriginalAmount());
					}
				}
				return uploadedAmount;
		}
		catch (Exception e) {
			LOG.error("Error Occurred While Setting the Current Uploaded Amount...! :: {}",e.getMessage());
			throw e;
		}
	}
}
