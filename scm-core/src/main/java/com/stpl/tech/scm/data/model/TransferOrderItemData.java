package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 11-06-2016.
 */
@Entity
@Table(name = "TRANSFER_ORDER_ITEM")
public class TransferOrderItemData {

    private Integer id;
    private int skuId;
    private String skuName;
    private List<SCMOrderPackagingData> packagingDetails = new ArrayList<SCMOrderPackagingData>(0);
    private BigDecimal requestedQuantity;
    private BigDecimal requestedAbsoluteQuantity;
    private BigDecimal transferredQuantity;
    private BigDecimal receivedQuantity;
    private String unitOfMeasure;
    private BigDecimal unitPrice;
    private BigDecimal negotiatedUnitPrice;
    private BigDecimal calculatedAmount;
    private RequestOrderItemData requestOrderItemData;
    private PurchaseOrderItemData purchaseOrderItemData;
    private TransferOrderData transferOrderData;
	private TransferOrderItemInvoice orderItemInvoice;
	private String taxCode;
	private BigDecimal taxAmount;
	private List<TransferOrderItemTaxDetail> orderItemTaxes = new ArrayList<>();
	private List<TransferOrderItemDrilldown> itemDrilldowns = new ArrayList<TransferOrderItemDrilldown>(0);
	private Integer associatedAssetId;
	private String associatedAssetTagValue;
	private BigDecimal excessQuantity;
    private Integer productionId;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TRANSFER_ORDER_ITEM_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "SKU_ID", nullable = false)
    public int getSkuId() {
        return skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    @Column(name = "SKU_NAME", nullable = false)
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "REQUESTED_QUANTITY", nullable = true)
    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    public void setRequestedQuantity(BigDecimal requestedQuantity) {
        this.requestedQuantity = requestedQuantity;
    }

    @Column(name = "REQUESTED_ABSOLUTE_QUANTITY", nullable = true)
    public BigDecimal getRequestedAbsoluteQuantity() {
        return requestedAbsoluteQuantity;
    }

    public void setRequestedAbsoluteQuantity(BigDecimal requestedAbsoluteQuantity) {
        this.requestedAbsoluteQuantity = requestedAbsoluteQuantity;
    }

    @Column(name = "TRANSFERRED_QUANTITY", nullable = true)
    public BigDecimal getTransferredQuantity() {
        return transferredQuantity;
    }

    public void setTransferredQuantity(BigDecimal transferredQuantity) {
        this.transferredQuantity = transferredQuantity;
    }

    @Column(name = "RECEIVED_QUANTITY", nullable = true)
    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = false)
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @Column(name = "UNIT_PRICE", nullable = true)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Column(name = "NEGOTIATED_UNIT_PRICE", nullable = true)
    public BigDecimal getNegotiatedUnitPrice() {
        return negotiatedUnitPrice;
    }

    public void setNegotiatedUnitPrice(BigDecimal negotiatedUnitPrice) {
        this.negotiatedUnitPrice = negotiatedUnitPrice;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REQUEST_ORDER_ITEM_ID", nullable = true)
    public RequestOrderItemData getRequestOrderItemData() {
        return requestOrderItemData;
    }

    public void setRequestOrderItemData(RequestOrderItemData requestOrderItemData) {
        this.requestOrderItemData = requestOrderItemData;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "transferOrderItemData")
    public List<SCMOrderPackagingData> getPackagingDetails() {
        return packagingDetails;
    }

    public void setPackagingDetails(List<SCMOrderPackagingData> packagingDetails) {
        this.packagingDetails = packagingDetails;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PURCHASE_ORDER_ITEM_ID", nullable = true)
    public PurchaseOrderItemData getPurchaseOrderItemData() {
        return purchaseOrderItemData;
    }

    public void setPurchaseOrderItemData(PurchaseOrderItemData purchaseOrderItemData) {
        this.purchaseOrderItemData = purchaseOrderItemData;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TRANSFER_ORDER_ID", nullable = false)
    public TransferOrderData getTransferOrderData() {
        return transferOrderData;
    }

    public void setTransferOrderData(TransferOrderData transferOrderData) {
        this.transferOrderData = transferOrderData;
    }

    @Column(name = "CALCULATED_AMOUNT", nullable = true)
    public BigDecimal getCalculatedAmount() {
        return calculatedAmount;
    }

    public void setCalculatedAmount(BigDecimal calculatedAmount) {
        this.calculatedAmount = calculatedAmount;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItem")
	public List<TransferOrderItemTaxDetail> getOrderItemTaxes() {
		return orderItemTaxes ;
	}

	public void setOrderItemTaxes(List<TransferOrderItemTaxDetail> orderItemTaxes) {
		this.orderItemTaxes = orderItemTaxes;
	}

	@Column(name = "TAX_CODE", nullable = true, length = 40)
	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ITEM_INVOICE_ID", nullable = true)
	public TransferOrderItemInvoice getOrderItemInvoice() {
		return orderItemInvoice;
	}

	public void setOrderItemInvoice(TransferOrderItemInvoice orderItemInvoice) {
		this.orderItemInvoice = orderItemInvoice;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItem")
	public List<TransferOrderItemDrilldown> getItemDrilldowns() {
		return itemDrilldowns;
	}

	public void setItemDrilldowns(List<TransferOrderItemDrilldown> itemDrilldowns) {
		this.itemDrilldowns = itemDrilldowns;
	}

    @Column(name = "ASSOCIATED_ASSET_ID", nullable = true)
    public Integer getAssociatedAssetId() {
        return associatedAssetId;
    }

    public void setAssociatedAssetId(Integer associatedAssetId) {
        this.associatedAssetId = associatedAssetId;
    }

    @Column(name = "ASSOCIATED_ASSET_TAG_VALUE", nullable = true)
    public String getAssociatedAssetTagValue() {
        return associatedAssetTagValue;
    }

    public void setAssociatedAssetTagValue(String associatedAssetTagValue) {
        this.associatedAssetTagValue = associatedAssetTagValue;
    }

    @Column(name = "EXCESS_QUANTITY", nullable = false)
    public BigDecimal getExcessQuantity() {
        return excessQuantity;
    }

    public void setExcessQuantity(BigDecimal excessQuantity) {
        this.excessQuantity = excessQuantity;
    }

    @Column(name = "PRODUCTION_ID", nullable = true)
    public Integer getProductionId() {
        return productionId;
    }

    public void setProductionId(Integer productionId) {
        this.productionId = productionId;
    }
}
