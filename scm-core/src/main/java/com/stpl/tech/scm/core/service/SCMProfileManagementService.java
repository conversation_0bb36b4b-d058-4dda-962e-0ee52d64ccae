package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 07-05-2016.
 */
public interface SCMProfileManagementService {

    public ProfileDefinition addNewProfile(ProfileDefinition profileDefinition) throws DataUpdationException, SumoException;

    public List<ProfileDefinition> viewAllProfiles();

    public ProfileDefinition viewProfile(int profileId);

    public List<ProfileAttributeMapping> addProfileAttributeMappings(List<ProfileAttributeMapping> profileAttributeMappings) throws SumoException;

    public List<ProfileAttributeMapping> getAllAttributeMappingsForProfile (int profileId);

    public List<ProfileAttributeMapping> getAllAttributeMappingsForProfileAndSKU (int profileId);

    public List<ProfileAttributeMapping> getAllAttributeMappingsForProfileAndAsset (int profileId);

    public List<ProfileAttributeMapping> getAllAttributeMappingsForProfile (int profileId, String status);

    public List<EntityAttributeValueMapping> addGenericAttributeValueMappings(List<EntityAttributeValueMapping> entityAttributeValueMappings) throws SumoException;

    public List<EntityAttributeValueMapping> addGenericAttributeValueMappingsAndAttribute(List<EntityAttributeValueMapping> entityAttributeValueMappings) throws SumoException;

    public List<EntityAttributeValueMapping> updateGenericAttributeValueMappings(List<EntityAttributeValueMapping> entityAttributeValueMappings) throws SumoException;

    public List<EntityAttributeValueMapping> getEntityAttributeMappings(int entityId, String entityType);

    public String uploadProfileMappingImage(MimeType mimeType, Integer profileMappingId, MultipartFile file) throws SumoException;

}
