package com.stpl.tech.scm.data.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;

import com.amazonaws.util.CollectionUtils;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SalesPerformaInvoiceDao;
import com.stpl.tech.scm.data.model.OutwardRegisterData;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteDetail;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteItemDetail;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCorrected;
import com.stpl.tech.scm.data.model.VendorSequenceId;
import com.stpl.tech.scm.data.model.SalesPerformaStatusEventData;
import com.stpl.tech.scm.domain.model.SalesPerformaStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-07-2018.
 */

@Repository
public class SalesPerformaInvoiceDaoImpl extends SCMAbstractDaoImpl implements SalesPerformaInvoiceDao {

    private static final Logger LOG = LoggerFactory.getLogger(SalesPerformaInvoiceDaoImpl.class);

    @Override
    public List<OutwardRegisterData> getOutWardRegisterEntry(Integer sendingUnit, Date startDate, Date endDate , String businessType){
        StringBuffer queryString = new StringBuffer("FROM OutwardRegisterData ORD WHERE ORD.unitId=:sendingUnit ");
        if (startDate != null && endDate != null) {
            queryString.append(" and ORD.submissionDateTime >= :startDate and ORD.submissionDateTime < :endDate ");
        }
        if(Objects.nonNull(businessType)){
            queryString.append(" and ORD.businessType = :businessType ");
        }
        queryString.append(" order by ORD.id desc");
        Query query = manager.createQuery(queryString.toString()).setParameter("sendingUnit", sendingUnit);
        if (startDate != null && endDate != null) {
            query.setParameter("startDate", SCMUtil.getDate(startDate))
                    .setParameter("endDate", SCMUtil.getDate(endDate));
        }
        if(Objects.nonNull(businessType)){
            query.setParameter("businessType",businessType);
        }

        return (List<OutwardRegisterData>)query.getResultList();
    }

    @Override
    public List<SalesPerformaDetailData> getInvoices(Integer sendingUnit, Date startDate, Date endDate,
                                                     List<String> status, Integer vendorId, Integer dispatchId, String businessType , String trnsType) {
        StringBuffer queryString = new StringBuffer("FROM SalesPerformaDetailData t WHERE t.sendingUnit=:unit ");
        if (startDate != null && endDate != null) {
            queryString.append(" and t.dispatchDate BETWEEN :startDate and :endDate");
        }
        if (vendorId != null && vendorId != 0) {
            queryString.append(" and t.vendor=:vendorId ");
        }
        if (dispatchId != null && dispatchId != 0) {
            queryString.append(" and t.dispatchLocation=:dispatchId ");
        }

        if (status != null) {
            queryString.append(" and t.status IN (:status) ");
        }

        if (businessType != null) {
            queryString.append(" and t.businessType =  (:businessType) ");
        }
        if(Objects.nonNull(trnsType)){
            queryString.append(" and t.type =  :type ");
        }

        queryString.append(" order by t.dispatchDate, t.invoiceId desc");

        Query query = manager.createQuery(queryString.toString()).setParameter("unit", sendingUnit);
        if (startDate != null && endDate != null) {
            query.setParameter("startDate", SCMUtil.getDate(startDate))
                    .setParameter("endDate", SCMUtil.getDate(endDate));
        }
        if (vendorId != null && vendorId != 0) {
            query.setParameter("vendorId", vendorId);
        }
        if (dispatchId != null && dispatchId != 0) {
            query.setParameter("dispatchId", dispatchId);
        }
        if (status != null) {
            query.setParameter("status", status);
        }
        if (businessType != null) {
            query.setParameter("businessType", businessType);
        }
        if (Objects.nonNull(trnsType)){
            query.setParameter("type", trnsType);
        }
        return query.getResultList();
    }

    @Override
    public int getNextInvoiceId(Integer vendorId, String type) {
        Query query = manager.createQuery("FROM VendorSequenceId E where E.vendorId = :vendorId and E.idType = :idType and E.financialYear IS NULL");
        query.setParameter("idType", type);
        query.setParameter("vendorId", vendorId);
        VendorSequenceId sequence = null;
        try {
            sequence = (VendorSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(vendorId, type);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    @Override
    public int getNextInvoiceId(int vendorId, String type, Integer financialYear) {
        Query query = manager.createQuery("FROM VendorSequenceId E where E.vendorId = :vendorId and E.idType = :idType and E.financialYear =:financialYear");
        query.setParameter("idType", type);
        query.setParameter("vendorId", vendorId);
        query.setParameter("financialYear", financialYear);
        VendorSequenceId sequence = null;
        try {
            sequence = (VendorSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(vendorId, type, financialYear);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    private VendorSequenceId addStateSequenceId(int vendorId, String type) {
        VendorSequenceId info = new VendorSequenceId(vendorId, type, 1);
        manager.persist(info);
        return info;
    }

    private VendorSequenceId addStateSequenceId(int vendorId, String type, Integer financialYear) {
        VendorSequenceId info = new VendorSequenceId(vendorId, type, 1, financialYear);
        manager.persist(info);
        return info;
    }

    @Override
   public SalesPerformaDetailData findByGeneratedId(String uploadedDocId){
        try{
           Query query = manager.createQuery("FROM SalesPerformaDetailData E where E.uploadDocId = :uploadedDocId");
           query.setParameter("uploadedDocId", uploadedDocId);
           return (SalesPerformaDetailData) query.getSingleResult();
        }catch ( NoResultException | NonUniqueResultException e){
            return null;
        }
   }

    @Override
    public SalesPerformaDetailData findByGeneratedDebitNoteId(String generatedDebitNoteId){
        try{
            Query query = manager.createQuery("FROM SalesPerformaDetailData E where E.generatedDebitNoteId = :generatedDebitNoteId");
            query.setParameter("generatedDebitNoteId", generatedDebitNoteId);
            return (SalesPerformaDetailData) query.getSingleResult();
        }catch ( NoResultException | NonUniqueResultException e){
            return null;
        }
    }

    @Override
    public SalesPerformaDetailData findByGeneratedCreditNoteId(String uploadedDocId){
        try{
            Query query = manager.createQuery("FROM SalesPerformaDetailData E where E.generatedCreditNoteId = :uploadedDocId");
            query.setParameter("uploadedDocId", uploadedDocId);
            return (SalesPerformaDetailData) query.getSingleResult();
        }catch ( NoResultException | NonUniqueResultException e){
            return null;
        }
    }

   @Override
    public List<Integer> getSubmittedInvoices(List<Integer> invoiceIdList){
        try{
            Query query = manager.createQuery("Select ORD.invoiceId FROM OutwardRegisterData ORD where ORD.invoiceId  IN (:invoiceIdList)");
            query.setParameter("invoiceIdList", invoiceIdList);
            return query.getResultList();
        }
        catch(Exception e){
            LOG.error("Error while Fetching list of invoices whose form are already Submitted :::",e);
            return new ArrayList<>();
        }
   }

    @Override
    public List<Integer> getOldInvoiceIds(Date oldDate, List<String> status) {
        StringBuffer queryString = new StringBuffer("SELECT invoiceId FROM SalesPerformaDetailData t WHERE t.status IN (:status) and t.createdAt <=: oldDate ");
        Query query = manager.createQuery(queryString.toString());
        if (Objects.nonNull(status)) {
            query.setParameter("status", status);
        }
        if (Objects.nonNull(oldDate)) {
            query.setParameter("oldDate", oldDate);
        }
        return query.getResultList();
    }

    @Override
    public List<SalesPerformaInvoiceCreditDebitNoteDetail> getCreditDebitNoteDetails(Date startDate, Date endDate, String status, Integer vendorId) {
        StringBuffer queryString = new StringBuffer(" FROM SalesPerformaInvoiceCreditDebitNoteDetail t WHERE t.generationTime >= :startDate and t.generationTime <=:endDate" +
                " and t.status = :status and t.vendorId = :vendorId  ORDER BY t.id DESC");
        Query query = manager.createQuery(queryString.toString());
        if (Objects.nonNull(startDate)) {
            query.setParameter("startDate", startDate);
        }
        if (Objects.nonNull(endDate)) {
            query.setParameter("endDate", endDate);
        }
        if (Objects.nonNull(endDate)) {
            query.setParameter("status", status);
        }
        if (Objects.nonNull(vendorId)) {
            query.setParameter("vendorId", vendorId);
        }
        return query.getResultList();
    }

    @Override
    public List<String> getUsedInvoiceIds() {
        StringBuffer queryString = new StringBuffer("SELECT t.invoiceId FROM SalesPerformaInvoiceCreditDebitNoteDetail t WHERE " +
                " t.status in (:status) ");
        Query query = manager.createQuery(queryString.toString());
        List<String> status = new ArrayList<>();
        status.add(SalesPerformaStatus.CLOSED.name());
        query.setParameter("status", status);
        return query.getResultList();
    }

    @Override
    public List<SalesPerformaInvoiceCreditDebitNoteItemDetail> getCreditDebitNoteDetails(Integer id) {
        StringBuffer queryString = new StringBuffer(" FROM SalesPerformaInvoiceCreditDebitNoteItemDetail t WHERE t.referenceId = :referenceId");
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("referenceId", id);
        return query.getResultList();
    }

    @Override
    public Integer getLastInvoiceId(Integer vendorId) {
        StringBuffer queryString = new StringBuffer("SELECT t.invoiceId FROM SalesPerformaDetailData t WHERE t.vendor =:vendorId ORDER BY t.invoiceId DESC ");
        Query query = manager.createQuery(queryString.toString());
        if (Objects.nonNull(vendorId) ) {
            query.setParameter("vendorId", vendorId);
        }
        List<Integer> invoiceIds = query.getResultList();
        if(!invoiceIds.isEmpty()){
            return invoiceIds.get(0);
        }
        return null;

    }

    @Override
    public SalesPerformaStatusEventData findLastEventById(Integer invoiceId){
        try{
            Query query = manager.createQuery("FROM SalesPerformaStatusEventData E where E.invoiceId = :invoiceId ORDER BY E.eventId desc");
            query.setParameter("invoiceId", invoiceId);
            List<SalesPerformaStatusEventData> dataList = query.getResultList();
            if(!dataList.isEmpty()){
                return dataList.get(0);
            }
            return null;
        }catch ( NoResultException | NonUniqueResultException e){
            return null;
        }
    }

    @Override
    public List<SalesPerformaInvoiceCorrected> getCorrectedInvoiceDetails(Integer invoiceId,String type){
        StringBuilder queryString = new StringBuilder("FROM SalesPerformaInvoiceCorrected SPIC WHERE SPIC.invoiceId=:invoiceId");
        if(Objects.nonNull(type)){
            queryString.append(" and SPIC.type=:type  ");
        }
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("invoiceId",invoiceId);
        if(Objects.nonNull(type)){
            query.setParameter("type",type);
        }

        List<SalesPerformaInvoiceCorrected> detail = (List<SalesPerformaInvoiceCorrected>) query.getResultList();
        if(!detail.isEmpty()){
            return detail;
        }
        return null;

    }

    @Override
    public SalesPerformaInvoiceCorrected findByCreditNoteId(String docId){
        try{
            Query query = manager.createQuery("FROM SalesPerformaInvoiceCorrected E where E.generatedCreditNoteId = :docId");
            query.setParameter("docId", docId);
            return (SalesPerformaInvoiceCorrected) query.getSingleResult();
        }catch ( NoResultException | NonUniqueResultException e){
            return null;
        }
    }

    @Override
    public SalesPerformaInvoiceCorrected findByDebitNoteId(String docId){
        try{
            Query query = manager.createQuery("FROM SalesPerformaInvoiceCorrected E where E.generatedDebitNoteId = :docId");
            query.setParameter("docId", docId);
            return (SalesPerformaInvoiceCorrected) query.getSingleResult();
        }catch ( NoResultException | NonUniqueResultException e){
            return null;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Object[]> getSkuTaxCode(List<Integer> skuIds, Integer unitId) {
        try {
            Query query = manager.createQuery("SELECT skuId, taxCode FROM UnitSkuMapping WHERE skuId IN (:skuIds) AND unitId = :unitId");
            query.setParameter("skuIds", skuIds)
                    .setParameter("unitId", unitId);
            return (List<Object[]>) query.getResultList();
        } catch (Exception exp) {
            LOG.error("Error while getting taxCode : ", exp);
        }
        return new ArrayList<>();
    }

}
