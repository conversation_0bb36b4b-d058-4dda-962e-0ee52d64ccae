package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.RiderActionEnum;
import com.stpl.tech.scm.domain.model.RiderRouteStepStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

@Entity
@Getter
@Setter
@Table(name = "RIDER_ROUTE_PLAN_STEP_DATA")
@AllArgsConstructor
@NoArgsConstructor
public class RiderRoutePlanStepData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "RIDER_ROUTE_PLAN_STEP_DATA_ID")
    private Integer riderRoutePlanStepDataId;

    @ManyToOne(targetEntity = RiderRoutePlanData.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "RIDER_ROUTE_PLAN_DATA_ID", nullable = false)
    private RiderRoutePlanData riderRoutePlanData;

    @Enumerated(EnumType.STRING)
    @Column(name = "ROUTE_STEP_STATUS", nullable = false)
    private RiderRouteStepStatusEnum routeStepStatus;

    @Column(name = "CURRENT_STORE", nullable = false)
    private Integer currentStore;

    @Column(name = "NEXT_STORE", nullable = false)
    private Integer nextStore;

    @Enumerated(EnumType.STRING)
    @Column(name = "DROP_STATUS", nullable = false)
    private RiderActionEnum dropStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DROP_TIME")
    private Date dropTime;

    @Column(name = "DROP_TEMPERATURE")
    private BigDecimal dropTemperature;

    @Enumerated(EnumType.STRING)
    @Column(name = "PICKUP_STATUS", nullable = false)
    private RiderActionEnum pickupStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "PICKUP_TIME")
    private Date pickupTime;

    @Column(name = "PICKUP_TEMPERATURE")
    private BigDecimal pickupTemperature;

    @Column(name = "ROUTE_STEP", nullable = false)
    private Integer routeStep;

    @Column(name = "PICKED_UP_TO_ID")
    private Integer pickedUpToId;

    @Column(name = "DROP_OFF_TO_ID")
    private Integer dropOffToId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "RIDER_REACHED_TIME")
    private Date riderReachedTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "RIDER_LEAVE_TIME")
    private Date riderLeaveTime;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "riderRoutePlanStepData")
    private Set<RiderRoutePlanItemData> riderRoutePlanItemDataSet;
}
