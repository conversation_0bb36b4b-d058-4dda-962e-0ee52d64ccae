package com.stpl.tech.scm.data.dao.impl;

import com.google.gson.Gson;
import com.stpl.tech.scm.data.dao.CapexManagementDao;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class CapexManagementDaoImpl extends SCMAbstractDaoImpl implements CapexManagementDao {

	private static final Logger LOG = LoggerFactory.getLogger(CapexManagementDaoImpl.class);

	@Override
	public List<CapexAuditDetail> findList(List<Integer> capexRequestIds, String version, String status) {
		try {
			StringBuilder queryString = new StringBuilder("select cr.unitName, cr.type, ca.capexRequestId, ca.id, ca.accessKey, ca.version, ca.downloadedBy, ca.status, cr.unitId, cr.generationTime from CapexAuditDetailData ca, CapexRequestDetailData cr  where cr.id = ca.capexRequestId and ca.capexRequestId IN :capexRequestIds");
			if (version != null) {
				queryString.append(" and ca.version =:version");
			}
			if (status != null && !status.equalsIgnoreCase("All")) {
				queryString.append(" and ca.status =:status");
			}

			Query query = manager.createQuery(queryString.toString());
			query.setParameter("capexRequestIds", capexRequestIds);
			if (version != null) {
				query.setParameter("version", version);
			}
			if (status != null && !status.equalsIgnoreCase("All")) {
				query.setParameter("status", status);
			}
			List<Object[]> results = query.getResultList();
			List<CapexAuditDetail> capexAuditDetailList = new ArrayList<CapexAuditDetail>();
			List<CapexAuditDetailData> auditDetails = getCapexAuditDetailList(capexRequestIds);
			for (Object[] record : results) {
				CapexAuditDetail capexAudit = new CapexAuditDetail();
				capexAudit.setUnitName((String) record[0]);
				capexAudit.setType((String) record[1]);
				capexAudit.setCapexRequestId((Integer) record[2]);
				capexAudit.setId((Integer) record[3]);
				capexAudit.setAccessKey((String) record[4]);
				capexAudit.setVersion((String) record[5]);
				capexAudit.setDownloadedBy((String) record[6]);
				capexAudit.setStatus((String) record[7]);
				capexAudit.setUnitId((Integer) record[8]);
				capexAudit.setGenerationTime((Date) record[9]);

				if (auditDetails.size() > 0) {
					Optional<CapexAuditDetailData> data = auditDetails.stream().filter((item) ->
							item.getCapexRequestId().equals(capexAudit.getCapexRequestId())).findFirst();
					if (data.isPresent()) {
						capexAudit.setLastApprovalDate(data.get().getApprovedDate());
					}
				}
				capexAuditDetailList.add(capexAudit);
			}
			return capexAuditDetailList;
		}catch (Exception e){
			LOG.error("Exception occurred while finding capex audit list ::: ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public CapexAuditDetail findAuditDetail(Integer capexId) {
		try {
			StringBuilder queryString = new StringBuilder("select ca.id, cr.unitId from CapexAuditDetailData ca, CapexRequestDetailData cr  where cr.id = ca.capexRequestId and ca.capexRequestId = :capexRequestId");

			Query query = manager.createQuery(queryString.toString());
			query.setParameter("capexRequestId", capexId);
			List<Object[]> results = query.getResultList();
			CapexAuditDetail capexAudit = new CapexAuditDetail();
			for (Object[] record : results) {
				capexAudit.setId((Integer) record[0]);
				capexAudit.setUnitId((Integer) record[1]);
			}
			return capexAudit;
		}catch (Exception e){
			LOG.error("Exception occurred while finding capex audit list ::: ",e);
			return null;
		}
	}

	private List<CapexAuditDetailData> getCapexAuditDetailList(List<Integer> capexRequestIds){
		try{
			Query query = manager.createQuery("FROM CapexAuditDetailData c WHERE c.capexRequestId IN(:capexRequestId) AND c.approvedDate IS NOT NULL ORDER BY c.id DESC");
			query.setParameter("capexRequestId",capexRequestIds);
			return query.getResultList();
		}
		catch (Exception e){
			LOG.error("Exception occurred while fetching audit detail data from database ::: ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<CapexRequestDetailData> getUnitVerify(Integer unitId) {
		Query query = manager.createQuery("FROM CapexRequestDetailData c where c.unitId =:unitId");
		query.setParameter("unitId", unitId);
		List<CapexRequestDetailData> capexData = new ArrayList<CapexRequestDetailData>();
		try {
			capexData = query.getResultList();
		} catch (Exception e) {
			return capexData;
		}
		return capexData;
	}

	@Override
	public CapexAuditDetailData findcapexAuditData(int capexRequestId) {
		Query query = manager.createQuery("FROM CapexAuditDetail c where c.capexRequestId =:capexRequestId");
		query.setParameter("capexRequestId", capexRequestId);
		CapexAuditDetailData capexData;
		try {
			capexData = (CapexAuditDetailData) query.getSingleResult();
		} catch (Exception e) {
			return null;
		}
		return capexData;
	}

	@Override
	public List<CapexBudgetDetailData> findBudgetAudit(Integer unitId, Integer capexRequestId) {
		Query query = manager.createQuery("FROM CapexBudgetDetailData c where c.unitId =:unitId and c.capexRequestId =:capexRequestId");
		query.setParameter("unitId", unitId);
		query.setParameter("capexRequestId", capexRequestId);
		List<CapexBudgetDetailData> capexData;
		try {
			capexData = query.getResultList();
		} catch (Exception e) {
			return null;
		}
		return (List<CapexBudgetDetailData>) capexData;
	}

	@Override
	public CapexAuditDetailData findAuditHistory(Integer capexRequestId) {
		Query query = manager.createQuery(
				"FROM CapexAuditDetailData c where c.capexRequestId =:capexRequestId ORDER BY c.capexRequestId DESC LIMIT 1;");
		query.setParameter("capexRequestId", capexRequestId);
		CapexAuditDetailData capexAuditDetail = (CapexAuditDetailData) query.getSingleResult();
		return capexAuditDetail;
	}

	@Override
	public CapexAuditDetailData findCapexDataForVersion(int capexRequestId, String version) {
		Query query = manager.createQuery(
				"FROM CapexAuditDetailData c where c.capexRequestId =:capexRequestId and c.version =:version");
		query.setParameter("capexRequestId", capexRequestId);
		query.setParameter("version", version);
		CapexAuditDetailData capexAuditDetail = (CapexAuditDetailData) query.getSingleResult();
		return capexAuditDetail;
	}

	@Override
	public List<Integer> findIds(Integer unitId, String version, String status) {
		try {
			StringBuilder queryString = new StringBuilder("select c.id FROM CapexRequestDetailData c");
			if (unitId != null) {
				queryString.append(" where c.unitId =:unitId");
			}
			Query query = manager.createQuery(queryString.toString());
			if (unitId != null) {
				query.setParameter("unitId", unitId);
			}
			List<Integer> capexRequestIds = query.getResultList();
			return capexRequestIds;
		}
		catch (Exception e){
			LOG.error("Exception occurred while fetching capex request Ids :: ",e );
			return new ArrayList<>();
		}
	}

    @Override
	public CapexRequestStatusLog findCapexStatusLog(Integer capexAuditId, Integer capexRequestId) {
		Query query = manager.createQuery(
				"FROM CapexRequestStatusLog c where c.capexRequestId =:capexRequestId and c.capexAuditId =:capexAuditId and c.status =:status");
		query.setParameter("capexRequestId", capexRequestId);
		query.setParameter("capexAuditId", capexAuditId);
		query.setParameter("status", CapexStatus.ACTIVE.toString());
		CapexRequestStatusLog capexStatusLog = (CapexRequestStatusLog) query.getSingleResult();
		return capexStatusLog;
	}

	@Override
	public List<CapexBudgetDetail> findBudgetDetails(Integer unitId, Integer capexRequestId) {
		StringBuilder queryString = new StringBuilder("FROM CapexBudgetDetailData c where  c.capexRequestId =:capexRequestId");
		 if(unitId!=null){
			 queryString.append(" and  c.unitId =:unitId");
		 }
		Query query = manager.createQuery(queryString.toString());
		if(unitId!=null) query.setParameter("unitId", unitId);
		query.setParameter("capexRequestId", capexRequestId);
		List<CapexBudgetDetailData> capexBudgetDetailData = query.getResultList();
		List<CapexBudgetDetail> capexBudgetDetail = new ArrayList<CapexBudgetDetail>();
		for(CapexBudgetDetailData capexbudgetData : capexBudgetDetailData) {
			CapexBudgetDetail capexData = new CapexBudgetDetail();
			capexData.setDepartmentName(capexbudgetData.getDepartmentName());
			capexData.setDepartmentId(capexbudgetData.getDepartmentId());
			capexData.setBudgetAmount(capexbudgetData.getBudgetAmount());
			capexData.setOriginalAmount(capexbudgetData.getOriginalAmount());
			capexData.setRemainingAmount(capexbudgetData.getRemainingAmount());
			capexData.setReceivingAmount(capexbudgetData.getReceivingAmount());
			capexData.setRunningAmount(capexbudgetData.getRunningAmount());
			capexData.setPaidAmount(capexbudgetData.getPaidAmount() == null? BigDecimal.ZERO : capexbudgetData.getPaidAmount());
			capexData.setInitialAmount(capexbudgetData.getInitialAmount() == null? capexbudgetData.getOriginalAmount() : capexbudgetData.getInitialAmount());
			capexBudgetDetail.add(capexData);
		}
		return capexBudgetDetail;
	}

	@Override
	public String findSOSrPrInOpenedStates(Integer bccId) {
		StringBuilder data = new StringBuilder();
		Set<Integer> soIds = new HashSet<Integer>();
		// Checking Service Orders are in Open State
		Query query = manager.createQuery("FROM ServiceOrderItemData c where c.businessCostCenterId =:bccId");
		query.setParameter("bccId", bccId);
		List<ServiceOrderItemData> serviceItems = query.getResultList();
		if (!serviceItems.isEmpty()) {
			for (ServiceOrderItemData soItem : serviceItems) {
				soIds.add(soItem.getServiceOrderId());
			}

			Query querySec = manager.createQuery("FROM ServiceOrderData c where c.id IN :soIds AND c.type = :type");
			querySec.setParameter("soIds", soIds);
			querySec.setParameter("type", "CAPEX");
			List<ServiceOrderData> serviceOrders = querySec.getResultList();
			soIds.clear();
			for (ServiceOrderData serviceOrder : serviceOrders) {
				if (!serviceOrder.getStatus().equalsIgnoreCase(ServiceOrderStatus.CLOSED.name())
					&& !serviceOrder.getStatus().equalsIgnoreCase(ServiceOrderStatus.CANCELLED.name())
					&& !serviceOrder.getStatus().equalsIgnoreCase(ServiceOrderStatus.REJECTED.name())
					&& !serviceOrder.getStatus().equalsIgnoreCase(ServiceOrderStatus.REJECTED_L1.name())
					&& !serviceOrder.getStatus().equalsIgnoreCase(ServiceOrderStatus.REJECTED_L2.name())
				    && !serviceOrder.getStatus().equals(ServiceOrderStatus.REJECTED_L3.name())
						&& !serviceOrder.getStatus().equals(ServiceOrderStatus.FIN_REJECTED_L1.name()) && !serviceOrder.getStatus().equals(ServiceOrderStatus.REJECTED_VENDOR_APPROVAL.name())) {
					soIds.add(serviceOrder.getId());
				}
			}
			Set<Integer> incompleteOrders = new HashSet<>();
			Set<String> costCenterNames = new HashSet<>();
			Map<Integer,String> map = new HashMap<>();
			for (ServiceOrderData so : serviceOrders) {
				if (!so.getStatus().equalsIgnoreCase(ServiceOrderStatus.CLOSED.name())
						&& !so.getStatus().equalsIgnoreCase(ServiceOrderStatus.CANCELLED.name())
					&& !so.getStatus().equalsIgnoreCase(ServiceOrderStatus.REJECTED.name())
					&& !so.getStatus().equalsIgnoreCase(ServiceOrderStatus.REJECTED_L1.name())
					&& !so.getStatus().equalsIgnoreCase(ServiceOrderStatus.REJECTED_L2.name())
						&& !so.getStatus().equalsIgnoreCase(ServiceOrderStatus.REJECTED_L3.name())
						&& !so.getStatus().equals(ServiceOrderStatus.FIN_REJECTED_L1.name()) && !so.getStatus().equals(ServiceOrderStatus.REJECTED_VENDOR_APPROVAL.name())) {
					incompleteOrders.add(so.getId());
					Query findCostCenterName = manager.createQuery("FROM CostCenterData ccd where ccd.costCenterId = :id");
					findCostCenterName.setParameter("id", so.getCostCenterId());
					CostCenterData costCenterData;
					try {
						costCenterData = (CostCenterData) findCostCenterName.getSingleResult();
					} catch (NoResultException | NonUniqueResultException e) {
						costCenterData = null;
					}
					if (costCenterData != null) {
						costCenterNames.add(costCenterData.getCostCenterName());
						if(!map.containsKey(so.getId())){
							map.put(so.getId(), costCenterData.getCostCenterName());
						}
					}
				}
			}
			if(!incompleteOrders.isEmpty()) {
//				data.append("Service Order of IDs: "+new Gson().toJson(incompleteOrders)+" for Cost Center Names "+new Gson().toJson(costCenterNames)+" for this capex unit are not in closed State, ");
				data.append("Service Order of IDs: "+ map.entrySet() +" for Cost Center Names for this capex unit are not in closed State, ");
				//return data.toString();
			}

			// Checking Service Receivings are in Open State
			Set<Integer> srItemIds = new HashSet<Integer>();
			if(!soIds.isEmpty()) {
				Query queryThird = manager.createQuery("FROM ServiceReceivedItemData c where c.serviceOrderId IN :soIds");
				queryThird.setParameter("soIds", soIds);
				List<ServiceReceivedItemData> serviceRecItems = queryThird.getResultList();
				Set<Integer> incompleteReceivings = new HashSet<>();
				for (ServiceReceivedItemData srItems : serviceRecItems) {
					srItemIds.add(srItems.getItemId());
					if (!(srItems.getPendingInvoiceQuantity().compareTo(BigDecimal.ZERO) == 0)) {
						incompleteReceivings.add(srItems.getServiceReceivedDataId());
					}
				}
				if(!incompleteReceivings.isEmpty()) {
					data.append("Service Receiving of IDs: "+new Gson().toJson(incompleteReceivings)+" for this capex unit are not in closed State, ");
					//return data.toString();
				}
			}
			// Checking Payment Request are in Open State
			if(!srItemIds.isEmpty()) {
				Set<Integer> paymentInvoiceIds = getPaymentInvoiceData(srItemIds);
				Query queryS = manager
					.createQuery("FROM PaymentInvoiceData p where p.id IN :paymentInvoiceIds");
				queryS.setParameter("paymentInvoiceIds", paymentInvoiceIds);
				List<PaymentInvoiceData> paymentInvoiceDatas = queryS.getResultList();
				Set<Integer> incompletePrs = new HashSet<>();
				for(PaymentInvoiceData pIData : paymentInvoiceDatas) {
					if(!pIData.getPaymentRequestData().getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.PAID.name())) {
						incompletePrs.add(pIData.getPaymentRequestData().getId());
					}
				}
				if(!incompletePrs.isEmpty()) {
					data.append("Payment Request of IDs: "+new Gson().toJson(incompletePrs)+" for this capex unit are not Paid.");
					//return data.toString();
				}
			}
		}
		return data.toString();
	}

	@Override
	public List<CapexBudgetDetailData> findCapexBudgetDetailData(Integer unitId, Integer capexRequestId) {
		try {
			Query query = manager.createQuery("FROM CapexBudgetDetailData c where c.unitId =:unitId and c.capexRequestId =:capexRequestId");
			query.setParameter("unitId", unitId);
			query.setParameter("capexRequestId", capexRequestId);
			return query.getResultList();
		}
		catch (Exception e){
			LOG.error("Exception Occurred while fetching data from database ! ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<CapexBudgetAuditDetail> findCapexBudgetAuditDetailData(Integer capexAuditDetailId) {
		try{
			Query query = manager.createQuery("FROM CapexBudgetAuditDetail c WHERE c.capexAuditDetailId.id =:capexAuditDetailId");
			query.setParameter("capexAuditDetailId",capexAuditDetailId);
			return query.getResultList();
		}catch (Exception e){
			LOG.error("Error occurred while fetching budget audit details data",e);
			return new ArrayList<>();
		}
	}

	private Set<Integer> getPaymentInvoiceData(Set<Integer> srItemIds) {
		Set<Integer> paymentInvoiceIds = new HashSet<Integer>();
		Query query = manager
				.createQuery("FROM PaymentInvoiceItemData p where p.serviceReceivedItemId IN :srItemIds");
		query.setParameter("srItemIds", srItemIds);
		List<PaymentInvoiceItemData> paymentInvoiceItemDatas = query.getResultList();
		for (PaymentInvoiceItemData paymentInvoiceItemData : paymentInvoiceItemDatas) {
			paymentInvoiceIds.add(paymentInvoiceItemData.getPaymentInvoiceData().getId());
		}
		return paymentInvoiceIds;
	}

	@Override
	public List<ServiceOrderData> findServiceOrderFromCapexIdAndDepartment(Integer capexRequestId, Integer departmentId, Integer vendorId, List<ServiceOrderStatus> statusList) {
		  try {
			  StringBuilder queryString = new StringBuilder("SELECT * FROM SERVICE_ORDER so where so.SERVICE_ORDER_ID IN(SELECT distinct(bad.KEY_VALUE) FROM CAPEX_BUDGET_DETAIL cbd inner join BUDGET_AUDIT_DETAIL bad on cbd.ID=bad.CAPEX_BUDGET_DETAIL_ID where cbd.CAPEX_REQUEST_ID= :capexId and bad.KEY_TYPE='SO_ID'");
			  if(departmentId!=null){
				  queryString.append(" and cbd.DEPARTMENT_ID = :depId)");
			  }else{
				  queryString.append(")");
			  }
			  if(vendorId!=null){
				  queryString.append(" and so.VENDOR_ID = :vendorId ");
			  }
			  if(statusList!=null && !statusList.isEmpty()){
				  queryString.append(" and so.SERVICE_ORDER_STATUS IN (:statusList)");
			  }
			  Query query = manager.createNativeQuery(queryString.toString(),ServiceOrderData.class);
			  query.setParameter("capexId", capexRequestId);
			  if (departmentId!=null)  query.setParameter("depId", departmentId);
			  if(vendorId!=null) query.setParameter("vendorId",vendorId);
			  if(statusList!=null && !statusList.isEmpty()){
				  List<String> statuses = statusList.stream().map(ServiceOrderStatus::name).collect(Collectors.toList());
				  query.setParameter("statusList",statuses); }
			  return (List<ServiceOrderData>) query.getResultList();
		  }catch (Exception e){
			  LOG.error("##################### Error in findServiceOrderFromCapexIdAndDepartment , capexRequestId : {}, departmentId :{}",capexRequestId,departmentId);
		      e.printStackTrace();
		    throw new RuntimeException(e.getMessage());
		  }
	}

	@Override
	public List<PurchaseOrderData> findPurchaseOrderFromCapexIdAndDepartment(Integer capexRequestId, Integer departmentId,Integer vendorId,List<PurchaseOrderStatus> statusList) {
		try {
			StringBuilder queryString = new StringBuilder(" FROM PurchaseOrderData po where po.id IN(SELECT distinct(bad.keyValue) FROM CapexBudgetDetailData cbd inner join BudgetAuditDetailData bad on cbd.id=bad.capexBudgetDetailId where cbd.capexRequestId= :capexId and bad.keyType='PO_ID' ");

			if(departmentId!=null){
				queryString.append(" and cbd.departmentId = :depId)");
			}else{
				queryString.append(")");
			}
			if(vendorId!=null){
				queryString.append(" and po.generatedForVendor = :vendorId ");
			}
			if(statusList!=null && !statusList.isEmpty()){
				queryString.append(" and po.status IN (:statusList)");
			}
			Query query = manager.createQuery(queryString.toString());
			query.setParameter("capexId", capexRequestId);

			if (departmentId!=null)  query.setParameter("depId", departmentId);
			if(vendorId!=null) query.setParameter("vendorId",vendorId);
			if(statusList!=null && !statusList.isEmpty()){
				List<String> statuses = statusList.stream().map(PurchaseOrderStatus::name).collect(Collectors.toList());
				query.setParameter("statusList",statuses); }

			return (List<PurchaseOrderData>) query.getResultList();
		}catch (Exception e){
			LOG.error("##################### Error in findServiceOrderFromCapexIdAndDepartment , capexRequestId : {}, departmentId :{}",capexRequestId,departmentId);
			e.printStackTrace();
			throw new RuntimeException(e.getMessage());
		}
	}

	@Override
	public BigDecimal findPurchaseAndServiceOrderFromCapexId(Integer capexRequestId, List<String> statusList, Class<?> type) {
		try {
			boolean isSo = ServiceOrder.class.equals(type);
			String sumValue = isSo ? "totalAmount" : "paidAmount";
			String clazz = isSo ? "ServiceOrderData" : "PurchaseOrderData";
			String keyType = isSo ? "SO_ID" : "PO_ID";

			StringBuilder queryString = new StringBuilder(
					"SELECT SUM(orders." + sumValue + ") " +
							"FROM " + clazz + " orders " +
							"WHERE orders.id IN (" +
							"SELECT DISTINCT(bad.keyValue) " +
							"FROM CapexBudgetDetailData cbd " +
							"INNER JOIN BudgetAuditDetailData bad " +
							"ON cbd.id = bad.capexBudgetDetailId " +
							"WHERE cbd.capexRequestId = :capexId " +
							"AND bad.keyType = :keyType)"
			);

			if (statusList != null && !statusList.isEmpty()) {
				queryString.append(" AND orders.status NOT IN (:status)");
			}

			Query query = manager.createQuery(queryString.toString());
			query.setParameter("capexId", capexRequestId);
			query.setParameter("keyType", keyType);

			if (statusList != null && !statusList.isEmpty()) {
				query.setParameter("status", statusList);
			}

			BigDecimal result = (BigDecimal) query.getSingleResult();
			return result;
		}catch (Exception e){
			LOG.error("##################### Error in findPurchaseAndServiceOrderFromCapexId , capexRequestId : {}",capexRequestId);
			throw new RuntimeException(e.getMessage());
		}
	}

	@Override
	public Integer soVendorCount(Integer capexRequestId, Integer departmentId) {
		try {
			Query query = manager.createNativeQuery("select COUNT(distinct(so.VENDOR_ID)) from SERVICE_ORDER so where so.SERVICE_ORDER_ID IN(SELECT distinct(bad.KEY_VALUE) FROM CAPEX_BUDGET_DETAIL cbd inner join BUDGET_AUDIT_DETAIL bad on cbd.ID=bad.CAPEX_BUDGET_DETAIL_ID where cbd.CAPEX_REQUEST_ID= :capexId and cbd.DEPARTMENT_ID = :depId and bad.KEY_TYPE='SO_ID')");
			query.setParameter("capexId", capexRequestId);
			query.setParameter("depId", departmentId);
			BigInteger soVendorCnt = (BigInteger) query.getSingleResult();
			return soVendorCnt.intValue();
		}catch (Exception e){
			LOG.error("##################### Error in soVendorCount , capexRequestId : {}, departmentId :{}",capexRequestId,departmentId);
			e.printStackTrace();
			throw new RuntimeException(e.getMessage());
		}

	}

	@Override
	public Integer poVendorCount(Integer capexRequestId, Integer departmentId) {
		try {
			Query query = manager.createNativeQuery("select COUNT(distinct(po.GENERATED_FOR_VENDOR_ID)) from PURCHASE_ORDER po where po.PURCHASE_ORDER_ID IN(SELECT distinct(bad.KEY_VALUE) FROM CAPEX_BUDGET_DETAIL cbd inner join BUDGET_AUDIT_DETAIL bad on cbd.ID=bad.CAPEX_BUDGET_DETAIL_ID where cbd.CAPEX_REQUEST_ID= :capexId and cbd.DEPARTMENT_ID = :depId and bad.KEY_TYPE='PO_ID')");
			query.setParameter("capexId", capexRequestId);
			query.setParameter("depId", departmentId);
			BigInteger poVendorCnt = (BigInteger) query.getSingleResult();
			return  poVendorCnt.intValue();
		}catch (Exception e){
			LOG.error("##################### Error in poVendorCount , capexRequestId : {}, departmentId :{}",capexRequestId,departmentId);
			e.printStackTrace();
			throw new RuntimeException(e.getMessage());
		}
	}

	@Override
	public Integer getUnitFromCapexId(Integer capexRequestId) {
		try {
			Query query = manager.createQuery("SELECT DISTINCT(cbd.unitId) FROM CapexBudgetDetailData cbd where cbd.capexRequestId= :capexId ");
			query.setParameter("capexId", capexRequestId);
			return (Integer) query.getSingleResult();
		}catch (Exception e){
			LOG.error("##################### Error in getUnitFromCapexId , capexRequestId : {}",capexRequestId);
			e.printStackTrace();
			throw new RuntimeException(e.getMessage());
		}
	}

	@Override
	public boolean isSoPoFromExpiredCapex(Integer soPoId, Date validDate, String type,Set<Integer> checkUnits){
		try {
			Query query = manager.createNativeQuery("SELECT * FROM CAPEX_REQUEST_DATA where CAPEX_ID = (SELECT CAPEX_REQUEST_ID FROM  CAPEX_BUDGET_DETAIL where ID = (SELECT CAPEX_BUDGET_DETAIL_ID FROM BUDGET_AUDIT_DETAIL where KEY_TYPE= :soPoType and KEY_VALUE = :soPoId group by KEY_TYPE)) and LAST_UPDATED_TIME < :validateTime and status = :status and UNIT_ID IN (:checkUnits)");
			query.setParameter("soPoType", type);
			query.setParameter("soPoId", soPoId);
			query.setParameter("validateTime", validDate);
			query.setParameter("status", CapexStatus.APPROVED.name());
			query.setParameter("checkUnits",checkUnits);
			List<CapexRequestDetailData> res = query.getResultList();
			return res.size() == 0;
		}catch (Exception e){
			LOG.error("##################### Error in isSoPoFromExpiredCapex , soPoId : {}, type : {}",soPoId,type);
			e.printStackTrace();
			throw new RuntimeException(e.getMessage());
		}
	}
}
