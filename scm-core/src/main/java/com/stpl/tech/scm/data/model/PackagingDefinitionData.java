/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * PackagingDefinitionData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PACKAGING_DEFINITION", uniqueConstraints = @UniqueConstraint(columnNames = {"PACKAGING_TYPE",
    "PACKAGING_CODE"}))
public class PackagingDefinitionData implements java.io.Serializable {

    private Integer packagingId;
    private String packagingType;
    private String packagingCode;
    private String packagingName;
    private String packagingStatus;
    private BigDecimal conversionRatio;
    private Integer subPackaging;
    private String unitOfMeasure;

    public PackagingDefinitionData() {
    }

    public PackagingDefinitionData(String packagingType, String packagingCode, String packagingName,
                                   String packagingStatus, BigDecimal conversionRatio, String unitOfMeasure) {
        this.packagingType = packagingType;
        this.packagingCode = packagingCode;
        this.packagingName = packagingName;
        this.packagingStatus = packagingStatus;
        this.conversionRatio = conversionRatio;
        this.unitOfMeasure = unitOfMeasure;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PACKAGING_ID", unique = true, nullable = false)
    public Integer getPackagingId() {
        return this.packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name = "PACKAGING_TYPE", nullable = false, length = 20)
    public String getPackagingType() {
        return this.packagingType;
    }

    public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
    }

    @Column(name = "PACKAGING_CODE", nullable = false, length = 30)
    public String getPackagingCode() {
        return this.packagingCode;
    }

    public void setPackagingCode(String packagingCode) {
        this.packagingCode = packagingCode;
    }

    @Column(name = "PACKAGING_NAME", nullable = false)
    public String getPackagingName() {
        return this.packagingName;
    }

    public void setPackagingName(String packagingName) {
        this.packagingName = packagingName;
    }

    @Column(name = "PACKAGING_STATUS", nullable = false, length = 15)
    public String getPackagingStatus() {
        return this.packagingStatus;
    }

    public void setPackagingStatus(String packagingStatus) {
        this.packagingStatus = packagingStatus;
    }

    @Column(name = "CONVERSION_RATIO", nullable = false)
    public BigDecimal getConversionRatio() {
        return this.conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }

    @Column(name = "SUB_PACKAGING", nullable = true)
    public Integer getSubPackaging() {
        return subPackaging;
    }

    public void setSubPackaging(Integer subPackaging) {
        this.subPackaging = subPackaging;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = false, length = 15)
    public String getUnitOfMeasure() {
        return this.unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }
}
