package com.stpl.tech.scm.reports;

import java.io.IOException;
import java.util.Date;

import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.stpl.tech.scm.core.exception.StockTakeException;
import com.stpl.tech.util.EnvType;

public interface VarianceReport {

	public void renderVariance() throws StockTakeException;

	public void renderNegativeWriteOff();

	public void renderWastage();

	public void renderUnsettledTO();
	
	public boolean renderSummaryView();

	public void generateReport(String filePath, byte[] content) throws IOException;

	public String getEmailId();

	public String getUnitName();

	public boolean isGenerated();

	public String getFileName();

	public String getMimeType();

	public WorkbookContext getWorkbook();

	public String getFilePath();

	public Date getBusinessDate();

	public EnvType getEnv();

}
