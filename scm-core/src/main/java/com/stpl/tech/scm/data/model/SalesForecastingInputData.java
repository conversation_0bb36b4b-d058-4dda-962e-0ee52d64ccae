package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Table(name = "SALES_FORECASTING_INPUT_DATA")
public class SalesForecastingInputData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SALES_FORECASTING_INPUT_DATA_ID", nullable = false, unique = true)
    private Integer salesForecastingInputDataId;
    @Column(name = "DATE_OF_ORDERING")
    private LocalDate dateOfOrdering;
    @Column(name = "PRODUCT_ID")
    private Integer productId;
    @Column(name = "UNIT_ID_PRODUCT_ID_DIMENSION_IDENTIFIER")
    private String unitIdProductIdDimensionIdentifier;
    @Column(name = "VEG_NON_VEG")
    private String vegNonVeg;
    @Column(name = "PRODUCT_NAME")
    private String productName;
    @Column(name = "PRODUCT_CATEGORY")
    private String productCategory;
    @Column(name = "PRODUCT_SUB_CATEGORY")
    private String productSubCategory;
    @Column(name = "PRODUCT_PREPARATION_TIME")
    private BigDecimal productPreparationTime;
    @Column(name = "PRODUCT_INCIDENCE")
    private BigDecimal productIncidence;
    @Column(name = "NEW_CUSTOMER_INTREST")
    private BigDecimal newCustomerInterest;
    @Column(name = "OLD_CUSTOMER_INTREST")
    private BigDecimal oldCustomerInterest;
    @Column(name = "UNIT_ID")
    private Integer unitId;
    @Column(name = "UNIT_NAME")
    private String unitName;
    @Column(name = "UNIT_CITY")
    private String unitCity;
    @Column(name = "UNIT_CATEGORY")
    private String unitCategory;
    @Column(name = "UNITS_UNDER_2_KMS")
    private Integer unitsUnder2Kms;
    @Column(name = "UNITS_UNDER_5_KMS")
    private Integer unitsUnder5Kms;
    @Column(name = "UNITS_UNDER_10_KMS")
    private Integer unitsUnder10Kms;
    @Column(name = "UNIT_LIVE_DATE")
    private LocalDate unitLiveDate;
    @Column(name = "IS_UNIT_CLOSED")
    private String isUnitClosed;
    @Column(name = "OPERATIONAL_HOURS")
    private Integer operationalHours;
    @Column(name = "OPERATIONAL_MINUTES")
    private Integer operationalMinutes;
    @Column(name = "UNIT_STATE")
    private String unitState;
    @Column(name = "TOTAL_SALE_QUANTITY")
    private Integer totalSaleQuantity;
    @Column(name = "CANCELLED_ORDER_QUANTITY")
    private Integer cancelledOrderQuantity;
    @Column(name = "DINE_IN_SALE_QUANTITY")
    private Integer dineInSaleQuantity;
    @Column(name = "COD_SALE_QUANTITY")
    private Integer codSaleQuantity;
    @Column(name = "ZOMATO_SALE_QUANTITY")
    private Integer zomatoSaleQuantity;
    @Column(name = "SWIGGY_SALE_QUANTITY")
    private Integer swiggySaleQuantity;
    @Column(name = "MAGICPIN_SALE_QUANTITY")
    private Integer magicpinSaleQuantity;
    @Column(name = "DINE_IN_AVERAGE_PRICE")
    private BigDecimal dineInAveragePrice;
    @Column(name = "ZOMATO_AVERAGE_PRICE")
    private BigDecimal zomatoAveragePrice;
    @Column(name = "SWIGGY_AVERAGE_PRICE")
    private BigDecimal swiggyAveragePrice;
    @Column(name = "MAGICPIN_AVERAGE_PRICE")
    private BigDecimal magicpinAveragePrice;
    @Column(name = "BF_DINE_IN_SALES")
    private Integer bfDineInSales;
    @Column(name = "BF_COD_SALES")
    private Integer bfCodSales;
    @Column(name = "BF_ZOMATO_SALES")
    private Integer bfZomatoSales;
    @Column(name = "BF_SWIGGY_SALES")
    private Integer bfSwiggySales;
    @Column(name = "BF_MAGIC_PIN_SALES")
    private Integer bfMagicPinSales;
    @Column(name = "LUNCH_DINE_IN_SALES")
    private Integer lunchDineInSales;
    @Column(name = "LUNCH_COD_SALES")
    private Integer lunchCodSales;
    @Column(name = "LUNCH_ZOMATO_SALES")
    private Integer lunchZomatoSales;
    @Column(name = "LUNCH_SWIGGY_SALES")
    private Integer lunchSwiggySales;
    @Column(name = "LUNCH_MAGIC_PIN_SALES")
    private Integer lunchMagicPinSales;
    @Column(name = "EVENING_DINE_IN_SALES")
    private Integer eveningDineInSales;
    @Column(name = "EVENING_COD_SALES")
    private Integer eveningCodSales;
    @Column(name = "EVENING_ZOMATO_SALES")
    private Integer eveningZomatoSales;
    @Column(name = "EVENING_SWIGGY_SALES")
    private Integer eveningSwiggySales;
    @Column(name = "EVENING_MAGIC_PIN_SALES")
    private Integer eveningMagicPinSales;
    @Column(name = "DINNER_DINE_IN_SALES")
    private Integer dinnerDineInSales;
    @Column(name = "DINNER_COD_SALES")
    private Integer dinnerCodSales;
    @Column(name = "DINNER_ZOMATO_SALES")
    private Integer dinnerZomatoSales;
    @Column(name = "DINNER_SWIGGY_SALES")
    private Integer dinnerSwiggySales;
    @Column(name = "DINNER_MAGIC_PIN_SALES")
    private Integer dinnerMagicPinSales;
    @Column(name = "P_DINNER_DINE_IN_SALES")
    private Integer pDinnerDineInSales;
    @Column(name = "P_DINNER_COD_SALES")
    private Integer pDinnerCodSales;
    @Column(name = "P_DINNER_ZOMATO_SALES")
    private Integer pDinnerZomatoSales;
    @Column(name = "P_DINNER_SWIGGY_SALES")
    private Integer pDinnerSwiggySales;
    @Column(name = "P_DINNER_MAGIC_PIN_SALES")
    private Integer pDinnerMagicPinSales;
    @Column(name = "Z_OVERNIGHT_DINE_IN_SALES")
    private Integer zOvernightDineInSales;
    @Column(name = "Z_OVERNIGHT_COD_SALES")
    private Integer zOvernightCodSales;
    @Column(name = "Z_OVERNIGHT_ZOMATO_SALES")
    private Integer zOvernightZomatoSales;
    @Column(name = "Z_OVERNIGHT_SWIGGY_SALES")
    private Integer zOvernightSwiggySales;
    @Column(name = "Z_OVERNIGHT_MAGIC_PIN_SALES")
    private Integer zOvernightMagicPinSales;
    @Column(name = "BIG_DAY_EVENT")
    private String bigDayEvent;
    @Column(name = "NEW_CUSTOMER_COUNT")
    private Integer newCustomerCount;
    @Column(name = "OLD_CUSTOMER_COUNT")
    private Integer oldCustomerCount;
    @Column(name = "DAY_OF_WEEK")
    private Integer dayOfWeek;
    @Column(name = "WEEK_NUMBER")
    private Integer weekNumber;
    @Column(name = "WEEK_PART")
    private String weekPart;
    @Column(name = "DAY_NAME")
    private String dayName;
    @Column(name = "HUMIDITY")
    private BigDecimal humidity;
    @Column(name = "WINDSPEED")
    private BigDecimal windspeed;
    @Column(name = "PRECIPITATION")
    private BigDecimal precipitation;
    @Column(name = "IRRADIANCE")
    private BigDecimal irradiance;
    @Column(name = "TEMPERATURE_MAX")
    private BigDecimal temperatureMax;
    @Column(name = "TEMPERATURE_MIN")
    private BigDecimal temperatureMin;
    @Column(name = "NUMBER_OF_STOCK_OUT")
    private Integer numberOfStockOut;
    @Column(name = "STOCK_OUT_TOTAL_DURATION")
    private Integer stockOutTotalDuration;
    @Column(name = "TOTAL_WASTGE_PERCENTAGE")
    private BigDecimal totalWastagePercentage;
    @Column(name = "DIMENSION")
    private String dimension;

}
