//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.20 at 05:17:20 PM IST
//

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ServiceReceiveItem {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	protected int costElementId;
	@XmlElement(required = true)
	protected String ascCode;
	@XmlElement(required = true)
	protected String costElementName;
	@XmlElement(required = true)
	protected String serviceDescription;
	@XmlElement(required = true)
	protected BigDecimal requestedQuantity;
	@XmlElement(required = true)
	protected BigDecimal receivedQuantity;
	@XmlElement(required = true)
	protected String unitOfMeasure;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal unitPrice;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal totalCost;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal totalAmount;
	protected BigDecimal totalTax;
	protected BigDecimal taxRate;
	protected BigDecimal tdsRate;
	protected int businessCostCenterId;
	protected String businessCostCenterName;
	protected String businessCostCenterCode;
	protected Integer serviceOrderId;
	protected Integer serviceOrderItemId;
	protected Integer serviceReceivedId;
	@XmlElement(required = true)
	protected BigDecimal pendingInvoiceQuantity;
	@XmlElement(required = true)
	protected BigDecimal invoiceQuantity;
	protected String type;
	protected Date costElementDate;
	protected Date costElementToDate;

	protected List<ServiceReceiveTaxDetail> taxes;

	@XmlElement(nillable = true)
	protected List<ServiceReceivedItemDrilldown> serviceReceivedItemDrillDown;

	protected  Date vendorSOApprovalDate;


	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public int getCostElementId() {
		return costElementId;
	}

	public void setCostElementId(int costElementId) {
		this.costElementId = costElementId;
	}

	public String getAscCode() {
		return ascCode;
	}

	public void setAscCode(String hsnCode) {
		this.ascCode = hsnCode;
	}

	public String getCostElementName() {
		return costElementName;
	}

	public void setCostElementName(String costElementName) {
		this.costElementName = costElementName;
	}

	public String getServiceDescription() {
		return serviceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		this.serviceDescription = serviceDescription;
	}

	public BigDecimal getRequestedQuantity() {
		return requestedQuantity;
	}

	public void setRequestedQuantity(BigDecimal requestedQuantity) {
		this.requestedQuantity = requestedQuantity;
	}

	public BigDecimal getReceivedQuantity() {
		return receivedQuantity;
	}

	public void setReceivedQuantity(BigDecimal receivedQuantity) {
		this.receivedQuantity = receivedQuantity;
	}

	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal amountPaid) {
		this.totalAmount = amountPaid;
	}

	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}

	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public BigDecimal getTdsRate() {
		return tdsRate;
	}

	public void setTdsRate(BigDecimal tdsRate) {
		this.tdsRate = tdsRate;
	}

	public List<ServiceReceiveTaxDetail> getTaxes() {
		if(this.taxes==null){
			this.taxes = new ArrayList<>();
		}
		return this.taxes;
	}

	public void setTaxes(List<ServiceReceiveTaxDetail> taxes) {
		this.taxes = taxes;
	}

	public int getBusinessCostCenterId() {
		return businessCostCenterId;
	}

	public void setBusinessCostCenterId(int businessCostCenterId) {
		this.businessCostCenterId = businessCostCenterId;
	}

	public String getBusinessCostCenterName() {
		return businessCostCenterName;
	}

	public void setBusinessCostCenterName(String businessCostCenterName) {
		this.businessCostCenterName = businessCostCenterName;
	}

	public Integer getServiceOrderId() {
		return serviceOrderId;
	}

	public void setServiceOrderId(Integer serviceOrderId) {
		this.serviceOrderId = serviceOrderId;
	}

	public Integer getServiceOrderItemId() {
		return serviceOrderItemId;
	}

	public void setServiceOrderItemId(Integer serviceOrderItemId) {
		this.serviceOrderItemId = serviceOrderItemId;
	}

	public Integer getServiceReceivedId() {
		return serviceReceivedId;
	}

	public void setServiceReceivedId(Integer serviceReceivedId) {
		this.serviceReceivedId = serviceReceivedId;
	}

	public BigDecimal getPendingInvoiceQuantity() {
		return pendingInvoiceQuantity;
	}

	public void setPendingInvoiceQuantity(BigDecimal pendingInvoiceQuantity) {
		this.pendingInvoiceQuantity = pendingInvoiceQuantity;
	}

	public BigDecimal getInvoiceQuantity() {
		return invoiceQuantity;
	}

	public void setInvoiceQuantity(BigDecimal invoiceQuantity) {
		this.invoiceQuantity = invoiceQuantity;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Date getCostElementDate() {
		return costElementDate;
	}

	public void setCostElementDate(Date costElementDate) {
		this.costElementDate = costElementDate;
	}

	public Date getCostElementToDate() {
		return costElementToDate;
	}

	public void setCostElementToDate(Date costElementToDate) {
		this.costElementToDate = costElementToDate;
	}

	public List<ServiceReceivedItemDrilldown> getServiceReceivedItemDrillDown() {
		return serviceReceivedItemDrillDown;
	}

	public void setServiceReceivedItemDrillDown(List<ServiceReceivedItemDrilldown> serviceReceivedItemDrillDown) {
		this.serviceReceivedItemDrillDown = serviceReceivedItemDrillDown;
	}
	public String getBusinessCostCenterCode() {
		return businessCostCenterCode;
	}

	public void setBusinessCostCenterCode(String businessCostCenterCode) {
		this.businessCostCenterCode = businessCostCenterCode;
	}

	public Date getVendorSOApprovalDate() {
		return vendorSOApprovalDate;
	}

	public void setVendorSOApprovalDate(Date vendorSOApprovalDate) {
		this.vendorSOApprovalDate = vendorSOApprovalDate;
	}
}
