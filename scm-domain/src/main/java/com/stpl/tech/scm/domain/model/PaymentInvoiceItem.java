//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.09.11 at 11:20:21 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for PaymentInvoiceItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PaymentInvoiceItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="paymentInvoiceItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="hsn" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="uom" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="category" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="packagingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="conversionRatio" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalTax" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="packagingPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="packagingName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="taxes" type="{http://www.w3schools.com}PaymentInvoiceItemTax" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="deviations" type="{http://www.w3schools.com}InvoiceDeviationMapping" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentInvoiceItem", propOrder = {
    "paymentInvoiceItemId",
    "skuId",
    "skuName",
    "hsn",
    "uom",
    "category",
    "subCategory",
    "packagingId",
    "conversionRatio",
    "quantity",
    "totalAmount",
    "totalTax",
    "totalPrice",
    "unitPrice",
    "packagingPrice",
    "packagingName",
    "taxes",
    "deviations"
})
public class PaymentInvoiceItem {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer paymentInvoiceItemId;
    protected int skuId;
    @XmlElement(required = true)
    protected String skuName;
    @XmlElement(required = true)
    protected String hsn;
    @XmlElement(required = true)
    protected String uom;
    @XmlElement(required = true)
    protected String category;
    @XmlElement(required = true)
    protected String subCategory;
    protected int packagingId;
    @XmlElement(required = true)
    protected BigDecimal conversionRatio;
    @XmlElement(required = true)
    protected BigDecimal quantity;
    @XmlElement(required = true)
    protected BigDecimal totalAmount;
    @XmlElement(required = true)
    protected BigDecimal totalTax;
    @XmlElement(required = true)
    protected BigDecimal totalPrice;
    @XmlElement(required = true)
    protected BigDecimal unitPrice;
    @XmlElement(required = true)
    protected BigDecimal packagingPrice;
    @XmlElement(required = true)
    protected String packagingName;
    protected BigDecimal tdsRate;
    @XmlElement(required = true)
    protected Integer serviceReceivedId;
    @XmlElement(required = true)
    protected Integer serviceReceivedItemId;
    protected List<PaymentInvoiceItemTax> taxes;
    protected List<InvoiceDeviationMapping> deviations;
    @XmlElement(required = true)
    protected String costDescription;
    protected  String budgetCategory;
    protected Date SkuDate;
    protected Date toSkuDate;
    protected String departmentName;
    protected String businessCostCenterName;
    protected Integer businessCostCenterId;
    protected String section;
    protected String bccCode;
    protected Integer categoryId;
    protected Integer subCategoryId;
    /**
     * Gets the value of the paymentInvoiceItemId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPaymentInvoiceItemId() {
        return paymentInvoiceItemId;
    }

    /**
     * Sets the value of the paymentInvoiceItemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPaymentInvoiceItemId(Integer value) {
        this.paymentInvoiceItemId = value;
    }

    /**
     * Gets the value of the skuId property.
     *
     */
    public int getSkuId() {
        return skuId;
    }

    /**
     * Sets the value of the skuId property.
     *
     */
    public void setSkuId(int value) {
        this.skuId = value;
    }

    /**
     * Gets the value of the skuName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     * Sets the value of the skuName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkuName(String value) {
        this.skuName = value;
    }

    /**
     * Gets the value of the hsn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHsn() {
        return hsn;
    }

    /**
     * Sets the value of the hsn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHsn(String value) {
        this.hsn = value;
    }

    /**
     * Gets the value of the uom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUom() {
        return uom;
    }

    /**
     * Sets the value of the uom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUom(String value) {
        this.uom = value;
    }

    /**
     * Gets the value of the category property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCategory() {
        return category;
    }

    /**
     * Sets the value of the category property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCategory(String value) {
        this.category = value;
    }

    /**
     * Gets the value of the subCategory property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSubCategory() {
        return subCategory;
    }

    /**
     * Sets the value of the subCategory property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSubCategory(String value) {
        this.subCategory = value;
    }

    /**
     * Gets the value of the packagingId property.
     *
     */
    public int getPackagingId() {
        return packagingId;
    }

    /**
     * Sets the value of the packagingId property.
     *
     */
    public void setPackagingId(int value) {
        this.packagingId = value;
    }

    /**
     * Gets the value of the conversionRatio property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    /**
     * Sets the value of the conversionRatio property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setConversionRatio(BigDecimal value) {
        this.conversionRatio = value;
    }

    /**
     * Gets the value of the quantity property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * Sets the value of the quantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setQuantity(BigDecimal value) {
        this.quantity = value;
    }

    /**
     * Gets the value of the totalAmount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalAmount(BigDecimal value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the totalTax property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    /**
     * Sets the value of the totalTax property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalTax(BigDecimal value) {
        this.totalTax = value;
    }

    /**
     * Gets the value of the totalPrice property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    /**
     * Sets the value of the totalPrice property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalPrice(BigDecimal value) {
        this.totalPrice = value;
    }

    /**
     * Gets the value of the unitPrice property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /**
     * Sets the value of the unitPrice property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setUnitPrice(BigDecimal value) {
        this.unitPrice = value;
    }

    /**
     * Gets the value of the packagingPrice property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getPackagingPrice() {
        return packagingPrice;
    }

    /**
     * Sets the value of the packagingPrice property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setPackagingPrice(BigDecimal value) {
        this.packagingPrice = value;
    }

    /**
     * Gets the value of the packagingName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPackagingName() {
        return packagingName;
    }

    /**
     * Sets the value of the packagingName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPackagingName(String value) {
        this.packagingName = value;
    }

    /**
     * Gets the value of the taxes property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the taxes property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTaxes().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentInvoiceItemTax }
     * 
     * 
     */
    public List<PaymentInvoiceItemTax> getTaxes() {
        if (taxes == null) {
            taxes = new ArrayList<PaymentInvoiceItemTax>();
        }
        return this.taxes;
    }

    /**
     * Gets the value of the deviations property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the deviations property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDeviations().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link InvoiceDeviationMapping }
     *
     *
     */
    public List<InvoiceDeviationMapping> getDeviations() {
        if (deviations == null) {
            deviations = new ArrayList<InvoiceDeviationMapping>();
        }
        return this.deviations;
    }

    public BigDecimal getTdsRate() {
        return tdsRate;
    }

    public void setTdsRate(BigDecimal tdsRate) {
        this.tdsRate = tdsRate;
    }

	public Integer getServiceReceivedId() {
		return serviceReceivedId;
	}

	public void setServiceReceivedId(Integer serviceReceivedId) {
		this.serviceReceivedId = serviceReceivedId;
	}

	public Integer getServiceReceivedItemId() {
		return serviceReceivedItemId;
	}

	public void setServiceReceivedItemId(Integer serviceReceivedItemId) {
		this.serviceReceivedItemId = serviceReceivedItemId;
	}

	public String getCostDescription() {
		return costDescription;
	}

	public void setCostDescription(String costDescription) {
		this.costDescription = costDescription;
	}

    public Date getSkuDate() {
        return SkuDate;
    }

    public void setSkuDate(Date skuDate) {
        SkuDate = skuDate;
    }
    public String getBudgetCategory() {
        return budgetCategory;
    }

    public void setBudgetCategory(String budgetCategory) {
        this.budgetCategory = budgetCategory;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getBusinessCostCenterId() {
        return businessCostCenterId;
    }

    public void setBusinessCostCenterId(Integer businessCostCenterId) {
        this.businessCostCenterId = businessCostCenterId;
    }

    public String getBusinessCostCenterName() {
        return businessCostCenterName;
    }

    public void setBusinessCostCenterName(String businessCostCenterName) {
        this.businessCostCenterName = businessCostCenterName;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public Date getToSkuDate() {
        return toSkuDate;
    }

    public void setToSkuDate(Date toSkuDate) {
        this.toSkuDate = toSkuDate;
    }

    public String getBccCode() {
        return bccCode;
    }

    public void setBccCode(String bccCode) {
        this.bccCode = bccCode;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(Integer subCategoryId) {
        this.subCategoryId = subCategoryId;
    }
}
