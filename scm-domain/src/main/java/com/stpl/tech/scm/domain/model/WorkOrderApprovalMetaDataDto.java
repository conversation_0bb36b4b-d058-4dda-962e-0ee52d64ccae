package com.stpl.tech.scm.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class WorkOrderApprovalMetaDataDto {
    private Integer workOrderId;

    // vendor metaData
    private Integer vendorId;
    private String vendorName;
    private String vendorIpAddress;
    private Integer vendorSignId;
    private Integer vendorSignDocId;
    private String vendorSignUrl;
    private Date vendorSignedDate;

    // approver metaData
    private Integer approverId;
    private String approverName;
    private String approverIpAddress;
    private Integer approverSignId;
    private Integer approverSignDocId;
    private String approverSignUrl;
    private Date approverSignedDate;

}
