package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class PaymentRequestMetaDataDomain {
    Integer paymentRequestId;
    Double gstRate;
    Boolean isRcm;
    Boolean isGstAvailed;
    LdcVendorDomain ldcVendorDomain;
    TdsLegerRateDomain tdsLedger;
    GstOfStplDomain recipientState;
    GstStateMetaDataDomain supplierState;
    Double tdsPercentage;

    String loggedMessages;
}
