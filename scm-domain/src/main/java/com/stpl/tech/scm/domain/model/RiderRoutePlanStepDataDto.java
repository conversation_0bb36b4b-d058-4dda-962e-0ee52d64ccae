package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiderRoutePlanStepDataDto implements Serializable {
    private Integer riderRoutePlanStepDataId;
    private Integer riderRoutePlanDataRiderRoutePlanDataId;
    private RiderRouteStepStatusEnum routeStepStatus;
    private Integer currentStore;
    private Integer nextStore;
    private RiderActionEnum dropStatus;
    private Date dropTime;
    private BigDecimal dropTemperature;
    private RiderActionEnum pickupStatus;
    private Date pickupTime;
    private BigDecimal pickupTemperature;
    private Integer routeStep;
    private Integer pickedUpToId;
    private Integer dropOffToId;
    private Date riderReachedTime;
    private Date riderLeaveTime;
    private Set<RiderRoutePlanItemDataDto> riderRoutePlanItemDataSet;
}
