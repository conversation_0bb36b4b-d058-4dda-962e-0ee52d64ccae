package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrder {
    private Integer workOrderId;
    private String generatedWorkOrderNumber;
    private VendorContractV2Dto vendorContract;
    private Integer contractId;
    private WorkOrderType workOrderType;
    private VendorContractStatus workOrderStatus;
    private Date startDate;
    private Date endDate;
    private Integer approvalRequestId;
    private String isByPassed;
    private Integer workOrderDocId;
    private Integer createdBy;
    private String createdByName;
    private Date createdAt;
    private Boolean isPreviouslyByPassed;
    private Set<VendorContractItemDataVO> vendorContractItemDataVOS = new HashSet<>();

    // approval metadata
    private String authIpAddress;
    private String token;
    private Integer authDigitalSignId;
    private Integer authSignedDocId;
    private Integer unsignedDocumentId;
    private String approverApproveLink;

    // vendor details
    private Integer vendorId;
    private String vendorName;
    private String vendorIpAddress;
    private Integer vendorDigitalSignId;
    private Integer vendorSignedDocId;
    private String vendorDesignation;
    private String vendorApprovalLink;
}
