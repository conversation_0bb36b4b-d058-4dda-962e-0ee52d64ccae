package com.stpl.tech.scm.domain.model;

public enum OrderTypes {
    SO("SO_ID", "ServiceOrderData", "so", "totalAmount", "ServiceOrderItemData", "", ""),
    PO("PO_ID", "PurchaseOrderData", "po", "paidAmount", "PurchaseOrderItemData", "purchaseOrderItemDatas","purchaseOrderData"),
    TO("TO_ID", "TransferOrderData", "to", "totalAmount", "TransferOrderItemData", "transferOrderItemDatas", "transferOrderData"),
    RO("RO_ID", "RequestOrderData", "ro", "totalAmount", "RequestOrderItemData", "requestOrderItemDatas", "requestOrderData");

    private final String keyType;
    private final String clazz;
    private final String shortName;
    private final String totalAmount;
    private final String childClassName;
    private final String parentOfItemData;
    private final String childOfParent;

    OrderTypes(String keyType, String clazz, String shortName, String totalAmount, String childClassName, String childOfParent, String parentOfItemData) {
        this.keyType = keyType;
        this.clazz = clazz;
        this.shortName = shortName;
        this.totalAmount = totalAmount;
        this.childClassName = childClassName;
        this.childOfParent = childOfParent;
        this.parentOfItemData = parentOfItemData;
    }

    public String getKeyType() {
        return keyType;
    }

    public String getClazz() {
        return clazz;
    }

    public String getShortName() {
        return shortName;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public String getChildClassName() {
        return childClassName;
    }

    public String getChildOfParent() {
        return childOfParent;
    }

    public String getParentOfItemData() {
        return parentOfItemData;
    }

}
