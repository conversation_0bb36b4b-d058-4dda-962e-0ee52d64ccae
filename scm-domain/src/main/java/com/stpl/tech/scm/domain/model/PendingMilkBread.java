/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.domain.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

public class PendingMilkBread {

    private Boolean receivingDone;
    private Date maxLimitTime;
    private Set<String> vendorIds =  new HashSet<>();
    private Set<Integer> roIds =  new HashSet<>();
    private Date serverTime;

    public PendingMilkBread() {
    }

    public PendingMilkBread(Boolean receivingDone) {
        this.receivingDone = receivingDone;
    }

    public Boolean getReceivingDone() {
        return receivingDone;
    }

    public void setReceivingDone(Boolean receivingDone) {
        this.receivingDone = receivingDone;
    }

    public Date getMaxLimitTime() {
        return maxLimitTime;
    }

    public void setMaxLimitTime(Date maxLimitTime) {
        this.maxLimitTime = maxLimitTime;
    }

    public Set<String> getVendorIds() {
        return vendorIds;
    }

    public void setVendorIds(Set<String> vendorIds) {
        this.vendorIds = vendorIds;
    }

    public Set<Integer> getRoIds() {
        return roIds;
    }

    public void setRoIds(Set<Integer> roIds) {
        this.roIds = roIds;
    }

    public Date getServerTime() {
        return serverTime;
    }

    public void setServerTime(Date serverTime) {
        this.serverTime = serverTime;
    }
}
