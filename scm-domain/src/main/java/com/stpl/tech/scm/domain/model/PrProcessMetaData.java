package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class PrProcessMetaData {

    List<String> budgetCategory;

    List<TdsLegerRateDomain> tdsLedgerRatesList;

    List<GstStateMetaDataDomain> gstStateMetaDataList;

    List<GstOfStplDomain> gstOfStplsList;
}
