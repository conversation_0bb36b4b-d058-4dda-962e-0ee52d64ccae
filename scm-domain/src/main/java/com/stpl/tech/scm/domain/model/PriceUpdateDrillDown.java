//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.02.22 at 07:12:15 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PriceUpdateEntry complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PriceUpdateEntry"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="keyType" type="{http://www.w3schools.com}PriceUpdateEntryType"/&gt;
 *         &lt;element name="keyId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="keyName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="updatedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="editedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="approvedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="entryStatus" type="{http://www.w3schools.com}PriceUpdateEventStatus"/&gt;
 *         &lt;element name="errorCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PriceUpdateEntry", propOrder = {
    "id",
    "keyType",
    "keyId",
    "keyName",
    "unitOfMeasure",
    "unitPrice",
    "updatedUnitPrice",
    "editedUnitPrice",
    "approvedUnitPrice",
    "entryStatus",
    "errorCode"
})
public class PriceUpdateDrillDown {

    protected int id;
    @XmlElement(required = true)
    protected String drilldownCategory;
    @XmlElement(required = true)
    protected String drilldownType;
    @XmlElement(required = true)
    protected String keyType;
    protected int keyId;
    @XmlElement(required = true)
    protected String keyName;
    @XmlElement(required = true)
    protected String unitOfMeasure;
    @XmlElement(required = true)
    protected BigDecimal unitPrice;
    @XmlElement(required = true)
    protected BigDecimal quantity;
    @XmlElement(required = true)
    protected BigDecimal cost;
    @XmlElement(required = true, nillable = true)
    protected boolean error;
    protected List<String> errors;

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the keyType property.
     * 
     * @return
     *     possible object is
     *     {@link PriceUpdateEntryType }
     *     
     */
    public String getKeyType() {
        return keyType;
    }

    /**
     * Sets the value of the keyType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PriceUpdateEntryType }
     *     
     */
    public void setKeyType(String value) {
        this.keyType = value;
    }

    /**
     * Gets the value of the keyId property.
     * 
     */
    public int getKeyId() {
        return keyId;
    }

    /**
     * Sets the value of the keyId property.
     * 
     */
    public void setKeyId(int value) {
        this.keyId = value;
    }

    /**
     * Gets the value of the keyName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKeyName() {
        return keyName;
    }

    /**
     * Sets the value of the keyName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKeyName(String value) {
        this.keyName = value;
    }

    /**
     * Gets the value of the unitOfMeasure property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    /**
     * Sets the value of the unitOfMeasure property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitOfMeasure(String value) {
        this.unitOfMeasure = value;
    }

    /**
     * Gets the value of the unitPrice property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /**
     * Sets the value of the unitPrice property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setUnitPrice(BigDecimal value) {
        this.unitPrice = value;
    }

    /**
     * Gets the value of the updatedUnitPrice property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * Sets the value of the updatedUnitPrice property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setQuantity(BigDecimal value) {
        this.quantity = value;
    }

    /**
     * Gets the value of the editedUnitPrice property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getCost() {
        return cost;
    }

    /**
     * Sets the value of the editedUnitPrice property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setCost(BigDecimal value) {
        this.cost = value;
    }

	/**
	 * @return the drilldownCategory
	 */
	public String getDrilldownCategory() {
		return drilldownCategory;
	}

	/**
	 * @param drilldownCategory the drilldownCategory to set
	 */
	public void setDrilldownCategory(String drilldownCategory) {
		this.drilldownCategory = drilldownCategory;
	}

	/**
	 * @return the drilldownName
	 */
	public String getDrilldownType() {
		return drilldownType;
	}

	/**
	 * @param drilldownName the drilldownName to set
	 */
	public void setDrilldownType(String drilldownType) {
		this.drilldownType = drilldownType;
	}

	/**
	 * @return the hasError
	 */
	public boolean isError() {
		return error;
	}

	/**
	 * @param hasError the hasError to set
	 */
	public void setError(boolean hasError) {
		this.error = hasError;
	}

	/**
	 * @return the errors
	 */
	public List<String> getErrors() {
		if(errors == null){
			errors = new ArrayList<String>();
		}
		return errors;
	}

    

}
