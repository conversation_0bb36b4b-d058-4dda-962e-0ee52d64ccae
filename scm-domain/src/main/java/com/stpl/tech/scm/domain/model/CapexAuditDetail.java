package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class CapexAuditDetail {

    @XmlElement(required = true)
    protected Integer id;
    @XmlElement(required = true)
    protected Integer capexRequestId;
    @XmlElement(required = true)
    protected String downloadedBy;
    @XmlElement(required = true)
    protected Integer unitId;
    @XmlElement(required = true)
    protected String unitName;
    @XmlElement(required = true)
    protected String uploadedBy;
    @XmlElement(required = true)
    protected String downloadedPath;
    @XmlElement(required = true)
    protected String uploadedPath;
    @XmlElement(required = true)
    protected String accessKey;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true)
    protected String version;
    @XmlElement(required = true)
    protected String type;
    @XmlElement(required = false)
    protected Date lastApprovalDate;
    @XmlElement(required = false)
    protected Date generationTime;
    @XmlElement(required = false)
    protected BigDecimal uploadedAmount;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCapexRequestId() {
        return capexRequestId;
    }

    public void setCapexRequestId(Integer capexRequestId) {
        this.capexRequestId = capexRequestId;
    }

    public String getDownloadedBy() {
        return downloadedBy;
    }

    public void setDownloadedBy(String downloadedBy) {
        this.downloadedBy = downloadedBy;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(String uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public String getDownloadedPath() {
        return downloadedPath;
    }

    public void setDownloadedPath(String downloadedPath) {
        this.downloadedPath = downloadedPath;
    }

    public String getUploadedPath() {
        return uploadedPath;
    }

    public void setUploadedPath(String uploadedPath) {
        this.uploadedPath = uploadedPath;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getLastApprovalDate() {
        return lastApprovalDate;
    }

    public void setLastApprovalDate(Date lastApprovalDate) {
        this.lastApprovalDate = lastApprovalDate;
    }

    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    public BigDecimal getUploadedAmount() {
        return uploadedAmount;
    }

    public void setUploadedAmount(BigDecimal uploadedAmount) {
        this.uploadedAmount = uploadedAmount;
    }
}
