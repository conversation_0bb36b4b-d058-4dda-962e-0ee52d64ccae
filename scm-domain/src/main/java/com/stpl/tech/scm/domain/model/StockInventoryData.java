/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.07.06 at 02:55:04 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for StockInventoryResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="StockInventoryResponse"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unit" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="eventType" type="{http://www.w3schools.com}StockEventType"/&gt;
 *         &lt;element name="errorResponse" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="inventoryResponse" type="{http://www.w3schools.com}ProductStockForUnit" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "StockInventoryResponse", propOrder = {
    "unit",
    "eventType",
    "errorResponse",
    "inventoryResponse"
})
public class StockInventoryData {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer unit;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer generatedBy;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected StockEventType eventType;
    @XmlSchemaType(name = "string")
    protected StockTakeType frequency;
    @XmlElement(required = true, nillable = true)
    protected String errorResponse;
    @XmlElement(required = true)
    protected List<ProductStockForUnit> inventoryResponse;

    protected Integer stockTakeSumoDayCloseEventId;
    protected List<DayCloseTxnItem> negativeVarianceList;

    /**
     * Gets the value of the unit property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getUnit() {
        return unit;
    }

    /**
     * Sets the value of the unit property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setUnit(Integer value) {
        this.unit = value;
    }

    public Integer getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(Integer generatedBy) {
		this.generatedBy = generatedBy;
	}

	/**
     * Gets the value of the eventType property.
     *
     * @return
     *     possible object is
     *     {@link StockEventType }
     *
     */
    public StockEventType getEventType() {
        return eventType;
    }

    /**
     * Sets the value of the eventType property.
     *
     * @param value
     *     allowed object is
     *     {@link StockEventType }
     *
     */
    public void setEventType(StockEventType value) {
        this.eventType = value;
    }

    
    public StockTakeType getFrequency() {
		return frequency;
	}

	public void setFrequency(StockTakeType frequency) {
		this.frequency = frequency;
	}

	/**
     * Gets the value of the errorResponse property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErrorResponse() {
        return errorResponse;
    }

    /**
     * Sets the value of the errorResponse property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErrorResponse(String value) {
        this.errorResponse = value;
    }

    /**
     * Gets the value of the inventoryResponse property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the inventoryResponse property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getInventoryResponse().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ProductStockForUnit }
     * 
     * 
     */
    public List<ProductStockForUnit> getInventoryResponse() {
        if (inventoryResponse == null) {
            inventoryResponse = new ArrayList<ProductStockForUnit>();
        }
        return this.inventoryResponse;
    }

    public Integer getStockTakeSumoDayCloseEventId() {
        return stockTakeSumoDayCloseEventId;
    }

    public void setStockTakeSumoDayCloseEventId(Integer stockTakeSumoDayCloseEventId) {
        this.stockTakeSumoDayCloseEventId = stockTakeSumoDayCloseEventId;
    }

    public List<DayCloseTxnItem> getNegativeVarianceList() {
        return negativeVarianceList;
    }

    public void setNegativeVarianceList(List<DayCloseTxnItem> negativeVarianceList) {
        this.negativeVarianceList = negativeVarianceList;
    }
}
