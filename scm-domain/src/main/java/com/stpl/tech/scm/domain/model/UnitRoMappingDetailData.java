package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnitRoMappingDetailData {

    private Integer unitId;
    private String unitName;
    private List<Integer> roIds;

    public UnitRoMappingDetailData(Integer unitId,String unitName){
        this.unitId=unitId;
        this.unitName = unitName;
        this.roIds = new ArrayList<>();
    }
}
