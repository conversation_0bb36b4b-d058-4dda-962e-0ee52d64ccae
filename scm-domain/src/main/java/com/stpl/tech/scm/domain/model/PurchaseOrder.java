//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.05.19 at 03:04:05 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for PurchaseOrder complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PurchaseOrder"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="initiationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="deliveryUnitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedForVendor" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="lastUpdatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="approvedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="fulfillmentDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="billAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="paidAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="receiptNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}PurchaseOrderStatus"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="recieptNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="totalTaxes" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="vendorNotified" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="forceClosed" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="dispatchLocation" type="{http://www.w3schools.com}VendorDispatchLocation"/&gt;
 *         &lt;element name="poInvoice" type="{http://www.w3schools.com}DocumentDetail"/&gt;
 *         &lt;element name="notifications" type="{http://www.w3schools.com}PurchaseOrderNotification" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="goodsReceivedList" type="{http://www.w3schools.com}GoodsReceived" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="orderItems" type="{http://www.w3schools.com}PurchaseOrderItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="orderType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PurchaseOrder", propOrder = {
    "id",
    "generationTime",
    "initiationTime",
    "lastUpdateTime",
    "deliveryUnitId",
    "generatedForVendor",
    "generatedBy",
    "lastUpdatedBy",
    "approvedBy",
    "fulfillmentDate",
    "billAmount",
    "paidAmount",
    "receiptNumber",
    "status",
    "comment",
    "totalTaxes",
    "vendorNotified",
    "forceClosed",
    "dispatchLocation",
    "poInvoice",
    "notifications",
    "goodsReceivedList",
    "orderItems",
        "orderType"
})
public class PurchaseOrder {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date initiationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(required = true)
    protected IdCodeName deliveryUnitId;
    @XmlElement(required = true)
    protected IdCodeName company;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName generatedForVendor;
    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName lastUpdatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName approvedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date fulfillmentDate;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal billAmount;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal paidAmount;
    @XmlElement(required = true, nillable = true)
    protected String receiptNumber;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PurchaseOrderStatus status;
    @XmlElement(required = true)
    protected String comment;
    @XmlElement(required = true)
    protected BigDecimal totalTaxes;
    protected boolean vendorNotified;
    protected boolean forceClosed;
    @XmlElement(required = true)
    protected VendorDispatchLocation dispatchLocation;
    @XmlElement(required = true)
    protected POCreationType creationType;
    @XmlElement(required = true)
    protected DocumentDetail poInvoice;
    protected List<PurchaseOrderNotification> notifications;
    protected List<VendorGR> goodsReceivedList;
    protected List<PurchaseOrderItem> orderItems;
    // TODO change signature to order type
    protected String orderType;
    protected Date expiryDate;
    protected String expiryStatus;
    protected Integer leadTime;
    protected String type;
    protected VendorAdvancePayment vendorAdvancePayment;
    protected List<VendorAdvancePayment> vendorAdvancePayments;
    protected BigDecimal prAmount;
    protected BigDecimal advanceAmount;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the generationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getGenerationTime() {
        return generationTime;
    }

    /**
     * Sets the value of the generationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGenerationTime(Date value) {
        this.generationTime = value;
    }

    /**
     * Gets the value of the initiationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getInitiationTime() {
        return initiationTime;
    }

    /**
     * Sets the value of the initiationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInitiationTime(Date value) {
        this.initiationTime = value;
    }

    /**
     * Gets the value of the lastUpdateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * Sets the value of the lastUpdateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastUpdateTime(Date value) {
        this.lastUpdateTime = value;
    }

    /**
     * Gets the value of the deliveryUnitId property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getDeliveryUnitId() {
        return deliveryUnitId;
    }

    /**
     * Sets the value of the deliveryUnitId property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setDeliveryUnitId(IdCodeName value) {
        this.deliveryUnitId = value;
    }

    /**
     * Gets the value of the generatedForVendor property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getGeneratedForVendor() {
        return generatedForVendor;
    }

    /**
     * Sets the value of the generatedForVendor property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setGeneratedForVendor(IdCodeName value) {
        this.generatedForVendor = value;
    }

    /**
     * Gets the value of the generatedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    /**
     * Sets the value of the generatedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setGeneratedBy(IdCodeName value) {
        this.generatedBy = value;
    }

    /**
     * Gets the value of the lastUpdatedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    /**
     * Sets the value of the lastUpdatedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setLastUpdatedBy(IdCodeName value) {
        this.lastUpdatedBy = value;
    }

    /**
     * Gets the value of the approvedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getApprovedBy() {
        return approvedBy;
    }

    /**
     * Sets the value of the approvedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setApprovedBy(IdCodeName value) {
        this.approvedBy = value;
    }

    /**
     * Gets the value of the fulfillmentDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getFulfillmentDate() {
        return fulfillmentDate;
    }

    /**
     * Sets the value of the fulfillmentDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFulfillmentDate(Date value) {
        this.fulfillmentDate = value;
    }

    /**
     * Gets the value of the billAmount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getBillAmount() {
        return billAmount;
    }

    /**
     * Sets the value of the billAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setBillAmount(BigDecimal value) {
        this.billAmount = value;
    }

    /**
     * Gets the value of the paidAmount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    /**
     * Sets the value of the paidAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPaidAmount(BigDecimal value) {
        this.paidAmount = value;
    }

    /**
     * Gets the value of the receiptNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReceiptNumber() {
        return receiptNumber;
    }

    /**
     * Sets the value of the receiptNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReceiptNumber(String value) {
        this.receiptNumber = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link PurchaseOrderStatus }
     *     
     */
    public PurchaseOrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link PurchaseOrderStatus }
     *     
     */
    public void setStatus(PurchaseOrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the comment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the totalTaxes property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    /**
     * Sets the value of the totalTaxes property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalTaxes(BigDecimal value) {
        this.totalTaxes = value;
    }

    /**
     * Gets the value of the vendorNotified property.
     * 
     */
    public boolean isVendorNotified() {
        return vendorNotified;
    }

    /**
     * Sets the value of the vendorNotified property.
     * 
     */
    public void setVendorNotified(boolean value) {
        this.vendorNotified = value;
    }

    /**
     * Gets the value of the forceClosed property.
     * 
     */
    public boolean isForceClosed() {
        return forceClosed;
    }

    /**
     * Sets the value of the forceClosed property.
     * 
     */
    public void setForceClosed(boolean value) {
        this.forceClosed = value;
    }

    /**
     * Gets the value of the dispatchLocation property.
     * 
     * @return
     *     possible object is
     *     {@link VendorDispatchLocation }
     *     
     */
    public VendorDispatchLocation getDispatchLocation() {
        return dispatchLocation;
    }

    /**
     * Sets the value of the dispatchLocation property.
     * 
     * @param value
     *     allowed object is
     *     {@link VendorDispatchLocation }
     *     
     */
    public void setDispatchLocation(VendorDispatchLocation value) {
        this.dispatchLocation = value;
    }

    /**
     * Gets the value of the poInvoice property.
     * 
     * @return
     *     possible object is
     *     {@link DocumentDetail }
     *     
     */
    public DocumentDetail getPoInvoice() {
        return poInvoice;
    }

    /**
     * Sets the value of the poInvoice property.
     * 
     * @param value
     *     allowed object is
     *     {@link DocumentDetail }
     *     
     */
    public void setPoInvoice(DocumentDetail value) {
        this.poInvoice = value;
    }

    /**
     * Gets the value of the notifications property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the notifications property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getNotifications().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PurchaseOrderNotification }
     * 
     * 
     */
    public List<PurchaseOrderNotification> getNotifications() {
        if (notifications == null) {
            notifications = new ArrayList<PurchaseOrderNotification>();
        }
        return this.notifications;
    }

    /**
     * Gets the value of the goodsReceivedList property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the goodsReceivedList property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getGoodsReceivedList().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link GoodsReceived }
     * 
     * 
     */
    public List<VendorGR> getGoodsReceivedList() {
        if (goodsReceivedList == null) {
            goodsReceivedList = new ArrayList<>();
        }
        return this.goodsReceivedList;
    }

    /**
     * Gets the value of the orderItems property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the orderItems property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOrderItems().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PurchaseOrderItem }
     * 
     * 
     */
    public List<PurchaseOrderItem> getOrderItems() {
        if (orderItems == null) {
            orderItems = new ArrayList<PurchaseOrderItem>();
        }
        return this.orderItems;
    }

    public POCreationType getCreationType() {
        return creationType;
    }

    public void setCreationType(POCreationType creationType) {
        this.creationType = creationType;
    }

	public IdCodeName getCompany() {
		return company;
	}

	public void setCompany(IdCodeName company) {
		this.company = company;
	}

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getExpiryStatus() {
        return expiryStatus;
    }

    public void setExpiryStatus(String expiryStatus) {
        this.expiryStatus = expiryStatus;
    }

    public Integer getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime) {
        this.leadTime = leadTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public VendorAdvancePayment getVendorAdvancePayment() {
        return vendorAdvancePayment;
    }

    public void setVendorAdvancePayment(VendorAdvancePayment vendorAdvancePayment) {
        this.vendorAdvancePayment = vendorAdvancePayment;
    }

    public List<VendorAdvancePayment> getVendorAdvancePayments() {
        return vendorAdvancePayments;
    }

    public void setVendorAdvancePayments(List<VendorAdvancePayment> vendorAdvancePayments) {
        this.vendorAdvancePayments = vendorAdvancePayments;
    }

    public BigDecimal getPrAmount() {
        return prAmount;
    }

    public void setPrAmount(BigDecimal prAmount) {
        this.prAmount = prAmount;
    }

    public BigDecimal getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(BigDecimal advanceAmount) {
        this.advanceAmount = advanceAmount;
    }
}
