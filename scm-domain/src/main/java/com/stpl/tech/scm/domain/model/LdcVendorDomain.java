package com.stpl.tech.scm.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.stereotype.Service;

import java.util.Date;

@Getter
@Setter
@ToString
public class LdcVendorDomain {
        Long ldcId;
        Double ldcLimit;
        Date ldcTenureFrom;
        Date ldcTenureTo;
        Double ldcTdsRate;
        String ldcTdsSection;
        String ldcCertificateNo;
        Double remainingLimit;
        Integer vendorId;
        String status = "ACTIVE";


}
