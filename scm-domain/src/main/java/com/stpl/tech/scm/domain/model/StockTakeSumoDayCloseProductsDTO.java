package com.stpl.tech.scm.domain.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class StockTakeSumoDayCloseProductsDTO {

    private Integer sumoDayCloseProductItemId;

    private Integer stockTakeSumoDayCloseEventId;

    private Integer productId;

    private String uom;

    private String productType;

    private String productName;

    private Date updatedTime;

    private List<DayCloseProductPackagingMappingsDTO> dayCloseProductPackagingMappings = new ArrayList<>(0);
}
