//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.11 at 12:23:12 PM IST
//

package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for RequestOrderItem complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="RequestOrderItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="requestedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestedAbsoluteQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="transferredQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="receivedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="negotiatedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="calculatedAmount" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="vendor" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="productCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RequestOrderItem", propOrder = { "id", "productId", "productName", "requestedQuantity",
		"requestedAbsoluteQuantity", "transferredQuantity", "receivedQuantity", "unitOfMeasure", "unitPrice",
		"negotiatedUnitPrice", "calculatedAmount", "vendor", "productCode", "unitId", "excessQuantity", "originalQuantity" })
public class RequestOrderItem {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	protected int productId;
	@XmlElement(required = true)
	protected String productName;
	protected float requestedQuantity;
	protected float requestedAbsoluteQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float transferredQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float receivedQuantity;
	@XmlElement(required = true)
	protected String unitOfMeasure;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float unitPrice;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float negotiatedUnitPrice;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float calculatedAmount;
	@XmlElement(required = true, nillable = true)
	protected IdCodeName vendor;
	@XmlElement(required = true, nillable = true)
	protected String productCode;
	@XmlElement(required = false, nillable = true)
	protected Integer skuId;
	@XmlElement(required = false, nillable = true)
	protected Integer packagingId;
	protected String packagingName;
	protected BigDecimal packagingQuantity;
	protected BigDecimal conversionRatio;
	protected BigDecimal tax;
	protected String code;
	protected List<TaxDetail> taxes;
	protected BigDecimal total;
	protected Integer unitId;
	protected BigDecimal excessQuantity;
	protected BigDecimal originalQuantity;
	protected Date expiryDate;
	private Boolean productionBookingCompleted;
	private BigDecimal productionBookingQuantity;
	protected String reason;
	protected String comment;
	protected BigDecimal predictedQuantity;
	protected BigDecimal suggestedQuantity;
	protected BigDecimal diffQuantity;

	/**
	 * Gets the value of the id property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 * @param value allowed object is {@link Integer }
	 *
	 */
	public void setId(Integer value) {
		this.id = value;
	}

	/**
	 * Gets the value of the productId property.
	 *
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * Sets the value of the productId property.
	 *
	 */
	public void setProductId(int value) {
		this.productId = value;
	}

	/**
	 * Gets the value of the productName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * Sets the value of the productName property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setProductName(String value) {
		this.productName = value;
	}

	/**
	 * Gets the value of the requestedQuantity property.
	 *
	 */
	public float getRequestedQuantity() {
		return requestedQuantity;
	}

	/**
	 * Sets the value of the requestedQuantity property.
	 *
	 */
	public void setRequestedQuantity(float value) {
		this.requestedQuantity = value;
	}

	/**
	 * Gets the value of the requestedAbsoluteQuantity property.
	 *
	 */
	public float getRequestedAbsoluteQuantity() {
		return requestedAbsoluteQuantity;
	}

	/**
	 * Sets the value of the requestedAbsoluteQuantity property.
	 *
	 */
	public void setRequestedAbsoluteQuantity(float value) {
		this.requestedAbsoluteQuantity = value;
	}

	/**
	 * Gets the value of the transferredQuantity property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getTransferredQuantity() {
		return transferredQuantity;
	}

	/**
	 * Sets the value of the transferredQuantity property.
	 *
	 * @param value allowed object is {@link Float }
	 *
	 */
	public void setTransferredQuantity(Float value) {
		this.transferredQuantity = value;
	}

	/**
	 * Gets the value of the receivedQuantity property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getReceivedQuantity() {
		return receivedQuantity;
	}

	/**
	 * Sets the value of the receivedQuantity property.
	 *
	 * @param value allowed object is {@link Float }
	 *
	 */
	public void setReceivedQuantity(Float value) {
		this.receivedQuantity = value;
	}

	/**
	 * Gets the value of the unitOfMeasure property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	/**
	 * Sets the value of the unitOfMeasure property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setUnitOfMeasure(String value) {
		this.unitOfMeasure = value;
	}

	/**
	 * Gets the value of the unitPrice property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getUnitPrice() {
		return unitPrice;
	}

	/**
	 * Sets the value of the unitPrice property.
	 *
	 * @param value allowed object is {@link Float }
	 *
	 */
	public void setUnitPrice(Float value) {
		this.unitPrice = value;
	}

	/**
	 * Gets the value of the negotiatedUnitPrice property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getNegotiatedUnitPrice() {
		return negotiatedUnitPrice;
	}

	/**
	 * Sets the value of the negotiatedUnitPrice property.
	 *
	 * @param value allowed object is {@link Float }
	 *
	 */
	public void setNegotiatedUnitPrice(Float value) {
		this.negotiatedUnitPrice = value;
	}

	/**
	 * Gets the value of the calculatedAmount property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getCalculatedAmount() {
		return calculatedAmount;
	}

	/**
	 * Sets the value of the calculatedAmount property.
	 *
	 * @param value allowed object is {@link Float }
	 *
	 */
	public void setCalculatedAmount(Float value) {
		this.calculatedAmount = value;
	}

	/**
	 * Gets the value of the vendor property.
	 *
	 * @return possible object is {@link IdCodeName }
	 *
	 */
	public IdCodeName getVendor() {
		return vendor;
	}

	/**
	 * Sets the value of the vendor property.
	 *
	 * @param value allowed object is {@link IdCodeName }
	 *
	 */
	public void setVendor(IdCodeName value) {
		this.vendor = value;
	}

	/**
	 * Gets the value of the productCode property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getProductCode() {
		return productCode;
	}

	/**
	 * Sets the value of the productCode property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setProductCode(String value) {
		this.productCode = value;
	}

	public Integer getSkuId() {
		return skuId;
	}

	public void setSkuId(Integer skuId) {
		this.skuId = skuId;
	}

	public Integer getPackagingId() {
		return packagingId;
	}

	public void setPackagingId(Integer packagingId) {
		this.packagingId = packagingId;
	}

	public String getPackagingName() {
		return packagingName;
	}

	public void setPackagingName(String packagingName) {
		this.packagingName = packagingName;
	}

	public BigDecimal getPackagingQuantity() {
		return packagingQuantity;
	}

	public void setPackagingQuantity(BigDecimal packagingQty) {
		this.packagingQuantity = packagingQty;
	}

	public BigDecimal getConversionRatio() {
		return conversionRatio;
	}

	public void setConversionRatio(BigDecimal conversionRatio) {
		this.conversionRatio = conversionRatio;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public List<TaxDetail> getTaxes() {
		return taxes;
	}

	public void setTaxes(List<TaxDetail> taxes) {
		this.taxes = taxes;
	}

	public BigDecimal getTotal() {
		return total;
	}

	public void setTotal(BigDecimal total) {
		this.total = total;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public BigDecimal getExcessQuantity() {
		return excessQuantity;
	}

	public void setExcessQuantity(BigDecimal excessQuantity) {
		this.excessQuantity = excessQuantity;
	}

	public BigDecimal getOriginalQuantity() {
		return originalQuantity;
	}

	public void setOriginalQuantity(BigDecimal originalQuantity) {
		this.originalQuantity = originalQuantity;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public Boolean getProductionBookingCompleted() {
		return productionBookingCompleted;
	}

	public void setProductionBookingCompleted(Boolean productionBookingCompleted) {
		this.productionBookingCompleted = productionBookingCompleted;
	}

	public BigDecimal getProductionBookingQuantity() {
		return productionBookingQuantity;
	}

	public void setProductionBookingQuantity(BigDecimal productionBookingQuantity) {
		this.productionBookingQuantity = productionBookingQuantity;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public BigDecimal getPredictedQuantity() {
		return predictedQuantity;
	}

	public void setPredictedQuantity(BigDecimal predictedQuantity) {
		this.predictedQuantity = predictedQuantity;
	}

	public BigDecimal getSuggestedQuantity() {
		return suggestedQuantity;
	}

	public void setSuggestedQuantity(BigDecimal suggestedQuantity) {
		this.suggestedQuantity = suggestedQuantity;
	}

	public BigDecimal getDiffQuantity() {
		return diffQuantity;
	}

	public void setDiffQuantity(BigDecimal diffQuantity) {
		this.diffQuantity = diffQuantity;
	}
}
