package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorContractVO {
    private Integer vendorContractId;
    private Integer vendorId;
    private String contractRequestedBy;
    private Integer unsignedDocumentId;
    private Integer signedDocumentId;
    private Integer authSignedDocumentId;
    private String vendorName;
    private Date startDate;
    private Date endDate;
    private String recordStatus;
    private String createdBy;
    private String isMailTriggered;
    private Date creationTime;

    private List<VendorContractItemVO> vendorContractItemVOS = new ArrayList<>();

}