package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class Fountain9SalesComparison {

    private String completeProductName;
    private String productName;
    private Integer productId;
    private String dimension;
    private Integer brandId;
    private Map<String, BigDecimal> salesDataMap;
    private Map<String, BigDecimal> fountain9DataMap;
    private Integer scmProductId;
    private String scmProductName;
    private String completeScmProductName;
    private Map<String, String> reasonsMap;
    private Map<String, BigDecimal> f9TotalMap;

    public String getCompleteProductName() {
        return completeProductName;
    }

    public void setCompleteProductName(String completeProductName) {
        this.completeProductName = completeProductName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Map<String, BigDecimal> getSalesDataMap() {
        return salesDataMap;
    }

    public void setSalesDataMap(Map<String, BigDecimal> salesDataMap) {
        this.salesDataMap = salesDataMap;
    }

    public Map<String, BigDecimal> getFountain9DataMap() {
        if (Objects.isNull(this.fountain9DataMap)) {
            return new HashMap<>();
        }
        return fountain9DataMap;
    }

    public void setFountain9DataMap(Map<String, BigDecimal> fountain9DataMap) {
        this.fountain9DataMap = fountain9DataMap;
    }

    public Integer getScmProductId() {
        return scmProductId;
    }

    public void setScmProductId(Integer scmProductId) {
        this.scmProductId = scmProductId;
    }

    public String getScmProductName() {
        return scmProductName;
    }

    public void setScmProductName(String scmProductName) {
        this.scmProductName = scmProductName;
    }

    public String getCompleteScmProductName() {
        return completeScmProductName;
    }

    public void setCompleteScmProductName(String completeScmProductName) {
        this.completeScmProductName = completeScmProductName;
    }

    public Map<String, String> getReasonsMap() {
        if (Objects.isNull(reasonsMap)) {
            return new HashMap<>();
        }
        return reasonsMap;
    }

    public void setReasonsMap(Map<String, String> reasonsMap) {
        this.reasonsMap = reasonsMap;
    }

    public Map<String, BigDecimal> getF9TotalMap() {
        if (Objects.isNull(f9TotalMap)) {
            return new HashMap<>();
        }
        return f9TotalMap;
    }

    public void setF9TotalMap(Map<String, BigDecimal> f9TotalMap) {
        this.f9TotalMap = f9TotalMap;
    }
}
