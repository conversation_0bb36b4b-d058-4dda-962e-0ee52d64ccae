//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.12 at 01:35:23 PM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for UnitDetail complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="UnitDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitCategoryId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="tin" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitDetail", propOrder = {
    "unitId",
    "unitName",
    "unitCategoryId",
    "unitEmail",
    "unitStatus",
    "tin"
})
public  class  UnitDetail {

    protected int unitId;
    @XmlElement(required = true)
    protected String unitName;
    protected int unitCategoryId;
    protected String unitRegion;
    @XmlElement(required = true)
    protected String unitEmail;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus unitStatus;
    @XmlElement(required = true)
    protected String tin;
    protected Integer closingEndTime;
    protected Integer closingStartTime;
    protected Integer companyId;
    protected String shortCode;
    protected String varianceAcknowledgementRequired;
    protected HubSpokeEnum hubSpokeType;

    /**
     * Gets the value of the unitId property.
     *
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     *
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the unitName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUnitName() {
        return unitName;
    }

    /**
     * Sets the value of the unitName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUnitName(String value) {
        this.unitName = value;
    }

    /**
     * Gets the value of the unitCategoryId property.
     *
     */
    public int getUnitCategoryId() {
        return unitCategoryId;
    }

    /**
     * Sets the value of the unitCategoryId property.
     *
     */
    public void setUnitCategoryId(int value) {
        this.unitCategoryId = value;
    }

    /**
     * Gets the value of the unitEmail property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUnitEmail() {
        return unitEmail;
    }

    /**
     * Sets the value of the unitEmail property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUnitEmail(String value) {
        this.unitEmail = value;
    }

    /**
     * Gets the value of the unitStatus property.
     *
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *
     */
    public SwitchStatus getUnitStatus() {
        return unitStatus;
    }

    /**
     * Sets the value of the unitStatus property.
     *
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *
     */
    public void setUnitStatus(SwitchStatus value) {
        this.unitStatus = value;
    }

    /**
     * Gets the value of the tin property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTin() {
        return tin;
    }

    /**
     * Sets the value of the tin property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTin(String value) {
        this.tin = value;
    }

    public Integer getClosingEndTime() {
        return closingEndTime;
    }

    public void setClosingEndTime(Integer closingEndTime) {
        this.closingEndTime = closingEndTime;
    }

    public Integer getClosingStartTime() {
        return closingStartTime;
    }

    public void setClosingStartTime(Integer closingStartTime) {
        this.closingStartTime = closingStartTime;
    }

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

    public String getUnitRegion() {
        return unitRegion;
    }

    public void setUnitRegion(String unitRegion) {
        this.unitRegion = unitRegion;
    }

    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    public String getVarianceAcknowledgementRequired() {
        return varianceAcknowledgementRequired;
    }

    public void setVarianceAcknowledgementRequired(String varianceAcknowledgementRequired) {
        this.varianceAcknowledgementRequired = varianceAcknowledgementRequired;
    }

    public HubSpokeEnum getHubSpokeType() {
        return hubSpokeType;
    }

    public void setHubSpokeType(HubSpokeEnum hubSpokeType) {
        this.hubSpokeType = hubSpokeType;
    }
}
