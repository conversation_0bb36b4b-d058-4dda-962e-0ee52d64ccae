package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class SREmailShort {
    private Integer itemId;
    private String businessCostCenterName;
    private String costElementName;
    private String ascCode;
    private String serviceDescription;
    private BigDecimal receivedQuantity;
    private String unitOfMeasure;
    private BigDecimal unitPrice;
    private BigDecimal totalPrice;
    private BigDecimal totalTax;
    private BigDecimal taxRate;
    private String latest;

    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    public String getBusinessCostCenterName() {
        return businessCostCenterName;
    }

    public void setBusinessCostCenterName(String businessCostCenterName) {
        this.businessCostCenterName = businessCostCenterName;
    }

    public String getCostElementName() {
        return costElementName;
    }

    public void setCostElementName(String costElementName) {
        this.costElementName = costElementName;
    }

    public String getAscCode() {
        return ascCode;
    }

    public void setAscCode(String ascCode) {
        this.ascCode = ascCode;
    }

    public String getServiceDescription() {
        return serviceDescription;
    }

    public void setServiceDescription(String serviceDescription) {
        this.serviceDescription = serviceDescription;
    }

    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getLatest() {
        return latest;
    }

    public void setLatest(String latest) {
        this.latest = latest;
    }
}
