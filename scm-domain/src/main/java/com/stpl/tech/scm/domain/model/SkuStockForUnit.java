package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 08-01-2018.
 */
public class SkuStockForUnit extends ProductStockForUnit {

    protected String name;
    protected List<PackagingDefinition> packagings;

    public SkuStockForUnit(ProductStockForUnit parent) {
        this.productId = parent.getProductId();
        this.skuId = parent.getSkuId();
        this.unitPrice = parent.getUnitPrice();
        this.variance = parent.getVariance();
        this.booked = parent.getBooked();
        this.reverseBooked = parent.getReverseBooked();
        this.received = parent.getReceived();
        this.transferred = parent.getTransferred();
        this.wasted = parent.getWasted();
        this.consumption = parent.getConsumption();
        this.reverseConsumption = parent.getReverseConsumption();
        this.opening = parent.getOpening();
        this.expectedValue = parent.getExpectedValue();
        this.uom = parent.getUom();
        this.inventoryId = parent.getInventoryId();
        this.stockValue = parent.getStockValue();
        this.varianceCost = parent.getVarianceCost();
        this.categoryDef=parent.getCategoryDef();
        this.subCategoryDef=parent.getSubCategoryDef();
        this.receivedWithInitiatedGr = parent.getReceivedWithInitiatedGr();
        this.categoryId = parent.getCategoryId();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<PackagingDefinition> getPackagings() {
        if(packagings==null){
            packagings = new ArrayList<>();
        }
        return packagings;
    }
}
