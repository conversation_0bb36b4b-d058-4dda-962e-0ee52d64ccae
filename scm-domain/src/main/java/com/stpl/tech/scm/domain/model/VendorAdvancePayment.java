package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class VendorAdvancePayment {
    private Integer advancePaymentId;
    private Integer vendorId;
    private Integer paymentRequestId;
    private String advanceType;
    private String advanceStatus;
    private BigDecimal prAmount;
    private BigDecimal availableAmount;
    private BigDecimal blockedAmount;
    private Integer poId;
    private Integer soId;
    private String createdBy;
    private Integer createdById;
    private Date createdAt;
    private String rejectedFor;
    private List<Integer> pendingGrs;
    private List<Integer> pendingSrs;
    private List<Integer> pendingPrs;
    private BigDecimal usedAmount;
    private BigDecimal finalAvailable;
    private String poSoClosed;
    private String refundDate;
    private String refundInitiatedByName;
    private Integer selectedSoPo;
    private String lastPoSoStatus;
    private Integer refundInitiatedBy;
    private Date refundReceivedDate;
    private Integer lastUpdatedBy;
    private Date lastUpdatedDate;
    private List<VendorAdvancePaymentAuditLog> vendorAdvancePaymentAuditLogs;
    private List<VendorAdvancePaymentStatusLog> vendorAdvancePaymentStatusLogs;
    private Integer parentSoPo;
    private Date maxSettlementTime;
    private Integer diffDays;
    private VendorAdvancePayment adjustedWith;
    private Integer advanceDocId;
    private Integer advanceRefundDocId;
    private String lastUpdatedByName;
    private BigDecimal completePrAmount;
    private BigDecimal completeAvailableAmount;
    private BigDecimal completeBlockedAmount;
}
