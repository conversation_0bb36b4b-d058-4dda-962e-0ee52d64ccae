package com.stpl.tech.scm.domain.model;

import java.util.List;

public class PaymentRequestStatusChangeVO {

    private Integer paymentRequestId;
    private Integer updatedBy;
    private PaymentRequestStatus currentStatus;
    private PaymentRequestStatus newStatus;
    private String reason;
    private boolean updated = false;
    private String paymentType;
    private List<PaymentRequestQuery> paymentRequestQueries;

    private PaymentRequestMetaDataDomain paymentRequestMetaDataDomain;
    private PaymentRequest paymentRequest;

    private Long ldcId;
    public Integer getPaymentRequestId() {
        return paymentRequestId;
    }

    public void setPaymentRequestId(Integer paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }

    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    public PaymentRequestStatus getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(PaymentRequestStatus currentStatus) {
        this.currentStatus = currentStatus;
    }

    public PaymentRequestStatus getNewStatus() {
        return newStatus;
    }

    public void setNewStatus(PaymentRequestStatus newStatus) {
        this.newStatus = newStatus;
    }

    public boolean isUpdated() {
        return updated;
    }

    public void setUpdated(boolean updated) {
        this.updated = updated;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

    public List<PaymentRequestQuery> getPaymentRequestQueries() {
        return paymentRequestQueries;
    }

    public void setPaymentRequestQueries(List<PaymentRequestQuery> paymentRequestQueries) {
        this.paymentRequestQueries = paymentRequestQueries;
    }

    public PaymentRequestMetaDataDomain getPaymentRequestMetaDataDomain() {
        return paymentRequestMetaDataDomain;
    }

    public void setPaymentRequestMetaDataDomain(PaymentRequestMetaDataDomain paymentRequestMetaDataDomain) {
        this.paymentRequestMetaDataDomain = paymentRequestMetaDataDomain;
    }

    public PaymentRequest getPaymentRequest() {
        return paymentRequest;
    }

    public void setPaymentRequest(PaymentRequest paymentRequest) {
        this.paymentRequest = paymentRequest;
    }

    public Long getLdcId() {
        return ldcId;
    }

    public void setLdcId(Long ldcId) {
        this.ldcId = ldcId;
    }
}
