/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class VendorAdvancePaymentStatusLog {
    private Integer advancePaymentStatusLogId;
    private Integer advancePaymentDataId;
    private String fromStatus;
    private String toStatus;
    private Integer loggedBy;
    private Date loggedAt;
    private String loggedByName;
}
