package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;

/**
 * <p>Java class for RequestOrder complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="RequestOrder"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="isMandatory" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="levelFrom" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="levelTo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AttributeProperties", propOrder = {
        "attributeId",
        "isMandatory",
        "levelFrom",
        "levelTo"
})
public class AttributeProperties {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer attributeId;

    protected boolean isMandatory;

    protected String levelFrom;

    protected String levelTo;

    public Integer getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }

    public boolean isMandatory() {
        return isMandatory;
    }

    public void setMandatory(boolean mandatory) {
        isMandatory = mandatory;
    }

    public String getLevelFrom() {
        return levelFrom;
    }

    public void setLevelFrom(String levelFrom) {
        this.levelFrom = levelFrom;
    }

    public String getLevelTo() {
        return levelTo;
    }

    public void setLevelTo(String levelTo) {
        this.levelTo = levelTo;
    }
}
