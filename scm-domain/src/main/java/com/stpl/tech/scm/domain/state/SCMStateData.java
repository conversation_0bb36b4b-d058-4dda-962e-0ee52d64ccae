//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.06 at 03:23:09 PM IST 
//


package com.stpl.tech.scm.domain.state;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SCMStateData complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SCMStateData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="stateCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="stateName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="stateType" type="{http://www.w3schools.com}SCMStateType"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SCMStateData", propOrder = {
    "stateCode",
    "stateName",
    "stateType"
})
public class SCMStateData {

    @XmlElement(required = true)
    protected String stateCode;
    @XmlElement(required = true)
    protected String stateName;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SCMStateType stateType;

    /**
     * Gets the value of the stateCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStateCode() {
        return stateCode;
    }

    /**
     * Sets the value of the stateCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStateCode(String value) {
        this.stateCode = value;
    }

    /**
     * Gets the value of the stateName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStateName() {
        return stateName;
    }

    /**
     * Sets the value of the stateName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStateName(String value) {
        this.stateName = value;
    }

    /**
     * Gets the value of the stateType property.
     * 
     * @return
     *     possible object is
     *     {@link SCMStateType }
     *     
     */
    public SCMStateType getStateType() {
        return stateType;
    }

    /**
     * Sets the value of the stateType property.
     * 
     * @param value
     *     allowed object is
     *     {@link SCMStateType }
     *     
     */
    public void setStateType(SCMStateType value) {
        this.stateType = value;
    }

}
