/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentRequestQuery {
    private Integer prQueryId;
    private Integer prId;
    private Integer paymentDeviationId;
    private String paymentDeviationDetail;
    private Integer queryRaisedBy;
    private String  queryRaisedByName;
    private Integer queryResolvedBy;
    private String queryResolvedByName;
    private String raisedByComment;
    private String resolvedByComment;
    private Integer uploadedDocumentId;
    private Integer queriedForPrId;
    private DocumentDetail documentDetail;
}
