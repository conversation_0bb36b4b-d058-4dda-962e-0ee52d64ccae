//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2018.07.03 at 02:06:35 PM IST
//


package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.ErrorsVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;


/**
 * <p>Java class for SalesPerformaInvoice complex type.
 * <p>
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;complexType name="SalesPerformaInvoice"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendor" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="dispatchLocation" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}SalesPerformaType"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}SalesPerformaStatus"/&gt;
 *         &lt;element name="invoice" type="{http://www.w3schools.com}DocumentDetail"/&gt;
 *         &lt;element name="eWayBill" type="{http://www.w3schools.com}DocumentDetail"/&gt;
 *         &lt;element name="vehicleRegdNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="vehicleHandlerName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="totalCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalSellingCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalTax" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="additionalCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="sendingUnit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="needsApproval" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="createdAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cancelledBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="cancelledAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="closureId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="items" type="{http://www.w3schools.com}SalesPerformaInvoiceItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SalesPerformaInvoice", propOrder = {
        "id",
        "vendor",
        "dispatchLocation",
        "type",
        "status",
        "invoice",
        "ewayBill",
        "totalCost",
        "totalSellingCost",
        "totalTax",
        "additionalCharges",
        "totalAmount",
        "sendingUnit",
        "needsApproval",
        "comment",
        "createdBy",
        "createdAt",
        "cancelledBy",
        "cancelledAt",
        "closureId",
        "items",
        "assetOrder",
        "deliveredDocumentUrl",
        "billingType",
        "billingAddress",
        "docPOId",
        "generatedBarcodeId"
})
public class SalesPerformaInvoice extends AbstractInventoryVO implements ConsumptionVO, ReceivingVO, ErrorsVO {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true)
    protected IdCodeName vendor;
    @XmlElement(required = true)
    protected IdCodeName dispatchLocation;
    protected IdCodeName sendingCompany;
    protected String dispatchAddress;
    protected String vendorAddress;
    protected String deliveryAddress;
    protected InvoiceDocType invoiceType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SalesPerformaType type;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SalesPerformaStatus status;
    @XmlElement(required = true)
    protected String invoice;
    @XmlElement(required = true)
    protected String ewayBill;
    @XmlElement(required = true)
    protected BigDecimal totalCost;
    @XmlElement(required = true)
    protected BigDecimal totalSellingCost;
    @XmlElement(required = true)
    protected BigDecimal totalTax;
    @XmlElement(required = true)
    protected BigDecimal additionalCharges;
    @XmlElement(required = true)
    protected BigDecimal totalAmount;
    @XmlElement(required = true)
    protected IdCodeName sendingUnit;
    protected boolean needsApproval;
    @XmlElement(required = true)
    protected String comment;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date createdAt;
    @XmlElement(required = true)
    protected IdCodeName cancelledBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date cancelledAt;
    protected Integer closureId;
    protected List<SalesPerformaInvoiceItem> items;
    protected Vehicle vehicle;
    protected Date dispatchDate;
    protected Date purchaseOrderDate;
    protected String docketNumber;
    protected IdCodeName from;
    protected IdCodeName to;
    protected String generatedId;
    protected boolean assetOrder;
    protected  String deliveredDocumentUrl;
    protected String purchasedOrderNumber;
    @XmlElement(required = true)
    protected String billingType;
    protected String billingAddress;
    private String businessType;
    private Integer docPOId;
    private String irnNo;
    private Integer cancelDocId;
    private String uploadDocId;
    private String uploadedAckNo;
    private String uploadedEwayNo;
    private String signedQrCode;
    private Integer generatedBarcodeId;
    private String sendingCompanyFssai;
    private Integer billingLocationId;
    private BigDecimal totalCorrectedAmount;
    private BigDecimal totalCorrectedSellingCost;
    private BigDecimal totalCorrectedTax;
    private Integer creditNoteDocId;
    private String creditNoteDocUrl;
    private String generatedCreditNoteId;
    private Integer debitNoteDocId;
    private String debitNoteDocUrl;
    private String generatedDebitNoteId;
    private CorrectedSalesInvoiceDetails correctionCreditNoteDetail;
    private CorrectedSalesInvoiceDetails correctionDebitNoteDetail;
    private String referenceInvoiceNumber;

    private VendorDispatchLocation vendorDispatchLocation;

    protected List<String> errors;
    private GstInvoiceType gstInvoiceType;

    public VendorDispatchLocation getVendorDispatchLocation() {
        return vendorDispatchLocation;
    }

    public void setVendorDispatchLocation(VendorDispatchLocation vendorDispatchLocation) {
        this.vendorDispatchLocation = vendorDispatchLocation;
    }

    public String getSendingCompanyFssai() {
        return sendingCompanyFssai;
    }

    public void setSendingCompanyFssai(String sendingCompanyFssai) {
        this.sendingCompanyFssai = sendingCompanyFssai;
    }

    /**
     * Gets the value of the id property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the vendor property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getVendor() {
        return vendor;
    }

    /**
     * Sets the value of the vendor property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setVendor(IdCodeName value) {
        this.vendor = value;
    }

    /**
     * Gets the value of the dispatchLocation property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getDispatchLocation() {
        return dispatchLocation;
    }

    /**
     * Sets the value of the dispatchLocation property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setDispatchLocation(IdCodeName value) {
        this.dispatchLocation = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return possible object is
     * {@link SalesPerformaType }
     */
    public SalesPerformaType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value allowed object is
     *              {@link SalesPerformaType }
     */
    public void setType(SalesPerformaType value) {
        this.type = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return possible object is
     * {@link SalesPerformaStatus }
     */
    public SalesPerformaStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value allowed object is
     *              {@link SalesPerformaStatus }
     */
    public void setStatus(SalesPerformaStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the invoice property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getInvoice() {
        return invoice;
    }

    /**
     * Sets the value of the invoice property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setInvoice(String value) {
        this.invoice = value;
    }

    /**
     * Gets the value of the eWayBill property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getEwayBill() {
        return ewayBill;
    }

    /**
     * Sets the value of the eWayBill property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setEwayBill(String value) {
        this.ewayBill = value;
    }

    public Vehicle getVehicle() {
        return vehicle;
    }

    public void setVehicle(Vehicle vehicle) {
        this.vehicle = vehicle;
    }

    /**
     * Gets the value of the totalCost property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getTotalCost() {
        return totalCost;
    }

    /**
     * Sets the value of the totalCost property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setTotalCost(BigDecimal value) {
        this.totalCost = value;
    }

    /**
     * Gets the value of the totalSellingCost property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getTotalSellingCost() {
        return totalSellingCost;
    }

    /**
     * Sets the value of the totalSellingCost property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setTotalSellingCost(BigDecimal value) {
        this.totalSellingCost = value;
    }

    /**
     * Gets the value of the totalTax property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    /**
     * Sets the value of the totalTax property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setTotalTax(BigDecimal value) {
        this.totalTax = value;
    }

    /**
     * Gets the value of the additionalCharges property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getAdditionalCharges() {
        return additionalCharges;
    }

    /**
     * Sets the value of the additionalCharges property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setAdditionalCharges(BigDecimal value) {
        this.additionalCharges = value;
    }

    /**
     * Gets the value of the totalAmount property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setTotalAmount(BigDecimal value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the sendingUnit property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getSendingUnit() {
        return sendingUnit;
    }

    /**
     * Sets the value of the sendingUnit property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setSendingUnit(IdCodeName value) {
        this.sendingUnit = value;
    }

    /**
     * Gets the value of the needsApproval property.
     */
    public boolean isNeedsApproval() {
        return needsApproval;
    }

    /**
     * Sets the value of the needsApproval property.
     */
    public void setNeedsApproval(boolean value) {
        this.needsApproval = value;
    }

    /**
     * Gets the value of the comment property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the createdBy property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the createdAt property.
     *
     * @return possible object is
     * {@link String }
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * Sets the value of the createdAt property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCreatedAt(Date value) {
        this.createdAt = value;
    }

    /**
     * Gets the value of the cancelledBy property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getCancelledBy() {
        return cancelledBy;
    }

    /**
     * Sets the value of the cancelledBy property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setCancelledBy(IdCodeName value) {
        this.cancelledBy = value;
    }

    /**
     * Gets the value of the cancelledAt property.
     *
     * @return possible object is
     * {@link String }
     */
    public Date getCancelledAt() {
        return cancelledAt;
    }

    /**
     * Sets the value of the cancelledAt property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCancelledAt(Date value) {
        this.cancelledAt = value;
    }

    /**
     * Gets the value of the closureId property.
     */
    public Integer getClosureId() {
        return closureId;
    }

    /**
     * Sets the value of the closureId property.
     */
    public void setClosureId(Integer value) {
        this.closureId = value;
    }

    /**
     * Gets the value of the items property.
     * <p>
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the items property.
     * <p>
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getItems().add(newItem);
     * </pre>
     * <p>
     * <p>
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SalesPerformaInvoiceItem }
     */
    public List<SalesPerformaInvoiceItem> getItems() {
        if (items == null) {
            items = new ArrayList<SalesPerformaInvoiceItem>();
        }
        return this.items;
    }

    @Override
    @JsonIgnore
    public int getKeyId() {
        return this.id;
    }

    @Override
    @JsonIgnore
    public StockEventType getKeyType() {
        return StockEventType.TRANSFER_OUT;
    }

    @Override
    @JsonIgnore
    public int getUnitId() {
        return this.sendingUnit.getId();
    }

    @Override
    @JsonIgnore
    public PriceUpdateEntryType getInventoryType() {
        return PriceUpdateEntryType.SKU;
    }

    @Override
    @JsonIgnore
    public List<InventoryItemVO> getInventoryItems() {
        return new ArrayList<>(this.items);
    }

    public Date getDispatchDate() {
        return dispatchDate;
    }

    public void setDispatchDate(Date dispatchDate) {
        this.dispatchDate = dispatchDate;
    }

    public String getDocketNumber() {
        return docketNumber;
    }

    public void setDocketNumber(String docketNumber) {
        this.docketNumber = docketNumber;
    }


    public String getDispatchAddress() {
        return dispatchAddress;
    }

    public void setDispatchAddress(String dispatchAddress) {
        this.dispatchAddress = dispatchAddress;
    }


    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public IdCodeName getSendingCompany() {
        return sendingCompany;
    }

    public void setSendingCompany(IdCodeName sendingCompany) {
        this.sendingCompany = sendingCompany;
    }

    public InvoiceDocType getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(InvoiceDocType invoiceType) {
        this.invoiceType = invoiceType;
    }

    public IdCodeName getFrom() {
        return from;
    }

    public void setFrom(IdCodeName from) {
        this.from = from;
    }

    public IdCodeName getTo() {
        return to;
    }

    public void setTo(IdCodeName to) {
        this.to = to;
    }

    public String getGeneratedId() {
        return generatedId;
    }

    public void setGeneratedId(String generatedId) {
        this.generatedId = generatedId;
    }

    public boolean isAssetOrder() {
        return assetOrder;
    }

    public void setAssetOrder(boolean assetOrder) {
        this.assetOrder = assetOrder;
    }

    public String getDeliveredDocumentUrl() {
        return deliveredDocumentUrl;
    }

    public void setDeliveredDocumentUrl(String deliveredDocumentUrl) {
        this.deliveredDocumentUrl = deliveredDocumentUrl;
    }

    public String getPurchasedOrderNumber() {
        return purchasedOrderNumber;
    }

    public void setPurchasedOrderNumber(String purchasedOrderNumber) {
        this.purchasedOrderNumber = purchasedOrderNumber;
    }

    public String getVendorAddress() {
        return vendorAddress;
    }

    public void setVendorAddress(String vendorAddress) {
        this.vendorAddress = vendorAddress;
    }

    public Date getPurchaseOrderDate() {
        return purchaseOrderDate;
    }

    public void setPurchaseOrderDate(Date purchaseOrderDate) {
        this.purchaseOrderDate = purchaseOrderDate;
    }

    public String getBillingType() {
        return billingType;
    }

    public void setBillingType(String billingType) {
        this.billingType = billingType;
    }

    public String getBillingAddress() {
        return billingAddress;
    }

    public void setBillingAddress(String bilingAddress) {
        this.billingAddress = bilingAddress;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getDocPOId() {
        return docPOId;
    }

    public void setDocPOId(Integer docPOId) {
        this.docPOId = docPOId;
    }

    public String getIrnNo() {

        return irnNo;
    }

    public void setIrnNo(String irnNo) {
        this.irnNo = irnNo;
    }

    public Integer getCancelDocId() {
        return cancelDocId;
    }

    public void setCancelDocId(Integer cancelDocId) {
        this.cancelDocId = cancelDocId;
    }

    public String getUploadDocId() {
        return uploadDocId;
    }

    public void setUploadDocId(String uploadDocId) {
        this.uploadDocId = uploadDocId;
    }

    public String getUploadedAckNo() {
        return uploadedAckNo;
    }

    public void setUploadedAckNo(String uploadedAckNo) {
        this.uploadedAckNo = uploadedAckNo;
    }

    public String getUploadedEwayNo() {
        return uploadedEwayNo;
    }

    public void setUploadedEwayNo(String uploadedEwayNo) {
        this.uploadedEwayNo = uploadedEwayNo;
    }

    public String getSignedQrCode() {
        return signedQrCode;
    }

    public void setSignedQrCode(String signedQrCode) {
        this.signedQrCode = signedQrCode;
    }

    public Integer getGeneratedBarcodeId() {
        return generatedBarcodeId;
    }

    public void setGeneratedBarcodeId(Integer generatedBarcodeId) {
        this.generatedBarcodeId = generatedBarcodeId;
    }

    public Integer getBillingLocationId() {
        return billingLocationId;
    }

    public void setBillingLocationId(Integer billingLocationId) {
        this.billingLocationId = billingLocationId;
    }

    public BigDecimal getTotalCorrectedAmount() {
        return totalCorrectedAmount;
    }

    public void setTotalCorrectedAmount(BigDecimal totalCorrectedAmount) {
        this.totalCorrectedAmount = totalCorrectedAmount;
    }

    public BigDecimal getTotalCorrectedSellingCost() {
        return totalCorrectedSellingCost;
    }

    public void setTotalCorrectedSellingCost(BigDecimal totalCorrectedSellingCost) {
        this.totalCorrectedSellingCost = totalCorrectedSellingCost;
    }

    public BigDecimal getTotalCorrectedTax() {
        return totalCorrectedTax;
    }

    public void setTotalCorrectedTax(BigDecimal totalCorrectedTax) {
        this.totalCorrectedTax = totalCorrectedTax;
    }

    public Integer getCreditNoteDocId() {
        return creditNoteDocId;
    }

    public void setCreditNoteDocId(Integer creditNoteDocId) {
        this.creditNoteDocId = creditNoteDocId;
    }

    public String getCreditNoteDocUrl() {
        return creditNoteDocUrl;
    }

    public void setCreditNoteDocUrl(String creditNoteDocUrl) {
        this.creditNoteDocUrl = creditNoteDocUrl;
    }

    public Integer getDebitNoteDocId() {
        return debitNoteDocId;
    }

    public void setDebitNoteDocId(Integer debitNoteDocId) {
        this.debitNoteDocId = debitNoteDocId;
    }

    public String getDebitNoteDocUrl() {
        return debitNoteDocUrl;
    }

    public void setDebitNoteDocUrl(String debitNoteDocUrl) {
        this.debitNoteDocUrl = debitNoteDocUrl;
    }

    public String getGeneratedCreditNoteId() {
        return generatedCreditNoteId;
    }

    public void setGeneratedCreditNoteId(String generatedCreditNoteId) {
        this.generatedCreditNoteId = generatedCreditNoteId;
    }

    public String getGeneratedDebitNoteId() {
        return generatedDebitNoteId;
    }

    public void setGeneratedDebitNoteId(String generatedDebitNoteId) {
        this.generatedDebitNoteId = generatedDebitNoteId;
    }

    public CorrectedSalesInvoiceDetails getCorrectionCreditNoteDetail() {
        return correctionCreditNoteDetail;
    }

    public void setCorrectionCreditNoteDetail(CorrectedSalesInvoiceDetails correctionCreditNoteDetail) {
        this.correctionCreditNoteDetail = correctionCreditNoteDetail;
    }

    public CorrectedSalesInvoiceDetails getCorrectionDebitNoteDetail() {
        return correctionDebitNoteDetail;
    }

    public void setCorrectionDebitNoteDetail(CorrectedSalesInvoiceDetails correctionDebitNoteDetail) {
        this.correctionDebitNoteDetail = correctionDebitNoteDetail;
    }

    public String getReferenceInvoiceNumber() {
        return referenceInvoiceNumber;
    }

    public void setReferenceInvoiceNumber(String referenceInvoiceNumber) {
        this.referenceInvoiceNumber = referenceInvoiceNumber;
    }

    @Override
    public List<String> getErrors() {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        return errors;
    }

    @Override
    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public GstInvoiceType getGstInvoiceType() {
        return gstInvoiceType;
    }
    public void setGstInvoiceType(GstInvoiceType gstInvoiceType) {
        this.gstInvoiceType = gstInvoiceType;
    }

}
