/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.12 at 03:52:20 PM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;
import java.util.List;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AssetDefinitionSlimObject", propOrder = {
        "assetId",
        "assetName",
        "unitId",
        "unitType",
        "assetStatus",
        "skuId",
        "productId",
        "profileId",
        "ownerType",
        "ownerId",
        "tagType",
        "tagValue"
})
public class AssetDefinitionSlimObject {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer assetId;

    @XmlElement(required = false)
    protected String assetName;


    /*
        UnitId signifies Location of Asset
     */
    @XmlElement(required = true)
    protected Integer unitId;

    @XmlElement(required = true)
    protected String unitType;

    protected String unitName;

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected AssetStatusType assetStatus;

    @XmlElement(required = true)
    protected Integer skuId;

    @XmlElement(required = true)
    protected Integer productId;

    @XmlElement(required = true)
    protected Integer profileId;

    @XmlElement(required = true)
    protected String ownerType;
    @XmlElement(required = true)
    protected Integer ownerId;

    protected String ownerName;

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected TagType tagType;
    @XmlElement(required = true)
    protected String tagValue;


    protected List<ProfileAttributeMapping> profileAttributeMappingList;

    protected List<EntityAttributeValueMapping> entityAttributeValueMappings;

    protected Float currentValue;

    protected Float currentValueWithoutTax;

    protected String uniqueFieldName;

    protected String uniqueFieldValue;

    protected String subCategoryName;



    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public String getAssetName() {
        return assetName;
    }

    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }


    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public AssetStatusType getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(AssetStatusType assetStatus) {
        this.assetStatus = assetStatus;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }


    public String getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public TagType getTagType() {
        return tagType;
    }

    public void setTagType(TagType tagType) {
        this.tagType = tagType;
    }

    public String getTagValue() {
        return tagValue;
    }

    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    public List<ProfileAttributeMapping> getProfileAttributeMappingList() {
        return profileAttributeMappingList;
    }

    public void setProfileAttributeMappingList(List<ProfileAttributeMapping> profileAttributeMappingList) {
        this.profileAttributeMappingList = profileAttributeMappingList;
    }

    public List<EntityAttributeValueMapping> getEntityAttributeValueMappings() {
        return entityAttributeValueMappings;
    }

    public void setEntityAttributeValueMappings(List<EntityAttributeValueMapping> entityAttributeValueMappings) {
        this.entityAttributeValueMappings = entityAttributeValueMappings;
    }

    public Float getCurrentValue() {
        return currentValue;
    }

    public void setCurrentValue(Float currentValue) {
        this.currentValue = currentValue;
    }

    public Float getCurrentValueWithoutTax() {
        return currentValueWithoutTax;
    }

    public void setCurrentValueWithoutTax(Float currentValueWithoutTax) {
        this.currentValueWithoutTax = currentValueWithoutTax;
    }

    public String getUniqueFieldName() {
        return uniqueFieldName;
    }

    public void setUniqueFieldName(String uniqueFieldName) {
        this.uniqueFieldName = uniqueFieldName;
    }

    public String getUniqueFieldValue() {
        return uniqueFieldValue;
    }

    public void setUniqueFieldValue(String uniqueFieldValue) {
        this.uniqueFieldValue = uniqueFieldValue;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getSubCategoryName() {
        return subCategoryName;
    }

    public void setSubCategoryName(String subCategoryName) {
        this.subCategoryName = subCategoryName;
    }
}
