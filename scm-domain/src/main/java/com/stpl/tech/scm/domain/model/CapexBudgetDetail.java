package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlElement;

public class CapexBudgetDetail {
	
	@XmlElement(required = true)
	protected Integer id;
	@XmlElement(required = true)
	protected Integer unitId;
	@XmlElement(required = true)
	protected String budgetType;
	@XmlElement(required = true)
	protected Integer departmentId;
	@XmlElement(required = true)
	protected String departmentName;
	@XmlElement(required = true)
	protected BigDecimal originalAmount;
	@XmlElement(required = true)
	protected BigDecimal budgetAmount;
	@XmlElement(required = true)
	protected BigDecimal runningAmount;
	@XmlElement(required = true)
	protected BigDecimal receivingAmount;
	@XmlElement(required = true)
	protected BigDecimal remainingAmount;
	@XmlElement(required = true)
	protected BigDecimal paidAmount;
	@XmlElement(required = true)
	protected BigDecimal initialAmount;
	@XmlElement(required = true)
	protected String status;
	protected Integer totalSO = 0;
	protected Integer soPendingApproval = 0;
	protected Integer totalPO = 0;
	protected Integer poPendingApproval = 0;
	protected Integer totalTO = 0;
	protected Integer toPendingApproval = 0;
	protected Integer totalRO = 0;
	protected Integer roPendingApproval = 0;

	protected  Integer vendorCount = 0;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getUnitId() {
		return unitId;
	}
	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}
	public String getBudgetType() {
		return budgetType;
	}
	public void setBudgetType(String budgetType) {
		this.budgetType = budgetType;
	}
	public Integer getDepartmentId() {
		return departmentId;
	}
	public void setDepartmentId(Integer departmentId) {
		this.departmentId = departmentId;
	}
	public String getDepartmentName() {
		return departmentName;
	}
	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}
	public BigDecimal getOriginalAmount() {
		return originalAmount;
	}
	public void setOriginalAmount(BigDecimal originalAmount) {
		this.originalAmount = originalAmount;
	}
	public BigDecimal getBudgetAmount() {
		return budgetAmount;
	}
	public void setBudgetAmount(BigDecimal budgetAmount) {
		this.budgetAmount = budgetAmount;
	}
	public BigDecimal getRunningAmount() {
		return runningAmount;
	}
	public void setRunningAmount(BigDecimal runningAmount) {
		this.runningAmount = runningAmount;
	}
	public BigDecimal getReceivingAmount() {
		return receivingAmount;
	}
	public void setReceivingAmount(BigDecimal receivingAmount) {
		this.receivingAmount = receivingAmount;
	}
	public BigDecimal getRemainingAmount() {
		return remainingAmount;
	}
	public void setRemainingAmount(BigDecimal remainingAmount) {
		this.remainingAmount = remainingAmount;
	}
	public BigDecimal getPaidAmount() {
		return paidAmount;
	}
	public void setPaidAmount(BigDecimal paidAmount) {
		this.paidAmount = paidAmount;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public BigDecimal getInitialAmount() {
		return initialAmount;
	}
	public void setInitialAmount(BigDecimal initialAmount) {
		this.initialAmount = initialAmount;
	}

	public Integer getTotalSO() {
		return totalSO;
	}

	public void setTotalSO(Integer totalSO) {
		this.totalSO = totalSO;
	}

	public Integer getSoPendingApproval() {
		return soPendingApproval;
	}

	public void setSoPendingApproval(Integer soPendingApproval) {
		this.soPendingApproval = soPendingApproval;
	}

	public Integer getTotalPO() {
		return totalPO;
	}

	public void setTotalPO(Integer totalPO) {
		this.totalPO = totalPO;
	}

	public Integer getPoPendingApproval() {
		return poPendingApproval;
	}

	public void setPoPendingApproval(Integer poPendingApproval) {
		this.poPendingApproval = poPendingApproval;
	}

	public Integer getVendorCount() {
		return vendorCount;
	}

	public void setVendorCount(Integer vendorCount) {
		this.vendorCount = vendorCount;
	}
	public Integer getTotalTO() {
		return totalTO;
	}

	public void setTotalTO(Integer totalTO) {
		this.totalTO = totalTO;
	}

	public Integer getToPendingApproval() {
		return toPendingApproval;
	}

	public void setToPendingApproval(Integer toPendingApproval) {
		this.toPendingApproval = toPendingApproval;
	}

	public Integer getTotalRO() {
		return totalRO;
	}

	public void setTotalRO(Integer totalRO) {
		this.totalRO = totalRO;
	}

	public Integer getRoPendingApproval() {
		return roPendingApproval;
	}

	public void setRoPendingApproval(Integer roPendingApproval) {
		this.roPendingApproval = roPendingApproval;
	}
}
