package com.stpl.tech.scm.domain.model;

import java.util.List;

public enum VendorContractStatus {
    ACTIVE,
    IN_ACTIVE,
    COMPLETED,
    CANCELLED,
    INITIATED,
    PARTIAL_REJECTED,
    CREATED,
    REJECTED,
    REMOVED,
    REMOVED_ALL,
    APPROVED,
    PENDING_VENDOR_APPROVAL,
    VENDOR_APPROVED,
    VENDOR_REJECTED,
    PENDING_APPROVER_APPROVAL,
    APPROVER_BY_PASSED,
    APPROVER_APPROVED,
    APPROVER_REJECTED,
    APPLIED,
    EXPIRED,
    DEACTIVATED,
    RE_VERIFY,
    FAILED;

    private static final List<String> REJECTION_STATUSES;

    static {
        REJECTION_STATUSES = List.of(
                VENDOR_REJECTED.name(),
                APPROVER_REJECTED.name(),
                REJECTED.name(),
                CANCELLED.name(),
                EXPIRED.name(),
                DEACTIVATED.name(),
                FAILED.name(),
                REMOVED_ALL.name(),
                REMOVED.name(),
                IN_ACTIVE.name()
        );
    }

    public static List<String> getRejectionStatuses() {
        return REJECTION_STATUSES;
    }

}
