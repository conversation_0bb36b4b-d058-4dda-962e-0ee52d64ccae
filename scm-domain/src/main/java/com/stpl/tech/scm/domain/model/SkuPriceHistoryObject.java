package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class SkuPriceHistoryObject {
    private int skuPriceDataId;
    private Integer packagingId;
    private BigDecimal negotiatedPrice;
    private String changeType;
    private String createdBy;
    private String createdAt;

    public SkuPriceHistoryObject(int skuPriceDataId, Integer packagingId, BigDecimal negotiatedPrice, String changeType, String createdBy, String createdAt) {
        this.skuPriceDataId = skuPriceDataId;
        this.packagingId  = packagingId;
        this.negotiatedPrice = negotiatedPrice;
        this.changeType = changeType;
        this.createdBy = createdBy;
        this.createdAt = createdAt;
    }

    public int getSkuPriceDataId() {
        return skuPriceDataId;
    }

    public void setSkuPriceDataId(int skuPriceDataId) {
        this.skuPriceDataId = skuPriceDataId;
    }

    public BigDecimal getNegotiatedPrice() {
        return negotiatedPrice;
    }

    public void setNegotiatedPrice(BigDecimal negotiatedPrice) {
        this.negotiatedPrice = negotiatedPrice;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }


    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }
}
