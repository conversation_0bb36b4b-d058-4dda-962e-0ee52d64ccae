//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.08.05 at 11:48:36 AM IST
//


package com.stpl.tech.scm.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for ExternalTransferDetail complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="ExternalTransferDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="vendorId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dispatchId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendorName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="locationName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}SCMOrderStatus"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="updatedAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExternalTransferDetail", propOrder = {
        "vendorId",
        "dispatchId",
        "vendorName",
        "locationName",
        "status",
        "updatedBy",
        "updatedAt"
})
public class ExternalTransferDetail {

    protected Integer id;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer vendorId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer dispatchId;
    @XmlElement(required = true)
    protected String vendorName;
    @XmlElement(required = true)
    protected String locationName;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SCMOrderStatus status;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updatedAt;
    protected AddressDetail addressDetail;
    protected String gstin;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Gets the value of the vendorId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getVendorId() {
        return vendorId;
    }

    /**
     * Sets the value of the vendorId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setVendorId(Integer value) {
        this.vendorId = value;
    }

    /**
     * Gets the value of the dispatchId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getDispatchId() {
        return dispatchId;
    }

    /**
     * Sets the value of the dispatchId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setDispatchId(Integer value) {
        this.dispatchId = value;
    }

    /**
     * Gets the value of the vendorName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getVendorName() {
        return vendorName;
    }

    /**
     * Sets the value of the vendorName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setVendorName(String value) {
        this.vendorName = value;
    }

    /**
     * Gets the value of the locationName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     * Sets the value of the locationName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLocationName(String value) {
        this.locationName = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link SCMOrderStatus }
     *
     */
    public SCMOrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link SCMOrderStatus }
     *
     */
    public void setStatus(SCMOrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the updatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }

    /**
     * Gets the value of the updatedAt property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * Sets the value of the updatedAt property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUpdatedAt(Date value) {
        this.updatedAt = value;
    }

    public AddressDetail getAddressDetail() {
        return addressDetail;
    }

    public void setAddressDetail(AddressDetail addressDetail) {
        this.addressDetail = addressDetail;
    }

    public String getGstin() {
        return gstin;
    }

    public void setGstin(String gstin) {
        this.gstin = gstin;
    }
}
