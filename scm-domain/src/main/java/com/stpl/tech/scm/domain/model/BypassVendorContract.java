package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BypassVendorContract {
    private Integer bypassContractId;

    private Integer vendorId;

    private String status;

    private Date createdAt;

    private Integer createdBy;

    private String createdByName;

    private Integer documentId;

    private Date startDate;

    private Date endDate;

    private String approvalRequestFrom;

    private String vendorUserName;

    private String vendorUserDesignation;

    private String ipAddress;

    private String authIpAddress;

    private Integer unsignedDocumentId;

    private Integer signedDocumentId;

    private Integer authSignedDocumentId;

    private Integer digitalSignID;

    private Integer authDigitalSignID;

    private String isOtpVerified;

    private String token;

    private String isByPassed;

    private String isMailTriggered;

    private Date mailTime;

    private String employeeName;

    private String employeeId;

    private Integer templateId;

    private String vendorApprovalLink;

    private String approverApproveLink;

    private List<SkuPriceDetail> skuPriceDetailList;
    private List<BypassVendorContractItemDataVO> bypassVendorContractItemDataVOList;
}
