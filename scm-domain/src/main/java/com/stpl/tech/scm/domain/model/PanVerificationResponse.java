package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PanVerificationResponse {
    @SerializedName("code")
    Integer code;
    @SerializedName("timestamp")
    long timestamp;
    @SerializedName("transaction_id")
    String transaction_id;
    @SerializedName("message")
    String message;
    @SerializedName("data")
    PanVerificationData panVerificationData;

}
