//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.20 at 05:17:20 PM IST
//

package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;
import org.apache.commons.lang.NotImplementedException;

/**
 * <p>
 * Java class for TransferOrderItem complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="TransferOrderItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="packagingDetails" type="{http://www.w3schools.com}SCMOrderPackaging" maxOccurs="unbounded"/&gt;
 *         &lt;element name="requestedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestedAbsoluteQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="transferredQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="receivedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="negotiatedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestOrderItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="purchaseOrderItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="goodReceivedItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransferOrderItem", propOrder = { "id", "productId", "skuId", "skuName", "packagingDetails", "requestedQuantity",
		"requestedAbsoluteQuantity", "transferredQuantity", "receivedQuantity", "unitOfMeasure", "unitPrice",
		"negotiatedUnitPrice", "requestOrderItemId", "purchaseOrderItemId", "goodReceivedItemId", "associatedAssetId", "associatedAssetTagValue", "excessQuantity" })
public class TransferOrderItem implements InventoryItemVO{

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	protected int productId;
	protected int skuId;
	@XmlElement(required = true)
	protected String skuName;
	@XmlElement(required = true)
	protected List<SCMOrderPackaging> packagingDetails;
	protected float requestedQuantity;
	protected float requestedAbsoluteQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float transferredQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float receivedQuantity;
	@XmlElement(required = true)
	protected String unitOfMeasure;
	@XmlElement(required = true, type = Double.class, nillable = true)
	protected Double unitPrice;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal price;
	@XmlElement(required = true, type = Double.class, nillable = true)
	protected Double negotiatedUnitPrice;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer requestOrderItemId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer purchaseOrderItemId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer goodReceivedItemId;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal total;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal tax;
	protected String code;
	protected List<TaxDetail> taxes;
	protected List<InventoryItemDrilldown> drillDowns;
	@XmlElement(required = true, nillable = true)
	protected PriceUpdateEntryType keyType;
	@XmlElement(required = true, nillable = true)
	protected String skuCode;
	@XmlElement(required = false, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date expiryDate;
	protected ConsumptionOrder consumptionOrder = ConsumptionOrder.FIFO;
	protected Integer associatedAssetId;
	protected String associatedAssetTagValue;
	protected BigDecimal excessQuantity;
	protected Date roItemExpiryDate;
	protected Integer productionId;
	protected Float itemTax;

	/**
	 * Gets the value of the id property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setId(Integer value) {
		this.id = value;
	}

	/**
	 * Gets the value of the skuId property.
	 *
	 */
	public int getSkuId() {
		return skuId;
	}

	/**
	 * Sets the value of the skuId property.
	 *
	 */
	public void setSkuId(int value) {
		this.skuId = value;
	}

	/**
	 * Gets the value of the skuName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getSkuName() {
		return skuName;
	}

	/**
	 * Sets the value of the skuName property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setSkuName(String value) {
		this.skuName = value;
	}

	/**
	 * Gets the value of the packagingDetails property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the packagingDetails property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getPackagingDetails().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link SCMOrderPackaging }
	 *
	 *
	 */
	public List<SCMOrderPackaging> getPackagingDetails() {
		if (packagingDetails == null) {
			packagingDetails = new ArrayList<SCMOrderPackaging>();
		}
		return this.packagingDetails;
	}

	public void setPackagingDetails(List<SCMOrderPackaging> packagingDetails) {
		this.packagingDetails = packagingDetails;
	}

	/**
	 * Gets the value of the requestedQuantity property.
	 *
	 */
	public float getRequestedQuantity() {
		return requestedQuantity;
	}

	/**
	 * Sets the value of the requestedQuantity property.
	 *
	 */
	public void setRequestedQuantity(float value) {
		this.requestedQuantity = value;
	}

	/**
	 * Gets the value of the requestedAbsoluteQuantity property.
	 *
	 */
	public float getRequestedAbsoluteQuantity() {
		return requestedAbsoluteQuantity;
	}

	/**
	 * Sets the value of the requestedAbsoluteQuantity property.
	 *
	 */
	public void setRequestedAbsoluteQuantity(float value) {
		this.requestedAbsoluteQuantity = value;
	}

	/**
	 * Gets the value of the transferredQuantity property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getTransferredQuantity() {
		return transferredQuantity;
	}

	/**
	 * Sets the value of the transferredQuantity property.
	 *
	 * @param value
	 *            allowed object is {@link Float }
	 *
	 */
	public void setTransferredQuantity(Float value) {
		this.transferredQuantity = value;
	}

	/**
	 * Gets the value of the receivedQuantity property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getReceivedQuantity() {
		return receivedQuantity;
	}

	/**
	 * Sets the value of the receivedQuantity property.
	 *
	 * @param value
	 *            allowed object is {@link Float }
	 *
	 */
	public void setReceivedQuantity(Float value) {
		this.receivedQuantity = value;
	}

	/**
	 * Gets the value of the unitOfMeasure property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	/**
	 * Sets the value of the unitOfMeasure property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setUnitOfMeasure(String value) {
		this.unitOfMeasure = value;
	}

	/**
	 * Gets the value of the unitPrice property.
	 *
	 * @return possible object is {@link Double }
	 *
	 */
	public Double getUnitPrice() {
		return unitPrice;
	}

	/**
	 * Sets the value of the unitPrice property.
	 *
	 * @param unitPrice
	 *            allowed object is {@link Double }
	 *
	 */
	public void setUnitPrice(Double unitPrice) {
		this.unitPrice = unitPrice;
	}

	/**
	 * Gets the value of the negotiatedUnitPrice property.
	 *
	 * @return possible object is {@link Double }
	 *
	 */
	public Double getNegotiatedUnitPrice() {
		return negotiatedUnitPrice;
	}

	/**
	 * Sets the value of the negotiatedUnitPrice property.
	 *
	 * @param negotiatedUnitPrice
	 *            allowed object is {@link Double }
	 *
	 */
	public void setNegotiatedUnitPrice(Double negotiatedUnitPrice) {
		this.negotiatedUnitPrice = negotiatedUnitPrice;
	}

	/**
	 * Gets the value of the requestOrderItemId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getRequestOrderItemId() {
		return requestOrderItemId;
	}

	/**
	 * Sets the value of the requestOrderItemId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setRequestOrderItemId(Integer value) {
		this.requestOrderItemId = value;
	}

	/**
	 * Gets the value of the purchaseOrderItemId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getPurchaseOrderItemId() {
		return purchaseOrderItemId;
	}

	/**
	 * Sets the value of the purchaseOrderItemId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setPurchaseOrderItemId(Integer value) {
		this.purchaseOrderItemId = value;
	}

	/**
	 * Gets the value of the goodReceivedItemId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getGoodReceivedItemId() {
		return goodReceivedItemId;
	}

	/**
	 * Sets the value of the goodReceivedItemId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setGoodReceivedItemId(Integer value) {
		this.goodReceivedItemId = value;
	}

	/**
	 * @return the tax
	 */
	public BigDecimal getTax() {
		return tax;
	}

	/**
	 * @param tax
	 *            the tax to set
	 */
	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @param code
	 *            the code to set
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * @return the taxes
	 */
	public List<TaxDetail> getTaxes() {
		if (taxes == null) {
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	/**
	 * @return the total
	 */
	public BigDecimal getTotal() {
		return total;
	}

	/**
	 * @param total the total to set
	 */
	public void setTotal(BigDecimal total) {
		this.total = total;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyId()
	 */
	@Override
	public int getKeyId() {

		return PriceUpdateEntryType.SKU.equals(this.keyType) ? this.skuId : this.productId;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyType()
	 */
	@Override
	public PriceUpdateEntryType getKeyType() {
		return keyType;
	}

	public void setKeyType(PriceUpdateEntryType keyType) {
		this.keyType = keyType;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyId()
	 */
	@Override
	public int getItemKeyId() {
		return this.id;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyType()
	 */
	@Override
	@JsonIgnore
	public StockEventType getItemKeyType() {
		return StockEventType.TRANSFER_OUT;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getDrillDowns()
	 */
	@Override
	public List<InventoryItemDrilldown> getDrillDowns() {
		if (this.drillDowns == null) {
			this.drillDowns = new ArrayList<>();
		}
		return this.drillDowns;
	}

	@Override
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getQuantity()
	 */
	@Override
	public BigDecimal getQuantity() {
		return new BigDecimal(this.transferredQuantity);
	}


	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#setQuantity(java.math.
	 * BigDecimal)
	 */
	@Override
	public void setQuantity(BigDecimal qty) {
		throw new NotImplementedException("Not Implemeneted for TR item");
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getUom()
	 */
	@Override
	public String getUom() {
		return this.unitOfMeasure;
	}

	/**
	 * Gets the value of the skuCode property.
	 *
	 * @return
	 *     possible object is
	 *     {@link String }
	 *
	 */
	public String getSkuCode() {
		return skuCode;
	}

	/**
	 * Sets the value of the skuCode property.
	 *
	 * @param value
	 *     allowed object is
	 *     {@link String }
	 *
	 */
	public void setSkuCode(String value) {
		this.skuCode = value;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public ConsumptionOrder getConsumptionOrder() {
		return consumptionOrder;
	}

	public void setConsumptionOrder(ConsumptionOrder consumptionOrder) {
		this.consumptionOrder = consumptionOrder;
	}

	public Integer getAssociatedAssetId() {
		return associatedAssetId;
	}

	public void setAssociatedAssetId(Integer associatedAssetId) {
		this.associatedAssetId = associatedAssetId;
	}

	public String getAssociatedAssetTagValue() {
		return associatedAssetTagValue;
	}

	public void setAssociatedAssetTagValue(String associatedAssetTagValue) {
		this.associatedAssetTagValue = associatedAssetTagValue;
	}

	public BigDecimal getExcessQuantity() {
		return excessQuantity;
	}

	public void setExcessQuantity(BigDecimal excessQuantity) {
		this.excessQuantity = excessQuantity;
	}

	public Date getRoItemExpiryDate() {
		return roItemExpiryDate;
	}

	public void setRoItemExpiryDate(Date roItemExpiryDate) {
		this.roItemExpiryDate = roItemExpiryDate;
	}

	public Integer getProductionId() {
		return productionId;
	}

	public void setProductionId(Integer productionId) {
		this.productionId = productionId;
	}

	public Float getItemTax() {
		return itemTax;
	}

	public void setItemTax(Float itemTax) {
		this.itemTax = itemTax;
	}
}
