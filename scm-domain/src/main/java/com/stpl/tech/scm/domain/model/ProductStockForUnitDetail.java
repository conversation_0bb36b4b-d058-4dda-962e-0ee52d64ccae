/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.12 at 05:40:48 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ProductStockForUnit complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ProductStockForUnit"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="eventType" type="{http://www.w3schools.com}StockTakeType"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductStockForUnit", propOrder = {
    "unitId",
    "generatedBy",
    "eventType",
    "data"
})
public class ProductStockForUnitDetail{

    protected int unitId;
    protected int generatedBy;
    @XmlSchemaType(name = "string")
    protected StockTakeType eventType;
    protected List<ProductStockForUnit> data;

    
    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the generatedBy property.
     * 
     */
    public int getGeneratedBy() {
        return generatedBy;
    }

    /**
     * Sets the value of the generatedBy property.
     * 
     */
    public void setGeneratedBy(int value) {
        this.generatedBy = value;
    }

    

    /**
     * Gets the value of the eventType property.
     * 
     * @return
     *     possible object is
     *     {@link StockTakeType }
     *     
     */
    public StockTakeType getEventType() {
        return eventType;
    }

    /**
     * Sets the value of the eventType property.
     * 
     * @param value
     *     allowed object is
     *     {@link StockTakeType }
     *     
     */
    public void setEventType(StockTakeType value) {
        this.eventType = value;
    }

    public List<ProductStockForUnit> getData() {
        if (data == null) {
        	data = new ArrayList<ProductStockForUnit>();
        }
        return this.data;
    }

}
