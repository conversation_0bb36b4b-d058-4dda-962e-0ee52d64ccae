/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 03:52:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "StockEventAssetMappingDefinition", propOrder = {
        "eventAssetMappingId",
        "eventId",
        "unitId",
        "assetId",
        "assetTagValue",
        "assetStatus",
        "found",
        "exists",
        "creationDate",
        "auditedBy",
        "auditDate",
        "auditStatus",
        "nonScannable",
        "manualChecked",
        "subCategory"
})
public class StockEventAssetMappingDefinition {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer eventAssetMappingId;

    @XmlElement(required = true)
    protected Integer eventId;
    /*
        UnitId signifies Location of Asset
     */
    @XmlElement(required = true)
    protected Integer unitId;

    @XmlElement(required = false)
    protected Integer assetId;

    @XmlElement(required = true)
    protected String assetTagValue;

    @XmlElement(required = false)
    protected String assetName;

    @XmlElement(required = false)
    protected String skuName;

    @XmlElement(required = false)
    @XmlSchemaType(name = "string")
    protected AssetStatusType assetStatus;



    @XmlElement(required = true)
    protected boolean found;

    @XmlElement(required = true)
    protected boolean exists;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date creationDate;

    @XmlElement(required = false)
    protected IdCodeName auditedBy;

    @XmlElement(required = false, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date auditDate;

    @XmlElement(required = false)
    @XmlSchemaType(name = "string")
    protected String auditStatus;

    protected Boolean checked;

    @XmlElement(required = false)
    protected  Boolean nonScannable;

    @XmlElement(required = false)
    protected Boolean manualChecked;


    @XmlElement(required = false)
    protected  String subCategory;

    @XmlElement(required = false)
    protected Integer productId;

    @XmlElement(required = false)
    protected String productName;

    @XmlElement(required = false)
    protected BigDecimal price;

    @XmlElement(required = false)
    protected BigDecimal tax;

    protected String imageUrl;

    protected String unitName;

    protected Boolean isLost;

    protected Integer skuId;

    public Integer getEventAssetMappingId() {
        return eventAssetMappingId;
    }

    public void setEventAssetMappingId(Integer eventAssetMappingId) {
        this.eventAssetMappingId = eventAssetMappingId;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public AssetStatusType getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(AssetStatusType assetStatus) {
        this.assetStatus = assetStatus;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public IdCodeName getAuditedBy() {
        return auditedBy;
    }

    public void setAuditedBy(IdCodeName auditedBy) {
        this.auditedBy = auditedBy;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAssetTagValue() {
        return assetTagValue;
    }

    public void setAssetTagValue(String assetTagValue) {
        this.assetTagValue = assetTagValue;
    }

    public boolean isFound() {
        return found;
    }

    public void setFound(boolean found) {
        this.found = found;
    }

    public boolean isExists() {
        return exists;
    }

    public void setExists(boolean exists) {
        this.exists = exists;
    }

    public String getAssetName() {
        return assetName;
    }

    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }

    public Boolean getChecked() {
        return checked;
    }

    public void setChecked(Boolean checked) {
        this.checked = checked;
    }

    public Boolean getNonScannable() {
        return nonScannable;
    }

    public void setNonScannable(Boolean nonScannable) {
        this.nonScannable = nonScannable;
    }

    public Boolean getManualChecked() {
        return manualChecked;
    }

    public void setManualChecked(Boolean manualChecked) {
        this.manualChecked = manualChecked;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public Boolean getLost() {
        return isLost;
    }

    public void setLost(Boolean lost) {
        isLost = lost;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }
}
