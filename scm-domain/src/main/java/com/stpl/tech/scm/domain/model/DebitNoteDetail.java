//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.09.18 at 04:47:49 PM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for DebitNoteDetail complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="DebitNoteDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="debitNoteId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="paymentRequestId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="invoiceNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="busyReferenceNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalTaxes" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="creditNoteReceivingTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="lastUpdatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="updateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="creditNoteReceived" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DebitNoteDetail", propOrder = {
    "debitNoteId",
    "paymentRequestId",
    "invoiceNumber",
    "busyReferenceNumber",
    "amount",
    "totalTaxes",
    "totalAmount",
    "creditNoteReceivingTime",
    "generationTime",
    "generatedBy",
    "lastUpdatedBy",
    "updateTime",
    "creditNoteReceived"
})
public class DebitNoteDetail {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer debitNoteId;
    protected int paymentRequestId;
    @XmlElement(required = true)
    protected String invoiceNumber;
    @XmlElement(required = true)
    protected String busyReferenceNumber;
    @XmlElement(required = true)
    protected BigDecimal amount;
    @XmlElement(required = true)
    protected BigDecimal totalTaxes;
    @XmlElement(required = true)
    protected BigDecimal totalAmount;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date creditNoteReceivingTime;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true)
    protected IdCodeName lastUpdatedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlElement(defaultValue = "false")
    protected boolean creditNoteReceived;
    protected Integer debitNoteDocId;
    protected String debitNoteStatus;
    protected BigDecimal advanceAmount;
    protected Integer advancePaymentId;
    protected BigDecimal paidAmount;
    protected List<CategorySubCategoryDebitNote> categorySubCategoryDebitNotes;

    /**
     * Gets the value of the debitNoteId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getDebitNoteId() {
        return debitNoteId;
    }

    /**
     * Sets the value of the debitNoteId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setDebitNoteId(Integer value) {
        this.debitNoteId = value;
    }

    /**
     * Gets the value of the paymentRequestId property.
     *
     */
    public int getPaymentRequestId() {
        return paymentRequestId;
    }

    /**
     * Sets the value of the paymentRequestId property.
     *
     */
    public void setPaymentRequestId(int value) {
        this.paymentRequestId = value;
    }

    /**
     * Gets the value of the invoiceNumber property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    /**
     * Sets the value of the invoiceNumber property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setInvoiceNumber(String value) {
        this.invoiceNumber = value;
    }

    /**
     * Gets the value of the busyReferenceNumber property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getBusyReferenceNumber() {
        return busyReferenceNumber;
    }

    /**
     * Sets the value of the busyReferenceNumber property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setBusyReferenceNumber(String value) {
        this.busyReferenceNumber = value;
    }

    /**
     * Gets the value of the amount property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * Sets the value of the amount property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setAmount(BigDecimal value) {
        this.amount = value;
    }

    /**
     * Gets the value of the totalTaxes property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    /**
     * Sets the value of the totalTaxes property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setTotalTaxes(BigDecimal value) {
        this.totalTaxes = value;
    }

    /**
     * Gets the value of the totalAmount property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setTotalAmount(BigDecimal value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the creditNoteReceivingTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getCreditNoteReceivingTime() {
        return creditNoteReceivingTime;
    }

    /**
     * Sets the value of the creditNoteReceivingTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCreditNoteReceivingTime(Date value) {
        this.creditNoteReceivingTime = value;
    }

    /**
     * Gets the value of the generationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getGenerationTime() {
        return generationTime;
    }

    /**
     * Sets the value of the generationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setGenerationTime(Date value) {
        this.generationTime = value;
    }

    /**
     * Gets the value of the generatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    /**
     * Sets the value of the generatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedBy(IdCodeName value) {
        this.generatedBy = value;
    }

    /**
     * Gets the value of the lastUpdatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    /**
     * Sets the value of the lastUpdatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setLastUpdatedBy(IdCodeName value) {
        this.lastUpdatedBy = value;
    }

    /**
     * Gets the value of the updateTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets the value of the updateTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUpdateTime(Date value) {
        this.updateTime = value;
    }

    /**
     * Gets the value of the creditNoteReceived property.
     *
     */
    public boolean isCreditNoteReceived() {
        return creditNoteReceived;
    }

    /**
     * Sets the value of the creditNoteReceived property.
     *
     */
    public void setCreditNoteReceived(boolean value) {
        this.creditNoteReceived = value;
    }

    public Integer getDebitNoteDocId() {
        return debitNoteDocId;
    }

    public void setDebitNoteDocId(Integer debitNoteDocId) {
        this.debitNoteDocId = debitNoteDocId;
    }

    public String getDebitNoteStatus() {
        return debitNoteStatus;
    }

    public void setDebitNoteStatus(String debitNoteStatus) {
        this.debitNoteStatus = debitNoteStatus;
    }

    public BigDecimal getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(BigDecimal advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public Integer getAdvancePaymentId() {
        return advancePaymentId;
    }

    public void setAdvancePaymentId(Integer advancePaymentId) {
        this.advancePaymentId = advancePaymentId;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public List<CategorySubCategoryDebitNote> getCategorySubCategoryDebitNotes() {
        return categorySubCategoryDebitNotes;
    }

    public void setCategorySubCategoryDebitNotes(List<CategorySubCategoryDebitNote> categorySubCategoryDebitNotes) {
        this.categorySubCategoryDebitNotes = categorySubCategoryDebitNotes;
    }
}
