/**
 * 
 */
package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVOAdaptor;
import com.stpl.tech.scm.domain.vo.InventoryVO;
import com.stpl.tech.util.AppUtils;

/**
 * <AUTHOR>
 *
 */
public abstract class AbstractInventoryVO implements InventoryVO {

	@JsonIgnore
	public Map<Integer, InventoryItemVO> getConsumption() {
		Map<Integer, InventoryItemVO> map = new HashMap<>();
		for (InventoryItemVO item : getInventoryItems()) {
			if (!map.containsKey(item.getKeyId())) {
				map.put(item.getKeyId(), new InventoryItemVOAdaptor(item));
			} else {
				InventoryItemVO mapData = map.get(item.getKeyId());
				mapData.setQuantity(AppUtils.add(mapData.getQuantity(), item.getQuantity()));
			}
		}
		return map;
	}

	@JsonIgnore
	public Map<Integer, BigDecimal> getItemsGroupedByQuantity() {
		return getInventoryItems().stream().collect(Collectors.groupingBy(
				InventoryItemVO::getKeyId,
				Collectors.reducing(
						BigDecimal.ZERO,
						InventoryItemVO::getQuantity,
						BigDecimal::add
				)
		));
	}


	@JsonIgnore
	public Map<InventoryPrice, Pair<InventoryItemVO, InventoryItemDrilldown>> getReceiving() {
		Map<InventoryPrice, Pair<InventoryItemVO, InventoryItemDrilldown>> map = new HashMap<>();
		for (InventoryItemVO item : getInventoryItems()) {
			if (item.getKeyType() == null) {
				item.setKeyType(this.getInventoryType());
			}
			if (item.getQuantity().compareTo(BigDecimal.ZERO) > 0 && item.getDrillDowns() != null
					&& item.getDrillDowns().size() > 0) {
				for (InventoryItemDrilldown dd : item.getDrillDowns()) {
					InventoryPrice key = new InventoryPrice(this.getInventoryType(), item.getKeyId(), dd.getPrice(),
							dd.getExpiryDate(), AppUtils.dateToString(dd.getExpiryDate()));
					if (!map.containsKey(key)) {
						map.put(key, new Pair<>(item, dd));
					} else {
						Pair<InventoryItemVO, InventoryItemDrilldown> mapData = map.get(key);
						mapData.getValue()
								.setQuantity(AppUtils.add(mapData.getValue().getQuantity(), dd.getQuantity().subtract(dd.getRejection())));
					}
				}
			}
		}
		return map;
	}

}
