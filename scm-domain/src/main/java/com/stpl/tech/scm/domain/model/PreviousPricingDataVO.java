package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PreviousPricingDataVO {
    private double totalCost;
    private double totalQuantityOrdered;
    private double unitPrice;
    private String skuPackagingName;
    private double currentPriceForTheRequiredPackaging;
    private String vendorName;
    private Date generatedAt;

}
