package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.List;

public class ServiceOrderSummary {

    protected List<ServiceOrderShort> serviceOrderShortList;

    protected BigDecimal approvedAmount;

    protected BigDecimal pendingApprovalL1;

    protected BigDecimal pendingApprovalL2;

    protected BigDecimal pendingApprovalL3;

    protected BigDecimal inProgessAmount;

    protected BigDecimal finApprovalL1;

    public List<ServiceOrderShort> getServiceOrderShortList() {
        return serviceOrderShortList;
    }

    public void setServiceOrderShortList(List<ServiceOrderShort> serviceOrderShortList) {
        this.serviceOrderShortList = serviceOrderShortList;
    }

    public BigDecimal getApprovedAmount() {
        return approvedAmount;
    }

    public void setApprovedAmount(BigDecimal approvedAmount) {
        this.approvedAmount = approvedAmount;
    }

    public BigDecimal getPendingApprovalL1() {
        return pendingApprovalL1;
    }

    public void setPendingApprovalL1(BigDecimal pendingApprovalL1) {
        this.pendingApprovalL1 = pendingApprovalL1;
    }

    public BigDecimal getPendingApprovalL2() {
        return pendingApprovalL2;
    }

    public void setPendingApprovalL2(BigDecimal pendingApprovalL2) {
        this.pendingApprovalL2 = pendingApprovalL2;
    }

    public BigDecimal getInProgessAmount() {
        return inProgessAmount;
    }

    public void setInProgessAmount(BigDecimal inProgessAmount) {
        this.inProgessAmount = inProgessAmount;
    }

    public BigDecimal getFinApprovalL1() {
        return finApprovalL1;
    }

    public void setFinApprovalL1(BigDecimal finApprovalL1) {
        this.finApprovalL1 = finApprovalL1;
    }

    public BigDecimal getPendingApprovalL3() {
        return pendingApprovalL3;
    }

    public void setPendingApprovalL3(BigDecimal pendingApprovalL3) {
        this.pendingApprovalL3 = pendingApprovalL3;
    }
}
