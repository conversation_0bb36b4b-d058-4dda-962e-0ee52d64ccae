package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SoPoSummary {
    private BigDecimal totalPoAmount = BigDecimal.ZERO;
    private BigDecimal totalSoAmount = BigDecimal.ZERO;
    private BigDecimal totalUploadedAmount = BigDecimal.ZERO;
}
