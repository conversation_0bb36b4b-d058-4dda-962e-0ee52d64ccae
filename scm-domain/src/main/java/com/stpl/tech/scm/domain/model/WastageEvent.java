//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.26 at 02:20:27 AM IST 
//

package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.ErrorsVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WastageEvent", propOrder = { "wastageId", "unitId", "businessDate", "generationTime", "comment",
		"status", "linkedKettleId", "reasonCode", "items", "errors" })
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WastageEvent extends AbstractInventoryVO implements ConsumptionVO, ReceivingVO, ErrorsVO {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer wastageId;
	protected int unitId;
	protected int generatedBy;
	protected Integer linkedKettleId;
	protected String linkedKettleIdType;
	protected String kettleReason;
	protected String grReason;
	protected Integer linkedGrId;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date businessDate;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date generationTime;
	@XmlElement(required = true, nillable = true)
	@XmlSchemaType(name = "string")
	protected StockEventStatus status;
	protected List<WastageData> items;
	@XmlElement(required = true, nillable = true)
	@XmlSchemaType(name = "string")
	protected WastageEventType type;
	protected PriceUpdateEntryType inventoryType;
	protected List<String> errors;
	protected String empName;
	protected List<String> autoBookedProducts;

	/**
	 * Gets the value of the wastageId property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getWastageId() {
		return wastageId;
	}

	/**
	 * Sets the value of the wastageId property.
	 * 
	 * @param value
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setWastageId(Integer value) {
		this.wastageId = value;
	}

	/**
	 * Gets the value of the unitId property.
	 * 
	 */
	public int getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the generatedBy property.
	 * 
	 */
	public void setGeneratedBy(int value) {
		this.generatedBy = value;
	}

	/**
	 * Gets the value of the generatedBy property.
	 *
	 */
	public int getGeneratedBy() {
		return generatedBy;
	}

	/**
	 * Sets the value of the unitId property.
	 *
	 */
	public void setUnitId(int value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the businessDate property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getBusinessDate() {
		return businessDate;
	}

	/**
	 * Sets the value of the businessDate property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setBusinessDate(Date value) {
		this.businessDate = value;
	}

	/**
	 * Gets the value of the generationTime property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getGenerationTime() {
		return generationTime;
	}

	/**
	 * Sets the value of the generationTime property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setGenerationTime(Date value) {
		this.generationTime = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link StockEventStatus }
	 * 
	 */
	public StockEventStatus getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link StockEventStatus }
	 * 
	 */
	public void setStatus(StockEventStatus value) {
		this.status = value;
	}

	public Integer getLinkedGrId() {
		return linkedGrId;
	}

	public void setLinkedGrId(Integer linkedGrId) {
		this.linkedGrId = linkedGrId;
	}

	public Integer getLinkedKettleId() {
		return linkedKettleId;
	}

	public void setLinkedKettleId(Integer linkedKettleId) {
		this.linkedKettleId = linkedKettleId;
	}

	public List<WastageData> getItems() {
		if (items == null) {
			items = new ArrayList<>();
		}
		return items;
	}

	public WastageEventType getType() {
		return type;
	}

	public void setType(WastageEventType type) {
		this.type = type;
	}

	public String getLinkedKettleIdType() {
		return linkedKettleIdType;
	}

	public void setLinkedKettleIdType(String linkedKettleIdType) {
		this.linkedKettleIdType = linkedKettleIdType;
	}

	public String getKettleReason() {
		return kettleReason;
	}

	public void setKettleReason(String kettleReason) {
		this.kettleReason = kettleReason;
	}

	public String getGrReason() {
		return grReason;
	}

	public void setGrReason(String grReason) {
		this.grReason = grReason;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getKeyId()
	 */
	@Override
	@JsonIgnore
	public int getKeyId() {
		return this.wastageId;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getKeyType()
	 */
	@Override
	@JsonIgnore
	public StockEventType getKeyType() {
		return StockEventType.WASTAGE;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getInventoryType()
	 */
	@Override
	@JsonIgnore
	public PriceUpdateEntryType getInventoryType() {
		return this.inventoryType;
	}

	public void setInventoryType(PriceUpdateEntryType inventoryType) {
		this.inventoryType = inventoryType;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.scm.domain.model.AbstractInventoryVO#getInventoryItems()
	 */
	@Override
	@JsonIgnore
	public List<InventoryItemVO> getInventoryItems() {
		List<InventoryItemVO> list = new ArrayList<>(this.items);
		return list;
	}

	public List<String> getErrors() {
		return errors;
	}

	public void setErrors(List<String> errors) {
		this.errors = errors;
	}


	public String getEmpName() {
		return empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	public List<String> getAutoBookedProducts() {
		return autoBookedProducts;
	}

	public void setAutoBookedProducts(List<String> autoBookedProducts) {
		this.autoBookedProducts = autoBookedProducts;
	}

	@Override
	public boolean isAssetOrder() {
		return false;
	}

	public void setItems(List<WastageData> items) {
		if (this.items == null) {
			this.items = new ArrayList<>();
		}
		this.items = items;
	}
}
