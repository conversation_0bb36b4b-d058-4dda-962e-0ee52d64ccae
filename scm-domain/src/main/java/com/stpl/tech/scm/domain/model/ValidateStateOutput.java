package com.stpl.tech.scm.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ValidateStateOutput {

    Long taskId;
    String KeyType;
    Integer keyValue;
    String CurrentKeyStatus;
    Integer initiatedBy;

    boolean isCheckPassed;

}
