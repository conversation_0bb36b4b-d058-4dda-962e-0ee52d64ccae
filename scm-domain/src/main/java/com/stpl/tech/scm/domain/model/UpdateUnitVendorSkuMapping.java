//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.11.19 at 11:43:30 AM IST 
//

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UpdateUnitVendorSkuMapping", propOrder = { "employeeId", "employeeName", "mapping" })
public class UpdateUnitVendorSkuMapping {

	protected int employeeId;
	protected String employeeName;
	protected UnitVendorSkuMapping mapping;

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public UnitVendorSkuMapping getMapping() {
		return mapping;
	}

	public void setMapping(UnitVendorSkuMapping mapping) {
		this.mapping = mapping;
	}

	

}
