//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.04.20 at 07:12:44 PM IST
//


package com.stpl.tech.scm.domain.model;

import org.apache.commons.lang.StringUtils;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for VendorDetail complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="VendorDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="vendorId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="entityName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="firstName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lastName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="primaryContact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="secondaryContact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="primaryEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="secondaryEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}VendorStatus"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}VendorType"/&gt;
 *         &lt;element name="requestedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="updateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="creditPeriodDays" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="debitBalance" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="paymentBlocked" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="vendorAddress" type="{http://www.w3schools.com}AddressDetail"/&gt;
 *         &lt;element name="companyDetails" type="{http://www.w3schools.com}VendorCompanyDetail"/&gt;
 *         &lt;element name="accountDetails" type="{http://www.w3schools.com}VendorAccountDetail"/&gt;
 *         &lt;element name="dispatchLocations" type="{http://www.w3schools.com}VendorDispatchLocation" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VendorDetail", propOrder = {
    "vendorId",
    "entityName",
    "firstName",
    "lastName",
    "primaryContact",
    "secondaryContact",
    "primaryEmail",
    "secondaryEmail",
    "status",
    "type",
    "requestedBy",
    "updatedBy",
    "updateTime",
    "creditPeriodDays",
    "paymentBlocked",
    "vendorAddress",
    "companyDetails",
    "accountDetails",
    "dispatchLocations"
})
public class VendorDetail {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer vendorId;
    @XmlElement(required = true)
    protected String entityName;
    @XmlElement(required = true)
    protected String firstName;
    @XmlElement(required = true)
    protected String lastName;
    @XmlElement(required = true)
    protected String primaryContact;
    @XmlElement(required = true)
    protected String secondaryContact;
    @XmlElement(required = true)
    protected String primaryEmail;
    @XmlElement(required = true)
    protected String secondaryEmail;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected VendorStatus status;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected VendorType type;
    @XmlElement(required = true)
    protected IdCodeName requestedBy;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer creditPeriodDays;
    @XmlElement(defaultValue = "false")
    protected boolean paymentBlocked;
    @XmlElement(required = true)
    protected AddressDetail vendorAddress;
    @XmlElement(required = true)
    protected VendorCompanyDetail companyDetails;
    @XmlElement(required = true)
    protected VendorAccountDetail accountDetails;
    protected List<VendorDispatchLocation> dispatchLocations;
    protected Integer registrationId;
    protected String link;
    protected boolean disclaimerAccepted;
    protected List<VendorDebitBalanceVO> vos;
    protected Integer creditDays;
    protected Integer companyId;
    protected Integer leadTime;
    protected VendorEditedData vendorEditedData;
    protected Boolean tds;
    protected DocumentDetail tdsDocument;
    protected String vendorBlocked;
    protected String blockedReason;
    protected Date unblockedTillDate;
    protected Date lastBlockedDate;
    protected String lastBlockedBy;
    protected Date lastUnBlockedDate;
    protected String lastUnBlockedBy;
    protected Integer changeRequestId;
    private String byPassContract;

    private String isEnterpriseVendor;

    private String isCCVendor = "N";
    private String isEcomParty = "N";
    private String canCreateContract = "Y";
    private Integer documentId;
    /**
     * Gets the value of the vendorId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getVendorId() {
        return vendorId;
    }

    /**
     * Sets the value of the vendorId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setVendorId(Integer value) {
        this.vendorId = value;
    }

    /**
     * Gets the value of the entityName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getEntityName() {
        return entityName;
    }

    /**
     * Sets the value of the entityName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setEntityName(String value) {
        this.entityName = value;
    }

    /**
     * Gets the value of the firstName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * Sets the value of the firstName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setFirstName(String value) {
        this.firstName = value;
    }

    /**
     * Gets the value of the lastName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * Sets the value of the lastName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastName(String value) {
        this.lastName = value;
    }

    /**
     * Gets the value of the primaryContact property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getPrimaryContact() {
        return primaryContact;
    }

    /**
     * Sets the value of the primaryContact property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setPrimaryContact(String value) {
        this.primaryContact = value;
    }

    /**
     * Gets the value of the secondaryContact property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSecondaryContact() {
        return secondaryContact;
    }

    /**
     * Sets the value of the secondaryContact property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSecondaryContact(String value) {
        this.secondaryContact = value;
    }

    /**
     * Gets the value of the primaryEmail property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getPrimaryEmail() {
        return primaryEmail;
    }

    /**
     * Sets the value of the primaryEmail property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setPrimaryEmail(String value) {
        this.primaryEmail = value;
    }

    /**
     * Gets the value of the secondaryEmail property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSecondaryEmail() {
        return secondaryEmail;
    }

    /**
     * Sets the value of the secondaryEmail property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSecondaryEmail(String value) {
        this.secondaryEmail = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link VendorStatus }
     *
     */
    public VendorStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link VendorStatus }
     *
     */
    public void setStatus(VendorStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return
     *     possible object is
     *     {@link VendorType }
     *
     */
    public VendorType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value
     *     allowed object is
     *     {@link VendorType }
     *
     */
    public void setType(VendorType value) {
        this.type = value;
    }

    /**
     * Gets the value of the requestedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getRequestedBy() {
        return requestedBy;
    }

    /**
     * Sets the value of the requestedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setRequestedBy(IdCodeName value) {
        this.requestedBy = value;
    }

    /**
     * Gets the value of the updatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }

    /**
     * Gets the value of the updateTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets the value of the updateTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUpdateTime(Date value) {
        this.updateTime = value;
    }

    /**
     * Gets the value of the creditPeriodDays property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getCreditPeriodDays() {
        return creditPeriodDays;
    }

    /**
     * Sets the value of the creditPeriodDays property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setCreditPeriodDays(Integer value) {
        this.creditPeriodDays = value;
    }

    /**
     * Gets the value of the paymentBlocked property.
     *
     */
    public boolean isPaymentBlocked() {
        return paymentBlocked;
    }

    /**
     * Sets the value of the paymentBlocked property.
     *
     */
    public void setPaymentBlocked(boolean value) {
        this.paymentBlocked = value;
    }

    /**
     * Gets the value of the vendorAddress property.
     *
     * @return
     *     possible object is
     *     {@link AddressDetail }
     *
     */
    public AddressDetail getVendorAddress() {
        return vendorAddress;
    }

    /**
     * Sets the value of the vendorAddress property.
     *
     * @param value
     *     allowed object is
     *     {@link AddressDetail }
     *
     */
    public void setVendorAddress(AddressDetail value) {
        this.vendorAddress = value;
    }

    /**
     * Gets the value of the companyDetails property.
     *
     * @return
     *     possible object is
     *     {@link VendorCompanyDetail }
     *
     */
    public VendorCompanyDetail getCompanyDetails() {
        return companyDetails;
    }

    /**
     * Sets the value of the companyDetails property.
     *
     * @param value
     *     allowed object is
     *     {@link VendorCompanyDetail }
     *
     */
    public void setCompanyDetails(VendorCompanyDetail value) {
        this.companyDetails = value;
    }

    /**
     * Gets the value of the accountDetails property.
     *
     * @return
     *     possible object is
     *     {@link VendorAccountDetail }
     *
     */
    public VendorAccountDetail getAccountDetails() {
        return accountDetails;
    }

    /**
     * Sets the value of the accountDetails property.
     *
     * @param value
     *     allowed object is
     *     {@link VendorAccountDetail }
     *
     */
    public void setAccountDetails(VendorAccountDetail value) {
        this.accountDetails = value;
    }

    /**
     * Gets the value of the dispatchLocations property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dispatchLocations property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDispatchLocations().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link VendorDispatchLocation }
     *
     *
     */
    public List<VendorDispatchLocation> getDispatchLocations() {
        if (dispatchLocations == null) {
            dispatchLocations = new ArrayList<VendorDispatchLocation>();
        }
        return this.dispatchLocations;
    }


    public Integer getRegistrationId() {
        return registrationId;
    }

    public void setRegistrationId(Integer registrationId) {
        this.registrationId = registrationId;
    }

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}



    public List<VendorDebitBalanceVO> getVos() {
    	if(vos == null){
    		vos = new ArrayList<>();
    	}
		return vos;
	}

    public void setVos(List<VendorDebitBalanceVO> vos) {
        this.vos = vos;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getCreditDays() {
        return creditDays;
    }

    public void setCreditDays(Integer creditDays) {
        this.creditDays = creditDays;
    }

    public Integer getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime) {
        this.leadTime = leadTime;
    }

    public VendorDetail trim(){
        VendorDetail vendorDetail = new VendorDetail();
        vendorDetail.setVendorId(this.getVendorId());
        vendorDetail.setEntityName(StringUtils.capitalize(this.getEntityName()));
        vendorDetail.setFirstName(StringUtils.capitalize(this.getFirstName()));
        vendorDetail.setLastName(StringUtils.capitalize(this.getLastName()));
        vendorDetail.setType(this.getType());
        vendorDetail.setStatus(this.getStatus());
        vendorDetail.setUpdatedBy(this.getUpdatedBy());
        vendorDetail.setUpdateTime(this.getUpdateTime());
        vendorDetail.setRequestedBy(this.getRequestedBy());
        vendorDetail.getDispatchLocations().addAll(this.getDispatchLocations());
        vendorDetail.setPrimaryEmail(this.getPrimaryEmail());
        vendorDetail.setPrimaryContact(this.getPrimaryContact());
        vendorDetail.setSecondaryEmail(this.getSecondaryEmail());
        vendorDetail.setSecondaryContact(this.getSecondaryContact());
        vendorDetail.setLeadTime(this.leadTime);
        vendorDetail.setVos(this.getVos());
        vendorDetail.setCompanyDetails(this.getCompanyDetails());
        vendorDetail.setVendorAddress(this.vendorAddress);
        vendorDetail.setVendorBlocked(this.getVendorBlocked());
        vendorDetail.setBlockedReason(this.getBlockedReason());
        vendorDetail.setByPassContract(this.getByPassContract());
        vendorDetail.setIsEcomParty(this.getIsEcomParty());
        vendorDetail.setIsCCVendor(this.getIsCCVendor());
        return vendorDetail;
   }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        VendorDetail that = (VendorDetail) o;

        return vendorId.equals(that.vendorId);
    }

    @Override
    public int hashCode() {
        return vendorId.hashCode();
    }

	public boolean isDisclaimerAccepted() {
		return disclaimerAccepted;
	}

	public void setDisclaimerAccepted(boolean disclaimerAccepted) {
		this.disclaimerAccepted = disclaimerAccepted;
	}

    public VendorEditedData getVendorEditedData() {
        return vendorEditedData;
    }

    public void setVendorEditedData(VendorEditedData vendorEditedData) {
        this.vendorEditedData = vendorEditedData;
    }

    public Boolean getTds() {
        return tds;
    }

    public void setTds(Boolean tds) {
        this.tds = tds;
    }

    public DocumentDetail getTdsDocument() {
        return tdsDocument;
    }

    public void setTdsDocument(DocumentDetail tdsDocument) {
        this.tdsDocument = tdsDocument;
    }

    public String getVendorBlocked() {
        return vendorBlocked;
    }

    public void setVendorBlocked(String vendorBlocked) {
        this.vendorBlocked = vendorBlocked;
    }

    public String getBlockedReason() {
        return blockedReason;
    }

    public void setBlockedReason(String blockedReason) {
        this.blockedReason = blockedReason;
    }

    public Date getUnblockedTillDate() {
        return unblockedTillDate;
    }

    public void setUnblockedTillDate(Date unblockedTillDate) {
        this.unblockedTillDate = unblockedTillDate;
    }

    public Date getLastBlockedDate() {
        return lastBlockedDate;
    }

    public void setLastBlockedDate(Date lastBlockedDate) {
        this.lastBlockedDate = lastBlockedDate;
    }

    public String getLastBlockedBy() {
        return lastBlockedBy;
    }

    public void setLastBlockedBy(String lastBlockedBy) {
        this.lastBlockedBy = lastBlockedBy;
    }

    public Date getLastUnBlockedDate() {
        return lastUnBlockedDate;
    }

    public void setLastUnBlockedDate(Date lastUnBlockedDate) {
        this.lastUnBlockedDate = lastUnBlockedDate;
    }

    public String getLastUnBlockedBy() {
        return lastUnBlockedBy;
    }

    public void setLastUnBlockedBy(String lastUnBlockedBy) {
        this.lastUnBlockedBy = lastUnBlockedBy;
    }

    public String getByPassContract() {
        return byPassContract;
    }

    public void setByPassContract(String byPassContract) {
        this.byPassContract = byPassContract;
    }

    public String getIsEnterpriseVendor() {
        return isEnterpriseVendor;
    }

    public void setIsEnterpriseVendor(String isEnterpriseVendor) {
        this.isEnterpriseVendor = isEnterpriseVendor;
    }

    public String getIsCCVendor() {
        return isCCVendor;
    }

    public void setIsCCVendor(String isCCVendor) {
        this.isCCVendor = isCCVendor;
    }

    public String getIsEcomParty() {
        return isEcomParty;
    }

    public void setIsEcomParty(String isEcomParty) {
        this.isEcomParty = isEcomParty;
    }

    public String getCanCreateContract() {
        return canCreateContract;
    }
    public void setCanCreateContract(String canCreateContract) {
        this.canCreateContract = canCreateContract;
    }

    public Integer getDocumentId() {
        return documentId;
    }
    public void setDocumentId(Integer documentId) {
        this.documentId = documentId;
    }
}
