/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DayCloseFrequencyMismatch {
    protected Integer closureId;
    protected Integer unitId;
    protected Integer productId;
    protected Date businessDate;
    protected Integer startOrderId;
    protected Integer endOrderId;
    protected String stockType;
}
