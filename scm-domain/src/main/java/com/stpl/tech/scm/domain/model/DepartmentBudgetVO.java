package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlElement;

public class DepartmentBudgetVO {
	
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer deptId;
	@XmlElement(required = true)
	protected String deptName;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal amount;
	protected BigDecimal actualAmount = null;
	public Integer getDeptId() {
		return deptId;
	}
	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public BigDecimal getActualAmount() {
		return actualAmount;
	}

	public void setActualAmount(BigDecimal actualAmount) {
		this.actualAmount = actualAmount;
	}
}
