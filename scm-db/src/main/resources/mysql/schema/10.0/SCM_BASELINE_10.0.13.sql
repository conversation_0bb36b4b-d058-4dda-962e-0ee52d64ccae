CREATE TABLE KETTLE_SCM.RIDER_INFO_DATA
(
    RIDER_INFO_DATA_ID    INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    EMPLOYEE_ID      INT                NOT NULL,
    VEHICLE_ID       INT                NOT NULL,
    UNIT_ID       INT                NOT NULL,
    CREATION_TIME    TIMESTAMP          NULL,
    RIDER_STATUS     VARCHAR(30)       NOT NULL,
    LAST_UPDATE_TIME TIMESTAMP        NULL
);

ALTER TABLE KETTLE_SCM.UNIT_DETAIL ADD COLUMN HUB_SPOKE_TYPE VARCHAR(15) DEFAULT NULL;

CREATE TABLE KETTLE_SCM.STOCK_REDISTRIBUTION_ROUTE_INFO_DATA
(
    STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    ROUTE_NAME                              VARCHAR(255)       NOT NULL,
    ROUTE_STATUS                            VARCHAR(20)       NOT NULL,
    CREATION_TIME                           TIMESTAMP           NULL,
    LAST_UPDATE_TIME                        TIMESTAMP        NULL
);

CREATE TABLE KETTLE_SCM.STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA
(
    STOCK_REDISTRIBUTION_ROUTE_UNIT_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID INT                NOT NULL,
    UNIT_ID INT                NOT NULL
);

CREATE TABLE KETTLE_SCM.RIDER_ROUTE_PLAN_DATA
(
    RIDER_ROUTE_PLAN_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    RIDER_ID                 INT                NOT NULL,
    ROUTE_ID                 INT                NOT NULL,
    RIDER_ROUTE_PLAN_STATUS  VARCHAR(30)       NOT NULL,
    RIDER_ROUTE_TYPE  VARCHAR(30)       NOT NULL,
    CREATION_TIME            TIMESTAMP           NULL,
    RIDE_START_TIME          TIMESTAMP        NULL,
    RIDE_END_TIME            TIMESTAMP        NULL
);

CREATE TABLE RIDER_ROUTE_PLAN_STEP_DATA
(
    RIDER_ROUTE_PLAN_STEP_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    RIDER_ROUTE_PLAN_DATA_ID      INT                NOT NULL,
    ROUTE_STEP_STATUS             VARCHAR(30)       NOT NULL,
    CURRENT_STORE                 INT                NOT NULL,
    NEXT_STORE                    INT                NOT NULL,
    DROP_STATUS             VARCHAR(30)       NOT NULL,
    DROP_TIME            TIMESTAMP          DEFAULT NULL,
    DROP_TEMPERATURE            DECIMAL(5,2)         DEFAULT NULL,
    PICKUP_STATUS             VARCHAR(30)       NOT NULL,
    PICKUP_TIME            TIMESTAMP          DEFAULT NULL,
    PICKUP_TEMPERATURE            DECIMAL(5,2)         DEFAULT NULL,
    ROUTE_STEP                    INT                NOT NULL,
    PICKED_UP_TO_ID               INT                NULL,
    DROP_OFF_TO_ID                INT                NULL,
    RIDER_REACHED_TIME            TIMESTAMP          NULL,
    RIDER_LEAVE_TIME            TIMESTAMP          DEFAULT NULL,
);


CREATE TABLE KETTLE_SCM.RIDER_ROUTE_PLAN_ITEM_DATA
(
    RIDER_ROUTE_PLAN_ITEM_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    RIDER_ROUTE_PLAN_STEP_DATA_ID INT                NOT NULL,
    RIDER_ACTION                  VARCHAR(10)       NOT NULL,
    PRODUCT_ID                  INT                NOT NULL,
    PACKAGING_ID                  INT                NOT NULL,
    PROPOSED_QUANTITY             DECIMAL(16,6)           NOT NULL,
    FINAL_QUANTITY                DECIMAL(16,6)            NULL,
    STOCK_TYPE                    VARCHAR(15)       NOT NULL,
    COMMENT                       TEXT       NULL
);


INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_INFO` (`RIDER_INFO_ID`, `EMPLOYEE_ID`, `VEHICLE_ID`, `CREATION_TIME`, `RIDER_STATUS`) VALUES ('1', '140199', '1', '2024-12-06 00:00:00', 'ACTIVE');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_DATA` (`RIDER_ROUTE_PLAN_DATA_ID`, `RIDER_ID`, `ROUTE_ID`, `RIDER_ROUTE_PLAN_STATUS`, `CREATION_TIME`) VALUES ('1', '1', '1', 'CREATED', '2024-12-06 00:00:00');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_STEP_DATA` (`RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ROUTE_PLAN_DATA_ID`, `ROUTE_STEP_STATUS`, `CURRENT_STORE`, `NEXT_STORE`, `TOTAL_PICK_UP_QUANTITY`, `TOTAL_PICK_UP_PRODUCTS`, `TOTAL_DROP_OFF_QUANTITY`, `TOTAL_DROP_OFF_PRODUCTS`, `ROUTE_STEP`) VALUES ('1', '1', 'CREATED', '26422', '10000', '5', '3', '7', '3', '1');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_STEP_DATA` (`RIDER_ROUTE_PLAN_DATA_ID`, `ROUTE_STEP_STATUS`, `CURRENT_STORE`, `NEXT_STORE`, `TOTAL_PICK_UP_QUANTITY`, `TOTAL_PICK_UP_PRODUCTS`, `TOTAL_DROP_OFF_QUANTITY`, `TOTAL_DROP_OFF_PRODUCTS`, `ROUTE_STEP`) VALUES ('1', 'CREATED', '10000', '10001', '1', '1', '2', '1', '2');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_ITEM_DATA` (`RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ACTION`, `PRODUCT_ID`, `PACKAGING_ID`, `PROPOSED_QUANTITY`, `STOCK_TYPE`) VALUES ('1', 'PICKUP', '100324', '1', '5', 'FRESH');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_ITEM_DATA` (`RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ACTION`, `PRODUCT_ID`, `PACKAGING_ID`, `PROPOSED_QUANTITY`, `STOCK_TYPE`) VALUES ('1', 'DROP', '100325', '3', '7', 'EXPIRING');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_ITEM_DATA` (`RIDER_ROUTE_PLAN_ITEM_DATA_ID`, `RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ACTION`, `PRODUCT_ID`, `PACKAGING_ID`, `PROPOSED_QUANTITY`, `STOCK_TYPE`) VALUES ('3', '2', 'PICKUP', '100324', '1', '1', 'EXPIRING');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_ITEM_DATA` (`RIDER_ROUTE_PLAN_ITEM_DATA_ID`, `RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ACTION`, `PRODUCT_ID`, `PACKAGING_ID`, `PROPOSED_QUANTITY`, `STOCK_TYPE`) VALUES ('4', '2', 'DROP', '100325', '3', '2', 'FRESH');
INSERT INTO `KETTLE_SCM_STAGE`.`STOCK_REDISTRIBUTION_ROUTE_INFO_DATA` (`ROUTE_NAME`, `ROUTE_STATUS`, `CREATION_TIME`) VALUES ('Shannus Test', 'ACTIVE', '2024-12-06 00:00:00');
INSERT INTO `KETTLE_SCM_STAGE`.`STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA` (`STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID`, `UNIT_ID`, `UNIT_STATUS`) VALUES ('1', '26422', 'ACTIVE');
INSERT INTO `KETTLE_SCM_STAGE`.`STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA` (`STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID`, `UNIT_ID`, `UNIT_STATUS`) VALUES ('1', '10000', 'ACTIVE');
INSERT INTO `KETTLE_SCM_STAGE`.`STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA` (`STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID`, `UNIT_ID`, `UNIT_STATUS`) VALUES ('1', '10001', 'ACTIVE');
UPDATE `KETTLE_SCM_STAGE`.`UNIT_DETAIL` SET `HUB_SPOKE_TYPE` = 'KITCHEN' WHERE (`UNIT_ID` = '26422');
UPDATE `KETTLE_SCM_STAGE`.`UNIT_DETAIL` SET `HUB_SPOKE_TYPE` = 'HUB' WHERE (`UNIT_ID` = '10000');
UPDATE `KETTLE_SCM_STAGE`.`UNIT_DETAIL` SET `HUB_SPOKE_TYPE` = 'CAFE' WHERE (`UNIT_ID` = '10001');

ALTER TABLE KETTLE_SCM_STAGE.CAPEX_AUDIT_DETAIL ADD VERSION_CREATION_DATE DATETIME;


ALTER TABLE `KETTLE_SCM`.`REFERENCE_ORDER_SCM_ITEM`
ADD COLUMN `EXPIRY_USAGE_LOGS` TEXT NULL;

CREATE TABLE `KETTLE_SCM`.DAY_WISE_ORDER_DATA
(
    DAY_WISE_ORDER_DATA_ID          INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    RO_ORDERING_DAYS_ID             INT                NULL,
    AVERAGE_CONSUMPTION_FOR_THE_DAY DECIMAL            NULL,
    SUGGESTED_QUANTITY_TO_ORDER     DECIMAL            NULL,
    ORDER_DATE                      DATETIME           NULL,
    CONSUMPTION_DATA                TEXT               NULL
);

ALTER TABLE `KETTLE_SCM_DEV`.`RO_SCM_ITEM_EXPIRY_DRILLDOWN`
ADD COLUMN `FULFILMENT_DATE` TIMESTAMP NULL;
