CREATE TABLE KETTLE_SCM_DEV.PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT(
DRILL_DOWN_UPDATE_EVENT_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
EVENT_ID INTEGER NULL,
INVENTORY_ID INTEGER NULL,
COST DECIMAL(16,6) NULL,
PRICE DECIMAL(16,6)  NULL
);



CREATE INDEX PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT_INVENTORY_ID ON KETTLE_SCM_DEV.PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT(INVENTORY_ID) USING BTREE;
CREATE INDEX PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT_EVENT_ID ON KETTLE_SCM_DEV.PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT(EVENT_ID) USING BTREE;
CREATE INDEX PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT_COST ON KETTLE_SCM_DEV.PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT(COST) USING BTREE;
CREATE INDEX PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT_PRICE ON KETTLE_SCM_DEV.PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT(PRICE) USING BTREE;


CREATE INDEX STOCK_ENTRY_UNIT_ID ON KETTLE_SCM.STOCK_ENTRY(UNIT_ID) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.SERVICE_ORDER ADD COLUMN UPLOADED_DOCUMENT_ID INTEGER(20) NULL;
ALTER TABLE KETTLE_SCM_DEV.CAPEX_AUDIT_DETAIL ADD COLUMN APPROVED_DATE TIMESTAMP NULL;

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN TAX_AMOUNT DECIMAL(10, 2)  DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN EXTRA_GR_AMOUNT DECIMAL(16,6) NULL;

ALTER TABLE KETTLE_SCM_DEV.CAPEX_BUDGET_DETAIL ADD COLUMN EXTRA_RECEIVING DECIMAL(16,6) NOT NULL DEFAULT '0.000000';

ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER ADD COLUMN TYPE varchar(45) NULL;

UPDATE KETTLE_SCM_DEV.PURCHASE_ORDER SET TYPE="OPEX" WHERE TYPE IS NULL;

ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER_ITEM_DETAIL ADD COLUMN TYPE varchar(45) NULL;

UPDATE KETTLE_SCM_DEV.PURCHASE_ORDER_ITEM_DETAIL SET TYPE="OPEX" WHERE TYPE IS NULL;

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN TYPE varchar(45) NULL;

UPDATE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA SET TYPE = "OPEX" WHERE TYPE IS NULL;

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER ADD COLUMN TYPE varchar(45) NULL;

UPDATE KETTLE_SCM_DEV.TRANSFER_ORDER SET TYPE="OPEX" WHERE TYPE IS NULL;
CREATE TABLE KETTLE_SCM_DEV.`PRODUCT_PROJECTIONS_DETAILS`(
	`PRODUCT_PROJECTIONS_DETAIL_ID` INTEGER(11) NOT NULL AUTO_INCREMENT,
  `START_DATE` DATE NULL,
  `END_DATE` DATE NULL,
  `GENERATED_BY` varchar(100) DEFAULT NULL,
	`UPLOADED_BY` varchar(100) DEFAULT NULL,
	`DOWNLOADED_PATH` varchar(200) DEFAULT NULL,
  `CREATION_TIME` timestamp NULL,
  PRIMARY KEY (PRODUCT_PROJECTIONS_DETAIL_ID)
);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Product Projections', 'Access to see Product Projections Menu Tab', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SPP', '7', 'MENU', 'SHOW', 'SuMo -> Product Projections', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Product Projections'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SPP'), 'ACTIVE', '120063', '2022-05-03 12:00:00');

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_PROJECTIONS_DETAILS ADD COLUMN END_TIME TIMESTAMP NULL;

ALTER TABLE `kettle_scm_dev`.`stock_event_definition`
ADD COLUMN `DEVICE_INFO` VARCHAR(300) NULL DEFAULT NULL AFTER `SPLIT`;
