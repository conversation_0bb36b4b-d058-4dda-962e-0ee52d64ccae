#ALREADY RUN IN PROD
/*
INSERT INTO PRODUCT_DEFINITION
(PRODUCT_NAME,PRODUCT_DESCRIPTION,CATEGORY_ID,SUB_CATEGORY_ID,SUPPORTS_LOOSE_ORDERING,HAS_INNER,HAS_CASE,STOCK_KEEPING_FREQUENCY,SHELF_LIFE_IN_DAYS,UNIT_OF_MEASURE,PARTICIPATES_IN_RECIPE,VARIANT_LEVEL_ORDERING,SUPPORTS_SPECIALIZED_ORDERING,UNIT_PRICE,NEGOTIATED_UNIT_PRICE)
VALUES
('3M Highland Selfsticky Notes','3M Highland Selfsticky Notes','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','17.5','17.5'),
('A/4 Plastic file','A/4 Plastic file','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','95','95'),
('Account Stamp','Account <PERSON>amp','2','14','Y','N','N','MONTHLY','1','PC','N','N','N','250','250'),
('Acrylic Water Jug','Acrylic Water Jug','2','10','Y','N','N','MONTHLY','1','PC','N','N','N','1080','1080'),
('Adarak Tulsi Chai Box','Adarak Tulsi Chai Box','1','4','Y','Y','N','MONTHLY','1','PC','Y','N','N','8.5','8.5'),
('Address Stamp','Address Stamp','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','150','150'),
('Ajanta lemon yellow colour','Ajanta lemon yellow colour','1','5','Y','N','N','MONTHLY','1','PC','Y','N','N','28','28'),
('Amchur','Amchur','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','557.5','557.5'),
('Amira Guru Rice','Amira Guru Rice','1','5','N','Y','N','MONTHLY','1','KG','N','N','N','24','24'),
('ANKUR XHL - TMH - 3910 METAL PENDANT HANGING Green Hanging with Holder / Wire 2 mtr','ANKUR XHL - TMH - 3910 METAL PENDANT HANGING Green Hanging with Holder / Wire 2 mtr','2','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','1400','1400'),
('ANKUR XOL-LED-IP/0483 30W 3000K','ANKUR XOL-LED-IP/0483 30W 3000K','2','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','1695','1695'),
('Arhar Dal 1 kg  Rajdhani','Arhar Dal 1 kg  Rajdhani','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','162','162'),
('Attendence register  56 pages','Attendence register  56 pages','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','50','50'),
('Bain Marie Bowl Lid','Bain Marie Bowl Lid','2','11','Y','N','N','MONTHLY','1','PC','N','N','N','112','112'),
('Ball Pen Use & Throw','Ball Pen Use & Throw','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','1.899999976','1.899999976'),
('Banana','Banana','1','6','Y','N','N','MONTHLY','1','PC','Y','N','N','50','50'),
('Barcode Printer Roll','Barcode Printer Roll','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','189','189'),
('Basil Herb','Basil Herb','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','245','245'),
('Basil Leaf','Basil Leaf','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','348.4400024','348.4400024'),
('Besan','Besan','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','82','82'),
('Black Chana','Black Chana','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','65','65'),
('Blast Freezer BC52 Model','Blast Freezer BC52 Model','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','350000','350000'),
('Bottle Gourd grate','Bottle Gourd grate','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','25','25'),
('Bull Spike Guard - monk','Bull Spike Guard - monk','2','11','Y','N','N','MONTHLY','1','PC','N','N','N','400','400'),
('Butter gravy','Butter gravy','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','117','117'),
('CABLE TIE','CABLE TIE','2','4','N','Y','N','MONTHLY','1','PC','N','N','N','50','50'),
('Cajun Seasoning','Cajun Seasoning','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','187.5','187.5'),
('Can side Sticker - Masala Chai can','Can side Sticker - Masala Chai can','1','12','Y','N','N','MONTHLY','1','PC','Y','N','N','0.349999994','0.349999994'),
('Can side Sticker-Tulsi Adrak Chai can','Can side Sticker-Tulsi Adrak Chai can','1','12','Y','N','N','MONTHLY','1','PC','Y','N','N','0.349999994','0.349999994'),
('Carrot grate','Carrot grate','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','60','60'),
('Carry Bag - 10Kg','Carry Bag - 10Kg','2','4','Y','N','N','MONTHLY','1','KG','N','N','N','110','110'),
('Carry Bag - 20 Kg','Carry Bag - 20 Kg','2','4','Y','N','N','MONTHLY','1','KG','N','N','N','275','275'),
('Carry Bag - 2Kg','Carry Bag - 2Kg','2','4','Y','N','N','MONTHLY','1','KG','N','N','N','110','110'),
('Carry Bag - 5Kg','Carry Bag - 5Kg','2','4','Y','N','N','MONTHLY','1','KG','N','N','N','110','110'),
('Cauliflower grate','Cauliflower grate','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','50','50'),
('Chaayos Apren','Chaayos Apren','2','13','Y','N','N','MONTHLY','1','PC','N','N','N','150','150'),
('Chai bazar cart','Chai bazar cart','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','70000','70000'),
('Chai Bazar Pouch','Chai Bazar Pouch','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','4.869999886','4.869999886'),
('Chana Dal','Chana Dal','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','76','76'),
('Chicken Breast','Chicken Breast','1','8','Y','N','N','MONTHLY','1','KG','Y','N','N','205','205'),
('Chicken Seekh Arabian Mint-Greenco Enterprises','Chicken Seekh Arabian Mint-Greenco Enterprises','1','8','Y','N','N','MONTHLY','1','KG','Y','N','N','270','270'),
('Chicken Seekh Hot (with a hint of Garlic)-Greenco Enterprises','Chicken Seekh Hot (with a hint of Garlic)-Greenco Enterprises','1','8','Y','N','N','MONTHLY','1','KG','Y','N','N','270','270'),
('Chikoo','Chikoo','1','6','Y','N','N','MONTHLY','1','KG','Y','N','N','60','60'),
('Chilli Jaipuri','Chilli Jaipuri','1','9','Y','N','N','MONTHLY','1','KG','Y','N','N','100','100'),
('Chopping Board white','Chopping Board white','2','10','Y','N','N','MONTHLY','1','PC','N','N','N','1190','1190'),
('Container 250 ml','Container 250 ml','3','10','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','16','16'),
('COOKER 22 LTR HAWKINS','COOKER 22 LTR HAWKINS','3','16','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','4718','4718'),
('Cookie packing material','Cookie packing material','1','4','Y','N','N','MONTHLY','1','KG','N','N','N','326.4500122','326.4500122'),
('copper lagan 18 heavy','copper lagan 18 heavy','3','16','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','7500','7500'),
('coriander chopped','coriander chopped','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','106.4499969','106.4499969'),
('Coriander Green','Coriander Green','1','9','Y','N','N','MONTHLY','1','KG','Y','N','N','35','35'),
('Coriander seeds','Coriander seeds','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','180','180'),
('Corn Flour Tops','Corn Flour Tops','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','130','130'),
('corrugated box (PETALS-1200 PCS)','corrugated box (PETALS-1200 PCS)','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','840','840'),
('corrugated box (PETALS-600 PCS)','corrugated box (PETALS-600 PCS)','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','420','420'),
('Curry Leaf','Curry Leaf','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','161.3200073','161.3200073'),
('Custard vanilla -tops','Custard vanilla -tops','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','135','135'),
('Cutlery Plate','Cutlery Plate','3','10','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','87.26999664','87.26999664'),
('Daily Checklist Book - Existing','Daily Checklist Book - Existing','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','140','140'),
('Degi Mirch','Degi Mirch','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','393.6000061','393.6000061'),
('Desi Ghee - Patanjali','Desi Ghee - Patanjali','1','3','N','Y','N','MONTHLY','1','KG','Y','N','N','530','530'),
('DIP CONTAINER LID','DIP CONTAINER LID','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','0.280000001','0.280000001'),
('Egg','Egg','1','8','N','Y','N','MONTHLY','1','PC','Y','N','N','3.670000076','3.670000076'),
('Fresh Cream','Fresh Cream','1','3','N','Y','N','MONTHLY','1','KG','Y','N','N','160','160'),
('Garam Masala Powder','Garam Masala Powder','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','590.4000244','590.4000244'),
('Garlic','Garlic','1','9','Y','N','N','MONTHLY','1','KG','Y','N','N','86','86'),
('Garlic chopped','Garlic chopped','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','142.6300049','142.6300049'),
('Gas lighter','Gas lighter','2','11','Y','N','N','MONTHLY','1','PC','N','N','N','105','105'),
('Gift Card - 1000','Gift Card - 1000','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','0','0'),
('Gloves Rubber','Gloves Rubber','2','4','N','Y','N','MONTHLY','1','PC','N','N','N','64','64'),
('Gn Pan 1/3*200 mm Deep','Gn Pan 1/3*200 mm Deep','3','16','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','1050','1050'),
('GN Pan 1/9 with Lid','GN Pan 1/9 with Lid','3','16','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','401','401'),
('GN PAN LID 1/2','GN PAN LID 1/2','3','16','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','305','305'),
('GN PAN LID 1/6','GN PAN LID 1/6','3','16','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','100','100'),
('GN Trolly 36"x24"x66"','GN Trolly 36"x24"x66"','3','16','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','15600','15600'),
('Granite 3*2 Ft BlacK','Granite 3*2 Ft BlacK','3','19','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','1200','1200'),
('green chilli chopped','green chilli chopped','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','100.6699982','100.6699982'),
('Green pea','Green pea','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','60','60'),
('GROUTED TABLE BASE 42"','GROUTED TABLE BASE 42"','3','19','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','940','940'),
('Haldi Powder','Haldi Powder','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','260','260'),
('Hard Disk 2 TB','Hard Disk 2 TB','3','17','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','6500','6500'),
('Hing','Hing','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','1209','1209'),
('HOT CUP 110 ML-IRCTC','HOT CUP 110 ML-IRCTC','2','4','N','Y','Y','MONTHLY','1','PC','Y','N','N','0.519999981','0.519999981'),
('Ice Scoop Stand','Ice Scoop Stand','2','11','Y','N','N','MONTHLY','1','PC','N','N','N','270','270'),
('Imly Tomato Chutney','Imly Tomato Chutney','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','250','250'),
('Inward Register','Inward Register','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','135','135'),
('Jeera Whole','Jeera Whole','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','199','199'),
('Kasuri Methi','Kasuri Methi','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','793.3400269','793.3400269'),
('kesar color','kesar color','1','5','Y','N','N','MONTHLY','1','PC','Y','N','N','70','70'),
('Kesar Tong','Kesar Tong','2','11','Y','N','N','MONTHLY','1','PC','N','N','N','20','20'),
('Kishmish','Kishmish','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','250','250'),
('Kitchen King','Kitchen King','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','580','580'),
('Kitchen Tissue roll','Kitchen Tissue roll','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','240','240'),
('LD 10*12','LD 10*12','2','4','Y','Y','N','MONTHLY','1','KG','Y','N','N','155','155'),
('LD 10X16','LD 10X16','2','4','Y','Y','N','MONTHLY','1','KG','Y','N','N','155','155'),
('LD 12X18','LD 12X18','2','4','Y','Y','N','MONTHLY','1','KG','Y','N','N','155','155'),
('LD 4X5','LD 4X5','2','4','Y','Y','N','MONTHLY','1','KG','Y','N','N','155','155'),
('LD 6 x 8','LD 6 x 8','2','4','Y','Y','N','MONTHLY','1','KG','Y','N','N','155','155'),
('LD 7*11','LD 7*11','2','4','Y','Y','N','MONTHLY','1','KG','Y','N','N','155','155'),
('LED - 15 W 3000 K- Ankur XSL - IP/9085 Rod 2 ft -  Black Finish','LED - 15 W 3000 K- Ankur XSL - IP/9085 Rod 2 ft -  Black Finish','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','850','850'),
('LPG','LPG','2','8','Y','N','N','MONTHLY','1','KG','N','N','N','46.70999908','46.70999908'),
('Maida Rajdhani','Maida Rajdhani','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','50','50'),
('Mango','Mango','1','6','Y','N','N','MONTHLY','1','KG','Y','N','N','60','60'),
('Mashed Potato','Mashed Potato','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','19','19'),
('Mayonnaise','Mayonnaise','1','3','N','Y','N','MONTHLY','1','KG','Y','N','N','103','103'),
('Measuring Spoon 1 tsp','Measuring Spoon 1 tsp','2','11','Y','N','N','MONTHLY','1','PC','N','N','N','30','30'),
('Meatza Meat Ball','Meatza Meat Ball','1','8','N','Y','N','MONTHLY','1','KG','Y','N','N','265','265'),
('Micro fibre Duster - blue','Micro fibre Duster - blue','2','14','Y','N','N','MONTHLY','1','PC','N','N','N','110','110'),
('Micro fibre Duster - Yellow','Micro fibre Duster - Yellow','2','14','Y','N','N','MONTHLY','1','PC','N','N','N','110','110'),
('Milk Boiler with insulation- Pradeep-5L','Milk Boiler with insulation- Pradeep-5L','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','10241','10241'),
('moong dal washed','moong dal washed','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','64','64'),
('Mota besan - rajdhani','Mota besan - rajdhani','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','84','84'),
('Mouse B100','Mouse B100','3','17','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','380.9500122','380.9500122'),
('MRP sticker (199)','MRP sticker (199)','1','12','Y','N','N','MONTHLY','1','PC','Y','N','N','0.300000012','0.300000012'),
('MRP sticker (299)','MRP sticker (299)','1','12','Y','N','N','MONTHLY','1','PC','Y','N','N','0.300000012','0.300000012'),
('Mushroom','Mushroom','1','9','Y','N','N','MONTHLY','1','KG','Y','N','N','150','150'),
('mushroom slices','mushroom slices','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','215.0500031','215.0500031'),
('Mustard Seed','Mustard Seed','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','70.04000092','70.04000092'),
('Mutton Keema','Mutton Keema','1','8','Y','N','N','MONTHLY','1','KG','Y','N','N','365','365'),
('Olive Oil','Olive Oil','1','5','N','Y','N','MONTHLY','1','L','Y','N','N','265','265'),
('onion chopped','onion chopped','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','70.09999847','70.09999847'),
('Outdoor Umbrella','Outdoor Umbrella','3','19','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','21000','21000'),
('Outward Register','Outward Register','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','135','135'),
('Pakoda box (450 gm)','Pakoda box (450 gm)','2','4','Y','Y','N','MONTHLY','1','PC','N','N','N','10','10'),
('Paneer','Paneer','1','3','Y','N','N','MONTHLY','1','KG','Y','N','N','200','200'),
('Pav bhaji Masala MDH','Pav bhaji Masala MDH','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','580','580'),
('Peanut','Peanut','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','115','115'),
('pen drive 4 gb moserbear','pen drive 4 gb moserbear','3','17','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','370','370'),
('Pick up tray green','Pick up tray green','2','11','Y','N','N','MONTHLY','1','PC','N','N','N','88','88'),
('polyviniyl gloves','polyviniyl gloves','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','6.400000095','6.400000095'),
('Portable Mini washing machine DMR 30 1208 blue','Portable Mini washing machine DMR 30 1208 blue','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','3701','3701'),
('Potato','Potato','1','9','Y','N','N','MONTHLY','1','KG','Y','N','N','16','16'),
('Potato Sliced','Potato Sliced','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','50','50'),
('Pulvriser 2hp','Pulvriser 2hp','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','10500','10500'),
('Push Pull Sticker','Push Pull Sticker','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','45','45'),
('RECOLD GEYSOR 25 LTR','RECOLD GEYSOR 25 LTR','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','6500','6500'),
('Red Chilli Powder','Red Chilli Powder','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','300','300'),
('Red Chilli Whole','Red Chilli Whole','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','160','160'),
('Refined oil','Refined oil','1','5','Y','N','N','MONTHLY','1','L','Y','N','N','70.66999817','70.66999817'),
('Reynolds Ball Pen','Reynolds Ball Pen','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','4.75','4.75'),
('Ripple cup 250 ml','Ripple cup 250 ml','1','4','N','Y','Y','MONTHLY','1','PC','Y','N','N','2.319999933','2.319999933'),
('Ripple Cup 360 ml','Ripple Cup 360 ml','1','4','N','Y','Y','MONTHLY','1','PC','Y','N','N','3.599999905','3.599999905'),
('Ripple Cup lid 250 ml','Ripple Cup lid 250 ml','1','4','N','Y','Y','MONTHLY','1','PC','Y','N','N','1.129999995','1.129999995'),
('Ripple Cup lid 360 ml','Ripple Cup lid 360 ml','1','4','N','Y','Y','MONTHLY','1','PC','Y','N','N','1.25999999','1.25999999'),
('Rosemary','Rosemary','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','392','392'),
('RTM Cardboard Box - Green - Cafe','RTM Cardboard Box - Green - Cafe','1','4','Y','N','N','MONTHLY','1','PC','Y','N','N','10','10'),
('RTM Premix - RTM 001','RTM Premix - RTM 001','1','5','Y','N','N','MONTHLY','1','KG','Y','N','N','480','480'),
('RTM premix-RTM 004-High Sugar-Orange-IRCTC','RTM premix-RTM 004-High Sugar-Orange-IRCTC','1','5','Y','N','N','MONTHLY','1','KG','Y','N','N','234.0299988','234.0299988'),
('RTM premix-RTM 004-Low Sugar-Orange-IRCTC inactive','RTM premix-RTM 004-Low Sugar-Orange-IRCTC inactive','1','5','Y','N','N','MONTHLY','1','KG','Y','N','N','234.6000061','234.6000061'),
('RTM premix-rtm 004-low sugar-orange-irctc new','RTM premix-rtm 004-low sugar-orange-irctc new','1','5','Y','N','N','MONTHLY','1','KG','Y','N','N','234.6000061','234.6000061'),
('RTM Sachet - RTM001- 22 gm Green Spice Jet','RTM Sachet - RTM001- 22 gm Green Spice Jet','1','20','Y','N','N','MONTHLY','1','PC','Y','N','N','15','15'),
('RTM Sachet-RTM 004-High Sugar-Orange-IRCTC','RTM Sachet-RTM 004-High Sugar-Orange-IRCTC','1','20','Y','N','N','MONTHLY','1','PC','Y','N','N','5','5'),
('RTM Sachet-RTM 004-low sugar-orange-irctc','RTM Sachet-RTM 004-low sugar-orange-irctc','1','20','Y','N','N','MONTHLY','1','PC','Y','N','N','5','5'),
('RTM Wrapper- 22 gm Green Spice Jet','RTM Wrapper- 22 gm Green Spice Jet','1','4','Y','N','N','MONTHLY','1','KG','Y','N','N','265','265'),
('RTM wrapper-high sugar-irctc','RTM wrapper-high sugar-irctc','1','4','Y','N','N','MONTHLY','1','KG','Y','N','N','213','213'),
('RTM wrapper-low sugar-irctc','RTM wrapper-low sugar-irctc','1','4','Y','N','N','MONTHLY','1','KG','Y','N','N','213','213'),
('Salt','Salt','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','16','16'),
('Samak rice','Samak rice','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','95','95'),
('Sewaiya - Vermicili R pure','Sewaiya - Vermicili R pure','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','120','120'),
('Silver Laminate Pouch','Silver Laminate Pouch','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','1.580000043','1.580000043'),
('Soft care Gel','Soft care Gel','2','14','Y','N','N','MONTHLY','1','PC','N','N','N','175.1300049','175.1300049'),
('Sonth','Sonth','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','530','530'),
('SOP book Training','SOP book Training','2','12','Y','N','N','MONTHLY','1','PC','N','N','N','250','250'),
('Soyabean','Soyabean','1','5','N','Y','N','MONTHLY','1','KG','Y','N','N','185','185'),
('spatula','spatula','2','11','Y','N','N','MONTHLY','1','PC','N','N','N','43','43'),
('Spinach','Spinach','1','9','Y','N','N','MONTHLY','1','KG','Y','N','N','20','20'),
('spinach blanched/ chopped','spinach blanched/ chopped','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','54.06000137','54.06000137'),
('Spinach Julienne','Spinach Julienne','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','50','50'),
('Strawberry','Strawberry','1','6','Y','N','N','MONTHLY','1','KG','Y','N','N','98','98'),
('Sweet Corn','Sweet Corn','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','67','67'),
('Tea kettle 15 ltr','Tea kettle 15 ltr','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','1895','1895'),
('TEA POUCH 1 LTR - SMALL SPOUT','TEA POUCH 1 LTR - SMALL SPOUT','2','4','Y','Y','N','MONTHLY','1','PC','N','N','N','9.539999962','9.539999962'),
('TEA POUCH 400 ml - SMALL SPOUT','TEA POUCH 400 ml - SMALL SPOUT','2','4','Y','Y','N','MONTHLY','1','PC','N','N','N','6.679999828','6.679999828'),
('Tik Tak Katori','Tik Tak Katori','2','10','Y','N','N','MONTHLY','1','PC','N','N','N','30.60000038','30.60000038'),
('Tomato cube 15X15mm','Tomato cube 15X15mm','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','61.02000046','61.02000046'),
('TSC ttp 244 pro barcode printer','TSC ttp 244 pro barcode printer','3','17','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','12450','12450'),
('Unidet','Unidet','2','14','Y','N','N','MONTHLY','1','L','N','N','N','60','60'),
('UPS APC BX6000','UPS APC BX6000','3','17','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','1955','1955'),
('Vim bar','Vim bar','2','14','Y','N','N','MONTHLY','1','PC','N','N','N','10','10'),
('VINEGAR TOPS 610 ML','VINEGAR TOPS 610 ML','1','5','Y','N','N','MONTHLY','1','PC','Y','N','N','32.54999924','32.54999924'),
('weighing machine charger','weighing machine charger','3','11','Y','N','N','FIXED_ASSETS','1','PC','N','N','N','550','550'),
('Yellow Peas','Yellow Peas','1','9','N','Y','N','MONTHLY','1','KG','Y','N','N','45','45'),
('Zip lock pouches','Zip lock pouches','2','4','Y','N','N','MONTHLY','1','PC','N','N','N','0.370000005','0.370000005');

UPDATE PRODUCT_DEFINITION SET CREATED_BY  = 120458, CREATION_DATE = current_timestamp(), PRODUCT_STATUS  = 'ACTIVE'  WHERE CREATED_BY = 0;

INSERT INTO SKU_DEFINITION (
SKU_NAME,SKU_DESCRIPTION,LINKED_PRODUCT_ID,SUPPORTS_LOOSE_ORDERING,HAS_INNER,HAS_CASE,SHELF_LIFE_IN_DAYS,UNIT_OF_MEASURE,UNIT_PRICE,NEGOTIATED_UNIT_PRICE,IS_DEFAULT
)
VALUES
('3M Highland Selfsticky Notes','3M Highland Selfsticky Notes','100956','Y','N','N','1','PC','17.5','17.5','Y'),
('A/4 Plastic file','A/4 Plastic file','100957','Y','N','N','1','PC','95','95','Y'),
('Account Stamp','Account Stamp','100958','Y','N','N','1','PC','250','250','Y'),
('Acrylic Water Jug','Acrylic Water Jug','100959','Y','N','N','1','PC','1080','1080','Y'),
('Adarak Tulsi Chai Box','Adarak Tulsi Chai Box','100960','Y','Y','N','1','PC','8.5','8.5','Y'),
('Address Stamp','Address Stamp','100961','Y','N','N','1','PC','150','150','Y'),
('Ajanta lemon yellow colour','Ajanta lemon yellow colour','100962','Y','N','N','1','PC','28','28','Y'),
('Amchur','Amchur','100963','N','Y','N','1','KG','557.5','557.5','Y'),
('Amira Guru Rice','Amira Guru Rice','100964','N','Y','N','1','KG','24','24','Y'),
('ANKUR XHL - TMH - 3910 METAL PENDANT HANGING Green Hanging with Holder / Wire 2 mtr','ANKUR XHL - TMH - 3910 METAL PENDANT HANGING Green Hanging with Holder / Wire 2 mtr','100965','Y','N','N','1','PC','1400','1400','Y'),
('ANKUR XOL-LED-IP/0483 30W 3000K','ANKUR XOL-LED-IP/0483 30W 3000K','100966','Y','N','N','1','PC','1695','1695','Y'),
('Arhar Dal 1 kg  Rajdhani','Arhar Dal 1 kg  Rajdhani','100967','N','Y','N','1','KG','162','162','Y'),
('Attendence register  56 pages','Attendence register  56 pages','100968','Y','N','N','1','PC','50','50','Y'),
('Bain Marie Bowl Lid','Bain Marie Bowl Lid','100969','Y','N','N','1','PC','112','112','Y'),
('Ball Pen Use & Throw','Ball Pen Use & Throw','100970','Y','N','N','1','PC','1.9','1.9','Y'),
('Banana','Banana','100971','Y','N','N','1','PC','50','50','Y'),
('Barcode Printer Roll','Barcode Printer Roll','100972','Y','N','N','1','PC','189','189','Y'),
('Basil Herb','Basil Herb','100973','N','Y','N','1','KG','245','245','Y'),
('Basil Leaf','Basil Leaf','100974','N','Y','N','1','KG','348.4','348.4','Y'),
('Besan','Besan','100975','N','Y','N','1','KG','82','82','Y'),
('Black Chana','Black Chana','100976','N','Y','N','1','KG','65','65','Y'),
('Blast Freezer BC52 Model','Blast Freezer BC52 Model','100977','Y','N','N','1','PC','350000','350000','Y'),
('Bottle Gourd grate','Bottle Gourd grate','100978','N','Y','N','1','KG','25','25','Y'),
('Bull Spike Guard - monk','Bull Spike Guard - monk','100979','Y','N','N','1','PC','400','400','Y'),
('Butter bowl - blue','Butter bowl - blue','100056','Y','N','N','1','PC','25','25','Y'),
('Butter gravy','Butter gravy','100980','N','Y','N','1','KG','117','117','Y'),
('CABLE TIE','CABLE TIE','100981','N','Y','N','1','PC','50','50','Y'),
('Cajun Seasoning','Cajun Seasoning','100982','N','Y','N','1','KG','187.5','187.5','Y'),
('Can side Sticker - Masala Chai can','Can side Sticker - Masala Chai can','100983','Y','N','N','1','PC','0.3','0.3','Y'),
('Can side Sticker-Tulsi Adrak Chai can','Can side Sticker-Tulsi Adrak Chai can','100984','Y','N','N','1','PC','0.3','0.3','Y'),
('Carrot grate','Carrot grate','100985','N','Y','N','1','KG','60','60','Y'),
('Carry Bag - 10Kg','Carry Bag - 10Kg','100986','Y','N','N','1','KG','110','110','Y'),
('Carry Bag - 20 Kg','Carry Bag - 20 Kg','100987','Y','N','N','1','KG','275','275','Y'),
('Carry Bag - 2Kg','Carry Bag - 2Kg','100988','Y','N','N','1','KG','110','110','Y'),
('Carry Bag - 5Kg','Carry Bag - 5Kg','100989','Y','N','N','1','KG','110','110','Y'),
('Cauliflower grate','Cauliflower grate','100990','N','Y','N','1','KG','50','50','Y'),
('Chaayos Apren','Chaayos Apren','100991','Y','N','N','1','PC','150','150','Y'),
('Chai bazar cart','Chai bazar cart','100992','Y','N','N','1','PC','70000','70000','Y'),
('Chai Bazar Pouch','Chai Bazar Pouch','100993','Y','N','N','1','PC','4.9','4.9','Y'),
('Chana Dal','Chana Dal','100994','N','Y','N','1','KG','76','76','Y'),
('Cheese','Cheese','100590','N','Y','N','1','KG','291.3','291.3','Y'),
('Chicken Breast','Chicken Breast','100995','Y','N','N','1','KG','205','205','Y'),
('Chicken Seekh Arabian Mint-Greenco Enterprises','Chicken Seekh Arabian Mint-Greenco Enterprises','100996','Y','N','N','1','KG','270','270','Y'),
('Chicken Seekh Hot (with a hint of Garlic)-Greenco Enterprises','Chicken Seekh Hot (with a hint of Garlic)-Greenco Enterprises','100997','Y','N','N','1','KG','270','270','Y'),
('Chikoo','Chikoo','100998','Y','N','N','1','KG','60','60','Y'),
('Chilli Jaipuri','Chilli Jaipuri','100999','Y','N','N','1','KG','100','100','Y'),
('Chopping Board white','Chopping Board white','101000','Y','N','N','1','PC','1190','1190','Y'),
('Container 250 ml','Container 250 ml','101001','Y','N','N','1','PC','16','16','Y'),
('COOKER 22 LTR HAWKINS','COOKER 22 LTR HAWKINS','101002','Y','N','N','1','PC','4718','4718','Y'),
('Cookie packing material','Cookie packing material','101003','Y','N','N','1','KG','326.5','326.5','Y'),
('copper lagan 18 heavy','copper lagan 18 heavy','101004','Y','N','N','1','PC','7500','7500','Y'),
('coriander chopped','coriander chopped','101005','N','Y','N','1','KG','106.4','106.4','Y'),
('Coriander Green','Coriander Green','101006','Y','N','N','1','KG','35','35','Y'),
('Coriander seeds','Coriander seeds','101007','N','Y','N','1','KG','180','180','Y'),
('Corn Flour Tops','Corn Flour Tops','101008','N','Y','N','1','KG','130','130','Y'),
('corrugated box (PETALS-1200 PCS)','corrugated box (PETALS-1200 PCS)','101009','Y','N','N','1','PC','840','840','Y'),
('corrugated box (PETALS-600 PCS)','corrugated box (PETALS-600 PCS)','101010','Y','N','N','1','PC','420','420','Y'),
('Curry Leaf','Curry Leaf','101011','N','Y','N','1','KG','161.3','161.3','Y'),
('Custard vanilla -tops','Custard vanilla -tops','101012','N','Y','N','1','KG','135','135','Y'),
('Cutlery Plate','Cutlery Plate','101013','Y','N','N','1','PC','87.3','87.3','Y'),
('Daily Checklist Book - Existing','Daily Checklist Book - Existing','101014','Y','N','N','1','PC','140','140','Y'),
('Degi Mirch','Degi Mirch','101015','N','Y','N','1','KG','393.6','393.6','Y'),
('Desi Ghee - Patanjali','Desi Ghee - Patanjali','101016','N','Y','N','1','KG','530','530','Y'),
('DIP CONTAINER LID','DIP CONTAINER LID','101017','Y','N','N','1','PC','0.3','0.3','Y'),
('Egg','Egg','101018','N','Y','N','1','PC','3.7','3.7','Y'),
('Fresh Cream','Fresh Cream','101019','N','Y','N','1','KG','160','160','Y'),
('Garam Masala Powder','Garam Masala Powder','101020','N','Y','N','1','KG','590.4','590.4','Y'),
('Garlic','Garlic','101021','Y','N','N','1','KG','86','86','Y'),
('Garlic chopped','Garlic chopped','101022','N','Y','N','1','KG','142.6','142.6','Y'),
('Gas lighter','Gas lighter','101023','Y','N','N','1','PC','105','105','Y'),
('Gift Card - 1000','Gift Card - 1000','101024','Y','N','N','1','PC','0','0','Y'),
('Gloves Rubber','Gloves Rubber','101025','N','Y','N','1','PC','64','64','Y'),
('Gn Pan 1/3*200 mm Deep','Gn Pan 1/3*200 mm Deep','101026','Y','N','N','1','PC','1050','1050','Y'),
('GN Pan 1/9 with Lid','GN Pan 1/9 with Lid','101027','Y','N','N','1','PC','401','401','Y'),
('GN PAN LID 1/2','GN PAN LID 1/2','101028','Y','N','N','1','PC','305','305','Y'),
('GN PAN LID 1/6','GN PAN LID 1/6','101029','Y','N','N','1','PC','100','100','Y'),
('GN Trolly 36"x24"x66"','GN Trolly 36"x24"x66"','101030','Y','N','N','1','PC','15600','15600','Y'),
('Granite 3*2 Ft BlacK','Granite 3*2 Ft BlacK','101031','Y','N','N','1','PC','1200','1200','Y'),
('green chilli chopped','green chilli chopped','101032','N','Y','N','1','KG','100.7','100.7','Y'),
('Green pea','Green pea','101033','N','Y','N','1','KG','60','60','Y'),
('GROUTED TABLE BASE 42"','GROUTED TABLE BASE 42"','101034','Y','N','N','1','PC','940','940','Y'),
('Haldi Powder','Haldi Powder','101035','N','Y','N','1','KG','260','260','Y'),
('Hard Disk 2 TB','Hard Disk 2 TB','101036','Y','N','N','1','PC','6500','6500','Y'),
('Hing','Hing','101037','N','Y','N','1','KG','1209','1209','Y'),
('HOT CUP 110 ML-IRCTC','HOT CUP 110 ML-IRCTC','101038','N','Y','Y','1','PC','0.5','0.5','Y'),
('Ice Scoop Stand','Ice Scoop Stand','101039','Y','N','N','1','PC','270','270','Y'),
('Imly Tomato Chutney','Imly Tomato Chutney','101040','N','Y','N','1','KG','250','250','Y'),
('Inward Register','Inward Register','101041','Y','N','N','1','PC','135','135','Y'),
('Jeera Whole','Jeera Whole','101042','N','Y','N','1','KG','199','199','Y'),
('Kasuri Methi','Kasuri Methi','101043','N','Y','N','1','KG','793.3','793.3','Y'),
('kesar color','kesar color','101044','Y','N','N','1','PC','70','70','Y'),
('Kesar Tong','Kesar Tong','101045','Y','N','N','1','PC','20','20','Y'),
('Kishmish','Kishmish','101046','N','Y','N','1','KG','250','250','Y'),
('Kitchen King','Kitchen King','101047','N','Y','N','1','KG','580','580','Y'),
('Kitchen Tissue roll','Kitchen Tissue roll','101048','Y','N','N','1','PC','240','240','Y'),
('LD 10*12','LD 10*12','101049','Y','Y','N','1','KG','155','155','Y'),
('LD 10X16','LD 10X16','101050','Y','Y','N','1','KG','155','155','Y'),
('LD 12X18','LD 12X18','101051','Y','Y','N','1','KG','155','155','Y'),
('LD 4X5','LD 4X5','101052','Y','Y','N','1','KG','155','155','Y'),
('LD 6 x 8','LD 6 x 8','101053','Y','Y','N','1','KG','155','155','Y'),
('LD 7*11','LD 7*11','101054','Y','Y','N','1','KG','155','155','Y'),
('LED - 15 W 3000 K- Ankur XSL - IP/9085 Rod 2 ft -  Black Finish','LED - 15 W 3000 K- Ankur XSL - IP/9085 Rod 2 ft -  Black Finish','101055','Y','N','N','1','PC','850','850','Y'),
('LPG','LPG','101056','Y','N','N','1','KG','46.7','46.7','Y'),
('Maida Rajdhani','Maida Rajdhani','101057','N','Y','N','1','KG','50','50','Y'),
('Mango','Mango','101058','Y','N','N','1','KG','60','60','Y'),
('Mashed Potato','Mashed Potato','101059','N','Y','N','1','KG','19','19','Y'),
('Mayonnaise','Mayonnaise','101060','N','Y','N','1','KG','103','103','Y'),
('Measuring Spoon 1 tsp','Measuring Spoon 1 tsp','101061','Y','N','N','1','PC','30','30','Y'),
('Meatza Meat Ball','Meatza Meat Ball','101062','N','Y','N','1','KG','265','265','Y'),
('Micro fibre Duster - blue','Micro fibre Duster - blue','101063','Y','N','N','1','PC','110','110','Y'),
('Micro fibre Duster - Yellow','Micro fibre Duster - Yellow','101064','Y','N','N','1','PC','110','110','Y'),
('Milk Boiler with insulation- Pradeep-5L','Milk Boiler with insulation- Pradeep-5L','101065','Y','N','N','1','PC','10241','10241','Y'),
('moong dal washed','moong dal washed','101066','N','Y','N','1','KG','64','64','Y'),
('Mota besan - rajdhani','Mota besan - rajdhani','101067','N','Y','N','1','KG','84','84','Y'),
('Mouse B100','Mouse B100','101068','Y','N','N','1','PC','381','381','Y'),
('MRP sticker (199)','MRP sticker (199)','101069','Y','N','N','1','PC','0.3','0.3','Y'),
('MRP sticker (299)','MRP sticker (299)','101070','Y','N','N','1','PC','0.3','0.3','Y'),
('Mushroom','Mushroom','101071','Y','N','N','1','KG','150','150','Y'),
('mushroom slices','mushroom slices','101072','N','Y','N','1','KG','215.1','215.1','Y'),
('Mustard Seed','Mustard Seed','101073','N','Y','N','1','KG','70','70','Y'),
('Mutton Keema','Mutton Keema','101074','Y','N','N','1','KG','365','365','Y'),
('Olive Oil','Olive Oil','101075','N','Y','N','1','L','265','265','Y'),
('onion chopped','onion chopped','101076','N','Y','N','1','KG','70.1','70.1','Y'),
('Outdoor Umbrella','Outdoor Umbrella','101077','Y','N','N','1','PC','21000','21000','Y'),
('Outward Register','Outward Register','101078','Y','N','N','1','PC','135','135','Y'),
('Pakoda box (450 gm)','Pakoda box (450 gm)','101079','Y','Y','N','1','PC','10','10','Y'),
('Paneer','Paneer','101080','Y','N','N','1','KG','200','200','Y'),
('Pav bhaji Masala MDH','Pav bhaji Masala MDH','101081','N','Y','N','1','KG','580','580','Y'),
('Peanut','Peanut','101082','N','Y','N','1','KG','115','115','Y'),
('pen drive 4 gb moserbear','pen drive 4 gb moserbear','101083','Y','N','N','1','PC','370','370','Y'),
('Pick up tray green','Pick up tray green','101084','Y','N','N','1','PC','88','88','Y'),
('polyviniyl gloves','polyviniyl gloves','101085','Y','N','N','1','PC','6.4','6.4','Y'),
('Portable Mini washing machine DMR 30 1208 blue','Portable Mini washing machine DMR 30 1208 blue','101086','Y','N','N','1','PC','3701','3701','Y'),
('Potato','Potato','101087','Y','N','N','1','KG','16','16','Y'),
('Potato Sliced','Potato Sliced','101088','N','Y','N','1','KG','50','50','Y'),
('Pulvriser 2hp','Pulvriser 2hp','101089','Y','N','N','1','PC','10500','10500','Y'),
('Push Pull Sticker','Push Pull Sticker','101090','Y','N','N','1','PC','45','45','Y'),
('RECOLD GEYSOR 25 LTR','RECOLD GEYSOR 25 LTR','101091','Y','N','N','1','PC','6500','6500','Y'),
('Red Chilli Powder','Red Chilli Powder','101092','N','Y','N','1','KG','300','300','Y'),
('Red Chilli Whole','Red Chilli Whole','101093','N','Y','N','1','KG','160','160','Y'),
('Refined oil','Refined oil','101094','Y','N','N','1','L','70.7','70.7','Y'),
('Reynolds Ball Pen','Reynolds Ball Pen','101095','Y','N','N','1','PC','4.8','4.8','Y'),
('Ripple cup 250 ml','Ripple cup 250 ml','101096','N','Y','Y','1','PC','2.3','2.3','Y'),
('Ripple Cup 360 ml','Ripple Cup 360 ml','101097','N','Y','Y','1','PC','3.6','3.6','Y'),
('Ripple Cup lid 250 ml','Ripple Cup lid 250 ml','101098','N','Y','Y','1','PC','1.1','1.1','Y'),
('Ripple Cup lid 360 ml','Ripple Cup lid 360 ml','101099','N','Y','Y','1','PC','1.3','1.3','Y'),
('Rosemary','Rosemary','101100','N','Y','N','1','KG','392','392','Y'),
('RTM Cardboard Box - Green - Cafe','RTM Cardboard Box - Green - Cafe','101101','Y','N','N','1','PC','10','10','Y'),
('RTM Premix - RTM 001','RTM Premix - RTM 001','101102','Y','N','N','1','KG','480','480','Y'),
('RTM premix-RTM 004-High Sugar-Orange-IRCTC','RTM premix-RTM 004-High Sugar-Orange-IRCTC','101103','Y','N','N','1','KG','234','234','Y'),
('RTM premix-RTM 004-Low Sugar-Orange-IRCTC inactive','RTM premix-RTM 004-Low Sugar-Orange-IRCTC inactive','101104','Y','N','N','1','KG','234.6','234.6','Y'),
('RTM premix-rtm 004-low sugar-orange-irctc new','RTM premix-rtm 004-low sugar-orange-irctc new','101105','Y','N','N','1','KG','234.6','234.6','Y'),
('RTM Sachet - RTM001- 22 gm Green Spice Jet','RTM Sachet - RTM001- 22 gm Green Spice Jet','101106','Y','N','N','1','PC','15','15','Y'),
('RTM Sachet-RTM 004-High Sugar-Orange-IRCTC','RTM Sachet-RTM 004-High Sugar-Orange-IRCTC','101107','Y','N','N','1','PC','5','5','Y'),
('RTM Sachet-RTM 004-low sugar-orange-irctc','RTM Sachet-RTM 004-low sugar-orange-irctc','101108','Y','N','N','1','PC','5','5','Y'),
('RTM Wrapper- 22 gm Green Spice Jet','RTM Wrapper- 22 gm Green Spice Jet','101109','Y','N','N','1','KG','265','265','Y'),
('RTM wrapper-high sugar-irctc','RTM wrapper-high sugar-irctc','101110','Y','N','N','1','KG','213','213','Y'),
('RTM wrapper-low sugar-irctc','RTM wrapper-low sugar-irctc','101111','Y','N','N','1','KG','213','213','Y'),
('Salt','Salt','101112','N','Y','N','1','KG','16','16','Y'),
('Samak rice','Samak rice','101113','N','Y','N','1','KG','95','95','Y'),
('Sewaiya - Vermicili R pure','Sewaiya - Vermicili R pure','101114','N','Y','N','1','KG','120','120','Y'),
('Silver Laminate Pouch','Silver Laminate Pouch','101115','Y','N','N','1','PC','1.6','1.6','Y'),
('Soft care Gel','Soft care Gel','101116','Y','N','N','1','PC','175.1','175.1','Y'),
('Sonth','Sonth','101117','N','Y','N','1','KG','530','530','Y'),
('SOP book Training','SOP book Training','101118','Y','N','N','1','PC','250','250','Y'),
('Soyabean','Soyabean','101119','N','Y','N','1','KG','185','185','Y'),
('spatula','spatula','101120','Y','N','N','1','PC','43','43','Y'),
('Spinach','Spinach','101121','Y','N','N','1','KG','20','20','Y'),
('spinach blanched/ chopped','spinach blanched/ chopped','101122','N','Y','N','1','KG','54.1','54.1','Y'),
('Spinach Julienne','Spinach Julienne','101123','N','Y','N','1','KG','50','50','Y'),
('Strawberry','Strawberry','101124','Y','N','N','1','KG','98','98','Y'),
('Sweet Corn','Sweet Corn','101125','N','Y','N','1','KG','67','67','Y'),
('Tea kettle 15 ltr','Tea kettle 15 ltr','101126','Y','N','N','1','PC','1895','1895','Y'),
('TEA POUCH 1 LTR - SMALL SPOUT','TEA POUCH 1 LTR - SMALL SPOUT','101127','Y','Y','N','1','PC','9.5','9.5','Y'),
('TEA POUCH 400 ml - SMALL SPOUT','TEA POUCH 400 ml - SMALL SPOUT','101128','Y','Y','N','1','PC','6.7','6.7','Y'),
('Tik Tak Katori','Tik Tak Katori','101129','Y','N','N','1','PC','30.6','30.6','Y'),
('Tomato cube 15X15mm','Tomato cube 15X15mm','101130','N','Y','N','1','KG','61','61','Y'),
('TSC ttp 244 pro barcode printer','TSC ttp 244 pro barcode printer','101131','Y','N','N','1','PC','12450','12450','Y'),
('Unidet','Unidet','101132','Y','N','N','1','L','60','60','Y'),
('UPS APC BX6000','UPS APC BX6000','101133','Y','N','N','1','PC','1955','1955','Y'),
('Vim bar','Vim bar','101134','Y','N','N','1','PC','10','10','Y'),
('VINEGAR TOPS 610 ML','VINEGAR TOPS 610 ML','101135','Y','N','N','1','PC','32.5','32.5','Y'),
('Water Tank 100 L','Water Tank 100 L','100698','Y','N','N','1','PC','1000','1000','Y'),
('weighing machine charger','weighing machine charger','101136','Y','N','N','1','PC','550','550','Y'),
('Yellow Peas','Yellow Peas','101137','N','Y','N','1','KG','45','45','Y'),
('Zip lock pouches','Zip lock pouches','101138','Y','N','N','1','PC','0.4','0.4','Y')


UPDATE SKU_DEFINITION SET CREATED_BY  = 120458, CREATION_DATE = current_timestamp(), SKU_STATUS = 'ACTIVE' WHERE CREATED_BY = 0;



INSERT INTO SKU_PACKAGING_MAPPING (SKU_ID,PACKAGING_ID,MAPPING_STATUS,IS_DEFAULT)
VALUES 
('1044','10','ACTIVE','N'),
('1047','59','ACTIVE','N'),
('1048','151','ACTIVE','N'),
('1051','59','ACTIVE','N'),
('1057','61','ACTIVE','N'),
('1058','43','ACTIVE','N'),
('1059','152','ACTIVE','N'),
('1060','59','ACTIVE','N'),
('1062','59','ACTIVE','N'),
('1065','59','ACTIVE','N'),
('1066','4','ACTIVE','N'),
('1067','50','ACTIVE','N'),
('1070','59','ACTIVE','N'),
('1075','59','ACTIVE','N'),
('1079','59','ACTIVE','N'),
('1080','59','ACTIVE','N'),
('1091','50','ACTIVE','N'),
('1093','44','ACTIVE','N'),
('1094','59','ACTIVE','N'),
('1097','43','ACTIVE','N'),
('1098','50','ACTIVE','N'),
('1101','50','ACTIVE','N'),
('1102','59','ACTIVE','N'),
('1104','11','ACTIVE','N'),
('1105','59','ACTIVE','N'),
('1106','50','ACTIVE','N'),
('1108','50','ACTIVE','N'),
('1111','10','ACTIVE','N'),
('1118','43','ACTIVE','N'),
('1119','59','ACTIVE','N'),
('1121','59','ACTIVE','N'),
('1123','93','ACTIVE','N'),
('1124','4','ACTIVE','N'),
('1126','59','ACTIVE','N'),
('1128','50','ACTIVE','N'),
('1129','59','ACTIVE','N'),
('1132','124','ACTIVE','N'),
('1133','50','ACTIVE','N'),
('1135','153','ACTIVE','N'),
('1136','153','ACTIVE','N'),
('1137','153','ACTIVE','N'),
('1138','153','ACTIVE','N'),
('1139','153','ACTIVE','N'),
('1140','153','ACTIVE','N'),
('1143','152','ACTIVE','N'),
('1145','59','ACTIVE','N'),
('1146','35','ACTIVE','N'),
('1148','59','ACTIVE','N'),
('1152','124','ACTIVE','N'),
('1153','152','ACTIVE','N'),
('1158','59','ACTIVE','N'),
('1159','44','ACTIVE','N'),
('1161','49','ACTIVE','N'),
('1162','50','ACTIVE','N'),
('1165','10','ACTIVE','N'),
('1167','50','ACTIVE','N'),
('1168','59','ACTIVE','N'),
('1174','59','ACTIVE','N'),
('1178','59','ACTIVE','N'),
('1179','59','ACTIVE','N'),
('1182','10','ACTIVE','N'),
('1183','10','ACTIVE','N'),
('1184','10','ACTIVE','N'),
('1185','10','ACTIVE','N'),
('1186','59','ACTIVE','N'),
('1198','59','ACTIVE','N'),
('1199','59','ACTIVE','N'),
('1200','47','ACTIVE','N'),
('1203','59','ACTIVE','N'),
('1205','59','ACTIVE','N'),
('1208','50','ACTIVE','N'),
('1209','59','ACTIVE','N'),
('1211','59','ACTIVE','N'),
('1213','18','ACTIVE','N'),
('1214','18','ACTIVE','N'),
('1216','59','ACTIVE','N'),
('1224','59','ACTIVE','N');


INSERT INTO SKU_PACKAGING_MAPPING (SKU_ID,PACKAGING_ID,MAPPING_STATUS,IS_DEFAULT)
VALUES 
('1124','23','ACTIVE','N'),
('1165','154','ACTIVE','N'),
('1182','28','ACTIVE','N'),
('1183','28','ACTIVE','N'),
('1184','28','ACTIVE','N'),
('1185','28','ACTIVE','N');

UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='MONDAY 1' WHERE `LIST_ID`='2';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='TUESDAY 1' WHERE `LIST_ID`='3';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='WEDNESDAY 1' WHERE `LIST_ID`='4';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='THURSDAY 1' WHERE `LIST_ID`='5';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='FRIDAY 1' WHERE `LIST_ID`='6';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='SATURDAY 1' WHERE `LIST_ID`='7';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='SUNDAY 1' WHERE `LIST_ID`='8';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='MONDAY 2' WHERE `LIST_ID`='9';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='TUESDAY 2' WHERE `LIST_ID`='10';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='WEDNESDAY 2' WHERE `LIST_ID`='11';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='THURSDAY 2' WHERE `LIST_ID`='12';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='FRIDAY 2' WHERE `LIST_ID`='13';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='SATURDAY 2' WHERE `LIST_ID`='14';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='SUNDAY 2' WHERE `LIST_ID`='15';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='NO LIST' WHERE `LIST_ID`='1';
UPDATE `KETTLE_SCM`.`INVENTORY_LISTS` SET `LIST_NAME`='ALL DAYS' WHERE `LIST_ID`='16';

UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 2 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 3 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 4 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 5 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 6 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 7 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 8 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 9 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 12 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 13 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 14 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 15 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 16 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 17 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 18 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 19 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 20 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 21 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 22 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 23 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 26 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 27 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 28 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 29 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 30 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 31 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 32 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 34 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 35 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 36 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 38 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 39 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 40 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 41 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 42 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 43 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 44 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 45 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 46 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 47 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 48 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 49 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 50 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 51 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 52 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 53 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 54 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 55 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 56 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 57 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 58 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 59 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 60 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 61 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 62 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 63 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 64 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 65 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 66 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 67 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 68 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 70 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 71 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 72 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 75 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 76 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 77 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 78 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 79 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 81 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 83 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 84 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 85 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 86 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 87 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 88 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 89 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 90 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 91 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 92 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 93 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 94 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 95 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 96 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 97 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 98 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 99 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 100 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 101 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 102 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 103 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 104 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 105 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 106 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 107 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 108 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 110 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 111 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 112 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 113 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 114 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 115 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 116 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 117 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 118 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 119 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 120 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 121 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 123 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 124 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 125 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 126 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 127 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 128 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 129 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 130 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 131 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 132 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 133 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 134 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 135 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 136 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 137 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 138 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 139 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 140 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 141 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 142 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 143 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 144 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 145 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 146 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 147 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 148 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 149 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 150 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 151 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 152 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 153 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 154 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 155 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 157 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 158 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 161 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 162 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 164 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 165 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 166 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 168 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 169 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 171 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 172 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 173 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 174 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 175 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 176 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 177 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 178 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 179 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 180 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 181 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 183 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 184 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 185 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 186 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 187 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 188 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 191 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 193 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 194 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 195 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 196 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 197 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 198 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 199 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 200 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 201 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 202 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 203 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 204 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 206 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 208 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 209 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 210 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 211 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 212 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 213 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 214 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 215 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 216 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 217 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 218 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 219 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 220 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 221 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 222 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 223 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 224 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 225 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 226 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 227 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 228 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 229 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 230 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 231 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 232 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 233 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 234 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 235 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 237 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 238 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 239 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 241 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 242 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 243 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 244 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 245 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 246 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 247 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 248 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 249 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 251 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 252 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 253 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 255 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 256 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 258 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 259 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 260 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 261 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 262 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 264 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 265 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 266 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 267 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 268 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 269 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 270 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 271 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 272 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 273 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 274 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 275 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 277 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 278 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 279 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 280 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 281 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 282 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 283 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 284 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 285 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 286 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 287 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 288 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 289 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 290 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 291 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 292 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 293 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 294 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 295 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 296 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 297 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 298 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 299 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 300 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 301 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 302 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 303 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 304 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 305 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 306 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 307 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 308 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 309 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 310 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 311 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 312 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 313 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 314 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 315 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 316 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 317 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 318 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 319 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 320 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 321 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 322 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 323 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 324 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 325 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 326 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 327 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 328 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 329 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 330 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 331 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 332 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 333 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 334 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 335 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 336 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 337 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 338 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 339 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 340 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 341 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 342 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 343 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 344 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 345 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 346 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 347 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 348 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 349 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 350 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 351 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 352 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 353 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 354 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 355 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 356 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 357 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 358 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 359 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 360 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 361 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 362 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 363 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 364 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 365 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 366 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 367 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 368 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 369 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 370 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 371 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 372 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 373 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 374 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 375 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 376 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 377 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 378 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 379 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 380 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 381 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 382 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 383 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 384 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 385 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 386 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 387 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 388 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 389 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 390 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 391 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 392 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 393 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 394 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 395 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 399 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 400 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 401 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 402 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 403 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 404 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 405 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 406 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 407 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 408 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 409 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 410 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 411 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 412 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 413 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 414 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 415 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 416 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 417 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 418 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 419 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 420 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 421 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 422 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 423 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 424 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 425 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 426 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 427 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 428 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 429 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 430 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 431 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 432 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 433 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 434 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 435 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 436 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 437 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 438 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 439 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 440 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 441 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 442 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 443 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 444 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 445 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 446 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 447 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 448 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 449 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 450 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 451 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 452 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 453 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 454 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 455 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 456 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 457 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 458 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 459 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 460 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 461 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 462 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 463 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 464 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 465 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 466 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 467 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 468 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 469 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 470 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 471 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 472 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 473 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 474 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 475 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 476 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 477 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 478 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 479 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 480 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 481 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 483 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 484 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 485 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 486 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 487 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 488 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 489 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 490 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 491 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 492 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 493 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 494 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 495 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 496 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 497 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 498 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 499 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 500 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 501 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 502 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 503 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 504 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 505 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 506 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 507 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 508 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 509 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 510 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 511 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 512 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 514 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 515 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 516 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 517 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 518 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 519 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 520 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 521 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 522 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 523 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 524 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 525 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 526 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 527 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 528 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 529 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 530 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 533 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 534 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 535 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 536 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 537 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 538 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 539 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 540 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 541 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 542 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 543 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 544 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 545 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 546 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 547 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 548 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 549 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 550 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 551 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 552 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 553 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 554 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 555 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 556 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 559 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 560 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 561 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 562 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 563 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 564 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 565 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 566 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 567 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 568 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 569 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 570 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 571 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 572 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 573 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 574 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 575 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 576 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 577 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 578 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 579 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 580 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 581 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 582 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 583 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 584 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 585 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 586 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 587 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 588 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 589 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 590 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 591 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 592 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 593 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 594 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 595 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 596 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 597 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 598 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 599 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 600 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 602 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 603 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 605 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 606 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 607 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 608 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 612 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 613 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 614 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 615 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 616 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 617 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 620 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 621 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 622 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 623 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 624 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 625 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 626 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 627 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 628 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 630 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 631 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 632 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 633 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 634 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 635 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 636 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 638 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 639 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 640 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 641 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 642 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 643 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 644 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 645 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 647 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 648 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 649 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 650 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 652 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 653 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 654 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 655 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 656 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 658 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 659 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 660 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 661 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 662 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 663 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 664 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 665 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 666 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 667 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 668 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 669 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 670 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 671 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 674 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 675 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 676 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 680 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 681 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 682 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 683 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 684 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 685 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 686 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 687 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 688 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 695 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 696 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 697 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 698 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 699 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 700 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 701 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 702 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 703 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 705 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 707 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 712 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 713 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 715 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 716 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 717 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 718 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 719 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 721 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 723 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 724 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 725 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 726 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 727 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 730 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 731 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 732 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 733 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 734 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 735 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 736 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 737 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 738 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 739 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 740 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 741 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 742 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 744 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 745 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 746 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 747 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 748 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 749 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 750 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 751 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 752 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 760 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 766 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 770 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 771 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 772 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 773 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 774 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 775 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 779 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 780 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 781 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 782 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 783 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 784 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 785 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 786 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 788 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 789 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 790 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 791 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 793 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 794 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 795 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 796 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 801 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 802 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 803 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 804 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 805 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 806 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 807 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 808 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 809 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 810 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 813 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 815 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 816 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 817 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 818 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 819 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 820 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 821 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 822 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 823 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 824 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 825 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 826 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 827 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 828 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 829 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 830 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 831 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 832 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 833 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 834 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 836 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 838 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 840 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 841 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 842 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 848 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 849 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 850 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 851 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 852 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 853 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 854 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 855 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 856 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 857 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 858 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 859 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 860 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 861 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 863 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 865 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 866 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 867 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 868 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 869 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 870 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 871 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 872 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 873 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 874 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 875 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 876 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 877 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 879 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 880 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 881 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 882 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 883 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 884 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 886 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 887 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 888 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 889 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 890 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 891 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 892 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 893 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 895 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 896 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 897 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 898 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 900 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 901 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 902 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 903 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 904 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 906 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 907 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 908 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 909 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 910 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 911 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 913 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 914 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 915 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 916 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 917 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 918 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 919 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 920 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 921 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 922 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 923 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 924 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 925 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 926 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 927 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 928 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 929 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 931 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 932 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 933 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 934 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 936 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 937 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 938 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 939 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 940 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 942 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 943 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 944 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 945 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 947 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 948 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 949 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 950 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 951 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 952 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 953 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 954 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 958 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 959 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 960 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 961 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 963 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 964 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 965 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 966 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 967 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 968 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 969 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 970 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 971 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 972 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 973 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 974 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 975 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 976 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 977 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 978 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 979 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 980 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 981 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 982 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 983 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 984 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 985 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 986 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 987 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 988 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 989 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 990 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 991 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 992 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 995 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 996 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 997 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 998 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 999 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1000 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1001 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1002 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1003 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1004 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1005 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1006 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1007 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1008 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1009 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1010 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1011 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1012 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1013 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1014 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1015 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1016 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 1017 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1018 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1019 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1020 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1021 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 1022 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 1023 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1024 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1025 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1026 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1027 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 1028 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1029 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1030 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1031 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1032 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1033 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 1035 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1036 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1039 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1040 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1041 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1042 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1043 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1044 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1045 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 1046 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 1047 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 1048 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1049 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1050 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 1051 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1052 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1053 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1054 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1055 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1056 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1057 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1058 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1059 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1060 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 1061 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1062 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 1063 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1064 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1065 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1066 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1067 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1068 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1069 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1070 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1071 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1072 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1073 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1074 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1075 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1076 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1077 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1078 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1079 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 1080 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1081 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1082 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1083 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1084 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1085 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1086 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1087 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 1088 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1089 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 1090 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1091 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1092 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1093 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1094 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1095 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1096 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1097 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1098 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1099 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1100 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1101 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 1102 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1103 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1104 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 1105 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1106 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1107 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1108 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 10 WHERE SKU_ID = 1109 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1110 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1111 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1112 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1113 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1114 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1115 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 1116 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 1117 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1118 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1119 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 1120 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1121 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 1122 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1123 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1124 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1125 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1126 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1127 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1128 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1129 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 1130 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1131 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1132 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1133 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1134 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1135 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1136 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1137 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1138 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1139 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1140 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1141 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1142 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1143 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1144 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1145 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 1146 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1147 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1148 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1149 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1150 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1151 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 5 WHERE SKU_ID = 1152 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1153 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 1154 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1155 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1156 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1157 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1158 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1159 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1160 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1161 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1162 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 1163 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1164 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1165 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 2 WHERE SKU_ID = 1166 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1167 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1168 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 1169 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1170 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1171 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1172 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1173 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1174 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1175 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1176 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1177 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1178 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1179 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1180 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1181 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1182 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1183 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1184 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1185 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1186 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1187 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 1188 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 1189 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 1 WHERE SKU_ID = 1190 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 4 WHERE SKU_ID = 1191 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1192 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1193 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1194 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1195 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1196 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1197 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1198 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1199 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1200 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1201 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 1202 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1203 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1204 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1205 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 7 WHERE SKU_ID = 1206 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1207 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1208 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1209 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1210 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1211 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1212 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1213 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 9 WHERE SKU_ID = 1214 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 15 WHERE SKU_ID = 1215 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1216 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 1217 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1218 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 14 WHERE SKU_ID = 1219 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 12 WHERE SKU_ID = 1220 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 6 WHERE SKU_ID = 1221 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 11 WHERE SKU_ID = 1222 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 13 WHERE SKU_ID = 1223 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 8 WHERE SKU_ID = 1224 ;
UPDATE KETTLE_SCM.SKU_DEFINITION SET INVENTORY_LIST_ID = 3 WHERE SKU_ID = 1225 ;


INSERT INTO UNIT_SKU_MAPPING (SKU_ID,UNIT_ID,MAPPING_STATUS,CREATED_BY,CREATED_AT)
VALUES
('112','24001','ACTIVE','120503',current_timestamp),
('153','24001','ACTIVE','120503',current_timestamp),
('260','24001','ACTIVE','120503',current_timestamp),
('615','24001','ACTIVE','120503',current_timestamp),
('834','24001','ACTIVE','120503',current_timestamp),
('836','24001','ACTIVE','120503',current_timestamp),
('929','24001','ACTIVE','120503',current_timestamp),
('145','24001','ACTIVE','120503',current_timestamp),
('1028','24001','ACTIVE','120503',current_timestamp),
('810','24001','ACTIVE','120503',current_timestamp),
('234','24001','ACTIVE','120503',current_timestamp),
('890','24001','ACTIVE','120503',current_timestamp),
('54','24001','ACTIVE','120503',current_timestamp),
('823','24001','ACTIVE','120503',current_timestamp),
('1035','24001','ACTIVE','120503',current_timestamp),
('1080','24001','ACTIVE','120503',current_timestamp),
('1105','24001','ACTIVE','120503',current_timestamp),
('1166','24001','ACTIVE','120503',current_timestamp),
('1102','24001','ACTIVE','120503',current_timestamp),
('1146','24001','ACTIVE','120503',current_timestamp),
('59','24001','ACTIVE','120503',current_timestamp),
('91','24001','ACTIVE','120503',current_timestamp),
('130','24001','ACTIVE','120503',current_timestamp),
('174','24001','ACTIVE','120503',current_timestamp),
('183','24001','ACTIVE','120503',current_timestamp),
('275','24001','ACTIVE','120503',current_timestamp),
('311','24001','ACTIVE','120503',current_timestamp),
('348','24001','ACTIVE','120503',current_timestamp),
('361','24001','ACTIVE','120503',current_timestamp),
('1044','24001','ACTIVE','120503',current_timestamp),
('17','24001','ACTIVE','120503',current_timestamp),
('22','24001','ACTIVE','120503',current_timestamp),
('34','24001','ACTIVE','120503',current_timestamp),
('35','24001','ACTIVE','120503',current_timestamp),
('67','24001','ACTIVE','120503',current_timestamp),
('68','24001','ACTIVE','120503',current_timestamp),
('76','24001','ACTIVE','120503',current_timestamp),
('79','24001','ACTIVE','120503',current_timestamp),
('87','24001','ACTIVE','120503',current_timestamp),
('92','24001','ACTIVE','120503',current_timestamp),
('180','24001','ACTIVE','120503',current_timestamp),
('196','24001','ACTIVE','120503',current_timestamp),
('210','24001','ACTIVE','120503',current_timestamp),
('301','24001','ACTIVE','120503',current_timestamp),
('332','24001','ACTIVE','120503',current_timestamp),
('372','24001','ACTIVE','120503',current_timestamp),
('771','24001','ACTIVE','120503',current_timestamp),
('772','24001','ACTIVE','120503',current_timestamp),
('1047','24001','ACTIVE','120503',current_timestamp),
('1048','24001','ACTIVE','120503',current_timestamp),
('1051','24001','ACTIVE','120503',current_timestamp),
('1057','24001','ACTIVE','120503',current_timestamp),
('1059','24001','ACTIVE','120503',current_timestamp),
('1060','24001','ACTIVE','120503',current_timestamp),
('1065','24001','ACTIVE','120503',current_timestamp),
('1067','24001','ACTIVE','120503',current_timestamp),
('1079','24001','ACTIVE','120503',current_timestamp),
('1093','24001','ACTIVE','120503',current_timestamp),
('1094','24001','ACTIVE','120503',current_timestamp),
('1098','24001','ACTIVE','120503',current_timestamp),
('1101','24001','ACTIVE','120503',current_timestamp),
('1106','24001','ACTIVE','120503',current_timestamp),
('1121','24001','ACTIVE','120503',current_timestamp),
('1123','24001','ACTIVE','120503',current_timestamp),
('1126','24001','ACTIVE','120503',current_timestamp),
('1128','24001','ACTIVE','120503',current_timestamp),
('1129','24001','ACTIVE','120503',current_timestamp),
('1132','24001','ACTIVE','120503',current_timestamp),
('1133','24001','ACTIVE','120503',current_timestamp),
('1143','24001','ACTIVE','120503',current_timestamp),
('1152','24001','ACTIVE','120503',current_timestamp),
('1153','24001','ACTIVE','120503',current_timestamp),
('1159','24001','ACTIVE','120503',current_timestamp),
('1161','24001','ACTIVE','120503',current_timestamp),
('1167','24001','ACTIVE','120503',current_timestamp),
('1168','24001','ACTIVE','120503',current_timestamp),
('1178','24001','ACTIVE','120503',current_timestamp),
('1179','24001','ACTIVE','120503',current_timestamp),
('1180','24001','ACTIVE','120503',current_timestamp),
('1186','24001','ACTIVE','120503',current_timestamp),
('1198','24001','ACTIVE','120503',current_timestamp),
('1199','24001','ACTIVE','120503',current_timestamp),
('1200','24001','ACTIVE','120503',current_timestamp),
('1203','24001','ACTIVE','120503',current_timestamp),
('1205','24001','ACTIVE','120503',current_timestamp),
('1221','24001','ACTIVE','120503',current_timestamp),
('1055','24001','ACTIVE','120503',current_timestamp),
('1084','24001','ACTIVE','120503',current_timestamp),
('1144','24001','ACTIVE','120503',current_timestamp),
('1210','24001','ACTIVE','120503',current_timestamp),
('1081','24001','ACTIVE','120503',current_timestamp),
('1082','24001','ACTIVE','120503',current_timestamp),
('1083','24001','ACTIVE','120503',current_timestamp),
('1104','24001','ACTIVE','120503',current_timestamp),
('1148','24001','ACTIVE','120503',current_timestamp),
('1160','24001','ACTIVE','120503',current_timestamp),
('1068','24001','ACTIVE','120503',current_timestamp),
('1069','24001','ACTIVE','120503',current_timestamp),
('1155','24001','ACTIVE','120503',current_timestamp),
('1156','24001','ACTIVE','120503',current_timestamp),
('123','24001','ACTIVE','120503',current_timestamp),
('171','24001','ACTIVE','120503',current_timestamp),
('218','24001','ACTIVE','120503',current_timestamp),
('373','24001','ACTIVE','120503',current_timestamp),
('65','24001','ACTIVE','120503',current_timestamp),
('113','24001','ACTIVE','120503',current_timestamp),
('168','24001','ACTIVE','120503',current_timestamp),
('206','24001','ACTIVE','120503',current_timestamp),
('238','24001','ACTIVE','120503',current_timestamp),
('255','24001','ACTIVE','120503',current_timestamp),
('364','24001','ACTIVE','120503',current_timestamp),
('520','24001','ACTIVE','120503',current_timestamp),
('521','24001','ACTIVE','120503',current_timestamp),
('522','24001','ACTIVE','120503',current_timestamp),
('523','24001','ACTIVE','120503',current_timestamp),
('524','24001','ACTIVE','120503',current_timestamp),
('1058','24001','ACTIVE','120503',current_timestamp),
('1062','24001','ACTIVE','120503',current_timestamp),
('1070','24001','ACTIVE','120503',current_timestamp),
('1075','24001','ACTIVE','120503',current_timestamp),
('1085','24001','ACTIVE','120503',current_timestamp),
('1091','24001','ACTIVE','120503',current_timestamp),
('1092','24001','ACTIVE','120503',current_timestamp),
('1097','24001','ACTIVE','120503',current_timestamp),
('1107','24001','ACTIVE','120503',current_timestamp),
('1108','24001','ACTIVE','120503',current_timestamp),
('1118','24001','ACTIVE','120503',current_timestamp),
('1119','24001','ACTIVE','120503',current_timestamp),
('1145','24001','ACTIVE','120503',current_timestamp),
('1158','24001','ACTIVE','120503',current_timestamp),
('1162','24001','ACTIVE','120503',current_timestamp),
('1173','24001','ACTIVE','120503',current_timestamp),
('1174','24001','ACTIVE','120503',current_timestamp),
('1207','24001','ACTIVE','120503',current_timestamp),
('1208','24001','ACTIVE','120503',current_timestamp),
('1209','24001','ACTIVE','120503',current_timestamp),
('1211','24001','ACTIVE','120503',current_timestamp),
('1216','24001','ACTIVE','120503',current_timestamp),
('813','24001','ACTIVE','120503',current_timestamp),
('1224','24001','ACTIVE','120503',current_timestamp),
('13','24001','ACTIVE','120503',current_timestamp),
('46','24001','ACTIVE','120503',current_timestamp),
('200','24001','ACTIVE','120503',current_timestamp),
('232','24001','ACTIVE','120503',current_timestamp),
('233','24001','ACTIVE','120503',current_timestamp),
('1086','24001','ACTIVE','120503',current_timestamp),
('1066','24001','ACTIVE','120503',current_timestamp),
('1071','24001','ACTIVE','120503',current_timestamp),
('1073','24001','ACTIVE','120503',current_timestamp),
('1074','24001','ACTIVE','120503',current_timestamp),
('1078','24001','ACTIVE','120503',current_timestamp),
('1134','24001','ACTIVE','120503',current_timestamp),
('1135','24001','ACTIVE','120503',current_timestamp),
('1136','24001','ACTIVE','120503',current_timestamp),
('1137','24001','ACTIVE','120503',current_timestamp),
('1138','24001','ACTIVE','120503',current_timestamp),
('1139','24001','ACTIVE','120503',current_timestamp),
('1140','24001','ACTIVE','120503',current_timestamp),
('1165','24001','ACTIVE','120503',current_timestamp),
('1171','24001','ACTIVE','120503',current_timestamp),
('1201','24001','ACTIVE','120503',current_timestamp),
('1214','24001','ACTIVE','120503',current_timestamp),
('45','24001','ACTIVE','120503',current_timestamp),
('100','24001','ACTIVE','120503',current_timestamp),
('102','24001','ACTIVE','120503',current_timestamp),
('103','24001','ACTIVE','120503',current_timestamp),
('106','24001','ACTIVE','120503',current_timestamp),
('107','24001','ACTIVE','120503',current_timestamp),
('150','24001','ACTIVE','120503',current_timestamp),
('151','24001','ACTIVE','120503',current_timestamp),
('313','24001','ACTIVE','120503',current_timestamp),
('323','24001','ACTIVE','120503',current_timestamp),
('324','24001','ACTIVE','120503',current_timestamp),
('336','24001','ACTIVE','120503',current_timestamp),
('337','24001','ACTIVE','120503',current_timestamp),
('353','24001','ACTIVE','120503',current_timestamp),
('387','24001','ACTIVE','120503',current_timestamp),
('717','24001','ACTIVE','120503',current_timestamp),
('734','24001','ACTIVE','120503',current_timestamp),
('888','24001','ACTIVE','120503',current_timestamp),
('976','24001','ACTIVE','120503',current_timestamp),
('1109','24001','ACTIVE','120503',current_timestamp),
('503','24001','ACTIVE','120503',current_timestamp),
('891','24001','ACTIVE','120503',current_timestamp),
('1142','24001','ACTIVE','120503',current_timestamp),
('7','24001','ACTIVE','120503',current_timestamp),
('23','24001','ACTIVE','120503',current_timestamp),
('48','24001','ACTIVE','120503',current_timestamp),
('63','24001','ACTIVE','120503',current_timestamp),
('90','24001','ACTIVE','120503',current_timestamp),
('93','24001','ACTIVE','120503',current_timestamp),
('264','24001','ACTIVE','120503',current_timestamp),
('291','24001','ACTIVE','120503',current_timestamp),
('1056','24001','ACTIVE','120503',current_timestamp),
('1181','24001','ACTIVE','120503',current_timestamp),
('368','24001','ACTIVE','120503',current_timestamp),
('369','24001','ACTIVE','120503',current_timestamp),
('370','24001','ACTIVE','120503',current_timestamp),
('371','24001','ACTIVE','120503',current_timestamp),
('374','24001','ACTIVE','120503',current_timestamp),
('1076','24001','ACTIVE','120503',current_timestamp),
('30','24001','ACTIVE','120503',current_timestamp),
('47','24001','ACTIVE','120503',current_timestamp),
('118','24001','ACTIVE','120503',current_timestamp),
('137','24001','ACTIVE','120503',current_timestamp),
('139','24001','ACTIVE','120503',current_timestamp),
('154','24001','ACTIVE','120503',current_timestamp),
('173','24001','ACTIVE','120503',current_timestamp),
('175','24001','ACTIVE','120503',current_timestamp),
('178','24001','ACTIVE','120503',current_timestamp),
('252','24001','ACTIVE','120503',current_timestamp),
('265','24001','ACTIVE','120503',current_timestamp),
('269','24001','ACTIVE','120503',current_timestamp),
('288','24001','ACTIVE','120503',current_timestamp),
('289','24001','ACTIVE','120503',current_timestamp),
('295','24001','ACTIVE','120503',current_timestamp),
('304','24001','ACTIVE','120503',current_timestamp),
('320','24001','ACTIVE','120503',current_timestamp),
('321','24001','ACTIVE','120503',current_timestamp),
('326','24001','ACTIVE','120503',current_timestamp),
('338','24001','ACTIVE','120503',current_timestamp),
('339','24001','ACTIVE','120503',current_timestamp),
('362','24001','ACTIVE','120503',current_timestamp),
('363','24001','ACTIVE','120503',current_timestamp),
('386','24001','ACTIVE','120503',current_timestamp),
('392','24001','ACTIVE','120503',current_timestamp),
('539','24001','ACTIVE','120503',current_timestamp),
('563','24001','ACTIVE','120503',current_timestamp),
('696','24001','ACTIVE','120503',current_timestamp),
('1202','24001','ACTIVE','120503',current_timestamp),
('1220','24001','ACTIVE','120503',current_timestamp),
('239','24001','ACTIVE','120503',current_timestamp),
('385','24001','ACTIVE','120503',current_timestamp),
('1061','24001','ACTIVE','120503',current_timestamp),
('1223','24001','ACTIVE','120503',current_timestamp),
('1217','24001','ACTIVE','120503',current_timestamp),
('1088','24001','ACTIVE','120503',current_timestamp),
('1090','24001','ACTIVE','120503',current_timestamp),
('1116','24001','ACTIVE','120503',current_timestamp),
('547','24001','ACTIVE','120503',current_timestamp),
('741','24001','ACTIVE','120503',current_timestamp),
('990','24001','ACTIVE','120503',current_timestamp),
('991','24001','ACTIVE','120503',current_timestamp),
('992','24001','ACTIVE','120503',current_timestamp),
('2','24001','ACTIVE','120503',current_timestamp),
('26','24001','ACTIVE','120503',current_timestamp),
('56','24001','ACTIVE','120503',current_timestamp),
('81','24001','ACTIVE','120503',current_timestamp),
('142','24001','ACTIVE','120503',current_timestamp),
('169','24001','ACTIVE','120503',current_timestamp),
('177','24001','ACTIVE','120503',current_timestamp),
('179','24001','ACTIVE','120503',current_timestamp),
('181','24001','ACTIVE','120503',current_timestamp),
('193','24001','ACTIVE','120503',current_timestamp),
('195','24001','ACTIVE','120503',current_timestamp),
('201','24001','ACTIVE','120503',current_timestamp),
('216','24001','ACTIVE','120503',current_timestamp),
('251','24001','ACTIVE','120503',current_timestamp),
('273','24001','ACTIVE','120503',current_timestamp),
('290','24001','ACTIVE','120503',current_timestamp),
('308','24001','ACTIVE','120503',current_timestamp),
('315','24001','ACTIVE','120503',current_timestamp),
('331','24001','ACTIVE','120503',current_timestamp),
('376','24001','ACTIVE','120503',current_timestamp),
('608','24001','ACTIVE','120503',current_timestamp),
('612','24001','ACTIVE','120503',current_timestamp),
('613','24001','ACTIVE','120503',current_timestamp),
('807','24001','ACTIVE','120503',current_timestamp),
('924','24001','ACTIVE','120503',current_timestamp),
('925','24001','ACTIVE','120503',current_timestamp),
('926','24001','ACTIVE','120503',current_timestamp),
('927','24001','ACTIVE','120503',current_timestamp),
('928','24001','ACTIVE','120503',current_timestamp),
('986','24001','ACTIVE','120503',current_timestamp),
('987','24001','ACTIVE','120503',current_timestamp),
('996','24001','ACTIVE','120503',current_timestamp),
('997','24001','ACTIVE','120503',current_timestamp),
('1027','24001','ACTIVE','120503',current_timestamp),
('1029','24001','ACTIVE','120503',current_timestamp),
('1030','24001','ACTIVE','120503',current_timestamp),
('1031','24001','ACTIVE','120503',current_timestamp),
('1032','24001','ACTIVE','120503',current_timestamp),
('1033','24001','ACTIVE','120503',current_timestamp),
('1036','24001','ACTIVE','120503',current_timestamp),
('1039','24001','ACTIVE','120503',current_timestamp),
('988','24001','ACTIVE','120503',current_timestamp),
('989','24001','ACTIVE','120503',current_timestamp);


INSERT INTO UNIT_SKU_MAPPING (SKU_ID,UNIT_ID,MAPPING_STATUS,CREATED_BY,CREATED_AT)
VALUES
('112','24002','ACTIVE','120503',current_timestamp),
('153','24002','ACTIVE','120503',current_timestamp),
('260','24002','ACTIVE','120503',current_timestamp),
('615','24002','ACTIVE','120503',current_timestamp),
('834','24002','ACTIVE','120503',current_timestamp),
('836','24002','ACTIVE','120503',current_timestamp),
('929','24002','ACTIVE','120503',current_timestamp),
('145','24002','ACTIVE','120503',current_timestamp),
('810','24002','ACTIVE','120503',current_timestamp),
('234','24002','ACTIVE','120503',current_timestamp),
('890','24002','ACTIVE','120503',current_timestamp),
('54','24002','ACTIVE','120503',current_timestamp),
('823','24002','ACTIVE','120503',current_timestamp),
('1080','24002','ACTIVE','120503',current_timestamp),
('1105','24002','ACTIVE','120503',current_timestamp),
('1166','24002','ACTIVE','120503',current_timestamp),
('1102','24002','ACTIVE','120503',current_timestamp),
('1146','24002','ACTIVE','120503',current_timestamp),
('59','24002','ACTIVE','120503',current_timestamp),
('91','24002','ACTIVE','120503',current_timestamp),
('130','24002','ACTIVE','120503',current_timestamp),
('174','24002','ACTIVE','120503',current_timestamp),
('183','24002','ACTIVE','120503',current_timestamp),
('275','24002','ACTIVE','120503',current_timestamp),
('311','24002','ACTIVE','120503',current_timestamp),
('361','24002','ACTIVE','120503',current_timestamp),
('1044','24002','ACTIVE','120503',current_timestamp),
('17','24002','ACTIVE','120503',current_timestamp),
('22','24002','ACTIVE','120503',current_timestamp),
('34','24002','ACTIVE','120503',current_timestamp),
('35','24002','ACTIVE','120503',current_timestamp),
('67','24002','ACTIVE','120503',current_timestamp),
('68','24002','ACTIVE','120503',current_timestamp),
('76','24002','ACTIVE','120503',current_timestamp),
('79','24002','ACTIVE','120503',current_timestamp),
('87','24002','ACTIVE','120503',current_timestamp),
('92','24002','ACTIVE','120503',current_timestamp),
('180','24002','ACTIVE','120503',current_timestamp),
('196','24002','ACTIVE','120503',current_timestamp),
('210','24002','ACTIVE','120503',current_timestamp),
('301','24002','ACTIVE','120503',current_timestamp),
('332','24002','ACTIVE','120503',current_timestamp),
('372','24002','ACTIVE','120503',current_timestamp),
('771','24002','ACTIVE','120503',current_timestamp),
('772','24002','ACTIVE','120503',current_timestamp),
('1047','24002','ACTIVE','120503',current_timestamp),
('1048','24002','ACTIVE','120503',current_timestamp),
('1051','24002','ACTIVE','120503',current_timestamp),
('1057','24002','ACTIVE','120503',current_timestamp),
('1059','24002','ACTIVE','120503',current_timestamp),
('1060','24002','ACTIVE','120503',current_timestamp),
('1065','24002','ACTIVE','120503',current_timestamp),
('1067','24002','ACTIVE','120503',current_timestamp),
('1079','24002','ACTIVE','120503',current_timestamp),
('1093','24002','ACTIVE','120503',current_timestamp),
('1094','24002','ACTIVE','120503',current_timestamp),
('1101','24002','ACTIVE','120503',current_timestamp),
('1106','24002','ACTIVE','120503',current_timestamp),
('1121','24002','ACTIVE','120503',current_timestamp),
('1123','24002','ACTIVE','120503',current_timestamp),
('1126','24002','ACTIVE','120503',current_timestamp),
('1128','24002','ACTIVE','120503',current_timestamp),
('1129','24002','ACTIVE','120503',current_timestamp),
('1132','24002','ACTIVE','120503',current_timestamp),
('1133','24002','ACTIVE','120503',current_timestamp),
('1143','24002','ACTIVE','120503',current_timestamp),
('1153','24002','ACTIVE','120503',current_timestamp),
('1159','24002','ACTIVE','120503',current_timestamp),
('1161','24002','ACTIVE','120503',current_timestamp),
('1167','24002','ACTIVE','120503',current_timestamp),
('1168','24002','ACTIVE','120503',current_timestamp),
('1178','24002','ACTIVE','120503',current_timestamp),
('1179','24002','ACTIVE','120503',current_timestamp),
('1180','24002','ACTIVE','120503',current_timestamp),
('1186','24002','ACTIVE','120503',current_timestamp),
('1198','24002','ACTIVE','120503',current_timestamp),
('1199','24002','ACTIVE','120503',current_timestamp),
('1200','24002','ACTIVE','120503',current_timestamp),
('1203','24002','ACTIVE','120503',current_timestamp),
('1205','24002','ACTIVE','120503',current_timestamp),
('1221','24002','ACTIVE','120503',current_timestamp),
('1055','24002','ACTIVE','120503',current_timestamp),
('1084','24002','ACTIVE','120503',current_timestamp),
('1144','24002','ACTIVE','120503',current_timestamp),
('1210','24002','ACTIVE','120503',current_timestamp),
('1081','24002','ACTIVE','120503',current_timestamp),
('1082','24002','ACTIVE','120503',current_timestamp),
('1083','24002','ACTIVE','120503',current_timestamp),
('1104','24002','ACTIVE','120503',current_timestamp),
('1148','24002','ACTIVE','120503',current_timestamp),
('1160','24002','ACTIVE','120503',current_timestamp),
('1068','24002','ACTIVE','120503',current_timestamp),
('1069','24002','ACTIVE','120503',current_timestamp),
('1155','24002','ACTIVE','120503',current_timestamp),
('1156','24002','ACTIVE','120503',current_timestamp),
('123','24002','ACTIVE','120503',current_timestamp),
('171','24002','ACTIVE','120503',current_timestamp),
('218','24002','ACTIVE','120503',current_timestamp),
('373','24002','ACTIVE','120503',current_timestamp),
('65','24002','ACTIVE','120503',current_timestamp),
('113','24002','ACTIVE','120503',current_timestamp),
('168','24002','ACTIVE','120503',current_timestamp),
('206','24002','ACTIVE','120503',current_timestamp),
('238','24002','ACTIVE','120503',current_timestamp),
('255','24002','ACTIVE','120503',current_timestamp),
('364','24002','ACTIVE','120503',current_timestamp),
('520','24002','ACTIVE','120503',current_timestamp),
('521','24002','ACTIVE','120503',current_timestamp),
('522','24002','ACTIVE','120503',current_timestamp),
('523','24002','ACTIVE','120503',current_timestamp),
('524','24002','ACTIVE','120503',current_timestamp),
('1058','24002','ACTIVE','120503',current_timestamp),
('1062','24002','ACTIVE','120503',current_timestamp),
('1070','24002','ACTIVE','120503',current_timestamp),
('1075','24002','ACTIVE','120503',current_timestamp),
('1085','24002','ACTIVE','120503',current_timestamp),
('1091','24002','ACTIVE','120503',current_timestamp),
('1092','24002','ACTIVE','120503',current_timestamp),
('1097','24002','ACTIVE','120503',current_timestamp),
('1107','24002','ACTIVE','120503',current_timestamp),
('1108','24002','ACTIVE','120503',current_timestamp),
('1118','24002','ACTIVE','120503',current_timestamp),
('1119','24002','ACTIVE','120503',current_timestamp),
('1145','24002','ACTIVE','120503',current_timestamp),
('1157','24002','ACTIVE','120503',current_timestamp),
('1158','24002','ACTIVE','120503',current_timestamp),
('1162','24002','ACTIVE','120503',current_timestamp),
('1173','24002','ACTIVE','120503',current_timestamp),
('1174','24002','ACTIVE','120503',current_timestamp),
('1207','24002','ACTIVE','120503',current_timestamp),
('1208','24002','ACTIVE','120503',current_timestamp),
('1209','24002','ACTIVE','120503',current_timestamp),
('1211','24002','ACTIVE','120503',current_timestamp),
('1216','24002','ACTIVE','120503',current_timestamp),
('813','24002','ACTIVE','120503',current_timestamp),
('1224','24002','ACTIVE','120503',current_timestamp),
('13','24002','ACTIVE','120503',current_timestamp),
('46','24002','ACTIVE','120503',current_timestamp),
('200','24002','ACTIVE','120503',current_timestamp),
('232','24002','ACTIVE','120503',current_timestamp),
('233','24002','ACTIVE','120503',current_timestamp),
('1086','24002','ACTIVE','120503',current_timestamp),
('1066','24002','ACTIVE','120503',current_timestamp),
('1071','24002','ACTIVE','120503',current_timestamp),
('1073','24002','ACTIVE','120503',current_timestamp),
('1074','24002','ACTIVE','120503',current_timestamp),
('1078','24002','ACTIVE','120503',current_timestamp),
('1134','24002','ACTIVE','120503',current_timestamp),
('1135','24002','ACTIVE','120503',current_timestamp),
('1136','24002','ACTIVE','120503',current_timestamp),
('1137','24002','ACTIVE','120503',current_timestamp),
('1138','24002','ACTIVE','120503',current_timestamp),
('1139','24002','ACTIVE','120503',current_timestamp),
('1140','24002','ACTIVE','120503',current_timestamp),
('1165','24002','ACTIVE','120503',current_timestamp),
('1171','24002','ACTIVE','120503',current_timestamp),
('1201','24002','ACTIVE','120503',current_timestamp),
('45','24002','ACTIVE','120503',current_timestamp),
('100','24002','ACTIVE','120503',current_timestamp),
('102','24002','ACTIVE','120503',current_timestamp),
('103','24002','ACTIVE','120503',current_timestamp),
('106','24002','ACTIVE','120503',current_timestamp),
('107','24002','ACTIVE','120503',current_timestamp),
('150','24002','ACTIVE','120503',current_timestamp),
('151','24002','ACTIVE','120503',current_timestamp),
('313','24002','ACTIVE','120503',current_timestamp),
('323','24002','ACTIVE','120503',current_timestamp),
('336','24002','ACTIVE','120503',current_timestamp),
('337','24002','ACTIVE','120503',current_timestamp),
('353','24002','ACTIVE','120503',current_timestamp),
('387','24002','ACTIVE','120503',current_timestamp),
('717','24002','ACTIVE','120503',current_timestamp),
('734','24002','ACTIVE','120503',current_timestamp),
('976','24002','ACTIVE','120503',current_timestamp),
('1109','24002','ACTIVE','120503',current_timestamp),
('503','24002','ACTIVE','120503',current_timestamp),
('891','24002','ACTIVE','120503',current_timestamp),
('1142','24002','ACTIVE','120503',current_timestamp),
('7','24002','ACTIVE','120503',current_timestamp),
('23','24002','ACTIVE','120503',current_timestamp),
('48','24002','ACTIVE','120503',current_timestamp),
('63','24002','ACTIVE','120503',current_timestamp),
('90','24002','ACTIVE','120503',current_timestamp),
('93','24002','ACTIVE','120503',current_timestamp),
('264','24002','ACTIVE','120503',current_timestamp),
('291','24002','ACTIVE','120503',current_timestamp),
('1056','24002','ACTIVE','120503',current_timestamp),
('1181','24002','ACTIVE','120503',current_timestamp),
('368','24002','ACTIVE','120503',current_timestamp),
('369','24002','ACTIVE','120503',current_timestamp),
('370','24002','ACTIVE','120503',current_timestamp),
('371','24002','ACTIVE','120503',current_timestamp),
('374','24002','ACTIVE','120503',current_timestamp),
('1076','24002','ACTIVE','120503',current_timestamp),
('30','24002','ACTIVE','120503',current_timestamp),
('47','24002','ACTIVE','120503',current_timestamp),
('118','24002','ACTIVE','120503',current_timestamp),
('137','24002','ACTIVE','120503',current_timestamp),
('139','24002','ACTIVE','120503',current_timestamp),
('154','24002','ACTIVE','120503',current_timestamp),
('173','24002','ACTIVE','120503',current_timestamp),
('175','24002','ACTIVE','120503',current_timestamp),
('178','24002','ACTIVE','120503',current_timestamp),
('252','24002','ACTIVE','120503',current_timestamp),
('265','24002','ACTIVE','120503',current_timestamp),
('269','24002','ACTIVE','120503',current_timestamp),
('288','24002','ACTIVE','120503',current_timestamp),
('289','24002','ACTIVE','120503',current_timestamp),
('295','24002','ACTIVE','120503',current_timestamp),
('304','24002','ACTIVE','120503',current_timestamp),
('320','24002','ACTIVE','120503',current_timestamp),
('321','24002','ACTIVE','120503',current_timestamp),
('326','24002','ACTIVE','120503',current_timestamp),
('338','24002','ACTIVE','120503',current_timestamp),
('339','24002','ACTIVE','120503',current_timestamp),
('362','24002','ACTIVE','120503',current_timestamp),
('363','24002','ACTIVE','120503',current_timestamp),
('386','24002','ACTIVE','120503',current_timestamp),
('392','24002','ACTIVE','120503',current_timestamp),
('539','24002','ACTIVE','120503',current_timestamp),
('563','24002','ACTIVE','120503',current_timestamp),
('696','24002','ACTIVE','120503',current_timestamp),
('1202','24002','ACTIVE','120503',current_timestamp),
('1220','24002','ACTIVE','120503',current_timestamp),
('239','24002','ACTIVE','120503',current_timestamp),
('385','24002','ACTIVE','120503',current_timestamp),
('1223','24002','ACTIVE','120503',current_timestamp),
('1217','24002','ACTIVE','120503',current_timestamp),
('1088','24002','ACTIVE','120503',current_timestamp),
('1090','24002','ACTIVE','120503',current_timestamp),
('1116','24002','ACTIVE','120503',current_timestamp),
('547','24002','ACTIVE','120503',current_timestamp),
('741','24002','ACTIVE','120503',current_timestamp),
('990','24002','ACTIVE','120503',current_timestamp),
('991','24002','ACTIVE','120503',current_timestamp),
('992','24002','ACTIVE','120503',current_timestamp),
('2','24002','ACTIVE','120503',current_timestamp),
('26','24002','ACTIVE','120503',current_timestamp),
('56','24002','ACTIVE','120503',current_timestamp),
('81','24002','ACTIVE','120503',current_timestamp),
('142','24002','ACTIVE','120503',current_timestamp),
('169','24002','ACTIVE','120503',current_timestamp),
('177','24002','ACTIVE','120503',current_timestamp),
('179','24002','ACTIVE','120503',current_timestamp),
('181','24002','ACTIVE','120503',current_timestamp),
('193','24002','ACTIVE','120503',current_timestamp),
('195','24002','ACTIVE','120503',current_timestamp),
('201','24002','ACTIVE','120503',current_timestamp),
('216','24002','ACTIVE','120503',current_timestamp),
('251','24002','ACTIVE','120503',current_timestamp),
('273','24002','ACTIVE','120503',current_timestamp),
('290','24002','ACTIVE','120503',current_timestamp),
('308','24002','ACTIVE','120503',current_timestamp),
('315','24002','ACTIVE','120503',current_timestamp),
('331','24002','ACTIVE','120503',current_timestamp),
('376','24002','ACTIVE','120503',current_timestamp),
('608','24002','ACTIVE','120503',current_timestamp),
('612','24002','ACTIVE','120503',current_timestamp),
('613','24002','ACTIVE','120503',current_timestamp),
('807','24002','ACTIVE','120503',current_timestamp),
('924','24002','ACTIVE','120503',current_timestamp),
('925','24002','ACTIVE','120503',current_timestamp),
('926','24002','ACTIVE','120503',current_timestamp),
('927','24002','ACTIVE','120503',current_timestamp),
('928','24002','ACTIVE','120503',current_timestamp),
('986','24002','ACTIVE','120503',current_timestamp),
('987','24002','ACTIVE','120503',current_timestamp),
('996','24002','ACTIVE','120503',current_timestamp),
('997','24002','ACTIVE','120503',current_timestamp),
('1029','24002','ACTIVE','120503',current_timestamp),
('1030','24002','ACTIVE','120503',current_timestamp),
('1031','24002','ACTIVE','120503',current_timestamp),
('1032','24002','ACTIVE','120503',current_timestamp),
('1033','24002','ACTIVE','120503',current_timestamp),
('1036','24002','ACTIVE','120503',current_timestamp),
('1039','24002','ACTIVE','120503',current_timestamp),
('988','24002','ACTIVE','120503',current_timestamp),
('1028','22001','ACTIVE','120503',current_timestamp),
('685','22001','ACTIVE','120503',current_timestamp),
('707','22001','ACTIVE','120503',current_timestamp),
('712','22001','ACTIVE','120503',current_timestamp),
('810','22001','ACTIVE','120503',current_timestamp),
('937','22001','ACTIVE','120503',current_timestamp),
('940','22001','ACTIVE','120503',current_timestamp),
('1102','22001','ACTIVE','120503',current_timestamp),
('1146','22001','ACTIVE','120503',current_timestamp),
('4','22001','ACTIVE','120503',current_timestamp),
('6','22001','ACTIVE','120503',current_timestamp),
('49','22001','ACTIVE','120503',current_timestamp),
('58','22001','ACTIVE','120503',current_timestamp),
('59','22001','ACTIVE','120503',current_timestamp),
('61','22001','ACTIVE','120503',current_timestamp),
('75','22001','ACTIVE','120503',current_timestamp),
('91','22001','ACTIVE','120503',current_timestamp),
('96','22001','ACTIVE','120503',current_timestamp),
('97','22001','ACTIVE','120503',current_timestamp),
('98','22001','ACTIVE','120503',current_timestamp),
('114','22001','ACTIVE','120503',current_timestamp),
('127','22001','ACTIVE','120503',current_timestamp),
('128','22001','ACTIVE','120503',current_timestamp),
('130','22001','ACTIVE','120503',current_timestamp),
('131','22001','ACTIVE','120503',current_timestamp),
('174','22001','ACTIVE','120503',current_timestamp),
('183','22001','ACTIVE','120503',current_timestamp),
('184','22001','ACTIVE','120503',current_timestamp),
('185','22001','ACTIVE','120503',current_timestamp),
('202','22001','ACTIVE','120503',current_timestamp),
('203','22001','ACTIVE','120503',current_timestamp),
('245','22001','ACTIVE','120503',current_timestamp),
('268','22001','ACTIVE','120503',current_timestamp),
('270','22001','ACTIVE','120503',current_timestamp),
('271','22001','ACTIVE','120503',current_timestamp),
('274','22001','ACTIVE','120503',current_timestamp),
('275','22001','ACTIVE','120503',current_timestamp),
('310','22001','ACTIVE','120503',current_timestamp),
('311','22001','ACTIVE','120503',current_timestamp),
('327','22001','ACTIVE','120503',current_timestamp),
('328','22001','ACTIVE','120503',current_timestamp),
('329','22001','ACTIVE','120503',current_timestamp),
('330','22001','ACTIVE','120503',current_timestamp),
('342','22001','ACTIVE','120503',current_timestamp),
('343','22001','ACTIVE','120503',current_timestamp),
('345','22001','ACTIVE','120503',current_timestamp),
('346','22001','ACTIVE','120503',current_timestamp),
('348','22001','ACTIVE','120503',current_timestamp),
('349','22001','ACTIVE','120503',current_timestamp),
('361','22001','ACTIVE','120503',current_timestamp),
('367','22001','ACTIVE','120503',current_timestamp),
('518','22001','ACTIVE','120503',current_timestamp),
('519','22001','ACTIVE','120503',current_timestamp),
('605','22001','ACTIVE','120503',current_timestamp),
('614','22001','ACTIVE','120503',current_timestamp),
('698','22001','ACTIVE','120503',current_timestamp),
('719','22001','ACTIVE','120503',current_timestamp),
('721','22001','ACTIVE','120503',current_timestamp),
('760','22001','ACTIVE','120503',current_timestamp),
('773','22001','ACTIVE','120503',current_timestamp),
('848','22001','ACTIVE','120503',current_timestamp),
('886','22001','ACTIVE','120503',current_timestamp),
('901','22001','ACTIVE','120503',current_timestamp),
('944','22001','ACTIVE','120503',current_timestamp),
('1044','22001','ACTIVE','120503',current_timestamp),
('1089','22001','ACTIVE','120503',current_timestamp),
('1182','22001','ACTIVE','120503',current_timestamp),
('1183','22001','ACTIVE','120503',current_timestamp),
('1184','22001','ACTIVE','120503',current_timestamp),
('1185','22001','ACTIVE','120503',current_timestamp),
('1187','22001','ACTIVE','120503',current_timestamp),
('1195','22001','ACTIVE','120503',current_timestamp),
('1196','22001','ACTIVE','120503',current_timestamp),
('1197','22001','ACTIVE','120503',current_timestamp),
('8','22001','ACTIVE','120503',current_timestamp),
('17','22001','ACTIVE','120503',current_timestamp),
('18','22001','ACTIVE','120503',current_timestamp),
('22','22001','ACTIVE','120503',current_timestamp),
('28','22001','ACTIVE','120503',current_timestamp),
('34','22001','ACTIVE','120503',current_timestamp),
('35','22001','ACTIVE','120503',current_timestamp),
('43','22001','ACTIVE','120503',current_timestamp),
('67','22001','ACTIVE','120503',current_timestamp),
('68','22001','ACTIVE','120503',current_timestamp),
('76','22001','ACTIVE','120503',current_timestamp),
('79','22001','ACTIVE','120503',current_timestamp),
('83','22001','ACTIVE','120503',current_timestamp),
('84','22001','ACTIVE','120503',current_timestamp),
('87','22001','ACTIVE','120503',current_timestamp),
('92','22001','ACTIVE','120503',current_timestamp),
('94','22001','ACTIVE','120503',current_timestamp),
('95','22001','ACTIVE','120503',current_timestamp),
('180','22001','ACTIVE','120503',current_timestamp),
('196','22001','ACTIVE','120503',current_timestamp),
('197','22001','ACTIVE','120503',current_timestamp),
('210','22001','ACTIVE','120503',current_timestamp),
('272','22001','ACTIVE','120503',current_timestamp),
('294','22001','ACTIVE','120503',current_timestamp),
('299','22001','ACTIVE','120503',current_timestamp),
('300','22001','ACTIVE','120503',current_timestamp),
('301','22001','ACTIVE','120503',current_timestamp),
('307','22001','ACTIVE','120503',current_timestamp),
('314','22001','ACTIVE','120503',current_timestamp),
('332','22001','ACTIVE','120503',current_timestamp),
('333','22001','ACTIVE','120503',current_timestamp),
('334','22001','ACTIVE','120503',current_timestamp),
('335','22001','ACTIVE','120503',current_timestamp),
('372','22001','ACTIVE','120503',current_timestamp),
('526','22001','ACTIVE','120503',current_timestamp),
('527','22001','ACTIVE','120503',current_timestamp),
('540','22001','ACTIVE','120503',current_timestamp),
('565','22001','ACTIVE','120503',current_timestamp),
('733','22001','ACTIVE','120503',current_timestamp),
('746','22001','ACTIVE','120503',current_timestamp),
('771','22001','ACTIVE','120503',current_timestamp),
('772','22001','ACTIVE','120503',current_timestamp),
('863','22001','ACTIVE','120503',current_timestamp),
('906','22001','ACTIVE','120503',current_timestamp),
('908','22001','ACTIVE','120503',current_timestamp),
('917','22001','ACTIVE','120503',current_timestamp),
('918','22001','ACTIVE','120503',current_timestamp),
('919','22001','ACTIVE','120503',current_timestamp),
('920','22001','ACTIVE','120503',current_timestamp),
('921','22001','ACTIVE','120503',current_timestamp),
('922','22001','ACTIVE','120503',current_timestamp),
('938','22001','ACTIVE','120503',current_timestamp),
('949','22001','ACTIVE','120503',current_timestamp),
('973','22001','ACTIVE','120503',current_timestamp),
('1046','22001','ACTIVE','120503',current_timestamp),
('1047','22001','ACTIVE','120503',current_timestamp),
('1048','22001','ACTIVE','120503',current_timestamp),
('1051','22001','ACTIVE','120503',current_timestamp),
('1057','22001','ACTIVE','120503',current_timestamp),
('1059','22001','ACTIVE','120503',current_timestamp),
('1060','22001','ACTIVE','120503',current_timestamp),
('1065','22001','ACTIVE','120503',current_timestamp),
('1067','22001','ACTIVE','120503',current_timestamp),
('1079','22001','ACTIVE','120503',current_timestamp),
('1093','22001','ACTIVE','120503',current_timestamp),
('1094','22001','ACTIVE','120503',current_timestamp),
('1098','22001','ACTIVE','120503',current_timestamp),
('1101','22001','ACTIVE','120503',current_timestamp),
('1106','22001','ACTIVE','120503',current_timestamp),
('1121','22001','ACTIVE','120503',current_timestamp),
('1123','22001','ACTIVE','120503',current_timestamp),
('1126','22001','ACTIVE','120503',current_timestamp),
('1128','22001','ACTIVE','120503',current_timestamp),
('1129','22001','ACTIVE','120503',current_timestamp),
('1130','22001','ACTIVE','120503',current_timestamp),
('1132','22001','ACTIVE','120503',current_timestamp),
('1133','22001','ACTIVE','120503',current_timestamp),
('1143','22001','ACTIVE','120503',current_timestamp),
('1152','22001','ACTIVE','120503',current_timestamp),
('1153','22001','ACTIVE','120503',current_timestamp),
('1159','22001','ACTIVE','120503',current_timestamp),
('1161','22001','ACTIVE','120503',current_timestamp),
('1167','22001','ACTIVE','120503',current_timestamp),
('1168','22001','ACTIVE','120503',current_timestamp),
('1178','22001','ACTIVE','120503',current_timestamp),
('1179','22001','ACTIVE','120503',current_timestamp),
('1180','22001','ACTIVE','120503',current_timestamp),
('1186','22001','ACTIVE','120503',current_timestamp),
('1188','22001','ACTIVE','120503',current_timestamp),
('1189','22001','ACTIVE','120503',current_timestamp),
('1191','22001','ACTIVE','120503',current_timestamp),
('1198','22001','ACTIVE','120503',current_timestamp),
('1199','22001','ACTIVE','120503',current_timestamp),
('1200','22001','ACTIVE','120503',current_timestamp),
('1203','22001','ACTIVE','120503',current_timestamp),
('1205','22001','ACTIVE','120503',current_timestamp),
('1221','22001','ACTIVE','120503',current_timestamp),
('1192','22001','ACTIVE','120503',current_timestamp),
('1193','22001','ACTIVE','120503',current_timestamp),
('1194','22001','ACTIVE','120503',current_timestamp),
('1068','22001','ACTIVE','120503',current_timestamp),
('1069','22001','ACTIVE','120503',current_timestamp),
('1155','22001','ACTIVE','120503',current_timestamp),
('1156','22001','ACTIVE','120503',current_timestamp),
('120','22001','ACTIVE','120503',current_timestamp),
('123','22001','ACTIVE','120503',current_timestamp),
('141','22001','ACTIVE','120503',current_timestamp),
('144','22001','ACTIVE','120503',current_timestamp),
('166','22001','ACTIVE','120503',current_timestamp),
('171','22001','ACTIVE','120503',current_timestamp),
('191','22001','ACTIVE','120503',current_timestamp),
('194','22001','ACTIVE','120503',current_timestamp),
('208','22001','ACTIVE','120503',current_timestamp),
('211','22001','ACTIVE','120503',current_timestamp),
('218','22001','ACTIVE','120503',current_timestamp),
('237','22001','ACTIVE','120503',current_timestamp),
('247','22001','ACTIVE','120503',current_timestamp),
('256','22001','ACTIVE','120503',current_timestamp),
('373','22001','ACTIVE','120503',current_timestamp),
('813','22001','ACTIVE','120503',current_timestamp),
('1224','22001','ACTIVE','120503',current_timestamp),
('1043','22001','ACTIVE','120503',current_timestamp),
('12','22001','ACTIVE','120503',current_timestamp),
('13','22001','ACTIVE','120503',current_timestamp),
('46','22001','ACTIVE','120503',current_timestamp),
('55','22001','ACTIVE','120503',current_timestamp),
('57','22001','ACTIVE','120503',current_timestamp),
('62','22001','ACTIVE','120503',current_timestamp),
('85','22001','ACTIVE','120503',current_timestamp),
('86','22001','ACTIVE','120503',current_timestamp),
('99','22001','ACTIVE','120503',current_timestamp),
('101','22001','ACTIVE','120503',current_timestamp),
('117','22001','ACTIVE','120503',current_timestamp),
('188','22001','ACTIVE','120503',current_timestamp),
('200','22001','ACTIVE','120503',current_timestamp),
('232','22001','ACTIVE','120503',current_timestamp),
('233','22001','ACTIVE','120503',current_timestamp),
('303','22001','ACTIVE','120503',current_timestamp),
('305','22001','ACTIVE','120503',current_timestamp),
('318','22001','ACTIVE','120503',current_timestamp),
('319','22001','ACTIVE','120503',current_timestamp),
('322','22001','ACTIVE','120503',current_timestamp),
('365','22001','ACTIVE','120503',current_timestamp),
('382','22001','ACTIVE','120503',current_timestamp),
('606','22001','ACTIVE','120503',current_timestamp),
('607','22001','ACTIVE','120503',current_timestamp),
('626','22001','ACTIVE','120503',current_timestamp),
('1064','22001','ACTIVE','120503',current_timestamp),
('1086','22001','ACTIVE','120503',current_timestamp),
('1215','22001','ACTIVE','120503',current_timestamp),
('1066','22001','ACTIVE','120503',current_timestamp),
('1071','22001','ACTIVE','120503',current_timestamp),
('1072','22001','ACTIVE','120503',current_timestamp),
('1073','22001','ACTIVE','120503',current_timestamp),
('1074','22001','ACTIVE','120503',current_timestamp),
('1078','22001','ACTIVE','120503',current_timestamp),
('1095','22001','ACTIVE','120503',current_timestamp),
('1096','22001','ACTIVE','120503',current_timestamp),
('1103','22001','ACTIVE','120503',current_timestamp),
('1110','22001','ACTIVE','120503',current_timestamp),
('1111','22001','ACTIVE','120503',current_timestamp),
('1124','22001','ACTIVE','120503',current_timestamp),
('1134','22001','ACTIVE','120503',current_timestamp),
('1135','22001','ACTIVE','120503',current_timestamp),
('1136','22001','ACTIVE','120503',current_timestamp),
('1137','22001','ACTIVE','120503',current_timestamp),
('1138','22001','ACTIVE','120503',current_timestamp),
('1139','22001','ACTIVE','120503',current_timestamp),
('1140','22001','ACTIVE','120503',current_timestamp),
('1165','22001','ACTIVE','120503',current_timestamp),
('1171','22001','ACTIVE','120503',current_timestamp),
('1176','22001','ACTIVE','120503',current_timestamp),
('1201','22001','ACTIVE','120503',current_timestamp),
('1213','22001','ACTIVE','120503',current_timestamp),
('1214','22001','ACTIVE','120503',current_timestamp),
('1225','22001','ACTIVE','120503',current_timestamp),
('14','22001','ACTIVE','120503',current_timestamp),
('15','22001','ACTIVE','120503',current_timestamp),
('16','22001','ACTIVE','120503',current_timestamp),
('20','22001','ACTIVE','120503',current_timestamp),
('21','22001','ACTIVE','120503',current_timestamp),
('36','22001','ACTIVE','120503',current_timestamp),
('38','22001','ACTIVE','120503',current_timestamp),
('40','22001','ACTIVE','120503',current_timestamp),
('42','22001','ACTIVE','120503',current_timestamp),
('45','22001','ACTIVE','120503',current_timestamp),
('60','22001','ACTIVE','120503',current_timestamp),
('64','22001','ACTIVE','120503',current_timestamp),
('77','22001','ACTIVE','120503',current_timestamp),
('100','22001','ACTIVE','120503',current_timestamp),
('102','22001','ACTIVE','120503',current_timestamp),
('103','22001','ACTIVE','120503',current_timestamp),
('104','22001','ACTIVE','120503',current_timestamp),
('105','22001','ACTIVE','120503',current_timestamp),
('106','22001','ACTIVE','120503',current_timestamp),
('107','22001','ACTIVE','120503',current_timestamp),
('111','22001','ACTIVE','120503',current_timestamp),
('115','22001','ACTIVE','120503',current_timestamp),
('116','22001','ACTIVE','120503',current_timestamp),
('126','22001','ACTIVE','120503',current_timestamp),
('132','22001','ACTIVE','120503',current_timestamp),
('138','22001','ACTIVE','120503',current_timestamp),
('147','22001','ACTIVE','120503',current_timestamp),
('150','22001','ACTIVE','120503',current_timestamp),
('151','22001','ACTIVE','120503',current_timestamp),
('157','22001','ACTIVE','120503',current_timestamp),
('164','22001','ACTIVE','120503',current_timestamp),
('176','22001','ACTIVE','120503',current_timestamp),
('212','22001','ACTIVE','120503',current_timestamp),
('219','22001','ACTIVE','120503',current_timestamp),
('220','22001','ACTIVE','120503',current_timestamp),
('221','22001','ACTIVE','120503',current_timestamp),
('222','22001','ACTIVE','120503',current_timestamp),
('223','22001','ACTIVE','120503',current_timestamp),
('224','22001','ACTIVE','120503',current_timestamp),
('225','22001','ACTIVE','120503',current_timestamp),
('226','22001','ACTIVE','120503',current_timestamp),
('227','22001','ACTIVE','120503',current_timestamp),
('228','22001','ACTIVE','120503',current_timestamp),
('229','22001','ACTIVE','120503',current_timestamp),
('230','22001','ACTIVE','120503',current_timestamp),
('248','22001','ACTIVE','120503',current_timestamp),
('253','22001','ACTIVE','120503',current_timestamp),
('258','22001','ACTIVE','120503',current_timestamp),
('259','22001','ACTIVE','120503',current_timestamp),
('267','22001','ACTIVE','120503',current_timestamp),
('283','22001','ACTIVE','120503',current_timestamp),
('284','22001','ACTIVE','120503',current_timestamp),
('302','22001','ACTIVE','120503',current_timestamp),
('312','22001','ACTIVE','120503',current_timestamp),
('313','22001','ACTIVE','120503',current_timestamp),
('323','22001','ACTIVE','120503',current_timestamp),
('324','22001','ACTIVE','120503',current_timestamp),
('336','22001','ACTIVE','120503',current_timestamp),
('337','22001','ACTIVE','120503',current_timestamp),
('341','22001','ACTIVE','120503',current_timestamp),
('347','22001','ACTIVE','120503',current_timestamp),
('350','22001','ACTIVE','120503',current_timestamp),
('351','22001','ACTIVE','120503',current_timestamp),
('352','22001','ACTIVE','120503',current_timestamp),
('353','22001','ACTIVE','120503',current_timestamp),
('354','22001','ACTIVE','120503',current_timestamp),
('355','22001','ACTIVE','120503',current_timestamp),
('359','22001','ACTIVE','120503',current_timestamp),
('360','22001','ACTIVE','120503',current_timestamp),
('366','22001','ACTIVE','120503',current_timestamp),
('380','22001','ACTIVE','120503',current_timestamp),
('381','22001','ACTIVE','120503',current_timestamp),
('387','22001','ACTIVE','120503',current_timestamp),
('394','22001','ACTIVE','120503',current_timestamp),
('533','22001','ACTIVE','120503',current_timestamp),
('534','22001','ACTIVE','120503',current_timestamp),
('537','22001','ACTIVE','120503',current_timestamp),
('538','22001','ACTIVE','120503',current_timestamp),
('602','22001','ACTIVE','120503',current_timestamp),
('603','22001','ACTIVE','120503',current_timestamp),
('617','22001','ACTIVE','120503',current_timestamp),
('620','22001','ACTIVE','120503',current_timestamp),
('631','22001','ACTIVE','120503',current_timestamp),
('688','22001','ACTIVE','120503',current_timestamp),
('695','22001','ACTIVE','120503',current_timestamp),
('717','22001','ACTIVE','120503',current_timestamp),
('724','22001','ACTIVE','120503',current_timestamp),
('725','22001','ACTIVE','120503',current_timestamp),
('731','22001','ACTIVE','120503',current_timestamp),
('732','22001','ACTIVE','120503',current_timestamp),
('734','22001','ACTIVE','120503',current_timestamp),
('740','22001','ACTIVE','120503',current_timestamp),
('779','22001','ACTIVE','120503',current_timestamp),
('801','22001','ACTIVE','120503',current_timestamp),
('865','22001','ACTIVE','120503',current_timestamp),
('879','22001','ACTIVE','120503',current_timestamp),
('880','22001','ACTIVE','120503',current_timestamp),
('888','22001','ACTIVE','120503',current_timestamp),
('907','22001','ACTIVE','120503',current_timestamp),
('936','22001','ACTIVE','120503',current_timestamp),
('939','22001','ACTIVE','120503',current_timestamp),
('943','22001','ACTIVE','120503',current_timestamp),
('945','22001','ACTIVE','120503',current_timestamp),
('947','22001','ACTIVE','120503',current_timestamp),
('948','22001','ACTIVE','120503',current_timestamp),
('951','22001','ACTIVE','120503',current_timestamp),
('975','22001','ACTIVE','120503',current_timestamp),
('976','22001','ACTIVE','120503',current_timestamp),
('1024','22001','ACTIVE','120503',current_timestamp),
('1025','22001','ACTIVE','120503',current_timestamp),
('1026','22001','ACTIVE','120503',current_timestamp),
('1049','22001','ACTIVE','120503',current_timestamp),
('1050','22001','ACTIVE','120503',current_timestamp),
('1053','22001','ACTIVE','120503',current_timestamp),
('1109','22001','ACTIVE','120503',current_timestamp),
('1125','22001','ACTIVE','120503',current_timestamp),
('1131','22001','ACTIVE','120503',current_timestamp),
('1147','22001','ACTIVE','120503',current_timestamp),
('1170','22001','ACTIVE','120503',current_timestamp),
('1206','22001','ACTIVE','120503',current_timestamp),
('491','22001','ACTIVE','120503',current_timestamp),
('492','22001','ACTIVE','120503',current_timestamp),
('493','22001','ACTIVE','120503',current_timestamp),
('494','22001','ACTIVE','120503',current_timestamp),
('495','22001','ACTIVE','120503',current_timestamp),
('500','22001','ACTIVE','120503',current_timestamp),
('501','22001','ACTIVE','120503',current_timestamp),
('502','22001','ACTIVE','120503',current_timestamp),
('503','22001','ACTIVE','120503',current_timestamp),
('504','22001','ACTIVE','120503',current_timestamp),
('506','22001','ACTIVE','120503',current_timestamp),
('507','22001','ACTIVE','120503',current_timestamp),
('508','22001','ACTIVE','120503',current_timestamp),
('509','22001','ACTIVE','120503',current_timestamp),
('516','22001','ACTIVE','120503',current_timestamp),
('517','22001','ACTIVE','120503',current_timestamp),
('535','22001','ACTIVE','120503',current_timestamp),
('683','22001','ACTIVE','120503',current_timestamp),
('727','22001','ACTIVE','120503',current_timestamp),
('770','22001','ACTIVE','120503',current_timestamp),
('789','22001','ACTIVE','120503',current_timestamp),
('790','22001','ACTIVE','120503',current_timestamp),
('795','22001','ACTIVE','120503',current_timestamp),
('802','22001','ACTIVE','120503',current_timestamp),
('815','22001','ACTIVE','120503',current_timestamp),
('829','22001','ACTIVE','120503',current_timestamp),
('838','22001','ACTIVE','120503',current_timestamp),
('841','22001','ACTIVE','120503',current_timestamp),
('871','22001','ACTIVE','120503',current_timestamp),
('881','22001','ACTIVE','120503',current_timestamp),
('882','22001','ACTIVE','120503',current_timestamp),
('891','22001','ACTIVE','120503',current_timestamp),
('902','22001','ACTIVE','120503',current_timestamp),
('909','22001','ACTIVE','120503',current_timestamp),
('923','22001','ACTIVE','120503',current_timestamp),
('950','22001','ACTIVE','120503',current_timestamp),
('958','22001','ACTIVE','120503',current_timestamp),
('959','22001','ACTIVE','120503',current_timestamp),
('960','22001','ACTIVE','120503',current_timestamp),
('961','22001','ACTIVE','120503',current_timestamp),
('965','22001','ACTIVE','120503',current_timestamp),
('967','22001','ACTIVE','120503',current_timestamp),
('968','22001','ACTIVE','120503',current_timestamp),
('1018','22001','ACTIVE','120503',current_timestamp),
('1019','22001','ACTIVE','120503',current_timestamp),
('7','22001','ACTIVE','120503',current_timestamp),
('23','22001','ACTIVE','120503',current_timestamp),
('27','22001','ACTIVE','120503',current_timestamp),
('32','22001','ACTIVE','120503',current_timestamp),
('44','22001','ACTIVE','120503',current_timestamp),
('48','22001','ACTIVE','120503',current_timestamp),
('50','22001','ACTIVE','120503',current_timestamp),
('51','22001','ACTIVE','120503',current_timestamp),
('52','22001','ACTIVE','120503',current_timestamp),
('53','22001','ACTIVE','120503',current_timestamp),
('63','22001','ACTIVE','120503',current_timestamp),
('66','22001','ACTIVE','120503',current_timestamp),
('71','22001','ACTIVE','120503',current_timestamp),
('72','22001','ACTIVE','120503',current_timestamp),
('78','22001','ACTIVE','120503',current_timestamp),
('89','22001','ACTIVE','120503',current_timestamp),
('90','22001','ACTIVE','120503',current_timestamp),
('93','22001','ACTIVE','120503',current_timestamp),
('110','22001','ACTIVE','120503',current_timestamp),
('119','22001','ACTIVE','120503',current_timestamp),
('121','22001','ACTIVE','120503',current_timestamp),
('134','22001','ACTIVE','120503',current_timestamp),
('146','22001','ACTIVE','120503',current_timestamp),
('165','22001','ACTIVE','120503',current_timestamp),
('198','22001','ACTIVE','120503',current_timestamp),
('217','22001','ACTIVE','120503',current_timestamp),
('262','22001','ACTIVE','120503',current_timestamp),
('264','22001','ACTIVE','120503',current_timestamp),
('278','22001','ACTIVE','120503',current_timestamp),
('285','22001','ACTIVE','120503',current_timestamp),
('291','22001','ACTIVE','120503',current_timestamp),
('298','22001','ACTIVE','120503',current_timestamp),
('306','22001','ACTIVE','120503',current_timestamp),
('316','22001','ACTIVE','120503',current_timestamp),
('325','22001','ACTIVE','120503',current_timestamp),
('344','22001','ACTIVE','120503',current_timestamp),
('379','22001','ACTIVE','120503',current_timestamp),
('388','22001','ACTIVE','120503',current_timestamp),
('389','22001','ACTIVE','120503',current_timestamp),
('390','22001','ACTIVE','120503',current_timestamp),
('391','22001','ACTIVE','120503',current_timestamp),
('395','22001','ACTIVE','120503',current_timestamp),
('542','22001','ACTIVE','120503',current_timestamp),
('548','22001','ACTIVE','120503',current_timestamp),
('818','22001','ACTIVE','120503',current_timestamp),
('825','22001','ACTIVE','120503',current_timestamp),
('831','22001','ACTIVE','120503',current_timestamp),
('858','22001','ACTIVE','120503',current_timestamp),
('859','22001','ACTIVE','120503',current_timestamp),
('875','22001','ACTIVE','120503',current_timestamp),
('911','22001','ACTIVE','120503',current_timestamp),
('974','22001','ACTIVE','120503',current_timestamp),
('995','22001','ACTIVE','120503',current_timestamp),
('1040','22001','ACTIVE','120503',current_timestamp),
('1041','22001','ACTIVE','120503',current_timestamp),
('1045','22001','ACTIVE','120503',current_timestamp),
('1052','22001','ACTIVE','120503',current_timestamp),
('1054','22001','ACTIVE','120503',current_timestamp),
('1056','22001','ACTIVE','120503',current_timestamp),
('1100','22001','ACTIVE','120503',current_timestamp),
('1127','22001','ACTIVE','120503',current_timestamp),
('1164','22001','ACTIVE','120503',current_timestamp),
('1181','22001','ACTIVE','120503',current_timestamp),
('1204','22001','ACTIVE','120503',current_timestamp),
('215','22001','ACTIVE','120503',current_timestamp),
('279','22001','ACTIVE','120503',current_timestamp),
('280','22001','ACTIVE','120503',current_timestamp),
('281','22001','ACTIVE','120503',current_timestamp),
('282','22001','ACTIVE','120503',current_timestamp),
('368','22001','ACTIVE','120503',current_timestamp),
('369','22001','ACTIVE','120503',current_timestamp),
('370','22001','ACTIVE','120503',current_timestamp),
('371','22001','ACTIVE','120503',current_timestamp),
('374','22001','ACTIVE','120503',current_timestamp),
('705','22001','ACTIVE','120503',current_timestamp),
('713','22001','ACTIVE','120503',current_timestamp),
('808','22001','ACTIVE','120503',current_timestamp),
('809','22001','ACTIVE','120503',current_timestamp),
('830','22001','ACTIVE','120503',current_timestamp),
('904','22001','ACTIVE','120503',current_timestamp),
('913','22001','ACTIVE','120503',current_timestamp),
('914','22001','ACTIVE','120503',current_timestamp),
('915','22001','ACTIVE','120503',current_timestamp),
('916','22001','ACTIVE','120503',current_timestamp),
('1076','22001','ACTIVE','120503',current_timestamp),
('29','22001','ACTIVE','120503',current_timestamp),
('30','22001','ACTIVE','120503',current_timestamp),
('31','22001','ACTIVE','120503',current_timestamp),
('41','22001','ACTIVE','120503',current_timestamp),
('47','22001','ACTIVE','120503',current_timestamp),
('88','22001','ACTIVE','120503',current_timestamp),
('118','22001','ACTIVE','120503',current_timestamp),
('124','22001','ACTIVE','120503',current_timestamp),
('125','22001','ACTIVE','120503',current_timestamp),
('129','22001','ACTIVE','120503',current_timestamp),
('135','22001','ACTIVE','120503',current_timestamp),
('136','22001','ACTIVE','120503',current_timestamp),
('137','22001','ACTIVE','120503',current_timestamp),
('139','22001','ACTIVE','120503',current_timestamp),
('140','22001','ACTIVE','120503',current_timestamp),
('154','22001','ACTIVE','120503',current_timestamp),
('161','22001','ACTIVE','120503',current_timestamp),
('162','22001','ACTIVE','120503',current_timestamp),
('173','22001','ACTIVE','120503',current_timestamp),
('175','22001','ACTIVE','120503',current_timestamp),
('178','22001','ACTIVE','120503',current_timestamp),
('213','22001','ACTIVE','120503',current_timestamp),
('235','22001','ACTIVE','120503',current_timestamp),
('243','22001','ACTIVE','120503',current_timestamp),
('252','22001','ACTIVE','120503',current_timestamp),
('265','22001','ACTIVE','120503',current_timestamp),
('269','22001','ACTIVE','120503',current_timestamp),
('288','22001','ACTIVE','120503',current_timestamp),
('289','22001','ACTIVE','120503',current_timestamp),
('295','22001','ACTIVE','120503',current_timestamp),
('304','22001','ACTIVE','120503',current_timestamp),
('317','22001','ACTIVE','120503',current_timestamp),
('320','22001','ACTIVE','120503',current_timestamp),
('321','22001','ACTIVE','120503',current_timestamp),
('326','22001','ACTIVE','120503',current_timestamp),
('338','22001','ACTIVE','120503',current_timestamp),
('339','22001','ACTIVE','120503',current_timestamp),
('362','22001','ACTIVE','120503',current_timestamp),
('363','22001','ACTIVE','120503',current_timestamp),
('386','22001','ACTIVE','120503',current_timestamp),
('392','22001','ACTIVE','120503',current_timestamp),
('393','22001','ACTIVE','120503',current_timestamp),
('539','22001','ACTIVE','120503',current_timestamp),
('563','22001','ACTIVE','120503',current_timestamp),
('621','22001','ACTIVE','120503',current_timestamp),
('696','22001','ACTIVE','120503',current_timestamp),
('783','22001','ACTIVE','120503',current_timestamp),
('784','22001','ACTIVE','120503',current_timestamp),
('785','22001','ACTIVE','120503',current_timestamp),
('786','22001','ACTIVE','120503',current_timestamp),
('803','22001','ACTIVE','120503',current_timestamp),
('868','22001','ACTIVE','120503',current_timestamp),
('998','22001','ACTIVE','120503',current_timestamp),
('999','22001','ACTIVE','120503',current_timestamp),
('1000','22001','ACTIVE','120503',current_timestamp),
('1001','22001','ACTIVE','120503',current_timestamp),
('1002','22001','ACTIVE','120503',current_timestamp),
('1003','22001','ACTIVE','120503',current_timestamp),
('1004','22001','ACTIVE','120503',current_timestamp),
('1005','22001','ACTIVE','120503',current_timestamp),
('1006','22001','ACTIVE','120503',current_timestamp),
('1007','22001','ACTIVE','120503',current_timestamp),
('1008','22001','ACTIVE','120503',current_timestamp),
('1009','22001','ACTIVE','120503',current_timestamp),
('1010','22001','ACTIVE','120503',current_timestamp),
('1011','22001','ACTIVE','120503',current_timestamp),
('1012','22001','ACTIVE','120503',current_timestamp),
('1013','22001','ACTIVE','120503',current_timestamp),
('1014','22001','ACTIVE','120503',current_timestamp),
('1015','22001','ACTIVE','120503',current_timestamp),
('1016','22001','ACTIVE','120503',current_timestamp),
('1020','22001','ACTIVE','120503',current_timestamp),
('1021','22001','ACTIVE','120503',current_timestamp),
('1042','22001','ACTIVE','120503',current_timestamp),
('1149','22001','ACTIVE','120503',current_timestamp),
('1150','22001','ACTIVE','120503',current_timestamp),
('1202','22001','ACTIVE','120503',current_timestamp),
('1218','22001','ACTIVE','120503',current_timestamp),
('1220','22001','ACTIVE','120503',current_timestamp),
('1087','22001','ACTIVE','120503',current_timestamp),
('1099','22001','ACTIVE','120503',current_timestamp),
('1','22001','ACTIVE','120503',current_timestamp),
('3','22001','ACTIVE','120503',current_timestamp),
('5','22001','ACTIVE','120503',current_timestamp),
('9','22001','ACTIVE','120503',current_timestamp),
('19','22001','ACTIVE','120503',current_timestamp),
('39','22001','ACTIVE','120503',current_timestamp),
('70','22001','ACTIVE','120503',current_timestamp),
('108','22001','ACTIVE','120503',current_timestamp),
('143','22001','ACTIVE','120503',current_timestamp),
('148','22001','ACTIVE','120503',current_timestamp),
('149','22001','ACTIVE','120503',current_timestamp),
('155','22001','ACTIVE','120503',current_timestamp),
('172','22001','ACTIVE','120503',current_timestamp),
('187','22001','ACTIVE','120503',current_timestamp),
('209','22001','ACTIVE','120503',current_timestamp),
('231','22001','ACTIVE','120503',current_timestamp),
('239','22001','ACTIVE','120503',current_timestamp),
('377','22001','ACTIVE','120503',current_timestamp),
('378','22001','ACTIVE','120503',current_timestamp),
('383','22001','ACTIVE','120503',current_timestamp),
('385','22001','ACTIVE','120503',current_timestamp),
('515','22001','ACTIVE','120503',current_timestamp),
('1061','22001','ACTIVE','120503',current_timestamp),
('1077','22001','ACTIVE','120503',current_timestamp),
('1141','22001','ACTIVE','120503',current_timestamp),
('1151','22001','ACTIVE','120503',current_timestamp),
('1172','22001','ACTIVE','120503',current_timestamp),
('1175','22001','ACTIVE','120503',current_timestamp),
('1177','22001','ACTIVE','120503',current_timestamp),
('1212','22001','ACTIVE','120503',current_timestamp),
('1222','22001','ACTIVE','120503',current_timestamp),
('1223','22001','ACTIVE','120503',current_timestamp),
('633','22001','ACTIVE','120503',current_timestamp),
('400','22001','ACTIVE','120503',current_timestamp),
('401','22001','ACTIVE','120503',current_timestamp),
('402','22001','ACTIVE','120503',current_timestamp),
('403','22001','ACTIVE','120503',current_timestamp),
('405','22001','ACTIVE','120503',current_timestamp),
('406','22001','ACTIVE','120503',current_timestamp),
('407','22001','ACTIVE','120503',current_timestamp),
('408','22001','ACTIVE','120503',current_timestamp),
('409','22001','ACTIVE','120503',current_timestamp),
('415','22001','ACTIVE','120503',current_timestamp),
('416','22001','ACTIVE','120503',current_timestamp),
('419','22001','ACTIVE','120503',current_timestamp),
('426','22001','ACTIVE','120503',current_timestamp),
('427','22001','ACTIVE','120503',current_timestamp),
('428','22001','ACTIVE','120503',current_timestamp),
('433','22001','ACTIVE','120503',current_timestamp),
('435','22001','ACTIVE','120503',current_timestamp),
('439','22001','ACTIVE','120503',current_timestamp),
('440','22001','ACTIVE','120503',current_timestamp),
('441','22001','ACTIVE','120503',current_timestamp),
('443','22001','ACTIVE','120503',current_timestamp),
('444','22001','ACTIVE','120503',current_timestamp),
('445','22001','ACTIVE','120503',current_timestamp),
('446','22001','ACTIVE','120503',current_timestamp),
('448','22001','ACTIVE','120503',current_timestamp),
('450','22001','ACTIVE','120503',current_timestamp),
('458','22001','ACTIVE','120503',current_timestamp),
('462','22001','ACTIVE','120503',current_timestamp),
('463','22001','ACTIVE','120503',current_timestamp),
('464','22001','ACTIVE','120503',current_timestamp),
('469','22001','ACTIVE','120503',current_timestamp),
('470','22001','ACTIVE','120503',current_timestamp),
('471','22001','ACTIVE','120503',current_timestamp),
('472','22001','ACTIVE','120503',current_timestamp),
('473','22001','ACTIVE','120503',current_timestamp),
('474','22001','ACTIVE','120503',current_timestamp),
('475','22001','ACTIVE','120503',current_timestamp),
('476','22001','ACTIVE','120503',current_timestamp),
('477','22001','ACTIVE','120503',current_timestamp),
('479','22001','ACTIVE','120503',current_timestamp),
('481','22001','ACTIVE','120503',current_timestamp),
('483','22001','ACTIVE','120503',current_timestamp),
('484','22001','ACTIVE','120503',current_timestamp),
('485','22001','ACTIVE','120503',current_timestamp),
('486','22001','ACTIVE','120503',current_timestamp),
('487','22001','ACTIVE','120503',current_timestamp),
('488','22001','ACTIVE','120503',current_timestamp),
('489','22001','ACTIVE','120503',current_timestamp),
('525','22001','ACTIVE','120503',current_timestamp),
('530','22001','ACTIVE','120503',current_timestamp),
('546','22001','ACTIVE','120503',current_timestamp),
('567','22001','ACTIVE','120503',current_timestamp),
('569','22001','ACTIVE','120503',current_timestamp),
('570','22001','ACTIVE','120503',current_timestamp),
('571','22001','ACTIVE','120503',current_timestamp),
('574','22001','ACTIVE','120503',current_timestamp),
('576','22001','ACTIVE','120503',current_timestamp),
('577','22001','ACTIVE','120503',current_timestamp),
('592','22001','ACTIVE','120503',current_timestamp),
('593','22001','ACTIVE','120503',current_timestamp),
('594','22001','ACTIVE','120503',current_timestamp),
('597','22001','ACTIVE','120503',current_timestamp),
('598','22001','ACTIVE','120503',current_timestamp),
('599','22001','ACTIVE','120503',current_timestamp),
('600','22001','ACTIVE','120503',current_timestamp),
('650','22001','ACTIVE','120503',current_timestamp),
('656','22001','ACTIVE','120503',current_timestamp),
('665','22001','ACTIVE','120503',current_timestamp),
('667','22001','ACTIVE','120503',current_timestamp),
('674','22001','ACTIVE','120503',current_timestamp),
('716','22001','ACTIVE','120503',current_timestamp),
('730','22001','ACTIVE','120503',current_timestamp),
('736','22001','ACTIVE','120503',current_timestamp),
('739','22001','ACTIVE','120503',current_timestamp),
('751','22001','ACTIVE','120503',current_timestamp),
('752','22001','ACTIVE','120503',current_timestamp),
('842','22001','ACTIVE','120503',current_timestamp),
('852','22001','ACTIVE','120503',current_timestamp),
('853','22001','ACTIVE','120503',current_timestamp),
('873','22001','ACTIVE','120503',current_timestamp),
('877','22001','ACTIVE','120503',current_timestamp),
('964','22001','ACTIVE','120503',current_timestamp),
('978','22001','ACTIVE','120503',current_timestamp),
('979','22001','ACTIVE','120503',current_timestamp),
('980','22001','ACTIVE','120503',current_timestamp),
('981','22001','ACTIVE','120503',current_timestamp),
('983','22001','ACTIVE','120503',current_timestamp),
('984','22001','ACTIVE','120503',current_timestamp),
('985','22001','ACTIVE','120503',current_timestamp),
('580','22001','ACTIVE','120503',current_timestamp),
('854','22001','ACTIVE','120503',current_timestamp),
('1117','22001','ACTIVE','120503',current_timestamp),
('1120','22001','ACTIVE','120503',current_timestamp),
('1163','22001','ACTIVE','120503',current_timestamp),
('199','22001','ACTIVE','120503',current_timestamp),
('204','22001','ACTIVE','120503',current_timestamp),
('214','22001','ACTIVE','120503',current_timestamp),
('261','22001','ACTIVE','120503',current_timestamp),
('277','22001','ACTIVE','120503',current_timestamp),
('340','22001','ACTIVE','120503',current_timestamp),
('375','22001','ACTIVE','120503',current_timestamp),
('686','22001','ACTIVE','120503',current_timestamp),
('687','22001','ACTIVE','120503',current_timestamp),
('697','22001','ACTIVE','120503',current_timestamp),
('699','22001','ACTIVE','120503',current_timestamp),
('702','22001','ACTIVE','120503',current_timestamp),
('703','22001','ACTIVE','120503',current_timestamp),
('737','22001','ACTIVE','120503',current_timestamp),
('742','22001','ACTIVE','120503',current_timestamp),
('747','22001','ACTIVE','120503',current_timestamp),
('750','22001','ACTIVE','120503',current_timestamp),
('781','22001','ACTIVE','120503',current_timestamp),
('782','22001','ACTIVE','120503',current_timestamp),
('833','22001','ACTIVE','120503',current_timestamp),
('849','22001','ACTIVE','120503',current_timestamp),
('895','22001','ACTIVE','120503',current_timestamp),
('897','22001','ACTIVE','120503',current_timestamp),
('898','22001','ACTIVE','120503',current_timestamp),
('963','22001','ACTIVE','120503',current_timestamp),
('977','22001','ACTIVE','120503',current_timestamp),
('1017','22001','ACTIVE','120503',current_timestamp),
('1122','22001','ACTIVE','120503',current_timestamp),
('1154','22001','ACTIVE','120503',current_timestamp),
('1169','22001','ACTIVE','120503',current_timestamp),
('1217','22001','ACTIVE','120503',current_timestamp),
('1219','22001','ACTIVE','120503',current_timestamp),
('616','22001','ACTIVE','120503',current_timestamp),
('832','22001','ACTIVE','120503',current_timestamp),
('1088','22001','ACTIVE','120503',current_timestamp),
('1090','22001','ACTIVE','120503',current_timestamp),
('1112','22001','ACTIVE','120503',current_timestamp),
('1113','22001','ACTIVE','120503',current_timestamp),
('1114','22001','ACTIVE','120503',current_timestamp),
('1115','22001','ACTIVE','120503',current_timestamp),
('1116','22001','ACTIVE','120503',current_timestamp),
('528','22001','ACTIVE','120503',current_timestamp),
('529','22001','ACTIVE','120503',current_timestamp),
('547','22001','ACTIVE','120503',current_timestamp),
('575','22001','ACTIVE','120503',current_timestamp),
('627','22001','ACTIVE','120503',current_timestamp),
('735','22001','ACTIVE','120503',current_timestamp),
('741','22001','ACTIVE','120503',current_timestamp),
('749','22001','ACTIVE','120503',current_timestamp),
('804','22001','ACTIVE','120503',current_timestamp),
('820','22001','ACTIVE','120503',current_timestamp),
('989','22001','ACTIVE','120503',current_timestamp),
('685','22002','ACTIVE','120503',current_timestamp),
('707','22002','ACTIVE','120503',current_timestamp),
('712','22002','ACTIVE','120503',current_timestamp),
('810','22002','ACTIVE','120503',current_timestamp),
('937','22002','ACTIVE','120503',current_timestamp),
('940','22002','ACTIVE','120503',current_timestamp),
('1102','22002','ACTIVE','120503',current_timestamp),
('1146','22002','ACTIVE','120503',current_timestamp),
('4','22002','ACTIVE','120503',current_timestamp),
('6','22002','ACTIVE','120503',current_timestamp),
('49','22002','ACTIVE','120503',current_timestamp),
('58','22002','ACTIVE','120503',current_timestamp),
('59','22002','ACTIVE','120503',current_timestamp),
('61','22002','ACTIVE','120503',current_timestamp),
('75','22002','ACTIVE','120503',current_timestamp),
('91','22002','ACTIVE','120503',current_timestamp),
('96','22002','ACTIVE','120503',current_timestamp),
('97','22002','ACTIVE','120503',current_timestamp),
('98','22002','ACTIVE','120503',current_timestamp),
('114','22002','ACTIVE','120503',current_timestamp),
('127','22002','ACTIVE','120503',current_timestamp),
('128','22002','ACTIVE','120503',current_timestamp),
('130','22002','ACTIVE','120503',current_timestamp),
('131','22002','ACTIVE','120503',current_timestamp),
('174','22002','ACTIVE','120503',current_timestamp),
('183','22002','ACTIVE','120503',current_timestamp),
('184','22002','ACTIVE','120503',current_timestamp),
('185','22002','ACTIVE','120503',current_timestamp),
('202','22002','ACTIVE','120503',current_timestamp),
('203','22002','ACTIVE','120503',current_timestamp),
('245','22002','ACTIVE','120503',current_timestamp),
('268','22002','ACTIVE','120503',current_timestamp),
('270','22002','ACTIVE','120503',current_timestamp),
('271','22002','ACTIVE','120503',current_timestamp),
('274','22002','ACTIVE','120503',current_timestamp),
('275','22002','ACTIVE','120503',current_timestamp),
('310','22002','ACTIVE','120503',current_timestamp),
('311','22002','ACTIVE','120503',current_timestamp),
('327','22002','ACTIVE','120503',current_timestamp),
('328','22002','ACTIVE','120503',current_timestamp),
('329','22002','ACTIVE','120503',current_timestamp),
('330','22002','ACTIVE','120503',current_timestamp),
('342','22002','ACTIVE','120503',current_timestamp),
('343','22002','ACTIVE','120503',current_timestamp),
('345','22002','ACTIVE','120503',current_timestamp),
('346','22002','ACTIVE','120503',current_timestamp),
('348','22002','ACTIVE','120503',current_timestamp),
('349','22002','ACTIVE','120503',current_timestamp),
('361','22002','ACTIVE','120503',current_timestamp),
('367','22002','ACTIVE','120503',current_timestamp),
('518','22002','ACTIVE','120503',current_timestamp),
('519','22002','ACTIVE','120503',current_timestamp),
('605','22002','ACTIVE','120503',current_timestamp),
('614','22002','ACTIVE','120503',current_timestamp),
('698','22002','ACTIVE','120503',current_timestamp),
('719','22002','ACTIVE','120503',current_timestamp),
('721','22002','ACTIVE','120503',current_timestamp),
('760','22002','ACTIVE','120503',current_timestamp),
('773','22002','ACTIVE','120503',current_timestamp),
('848','22002','ACTIVE','120503',current_timestamp),
('886','22002','ACTIVE','120503',current_timestamp),
('901','22002','ACTIVE','120503',current_timestamp),
('944','22002','ACTIVE','120503',current_timestamp),
('1044','22002','ACTIVE','120503',current_timestamp),
('1089','22002','ACTIVE','120503',current_timestamp),
('1182','22002','ACTIVE','120503',current_timestamp),
('1183','22002','ACTIVE','120503',current_timestamp),
('1184','22002','ACTIVE','120503',current_timestamp),
('1185','22002','ACTIVE','120503',current_timestamp),
('1187','22002','ACTIVE','120503',current_timestamp),
('1195','22002','ACTIVE','120503',current_timestamp),
('1196','22002','ACTIVE','120503',current_timestamp),
('1197','22002','ACTIVE','120503',current_timestamp),
('8','22002','ACTIVE','120503',current_timestamp),
('17','22002','ACTIVE','120503',current_timestamp),
('18','22002','ACTIVE','120503',current_timestamp),
('22','22002','ACTIVE','120503',current_timestamp),
('28','22002','ACTIVE','120503',current_timestamp),
('34','22002','ACTIVE','120503',current_timestamp),
('35','22002','ACTIVE','120503',current_timestamp),
('43','22002','ACTIVE','120503',current_timestamp),
('67','22002','ACTIVE','120503',current_timestamp),
('68','22002','ACTIVE','120503',current_timestamp),
('76','22002','ACTIVE','120503',current_timestamp),
('79','22002','ACTIVE','120503',current_timestamp),
('83','22002','ACTIVE','120503',current_timestamp),
('84','22002','ACTIVE','120503',current_timestamp),
('87','22002','ACTIVE','120503',current_timestamp),
('92','22002','ACTIVE','120503',current_timestamp),
('94','22002','ACTIVE','120503',current_timestamp),
('95','22002','ACTIVE','120503',current_timestamp),
('180','22002','ACTIVE','120503',current_timestamp),
('196','22002','ACTIVE','120503',current_timestamp),
('197','22002','ACTIVE','120503',current_timestamp),
('210','22002','ACTIVE','120503',current_timestamp),
('272','22002','ACTIVE','120503',current_timestamp),
('294','22002','ACTIVE','120503',current_timestamp),
('300','22002','ACTIVE','120503',current_timestamp),
('301','22002','ACTIVE','120503',current_timestamp),
('307','22002','ACTIVE','120503',current_timestamp),
('314','22002','ACTIVE','120503',current_timestamp),
('332','22002','ACTIVE','120503',current_timestamp),
('333','22002','ACTIVE','120503',current_timestamp),
('334','22002','ACTIVE','120503',current_timestamp),
('335','22002','ACTIVE','120503',current_timestamp),
('372','22002','ACTIVE','120503',current_timestamp),
('526','22002','ACTIVE','120503',current_timestamp),
('527','22002','ACTIVE','120503',current_timestamp),
('540','22002','ACTIVE','120503',current_timestamp),
('565','22002','ACTIVE','120503',current_timestamp),
('733','22002','ACTIVE','120503',current_timestamp),
('746','22002','ACTIVE','120503',current_timestamp),
('771','22002','ACTIVE','120503',current_timestamp),
('772','22002','ACTIVE','120503',current_timestamp),
('863','22002','ACTIVE','120503',current_timestamp),
('906','22002','ACTIVE','120503',current_timestamp),
('908','22002','ACTIVE','120503',current_timestamp),
('917','22002','ACTIVE','120503',current_timestamp),
('918','22002','ACTIVE','120503',current_timestamp),
('919','22002','ACTIVE','120503',current_timestamp),
('920','22002','ACTIVE','120503',current_timestamp),
('921','22002','ACTIVE','120503',current_timestamp),
('922','22002','ACTIVE','120503',current_timestamp),
('938','22002','ACTIVE','120503',current_timestamp),
('949','22002','ACTIVE','120503',current_timestamp),
('973','22002','ACTIVE','120503',current_timestamp),
('1046','22002','ACTIVE','120503',current_timestamp),
('1047','22002','ACTIVE','120503',current_timestamp),
('1048','22002','ACTIVE','120503',current_timestamp),
('1051','22002','ACTIVE','120503',current_timestamp),
('1057','22002','ACTIVE','120503',current_timestamp),
('1059','22002','ACTIVE','120503',current_timestamp),
('1060','22002','ACTIVE','120503',current_timestamp),
('1065','22002','ACTIVE','120503',current_timestamp),
('1067','22002','ACTIVE','120503',current_timestamp),
('1079','22002','ACTIVE','120503',current_timestamp),
('1093','22002','ACTIVE','120503',current_timestamp),
('1094','22002','ACTIVE','120503',current_timestamp),
('1101','22002','ACTIVE','120503',current_timestamp),
('1106','22002','ACTIVE','120503',current_timestamp),
('1121','22002','ACTIVE','120503',current_timestamp),
('1123','22002','ACTIVE','120503',current_timestamp),
('1126','22002','ACTIVE','120503',current_timestamp),
('1128','22002','ACTIVE','120503',current_timestamp),
('1129','22002','ACTIVE','120503',current_timestamp),
('1132','22002','ACTIVE','120503',current_timestamp),
('1133','22002','ACTIVE','120503',current_timestamp),
('1143','22002','ACTIVE','120503',current_timestamp),
('1153','22002','ACTIVE','120503',current_timestamp),
('1159','22002','ACTIVE','120503',current_timestamp),
('1161','22002','ACTIVE','120503',current_timestamp),
('1167','22002','ACTIVE','120503',current_timestamp),
('1168','22002','ACTIVE','120503',current_timestamp),
('1178','22002','ACTIVE','120503',current_timestamp),
('1179','22002','ACTIVE','120503',current_timestamp),
('1180','22002','ACTIVE','120503',current_timestamp),
('1186','22002','ACTIVE','120503',current_timestamp),
('1188','22002','ACTIVE','120503',current_timestamp),
('1189','22002','ACTIVE','120503',current_timestamp),
('1191','22002','ACTIVE','120503',current_timestamp),
('1198','22002','ACTIVE','120503',current_timestamp),
('1199','22002','ACTIVE','120503',current_timestamp),
('1200','22002','ACTIVE','120503',current_timestamp),
('1203','22002','ACTIVE','120503',current_timestamp),
('1205','22002','ACTIVE','120503',current_timestamp),
('1221','22002','ACTIVE','120503',current_timestamp),
('1144','22002','ACTIVE','120503',current_timestamp),
('1192','22002','ACTIVE','120503',current_timestamp),
('1193','22002','ACTIVE','120503',current_timestamp),
('1194','22002','ACTIVE','120503',current_timestamp),
('1068','22002','ACTIVE','120503',current_timestamp),
('1069','22002','ACTIVE','120503',current_timestamp),
('1155','22002','ACTIVE','120503',current_timestamp),
('1156','22002','ACTIVE','120503',current_timestamp),
('120','22002','ACTIVE','120503',current_timestamp),
('123','22002','ACTIVE','120503',current_timestamp),
('141','22002','ACTIVE','120503',current_timestamp),
('144','22002','ACTIVE','120503',current_timestamp),
('166','22002','ACTIVE','120503',current_timestamp),
('171','22002','ACTIVE','120503',current_timestamp),
('191','22002','ACTIVE','120503',current_timestamp),
('194','22002','ACTIVE','120503',current_timestamp),
('208','22002','ACTIVE','120503',current_timestamp),
('211','22002','ACTIVE','120503',current_timestamp),
('218','22002','ACTIVE','120503',current_timestamp),
('237','22002','ACTIVE','120503',current_timestamp),
('247','22002','ACTIVE','120503',current_timestamp),
('256','22002','ACTIVE','120503',current_timestamp),
('373','22002','ACTIVE','120503',current_timestamp),
('813','22002','ACTIVE','120503',current_timestamp),
('1224','22002','ACTIVE','120503',current_timestamp),
('1043','22002','ACTIVE','120503',current_timestamp),
('12','22002','ACTIVE','120503',current_timestamp),
('13','22002','ACTIVE','120503',current_timestamp),
('46','22002','ACTIVE','120503',current_timestamp),
('55','22002','ACTIVE','120503',current_timestamp),
('57','22002','ACTIVE','120503',current_timestamp),
('62','22002','ACTIVE','120503',current_timestamp),
('85','22002','ACTIVE','120503',current_timestamp),
('86','22002','ACTIVE','120503',current_timestamp),
('99','22002','ACTIVE','120503',current_timestamp),
('101','22002','ACTIVE','120503',current_timestamp),
('117','22002','ACTIVE','120503',current_timestamp),
('188','22002','ACTIVE','120503',current_timestamp),
('200','22002','ACTIVE','120503',current_timestamp),
('232','22002','ACTIVE','120503',current_timestamp),
('233','22002','ACTIVE','120503',current_timestamp),
('303','22002','ACTIVE','120503',current_timestamp),
('305','22002','ACTIVE','120503',current_timestamp),
('318','22002','ACTIVE','120503',current_timestamp),
('319','22002','ACTIVE','120503',current_timestamp),
('322','22002','ACTIVE','120503',current_timestamp),
('365','22002','ACTIVE','120503',current_timestamp),
('382','22002','ACTIVE','120503',current_timestamp),
('606','22002','ACTIVE','120503',current_timestamp),
('607','22002','ACTIVE','120503',current_timestamp),
('626','22002','ACTIVE','120503',current_timestamp),
('1064','22002','ACTIVE','120503',current_timestamp),
('1086','22002','ACTIVE','120503',current_timestamp),
('1215','22002','ACTIVE','120503',current_timestamp),
('1071','22002','ACTIVE','120503',current_timestamp),
('1072','22002','ACTIVE','120503',current_timestamp),
('1073','22002','ACTIVE','120503',current_timestamp),
('1074','22002','ACTIVE','120503',current_timestamp),
('1078','22002','ACTIVE','120503',current_timestamp),
('1103','22002','ACTIVE','120503',current_timestamp),
('1110','22002','ACTIVE','120503',current_timestamp),
('1111','22002','ACTIVE','120503',current_timestamp),
('1124','22002','ACTIVE','120503',current_timestamp),
('1134','22002','ACTIVE','120503',current_timestamp),
('1135','22002','ACTIVE','120503',current_timestamp),
('1136','22002','ACTIVE','120503',current_timestamp),
('1137','22002','ACTIVE','120503',current_timestamp),
('1138','22002','ACTIVE','120503',current_timestamp),
('1139','22002','ACTIVE','120503',current_timestamp),
('1140','22002','ACTIVE','120503',current_timestamp),
('1165','22002','ACTIVE','120503',current_timestamp),
('1171','22002','ACTIVE','120503',current_timestamp),
('1176','22002','ACTIVE','120503',current_timestamp),
('1201','22002','ACTIVE','120503',current_timestamp),
('1213','22002','ACTIVE','120503',current_timestamp),
('1214','22002','ACTIVE','120503',current_timestamp),
('1225','22002','ACTIVE','120503',current_timestamp),
('14','22002','ACTIVE','120503',current_timestamp),
('15','22002','ACTIVE','120503',current_timestamp),
('16','22002','ACTIVE','120503',current_timestamp),
('20','22002','ACTIVE','120503',current_timestamp),
('21','22002','ACTIVE','120503',current_timestamp),
('36','22002','ACTIVE','120503',current_timestamp),
('38','22002','ACTIVE','120503',current_timestamp),
('40','22002','ACTIVE','120503',current_timestamp),
('42','22002','ACTIVE','120503',current_timestamp),
('45','22002','ACTIVE','120503',current_timestamp),
('60','22002','ACTIVE','120503',current_timestamp),
('64','22002','ACTIVE','120503',current_timestamp),
('77','22002','ACTIVE','120503',current_timestamp),
('100','22002','ACTIVE','120503',current_timestamp),
('102','22002','ACTIVE','120503',current_timestamp),
('103','22002','ACTIVE','120503',current_timestamp),
('104','22002','ACTIVE','120503',current_timestamp),
('105','22002','ACTIVE','120503',current_timestamp),
('106','22002','ACTIVE','120503',current_timestamp),
('107','22002','ACTIVE','120503',current_timestamp),
('111','22002','ACTIVE','120503',current_timestamp),
('115','22002','ACTIVE','120503',current_timestamp),
('116','22002','ACTIVE','120503',current_timestamp),
('126','22002','ACTIVE','120503',current_timestamp),
('132','22002','ACTIVE','120503',current_timestamp),
('138','22002','ACTIVE','120503',current_timestamp),
('147','22002','ACTIVE','120503',current_timestamp),
('150','22002','ACTIVE','120503',current_timestamp),
('151','22002','ACTIVE','120503',current_timestamp),
('157','22002','ACTIVE','120503',current_timestamp),
('164','22002','ACTIVE','120503',current_timestamp),
('176','22002','ACTIVE','120503',current_timestamp),
('212','22002','ACTIVE','120503',current_timestamp),
('219','22002','ACTIVE','120503',current_timestamp),
('220','22002','ACTIVE','120503',current_timestamp),
('221','22002','ACTIVE','120503',current_timestamp),
('222','22002','ACTIVE','120503',current_timestamp),
('223','22002','ACTIVE','120503',current_timestamp),
('224','22002','ACTIVE','120503',current_timestamp),
('225','22002','ACTIVE','120503',current_timestamp),
('226','22002','ACTIVE','120503',current_timestamp),
('227','22002','ACTIVE','120503',current_timestamp),
('228','22002','ACTIVE','120503',current_timestamp),
('229','22002','ACTIVE','120503',current_timestamp),
('230','22002','ACTIVE','120503',current_timestamp),
('248','22002','ACTIVE','120503',current_timestamp),
('253','22002','ACTIVE','120503',current_timestamp),
('258','22002','ACTIVE','120503',current_timestamp),
('259','22002','ACTIVE','120503',current_timestamp),
('267','22002','ACTIVE','120503',current_timestamp),
('283','22002','ACTIVE','120503',current_timestamp),
('302','22002','ACTIVE','120503',current_timestamp),
('312','22002','ACTIVE','120503',current_timestamp),
('313','22002','ACTIVE','120503',current_timestamp),
('323','22002','ACTIVE','120503',current_timestamp),
('324','22002','ACTIVE','120503',current_timestamp),
('336','22002','ACTIVE','120503',current_timestamp),
('337','22002','ACTIVE','120503',current_timestamp),
('341','22002','ACTIVE','120503',current_timestamp),
('347','22002','ACTIVE','120503',current_timestamp),
('350','22002','ACTIVE','120503',current_timestamp),
('351','22002','ACTIVE','120503',current_timestamp),
('352','22002','ACTIVE','120503',current_timestamp),
('353','22002','ACTIVE','120503',current_timestamp),
('354','22002','ACTIVE','120503',current_timestamp),
('355','22002','ACTIVE','120503',current_timestamp),
('359','22002','ACTIVE','120503',current_timestamp),
('360','22002','ACTIVE','120503',current_timestamp),
('366','22002','ACTIVE','120503',current_timestamp),
('380','22002','ACTIVE','120503',current_timestamp),
('381','22002','ACTIVE','120503',current_timestamp),
('387','22002','ACTIVE','120503',current_timestamp),
('394','22002','ACTIVE','120503',current_timestamp),
('533','22002','ACTIVE','120503',current_timestamp),
('534','22002','ACTIVE','120503',current_timestamp),
('537','22002','ACTIVE','120503',current_timestamp),
('538','22002','ACTIVE','120503',current_timestamp),
('602','22002','ACTIVE','120503',current_timestamp),
('603','22002','ACTIVE','120503',current_timestamp),
('617','22002','ACTIVE','120503',current_timestamp),
('620','22002','ACTIVE','120503',current_timestamp),
('631','22002','ACTIVE','120503',current_timestamp),
('688','22002','ACTIVE','120503',current_timestamp),
('695','22002','ACTIVE','120503',current_timestamp),
('717','22002','ACTIVE','120503',current_timestamp),
('724','22002','ACTIVE','120503',current_timestamp),
('725','22002','ACTIVE','120503',current_timestamp),
('731','22002','ACTIVE','120503',current_timestamp),
('732','22002','ACTIVE','120503',current_timestamp),
('734','22002','ACTIVE','120503',current_timestamp),
('740','22002','ACTIVE','120503',current_timestamp),
('779','22002','ACTIVE','120503',current_timestamp),
('865','22002','ACTIVE','120503',current_timestamp),
('879','22002','ACTIVE','120503',current_timestamp),
('880','22002','ACTIVE','120503',current_timestamp),
('888','22002','ACTIVE','120503',current_timestamp),
('907','22002','ACTIVE','120503',current_timestamp),
('936','22002','ACTIVE','120503',current_timestamp),
('939','22002','ACTIVE','120503',current_timestamp),
('943','22002','ACTIVE','120503',current_timestamp),
('945','22002','ACTIVE','120503',current_timestamp),
('947','22002','ACTIVE','120503',current_timestamp),
('948','22002','ACTIVE','120503',current_timestamp),
('951','22002','ACTIVE','120503',current_timestamp),
('975','22002','ACTIVE','120503',current_timestamp),
('976','22002','ACTIVE','120503',current_timestamp),
('1024','22002','ACTIVE','120503',current_timestamp),
('1049','22002','ACTIVE','120503',current_timestamp),
('1050','22002','ACTIVE','120503',current_timestamp),
('1053','22002','ACTIVE','120503',current_timestamp),
('1109','22002','ACTIVE','120503',current_timestamp),
('1125','22002','ACTIVE','120503',current_timestamp),
('1131','22002','ACTIVE','120503',current_timestamp),
('1147','22002','ACTIVE','120503',current_timestamp),
('1170','22002','ACTIVE','120503',current_timestamp),
('1206','22002','ACTIVE','120503',current_timestamp),
('491','22002','ACTIVE','120503',current_timestamp),
('492','22002','ACTIVE','120503',current_timestamp),
('493','22002','ACTIVE','120503',current_timestamp),
('494','22002','ACTIVE','120503',current_timestamp),
('495','22002','ACTIVE','120503',current_timestamp),
('500','22002','ACTIVE','120503',current_timestamp),
('501','22002','ACTIVE','120503',current_timestamp),
('502','22002','ACTIVE','120503',current_timestamp),
('503','22002','ACTIVE','120503',current_timestamp),
('504','22002','ACTIVE','120503',current_timestamp),
('507','22002','ACTIVE','120503',current_timestamp),
('508','22002','ACTIVE','120503',current_timestamp),
('509','22002','ACTIVE','120503',current_timestamp),
('516','22002','ACTIVE','120503',current_timestamp),
('517','22002','ACTIVE','120503',current_timestamp),
('535','22002','ACTIVE','120503',current_timestamp),
('683','22002','ACTIVE','120503',current_timestamp),
('727','22002','ACTIVE','120503',current_timestamp),
('770','22002','ACTIVE','120503',current_timestamp),
('789','22002','ACTIVE','120503',current_timestamp),
('795','22002','ACTIVE','120503',current_timestamp),
('802','22002','ACTIVE','120503',current_timestamp),
('815','22002','ACTIVE','120503',current_timestamp),
('829','22002','ACTIVE','120503',current_timestamp),
('838','22002','ACTIVE','120503',current_timestamp),
('841','22002','ACTIVE','120503',current_timestamp),
('871','22002','ACTIVE','120503',current_timestamp),
('881','22002','ACTIVE','120503',current_timestamp),
('882','22002','ACTIVE','120503',current_timestamp),
('891','22002','ACTIVE','120503',current_timestamp),
('902','22002','ACTIVE','120503',current_timestamp),
('909','22002','ACTIVE','120503',current_timestamp),
('923','22002','ACTIVE','120503',current_timestamp),
('950','22002','ACTIVE','120503',current_timestamp),
('958','22002','ACTIVE','120503',current_timestamp),
('959','22002','ACTIVE','120503',current_timestamp),
('960','22002','ACTIVE','120503',current_timestamp),
('961','22002','ACTIVE','120503',current_timestamp),
('965','22002','ACTIVE','120503',current_timestamp),
('967','22002','ACTIVE','120503',current_timestamp),
('968','22002','ACTIVE','120503',current_timestamp),
('1018','22002','ACTIVE','120503',current_timestamp),
('1019','22002','ACTIVE','120503',current_timestamp),
('7','22002','ACTIVE','120503',current_timestamp),
('23','22002','ACTIVE','120503',current_timestamp),
('27','22002','ACTIVE','120503',current_timestamp),
('32','22002','ACTIVE','120503',current_timestamp),
('44','22002','ACTIVE','120503',current_timestamp),
('48','22002','ACTIVE','120503',current_timestamp),
('50','22002','ACTIVE','120503',current_timestamp),
('51','22002','ACTIVE','120503',current_timestamp),
('52','22002','ACTIVE','120503',current_timestamp),
('53','22002','ACTIVE','120503',current_timestamp),
('63','22002','ACTIVE','120503',current_timestamp),
('66','22002','ACTIVE','120503',current_timestamp),
('71','22002','ACTIVE','120503',current_timestamp),
('72','22002','ACTIVE','120503',current_timestamp),
('78','22002','ACTIVE','120503',current_timestamp),
('89','22002','ACTIVE','120503',current_timestamp),
('90','22002','ACTIVE','120503',current_timestamp),
('93','22002','ACTIVE','120503',current_timestamp),
('110','22002','ACTIVE','120503',current_timestamp),
('119','22002','ACTIVE','120503',current_timestamp),
('121','22002','ACTIVE','120503',current_timestamp),
('134','22002','ACTIVE','120503',current_timestamp),
('146','22002','ACTIVE','120503',current_timestamp),
('165','22002','ACTIVE','120503',current_timestamp),
('198','22002','ACTIVE','120503',current_timestamp),
('217','22002','ACTIVE','120503',current_timestamp),
('262','22002','ACTIVE','120503',current_timestamp),
('264','22002','ACTIVE','120503',current_timestamp),
('278','22002','ACTIVE','120503',current_timestamp),
('285','22002','ACTIVE','120503',current_timestamp),
('291','22002','ACTIVE','120503',current_timestamp),
('298','22002','ACTIVE','120503',current_timestamp),
('306','22002','ACTIVE','120503',current_timestamp),
('316','22002','ACTIVE','120503',current_timestamp),
('325','22002','ACTIVE','120503',current_timestamp),
('344','22002','ACTIVE','120503',current_timestamp),
('379','22002','ACTIVE','120503',current_timestamp),
('388','22002','ACTIVE','120503',current_timestamp),
('389','22002','ACTIVE','120503',current_timestamp),
('390','22002','ACTIVE','120503',current_timestamp),
('391','22002','ACTIVE','120503',current_timestamp),
('395','22002','ACTIVE','120503',current_timestamp),
('542','22002','ACTIVE','120503',current_timestamp),
('548','22002','ACTIVE','120503',current_timestamp),
('818','22002','ACTIVE','120503',current_timestamp),
('825','22002','ACTIVE','120503',current_timestamp),
('831','22002','ACTIVE','120503',current_timestamp),
('858','22002','ACTIVE','120503',current_timestamp),
('859','22002','ACTIVE','120503',current_timestamp),
('875','22002','ACTIVE','120503',current_timestamp),
('911','22002','ACTIVE','120503',current_timestamp),
('974','22002','ACTIVE','120503',current_timestamp),
('995','22002','ACTIVE','120503',current_timestamp),
('1040','22002','ACTIVE','120503',current_timestamp),
('1041','22002','ACTIVE','120503',current_timestamp),
('1045','22002','ACTIVE','120503',current_timestamp),
('1052','22002','ACTIVE','120503',current_timestamp),
('1054','22002','ACTIVE','120503',current_timestamp),
('1056','22002','ACTIVE','120503',current_timestamp),
('1100','22002','ACTIVE','120503',current_timestamp),
('1127','22002','ACTIVE','120503',current_timestamp),
('1164','22002','ACTIVE','120503',current_timestamp),
('1181','22002','ACTIVE','120503',current_timestamp),
('1204','22002','ACTIVE','120503',current_timestamp),
('215','22002','ACTIVE','120503',current_timestamp),
('279','22002','ACTIVE','120503',current_timestamp),
('280','22002','ACTIVE','120503',current_timestamp),
('281','22002','ACTIVE','120503',current_timestamp),
('282','22002','ACTIVE','120503',current_timestamp),
('368','22002','ACTIVE','120503',current_timestamp),
('369','22002','ACTIVE','120503',current_timestamp),
('370','22002','ACTIVE','120503',current_timestamp),
('371','22002','ACTIVE','120503',current_timestamp),
('374','22002','ACTIVE','120503',current_timestamp),
('705','22002','ACTIVE','120503',current_timestamp),
('713','22002','ACTIVE','120503',current_timestamp),
('808','22002','ACTIVE','120503',current_timestamp),
('809','22002','ACTIVE','120503',current_timestamp),
('830','22002','ACTIVE','120503',current_timestamp),
('904','22002','ACTIVE','120503',current_timestamp),
('913','22002','ACTIVE','120503',current_timestamp),
('914','22002','ACTIVE','120503',current_timestamp),
('915','22002','ACTIVE','120503',current_timestamp),
('916','22002','ACTIVE','120503',current_timestamp),
('1076','22002','ACTIVE','120503',current_timestamp),
('29','22002','ACTIVE','120503',current_timestamp),
('30','22002','ACTIVE','120503',current_timestamp),
('31','22002','ACTIVE','120503',current_timestamp),
('41','22002','ACTIVE','120503',current_timestamp),
('47','22002','ACTIVE','120503',current_timestamp),
('88','22002','ACTIVE','120503',current_timestamp),
('118','22002','ACTIVE','120503',current_timestamp),
('124','22002','ACTIVE','120503',current_timestamp),
('125','22002','ACTIVE','120503',current_timestamp),
('129','22002','ACTIVE','120503',current_timestamp),
('135','22002','ACTIVE','120503',current_timestamp),
('136','22002','ACTIVE','120503',current_timestamp),
('137','22002','ACTIVE','120503',current_timestamp),
('139','22002','ACTIVE','120503',current_timestamp),
('140','22002','ACTIVE','120503',current_timestamp),
('154','22002','ACTIVE','120503',current_timestamp),
('161','22002','ACTIVE','120503',current_timestamp),
('162','22002','ACTIVE','120503',current_timestamp),
('173','22002','ACTIVE','120503',current_timestamp),
('175','22002','ACTIVE','120503',current_timestamp),
('178','22002','ACTIVE','120503',current_timestamp),
('213','22002','ACTIVE','120503',current_timestamp),
('235','22002','ACTIVE','120503',current_timestamp),
('243','22002','ACTIVE','120503',current_timestamp),
('252','22002','ACTIVE','120503',current_timestamp),
('265','22002','ACTIVE','120503',current_timestamp),
('269','22002','ACTIVE','120503',current_timestamp),
('288','22002','ACTIVE','120503',current_timestamp),
('289','22002','ACTIVE','120503',current_timestamp),
('295','22002','ACTIVE','120503',current_timestamp),
('304','22002','ACTIVE','120503',current_timestamp),
('317','22002','ACTIVE','120503',current_timestamp),
('320','22002','ACTIVE','120503',current_timestamp),
('321','22002','ACTIVE','120503',current_timestamp),
('326','22002','ACTIVE','120503',current_timestamp),
('338','22002','ACTIVE','120503',current_timestamp),
('339','22002','ACTIVE','120503',current_timestamp),
('362','22002','ACTIVE','120503',current_timestamp),
('363','22002','ACTIVE','120503',current_timestamp),
('386','22002','ACTIVE','120503',current_timestamp),
('392','22002','ACTIVE','120503',current_timestamp),
('393','22002','ACTIVE','120503',current_timestamp),
('539','22002','ACTIVE','120503',current_timestamp),
('563','22002','ACTIVE','120503',current_timestamp),
('621','22002','ACTIVE','120503',current_timestamp),
('696','22002','ACTIVE','120503',current_timestamp),
('783','22002','ACTIVE','120503',current_timestamp),
('784','22002','ACTIVE','120503',current_timestamp),
('785','22002','ACTIVE','120503',current_timestamp),
('786','22002','ACTIVE','120503',current_timestamp),
('803','22002','ACTIVE','120503',current_timestamp),
('1002','22002','ACTIVE','120503',current_timestamp),
('1003','22002','ACTIVE','120503',current_timestamp),
('1004','22002','ACTIVE','120503',current_timestamp),
('1009','22002','ACTIVE','120503',current_timestamp),
('1010','22002','ACTIVE','120503',current_timestamp),
('1011','22002','ACTIVE','120503',current_timestamp),
('1012','22002','ACTIVE','120503',current_timestamp),
('1013','22002','ACTIVE','120503',current_timestamp),
('1014','22002','ACTIVE','120503',current_timestamp),
('1015','22002','ACTIVE','120503',current_timestamp),
('1016','22002','ACTIVE','120503',current_timestamp),
('1042','22002','ACTIVE','120503',current_timestamp),
('1149','22002','ACTIVE','120503',current_timestamp),
('1150','22002','ACTIVE','120503',current_timestamp),
('1202','22002','ACTIVE','120503',current_timestamp),
('1218','22002','ACTIVE','120503',current_timestamp),
('1220','22002','ACTIVE','120503',current_timestamp),
('1087','22002','ACTIVE','120503',current_timestamp),
('1099','22002','ACTIVE','120503',current_timestamp),
('1','22002','ACTIVE','120503',current_timestamp),
('3','22002','ACTIVE','120503',current_timestamp),
('5','22002','ACTIVE','120503',current_timestamp),
('9','22002','ACTIVE','120503',current_timestamp),
('19','22002','ACTIVE','120503',current_timestamp),
('39','22002','ACTIVE','120503',current_timestamp),
('70','22002','ACTIVE','120503',current_timestamp),
('108','22002','ACTIVE','120503',current_timestamp),
('143','22002','ACTIVE','120503',current_timestamp),
('148','22002','ACTIVE','120503',current_timestamp),
('149','22002','ACTIVE','120503',current_timestamp),
('155','22002','ACTIVE','120503',current_timestamp),
('172','22002','ACTIVE','120503',current_timestamp),
('187','22002','ACTIVE','120503',current_timestamp),
('209','22002','ACTIVE','120503',current_timestamp),
('231','22002','ACTIVE','120503',current_timestamp),
('239','22002','ACTIVE','120503',current_timestamp),
('377','22002','ACTIVE','120503',current_timestamp),
('378','22002','ACTIVE','120503',current_timestamp),
('383','22002','ACTIVE','120503',current_timestamp),
('385','22002','ACTIVE','120503',current_timestamp),
('515','22002','ACTIVE','120503',current_timestamp),
('1141','22002','ACTIVE','120503',current_timestamp),
('1172','22002','ACTIVE','120503',current_timestamp),
('1175','22002','ACTIVE','120503',current_timestamp),
('1177','22002','ACTIVE','120503',current_timestamp),
('1212','22002','ACTIVE','120503',current_timestamp),
('1222','22002','ACTIVE','120503',current_timestamp),
('1223','22002','ACTIVE','120503',current_timestamp),
('633','22002','ACTIVE','120503',current_timestamp),
('400','22002','ACTIVE','120503',current_timestamp),
('401','22002','ACTIVE','120503',current_timestamp),
('402','22002','ACTIVE','120503',current_timestamp),
('403','22002','ACTIVE','120503',current_timestamp),
('405','22002','ACTIVE','120503',current_timestamp),
('406','22002','ACTIVE','120503',current_timestamp),
('407','22002','ACTIVE','120503',current_timestamp),
('408','22002','ACTIVE','120503',current_timestamp),
('409','22002','ACTIVE','120503',current_timestamp),
('415','22002','ACTIVE','120503',current_timestamp),
('416','22002','ACTIVE','120503',current_timestamp),
('419','22002','ACTIVE','120503',current_timestamp),
('426','22002','ACTIVE','120503',current_timestamp),
('427','22002','ACTIVE','120503',current_timestamp),
('428','22002','ACTIVE','120503',current_timestamp),
('433','22002','ACTIVE','120503',current_timestamp),
('435','22002','ACTIVE','120503',current_timestamp),
('439','22002','ACTIVE','120503',current_timestamp),
('440','22002','ACTIVE','120503',current_timestamp),
('441','22002','ACTIVE','120503',current_timestamp),
('443','22002','ACTIVE','120503',current_timestamp),
('445','22002','ACTIVE','120503',current_timestamp),
('446','22002','ACTIVE','120503',current_timestamp),
('448','22002','ACTIVE','120503',current_timestamp),
('450','22002','ACTIVE','120503',current_timestamp),
('458','22002','ACTIVE','120503',current_timestamp),
('462','22002','ACTIVE','120503',current_timestamp),
('463','22002','ACTIVE','120503',current_timestamp),
('464','22002','ACTIVE','120503',current_timestamp),
('470','22002','ACTIVE','120503',current_timestamp),
('471','22002','ACTIVE','120503',current_timestamp),
('472','22002','ACTIVE','120503',current_timestamp),
('473','22002','ACTIVE','120503',current_timestamp),
('474','22002','ACTIVE','120503',current_timestamp),
('475','22002','ACTIVE','120503',current_timestamp),
('477','22002','ACTIVE','120503',current_timestamp),
('479','22002','ACTIVE','120503',current_timestamp),
('481','22002','ACTIVE','120503',current_timestamp),
('483','22002','ACTIVE','120503',current_timestamp),
('484','22002','ACTIVE','120503',current_timestamp),
('485','22002','ACTIVE','120503',current_timestamp),
('486','22002','ACTIVE','120503',current_timestamp),
('487','22002','ACTIVE','120503',current_timestamp),
('488','22002','ACTIVE','120503',current_timestamp),
('489','22002','ACTIVE','120503',current_timestamp),
('525','22002','ACTIVE','120503',current_timestamp),
('530','22002','ACTIVE','120503',current_timestamp),
('546','22002','ACTIVE','120503',current_timestamp),
('567','22002','ACTIVE','120503',current_timestamp),
('569','22002','ACTIVE','120503',current_timestamp),
('570','22002','ACTIVE','120503',current_timestamp),
('571','22002','ACTIVE','120503',current_timestamp),
('574','22002','ACTIVE','120503',current_timestamp),
('576','22002','ACTIVE','120503',current_timestamp),
('577','22002','ACTIVE','120503',current_timestamp),
('592','22002','ACTIVE','120503',current_timestamp),
('593','22002','ACTIVE','120503',current_timestamp),
('594','22002','ACTIVE','120503',current_timestamp),
('597','22002','ACTIVE','120503',current_timestamp),
('598','22002','ACTIVE','120503',current_timestamp),
('599','22002','ACTIVE','120503',current_timestamp),
('600','22002','ACTIVE','120503',current_timestamp),
('650','22002','ACTIVE','120503',current_timestamp),
('656','22002','ACTIVE','120503',current_timestamp),
('665','22002','ACTIVE','120503',current_timestamp),
('674','22002','ACTIVE','120503',current_timestamp),
('716','22002','ACTIVE','120503',current_timestamp),
('730','22002','ACTIVE','120503',current_timestamp),
('736','22002','ACTIVE','120503',current_timestamp),
('739','22002','ACTIVE','120503',current_timestamp),
('751','22002','ACTIVE','120503',current_timestamp),
('752','22002','ACTIVE','120503',current_timestamp),
('842','22002','ACTIVE','120503',current_timestamp),
('852','22002','ACTIVE','120503',current_timestamp),
('873','22002','ACTIVE','120503',current_timestamp),
('877','22002','ACTIVE','120503',current_timestamp),
('964','22002','ACTIVE','120503',current_timestamp),
('984','22002','ACTIVE','120503',current_timestamp),
('580','22002','ACTIVE','120503',current_timestamp),
('854','22002','ACTIVE','120503',current_timestamp),
('1117','22002','ACTIVE','120503',current_timestamp),
('1120','22002','ACTIVE','120503',current_timestamp),
('1163','22002','ACTIVE','120503',current_timestamp),
('199','22002','ACTIVE','120503',current_timestamp),
('204','22002','ACTIVE','120503',current_timestamp),
('214','22002','ACTIVE','120503',current_timestamp),
('261','22002','ACTIVE','120503',current_timestamp),
('277','22002','ACTIVE','120503',current_timestamp),
('340','22002','ACTIVE','120503',current_timestamp),
('375','22002','ACTIVE','120503',current_timestamp),
('686','22002','ACTIVE','120503',current_timestamp),
('687','22002','ACTIVE','120503',current_timestamp),
('697','22002','ACTIVE','120503',current_timestamp),
('699','22002','ACTIVE','120503',current_timestamp),
('702','22002','ACTIVE','120503',current_timestamp),
('703','22002','ACTIVE','120503',current_timestamp),
('737','22002','ACTIVE','120503',current_timestamp),
('742','22002','ACTIVE','120503',current_timestamp),
('747','22002','ACTIVE','120503',current_timestamp),
('781','22002','ACTIVE','120503',current_timestamp),
('782','22002','ACTIVE','120503',current_timestamp),
('833','22002','ACTIVE','120503',current_timestamp),
('849','22002','ACTIVE','120503',current_timestamp),
('895','22002','ACTIVE','120503',current_timestamp),
('897','22002','ACTIVE','120503',current_timestamp),
('898','22002','ACTIVE','120503',current_timestamp),
('963','22002','ACTIVE','120503',current_timestamp),
('977','22002','ACTIVE','120503',current_timestamp),
('1122','22002','ACTIVE','120503',current_timestamp),
('1154','22002','ACTIVE','120503',current_timestamp),
('616','22002','ACTIVE','120503',current_timestamp),
('832','22002','ACTIVE','120503',current_timestamp),
('1112','22002','ACTIVE','120503',current_timestamp),
('1113','22002','ACTIVE','120503',current_timestamp),
('1114','22002','ACTIVE','120503',current_timestamp),
('1115','22002','ACTIVE','120503',current_timestamp),
('1116','22002','ACTIVE','120503',current_timestamp),
('528','22002','ACTIVE','120503',current_timestamp),
('529','22002','ACTIVE','120503',current_timestamp),
('547','22002','ACTIVE','120503',current_timestamp),
('575','22002','ACTIVE','120503',current_timestamp),
('627','22002','ACTIVE','120503',current_timestamp),
('735','22002','ACTIVE','120503',current_timestamp),
('741','22002','ACTIVE','120503',current_timestamp),
('749','22002','ACTIVE','120503',current_timestamp),
('804','22002','ACTIVE','120503',current_timestamp),
('820','22002','ACTIVE','120503',current_timestamp),
('989','22002','ACTIVE','120503',current_timestamp);



UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = null;

UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = null;

UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100956;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219094' WHERE PRODUCT_ID = 100957;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96110000' WHERE PRODUCT_ID = 100958;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100959;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100960;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96110000' WHERE PRODUCT_ID = 100961;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '8055000' WHERE PRODUCT_ID = 100962;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 100963;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '10063020' WHERE PRODUCT_ID = 100964;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39189090' WHERE PRODUCT_ID = 100965;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39189090' WHERE PRODUCT_ID = 100966;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '11063090' WHERE PRODUCT_ID = 100967;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100566;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100968;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100969;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96089990' WHERE PRODUCT_ID = 100970;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '08031090' WHERE PRODUCT_ID = 100971;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40170030' WHERE PRODUCT_ID = 100972;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '12119060' WHERE PRODUCT_ID = 100973;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '12119070' WHERE PRODUCT_ID = 100974;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '11063030' WHERE PRODUCT_ID = 100975;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07131000' WHERE PRODUCT_ID = 100976;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84181010' WHERE PRODUCT_ID = 100977;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07099300' WHERE PRODUCT_ID = 100978;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85351010' WHERE PRODUCT_ID = 100979;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100056;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039010' WHERE PRODUCT_ID = 100980;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39239090' WHERE PRODUCT_ID = 100981;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 100982;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100983;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100984;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07061000' WHERE PRODUCT_ID = 100985;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100986;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100987;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100988;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100989;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07041000' WHERE PRODUCT_ID = 100990;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100787;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049270' WHERE PRODUCT_ID = 100991;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100992;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100993;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '11063090' WHERE PRODUCT_ID = 100994;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4062000' WHERE PRODUCT_ID = 100590;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '02109900' WHERE PRODUCT_ID = 100995;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '02109900' WHERE PRODUCT_ID = 100996;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '02109900' WHERE PRODUCT_ID = 100997;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '08109090' WHERE PRODUCT_ID = 100998;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 100999;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44101110' WHERE PRODUCT_ID = 101000;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 101001;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84796000' WHERE PRODUCT_ID = 101002;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48239013' WHERE PRODUCT_ID = 101003;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74181039' WHERE PRODUCT_ID = 101004;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '09096149' WHERE PRODUCT_ID = 101005;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '09101130' WHERE PRODUCT_ID = 101006;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '09096141' WHERE PRODUCT_ID = 101007;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '11022000' WHERE PRODUCT_ID = 101008;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 101009;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 101010;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 101011;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '08109040' WHERE PRODUCT_ID = 101012;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82149090' WHERE PRODUCT_ID = 101013;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 101014;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 101015;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '04059020' WHERE PRODUCT_ID = 101016;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 101017;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '04081100' WHERE PRODUCT_ID = 101018;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '04022990' WHERE PRODUCT_ID = 101019;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 101020;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07031010' WHERE PRODUCT_ID = 101021;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07031010' WHERE PRODUCT_ID = 101022;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96138090' WHERE PRODUCT_ID = 101023;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49090090' WHERE PRODUCT_ID = 101024;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40151100' WHERE PRODUCT_ID = 101025;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 101026;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 101027;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 101028;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 101029;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 101030;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '25169090' WHERE PRODUCT_ID = 101031;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07096010' WHERE PRODUCT_ID = 101032;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07096010' WHERE PRODUCT_ID = 101033;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 101034;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 101035;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 101036;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 101037;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 101038;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76152090' WHERE PRODUCT_ID = 101039;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21032000' WHERE PRODUCT_ID = 101040;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 101041;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '09092110' WHERE PRODUCT_ID = 101042;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 101043;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9102010' WHERE PRODUCT_ID = 101044;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9102010' WHERE PRODUCT_ID = 101045;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '08062090' WHERE PRODUCT_ID = 101046;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9102010' WHERE PRODUCT_ID = 101047;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 101048;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232100' WHERE PRODUCT_ID = 101049;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232100' WHERE PRODUCT_ID = 101050;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232100' WHERE PRODUCT_ID = 101051;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232100' WHERE PRODUCT_ID = 101052;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232100' WHERE PRODUCT_ID = 101053;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232100' WHERE PRODUCT_ID = 101054;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94055039' WHERE PRODUCT_ID = 101055;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '27111100' WHERE PRODUCT_ID = 101056;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '11063090' WHERE PRODUCT_ID = 101057;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '08041010' WHERE PRODUCT_ID = 101058;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07101000' WHERE PRODUCT_ID = 101059;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21032000' WHERE PRODUCT_ID = 101060;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82159900' WHERE PRODUCT_ID = 101061;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16023900' WHERE PRODUCT_ID = 101062;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63071020' WHERE PRODUCT_ID = 101063;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63071020' WHERE PRODUCT_ID = 101064;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239190' WHERE PRODUCT_ID = 101065;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '11063090' WHERE PRODUCT_ID = 101066;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '11063090' WHERE PRODUCT_ID = 101067;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 101068;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 101069;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 101070;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07096010' WHERE PRODUCT_ID = 101071;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07096010' WHERE PRODUCT_ID = 101072;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '12072010' WHERE PRODUCT_ID = 101073;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '02044300' WHERE PRODUCT_ID = 101074;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '15099090' WHERE PRODUCT_ID = 101075;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07031020' WHERE PRODUCT_ID = 101076;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '66019900' WHERE PRODUCT_ID = 101077;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 101078;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 101079;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '04069000' WHERE PRODUCT_ID = 101080;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 101081;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '08134090' WHERE PRODUCT_ID = 101082;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 101083;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 101084;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40159030' WHERE PRODUCT_ID = 101085;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84222000' WHERE PRODUCT_ID = 101086;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07031020' WHERE PRODUCT_ID = 101087;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07031020' WHERE PRODUCT_ID = 101088;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239190' WHERE PRODUCT_ID = 101089;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 101090;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85162100' WHERE PRODUCT_ID = 101091;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 101092;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9101110' WHERE PRODUCT_ID = 101093;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '15043000' WHERE PRODUCT_ID = 101094;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96089990' WHERE PRODUCT_ID = 101095;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 101096;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 101097;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 101098;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 101099;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '12119070' WHERE PRODUCT_ID = 101100;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101101;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101102;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101103;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101104;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101105;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101106;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101107;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101108;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101109;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101110;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 101111;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '25010090' WHERE PRODUCT_ID = 101112;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '10064000' WHERE PRODUCT_ID = 101113;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19021900' WHERE PRODUCT_ID = 101114;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 101115;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '30039033' WHERE PRODUCT_ID = 101116;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9101110' WHERE PRODUCT_ID = 101117;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 101118;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21069099' WHERE PRODUCT_ID = 101119;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239190' WHERE PRODUCT_ID = 101120;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07097000' WHERE PRODUCT_ID = 101121;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07097000' WHERE PRODUCT_ID = 101122;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07097000' WHERE PRODUCT_ID = 101123;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '08041010' WHERE PRODUCT_ID = 101124;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20058000' WHERE PRODUCT_ID = 101125;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 101126;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 101127;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 101128;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69111019' WHERE PRODUCT_ID = 101129;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21032000' WHERE PRODUCT_ID = 101130;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84433240' WHERE PRODUCT_ID = 101131;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 101132;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 101133;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 101134;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22090090' WHERE PRODUCT_ID = 101135;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76110000' WHERE PRODUCT_ID = 100698;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84239020' WHERE PRODUCT_ID = 101136;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '11063090' WHERE PRODUCT_ID = 101137;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '42021110' WHERE PRODUCT_ID = 101138;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '33030020' WHERE PRODUCT_ID = 100294;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '12119070' WHERE PRODUCT_ID = 100370;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100779;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100014;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100015;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '15179090' WHERE PRODUCT_ID = 100089;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69041000' WHERE PRODUCT_ID = 100111;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100118;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100121;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100215;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63071090' WHERE PRODUCT_ID = 100268;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61099010' WHERE PRODUCT_ID = 100278;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61099010' WHERE PRODUCT_ID = 100279;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61099010' WHERE PRODUCT_ID = 100280;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61099010' WHERE PRODUCT_ID = 100281;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100366;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100367;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100368;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100369;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049260' WHERE PRODUCT_ID = 100372;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100665;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100673;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049260' WHERE PRODUCT_ID = 100677;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049260' WHERE PRODUCT_ID = 100725;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049260' WHERE PRODUCT_ID = 100726;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049260' WHERE PRODUCT_ID = 100727;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049260' WHERE PRODUCT_ID = 100728;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100757;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 100767;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 100768;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '95037010' WHERE PRODUCT_ID = 100770;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049260' WHERE PRODUCT_ID = 100800;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '63049260' WHERE PRODUCT_ID = 100801;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100814;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 100818;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '35061000' WHERE PRODUCT_ID = 100822;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '62011210' WHERE PRODUCT_ID = 100831;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100841;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100842;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100843;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '61091000' WHERE PRODUCT_ID = 100844;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100879;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85365020' WHERE PRODUCT_ID = 100893;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100896;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '83030000' WHERE PRODUCT_ID = 100071;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85167200' WHERE PRODUCT_ID = 100109;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84101100' WHERE PRODUCT_ID = 100291;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84101100' WHERE PRODUCT_ID = 100292;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85167200' WHERE PRODUCT_ID = 100376;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94019000' WHERE PRODUCT_ID = 100409;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73089090' WHERE PRODUCT_ID = 100434;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100435;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73089090' WHERE PRODUCT_ID = 100475;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85392110' WHERE PRODUCT_ID = 100477;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40149090' WHERE PRODUCT_ID = 100478;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100479;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85162100' WHERE PRODUCT_ID = 100529;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84148090' WHERE PRODUCT_ID = 100531;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73089090' WHERE PRODUCT_ID = 100533;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69041000' WHERE PRODUCT_ID = 100543;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73089090' WHERE PRODUCT_ID = 100546;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73211190' WHERE PRODUCT_ID = 100548;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73211190' WHERE PRODUCT_ID = 100549;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94019000' WHERE PRODUCT_ID = 100559;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94019000' WHERE PRODUCT_ID = 100567;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94019000' WHERE PRODUCT_ID = 100568;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94019000' WHERE PRODUCT_ID = 100569;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73089090' WHERE PRODUCT_ID = 100578;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100579;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84181010' WHERE PRODUCT_ID = 100602;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69041000' WHERE PRODUCT_ID = 100615;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239990' WHERE PRODUCT_ID = 100617;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239990' WHERE PRODUCT_ID = 100618;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73089090' WHERE PRODUCT_ID = 100625;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239990' WHERE PRODUCT_ID = 100627;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239990' WHERE PRODUCT_ID = 100628;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100629;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85162100' WHERE PRODUCT_ID = 100630;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239990' WHERE PRODUCT_ID = 100633;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84191920' WHERE PRODUCT_ID = 100634;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73021090' WHERE PRODUCT_ID = 100635;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73021090' WHERE PRODUCT_ID = 100636;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100637;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100643;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100644;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69099000' WHERE PRODUCT_ID = 100646;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100649;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '68022200' WHERE PRODUCT_ID = 100651;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74153310' WHERE PRODUCT_ID = 100652;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100655;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84151010' WHERE PRODUCT_ID = 100657;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84151010' WHERE PRODUCT_ID = 100658;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100659;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100661;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100672;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100676;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100685;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84181010' WHERE PRODUCT_ID = 100689;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76109090' WHERE PRODUCT_ID = 100693;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74153310' WHERE PRODUCT_ID = 100694;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69109000' WHERE PRODUCT_ID = 100700;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84388090' WHERE PRODUCT_ID = 100701;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76109090' WHERE PRODUCT_ID = 100704;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100723;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100724;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '87142020' WHERE PRODUCT_ID = 100746;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '87119099' WHERE PRODUCT_ID = 100760;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '87119099' WHERE PRODUCT_ID = 100761;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84181010' WHERE PRODUCT_ID = 100773;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85044030' WHERE PRODUCT_ID = 100783;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70159090' WHERE PRODUCT_ID = 100799;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84181010' WHERE PRODUCT_ID = 100807;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100826;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85361010' WHERE PRODUCT_ID = 100827;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84145990' WHERE PRODUCT_ID = 100892;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73089090' WHERE PRODUCT_ID = 100897;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39189090' WHERE PRODUCT_ID = 100905;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20019000' WHERE PRODUCT_ID = 100025;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20039090' WHERE PRODUCT_ID = 100250;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '07131000' WHERE PRODUCT_ID = 100851;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '08062090' WHERE PRODUCT_ID = 100862;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19022010' WHERE PRODUCT_ID = 100880;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100004;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100006;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '8045040' WHERE PRODUCT_ID = 100008;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109914' WHERE PRODUCT_ID = 100017;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059020' WHERE PRODUCT_ID = 100018;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '8021100' WHERE PRODUCT_ID = 100019;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21023000' WHERE PRODUCT_ID = 100023;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100026;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21069099' WHERE PRODUCT_ID = 100029;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9041130' WHERE PRODUCT_ID = 100035;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '25010090' WHERE PRODUCT_ID = 100036;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100038;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '18062000' WHERE PRODUCT_ID = 100044;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 100050;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4051000' WHERE PRODUCT_ID = 100055;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4051000' WHERE PRODUCT_ID = 100059;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4051000' WHERE PRODUCT_ID = 100060;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100062;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '7096010' WHERE PRODUCT_ID = 100066;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9083190' WHERE PRODUCT_ID = 100068;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9083190' WHERE PRODUCT_ID = 100069;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100070;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100076;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109100' WHERE PRODUCT_ID = 100077;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 100080;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '18062000' WHERE PRODUCT_ID = 100084;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '18062000' WHERE PRODUCT_ID = 100085;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100088;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 100092;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9071010' WHERE PRODUCT_ID = 100093;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9011190' WHERE PRODUCT_ID = 100095;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9011190' WHERE PRODUCT_ID = 100096;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100097;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100098;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100099;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100112;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '7070000' WHERE PRODUCT_ID = 100113;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100114;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100120;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024030' WHERE PRODUCT_ID = 100122;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100123;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100127;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100128;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100130;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100131;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100141;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100144;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100145;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100153;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9101110' WHERE PRODUCT_ID = 100159;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9101110' WHERE PRODUCT_ID = 100160;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100166;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '7096010' WHERE PRODUCT_ID = 100168;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9022020' WHERE PRODUCT_ID = 100170;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9022090' WHERE PRODUCT_ID = 100171;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40151100' WHERE PRODUCT_ID = 100174;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4090000' WHERE PRODUCT_ID = 100180;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100183;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100184;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100185;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22019010' WHERE PRODUCT_ID = 100186;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100190;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100191;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100192;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100194;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9102010' WHERE PRODUCT_ID = 100196;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21032000' WHERE PRODUCT_ID = 100197;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100202;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100203;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22021020' WHERE PRODUCT_ID = 100206;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100207;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100208;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '8055000' WHERE PRODUCT_ID = 100210;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100211;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109100' WHERE PRODUCT_ID = 100218;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4021090' WHERE PRODUCT_ID = 100234;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9022090' WHERE PRODUCT_ID = 100237;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '12119070' WHERE PRODUCT_ID = 100238;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20079990' WHERE PRODUCT_ID = 100241;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20079990' WHERE PRODUCT_ID = 100242;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100245;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100246;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100247;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '7031010' WHERE PRODUCT_ID = 100254;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100255;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100259;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100267;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100269;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100270;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19049000' WHERE PRODUCT_ID = 100271;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100273;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 100274;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19054000' WHERE PRODUCT_ID = 100297;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9021090' WHERE PRODUCT_ID = 100298;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9096111' WHERE PRODUCT_ID = 100299;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 100305;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 100308;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219099' WHERE PRODUCT_ID = 100309;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22011020' WHERE PRODUCT_ID = 100312;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100325;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100326;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100327;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39173990' WHERE PRODUCT_ID = 100328;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '17021190' WHERE PRODUCT_ID = 100330;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '17021190' WHERE PRODUCT_ID = 100331;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '17019100' WHERE PRODUCT_ID = 100332;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '17021190' WHERE PRODUCT_ID = 100333;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100340;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100341;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100343;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100344;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100346;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100347;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4031000' WHERE PRODUCT_ID = 100356;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100359;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '7020000' WHERE PRODUCT_ID = 100362;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 100365;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100371;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22029090' WHERE PRODUCT_ID = 100394;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059020' WHERE PRODUCT_ID = 100395;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059020' WHERE PRODUCT_ID = 100396;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100511;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100512;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100517;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100518;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '7129020' WHERE PRODUCT_ID = 100519;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19030000' WHERE PRODUCT_ID = 100587;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219010' WHERE PRODUCT_ID = 100591;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219010' WHERE PRODUCT_ID = 100600;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100601;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100666;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100671;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100674;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100675;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100681;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100682;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100699;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100708;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100715;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19023090' WHERE PRODUCT_ID = 100716;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76151030' WHERE PRODUCT_ID = 100717;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100739;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100740;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100741;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100742;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059040' WHERE PRODUCT_ID = 100752;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19053290' WHERE PRODUCT_ID = 100753;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '7096010' WHERE PRODUCT_ID = 100755;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039090' WHERE PRODUCT_ID = 100756;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '17011410' WHERE PRODUCT_ID = 100758;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4051000' WHERE PRODUCT_ID = 100764;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100774;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100775;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100776;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100777;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76169920' WHERE PRODUCT_ID = 100795;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9109990' WHERE PRODUCT_ID = 100796;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19053211' WHERE PRODUCT_ID = 100797;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100811;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100819;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100828;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21012010' WHERE PRODUCT_ID = 100832;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9022090' WHERE PRODUCT_ID = 100834;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22019090' WHERE PRODUCT_ID = 100845;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22019090' WHERE PRODUCT_ID = 100846;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22019090' WHERE PRODUCT_ID = 100847;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22019090' WHERE PRODUCT_ID = 100848;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22019090' WHERE PRODUCT_ID = 100849;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '22019090' WHERE PRODUCT_ID = 100850;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100856;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19054000' WHERE PRODUCT_ID = 100864;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '10040090' WHERE PRODUCT_ID = 100865;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100867;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '8011990' WHERE PRODUCT_ID = 100868;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '17049030' WHERE PRODUCT_ID = 100874;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100877;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059010' WHERE PRODUCT_ID = 100878;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100895;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48091090' WHERE PRODUCT_ID = 100007;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100012;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100013;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100016;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76152090' WHERE PRODUCT_ID = 100021;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76152090' WHERE PRODUCT_ID = 100022;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96089990' WHERE PRODUCT_ID = 100024;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48171000' WHERE PRODUCT_ID = 100028;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232990' WHERE PRODUCT_ID = 100030;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232990' WHERE PRODUCT_ID = 100031;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39232990' WHERE PRODUCT_ID = 100032;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96100000' WHERE PRODUCT_ID = 100033;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96100000' WHERE PRODUCT_ID = 100037;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '72254011' WHERE PRODUCT_ID = 100039;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '83030000' WHERE PRODUCT_ID = 100041;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96032900' WHERE PRODUCT_ID = 100042;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '42029900' WHERE PRODUCT_ID = 100043;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100045;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100046;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82083000' WHERE PRODUCT_ID = 100047;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96039000' WHERE PRODUCT_ID = 100048;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39199010' WHERE PRODUCT_ID = 100049;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100051;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100052;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100053;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100054;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82083000' WHERE PRODUCT_ID = 100058;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84722000' WHERE PRODUCT_ID = 100061;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96034020' WHERE PRODUCT_ID = 100063;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84701000' WHERE PRODUCT_ID = 100064;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34060010' WHERE PRODUCT_ID = 100065;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48091090' WHERE PRODUCT_ID = 100067;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100072;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100073;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100074;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100075;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100078;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96099030' WHERE PRODUCT_ID = 100079;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44101110' WHERE PRODUCT_ID = 100086;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44101110' WHERE PRODUCT_ID = 100087;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39199010' WHERE PRODUCT_ID = 100090;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39199010' WHERE PRODUCT_ID = 100091;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100094;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100100;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100101;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100102;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100103;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100104;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100105;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100106;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100107;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100108;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96082000' WHERE PRODUCT_ID = 100110;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100115;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100116;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100117;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100119;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120090' WHERE PRODUCT_ID = 100125;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 100126;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190020' WHERE PRODUCT_ID = 100129;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39181010' WHERE PRODUCT_ID = 100132;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40169100' WHERE PRODUCT_ID = 100133;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39199010' WHERE PRODUCT_ID = 100134;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96031000' WHERE PRODUCT_ID = 100135;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96031000' WHERE PRODUCT_ID = 100136;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100137;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96034010' WHERE PRODUCT_ID = 100138;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96034010' WHERE PRODUCT_ID = 100139;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96039000' WHERE PRODUCT_ID = 100140;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40169200' WHERE PRODUCT_ID = 100146;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85351010' WHERE PRODUCT_ID = 100147;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100150;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100151;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100152;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100154;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39172390' WHERE PRODUCT_ID = 100157;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100158;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100161;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96034010' WHERE PRODUCT_ID = 100162;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100163;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100164;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '35011000' WHERE PRODUCT_ID = 100165;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85109000' WHERE PRODUCT_ID = 100173;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100175;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100176;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44170000' WHERE PRODUCT_ID = 100178;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39211390' WHERE PRODUCT_ID = 100188;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85143090' WHERE PRODUCT_ID = 100189;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100198;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82083000' WHERE PRODUCT_ID = 100200;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48182000' WHERE PRODUCT_ID = 100212;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96039000' WHERE PRODUCT_ID = 100213;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100217;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100219;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100220;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100221;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100222;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100223;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100224;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100225;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100226;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100227;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100228;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100229;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82159900' WHERE PRODUCT_ID = 100230;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100232;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100233;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100235;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96031000' WHERE PRODUCT_ID = 100243;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '92081000' WHERE PRODUCT_ID = 100248;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96162000' WHERE PRODUCT_ID = 100251;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '27101980' WHERE PRODUCT_ID = 100252;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82159900' WHERE PRODUCT_ID = 100257;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100258;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96099010' WHERE PRODUCT_ID = 100261;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96082000' WHERE PRODUCT_ID = 100263;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96039000' WHERE PRODUCT_ID = 100264;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100265;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100266;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40170030' WHERE PRODUCT_ID = 100277;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100282;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100283;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84624910' WHERE PRODUCT_ID = 100284;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100285;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100286;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100287;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100288;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100290;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96162000' WHERE PRODUCT_ID = 100293;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96110000' WHERE PRODUCT_ID = 100295;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40169320' WHERE PRODUCT_ID = 100296;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82130000' WHERE PRODUCT_ID = 100300;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76152090' WHERE PRODUCT_ID = 100301;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96034010' WHERE PRODUCT_ID = 100302;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39211390' WHERE PRODUCT_ID = 100303;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82141010' WHERE PRODUCT_ID = 100304;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '32131000' WHERE PRODUCT_ID = 100307;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '78060010' WHERE PRODUCT_ID = 100310;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84722000' WHERE PRODUCT_ID = 100311;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100314;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96034010' WHERE PRODUCT_ID = 100315;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82159900' WHERE PRODUCT_ID = 100316;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82159900' WHERE PRODUCT_ID = 100317;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96162000' WHERE PRODUCT_ID = 100318;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 100319;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84248990' WHERE PRODUCT_ID = 100320;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74181039' WHERE PRODUCT_ID = 100321;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 100322;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100323;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239990' WHERE PRODUCT_ID = 100324;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100334;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100335;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100336;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100337;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100339;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84722000' WHERE PRODUCT_ID = 100342;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100345;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100348;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100349;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100350;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100351;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100352;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '90269000' WHERE PRODUCT_ID = 100353;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '91051090' WHERE PRODUCT_ID = 100357;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74181039' WHERE PRODUCT_ID = 100358;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44170000' WHERE PRODUCT_ID = 100360;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48030010' WHERE PRODUCT_ID = 100361;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100363;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '42021190' WHERE PRODUCT_ID = 100364;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96034010' WHERE PRODUCT_ID = 100377;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74181039' WHERE PRODUCT_ID = 100378;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 100379;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 100380;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96031000' WHERE PRODUCT_ID = 100384;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96031000' WHERE PRODUCT_ID = 100385;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96034010' WHERE PRODUCT_ID = 100386;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96082000' WHERE PRODUCT_ID = 100387;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48171000' WHERE PRODUCT_ID = 100388;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48171000' WHERE PRODUCT_ID = 100389;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100390;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100391;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100392;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100393;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100489;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100490;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100491;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100492;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100493;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100494;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100495;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39211390' WHERE PRODUCT_ID = 100496;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100497;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100498;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82031000' WHERE PRODUCT_ID = 100499;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49111010' WHERE PRODUCT_ID = 100500;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100501;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49111010' WHERE PRODUCT_ID = 100502;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49111010' WHERE PRODUCT_ID = 100503;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49111010' WHERE PRODUCT_ID = 100504;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100505;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100506;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '66019900' WHERE PRODUCT_ID = 100507;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100509;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100510;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84729010' WHERE PRODUCT_ID = 100516;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190020' WHERE PRODUCT_ID = 100520;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100521;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100522;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84198960' WHERE PRODUCT_ID = 100523;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100524;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73151100' WHERE PRODUCT_ID = 100525;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44211000' WHERE PRODUCT_ID = 100526;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '34011942' WHERE PRODUCT_ID = 100527;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73170099' WHERE PRODUCT_ID = 100535;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85365020' WHERE PRODUCT_ID = 100544;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85365020' WHERE PRODUCT_ID = 100545;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100547;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100550;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100588;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100589;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100592;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100593;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100603;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85143090' WHERE PRODUCT_ID = 100604;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190020' WHERE PRODUCT_ID = 100605;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85143090' WHERE PRODUCT_ID = 100606;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '83071000' WHERE PRODUCT_ID = 100607;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '83071000' WHERE PRODUCT_ID = 100608;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '83071000' WHERE PRODUCT_ID = 100609;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '83071000' WHERE PRODUCT_ID = 100610;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100611;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239190' WHERE PRODUCT_ID = 100614;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39219010' WHERE PRODUCT_ID = 100667;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100668;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100679;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85176920' WHERE PRODUCT_ID = 100680;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85069000' WHERE PRODUCT_ID = 100683;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100684;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '92079000' WHERE PRODUCT_ID = 100686;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '97019099' WHERE PRODUCT_ID = 100687;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '97019099' WHERE PRODUCT_ID = 100688;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76152090' WHERE PRODUCT_ID = 100690;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76152090' WHERE PRODUCT_ID = 100691;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74181039' WHERE PRODUCT_ID = 100692;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100697;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100710;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44042090' WHERE PRODUCT_ID = 100711;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100712;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73141490' WHERE PRODUCT_ID = 100713;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100714;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100720;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100721;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73221100' WHERE PRODUCT_ID = 100722;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96089990' WHERE PRODUCT_ID = 100729;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100730;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49090090' WHERE PRODUCT_ID = 100731;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49090090' WHERE PRODUCT_ID = 100732;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49090090' WHERE PRODUCT_ID = 100733;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44219060' WHERE PRODUCT_ID = 100734;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39261091' WHERE PRODUCT_ID = 100735;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100736;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100737;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100738;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100743;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100744;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100750;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100751;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100759;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100762;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100763;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100765;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73221900' WHERE PRODUCT_ID = 100766;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 100769;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '41139000' WHERE PRODUCT_ID = 100771;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100772;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49090090' WHERE PRODUCT_ID = 100778;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100780;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100781;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76152090' WHERE PRODUCT_ID = 100784;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49100090' WHERE PRODUCT_ID = 100791;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96110000' WHERE PRODUCT_ID = 100792;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100793;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49090090' WHERE PRODUCT_ID = 100794;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73102110' WHERE PRODUCT_ID = 100798;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73012010' WHERE PRODUCT_ID = 100802;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100803;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100804;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48091090' WHERE PRODUCT_ID = 100808;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100812;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100813;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100815;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48171000' WHERE PRODUCT_ID = 100816;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48171000' WHERE PRODUCT_ID = 100817;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49090090' WHERE PRODUCT_ID = 100820;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100821;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49011010' WHERE PRODUCT_ID = 100823;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49021010' WHERE PRODUCT_ID = 100824;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100829;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84722000' WHERE PRODUCT_ID = 100833;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84152090' WHERE PRODUCT_ID = 100835;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48201010' WHERE PRODUCT_ID = 100840;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100858;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100859;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100860;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84421000' WHERE PRODUCT_ID = 100861;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69120020' WHERE PRODUCT_ID = 100863;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39249090' WHERE PRODUCT_ID = 100866;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '82055110' WHERE PRODUCT_ID = 100869;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39241090' WHERE PRODUCT_ID = 100870;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100871;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100872;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39162011' WHERE PRODUCT_ID = 100873;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100875;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239190' WHERE PRODUCT_ID = 100876;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73141490' WHERE PRODUCT_ID = 100883;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73141490' WHERE PRODUCT_ID = 100884;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73141490' WHERE PRODUCT_ID = 100885;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73141490' WHERE PRODUCT_ID = 100886;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100888;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100889;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100890;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '49119990' WHERE PRODUCT_ID = 100891;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100894;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39221000' WHERE PRODUCT_ID = 100001;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39221000' WHERE PRODUCT_ID = 100003;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39221000' WHERE PRODUCT_ID = 100005;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84151010' WHERE PRODUCT_ID = 100009;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100020;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85143090' WHERE PRODUCT_ID = 100040;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '83030000' WHERE PRODUCT_ID = 100143;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84241000' WHERE PRODUCT_ID = 100148;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84241000' WHERE PRODUCT_ID = 100149;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84158290' WHERE PRODUCT_ID = 100155;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85166000' WHERE PRODUCT_ID = 100172;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84158290' WHERE PRODUCT_ID = 100187;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 100199;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85171919' WHERE PRODUCT_ID = 100204;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84351000' WHERE PRODUCT_ID = 100209;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 100214;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85143090' WHERE PRODUCT_ID = 100231;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84602910' WHERE PRODUCT_ID = 100239;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85171919' WHERE PRODUCT_ID = 100240;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 100244;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 100260;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 100275;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84433240' WHERE PRODUCT_ID = 100276;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 100338;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70071900' WHERE PRODUCT_ID = 100354;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70071900' WHERE PRODUCT_ID = 100355;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 100373;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84158290' WHERE PRODUCT_ID = 100375;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84186920' WHERE PRODUCT_ID = 100381;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76110000' WHERE PRODUCT_ID = 100382;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84231000' WHERE PRODUCT_ID = 100383;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100397;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100398;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100399;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100400;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100401;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100402;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100403;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100404;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100405;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100406;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100407;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100408;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100410;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100411;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100412;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94021090' WHERE PRODUCT_ID = 100413;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94021090' WHERE PRODUCT_ID = 100414;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100415;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100416;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100417;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100418;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74181039' WHERE PRODUCT_ID = 100419;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100420;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100421;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100422;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100423;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100424;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100425;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100426;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100427;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100428;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100429;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100430;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100431;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73012010' WHERE PRODUCT_ID = 100432;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73012010' WHERE PRODUCT_ID = 100433;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100436;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100437;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100438;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100439;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100440;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100441;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100442;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100443;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100444;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100445;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100446;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100447;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100448;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100449;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100450;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100451;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100452;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100453;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100454;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100455;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100456;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100457;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100458;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100459;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100460;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100461;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100462;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100463;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100464;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100465;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100466;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100467;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100468;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100469;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100470;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100471;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100472;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100473;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100474;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '70109000' WHERE PRODUCT_ID = 100476;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100480;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85441990' WHERE PRODUCT_ID = 100481;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40094100' WHERE PRODUCT_ID = 100482;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40094100' WHERE PRODUCT_ID = 100483;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40094100' WHERE PRODUCT_ID = 100484;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100485;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100486;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100487;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100488;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100508;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85044010' WHERE PRODUCT_ID = 100513;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84722000' WHERE PRODUCT_ID = 100514;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '76110000' WHERE PRODUCT_ID = 100515;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100528;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100530;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100532;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84841010' WHERE PRODUCT_ID = 100534;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100536;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100537;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100538;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100539;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100540;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100541;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100542;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100551;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100552;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100553;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100554;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100555;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100556;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100557;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100558;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100560;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100561;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100562;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100563;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100564;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100565;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100570;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100571;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100572;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100573;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100574;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100575;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100576;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100577;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100580;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100581;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100582;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100583;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100584;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100585;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100586;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85251030' WHERE PRODUCT_ID = 100612;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '85251030' WHERE PRODUCT_ID = 100613;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100616;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94054090' WHERE PRODUCT_ID = 100619;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100620;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100624;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100626;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74081990' WHERE PRODUCT_ID = 100631;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100632;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '39161090' WHERE PRODUCT_ID = 100638;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '48191090' WHERE PRODUCT_ID = 100639;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100640;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100641;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84186920' WHERE PRODUCT_ID = 100642;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74111000' WHERE PRODUCT_ID = 100645;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44140000' WHERE PRODUCT_ID = 100647;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '44190010' WHERE PRODUCT_ID = 100648;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '69089090' WHERE PRODUCT_ID = 100650;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100653;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100654;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '96031000' WHERE PRODUCT_ID = 100656;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100660;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74081990' WHERE PRODUCT_ID = 100662;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74081990' WHERE PRODUCT_ID = 100663;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '74081990' WHERE PRODUCT_ID = 100664;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100670;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100678;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100695;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100696;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100702;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100703;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100718;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100719;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100747;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100748;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100782;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100785;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100786;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100788;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100789;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73269099' WHERE PRODUCT_ID = 100790;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100805;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100806;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100809;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100810;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '84713010' WHERE PRODUCT_ID = 100825;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100830;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100887;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100898;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100899;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100900;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '40094100' WHERE PRODUCT_ID = 100901;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100902;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94059900' WHERE PRODUCT_ID = 100903;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '94031090' WHERE PRODUCT_ID = 100904;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19022010' WHERE PRODUCT_ID = 100002;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4051000' WHERE PRODUCT_ID = 100010;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100011;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20089999' WHERE PRODUCT_ID = 100027;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20089992' WHERE PRODUCT_ID = 100034;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100057;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100081;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20089999' WHERE PRODUCT_ID = 100082;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19053211' WHERE PRODUCT_ID = 100083;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100142;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100167;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039020' WHERE PRODUCT_ID = 100169;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039020' WHERE PRODUCT_ID = 100177;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '7019000' WHERE PRODUCT_ID = 100179;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039090' WHERE PRODUCT_ID = 100181;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4051000' WHERE PRODUCT_ID = 100182;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100193;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '15021090' WHERE PRODUCT_ID = 100195;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20089999' WHERE PRODUCT_ID = 100201;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039090' WHERE PRODUCT_ID = 100205;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20089911' WHERE PRODUCT_ID = 100216;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4051000' WHERE PRODUCT_ID = 100236;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100249;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4051000' WHERE PRODUCT_ID = 100253;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '73239110' WHERE PRODUCT_ID = 100256;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100262;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19041090' WHERE PRODUCT_ID = 100272;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039020' WHERE PRODUCT_ID = 100289;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100306;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4069000' WHERE PRODUCT_ID = 100313;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20088000' WHERE PRODUCT_ID = 100329;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100374;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100594;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100595;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21069060' WHERE PRODUCT_ID = 100596;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100597;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100598;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19059090' WHERE PRODUCT_ID = 100599;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19030000' WHERE PRODUCT_ID = 100705;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19041090' WHERE PRODUCT_ID = 100706;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19041090' WHERE PRODUCT_ID = 100707;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039020' WHERE PRODUCT_ID = 100709;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20059900' WHERE PRODUCT_ID = 100749;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20041000' WHERE PRODUCT_ID = 100754;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20041000' WHERE PRODUCT_ID = 100852;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039090' WHERE PRODUCT_ID = 100853;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '20059900' WHERE PRODUCT_ID = 100854;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21039020' WHERE PRODUCT_ID = 100855;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '9024090' WHERE PRODUCT_ID = 100857;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19041090' WHERE PRODUCT_ID = 100881;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19041090' WHERE PRODUCT_ID = 100882;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100906;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '16021000' WHERE PRODUCT_ID = 100907;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '19021900' WHERE PRODUCT_ID = 100908;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '4029110' WHERE PRODUCT_ID = 100909;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21050000' WHERE PRODUCT_ID = 100910;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21050000' WHERE PRODUCT_ID = 100911;
UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE = '21050000' WHERE PRODUCT_ID = 100912;

UPDATE PRODUCT_DEFINITION SET TAX_CATEGORY_CODE=LPAD(TAX_CATEGORY_CODE,8,'0') WHERE TAX_CATEGORY_CODE IS NOT NULL;

*
*
**/