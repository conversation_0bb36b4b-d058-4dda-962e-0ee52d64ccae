DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRODUCT_FULFILLMENT_TYPE_DATA;
CREATE TABLE KETTLE_SCM_DEV.PRODUCT_FULFILLMENT_TYPE_DATA (
  ID                              INT PRIMARY KEY AUTO_INCREMENT,
  FULFILLMENT_TYPE                VARCHAR(30) NOT NULL,
  PRODUCT_DEFINITION_ID           INT         NOT NULL,
  PRODUCT_FULFILLMENT_TYPE_STATUS VARCHAR(10)  NOT NULL,
  FOREIGN KEY (PRODUCT_DEFINITION_ID) REFERENCES PRODUCT_DEFINITION (PRODUCT_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.REFERENCE_ORDER;
CREATE TABLE KETTLE_SCM_DEV.REFERENCE_ORDER (
  REFERENCE_ORDER_ID     INT PRIMARY KEY AUTO_INCREMENT,
  GENERATION_TIME        TIMESTAMP   NOT NULL,
  REQUEST_UNIT_ID        INT         NOT NULL,
  GENERATED_BY           INT         NOT NULL,
  FULFILLMENT_UNIT_ID    INT             DEFAULT NULL,
  FULFILLMENT_DATE       TIMESTAMP   NOT NULL,
  COMMENT                VARCHAR(1000)   DEFAULT NULL,
  REFERENCE_ORDER_STATUS VARCHAR(30) NOT NULL
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.REFERENCE_ORDER_MENU_ITEM;
CREATE TABLE KETTLE_SCM_DEV.REFERENCE_ORDER_MENU_ITEM (
  MENU_ITEM_ID                INT PRIMARY KEY AUTO_INCREMENT,
  PRODUCT_ID                  INT            NOT NULL,
  PRODUCT_NAME                VARCHAR(255)   NOT NULL,
  REQUESTED_QUANTITY          DECIMAL(16, 6) NOT NULL,
  REQUESTED_ABSOLUTE_QUANTITY DECIMAL(16, 6) NOT NULL,
  TRANSFERRED_QUANTITY        DECIMAL(16, 6)  DEFAULT NULL,
  RECEIVED_QUANTITY           DECIMAL(16, 6)  DEFAULT NULL,
  REFERENCE_ORDER_ID          INT            NOT NULL,
  FOREIGN KEY (REFERENCE_ORDER_ID) REFERENCES REFERENCE_ORDER (REFERENCE_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.REFERENCE_ORDER_SCM_ITEM;
CREATE TABLE KETTLE_SCM_DEV.REFERENCE_ORDER_SCM_ITEM (
  SCM_ITEM_ID                 INT PRIMARY KEY AUTO_INCREMENT,
  PRODUCT_ID                  INT            NOT NULL,
  PRODUCT_NAME                VARCHAR(255)   NOT NULL,
  REQUESTED_QUANTITY          DECIMAL(16, 6) NOT NULL,
  REQUESTED_ABSOLUTE_QUANTITY DECIMAL(16, 6) NOT NULL,
  TRANSFERRED_QUANTITY        DECIMAL(16, 6)  DEFAULT NULL,
  RECEIVED_QUANTITY           DECIMAL(16, 6)  DEFAULT NULL,
  FULFILLMENT_TYPE            VARCHAR(30)     DEFAULT NULL,
  REFERENCE_ORDER_ID          INT            NOT NULL,
  UNIT_OF_MEASURE             VARCHAR(10)    NOT NULL,
  FOREIGN KEY (REFERENCE_ORDER_ID) REFERENCES REFERENCE_ORDER (REFERENCE_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.REQUEST_ORDER;
CREATE TABLE KETTLE_SCM_DEV.REQUEST_ORDER (
  REQUEST_ORDER_ID     INT PRIMARY KEY AUTO_INCREMENT,
  GENERATION_TIME      TIMESTAMP   NOT NULL,
  REQUEST_UNIT_ID      INT         NOT NULL,
  FULFILLMENT_UNIT_ID  INT         NOT NULL,
  GENERATED_BY         INT         NOT NULL,
  REQUEST_ORDER_STATUS VARCHAR(30) NOT NULL,
  REFERENCE_ORDER_ID   INT         NOT NULL,
  FULFILLMENT_DATE     TIMESTAMP   NOT NULL,
  COMMENT              VARCHAR(1000)   DEFAULT NULL,
  FOREIGN KEY (REFERENCE_ORDER_ID) REFERENCES REFERENCE_ORDER (REFERENCE_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.REQUEST_ORDER_ITEM;
CREATE TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM (
  REQUEST_ORDER_ITEM_ID       INT PRIMARY KEY AUTO_INCREMENT,
  PRODUCT_ID                  INT            NOT NULL,
  PRODUCT_NAME                VARCHAR(255)   NOT NULL,
  REQUESTED_QUANTITY          DECIMAL(16, 6) NOT NULL,
  REQUESTED_ABSOLUTE_QUANTITY DECIMAL(16, 6) NOT NULL,
  TRANSFERRED_QUANTITY        DECIMAL(16, 6)  DEFAULT NULL,
  RECEIVED_QUANTITY           DECIMAL(16, 6)  DEFAULT NULL,
  REQUEST_ORDER_ID            INT            NOT NULL,
  UNIT_OF_MEASURE             VARCHAR(10)    NOT NULL,
  FOREIGN KEY (REQUEST_ORDER_ID) REFERENCES REQUEST_ORDER (REQUEST_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.PURCHASE_ORDER;
CREATE TABLE KETTLE_SCM_DEV.PURCHASE_ORDER (
  PURCHASE_ORDER_ID     INT PRIMARY KEY AUTO_INCREMENT,
  GENERATION_TIME       TIMESTAMP   NOT NULL,
  GENERATION_UNIT_ID    INT         NOT NULL,
  GENERATED_FOR_UNIT_ID INT         NOT NULL,
  GENERATED_BY          INT         NOT NULL,
  BILL_AMOUNT           DECIMAL(16, 6)  DEFAULT NULL,
  PAID_AMOUNT           DECIMAL(16, 6)  DEFAULT NULL,
  ORDER_RECEIPT_NUMBER  VARCHAR(255)    DEFAULT NULL,
  PURCHASE_ORDER_STATUS VARCHAR(30) NOT NULL,
  COMMENT               VARCHAR(1000)   DEFAULT NULL,
  REQUEST_ORDER_ID      INT             DEFAULT NULL,
  FOREIGN KEY (REQUEST_ORDER_ID) REFERENCES REQUEST_ORDER (REQUEST_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.PURCHASE_ORDER_ITEM;
CREATE TABLE KETTLE_SCM_DEV.PURCHASE_ORDER_ITEM (
  PURCHASE_ORDER_ITEM_ID      INT PRIMARY KEY AUTO_INCREMENT,
  SKU_ID                      INT            NOT NULL,
  SKU_NAME                    VARCHAR(255)   NOT NULL,
  REQUESTED_QUANTITY          DECIMAL(16, 6) NOT NULL,
  REQUESTED_ABSOLUTE_QUANTITY DECIMAL(16, 6) NOT NULL,
  TRANSFERRED_QUANTITY        DECIMAL(16, 6)  DEFAULT NULL,
  RECEIVED_QUANTITY           DECIMAL(16, 6)  DEFAULT NULL,
  UNIT_OF_MEASURE             VARCHAR(10)    NOT NULL,
  UNIT_PRICE                  DECIMAL(16, 6)  DEFAULT NULL,
  NEGOTIATED_UNIT_PRICE       DECIMAL(16, 6)  DEFAULT NULL,
  TOTAL_COST                  DECIMAL(16, 6)  DEFAULT NULL,
  AMOUNT_PAID                 DECIMAL(16, 6)  DEFAULT NULL,
  PURCHASE_ORDER_ID           INT            NOT NULL,
  FOREIGN KEY (PURCHASE_ORDER_ID) REFERENCES PURCHASE_ORDER (PURCHASE_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.TRANSFER_ORDER;
CREATE TABLE KETTLE_SCM_DEV.TRANSFER_ORDER (
  TRANSFER_ORDER_ID     INT PRIMARY KEY AUTO_INCREMENT,
  GENERATION_TIME       TIMESTAMP   NOT NULL,
  GENERATION_UNIT_ID    INT         NOT NULL,
  GENERATED_FOR_UNIT_ID INT         NOT NULL,
  GENERATED_BY          INT         NOT NULL,
  TRANSFER_ORDER_STATUS VARCHAR(30) NOT NULL,
  COMMENT               VARCHAR(1000)   DEFAULT NULL,
  REQUEST_ORDER_ID      INT             DEFAULT NULL,
  PURCHASE_ORDER_ID     INT             DEFAULT NULL,
  FOREIGN KEY (REQUEST_ORDER_ID) REFERENCES REQUEST_ORDER (REQUEST_ORDER_ID),
  FOREIGN KEY (PURCHASE_ORDER_ID) REFERENCES PURCHASE_ORDER (PURCHASE_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM;
CREATE TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM (
  TRANSFER_ORDER_ITEM_ID      INT PRIMARY KEY AUTO_INCREMENT,
  SKU_ID                      INT            NOT NULL,
  SKU_NAME                    VARCHAR(255)   NOT NULL,
  REQUESTED_QUANTITY          DECIMAL(16, 6) NOT NULL,
  REQUESTED_ABSOLUTE_QUANTITY DECIMAL(16, 6) NOT NULL,
  TRANSFERRED_QUANTITY        DECIMAL(16, 6)  DEFAULT NULL,
  UNIT_OF_MEASURE             VARCHAR(10)    NOT NULL,
  UNIT_PRICE                  DECIMAL(16, 6)  DEFAULT NULL,
  NEGOTIATED_UNIT_PRICE       DECIMAL(16, 6)  DEFAULT NULL,
  TRANSFER_ORDER_ID           INT            NOT NULL,
  FOREIGN KEY (TRANSFER_ORDER_ID) REFERENCES TRANSFER_ORDER (TRANSFER_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.GOODS_RECEIVED;
CREATE TABLE KETTLE_SCM_DEV.GOODS_RECEIVED (
  GOODS_RECEIVED_ID     INT PRIMARY KEY AUTO_INCREMENT,
  GENERATION_TIME       TIMESTAMP   NOT NULL,
  GENERATION_UNIT_ID       INT         NOT NULL,
  GENERATED_FOR_UNIT_ID    INT         NOT NULL,
  GENERATED_BY          INT         NOT NULL,
  GOODS_RECEIVED_STATUS VARCHAR(30) NOT NULL,
  COMMENT               VARCHAR(1000)   DEFAULT NULL,
  REQUEST_ORDER_ID      INT             DEFAULT NULL,
  TRANSFER_ORDER_ID     INT             DEFAULT NULL,
  FOREIGN KEY (REQUEST_ORDER_ID) REFERENCES REQUEST_ORDER (REQUEST_ORDER_ID),
  FOREIGN KEY (TRANSFER_ORDER_ID) REFERENCES TRANSFER_ORDER (TRANSFER_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM;
CREATE TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM (
  GOODS_RECEIVED_ITEM_ID      INT PRIMARY KEY AUTO_INCREMENT,
  SKU_ID                      INT            NOT NULL,
  SKU_NAME                    VARCHAR(255)   NOT NULL,
  REQUESTED_QUANTITY          DECIMAL(16, 6) NOT NULL,
  REQUESTED_ABSOLUTE_QUANTITY DECIMAL(16, 6) NOT NULL,
  TRANSFERRED_QUANTITY        DECIMAL(16, 6) NOT NULL,
  RECEIVED_QUANTITY           DECIMAL(16, 6) DEFAULT NULL,
  UNIT_OF_MEASURE             VARCHAR(10)    NOT NULL,
  UNIT_PRICE                  DECIMAL(16, 6)  DEFAULT NULL,
  NEGOTIATED_UNIT_PRICE       DECIMAL(16, 6)  DEFAULT NULL,
  GOODS_RECEIVED_ID           INT            NOT NULL,
  FOREIGN KEY (GOODS_RECEIVED_ID) REFERENCES GOODS_RECEIVED (GOODS_RECEIVED_ID)
);

ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION ADD COLUMN SKU_IMAGE VARCHAR(255) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN PRODUCT_IMAGE VARCHAR(255) DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION ADD COLUMN UNIT_PRICE DECIMAL(16, 6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION ADD COLUMN NEGOTIATED_UNIT_PRICE DECIMAL(16, 6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION ADD COLUMN PRICE_LAST_UPDATED TIMESTAMP;

CREATE TABLE KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (
  SUB_CATEGORY_ID          INT PRIMARY KEY AUTO_INCREMENT,
  SUB_CATEGORY_NAME        VARCHAR(30) NOT NULL,
  SUB_CATEGORY_CODE        VARCHAR(30) NOT NULL,
  SUB_CATEGORY_DESCRIPTION VARCHAR(30)     DEFAULT NULL,
  SUB_CATEGORY_STATUS      VARCHAR(30) NOT NULL,
  LINKED_CATEGORY_ID       INT         NOT NULL,
  FOREIGN KEY (LINKED_CATEGORY_ID) REFERENCES CATEGORY_DEFINITION (CATEGORY_ID)
);

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN SUB_CATEGORY_ID INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD FOREIGN KEY (SUB_CATEGORY_ID) REFERENCES SUB_CATEGORY_DEFINITION (SUB_CATEGORY_ID);

INSERT INTO KETTLE_SCM_DEV.CATEGORY_DEFINITION (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`)
VALUES ('COGS', 'COGS', 'COGS', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_DEFINITION (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`)
VALUES ('Consumables', 'CONSUMABLES', 'CONSUMABLES', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_DEFINITION (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`)
VALUES ('Fixed Assets', 'FIXED_ASSETS', 'Fixed Assets', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_DEFINITION (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`)
VALUES ('Semi Finished', 'SEMI_FINISHED', 'Semi Finished', 'ACTIVE');

INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Bakery', 'BAKERY', 'Bakery', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Tea', 'TEA', 'Tea', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Dairy', 'DAIRY', 'Dairy', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Disposables', 'DIPOSABLES', 'Disposables', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Dry Grocery', 'DRY_GROCERY', 'Dry Grocery', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Fruits', 'FRUITS', 'Fruits', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Meat', 'MEAT', 'Meat', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Others', 'OTHERS', 'Others', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Vegetables', 'VEGETABLES', 'Vegetables', 'ACTIVE', '1');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Cutlery', 'CUTLERY', 'Cutlery', 'ACTIVE', '2');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Equipment', 'EQUIPMENT', 'Equipment', 'ACTIVE', '2');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Stationary', 'STATIONERY', 'Stationary', 'ACTIVE', '2');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Uniform', 'UNIFORM', 'Uniform', 'ACTIVE', '2');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Utility', 'UTILITY', 'Utility', 'ACTIVE', '2');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Marketing', 'MARKETING', 'Marketing', 'ACTIVE', '2');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Kitchen equipment', 'KITCHEN_EQUIPMENT', 'Kitchen equipment', 'ACTIVE', '3');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('IT', 'IT', 'IT', 'ACTIVE', '3');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Office Equipment ', 'OFFICE_EQUIPMENT', 'Office Equipment ', 'ACTIVE', '3');
INSERT INTO KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION (`SUB_CATEGORY_NAME`, `SUB_CATEGORY_CODE`, `SUB_CATEGORY_DESCRIPTION`, `SUB_CATEGORY_STATUS`, `LINKED_CATEGORY_ID`)
VALUES ('Furniture', 'FURNITURE', 'Furniture', 'ACTIVE', '3');

CREATE TABLE KETTLE_SCM_DEV.FULFILLMENT_UNIT_MAPPING(
  FULFILLMENT_UNIT_MAPPING_ID INT PRIMARY KEY AUTO_INCREMENT,
  REQUESTING_UNIT_ID INT NOT NULL,
  FULFILLMENT_TYPE VARCHAR(30) NOT NULL,
  FULFILLING_UNIT_ID INT NOT NULL,
  MAPPING_STATUS VARCHAR(30) NOT NULL,
  FOREIGN KEY (REQUESTING_UNIT_ID) REFERENCES UNIT_DETAIL(UNIT_ID),
  FOREIGN KEY (FULFILLING_UNIT_ID) REFERENCES UNIT_DETAIL(UNIT_ID)
);

CREATE TABLE KETTLE_SCM_DEV.UNIT_CATEGORY(
  CATEGORY_ID INT PRIMARY KEY AUTO_INCREMENT,
  CATEGORY_NAME VARCHAR(30) NOT NULL UNIQUE,
  CATEGORY_CODE VARCHAR(30) NOT NULL,
  CATEGORY_DESCRIPTION VARCHAR(255) DEFAULT NULL,
  CATEGORY_STATUS VARCHAR(30) NOT NULL
);

ALTER TABLE KETTLE_SCM_DEV.UNIT_DETAIL ADD COLUMN CATEGORY_ID INT NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.UNIT_DETAIL ADD FOREIGN KEY (CATEGORY_ID) REFERENCES UNIT_CATEGORY(CATEGORY_ID);

INSERT INTO KETTLE_SCM_DEV.UNIT_CATEGORY (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`) VALUES ('Cafe', 'CAFE', 'Cafe', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.UNIT_CATEGORY (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`) VALUES ('Delivery', 'DELIVERY', 'Delivery', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.UNIT_CATEGORY (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`) VALUES ('Warehouse', 'WAREHOUSE', 'Warehouse', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.UNIT_CATEGORY (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`) VALUES ('Kitchen', 'KITCHEN', 'Kitchen', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.UNIT_CATEGORY (`CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`) VALUES ('Call Center', 'CALL_CENTER', 'Call Center', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.UNIT_CATEGORY (`CATEGORY_ID`, `CATEGORY_NAME`, `CATEGORY_CODE`, `CATEGORY_DESCRIPTION`, `CATEGORY_STATUS`) VALUES ('6', 'Office', 'OFFICE', 'Office', 'ACTIVE');



ALTER TABLE KETTLE_SCM_DEV.UNIT_DETAIL DROP COLUMN UNIT_CATEGORY;

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN REQUEST_ORDER_ITEM_ID INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD FOREIGN KEY (REQUEST_ORDER_ITEM_ID) REFERENCES REQUEST_ORDER_ITEM(REQUEST_ORDER_ITEM_ID);
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN TRANSFER_ORDER_ITEM_ID INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD FOREIGN KEY (TRANSFER_ORDER_ITEM_ID) REFERENCES TRANSFER_ORDER_ITEM(TRANSFER_ORDER_ITEM_ID);

CREATE TABLE KETTLE_SCM_DEV.SCM_ORDER_PACKAGING(
  SCM_ORDER_PACKAGING_ID INT PRIMARY KEY AUTO_INCREMENT,
  PACKAGING_ID INT NOT NULL,
  NUMBER_OF_UNITS_PACKED INT NOT NULL,
  NUMBER_OF_UNITS_RECEIVED INT DEFAULT NULL,
  TRANSFERRED_QUANTITY DECIMAL(16,6) NOT NULL,
  RECEIVED_QUANTITY DECIMAL(16,6) DEFAULT NULL,
  GOODS_RECEIVED_ITEM_ID INT NOT NULL,
  TRANSFER_ORDER_ITEM_ID INT NOT NULL,
  FOREIGN KEY (PACKAGING_ID) REFERENCES PACKAGING_DEFINITION(PACKAGING_ID),
  FOREIGN KEY (TRANSFER_ORDER_ITEM_ID) REFERENCES TRANSFER_ORDER_ITEM(TRANSFER_ORDER_ITEM_ID),
  FOREIGN KEY (GOODS_RECEIVED_ITEM_ID) REFERENCES GOODS_RECEIVED_ITEM(GOODS_RECEIVED_ITEM_ID)
);

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD COLUMN RECEIVED_QUANTITY DECIMAL(16,6) DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD COLUMN REQUEST_ORDER_ITEM_ID INT NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD FOREIGN KEY (REQUEST_ORDER_ITEM_ID) REFERENCES REQUEST_ORDER_ITEM(REQUEST_ORDER_ITEM_ID);

ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER ADD COLUMN INITIATION_TIME TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER ADD COLUMN LAST_UPDATE_TIME TIMESTAMP NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN LAST_UPDATE_TIME TIMESTAMP NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER ADD COLUMN INITIATION_TIME TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER ADD COLUMN LAST_UPDATE_TIME TIMESTAMP NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER ADD COLUMN INITIATION_TIME TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER ADD COLUMN LAST_UPDATE_TIME TIMESTAMP NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN LAST_UPDATE_TIME TIMESTAMP NOT NULL;


ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION DROP COLUMN SUB_CATEGORY;
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION DROP COLUMN PRODUCT_TYPE;

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_FULFILLMENT_TYPE_DATA MODIFY COLUMN PRODUCT_FULFILLMENT_TYPE_STATUS VARCHAR(10) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION DROP COLUMN FULFILLMENT_TYPE;




## upload product sheet and then run this query
UPDATE KETTLE_SCM_DEV.PRODUCT_DEFINITION SET SUB_CATEGORY_ID =NULL WHERE SUB_CATEGORY_ID=0;

CREATE TABLE KETTLE_SCM_DEV.RO_MENU_ITEM_VARIANT(
  RO_MENU_VARIANT_ID INT PRIMARY KEY AUTO_INCREMENT,
  VARIANT_NAME VARCHAR(255) NOT NULL,
  VARIANT_CONVERSION_QUANTITY DECIMAL(16,6) NOT NULL,
  VARIANT_ORDERED_QUANTITY DECIMAL(16,6) NOT NULL,
  MENU_ITEM_ID INT NOT NULL,
  FOREIGN KEY (MENU_ITEM_ID) REFERENCES REFERENCE_ORDER_MENU_ITEM(MENU_ITEM_ID)
);


ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER_MENU_ITEM ADD COLUMN DIMENSION VARCHAR(30) DEFAULT NULL;


ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER ADD COLUMN FULFILLMENT_DATE TIMESTAMP NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD COLUMN PURCHASE_ORDER_ITEM_ID INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD FOREIGN KEY (PURCHASE_ORDER_ITEM_ID) REFERENCES PURCHASE_ORDER_ITEM(PURCHASE_ORDER_ITEM_ID);


ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER MODIFY COLUMN REFERENCE_ORDER_ID INT DEFAULT NULL;


ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN PURCHASE_ORDER_ID INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD FOREIGN KEY (PURCHASE_ORDER_ID) REFERENCES PURCHASE_ORDER(PURCHASE_ORDER_ID);

ALTER TABLE KETTLE_SCM_DEV.PACKAGING_DEFINITION DROP FOREIGN KEY PACKAGING_DEFINITION_ibfk_2;

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN RECEIVED_BY INTEGER DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.PACKAGING_DEFINITION MODIFY COLUMN CONVERSION_RATIO DECIMAL(16,6) NOT NULL;


ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM DROP COLUMN REQUESTED_QUANTITY;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM DROP COLUMN REQUESTED_ABSOLUTE_QUANTITY;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM MODIFY COLUMN REQUESTED_QUANTITY DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM MODIFY COLUMN REQUESTED_ABSOLUTE_QUANTITY DECIMAL(16,6) DEFAULT NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`SCM_ORDER_PACKAGING`
DROP FOREIGN KEY `SCM_ORDER_PACKAGING_ibfk_3`;
ALTER TABLE `KETTLE_SCM_DEV`.`SCM_ORDER_PACKAGING`
CHANGE COLUMN `GOODS_RECEIVED_ITEM_ID` `GOODS_RECEIVED_ITEM_ID` INT(11) NULL DEFAULT NULL ;
ALTER TABLE `KETTLE_SCM_DEV`.`SCM_ORDER_PACKAGING`
ADD CONSTRAINT `SCM_ORDER_PACKAGING_ibfk_3`
FOREIGN KEY (`GOODS_RECEIVED_ITEM_ID`)
REFERENCES `KETTLE_SCM_DEV`.`GOODS_RECEIVED_ITEM` (`GOODS_RECEIVED_ITEM_ID`);


ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN PURCHASE_ORDER_ITEM_ID INTEGER NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD FOREIGN KEY (PURCHASE_ORDER_ITEM_ID) REFERENCES PURCHASE_ORDER_ITEM(PURCHASE_ORDER_ITEM_ID);


ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN SUPPORTS_SPECIALIZED_ORDERING VARCHAR(1) NOT NULL DEFAULT "N";

CREATE TABLE KETTLE_SCM_DEV.VENDOR_DETAIL(
  VENDOR_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  VENDOR_NAME VARCHAR(255) NOT NULL,
  VENDOR_DESCRIPTION VARCHAR(1000) DEFAULT NULL,
  VENDOR_TIN VARCHAR(30) DEFAULT NULL,
  VENDOR_COMPANY VARCHAR(255) DEFAULT NULL,
  VENDOR_ADDRESS VARCHAR(255) DEFAULT NULL
);

UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100234';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100112';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100145';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100153';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100192';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100246';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100190';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100259';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100395';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100018';
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `SUPPORTS_SPECIALIZED_ORDERING`='Y' WHERE `PRODUCT_ID`='100396';



ALTER TABLE KETTLE_SCM_DEV.UNIT_DETAIL ADD COLUMN TIN_NUMBER VARCHAR(30) NOT NULL DEFAULT "0";

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM ADD COLUMN VENDOR_ID INTEGER DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION ADD COLUMN IS_DEFAULT VARCHAR(1) DEFAULT 'N' NOT NULL;

UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='9';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='65';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='113';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='159';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='168';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='248';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='255';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='499';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='516';
UPDATE `KETTLE_SCM_DEV`.`SKU_DEFINITION` SET `IS_DEFAULT`='Y' WHERE `SKU_ID`='508';

update SKU_DEFINITION SET IS_DEFAULT = 'Y' WHERE LINKED_PRODUCT_ID NOT IN (100009,100066,100113,100159,100168,100248,100254,100495,100502,100505);


ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER_MENU_ITEM ADD COLUMN SUGGESTED_QUANTITY DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER_MENU_ITEM ADD COLUMN DINE_IN_QUANTITY DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER_MENU_ITEM ADD COLUMN DELIVERY_QUANTITY DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER_MENU_ITEM ADD COLUMN TAKEAWAY_QUANTITY DECIMAL(16,6) DEFAULT NULL;
