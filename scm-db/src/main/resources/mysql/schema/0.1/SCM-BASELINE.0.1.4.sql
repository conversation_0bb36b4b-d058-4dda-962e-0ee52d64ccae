DROP TABLE IF EXISTS KETTLE_SCM_DEV.STOCK_ENTRY;
CREATE TABLE KETTLE_SCM_DEV.STOCK_ENTRY (
  STOCK_ENTRY_ID INT NOT NULL AUTO_INCREMENT,
  UNIT_ID INT NOT NULL,
  PRODUCT_ID INT NOT NULL,
  <PERSON>IT_OF_MEASURE VARCHAR(45) NOT NULL,
  CURRENT_STOCK DECIMAL(16,6) NULL,
  UPDATE_EVENT_ID INT NOT NULL,
  PRIMARY KEY (STOCK_ENTRY_ID),
  UNIQUE INDEX STOCK_ENTRY_ID_UNIQUE (STOCK_ENTRY_ID ASC)
);

ALTER TABLE KETTLE_SCM_DEV.SKU_PACKAGING_MAPPING ADD COLUMN IS_DEFAULT VARCHAR(1) NOT NULL DEFAULT "N";