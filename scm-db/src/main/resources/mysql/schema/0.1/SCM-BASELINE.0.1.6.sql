ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL ADD COLUMN PRIMARY_CONTACT VARCHAR(20);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL ADD COLUMN SECONDARY_CONTACT VARCHAR(20);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL ADD COLUMN PRIMARY_EMAIL VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL ADD COLUMN SECONDARY_EMAIL VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL ADD COLUMN PAYMENT_CYCLE VARCHAR(30) NOT NULL DEFAULT 'WEEKLY';
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL ADD COLUMN PAYMENT_CYCLE_DAY INTEGER NOT NULL DEFAULT 1;
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL ADD COLUMN VENDOR_STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE';


ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN IS_SPECIAL_ORDER VARCHAR(1) NOT NULL DEFAULT "N";


ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT MODIFY COLUMN GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED MODIFY COLUMN GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED MODIFY COLUMN LAST_UPDATE_TIME TIMESTAMP NOT NULL DEFAULT 0;

ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER MODIFY COLUMN GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER MODIFY COLUMN GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER MODIFY COLUMN GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION MODIFY COLUMN PRICE_LAST_UPDATED TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.STOCK_INVENTORY MODIFY COLUMN GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER MODIFY COLUMN GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT MODIFY COLUMN GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

UPDATE KETTLE_SCM_DEV.REQUEST_ORDER SET IS_SPECIAL_ORDER = 'Y' WHERE REQUEST_ORDER_ID IN (SELECT REQUEST_ORDER_ID from KETTLE_SCM_DEV.REQUEST_ORDER_ITEM WHERE VENDOR_ID IS NOT NULL GROUP BY REQUEST_ORDER_ID);