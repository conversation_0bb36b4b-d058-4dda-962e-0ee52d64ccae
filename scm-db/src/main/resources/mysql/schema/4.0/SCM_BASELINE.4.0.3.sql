ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION
ADD COLUMN VARIANCE_TYPE VARCHAR(30) NULL;

CREATE TABLE KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA(
DAY_CLOSURE_AGGREGATED_DATA_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
EVENT_ID INTEGER NOT NULL,
UNIT_ID INTEGER NOT NULL,
FREQUENCY VARCHAR(25) NOT NULL,
BUSINESS_DATE TIMESTAMP NOT NULL,
AG<PERSON><PERSON>ATION_TYPE VARCHAR(100) NOT NULL,
AGGR<PERSON>ATION_CODE VARCHAR(100) NOT NULL,
AGGR<PERSON>ATION_DESC VARCHAR(100) NOT NULL,
VALUE_TODAY DECIMAL(16,6)
);

CREATE INDEX DAY_CLOSURE_AGGREGATED_DATA_EVENT_ID ON KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA(EVENT_ID) USING BTREE;
CREATE INDEX DAY_CLOSURE_AGGREGATED_DATA_UNIT_ID ON KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA(UNIT_ID) USING BTREE;
CREATE INDEX DAY_CLOSURE_AGGREGATED_DATA_FREQUENCY ON KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA(FREQUENCY) USING BTREE;
CREATE INDEX DAY_CLOSURE_AGGREGATED_DATA_BUSINESS_DATE ON KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA(BUSINESS_DATE) USING BTREE;
CREATE INDEX DAY_CLOSURE_AGGREGATED_DATA_AGGREGATION_TYPE ON KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA(AGGREGATION_TYPE) USING BTREE;
CREATE INDEX DAY_CLOSURE_AGGREGATED_DATA_AGGREGATION_CODE ON KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA(AGGREGATION_CODE) USING BTREE;
CREATE INDEX DAY_CLOSURE_AGGREGATED_DATA_AGGREGATION_DESC ON KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA(AGGREGATION_CODE) USING BTREE;

DELIMITER $$
CREATE PROCEDURE `SP_DAILY_DAY_CLOSURE_AGGREGATED_DATA`(IN CLOSE_EVENT_ID INTEGER)
proc_label : BEGIN
delete from KETTLE_SCM.DAY_CLOSURE_AGGREGATED_DATA where EVENT_ID = CLOSE_EVENT_ID;
INSERT INTO KETTLE_SCM.DAY_CLOSURE_AGGREGATED_DATA (EVENT_ID,UNIT_ID,FREQUENCY, BUSINESS_DATE, AGGREGATION_TYPE, AGGREGATION_CODE,AGGREGATION_DESC, VALUE_TODAY)
SELECT 
	dce.EVENT_ID,
    dce.UNIT_ID,
    dce.CLOSURE_EVENT_FREQUENCY,
    dce.BUSINESS_DATE,
    'WASTAGE',
    'PRODUCT_CATEGORY',
    cd.CATEGORY_NAME,
    SUM((COALESCE(dcpv.WASTAGE, 0) * pd.NEGOTIATED_UNIT_PRICE)) AS WASTAGE_COST
FROM
    KETTLE_SCM.PRODUCT_DEFINITION pd 
        INNER JOIN
    KETTLE_SCM.CATEGORY_DEFINITION cd ON cd.CATEGORY_ID = pd.CATEGORY_ID
    INNER JOIN
		DAY_CLOSE_EVENT dce ON dce.EVENT_ID = CLOSE_EVENT_ID
        LEFT OUTER JOIN
    KETTLE_SCM.DAY_CLOSE_PRODUCT_VALUES dcpv ON
		dcpv.EVENT_ID = dce.EVENT_ID and
		dcpv.PRODUCT_ID = pd.PRODUCT_ID
   
WHERE
      pd.PRODUCT_STATUS = 'ACTIVE'
GROUP BY cd.CATEGORY_NAME;
INSERT INTO KETTLE_SCM.DAY_CLOSURE_AGGREGATED_DATA (EVENT_ID,UNIT_ID,FREQUENCY, BUSINESS_DATE, AGGREGATION_TYPE, AGGREGATION_CODE,AGGREGATION_DESC, VALUE_TODAY)
SELECT 
	dce.EVENT_ID,
    dce.UNIT_ID,
    dce.CLOSURE_EVENT_FREQUENCY,
    dce.BUSINESS_DATE,
    'VARIANCE',
    'PRODUCT_CATEGORY',
    cd.CATEGORY_NAME,
    SUM((COALESCE(dcpv.VARIANCE, 0) * pd.NEGOTIATED_UNIT_PRICE)) AS WASTAGE_COST
FROM
    KETTLE_SCM.PRODUCT_DEFINITION pd 
        INNER JOIN
    KETTLE_SCM.CATEGORY_DEFINITION cd ON cd.CATEGORY_ID = pd.CATEGORY_ID
    INNER JOIN
		KETTLE_SCM.DAY_CLOSE_EVENT dce ON dce.EVENT_ID = CLOSE_EVENT_ID
        LEFT OUTER JOIN
    KETTLE_SCM.STOCK_INVENTORY dcpv ON
		dcpv.CURRENT_EVENT_ID = dce.EVENT_ID and
		dcpv.PRODUCT_ID = pd.PRODUCT_ID
   
WHERE
dcpv.CURRENT_EVENT_ID = CLOSE_EVENT_ID and
      pd.PRODUCT_STATUS = 'ACTIVE'
GROUP BY cd.CATEGORY_NAME;

INSERT INTO KETTLE_SCM.DAY_CLOSURE_AGGREGATED_DATA (EVENT_ID,UNIT_ID,FREQUENCY, BUSINESS_DATE, AGGREGATION_TYPE, AGGREGATION_CODE,AGGREGATION_DESC, VALUE_TODAY)
SELECT 
	dce.EVENT_ID,
    dce.UNIT_ID,
    dce.CLOSURE_EVENT_FREQUENCY,
    dce.BUSINESS_DATE,
    'VARIANCE',
    'VARIANCE_TYPE',
    coalesce(pd.VARIANCE_TYPE, 'TBD') VARIANCE_TYPE,
    SUM((COALESCE(dcpv.VARIANCE, 0) * pd.NEGOTIATED_UNIT_PRICE)) AS WASTAGE_COST
FROM
    KETTLE_SCM.PRODUCT_DEFINITION pd 
	INNER JOIN
		KETTLE_SCM.DAY_CLOSE_EVENT dce ON dce.EVENT_ID = CLOSE_EVENT_ID
        LEFT OUTER JOIN
    KETTLE_SCM.STOCK_INVENTORY dcpv ON
		dcpv.CURRENT_EVENT_ID = dce.EVENT_ID and
		dcpv.PRODUCT_ID = pd.PRODUCT_ID
   
WHERE
dcpv.CURRENT_EVENT_ID = CLOSE_EVENT_ID and
      pd.PRODUCT_STATUS = 'ACTIVE'
GROUP BY VARIANCE_TYPE;

INSERT INTO KETTLE_SCM.DAY_CLOSURE_AGGREGATED_DATA (EVENT_ID,UNIT_ID,FREQUENCY, BUSINESS_DATE, AGGREGATION_TYPE, AGGREGATION_CODE,AGGREGATION_DESC, VALUE_TODAY)
select dce.EVENT_ID,
    dce.UNIT_ID,
    dce.CLOSURE_EVENT_FREQUENCY,
    dce.BUSINESS_DATE,
    'WASTAGE',
    'REASON',
    wt.COMMENT,
    coalesce(wi.WASTAGE_COST, 0) AS WASTAGE_COST from KETTLE_SCM.DAY_CLOSE_EVENT dce
    INNER JOIN (select distinct coalesce(COMMENT, 'Other') COMMENT from KETTLE_SCM.WASTAGE_ITEM_DATA) wt
LEFT OUTER JOIN (
SELECT 
    dcpv.EVENT_ID,
    coalesce(wi.COMMENT, 'Other') COMMENT,
    SUM(wi.COST) AS WASTAGE_COST
FROM
    KETTLE_SCM.DAY_CLOSE_EVENT_RANGE dcpv
    INNER JOIN KETTLE_SCM.DAY_CLOSE_EVENT dce
    ON dcpv.EVENT_ID = dce.EVENT_ID
        INNER JOIN
    KETTLE_SCM.WASTAGE_EVENT we ON dcpv.TYPE = 'WASTAGE'
		AND dcpv.EVENT_ID = CLOSE_EVENT_ID
        AND we.WASTAGE_ID >= dcpv.START_ID
        AND we.WASTAGE_ID <= dcpv.END_ID
        AND we.UNIT_ID = dce.UNIT_ID
        INNER JOIN
    KETTLE_SCM.WASTAGE_ITEM_DATA wi ON we.WASTAGE_ID = wi.WASTAGE_ID
GROUP BY wi.COMMENT)wi ON wt.COMMENT = wi.COMMENT
and dce.EVENT_ID = wi.EVENT_ID
where dce.EVENT_ID = CLOSE_EVENT_ID;
END$$
DELIMITER ;



DELIMITER $$
CREATE PROCEDURE KETTLE_SCM_DEV.SP_DAILY_DAY_CLOSURE_AGGREGATED_DATA_VIEW(IN CLOSE_EVENT_ID INTEGER)
proc_label : BEGIN
SELECT DISTINCT
		'VALUE_TODAY' VALUE_TYPE,
        dca.FREQUENCY,
            dca.AGGREGATION_TYPE,
            dca.AGGREGATION_CODE,
            dca.AGGREGATION_DESC,
            COALESCE(dca.VALUE_TODAY, 0) TOTAL
    FROM
        KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA dca
    WHERE
        EVENT_ID = CLOSE_EVENT_ID
UNION ALL        
SELECT 
    'VALUE_YTD' VALUE_TYPE,
    dca.FREQUENCY,
    dca.AGGREGATION_TYPE,
    dca.AGGREGATION_CODE,
    dca.AGGREGATION_DESC,
    SUM(dca.VALUE_TODAY) TOTAL
FROM
    KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA dca,
    (SELECT 
        *
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT
    WHERE
        EVENT_ID = CLOSE_EVENT_ID) dce
WHERE
    dca.EVENT_ID <= dce.EVENT_ID
        AND dca.BUSINESS_DATE BETWEEN DATE_FORMAT(dce.BUSINESS_DATE, '%Y-01-01') AND dce.BUSINESS_DATE
        AND dca.UNIT_ID = dce.UNIT_ID
        AND dca.FREQUENCY = dce.CLOSURE_EVENT_FREQUENCY
GROUP BY dca.FREQUENCY , dca.AGGREGATION_TYPE , dca.AGGREGATION_CODE , dca.AGGREGATION_DESC 
UNION ALL SELECT 
    'VALUE_MTD' VALUE_TYPE,
    dca.FREQUENCY,
    dca.AGGREGATION_TYPE,
    dca.AGGREGATION_CODE,
    dca.AGGREGATION_DESC,
    SUM(dca.VALUE_TODAY) TOTAL
FROM
    KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA dca,
    (SELECT 
        *
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT
    WHERE
        EVENT_ID = CLOSE_EVENT_ID) dce
WHERE
    dca.EVENT_ID <= dce.EVENT_ID
        AND dca.BUSINESS_DATE BETWEEN DATE_FORMAT(dce.BUSINESS_DATE, '%Y-%m-01') AND dce.BUSINESS_DATE
        AND dca.UNIT_ID = dce.UNIT_ID
        AND dca.FREQUENCY = dce.CLOSURE_EVENT_FREQUENCY
GROUP BY dca.FREQUENCY , dca.AGGREGATION_TYPE , dca.AGGREGATION_CODE , dca.AGGREGATION_DESC 
UNION ALL SELECT 
    'VALUE_LMTD' VALUE_TYPE,
    dce.FREQUENCY,
    dce.AGGREGATION_TYPE,
    dce.AGGREGATION_CODE,
    dce.AGGREGATION_DESC,
    COALESCE(dca.TOTAL, 0) TOTAL
FROM
    (SELECT DISTINCT
        dca.FREQUENCY,
            dca.AGGREGATION_TYPE,
            dca.AGGREGATION_CODE,
            dca.AGGREGATION_DESC
    FROM
        KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA dca
    WHERE
        EVENT_ID = CLOSE_EVENT_ID) dce
        LEFT OUTER JOIN
    (SELECT 
        dca.FREQUENCY,
            dca.AGGREGATION_TYPE,
            dca.AGGREGATION_CODE,
            dca.AGGREGATION_DESC,
            SUM(dca.VALUE_TODAY) TOTAL
    FROM
        KETTLE_SCM_DEV.DAY_CLOSURE_AGGREGATED_DATA dca, (SELECT 
        *
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT
    WHERE
        EVENT_ID = CLOSE_EVENT_ID) dce
    WHERE
        dca.EVENT_ID <= dce.EVENT_ID
            AND dca.BUSINESS_DATE BETWEEN DATE_FORMAT(DATE_ADD(dce.BUSINESS_DATE, INTERVAL - 1 MONTH), '%Y-%m-01') AND DATE_ADD(dce.BUSINESS_DATE, INTERVAL - 1 MONTH)
            AND dca.UNIT_ID = dce.UNIT_ID
            AND dca.FREQUENCY = dce.CLOSURE_EVENT_FREQUENCY
    GROUP BY dca.FREQUENCY , dca.AGGREGATION_TYPE , dca.AGGREGATION_CODE , dca.AGGREGATION_DESC) dca ON dca.FREQUENCY = dce.FREQUENCY
        AND dca.AGGREGATION_TYPE = dce.AGGREGATION_TYPE
        AND dca.AGGREGATION_CODE = dce.AGGREGATION_CODE;

END$$
DELIMITER ;
