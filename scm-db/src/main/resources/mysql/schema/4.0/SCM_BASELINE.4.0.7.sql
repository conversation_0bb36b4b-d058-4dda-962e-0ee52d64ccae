ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER add column EXTERNAL_TRANSFER VARCHAR(1) NOT NULL DEFAULT "N";

DROP TABLE IF EXISTS KETTLE_SCM_DEV.EXTERNAL_TRANSFER_DETAILS;
CREATE TABLE KETTLE_SCM_DEV.EXTERNAL_TRANSFER_DETAILS(
  EXT_TR_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
  TRANSFER_ORDER_ID INTEGER NOT NULL,
  VENDOR_ID INTEGER NOT NULL,
  DISPATCH_ID INTEGER NOT NULL,
  VENDOR_NAME VARCHAR(200) NOT NULL,
  LOCATION_NAME VARCHAR(100) NOT NULL,
  APPROVAL_STATUS VARCHAR(50) NULL,
  UPDATED_BY INT NULL,
  UPDATED_AT TIMESTAMP NULL,
  CONSTRAINT
    FOREIGN KEY (TRANSFER_ORDER_ID)
    REFERENCES TRANSFER_ORDER (TRANSFER_ORDER_ID)
);

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL(ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('TRNTE', '7', 'SUBMENU', 'SHOW', 'SuMo -> Transfers -> TO/EP -> TRNTE -> SHOW', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
SELECT 1,ACTION_DETAIL_ID,'ACTIVE','100000','2017-07-01 00:00:00' FROM KETTLE_MASTER_DEV.ACTION_DETAIL where ACTION_CODE="TRNTE";

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL(ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ACKTREP', '7', 'ACTION', 'SHOW', 'SuMo -> Transfers -> TO/EP -> ACKTREP -> SHOW', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
SELECT 6,ACTION_DETAIL_ID,'ACTIVE','100000','2017-07-01 00:00:00' FROM KETTLE_MASTER_DEV.ACTION_DETAIL where ACTION_CODE="ACKTREP";

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
SELECT 7,ACTION_DETAIL_ID,'ACTIVE','100000','2017-07-01 00:00:00' FROM KETTLE_MASTER_DEV.ACTION_DETAIL where ACTION_CODE="ACKTREP";

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
SELECT 8,ACTION_DETAIL_ID,'ACTIVE','100000','2017-07-01 00:00:00' FROM KETTLE_MASTER_DEV.ACTION_DETAIL where ACTION_CODE="ACKTREP";

alter table KETTLE_SCM_DEV.GOODS_RECEIVED add column PARENT_GR_COMMENT VARCHAR(1000) DEFAULT NULL;