DROP TABLE IF EXISTS SKU_ATTRIBUTE_VALUE;
DROP TABLE IF EXISTS SKU_PACKAGING_MAPPING;
DROP TABLE IF EXISTS SKU_DEFINITION;
DROP TABLE IF EXISTS UNIT_PRODUCT_MAPPING;
DROP TABLE IF EXISTS PRODUCT_PACKAGING_MAPPING;
DROP TABLE IF EXISTS PRODUCT_DEFINITION;
DROP TABLE IF EXISTS PACKAGING_DEFINITION;
DROP TABLE IF EXISTS CATEGORY_ATTRIBUTE_VALUE;
DROP TABLE IF EXISTS CATEGORY_ATTRIBUTE_MAPPING;
DROP TABLE IF EXISTS CATEGORY_DEFINITION;
DROP TABLE IF EXISTS ATTRIBUTE_VALUE;
DROP TABLE IF EXISTS ATTRIBUTE_DEFINITION;
DROP TABLE IF EXISTS UNIT_DETAIL;

CREATE TABLE UNIT_DETAIL (
  UNIT_ID INT NOT NULL ,
  UNIT_NAME VARCHAR(255) NOT NULL ,
  UNIT_CATEGORY VARCHAR(30) NOT NULL ,
  UNIT_EMAIL VARCHAR(50) NOT NULL ,
  UNIT_STATUS VARCHAR(15) NOT NULL ,
  PRIMARY KEY (UNIT_ID)  
);

CREATE TABLE ATTRIBUTE_DEFINITION (
  ATTRIBUTE_ID INT NOT NULL AUTO_INCREMENT,
  ATTRIBUTE_NAME VARCHAR(255) NOT NULL ,
  ATTRIBUTE_TYPE VARCHAR(50) NOT NULL ,
  ATTRIBUTE_CODE VARCHAR(50) NOT NULL ,
  ATTRIBUTE_SHORT_CODE VARCHAR(10) NULL ,
  ATTRIBUTE_DESCRIPTION VARCHAR(1000) NULL ,
  ATTRIBUTE_STATUS VARCHAR(15) NOT NULL ,
  PRIMARY KEY (ATTRIBUTE_ID),
  UNIQUE (ATTRIBUTE_NAME)
);

CREATE TABLE ATTRIBUTE_VALUE (
  ATTRIBUTE_VALUE_ID INT NOT NULL AUTO_INCREMENT,
  ATTRIBUTE_ID INT NOT NULL,
  ATTRIBUTE_VALUE VARCHAR(255) NOT NULL ,
  ATTRIBUTE_VALUE_SHORT_CODE VARCHAR(10) NULL ,
  ATTRIBUTE_VALUE_STATUS VARCHAR(15) NOT NULL ,
  PRIMARY KEY (ATTRIBUTE_VALUE_ID),
  UNIQUE (ATTRIBUTE_ID, ATTRIBUTE_VALUE),
  FOREIGN KEY ATTRIBUTE_VALUE_ATTRIBUTE_DEFINITION_ID (ATTRIBUTE_ID)
  REFERENCES ATTRIBUTE_DEFINITION(ATTRIBUTE_ID)
   ON DELETE RESTRICT
   ON UPDATE CASCADE
);

CREATE TABLE CATEGORY_DEFINITION (
  CATEGORY_ID INT NOT NULL AUTO_INCREMENT,
  CATEGORY_NAME VARCHAR(255) NOT NULL ,
  CATEGORY_CODE VARCHAR(50) NOT NULL ,
  CATEGORY_DESCRIPTION VARCHAR(1000) NULL ,
  CATEGORY_STATUS VARCHAR(15) NOT NULL ,
  PRIMARY KEY (CATEGORY_ID),
  UNIQUE (CATEGORY_NAME)
);

DROP TABLE IF EXISTS CATEGORY_ATTRIBUTE_MAPPING;
CREATE TABLE CATEGORY_ATTRIBUTE_MAPPING (
  CATEGORY_ATTRIBUTE_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
  CATEGORY_ID INT NOT NULL,
  ATTRIBUTE_ID INT NOT NULL,
  IS_MANDATORY VARCHAR(1) NOT NULL ,
  MAPPING_ORDER  INTEGER NOT NULL ,
  IS_USED_IN_NAMING VARCHAR(1) NOT NULL ,
  MAPPING_STATUS VARCHAR(15) NOT NULL ,
  PRIMARY KEY (CATEGORY_ATTRIBUTE_MAPPING_ID),
  UNIQUE (CATEGORY_ID,ATTRIBUTE_ID),
  FOREIGN KEY CATEGORY_ATTRIBUTE_MAPPING_CATEGORY_ID (CATEGORY_ID)
  REFERENCES CATEGORY_DEFINITION(CATEGORY_ID)
   ON DELETE RESTRICT
   ON UPDATE CASCADE,
  FOREIGN KEY CATEGORY_ATTRIBUTE_MAPPING_ATTRIBUTE_ID (ATTRIBUTE_ID)
  REFERENCES ATTRIBUTE_DEFINITION(ATTRIBUTE_ID)
   ON DELETE RESTRICT
   ON UPDATE CASCADE
);

CREATE TABLE CATEGORY_ATTRIBUTE_VALUE (
  CATEGORY_ATTRIBUTE_VALUE_ID INT NOT NULL AUTO_INCREMENT,
  CATEGORY_ATTRIBUTE_MAPPING_ID INT NOT NULL,
  ATTRIBUTE_VALUE_ID INT NOT NULL,
  MAPPING_STATUS VARCHAR(15) NOT NULL ,
  PRIMARY KEY (CATEGORY_ATTRIBUTE_VALUE_ID),
  UNIQUE (CATEGORY_ATTRIBUTE_MAPPING_ID,ATTRIBUTE_VALUE_ID),
  FOREIGN KEY CATEGORY_ATTRIBUTE_VALUE_CATEGORY_ID (CATEGORY_ATTRIBUTE_MAPPING_ID)
  REFERENCES CATEGORY_ATTRIBUTE_MAPPING(CATEGORY_ATTRIBUTE_MAPPING_ID)
   ON DELETE RESTRICT
   ON UPDATE CASCADE,
  FOREIGN KEY CATEGORY_ATTRIBUTE_VALUE_ATTRIBUTE_VALUE_ID (ATTRIBUTE_VALUE_ID)
  REFERENCES ATTRIBUTE_VALUE (ATTRIBUTE_VALUE_ID)
   ON DELETE RESTRICT
   ON UPDATE CASCADE
);


CREATE TABLE PRODUCT_DEFINITION (
  PRODUCT_ID INT NOT NULL AUTO_INCREMENT,
  PRODUCT_NAME VARCHAR(255) NOT NULL ,
  PRODUCT_DESCRIPTION VARCHAR(1000) NULL ,
  CATEGORY_ID INT NOT NULL,
  SUPPORTS_LOOSE_ORDERING VARCHAR(1) NOT NULL ,
  CREATION_DATE TIMESTAMP NULL,
  CREATED_BY INTEGER NOT NULL ,
  HAS_INNER VARCHAR(1) NOT NULL ,
  HAS_CASE VARCHAR(1) NOT NULL ,
  PRODUCT_TYPE VARCHAR(20) NOT NULL ,
  STOCK_KEEPING_FREQUENCY VARCHAR(15) NOT NULL ,
  PRODUCT_CODE VARCHAR(30) NOT NULL,
  SHELF_LIFE_IN_DAYS INTEGER NOT NULL,
  PRODUCT_STATUS VARCHAR(15) NOT NULL,
  UNIT_OF_MEASURE VARCHAR(15) NOT NULL,
  FULFILLMENT_TYPE VARCHAR(15) NOT NULL,
  PRIMARY KEY (PRODUCT_ID),
  UNIQUE (PRODUCT_NAME),
  FOREIGN KEY PRODUCT_DEFINITION_CATEGORY_ID (CATEGORY_ID)
  REFERENCES CATEGORY_DEFINITION(CATEGORY_ID)
   ON DELETE RESTRICT
   ON UPDATE CASCADE
);

CREATE TABLE PACKAGING_DEFINITION (
  PACKAGING_ID INT NOT NULL AUTO_INCREMENT,
  PACKAGING_TYPE VARCHAR(20) NOT NULL ,
  PACKAGING_CODE VARCHAR(30) NOT NULL,
  PACKAGING_NAME VARCHAR(255) NOT NULL ,
  PACKAGING_STATUS VARCHAR(15) NOT NULL,
  CONVERSION_RATIO INT NOT NULL,
  UNIT_OF_MEASURE VARCHAR(15) NOT NULL,
  PRIMARY KEY (PACKAGING_ID),
  UNIQUE (PACKAGING_TYPE,PACKAGING_CODE)
);

CREATE TABLE PRODUCT_PACKAGING_MAPPING (
  PRODUCT_PACKAGING_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
  PACKAGING_ID INT NOT NULL,
  PRODUCT_ID INT NOT NULL,
  MAPPING_STATUS VARCHAR(15) NOT NULL,
  PRIMARY KEY (PRODUCT_PACKAGING_MAPPING_ID),
  UNIQUE (PACKAGING_ID,PRODUCT_ID)
);

CREATE TABLE UNIT_PRODUCT_MAPPING(
UNIT_PRODUCT_MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
VENDOR_ID INTEGER NOT NULL,
MAPPING_STATUS VARCHAR(15) NOT NULL,
PRIMARY KEY (UNIT_PRODUCT_MAPPING_ID),
UNIQUE (UNIT_ID, PRODUCT_ID)
);

CREATE TABLE SKU_DEFINITION (
  SKU_ID INT NOT NULL AUTO_INCREMENT,
  SKU_NAME VARCHAR(255) NOT NULL,
  SKU_DESCRIPTION VARCHAR(1000) NULL,
  SUPPORTS_LOOSE_ORDERING VARCHAR(1) NOT NULL ,
  CREATION_DATE TIMESTAMP NULL,
  CREATED_BY INTEGER NOT NULL ,
  HAS_INNER VARCHAR(1) NOT NULL ,
  HAS_CASE VARCHAR(1) NOT NULL ,
  LINKED_PRODUCT_ID INTEGER NOT NULL,
  SHELF_LIFE_IN_DAYS INTEGER NOT NULL,
  SKU_STATUS VARCHAR(15) NOT NULL,
  UNIT_OF_MEASURE VARCHAR(15) NOT NULL,
  PRIMARY KEY (SKU_ID),
  UNIQUE (SKU_NAME),
  FOREIGN KEY PRODUCT_DEFINITION_LINKED_PRODUCT_ID (LINKED_PRODUCT_ID)
  REFERENCES PRODUCT_DEFINITION(PRODUCT_ID)
   ON DELETE RESTRICT
   ON UPDATE CASCADE
);

CREATE TABLE SKU_PACKAGING_MAPPING (
  SKU_PACKAGING_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
  PACKAGING_ID INT NOT NULL,
  SKU_ID INT NOT NULL,
  MAPPING_STATUS VARCHAR(15) NOT NULL,
  PRIMARY KEY (SKU_PACKAGING_MAPPING_ID),
  UNIQUE (PACKAGING_ID,SKU_ID)
);

CREATE TABLE SKU_ATTRIBUTE_VALUE (
  SKU_ATTRIBUTE_VALUE_ID INT NOT NULL AUTO_INCREMENT,
  SKU_ID INT NOT NULL,
  ATTRIBUTE_ID INT NOT NULL,
  ATTRIBUTE_VALUE_ID INT NOT NULL,
  MAPPING_STATUS VARCHAR(15) NOT NULL,
  PRIMARY KEY (SKU_ATTRIBUTE_VALUE_ID),
  UNIQUE (SKU_ID,ATTRIBUTE_ID)
);

INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Type', 'CATEGORY', 'TYPE', 'TPE', 'Type of product/SKU', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Veg / Non Veg', 'CATEGORY', 'VEG_NON_VEG', 'VNV', 'Whether its a Veg Product or a Non-Veg product', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Brand', 'CATEGORY', 'BRAND', 'BND', 'Brand', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Capacity', 'CATEGORY', 'CAPACITY', 'CPT', 'Capacity', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Texture', 'CATEGORY', 'TEXTURE', 'TXR', 'Texture', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Quality', 'CATEGORY', 'QUALITY', 'QLT', 'Quality', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Color', 'CATEGORY', 'COLOR', 'CLR', 'Color', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Case', 'DIMENSION', 'CASE', 'CSE', 'Case Probable Values', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Inner', 'DIMENSION', 'INNER', 'INR', 'Inner Probable Values', 'ACTIVE');
INSERT INTO `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_NAME`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_SHORT_CODE`, `ATTRIBUTE_DESCRIPTION`, `ATTRIBUTE_STATUS`) VALUES ('Loose', 'DIMENSION', 'LOOSE', 'LOS', 'Loose Probable Values', 'ACTIVE');


ALTER TABLE PRODUCT_DEFINITION ADD COLUMN SUB_CATEGORY VARCHAR(30) DEFAULT NULL;

ALTER TABLE CATEGORY_ATTRIBUTE_MAPPING
DROP FOREIGN KEY CATEGORY_ATTRIBUTE_MAPPING_ibfk_2;

ALTER TABLE CATEGORY_ATTRIBUTE_MAPPING
ADD CONSTRAINT CATEGORY_ATTRIBUTE_MAPPING_ibfk_2
FOREIGN KEY (ATTRIBUTE_ID)
REFERENCES ATTRIBUTE_DEFINITION (ATTRIBUTE_ID)
  ON DELETE RESTRICT
  ON UPDATE CASCADE;


ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN PARTICIPATES_IN_RECIPE VARCHAR(1) NOT NULL DEFAULT "N";
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN VARIANT_LEVEL_ORDERING VARCHAR(1) NOT NULL DEFAULT "N";


ALTER TABLE KETTLE_SCM_DEV.PACKAGING_DEFINITION ADD COLUMN SUB_PACKAGING INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.PACKAGING_DEFINITION ADD FOREIGN KEY (SUB_PACKAGING) REFERENCES PACKAGING_DEFINITION (PACKAGING_ID);

