ALTER TABLE KETTLE_SCM_DEV.VEND<PERSON>_DETAIL_DATA
ADD COLUMN LEAD_TIME INT NOT NULL;

ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER
ADD COLUMN EXPIRY_DATE DATE NULL,
ADD COLUMN EXPIRY_STATUS VARCHAR(20) NULL;

CREATE TABLE KETTLE_SCM_DEV.PURCHASE_ORDER_EXTENDED_STATUS_LOG (
    PURCHASE_ORDER_EXTENDED_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    FROM_STATUS VARCHAR(255) NOT NULL,
    TO_STATUS VARCHAR(255) NOT NULL,
    UPDATED_BY INT(11) NOT NULL,
    UPDATE_TIME TIMESTAMP,
    PURCHASE_ORDER_ID INT(11) NOT NULL
);

CREATE TABLE KETTLE_SCM_DEV.GOODS_RECIEVED_TO_PAYMENT_REQUEST_MAPPING (
    MAPPING_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    PAYMENT_REQUEST_ID INT(11) NOT NULL,
    GOODS_RECIEVED_ID INT(11) NOT NULL,
    MAPPING_STATUS VARCHAR(255) NOT NULL,
    UPDATE_TIME TIMESTAMP,
);

ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER_EXTENDED_STATUS_LOG
ADD COLUMN EXTENSION_REASON VARCHAR(45) NOT NULL,
ADD COLUMN DESCRIPTION VARCHAR(200) NULL;


ALTER TABLE KETTLE_SCM_DEV.SKU_PRICE_DATA
ADD COLUMN LEAD_TIME INT ;

ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER
ADD COLUMN LEAD_TIME INT ;

ALTER TABLE KETTLE_SCM_DEV.UNIT_SKU_MAPPING
ADD COLUMN PROFILE VARCHAR (32) DEFAULT 'P0';

ALTER TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING
ADD COLUMN PROFILE VARCHAR (32) DEFAULT 'P0';

CREATE TABLE KETTLE_SCM_DEV.EXCEPTION_DATE_ENTRY(
  KEY_ID INTEGER NOT NULL AUTO_INCREMENT,
  UNIT_ID INTEGER,
  UNIT_NAME VARCHAR(255),
  BUSINESS_DATE DATE,
  STATUS VARCHAR(20) ,
  UPDATED_BY VARCHAR(255),
  UPDATED_TIME VARCHAR(255),
  PRIMARY KEY (KEY_ID)
) ENGINE=InnoDB AUTO_INCREMENT=4096 DEFAULT CHARSET=latin1;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN FILING_NO VARCHAR(32) NULL;

ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN PURCHASED_ORDER_DATE DATE NULL;
ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN BILLING_TYPE VARCHAR(32);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE_ITEM ADD COLUMN SKU_DATE DATE NULL;
ALTER TABLE KETTLE_SCM_DEV.SERVICE_ORDER_ITEM ADD COLUMN COST_ELEMENT_DATE DATE NULL;
ALTER TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM ADD COLUMN RECEIVED_COST_ELEMENT_DATE DATE NULL;

ALTER TABLE KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION ADD COLUMN LIFE_TIME_CATEGORY_IN_MONTHS INT;

UPDATE `KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION` SET `LIFE_TIME_CATEGORY_IN_MONTHS` = '60' WHERE (`SUB_CATEGORY_ID` = '11');
UPDATE `KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION` SET `LIFE_TIME_CATEGORY_IN_MONTHS` = '96' WHERE (`SUB_CATEGORY_ID` = '16');
UPDATE `KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION` SET `LIFE_TIME_CATEGORY_IN_MONTHS` = '36' WHERE (`SUB_CATEGORY_ID` = '17');
UPDATE `KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION` SET `LIFE_TIME_CATEGORY_IN_MONTHS` = '60' WHERE (`SUB_CATEGORY_ID` = '18');
UPDATE `KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION` SET `LIFE_TIME_CATEGORY_IN_MONTHS` = '96' WHERE (`SUB_CATEGORY_ID` = '19');
UPDATE `KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION` SET `LIFE_TIME_CATEGORY_IN_MONTHS` = '36' WHERE (`SUB_CATEGORY_ID` = '28');


ALTER TABLE KETTLE_SCM_DEV.VENDOR_GR_PO_ITEM_MAPPING_DATA ADD COLUMN ACCEPTED_QUANTITY DECIMAL(16,6);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_GR_PO_ITEM_MAPPING_DATA ADD COLUMN LAST_UPDATED_BY INT(11);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_GR_PO_ITEM_MAPPING_DATA ADD COLUMN UPDATED_DATE TIMESTAMP;



CREATE TABLE KETTLE_SCM_DEV.GR_ITEM_QUANTITY_DEVIATION (
    GRITEM_QUANTITY_DEVIATION INT(11) PRIMARY KEY AUTO_INCREMENT,
    DEVIATION_DETAIL VARCHAR(255) NOT NULL
);

INSERT INTO KETTLE_SCM_DEV.GR_ITEM_QUANTITY_DEVIATION (`DEVIATION_DETAIL` ) VALUES ('QUANTITY ISSUE');
INSERT INTO KETTLE_SCM_DEV.GR_ITEM_QUANTITY_DEVIATION (`DEVIATION_DETAIL` ) VALUES ('SPECIFICATION NOT MATCHED WITH STANDARD');
INSERT INTO KETTLE_SCM_DEV.GR_ITEM_QUANTITY_DEVIATION (`DEVIATION_DETAIL` ) VALUES ('QUALITY ISSUE');
INSERT INTO KETTLE_SCM_DEV.GR_ITEM_QUANTITY_DEVIATION (`DEVIATION_DETAIL` ) VALUES ('OTHER');


ALTER TABLE KETTLE_SCM_DEV.VENDOR_GR_PO_ITEM_MAPPING_DATA
ADD COLUMN UPDATION_REASON VARCHAR(45) NULL,
ADD COLUMN DESCRIPTION VARCHAR(200) NULL;

CREATE TABLE KETTLE_SCM_DEV.BUSINESS_VENDOR_MAPPING (
    BUSINESS_VENDOR_MAPPING_ID INTEGER(11) NOT NULL AUTO_INCREMENT,
    BUSINESS_ID INTEGER(11) NOT NULL,
    VENDOR_ID INTEGER(11) NOT NULL,
    MAPPING_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL,
    PRIMARY KEY (BUSINESS_VENDOR_MAPPING_ID)
);

CREATE TABLE KETTLE_SCM_DEV.BUSINESS_DETAIL_DATA (
BUSINESS_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
BUSINESS_NAME VARCHAR (255) NOT NULL,
BUSINESS_DESCRIPTION VARCHAR (5000) NOT NULL
);

ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN PO_DOC_ID INTEGER;
ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN BUSINESS_TYPE varchar(32);

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN APPROVED_BY int(11) NULL;

ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN IRN_NO VARCHAR(5000);
ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN CANCEL_DOC_ID INTEGER;
ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN UPLOAD_DOC_ID VARCHAR(50);
ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN UPLOADED_EWAY_NO VARCHAR(50);
ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN UPLOADED_ACK_NO VARCHAR(50);


ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN SIGNED_QR_CODE VARCHAR(5000);
ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN BARCODE_ID INTEGER;

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN STATE_NAME VARCHAR(255) NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN STATE_CODE VARCHAR(32) NULL;

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN RECIPE_REQUIRED VARCHAR(1) NOT NULL DEFAULT 'N';
UPDATE KETTLE_SCM_DEV.PRODUCT_DEFINITION SET RECIPE_REQUIRED = 'Y' WHERE CATEGORY_ID = 3;
UPDATE KETTLE_SCM_DEV.PRODUCT_DEFINITION SET RECIPE_REQUIRED = 'Y' WHERE CATEGORY_ID = 4;

