CREATE TABLE KETTLE_SCM_DEV.STOCK_EVENT_CALENDAR (
  EVENT_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  SCHEDULED_DATE TIMESTAMP NULL,
  CREATION_TIME TIMESTAMP NULL,
  EVENT_TIME TIMESTAMP NULL,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ON_TIME TIMESTAMP NULL,
  UNIT_ID INTEGER NOT NULL,
  EVENT_FREQUENCY_TYPE VARCHAR(20) NOT NULL,
  UPDATE_TIME TIMESTAMP NULL,
  UPDATED_BY INTEGER NULL,
  CANCELLED_BY INTEGER NULL
);
ALTER TABLE KETTLE_SCM_DEV.STOCK_EVENT_CALENDAR ADD COLUMN EVENT_STATUS VARCHAR(20) NOT NULL;

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SECU', '7', '<PERSON><PERSON><PERSON>ENU', 'SHOW', 'SuMo -> Update-> Update stock calendar', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`)
VALUES ('STOCK_CALENDAR_MANAGER', 'Access to manage stock event calendar', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'STOCK_CALENDAR_MANAGER'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SECU'), 'ACTIVE', '120502', '2019-04-11 11:20:27');

ALTER TABLE KETTLE_SCM_DEV.STOCK_EVENT_CALENDAR
ADD INDEX `EVENT_STATUS_INDEX` (`EVENT_STATUS` ASC)
ADD INDEX `UNIT_ID_INDEX` (`UNIT_ID` ASC),
ADD INDEX `FREQUENCY_INDEX` (`EVENT_FREQUENCY_TYPE` ASC),
ADD INDEX `SHCEDULE_DATE_INDEX` (`SCHEDULED_DATE` ASC);

ALTER TABLE `KETTLE_SCM_DEV`.`COST_ELEMENT_DATA`
CHANGE COLUMN `COST_ELEMENT_DESCRIPTION` `COST_ELEMENT_DESCRIPTION` VARCHAR(500) NULL DEFAULT NULL ;

ALTER TABLE KETTLE_SCM_DEV.VENDOR_SKU_MAPPING ADD COLUMN SKU_ALIAS VARCHAR(100);
ALTER TABLE  KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE_ITEM ADD COLUMN SKU_ALIAS varchar(100);