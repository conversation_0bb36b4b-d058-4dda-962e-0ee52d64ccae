INSERT INTO `KETTLE_MASTER_DUMP`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('VSRCVA', '12', 'ACTION', 'VIEW', 'SERVICE ORDERING -> VIEW SERVICE ORDER -> FIND_ALL', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DUMP`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('1', (select ACTION_DETAIL_ID from KETTLE_MASTER_DUMP.ACTION_DETAIL WHERE ACTION_CODE = 'VSRCVA'), 'ACTIVE', '120063', '2020-03-27 00:00:00');
INSERT INTO `KETTLE_MASTER_DUMP`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('2', (select ACTION_DETAIL_ID from KETTLE_MASTER_DUMP.ACTION_DETAIL WHERE ACTION_CODE = 'VSRCVA'), 'ACTIVE', '120063', '2020-03-27 00:00:00');
INSERT INTO `KETTLE_MASTER_DUMP`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('6', (select ACTION_DETAIL_ID from KETTLE_MASTER_DUMP.ACTION_DETAIL WHERE ACTION_CODE = 'VSRCVA'), 'ACTIVE', '120063', '2020-03-27 00:00:00');
INSERT INTO `KETTLE_MASTER_DUMP`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('7', (select ACTION_DETAIL_ID from KETTLE_MASTER_DUMP.ACTION_DETAIL WHERE ACTION_CODE = 'VSRCVA'), 'ACTIVE', '120063', '2020-03-27 00:00:00');
INSERT INTO `KETTLE_MASTER_DUMP`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('8', (select ACTION_DETAIL_ID from KETTLE_MASTER_DUMP.ACTION_DETAIL WHERE ACTION_CODE = 'VSRCVA'), 'ACTIVE', '120063', '2020-03-27 00:00:00');

ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_ORDER` 
ADD COLUMN `TAG_NAME` VARCHAR(100) NULL DEFAULT NULL ;

CREATE TABLE `KETTLE_SCM_DEV`.`CAPEX_REQUEST_DATA` (
  `CAPEX_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NOT NULL,
  `UNIT_NAME` VARCHAR(100) NOT NULL,
  `TYPE` VARCHAR(45) NOT NULL,
  `VERSION` VARCHAR(45) NULL DEFAULT NULL,
  `ACCESS_KEY` VARCHAR(100) NULL DEFAULT NULL,
  `GENERATED_TIME` DATETIME NULL DEFAULT NULL,
  `LAST_UPDATED_TIME` DATETIME NULL DEFAULT NULL,
  `GENERATED_BY` INT(11) NULL DEFAULT NULL,
  `LAST_UPDATED_BY` INT(11) NULL DEFAULT NULL,
  `STATUS` VARCHAR(50) NULL DEFAULT NULL,
  PRIMARY KEY (`CAPEX_ID`));


CREATE TABLE `KETTLE_SCM_DEV`.`CAPEX_TEMPLATE` (
  `ID` INT(10) NOT NULL,
  `TYPE` VARCHAR(100) NULL,
  `BUCKET` VARCHAR(255) NULL,
  `KEY` VARCHAR(255) NULL,
  `URL` VARCHAR(255) NULL,
  `VERSION` VARCHAR(45) NULL,
  `STATUS` VARCHAR(45) NULL,
  PRIMARY KEY (`ID`));



INSERT INTO `KETTLE_SCM_DEV`.`CAPEX_TEMPLATE` (`ID`, `TYPE`, `BUCKET`, `KEY`, `URL`, `VERSION`, `STATUS`) VALUES ('1', 'Renovation', 'dev.capex-template', 'Capex_Template.xlsx','https://chaayos.s3-eu-west-1.amazonaws.com', '1.0', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`CAPEX_TEMPLATE` (`ID`, `TYPE`, `BUCKET`, `KEY`, `URL`, `VERSION`, `STATUS`) VALUES ('2', 'New Cafe', 'dev.capex-template', 'Capex_Template.xlsx', 'https://chaayos.s3-eu-west-1.amazonaws.com', '1.0', 'ACTIVE');


CREATE TABLE `KETTLE_SCM_DEV`.`CAPEX_AUDIT_DETAIL` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `CAPEX_REQUEST_ID` int(11) NOT NULL,
  `DOWNLOAD_BY` varchar(100) DEFAULT NULL,
  `UPLOADED_BY` varchar(100) DEFAULT NULL,
  `DOWNLOADED_PATH` varchar(200) DEFAULT NULL,
  `UPLOADED_PATH` varchar(200) DEFAULT NULL,
  `ACCESS_KEY` varchar(100) DEFAULT NULL,
  `AMOUNT` decimal(16,6) NOT NULL,
  `STATUS` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `KETTLE_SCM_DEV`.`CAPEX_BUDGET_AUDIT_DETAIL` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `CAPEX_AUDIT_DETAIL_ID` int(11) DEFAULT NULL,
  `DEPARTMENT_ID` int(11) DEFAULT NULL,
  `DEPARTMENT_NAME` varchar(100) DEFAULT NULL,
  `AMOUNT` decimal(16,6) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `CAPEX_AUDIT_ID_idx` (`CAPEX_AUDIT_DETAIL_ID`),
  CONSTRAINT `CAPEX_AUDIT_ID` FOREIGN KEY (`CAPEX_AUDIT_DETAIL_ID`)
  REFERENCES `CAPEX_AUDIT_DETAIL` (`ID`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8;

CREATE TABLE `KETTLE_SCM_DEV`.`CAPEX_BUDGET_DETAIL` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NULL,
  `BUDGET_TYPE` VARCHAR(45) NULL,
  `DEPARTMENT_ID` INT(11) NULL,
  `DEPARTMENT_NAME` VARCHAR(100) NULL,
  `BUDGET_AMOUNT` DECIMAL(16,6) NULL,
  `RUNNING_AMOUNT` DECIMAL(16,6) NULL,
  `RECEIVED_AMOUNT` DECIMAL(16,6) NULL,
  `REMAINING_AMOUNT` DECIMAL(16,6) NULL,
  `STATUS` VARCHAR(45) NULL,
  PRIMARY KEY (`ID`));
  
ALTER TABLE `KETTLE_SCM_DEV`.`COST_ELEMENT_DATA` 
DROP COLUMN `TDS_RATE`,
DROP COLUMN `COST_CENTER_ID`;

ALTER TABLE `KETTLE_SCM_DEV`.`CAPEX_AUDIT_DETAIL` 
DROP COLUMN `AMOUNT`;

ALTER TABLE `KETTLE_SCM_DEV`.`CAPEX_AUDIT_DETAIL` 
ADD COLUMN `STATUS` VARCHAR(45) NULL DEFAULT NULL;

CREATE TABLE KETTLE_SCM_DEV.CAPEX_REQUEST_STATUS_LOG (
    CAPEX_REQUEST_STATUS_LOG_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    FROM_STATUS VARCHAR(255) NOT NULL,
    TO_STATUS VARCHAR(255) NOT NULL,
    UPDATED_BY INT(11),
    UPDATE_TIME TIMESTAMP,
    CAPEX_REQUEST_ID INT(11),
    CAPEX_AUDIT_ID INT(11),
	STATUS VARCHAR(50),
    REMARKS VARCHAR(255)
);

ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_ORDER_STATUS_EVENT` 
CHANGE COLUMN `FROM_STATUS` `FROM_STATUS` VARCHAR(30) NULL DEFAULT NULL ,
CHANGE COLUMN `TO_STATUS` `TO_STATUS` VARCHAR(30) NULL DEFAULT NULL ;

CREATE TABLE `KETTLE_SCM_DEV`.`BUDGET_AUDIT_DETAIL` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `CAPEX_AUDIT_ID` INT(11) NULL,
  `CAPEX_BUDGET_DETAIL_ID` INT(11) NULL,
  `AMOUNT_TYPE` VARCHAR(100) NULL,
  `PREVIOUS_VALUE` DECIMAL(16,6) NULL,
  `FINAL_VALUE` DECIMAL(16,6) NULL,
  `ACTION_TYPE` VARCHAR(100) NULL,
  `ACTION` VARCHAR(100) NULL,
  `KEY_TYPE` VARCHAR(100) NULL,
  `KEY_VALUE` INT(11) NULL,
  `COMMENT` VARCHAR(300) NULL,
  `ACTION_BY` INT(11) NULL,
  `ACTION_TIME` TIMESTAMP NULL,
  PRIMARY KEY (`ID`));
  
ALTER TABLE `KETTLE_SCM_DEV`.`CAPEX_BUDGET_DETAIL` 
ADD COLUMN `ORIGINAL_AMOUNT` DECIMAL(16,6) NULL DEFAULT NULL,
ADD COLUMN `PAID_AMOUNT` DECIMAL(16,6) NULL DEFAULT NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`CAPEX_BUDGET_DETAIL` 
ADD COLUMN `INITIAL_AMOUNT` DECIMAL(16,6) NULL DEFAULT NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`CAPEX_REQUEST_DATA` 
ADD COLUMN `COMMENT` VARCHAR(500) NULL DEFAULT NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`COST_ELEMENT_DATA` 
ADD COLUMN `ISPRICEUPDATE` VARCHAR(45) NULL DEFAULT NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`CAPEX_BUDGET_DETAIL` 
ADD COLUMN `CAPEX_REQUEST_ID` INT(11) NOT NULL AFTER `INITIAL_AMOUNT`,
ADD COLUMN `CAPEX_AUDIT_ID` INT(11) NOT NULL AFTER `CAPEX_REQUEST_ID`,
ADD COLUMN `CAPEX_BUDGET_AUDIT_ID` INT(11) NOT NULL AFTER `CAPEX_AUDIT_ID`;




