DELIMITER $$
CREATE PROCEDURE KETTLE_SCM.REMOVE_DUPLICATE_KEY_ERROR()
BEGIN

UPDATE KETTLE_SCM.COST_DETAIL_DATA cd,
    (SELECT 
        UNIT_ID, KEY_ID, COUNT(*) CNT, MIN(COST_DETAIL_DATA_ID) COST_DETAIL_DATA_ID
    FROM
        KETTLE_SCM.COST_DETAIL_DATA
    WHERE
        IS_LATEST = 'Y'
    GROUP BY UNIT_ID , KEY_ID
    HAVING COUNT(*) > 1) r 
SET 
    cd.IS_LATEST = 'N'
WHERE
    r.COST_DETAIL_DATA_ID = cd.COST_DETAIL_DATA_ID
    AND cd.IS_LATEST = 'Y';
 
 END$$
DELIMITER ;
