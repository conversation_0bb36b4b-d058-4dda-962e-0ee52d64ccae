INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('PRODUCT_ID', 'PRODUCT_NAME', 'PRODUCT_DESCRIPTION', 'CATEGORY_ID', 'SUPPORTS_LOOSE_ORDERING', 'CREATION_DATE', 'CREATED_BY', 'HAS_INNER', 'HAS_CASE', 'STOCK_KEEPING_FREQUENCY', 'PRODUCT_CODE', 'SHELF_LIFE_IN_DAYS', 'PRODUCT_STATUS', 'UNIT_OF_MEASURE', 'PARTICIPATES_IN_RECIPE', 'VARIANT_LEVEL_ORDERING', 'PRODUCT_IMAGE', 'SUB_CATEGORY_ID');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100001', '1 Sink Unit', '1 Sink Unit', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '1', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100002', '2 Minutes Filling', '2 Minutes Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '2', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100003', '2 Sink Unit', '2 Sink Unit', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '3', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100004', '250 Ml Sipper Lid', '250 Ml Sipper Lid', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '4', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100005', '3 Sink Unit', '3 Sink Unit', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '5', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100006', '360 Ml Sipper Lid', '360 Ml Sipper Lid', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '6', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100007', 'A4 Paper Khanna', 'A4 Paper Khanna', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '7', '1', 'ACTIVE', 'PACKET', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100008', 'Aam Papad', 'Aam Papad', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '8', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100009', 'AC', 'Ac', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '9', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100010', 'Achari Butter', 'Achari Butter', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '10', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100011', 'Achari Chicken Samosa', 'Achari Chicken Samosa', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '11', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100012', 'Acrylic Milk Bottle', 'Acrylic Milk Bottle', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '12', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100013', 'Acrylic Water Glass', 'Acrylic Water Glass', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '13', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100014', 'Add On Stand - 4', 'Add On Stand - 4', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '14', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100015', 'Add On Stand - 8', 'Add On Stand - 8', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '15', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100016', 'Add Ons Box', 'Add Ons Box', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '16', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100017', 'Ajwain', 'Ajwain', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '17', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100018', 'Jeera Cookies Pieces', 'Jeera Cookies Pieces', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '18', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100019', 'Babycorn', 'Babycorn', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '19', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100020', 'Badam', 'Badam', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '20', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100021', 'Bain Marie', 'Bain Marie', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '21', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100022', 'Bain Marie Basket', 'Bain Marie Basket', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '22', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100023', 'Bain Marie Display Stand', 'Bain Marie Display Stand', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '23', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100024', 'Baking Powder', 'Baking Powder', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '24', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100025', 'Ball Pen', 'Ball Pen', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '25', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100026', 'Balsamico Filling', 'Balsamico Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '26', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100027', 'Banana Cake Whole', 'Banana Cake Whole', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '27', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100028', 'Banana Filling', 'Banana Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '28', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100029', 'Banking Envelope', 'Banking Envelope', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '29', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100030', 'Bhujia', 'Bhujia', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '30', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100031', 'Bin Liner 36*55 - Large', 'Bin Liner 36*55 - Large', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '31', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100032', 'Bin Liner Medium', 'Bin Liner Medium', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '32', '1', 'ACTIVE', 'KG', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100033', 'Bin Liner Small', 'Bin Liner Small', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '33', '1', 'ACTIVE', 'KG', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100034', 'Black Board', 'Black Board', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '34', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100035', 'Black Grape Filling', 'Black Grape Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '35', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100036', 'Black Pepper', 'Black Pepper', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '36', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100037', 'Black Salt', 'Black Salt', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '37', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100038', 'Blackboard Stand', 'Blackboard Stand', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '38', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100039', 'Blueberry Cake Whole', 'Blueberry Cake Whole', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '39', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100040', 'Boiler Element', 'Boiler Element', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '40', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100041', 'Bolt Pronto Oven', 'Bolt Pronto Oven', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '41', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100042', 'Bolt Tray', 'Bolt Tray', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '42', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100043', 'Bottle Cleaning Brush', 'Bottle Cleaning Brush', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '43', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100044', 'Bottle Stand - Cold Station', 'Bottle Stand - Cold Station', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '44', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100045', 'Bournvita', 'Bournvita', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '45', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100046', 'Box File', 'Box File', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '46', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100047', 'Bread Box', 'Bread Box', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '47', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100048', 'Bread Knife', 'Bread Knife', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '48', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100049', 'Broom Hard', 'Broom Hard', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '49', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100050', 'Brown Tape', 'Brown Tape', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '50', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100051', 'Bubble Wrap Sheet', 'Bubble Wrap Sheet', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '51', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100052', 'Built Too : Cold Station Setup', 'Built Too : Cold Station Setup', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '52', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100053', 'Built Too : Food Station Setup', 'Built Too : Food Station Setup', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '53', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100054', 'Built Too : Service Area', 'Built Too : Service Area', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '54', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100055', 'Built Too: Hot Station Setup', 'Built Too: Hot Station Setup', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '55', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100056', 'Butter', 'Butter', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '56', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '3');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100057', 'Butter Bowl', 'Butter Bowl', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '57', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100058', 'Butter Chicken Filling', 'Butter Chicken Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '58', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100059', 'Butter Knife', 'Butter Knife', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '59', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100060', 'Butter Paper Non-Veg', 'Butter Paper Non-Veg', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '60', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100061', 'Butter Paper Veg', 'Butter Paper Veg', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '61', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100062', 'C Fold Dispenser', 'C Fold Dispenser', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '62', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100063', 'C/Fold Tissue', 'C/Fold Tissue', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '63', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100064', 'Cake Brush', 'Cake Brush', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '64', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100065', 'Calculator', 'Calculator', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '65', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100066', 'Candle', 'Candle', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '66', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100067', 'Capsicum', 'Capsicum', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '67', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100068', 'Carbon Paper', 'Carbon Paper', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '68', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100069', 'Cardamom Black', 'Cardamom Black', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '69', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100070', 'Cardamom Green', 'Cardamom Green', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '70', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100071', 'Carrot Cake Whole', 'Carrot Cake Whole', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '71', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100072', 'Cash Drawer', 'Cash Drawer', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '72', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100073', 'Cash Handover Sheet & Daily Cash Pickup Register', 'Cash Handover Sheet & Daily Cash Pickup Register', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '73', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100074', 'Cash Voucher Pad', 'Cash Voucher Pad', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '74', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100075', 'Ceramic Mug Medium', 'Ceramic Mug Medium', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '75', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100076', 'Ceramic Mug Small', 'Ceramic Mug Small', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '76', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100077', 'Chai delivery  Box 1 Ltr', 'Chai delivery  Box 1 Ltr', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '77', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100078', 'Chai Masala', 'Chai Masala', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '78', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100079', 'Chai Sachet Container', 'Chai Sachet Container', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '79', '1', 'ACTIVE', 'SACHET', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100080', 'Chalk', 'Chalk', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '80', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100081', 'Chat Masala', 'Chat Masala', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '81', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100082', 'Chatpata Kebab Filling', 'Chatpata Kebab Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '82', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100083', 'Chikoo Filling', 'Chikoo Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '83', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100084', 'Chocolate Bun Filling', 'Chocolate Bun Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '84', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100085', 'Chocolate Drinking Mix', 'Chocolate Drinking Mix', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '85', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100086', 'Chocolate Syrup', 'Chocolate Syrup', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '86', '1', 'ACTIVE', 'L', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100087', 'Chopping Board - Non Veg', 'Chopping Board - Non Veg', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '87', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100088', 'Chopping Board - Veg', 'Chopping Board - Veg', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '88', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100089', 'Cinnamon Roll', 'Cinnamon Roll', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '89', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100090', 'Citronella Oil', 'Citronella Oil', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '90', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100091', 'Clear Tape 1\"', 'Clear Tape 1\"', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '91', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100092', 'Clear Tape 2\"', 'Clear Tape 2\"', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '92', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100093', 'Cling Wrap', 'Cling Wrap', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '93', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100094', 'Clove Whole', 'Clove Whole', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '94', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100095', 'Cobra File', 'Cobra File', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '95', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100096', 'Coffee Delight', 'Coffee Delight', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '96', '1', 'ACTIVE', 'L', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100097', 'Coffee Powder', 'Coffee Powder', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '97', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100098', 'Cold Cup 350 Ml', 'Cold Cup 350 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '98', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100099', 'Cold Cup Lid', 'Cold Cup Lid', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '99', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100100', 'Cold Cups 500 Ml', 'Cold Cups 500 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '100', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100101', 'Condiment Box', 'Condiment Box', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '101', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100102', 'Container 1 L', 'Container 1 L', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '102', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100103', 'Container 1.5L', 'Container 1.5L', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '103', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100104', 'Container 10 L', 'Container 10 L', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '104', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100105', 'Container 2 L', 'Container 2 L', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '105', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100106', 'Container 200 Ml', 'Container 200 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '106', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100107', 'Container 3.8 L', 'Container 3.8 L', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '107', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100108', 'Container 5 L', 'Container 5 L', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '108', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100109', 'Container 500Ml', 'Container 500Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '109', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100110', 'Conveyer Toaster', 'Conveyer Toaster', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '110', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100111', 'Coriander', 'Coriander', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '111', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100112', 'Correction Pen', 'Correction Pen', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '112', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100113', 'Counter Top', 'Counter Top', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '113', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100114', 'Croissant', 'Croissant', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '114', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100115', 'Cucumber', 'Cucumber', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '115', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100116', 'Cup Holder', 'Cup Holder', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '116', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100117', 'Cup Stand - Cold Station', 'Cup Stand - Cold Station', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '117', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100118', 'Cup Stand - Hot Station', 'Cup Stand - Hot Station', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '118', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100119', 'Cutting Chai Glass', 'Cutting Chai Glass', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '119', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100120', 'D10', 'D10', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '120', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100121', 'Daily Checklist Book (Gold Standard)', 'Daily Checklist Book (Gold Standard)', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '121', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100122', 'Darjeeling First Flush Patti', 'Darjeeling First Flush Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '122', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100123', 'Day Part Plan', 'Day Part Plan', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '123', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100124', 'Desi Chai Can', 'Desi Chai Can', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '124', '90', 'ACTIVE', 'PC', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100125', 'Desi Chai Patti', 'Desi Chai Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '125', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100126', 'Diesel', 'Diesel', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '126', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100127', 'Diffuser', 'Diffuser', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '127', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100128', 'Dip Bottle', 'Dip Bottle', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '128', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100129', 'Dip Container With Lid', 'Dip Container With Lid', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '129', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100130', 'Dip Cup', 'Dip Cup', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '130', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100131', 'Display Basket', 'Display Basket', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '131', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100132', 'Disposable Cap', 'Disposable Cap', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '132', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100133', 'TO GO Tray Mat', 'TO GO Tray Mat', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '133', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100134', 'Dome Cover', 'Dome Cover', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '134', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100135', 'Doormat', 'Doormat', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '135', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100136', 'Double Side Tape 1\"', 'Double Side Tape 1\"', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '136', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100137', 'Dust Control Mop Head', 'Dust Control Mop Head', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '137', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100138', 'Dust Control Mop Rod', 'Dust Control Mop Rod', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '138', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100139', 'Dust Pan', 'Dust Pan', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '139', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100140', 'Dustbin Padal', 'Dustbin Padal', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '140', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100141', 'Duster', 'Duster', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '141', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100142', 'Dustpan With Broom', 'Dustpan With Broom', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '142', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100143', 'Earl Grey Patti', 'Earl Grey Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '143', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100144', 'Egg Bun Filling', 'Egg Bun Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '144', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100145', 'Electronic Safe', 'Electronic Safe', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '145', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100146', 'English Breakfast Patti', 'English Breakfast Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '146', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100147', 'English Oven Bun', 'English Oven Bun', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '147', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100148', 'Eraser', 'Eraser', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '148', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100149', 'Extension Board', 'Extension Board', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '149', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100150', 'Fire Extinguisher 2Kg', 'Fire Extinguisher 2Kg', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '150', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100151', 'Fire Extinguisher 4Kg', 'Fire Extinguisher 4Kg', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '151', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100152', 'Fly Catcher Pad', 'Fly Catcher Pad', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '152', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100153', 'Fly Catcher Tubelight', 'Fly Catcher Tubelight', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '153', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100154', 'Fly Killer Machine', 'Fly Killer Machine', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '154', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100155', 'Focaccia', 'Focaccia', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '155', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100156', 'Food Temperature Thermometer', 'Food Temperature Thermometer', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '156', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100157', 'Fridge', 'Fridge', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '157', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100158', 'Frozen Tortilla', 'Frozen Tortilla', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '158', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100159', 'Funnel', 'Funnel', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '159', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100160', 'Garlic', 'Garlic', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '160', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100161', 'Gental Soap', 'Gental Soap', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '161', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100162', 'Ginger', 'Ginger', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '162', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100163', 'Ginger Bits', 'Ginger Bits', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '163', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100164', 'Glass Cleaning Handle', 'Glass Cleaning Handle', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '164', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100165', 'Glass Cleaning Kit', 'Glass Cleaning Kit', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '165', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100166', 'Glass Jar', 'Glass Jar', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '166', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100167', 'Coloured glasses', 'Coloured glasses', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '167', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100168', 'Glue Stick', 'Glue Stick', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '168', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100169', 'Gods Chai Patti', 'Gods Chai Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '169', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100170', 'Green Chicken Filling', 'Green Chicken Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '170', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100171', 'Green Chilli', 'Green Chilli', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '171', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100172', 'Green Chilli Mint Mayo Sauce', 'Green Chilli Mint Mayo Sauce', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '172', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100173', 'Green Tea Can', 'Green Tea Can', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '173', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100174', 'Green Tea Patti', 'Green Tea Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '174', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100175', 'Griller', 'Griller', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '175', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100176', 'Grooming Kit', 'Grooming Kit', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '176', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100177', 'Hand Gloves', 'Hand Gloves', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '177', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100178', 'Hand Wash', 'Hand Wash', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '178', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100179', 'Handheld Menu Stand', 'Handheld Menu Stand', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '179', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100180', 'Hari Chuttney', 'Hari Chuttney', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '180', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100181', 'Toilet Cleaner', 'Toilet Cleaner', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '181', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100182', 'Homestyle Aloo Filling', 'Homestyle Aloo Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '182', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100183', 'Honey', 'Honey', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '183', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100184', 'Honey Garlic Mayo Sauce', 'Honey Garlic Mayo Sauce', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '184', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100185', 'Honey Lemon Butter', 'Honey Lemon Butter', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '185', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100186', 'Hot Cup 150 Ml', 'Hot Cup 150 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '186', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100187', 'Hot Cup 250 Ml', 'Hot Cup 250 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '187', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100188', 'Hot Cups 360 Ml', 'Hot Cups 360 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '188', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100189', 'Ice', 'Ice', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '189', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100190', 'Ice Machine', 'Ice Machine', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '190', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100191', 'Ice Tray', 'Ice Tray', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '191', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100192', 'Induction Plate', 'Induction Plate', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '192', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100193', 'Italian Burger Bun', 'Italian Burger Bun', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '193', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100194', 'Jasmine Tea Patti', 'Jasmine Tea Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '194', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100195', 'Jumbo Bread', 'Jumbo Bread', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '195', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100196', 'Kadhai Paneer Filling', 'Kadhai Paneer Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '196', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100198', 'Kashmiri Kahwa Patti', 'Kashmiri Kahwa Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '198', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100199', 'Keema Pav Filling', 'Keema Pav Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '199', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100200', 'Kesar', 'Kesar', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '200', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100201', 'Ketchup Sachet', 'Ketchup Sachet', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '201', '1', 'ACTIVE', 'SACHET', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100202', 'Key Ring', 'Key Ring', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '202', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100203', 'Keyboard', 'Keyboard', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '203', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100204', 'Kitchen Knife', 'Kitchen Knife', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '204', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100205', 'Kiwi Filling', 'Kiwi Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '205', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100206', 'Kulhad 120 Ml', 'Kulhad 120 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '206', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100207', 'Kulhad 250 Ml', 'Kulhad 250 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '207', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100208', 'Landline', 'Landline', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '208', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100209', 'Lasoon Chutney', 'Lasoon Chuttney', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '209', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100210', 'Lemon', 'Lemon', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '210', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100211', 'Lemon Cake Whole', 'Lemon Cake Whole', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '211', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100212', 'Lemon Grass Patti', 'Lemon Grass Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '212', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100213', 'Lemon Juice Machine', 'Lemon Juice Machine', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '213', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100214', 'Lemon Powder', 'Lemon Powder', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '214', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100215', 'Lopchu Patti', 'Lopchu Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '215', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100216', 'M Fold Hand Towel', 'M Fold Hand Towel', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '216', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100217', 'Magic Broom', 'Magic Broom', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '217', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100218', 'Manager Laptop', 'Manager Laptop', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '218', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100219', 'Manager Shirt', 'Manager Shirt', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '219', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100220', 'Mango Filling', 'Mango Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '220', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100221', 'Manual Bill Book', 'Manual Bill Book', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '221', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100222', 'Masala Chai Can', 'Masala Chai Can', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '222', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100223', 'Measuring Beaker 100Ml', 'Measuring Beaker 100Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '223', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100224', 'Measuring Beaker 115 Ml', 'Measuring Beaker 115 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '224', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100225', 'Measuring Beaker 165 Ml', 'Measuring Beaker 165 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '225', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100226', 'Measuring Beaker 230 Ml', 'Measuring Beaker 230 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '226', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100227', 'Measuring Beaker 250Ml', 'Measuring Beaker 250Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '227', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100228', 'Measuring Beaker 330 Ml', 'Measuring Beaker 330 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '228', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100229', 'Measuring Beaker 460 Ml', 'Measuring Beaker 460 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '229', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100230', 'Measuring Beaker 50 Ml', 'Measuring Beaker 50 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '230', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100231', 'Measuring Beaker 575 Ml', 'Measuring Beaker 575 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '231', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100232', 'Measuring Jar 1 Ltr', 'Measuring Jar 1 Ltr', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '232', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100233', 'Measuring Jar 2 Ltr', 'Measuring Jar 2 Ltr', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '233', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100234', 'Measuring Spoon Set', 'Measuring Spoon Set', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '234', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100235', 'Microwave Oven', 'Microwave Oven', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '235', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100236', 'Microwave Bowl - Non Veg', 'Microwave Bowl - Non Veg', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '236', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100237', 'Microwave Bowl - Veg', 'Microwave Bowl - Veg', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '237', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100238', 'Milk', 'Milk', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '238', '1', 'ACTIVE', 'L', 'Y', 'Y', '', '3');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100239', 'Mineral Water Jar 20 Ltr', 'Mineral Water Jar 20 Ltr', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '239', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100240', 'Mint And Jalapeno Butter', 'Mint And Jalapeno Butter', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '240', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100241', 'Mint Green Patti', 'Mint Green Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '241', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100242', 'Mint Leaves', 'Mint Leaves', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '242', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100243', 'Mixer Grinder', 'Mixer Grinder', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '243', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100244', 'Mobile', 'Mobile', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '244', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100245', 'Passion Fruit Puree', 'Passion Fruit Puree', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '245', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100246', 'Peach Puree', 'Peach Puree', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '246', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100247', 'Mop Trolly', 'Mop Trolly', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '247', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100248', 'Mouse', 'Mouse', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '248', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100249', 'Mud Sticker', 'Mud Sticker', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '249', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100250', 'Multigrain Bread', 'Multigrain Bread', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '250', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100251', 'Muscatel Patti', 'Muscatel Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '251', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100252', 'Music System', 'Music System', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '252', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100253', 'Mutton Lazeez Filling', 'Mutton Lazeez Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '253', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100254', 'Napoli Filling', 'Napoli Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '254', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100255', 'Room Freshner Bar', 'Room Freshner Bar', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '255', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100256', 'Oil & Grease Trap', 'Oil & Grease Trap', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '256', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100257', 'Olive And Sundried Tomato Butter', 'Olive And Sundried Tomato Butter', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '257', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100258', 'Onion', 'Onion', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '258', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100259', 'Orange Pekoe Patti', 'Orange Pekoe Patti', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '259', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100260', 'Paneer Khurchan Samosa', 'Paneer Khurchan Samosa', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '260', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100261', 'Parfait Spoon', 'Parfait Spoon', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '261', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100262', 'Pastry Tong', 'Pastry Tong', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '262', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100263', 'Pav', 'Pav', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '263', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100264', 'Pen Drive 8 Gb', 'Pen Drive 8 Gb', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '264', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100265', 'Pencil', 'Pencil', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '265', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100266', 'Pepper Chicken Filling', 'Pepper Chicken Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '266', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100267', 'Permanet Marker', 'Permanet Marker', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '267', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100268', 'Phool Broom', 'Phool Broom', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '268', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100269', 'Plastic Chair', 'Plastic Chair', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '269', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100270', 'Plastic Poha Container', 'Plastic Poha Container', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '270', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100271', 'Plastic Spoon', 'Plastic Spoon', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '271', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100272', 'Pocha', 'Pocha', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '272', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100273', 'Poha Box', 'Poha Box', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '273', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100274', 'Poha Box Lid', 'Poha Box Lid', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '274', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100275', 'Poha Dry', 'Poha Dry', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '275', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100276', 'Poha Masala', 'Poha Masala', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '276', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100277', 'Poha Plate', 'Poha Plate', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '277', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100278', 'Polythene PP', 'Polythene PP', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '278', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100279', 'POS Laptop', 'Pos Laptop', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '279', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100280', 'Potato', 'Potato', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '280', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '9');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100281', 'Printer', 'Printer', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '281', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100282', 'Printer Roll', 'Printer Roll', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '282', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100283', 'Pullover (Large)', 'Pullover (Large)', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '283', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100284', 'Pullover (Medium)', 'Pullover (Medium)', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '284', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100285', 'Pullover (Small)', 'Pullover (Small)', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '285', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100286', 'Pullover (Xl)', 'Pullover (Xl)', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '286', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100287', 'Pump 10 Ml', 'Pump 10 Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '287', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100288', 'Pump 15Ml', 'Pumps 15Ml', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '288', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100289', 'Punching Machine', 'Punching Machine', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '289', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100290', 'R1', 'R1', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '290', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100291', 'R2 - Floor Cleaner', 'R2 - Floor Cleaner', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '291', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100292', 'R3', 'R3', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '292', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100293', 'R4', 'R4', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '293', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100294', 'Red Chilli Mayo Sauce', 'Red Chilli Mayo Sauce', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '294', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100295', 'Register', 'Register', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '295', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100296', 'RO - 15 L', 'RO - 15 L', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '296', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100297', 'RO - 25 L', 'RO - 25 L', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '297', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100300', 'Room Freshner', 'Room Freshner', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '300', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100301', 'Rose Water', 'Rose Water', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '301', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100302', 'Rubber  Stamp', 'Rubber  Stamp', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '302', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100303', 'Rubber Band', 'Rubber Band', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '303', '1', 'ACTIVE', 'PACKET', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100304', 'Rusk Pack', 'Rusk Pack', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '304', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100305', 'Sachet - Desi Regular', 'Sachet - Desi Regular', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '305', '1', 'ACTIVE', 'SACHET', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100306', 'Saunf', 'Saunf', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '306', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100307', 'Scissor', 'Scissor', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '307', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100308', 'Scoop 1/4', 'Scoop 1/4', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '308', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100309', 'Scotch Bright', 'Scotch Bright', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '309', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100310', 'Service Tray', 'Service Tray', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '310', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100311', 'Sharpner', 'Sharpner', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '311', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100312', 'Shikanji Masala', 'Shikanji Masala', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '312', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100313', 'Sicilian Chicken Filling', 'Sicilian Chicken Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '313', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100314', 'Sign Board - Wet Floor', 'Sign Board - Wet Floor', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '314', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100315', 'Silver Cutting', 'Silver Cutting', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '315', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100316', 'Silver Foil', 'Silver Foil', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '316', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100317', 'Sink Mesh Jali', 'Sink Mesh Jali', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '317', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100318', 'Soap Dispenser', 'Soap Dispenser', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '318', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100319', 'Soda', 'Soda', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '319', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100320', 'Spinach Corn Cheese Filling', 'Spinach Corn Cheese Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '320', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100321', 'Spiral Note Book', 'Spiral Note Book', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '321', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100322', 'Sponge Wipe', 'Sponge Wipe', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '322', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100323', 'Spoon Ss Big', 'Spoon Ss Big', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '323', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100324', 'Spoon Ss Small', 'Spoon Ss Small', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '324', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100325', 'Spray', 'Spray', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '325', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100326', 'Spray Bottle', 'Spray Bottle', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '326', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100327', 'Sprinkler', 'Sprinkler', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '327', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100328', 'Square Dustbin', 'Square Dustbin', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '328', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100329', 'Squeeze Bottle', 'Squeeze Bottle', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '329', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100330', 'Steel Scale', 'Steel Scale', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '330', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100331', 'Steel Scruber', 'Steel Scruber', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '331', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100332', 'Sticker - Non Veg', 'Sticker - Non Veg', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '332', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100333', 'Sticker - Veg', 'Sticker - Veg', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '333', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100334', 'Stirrer', 'Stirrer', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '334', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100335', 'Straw', 'Straw', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '335', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100336', 'Strawberry Filling', 'Strawberry Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '336', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100337', 'Sugar', 'Sugar', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '337', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100338', 'Sugar Free Sachet', 'Sugar Free Sachet', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '338', '1', 'ACTIVE', 'SACHET', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100339', 'Sugar Sachet - Brown', 'Sugar Sachet - Brown', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '339', '1', 'ACTIVE', 'SACHET', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100340', 'Sugar Sachet - White', 'Sugar Sachet - White', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '340', '1', 'ACTIVE', 'SACHET', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100341', 'Chutney Jar', 'Chuttney Jar', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '341', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100342', 'Grinder Jaar', 'Grinder Jaar', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '342', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100343', 'Suma Det', 'Suma Det', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '343', '1', 'ACTIVE', 'L', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100344', 'Suma Grill', 'Suma Grill', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '344', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100345', 'Tab', 'Tab', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '345', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100346', 'Tab Stand 10 Inches', 'Tab Stand 10\"', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '346', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100347', 'Take Away Bag - Large', 'Take Away Bag - Large', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '347', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100348', 'Take Away Bag - Small', 'Take Away Bag - Small', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '348', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100349', 'Tape Dispenser', 'Tape Dispenser', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '349', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100350', 'Chai Delivery Box 400 Ml', 'Chai delivery Box 400 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '350', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100351', 'Tea Fun Box', 'Tea Fun Box', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '351', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100352', 'Tea Pan 6 L', 'Tea Pan 6 L', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '352', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100353', 'Chai delivery Pouch 1 L', 'Chai delivery Pouch 1 L', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '353', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100354', 'Chai delivery Pouch 400 Ml', 'Tea Pouch 400 Ml', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '354', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100355', 'Tea Saucepan Medium', 'Tea Saucepan Medium', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '355', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100356', 'Tea Saucepan Small', 'Tea Saucepan Small', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '356', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100357', 'Tea Stainer Large', 'Tea Stainer Large', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '357', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100358', 'Tea Stainer Medium', 'Tea Stainer Medium', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '358', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100359', 'Tea Stainer Small', 'Tea Stainer Small', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '359', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100360', 'Temperature And Humidity Meter', 'Temperature And Humidity Meter', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '360', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100361', 'Tempered Glass - Customer Screen Tab', 'Tempered Glass - Customer Screen Tab', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '361', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100362', 'Tempered Glass - Workstation Tab', 'Tempered Glass - Workstation Tab', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '362', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100363', 'Thandai', 'Thandai', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '363', '1', 'ACTIVE', 'L', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100364', 'Timer', 'Timer', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '364', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100365', 'Tissue Holder', 'Tissue Holder', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '365', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100366', 'Tissue Paper', 'Tissue Paper', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '366', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100367', 'Toilet Brush', 'Toilet Brush', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '367', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100368', 'Toilet Roll', 'Toilet Roll', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '368', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100369', 'Tomato', 'Tomato', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '369', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100370', 'Tong', 'Tong', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '370', '1', 'ACTIVE', 'PC', 'N', 'N', '', '10');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100371', 'Tool Kit', 'Tool Kit', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '371', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100372', 'Tray Mat', 'Tray Mat', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '372', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '4');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100373', 'T-Shirt Large', 'T-Shirt Large', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '373', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100374', 'T-Shirt Medium', 'T-Shirt Medium', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '374', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100375', 'T-Shirt Small', 'T-Shirt Small', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '375', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100376', 'T-Shirt Xl', 'T-Shirt Xl', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '376', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100377', 'Tulsi', 'Tulsi', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '377', '1', 'ACTIVE', 'KG', 'Y', 'N', '', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100378', 'Tulsi Adrak Chai Can', 'Tulsi Adrak Chai Can', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '378', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100379', 'Uniform Cap', 'Uniform Cap', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '379', '1', 'ACTIVE', 'PC', 'N', 'N', '', '13');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100380', 'UPS', 'Ups', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '380', '1', 'ACTIVE', 'PC', 'N', 'N', '', '17');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100381', 'Vada Pav Filling', 'Vada Pav Filling', '4', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'DAILY', '381', '1', 'ACTIVE', 'PC', 'Y', 'N', '', '0');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100382', 'Visi Cooler -110 L', 'Visi Cooler -110 L', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '382', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100383', 'Vitamix Rinsomatic', 'Vitamix Rinsomatic', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '383', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100384', 'Washroom Cleaning Checklist', 'Washroom Cleaning Checklist', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '384', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100385', 'Wastage Basket', 'Wastage Basket', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '385', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100386', 'Water Boiler', 'Water Boiler', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '386', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100387', 'Water Bottle', 'Water Bottle', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '387', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100388', 'Water Cooler', 'Water Cooler', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '388', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100389', 'Water Tank 1000 L', 'Water Tank 1000 L', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '389', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100390', 'Weighing Scale', 'Weighing Scale', '3', 'N', '11-06-2016 00:00', '100000', 'N', 'N', 'MONTHLY', '390', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100391', 'Wet Mop Head', 'Wet Mop Head', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '391', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100392', 'Wet Mop Rod', 'Wet Mop Rod', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '392', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100393', 'White Board Duster', 'White Board Duster', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '393', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100394', 'White Board Marker', 'White Board Marker', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '394', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100395', 'White Envelop Medium', 'White Envelop Medium', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '395', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100396', 'White Envelop Small', 'White Envelop Small', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '396', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100397', 'Wiper', 'Wiper', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '397', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100398', 'Wiper Small', 'Wiper Small', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '398', '1', 'ACTIVE', 'PC', 'N', 'N', '', '14');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100399', 'Wooden Cake Board', 'Wooden Cake Board', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '399', '1', 'ACTIVE', 'PC', 'N', 'N', '', '11');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100400', 'Writing Pad', 'Writing Pad', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'MONTHLY', '400', '1', 'ACTIVE', 'PC', 'N', 'N', '', '12');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100401', 'Rooh Afja', 'Rooh Afja', '2', 'Y', '11-06-2016 00:00', '100000', 'Y', 'Y', 'WEEKLY', '401', '1', 'ACTIVE', 'L', 'Y', 'N', ' ', '5');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100402', 'Badam pista cookies pieces', 'Badam pista cookies pieces', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'N', 'DAILY', '402', '', '', 'PC', 'Y', 'N', '', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `CATEGORY_ID`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `STOCK_KEEPING_FREQUENCY`, `PRODUCT_CODE`, `SHELF_LIFE_IN_DAYS`, `PRODUCT_STATUS`, `UNIT_OF_MEASURE`, `PARTICIPATES_IN_RECIPE`, `VARIANT_LEVEL_ORDERING`, `PRODUCT_IMAGE`, `SUB_CATEGORY_ID`) VALUES ('100403', 'Oatmeal cookies pieces', 'Oatmeal cookies pieces', '1', 'Y', '11-06-2016 00:00', '100000', 'Y', 'N', 'DAILY', '403', '', '', 'PC', 'Y', 'N', '', '1');



UPDATE KETTLE_SCM_DEV.PRODUCT_DEFINITION SET SUB_CATEGORY_ID = NULL WHERE SUB_CATEGORY_ID = 0;




INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('1', 'EXTERNAL', '100114', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('2', 'KITCHEN', '100147', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('3', 'EXTERNAL', '100155', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('4', 'EXTERNAL', '100158', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('5', 'EXTERNAL', '100195', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('6', 'EXTERNAL', '100250', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('7', 'KITCHEN', '100193', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('8', 'EXTERNAL', '100263', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('9', 'KITCHEN', '100027', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('10', 'KITCHEN', '100039', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('11', 'KITCHEN', '100071', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('12', 'KITCHEN', '100211', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('13', 'WAREHOUSE', '100018', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('14', 'WAREHOUSE', '100124', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('15', 'WAREHOUSE', '100173', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('16', 'WAREHOUSE', '100222', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('17', 'WAREHOUSE', '100378', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('18', 'WAREHOUSE', '100056', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('19', 'EXTERNAL', '100238', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('20', 'WAREHOUSE', '100004', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('21', 'WAREHOUSE', '100006', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('22', 'WAREHOUSE', '100051', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('23', 'WAREHOUSE', '100273', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('24', 'WAREHOUSE', '100060', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('25', 'WAREHOUSE', '100061', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('26', 'WAREHOUSE', '100063', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('27', 'WAREHOUSE', '100077', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('28', 'WAREHOUSE', '100347', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('29', 'WAREHOUSE', '100348', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('30', 'WAREHOUSE', '100350', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('31', 'WAREHOUSE', '100093', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('32', 'WAREHOUSE', '100098', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('33', 'WAREHOUSE', '100099', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('34', 'WAREHOUSE', '100100', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('35', 'WAREHOUSE', '100116', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('36', 'WAREHOUSE', '100129', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('37', 'WAREHOUSE', '100130', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('38', 'WAREHOUSE', '100132', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('39', 'WAREHOUSE', '100133', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('40', 'WAREHOUSE', '100177', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('41', 'WAREHOUSE', '100187', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('42', 'WAREHOUSE', '100186', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('43', 'WAREHOUSE', '100188', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('44', 'WAREHOUSE', '100206', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('45', 'WAREHOUSE', '100207', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('46', 'WAREHOUSE', '100249', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('47', 'WAREHOUSE', '100271', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('48', 'WAREHOUSE', '100277', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('49', 'WAREHOUSE', '100278', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('50', 'WAREHOUSE', '100315', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('51', 'WAREHOUSE', '100316', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('52', 'WAREHOUSE', '100332', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('53', 'WAREHOUSE', '100333', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('54', 'WAREHOUSE', '100334', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('55', 'WAREHOUSE', '100335', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('56', 'WAREHOUSE', '100351', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('57', 'WAREHOUSE', '100353', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('58', 'WAREHOUSE', '100354', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('59', 'WAREHOUSE', '100366', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('60', 'WAREHOUSE', '100372', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('61', 'WAREHOUSE', '100008', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('62', 'WAREHOUSE', '100017', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('63', 'WAREHOUSE', '100020', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('64', 'WAREHOUSE', '100024', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('65', 'WAREHOUSE', '100030', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('66', 'WAREHOUSE', '100036', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('67', 'WAREHOUSE', '100037', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('68', 'WAREHOUSE', '100045', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('69', 'WAREHOUSE', '100339', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('70', 'WAREHOUSE', '100069', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('71', 'WAREHOUSE', '100070', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('72', 'WAREHOUSE', '100081', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('73', 'WAREHOUSE', '100085', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('74', 'WAREHOUSE', '100086', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('75', 'WAREHOUSE', '100089', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('76', 'WAREHOUSE', '100094', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('77', 'WAREHOUSE', '100096', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('78', 'WAREHOUSE', '100097', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('79', 'WAREHOUSE', '100305', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('80', 'WAREHOUSE', '100163', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('81', 'WAREHOUSE', '100183', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('82', 'WAREHOUSE', '100200', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('83', 'WAREHOUSE', '100201', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('84', 'WAREHOUSE', '100214', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('85', 'WAREHOUSE', '100242', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('86', 'WAREHOUSE', '100245', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('87', 'WAREHOUSE', '100246', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('88', 'WAREHOUSE', '100275', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('89', 'WAREHOUSE', '100401', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('90', 'WAREHOUSE', '100301', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('91', 'WAREHOUSE', '100304', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('92', 'WAREHOUSE', '100306', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('93', 'WAREHOUSE', '100312', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('94', 'WAREHOUSE', '100319', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('95', 'WAREHOUSE', '100337', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('96', 'WAREHOUSE', '100338', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('97', 'WAREHOUSE', '100340', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('98', 'WAREHOUSE', '100363', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('99', 'WAREHOUSE', '100369', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('100', 'WAREHOUSE', '100377', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('101', 'WAREHOUSE', '100189', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('102', 'WAREHOUSE', '100122', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('103', 'WAREHOUSE', '100125', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('104', 'WAREHOUSE', '100143', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('105', 'WAREHOUSE', '100146', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('106', 'WAREHOUSE', '100169', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('107', 'WAREHOUSE', '100174', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('108', 'WAREHOUSE', '100194', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('109', 'WAREHOUSE', '100198', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('110', 'WAREHOUSE', '100212', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('111', 'WAREHOUSE', '100215', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('112', 'WAREHOUSE', '100241', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('113', 'WAREHOUSE', '100251', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('114', 'WAREHOUSE', '100259', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('115', 'KITCHEN', '100019', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('116', 'KITCHEN', '100067', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('117', 'KITCHEN', '100111', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('118', 'KITCHEN', '100115', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('119', 'KITCHEN', '100160', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('120', 'KITCHEN', '100162', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('121', 'KITCHEN', '100171', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('122', 'KITCHEN', '100210', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('123', 'KITCHEN', '100258', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('124', 'KITCHEN', '100280', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('125', 'WAREHOUSE', '100308', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('126', 'WAREHOUSE', '100057', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('127', 'WAREHOUSE', '100059', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('128', 'WAREHOUSE', '100075', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('129', 'WAREHOUSE', '100076', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('130', 'WAREHOUSE', '100088', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('131', 'WAREHOUSE', '100087', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('132', 'WAREHOUSE', '100119', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('133', 'WAREHOUSE', '100101', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('134', 'WAREHOUSE', '100166', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('135', 'WAREHOUSE', '100191', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('136', 'WAREHOUSE', '100204', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('137', 'WAREHOUSE', '100237', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('138', 'WAREHOUSE', '100236', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('139', 'WAREHOUSE', '100103', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('140', 'WAREHOUSE', '100310', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('141', 'WAREHOUSE', '100323', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('142', 'WAREHOUSE', '100324', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('143', 'WAREHOUSE', '100327', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('144', 'WAREHOUSE', '100370', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('145', 'WAREHOUSE', '100012', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('146', 'WAREHOUSE', '100013', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('147', 'WAREHOUSE', '100014', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('148', 'WAREHOUSE', '100015', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('149', 'WAREHOUSE', '100016', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('150', 'WAREHOUSE', '100270', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('151', 'WAREHOUSE', '100022', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('152', 'WAREHOUSE', '100023', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('153', 'WAREHOUSE', '100107', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('154', 'WAREHOUSE', '100031', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('155', 'WAREHOUSE', '100038', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('156', 'WAREHOUSE', '100042', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('157', 'WAREHOUSE', '100044', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('158', 'WAREHOUSE', '100047', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('159', 'WAREHOUSE', '100048', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('160', 'WAREHOUSE', '100062', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('161', 'WAREHOUSE', '100064', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('162', 'WAREHOUSE', '100066', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('163', 'WAREHOUSE', '100079', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('164', 'WAREHOUSE', '100102', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('165', 'WAREHOUSE', '100104', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('166', 'WAREHOUSE', '100105', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('167', 'WAREHOUSE', '100106', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('168', 'WAREHOUSE', '100109', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('169', 'WAREHOUSE', '100113', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('170', 'WAREHOUSE', '100117', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('171', 'WAREHOUSE', '100118', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('172', 'WAREHOUSE', '100128', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('173', 'WAREHOUSE', '100134', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('174', 'WAREHOUSE', '100140', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('175', 'WAREHOUSE', '100149', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('176', 'WAREHOUSE', '100152', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('177', 'WAREHOUSE', '100153', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('178', 'WAREHOUSE', '100154', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('179', 'WAREHOUSE', '100159', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('180', 'WAREHOUSE', '100167', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('181', 'WAREHOUSE', '100179', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('182', 'WAREHOUSE', '100040', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('183', 'WAREHOUSE', '100192', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('184', 'WAREHOUSE', '100216', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('185', 'WAREHOUSE', '100371', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('186', 'WAREHOUSE', '100229', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('187', 'WAREHOUSE', '100230', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('188', 'WAREHOUSE', '100231', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('189', 'WAREHOUSE', '100223', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('190', 'WAREHOUSE', '100227', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('191', 'WAREHOUSE', '100232', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('192', 'WAREHOUSE', '100233', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('193', 'WAREHOUSE', '100234', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('194', 'WAREHOUSE', '100287', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('195', 'WAREHOUSE', '100288', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('196', 'WAREHOUSE', '100346', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('197', 'WAREHOUSE', '100256', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('198', 'WAREHOUSE', '100261', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('199', 'WAREHOUSE', '100262', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('200', 'WAREHOUSE', '100252', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('201', 'WAREHOUSE', '100269', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('202', 'WAREHOUSE', '100108', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('203', 'WAREHOUSE', '100318', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('204', 'WAREHOUSE', '100307', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('205', 'WAREHOUSE', '100317', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('206', 'WAREHOUSE', '100328', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('207', 'WAREHOUSE', '100329', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('208', 'WAREHOUSE', '100226', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('209', 'WAREHOUSE', '100228', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('210', 'WAREHOUSE', '100224', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('211', 'WAREHOUSE', '100225', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('212', 'WAREHOUSE', '100341', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('213', 'WAREHOUSE', '100342', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('214', 'WAREHOUSE', '100352', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('215', 'WAREHOUSE', '100355', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('216', 'WAREHOUSE', '100356', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('217', 'WAREHOUSE', '100357', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('218', 'WAREHOUSE', '100358', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('219', 'WAREHOUSE', '100359', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('220', 'WAREHOUSE', '100360', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('221', 'WAREHOUSE', '100364', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('222', 'WAREHOUSE', '100365', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('223', 'WAREHOUSE', '100385', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('224', 'WAREHOUSE', '100386', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('225', 'WAREHOUSE', '100392', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('226', 'WAREHOUSE', '100399', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('227', 'WAREHOUSE', '100007', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('228', 'WAREHOUSE', '100025', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('229', 'WAREHOUSE', '100029', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('230', 'WAREHOUSE', '100034', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('231', 'WAREHOUSE', '100050', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('232', 'WAREHOUSE', '100052', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('233', 'WAREHOUSE', '100053', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('234', 'WAREHOUSE', '100054', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('235', 'WAREHOUSE', '100055', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('236', 'WAREHOUSE', '100068', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('237', 'WAREHOUSE', '100073', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('238', 'WAREHOUSE', '100074', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('239', 'WAREHOUSE', '100065', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('240', 'WAREHOUSE', '100080', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('241', 'WAREHOUSE', '100091', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('242', 'WAREHOUSE', '100092', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('243', 'WAREHOUSE', '100095', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('244', 'WAREHOUSE', '100112', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('245', 'WAREHOUSE', '100121', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('246', 'WAREHOUSE', '100123', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('247', 'WAREHOUSE', '100135', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('248', 'WAREHOUSE', '100136', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('249', 'WAREHOUSE', '100168', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('250', 'WAREHOUSE', '100289', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('251', 'WAREHOUSE', '100202', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('252', 'WAREHOUSE', '100221', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('253', 'WAREHOUSE', '100148', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('254', 'WAREHOUSE', '100265', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('255', 'WAREHOUSE', '100311', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('256', 'WAREHOUSE', '100267', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('257', 'WAREHOUSE', '100282', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('258', 'WAREHOUSE', '100302', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('259', 'WAREHOUSE', '100303', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('260', 'WAREHOUSE', '100046', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('261', 'WAREHOUSE', '100321', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('262', 'WAREHOUSE', '100330', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('263', 'WAREHOUSE', '100349', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('264', 'WAREHOUSE', '100295', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('265', 'WAREHOUSE', '100384', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('266', 'WAREHOUSE', '100393', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('267', 'WAREHOUSE', '100394', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('268', 'WAREHOUSE', '100395', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('269', 'WAREHOUSE', '100396', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('270', 'WAREHOUSE', '100400', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('271', 'WAREHOUSE', '100379', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('272', 'WAREHOUSE', '100283', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('273', 'WAREHOUSE', '100284', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('274', 'WAREHOUSE', '100285', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('275', 'WAREHOUSE', '100286', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('276', 'WAREHOUSE', '100219', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('277', 'WAREHOUSE', '100373', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('278', 'WAREHOUSE', '100374', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('279', 'WAREHOUSE', '100375', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('280', 'WAREHOUSE', '100376', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('281', 'WAREHOUSE', '100300', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('282', 'WAREHOUSE', '100032', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('283', 'WAREHOUSE', '100033', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('284', 'WAREHOUSE', '100043', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('285', 'WAREHOUSE', '100049', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('286', 'WAREHOUSE', '100090', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('287', 'WAREHOUSE', '100120', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('288', 'WAREHOUSE', '100325', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('289', 'WAREHOUSE', '100126', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('290', 'WAREHOUSE', '100127', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('291', 'WAREHOUSE', '100137', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('292', 'WAREHOUSE', '100138', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('293', 'WAREHOUSE', '100139', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('294', 'WAREHOUSE', '100141', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('295', 'WAREHOUSE', '100142', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('296', 'WAREHOUSE', '100156', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('297', 'WAREHOUSE', '100161', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('298', 'WAREHOUSE', '100164', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('299', 'WAREHOUSE', '100165', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('300', 'WAREHOUSE', '100176', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('301', 'WAREHOUSE', '100178', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('302', 'WAREHOUSE', '100181', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('303', 'WAREHOUSE', '100398', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('304', 'WAREHOUSE', '100217', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('305', 'WAREHOUSE', '100239', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('306', 'WAREHOUSE', '100247', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('307', 'WAREHOUSE', '100255', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('308', 'WAREHOUSE', '100268', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('309', 'WAREHOUSE', '100272', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('310', 'WAREHOUSE', '100131', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('311', 'WAREHOUSE', '100387', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('312', 'WAREHOUSE', '100290', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('313', 'WAREHOUSE', '100291', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('314', 'WAREHOUSE', '100292', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('315', 'WAREHOUSE', '100293', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('316', 'WAREHOUSE', '100309', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('317', 'WAREHOUSE', '100314', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('318', 'WAREHOUSE', '100322', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('319', 'WAREHOUSE', '100326', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('320', 'WAREHOUSE', '100331', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('321', 'WAREHOUSE', '100343', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('322', 'WAREHOUSE', '100344', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('323', 'WAREHOUSE', '100367', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('324', 'WAREHOUSE', '100368', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('325', 'WAREHOUSE', '100391', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('326', 'WAREHOUSE', '100397', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('327', 'WAREHOUSE', '100009', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('328', 'WAREHOUSE', '100218', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('329', 'WAREHOUSE', '100279', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('330', 'WAREHOUSE', '100208', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('331', 'WAREHOUSE', '100281', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('332', 'WAREHOUSE', '100203', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('333', 'WAREHOUSE', '100380', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('334', 'WAREHOUSE', '100345', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('335', 'WAREHOUSE', '100244', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('336', 'WAREHOUSE', '100248', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('337', 'WAREHOUSE', '100264', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('338', 'WAREHOUSE', '100361', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('339', 'WAREHOUSE', '100362', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('340', 'WAREHOUSE', '100001', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('341', 'WAREHOUSE', '100003', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('342', 'WAREHOUSE', '100005', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('343', 'WAREHOUSE', '100021', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('344', 'WAREHOUSE', '100041', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('345', 'WAREHOUSE', '100110', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('346', 'WAREHOUSE', '100175', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('347', 'WAREHOUSE', '100190', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('348', 'WAREHOUSE', '100213', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('349', 'WAREHOUSE', '100235', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('350', 'WAREHOUSE', '100243', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('351', 'WAREHOUSE', '100157', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('352', 'WAREHOUSE', '100382', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('353', 'WAREHOUSE', '100383', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('354', 'WAREHOUSE', '100390', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('355', 'WAREHOUSE', '100072', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('356', 'WAREHOUSE', '100296', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('357', 'WAREHOUSE', '100297', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('358', 'WAREHOUSE', '100145', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('359', 'WAREHOUSE', '100150', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('360', 'WAREHOUSE', '100151', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('361', 'WAREHOUSE', '100252', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('362', 'WAREHOUSE', '100388', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('363', 'WAREHOUSE', '100389', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('364', 'KITCHEN', '100002', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('365', 'KITCHEN', '100010', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('366', 'KITCHEN', '100011', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('367', 'KITCHEN', '100026', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('368', 'KITCHEN', '100028', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('369', 'KITCHEN', '100035', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('370', 'KITCHEN', '100058', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('371', 'KITCHEN', '100078', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('372', 'KITCHEN', '100082', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('373', 'KITCHEN', '100083', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('374', 'KITCHEN', '100084', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('375', 'KITCHEN', '100144', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('376', 'KITCHEN', '100170', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('377', 'KITCHEN', '100172', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('378', 'KITCHEN', '100180', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('379', 'KITCHEN', '100182', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('380', 'KITCHEN', '100184', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('381', 'KITCHEN', '100185', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('382', 'KITCHEN', '100196', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('383', 'KITCHEN', '100199', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('384', 'KITCHEN', '100205', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('385', 'KITCHEN', '100209', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('386', 'KITCHEN', '100220', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('387', 'KITCHEN', '100240', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('388', 'KITCHEN', '100253', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('389', 'KITCHEN', '100254', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('390', 'KITCHEN', '100257', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('391', 'KITCHEN', '100260', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('392', 'KITCHEN', '100266', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('393', 'KITCHEN', '100276', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('394', 'KITCHEN', '100294', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('395', 'KITCHEN', '100313', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('396', 'KITCHEN', '100320', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('397', 'KITCHEN', '100336', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('398', 'KITCHEN', '100381', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`PRODUCT_FULFILLMENT_TYPE_DATA` (`ID`, `FULFILLMENT_TYPE`, `PRODUCT_DEFINITION_ID`, `PRODUCT_FULFILLMENT_TYPE_STATUS`) VALUES ('399', 'WAREHOUSE', '100274', 'ACTIVE');



#attribute values
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('1', '1', 'Regular', '1', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('2', '1', 'Diamond Cut', '2', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('3', '1', 'Julliene', '3', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('4', '1', 'Slices', '4', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('5', '1', 'Chopped', '5', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('6', '1', '1 Inch', '6', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('7', '1', 'Peeled', '7', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('8', '1', 'Paste', '8', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('9', '1', 'Boiled', '9', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('10', '3', 'Khanna', '10', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('11', '3', 'Rasella', '11', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('12', '3', 'Daikin', '12', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('13', '3', 'Chaayos', '13', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('14', '3', 'Snail', '14', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('15', '3', 'Canford', '15', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('16', '3', 'Sunpat', '16', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('17', '3', 'Weikfiled', '17', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('18', '3', 'Natraj', '18', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('19', '3', 'Haldiram', '19', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('20', '3', 'Pradeep', '20', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('21', '3', 'Bolt', '21', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('22', '3', 'Cadburry', '22', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('23', '3', 'Shipra', '23', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('24', '3', 'Glare', '24', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('25', '3', 'Amul', '25', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('26', '3', 'Fable', '26', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('27', '3', 'Sujata', '27', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('28', '3', 'Chrome', '28', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('29', '3', 'Royal', '29', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('30', '3', 'Catch', '30', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('31', '3', 'Nutech', '31', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('32', '3', 'Omex', '32', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('33', '3', 'Mdh', '33', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('34', '3', 'Harshey', '34', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('35', '3', 'Hdpl', '35', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('36', '3', 'Aroma', '36', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('37', '3', 'Bayers', '37', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('38', '3', 'Nestlle', '38', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('39', '3', 'Smart', '39', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('40', '3', 'Akasa', '40', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('41', '3', 'Tample', '41', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('42', '3', 'Plus', '42', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('43', '3', 'Taski', '43', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('44', '3', 'Tata', '44', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('45', '3', 'Hp, Bharat', '45', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('46', '3', 'Grassland', '46', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('47', '3', 'Godrej', '47', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('48', '3', 'Cremica', '48', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('49', '3', 'Fire Killer', '49', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('50', '3', 'Philips', '50', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('51', '3', 'LG', '51', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('52', '3', 'Invanta', '52', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('53', '3', 'Artline', '53', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('54', '3', 'Dabur', '54', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('55', '3', 'Elan Pro', '55', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('56', '3', 'Glen', '56', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('57', '3', 'Chaniese', '57', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('58', '3', 'Mogra', '58', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('59', '3', 'Delmonta', '59', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('60', '3', 'Acer', '60', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('61', '3', 'Lenova', '61', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('62', '3', 'Monin', '62', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('63', '3', 'Sony', '63', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('64', '3', 'Sanddisk', '64', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('65', '3', 'Aristo', '65', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('66', '3', 'Rajdhani', '66', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('67', '3', 'Ecoware', '67', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('68', '3', 'Grv', '68', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('69', '3', 'Epson', '69', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('70', '3', 'Kangaro', '70', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('71', '3', 'Trison', '71', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('72', '3', 'Kent', '72', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('73', '3', 'Scotch Bright', '73', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('74', '3', 'Kenford', '74', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('75', '3', 'Jain', '75', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('76', '3', 'Hindustan', '76', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('77', '3', 'Kingfisher', '77', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('78', '3', 'Falcon', '78', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('79', '3', 'Solo', '79', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('80', '3', 'Conta', '80', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('81', '3', 'Supreme', '81', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('82', '3', 'Samsung', '82', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('83', '3', 'Vinod', '83', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('84', '3', 'Ppl', '84', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('85', '3', 'Local', '85', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('86', '3', 'Sonata', '86', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('87', '3', 'Osram', '87', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('88', '3', 'Guruji', '88', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('89', '3', 'Sunmex', '89', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('90', '3', 'Intex', '90', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('91', '3', 'Apc', '91', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('92', '3', 'Elen Pro', '92', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('93', '3', 'Vitamix', '93', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('94', '3', 'Poly Pro', '94', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('95', '3', 'Blue Star', '95', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('96', '3', 'Sintex', '96', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('97', '3', 'Cas', '97', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('98', '3', 'Flacon', '98', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('99', '2', 'Veg', '99', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('100', '2', 'Non Veg', '100', 'ACTIVE');
UPDATE KETTLE_SCM_DEV.ATTRIBUTE_VALUE SET `ATTRIBUTE_VALUE`='Hp' WHERE `ATTRIBUTE_VALUE_ID`='45';
INSERT INTO KETTLE_SCM_DEV.ATTRIBUTE_VALUE (`ATTRIBUTE_ID`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_VALUE_SHORT_CODE`, `ATTRIBUTE_VALUE_STATUS`) VALUES ('3', 'Bharat', '101', 'ACTIVE');


INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('1', '5', '10', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('2', '2', '11', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('3', '9', '12', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('4', '1', '13', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('5', '5', '14', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('6', '5', '15', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('7', '5', '16', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('8', '2', '17', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('9', '5', '18', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('10', '2', '19', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('11', '5', '20', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('12', '9', '21', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('13', '5', '21', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('14', '2', '22', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('15', '5', '23', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('16', '5', '24', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('17', '2', '25', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('18', '5', '26', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('19', '5', '27', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('20', '5', '28', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('21', '5', '29', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('22', '2', '30', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('23', '2', '31', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('24', '5', '32', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('25', '2', '33', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('26', '2', '34', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('27', '5', '35', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('28', '5', '36', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('29', '2', '37', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('30', '2', '38', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('31', '5', '39', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('32', '9', '40', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('33', '5', '41', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('34', '5', '42', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('35', '5', '43', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('36', '2', '13', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('37', '2', '44', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('38', '5', '45', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('39', '5', '46', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('40', '9', '47', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('41', '2', '48', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('42', '9', '49', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('43', '5', '50', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('44', '9', '51', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('45', '5', '52', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('46', '5', '53', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('47', '2', '54', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('48', '9', '55', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('49', '5', '56', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('50', '2', '57', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('51', '1', '57', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('52', '2', '58', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('53', '2', '59', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('54', '9', '60', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('55', '9', '27', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('56', '2', '62', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('57', '5', '63', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('58', '9', '64', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('59', '5', '65', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('60', '2', '66', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('61', '2', '67', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('62', '2', '68', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('63', '9', '69', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('64', '5', '62', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('65', '5', '70', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('66', '5', '71', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('67', '9', '72', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('68', '5', '102', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('69', '5', '73', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('70', '5', '74', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('71', '2', '75', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('72', '2', '76', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('73', '2', '77', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('74', '5', '78', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('75', '5', '79', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('76', '5', '80', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('77', '5', '81', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('78', '9', '82', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('79', '5', '83', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('80', '2', '84', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('81', '5', '85', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('82', '2', '88', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('83', '5', '89', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('84', '9', '90', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('85', '9', '92', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('86', '9', '93', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('87', '5', '94', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('88', '9', '95', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('89', '9', '96', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('90', '9', '97', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('91', '5', '98', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('92', '5', '87', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('93', '9', '61', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('94', '5', '86', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('95', '9', '91', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_VALUE (`CATEGORY_ATTRIBUTE_VALUE_ID`, `CATEGORY_ATTRIBUTE_MAPPING_ID`, `ATTRIBUTE_VALUE_ID`, `MAPPING_STATUS`) VALUES ('96', '5', '101', 'ACTIVE');






INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('1', '3', '100001', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('2', '2', '100002', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('3', '3', '100003', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('4', '3', '100004', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('5', '3', '100005', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('6', '3', '100006', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('7', '3', '100007', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('8', '3', '100008', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('9', '3', '100009', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('10', '2', '100010', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('11', '3', '100011', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('12', '3', '100012', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('13', '3', '100013', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('14', '3', '100014', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('15', '3', '100015', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('16', '3', '100016', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('17', '2', '100017', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('18', '3', '100018', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('19', '2', '100019', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('20', '2', '100020', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('21', '3', '100021', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('22', '3', '100022', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('23', '3', '100023', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('24', '2', '100024', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('25', '3', '100025', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('26', '2', '100026', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('27', '3', '100027', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('28', '2', '100028', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('29', '3', '100029', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('30', '2', '100030', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('31', '2', '100031', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('32', '2', '100032', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('33', '2', '100033', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('34', '3', '100034', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('35', '2', '100035', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('36', '2', '100036', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('37', '2', '100037', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('38', '3', '100038', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('39', '3', '100039', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('40', '3', '100040', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('41', '3', '100041', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('42', '3', '100042', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('43', '3', '100043', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('44', '3', '100044', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('45', '2', '100045', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('46', '3', '100046', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('47', '3', '100047', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('48', '3', '100048', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('49', '3', '100049', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('50', '3', '100050', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('51', '3', '100051', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('52', '3', '100052', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('53', '3', '100053', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('54', '3', '100054', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('55', '3', '100055', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('56', '2', '100056', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('57', '3', '100057', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('58', '2', '100058', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('59', '3', '100059', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('60', '3', '100060', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('61', '3', '100061', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('62', '3', '100062', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('63', '3', '100063', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('64', '3', '100064', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('65', '3', '100065', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('66', '3', '100066', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('67', '2', '100067', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('68', '3', '100068', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('69', '3', '100069', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('70', '3', '100070', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('71', '3', '100071', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('72', '3', '100072', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('73', '3', '100073', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('74', '3', '100074', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('75', '3', '100075', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('76', '3', '100076', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('77', '3', '100077', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('78', '2', '100078', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('79', '3', '100079', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('80', '3', '100080', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('81', '2', '100081', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('82', '2', '100082', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('83', '2', '100083', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('84', '2', '100084', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('85', '2', '100085', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('86', '2', '100086', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('87', '3', '100087', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('88', '3', '100088', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('89', '2', '100089', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('90', '1', '100090', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('91', '3', '100091', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('92', '3', '100092', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('93', '3', '100093', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('94', '2', '100094', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('95', '3', '100095', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('96', '1', '100096', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('97', '2', '100097', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('98', '3', '100098', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('99', '3', '100099', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('100', '3', '100100', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('101', '3', '100101', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('102', '3', '100102', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('103', '3', '100103', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('104', '3', '100104', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('105', '3', '100105', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('106', '3', '100106', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('107', '3', '100107', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('108', '3', '100108', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('109', '3', '100109', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('110', '3', '100110', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('111', '2', '100111', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('112', '3', '100112', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('113', '3', '100113', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('114', '3', '100114', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('115', '3', '100115', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('116', '3', '100116', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('117', '3', '100117', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('118', '3', '100118', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('119', '3', '100119', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('120', '3', '100120', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('121', '3', '100121', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('122', '2', '100122', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('123', '3', '100123', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('124', '3', '100124', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('125', '2', '100125', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('126', '3', '100126', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('127', '3', '100127', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('128', '3', '100128', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('129', '3', '100129', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('130', '3', '100130', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('131', '3', '100131', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('132', '3', '100132', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('133', '3', '100133', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('134', '3', '100134', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('135', '3', '100135', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('136', '3', '100136', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('137', '3', '100137', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('138', '3', '100138', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('139', '3', '100139', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('140', '3', '100140', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('141', '3', '100141', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('142', '3', '100142', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('143', '2', '100143', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('144', '2', '100144', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('145', '3', '100145', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('146', '2', '100146', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('147', '2', '100147', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('148', '3', '100148', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('149', '3', '100149', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('150', '3', '100150', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('151', '3', '100151', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('152', '3', '100152', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('153', '3', '100153', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('154', '3', '100154', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('155', '3', '100155', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('156', '3', '100156', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('157', '3', '100157', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('158', '3', '100158', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('159', '3', '100159', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('160', '2', '100160', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('161', '3', '100161', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('162', '2', '100162', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('163', '2', '100163', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('164', '3', '100164', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('165', '3', '100165', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('166', '3', '100166', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('167', '3', '100167', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('168', '3', '100168', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('169', '2', '100169', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('170', '2', '100170', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('171', '2', '100171', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('172', '2', '100172', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('173', '3', '100173', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('174', '2', '100174', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('175', '3', '100175', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('176', '3', '100176', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('177', '3', '100177', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('178', '3', '100178', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('179', '3', '100179', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('180', '2', '100180', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('181', '3', '100181', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('182', '2', '100182', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('183', '2', '100183', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('184', '2', '100184', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('185', '2', '100185', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('186', '3', '100186', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('187', '3', '100187', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('188', '3', '100188', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('189', '3', '100189', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('190', '3', '100190', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('191', '3', '100191', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('192', '3', '100192', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('193', '3', '100193', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('194', '2', '100194', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('195', '3', '100195', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('196', '2', '100196', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('197', '2', '100198', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('198', '2', '100199', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('199', '3', '100200', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('200', '3', '100201', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('201', '3', '100202', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('202', '3', '100203', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('203', '3', '100204', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('204', '2', '100205', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('205', '3', '100206', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('206', '3', '100207', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('207', '3', '100208', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('208', '2', '100209', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('209', '2', '100210', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('210', '3', '100211', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('211', '2', '100212', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('212', '3', '100213', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('213', '2', '100214', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('214', '2', '100215', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('215', '3', '100216', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('216', '3', '100217', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('217', '3', '100218', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('218', '3', '100219', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('219', '2', '100220', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('220', '3', '100221', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('221', '3', '100222', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('222', '3', '100223', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('223', '3', '100224', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('224', '3', '100225', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('225', '3', '100226', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('226', '3', '100227', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('227', '3', '100228', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('228', '3', '100229', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('229', '3', '100230', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('230', '3', '100231', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('231', '3', '100232', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('232', '3', '100233', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('233', '3', '100234', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('234', '3', '100235', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('235', '3', '100236', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('236', '3', '100237', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('237', '3', '100238', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('238', '3', '100239', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('239', '2', '100240', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('240', '2', '100241', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('241', '2', '100242', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('242', '3', '100243', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('243', '3', '100244', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('244', '3', '100245', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('245', '3', '100246', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('246', '3', '100247', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('247', '3', '100248', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('248', '3', '100249', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('249', '3', '100250', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('250', '2', '100251', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('251', '3', '100252', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('252', '2', '100253', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('253', '2', '100254', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('254', '3', '100255', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('255', '3', '100256', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('256', '2', '100257', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('257', '2', '100258', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('258', '2', '100259', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('259', '3', '100260', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('260', '3', '100261', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('261', '3', '100262', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('262', '3', '100263', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('263', '3', '100264', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('264', '3', '100265', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('265', '2', '100266', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('266', '3', '100267', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('267', '3', '100268', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('268', '3', '100269', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('269', '3', '100270', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('270', '3', '100271', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('271', '3', '100272', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('272', '3', '100273', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('273', '3', '100274', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('274', '2', '100275', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('275', '2', '100276', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('276', '3', '100277', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('277', '2', '100278', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('278', '3', '100279', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('279', '2', '100280', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('280', '3', '100281', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('281', '3', '100282', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('282', '3', '100283', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('283', '3', '100284', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('284', '3', '100285', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('285', '3', '100286', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('286', '3', '100287', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('287', '3', '100288', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('288', '3', '100289', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('289', '1', '100290', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('290', '1', '100291', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('291', '1', '100292', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('292', '1', '100293', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('293', '2', '100294', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('294', '3', '100295', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('295', '3', '100296', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('296', '3', '100297', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('297', '1', '100401', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('298', '3', '100300', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('299', '1', '100301', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('300', '3', '100302', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('301', '3', '100303', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('302', '3', '100304', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('303', '3', '100305', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('304', '2', '100306', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('305', '3', '100307', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('306', '3', '100308', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('307', '3', '100309', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('308', '3', '100310', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('309', '3', '100311', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('310', '2', '100312', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('311', '2', '100313', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('312', '3', '100314', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('313', '3', '100315', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('314', '3', '100316', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('315', '3', '100317', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('316', '3', '100318', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('317', '1', '100319', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('318', '2', '100320', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('319', '3', '100321', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('320', '3', '100322', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('321', '3', '100323', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('322', '3', '100324', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('323', '3', '100325', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('324', '3', '100326', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('325', '3', '100327', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('326', '3', '100328', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('327', '3', '100329', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('328', '3', '100330', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('329', '3', '100331', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('330', '3', '100332', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('331', '3', '100333', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('332', '3', '100334', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('333', '3', '100335', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('334', '2', '100336', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('335', '2', '100337', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('336', '3', '100338', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('337', '3', '100339', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('338', '3', '100340', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('339', '3', '100341', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('340', '3', '100342', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('341', '3', '100343', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('342', '3', '100344', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('343', '3', '100345', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('344', '3', '100346', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('345', '3', '100347', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('346', '3', '100348', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('347', '3', '100349', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('348', '3', '100350', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('349', '3', '100351', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('350', '3', '100352', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('351', '3', '100353', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('352', '3', '100354', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('353', '3', '100355', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('354', '3', '100356', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('355', '3', '100357', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('356', '3', '100358', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('357', '3', '100359', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('358', '3', '100360', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('359', '3', '100361', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('360', '3', '100362', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('361', '1', '100363', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('362', '3', '100364', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('363', '3', '100365', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('364', '3', '100366', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('365', '3', '100367', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('366', '3', '100368', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('367', '2', '100369', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('368', '3', '100370', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('369', '3', '100371', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('370', '3', '100372', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('371', '3', '100373', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('372', '3', '100374', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('373', '3', '100375', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('374', '3', '100376', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('375', '2', '100377', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('376', '3', '100378', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('377', '3', '100379', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('378', '3', '100380', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('379', '2', '100381', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('380', '3', '100382', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('381', '3', '100383', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('382', '3', '100384', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('383', '3', '100385', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('384', '3', '100386', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('385', '3', '100387', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('386', '3', '100388', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('387', '3', '100389', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('388', '3', '100390', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('389', '3', '100391', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('390', '3', '100392', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('391', '3', '100393', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('392', '3', '100394', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('393', '3', '100395', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('394', '3', '100396', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('395', '3', '100397', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('396', '3', '100398', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('397', '3', '100399', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('398', '3', '100400', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('399', '93', '100002', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('400', '4', '100004', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('401', '4', '100006', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('402', '61', '100017', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('403', '61', '100020', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('404', '62', '100025', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('405', '94', '100026', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('406', '4', '100029', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('407', '61', '100030', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('408', '63', '100031', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('409', '61', '100037', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('410', '61', '100045', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('411', '44', '100058', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('412', '26', '100060', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('413', '26', '100061', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('414', '4', '100063', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('415', '11', '100066', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('416', '95', '100082', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('417', '96', '100084', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('418', '52', '100085', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('419', '61', '100089', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('420', '61', '100094', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('421', '64', '100098', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('422', '65', '100099', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('423', '64', '100100', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('424', '61', '100111', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('425', '5', '100129', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('426', '22', '100130', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('427', '24', '100132', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('428', '8', '100133', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('429', '97', '100144', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('430', '94', '100170', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('431', '100', '100172', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('432', '22', '100177', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('433', '67', '100178', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('434', '98', '100182', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('435', '61', '100184', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('436', '31', '100186', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('437', '28', '100187', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('438', '5', '100188', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('439', '44', '100196', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('440', '95', '100199', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('441', '8', '100201', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('442', '68', '100249', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('443', '99', '100253', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('444', '94', '100254', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('445', '94', '100266', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('446', '34', '100271', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('447', '69', '100273', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('448', '70', '100274', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('449', '28', '100277', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('450', '101', '100294', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('451', '55', '100304', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('452', '61', '100306', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('453', '44', '100313', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('454', '94', '100320', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('455', '72', '100322', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('456', '73', '100332', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('457', '73', '100333', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('458', '39', '100334', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('459', '4', '100335', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('460', '5', '100338', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('461', '48', '100339', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('462', '48', '100340', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('463', '67', '100343', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('464', '67', '100344', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('465', '11', '100350', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('466', '42', '100351', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('467', '9', '100372', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('468', '66', '100379', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('469', '95', '100381', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('470', '11', '100395', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('471', '11', '100396', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('472', '5', '100004', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('473', '5', '100006', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('474', '86', '100030', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('475', '92', '100031', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('476', '60', '100042', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('477', '88', '100045', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('478', '102', '100046', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('479', '60', '100048', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('480', '89', '100050', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('481', '90', '100060', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('482', '90', '100061', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('483', '5', '100063', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('484', '28', '100066', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('485', '91', '100085', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('486', '89', '100091', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('487', '89', '100092', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('488', '74', '100093', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('489', '74', '100095', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('490', '75', '100102', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('491', '76', '100103', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('492', '74', '100105', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('493', '77', '100106', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('494', '76', '100109', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('495', '78', '100119', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('496', '79', '100120', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('497', '74', '100128', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('498', '74', '100141', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('499', '80', '100142', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('500', '81', '100153', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('501', '74', '100166', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('502', '76', '100168', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('503', '80', '100192', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('504', '82', '100200', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('505', '83', '100202', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('506', '80', '100204', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('507', '75', '100206', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('508', '84', '100207', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('509', '85', '100216', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('510', '89', '100232', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('511', '89', '100233', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('512', '89', '100236', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('513', '89', '100237', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('514', '102', '100245', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('515', '102', '100246', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('516', '80', '100255', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('517', '82', '100267', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('518', '74', '100272', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('519', '80', '100295', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('520', '82', '100307', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('521', '82', '100315', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('522', '85', '100316', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('523', '74', '100317', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('524', '82', '100321', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('525', '74', '100323', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('526', '74', '100324', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('527', '40', '100353', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('528', '40', '100354', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('529', '74', '100357', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('530', '74', '100358', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('531', '74', '100359', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('532', '77', '100366', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('533', '75', '100368', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('534', '82', '100370', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('535', '89', '100387', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('536', '102', '100390', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('537', '82', '100394', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING (`PRODUCT_PACKAGING_MAPPING_ID`, `PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('538', '82', '100400', 'ACTIVE');




INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('1', '4', '2', 'Y', '1', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('2', '1', '3', 'N', '2', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('3', '1', '1', 'N', '3', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('4', '1', '2', 'N', '1', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('5', '2', '1', 'N', '2', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('6', '2', '3', 'N', '1', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('7', '2', '4', 'N', '3', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('8', '2', '7', 'N', '4', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('9', '3', '1', 'N', '2', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('10', '3', '4', 'N', '3', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('11', '3', '3', 'N', '1', 'Y', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`CATEGORY_ATTRIBUTE_MAPPING_ID`, `CATEGORY_ID`, `ATTRIBUTE_ID`, `IS_MANDATORY`, `MAPPING_ORDER`, `IS_USED_IN_NAMING`, `MAPPING_STATUS`) VALUES ('12', '3', '7', 'N', '4', 'Y', 'ACTIVE');


INSERT INTO KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING (`PACKAGING_ID`, `PRODUCT_ID`, `MAPPING_STATUS`) VALUES ('102', '100238', 'ACTIVE');
UPDATE KETTLE_SCM_DEV.CATEGORY_ATTRIBUTE_MAPPING SET `PACKAGING_ID`='2' WHERE `PRODUCT_PACKAGING_MAPPING_ID`='237';



INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Tomato Regular', 'Tomato Regular', 'Y', '2016-06-25 00:00:00', '100000', 'N', 'N', '100369', '4', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
UPDATE `kettle_scm_dev`.`sku_definition` SET `SKU_STATUS`='ACTIVE' WHERE `SKU_ID`='2';
UPDATE `kettle_scm_dev`.`sku_definition` SET `SKU_STATUS`='ACTIVE' WHERE `SKU_ID`='1';
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Cucumber Slices', 'Cucumber Slices', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'N', '100115', '4', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Chat Masala MDH', 'Chat Masala MDH', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100081', '365', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Vada Pav Filling Chaayos', 'Vada Pav Filling', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'N', '100381', '4', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Butter Amul', 'Butter Amul', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100056', '365', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Pav', 'Pav', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100263', '2', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Foccacia', 'Foccacia', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100155', '2', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Multigrain', 'Multigrain', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100250', '2', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Hari Chuttney', 'Hari Chuttney', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'N', '100180', '1', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Green Mint Mayo Sauce', 'Green Mint Mayo Sauce', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'N', '100172', '4', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Honey Garlic Mayo Sauce', 'Honey Garlic Mayo Sauce', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'N', '100184', '4', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Red Chilli Mayo Sauce', 'Red Chilli Mayo Sauce', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'N', '100294', '4', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Take Away Bag - Small', 'Take Away Bag - Small', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100348', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Dip Cup', 'Dip Cup', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100130', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Dip Container With Lid', 'Dip Container With Lid', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100129', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Tissue Paper', 'Tissue Paper', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100366', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Hot Cup 250 ML', 'Hot Cup 250 ML', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100187', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Hot Cup 360 ML', 'Hot Cup 360 ML', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100188', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Desi Chai Patti', 'Desi Chai Patti', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100125', '-1', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Sachet - Desi Regular', 'Sachet - Desi Regular', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100305', '-1', 'ACTIVE', 'KG', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Cup Holder', 'Cup Holder', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100116', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Rooh Afza', 'Rooh Afza', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100401', '30', 'ACTIVE', 'L', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Chai Delivery Box 400 ML', 'Chai Delivery Box 400 ML', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100350', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Chai Delivery Pouch 400 ML', 'Chai Delivery Pouch 400 ML', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100354', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Chai Delivery Pouch 1 LChai Delivery Pouch 1 LChai Delivery Pouch 1 L', 'Chai Delivery Pouch 1 L', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100353', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Chi Delivery Box 1 L', 'Chi Delivery Box 1 L', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100077', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Take Away Bag Large', 'Take Away Bag Large', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100347', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
INSERT INTO `kettle_scm_dev`.`sku_definition` (`SKU_NAME`, `SKU_DESCRIPTION`, `SUPPORTS_LOOSE_ORDERING`, `CREATION_DATE`, `CREATED_BY`, `HAS_INNER`, `HAS_CASE`, `LINKED_PRODUCT_ID`, `SHELF_LIFE_IN_DAYS`, `SKU_STATUS`, `UNIT_OF_MEASURE`, `PRICE_LAST_UPDATED`) VALUES ('Stirrer', 'Stirrer', 'Y', '2016-06-25 00:00:00', '100000', 'Y', 'Y', '100334', '-1', 'ACTIVE', 'PC', '2016-06-25 17:45:37');
