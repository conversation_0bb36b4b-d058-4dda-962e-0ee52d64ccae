-- MySQL dump 10.13  Distrib 5.5.22, for Win32 (x86)
--
-- Host: dev.kettle.chaayos.com    Database: KETTLE_SCM_DEV
-- ------------------------------------------------------
-- Server version	5.5.46

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ATTRIBUTE_DEFINITION`
--

DROP TABLE IF EXISTS `ATTRIBUTE_DEFINITION`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ATTRIBUTE_DEFINITION` (
  `ATTRIBUTE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `ATTRIBUTE_NAME` varchar(255) NOT NULL,
  `ATTRIBUTE_TYPE` varchar(50) NOT NULL,
  `ATTRIBUTE_CODE` varchar(50) NOT NULL,
  `ATTRIBUTE_SHORT_CODE` varchar(10) DEFAULT NULL,
  `ATTRIBUTE_DESCRIPTION` varchar(1000) DEFAULT NULL,
  `ATTRIBUTE_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`ATTRIBUTE_ID`),
  UNIQUE KEY `ATTRIBUTE_NAME` (`ATTRIBUTE_NAME`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ATTRIBUTE_DEFINITION`
--

LOCK TABLES `ATTRIBUTE_DEFINITION` WRITE;
/*!40000 ALTER TABLE `ATTRIBUTE_DEFINITION` DISABLE KEYS */;
INSERT INTO `ATTRIBUTE_DEFINITION` VALUES (1,'Type','CATEGORY','TYPE','TPE','Type of product/SKU','ACTIVE'),(2,'Veg / Non Veg','CATEGORY','VEG_NON_VEG','VNV','Whether its a Veg Product or a Non-Veg product','ACTIVE'),(3,'Brand','CATEGORY','BRAND','BND','Brand','ACTIVE'),(4,'Capacity','CATEGORY','CAPACITY','CPT','Capacity','ACTIVE'),(5,'Texture','CATEGORY','TEXTURE','TXR','Texture','ACTIVE'),(6,'Quality','CATEGORY','QUALITY','QLT','Quality','ACTIVE'),(7,'Color','CATEGORY','COLOR','CLR','Color','ACTIVE'),(8,'Case','DIMENSION','CASE','CSE','Case Probable Values','ACTIVE'),(9,'Inner','DIMENSION','INNER','INR','Inner Probable Values','ACTIVE'),(10,'Loose','DIMENSION','LOOSE','LOS','Loose Probable Values','ACTIVE');
/*!40000 ALTER TABLE `ATTRIBUTE_DEFINITION` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ATTRIBUTE_VALUE`
--

DROP TABLE IF EXISTS `ATTRIBUTE_VALUE`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ATTRIBUTE_VALUE` (
  `ATTRIBUTE_VALUE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `ATTRIBUTE_ID` int(11) NOT NULL,
  `ATTRIBUTE_VALUE` varchar(255) NOT NULL,
  `ATTRIBUTE_VALUE_SHORT_CODE` varchar(10) DEFAULT NULL,
  `ATTRIBUTE_VALUE_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`ATTRIBUTE_VALUE_ID`),
  UNIQUE KEY `ATTRIBUTE_ID` (`ATTRIBUTE_ID`,`ATTRIBUTE_VALUE`),
  CONSTRAINT `ATTRIBUTE_VALUE_ibfk_1` FOREIGN KEY (`ATTRIBUTE_ID`) REFERENCES `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_ID`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ATTRIBUTE_VALUE`
--

LOCK TABLES `ATTRIBUTE_VALUE` WRITE;
/*!40000 ALTER TABLE `ATTRIBUTE_VALUE` DISABLE KEYS */;
INSERT INTO `ATTRIBUTE_VALUE` VALUES (1,1,'Regular','1','ACTIVE'),(2,1,'Diamond Cut','2','ACTIVE'),(3,1,'Julliene','3','ACTIVE'),(4,1,'Slices','4','ACTIVE'),(5,1,'Chopped','5','ACTIVE'),(6,1,'1 Inch','6','ACTIVE'),(7,1,'Peeled','7','ACTIVE'),(8,1,'Paste','8','ACTIVE'),(9,1,'Boiled','9','ACTIVE'),(10,3,'Khanna','10','ACTIVE'),(11,3,'Rasella','11','ACTIVE'),(12,3,'Daikin','12','ACTIVE'),(13,3,'Chaayos','13','ACTIVE'),(14,3,'Snail','14','ACTIVE'),(15,3,'Canford','15','ACTIVE'),(16,3,'Sunpat','16','ACTIVE'),(17,3,'Weikfiled','17','ACTIVE'),(18,3,'Natraj','18','ACTIVE'),(19,3,'Haldiram','19','ACTIVE'),(20,3,'Pradeep','20','ACTIVE'),(21,3,'Bolt','21','ACTIVE'),(22,3,'Cadburry','22','ACTIVE'),(23,3,'Shipra','23','ACTIVE'),(24,3,'Glare','24','ACTIVE'),(25,3,'Amul','25','ACTIVE'),(26,3,'Fable','26','ACTIVE'),(27,3,'Sujata','27','ACTIVE'),(28,3,'Chrome','28','ACTIVE'),(29,3,'Royal','29','ACTIVE'),(30,3,'Catch','30','ACTIVE'),(31,3,'Nutech','31','ACTIVE'),(32,3,'Omex','32','ACTIVE'),(33,3,'Mdh','33','ACTIVE'),(34,3,'Harshey','34','ACTIVE'),(35,3,'Hdpl','35','ACTIVE'),(36,3,'Aroma','36','ACTIVE'),(37,3,'Bayers','37','ACTIVE'),(38,3,'Nestlle','38','ACTIVE'),(39,3,'Smart','39','ACTIVE'),(40,3,'Akasa','40','ACTIVE'),(41,3,'Tample','41','ACTIVE'),(42,3,'Plus','42','ACTIVE'),(43,3,'Taski','43','ACTIVE'),(44,3,'Tata','44','ACTIVE'),(45,3,'Hp','45','ACTIVE'),(46,3,'Grassland','46','ACTIVE'),(47,3,'Godrej','47','ACTIVE'),(48,3,'Cremica','48','ACTIVE'),(49,3,'Fire Killer','49','ACTIVE'),(50,3,'Philips','50','ACTIVE'),(51,3,'LG','51','ACTIVE'),(52,3,'Invanta','52','ACTIVE'),(53,3,'Artline','53','ACTIVE'),(54,3,'Dabur','54','ACTIVE'),(55,3,'Elan Pro','55','ACTIVE'),(56,3,'Glen','56','ACTIVE'),(57,3,'Chaniese','57','ACTIVE'),(58,3,'Mogra','58','ACTIVE'),(59,3,'Delmonta','59','ACTIVE'),(60,3,'Acer','60','ACTIVE'),(61,3,'Lenova','61','ACTIVE'),(62,3,'Monin','62','ACTIVE'),(63,3,'Sony','63','ACTIVE'),(64,3,'Sanddisk','64','ACTIVE'),(65,3,'Aristo','65','ACTIVE'),(66,3,'Rajdhani','66','ACTIVE'),(67,3,'Ecoware','67','ACTIVE'),(68,3,'Grv','68','ACTIVE'),(69,3,'Epson','69','ACTIVE'),(70,3,'Kangaro','70','ACTIVE'),(71,3,'Trison','71','ACTIVE'),(72,3,'Kent','72','ACTIVE'),(73,3,'Scotch Bright','73','ACTIVE'),(74,3,'Kenford','74','ACTIVE'),(75,3,'Jain','75','ACTIVE'),(76,3,'Hindustan','76','ACTIVE'),(77,3,'Kingfisher','77','ACTIVE'),(78,3,'Falcon','78','ACTIVE'),(79,3,'Solo','79','ACTIVE'),(80,3,'Conta','80','ACTIVE'),(81,3,'Supreme','81','ACTIVE'),(82,3,'Samsung','82','ACTIVE'),(83,3,'Vinod','83','ACTIVE'),(84,3,'Ppl','84','ACTIVE'),(85,3,'Local','85','ACTIVE'),(86,3,'Sonata','86','ACTIVE'),(87,3,'Osram','87','ACTIVE'),(88,3,'Guruji','88','ACTIVE'),(89,3,'Sunmex','89','ACTIVE'),(90,3,'Intex','90','ACTIVE'),(91,3,'Apc','91','ACTIVE'),(92,3,'Elen Pro','92','ACTIVE'),(93,3,'Vitamix','93','ACTIVE'),(94,3,'Poly Pro','94','ACTIVE'),(95,3,'Blue Star','95','ACTIVE'),(96,3,'Sintex','96','ACTIVE'),(97,3,'Cas','97','ACTIVE'),(98,3,'Flacon','98','ACTIVE'),(99,2,'Veg','99','ACTIVE'),(100,2,'Non Veg','100','ACTIVE'),(101,3,'Bharat','101','ACTIVE'),(102,3,'Hamdard','102','ACTIVE');
/*!40000 ALTER TABLE `ATTRIBUTE_VALUE` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CATEGORY_ATTRIBUTE_MAPPING`
--

DROP TABLE IF EXISTS `CATEGORY_ATTRIBUTE_MAPPING`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CATEGORY_ATTRIBUTE_MAPPING` (
  `CATEGORY_ATTRIBUTE_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `CATEGORY_ID` int(11) NOT NULL,
  `ATTRIBUTE_ID` int(11) NOT NULL,
  `IS_MANDATORY` varchar(1) NOT NULL,
  `MAPPING_ORDER` int(11) NOT NULL,
  `IS_USED_IN_NAMING` varchar(1) NOT NULL,
  `MAPPING_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`CATEGORY_ATTRIBUTE_MAPPING_ID`),
  UNIQUE KEY `CATEGORY_ID` (`CATEGORY_ID`,`ATTRIBUTE_ID`),
  KEY `CATEGORY_ATTRIBUTE_MAPPING_ibfk_2` (`ATTRIBUTE_ID`),
  CONSTRAINT `CATEGORY_ATTRIBUTE_MAPPING_ibfk_2` FOREIGN KEY (`ATTRIBUTE_ID`) REFERENCES `ATTRIBUTE_DEFINITION` (`ATTRIBUTE_ID`) ON UPDATE CASCADE,
  CONSTRAINT `CATEGORY_ATTRIBUTE_MAPPING_ibfk_1` FOREIGN KEY (`CATEGORY_ID`) REFERENCES `CATEGORY_DEFINITION` (`CATEGORY_ID`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CATEGORY_ATTRIBUTE_MAPPING`
--

LOCK TABLES `CATEGORY_ATTRIBUTE_MAPPING` WRITE;
/*!40000 ALTER TABLE `CATEGORY_ATTRIBUTE_MAPPING` DISABLE KEYS */;
INSERT INTO `CATEGORY_ATTRIBUTE_MAPPING` VALUES (1,4,2,'Y',1,'Y','ACTIVE'),(2,1,3,'N',2,'Y','ACTIVE'),(3,1,1,'N',3,'Y','ACTIVE'),(4,1,2,'N',1,'Y','ACTIVE'),(5,2,1,'N',2,'Y','ACTIVE'),(6,2,3,'N',1,'Y','ACTIVE'),(7,2,4,'N',3,'Y','ACTIVE'),(8,2,7,'N',4,'Y','ACTIVE'),(9,3,1,'N',2,'Y','ACTIVE'),(10,3,4,'N',3,'Y','ACTIVE'),(11,3,3,'N',1,'Y','ACTIVE'),(12,3,7,'N',4,'Y','ACTIVE');
/*!40000 ALTER TABLE `CATEGORY_ATTRIBUTE_MAPPING` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CATEGORY_ATTRIBUTE_VALUE`
--

DROP TABLE IF EXISTS `CATEGORY_ATTRIBUTE_VALUE`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CATEGORY_ATTRIBUTE_VALUE` (
  `CATEGORY_ATTRIBUTE_VALUE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `CATEGORY_ATTRIBUTE_MAPPING_ID` int(11) NOT NULL,
  `ATTRIBUTE_VALUE_ID` int(11) NOT NULL,
  `MAPPING_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`CATEGORY_ATTRIBUTE_VALUE_ID`),
  UNIQUE KEY `CATEGORY_ATTRIBUTE_MAPPING_ID` (`CATEGORY_ATTRIBUTE_MAPPING_ID`,`ATTRIBUTE_VALUE_ID`),
  KEY `CATEGORY_ATTRIBUTE_VALUE_ATTRIBUTE_VALUE_ID` (`ATTRIBUTE_VALUE_ID`),
  CONSTRAINT `CATEGORY_ATTRIBUTE_VALUE_ibfk_1` FOREIGN KEY (`CATEGORY_ATTRIBUTE_MAPPING_ID`) REFERENCES `CATEGORY_ATTRIBUTE_MAPPING` (`CATEGORY_ATTRIBUTE_MAPPING_ID`) ON UPDATE CASCADE,
  CONSTRAINT `CATEGORY_ATTRIBUTE_VALUE_ibfk_2` FOREIGN KEY (`ATTRIBUTE_VALUE_ID`) REFERENCES `ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=97 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CATEGORY_ATTRIBUTE_VALUE`
--

LOCK TABLES `CATEGORY_ATTRIBUTE_VALUE` WRITE;
/*!40000 ALTER TABLE `CATEGORY_ATTRIBUTE_VALUE` DISABLE KEYS */;
INSERT INTO `CATEGORY_ATTRIBUTE_VALUE` VALUES (1,5,10,'ACTIVE'),(2,2,11,'ACTIVE'),(3,9,12,'ACTIVE'),(4,1,13,'ACTIVE'),(5,5,14,'ACTIVE'),(6,5,15,'ACTIVE'),(7,5,16,'ACTIVE'),(8,2,17,'ACTIVE'),(9,5,18,'ACTIVE'),(10,2,19,'ACTIVE'),(11,5,20,'ACTIVE'),(12,9,21,'ACTIVE'),(13,5,21,'ACTIVE'),(14,2,22,'ACTIVE'),(15,5,23,'ACTIVE'),(16,5,24,'ACTIVE'),(17,2,25,'ACTIVE'),(18,5,26,'ACTIVE'),(19,5,27,'ACTIVE'),(20,5,28,'ACTIVE'),(21,5,29,'ACTIVE'),(22,2,30,'ACTIVE'),(23,2,31,'ACTIVE'),(24,5,32,'ACTIVE'),(25,2,33,'ACTIVE'),(26,2,34,'ACTIVE'),(27,5,35,'ACTIVE'),(28,5,36,'ACTIVE'),(29,2,37,'ACTIVE'),(30,2,38,'ACTIVE'),(31,5,39,'ACTIVE'),(32,9,40,'ACTIVE'),(33,5,41,'ACTIVE'),(34,5,42,'ACTIVE'),(35,5,43,'ACTIVE'),(36,2,13,'ACTIVE'),(37,2,44,'ACTIVE'),(38,5,45,'ACTIVE'),(39,5,46,'ACTIVE'),(40,9,47,'ACTIVE'),(41,2,48,'ACTIVE'),(42,9,49,'ACTIVE'),(43,5,50,'ACTIVE'),(44,9,51,'ACTIVE'),(45,5,52,'ACTIVE'),(46,5,53,'ACTIVE'),(47,2,54,'ACTIVE'),(48,9,55,'ACTIVE'),(49,5,56,'ACTIVE'),(50,2,57,'ACTIVE'),(51,1,57,'ACTIVE'),(52,2,58,'ACTIVE'),(53,2,59,'ACTIVE'),(54,9,60,'ACTIVE'),(55,9,27,'ACTIVE'),(56,2,62,'ACTIVE'),(57,5,63,'ACTIVE'),(58,9,64,'ACTIVE'),(59,5,65,'ACTIVE'),(60,2,66,'ACTIVE'),(61,2,67,'ACTIVE'),(62,2,68,'ACTIVE'),(63,9,69,'ACTIVE'),(64,5,62,'ACTIVE'),(65,5,70,'ACTIVE'),(66,5,71,'ACTIVE'),(67,9,72,'ACTIVE'),(68,5,102,'ACTIVE'),(69,5,73,'ACTIVE'),(70,5,74,'ACTIVE'),(71,2,75,'ACTIVE'),(72,2,76,'ACTIVE'),(73,2,77,'ACTIVE'),(74,5,78,'ACTIVE'),(75,5,79,'ACTIVE'),(76,5,80,'ACTIVE'),(77,5,81,'ACTIVE'),(78,9,82,'ACTIVE'),(79,5,83,'ACTIVE'),(80,2,84,'ACTIVE'),(81,5,85,'ACTIVE'),(82,2,88,'ACTIVE'),(83,5,89,'ACTIVE'),(84,9,90,'ACTIVE'),(85,9,92,'ACTIVE'),(86,9,93,'ACTIVE'),(87,5,94,'ACTIVE'),(88,9,95,'ACTIVE'),(89,9,96,'ACTIVE'),(90,9,97,'ACTIVE'),(91,5,98,'ACTIVE'),(92,5,87,'ACTIVE'),(93,9,61,'ACTIVE'),(94,5,86,'ACTIVE'),(95,9,91,'ACTIVE'),(96,5,101,'ACTIVE');
/*!40000 ALTER TABLE `CATEGORY_ATTRIBUTE_VALUE` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `CATEGORY_DEFINITION`
--

DROP TABLE IF EXISTS `CATEGORY_DEFINITION`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CATEGORY_DEFINITION` (
  `CATEGORY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `CATEGORY_NAME` varchar(255) NOT NULL,
  `CATEGORY_CODE` varchar(50) NOT NULL,
  `CATEGORY_DESCRIPTION` varchar(1000) DEFAULT NULL,
  `CATEGORY_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`CATEGORY_ID`),
  UNIQUE KEY `CATEGORY_NAME` (`CATEGORY_NAME`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `CATEGORY_DEFINITION`
--

LOCK TABLES `CATEGORY_DEFINITION` WRITE;
/*!40000 ALTER TABLE `CATEGORY_DEFINITION` DISABLE KEYS */;
INSERT INTO `CATEGORY_DEFINITION` VALUES (1,'COGS','COGS','COGS','ACTIVE'),(2,'Consumables','CONSUMABLES','CONSUMABLES','ACTIVE'),(3,'Fixed Assets','FIXED_ASSETS','Fixed Assets','ACTIVE'),(4,'Semi Finished','SEMI_FINISHED','Semi Finished','ACTIVE');
/*!40000 ALTER TABLE `CATEGORY_DEFINITION` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DAY_CLOSE_EVENT`
--

DROP TABLE IF EXISTS `DAY_CLOSE_EVENT`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DAY_CLOSE_EVENT` (
  `EVENT_ID` int(11) NOT NULL AUTO_INCREMENT,
  `BUSINESS_DATE` date NOT NULL,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `STATUS` varchar(45) NOT NULL DEFAULT 'INITIATED',
  `DAY_CLOSURE_ID` int(11) NOT NULL,
  `UNIT_ID` int(11) NOT NULL,
  `CLOSURE_EVENT_TYPE` varchar(45) NOT NULL,
  PRIMARY KEY (`EVENT_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DAY_CLOSE_EVENT`
--

LOCK TABLES `DAY_CLOSE_EVENT` WRITE;
/*!40000 ALTER TABLE `DAY_CLOSE_EVENT` DISABLE KEYS */;
INSERT INTO `DAY_CLOSE_EVENT` VALUES (1,'2016-05-25','2016-06-27 10:17:55','CLOSED',6401,10000,'CLOSING'),(2,'2016-05-22','2016-06-27 10:17:55','CLOSED',6382,10001,'CLOSING'),(3,'2016-05-31','2016-06-27 10:17:55','CLOSED',6395,10002,'CLOSING'),(4,'2016-05-22','2016-06-27 10:17:55','CLOSED',6384,10003,'CLOSING'),(5,'2016-05-23','2016-06-27 10:17:55','CLOSED',6390,10004,'CLOSING'),(6,'2016-06-04','2016-06-27 10:17:55','CLOSED',6400,10005,'CLOSING'),(7,'2016-06-01','2016-06-27 10:17:55','CLOSED',6402,10006,'CLOSING'),(8,'2016-03-16','2016-06-27 10:17:55','CLOSED',3853,10007,'CLOSING'),(9,'2016-06-01','2016-06-27 10:17:55','CLOSED',6398,10008,'CLOSING'),(10,'2016-05-22','2016-06-27 10:17:55','CLOSED',6378,10009,'CLOSING'),(11,'2016-05-22','2016-06-27 10:17:55','CLOSED',6380,10010,'CLOSING'),(12,'2016-06-01','2016-06-27 10:17:55','CLOSED',6396,10011,'CLOSING'),(13,'2016-05-22','2016-06-27 10:17:55','CLOSED',6377,10012,'CLOSING'),(14,'2016-05-22','2016-06-27 10:17:55','CLOSED',6374,10013,'CLOSING'),(15,'2016-05-10','2016-06-27 10:17:55','CLOSED',5932,10014,'CLOSING'),(16,'2015-12-14','2016-06-27 10:17:55','CLOSED',954,11001,'CLOSING'),(17,'2016-05-22','2016-06-27 10:17:55','CLOSED',6357,12001,'CLOSING'),(18,'2016-05-22','2016-06-27 10:17:55','CLOSED',6353,12002,'CLOSING'),(19,'2016-05-22','2016-06-27 10:17:55','CLOSED',6362,12003,'CLOSING'),(20,'2016-05-22','2016-06-27 10:17:55','CLOSED',6358,12004,'CLOSING'),(21,'2016-05-22','2016-06-27 10:17:55','CLOSED',6359,12005,'CLOSING'),(22,'2016-05-22','2016-06-27 10:17:55','CLOSED',6360,12006,'CLOSING'),(23,'2016-05-22','2016-06-27 10:17:55','CLOSED',6361,12007,'CLOSING'),(24,'2016-05-22','2016-06-27 10:17:55','CLOSED',6363,12008,'CLOSING'),(25,'2016-05-22','2016-06-27 10:17:55','CLOSED',6356,12009,'CLOSING'),(26,'2016-04-08','2016-06-27 10:17:55','CLOSED',4722,12010,'CLOSING'),(27,'2016-05-22','2016-06-27 10:17:55','CLOSED',6371,12011,'CLOSING'),(28,'2016-05-22','2016-06-27 10:17:55','CLOSED',6366,12012,'CLOSING'),(29,'2016-05-22','2016-06-27 10:17:55','CLOSED',6355,12013,'CLOSING'),(30,'2016-05-22','2016-06-27 10:17:55','CLOSED',6368,12014,'CLOSING'),(31,'2016-05-22','2016-06-27 10:17:55','CLOSED',6367,12015,'CLOSING'),(32,'2016-05-22','2016-06-27 10:17:55','CLOSED',6352,12016,'CLOSING'),(33,'2016-05-22','2016-06-27 10:17:55','CLOSED',6386,12017,'CLOSING'),(34,'2016-05-22','2016-06-27 10:17:55','CLOSED',6375,12018,'CLOSING'),(35,'2016-05-22','2016-06-27 10:17:55','CLOSED',6369,12019,'CLOSING'),(36,'2016-05-22','2016-06-27 10:17:55','CLOSED',6364,12020,'CLOSING'),(37,'2016-05-22','2016-06-27 10:17:55','CLOSED',6387,12021,'CLOSING'),(38,'2016-05-22','2016-06-27 10:17:55','CLOSED',6365,12022,'CLOSING'),(39,'2016-05-22','2016-06-27 10:17:55','CLOSED',6379,12023,'CLOSING'),(40,'2016-05-22','2016-06-27 10:17:55','CLOSED',6370,12028,'CLOSING'),(41,'2016-05-23','2016-06-27 10:17:55','CLOSED',6392,12029,'CLOSING'),(42,'2016-05-22','2016-06-27 10:17:55','CLOSED',6389,12030,'CLOSING');
/*!40000 ALTER TABLE `DAY_CLOSE_EVENT` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DAY_CLOSE_EVENT_RANGE`
--

DROP TABLE IF EXISTS `DAY_CLOSE_EVENT_RANGE`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DAY_CLOSE_EVENT_RANGE` (
  `EVENT_RANGE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `EVENT_ID` int(11) NOT NULL,
  `TYPE` varchar(45) NOT NULL,
  `START_ID` int(11) NOT NULL,
  `END_ID` int(11) NOT NULL,
  PRIMARY KEY (`EVENT_RANGE_ID`),
  KEY `EVENT_ID` (`EVENT_ID`),
  CONSTRAINT `DAY_CLOSE_EVENT_RANGE_ibfk_1` FOREIGN KEY (`EVENT_ID`) REFERENCES `DAY_CLOSE_EVENT` (`EVENT_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DAY_CLOSE_EVENT_RANGE`
--

LOCK TABLES `DAY_CLOSE_EVENT_RANGE` WRITE;
/*!40000 ALTER TABLE `DAY_CLOSE_EVENT_RANGE` DISABLE KEYS */;
/*!40000 ALTER TABLE `DAY_CLOSE_EVENT_RANGE` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DAY_CLOSE_PRODUCT_VALUES`
--

DROP TABLE IF EXISTS `DAY_CLOSE_PRODUCT_VALUES`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DAY_CLOSE_PRODUCT_VALUES` (
  `CALCULATED_PRODUCT_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PRODUCT_ID` int(11) NOT NULL,
  `UNIT_ID` int(11) NOT NULL,
  `BUSINESS_DATE` date NOT NULL,
  `CONSUMPTION` decimal(10,2) NOT NULL DEFAULT '0.00',
  `TRANSFER_OUT` decimal(10,2) NOT NULL DEFAULT '0.00',
  `WASTAGE` decimal(10,2) NOT NULL DEFAULT '0.00',
  `RECEIVED` decimal(10,2) NOT NULL DEFAULT '0.00',
  `UOM` varchar(10) NOT NULL,
  `EVENT_ID` int(11) NOT NULL,
  PRIMARY KEY (`CALCULATED_PRODUCT_ID`),
  KEY `EVENT_ID` (`EVENT_ID`),
  CONSTRAINT `DAY_CLOSE_PRODUCT_VALUES_ibfk_1` FOREIGN KEY (`EVENT_ID`) REFERENCES `DAY_CLOSE_EVENT` (`EVENT_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DAY_CLOSE_PRODUCT_VALUES`
--

LOCK TABLES `DAY_CLOSE_PRODUCT_VALUES` WRITE;
/*!40000 ALTER TABLE `DAY_CLOSE_PRODUCT_VALUES` DISABLE KEYS */;
/*!40000 ALTER TABLE `DAY_CLOSE_PRODUCT_VALUES` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FULFILLMENT_UNIT_MAPPING`
--

DROP TABLE IF EXISTS `FULFILLMENT_UNIT_MAPPING`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `FULFILLMENT_UNIT_MAPPING` (
  `FULFILLMENT_UNIT_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `REQUESTING_UNIT_ID` int(11) NOT NULL,
  `FULFILLMENT_TYPE` varchar(30) NOT NULL,
  `FULFILLING_UNIT_ID` int(11) NOT NULL,
  `MAPPING_STATUS` varchar(30) NOT NULL,
  PRIMARY KEY (`FULFILLMENT_UNIT_MAPPING_ID`),
  KEY `REQUESTING_UNIT_ID` (`REQUESTING_UNIT_ID`),
  KEY `FULFILLING_UNIT_ID` (`FULFILLING_UNIT_ID`),
  CONSTRAINT `FULFILLMENT_UNIT_MAPPING_ibfk_1` FOREIGN KEY (`REQUESTING_UNIT_ID`) REFERENCES `UNIT_DETAIL` (`UNIT_ID`),
  CONSTRAINT `FULFILLMENT_UNIT_MAPPING_ibfk_2` FOREIGN KEY (`FULFILLING_UNIT_ID`) REFERENCES `UNIT_DETAIL` (`UNIT_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=124 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FULFILLMENT_UNIT_MAPPING`
--

LOCK TABLES `FULFILLMENT_UNIT_MAPPING` WRITE;
/*!40000 ALTER TABLE `FULFILLMENT_UNIT_MAPPING` DISABLE KEYS */;
INSERT INTO `FULFILLMENT_UNIT_MAPPING` VALUES (1,10000,'KITCHEN',14001,'ACTIVE'),(2,10001,'KITCHEN',14001,'ACTIVE'),(3,10002,'KITCHEN',14001,'ACTIVE'),(4,10003,'KITCHEN',14001,'ACTIVE'),(5,10004,'KITCHEN',14001,'ACTIVE'),(6,10005,'KITCHEN',14001,'ACTIVE'),(7,10006,'KITCHEN',14001,'ACTIVE'),(8,10007,'KITCHEN',14001,'ACTIVE'),(9,10008,'KITCHEN',14001,'ACTIVE'),(10,10009,'KITCHEN',14001,'ACTIVE'),(11,10010,'KITCHEN',14001,'ACTIVE'),(12,10011,'KITCHEN',14001,'ACTIVE'),(13,10012,'KITCHEN',14001,'ACTIVE'),(14,10013,'KITCHEN',14001,'ACTIVE'),(15,10014,'KITCHEN',14001,'ACTIVE'),(16,11001,'KITCHEN',14001,'ACTIVE'),(17,12001,'KITCHEN',14001,'ACTIVE'),(18,12002,'KITCHEN',14001,'ACTIVE'),(19,12003,'KITCHEN',14001,'ACTIVE'),(20,12004,'KITCHEN',14001,'ACTIVE'),(21,12005,'KITCHEN',14001,'ACTIVE'),(22,12006,'KITCHEN',14001,'ACTIVE'),(23,12007,'KITCHEN',14001,'ACTIVE'),(24,12008,'KITCHEN',14001,'ACTIVE'),(25,12009,'KITCHEN',14001,'ACTIVE'),(26,12010,'KITCHEN',14001,'ACTIVE'),(27,12011,'KITCHEN',14001,'ACTIVE'),(28,12012,'KITCHEN',14001,'ACTIVE'),(29,12013,'KITCHEN',14001,'ACTIVE'),(30,12014,'KITCHEN',14001,'ACTIVE'),(31,12015,'KITCHEN',14001,'ACTIVE'),(32,12016,'KITCHEN',14001,'ACTIVE'),(33,12017,'KITCHEN',14001,'ACTIVE'),(34,12018,'KITCHEN',14001,'ACTIVE'),(35,12019,'KITCHEN',14001,'ACTIVE'),(36,12020,'KITCHEN',14001,'ACTIVE'),(37,12021,'KITCHEN',14001,'ACTIVE'),(38,12022,'KITCHEN',14002,'ACTIVE'),(39,12023,'KITCHEN',14002,'ACTIVE'),(40,14001,'WAREHOUSE',13001,'ACTIVE'),(41,14002,'WAREHOUSE',13002,'ACTIVE'),(42,10000,'WAREHOUSE',13001,'ACTIVE'),(43,10001,'WAREHOUSE',13001,'ACTIVE'),(44,10002,'WAREHOUSE',13001,'ACTIVE'),(45,10003,'WAREHOUSE',13001,'ACTIVE'),(46,10004,'WAREHOUSE',13001,'ACTIVE'),(47,10005,'WAREHOUSE',13001,'ACTIVE'),(48,10006,'WAREHOUSE',13001,'ACTIVE'),(49,10007,'WAREHOUSE',13001,'ACTIVE'),(50,10008,'WAREHOUSE',13001,'ACTIVE'),(51,10009,'WAREHOUSE',13001,'ACTIVE'),(52,10010,'WAREHOUSE',13001,'ACTIVE'),(53,10011,'WAREHOUSE',13001,'ACTIVE'),(54,10012,'WAREHOUSE',13001,'ACTIVE'),(55,10013,'WAREHOUSE',13001,'ACTIVE'),(56,10014,'WAREHOUSE',13001,'ACTIVE'),(57,11001,'WAREHOUSE',13001,'ACTIVE'),(58,12001,'WAREHOUSE',13001,'ACTIVE'),(59,12002,'WAREHOUSE',13001,'ACTIVE'),(60,12003,'WAREHOUSE',13001,'ACTIVE'),(61,12004,'WAREHOUSE',13001,'ACTIVE'),(62,12005,'WAREHOUSE',13001,'ACTIVE'),(63,12006,'WAREHOUSE',13001,'ACTIVE'),(64,12007,'WAREHOUSE',13001,'ACTIVE'),(65,12008,'WAREHOUSE',13001,'ACTIVE'),(66,12009,'WAREHOUSE',13001,'ACTIVE'),(67,12010,'WAREHOUSE',13001,'ACTIVE'),(68,12011,'WAREHOUSE',13001,'ACTIVE'),(69,12012,'WAREHOUSE',13001,'ACTIVE'),(70,12013,'WAREHOUSE',13001,'ACTIVE'),(71,12014,'WAREHOUSE',13001,'ACTIVE'),(72,12015,'WAREHOUSE',13001,'ACTIVE'),(73,12016,'WAREHOUSE',13001,'ACTIVE'),(74,12017,'WAREHOUSE',13001,'ACTIVE'),(75,12018,'WAREHOUSE',13001,'ACTIVE'),(76,12019,'WAREHOUSE',13001,'ACTIVE'),(77,12020,'WAREHOUSE',13001,'ACTIVE'),(78,12021,'WAREHOUSE',13001,'ACTIVE'),(79,12022,'WAREHOUSE',13002,'ACTIVE'),(80,12023,'WAREHOUSE',13002,'ACTIVE'),(81,10000,'EXTERNAL',10000,'ACTIVE'),(82,10001,'EXTERNAL',10001,'ACTIVE'),(83,10002,'EXTERNAL',10002,'ACTIVE'),(84,10003,'EXTERNAL',10003,'ACTIVE'),(85,10004,'EXTERNAL',10004,'ACTIVE'),(86,10005,'EXTERNAL',10005,'ACTIVE'),(87,10006,'EXTERNAL',10006,'ACTIVE'),(88,10007,'EXTERNAL',10007,'ACTIVE'),(89,10008,'EXTERNAL',10008,'ACTIVE'),(90,10009,'EXTERNAL',10009,'ACTIVE'),(91,10010,'EXTERNAL',10010,'ACTIVE'),(92,10011,'EXTERNAL',10011,'ACTIVE'),(93,10012,'EXTERNAL',10012,'ACTIVE'),(94,10013,'EXTERNAL',10013,'ACTIVE'),(95,10014,'EXTERNAL',10014,'ACTIVE'),(96,11001,'EXTERNAL',11001,'ACTIVE'),(97,12001,'EXTERNAL',12001,'ACTIVE'),(98,12002,'EXTERNAL',12002,'ACTIVE'),(99,12003,'EXTERNAL',12003,'ACTIVE'),(100,12004,'EXTERNAL',12004,'ACTIVE'),(101,12005,'EXTERNAL',12005,'ACTIVE'),(102,12006,'EXTERNAL',12006,'ACTIVE'),(103,12007,'EXTERNAL',12007,'ACTIVE'),(104,12008,'EXTERNAL',12008,'ACTIVE'),(105,12009,'EXTERNAL',12009,'ACTIVE'),(106,12010,'EXTERNAL',12010,'ACTIVE'),(107,12011,'EXTERNAL',12011,'ACTIVE'),(108,12012,'EXTERNAL',12012,'ACTIVE'),(109,12013,'EXTERNAL',12013,'ACTIVE'),(110,12014,'EXTERNAL',12014,'ACTIVE'),(111,12015,'EXTERNAL',12015,'ACTIVE'),(112,12016,'EXTERNAL',12016,'ACTIVE'),(113,12017,'EXTERNAL',12017,'ACTIVE'),(114,12018,'EXTERNAL',12018,'ACTIVE'),(115,12019,'EXTERNAL',12019,'ACTIVE'),(116,12020,'EXTERNAL',12020,'ACTIVE'),(117,12021,'EXTERNAL',12021,'ACTIVE'),(118,12022,'EXTERNAL',12022,'ACTIVE'),(119,12023,'EXTERNAL',12023,'ACTIVE'),(120,14001,'EXTERNAL',14001,'ACTIVE'),(121,14002,'EXTERNAL',14002,'ACTIVE'),(122,13001,'EXTERNAL',13001,'ACTIVE'),(123,13002,'EXTERNAL',13002,'ACTIVE');
/*!40000 ALTER TABLE `FULFILLMENT_UNIT_MAPPING` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `GOODS_RECEIVED`
--

DROP TABLE IF EXISTS `GOODS_RECEIVED`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `GOODS_RECEIVED` (
  `GOODS_RECEIVED_ID` int(11) NOT NULL AUTO_INCREMENT,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `GENERATION_UNIT_ID` int(11) NOT NULL,
  `GENERATED_FOR_UNIT_ID` int(11) NOT NULL,
  `GENERATED_BY` int(11) NOT NULL,
  `GOODS_RECEIVED_STATUS` varchar(30) NOT NULL,
  `COMMENT` varchar(1000) DEFAULT NULL,
  `REQUEST_ORDER_ID` int(11) DEFAULT NULL,
  `TRANSFER_ORDER_ID` int(11) DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `PURCHASE_ORDER_ID` int(11) DEFAULT NULL,
  `RECEIVED_BY` int(11) DEFAULT NULL,
  PRIMARY KEY (`GOODS_RECEIVED_ID`),
  KEY `REQUEST_ORDER_ID` (`REQUEST_ORDER_ID`),
  KEY `TRANSFER_ORDER_ID` (`TRANSFER_ORDER_ID`),
  KEY `PURCHASE_ORDER_ID` (`PURCHASE_ORDER_ID`),
  CONSTRAINT `GOODS_RECEIVED_ibfk_1` FOREIGN KEY (`REQUEST_ORDER_ID`) REFERENCES `REQUEST_ORDER` (`REQUEST_ORDER_ID`),
  CONSTRAINT `GOODS_RECEIVED_ibfk_2` FOREIGN KEY (`TRANSFER_ORDER_ID`) REFERENCES `TRANSFER_ORDER` (`TRANSFER_ORDER_ID`),
  CONSTRAINT `GOODS_RECEIVED_ibfk_3` FOREIGN KEY (`PURCHASE_ORDER_ID`) REFERENCES `PURCHASE_ORDER` (`PURCHASE_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `GOODS_RECEIVED`
--

LOCK TABLES `GOODS_RECEIVED` WRITE;
/*!40000 ALTER TABLE `GOODS_RECEIVED` DISABLE KEYS */;
INSERT INTO `GOODS_RECEIVED` VALUES (16,'2016-06-27 21:11:39',10000,10000,120056,'SETTLED','received',85,16,'2016-06-27 21:17:30',16,100000),(17,'2016-06-27 23:13:21',13001,10000,120056,'SETTLED',NULL,86,17,'2016-06-28 00:00:02',17,100000),(18,'2016-06-27 23:41:23',10000,10000,120056,'SETTLED',NULL,30,18,'2016-06-28 09:38:45',18,100000),(19,'2016-06-27 23:49:19',10000,10000,120056,'CANCELLED',NULL,1,19,'2016-06-28 18:14:02',19,NULL),(20,'2016-06-27 23:55:46',10000,10000,120056,'CANCELLED',NULL,3,20,'2016-06-28 18:13:45',20,NULL),(21,'2016-06-27 23:56:06',10000,10000,120056,'SETTLED',NULL,8,21,'2016-06-27 23:57:43',21,100000),(22,'2016-06-28 09:37:47',10000,10000,120056,'CANCELLED',NULL,5,22,'2016-06-28 18:13:39',22,NULL),(23,'2016-06-28 18:24:12',10006,10006,120056,'SETTLED',NULL,87,23,'2016-06-28 18:26:18',23,100000);
/*!40000 ALTER TABLE `GOODS_RECEIVED` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `GOODS_RECEIVED_ITEM`
--

DROP TABLE IF EXISTS `GOODS_RECEIVED_ITEM`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `GOODS_RECEIVED_ITEM` (
  `GOODS_RECEIVED_ITEM_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SKU_ID` int(11) NOT NULL,
  `SKU_NAME` varchar(255) NOT NULL,
  `TRANSFERRED_QUANTITY` decimal(10,2) NOT NULL,
  `RECEIVED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `UNIT_OF_MEASURE` varchar(10) NOT NULL,
  `UNIT_PRICE` decimal(10,2) DEFAULT NULL,
  `NEGOTIATED_UNIT_PRICE` decimal(10,2) DEFAULT NULL,
  `GOODS_RECEIVED_ID` int(11) NOT NULL,
  `REQUEST_ORDER_ITEM_ID` int(11) DEFAULT NULL,
  `TRANSFER_ORDER_ITEM_ID` int(11) DEFAULT NULL,
  `PURCHASE_ORDER_ITEM_ID` int(11) DEFAULT NULL,
  PRIMARY KEY (`GOODS_RECEIVED_ITEM_ID`),
  KEY `GOODS_RECEIVED_ID` (`GOODS_RECEIVED_ID`),
  KEY `REQUEST_ORDER_ITEM_ID` (`REQUEST_ORDER_ITEM_ID`),
  KEY `TRANSFER_ORDER_ITEM_ID` (`TRANSFER_ORDER_ITEM_ID`),
  KEY `PURCHASE_ORDER_ITEM_ID` (`PURCHASE_ORDER_ITEM_ID`),
  CONSTRAINT `GOODS_RECEIVED_ITEM_ibfk_6` FOREIGN KEY (`PURCHASE_ORDER_ITEM_ID`) REFERENCES `PURCHASE_ORDER_ITEM` (`PURCHASE_ORDER_ITEM_ID`),
  CONSTRAINT `GOODS_RECEIVED_ITEM_ibfk_1` FOREIGN KEY (`GOODS_RECEIVED_ID`) REFERENCES `GOODS_RECEIVED` (`GOODS_RECEIVED_ID`),
  CONSTRAINT `GOODS_RECEIVED_ITEM_ibfk_2` FOREIGN KEY (`REQUEST_ORDER_ITEM_ID`) REFERENCES `REQUEST_ORDER_ITEM` (`REQUEST_ORDER_ITEM_ID`),
  CONSTRAINT `GOODS_RECEIVED_ITEM_ibfk_3` FOREIGN KEY (`TRANSFER_ORDER_ITEM_ID`) REFERENCES `TRANSFER_ORDER_ITEM` (`TRANSFER_ORDER_ITEM_ID`),
  CONSTRAINT `GOODS_RECEIVED_ITEM_ibfk_4` FOREIGN KEY (`REQUEST_ORDER_ITEM_ID`) REFERENCES `REQUEST_ORDER_ITEM` (`REQUEST_ORDER_ITEM_ID`),
  CONSTRAINT `GOODS_RECEIVED_ITEM_ibfk_5` FOREIGN KEY (`TRANSFER_ORDER_ITEM_ID`) REFERENCES `TRANSFER_ORDER_ITEM` (`TRANSFER_ORDER_ITEM_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `GOODS_RECEIVED_ITEM`
--

LOCK TABLES `GOODS_RECEIVED_ITEM` WRITE;
/*!40000 ALTER TABLE `GOODS_RECEIVED_ITEM` DISABLE KEYS */;
INSERT INTO `GOODS_RECEIVED_ITEM` VALUES (18,1,'Milk Amul',1.50,1.50,'L',39.50,NULL,16,159,18,18),(19,22,'Sachet - Desi Regular',20.00,0.00,'KG',0.00,NULL,17,160,19,19),(20,19,'Hot Cup 250 ML',1000.00,1000.00,'PC',0.00,NULL,17,161,20,20),(21,1,'Milk Amul',1.00,0.00,'L',39.50,NULL,18,95,21,21),(22,1,'Milk Amul',10.00,NULL,'L',39.50,NULL,19,1,22,22),(23,1,'Milk Amul',2.50,NULL,'L',39.50,NULL,20,4,23,23),(24,8,'Pav',1.00,1.00,'PC',0.00,NULL,21,13,24,24),(25,8,'Pav',1.00,NULL,'PC',0.00,NULL,22,7,25,25),(26,1,'Milk Amul',12.00,0.00,'L',39.50,NULL,23,162,26,26),(27,8,'Pav',10.00,0.00,'PC',0.00,NULL,23,163,27,27);
/*!40000 ALTER TABLE `GOODS_RECEIVED_ITEM` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PACKAGING_DEFINITION`
--

DROP TABLE IF EXISTS `PACKAGING_DEFINITION`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PACKAGING_DEFINITION` (
  `PACKAGING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PACKAGING_TYPE` varchar(20) NOT NULL,
  `PACKAGING_CODE` varchar(30) NOT NULL,
  `PACKAGING_NAME` varchar(255) NOT NULL,
  `PACKAGING_STATUS` varchar(15) NOT NULL,
  `CONVERSION_RATIO` decimal(10,2) NOT NULL,
  `UNIT_OF_MEASURE` varchar(15) NOT NULL,
  `SUB_PACKAGING` int(11) DEFAULT NULL,
  PRIMARY KEY (`PACKAGING_ID`),
  UNIQUE KEY `PACKAGING_TYPE` (`PACKAGING_TYPE`,`PACKAGING_CODE`),
  KEY `SUB_PACKAGING` (`SUB_PACKAGING`),
  CONSTRAINT `PACKAGING_DEFINITION_ibfk_1` FOREIGN KEY (`SUB_PACKAGING`) REFERENCES `PACKAGING_DEFINITION` (`PACKAGING_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PACKAGING_DEFINITION`
--

LOCK TABLES `PACKAGING_DEFINITION` WRITE;
/*!40000 ALTER TABLE `PACKAGING_DEFINITION` DISABLE KEYS */;
INSERT INTO `PACKAGING_DEFINITION` VALUES (1,'LOOSE','L','LITRES','ACTIVE',1.00,'L',NULL),(2,'LOOSE','KG','KILOGRAMS','ACTIVE',1.00,'KG',NULL),(3,'LOOSE','PC','PIECES','ACTIVE',1.00,'PC',NULL),(4,'INNER','100_PC_PKT','100 PC PACKET','ACTIVE',100.00,'PC',3),(5,'CASE','100_PC_20_PKT_BOX','100 PC 20 PACKET BOX','ACTIVE',2000.00,'PC',5),(6,'INNER','150_PC_PKT','150 PC PACKET','ACTIVE',150.00,'PC',3),(7,'CASE','150_PC_16_PKT_BOX','150 PC 16 PACKET BOX','ACTIVE',2400.00,'PC',7),(8,'CASE','100_PC_10_PKT_BOX','100 PC 10 PACKET BOX','ACTIVE',1000.00,'PC',5),(9,'INNER','500_PC_PKT','500 PC PACKET','ACTIVE',500.00,'PC',3),(10,'INNER','50_PC_PKT','50 PC PACKET','ACTIVE',50.00,'PC',3),(11,'INNER','25_PC_PKT','25 PC PACKET','ACTIVE',20.00,'PC',3),(12,'CASE','25_PC_16_PKT_BOX','25 PC 16 PACKET BOX','ACTIVE',400.00,'PC',13),(13,'CASE','25_PC_20_PKT_BOX','25 PC 20 PACKET BOX','ACTIVE',500.00,'PC',5),(14,'CASE','100_PC_200_PKT_BOX','100 PC 200 PACKET BOX','ACTIVE',20000.00,'PC',5),(15,'CASE','100_PC_250_PKT_BOX','100 PC 250 PACKET BOX','ACTIVE',25000.00,'PC',5),(16,'INNER','40_PC_PKT','40 PC PACKET','ACTIVE',40.00,'PC',3),(17,'CASE','40_PC_28_PKT_BOX','40 PC 28 PACKET BOX','ACTIVE',1120.00,'PC',18),(18,'CASE','400_PC_BOX','400 PC BOX','ACTIVE',400.00,'PC',3),(19,'CASE','500_PC_BOX','500 PC BOX','ACTIVE',500.00,'PC',3),(20,'CASE','700_PC_BOX','700 PC BOX','ACTIVE',700.00,'PC',3),(21,'CASE','100_PC_100_PKT_BOX','100 PC 100 PACKET BOX','ACTIVE',10000.00,'PC',5),(22,'CASE','100_PC_60_PKT_BOX','100 PC 60 PACKET BOX','ACTIVE',6000.00,'PC',5),(23,'CASE','100_PC_50_PKT_BOX','100 PC 50 PACKET BOX','ACTIVE',5000.00,'PC',5),(24,'CASE','100_PC_40_PKT_BOX','100 PC 40 PACKET BOX','ACTIVE',4000.00,'PC',5),(25,'INNER','1000_PC_PKT','1000 PC PACKET','ACTIVE',1000.00,'PC',3),(26,'CASE','1000_PC_5_PKT_BOX','1000 PC 5 PACKET BOX','ACTIVE',5000.00,'PC',27),(27,'CASE','50_PC_40_PKT_BOX','50 PC 40 PACKET BOX','ACTIVE',2000.00,'PC',12),(28,'CASE','50_PC_20_PKT_BOX','50 PC 20 PACKET BOX','ACTIVE',1000.00,'PC',12),(29,'CASE','50_PC_300_PKT_BOX','50 PC 300 PACKET BOX','ACTIVE',15000.00,'PC',12),(30,'CASE','50_PC_100_PKT_BOX','50 PC 100 PACKET BOX','ACTIVE',5000.00,'PC',12),(31,'CASE','100_PC_25_PKT_BOX','100 PC 25 PACKET BOX','ACTIVE',2500.00,'PC',5),(32,'INNER','70_PC_PKT','70 PC PACKET','ACTIVE',70.00,'PC',3),(33,'CASE','50_PC_50_PKT_BOX','50 PC 50 PACKET BOX','ACTIVE',2500.00,'PC',12),(34,'CASE','50_PC_30_PKT_BOX','50 PC 30 PACKET BOX','ACTIVE',1500.00,'PC',12),(35,'INNER','3_KG_PKT','3 KG PACKET','ACTIVE',3.00,'KG',3),(36,'CASE','3_KG_10-PKT_BOX','3 KG 10 PACKET BOX','ACTIVE',30.00,'KG',37),(37,'INNER','98_PC_PKT','98 PC PACKET','ACTIVE',98.00,'PC',3),(38,'CASE','100_PC_150_PKT_BOX','100 PC 150 PACKET BOX','ACTIVE',15000.00,'PC',5),(39,'CASE','200_PC_BOX','200 PC BOX','ACTIVE',200.00,'PC',3),(40,'CASE','50_PC_10_PKT_BOX','50 PC 10 PACKET BOX','ACTIVE',500.00,'PC',12),(41,'CASE','50_PC_8_PKT_BOX','50 PC 8 PACKET BOX','ACTIVE',400.00,'PC',12),(42,'CASE','100_PC_48_PKT_BOX','100 PC 48 PACKET BOX','ACTIVE',4800.00,'PC',5),(43,'INNER','0.1_KG_PKT','0.1 KG PACKET','ACTIVE',0.10,'Kg',3),(44,'INNER','0.250_KG_PKT','0.25 KG PACKET','ACTIVE',0.25,'Kg',3),(45,'INNER','200_PC_PKT','200 PC PACKET','ACTIVE',200.00,'PC',3),(46,'CASE','200_PC_10_PKT_BOX','200 PC 10 PACKET BOX','ACTIVE',2000.00,'PC',48),(47,'INNER','0.200_KG_PKT','0.200 KG PACKET','ACTIVE',0.20,'Kg',3),(48,'INNER','0.623_L_PKT','0.623 KG PACKET','ACTIVE',0.62,'L',3),(49,'INNER','1_L_PKT','1 L PACKET','ACTIVE',1.00,'Kg',2),(50,'INNER','0.500_KG_PKT','0.20 KG PACKET','ACTIVE',0.20,'Kg',3),(51,'INNER','0.75_L_PKT','0.750 L PACKET','ACTIVE',0.75,'Kg',2),(52,'INNER','39_PC_PKT','39 PC PACKET','ACTIVE',39.00,'PC',3),(53,'CASE','39_PC_24_PKT_BOX','39 PC 24 PACKET BOX','ACTIVE',936.00,'PC',48),(54,'INNER','0.600_KG_PKT','0.600 KG PACKET','ACTIVE',0.60,'Kg',2),(55,'INNER','10_PC_PKT','10 PC PACKET','ACTIVE',10.00,'Pc',3),(56,'INNER','5_L_PKT','5 L PACKET','ACTIVE',5.00,'L',2),(57,'INNER','0.650_L_PKT','0.650 L PACKET','ACTIVE',0.65,'L',2),(58,'CASE','30_PC_BOX','30 PC BOX','ACTIVE',30.00,'PC',3),(59,'INNER','1_KG_PKT','1 KG PACKET','ACTIVE',1.00,'Kg',2),(60,'INNER','20_PC_PKT','20 PC PACKET','ACTIVE',20.00,'PC',3),(61,'INNER','5_KG_PKT','5 KG PACKET','ACTIVE',5.00,'Kg',2),(62,'CASE','40_PC_50_PKT_BOX','40 PC 28 PACKET BOX','ACTIVE',1120.00,'PC',18),(63,'CASE','100_PC_28_PKT_BOX','100 PC 48 PACKET BOX','ACTIVE',4800.00,'PC',5),(64,'INNER','5_PC_PKT','5 PC PACKET','ACTIVE',5.00,'PC',3),(65,'CASE','5_PC_10_PKT_BOX','5 PC 10 PACKET BOX','ACTIVE',50.00,'PC',66),(66,'INNER','91_PC_PKT','91 PC PACKET','ACTIVE',91.00,'PC',3),(67,'CASE','150_PC_10_PKT_BOX','150 PC 10 PACKET BOX','ACTIVE',1500.00,'PC',7),(68,'CASE','100_PC_16_PKT_BOX','100 PC 16 PACKET BOX','ACTIVE',1600.00,'PC',5),(69,'INNER','3_PC_PKT','3 PC PACKET','ACTIVE',3.00,'PC',3),(70,'CASE','3_PC_132_PKT_BOX','3 PC 132 PACKET BOX','ACTIVE',396.00,'PC',71),(71,'INNER','99_PC_PKT','99 PC PACKET','ACTIVE',99.00,'PC',3),(72,'CASE','12_PC_BOX','12 PC BOX','ACTIVE',12.00,'PC',3),(73,'CASE','100_PC_BOX','100 PC BOX','ACTIVE',100.00,'PC',3),(74,'CASE','24_PC_BOX','24 PC BOX','ACTIVE',24.00,'PC',3),(75,'CASE','48_PC_BOX','48 PC BOX','ACTIVE',48.00,'PC',3),(76,'CASE','144_PC_BOX','144 PC BOX','ACTIVE',144.00,'PC',3),(77,'CASE','2_PC_BOX','2 PC BOX','ACTIVE',2.00,'PC',3),(78,'CASE','5_PC_BOX','5 PC BOX','ACTIVE',5.00,'PC',3),(79,'CASE','25_PC_BOX','25 PC BOX','ACTIVE',25.00,'PC',3),(80,'CASE','10_PC_BOX','10 PC BOX','ACTIVE',10.00,'PC',3),(81,'CASE','50_PC_BOX','50 PC BOX','ACTIVE',50.00,'PC',3),(82,'CASE','70_PC_BOX','70 PC BOX','ACTIVE',70.00,'PC',3),(83,'CASE','20_PC_BOX','20 PC BOX','ACTIVE',20.00,'PC',3),(84,'CASE','1_KG_12_PKT_BOX','1 KG 12 PACKET','ACTIVE',12.00,'Kg',61),(85,'CASE','1_KG_8_PKT','1 KG 8 PACKET','ACTIVE',8.00,'Kg',61),(86,'CASE','1_KG_8_PKT_BOX','4 PC BOX','ACTIVE',4.00,'PC',3),(87,'CASE','6_PC_BOX','6 PC BOX','ACTIVE',6.00,'PC',3),(88,'CASE','1000_PC_18_PKT_BOX','1000 PC 18 PACKET BOX','ACTIVE',18000.00,'PC',27),(89,'CASE','0.5_KG_20_PKT_BOX','0.500 KG 20 PKT BOX','ACTIVE',10.00,'KG',52),(90,'CASE','5_KG_25_PKT_BOX','5 KG 25 PKT BOX','ACTIVE',125.00,'KG',63),(91,'INNER','0.124_KG_PKT','0.124 KG PKT','ACTIVE',0.12,'KG',2),(92,'INNER','0.08_KG_PKT','0.08 KG PKT','ACTIVE',0.08,'KG',2),(93,'INNER','0.05_KG_PKT','0.05 KG PKT','ACTIVE',0.05,'KG',2),(94,'INNER','0.06_KG_PKT','0.06 KG PKT','ACTIVE',0.06,'KG',2),(95,'INNER','0.07_KG_PKT','0.07 KG PKT','ACTIVE',0.07,'KG',2),(96,'INNER','0.09_KG_PKT','0.09 KG PKT','ACTIVE',0.09,'KG',2),(97,'INNER','0.04_KG_PKT','0.04 KG PKT','ACTIVE',0.04,'KG',2),(98,'INNER','0.798_KG_PKT','0.798 KG PKT','ACTIVE',0.80,'KG',2),(99,'INNER','0.860_KG_PKT','0.860 KG PKT','ACTIVE',0.86,'KG',2),(100,'CASE','4_PC_BOX','4 PC BOX','ACTIVE',4.00,'PC',3),(101,'INNER','6_PC_PKT','6 PC PKT','ACTIVE',6.00,'PC',3),(102,'INNER','0.5_KG_PKT','0.5 KG PKT','ACTIVE',0.50,'KG',2),(103,'INNER','0.5_PC_PKT','0.5 PC PKT','ACTIVE',0.50,'PC',3),(104,'INNER','0.45_KG_PKT','0.45 KG PKT','ACTIVE',0.45,'KG',2),(105,'INNER','9_PC_PKT','9 PC PKT','ACTIVE',9.00,'PC',3),(106,'INNER','4_PC_PKT','4 PC PKT','ACTIVE',4.00,'PC',3),(107,'INNER','12_PC_PKT','12 PC PKT','ACTIVE',12.00,'PC',3),(108,'INNER','0.001_KG_PKT','0.001 KG PKT','ACTIVE',0.00,'KG',2),(109,'INNER','60_PC_PKT','60 PC PKT','ACTIVE',60.00,'PC',3),(110,'INNER','8_PC_PKT','8 PC PKT','ACTIVE',8.00,'PC',3),(111,'INNER','0.5_L_PKT','0.5 L PKT','ACTIVE',0.50,'L',1),(112,'INNER','0.250_L_PKT','0.25 L PKT','ACTIVE',0.25,'L',1),(113,'INNER','0.2_PC_PKT','0.2 PC PKT','ACTIVE',0.20,'PC',3),(114,'INNER','28_PC_PKT','28 PC PKT','ACTIVE',28.00,'PC',3),(115,'LOOSE','SACHET','SACHET','ACTIVE',1.00,'SACHET',NULL),(116,'INNER','BOTTLE','BOTTLE','ACTIVE',1.00,'L',1),(117,'INNER','0.6_L_BOTTLE','0.6 L BOTTLE','ACTIVE',0.60,'L',118),(118,'CASE','0.6_L_24_BOTTLE_BOX','0.6 L 24 BOTTLE BOX','ACTIVE',14.40,'L',119),(119,'INNER','0.105_KG_PKT','0.105 KG PKT','ACTIVE',0.11,'KG',2),(120,'INNER','1_L_BOTTLE','1 L BOTTLE','ACTIVE',1.00,'L',118),(121,'CASE','1_L_4_BOTTLE_BOX','1 L 4 BOTTLE BOX','ACTIVE',4.00,'L',121),(122,'INNER','100_SACHET_PKT','200 SACHET PKT','ACTIVE',200.00,'SACHET',116),(123,'INNER','200_SACHET_PKT','200 SACHET PKT','ACTIVE',200.00,'SACHET',116),(124,'INNER','2_KG_PKT','2 KG PACKET','ACTIVE',2.00,'KG',3),(125,'CASE','0.623_L_12_PKT_BOX','0.623 L 12 PKT BOX','ACTIVE',7.48,'L',49);
/*!40000 ALTER TABLE `PACKAGING_DEFINITION` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PRODUCT_DEFINITION`
--

DROP TABLE IF EXISTS `PRODUCT_DEFINITION`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PRODUCT_DEFINITION` (
  `PRODUCT_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PRODUCT_NAME` varchar(255) NOT NULL,
  `PRODUCT_DESCRIPTION` varchar(1000) DEFAULT NULL,
  `CATEGORY_ID` int(11) NOT NULL,
  `SUPPORTS_LOOSE_ORDERING` varchar(1) NOT NULL,
  `CREATION_DATE` timestamp NULL DEFAULT NULL,
  `CREATED_BY` int(11) NOT NULL,
  `HAS_INNER` varchar(1) NOT NULL,
  `HAS_CASE` varchar(1) NOT NULL,
  `STOCK_KEEPING_FREQUENCY` varchar(15) NOT NULL,
  `PRODUCT_CODE` varchar(30) NOT NULL,
  `SHELF_LIFE_IN_DAYS` int(11) NOT NULL,
  `PRODUCT_STATUS` varchar(15) NOT NULL,
  `UNIT_OF_MEASURE` varchar(15) NOT NULL,
  `PARTICIPATES_IN_RECIPE` varchar(1) NOT NULL DEFAULT 'N',
  `VARIANT_LEVEL_ORDERING` varchar(1) NOT NULL DEFAULT 'N',
  `PRODUCT_IMAGE` varchar(255) DEFAULT NULL,
  `SUB_CATEGORY_ID` int(11) DEFAULT NULL,
  `SUPPORTS_SPECIALIZED_ORDERING` varchar(1) NOT NULL DEFAULT 'N',
  PRIMARY KEY (`PRODUCT_ID`),
  UNIQUE KEY `PRODUCT_NAME` (`PRODUCT_NAME`),
  KEY `PRODUCT_DEFINITION_CATEGORY_ID` (`CATEGORY_ID`),
  KEY `SUB_CATEGORY_ID` (`SUB_CATEGORY_ID`),
  CONSTRAINT `PRODUCT_DEFINITION_ibfk_1` FOREIGN KEY (`CATEGORY_ID`) REFERENCES `CATEGORY_DEFINITION` (`CATEGORY_ID`) ON UPDATE CASCADE,
  CONSTRAINT `PRODUCT_DEFINITION_ibfk_2` FOREIGN KEY (`SUB_CATEGORY_ID`) REFERENCES `SUB_CATEGORY_DEFINITION` (`SUB_CATEGORY_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=100513 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PRODUCT_DEFINITION`
--

LOCK TABLES `PRODUCT_DEFINITION` WRITE;
/*!40000 ALTER TABLE `PRODUCT_DEFINITION` DISABLE KEYS */;
INSERT INTO `PRODUCT_DEFINITION` VALUES (100001,'1 Sink Unit','1 Sink Unit',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','1',1,'ACTIVE','PC','N','N','',11,'N'),(100002,'2 Minutes Filling','2 Minutes Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','2',1,'ACTIVE','PC','Y','N','',20,'N'),(100003,'2 Sink Unit','2 Sink Unit',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','3',1,'ACTIVE','PC','N','N','',11,'N'),(100004,'250 Ml Sipper Lid','250 Ml Sipper Lid',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','4',1,'ACTIVE','PC','Y','N','',4,'N'),(100005,'3 Sink Unit','3 Sink Unit',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','5',1,'ACTIVE','PC','N','N','',11,'N'),(100006,'360 Ml Sipper Lid','360 Ml Sipper Lid',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','6',1,'ACTIVE','PC','Y','N','',4,'N'),(100007,'A4 Paper','A4 Paper Khanna',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','7',1,'ACTIVE','PC','N','N','',12,'N'),(100008,'Aam Papad','Aam Papad',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','8',1,'ACTIVE','KG','Y','N','',5,'N'),(100009,'AC','Ac',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','9',1,'ACTIVE','PC','N','N','',11,'N'),(100010,'Achari Butter','Achari Butter',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','10',1,'ACTIVE','KG','Y','N','',20,'N'),(100011,'Achari Chicken Samosa','Achari Chicken Samosa',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','11',1,'ACTIVE','PC','Y','N','',20,'N'),(100012,'Acrylic Milk Bottle','Acrylic Milk Bottle',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','12',1,'ACTIVE','PC','N','N','',10,'N'),(100013,'Acrylic Water Glass','Acrylic Water Glass',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','13',1,'ACTIVE','PC','N','N','',10,'N'),(100014,'Add On Stand - 4','Add On Stand - 4',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','14',1,'ACTIVE','PC','N','N','',11,'N'),(100015,'Add On Stand - 8','Add On Stand - 8',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','15',1,'ACTIVE','PC','N','N','',11,'N'),(100016,'Add On Box','Add Ons Box',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','16',1,'ACTIVE','PC','N','N','',11,'N'),(100017,'Ajwain','Ajwain',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','17',1,'ACTIVE','KG','Y','N','',5,'N'),(100018,'Jeera Cookies Pieces','Jeera Cookies Pieces',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','18',1,'ACTIVE','PC','Y','N','',1,'N'),(100019,'Badam','Badam',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','20',1,'ACTIVE','KG','Y','N','',5,'N'),(100020,'Bain Marie','Bain Marie',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','21',1,'ACTIVE','PC','N','N','',11,'N'),(100021,'Bain Marie Basket','Bain Marie Basket',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','22',1,'ACTIVE','PC','N','N','',11,'N'),(100022,'Bain Marie Display Stand','Bain Marie Display Stand',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','23',1,'ACTIVE','PC','N','N','',11,'N'),(100023,'Baking Powder','Baking Powder',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','24',1,'ACTIVE','KG','Y','N','',5,'N'),(100024,'Ball Pen','Ball Pen',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','25',1,'ACTIVE','PC','N','N','',12,'N'),(100025,'Balsamico Filling','Balsamico Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','26',1,'ACTIVE','PC','Y','N','',20,'N'),(100026,'Banana Cake Whole','Banana Cake Whole',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','27',1,'ACTIVE','PC','Y','N','',1,'N'),(100027,'Banana Filling','Banana Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','28',1,'ACTIVE','PC','Y','N','',20,'N'),(100028,'Banking Envelope','Banking Envelope',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','29',1,'ACTIVE','PC','N','N','',12,'N'),(100029,'Bhujia','Bhujia',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','30',1,'ACTIVE','KG','Y','N','',5,'N'),(100030,'Bin Liner 36*55 - Large','Bin Liner 36*55 - Large',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','31',1,'ACTIVE','PC','N','N','',11,'N'),(100031,'Bin Liner Medium','Bin Liner Medium',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','32',1,'ACTIVE','KG','N','N','',14,'N'),(100032,'Bin Liner Small','Bin Liner Small',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','33',1,'ACTIVE','KG','N','N','',14,'N'),(100033,'Black Board','Black Board',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','34',1,'ACTIVE','PC','N','N','',12,'N'),(100034,'Black Grape Filling','Black Grape Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','35',1,'ACTIVE','PC','Y','N','',20,'N'),(100035,'Black Pepper','Black Pepper',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','36',1,'ACTIVE','KG','Y','N','',5,'N'),(100036,'Black Salt','Black Salt',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','37',1,'ACTIVE','KG','Y','N','',5,'N'),(100037,'Blackboard Stand','Blackboard Stand',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','38',1,'ACTIVE','PC','N','N','',11,'N'),(100038,'Blueberry Cake Whole','Blueberry Cake Whole',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','39',1,'ACTIVE','PC','Y','N','',1,'N'),(100039,'Boiler Element','Boiler Element',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','40',1,'ACTIVE','PC','N','N','',11,'N'),(100040,'Bolt Pronto Oven','Bolt Pronto Oven',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','41',1,'ACTIVE','PC','N','N','',11,'N'),(100041,'Bolt Tray','Bolt Tray',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','42',1,'ACTIVE','PC','N','N','',11,'N'),(100042,'Bottle Cleaning Brush','Bottle Cleaning Brush',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','43',1,'ACTIVE','PC','N','N','',14,'N'),(100043,'Bottle Stand - Cold Station','Bottle Stand - Cold Station',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','44',1,'ACTIVE','PC','N','N','',11,'N'),(100044,'Bournvita','Bournvita',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','45',1,'ACTIVE','KG','Y','N','',5,'N'),(100045,'Box File','Box File',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','46',1,'ACTIVE','PC','N','N','',12,'N'),(100046,'Bread Box','Bread Box',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','47',1,'ACTIVE','PC','N','N','',11,'N'),(100047,'Bread Knife','Bread Knife',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','48',1,'ACTIVE','PC','N','N','',10,'N'),(100048,'Broom Hard','Broom Hard',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','49',1,'ACTIVE','PC','N','N','',14,'N'),(100049,'Brown Tape','Brown Tape',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','50',1,'ACTIVE','PC','N','N','',12,'N'),(100050,'Bubble Wrap Sheet','Bubble Wrap Sheet',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','51',1,'ACTIVE','PC','Y','N','',4,'N'),(100051,'Built Too : Cold Station Setup','Built Too : Cold Station Setup',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','52',1,'ACTIVE','PC','N','N','',12,'N'),(100052,'Built Too : Food Station Setup','Built Too : Food Station Setup',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','53',1,'ACTIVE','PC','N','N','',12,'N'),(100053,'Built Too : Service Area','Built Too : Service Area',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','54',1,'ACTIVE','PC','N','N','',12,'N'),(100054,'Built Too: Hot Station Setup','Built Too: Hot Station Setup',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','55',1,'ACTIVE','PC','N','N','',12,'N'),(100055,'Butter','Butter',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','56',1,'ACTIVE','KG','Y','N','',3,'N'),(100056,'Butter Bowl','Butter Bowl',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','57',1,'ACTIVE','PC','N','N','',10,'N'),(100057,'Butter Chicken Filling','Butter Chicken Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','58',1,'ACTIVE','PC','Y','N','',20,'N'),(100058,'Butter Knife','Butter Knife',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','59',1,'ACTIVE','PC','N','N','',10,'N'),(100059,'Butter Paper Non-Veg','Butter Paper Non-Veg',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','60',1,'ACTIVE','PC','Y','N','',4,'N'),(100060,'Butter Paper Veg','Butter Paper Veg',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','61',1,'ACTIVE','PC','Y','N','',4,'N'),(100061,'C Fold Dispenser','C Fold Dispenser',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','62',1,'ACTIVE','PC','N','N','',11,'N'),(100062,'C Fold Tissue','C/Fold Tissue',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','63',1,'ACTIVE','PC','Y','N','',4,'N'),(100063,'Cake Brush','Cake Brush',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','64',1,'ACTIVE','PC','N','N','',10,'N'),(100064,'Calculator','Calculator',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','65',1,'ACTIVE','PC','N','N','',12,'N'),(100065,'Candle','Candle',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','66',1,'ACTIVE','PC','N','N','',11,'N'),(100066,'Capsicum Julienne','Capsicum',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','67',1,'ACTIVE','KG','Y','N','',9,'N'),(100067,'Carbon Paper','Carbon Paper',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','68',1,'ACTIVE','PC','N','N','',12,'N'),(100068,'Cardamom Black','Cardamom Black',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','69',1,'ACTIVE','KG','Y','N','',5,'N'),(100069,'Cardamom Green','Cardamom Green',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','70',1,'ACTIVE','KG','Y','N','',5,'N'),(100070,'Carrot Cake Whole','Carrot Cake Whole',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','71',1,'ACTIVE','PC','Y','N','',1,'N'),(100071,'Cash Drawer','Cash Drawer',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','72',1,'ACTIVE','PC','N','N','',11,'N'),(100072,'Cash Handover Sheet & Daily Cash Pickup Register','Cash Handover Sheet & Daily Cash Pickup Register',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','73',1,'ACTIVE','PC','N','N','',12,'N'),(100073,'Cash Voucher Pad','Cash Voucher Pad',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','74',1,'ACTIVE','PC','N','N','',12,'N'),(100074,'Ceramic Mug Medium','Ceramic Mug Medium',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','75',1,'ACTIVE','PC','N','N','',10,'N'),(100075,'Ceramic Mug Small','Ceramic Mug Small',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','76',1,'ACTIVE','PC','N','N','',10,'N'),(100076,'Chai Delivery  Box 1 L','Chai delivery  Box 1 Ltr',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','77',1,'ACTIVE','PC','Y','N','',4,'N'),(100077,'Chai Masala','Chai Masala',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','78',1,'ACTIVE','KG','Y','N','',20,'N'),(100078,'Chai Sachet Container','Chai Sachet Container',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','79',1,'ACTIVE','PC','N','N','',11,'N'),(100079,'Chalk','Chalk',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','80',1,'ACTIVE','PC','N','N','',12,'N'),(100080,'Chat Masala','Chat Masala',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','81',1,'ACTIVE','KG','Y','N','',5,'N'),(100081,'Chatpata Kebab Filling','Chatpata Kebab Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','82',1,'ACTIVE','PC','Y','N','',20,'N'),(100082,'Chikoo Filling','Chikoo Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','83',1,'ACTIVE','PC','Y','N','',20,'N'),(100083,'Chocolate Bun Filling','Chocolate Bun Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','84',1,'ACTIVE','PC','Y','N','',20,'N'),(100084,'Chocolate Drinking Mix','Chocolate Drinking Mix',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','85',1,'ACTIVE','KG','Y','N','',5,'N'),(100085,'Chocolate Syrup','Chocolate Syrup',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','86',1,'ACTIVE','L','Y','N','',5,'N'),(100086,'Chopping Board - Non Veg','Chopping Board - Non Veg',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','87',1,'ACTIVE','PC','N','N','',10,'N'),(100087,'Chopping Board - Veg','Chopping Board - Veg',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','88',1,'ACTIVE','PC','N','N','',10,'N'),(100088,'Cinnamon Roll','Cinnamon Roll',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','89',1,'ACTIVE','KG','Y','N','',5,'N'),(100089,'Citronella Oil','Citronella Oil',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','90',1,'ACTIVE','L','N','N','',14,'N'),(100090,'Clear Tape 1\"','Clear Tape 1\"',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','91',1,'ACTIVE','PC','N','N','',12,'N'),(100091,'Clear Tape 2\"','Clear Tape 2\"',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','92',1,'ACTIVE','PC','N','N','',12,'N'),(100092,'Cling Wrap','Cling Wrap',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','93',1,'ACTIVE','PC','Y','N','',4,'N'),(100093,'Clove Whole','Clove Whole',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','94',1,'ACTIVE','KG','Y','N','',5,'N'),(100094,'Cobra File','Cobra File',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','95',1,'ACTIVE','PC','N','N','',12,'N'),(100095,'Coffee Delight','Coffee Delight',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','96',1,'ACTIVE','L','Y','N','',5,'N'),(100096,'Coffee Powder','Coffee Powder',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','97',1,'ACTIVE','KG','Y','N','',5,'N'),(100097,'Cold Cup 350 ML','Cold Cup 350 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','98',1,'ACTIVE','PC','Y','N','',4,'N'),(100098,'Cold Cup Lid','Cold Cup Lid',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','99',1,'ACTIVE','PC','Y','N','',4,'N'),(100099,'Cold Cups 500 ML','Cold Cups 500 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','100',1,'ACTIVE','PC','Y','N','',4,'N'),(100100,'Condiment Box','Condiment Box',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','101',1,'ACTIVE','PC','N','N','',10,'N'),(100101,'Container 1 L','Container 1 L',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','102',1,'ACTIVE','PC','N','N','',11,'N'),(100102,'Container 1.5 L','Container 1.5L',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','103',1,'ACTIVE','PC','N','N','',10,'N'),(100103,'Container 10 L','Container 10 L',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','104',1,'ACTIVE','PC','N','N','',11,'N'),(100104,'Container 2 L','Container 2 L',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','105',1,'ACTIVE','PC','N','N','',11,'N'),(100105,'Container 200 ML','Container 200 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','106',1,'ACTIVE','PC','N','N','',11,'N'),(100106,'Container 3.8 L','Container 3.8 L',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','107',1,'ACTIVE','PC','N','N','',11,'N'),(100107,'Container 5 L','Container 5 L',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','108',1,'ACTIVE','PC','N','N','',11,'N'),(100108,'Container 500 ML','Container 500Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','109',1,'ACTIVE','PC','N','N','',11,'N'),(100109,'Conveyer Toaster','Conveyer Toaster',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','110',1,'ACTIVE','PC','N','N','',11,'N'),(100110,'Correction Pen','Correction Pen',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','112',1,'ACTIVE','PC','N','N','',12,'N'),(100111,'Counter Top','Counter Top',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','113',1,'ACTIVE','PC','N','N','',11,'N'),(100112,'Croissant','Croissant',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','114',1,'ACTIVE','PC','Y','N','',1,'N'),(100113,'Cucumber Slices','Cucumber',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','115',1,'ACTIVE','KG','Y','N','',9,'N'),(100114,'Cup Holder','Cup Holder',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','116',1,'ACTIVE','PC','Y','N','',4,'N'),(100115,'Cup Stand - Cold Station','Cup Stand - Cold Station',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','117',1,'ACTIVE','PC','N','N','',11,'N'),(100116,'Cup Stand - Hot Station','Cup Stand - Hot Station',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','118',1,'ACTIVE','PC','N','N','',11,'N'),(100117,'Cutting Chai Glass','Cutting Chai Glass',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','119',1,'ACTIVE','PC','N','N','',10,'N'),(100118,'D10','D10',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','120',1,'ACTIVE','L','N','N','',14,'N'),(100119,'Daily Checklist Book (Gold Standard)','Daily Checklist Book (Gold Standard)',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','121',1,'ACTIVE','PC','N','N','',12,'N'),(100120,'Darjeeling First Flush Patti','Darjeeling First Flush Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','122',1,'ACTIVE','KG','Y','N','',2,'N'),(100121,'Day Part Plan','Day Part Plan',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','123',1,'ACTIVE','PC','N','N','',12,'N'),(100122,'Desi Chai Can','Desi Chai Can',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','124',90,'ACTIVE','PC','Y','N','',2,'N'),(100123,'Desi Chai Patti','Desi Chai Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','125',1,'ACTIVE','KG','Y','N','',2,'N'),(100124,'Diesel','Diesel',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','126',1,'ACTIVE','L','N','N','',14,'N'),(100125,'Diffuser','Diffuser',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','127',1,'ACTIVE','PC','N','N','',14,'N'),(100126,'Dip Bottle','Dip Bottle',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','128',1,'ACTIVE','PC','N','N','',11,'N'),(100127,'Dip Container With Lid','Dip Container With Lid',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','129',1,'ACTIVE','PC','Y','N','',4,'N'),(100128,'Dip Cup','Dip Cup',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','130',1,'ACTIVE','PC','Y','N','',4,'N'),(100129,'Display Basket','Display Basket',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','131',1,'ACTIVE','PC','N','N','',14,'N'),(100130,'Disposable Cap','Disposable Cap',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','132',1,'ACTIVE','PC','Y','N','',4,'N'),(100131,'To Go Tray Mat','TO GO Tray Mat',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','133',1,'ACTIVE','PC','Y','N','',4,'N'),(100132,'Dome Cover','Dome Cover',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','134',1,'ACTIVE','PC','N','N','',11,'N'),(100133,'Doormat','Doormat',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','135',1,'ACTIVE','PC','N','N','',12,'N'),(100134,'Double Side Tape 1\"','Double Side Tape 1\"',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','136',1,'ACTIVE','PC','N','N','',12,'N'),(100135,'Dust Control Mop Head','Dust Control Mop Head',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','137',1,'ACTIVE','PC','N','N','',14,'N'),(100136,'Dust Control Mop Rod','Dust Control Mop Rod',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','138',1,'ACTIVE','PC','N','N','',14,'N'),(100137,'Dust Pan','Dust Pan',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','139',1,'ACTIVE','PC','N','N','',14,'N'),(100138,'Dustbin Padal','Dustbin Padal',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','140',1,'ACTIVE','PC','N','N','',11,'N'),(100139,'Duster','Duster',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','141',1,'ACTIVE','PC','N','N','',14,'N'),(100140,'Dustpan With Broom','Dustpan With Broom',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','142',1,'ACTIVE','PC','N','N','',14,'N'),(100141,'Earl Grey Patti','Earl Grey Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','143',1,'ACTIVE','KG','Y','N','',2,'N'),(100142,'Egg Bun Filling','Egg Bun Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','144',1,'ACTIVE','PC','Y','N','',20,'N'),(100143,'Electronic Safe','Electronic Safe',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','145',1,'ACTIVE','PC','N','N','',11,'N'),(100144,'English Breakfast Patti','English Breakfast Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','146',1,'ACTIVE','KG','Y','N','',2,'N'),(100145,'English Oven Bun','English Oven Bun',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','147',1,'ACTIVE','PC','Y','N','',1,'N'),(100146,'Eraser','Eraser',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','148',1,'ACTIVE','PC','N','N','',12,'N'),(100147,'Extension Board','Extension Board',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','149',1,'ACTIVE','PC','N','N','',11,'N'),(100148,'Fire Extinguisher 2 Kg','Fire Extinguisher 2Kg',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','150',1,'ACTIVE','PC','N','N','',11,'N'),(100149,'Fire Extinguisher 4 Kg','Fire Extinguisher 4Kg',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','151',1,'ACTIVE','PC','N','N','',11,'N'),(100150,'Fly Catcher Pad','Fly Catcher Pad',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','152',1,'ACTIVE','PC','N','N','',11,'N'),(100151,'Fly Catcher Tubelight','Fly Catcher Tubelight',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','153',1,'ACTIVE','PC','N','N','',11,'N'),(100152,'Fly Killer Machine','Fly Killer Machine',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','154',1,'ACTIVE','PC','N','N','',11,'N'),(100153,'Focaccia','Focaccia',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','155',1,'ACTIVE','PC','Y','N','',1,'N'),(100154,'Food Temperature Thermometer','Food Temperature Thermometer',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','156',1,'ACTIVE','PC','N','N','',14,'N'),(100155,'Fridge','Fridge',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','157',1,'ACTIVE','PC','N','N','',11,'N'),(100156,'Frozen Tortilla','Frozen Tortilla',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','158',1,'ACTIVE','PC','Y','N','',1,'N'),(100157,'Funnel','Funnel',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','159',1,'ACTIVE','PC','N','N','',11,'N'),(100158,'Gental Soap','Gental Soap',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','161',1,'ACTIVE','PC','N','N','',14,'N'),(100159,'Ginger Slices','Ginger',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','162',1,'ACTIVE','KG','Y','N','',9,'N'),(100160,'Ginger Bits','Ginger Bits',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','163',1,'ACTIVE','KG','Y','N','',5,'N'),(100161,'Glass Cleaning Handle','Glass Cleaning Handle',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','164',1,'ACTIVE','PC','N','N','',14,'N'),(100162,'Glass Cleaning Kit','Glass Cleaning Kit',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','165',1,'ACTIVE','PC','N','N','',14,'N'),(100163,'Glass Jar','Glass Jar',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','166',1,'ACTIVE','PC','N','N','',10,'N'),(100164,'Coloured Glasses','Coloured glasses',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','167',1,'ACTIVE','PC','N','N','',11,'N'),(100165,'Glue Stick','Glue Stick',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','168',1,'ACTIVE','PC','N','N','',12,'N'),(100166,'Gods Chai Patti','Gods Chai Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','169',1,'ACTIVE','KG','Y','N','',2,'N'),(100167,'Green Chicken Filling','Green Chicken Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','170',1,'ACTIVE','PC','Y','N','',20,'N'),(100168,'Green Chilli 1 inch','Green Chilli',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','171',1,'ACTIVE','KG','Y','N','',9,'N'),(100169,'Green Chilli Mint Mayo Sauce','Green Chilli Mint Mayo Sauce',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','172',1,'ACTIVE','KG','Y','N','',20,'N'),(100170,'Green Tea Can','Green Tea Can',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','173',1,'ACTIVE','PC','Y','N','',2,'N'),(100171,'Green Tea Patti','Green Tea Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','174',1,'ACTIVE','KG','Y','N','',2,'N'),(100172,'Griller','Griller',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','175',1,'ACTIVE','PC','N','N','',11,'N'),(100173,'Grooming Kit','Grooming Kit',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','176',1,'ACTIVE','PC','N','N','',14,'N'),(100174,'Hand Gloves','Hand Gloves',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','177',1,'ACTIVE','PC','Y','N','',4,'N'),(100175,'Hand Wash','Hand Wash',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','178',1,'ACTIVE','L','N','N','',14,'N'),(100176,'Handheld Menu Stand','Handheld Menu Stand',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','179',1,'ACTIVE','PC','N','N','',11,'N'),(100177,'Hari Chuttney','Hari Chuttney',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','180',1,'ACTIVE','KG','Y','N','',20,'N'),(100178,'Toilet Cleaner','Toilet Cleaner',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','181',1,'ACTIVE','PC','N','N','',14,'N'),(100179,'Homestyle Aloo Filling','Homestyle Aloo Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','182',1,'ACTIVE','PC','Y','N','',20,'N'),(100180,'Honey','Honey',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','183',1,'ACTIVE','KG','Y','N','',5,'N'),(100181,'Honey Garlic Mayo Sauce','Honey Garlic Mayo Sauce',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','184',1,'ACTIVE','KG','Y','N','',20,'N'),(100182,'Honey Lemon Butter','Honey Lemon Butter',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','185',1,'ACTIVE','KG','Y','N','',20,'N'),(100183,'Hot Cup 150 ML','Hot Cup 150 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','186',1,'ACTIVE','PC','Y','N','',4,'N'),(100184,'Hot Cup 250 ML','Hot Cup 250 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','187',1,'ACTIVE','PC','Y','N','',4,'N'),(100185,'Hot Cups 360 ML','Hot Cups 360 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','188',1,'ACTIVE','PC','Y','N','',4,'N'),(100186,'Ice','Ice',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','189',1,'ACTIVE','KG','Y','N','',8,'N'),(100187,'Ice Machine','Ice Machine',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','190',1,'ACTIVE','PC','N','N','',11,'N'),(100188,'Ice Tray','Ice Tray',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','191',1,'ACTIVE','PC','N','N','',10,'N'),(100189,'Induction Plate','Induction Plate',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','192',1,'ACTIVE','PC','N','N','',11,'N'),(100190,'Italian Burger Bun','Italian Burger Bun',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','193',1,'ACTIVE','PC','Y','N','',1,'N'),(100191,'Jasmine Tea Patti','Jasmine Tea Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','194',1,'ACTIVE','KG','Y','N','',2,'N'),(100192,'Jumbo Bread','Jumbo Bread',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','195',1,'ACTIVE','PC','Y','N','',1,'N'),(100193,'Kadhai Paneer Filling','Kadhai Paneer Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','196',1,'ACTIVE','PC','Y','N','',20,'N'),(100194,'Kashmiri Kahwa Patti','Kashmiri Kahwa Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','198',1,'ACTIVE','KG','Y','N','',2,'N'),(100195,'Keema Pav Filling','Keema Pav Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','199',1,'ACTIVE','PC','Y','N','',20,'N'),(100196,'Kesar','Kesar',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','200',1,'ACTIVE','PC','Y','N','',5,'N'),(100197,'Ketchup Sachet','Ketchup Sachet',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','201',1,'ACTIVE','SACHET','Y','N','',5,'N'),(100198,'Key Ring','Key Ring',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','202',1,'ACTIVE','PC','N','N','',12,'N'),(100199,'Keyboard','Keyboard',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','203',1,'ACTIVE','PC','N','N','',17,'N'),(100200,'Kitchen Knife','Kitchen Knife',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','204',1,'ACTIVE','PC','N','N','',10,'N'),(100201,'Kiwi Filling','Kiwi Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','205',1,'ACTIVE','PC','Y','N','',20,'N'),(100202,'Kulhad 120 ML','Kulhad 120 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','206',1,'ACTIVE','PC','Y','N','',4,'N'),(100203,'Kulhad 250 ML','Kulhad 250 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','207',1,'ACTIVE','PC','Y','N','',4,'N'),(100204,'Landline','Landline',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','208',1,'ACTIVE','PC','N','N','',17,'N'),(100205,'Lasoon Chuttney','Lasoon Chuttney',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','209',1,'ACTIVE','KG','Y','N','',20,'N'),(100206,'Lemon whole','Lemon',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','210',1,'ACTIVE','KG','Y','N','',9,'N'),(100207,'Lemon Cake Whole','Lemon Cake Whole',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','211',1,'ACTIVE','PC','Y','N','',1,'N'),(100208,'Lemon Grass Patti','Lemon Grass Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','212',1,'ACTIVE','KG','Y','N','',2,'N'),(100209,'Lemon Juice Machine','Lemon Juice Machine',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','213',1,'ACTIVE','PC','N','N','',11,'N'),(100210,'Lemon Powder','Lemon Powder',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','214',1,'ACTIVE','PC','Y','N','',5,'N'),(100211,'Lopchu Patti','Lopchu Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','215',1,'ACTIVE','KG','Y','N','',2,'N'),(100212,'M Fold Hand Towel','M Fold Hand Towel',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','216',1,'ACTIVE','PC','N','N','',11,'N'),(100213,'Magic Broom','Magic Broom',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','217',1,'ACTIVE','PC','N','N','',14,'N'),(100214,'Manager Laptop','Manager Laptop',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','218',1,'ACTIVE','PC','N','N','',17,'N'),(100215,'Manager Shirt','Manager Shirt',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','219',1,'ACTIVE','PC','N','N','',13,'N'),(100216,'Mango Filling','Mango Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','220',1,'ACTIVE','PC','Y','N','',20,'N'),(100217,'Manual Bill Book','Manual Bill Book',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','221',1,'ACTIVE','PC','N','N','',12,'N'),(100218,'Masala Chai Can','Masala Chai Can',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','222',1,'ACTIVE','PC','Y','N','',2,'N'),(100219,'Measuring Beaker 100 ML','Measuring Beaker 100Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','223',1,'ACTIVE','PC','N','N','',11,'N'),(100220,'Measuring Beaker 115 ML','Measuring Beaker 115 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','224',1,'ACTIVE','PC','N','N','',11,'N'),(100221,'Measuring Beaker 165 ML','Measuring Beaker 165 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','225',1,'ACTIVE','PC','N','N','',11,'N'),(100222,'Measuring Beaker 230 ML','Measuring Beaker 230 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','226',1,'ACTIVE','PC','N','N','',11,'N'),(100223,'Measuring Beaker 250 ML','Measuring Beaker 250Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','227',1,'ACTIVE','PC','N','N','',11,'N'),(100224,'Measuring Beaker 330 ML','Measuring Beaker 330 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','228',1,'ACTIVE','PC','N','N','',11,'N'),(100225,'Measuring Beaker 460 ML','Measuring Beaker 460 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','229',1,'ACTIVE','PC','N','N','',11,'N'),(100226,'Measuring Beaker 50 ML','Measuring Beaker 50 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','230',1,'ACTIVE','PC','N','N','',11,'N'),(100227,'Measuring Beaker 575 ML','Measuring Beaker 575 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','231',1,'ACTIVE','PC','N','N','',11,'N'),(100228,'Measuring Jar 1 L','Measuring Jar 1 Ltr',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','232',1,'ACTIVE','PC','N','N','',11,'N'),(100229,'Measuring Jar 2 L','Measuring Jar 2 Ltr',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','233',1,'ACTIVE','PC','N','N','',11,'N'),(100230,'Measuring Spoon Set','Measuring Spoon Set',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','234',1,'ACTIVE','PC','N','N','',11,'N'),(100231,'Microwave Oven','Microwave Oven',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','235',1,'ACTIVE','PC','N','N','',11,'N'),(100232,'Microwave Bowl - Non Veg','Microwave Bowl - Non Veg',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','236',1,'ACTIVE','PC','N','N','',10,'N'),(100233,'Microwave Bowl - Veg','Microwave Bowl - Veg',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','237',1,'ACTIVE','PC','N','N','',10,'N'),(100234,'Milk','Milk',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','238',1,'ACTIVE','L','Y','Y','',3,'N'),(100235,'Mineral Water Jar 20 L','Mineral Water Jar 20 Ltr',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','239',1,'ACTIVE','PC','N','N','',14,'N'),(100236,'Mint And Jalapeno Butter','Mint And Jalapeno Butter',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','240',1,'ACTIVE','KG','Y','N','',20,'N'),(100237,'Mint Green Patti','Mint Green Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','241',1,'ACTIVE','KG','Y','N','',2,'N'),(100238,'Mint Leaves','Mint Leaves',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','242',1,'ACTIVE','KG','Y','N','',9,'N'),(100239,'Mixer Grinder','Mixer Grinder',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','243',1,'ACTIVE','PC','N','N','',11,'N'),(100240,'Mobile','Mobile',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','244',1,'ACTIVE','PC','N','N','',17,'N'),(100241,'Passion Fruit Puree','Passion Fruit Puree',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','245',1,'ACTIVE','L','Y','N','',5,'N'),(100242,'Peach Puree','Peach Puree',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','246',1,'ACTIVE','L','Y','N','',5,'N'),(100243,'Mop Trolly','Mop Trolly',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','247',1,'ACTIVE','PC','N','N','',14,'N'),(100244,'Mouse','Mouse',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','248',1,'ACTIVE','PC','N','N','',17,'N'),(100245,'Mud Sticker','Mud Sticker',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','249',1,'ACTIVE','PC','Y','N','',4,'N'),(100246,'Multigrain Bread','Multigrain Bread',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','250',1,'ACTIVE','PC','Y','N','',1,'N'),(100247,'Muscatel Patti','Muscatel Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','251',1,'ACTIVE','KG','Y','N','',2,'N'),(100248,'Music System','Music System',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','252',1,'ACTIVE','PC','N','N','',11,'N'),(100249,'Mutton Lazeez Filling','Mutton Lazeez Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','253',1,'ACTIVE','PC','Y','N','',20,'N'),(100250,'Napoli Filling','Napoli Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','254',1,'ACTIVE','PC','Y','N','',20,'N'),(100251,'Room Freshner Bar','Room Freshner Bar',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','255',1,'ACTIVE','PC','N','N','',14,'N'),(100252,'Oil & Grease Trap','Oil & Grease Trap',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','256',1,'ACTIVE','PC','N','N','',11,'N'),(100253,'Olive And Sundried Tomato Butter','Olive And Sundried Tomato Butter',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','257',1,'ACTIVE','KG','Y','N','',20,'N'),(100254,'Onion Julienne','Onion',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','258',1,'ACTIVE','KG','Y','N','',9,'N'),(100255,'Orange Pekoe Patti','Orange Pekoe Patti',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','259',1,'ACTIVE','KG','Y','N','',2,'N'),(100256,'Paneer Khurchan Samosa','Paneer Khurchan Samosa',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','260',1,'ACTIVE','PC','Y','N','',20,'N'),(100257,'Parfait Spoon','Parfait Spoon',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','261',1,'ACTIVE','PC','N','N','',11,'N'),(100258,'Pastry Tong','Pastry Tong',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','262',1,'ACTIVE','PC','N','N','',11,'N'),(100259,'Pav','Pav',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','263',1,'ACTIVE','PC','Y','N','',1,'N'),(100260,'Pen Drive 8 Gb','Pen Drive 8 Gb',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','264',1,'ACTIVE','PC','N','N','',17,'N'),(100261,'Pencil','Pencil',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','265',1,'ACTIVE','PC','N','N','',12,'N'),(100262,'Pepper Chicken Filling','Pepper Chicken Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','266',1,'ACTIVE','PC','Y','N','',20,'N'),(100263,'Permanet Marker','Permanet Marker',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','267',1,'ACTIVE','PC','N','N','',12,'N'),(100264,'Phool Broom','Phool Broom',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','268',1,'ACTIVE','PC','N','N','',14,'N'),(100265,'Plastic Chair','Plastic Chair',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','269',1,'ACTIVE','PC','N','N','',11,'N'),(100266,'Plastic Poha Container','Plastic Poha Container',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','270',1,'ACTIVE','PC','N','N','',11,'N'),(100267,'Plastic Spoon','Plastic Spoon',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','271',1,'ACTIVE','PC','Y','N','',4,'N'),(100268,'Pocha','Pocha',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','272',1,'ACTIVE','PC','N','N','',14,'N'),(100269,'Poha Box','Poha Box',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','273',1,'ACTIVE','PC','Y','N','',4,'N'),(100270,'Poha Box Lid','Poha Box Lid',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','274',1,'ACTIVE','PC','Y','N','',4,'N'),(100271,'Poha Dry','Poha Dry',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','275',1,'ACTIVE','KG','Y','N','',5,'N'),(100272,'Poha Masala','Poha Masala',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','276',1,'ACTIVE','PC','Y','N','',20,'N'),(100273,'Poha Plate','Poha Plate',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','277',1,'ACTIVE','PC','Y','N','',4,'N'),(100274,'Polythene Pp','Polythene PP',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','278',1,'ACTIVE','KG','Y','N','',4,'N'),(100275,'Pos Laptop','Pos Laptop',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','279',1,'ACTIVE','PC','N','N','',17,'N'),(100276,'Printer','Printer',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','281',1,'ACTIVE','PC','N','N','',17,'N'),(100277,'Printer Roll','Printer Roll',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','282',1,'ACTIVE','PC','N','N','',12,'N'),(100278,'Pullover - Large','Pullover (Large)',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','283',1,'ACTIVE','PC','N','N','',13,'N'),(100279,'Pullover - Medium','Pullover (Medium)',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','284',1,'ACTIVE','PC','N','N','',13,'N'),(100280,'Pullover - Small','Pullover (Small)',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','285',1,'ACTIVE','PC','N','N','',13,'N'),(100281,'Pullover - XL','Pullover (Xl)',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','286',1,'ACTIVE','PC','N','N','',13,'N'),(100282,'Pump 10 ML Monin','Pump 10 Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','287',1,'ACTIVE','PC','N','N','',11,'N'),(100283,'Pump 15 ML','Pumps 15Ml',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','288',1,'ACTIVE','PC','N','N','',11,'N'),(100284,'Punching Machine','Punching Machine',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','289',1,'ACTIVE','PC','N','N','',12,'N'),(100285,'R1','R1',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','290',1,'ACTIVE','PC','N','N','',14,'N'),(100286,'R2 - Floor Cleaner','R2 - Floor Cleaner',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','291',1,'ACTIVE','L','N','N','',14,'N'),(100287,'R3','R3',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','292',1,'ACTIVE','L','N','N','',14,'N'),(100288,'R4','R4',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','293',1,'ACTIVE','L','N','N','',14,'N'),(100289,'Red Chilli Mayo Sauce','Red Chilli Mayo Sauce',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','294',1,'ACTIVE','KG','Y','N','',20,'N'),(100290,'Register','Register',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','295',1,'ACTIVE','PC','N','N','',12,'N'),(100291,'RO - 15 L','RO - 15 L',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','296',1,'ACTIVE','PC','N','N','',11,'N'),(100292,'RO - 25 L','RO - 25 L',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','297',1,'ACTIVE','PC','N','N','',11,'N'),(100293,'Room Freshner','Room Freshner',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','300',1,'ACTIVE','L','N','N','',14,'N'),(100294,'Rose Water','Rose Water',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','301',1,'ACTIVE','PC','Y','N','',5,'N'),(100295,'Rubber  Stamp','Rubber  Stamp',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','302',1,'ACTIVE','PC','N','N','',12,'N'),(100296,'Rubber Band','Rubber Band',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','303',1,'ACTIVE','PC','N','N','',12,'N'),(100297,'Rusk Pack','Rusk Pack',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','304',1,'ACTIVE','PC','Y','N','',5,'N'),(100298,'Sachet - Desi Regular','Sachet - Desi Regular',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','305',1,'ACTIVE','SACHET','Y','N','',5,'N'),(100299,'Saunf','Saunf',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','306',1,'ACTIVE','KG','Y','N','',5,'N'),(100300,'Scissor','Scissor',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','307',1,'ACTIVE','PC','N','N','',11,'N'),(100301,'Scoop 1/4','Scoop 1/4',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','308',1,'ACTIVE','PC','N','N','',10,'N'),(100302,'Scotch Bright','Scotch Bright',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','309',1,'ACTIVE','PC','N','N','',14,'N'),(100303,'Service Tray','Service Tray',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','310',1,'ACTIVE','PC','N','N','',10,'N'),(100304,'Sharpner','Sharpner',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','311',1,'ACTIVE','PC','N','N','',12,'N'),(100305,'Shikanji Masala','Shikanji Masala',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','312',1,'ACTIVE','KG','Y','N','',5,'N'),(100306,'Sicilian Chicken Filling','Sicilian Chicken Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','313',1,'ACTIVE','PC','Y','N','',20,'N'),(100307,'Sign Board - Wet Floor','Sign Board - Wet Floor',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','314',1,'ACTIVE','PC','N','N','',14,'N'),(100308,'Silver Cutting','Silver Cutting',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','315',1,'ACTIVE','PC','Y','N','',4,'N'),(100309,'Silver Foil','Silver Foil',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','316',1,'ACTIVE','PC','Y','N','',4,'N'),(100310,'Sink Mesh Jali','Sink Mesh Jali',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','317',1,'ACTIVE','PC','N','N','',11,'N'),(100311,'Soap Dispenser','Soap Dispenser',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','318',1,'ACTIVE','PC','N','N','',11,'N'),(100312,'Soda','Soda',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','319',1,'ACTIVE','L','Y','N','',5,'N'),(100313,'Spinach Corn Cheese Filling','Spinach Corn Cheese Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','320',1,'ACTIVE','PC','Y','N','',20,'N'),(100314,'Spiral Note Book','Spiral Note Book',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','321',1,'ACTIVE','PC','N','N','',12,'N'),(100315,'Sponge Wipe','Sponge Wipe',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','322',1,'ACTIVE','PC','N','N','',14,'N'),(100316,'Spoon Ss Big','Spoon Ss Big',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','323',1,'ACTIVE','PC','N','N','',10,'N'),(100317,'Spoon Ss Small','Spoon Ss Small',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','324',1,'ACTIVE','PC','N','N','',10,'N'),(100318,'Spray','Spray',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','325',1,'ACTIVE','PC','N','N','',14,'N'),(100319,'Spray Bottle','Spray Bottle',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','326',1,'ACTIVE','PC','N','N','',14,'N'),(100320,'Sprinkler','Sprinkler',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','327',1,'ACTIVE','PC','N','N','',10,'N'),(100321,'Square Dustbin','Square Dustbin',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','328',1,'ACTIVE','PC','N','N','',11,'N'),(100322,'Squeeze Bottle','Squeeze Bottle',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','329',1,'ACTIVE','PC','N','N','',11,'N'),(100323,'Steel Scale','Steel Scale',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','330',1,'ACTIVE','PC','N','N','',12,'N'),(100324,'Steel Scruber','Steel Scruber',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','331',1,'ACTIVE','PC','N','N','',14,'N'),(100325,'Sticker - Non Veg','Sticker - Non Veg',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','332',1,'ACTIVE','PC','Y','N','',4,'N'),(100326,'Sticker - Veg','Sticker - Veg',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','333',1,'ACTIVE','PC','Y','N','',4,'N'),(100327,'Stirrer','Stirrer',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','334',1,'ACTIVE','PC','Y','N','',4,'N'),(100328,'Straw','Straw',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','335',1,'ACTIVE','PC','Y','N','',4,'N'),(100329,'Strawberry Filling','Strawberry Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','336',1,'ACTIVE','PC','Y','N','',20,'N'),(100330,'Sugar','Sugar',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','337',1,'ACTIVE','KG','Y','N','',5,'N'),(100331,'Sugar Free Sachet','Sugar Free Sachet',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','338',1,'ACTIVE','SACHET','Y','N','',5,'N'),(100332,'Sugar Sachet - Brown','Sugar Sachet - Brown',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','339',1,'ACTIVE','SACHET','Y','N','',5,'N'),(100333,'Sugar Sachet - White','Sugar Sachet - White',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','340',1,'ACTIVE','SACHET','Y','N','',5,'N'),(100334,'Chutney Jar','Chuttney Jar',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','341',1,'ACTIVE','PC','N','N','',11,'N'),(100335,'Grinder Jaar','Grinder Jaar',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','342',1,'ACTIVE','PC','N','N','',11,'N'),(100336,'Suma Det','Suma Det',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','343',1,'ACTIVE','L','N','N','',14,'N'),(100337,'Suma Grill','Suma Grill',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','344',1,'ACTIVE','PC','N','N','',14,'N'),(100338,'Tab','Tab',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','345',1,'ACTIVE','PC','N','N','',17,'N'),(100339,'Tab Stand 10 Inches','Tab Stand 10\"',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','346',1,'ACTIVE','PC','N','N','',11,'N'),(100340,'Take Away Bag - Large','Take Away Bag - Large',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','347',1,'ACTIVE','PC','Y','N','',4,'N'),(100341,'Take Away Bag - Small','Take Away Bag - Small',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','348',1,'ACTIVE','PC','Y','N','',4,'N'),(100342,'Tape Dispenser','Tape Dispenser',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','349',1,'ACTIVE','PC','N','N','',12,'N'),(100343,'Chai Delivery Box 400 ML','Chai delivery Box 400 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','350',1,'ACTIVE','PC','Y','N','',4,'N'),(100344,'Tea Fun Box','Tea Fun Box',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','351',1,'ACTIVE','PC','Y','N','',4,'N'),(100345,'Tea Pan 6 L','Tea Pan 6 L',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','352',1,'ACTIVE','PC','N','N','',11,'N'),(100346,'Chai Delivery Pouch 1 L','Chai delivery Pouch 1 L',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','353',1,'ACTIVE','PC','Y','N','',4,'N'),(100347,'Chai Delivery Pouch 400 ML','Tea Pouch 400 Ml',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','354',1,'ACTIVE','PC','Y','N','',4,'N'),(100348,'Tea Saucepan - Medium','Tea Saucepan Medium',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','355',1,'ACTIVE','PC','N','N','',11,'N'),(100349,'Tea Saucepan - Small','Tea Saucepan Small',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','356',1,'ACTIVE','PC','N','N','',11,'N'),(100350,'Tea Strainer - Large','Tea Strainer - Large',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','357',1,'ACTIVE','PC','N','N','',11,'N'),(100351,'Tea Strainer - Medium','Tea Strainer - Medium',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','358',1,'ACTIVE','PC','N','N','',11,'N'),(100352,'Tea Strainer - Small','Tea Strainer - Small',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','359',1,'ACTIVE','PC','N','N','',11,'N'),(100353,'Temperature And Humidity Meter','Temperature And Humidity Meter',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','360',1,'ACTIVE','PC','N','N','',11,'N'),(100354,'Tempered Glass - Customer Screen Tab','Tempered Glass - Customer Screen Tab',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','361',1,'ACTIVE','PC','N','N','',17,'N'),(100355,'Tempered Glass - Workstation Tab','Tempered Glass - Workstation Tab',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','362',1,'ACTIVE','PC','N','N','',17,'N'),(100356,'Thandai','Thandai',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','363',1,'ACTIVE','L','Y','N','',5,'N'),(100357,'Timer','Timer',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','364',1,'ACTIVE','PC','N','N','',11,'N'),(100358,'Tissue Holder','Tissue Holder',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','365',1,'ACTIVE','PC','N','N','',11,'N'),(100359,'Tissue Paper','Tissue Paper',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','366',1,'ACTIVE','PC','Y','N','',4,'N'),(100360,'Toilet Brush','Toilet Brush',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','367',1,'ACTIVE','PC','N','N','',14,'N'),(100361,'Toilet Roll','Toilet Roll',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','368',1,'ACTIVE','PC','N','N','',14,'N'),(100362,'Tomato','Tomato',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','369',1,'ACTIVE','KG','Y','N','',9,'N'),(100363,'Tong','Tong',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','370',1,'ACTIVE','PC','N','N','',10,'N'),(100364,'Tool Kit','Tool Kit',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','371',1,'ACTIVE','PC','N','N','',11,'N'),(100365,'Tray Mat','Tray Mat',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','372',1,'ACTIVE','PC','Y','N','',4,'N'),(100366,'T-Shirt - Large','T-Shirt Large',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','373',1,'ACTIVE','PC','N','N','',13,'N'),(100367,'T-Shirt - Medium','T-Shirt Medium',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','374',1,'ACTIVE','PC','N','N','',13,'N'),(100368,'T-Shirt - Small','T-Shirt Small',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','375',1,'ACTIVE','PC','N','N','',13,'N'),(100369,'T-Shirt - XL','T-Shirt Xl',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','376',1,'ACTIVE','PC','N','N','',13,'N'),(100370,'Tulsi','Tulsi',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','377',1,'ACTIVE','KG','Y','N','',5,'N'),(100371,'Tulsi Adrak Chai Can','Tulsi Adrak Chai Can',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','378',1,'ACTIVE','PC','Y','N','',2,'N'),(100372,'Uniform Cap','Uniform Cap',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','379',1,'ACTIVE','PC','N','N','',13,'N'),(100373,'Ups','Ups',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','380',1,'ACTIVE','PC','N','N','',17,'N'),(100374,'Vada Pav Filling','Vada Pav Filling',4,'Y','2016-06-11 00:00:00',100000,'Y','Y','DAILY','381',1,'ACTIVE','PC','Y','N','',20,'N'),(100375,'Visi Cooler -110 L','Visi Cooler -110 L',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','382',1,'ACTIVE','PC','N','N','',11,'N'),(100376,'Vitamix Rinsomatic','Vitamix Rinsomatic',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','383',1,'ACTIVE','PC','N','N','',11,'N'),(100377,'Washroom Cleaning Checklist','Washroom Cleaning Checklist',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','384',1,'ACTIVE','PC','N','N','',12,'N'),(100378,'Wastage Basket','Wastage Basket',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','385',1,'ACTIVE','PC','N','N','',11,'N'),(100379,'Water Boiler','Water Boiler',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','386',1,'ACTIVE','PC','N','N','',11,'N'),(100380,'Water Bottle','Water Bottle',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','387',1,'ACTIVE','PC','N','N','',10,'N'),(100381,'Water Cooler','Water Cooler',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','388',1,'ACTIVE','PC','N','N','',11,'N'),(100382,'Water Tank 1000 L','Water Tank 1000 L',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','389',1,'ACTIVE','PC','N','N','',11,'N'),(100383,'Weighing Scale','Weighing Scale',3,'N','2016-06-11 00:00:00',100000,'N','N','MONTHLY','390',1,'ACTIVE','PC','N','N','',11,'N'),(100384,'Wet Mop Head','Wet Mop Head',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','391',1,'ACTIVE','PC','N','N','',14,'N'),(100385,'Wet Mop Rod','Wet Mop Rod',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','392',1,'ACTIVE','PC','N','N','',11,'N'),(100386,'White Board Duster','White Board Duster',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','393',1,'ACTIVE','PC','N','N','',12,'N'),(100387,'White Board Marker','White Board Marker',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','394',1,'ACTIVE','PC','N','N','',12,'N'),(100388,'White Envelop Medium','White Envelop Medium',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','395',1,'ACTIVE','PC','N','N','',12,'N'),(100389,'White Envelop Small','White Envelop Small',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','396',1,'ACTIVE','PC','N','N','',12,'N'),(100390,'Wiper','Wiper',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','397',1,'ACTIVE','PC','N','N','',14,'N'),(100391,'Wiper Small','Wiper Small',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','398',1,'ACTIVE','PC','N','N','',14,'N'),(100392,'Wooden Cake Board','Wooden Cake Board',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','399',1,'ACTIVE','PC','N','N','',11,'N'),(100393,'Writing Pad','Writing Pad',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','MONTHLY','400',1,'ACTIVE','PC','N','N','',12,'N'),(100394,'Rooh Afja','Rooh Afja',2,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','401',1,'ACTIVE','L','Y','N',' ',5,'N'),(100395,'Badam Pista Cookies Pieces','Badam pista cookies pieces',1,'Y','2016-06-11 00:00:00',100000,'Y','N','DAILY','402',1,'ACTIVE','PC','Y','N','',1,'N'),(100396,'Oatmeal Cookies Pieces','Oatmeal cookies pieces',1,'Y','2016-06-11 00:00:00',100000,'Y','N','DAILY','403',1,'ACTIVE','PC','Y','N','',1,'N'),(100397,'Wooden Table Top - Regular','Wooden Table Top - Regular',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','404',-1,'ACTIVE','PC','N','N','',19,'N'),(100398,'Wooden Table Top - Big','Wooden Table Top - Big',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','405',-1,'ACTIVE','PC','N','N','',19,'N'),(100399,'Community Table - 8 Feet','Community Table - 8 Feet',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','406',-1,'ACTIVE','PC','N','N','',19,'N'),(100400,'Community Table - 10 Feet','Community Table - 10 Feet',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','407',-1,'ACTIVE','PC','N','N','',19,'N'),(100401,'Community Table - Custom Made','Community Table - Custom made',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','408',-1,'ACTIVE','PC','N','N','',19,'N'),(100402,'High Table - 10 Feet','High Table - 10 feet',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','409',-1,'ACTIVE','PC','N','N','',19,'N'),(100403,'High Table - 8 Feet','High Table - 8 feet',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','410',-1,'ACTIVE','PC','N','N','',19,'N'),(100404,'High Table - Custom Made - 5\'10\"X22\"X42\"','High Table - Custom Made - 5\'10\"x22\"x42\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','411',-1,'ACTIVE','PC','N','N','',19,'N'),(100405,'High Table - Custom Made - 5\'10\"X16\"X42\"','High Table - Custom Made - 5\'10\"x16\"x42\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','412',-1,'ACTIVE','PC','N','N','',19,'N'),(100406,'Ms Chair - Crossback - Upholtsered With Brown Canvas - Green','MS Chair - CrossBack - upholtsered with Brown Canvas - Green',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','413',-1,'ACTIVE','PC','N','N','',19,'N'),(100407,'Ms Chair - Crossback - Upholtsered With Brown Canvas - White','MS Chair - CrossBack - upholtsered with Brown Canvas - White',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','414',-1,'ACTIVE','PC','N','N','',19,'N'),(100408,'Ms Chair - Wire Mesh - Teal','MS Chair - Wire Mesh - Teal',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','415',-1,'ACTIVE','PC','N','N','',19,'N'),(100409,'Low Seater Chindi Weave','Low Seater Chindi weave',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','416',-1,'ACTIVE','PC','N','N','',19,'N'),(100410,'Chindi Chair','Chindi Chair',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','417',-1,'ACTIVE','PC','N','N','',19,'N'),(100411,'Jute Chair','Jute chair',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','418',-1,'ACTIVE','PC','N','N','',19,'N'),(100412,'Sofa Chair','Sofa Chair',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','419',-1,'ACTIVE','PC','N','N','',19,'N'),(100413,'Jute Stool - Tall - Black Color Base (Powder Coated)','Jute stool - tall - Black color base (powder coated)',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','420',-1,'ACTIVE','PC','N','N','',19,'N'),(100414,'Jute Stool - Black Color Base (Powder Coated)','Jute Stool - Black color base (powder coated)',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','421',-1,'ACTIVE','PC','N','N','',19,'N'),(100415,'Wooden Bench With Back Support','Wooden Bench with back support',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','422',-1,'ACTIVE','PC','N','N','',19,'N'),(100416,'Wooden Bench','Wooden Bench',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','423',-1,'ACTIVE','PC','N','N','',19,'N'),(100417,'Wooden Running Table','Wooden Running Table',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','424',-1,'ACTIVE','PC','N','N','',19,'N'),(100418,'Canteen Table With Swivel Stools','Canteen table with swivel stools',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','426',-1,'ACTIVE','PC','N','N','',19,'N'),(100419,'Dustbin Cabinet','Dustbin cabinet',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','427',-1,'ACTIVE','PC','N','N','',19,'N'),(100420,'2 Seater Table With Wooden Wheels','2 Seater table with wooden wheels',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','428',-1,'ACTIVE','PC','N','N','',19,'N'),(100421,'School Bench And Table - 4 Seater','School Bench and table (4-seater)',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','429',-1,'ACTIVE','PC','N','N','',19,'N'),(100422,'Outdoor - Bench - 2 Seater','Outdoor - Bench (2-Seater)',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','430',-1,'ACTIVE','PC','N','N','',19,'N'),(100423,'Outdoor - Bench With Back - 2 Seater','Outdoor - Bench with back (2-Seater)',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','431',-1,'ACTIVE','PC','N','N','',19,'N'),(100424,'Outdoor - Table - 4 Seater','Outdoor - Table (4-Seater)',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','432',-1,'ACTIVE','PC','N','N','',19,'N'),(100425,'Wooden Table Top - Round 22\"','Wooden Table Top - Round 22\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','433',-1,'ACTIVE','PC','N','N','',19,'N'),(100426,'Wooden Table Top - Round 20\"','Wooden Table Top - Round 20\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','434',-1,'ACTIVE','PC','N','N','',19,'N'),(100427,'Community Table Herringbone Top - 6\'','Community Table Herringbone Top - 6\'',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','435',-1,'ACTIVE','PC','N','N','',19,'N'),(100428,'Community Table Herringbone Top - 7\'6\"','Community Table Herringbone Top - 7\'6\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','436',-1,'ACTIVE','PC','N','N','',19,'N'),(100429,'Double Seater Sofa - Multicolour Patch','Double seater sofa - multicolour patch',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','437',-1,'ACTIVE','PC','N','N','',19,'N'),(100430,'Coffee Table With Wheels','Coffee Table with Wheels',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','438',-1,'ACTIVE','PC','N','N','',19,'N'),(100431,'Wall Seating Table & Stool Set','Wall seating Table & Stool set',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','439',-1,'ACTIVE','PC','N','N','',19,'N'),(100432,'Chaayos \"C\" Logo 22\"','Chaayos \"C\" Logo 22\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','440',-1,'ACTIVE','PC','N','N','',19,'N'),(100433,'Chaayos 20\"','Chaayos 20\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','441',-1,'ACTIVE','PC','N','N','',19,'N'),(100434,'Experiments With Chai 4\"','Experiments with chai 4\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','443',-1,'ACTIVE','PC','N','N','',19,'N'),(100435,'Cup Cutout 18\"','Cup cutout 18\"',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','444',-1,'ACTIVE','PC','N','N','',19,'N'),(100436,'Wooden Crates For Planters','Wooden Crates for Planters',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','445',-1,'ACTIVE','PC','N','N','',19,'N'),(100437,'Batti Gul','Batti Gul',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','446',-1,'ACTIVE','PC','N','N','',19,'N'),(100438,'Cello Chair - Green','CELLO CHAIR - GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','447',-1,'ACTIVE','PC','N','N','',19,'N'),(100439,'Cello Chair - Brown','CELLO CHAIR - BROWN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','448',-1,'ACTIVE','PC','N','N','',19,'N'),(100440,'Cello Chair - Grey','CELLO CHAIR - GREY',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','449',-1,'ACTIVE','PC','N','N','',19,'N'),(100441,'Cello Chair - Yellow','CELLO CHAIR - YELLOW',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','450',-1,'ACTIVE','PC','N','N','',19,'N'),(100442,'Cello Chair - White','CELLO CHAIR - WHITE',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','451',-1,'ACTIVE','PC','N','N','',19,'N'),(100443,'Cello Chair Wooden Hm Seat - Yellow','CELLO CHAIR WOODEN HM SEAT - YELLOW',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','452',-1,'ACTIVE','PC','N','N','',19,'N'),(100444,'Cello Chair Wooden Hm Seat - Green','CELLO CHAIR WOODEN HM SEAT - GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','453',-1,'ACTIVE','PC','N','N','',19,'N'),(100445,'Cello Chair Wooden Hm Seat - White D/Green','CELLO CHAIR WOODEN HM SEAT - WHITE D/GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','454',-1,'ACTIVE','PC','N','N','',19,'N'),(100446,'Cello Chair Wooden Hm - Grey D/Green','CELLO CHAIR WOODEN HM - GREY D/GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','455',-1,'ACTIVE','PC','N','N','',19,'N'),(100447,'Cello Chair Wooden Hm - Brown','CELLO CHAIR WOODEN HM - BROWN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','456',-1,'ACTIVE','PC','N','N','',19,'N'),(100448,'Arm Chair Wooden Hm Seat - Red','ARM CHAIR WOODEN HM SEAT - RED',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','457',-1,'ACTIVE','PC','N','N','',19,'N'),(100449,'Arm Chair Wooden Hm Seat - White D/Green','ARM CHAIR WOODEN HM SEAT - WHITE D/GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','458',-1,'ACTIVE','PC','N','N','',19,'N'),(100450,'Arm Chair Wooden Hm Seat - Brown','ARM CHAIR WOODEN HM SEAT - BROWN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','459',-1,'ACTIVE','PC','N','N','',19,'N'),(100451,'Arm Chair Wooden Hm Seat - Green','ARM CHAIR WOODEN HM SEAT - GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','460',-1,'ACTIVE','PC','N','N','',19,'N'),(100452,'Arm Chair Wooden Hm Seat - Yellow','ARM CHAIR WOODEN HM SEAT - YELLOW',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','461',-1,'ACTIVE','PC','N','N','',19,'N'),(100453,'Cross Back Chair Green','CROSS BACK CHAIR GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','462',-1,'ACTIVE','PC','N','N','',19,'N'),(100454,'Cross Back Chair Brown','CROSS BACK CHAIR BROWN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','463',-1,'ACTIVE','PC','N','N','',19,'N'),(100455,'Cross Back Chair White','CROSS BACK CHAIR WHITE',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','464',-1,'ACTIVE','PC','N','N','',19,'N'),(100456,'Cinema Chair Crackle Green','CINEMA CHAIR CRACKLE GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','465',-1,'ACTIVE','PC','N','N','',19,'N'),(100457,'Cinema Chair Crackle Orange','CINEMA CHAIR CRACKLE ORANGE',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','466',-1,'ACTIVE','PC','N','N','',19,'N'),(100458,'Folding Iron Chair ','FOLDING IRON CHAIR ',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','468',-1,'ACTIVE','PC','N','N','',19,'N'),(100459,'Cello High Chair Wooden Hm Seat - Yellow','CELLO HIGH CHAIR WOODEN HM SEAT - YELLOW',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','469',-1,'ACTIVE','PC','N','N','',19,'N'),(100460,'Cello High Chair Wooden Hm Seat - White','CELLO HIGH CHAIR WOODEN HM SEAT - WHITE',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','470',-1,'ACTIVE','PC','N','N','',19,'N'),(100461,'Chindi High Chair','CHINDI HIGH CHAIR',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','471',-1,'ACTIVE','PC','N','N','',19,'N'),(100462,'Chindi Bench','CHINDI BENCH',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','472',-1,'ACTIVE','PC','N','N','',19,'N'),(100463,'Cello Stool W-Seat Green','CELLO STOOL W-SEAT GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','473',-1,'ACTIVE','PC','N','N','',19,'N'),(100464,'Cello Stool W-Seat Yellow','CELLO STOOL W-SEAT YELLOW',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','474',-1,'ACTIVE','PC','N','N','',19,'N'),(100465,'Cello Stool W-Seat White','CELLO STOOL W-SEAT WHITE',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','475',-1,'ACTIVE','PC','N','N','',19,'N'),(100466,'Tea Strainer Light','TEA STRAINER LIGHT',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','476',-1,'ACTIVE','PC','N','N','',19,'N'),(100467,'Wire Kettle Lamp Green','WIRE KETTLE LAMP GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','477',-1,'ACTIVE','PC','N','N','',19,'N'),(100468,'Wire Kettle Lamp Yellow','WIRE KETTLE LAMP YELLOW',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','478',-1,'ACTIVE','PC','N','N','',19,'N'),(100469,'Yellow Spoke Light - Large','YELLOW SPOKE LIGHT - LARGE',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','479',-1,'ACTIVE','PC','N','N','',19,'N'),(100470,'Yellow Spoke Light - Small','YELLOW SPOKE LIGHT - SMALL',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','480',-1,'ACTIVE','PC','N','N','',19,'N'),(100471,'Rect Cheeka No Hanlde Black (7 Hole)','RECT CHEEKA NO HANLDE BLACK (7 HOLE)',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','481',-1,'ACTIVE','PC','N','N','',19,'N'),(100472,'Rect Cheeka No Hanlde Yellow (7 Hole)','RECT CHEEKA NO HANLDE YELLOW (7 HOLE)',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','482',-1,'ACTIVE','PC','N','N','',19,'N'),(100473,'Rect Cheeka W/Handle Black','RECT CHEEKA W/HANDLE BLACK',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','483',-1,'ACTIVE','PC','N','N','',19,'N'),(100474,'Rect Cheeka W/Handle Yellow','RECT CHEEKA W/HANDLE YELLOW',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','484',-1,'ACTIVE','PC','N','N','',19,'N'),(100475,'Round Cheeka Black','ROUND CHEEKA BLACK',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','486',-1,'ACTIVE','PC','N','N','',19,'N'),(100476,'S/6 Colored Glasses','S/6 COLORED GLASSES',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','487',-1,'ACTIVE','PC','N','N','',19,'N'),(100477,'Wall Scone Green','WALL SCONE GREEN',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','488',-1,'ACTIVE','PC','N','N','',19,'N'),(100478,'Nipple','NIPPLE',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','489',-1,'ACTIVE','PC','N','N','',19,'N'),(100479,'Naaki Black','NAAKI BLACK',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','490',-1,'ACTIVE','PC','N','N','',19,'N'),(100480,'Key Rings Black','KEY RINGS BLACK',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','491',-1,'ACTIVE','PC','N','N','',19,'N'),(100481,'Connector Black','CONNECTOR BLACK',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','492',-1,'ACTIVE','PC','N','N','',19,'N'),(100482,'Pipe 6\" Black','PIPE 6\" BLACK',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','493',-1,'ACTIVE','PC','N','N','',19,'N'),(100483,'Pipe 12\" Black','PIPE 12\" BLACK',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','494',-1,'ACTIVE','PC','N','N','',19,'N'),(100484,'Pipe 24\" Black','PIPE 24\" BLACK',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','495',-1,'ACTIVE','PC','N','N','',19,'N'),(100485,'Small Kettle Red','SMALL KETTLE RED',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','496',-1,'ACTIVE','PC','N','N','',19,'N'),(100486,'Small Kettle White','SMALL KETTLE WHITE',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','497',-1,'ACTIVE','PC','N','N','',19,'N'),(100487,'Small Kettle Grey','SMALL KETTLE GREY',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','498',-1,'ACTIVE','PC','N','N','',19,'N'),(100488,'Small Kettle Yellow','SMALL KETTLE YELLOW',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','499',-1,'ACTIVE','PC','N','N','',19,'N'),(100489,'Small Kettle Green','SMALL KETTLE GREEN',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','500',-1,'ACTIVE','PC','N','N','',19,'N'),(100490,'Large Kettle Yellow','LARGE KETTLE YELLOW',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','501',-1,'ACTIVE','PC','N','N','',19,'N'),(100491,'Large Kettle Green','LARGE KETTLE GREEN',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','502',-1,'ACTIVE','PC','N','N','',19,'N'),(100492,'Large Kettle Lamp Yellow','LARGE KETTLE LAMP YELLOW',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','503',-1,'ACTIVE','PC','N','N','',19,'N'),(100493,'Large Kettle Lamp Green','LARGE KETTLE LAMP GREEN',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','504',-1,'ACTIVE','PC','N','N','',19,'N'),(100494,'Large Kettle Lamp Nickle','LARGE KETTLE LAMP NICKLE',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','505',-1,'ACTIVE','PC','N','N','',19,'N'),(100495,'Coupons','Coupons',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','500',-1,'ACTIVE','PC','N','N','',15,'N'),(100496,'Sampling Tray','Sampling Tray',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','501',-1,'ACTIVE','PC','N','N','',15,'N'),(100497,'Corporate Gift Box','Corporate Gift Box',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','502',-1,'ACTIVE','PC','N','N','',15,'N'),(100498,'Corporate Gift Box Sleeve','Corporate Gift Box Sleeve',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','503',-1,'ACTIVE','PC','N','N','',15,'N'),(100499,'Fliers','Fliers',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','504',-1,'ACTIVE','PC','N','N','',15,'N'),(100500,'Roll-Out Standee','Roll-Out Standee',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','505',-1,'ACTIVE','PC','N','N','',15,'N'),(100501,'Iron Sunboard Hanger','Iron Sunboard Hanger',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','506',-1,'ACTIVE','PC','N','N','',15,'N'),(100502,'Handheld Menu','Handheld Menu',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','507',-1,'ACTIVE','PC','N','N','',15,'N'),(100503,'Tent Card','Tent Card',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','508',-1,'ACTIVE','PC','N','N','',15,'N'),(100504,'Sunboard','Sunboard',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','509',-1,'ACTIVE','PC','N','N','',15,'N'),(100505,'Poster','Poster',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','510',-1,'ACTIVE','PC','N','N','',15,'N'),(100506,'Sticker','Sticker',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','511',-1,'ACTIVE','PC','N','N','',15,'N'),(100507,'Umbrella','Umbrella',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','512',-1,'ACTIVE','PC','N','N','',15,'N'),(100508,'Arm Chair Upholstery Wooden Hm Seat - Red','Arm CHAIR upholstery WOODEN HM SEAT - RED',3,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','513',-1,'ACTIVE','PC','N','N','',15,'N'),(100509,'Wooden Wheel Small 12\"','Wooden wheel small 12\"',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','514',-1,'ACTIVE','PC','N','N','',15,'N'),(100510,'Wooden Wheel Big 18\"','Wooden Wheel Big 18\"',2,'Y','2016-06-11 00:00:00',100000,'N','N','MONTHLY','515',-1,'ACTIVE','PC','N','N','',15,'N'),(100511,'Food Sticker - Non Veg','Sticker - Non Veg',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','332',1,'ACTIVE','PC','Y','N','',4,'N'),(100512,'Food Sticker - Veg','Sticker - Non Veg',1,'Y','2016-06-11 00:00:00',100000,'Y','Y','WEEKLY','332',1,'ACTIVE','PC','Y','N','',4,'N');
/*!40000 ALTER TABLE `PRODUCT_DEFINITION` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PRODUCT_FULFILLMENT_TYPE_DATA`
--

DROP TABLE IF EXISTS `PRODUCT_FULFILLMENT_TYPE_DATA`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PRODUCT_FULFILLMENT_TYPE_DATA` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `FULFILLMENT_TYPE` varchar(30) NOT NULL,
  `PRODUCT_DEFINITION_ID` int(11) NOT NULL,
  `PRODUCT_FULFILLMENT_TYPE_STATUS` varchar(10) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `PRODUCT_DEFINITION_ID` (`PRODUCT_DEFINITION_ID`),
  CONSTRAINT `PRODUCT_FULFILLMENT_TYPE_DATA_ibfk_1` FOREIGN KEY (`PRODUCT_DEFINITION_ID`) REFERENCES `PRODUCT_DEFINITION` (`PRODUCT_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=513 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PRODUCT_FULFILLMENT_TYPE_DATA`
--

LOCK TABLES `PRODUCT_FULFILLMENT_TYPE_DATA` WRITE;
/*!40000 ALTER TABLE `PRODUCT_FULFILLMENT_TYPE_DATA` DISABLE KEYS */;
INSERT INTO `PRODUCT_FULFILLMENT_TYPE_DATA` VALUES (1,'WAREHOUSE',100001,'ACTIVE'),(2,'KITCHEN',100002,'ACTIVE'),(3,'WAREHOUSE',100003,'ACTIVE'),(4,'WAREHOUSE',100004,'ACTIVE'),(5,'WAREHOUSE',100005,'ACTIVE'),(6,'WAREHOUSE',100006,'ACTIVE'),(7,'WAREHOUSE',100007,'ACTIVE'),(8,'WAREHOUSE',100008,'ACTIVE'),(9,'WAREHOUSE',100009,'ACTIVE'),(10,'KITCHEN',100010,'ACTIVE'),(11,'KITCHEN',100011,'ACTIVE'),(12,'WAREHOUSE',100012,'ACTIVE'),(13,'WAREHOUSE',100013,'ACTIVE'),(14,'WAREHOUSE',100014,'ACTIVE'),(15,'WAREHOUSE',100015,'ACTIVE'),(16,'WAREHOUSE',100016,'ACTIVE'),(17,'WAREHOUSE',100017,'ACTIVE'),(18,'EXTERNAL',100018,'ACTIVE'),(19,'WAREHOUSE',100019,'ACTIVE'),(20,'WAREHOUSE',100020,'ACTIVE'),(21,'WAREHOUSE',100021,'ACTIVE'),(22,'WAREHOUSE',100022,'ACTIVE'),(23,'WAREHOUSE',100023,'ACTIVE'),(24,'WAREHOUSE',100024,'ACTIVE'),(25,'KITCHEN',100025,'ACTIVE'),(26,'KITCHEN',100026,'ACTIVE'),(27,'KITCHEN',100027,'ACTIVE'),(28,'WAREHOUSE',100028,'ACTIVE'),(29,'WAREHOUSE',100029,'ACTIVE'),(30,'WAREHOUSE',100030,'ACTIVE'),(31,'WAREHOUSE',100031,'ACTIVE'),(32,'WAREHOUSE',100032,'ACTIVE'),(33,'WAREHOUSE',100033,'ACTIVE'),(34,'KITCHEN',100034,'ACTIVE'),(35,'WAREHOUSE',100035,'ACTIVE'),(36,'WAREHOUSE',100036,'ACTIVE'),(37,'WAREHOUSE',100037,'ACTIVE'),(38,'KITCHEN',100038,'ACTIVE'),(39,'WAREHOUSE',100039,'ACTIVE'),(40,'WAREHOUSE',100040,'ACTIVE'),(41,'WAREHOUSE',100041,'ACTIVE'),(42,'WAREHOUSE',100042,'ACTIVE'),(43,'WAREHOUSE',100043,'ACTIVE'),(44,'WAREHOUSE',100044,'ACTIVE'),(45,'WAREHOUSE',100045,'ACTIVE'),(46,'WAREHOUSE',100046,'ACTIVE'),(47,'WAREHOUSE',100047,'ACTIVE'),(48,'WAREHOUSE',100048,'ACTIVE'),(49,'WAREHOUSE',100049,'ACTIVE'),(50,'WAREHOUSE',100050,'ACTIVE'),(51,'WAREHOUSE',100051,'ACTIVE'),(52,'WAREHOUSE',100052,'ACTIVE'),(53,'WAREHOUSE',100053,'ACTIVE'),(54,'WAREHOUSE',100054,'ACTIVE'),(55,'KITCHEN',100055,'ACTIVE'),(56,'WAREHOUSE',100056,'ACTIVE'),(57,'KITCHEN',100057,'ACTIVE'),(58,'WAREHOUSE',100058,'ACTIVE'),(59,'WAREHOUSE',100059,'ACTIVE'),(60,'WAREHOUSE',100060,'ACTIVE'),(61,'WAREHOUSE',100061,'ACTIVE'),(62,'WAREHOUSE',100062,'ACTIVE'),(63,'WAREHOUSE',100063,'ACTIVE'),(64,'WAREHOUSE',100064,'ACTIVE'),(65,'WAREHOUSE',100065,'ACTIVE'),(66,'KITCHEN',100066,'ACTIVE'),(67,'WAREHOUSE',100067,'ACTIVE'),(68,'WAREHOUSE',100068,'ACTIVE'),(69,'WAREHOUSE',100069,'ACTIVE'),(70,'KITCHEN',100070,'ACTIVE'),(71,'WAREHOUSE',100071,'ACTIVE'),(72,'WAREHOUSE',100072,'ACTIVE'),(73,'WAREHOUSE',100073,'ACTIVE'),(74,'WAREHOUSE',100074,'ACTIVE'),(75,'WAREHOUSE',100075,'ACTIVE'),(76,'WAREHOUSE',100076,'ACTIVE'),(77,'KITCHEN',100077,'ACTIVE'),(78,'WAREHOUSE',100078,'ACTIVE'),(79,'WAREHOUSE',100079,'ACTIVE'),(80,'WAREHOUSE',100080,'ACTIVE'),(81,'KITCHEN',100081,'ACTIVE'),(82,'KITCHEN',100082,'ACTIVE'),(83,'KITCHEN',100083,'ACTIVE'),(84,'WAREHOUSE',100084,'ACTIVE'),(85,'WAREHOUSE',100085,'ACTIVE'),(86,'WAREHOUSE',100086,'ACTIVE'),(87,'WAREHOUSE',100087,'ACTIVE'),(88,'WAREHOUSE',100088,'ACTIVE'),(89,'WAREHOUSE',100089,'ACTIVE'),(90,'WAREHOUSE',100090,'ACTIVE'),(91,'WAREHOUSE',100091,'ACTIVE'),(92,'WAREHOUSE',100092,'ACTIVE'),(93,'WAREHOUSE',100093,'ACTIVE'),(94,'WAREHOUSE',100094,'ACTIVE'),(95,'WAREHOUSE',100095,'ACTIVE'),(96,'WAREHOUSE',100096,'ACTIVE'),(97,'WAREHOUSE',100097,'ACTIVE'),(98,'WAREHOUSE',100098,'ACTIVE'),(99,'WAREHOUSE',100099,'ACTIVE'),(100,'WAREHOUSE',100100,'ACTIVE'),(101,'WAREHOUSE',100101,'ACTIVE'),(102,'WAREHOUSE',100102,'ACTIVE'),(103,'WAREHOUSE',100103,'ACTIVE'),(104,'WAREHOUSE',100104,'ACTIVE'),(105,'WAREHOUSE',100105,'ACTIVE'),(106,'WAREHOUSE',100106,'ACTIVE'),(107,'WAREHOUSE',100107,'ACTIVE'),(108,'WAREHOUSE',100108,'ACTIVE'),(109,'WAREHOUSE',100109,'ACTIVE'),(110,'WAREHOUSE',100110,'ACTIVE'),(111,'WAREHOUSE',100111,'ACTIVE'),(112,'EXTERNAL',100112,'ACTIVE'),(113,'KITCHEN',100113,'ACTIVE'),(114,'WAREHOUSE',100114,'ACTIVE'),(115,'WAREHOUSE',100115,'ACTIVE'),(116,'WAREHOUSE',100116,'ACTIVE'),(117,'WAREHOUSE',100117,'ACTIVE'),(118,'WAREHOUSE',100118,'ACTIVE'),(119,'WAREHOUSE',100119,'ACTIVE'),(120,'WAREHOUSE',100120,'ACTIVE'),(121,'WAREHOUSE',100121,'ACTIVE'),(122,'WAREHOUSE',100122,'ACTIVE'),(123,'WAREHOUSE',100123,'ACTIVE'),(124,'WAREHOUSE',100124,'ACTIVE'),(125,'WAREHOUSE',100125,'ACTIVE'),(126,'WAREHOUSE',100126,'ACTIVE'),(127,'WAREHOUSE',100127,'ACTIVE'),(128,'WAREHOUSE',100128,'ACTIVE'),(129,'WAREHOUSE',100129,'ACTIVE'),(130,'WAREHOUSE',100130,'ACTIVE'),(131,'WAREHOUSE',100131,'ACTIVE'),(132,'WAREHOUSE',100132,'ACTIVE'),(133,'WAREHOUSE',100133,'ACTIVE'),(134,'WAREHOUSE',100134,'ACTIVE'),(135,'WAREHOUSE',100135,'ACTIVE'),(136,'WAREHOUSE',100136,'ACTIVE'),(137,'WAREHOUSE',100137,'ACTIVE'),(138,'WAREHOUSE',100138,'ACTIVE'),(139,'WAREHOUSE',100139,'ACTIVE'),(140,'WAREHOUSE',100140,'ACTIVE'),(141,'WAREHOUSE',100141,'ACTIVE'),(142,'KITCHEN',100142,'ACTIVE'),(143,'WAREHOUSE',100143,'ACTIVE'),(144,'WAREHOUSE',100144,'ACTIVE'),(145,'EXTERNAL',100145,'ACTIVE'),(146,'WAREHOUSE',100146,'ACTIVE'),(147,'WAREHOUSE',100147,'ACTIVE'),(148,'WAREHOUSE',100148,'ACTIVE'),(149,'WAREHOUSE',100149,'ACTIVE'),(150,'WAREHOUSE',100150,'ACTIVE'),(151,'WAREHOUSE',100151,'ACTIVE'),(152,'WAREHOUSE',100152,'ACTIVE'),(153,'EXTERNAL',100153,'ACTIVE'),(154,'WAREHOUSE',100154,'ACTIVE'),(155,'WAREHOUSE',100155,'ACTIVE'),(156,'KITCHEN',100156,'ACTIVE'),(157,'WAREHOUSE',100157,'ACTIVE'),(158,'WAREHOUSE',100158,'ACTIVE'),(159,'KITCHEN',100159,'ACTIVE'),(160,'WAREHOUSE',100160,'ACTIVE'),(161,'WAREHOUSE',100161,'ACTIVE'),(162,'WAREHOUSE',100162,'ACTIVE'),(163,'WAREHOUSE',100163,'ACTIVE'),(164,'WAREHOUSE',100164,'ACTIVE'),(165,'WAREHOUSE',100165,'ACTIVE'),(166,'WAREHOUSE',100166,'ACTIVE'),(167,'KITCHEN',100167,'ACTIVE'),(168,'KITCHEN',100168,'ACTIVE'),(169,'KITCHEN',100169,'ACTIVE'),(170,'WAREHOUSE',100170,'ACTIVE'),(171,'WAREHOUSE',100171,'ACTIVE'),(172,'WAREHOUSE',100172,'ACTIVE'),(173,'WAREHOUSE',100173,'ACTIVE'),(174,'WAREHOUSE',100174,'ACTIVE'),(175,'WAREHOUSE',100175,'ACTIVE'),(176,'WAREHOUSE',100176,'ACTIVE'),(177,'KITCHEN',100177,'ACTIVE'),(178,'WAREHOUSE',100178,'ACTIVE'),(179,'KITCHEN',100179,'ACTIVE'),(180,'WAREHOUSE',100180,'ACTIVE'),(181,'KITCHEN',100181,'ACTIVE'),(182,'KITCHEN',100182,'ACTIVE'),(183,'WAREHOUSE',100183,'ACTIVE'),(184,'WAREHOUSE',100184,'ACTIVE'),(185,'WAREHOUSE',100185,'ACTIVE'),(186,'WAREHOUSE',100186,'ACTIVE'),(187,'WAREHOUSE',100187,'ACTIVE'),(188,'WAREHOUSE',100188,'ACTIVE'),(189,'WAREHOUSE',100189,'ACTIVE'),(190,'KITCHEN',100190,'ACTIVE'),(191,'WAREHOUSE',100191,'ACTIVE'),(192,'EXTERNAL',100192,'ACTIVE'),(193,'KITCHEN',100193,'ACTIVE'),(194,'WAREHOUSE',100194,'ACTIVE'),(195,'KITCHEN',100195,'ACTIVE'),(196,'WAREHOUSE',100196,'ACTIVE'),(197,'WAREHOUSE',100197,'ACTIVE'),(198,'WAREHOUSE',100198,'ACTIVE'),(199,'WAREHOUSE',100199,'ACTIVE'),(200,'WAREHOUSE',100200,'ACTIVE'),(201,'KITCHEN',100201,'ACTIVE'),(202,'WAREHOUSE',100202,'ACTIVE'),(203,'WAREHOUSE',100203,'ACTIVE'),(204,'WAREHOUSE',100204,'ACTIVE'),(205,'KITCHEN',100205,'ACTIVE'),(206,'KITCHEN',100206,'ACTIVE'),(207,'KITCHEN',100207,'ACTIVE'),(208,'WAREHOUSE',100208,'ACTIVE'),(209,'WAREHOUSE',100209,'ACTIVE'),(210,'WAREHOUSE',100210,'ACTIVE'),(211,'WAREHOUSE',100211,'ACTIVE'),(212,'WAREHOUSE',100212,'ACTIVE'),(213,'WAREHOUSE',100213,'ACTIVE'),(214,'WAREHOUSE',100214,'ACTIVE'),(215,'WAREHOUSE',100215,'ACTIVE'),(216,'KITCHEN',100216,'ACTIVE'),(217,'WAREHOUSE',100217,'ACTIVE'),(218,'WAREHOUSE',100218,'ACTIVE'),(219,'WAREHOUSE',100219,'ACTIVE'),(220,'WAREHOUSE',100220,'ACTIVE'),(221,'WAREHOUSE',100221,'ACTIVE'),(222,'WAREHOUSE',100222,'ACTIVE'),(223,'WAREHOUSE',100223,'ACTIVE'),(224,'WAREHOUSE',100224,'ACTIVE'),(225,'WAREHOUSE',100225,'ACTIVE'),(226,'WAREHOUSE',100226,'ACTIVE'),(227,'WAREHOUSE',100227,'ACTIVE'),(228,'WAREHOUSE',100228,'ACTIVE'),(229,'WAREHOUSE',100229,'ACTIVE'),(230,'WAREHOUSE',100230,'ACTIVE'),(231,'WAREHOUSE',100231,'ACTIVE'),(232,'WAREHOUSE',100232,'ACTIVE'),(233,'WAREHOUSE',100233,'ACTIVE'),(234,'EXTERNAL',100234,'ACTIVE'),(235,'WAREHOUSE',100235,'ACTIVE'),(236,'KITCHEN',100236,'ACTIVE'),(237,'WAREHOUSE',100237,'ACTIVE'),(238,'KITCHEN',100238,'ACTIVE'),(239,'WAREHOUSE',100239,'ACTIVE'),(240,'WAREHOUSE',100240,'ACTIVE'),(241,'WAREHOUSE',100241,'ACTIVE'),(242,'WAREHOUSE',100242,'ACTIVE'),(243,'WAREHOUSE',100243,'ACTIVE'),(244,'WAREHOUSE',100244,'ACTIVE'),(245,'WAREHOUSE',100245,'ACTIVE'),(246,'EXTERNAL',100246,'ACTIVE'),(247,'WAREHOUSE',100247,'ACTIVE'),(248,'WAREHOUSE',100248,'ACTIVE'),(249,'KITCHEN',100249,'ACTIVE'),(250,'KITCHEN',100250,'ACTIVE'),(251,'WAREHOUSE',100251,'ACTIVE'),(252,'WAREHOUSE',100252,'ACTIVE'),(253,'KITCHEN',100253,'ACTIVE'),(254,'KITCHEN',100254,'ACTIVE'),(255,'WAREHOUSE',100255,'ACTIVE'),(256,'KITCHEN',100256,'ACTIVE'),(257,'WAREHOUSE',100257,'ACTIVE'),(258,'WAREHOUSE',100258,'ACTIVE'),(259,'EXTERNAL',100259,'ACTIVE'),(260,'WAREHOUSE',100260,'ACTIVE'),(261,'WAREHOUSE',100261,'ACTIVE'),(262,'KITCHEN',100262,'ACTIVE'),(263,'WAREHOUSE',100263,'ACTIVE'),(264,'WAREHOUSE',100264,'ACTIVE'),(265,'WAREHOUSE',100265,'ACTIVE'),(266,'WAREHOUSE',100266,'ACTIVE'),(267,'WAREHOUSE',100267,'ACTIVE'),(268,'WAREHOUSE',100268,'ACTIVE'),(269,'WAREHOUSE',100269,'ACTIVE'),(270,'WAREHOUSE',100270,'ACTIVE'),(271,'WAREHOUSE',100271,'ACTIVE'),(272,'KITCHEN',100272,'ACTIVE'),(273,'WAREHOUSE',100273,'ACTIVE'),(274,'WAREHOUSE',100274,'ACTIVE'),(275,'WAREHOUSE',100275,'ACTIVE'),(276,'WAREHOUSE',100276,'ACTIVE'),(277,'WAREHOUSE',100277,'ACTIVE'),(278,'WAREHOUSE',100278,'ACTIVE'),(279,'WAREHOUSE',100279,'ACTIVE'),(280,'WAREHOUSE',100280,'ACTIVE'),(281,'WAREHOUSE',100281,'ACTIVE'),(282,'WAREHOUSE',100282,'ACTIVE'),(283,'WAREHOUSE',100283,'ACTIVE'),(284,'WAREHOUSE',100284,'ACTIVE'),(285,'WAREHOUSE',100285,'ACTIVE'),(286,'WAREHOUSE',100286,'ACTIVE'),(287,'WAREHOUSE',100287,'ACTIVE'),(288,'WAREHOUSE',100288,'ACTIVE'),(289,'KITCHEN',100289,'ACTIVE'),(290,'WAREHOUSE',100290,'ACTIVE'),(291,'WAREHOUSE',100291,'ACTIVE'),(292,'WAREHOUSE',100292,'ACTIVE'),(293,'WAREHOUSE',100293,'ACTIVE'),(294,'WAREHOUSE',100294,'ACTIVE'),(295,'WAREHOUSE',100295,'ACTIVE'),(296,'WAREHOUSE',100296,'ACTIVE'),(297,'WAREHOUSE',100297,'ACTIVE'),(298,'WAREHOUSE',100298,'ACTIVE'),(299,'WAREHOUSE',100299,'ACTIVE'),(300,'WAREHOUSE',100300,'ACTIVE'),(301,'WAREHOUSE',100301,'ACTIVE'),(302,'WAREHOUSE',100302,'ACTIVE'),(303,'WAREHOUSE',100303,'ACTIVE'),(304,'WAREHOUSE',100304,'ACTIVE'),(305,'WAREHOUSE',100305,'ACTIVE'),(306,'KITCHEN',100306,'ACTIVE'),(307,'WAREHOUSE',100307,'ACTIVE'),(308,'WAREHOUSE',100308,'ACTIVE'),(309,'WAREHOUSE',100309,'ACTIVE'),(310,'WAREHOUSE',100310,'ACTIVE'),(311,'WAREHOUSE',100311,'ACTIVE'),(312,'WAREHOUSE',100312,'ACTIVE'),(313,'KITCHEN',100313,'ACTIVE'),(314,'WAREHOUSE',100314,'ACTIVE'),(315,'WAREHOUSE',100315,'ACTIVE'),(316,'WAREHOUSE',100316,'ACTIVE'),(317,'WAREHOUSE',100317,'ACTIVE'),(318,'WAREHOUSE',100318,'ACTIVE'),(319,'WAREHOUSE',100319,'ACTIVE'),(320,'WAREHOUSE',100320,'ACTIVE'),(321,'WAREHOUSE',100321,'ACTIVE'),(322,'WAREHOUSE',100322,'ACTIVE'),(323,'WAREHOUSE',100323,'ACTIVE'),(324,'WAREHOUSE',100324,'ACTIVE'),(325,'WAREHOUSE',100325,'ACTIVE'),(326,'WAREHOUSE',100326,'ACTIVE'),(327,'WAREHOUSE',100327,'ACTIVE'),(328,'WAREHOUSE',100328,'ACTIVE'),(329,'KITCHEN',100329,'ACTIVE'),(330,'WAREHOUSE',100330,'ACTIVE'),(331,'WAREHOUSE',100331,'ACTIVE'),(332,'WAREHOUSE',100332,'ACTIVE'),(333,'WAREHOUSE',100333,'ACTIVE'),(334,'WAREHOUSE',100334,'ACTIVE'),(335,'WAREHOUSE',100335,'ACTIVE'),(336,'WAREHOUSE',100336,'ACTIVE'),(337,'WAREHOUSE',100337,'ACTIVE'),(338,'WAREHOUSE',100338,'ACTIVE'),(339,'WAREHOUSE',100339,'ACTIVE'),(340,'WAREHOUSE',100340,'ACTIVE'),(341,'WAREHOUSE',100341,'ACTIVE'),(342,'WAREHOUSE',100342,'ACTIVE'),(343,'WAREHOUSE',100343,'ACTIVE'),(344,'WAREHOUSE',100344,'ACTIVE'),(345,'WAREHOUSE',100345,'ACTIVE'),(346,'WAREHOUSE',100346,'ACTIVE'),(347,'WAREHOUSE',100347,'ACTIVE'),(348,'WAREHOUSE',100348,'ACTIVE'),(349,'WAREHOUSE',100349,'ACTIVE'),(350,'WAREHOUSE',100350,'ACTIVE'),(351,'WAREHOUSE',100351,'ACTIVE'),(352,'WAREHOUSE',100352,'ACTIVE'),(353,'WAREHOUSE',100353,'ACTIVE'),(354,'WAREHOUSE',100354,'ACTIVE'),(355,'WAREHOUSE',100355,'ACTIVE'),(356,'WAREHOUSE',100356,'ACTIVE'),(357,'WAREHOUSE',100357,'ACTIVE'),(358,'WAREHOUSE',100358,'ACTIVE'),(359,'WAREHOUSE',100359,'ACTIVE'),(360,'WAREHOUSE',100360,'ACTIVE'),(361,'WAREHOUSE',100361,'ACTIVE'),(362,'KITCHEN',100362,'ACTIVE'),(363,'WAREHOUSE',100363,'ACTIVE'),(364,'WAREHOUSE',100364,'ACTIVE'),(365,'WAREHOUSE',100365,'ACTIVE'),(366,'WAREHOUSE',100366,'ACTIVE'),(367,'WAREHOUSE',100367,'ACTIVE'),(368,'WAREHOUSE',100368,'ACTIVE'),(369,'WAREHOUSE',100369,'ACTIVE'),(370,'WAREHOUSE',100370,'ACTIVE'),(371,'WAREHOUSE',100371,'ACTIVE'),(372,'WAREHOUSE',100372,'ACTIVE'),(373,'WAREHOUSE',100373,'ACTIVE'),(374,'KITCHEN',100374,'ACTIVE'),(375,'WAREHOUSE',100375,'ACTIVE'),(376,'WAREHOUSE',100376,'ACTIVE'),(377,'WAREHOUSE',100377,'ACTIVE'),(378,'WAREHOUSE',100378,'ACTIVE'),(379,'WAREHOUSE',100379,'ACTIVE'),(380,'WAREHOUSE',100380,'ACTIVE'),(381,'WAREHOUSE',100381,'ACTIVE'),(382,'WAREHOUSE',100382,'ACTIVE'),(383,'WAREHOUSE',100383,'ACTIVE'),(384,'WAREHOUSE',100384,'ACTIVE'),(385,'WAREHOUSE',100385,'ACTIVE'),(386,'WAREHOUSE',100386,'ACTIVE'),(387,'WAREHOUSE',100387,'ACTIVE'),(388,'WAREHOUSE',100388,'ACTIVE'),(389,'WAREHOUSE',100389,'ACTIVE'),(390,'WAREHOUSE',100390,'ACTIVE'),(391,'WAREHOUSE',100391,'ACTIVE'),(392,'WAREHOUSE',100392,'ACTIVE'),(393,'WAREHOUSE',100393,'ACTIVE'),(394,'WAREHOUSE',100394,'ACTIVE'),(395,'EXTERNAL',100395,'ACTIVE'),(396,'EXTERNAL',100396,'ACTIVE'),(397,'WAREHOUSE',100397,'ACTIVE'),(398,'WAREHOUSE',100398,'ACTIVE'),(399,'WAREHOUSE',100399,'ACTIVE'),(400,'WAREHOUSE',100400,'ACTIVE'),(401,'WAREHOUSE',100401,'ACTIVE'),(402,'WAREHOUSE',100402,'ACTIVE'),(403,'WAREHOUSE',100403,'ACTIVE'),(404,'WAREHOUSE',100404,'ACTIVE'),(405,'WAREHOUSE',100405,'ACTIVE'),(406,'WAREHOUSE',100406,'ACTIVE'),(407,'WAREHOUSE',100407,'ACTIVE'),(408,'WAREHOUSE',100408,'ACTIVE'),(409,'WAREHOUSE',100409,'ACTIVE'),(410,'WAREHOUSE',100410,'ACTIVE'),(411,'WAREHOUSE',100411,'ACTIVE'),(412,'WAREHOUSE',100412,'ACTIVE'),(413,'WAREHOUSE',100413,'ACTIVE'),(414,'WAREHOUSE',100414,'ACTIVE'),(415,'WAREHOUSE',100415,'ACTIVE'),(416,'WAREHOUSE',100416,'ACTIVE'),(417,'WAREHOUSE',100417,'ACTIVE'),(418,'WAREHOUSE',100418,'ACTIVE'),(419,'WAREHOUSE',100419,'ACTIVE'),(420,'WAREHOUSE',100420,'ACTIVE'),(421,'WAREHOUSE',100421,'ACTIVE'),(422,'WAREHOUSE',100422,'ACTIVE'),(423,'WAREHOUSE',100423,'ACTIVE'),(424,'WAREHOUSE',100424,'ACTIVE'),(425,'WAREHOUSE',100425,'ACTIVE'),(426,'WAREHOUSE',100426,'ACTIVE'),(427,'WAREHOUSE',100427,'ACTIVE'),(428,'WAREHOUSE',100428,'ACTIVE'),(429,'WAREHOUSE',100429,'ACTIVE'),(430,'WAREHOUSE',100430,'ACTIVE'),(431,'WAREHOUSE',100431,'ACTIVE'),(432,'WAREHOUSE',100432,'ACTIVE'),(433,'WAREHOUSE',100433,'ACTIVE'),(434,'WAREHOUSE',100434,'ACTIVE'),(435,'WAREHOUSE',100435,'ACTIVE'),(436,'WAREHOUSE',100436,'ACTIVE'),(437,'WAREHOUSE',100437,'ACTIVE'),(438,'WAREHOUSE',100438,'ACTIVE'),(439,'WAREHOUSE',100439,'ACTIVE'),(440,'WAREHOUSE',100440,'ACTIVE'),(441,'WAREHOUSE',100441,'ACTIVE'),(442,'WAREHOUSE',100442,'ACTIVE'),(443,'WAREHOUSE',100443,'ACTIVE'),(444,'WAREHOUSE',100444,'ACTIVE'),(445,'WAREHOUSE',100445,'ACTIVE'),(446,'WAREHOUSE',100446,'ACTIVE'),(447,'WAREHOUSE',100447,'ACTIVE'),(448,'WAREHOUSE',100448,'ACTIVE'),(449,'WAREHOUSE',100449,'ACTIVE'),(450,'WAREHOUSE',100450,'ACTIVE'),(451,'WAREHOUSE',100451,'ACTIVE'),(452,'WAREHOUSE',100452,'ACTIVE'),(453,'WAREHOUSE',100453,'ACTIVE'),(454,'WAREHOUSE',100454,'ACTIVE'),(455,'WAREHOUSE',100455,'ACTIVE'),(456,'WAREHOUSE',100456,'ACTIVE'),(457,'WAREHOUSE',100457,'ACTIVE'),(458,'WAREHOUSE',100458,'ACTIVE'),(459,'WAREHOUSE',100459,'ACTIVE'),(460,'WAREHOUSE',100460,'ACTIVE'),(461,'WAREHOUSE',100461,'ACTIVE'),(462,'WAREHOUSE',100462,'ACTIVE'),(463,'WAREHOUSE',100463,'ACTIVE'),(464,'WAREHOUSE',100464,'ACTIVE'),(465,'WAREHOUSE',100465,'ACTIVE'),(466,'WAREHOUSE',100466,'ACTIVE'),(467,'WAREHOUSE',100467,'ACTIVE'),(468,'WAREHOUSE',100468,'ACTIVE'),(469,'WAREHOUSE',100469,'ACTIVE'),(470,'WAREHOUSE',100470,'ACTIVE'),(471,'WAREHOUSE',100471,'ACTIVE'),(472,'WAREHOUSE',100472,'ACTIVE'),(473,'WAREHOUSE',100473,'ACTIVE'),(474,'WAREHOUSE',100474,'ACTIVE'),(475,'WAREHOUSE',100475,'ACTIVE'),(476,'WAREHOUSE',100476,'ACTIVE'),(477,'WAREHOUSE',100477,'ACTIVE'),(478,'WAREHOUSE',100478,'ACTIVE'),(479,'WAREHOUSE',100479,'ACTIVE'),(480,'WAREHOUSE',100480,'ACTIVE'),(481,'WAREHOUSE',100481,'ACTIVE'),(482,'WAREHOUSE',100482,'ACTIVE'),(483,'WAREHOUSE',100483,'ACTIVE'),(484,'WAREHOUSE',100484,'ACTIVE'),(485,'WAREHOUSE',100485,'ACTIVE'),(486,'WAREHOUSE',100486,'ACTIVE'),(487,'WAREHOUSE',100487,'ACTIVE'),(488,'WAREHOUSE',100488,'ACTIVE'),(489,'WAREHOUSE',100489,'ACTIVE'),(490,'WAREHOUSE',100490,'ACTIVE'),(491,'WAREHOUSE',100491,'ACTIVE'),(492,'WAREHOUSE',100492,'ACTIVE'),(493,'WAREHOUSE',100493,'ACTIVE'),(494,'WAREHOUSE',100494,'ACTIVE'),(495,'WAREHOUSE',100495,'ACTIVE'),(496,'WAREHOUSE',100496,'ACTIVE'),(497,'WAREHOUSE',100497,'ACTIVE'),(498,'WAREHOUSE',100498,'ACTIVE'),(499,'WAREHOUSE',100499,'ACTIVE'),(500,'WAREHOUSE',100500,'ACTIVE'),(501,'WAREHOUSE',100501,'ACTIVE'),(502,'WAREHOUSE',100502,'ACTIVE'),(503,'WAREHOUSE',100503,'ACTIVE'),(504,'WAREHOUSE',100504,'ACTIVE'),(505,'WAREHOUSE',100505,'ACTIVE'),(506,'WAREHOUSE',100506,'ACTIVE'),(507,'WAREHOUSE',100507,'ACTIVE'),(508,'WAREHOUSE',100508,'ACTIVE'),(509,'WAREHOUSE',100509,'ACTIVE'),(510,'WAREHOUSE',100510,'ACTIVE'),(511,'WAREHOUSE',100511,'ACTIVE'),(512,'WAREHOUSE',100512,'ACTIVE');
/*!40000 ALTER TABLE `PRODUCT_FULFILLMENT_TYPE_DATA` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PRODUCT_PACKAGING_MAPPING`
--

DROP TABLE IF EXISTS `PRODUCT_PACKAGING_MAPPING`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PRODUCT_PACKAGING_MAPPING` (
  `PRODUCT_PACKAGING_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PACKAGING_ID` int(11) NOT NULL,
  `PRODUCT_ID` int(11) NOT NULL,
  `MAPPING_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`PRODUCT_PACKAGING_MAPPING_ID`),
  UNIQUE KEY `PACKAGING_ID` (`PACKAGING_ID`,`PRODUCT_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=741 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PRODUCT_PACKAGING_MAPPING`
--

LOCK TABLES `PRODUCT_PACKAGING_MAPPING` WRITE;
/*!40000 ALTER TABLE `PRODUCT_PACKAGING_MAPPING` DISABLE KEYS */;
INSERT INTO `PRODUCT_PACKAGING_MAPPING` VALUES (1,3,100001,'ACTIVE'),(2,3,100002,'ACTIVE'),(3,3,100003,'ACTIVE'),(4,3,100004,'ACTIVE'),(5,3,100005,'ACTIVE'),(6,3,100006,'ACTIVE'),(7,3,100007,'ACTIVE'),(8,2,100008,'ACTIVE'),(9,2,100010,'ACTIVE'),(10,3,100011,'ACTIVE'),(11,3,100012,'ACTIVE'),(12,3,100013,'ACTIVE'),(13,3,100014,'ACTIVE'),(14,3,100015,'ACTIVE'),(15,3,100016,'ACTIVE'),(16,2,100017,'ACTIVE'),(17,2,100019,'ACTIVE'),(18,3,100020,'ACTIVE'),(19,3,100021,'ACTIVE'),(20,3,100022,'ACTIVE'),(21,2,100023,'ACTIVE'),(22,3,100024,'ACTIVE'),(23,3,100025,'ACTIVE'),(24,3,100026,'ACTIVE'),(25,3,100027,'ACTIVE'),(26,3,100028,'ACTIVE'),(27,2,100029,'ACTIVE'),(28,2,100030,'ACTIVE'),(29,2,100031,'ACTIVE'),(30,2,100032,'ACTIVE'),(31,3,100033,'ACTIVE'),(32,3,100034,'ACTIVE'),(33,2,100035,'ACTIVE'),(34,2,100036,'ACTIVE'),(35,3,100037,'ACTIVE'),(36,3,100038,'ACTIVE'),(37,3,100039,'ACTIVE'),(38,3,100040,'ACTIVE'),(39,3,100041,'ACTIVE'),(40,3,100042,'ACTIVE'),(41,3,100043,'ACTIVE'),(42,2,100044,'ACTIVE'),(43,3,100045,'ACTIVE'),(44,3,100046,'ACTIVE'),(45,3,100047,'ACTIVE'),(46,3,100048,'ACTIVE'),(47,3,100049,'ACTIVE'),(48,3,100050,'ACTIVE'),(49,3,100051,'ACTIVE'),(50,3,100052,'ACTIVE'),(51,3,100053,'ACTIVE'),(52,3,100054,'ACTIVE'),(53,2,100055,'ACTIVE'),(54,3,100056,'ACTIVE'),(55,3,100057,'ACTIVE'),(56,3,100058,'ACTIVE'),(57,3,100059,'ACTIVE'),(58,3,100060,'ACTIVE'),(59,3,100061,'ACTIVE'),(60,3,100062,'ACTIVE'),(61,3,100063,'ACTIVE'),(62,3,100064,'ACTIVE'),(63,3,100065,'ACTIVE'),(64,2,100066,'ACTIVE'),(65,3,100067,'ACTIVE'),(66,2,100068,'ACTIVE'),(67,2,100069,'ACTIVE'),(68,3,100070,'ACTIVE'),(69,3,100071,'ACTIVE'),(70,3,100072,'ACTIVE'),(71,3,100073,'ACTIVE'),(72,3,100074,'ACTIVE'),(73,3,100075,'ACTIVE'),(74,3,100076,'ACTIVE'),(75,2,100077,'ACTIVE'),(76,3,100078,'ACTIVE'),(77,3,100079,'ACTIVE'),(78,2,100080,'ACTIVE'),(79,3,100081,'ACTIVE'),(80,3,100082,'ACTIVE'),(81,3,100083,'ACTIVE'),(82,2,100084,'ACTIVE'),(83,1,100085,'ACTIVE'),(84,3,100086,'ACTIVE'),(85,3,100087,'ACTIVE'),(86,2,100088,'ACTIVE'),(87,1,100089,'ACTIVE'),(88,3,100090,'ACTIVE'),(89,3,100091,'ACTIVE'),(90,3,100092,'ACTIVE'),(91,2,100093,'ACTIVE'),(92,3,100094,'ACTIVE'),(93,1,100095,'ACTIVE'),(94,3,100097,'ACTIVE'),(95,3,100098,'ACTIVE'),(96,3,100099,'ACTIVE'),(97,3,100100,'ACTIVE'),(98,3,100101,'ACTIVE'),(99,3,100102,'ACTIVE'),(100,3,100103,'ACTIVE'),(101,3,100104,'ACTIVE'),(102,3,100105,'ACTIVE'),(103,3,100106,'ACTIVE'),(104,3,100107,'ACTIVE'),(105,3,100108,'ACTIVE'),(106,3,100109,'ACTIVE'),(107,3,100110,'ACTIVE'),(108,3,100111,'ACTIVE'),(109,3,100112,'ACTIVE'),(110,2,100113,'ACTIVE'),(111,3,100114,'ACTIVE'),(112,3,100115,'ACTIVE'),(113,3,100116,'ACTIVE'),(114,3,100117,'ACTIVE'),(115,1,100118,'ACTIVE'),(116,3,100119,'ACTIVE'),(117,2,100120,'ACTIVE'),(118,3,100121,'ACTIVE'),(119,3,100122,'ACTIVE'),(120,2,100123,'ACTIVE'),(121,1,100124,'ACTIVE'),(122,3,100125,'ACTIVE'),(123,3,100126,'ACTIVE'),(124,3,100127,'ACTIVE'),(125,3,100128,'ACTIVE'),(126,3,100129,'ACTIVE'),(127,3,100130,'ACTIVE'),(128,3,100131,'ACTIVE'),(129,3,100132,'ACTIVE'),(130,3,100133,'ACTIVE'),(131,3,100134,'ACTIVE'),(132,3,100135,'ACTIVE'),(133,3,100136,'ACTIVE'),(134,3,100137,'ACTIVE'),(135,3,100138,'ACTIVE'),(136,3,100139,'ACTIVE'),(137,3,100140,'ACTIVE'),(138,2,100141,'ACTIVE'),(139,3,100142,'ACTIVE'),(140,3,100143,'ACTIVE'),(141,2,100144,'ACTIVE'),(142,3,100145,'ACTIVE'),(143,3,100146,'ACTIVE'),(144,3,100147,'ACTIVE'),(145,3,100148,'ACTIVE'),(146,3,100149,'ACTIVE'),(147,3,100150,'ACTIVE'),(148,3,100151,'ACTIVE'),(149,3,100152,'ACTIVE'),(150,3,100153,'ACTIVE'),(151,3,100154,'ACTIVE'),(152,3,100155,'ACTIVE'),(153,3,100156,'ACTIVE'),(154,3,100157,'ACTIVE'),(155,3,100158,'ACTIVE'),(156,2,100159,'ACTIVE'),(157,2,100160,'ACTIVE'),(158,3,100161,'ACTIVE'),(159,3,100162,'ACTIVE'),(160,3,100163,'ACTIVE'),(161,3,100164,'ACTIVE'),(162,3,100165,'ACTIVE'),(163,2,100166,'ACTIVE'),(164,3,100167,'ACTIVE'),(165,2,100168,'ACTIVE'),(166,2,100169,'ACTIVE'),(167,3,100170,'ACTIVE'),(168,2,100171,'ACTIVE'),(169,3,100172,'ACTIVE'),(170,3,100173,'ACTIVE'),(171,3,100174,'ACTIVE'),(172,1,100175,'ACTIVE'),(173,3,100176,'ACTIVE'),(174,2,100177,'ACTIVE'),(175,3,100178,'ACTIVE'),(176,3,100179,'ACTIVE'),(177,2,100180,'ACTIVE'),(178,2,100181,'ACTIVE'),(179,2,100182,'ACTIVE'),(180,3,100183,'ACTIVE'),(181,3,100184,'ACTIVE'),(182,3,100185,'ACTIVE'),(183,2,100186,'ACTIVE'),(184,3,100187,'ACTIVE'),(185,3,100188,'ACTIVE'),(186,3,100189,'ACTIVE'),(187,3,100190,'ACTIVE'),(188,2,100191,'ACTIVE'),(189,3,100192,'ACTIVE'),(190,3,100193,'ACTIVE'),(191,2,100194,'ACTIVE'),(192,3,100195,'ACTIVE'),(193,3,100196,'ACTIVE'),(194,114,100197,'ACTIVE'),(195,3,100198,'ACTIVE'),(196,3,100199,'ACTIVE'),(197,3,100200,'ACTIVE'),(198,3,100201,'ACTIVE'),(199,3,100202,'ACTIVE'),(200,3,100203,'ACTIVE'),(201,3,100204,'ACTIVE'),(202,2,100205,'ACTIVE'),(203,2,100206,'ACTIVE'),(204,3,100207,'ACTIVE'),(205,2,100208,'ACTIVE'),(206,3,100209,'ACTIVE'),(207,2,100211,'ACTIVE'),(208,3,100212,'ACTIVE'),(209,3,100213,'ACTIVE'),(210,3,100214,'ACTIVE'),(211,3,100215,'ACTIVE'),(212,3,100216,'ACTIVE'),(213,3,100217,'ACTIVE'),(214,3,100218,'ACTIVE'),(215,3,100219,'ACTIVE'),(216,3,100220,'ACTIVE'),(217,3,100221,'ACTIVE'),(218,3,100222,'ACTIVE'),(219,3,100223,'ACTIVE'),(220,3,100224,'ACTIVE'),(221,3,100225,'ACTIVE'),(222,3,100226,'ACTIVE'),(223,3,100227,'ACTIVE'),(224,3,100228,'ACTIVE'),(225,3,100229,'ACTIVE'),(226,3,100230,'ACTIVE'),(227,3,100231,'ACTIVE'),(228,3,100232,'ACTIVE'),(229,3,100233,'ACTIVE'),(230,1,100234,'ACTIVE'),(231,3,100235,'ACTIVE'),(232,2,100236,'ACTIVE'),(233,2,100237,'ACTIVE'),(234,2,100238,'ACTIVE'),(235,3,100239,'ACTIVE'),(236,3,100240,'ACTIVE'),(237,1,100241,'ACTIVE'),(238,1,100242,'ACTIVE'),(239,3,100243,'ACTIVE'),(240,3,100244,'ACTIVE'),(241,3,100245,'ACTIVE'),(242,3,100246,'ACTIVE'),(243,2,100247,'ACTIVE'),(244,3,100248,'ACTIVE'),(245,3,100249,'ACTIVE'),(246,3,100250,'ACTIVE'),(247,3,100251,'ACTIVE'),(248,3,100252,'ACTIVE'),(249,2,100253,'ACTIVE'),(250,2,100254,'ACTIVE'),(251,2,100255,'ACTIVE'),(252,3,100256,'ACTIVE'),(253,3,100257,'ACTIVE'),(254,3,100258,'ACTIVE'),(255,3,100259,'ACTIVE'),(256,3,100260,'ACTIVE'),(257,3,100261,'ACTIVE'),(258,3,100262,'ACTIVE'),(259,3,100263,'ACTIVE'),(260,3,100264,'ACTIVE'),(261,3,100265,'ACTIVE'),(262,3,100266,'ACTIVE'),(263,3,100267,'ACTIVE'),(264,3,100268,'ACTIVE'),(265,3,100269,'ACTIVE'),(266,3,100270,'ACTIVE'),(267,2,100271,'ACTIVE'),(268,3,100272,'ACTIVE'),(269,3,100273,'ACTIVE'),(270,2,100274,'ACTIVE'),(271,3,100275,'ACTIVE'),(272,3,100276,'ACTIVE'),(273,3,100277,'ACTIVE'),(274,3,100278,'ACTIVE'),(275,3,100279,'ACTIVE'),(276,3,100280,'ACTIVE'),(277,3,100281,'ACTIVE'),(278,3,100282,'ACTIVE'),(279,3,100283,'ACTIVE'),(280,3,100284,'ACTIVE'),(281,3,100285,'ACTIVE'),(282,1,100286,'ACTIVE'),(283,1,100287,'ACTIVE'),(284,1,100288,'ACTIVE'),(285,2,100289,'ACTIVE'),(286,3,100290,'ACTIVE'),(287,3,100291,'ACTIVE'),(288,3,100292,'ACTIVE'),(289,1,100394,'ACTIVE'),(290,1,100293,'ACTIVE'),(291,3,100294,'ACTIVE'),(292,3,100295,'ACTIVE'),(293,3,100296,'ACTIVE'),(294,3,100297,'ACTIVE'),(295,114,100298,'ACTIVE'),(296,2,100299,'ACTIVE'),(297,3,100300,'ACTIVE'),(298,3,100301,'ACTIVE'),(299,3,100302,'ACTIVE'),(300,3,100303,'ACTIVE'),(301,3,100304,'ACTIVE'),(302,2,100305,'ACTIVE'),(303,3,100306,'ACTIVE'),(304,3,100307,'ACTIVE'),(305,3,100308,'ACTIVE'),(306,3,100309,'ACTIVE'),(307,3,100310,'ACTIVE'),(308,3,100311,'ACTIVE'),(309,1,100312,'ACTIVE'),(310,3,100313,'ACTIVE'),(311,3,100314,'ACTIVE'),(312,3,100315,'ACTIVE'),(313,3,100316,'ACTIVE'),(314,3,100317,'ACTIVE'),(315,3,100318,'ACTIVE'),(316,3,100319,'ACTIVE'),(317,3,100320,'ACTIVE'),(318,3,100321,'ACTIVE'),(319,3,100322,'ACTIVE'),(320,3,100323,'ACTIVE'),(321,3,100324,'ACTIVE'),(322,3,100325,'ACTIVE'),(323,3,100326,'ACTIVE'),(324,3,100327,'ACTIVE'),(325,3,100328,'ACTIVE'),(326,3,100329,'ACTIVE'),(327,2,100330,'ACTIVE'),(328,114,100331,'ACTIVE'),(329,114,100332,'ACTIVE'),(330,114,100333,'ACTIVE'),(331,3,100334,'ACTIVE'),(332,3,100335,'ACTIVE'),(333,1,100336,'ACTIVE'),(334,3,100337,'ACTIVE'),(335,3,100338,'ACTIVE'),(336,3,100339,'ACTIVE'),(337,3,100340,'ACTIVE'),(338,3,100341,'ACTIVE'),(339,3,100342,'ACTIVE'),(340,3,100343,'ACTIVE'),(341,3,100344,'ACTIVE'),(342,3,100345,'ACTIVE'),(343,3,100346,'ACTIVE'),(344,3,100347,'ACTIVE'),(345,3,100348,'ACTIVE'),(346,3,100349,'ACTIVE'),(347,3,100350,'ACTIVE'),(348,3,100351,'ACTIVE'),(349,3,100352,'ACTIVE'),(350,3,100353,'ACTIVE'),(351,3,100354,'ACTIVE'),(352,3,100355,'ACTIVE'),(353,1,100356,'ACTIVE'),(354,3,100357,'ACTIVE'),(355,3,100358,'ACTIVE'),(356,3,100359,'ACTIVE'),(357,3,100360,'ACTIVE'),(358,3,100361,'ACTIVE'),(359,2,100362,'ACTIVE'),(360,3,100363,'ACTIVE'),(361,3,100364,'ACTIVE'),(362,3,100365,'ACTIVE'),(363,3,100366,'ACTIVE'),(364,3,100367,'ACTIVE'),(365,3,100368,'ACTIVE'),(366,3,100369,'ACTIVE'),(367,3,100371,'ACTIVE'),(368,3,100372,'ACTIVE'),(369,3,100373,'ACTIVE'),(370,3,100374,'ACTIVE'),(371,3,100375,'ACTIVE'),(372,3,100376,'ACTIVE'),(373,3,100377,'ACTIVE'),(374,3,100378,'ACTIVE'),(375,3,100379,'ACTIVE'),(376,3,100380,'ACTIVE'),(377,3,100381,'ACTIVE'),(378,3,100382,'ACTIVE'),(379,3,100383,'ACTIVE'),(380,3,100384,'ACTIVE'),(381,3,100385,'ACTIVE'),(382,3,100386,'ACTIVE'),(383,3,100387,'ACTIVE'),(384,3,100388,'ACTIVE'),(385,3,100389,'ACTIVE'),(386,3,100390,'ACTIVE'),(387,3,100391,'ACTIVE'),(388,3,100392,'ACTIVE'),(389,3,100393,'ACTIVE'),(390,4,100004,'ACTIVE'),(391,4,100006,'ACTIVE'),(392,118,100008,'ACTIVE'),(393,58,100017,'ACTIVE'),(394,58,100019,'ACTIVE'),(395,59,100024,'ACTIVE'),(396,4,100028,'ACTIVE'),(397,58,100029,'ACTIVE'),(398,60,100030,'ACTIVE'),(399,58,100036,'ACTIVE'),(400,58,100044,'ACTIVE'),(401,24,100059,'ACTIVE'),(402,24,100060,'ACTIVE'),(403,4,100062,'ACTIVE'),(404,0,100065,'ACTIVE'),(405,101,100066,'ACTIVE'),(406,101,100084,'ACTIVE'),(407,47,100085,'ACTIVE'),(408,58,100088,'ACTIVE'),(409,58,100093,'ACTIVE'),(410,15,100097,'ACTIVE'),(411,4,100098,'ACTIVE'),(412,15,100099,'ACTIVE'),(413,46,100113,'ACTIVE'),(414,43,100120,'ACTIVE'),(415,58,100123,'ACTIVE'),(416,4,100127,'ACTIVE'),(417,4,100128,'ACTIVE'),(418,4,100130,'ACTIVE'),(419,4,100131,'ACTIVE'),(420,43,100141,'ACTIVE'),(421,43,100144,'ACTIVE'),(422,101,100159,'ACTIVE'),(423,123,100160,'ACTIVE'),(424,43,100166,'ACTIVE'),(425,97,100169,'ACTIVE'),(426,43,100171,'ACTIVE'),(427,4,100174,'ACTIVE'),(428,55,100175,'ACTIVE'),(429,58,100180,'ACTIVE'),(430,58,100181,'ACTIVE'),(431,0,100183,'ACTIVE'),(432,0,100184,'ACTIVE'),(433,4,100185,'ACTIVE'),(434,43,100191,'ACTIVE'),(435,43,100194,'ACTIVE'),(436,0,100197,'ACTIVE'),(437,43,100208,'ACTIVE'),(438,43,100211,'ACTIVE'),(439,43,100237,'ACTIVE'),(440,119,100241,'ACTIVE'),(441,119,100242,'ACTIVE'),(442,65,100245,'ACTIVE'),(443,43,100247,'ACTIVE'),(444,101,100254,'ACTIVE'),(445,43,100255,'ACTIVE'),(446,0,100267,'ACTIVE'),(447,6,100269,'ACTIVE'),(448,4,100270,'ACTIVE'),(449,58,100271,'ACTIVE'),(450,0,100273,'ACTIVE'),(451,60,100274,'ACTIVE'),(452,98,100289,'ACTIVE'),(453,51,100297,'ACTIVE'),(454,58,100299,'ACTIVE'),(455,58,100305,'ACTIVE'),(456,116,100312,'ACTIVE'),(457,68,100315,'ACTIVE'),(458,70,100325,'ACTIVE'),(459,70,100326,'ACTIVE'),(460,4,100327,'ACTIVE'),(461,4,100328,'ACTIVE'),(462,58,100330,'ACTIVE'),(463,0,100331,'ACTIVE'),(464,122,100332,'ACTIVE'),(465,122,100333,'ACTIVE'),(466,55,100336,'ACTIVE'),(467,63,100337,'ACTIVE'),(468,0,100343,'ACTIVE'),(469,0,100344,'ACTIVE'),(470,9,100365,'ACTIVE'),(471,63,100372,'ACTIVE'),(472,0,100388,'ACTIVE'),(473,0,100389,'ACTIVE'),(474,5,100004,'ACTIVE'),(475,5,100006,'ACTIVE'),(476,83,100029,'ACTIVE'),(477,89,100030,'ACTIVE'),(478,57,100041,'ACTIVE'),(479,85,100044,'ACTIVE'),(480,99,100045,'ACTIVE'),(481,57,100047,'ACTIVE'),(482,86,100049,'ACTIVE'),(483,87,100059,'ACTIVE'),(484,87,100060,'ACTIVE'),(485,5,100062,'ACTIVE'),(486,26,100065,'ACTIVE'),(487,88,100084,'ACTIVE'),(488,48,100085,'ACTIVE'),(489,86,100090,'ACTIVE'),(490,86,100091,'ACTIVE'),(491,71,100092,'ACTIVE'),(492,71,100094,'ACTIVE'),(493,72,100101,'ACTIVE'),(494,73,100102,'ACTIVE'),(495,71,100104,'ACTIVE'),(496,74,100105,'ACTIVE'),(497,73,100108,'ACTIVE'),(498,75,100117,'ACTIVE'),(499,76,100118,'ACTIVE'),(500,71,100126,'ACTIVE'),(501,71,100139,'ACTIVE'),(502,77,100140,'ACTIVE'),(503,78,100151,'ACTIVE'),(504,71,100163,'ACTIVE'),(505,73,100165,'ACTIVE'),(506,77,100189,'ACTIVE'),(507,79,100196,'ACTIVE'),(508,80,100198,'ACTIVE'),(509,77,100200,'ACTIVE'),(510,72,100202,'ACTIVE'),(511,81,100203,'ACTIVE'),(512,82,100212,'ACTIVE'),(513,86,100228,'ACTIVE'),(514,86,100229,'ACTIVE'),(515,86,100232,'ACTIVE'),(516,86,100233,'ACTIVE'),(517,120,100241,'ACTIVE'),(518,120,100242,'ACTIVE'),(519,77,100251,'ACTIVE'),(520,79,100263,'ACTIVE'),(521,71,100268,'ACTIVE'),(522,77,100290,'ACTIVE'),(523,79,100300,'ACTIVE'),(524,79,100308,'ACTIVE'),(525,82,100309,'ACTIVE'),(526,71,100310,'ACTIVE'),(527,79,100314,'ACTIVE'),(528,71,100316,'ACTIVE'),(529,71,100317,'ACTIVE'),(530,38,100346,'ACTIVE'),(531,38,100347,'ACTIVE'),(532,71,100350,'ACTIVE'),(533,71,100351,'ACTIVE'),(534,71,100352,'ACTIVE'),(535,74,100359,'ACTIVE'),(536,72,100361,'ACTIVE'),(537,79,100363,'ACTIVE'),(538,86,100380,'ACTIVE'),(539,99,100383,'ACTIVE'),(540,79,100387,'ACTIVE'),(541,79,100393,'ACTIVE'),(542,9,100007,'ACTIVE'),(543,100,100011,'ACTIVE'),(544,101,100023,'ACTIVE'),(545,102,100025,'ACTIVE'),(546,58,100035,'ACTIVE'),(547,102,100044,'ACTIVE'),(548,101,100055,'ACTIVE'),(549,58,100066,'ACTIVE'),(550,58,100068,'ACTIVE'),(551,58,100069,'ACTIVE'),(552,58,100077,'ACTIVE'),(553,101,100080,'ACTIVE'),(554,58,100084,'ACTIVE'),(555,102,100085,'ACTIVE'),(556,48,100095,'ACTIVE'),(557,101,100096,'ACTIVE'),(558,58,100113,'ACTIVE'),(559,55,100118,'ACTIVE'),(560,42,100120,'ACTIVE'),(561,42,100141,'ACTIVE'),(562,42,100144,'ACTIVE'),(563,100,100145,'ACTIVE'),(564,104,100150,'ACTIVE'),(565,105,100153,'ACTIVE'),(566,106,100156,'ACTIVE'),(567,55,100158,'ACTIVE'),(568,58,100159,'ACTIVE'),(569,42,100166,'ACTIVE'),(570,58,100168,'ACTIVE'),(571,48,100175,'ACTIVE'),(572,15,100184,'ACTIVE'),(573,15,100185,'ACTIVE'),(574,58,100186,'ACTIVE'),(575,101,100191,'ACTIVE'),(576,4,100197,'ACTIVE'),(577,4,100202,'ACTIVE'),(578,108,100203,'ACTIVE'),(579,109,100207,'ACTIVE'),(580,42,100208,'ACTIVE'),(581,101,100210,'ACTIVE'),(582,42,100211,'ACTIVE'),(583,110,100234,'ACTIVE'),(584,103,100238,'ACTIVE'),(585,48,100241,'ACTIVE'),(586,48,100242,'ACTIVE'),(587,105,100246,'ACTIVE'),(588,58,100254,'ACTIVE'),(589,42,100255,'ACTIVE'),(590,100,100256,'ACTIVE'),(591,10,100266,'ACTIVE'),(592,4,100267,'ACTIVE'),(593,4,100269,'ACTIVE'),(594,101,100271,'ACTIVE'),(595,54,100274,'ACTIVE'),(596,55,100285,'ACTIVE'),(597,55,100286,'ACTIVE'),(598,55,100287,'ACTIVE'),(599,55,100288,'ACTIVE'),(600,50,100394,'ACTIVE'),(601,111,100294,'ACTIVE'),(602,112,100297,'ACTIVE'),(603,44,100298,'ACTIVE'),(604,10,100331,'ACTIVE'),(605,44,100332,'ACTIVE'),(606,44,100333,'ACTIVE'),(607,55,100337,'ACTIVE'),(608,10,100340,'ACTIVE'),(609,4,100341,'ACTIVE'),(610,10,100343,'ACTIVE'),(611,10,100346,'ACTIVE'),(612,10,100347,'ACTIVE'),(613,50,100356,'ACTIVE'),(614,4,100359,'ACTIVE'),(615,58,100362,'ACTIVE'),(616,24,100365,'ACTIVE'),(617,58,100370,'ACTIVE'),(618,79,100018,'ACTIVE'),(619,79,100395,'ACTIVE'),(620,79,100396,'ACTIVE'),(621,3,100018,'ACTIVE'),(622,3,100395,'ACTIVE'),(623,3,100396,'ACTIVE'),(624,3,100397,'ACTIVE'),(625,3,100398,'ACTIVE'),(626,3,100399,'ACTIVE'),(627,3,100400,'ACTIVE'),(628,3,100401,'ACTIVE'),(629,3,100402,'ACTIVE'),(630,3,100403,'ACTIVE'),(631,3,100404,'ACTIVE'),(632,3,100405,'ACTIVE'),(633,3,100406,'ACTIVE'),(634,3,100407,'ACTIVE'),(635,3,100408,'ACTIVE'),(636,3,100409,'ACTIVE'),(637,3,100410,'ACTIVE'),(638,3,100411,'ACTIVE'),(639,3,100412,'ACTIVE'),(640,3,100413,'ACTIVE'),(641,3,100414,'ACTIVE'),(642,3,100415,'ACTIVE'),(643,3,100416,'ACTIVE'),(644,3,100417,'ACTIVE'),(645,3,100418,'ACTIVE'),(646,3,100419,'ACTIVE'),(647,3,100420,'ACTIVE'),(648,3,100421,'ACTIVE'),(649,3,100422,'ACTIVE'),(650,3,100423,'ACTIVE'),(651,3,100424,'ACTIVE'),(652,3,100425,'ACTIVE'),(653,3,100426,'ACTIVE'),(654,3,100427,'ACTIVE'),(655,3,100428,'ACTIVE'),(656,3,100429,'ACTIVE'),(657,3,100430,'ACTIVE'),(658,3,100431,'ACTIVE'),(659,3,100432,'ACTIVE'),(660,3,100433,'ACTIVE'),(661,3,100434,'ACTIVE'),(662,3,100435,'ACTIVE'),(663,3,100436,'ACTIVE'),(664,3,100437,'ACTIVE'),(665,3,100438,'ACTIVE'),(666,3,100439,'ACTIVE'),(667,3,100440,'ACTIVE'),(668,3,100441,'ACTIVE'),(669,3,100442,'ACTIVE'),(670,3,100443,'ACTIVE'),(671,3,100444,'ACTIVE'),(672,3,100445,'ACTIVE'),(673,3,100446,'ACTIVE'),(674,3,100447,'ACTIVE'),(675,3,100448,'ACTIVE'),(676,3,100449,'ACTIVE'),(677,3,100450,'ACTIVE'),(678,3,100451,'ACTIVE'),(679,3,100452,'ACTIVE'),(680,3,100453,'ACTIVE'),(681,3,100454,'ACTIVE'),(682,3,100455,'ACTIVE'),(683,3,100456,'ACTIVE'),(684,3,100457,'ACTIVE'),(685,3,100458,'ACTIVE'),(686,3,100459,'ACTIVE'),(687,3,100460,'ACTIVE'),(688,3,100461,'ACTIVE'),(689,3,100462,'ACTIVE'),(690,3,100463,'ACTIVE'),(691,3,100464,'ACTIVE'),(692,3,100465,'ACTIVE'),(693,3,100466,'ACTIVE'),(694,3,100467,'ACTIVE'),(695,3,100468,'ACTIVE'),(696,3,100469,'ACTIVE'),(697,3,100470,'ACTIVE'),(698,3,100471,'ACTIVE'),(699,3,100472,'ACTIVE'),(700,3,100473,'ACTIVE'),(701,3,100474,'ACTIVE'),(702,3,100475,'ACTIVE'),(703,3,100476,'ACTIVE'),(704,3,100477,'ACTIVE'),(705,3,100478,'ACTIVE'),(706,3,100479,'ACTIVE'),(707,3,100480,'ACTIVE'),(708,3,100481,'ACTIVE'),(709,3,100482,'ACTIVE'),(710,3,100483,'ACTIVE'),(711,3,100484,'ACTIVE'),(712,3,100485,'ACTIVE'),(713,3,100486,'ACTIVE'),(714,3,100487,'ACTIVE'),(715,3,100488,'ACTIVE'),(716,3,100489,'ACTIVE'),(717,3,100490,'ACTIVE'),(718,3,100491,'ACTIVE'),(719,3,100492,'ACTIVE'),(720,3,100493,'ACTIVE'),(721,3,100494,'ACTIVE'),(722,3,100495,'ACTIVE'),(723,3,100496,'ACTIVE'),(724,3,100497,'ACTIVE'),(725,3,100498,'ACTIVE'),(726,3,100499,'ACTIVE'),(727,3,100500,'ACTIVE'),(728,3,100501,'ACTIVE'),(729,3,100502,'ACTIVE'),(730,3,100503,'ACTIVE'),(731,3,100504,'ACTIVE'),(732,3,100505,'ACTIVE'),(733,3,100506,'ACTIVE'),(734,3,100507,'ACTIVE'),(735,3,100508,'ACTIVE'),(736,3,100509,'ACTIVE'),(737,3,100510,'ACTIVE'),(738,3,100009,'ACTIVE'),(739,3,100511,'ACTIVE'),(740,3,100512,'ACTIVE');
/*!40000 ALTER TABLE `PRODUCT_PACKAGING_MAPPING` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PURCHASE_ORDER`
--

DROP TABLE IF EXISTS `PURCHASE_ORDER`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PURCHASE_ORDER` (
  `PURCHASE_ORDER_ID` int(11) NOT NULL AUTO_INCREMENT,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `GENERATION_UNIT_ID` int(11) NOT NULL,
  `GENERATED_FOR_UNIT_ID` int(11) NOT NULL,
  `GENERATED_BY` int(11) NOT NULL,
  `BILL_AMOUNT` decimal(10,2) DEFAULT NULL,
  `PAID_AMOUNT` decimal(10,2) DEFAULT NULL,
  `ORDER_RECEIPT_NUMBER` varchar(255) DEFAULT NULL,
  `PURCHASE_ORDER_STATUS` varchar(30) NOT NULL,
  `COMMENT` varchar(1000) DEFAULT NULL,
  `REQUEST_ORDER_ID` int(11) DEFAULT NULL,
  `INITIATION_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `LAST_UPDATE_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `FULFILLMENT_DATE` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`PURCHASE_ORDER_ID`),
  KEY `REQUEST_ORDER_ID` (`REQUEST_ORDER_ID`),
  CONSTRAINT `PURCHASE_ORDER_ibfk_1` FOREIGN KEY (`REQUEST_ORDER_ID`) REFERENCES `REQUEST_ORDER` (`REQUEST_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PURCHASE_ORDER`
--

LOCK TABLES `PURCHASE_ORDER` WRITE;
/*!40000 ALTER TABLE `PURCHASE_ORDER` DISABLE KEYS */;
INSERT INTO `PURCHASE_ORDER` VALUES (16,'2016-06-27 21:11:39',10000,10000,120056,NULL,NULL,NULL,'SETTLED',NULL,85,'2016-06-27 21:11:39','2016-06-27 21:11:39','2016-06-27 21:11:39'),(17,'2016-06-27 23:13:21',13001,13001,120056,NULL,NULL,NULL,'SETTLED',NULL,86,'2016-06-27 23:13:21','2016-06-27 23:13:21','2016-06-27 23:13:21'),(18,'2016-06-27 23:41:23',10000,10000,120056,NULL,NULL,NULL,'SETTLED',NULL,30,'2016-06-27 23:41:23','2016-06-27 23:41:23','2016-06-27 23:41:23'),(19,'2016-06-27 23:49:19',10000,10000,120056,NULL,NULL,NULL,'CREATED',NULL,1,'2016-06-27 23:49:19','2016-06-27 23:49:19','2016-06-27 23:49:19'),(20,'2016-06-27 23:55:46',10000,10000,120056,NULL,NULL,NULL,'CREATED',NULL,3,'2016-06-27 23:55:46','2016-06-27 23:55:46','2016-06-27 23:55:46'),(21,'2016-06-27 23:56:06',10000,10000,120056,NULL,NULL,NULL,'SETTLED',NULL,8,'2016-06-27 23:56:06','2016-06-27 23:56:06','2016-06-27 23:56:06'),(22,'2016-06-28 09:37:47',10000,10000,120056,NULL,NULL,NULL,'CREATED',NULL,5,'2016-06-28 09:37:47','2016-06-28 09:37:47','2016-06-28 09:37:47'),(23,'2016-06-28 18:24:12',10006,10006,120056,NULL,NULL,NULL,'SETTLED',NULL,87,'2016-06-28 18:24:12','2016-06-28 18:24:12','2016-06-28 18:24:12');
/*!40000 ALTER TABLE `PURCHASE_ORDER` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PURCHASE_ORDER_ITEM`
--

DROP TABLE IF EXISTS `PURCHASE_ORDER_ITEM`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `PURCHASE_ORDER_ITEM` (
  `PURCHASE_ORDER_ITEM_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SKU_ID` int(11) NOT NULL,
  `SKU_NAME` varchar(255) NOT NULL,
  `REQUESTED_QUANTITY` decimal(10,2) NOT NULL,
  `REQUESTED_ABSOLUTE_QUANTITY` decimal(10,2) NOT NULL,
  `TRANSFERRED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `RECEIVED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `UNIT_OF_MEASURE` varchar(10) NOT NULL,
  `UNIT_PRICE` decimal(10,2) DEFAULT NULL,
  `NEGOTIATED_UNIT_PRICE` decimal(10,2) DEFAULT NULL,
  `TOTAL_COST` decimal(10,2) DEFAULT NULL,
  `AMOUNT_PAID` decimal(10,2) DEFAULT NULL,
  `PURCHASE_ORDER_ID` int(11) NOT NULL,
  PRIMARY KEY (`PURCHASE_ORDER_ITEM_ID`),
  KEY `PURCHASE_ORDER_ID` (`PURCHASE_ORDER_ID`),
  CONSTRAINT `PURCHASE_ORDER_ITEM_ibfk_1` FOREIGN KEY (`PURCHASE_ORDER_ID`) REFERENCES `PURCHASE_ORDER` (`PURCHASE_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PURCHASE_ORDER_ITEM`
--

LOCK TABLES `PURCHASE_ORDER_ITEM` WRITE;
/*!40000 ALTER TABLE `PURCHASE_ORDER_ITEM` DISABLE KEYS */;
INSERT INTO `PURCHASE_ORDER_ITEM` VALUES (18,1,'Milk Amul',0.00,0.00,1.50,0.00,'L',39.50,NULL,NULL,NULL,16),(19,22,'Sachet - Desi Regular',0.00,0.00,20.00,0.00,'KG',0.00,NULL,NULL,NULL,17),(20,19,'Hot Cup 250 ML',0.00,0.00,1000.00,0.00,'PC',0.00,NULL,NULL,NULL,17),(21,1,'Milk Amul',0.00,0.00,1.00,0.00,'L',39.50,NULL,NULL,NULL,18),(22,1,'Milk Amul',0.00,0.00,10.00,0.00,'L',39.50,NULL,NULL,NULL,19),(23,1,'Milk Amul',0.00,0.00,2.50,0.00,'L',39.50,NULL,NULL,NULL,20),(24,8,'Pav',0.00,0.00,1.00,0.00,'PC',0.00,NULL,NULL,NULL,21),(25,8,'Pav',0.00,0.00,1.00,0.00,'PC',0.00,NULL,NULL,NULL,22),(26,1,'Milk Amul',0.00,0.00,12.00,0.00,'L',39.50,NULL,NULL,NULL,23),(27,8,'Pav',0.00,0.00,10.00,0.00,'PC',0.00,NULL,NULL,NULL,23);
/*!40000 ALTER TABLE `PURCHASE_ORDER_ITEM` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `REFERENCE_ORDER`
--

DROP TABLE IF EXISTS `REFERENCE_ORDER`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `REFERENCE_ORDER` (
  `REFERENCE_ORDER_ID` int(11) NOT NULL AUTO_INCREMENT,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `REQUEST_UNIT_ID` int(11) NOT NULL,
  `GENERATED_BY` int(11) NOT NULL,
  `FULFILLMENT_UNIT_ID` int(11) DEFAULT NULL,
  `FULFILLMENT_DATE` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `COMMENT` varchar(1000) DEFAULT NULL,
  `REFERENCE_ORDER_STATUS` varchar(10) NOT NULL,
  `INITIATION_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `LAST_UPDATE_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`REFERENCE_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=92 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `REFERENCE_ORDER`
--

LOCK TABLES `REFERENCE_ORDER` WRITE;
/*!40000 ALTER TABLE `REFERENCE_ORDER` DISABLE KEYS */;
INSERT INTO `REFERENCE_ORDER` VALUES (1,'2016-06-26 00:00:00',10000,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(2,'2016-06-26 00:00:00',10000,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(3,'2016-06-26 00:00:00',10000,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(4,'2016-06-26 00:00:00',10000,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(5,'2016-06-26 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(6,'2016-06-26 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(7,'2016-06-26 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(8,'2016-06-26 00:00:00',12015,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(9,'2016-06-26 00:00:00',12015,100000,NULL,'2016-06-27 05:30:00',NULL,'CREATED','2016-06-26 00:00:00','2016-06-26 00:00:00'),(10,'2016-06-27 00:00:00',10011,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(11,'2016-06-27 00:00:00',10000,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(12,'2016-06-27 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(13,'2016-06-27 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(14,'2016-06-27 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(15,'2016-06-27 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(16,'2016-06-27 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(17,'2016-06-27 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(18,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(19,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(20,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(21,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(22,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(23,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(24,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(25,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(26,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(27,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(28,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(29,'2016-06-27 00:00:00',10012,100000,NULL,'2016-06-29 05:30:00','Oh Yeah !!!','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(30,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(31,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(32,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(33,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(34,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(35,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(36,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(37,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(38,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(39,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(40,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(41,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(42,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(43,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(44,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(45,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(46,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(47,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(48,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(49,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(50,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(51,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(52,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(53,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(54,'2016-06-27 00:00:00',10008,100000,NULL,'2016-06-30 05:30:00','Oh Yes Baby...','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(55,'2016-06-27 00:00:00',10000,100000,NULL,'2016-06-29 05:30:00','Hello','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(56,'2016-06-27 00:00:00',10000,100000,NULL,'2016-06-30 05:30:00','Hola !','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(57,'2016-06-27 00:00:00',10000,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(59,'2016-06-27 00:00:00',10000,100000,NULL,'2016-07-27 05:30:00','send fast','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(60,'2016-06-27 00:00:00',10006,100000,NULL,'2016-06-28 05:30:00','Urgent delivery','CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(61,'2016-06-27 00:00:00',10006,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-27 00:00:00','2016-06-27 00:00:00'),(62,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(63,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(64,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(65,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(66,'2016-06-28 00:00:00',10001,100000,NULL,'2016-06-30 05:30:00','Yes','CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(67,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-29 05:30:00','asap','CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(68,'2016-06-28 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(69,'2016-06-28 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(70,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(71,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(72,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(73,'2016-06-28 00:00:00',10002,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(74,'2016-06-28 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(75,'2016-06-28 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(76,'2016-06-28 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(77,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(78,'2016-06-28 00:00:00',10002,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(79,'2016-06-28 00:00:00',10006,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(80,'2016-06-28 00:00:00',10002,100000,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(81,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(82,'2016-06-28 00:00:00',10004,100000,NULL,'2016-06-30 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(83,'2016-06-28 00:00:00',10000,120063,NULL,'2016-06-29 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(84,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-28 05:30:00',NULL,'CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(85,'2016-06-28 00:00:00',10000,100000,NULL,'2016-06-30 05:30:00','done','CREATED','2016-06-28 00:00:00','2016-06-28 00:00:00'),(86,'2016-06-29 21:29:21',10000,100026,NULL,'2016-06-30 05:30:00','ds','CREATED','2016-06-29 21:29:21','2016-06-29 21:29:21'),(87,'2016-06-29 21:29:31',10000,100026,NULL,'2016-06-30 05:30:00','fs','CREATED','2016-06-29 21:29:31','2016-06-29 21:29:31'),(88,'2016-06-30 10:46:20',10000,100000,NULL,'2016-07-02 00:00:00',NULL,'CREATED','2016-06-30 10:46:20','2016-06-30 10:46:20'),(89,'2016-06-30 10:46:39',10000,100000,NULL,'2016-07-02 00:00:00',NULL,'CREATED','2016-06-30 10:46:39','2016-06-30 10:46:39'),(90,'2016-06-30 10:47:42',10000,100000,NULL,'2016-07-02 00:00:00',NULL,'CREATED','2016-06-30 10:47:42','2016-06-30 10:47:42'),(91,'2016-06-30 10:48:53',10000,100000,NULL,'2016-07-02 00:00:00','rocking','CREATED','2016-06-30 10:48:53','2016-06-30 10:48:53');
/*!40000 ALTER TABLE `REFERENCE_ORDER` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `REFERENCE_ORDER_MENU_ITEM`
--

DROP TABLE IF EXISTS `REFERENCE_ORDER_MENU_ITEM`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `REFERENCE_ORDER_MENU_ITEM` (
  `MENU_ITEM_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PRODUCT_ID` int(11) NOT NULL,
  `PRODUCT_NAME` varchar(255) NOT NULL,
  `REQUESTED_QUANTITY` decimal(10,2) NOT NULL,
  `REQUESTED_ABSOLUTE_QUANTITY` decimal(10,2) NOT NULL,
  `TRANSFERRED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `RECEIVED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `REFERENCE_ORDER_ID` int(11) NOT NULL,
  `DIMENSION` varchar(30) DEFAULT NULL,
  PRIMARY KEY (`MENU_ITEM_ID`),
  KEY `REFERENCE_ORDER_ID` (`REFERENCE_ORDER_ID`),
  CONSTRAINT `REFERENCE_ORDER_MENU_ITEM_ibfk_1` FOREIGN KEY (`REFERENCE_ORDER_ID`) REFERENCES `REFERENCE_ORDER` (`REFERENCE_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=981 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `REFERENCE_ORDER_MENU_ITEM`
--

LOCK TABLES `REFERENCE_ORDER_MENU_ITEM` WRITE;
/*!40000 ALTER TABLE `REFERENCE_ORDER_MENU_ITEM` DISABLE KEYS */;
INSERT INTO `REFERENCE_ORDER_MENU_ITEM` VALUES (1,10,'Desi Chai',150.00,150.00,NULL,NULL,1,'Regular'),(2,630,'Bombay Special',101.00,101.00,NULL,NULL,1,'None'),(3,10,'Desi Chai',150.00,150.00,NULL,NULL,2,'Regular'),(4,630,'Bombay Special',101.00,101.00,NULL,NULL,2,'None'),(5,670,'Vada Pav',1.00,1.00,NULL,NULL,3,'None'),(6,670,'Vada Pav',1.00,1.00,NULL,NULL,4,'None'),(7,670,'Vada Pav',5.00,5.00,NULL,NULL,5,'None'),(8,10,'Desi Chai',100.00,100.00,NULL,NULL,6,'Regular'),(9,10,'Desi Chai',100.00,100.00,NULL,NULL,7,'Regular'),(10,10,'Desi Chai',10.00,10.00,NULL,NULL,8,'Regular'),(11,10,'Desi Chai',10.00,10.00,NULL,NULL,8,'Full'),(12,10,'Desi Chai',5.00,5.00,NULL,NULL,8,'ChotiKetli'),(13,10,'Desi Chai',5.00,5.00,NULL,NULL,8,'BadiKetli'),(14,11,'Full Doodh',7.00,7.00,NULL,NULL,8,'Regular'),(15,11,'Full Doodh',6.00,6.00,NULL,NULL,8,'Full'),(16,430,'Rooh Afza',5.00,5.00,NULL,NULL,8,'Regular'),(17,430,'Rooh Afza',4.00,4.00,NULL,NULL,8,'Full'),(18,670,'Vada Pav',5.00,5.00,NULL,NULL,8,'None'),(19,710,'Tulsi Adrak Chai Pack',3.00,3.00,NULL,NULL,8,'None'),(20,10,'Desi Chai',10.00,10.00,NULL,NULL,9,'Regular'),(21,10,'Desi Chai',5.00,5.00,NULL,NULL,9,'Full'),(22,10,'Desi Chai',15.00,15.00,NULL,NULL,9,'ChotiKetli'),(23,10,'Desi Chai',5.00,5.00,NULL,NULL,9,'BadiKetli'),(24,430,'Rooh Afza',10.00,10.00,NULL,NULL,9,'Regular'),(25,430,'Rooh Afza',5.00,5.00,NULL,NULL,9,'Full'),(26,670,'Vada Pav',20.00,20.00,NULL,NULL,9,'None'),(27,10,'Desi Chai',10.00,10.00,NULL,NULL,10,'Regular'),(28,10,'Desi Chai',5.00,5.00,NULL,NULL,10,'Full'),(29,10,'Desi Chai',5.00,5.00,NULL,NULL,10,'ChotiKetli'),(30,10,'Desi Chai',6.00,6.00,NULL,NULL,10,'BadiKetli'),(31,10,'Desi Chai',10.00,10.00,NULL,NULL,11,'Regular'),(32,670,'Vada Pav',5.00,5.00,NULL,NULL,12,'None'),(33,10,'Desi Chai',20.00,20.00,NULL,NULL,13,'Regular'),(34,11,'Full Doodh',12.00,12.00,NULL,NULL,13,'Regular'),(35,430,'Rooh Afza',6.00,6.00,NULL,NULL,13,'Regular'),(36,670,'Vada Pav',5.00,5.00,NULL,NULL,13,'None'),(37,10,'Desi Chai',20.00,20.00,NULL,NULL,14,'Regular'),(38,11,'Full Doodh',5.00,5.00,NULL,NULL,14,'Regular'),(39,430,'Rooh Afza',5.00,5.00,NULL,NULL,14,'Regular'),(40,670,'Vada Pav',10.00,10.00,NULL,NULL,14,'None'),(41,670,'Vada Pav',5.00,5.00,NULL,NULL,15,'None'),(42,670,'Vada Pav',10.00,10.00,NULL,NULL,16,'None'),(43,430,'Rooh Afza',8.00,8.00,NULL,NULL,17,'Regular'),(44,10,'Desi Chai',12.00,12.00,NULL,NULL,18,'Regular'),(45,10,'Desi Chai',12.00,12.00,NULL,NULL,19,'Regular'),(46,10,'Desi Chai',12.00,12.00,NULL,NULL,20,'Regular'),(47,10,'Desi Chai',12.00,12.00,NULL,NULL,21,'Regular'),(48,10,'Desi Chai',12.00,12.00,NULL,NULL,22,'Regular'),(49,10,'Desi Chai',12.00,12.00,NULL,NULL,23,'Regular'),(50,10,'Desi Chai',12.00,12.00,NULL,NULL,24,'Regular'),(51,10,'Desi Chai',12.00,12.00,NULL,NULL,25,'Regular'),(52,10,'Desi Chai',12.00,12.00,NULL,NULL,26,'Regular'),(53,10,'Desi Chai',12.00,12.00,NULL,NULL,27,'Regular'),(54,10,'Desi Chai',12.00,12.00,NULL,NULL,28,'Regular'),(55,10,'Desi Chai',12.00,12.00,NULL,NULL,29,'Regular'),(56,10,'Desi Chai',1.00,1.00,NULL,NULL,55,'Regular'),(57,10,'Desi Chai',1.00,1.00,NULL,NULL,55,'Full'),(58,10,'Desi Chai',2.00,2.00,NULL,NULL,55,'ChotiKetli'),(59,10,'Desi Chai',3.00,3.00,NULL,NULL,55,'BadiKetli'),(60,10,'Desi Chai',20.00,20.00,NULL,NULL,59,'Regular'),(61,10,'Desi Chai',10.00,10.00,NULL,NULL,60,'Regular'),(62,10,'Desi Chai',5.00,5.00,NULL,NULL,60,'Full'),(63,10,'Desi Chai',5.00,5.00,NULL,NULL,60,'ChotiKetli'),(64,10,'Desi Chai',5.00,5.00,NULL,NULL,60,'BadiKetli'),(65,430,'Rooh Afza',5.00,5.00,NULL,NULL,60,'Regular'),(66,430,'Rooh Afza',10.00,10.00,NULL,NULL,60,'Full'),(67,670,'Vada Pav',10.00,10.00,NULL,NULL,60,'None'),(68,10,'Desi Chai',10.00,10.00,NULL,NULL,61,'Regular'),(69,10,'Desi Chai',5.00,5.00,NULL,NULL,61,'Full'),(70,10,'Desi Chai',5.00,5.00,NULL,NULL,61,'ChotiKetli'),(71,10,'Desi Chai',5.00,5.00,NULL,NULL,61,'BadiKetli'),(72,11,'Full Doodh',5.00,5.00,NULL,NULL,61,'Regular'),(73,11,'Full Doodh',2.00,2.00,NULL,NULL,61,'Full'),(74,430,'Rooh Afza',5.00,5.00,NULL,NULL,61,'Regular'),(75,430,'Rooh Afza',10.00,10.00,NULL,NULL,61,'Full'),(76,670,'Vada Pav',10.00,10.00,NULL,NULL,61,'None'),(77,10,'Desi Chai',1.00,1.00,NULL,NULL,62,'Regular'),(78,11,'Full Doodh',1.00,1.00,NULL,NULL,62,'Regular'),(79,670,'Vada Pav',1.00,1.00,NULL,NULL,62,'None'),(80,10,'Desi Chai',1.00,1.00,NULL,NULL,63,'Regular'),(81,10,'Desi Chai',1.00,1.00,NULL,NULL,64,'Regular'),(82,10,'Desi Chai',1.00,1.00,NULL,NULL,65,'Regular'),(83,10,'Desi Chai',1.00,1.00,NULL,NULL,66,'Regular'),(84,11,'Full Doodh',120.00,120.00,NULL,NULL,67,'Regular'),(85,10,'Desi Chai',100.00,100.00,NULL,NULL,68,'Regular'),(86,670,'Vada Pav',100.00,100.00,NULL,NULL,68,'None'),(87,10,'Desi Chai',100.00,100.00,NULL,NULL,69,'Regular'),(88,670,'Vada Pav',100.00,100.00,NULL,NULL,69,'None'),(89,500,'Sicilian Chicken',10.00,10.00,NULL,NULL,70,'None'),(90,510,'Green Chicken',5.00,5.00,NULL,NULL,70,'None'),(91,520,'Pepper Chicken',4.00,4.00,NULL,NULL,70,'None'),(92,530,'Napoli',8.00,8.00,NULL,NULL,70,'None'),(93,740,'Blueberry Cake',5.00,5.00,NULL,NULL,70,'None'),(94,750,'Banana Cake',5.00,5.00,NULL,NULL,70,'None'),(95,10,'Desi Chai',120.00,120.00,NULL,NULL,71,'Regular'),(96,740,'Blueberry Cake',12.00,12.00,NULL,NULL,72,'None'),(97,750,'Banana Cake',16.00,16.00,NULL,NULL,72,'None'),(98,760,'Carrot Cake',12.00,12.00,NULL,NULL,72,'None'),(99,670,'Vada Pav',40.00,40.00,NULL,NULL,73,'None'),(100,10,'Desi Chai',15.00,15.00,NULL,NULL,74,'Regular'),(101,10,'Desi Chai',2.00,2.00,NULL,NULL,74,'BadiKetli'),(102,430,'Rooh Afza',10.00,10.00,NULL,NULL,74,'Regular'),(103,670,'Vada Pav',10.00,10.00,NULL,NULL,74,'None'),(104,10,'Desi Chai',100.00,100.00,NULL,NULL,75,'Regular'),(105,10,'Desi Chai',100.00,100.00,NULL,NULL,75,'Full'),(106,430,'Rooh Afza',100.00,100.00,NULL,NULL,75,'Regular'),(107,430,'Rooh Afza',100.00,100.00,NULL,NULL,75,'Full'),(108,670,'Vada Pav',100.00,100.00,NULL,NULL,75,'None'),(109,10,'Desi Chai',100.00,100.00,NULL,NULL,76,'Regular'),(110,10,'Desi Chai',100.00,100.00,NULL,NULL,76,'Full'),(111,430,'Rooh Afza',100.00,100.00,NULL,NULL,76,'Regular'),(112,430,'Rooh Afza',100.00,100.00,NULL,NULL,76,'Full'),(113,670,'Vada Pav',100.00,100.00,NULL,NULL,76,'None'),(114,10,'Desi Chai',100.00,100.00,NULL,NULL,77,'Regular'),(115,10,'Desi Chai',100.00,100.00,NULL,NULL,77,'Full'),(116,10,'Desi Chai',200.00,200.00,NULL,NULL,78,'Regular'),(117,11,'Full Doodh',45.00,45.00,NULL,NULL,78,'Regular'),(118,430,'Rooh Afza',50.00,50.00,NULL,NULL,78,'Full'),(119,670,'Vada Pav',100.00,100.00,NULL,NULL,78,'None'),(120,10,'Desi Chai',100.00,100.00,NULL,NULL,79,'Regular'),(121,10,'Desi Chai',200.00,200.00,NULL,NULL,80,'Regular'),(122,11,'Full Doodh',45.00,45.00,NULL,NULL,80,'Regular'),(123,430,'Rooh Afza',50.00,50.00,NULL,NULL,80,'Full'),(124,670,'Vada Pav',100.00,100.00,NULL,NULL,80,'None'),(125,10,'Desi Chai',89.00,89.00,NULL,NULL,81,'Regular'),(126,11,'Full Doodh',67.00,67.00,NULL,NULL,81,'Regular'),(127,430,'Rooh Afza',60.00,60.00,NULL,NULL,81,'Regular'),(128,670,'Vada Pav',50.00,50.00,NULL,NULL,81,'None'),(129,10,'Desi Chai',200.00,200.00,NULL,NULL,82,'Regular'),(130,10,'Desi Chai',5.00,5.00,NULL,NULL,83,'Regular'),(131,10,'Desi Chai',4.00,4.00,NULL,NULL,83,'Full'),(132,10,'Desi Chai',6.00,6.00,NULL,NULL,83,'ChotiKetli'),(133,10,'Desi Chai',200.00,200.00,NULL,NULL,84,'Regular'),(134,10,'Desi Chai',200.00,200.00,NULL,NULL,85,'Regular'),(135,500,'Sicilian Chicken',100.00,100.00,NULL,NULL,86,'None'),(136,510,'Green Chicken',100.00,100.00,NULL,NULL,86,'None'),(137,520,'Pepper Chicken',100.00,100.00,NULL,NULL,86,'None'),(138,530,'Napoli',100.00,100.00,NULL,NULL,86,'None'),(139,540,'Spinach Corn Cheese',100.00,100.00,NULL,NULL,86,'None'),(140,550,'Balsamico',100.00,100.00,NULL,NULL,86,'None'),(141,560,'Chocolate Bun',100.00,100.00,NULL,NULL,86,'None'),(142,570,'Butter-Chicken Wrap',100.00,100.00,NULL,NULL,86,'None'),(143,580,'Mutton Lazeez Wrap',100.00,100.00,NULL,NULL,86,'None'),(144,590,'Kadhai Paneer Wrap',100.00,100.00,NULL,NULL,86,'None'),(145,600,'Chatpata Kebab Wrap',100.00,100.00,NULL,NULL,86,'None'),(146,610,'2 Minutes Sandwich',100.00,100.00,NULL,NULL,86,'None'),(147,620,'Homestyle Aloo',100.00,100.00,NULL,NULL,86,'None'),(148,630,'Bombay Special',100.00,100.00,NULL,NULL,86,'None'),(149,640,'Keema Pav',100.00,100.00,NULL,NULL,86,'None'),(150,650,'Egg Bun',100.00,100.00,NULL,NULL,86,'None'),(151,651,'Paneer Sambossa',100.00,100.00,NULL,NULL,86,'Single'),(152,652,'Chicken Sambossa',100.00,100.00,NULL,NULL,86,'Single'),(153,660,'Poha',100.00,100.00,NULL,NULL,86,'None'),(154,670,'Vada Pav',100.00,100.00,NULL,NULL,86,'None'),(155,680,'Bun Bhujia',100.00,100.00,NULL,NULL,86,'None'),(156,681,'Bun Maska - Achari',100.00,100.00,NULL,NULL,86,'None'),(157,682,'Bun Maska - Honey Lemon',100.00,100.00,NULL,NULL,86,'None'),(158,683,'Bun Maska - Sundried Tomatoes & Olives',100.00,100.00,NULL,NULL,86,'None'),(159,684,'Bun Maska - Mint Jalapeno',100.00,100.00,NULL,NULL,86,'None'),(160,690,'Bun Maska',100.00,100.00,NULL,NULL,86,'None'),(161,865,'Gujiya',100.00,100.00,NULL,NULL,86,'Double'),(162,740,'Blueberry Cake',100.00,100.00,NULL,NULL,86,'None'),(163,750,'Banana Cake',100.00,100.00,NULL,NULL,86,'None'),(164,760,'Carrot Cake',100.00,100.00,NULL,NULL,86,'None'),(165,770,'Lemon Cake',100.00,100.00,NULL,NULL,86,'None'),(166,780,'Rusk',100.00,100.00,NULL,NULL,86,'None'),(167,790,'Jeera Cookies',100.00,100.00,NULL,NULL,86,'None'),(168,800,'Badam Pista Cookies',100.00,100.00,NULL,NULL,86,'None'),(169,810,'Oatmeal Cookies',100.00,100.00,NULL,NULL,86,'None'),(170,866,'Chocolate Cupcake',100.00,100.00,NULL,NULL,86,'None'),(171,867,'Strawberry Cupcake',100.00,100.00,NULL,NULL,86,'None'),(172,500,'Sicilian Chicken',100.00,100.00,NULL,NULL,87,'None'),(173,510,'Green Chicken',100.00,100.00,NULL,NULL,87,'None'),(174,520,'Pepper Chicken',100.00,100.00,NULL,NULL,87,'None'),(175,530,'Napoli',100.00,100.00,NULL,NULL,87,'None'),(176,540,'Spinach Corn Cheese',100.00,100.00,NULL,NULL,87,'None'),(177,550,'Balsamico',100.00,100.00,NULL,NULL,87,'None'),(178,560,'Chocolate Bun',100.00,100.00,NULL,NULL,87,'None'),(179,570,'Butter-Chicken Wrap',100.00,100.00,NULL,NULL,87,'None'),(180,580,'Mutton Lazeez Wrap',100.00,100.00,NULL,NULL,87,'None'),(181,590,'Kadhai Paneer Wrap',100.00,100.00,NULL,NULL,87,'None'),(182,600,'Chatpata Kebab Wrap',100.00,100.00,NULL,NULL,87,'None'),(183,610,'2 Minutes Sandwich',100.00,100.00,NULL,NULL,87,'None'),(184,620,'Homestyle Aloo',100.00,100.00,NULL,NULL,87,'None'),(185,630,'Bombay Special',100.00,100.00,NULL,NULL,87,'None'),(186,640,'Keema Pav',100.00,100.00,NULL,NULL,87,'None'),(187,650,'Egg Bun',100.00,100.00,NULL,NULL,87,'None'),(188,651,'Paneer Sambossa',100.00,100.00,NULL,NULL,87,'Single'),(189,652,'Chicken Sambossa',100.00,100.00,NULL,NULL,87,'Single'),(190,660,'Poha',100.00,100.00,NULL,NULL,87,'None'),(191,670,'Vada Pav',100.00,100.00,NULL,NULL,87,'None'),(192,680,'Bun Bhujia',100.00,100.00,NULL,NULL,87,'None'),(193,681,'Bun Maska - Achari',100.00,100.00,NULL,NULL,87,'None'),(194,682,'Bun Maska - Honey Lemon',100.00,100.00,NULL,NULL,87,'None'),(195,683,'Bun Maska - Sundried Tomatoes & Olives',100.00,100.00,NULL,NULL,87,'None'),(196,684,'Bun Maska - Mint Jalapeno',100.00,100.00,NULL,NULL,87,'None'),(197,690,'Bun Maska',100.00,100.00,NULL,NULL,87,'None'),(198,865,'Gujiya',100.00,100.00,NULL,NULL,87,'Double'),(199,740,'Blueberry Cake',100.00,100.00,NULL,NULL,87,'None'),(200,750,'Banana Cake',100.00,100.00,NULL,NULL,87,'None'),(201,760,'Carrot Cake',100.00,100.00,NULL,NULL,87,'None'),(202,770,'Lemon Cake',100.00,100.00,NULL,NULL,87,'None'),(203,780,'Rusk',100.00,100.00,NULL,NULL,87,'None'),(204,790,'Jeera Cookies',100.00,100.00,NULL,NULL,87,'None'),(205,800,'Badam Pista Cookies',100.00,100.00,NULL,NULL,87,'None'),(206,810,'Oatmeal Cookies',100.00,100.00,NULL,NULL,87,'None'),(207,866,'Chocolate Cupcake',100.00,100.00,NULL,NULL,87,'None'),(208,867,'Strawberry Cupcake',100.00,100.00,NULL,NULL,87,'None'),(209,10,'Desi Chai',100.00,100.00,NULL,NULL,88,'Regular'),(210,10,'Desi Chai',100.00,100.00,NULL,NULL,88,'Full'),(211,10,'Desi Chai',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(212,10,'Desi Chai',100.00,100.00,NULL,NULL,88,'BadiKetli'),(213,11,'Full Doodh',100.00,100.00,NULL,NULL,88,'Regular'),(214,11,'Full Doodh',100.00,100.00,NULL,NULL,88,'Full'),(215,11,'Full Doodh',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(216,11,'Full Doodh',100.00,100.00,NULL,NULL,88,'BadiKetli'),(217,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,88,'Regular'),(218,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,88,'Full'),(219,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(220,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,88,'BadiKetli'),(221,20,'Sulemani Chai',100.00,100.00,NULL,NULL,88,'None'),(222,30,'Cutting Chai',100.00,100.00,NULL,NULL,88,'None'),(223,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,88,'Regular'),(224,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,88,'Full'),(225,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(226,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,88,'BadiKetli'),(227,60,'God\'s Chai',100.00,100.00,NULL,NULL,88,'Regular'),(228,60,'God\'s Chai',100.00,100.00,NULL,NULL,88,'Full'),(229,60,'God\'s Chai',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(230,60,'God\'s Chai',100.00,100.00,NULL,NULL,88,'BadiKetli'),(231,80,'Kulhad Chai',100.00,100.00,NULL,NULL,88,'None'),(232,80,'Kulhad Chai',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(233,80,'Kulhad Chai',100.00,100.00,NULL,NULL,88,'BadiKetli'),(234,90,'Cinnamon Green',100.00,100.00,NULL,NULL,88,'Regular'),(235,90,'Cinnamon Green',100.00,100.00,NULL,NULL,88,'Full'),(236,90,'Cinnamon Green',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(237,90,'Cinnamon Green',100.00,100.00,NULL,NULL,88,'BadiKetli'),(238,100,'Lemon Green',100.00,100.00,NULL,NULL,88,'Regular'),(239,100,'Lemon Green',100.00,100.00,NULL,NULL,88,'Full'),(240,100,'Lemon Green',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(241,100,'Lemon Green',100.00,100.00,NULL,NULL,88,'BadiKetli'),(242,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,88,'Regular'),(243,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,88,'Full'),(244,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(245,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,88,'BadiKetli'),(246,120,'Moroccan Mint',100.00,100.00,NULL,NULL,88,'Regular'),(247,120,'Moroccan Mint',100.00,100.00,NULL,NULL,88,'Full'),(248,120,'Moroccan Mint',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(249,120,'Moroccan Mint',100.00,100.00,NULL,NULL,88,'BadiKetli'),(250,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,88,'Regular'),(251,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,88,'Full'),(252,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(253,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,88,'BadiKetli'),(254,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,88,'Regular'),(255,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,88,'Full'),(256,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(257,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,88,'BadiKetli'),(258,150,'Pahadi Chai',100.00,100.00,NULL,NULL,88,'Regular'),(259,150,'Pahadi Chai',100.00,100.00,NULL,NULL,88,'Full'),(260,150,'Pahadi Chai',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(261,150,'Pahadi Chai',100.00,100.00,NULL,NULL,88,'BadiKetli'),(262,160,'Rose Cardamom',100.00,100.00,NULL,NULL,88,'Regular'),(263,160,'Rose Cardamom',100.00,100.00,NULL,NULL,88,'Full'),(264,160,'Rose Cardamom',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(265,160,'Rose Cardamom',100.00,100.00,NULL,NULL,88,'BadiKetli'),(266,170,'Green Tea',100.00,100.00,NULL,NULL,88,'Regular'),(267,170,'Green Tea',100.00,100.00,NULL,NULL,88,'Full'),(268,170,'Green Tea',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(269,170,'Green Tea',100.00,100.00,NULL,NULL,88,'BadiKetli'),(270,180,'English Breakfast',100.00,100.00,NULL,NULL,88,'Regular'),(271,180,'English Breakfast',100.00,100.00,NULL,NULL,88,'Full'),(272,180,'English Breakfast',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(273,180,'English Breakfast',100.00,100.00,NULL,NULL,88,'BadiKetli'),(274,190,'Earl Grey',100.00,100.00,NULL,NULL,88,'Regular'),(275,190,'Earl Grey',100.00,100.00,NULL,NULL,88,'Full'),(276,190,'Earl Grey',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(277,190,'Earl Grey',100.00,100.00,NULL,NULL,88,'BadiKetli'),(278,200,'Orange Pekoe',100.00,100.00,NULL,NULL,88,'Regular'),(279,200,'Orange Pekoe',100.00,100.00,NULL,NULL,88,'Full'),(280,200,'Orange Pekoe',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(281,200,'Orange Pekoe',100.00,100.00,NULL,NULL,88,'BadiKetli'),(282,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,88,'Regular'),(283,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,88,'Full'),(284,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(285,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,88,'BadiKetli'),(286,220,'Lemon Grass',100.00,100.00,NULL,NULL,88,'Regular'),(287,220,'Lemon Grass',100.00,100.00,NULL,NULL,88,'Full'),(288,220,'Lemon Grass',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(289,220,'Lemon Grass',100.00,100.00,NULL,NULL,88,'BadiKetli'),(290,240,'Jasmine Tea',100.00,100.00,NULL,NULL,88,'Regular'),(291,240,'Jasmine Tea',100.00,100.00,NULL,NULL,88,'Full'),(292,240,'Jasmine Tea',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(293,240,'Jasmine Tea',100.00,100.00,NULL,NULL,88,'BadiKetli'),(294,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,88,'Regular'),(295,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,88,'Full'),(296,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(297,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,88,'BadiKetli'),(298,260,'LoPCHU',100.00,100.00,NULL,NULL,88,'Regular'),(299,260,'LoPCHU',100.00,100.00,NULL,NULL,88,'Full'),(300,260,'LoPCHU',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(301,260,'LoPCHU',100.00,100.00,NULL,NULL,88,'BadiKetli'),(302,270,'Hot Chocolate',100.00,100.00,NULL,NULL,88,'Regular'),(303,270,'Hot Chocolate',100.00,100.00,NULL,NULL,88,'Full'),(304,270,'Hot Chocolate',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(305,270,'Hot Chocolate',100.00,100.00,NULL,NULL,88,'BadiKetli'),(306,271,'Desi Filter Kaafi',100.00,100.00,NULL,NULL,88,'Regular'),(307,272,'Drinking Chocolate',100.00,100.00,NULL,NULL,88,'Regular'),(308,272,'Drinking Chocolate',100.00,100.00,NULL,NULL,88,'Full'),(309,280,'Hot Coffee',100.00,100.00,NULL,NULL,88,'Regular'),(310,280,'Hot Coffee',100.00,100.00,NULL,NULL,88,'Full'),(311,280,'Hot Coffee',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(312,280,'Hot Coffee',100.00,100.00,NULL,NULL,88,'BadiKetli'),(313,290,'Bournvita',100.00,100.00,NULL,NULL,88,'Regular'),(314,290,'Bournvita',100.00,100.00,NULL,NULL,88,'Full'),(315,290,'Bournvita',100.00,100.00,NULL,NULL,88,'ChotiKetli'),(316,290,'Bournvita',100.00,100.00,NULL,NULL,88,'BadiKetli'),(317,40,'Thandi Chai',100.00,100.00,NULL,NULL,88,'None'),(318,300,'Lemon Iced Tea',100.00,100.00,NULL,NULL,88,'Regular'),(319,300,'Lemon Iced Tea',100.00,100.00,NULL,NULL,88,'Full'),(320,310,'Peach Iced Tea',100.00,100.00,NULL,NULL,88,'Regular'),(321,310,'Peach Iced Tea',100.00,100.00,NULL,NULL,88,'Full'),(322,320,'Passion Fruit Iced Tea',100.00,100.00,NULL,NULL,88,'Regular'),(323,320,'Passion Fruit Iced Tea',100.00,100.00,NULL,NULL,88,'Full'),(324,330,'Strawberry Iced Tea',100.00,100.00,NULL,NULL,88,'Regular'),(325,330,'Strawberry Iced Tea',100.00,100.00,NULL,NULL,88,'Full'),(326,340,'Kiwi Iced Tea',100.00,100.00,NULL,NULL,88,'Regular'),(327,340,'Kiwi Iced Tea',100.00,100.00,NULL,NULL,88,'Full'),(328,350,'Red Berry Iced Tea',100.00,100.00,NULL,NULL,88,'Regular'),(329,350,'Red Berry Iced Tea',100.00,100.00,NULL,NULL,88,'Full'),(330,360,'Kiwi Shake',100.00,100.00,NULL,NULL,88,'None'),(331,370,'Strawberry Shake',100.00,100.00,NULL,NULL,88,'None'),(332,380,'Chikoo Shake',100.00,100.00,NULL,NULL,88,'None'),(333,390,'Banana Shake',100.00,100.00,NULL,NULL,88,'None'),(334,400,'Mango Shake',100.00,100.00,NULL,NULL,88,'None'),(335,410,'Black Grape Shake',100.00,100.00,NULL,NULL,88,'None'),(336,420,'Chocolate Shake',100.00,100.00,NULL,NULL,88,'Regular'),(337,420,'Chocolate Shake',100.00,100.00,NULL,NULL,88,'Full'),(338,430,'Rooh Afza',100.00,100.00,NULL,NULL,88,'Regular'),(339,430,'Rooh Afza',100.00,100.00,NULL,NULL,88,'Full'),(340,440,'Thandai',100.00,100.00,NULL,NULL,88,'Regular'),(341,440,'Thandai',100.00,100.00,NULL,NULL,88,'Full'),(342,450,'Mint Lemonade',100.00,100.00,NULL,NULL,88,'Regular'),(343,450,'Mint Lemonade',100.00,100.00,NULL,NULL,88,'Full'),(344,460,'Modinagar Shikanji',100.00,100.00,NULL,NULL,88,'Regular'),(345,460,'Modinagar Shikanji',100.00,100.00,NULL,NULL,88,'Full'),(346,470,'Fresh Lime',100.00,100.00,NULL,NULL,88,'Regular'),(347,470,'Fresh Lime',100.00,100.00,NULL,NULL,88,'Full'),(348,480,'Cold Coffee',100.00,100.00,NULL,NULL,88,'Regular'),(349,480,'Cold Coffee',100.00,100.00,NULL,NULL,88,'Full'),(350,490,'Home Made Chaach',100.00,100.00,NULL,NULL,88,'Regular'),(351,490,'Home Made Chaach',100.00,100.00,NULL,NULL,88,'Full'),(352,500,'Sicilian Chicken',100.00,100.00,NULL,NULL,88,'None'),(353,510,'Green Chicken',100.00,100.00,NULL,NULL,88,'None'),(354,520,'Pepper Chicken',100.00,100.00,NULL,NULL,88,'None'),(355,530,'Napoli',100.00,100.00,NULL,NULL,88,'None'),(356,540,'Spinach Corn Cheese',100.00,100.00,NULL,NULL,88,'None'),(357,550,'Balsamico',100.00,100.00,NULL,NULL,88,'None'),(358,560,'Chocolate Bun',100.00,100.00,NULL,NULL,88,'None'),(359,570,'Butter-Chicken Wrap',100.00,100.00,NULL,NULL,88,'None'),(360,580,'Mutton Lazeez Wrap',100.00,100.00,NULL,NULL,88,'None'),(361,590,'Kadhai Paneer Wrap',100.00,100.00,NULL,NULL,88,'None'),(362,600,'Chatpata Kebab Wrap',100.00,100.00,NULL,NULL,88,'None'),(363,610,'2 Minutes Sandwich',100.00,100.00,NULL,NULL,88,'None'),(364,620,'Homestyle Aloo',100.00,100.00,NULL,NULL,88,'None'),(365,630,'Bombay Special',100.00,100.00,NULL,NULL,88,'None'),(366,640,'Keema Pav',100.00,100.00,NULL,NULL,88,'None'),(367,650,'Egg Bun',100.00,100.00,NULL,NULL,88,'None'),(368,651,'Paneer Sambossa',100.00,100.00,NULL,NULL,88,'Single'),(369,652,'Chicken Sambossa',100.00,100.00,NULL,NULL,88,'Single'),(370,660,'Poha',100.00,100.00,NULL,NULL,88,'None'),(371,670,'Vada Pav',100.00,100.00,NULL,NULL,88,'None'),(372,680,'Bun Bhujia',100.00,100.00,NULL,NULL,88,'None'),(373,681,'Bun Maska - Achari',100.00,100.00,NULL,NULL,88,'None'),(374,682,'Bun Maska - Honey Lemon',100.00,100.00,NULL,NULL,88,'None'),(375,683,'Bun Maska - Sundried Tomatoes & Olives',100.00,100.00,NULL,NULL,88,'None'),(376,684,'Bun Maska - Mint Jalapeno',100.00,100.00,NULL,NULL,88,'None'),(377,690,'Bun Maska',100.00,100.00,NULL,NULL,88,'None'),(378,865,'Gujiya',100.00,100.00,NULL,NULL,88,'Double'),(379,691,'Chai Patti',100.00,100.00,NULL,NULL,88,'None'),(380,692,'Diwali Gift Box',100.00,100.00,NULL,NULL,88,'None'),(381,700,'Desi Chai Pack',100.00,100.00,NULL,NULL,88,'None'),(382,710,'Tulsi Adrak Chai Pack',100.00,100.00,NULL,NULL,88,'None'),(383,720,'Green Tea Pack',100.00,100.00,NULL,NULL,88,'None'),(384,730,'Chai Masala Pack',100.00,100.00,NULL,NULL,88,'None'),(385,840,'Badi Ketli',100.00,100.00,NULL,NULL,88,'None'),(386,850,'Choti Ketli',100.00,100.00,NULL,NULL,88,'None'),(387,740,'Blueberry Cake',100.00,100.00,NULL,NULL,88,'None'),(388,750,'Banana Cake',100.00,100.00,NULL,NULL,88,'None'),(389,760,'Carrot Cake',100.00,100.00,NULL,NULL,88,'None'),(390,770,'Lemon Cake',100.00,100.00,NULL,NULL,88,'None'),(391,780,'Rusk',100.00,100.00,NULL,NULL,88,'None'),(392,790,'Jeera Cookies',100.00,100.00,NULL,NULL,88,'None'),(393,800,'Badam Pista Cookies',100.00,100.00,NULL,NULL,88,'None'),(394,810,'Oatmeal Cookies',100.00,100.00,NULL,NULL,88,'None'),(395,866,'Chocolate Cupcake',100.00,100.00,NULL,NULL,88,'None'),(396,867,'Strawberry Cupcake',100.00,100.00,NULL,NULL,88,'None'),(397,860,'Additional Honey',100.00,100.00,NULL,NULL,88,'None'),(398,861,'Additional Kulhad',100.00,100.00,NULL,NULL,88,'None'),(399,862,'Extra Coffee Powder',100.00,100.00,NULL,NULL,88,'None'),(400,863,'Extra Chocolate Syrup',100.00,100.00,NULL,NULL,88,'None'),(401,864,'Extra RoofAfza Syrup',100.00,100.00,NULL,NULL,88,'None'),(402,10,'Desi Chai',100.00,100.00,NULL,NULL,89,'Regular'),(403,10,'Desi Chai',100.00,100.00,NULL,NULL,89,'Full'),(404,10,'Desi Chai',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(405,10,'Desi Chai',100.00,100.00,NULL,NULL,89,'BadiKetli'),(406,11,'Full Doodh',100.00,100.00,NULL,NULL,89,'Regular'),(407,11,'Full Doodh',100.00,100.00,NULL,NULL,89,'Full'),(408,11,'Full Doodh',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(409,11,'Full Doodh',100.00,100.00,NULL,NULL,89,'BadiKetli'),(410,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,89,'Regular'),(411,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,89,'Full'),(412,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(413,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,89,'BadiKetli'),(414,20,'Sulemani Chai',100.00,100.00,NULL,NULL,89,'None'),(415,30,'Cutting Chai',100.00,100.00,NULL,NULL,89,'None'),(416,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,89,'Regular'),(417,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,89,'Full'),(418,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(419,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,89,'BadiKetli'),(420,60,'God\'s Chai',100.00,100.00,NULL,NULL,89,'Regular'),(421,60,'God\'s Chai',100.00,100.00,NULL,NULL,89,'Full'),(422,60,'God\'s Chai',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(423,60,'God\'s Chai',100.00,100.00,NULL,NULL,89,'BadiKetli'),(424,80,'Kulhad Chai',100.00,100.00,NULL,NULL,89,'None'),(425,80,'Kulhad Chai',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(426,80,'Kulhad Chai',100.00,100.00,NULL,NULL,89,'BadiKetli'),(427,90,'Cinnamon Green',100.00,100.00,NULL,NULL,89,'Regular'),(428,90,'Cinnamon Green',100.00,100.00,NULL,NULL,89,'Full'),(429,90,'Cinnamon Green',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(430,90,'Cinnamon Green',100.00,100.00,NULL,NULL,89,'BadiKetli'),(431,100,'Lemon Green',100.00,100.00,NULL,NULL,89,'Regular'),(432,100,'Lemon Green',100.00,100.00,NULL,NULL,89,'Full'),(433,100,'Lemon Green',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(434,100,'Lemon Green',100.00,100.00,NULL,NULL,89,'BadiKetli'),(435,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,89,'Regular'),(436,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,89,'Full'),(437,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(438,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,89,'BadiKetli'),(439,120,'Moroccan Mint',100.00,100.00,NULL,NULL,89,'Regular'),(440,120,'Moroccan Mint',100.00,100.00,NULL,NULL,89,'Full'),(441,120,'Moroccan Mint',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(442,120,'Moroccan Mint',100.00,100.00,NULL,NULL,89,'BadiKetli'),(443,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,89,'Regular'),(444,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,89,'Full'),(445,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(446,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,89,'BadiKetli'),(447,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,89,'Regular'),(448,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,89,'Full'),(449,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(450,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,89,'BadiKetli'),(451,150,'Pahadi Chai',100.00,100.00,NULL,NULL,89,'Regular'),(452,150,'Pahadi Chai',100.00,100.00,NULL,NULL,89,'Full'),(453,150,'Pahadi Chai',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(454,150,'Pahadi Chai',100.00,100.00,NULL,NULL,89,'BadiKetli'),(455,160,'Rose Cardamom',100.00,100.00,NULL,NULL,89,'Regular'),(456,160,'Rose Cardamom',100.00,100.00,NULL,NULL,89,'Full'),(457,160,'Rose Cardamom',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(458,160,'Rose Cardamom',100.00,100.00,NULL,NULL,89,'BadiKetli'),(459,170,'Green Tea',100.00,100.00,NULL,NULL,89,'Regular'),(460,170,'Green Tea',100.00,100.00,NULL,NULL,89,'Full'),(461,170,'Green Tea',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(462,170,'Green Tea',100.00,100.00,NULL,NULL,89,'BadiKetli'),(463,180,'English Breakfast',100.00,100.00,NULL,NULL,89,'Regular'),(464,180,'English Breakfast',100.00,100.00,NULL,NULL,89,'Full'),(465,180,'English Breakfast',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(466,180,'English Breakfast',100.00,100.00,NULL,NULL,89,'BadiKetli'),(467,190,'Earl Grey',100.00,100.00,NULL,NULL,89,'Regular'),(468,190,'Earl Grey',100.00,100.00,NULL,NULL,89,'Full'),(469,190,'Earl Grey',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(470,190,'Earl Grey',100.00,100.00,NULL,NULL,89,'BadiKetli'),(471,200,'Orange Pekoe',100.00,100.00,NULL,NULL,89,'Regular'),(472,200,'Orange Pekoe',100.00,100.00,NULL,NULL,89,'Full'),(473,200,'Orange Pekoe',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(474,200,'Orange Pekoe',100.00,100.00,NULL,NULL,89,'BadiKetli'),(475,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,89,'Regular'),(476,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,89,'Full'),(477,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(478,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,89,'BadiKetli'),(479,220,'Lemon Grass',100.00,100.00,NULL,NULL,89,'Regular'),(480,220,'Lemon Grass',100.00,100.00,NULL,NULL,89,'Full'),(481,220,'Lemon Grass',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(482,220,'Lemon Grass',100.00,100.00,NULL,NULL,89,'BadiKetli'),(483,240,'Jasmine Tea',100.00,100.00,NULL,NULL,89,'Regular'),(484,240,'Jasmine Tea',100.00,100.00,NULL,NULL,89,'Full'),(485,240,'Jasmine Tea',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(486,240,'Jasmine Tea',100.00,100.00,NULL,NULL,89,'BadiKetli'),(487,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,89,'Regular'),(488,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,89,'Full'),(489,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(490,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,89,'BadiKetli'),(491,260,'LoPCHU',100.00,100.00,NULL,NULL,89,'Regular'),(492,260,'LoPCHU',100.00,100.00,NULL,NULL,89,'Full'),(493,260,'LoPCHU',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(494,260,'LoPCHU',100.00,100.00,NULL,NULL,89,'BadiKetli'),(495,270,'Hot Chocolate',100.00,100.00,NULL,NULL,89,'Regular'),(496,270,'Hot Chocolate',100.00,100.00,NULL,NULL,89,'Full'),(497,270,'Hot Chocolate',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(498,270,'Hot Chocolate',100.00,100.00,NULL,NULL,89,'BadiKetli'),(499,271,'Desi Filter Kaafi',100.00,100.00,NULL,NULL,89,'Regular'),(500,272,'Drinking Chocolate',100.00,100.00,NULL,NULL,89,'Regular'),(501,272,'Drinking Chocolate',100.00,100.00,NULL,NULL,89,'Full'),(502,280,'Hot Coffee',100.00,100.00,NULL,NULL,89,'Regular'),(503,280,'Hot Coffee',100.00,100.00,NULL,NULL,89,'Full'),(504,280,'Hot Coffee',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(505,280,'Hot Coffee',100.00,100.00,NULL,NULL,89,'BadiKetli'),(506,290,'Bournvita',100.00,100.00,NULL,NULL,89,'Regular'),(507,290,'Bournvita',100.00,100.00,NULL,NULL,89,'Full'),(508,290,'Bournvita',100.00,100.00,NULL,NULL,89,'ChotiKetli'),(509,290,'Bournvita',100.00,100.00,NULL,NULL,89,'BadiKetli'),(510,40,'Thandi Chai',100.00,100.00,NULL,NULL,89,'None'),(511,300,'Lemon Iced Tea',100.00,100.00,NULL,NULL,89,'Regular'),(512,300,'Lemon Iced Tea',100.00,100.00,NULL,NULL,89,'Full'),(513,310,'Peach Iced Tea',100.00,100.00,NULL,NULL,89,'Regular'),(514,310,'Peach Iced Tea',100.00,100.00,NULL,NULL,89,'Full'),(515,320,'Passion Fruit Iced Tea',100.00,100.00,NULL,NULL,89,'Regular'),(516,320,'Passion Fruit Iced Tea',100.00,100.00,NULL,NULL,89,'Full'),(517,330,'Strawberry Iced Tea',100.00,100.00,NULL,NULL,89,'Regular'),(518,330,'Strawberry Iced Tea',100.00,100.00,NULL,NULL,89,'Full'),(519,340,'Kiwi Iced Tea',100.00,100.00,NULL,NULL,89,'Regular'),(520,340,'Kiwi Iced Tea',100.00,100.00,NULL,NULL,89,'Full'),(521,350,'Red Berry Iced Tea',100.00,100.00,NULL,NULL,89,'Regular'),(522,350,'Red Berry Iced Tea',100.00,100.00,NULL,NULL,89,'Full'),(523,360,'Kiwi Shake',100.00,100.00,NULL,NULL,89,'None'),(524,370,'Strawberry Shake',100.00,100.00,NULL,NULL,89,'None'),(525,380,'Chikoo Shake',100.00,100.00,NULL,NULL,89,'None'),(526,390,'Banana Shake',100.00,100.00,NULL,NULL,89,'None'),(527,400,'Mango Shake',100.00,100.00,NULL,NULL,89,'None'),(528,410,'Black Grape Shake',100.00,100.00,NULL,NULL,89,'None'),(529,420,'Chocolate Shake',100.00,100.00,NULL,NULL,89,'Regular'),(530,420,'Chocolate Shake',100.00,100.00,NULL,NULL,89,'Full'),(531,430,'Rooh Afza',100.00,100.00,NULL,NULL,89,'Regular'),(532,430,'Rooh Afza',100.00,100.00,NULL,NULL,89,'Full'),(533,440,'Thandai',100.00,100.00,NULL,NULL,89,'Regular'),(534,440,'Thandai',100.00,100.00,NULL,NULL,89,'Full'),(535,450,'Mint Lemonade',100.00,100.00,NULL,NULL,89,'Regular'),(536,450,'Mint Lemonade',100.00,100.00,NULL,NULL,89,'Full'),(537,460,'Modinagar Shikanji',100.00,100.00,NULL,NULL,89,'Regular'),(538,460,'Modinagar Shikanji',100.00,100.00,NULL,NULL,89,'Full'),(539,470,'Fresh Lime',100.00,100.00,NULL,NULL,89,'Regular'),(540,470,'Fresh Lime',100.00,100.00,NULL,NULL,89,'Full'),(541,480,'Cold Coffee',100.00,100.00,NULL,NULL,89,'Regular'),(542,480,'Cold Coffee',100.00,100.00,NULL,NULL,89,'Full'),(543,490,'Home Made Chaach',100.00,100.00,NULL,NULL,89,'Regular'),(544,490,'Home Made Chaach',100.00,100.00,NULL,NULL,89,'Full'),(545,500,'Sicilian Chicken',100.00,100.00,NULL,NULL,89,'None'),(546,510,'Green Chicken',100.00,100.00,NULL,NULL,89,'None'),(547,520,'Pepper Chicken',100.00,100.00,NULL,NULL,89,'None'),(548,530,'Napoli',100.00,100.00,NULL,NULL,89,'None'),(549,540,'Spinach Corn Cheese',100.00,100.00,NULL,NULL,89,'None'),(550,550,'Balsamico',100.00,100.00,NULL,NULL,89,'None'),(551,560,'Chocolate Bun',100.00,100.00,NULL,NULL,89,'None'),(552,570,'Butter-Chicken Wrap',100.00,100.00,NULL,NULL,89,'None'),(553,580,'Mutton Lazeez Wrap',100.00,100.00,NULL,NULL,89,'None'),(554,590,'Kadhai Paneer Wrap',100.00,100.00,NULL,NULL,89,'None'),(555,600,'Chatpata Kebab Wrap',100.00,100.00,NULL,NULL,89,'None'),(556,610,'2 Minutes Sandwich',100.00,100.00,NULL,NULL,89,'None'),(557,620,'Homestyle Aloo',100.00,100.00,NULL,NULL,89,'None'),(558,630,'Bombay Special',100.00,100.00,NULL,NULL,89,'None'),(559,640,'Keema Pav',100.00,100.00,NULL,NULL,89,'None'),(560,650,'Egg Bun',100.00,100.00,NULL,NULL,89,'None'),(561,651,'Paneer Sambossa',100.00,100.00,NULL,NULL,89,'Single'),(562,652,'Chicken Sambossa',100.00,100.00,NULL,NULL,89,'Single'),(563,660,'Poha',100.00,100.00,NULL,NULL,89,'None'),(564,670,'Vada Pav',100.00,100.00,NULL,NULL,89,'None'),(565,680,'Bun Bhujia',100.00,100.00,NULL,NULL,89,'None'),(566,681,'Bun Maska - Achari',100.00,100.00,NULL,NULL,89,'None'),(567,682,'Bun Maska - Honey Lemon',100.00,100.00,NULL,NULL,89,'None'),(568,683,'Bun Maska - Sundried Tomatoes & Olives',100.00,100.00,NULL,NULL,89,'None'),(569,684,'Bun Maska - Mint Jalapeno',100.00,100.00,NULL,NULL,89,'None'),(570,690,'Bun Maska',100.00,100.00,NULL,NULL,89,'None'),(571,865,'Gujiya',100.00,100.00,NULL,NULL,89,'Double'),(572,691,'Chai Patti',100.00,100.00,NULL,NULL,89,'None'),(573,692,'Diwali Gift Box',100.00,100.00,NULL,NULL,89,'None'),(574,700,'Desi Chai Pack',100.00,100.00,NULL,NULL,89,'None'),(575,710,'Tulsi Adrak Chai Pack',100.00,100.00,NULL,NULL,89,'None'),(576,720,'Green Tea Pack',100.00,100.00,NULL,NULL,89,'None'),(577,730,'Chai Masala Pack',100.00,100.00,NULL,NULL,89,'None'),(578,840,'Badi Ketli',100.00,100.00,NULL,NULL,89,'None'),(579,850,'Choti Ketli',100.00,100.00,NULL,NULL,89,'None'),(580,740,'Blueberry Cake',100.00,100.00,NULL,NULL,89,'None'),(581,750,'Banana Cake',100.00,100.00,NULL,NULL,89,'None'),(582,760,'Carrot Cake',100.00,100.00,NULL,NULL,89,'None'),(583,770,'Lemon Cake',100.00,100.00,NULL,NULL,89,'None'),(584,780,'Rusk',100.00,100.00,NULL,NULL,89,'None'),(585,790,'Jeera Cookies',100.00,100.00,NULL,NULL,89,'None'),(586,800,'Badam Pista Cookies',100.00,100.00,NULL,NULL,89,'None'),(587,810,'Oatmeal Cookies',100.00,100.00,NULL,NULL,89,'None'),(588,866,'Chocolate Cupcake',100.00,100.00,NULL,NULL,89,'None'),(589,867,'Strawberry Cupcake',100.00,100.00,NULL,NULL,89,'None'),(590,860,'Additional Honey',100.00,100.00,NULL,NULL,89,'None'),(591,861,'Additional Kulhad',100.00,100.00,NULL,NULL,89,'None'),(592,862,'Extra Coffee Powder',100.00,100.00,NULL,NULL,89,'None'),(593,863,'Extra Chocolate Syrup',100.00,100.00,NULL,NULL,89,'None'),(594,864,'Extra RoofAfza Syrup',100.00,100.00,NULL,NULL,89,'None'),(595,10,'Desi Chai',100.00,100.00,NULL,NULL,90,'Regular'),(596,10,'Desi Chai',100.00,100.00,NULL,NULL,90,'Full'),(597,10,'Desi Chai',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(598,10,'Desi Chai',100.00,100.00,NULL,NULL,90,'BadiKetli'),(599,11,'Full Doodh',100.00,100.00,NULL,NULL,90,'Regular'),(600,11,'Full Doodh',100.00,100.00,NULL,NULL,90,'Full'),(601,11,'Full Doodh',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(602,11,'Full Doodh',100.00,100.00,NULL,NULL,90,'BadiKetli'),(603,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,90,'Regular'),(604,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,90,'Full'),(605,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(606,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,90,'BadiKetli'),(607,20,'Sulemani Chai',100.00,100.00,NULL,NULL,90,'None'),(608,30,'Cutting Chai',100.00,100.00,NULL,NULL,90,'None'),(609,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,90,'Regular'),(610,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,90,'Full'),(611,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(612,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,90,'BadiKetli'),(613,60,'God\'s Chai',100.00,100.00,NULL,NULL,90,'Regular'),(614,60,'God\'s Chai',100.00,100.00,NULL,NULL,90,'Full'),(615,60,'God\'s Chai',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(616,60,'God\'s Chai',100.00,100.00,NULL,NULL,90,'BadiKetli'),(617,80,'Kulhad Chai',100.00,100.00,NULL,NULL,90,'None'),(618,80,'Kulhad Chai',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(619,80,'Kulhad Chai',100.00,100.00,NULL,NULL,90,'BadiKetli'),(620,90,'Cinnamon Green',100.00,100.00,NULL,NULL,90,'Regular'),(621,90,'Cinnamon Green',100.00,100.00,NULL,NULL,90,'Full'),(622,90,'Cinnamon Green',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(623,90,'Cinnamon Green',100.00,100.00,NULL,NULL,90,'BadiKetli'),(624,100,'Lemon Green',100.00,100.00,NULL,NULL,90,'Regular'),(625,100,'Lemon Green',100.00,100.00,NULL,NULL,90,'Full'),(626,100,'Lemon Green',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(627,100,'Lemon Green',100.00,100.00,NULL,NULL,90,'BadiKetli'),(628,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,90,'Regular'),(629,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,90,'Full'),(630,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(631,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,90,'BadiKetli'),(632,120,'Moroccan Mint',100.00,100.00,NULL,NULL,90,'Regular'),(633,120,'Moroccan Mint',100.00,100.00,NULL,NULL,90,'Full'),(634,120,'Moroccan Mint',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(635,120,'Moroccan Mint',100.00,100.00,NULL,NULL,90,'BadiKetli'),(636,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,90,'Regular'),(637,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,90,'Full'),(638,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(639,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,90,'BadiKetli'),(640,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,90,'Regular'),(641,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,90,'Full'),(642,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(643,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,90,'BadiKetli'),(644,150,'Pahadi Chai',100.00,100.00,NULL,NULL,90,'Regular'),(645,150,'Pahadi Chai',100.00,100.00,NULL,NULL,90,'Full'),(646,150,'Pahadi Chai',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(647,150,'Pahadi Chai',100.00,100.00,NULL,NULL,90,'BadiKetli'),(648,160,'Rose Cardamom',100.00,100.00,NULL,NULL,90,'Regular'),(649,160,'Rose Cardamom',100.00,100.00,NULL,NULL,90,'Full'),(650,160,'Rose Cardamom',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(651,160,'Rose Cardamom',100.00,100.00,NULL,NULL,90,'BadiKetli'),(652,170,'Green Tea',100.00,100.00,NULL,NULL,90,'Regular'),(653,170,'Green Tea',100.00,100.00,NULL,NULL,90,'Full'),(654,170,'Green Tea',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(655,170,'Green Tea',100.00,100.00,NULL,NULL,90,'BadiKetli'),(656,180,'English Breakfast',100.00,100.00,NULL,NULL,90,'Regular'),(657,180,'English Breakfast',100.00,100.00,NULL,NULL,90,'Full'),(658,180,'English Breakfast',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(659,180,'English Breakfast',100.00,100.00,NULL,NULL,90,'BadiKetli'),(660,190,'Earl Grey',100.00,100.00,NULL,NULL,90,'Regular'),(661,190,'Earl Grey',100.00,100.00,NULL,NULL,90,'Full'),(662,190,'Earl Grey',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(663,190,'Earl Grey',100.00,100.00,NULL,NULL,90,'BadiKetli'),(664,200,'Orange Pekoe',100.00,100.00,NULL,NULL,90,'Regular'),(665,200,'Orange Pekoe',100.00,100.00,NULL,NULL,90,'Full'),(666,200,'Orange Pekoe',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(667,200,'Orange Pekoe',100.00,100.00,NULL,NULL,90,'BadiKetli'),(668,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,90,'Regular'),(669,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,90,'Full'),(670,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(671,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,90,'BadiKetli'),(672,220,'Lemon Grass',100.00,100.00,NULL,NULL,90,'Regular'),(673,220,'Lemon Grass',100.00,100.00,NULL,NULL,90,'Full'),(674,220,'Lemon Grass',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(675,220,'Lemon Grass',100.00,100.00,NULL,NULL,90,'BadiKetli'),(676,240,'Jasmine Tea',100.00,100.00,NULL,NULL,90,'Regular'),(677,240,'Jasmine Tea',100.00,100.00,NULL,NULL,90,'Full'),(678,240,'Jasmine Tea',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(679,240,'Jasmine Tea',100.00,100.00,NULL,NULL,90,'BadiKetli'),(680,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,90,'Regular'),(681,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,90,'Full'),(682,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(683,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,90,'BadiKetli'),(684,260,'LoPCHU',100.00,100.00,NULL,NULL,90,'Regular'),(685,260,'LoPCHU',100.00,100.00,NULL,NULL,90,'Full'),(686,260,'LoPCHU',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(687,260,'LoPCHU',100.00,100.00,NULL,NULL,90,'BadiKetli'),(688,270,'Hot Chocolate',100.00,100.00,NULL,NULL,90,'Regular'),(689,270,'Hot Chocolate',100.00,100.00,NULL,NULL,90,'Full'),(690,270,'Hot Chocolate',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(691,270,'Hot Chocolate',100.00,100.00,NULL,NULL,90,'BadiKetli'),(692,271,'Desi Filter Kaafi',100.00,100.00,NULL,NULL,90,'Regular'),(693,272,'Drinking Chocolate',100.00,100.00,NULL,NULL,90,'Regular'),(694,272,'Drinking Chocolate',100.00,100.00,NULL,NULL,90,'Full'),(695,280,'Hot Coffee',100.00,100.00,NULL,NULL,90,'Regular'),(696,280,'Hot Coffee',100.00,100.00,NULL,NULL,90,'Full'),(697,280,'Hot Coffee',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(698,280,'Hot Coffee',100.00,100.00,NULL,NULL,90,'BadiKetli'),(699,290,'Bournvita',100.00,100.00,NULL,NULL,90,'Regular'),(700,290,'Bournvita',100.00,100.00,NULL,NULL,90,'Full'),(701,290,'Bournvita',100.00,100.00,NULL,NULL,90,'ChotiKetli'),(702,290,'Bournvita',100.00,100.00,NULL,NULL,90,'BadiKetli'),(703,40,'Thandi Chai',100.00,100.00,NULL,NULL,90,'None'),(704,300,'Lemon Iced Tea',100.00,100.00,NULL,NULL,90,'Regular'),(705,300,'Lemon Iced Tea',100.00,100.00,NULL,NULL,90,'Full'),(706,310,'Peach Iced Tea',100.00,100.00,NULL,NULL,90,'Regular'),(707,310,'Peach Iced Tea',100.00,100.00,NULL,NULL,90,'Full'),(708,320,'Passion Fruit Iced Tea',100.00,100.00,NULL,NULL,90,'Regular'),(709,320,'Passion Fruit Iced Tea',100.00,100.00,NULL,NULL,90,'Full'),(710,330,'Strawberry Iced Tea',100.00,100.00,NULL,NULL,90,'Regular'),(711,330,'Strawberry Iced Tea',100.00,100.00,NULL,NULL,90,'Full'),(712,340,'Kiwi Iced Tea',100.00,100.00,NULL,NULL,90,'Regular'),(713,340,'Kiwi Iced Tea',100.00,100.00,NULL,NULL,90,'Full'),(714,350,'Red Berry Iced Tea',100.00,100.00,NULL,NULL,90,'Regular'),(715,350,'Red Berry Iced Tea',100.00,100.00,NULL,NULL,90,'Full'),(716,360,'Kiwi Shake',100.00,100.00,NULL,NULL,90,'None'),(717,370,'Strawberry Shake',100.00,100.00,NULL,NULL,90,'None'),(718,380,'Chikoo Shake',100.00,100.00,NULL,NULL,90,'None'),(719,390,'Banana Shake',100.00,100.00,NULL,NULL,90,'None'),(720,400,'Mango Shake',100.00,100.00,NULL,NULL,90,'None'),(721,410,'Black Grape Shake',100.00,100.00,NULL,NULL,90,'None'),(722,420,'Chocolate Shake',100.00,100.00,NULL,NULL,90,'Regular'),(723,420,'Chocolate Shake',100.00,100.00,NULL,NULL,90,'Full'),(724,430,'Rooh Afza',100.00,100.00,NULL,NULL,90,'Regular'),(725,430,'Rooh Afza',100.00,100.00,NULL,NULL,90,'Full'),(726,440,'Thandai',100.00,100.00,NULL,NULL,90,'Regular'),(727,440,'Thandai',100.00,100.00,NULL,NULL,90,'Full'),(728,450,'Mint Lemonade',100.00,100.00,NULL,NULL,90,'Regular'),(729,450,'Mint Lemonade',100.00,100.00,NULL,NULL,90,'Full'),(730,460,'Modinagar Shikanji',100.00,100.00,NULL,NULL,90,'Regular'),(731,460,'Modinagar Shikanji',100.00,100.00,NULL,NULL,90,'Full'),(732,470,'Fresh Lime',100.00,100.00,NULL,NULL,90,'Regular'),(733,470,'Fresh Lime',100.00,100.00,NULL,NULL,90,'Full'),(734,480,'Cold Coffee',100.00,100.00,NULL,NULL,90,'Regular'),(735,480,'Cold Coffee',100.00,100.00,NULL,NULL,90,'Full'),(736,490,'Home Made Chaach',100.00,100.00,NULL,NULL,90,'Regular'),(737,490,'Home Made Chaach',100.00,100.00,NULL,NULL,90,'Full'),(738,500,'Sicilian Chicken',100.00,100.00,NULL,NULL,90,'None'),(739,510,'Green Chicken',100.00,100.00,NULL,NULL,90,'None'),(740,520,'Pepper Chicken',100.00,100.00,NULL,NULL,90,'None'),(741,530,'Napoli',100.00,100.00,NULL,NULL,90,'None'),(742,540,'Spinach Corn Cheese',100.00,100.00,NULL,NULL,90,'None'),(743,550,'Balsamico',100.00,100.00,NULL,NULL,90,'None'),(744,560,'Chocolate Bun',100.00,100.00,NULL,NULL,90,'None'),(745,570,'Butter-Chicken Wrap',100.00,100.00,NULL,NULL,90,'None'),(746,580,'Mutton Lazeez Wrap',100.00,100.00,NULL,NULL,90,'None'),(747,590,'Kadhai Paneer Wrap',100.00,100.00,NULL,NULL,90,'None'),(748,600,'Chatpata Kebab Wrap',100.00,100.00,NULL,NULL,90,'None'),(749,610,'2 Minutes Sandwich',100.00,100.00,NULL,NULL,90,'None'),(750,620,'Homestyle Aloo',100.00,100.00,NULL,NULL,90,'None'),(751,630,'Bombay Special',100.00,100.00,NULL,NULL,90,'None'),(752,640,'Keema Pav',100.00,100.00,NULL,NULL,90,'None'),(753,650,'Egg Bun',100.00,100.00,NULL,NULL,90,'None'),(754,651,'Paneer Sambossa',100.00,100.00,NULL,NULL,90,'Single'),(755,652,'Chicken Sambossa',100.00,100.00,NULL,NULL,90,'Single'),(756,660,'Poha',100.00,100.00,NULL,NULL,90,'None'),(757,670,'Vada Pav',100.00,100.00,NULL,NULL,90,'None'),(758,680,'Bun Bhujia',100.00,100.00,NULL,NULL,90,'None'),(759,681,'Bun Maska - Achari',100.00,100.00,NULL,NULL,90,'None'),(760,682,'Bun Maska - Honey Lemon',100.00,100.00,NULL,NULL,90,'None'),(761,683,'Bun Maska - Sundried Tomatoes & Olives',100.00,100.00,NULL,NULL,90,'None'),(762,684,'Bun Maska - Mint Jalapeno',100.00,100.00,NULL,NULL,90,'None'),(763,690,'Bun Maska',100.00,100.00,NULL,NULL,90,'None'),(764,865,'Gujiya',100.00,100.00,NULL,NULL,90,'Double'),(765,691,'Chai Patti',100.00,100.00,NULL,NULL,90,'None'),(766,692,'Diwali Gift Box',100.00,100.00,NULL,NULL,90,'None'),(767,700,'Desi Chai Pack',100.00,100.00,NULL,NULL,90,'None'),(768,710,'Tulsi Adrak Chai Pack',100.00,100.00,NULL,NULL,90,'None'),(769,720,'Green Tea Pack',100.00,100.00,NULL,NULL,90,'None'),(770,730,'Chai Masala Pack',100.00,100.00,NULL,NULL,90,'None'),(771,840,'Badi Ketli',100.00,100.00,NULL,NULL,90,'None'),(772,850,'Choti Ketli',100.00,100.00,NULL,NULL,90,'None'),(773,740,'Blueberry Cake',100.00,100.00,NULL,NULL,90,'None'),(774,750,'Banana Cake',100.00,100.00,NULL,NULL,90,'None'),(775,760,'Carrot Cake',100.00,100.00,NULL,NULL,90,'None'),(776,770,'Lemon Cake',100.00,100.00,NULL,NULL,90,'None'),(777,780,'Rusk',100.00,100.00,NULL,NULL,90,'None'),(778,790,'Jeera Cookies',100.00,100.00,NULL,NULL,90,'None'),(779,800,'Badam Pista Cookies',100.00,100.00,NULL,NULL,90,'None'),(780,810,'Oatmeal Cookies',100.00,100.00,NULL,NULL,90,'None'),(781,866,'Chocolate Cupcake',100.00,100.00,NULL,NULL,90,'None'),(782,867,'Strawberry Cupcake',100.00,100.00,NULL,NULL,90,'None'),(783,860,'Additional Honey',100.00,100.00,NULL,NULL,90,'None'),(784,861,'Additional Kulhad',100.00,100.00,NULL,NULL,90,'None'),(785,862,'Extra Coffee Powder',100.00,100.00,NULL,NULL,90,'None'),(786,863,'Extra Chocolate Syrup',100.00,100.00,NULL,NULL,90,'None'),(787,864,'Extra RoofAfza Syrup',100.00,100.00,NULL,NULL,90,'None'),(788,10,'Desi Chai',100.00,100.00,NULL,NULL,91,'Regular'),(789,10,'Desi Chai',100.00,100.00,NULL,NULL,91,'Full'),(790,10,'Desi Chai',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(791,10,'Desi Chai',100.00,100.00,NULL,NULL,91,'BadiKetli'),(792,11,'Full Doodh',100.00,100.00,NULL,NULL,91,'Regular'),(793,11,'Full Doodh',100.00,100.00,NULL,NULL,91,'Full'),(794,11,'Full Doodh',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(795,11,'Full Doodh',100.00,100.00,NULL,NULL,91,'BadiKetli'),(796,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,91,'Regular'),(797,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,91,'Full'),(798,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(799,12,'Desi Doodh Kum',100.00,100.00,NULL,NULL,91,'BadiKetli'),(800,20,'Sulemani Chai',100.00,100.00,NULL,NULL,91,'None'),(801,30,'Cutting Chai',100.00,100.00,NULL,NULL,91,'None'),(802,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,91,'Regular'),(803,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,91,'Full'),(804,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(805,50,'Desi Paani Kum',100.00,100.00,NULL,NULL,91,'BadiKetli'),(806,60,'God\'s Chai',100.00,100.00,NULL,NULL,91,'Regular'),(807,60,'God\'s Chai',100.00,100.00,NULL,NULL,91,'Full'),(808,60,'God\'s Chai',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(809,60,'God\'s Chai',100.00,100.00,NULL,NULL,91,'BadiKetli'),(810,80,'Kulhad Chai',100.00,100.00,NULL,NULL,91,'None'),(811,80,'Kulhad Chai',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(812,80,'Kulhad Chai',100.00,100.00,NULL,NULL,91,'BadiKetli'),(813,90,'Cinnamon Green',100.00,100.00,NULL,NULL,91,'Regular'),(814,90,'Cinnamon Green',100.00,100.00,NULL,NULL,91,'Full'),(815,90,'Cinnamon Green',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(816,90,'Cinnamon Green',100.00,100.00,NULL,NULL,91,'BadiKetli'),(817,100,'Lemon Green',100.00,100.00,NULL,NULL,91,'Regular'),(818,100,'Lemon Green',100.00,100.00,NULL,NULL,91,'Full'),(819,100,'Lemon Green',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(820,100,'Lemon Green',100.00,100.00,NULL,NULL,91,'BadiKetli'),(821,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,91,'Regular'),(822,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,91,'Full'),(823,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(824,110,'Mint N Lemon Green',100.00,100.00,NULL,NULL,91,'BadiKetli'),(825,120,'Moroccan Mint',100.00,100.00,NULL,NULL,91,'Regular'),(826,120,'Moroccan Mint',100.00,100.00,NULL,NULL,91,'Full'),(827,120,'Moroccan Mint',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(828,120,'Moroccan Mint',100.00,100.00,NULL,NULL,91,'BadiKetli'),(829,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,91,'Regular'),(830,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,91,'Full'),(831,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(832,130,'Honey Ginger Lemon',100.00,100.00,NULL,NULL,91,'BadiKetli'),(833,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,91,'Regular'),(834,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,91,'Full'),(835,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(836,140,'Aam Papad Chai',100.00,100.00,NULL,NULL,91,'BadiKetli'),(837,150,'Pahadi Chai',100.00,100.00,NULL,NULL,91,'Regular'),(838,150,'Pahadi Chai',100.00,100.00,NULL,NULL,91,'Full'),(839,150,'Pahadi Chai',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(840,150,'Pahadi Chai',100.00,100.00,NULL,NULL,91,'BadiKetli'),(841,160,'Rose Cardamom',100.00,100.00,NULL,NULL,91,'Regular'),(842,160,'Rose Cardamom',100.00,100.00,NULL,NULL,91,'Full'),(843,160,'Rose Cardamom',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(844,160,'Rose Cardamom',100.00,100.00,NULL,NULL,91,'BadiKetli'),(845,170,'Green Tea',100.00,100.00,NULL,NULL,91,'Regular'),(846,170,'Green Tea',100.00,100.00,NULL,NULL,91,'Full'),(847,170,'Green Tea',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(848,170,'Green Tea',100.00,100.00,NULL,NULL,91,'BadiKetli'),(849,180,'English Breakfast',100.00,100.00,NULL,NULL,91,'Regular'),(850,180,'English Breakfast',100.00,100.00,NULL,NULL,91,'Full'),(851,180,'English Breakfast',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(852,180,'English Breakfast',100.00,100.00,NULL,NULL,91,'BadiKetli'),(853,190,'Earl Grey',100.00,100.00,NULL,NULL,91,'Regular'),(854,190,'Earl Grey',100.00,100.00,NULL,NULL,91,'Full'),(855,190,'Earl Grey',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(856,190,'Earl Grey',100.00,100.00,NULL,NULL,91,'BadiKetli'),(857,200,'Orange Pekoe',100.00,100.00,NULL,NULL,91,'Regular'),(858,200,'Orange Pekoe',100.00,100.00,NULL,NULL,91,'Full'),(859,200,'Orange Pekoe',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(860,200,'Orange Pekoe',100.00,100.00,NULL,NULL,91,'BadiKetli'),(861,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,91,'Regular'),(862,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,91,'Full'),(863,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(864,210,'Darjeeling First Flush',100.00,100.00,NULL,NULL,91,'BadiKetli'),(865,220,'Lemon Grass',100.00,100.00,NULL,NULL,91,'Regular'),(866,220,'Lemon Grass',100.00,100.00,NULL,NULL,91,'Full'),(867,220,'Lemon Grass',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(868,220,'Lemon Grass',100.00,100.00,NULL,NULL,91,'BadiKetli'),(869,240,'Jasmine Tea',100.00,100.00,NULL,NULL,91,'Regular'),(870,240,'Jasmine Tea',100.00,100.00,NULL,NULL,91,'Full'),(871,240,'Jasmine Tea',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(872,240,'Jasmine Tea',100.00,100.00,NULL,NULL,91,'BadiKetli'),(873,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,91,'Regular'),(874,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,91,'Full'),(875,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(876,250,'Darjeeling Muscatel',100.00,100.00,NULL,NULL,91,'BadiKetli'),(877,260,'LoPCHU',100.00,100.00,NULL,NULL,91,'Regular'),(878,260,'LoPCHU',100.00,100.00,NULL,NULL,91,'Full'),(879,260,'LoPCHU',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(880,260,'LoPCHU',100.00,100.00,NULL,NULL,91,'BadiKetli'),(881,270,'Hot Chocolate',100.00,100.00,NULL,NULL,91,'Regular'),(882,270,'Hot Chocolate',100.00,100.00,NULL,NULL,91,'Full'),(883,270,'Hot Chocolate',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(884,270,'Hot Chocolate',100.00,100.00,NULL,NULL,91,'BadiKetli'),(885,271,'Desi Filter Kaafi',100.00,100.00,NULL,NULL,91,'Regular'),(886,272,'Drinking Chocolate',100.00,100.00,NULL,NULL,91,'Regular'),(887,272,'Drinking Chocolate',100.00,100.00,NULL,NULL,91,'Full'),(888,280,'Hot Coffee',100.00,100.00,NULL,NULL,91,'Regular'),(889,280,'Hot Coffee',100.00,100.00,NULL,NULL,91,'Full'),(890,280,'Hot Coffee',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(891,280,'Hot Coffee',100.00,100.00,NULL,NULL,91,'BadiKetli'),(892,290,'Bournvita',100.00,100.00,NULL,NULL,91,'Regular'),(893,290,'Bournvita',100.00,100.00,NULL,NULL,91,'Full'),(894,290,'Bournvita',100.00,100.00,NULL,NULL,91,'ChotiKetli'),(895,290,'Bournvita',100.00,100.00,NULL,NULL,91,'BadiKetli'),(896,40,'Thandi Chai',100.00,100.00,NULL,NULL,91,'None'),(897,300,'Lemon Iced Tea',100.00,100.00,NULL,NULL,91,'Regular'),(898,300,'Lemon Iced Tea',100.00,100.00,NULL,NULL,91,'Full'),(899,310,'Peach Iced Tea',100.00,100.00,NULL,NULL,91,'Regular'),(900,310,'Peach Iced Tea',100.00,100.00,NULL,NULL,91,'Full'),(901,320,'Passion Fruit Iced Tea',100.00,100.00,NULL,NULL,91,'Regular'),(902,320,'Passion Fruit Iced Tea',100.00,100.00,NULL,NULL,91,'Full'),(903,330,'Strawberry Iced Tea',100.00,100.00,NULL,NULL,91,'Regular'),(904,330,'Strawberry Iced Tea',100.00,100.00,NULL,NULL,91,'Full'),(905,340,'Kiwi Iced Tea',100.00,100.00,NULL,NULL,91,'Regular'),(906,340,'Kiwi Iced Tea',100.00,100.00,NULL,NULL,91,'Full'),(907,350,'Red Berry Iced Tea',100.00,100.00,NULL,NULL,91,'Regular'),(908,350,'Red Berry Iced Tea',100.00,100.00,NULL,NULL,91,'Full'),(909,360,'Kiwi Shake',100.00,100.00,NULL,NULL,91,'None'),(910,370,'Strawberry Shake',100.00,100.00,NULL,NULL,91,'None'),(911,380,'Chikoo Shake',100.00,100.00,NULL,NULL,91,'None'),(912,390,'Banana Shake',100.00,100.00,NULL,NULL,91,'None'),(913,400,'Mango Shake',100.00,100.00,NULL,NULL,91,'None'),(914,410,'Black Grape Shake',100.00,100.00,NULL,NULL,91,'None'),(915,420,'Chocolate Shake',100.00,100.00,NULL,NULL,91,'Regular'),(916,420,'Chocolate Shake',100.00,100.00,NULL,NULL,91,'Full'),(917,430,'Rooh Afza',100.00,100.00,NULL,NULL,91,'Regular'),(918,430,'Rooh Afza',100.00,100.00,NULL,NULL,91,'Full'),(919,440,'Thandai',100.00,100.00,NULL,NULL,91,'Regular'),(920,440,'Thandai',100.00,100.00,NULL,NULL,91,'Full'),(921,450,'Mint Lemonade',100.00,100.00,NULL,NULL,91,'Regular'),(922,450,'Mint Lemonade',100.00,100.00,NULL,NULL,91,'Full'),(923,460,'Modinagar Shikanji',100.00,100.00,NULL,NULL,91,'Regular'),(924,460,'Modinagar Shikanji',100.00,100.00,NULL,NULL,91,'Full'),(925,470,'Fresh Lime',100.00,100.00,NULL,NULL,91,'Regular'),(926,470,'Fresh Lime',100.00,100.00,NULL,NULL,91,'Full'),(927,480,'Cold Coffee',100.00,100.00,NULL,NULL,91,'Regular'),(928,480,'Cold Coffee',100.00,100.00,NULL,NULL,91,'Full'),(929,490,'Home Made Chaach',100.00,100.00,NULL,NULL,91,'Regular'),(930,490,'Home Made Chaach',100.00,100.00,NULL,NULL,91,'Full'),(931,500,'Sicilian Chicken',100.00,100.00,NULL,NULL,91,'None'),(932,510,'Green Chicken',100.00,100.00,NULL,NULL,91,'None'),(933,520,'Pepper Chicken',100.00,100.00,NULL,NULL,91,'None'),(934,530,'Napoli',100.00,100.00,NULL,NULL,91,'None'),(935,540,'Spinach Corn Cheese',100.00,100.00,NULL,NULL,91,'None'),(936,550,'Balsamico',100.00,100.00,NULL,NULL,91,'None'),(937,560,'Chocolate Bun',100.00,100.00,NULL,NULL,91,'None'),(938,570,'Butter-Chicken Wrap',100.00,100.00,NULL,NULL,91,'None'),(939,580,'Mutton Lazeez Wrap',100.00,100.00,NULL,NULL,91,'None'),(940,590,'Kadhai Paneer Wrap',100.00,100.00,NULL,NULL,91,'None'),(941,600,'Chatpata Kebab Wrap',100.00,100.00,NULL,NULL,91,'None'),(942,610,'2 Minutes Sandwich',100.00,100.00,NULL,NULL,91,'None'),(943,620,'Homestyle Aloo',100.00,100.00,NULL,NULL,91,'None'),(944,630,'Bombay Special',100.00,100.00,NULL,NULL,91,'None'),(945,640,'Keema Pav',100.00,100.00,NULL,NULL,91,'None'),(946,650,'Egg Bun',100.00,100.00,NULL,NULL,91,'None'),(947,651,'Paneer Sambossa',100.00,100.00,NULL,NULL,91,'Single'),(948,652,'Chicken Sambossa',100.00,100.00,NULL,NULL,91,'Single'),(949,660,'Poha',100.00,100.00,NULL,NULL,91,'None'),(950,670,'Vada Pav',100.00,100.00,NULL,NULL,91,'None'),(951,680,'Bun Bhujia',100.00,100.00,NULL,NULL,91,'None'),(952,681,'Bun Maska - Achari',100.00,100.00,NULL,NULL,91,'None'),(953,682,'Bun Maska - Honey Lemon',100.00,100.00,NULL,NULL,91,'None'),(954,683,'Bun Maska - Sundried Tomatoes & Olives',100.00,100.00,NULL,NULL,91,'None'),(955,684,'Bun Maska - Mint Jalapeno',100.00,100.00,NULL,NULL,91,'None'),(956,690,'Bun Maska',100.00,100.00,NULL,NULL,91,'None'),(957,865,'Gujiya',100.00,100.00,NULL,NULL,91,'Double'),(958,691,'Chai Patti',100.00,100.00,NULL,NULL,91,'None'),(959,692,'Diwali Gift Box',100.00,100.00,NULL,NULL,91,'None'),(960,700,'Desi Chai Pack',100.00,100.00,NULL,NULL,91,'None'),(961,710,'Tulsi Adrak Chai Pack',100.00,100.00,NULL,NULL,91,'None'),(962,720,'Green Tea Pack',100.00,100.00,NULL,NULL,91,'None'),(963,730,'Chai Masala Pack',100.00,100.00,NULL,NULL,91,'None'),(964,840,'Badi Ketli',100.00,100.00,NULL,NULL,91,'None'),(965,850,'Choti Ketli',100.00,100.00,NULL,NULL,91,'None'),(966,740,'Blueberry Cake',100.00,100.00,NULL,NULL,91,'None'),(967,750,'Banana Cake',100.00,100.00,NULL,NULL,91,'None'),(968,760,'Carrot Cake',100.00,100.00,NULL,NULL,91,'None'),(969,770,'Lemon Cake',100.00,100.00,NULL,NULL,91,'None'),(970,780,'Rusk',100.00,100.00,NULL,NULL,91,'None'),(971,790,'Jeera Cookies',100.00,100.00,NULL,NULL,91,'None'),(972,800,'Badam Pista Cookies',100.00,100.00,NULL,NULL,91,'None'),(973,810,'Oatmeal Cookies',100.00,100.00,NULL,NULL,91,'None'),(974,866,'Chocolate Cupcake',100.00,100.00,NULL,NULL,91,'None'),(975,867,'Strawberry Cupcake',100.00,100.00,NULL,NULL,91,'None'),(976,860,'Additional Honey',100.00,100.00,NULL,NULL,91,'None'),(977,861,'Additional Kulhad',100.00,100.00,NULL,NULL,91,'None'),(978,862,'Extra Coffee Powder',100.00,100.00,NULL,NULL,91,'None'),(979,863,'Extra Chocolate Syrup',100.00,100.00,NULL,NULL,91,'None'),(980,864,'Extra RoofAfza Syrup',100.00,100.00,NULL,NULL,91,'None');
/*!40000 ALTER TABLE `REFERENCE_ORDER_MENU_ITEM` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `REFERENCE_ORDER_SCM_ITEM`
--

DROP TABLE IF EXISTS `REFERENCE_ORDER_SCM_ITEM`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `REFERENCE_ORDER_SCM_ITEM` (
  `SCM_ITEM_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PRODUCT_ID` int(11) NOT NULL,
  `PRODUCT_NAME` varchar(255) NOT NULL,
  `REQUESTED_QUANTITY` decimal(10,2) NOT NULL,
  `REQUESTED_ABSOLUTE_QUANTITY` decimal(10,2) NOT NULL,
  `TRANSFERRED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `RECEIVED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `FULFILLMENT_TYPE` varchar(30) DEFAULT NULL,
  `REFERENCE_ORDER_ID` int(11) NOT NULL,
  `UNIT_OF_MEASURE` varchar(10) NOT NULL,
  PRIMARY KEY (`SCM_ITEM_ID`),
  KEY `REFERENCE_ORDER_ID` (`REFERENCE_ORDER_ID`),
  CONSTRAINT `REFERENCE_ORDER_SCM_ITEM_ibfk_1` FOREIGN KEY (`REFERENCE_ORDER_ID`) REFERENCES `REFERENCE_ORDER` (`REFERENCE_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=699 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `REFERENCE_ORDER_SCM_ITEM`
--

LOCK TABLES `REFERENCE_ORDER_SCM_ITEM` WRITE;
/*!40000 ALTER TABLE `REFERENCE_ORDER_SCM_ITEM` DISABLE KEYS */;
INSERT INTO `REFERENCE_ORDER_SCM_ITEM` VALUES (1,100305,'Sachet - Desi Regular',150.00,150.00,0.00,0.00,'WAREHOUSE',1,'SACHET'),(2,100238,'Milk',11.51,11.51,0.00,0.00,'EXTERNAL',1,'L'),(3,100187,'Hot Cup 250 Ml',150.00,150.00,0.00,0.00,'WAREHOUSE',1,'PC'),(4,100305,'Sachet - Desi Regular',150.00,150.00,0.00,0.00,'WAREHOUSE',2,'SACHET'),(5,100238,'Milk',11.51,11.51,0.00,0.00,'EXTERNAL',2,'L'),(6,100187,'Hot Cup 250 Ml',150.00,150.00,0.00,0.00,'WAREHOUSE',2,'PC'),(7,100263,'Pav',1.00,1.00,0.00,0.00,'EXTERNAL',3,'PC'),(8,100381,'Vada Pav Filling',1.00,1.00,0.00,0.00,'KITCHEN',3,'PC'),(9,100180,'Hari Chuttney',0.03,0.03,0.00,0.00,'KITCHEN',3,'KG'),(10,100209,'Lasoon Chutney',0.00,0.00,0.00,0.00,'KITCHEN',3,'KG'),(11,100061,'Butter Paper Veg',1.00,1.00,0.00,0.00,'WAREHOUSE',3,'PC'),(12,100130,'Dip Cup',1.00,1.00,0.00,0.00,'WAREHOUSE',3,'PC'),(13,100263,'Pav',1.00,1.00,0.00,0.00,'EXTERNAL',4,'PC'),(14,100381,'Vada Pav Filling',1.00,1.00,0.00,0.00,'KITCHEN',4,'PC'),(15,100180,'Hari Chuttney',0.03,0.03,0.00,0.00,'KITCHEN',4,'KG'),(16,100209,'Lasoon Chutney',0.00,0.00,0.00,0.00,'KITCHEN',4,'KG'),(17,100061,'Butter Paper Veg',1.00,1.00,0.00,0.00,'WAREHOUSE',4,'PC'),(18,100130,'Dip Cup',1.00,1.00,0.00,0.00,'WAREHOUSE',4,'PC'),(19,100263,'Pav',5.00,5.00,0.00,0.00,'EXTERNAL',5,'PC'),(20,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,'KITCHEN',5,'PC'),(21,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,'KITCHEN',5,'KG'),(22,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,'KITCHEN',5,'KG'),(23,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,'WAREHOUSE',5,'PC'),(24,100130,'Dip Cup',5.00,5.00,0.00,0.00,'WAREHOUSE',5,'PC'),(25,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,'WAREHOUSE',6,'SACHET'),(26,100238,'Milk',7.67,7.67,0.00,0.00,'EXTERNAL',6,'L'),(27,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',6,'PC'),(28,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,'WAREHOUSE',7,'SACHET'),(29,100238,'Milk',7.67,7.67,0.00,0.00,'EXTERNAL',7,'L'),(30,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',7,'PC'),(31,100305,'Sachet - Desi Regular',45.00,45.00,0.00,0.00,'WAREHOUSE',8,'SACHET'),(32,100238,'Milk',9.69,9.69,0.00,0.00,'EXTERNAL',8,'L'),(33,100187,'Hot Cup 250 Ml',27.00,27.00,0.00,0.00,'WAREHOUSE',8,'PC'),(34,100188,'Hot Cups 360 Ml',16.00,16.00,0.00,0.00,'WAREHOUSE',8,'PC'),(35,100186,'Hot Cup 150 Ml',70.00,70.00,0.00,0.00,'WAREHOUSE',8,'PC'),(36,100350,'Chai delivery Box 400 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',8,'PC'),(37,100354,'Chai delivery Pouch 400 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',8,'PC'),(38,100334,'Stirrer',70.00,70.00,0.00,0.00,'WAREHOUSE',8,'PACKET'),(39,100340,'Sugar Sachet - White',35.00,35.00,0.00,0.00,'WAREHOUSE',8,'SACHET'),(40,100347,'Take Away Bag - Large',10.00,10.00,0.00,0.00,'WAREHOUSE',8,'PC'),(41,100077,'Chai delivery  Box 1 Ltr',5.00,5.00,0.00,0.00,'WAREHOUSE',8,'PC'),(42,100353,'Chai delivery Pouch 1 L',5.00,5.00,0.00,0.00,'WAREHOUSE',8,'PC'),(43,100004,'250 Ml Sipper Lid',7.00,7.00,0.00,0.00,'WAREHOUSE',8,'PC'),(44,100372,'Tray Mat',11.00,11.00,0.00,0.00,'WAREHOUSE',8,'PC'),(45,100098,'Cold Cup 350 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',8,'PC'),(46,100099,'Cold Cup Lid',9.00,9.00,0.00,0.00,'WAREHOUSE',8,'PC'),(47,100100,'Cold Cups 500 Ml',4.00,4.00,0.00,0.00,'WAREHOUSE',8,'PC'),(48,100263,'Pav',5.00,5.00,0.00,0.00,'EXTERNAL',8,'PC'),(49,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,'KITCHEN',8,'PC'),(50,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,'KITCHEN',8,'KG'),(51,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,'KITCHEN',8,'KG'),(52,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,'WAREHOUSE',8,'PC'),(53,100130,'Dip Cup',5.00,5.00,0.00,0.00,'WAREHOUSE',8,'PC'),(54,100305,'Sachet - Desi Regular',55.00,55.00,0.00,0.00,'WAREHOUSE',9,'SACHET'),(55,100238,'Milk',8.04,8.04,0.00,0.00,'EXTERNAL',9,'L'),(56,100187,'Hot Cup 250 Ml',15.00,15.00,0.00,0.00,'WAREHOUSE',9,'PC'),(57,100188,'Hot Cups 360 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',9,'PC'),(58,100186,'Hot Cup 150 Ml',110.00,110.00,0.00,0.00,'WAREHOUSE',9,'PC'),(59,100350,'Chai delivery Box 400 Ml',15.00,15.00,0.00,0.00,'WAREHOUSE',9,'PC'),(60,100354,'Chai delivery Pouch 400 Ml',15.00,15.00,0.00,0.00,'WAREHOUSE',9,'PC'),(61,100334,'Stirrer',110.00,110.00,0.00,0.00,'WAREHOUSE',9,'PACKET'),(62,100340,'Sugar Sachet - White',55.00,55.00,0.00,0.00,'WAREHOUSE',9,'SACHET'),(63,100347,'Take Away Bag - Large',20.00,20.00,0.00,0.00,'WAREHOUSE',9,'PC'),(64,100077,'Chai delivery  Box 1 Ltr',5.00,5.00,0.00,0.00,'WAREHOUSE',9,'PC'),(65,100353,'Chai delivery Pouch 1 L',5.00,5.00,0.00,0.00,'WAREHOUSE',9,'PC'),(66,100098,'Cold Cup 350 Ml',10.00,10.00,0.00,0.00,'WAREHOUSE',9,'PC'),(67,100099,'Cold Cup Lid',15.00,15.00,0.00,0.00,'WAREHOUSE',9,'PC'),(68,100372,'Tray Mat',7.50,7.50,0.00,0.00,'WAREHOUSE',9,'PC'),(69,100100,'Cold Cups 500 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',9,'PC'),(70,100263,'Pav',20.00,20.00,0.00,0.00,'EXTERNAL',9,'PC'),(71,100381,'Vada Pav Filling',20.00,20.00,0.00,0.00,'KITCHEN',9,'PC'),(72,100180,'Hari Chuttney',0.60,0.60,0.00,0.00,'KITCHEN',9,'KG'),(73,100209,'Lasoon Chutney',0.10,0.10,0.00,0.00,'KITCHEN',9,'KG'),(74,100061,'Butter Paper Veg',20.00,20.00,0.00,0.00,'WAREHOUSE',9,'PC'),(75,100130,'Dip Cup',20.00,20.00,0.00,0.00,'WAREHOUSE',9,'PC'),(76,100305,'Sachet - Desi Regular',50.00,50.00,0.00,0.00,'WAREHOUSE',10,'SACHET'),(77,100238,'Milk',4.39,4.39,0.00,0.00,'EXTERNAL',10,'L'),(78,100187,'Hot Cup 250 Ml',15.00,15.00,0.00,0.00,'WAREHOUSE',10,'PC'),(79,100188,'Hot Cups 360 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',10,'PC'),(80,100186,'Hot Cup 150 Ml',80.00,80.00,0.00,0.00,'WAREHOUSE',10,'PC'),(81,100350,'Chai delivery Box 400 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',10,'PC'),(82,100354,'Chai delivery Pouch 400 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',10,'PC'),(83,100334,'Stirrer',80.00,80.00,0.00,0.00,'WAREHOUSE',10,'PACKET'),(84,100340,'Sugar Sachet - White',40.00,40.00,0.00,0.00,'WAREHOUSE',10,'SACHET'),(85,100347,'Take Away Bag - Large',11.00,11.00,0.00,0.00,'WAREHOUSE',10,'PC'),(86,100077,'Chai delivery  Box 1 Ltr',6.00,6.00,0.00,0.00,'WAREHOUSE',10,'PC'),(87,100353,'Chai delivery Pouch 1 L',6.00,6.00,0.00,0.00,'WAREHOUSE',10,'PC'),(88,100305,'Sachet - Desi Regular',10.00,10.00,0.00,0.00,'WAREHOUSE',11,'SACHET'),(89,100238,'Milk',0.77,0.77,0.00,0.00,'EXTERNAL',11,'L'),(90,100187,'Hot Cup 250 Ml',10.00,10.00,0.00,0.00,'WAREHOUSE',11,'PC'),(91,100263,'Pav',5.00,5.00,0.00,0.00,'EXTERNAL',12,'PC'),(92,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,'KITCHEN',12,'PC'),(93,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,'KITCHEN',12,'KG'),(94,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,'KITCHEN',12,'KG'),(95,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,'WAREHOUSE',12,'PC'),(96,100130,'Dip Cup',5.00,5.00,0.00,0.00,'WAREHOUSE',12,'PC'),(97,100305,'Sachet - Desi Regular',20.00,20.00,0.00,0.00,'WAREHOUSE',13,'SACHET'),(98,100238,'Milk',5.19,5.19,0.00,0.00,'EXTERNAL',13,'L'),(99,100187,'Hot Cup 250 Ml',32.00,32.00,0.00,0.00,'WAREHOUSE',13,'PC'),(100,100004,'250 Ml Sipper Lid',12.00,12.00,0.00,0.00,'WAREHOUSE',13,'PC'),(101,100372,'Tray Mat',9.00,9.00,0.00,0.00,'WAREHOUSE',13,'PC'),(102,100098,'Cold Cup 350 Ml',6.00,6.00,0.00,0.00,'WAREHOUSE',13,'PC'),(103,100099,'Cold Cup Lid',6.00,6.00,0.00,0.00,'WAREHOUSE',13,'PC'),(104,100263,'Pav',5.00,5.00,0.00,0.00,'EXTERNAL',13,'PC'),(105,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,'KITCHEN',13,'PC'),(106,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,'KITCHEN',13,'KG'),(107,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,'KITCHEN',13,'KG'),(108,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,'WAREHOUSE',13,'PC'),(109,100130,'Dip Cup',5.00,5.00,0.00,0.00,'WAREHOUSE',13,'PC'),(110,100305,'Sachet - Desi Regular',20.00,20.00,0.00,0.00,'WAREHOUSE',14,'SACHET'),(111,100238,'Milk',3.43,3.43,0.00,0.00,'EXTERNAL',14,'L'),(112,100187,'Hot Cup 250 Ml',25.00,25.00,0.00,0.00,'WAREHOUSE',14,'PC'),(113,100004,'250 Ml Sipper Lid',5.00,5.00,0.00,0.00,'WAREHOUSE',14,'PC'),(114,100372,'Tray Mat',5.00,5.00,0.00,0.00,'WAREHOUSE',14,'PC'),(115,100098,'Cold Cup 350 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',14,'PC'),(116,100099,'Cold Cup Lid',5.00,5.00,0.00,0.00,'WAREHOUSE',14,'PC'),(117,100263,'Pav',10.00,10.00,0.00,0.00,'EXTERNAL',14,'PC'),(118,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,'KITCHEN',14,'PC'),(119,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,'KITCHEN',14,'KG'),(120,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,'KITCHEN',14,'KG'),(121,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,'WAREHOUSE',14,'PC'),(122,100130,'Dip Cup',10.00,10.00,0.00,0.00,'WAREHOUSE',14,'PC'),(123,100263,'Pav',5.00,5.00,0.00,0.00,'EXTERNAL',15,'PC'),(124,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,'KITCHEN',15,'PC'),(125,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,'KITCHEN',15,'KG'),(126,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,'KITCHEN',15,'KG'),(127,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,'WAREHOUSE',15,'PC'),(128,100130,'Dip Cup',5.00,5.00,0.00,0.00,'WAREHOUSE',15,'PC'),(129,100263,'Pav',10.00,10.00,0.00,0.00,'EXTERNAL',16,'PC'),(130,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,'KITCHEN',16,'PC'),(131,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,'KITCHEN',16,'KG'),(132,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,'KITCHEN',16,'KG'),(133,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,'WAREHOUSE',16,'PC'),(134,100130,'Dip Cup',10.00,10.00,0.00,0.00,'WAREHOUSE',16,'PC'),(135,100238,'Milk',1.20,1.20,0.00,0.00,'EXTERNAL',17,'L'),(136,100098,'Cold Cup 350 Ml',8.00,8.00,0.00,0.00,'WAREHOUSE',17,'PC'),(137,100099,'Cold Cup Lid',8.00,8.00,0.00,0.00,'WAREHOUSE',17,'PC'),(138,100372,'Tray Mat',4.00,4.00,0.00,0.00,'WAREHOUSE',17,'PC'),(139,100305,'Sachet - Desi Regular',20.00,20.00,0.00,0.00,'WAREHOUSE',55,'SACHET'),(140,100238,'Milk',1.65,1.65,0.00,0.00,'EXTERNAL',55,'L'),(141,100187,'Hot Cup 250 Ml',2.00,2.00,0.00,0.00,'WAREHOUSE',55,'PC'),(142,100188,'Hot Cups 360 Ml',1.00,1.00,0.00,0.00,'WAREHOUSE',55,'PC'),(143,100186,'Hot Cup 150 Ml',38.00,38.00,0.00,0.00,'WAREHOUSE',55,'PC'),(144,100350,'Chai delivery Box 400 Ml',2.00,2.00,0.00,0.00,'WAREHOUSE',55,'PC'),(145,100354,'Chai delivery Pouch 400 Ml',2.00,2.00,0.00,0.00,'WAREHOUSE',55,'PC'),(146,100334,'Stirrer',38.00,38.00,0.00,0.00,'WAREHOUSE',55,'PACKET'),(147,100340,'Sugar Sachet - White',19.00,19.00,0.00,0.00,'WAREHOUSE',55,'SACHET'),(148,100347,'Take Away Bag - Large',5.00,5.00,0.00,0.00,'WAREHOUSE',55,'PC'),(149,100077,'Chai delivery  Box 1 Ltr',3.00,3.00,0.00,0.00,'WAREHOUSE',55,'PC'),(150,100353,'Chai delivery Pouch 1 L',3.00,3.00,0.00,0.00,'WAREHOUSE',55,'PC'),(151,100305,'Sachet - Desi Regular',20.00,20.00,0.00,0.00,'WAREHOUSE',59,'SACHET'),(152,100238,'Milk',1.53,1.53,0.00,0.00,'EXTERNAL',59,'L'),(153,100187,'Hot Cup 250 Ml',20.00,20.00,0.00,0.00,'WAREHOUSE',59,'PC'),(154,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,'WAREHOUSE',60,'SACHET'),(155,100238,'Milk',6.75,6.75,0.00,0.00,'EXTERNAL',60,'L'),(156,100187,'Hot Cup 250 Ml',15.00,15.00,0.00,0.00,'WAREHOUSE',60,'PC'),(157,100188,'Hot Cups 360 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',60,'PC'),(158,100186,'Hot Cup 150 Ml',70.00,70.00,0.00,0.00,'WAREHOUSE',60,'PC'),(159,100350,'Chai delivery Box 400 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',60,'PC'),(160,100354,'Chai delivery Pouch 400 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',60,'PC'),(161,100334,'Stirrer',70.00,70.00,0.00,0.00,'WAREHOUSE',60,'PACKET'),(162,100340,'Sugar Sachet - White',35.00,35.00,0.00,0.00,'WAREHOUSE',60,'SACHET'),(163,100347,'Take Away Bag - Large',10.00,10.00,0.00,0.00,'WAREHOUSE',60,'PC'),(164,100077,'Chai delivery  Box 1 Ltr',5.00,5.00,0.00,0.00,'WAREHOUSE',60,'PC'),(165,100353,'Chai delivery Pouch 1 L',5.00,5.00,0.00,0.00,'WAREHOUSE',60,'PC'),(166,100098,'Cold Cup 350 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',60,'PC'),(167,100099,'Cold Cup Lid',15.00,15.00,0.00,0.00,'WAREHOUSE',60,'PC'),(168,100372,'Tray Mat',7.50,7.50,0.00,0.00,'WAREHOUSE',60,'PC'),(169,100100,'Cold Cups 500 Ml',10.00,10.00,0.00,0.00,'WAREHOUSE',60,'PC'),(170,100263,'Pav',10.00,10.00,0.00,0.00,'EXTERNAL',60,'PC'),(171,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,'KITCHEN',60,'PC'),(172,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,'KITCHEN',60,'KG'),(173,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,'KITCHEN',60,'KG'),(174,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,'WAREHOUSE',60,'PC'),(175,100130,'Dip Cup',10.00,10.00,0.00,0.00,'WAREHOUSE',60,'PC'),(176,100305,'Sachet - Desi Regular',45.00,45.00,0.00,0.00,'WAREHOUSE',61,'SACHET'),(177,100238,'Milk',8.56,8.56,0.00,0.00,'EXTERNAL',61,'L'),(178,100187,'Hot Cup 250 Ml',50.00,50.00,0.00,0.00,'WAREHOUSE',61,'PC'),(179,100188,'Hot Cups 360 Ml',7.00,7.00,0.00,0.00,'WAREHOUSE',61,'PC'),(180,100186,'Hot Cup 150 Ml',70.00,70.00,0.00,0.00,'WAREHOUSE',61,'PC'),(181,100350,'Chai delivery Box 400 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',61,'PC'),(182,100354,'Chai delivery Pouch 400 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',61,'PC'),(183,100334,'Stirrer',70.00,70.00,0.00,0.00,'WAREHOUSE',61,'PACKET'),(184,100340,'Sugar Sachet - White',35.00,35.00,0.00,0.00,'WAREHOUSE',61,'SACHET'),(185,100347,'Take Away Bag - Large',10.00,10.00,0.00,0.00,'WAREHOUSE',61,'PC'),(186,100077,'Chai delivery  Box 1 Ltr',5.00,5.00,0.00,0.00,'WAREHOUSE',61,'PC'),(187,100353,'Chai delivery Pouch 1 L',5.00,5.00,0.00,0.00,'WAREHOUSE',61,'PC'),(188,100004,'250 Ml Sipper Lid',5.00,5.00,0.00,0.00,'WAREHOUSE',61,'PC'),(189,100372,'Tray Mat',11.00,11.00,0.00,0.00,'WAREHOUSE',61,'PC'),(190,100098,'Cold Cup 350 Ml',5.00,5.00,0.00,0.00,'WAREHOUSE',61,'PC'),(191,100099,'Cold Cup Lid',15.00,15.00,0.00,0.00,'WAREHOUSE',61,'PC'),(192,100100,'Cold Cups 500 Ml',10.00,10.00,0.00,0.00,'WAREHOUSE',61,'PC'),(193,100263,'Pav',10.00,10.00,0.00,0.00,'EXTERNAL',61,'PC'),(194,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,'KITCHEN',61,'PC'),(195,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,'KITCHEN',61,'KG'),(196,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,'KITCHEN',61,'KG'),(197,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,'WAREHOUSE',61,'PC'),(198,100130,'Dip Cup',10.00,10.00,0.00,0.00,'WAREHOUSE',61,'PC'),(199,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,'WAREHOUSE',62,'SACHET'),(200,100238,'Milk',0.31,0.31,0.00,0.00,'EXTERNAL',62,'L'),(201,100187,'Hot Cup 250 Ml',2.00,2.00,0.00,0.00,'WAREHOUSE',62,'PC'),(202,100004,'250 Ml Sipper Lid',1.00,1.00,0.00,0.00,'WAREHOUSE',62,'PC'),(203,100372,'Tray Mat',0.50,0.50,0.00,0.00,'WAREHOUSE',62,'PC'),(204,100263,'Pav',1.00,1.00,0.00,0.00,'EXTERNAL',62,'PC'),(205,100381,'Vada Pav Filling',1.00,1.00,0.00,0.00,'KITCHEN',62,'PC'),(206,100180,'Hari Chuttney',0.03,0.03,0.00,0.00,'KITCHEN',62,'KG'),(207,100209,'Lasoon Chutney',0.00,0.00,0.00,0.00,'KITCHEN',62,'KG'),(208,100061,'Butter Paper Veg',1.00,1.00,0.00,0.00,'WAREHOUSE',62,'PC'),(209,100130,'Dip Cup',1.00,1.00,0.00,0.00,'WAREHOUSE',62,'PC'),(210,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,'WAREHOUSE',63,'SACHET'),(211,100238,'Milk',2.00,2.00,0.00,0.00,'EXTERNAL',63,'L'),(212,100187,'Hot Cup 250 Ml',1.00,1.00,0.00,0.00,'WAREHOUSE',63,'PC'),(213,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,'WAREHOUSE',64,'SACHET'),(214,100238,'Milk',0.08,0.08,0.00,0.00,'EXTERNAL',64,'L'),(215,100187,'Hot Cup 250 Ml',1.00,1.00,0.00,0.00,'WAREHOUSE',64,'PC'),(216,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,'WAREHOUSE',65,'SACHET'),(217,100238,'Milk',0.08,0.08,0.00,0.00,'EXTERNAL',65,'L'),(218,100187,'Hot Cup 250 Ml',1.00,1.00,0.00,0.00,'WAREHOUSE',65,'PC'),(219,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,'WAREHOUSE',66,'SACHET'),(220,100238,'Milk',0.08,0.08,0.00,0.00,'EXTERNAL',66,'L'),(221,100187,'Hot Cup 250 Ml',1.00,1.00,0.00,0.00,'WAREHOUSE',66,'PC'),(222,100238,'Milk',27.60,27.60,0.00,0.00,'EXTERNAL',67,'L'),(223,100004,'250 Ml Sipper Lid',120.00,120.00,0.00,0.00,'WAREHOUSE',67,'PC'),(224,100187,'Hot Cup 250 Ml',120.00,120.00,0.00,0.00,'WAREHOUSE',67,'PC'),(225,100372,'Tray Mat',60.00,60.00,0.00,0.00,'WAREHOUSE',67,'PC'),(226,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,'WAREHOUSE',68,'SACHET'),(227,100238,'Milk',7.67,7.67,0.00,0.00,'EXTERNAL',68,'L'),(228,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',68,'PC'),(229,100263,'Pav',100.00,100.00,0.00,0.00,'EXTERNAL',68,'PC'),(230,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',68,'PC'),(231,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,'KITCHEN',68,'KG'),(232,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',68,'KG'),(233,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,'WAREHOUSE',68,'PC'),(234,100130,'Dip Cup',100.00,100.00,0.00,0.00,'WAREHOUSE',68,'PC'),(235,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,'WAREHOUSE',69,'SACHET'),(236,100238,'Milk',50.00,50.00,0.00,0.00,'EXTERNAL',69,'L'),(237,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',69,'PC'),(238,100263,'Pav',100.00,100.00,0.00,0.00,'EXTERNAL',69,'PC'),(239,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',69,'PC'),(240,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,'KITCHEN',69,'KG'),(241,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',69,'KG'),(242,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,'WAREHOUSE',69,'PC'),(243,100130,'Dip Cup',100.00,100.00,0.00,0.00,'WAREHOUSE',69,'PC'),(244,100313,'Sicilian Chicken Filling',10.00,10.00,0.00,0.00,'KITCHEN',70,'PC'),(245,100060,'Butter Paper Non-Veg',15.00,15.00,0.00,0.00,'WAREHOUSE',70,'PC'),(246,100294,'Red Chilli Mayo Sauce',10.00,10.00,0.00,0.00,'KITCHEN',70,'PC'),(247,100366,'Tissue Paper',17.15,17.15,0.00,0.00,'WAREHOUSE',70,'PACKET'),(248,100130,'Dip Cup',27.00,27.00,0.00,0.00,'WAREHOUSE',70,'PC'),(249,100184,'Honey Garlic Mayo Sauce',0.34,0.34,0.00,0.00,'KITCHEN',70,'KG'),(250,100250,'Multigrain Bread',5.00,5.00,0.00,0.00,'EXTERNAL',70,'PC'),(251,100170,'Green Chicken Filling',5.00,5.00,0.00,0.00,'KITCHEN',70,'PC'),(252,100372,'Tray Mat',8.50,8.50,0.00,0.00,'WAREHOUSE',70,'PC'),(253,100061,'Butter Paper Veg',12.00,12.00,0.00,0.00,'WAREHOUSE',70,'PC'),(254,100114,'Croissant',4.00,4.00,0.00,0.00,'EXTERNAL',70,'PC'),(255,100266,'Pepper Chicken Filling',4.00,4.00,0.00,0.00,'KITCHEN',70,'PC'),(256,100155,'Focaccia',8.00,8.00,0.00,0.00,'EXTERNAL',70,'PC'),(257,100254,'Napoli Filling',8.00,8.00,0.00,0.00,'KITCHEN',70,'PC'),(258,100039,'Blueberry Cake Whole',0.63,0.63,0.00,0.00,'KITCHEN',70,'PC'),(259,100271,'Plastic Spoon',0.10,0.10,0.00,0.00,'WAREHOUSE',70,'PACKET'),(260,100277,'Poha Plate',5.00,5.00,0.00,0.00,'WAREHOUSE',70,'PC'),(261,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,'WAREHOUSE',71,'SACHET'),(262,100238,'Milk',9.00,9.00,0.00,0.00,'EXTERNAL',71,'L'),(263,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',71,'PC'),(264,100039,'Blueberry Cake Whole',1.50,1.50,0.00,0.00,'KITCHEN',72,'PC'),(265,100366,'Tissue Paper',0.12,0.12,0.00,0.00,'WAREHOUSE',72,'PACKET'),(266,100271,'Plastic Spoon',0.24,0.24,0.00,0.00,'WAREHOUSE',72,'PACKET'),(267,100277,'Poha Plate',12.00,12.00,0.00,0.00,'WAREHOUSE',72,'PC'),(268,100263,'Pav',40.00,40.00,0.00,0.00,'EXTERNAL',73,'PC'),(269,100381,'Vada Pav Filling',40.00,40.00,0.00,0.00,'KITCHEN',73,'PC'),(270,100180,'Hari Chuttney',1.20,1.20,0.00,0.00,'KITCHEN',73,'KG'),(271,100209,'Lasoon Chutney',0.20,0.20,0.00,0.00,'KITCHEN',73,'KG'),(272,100061,'Butter Paper Veg',40.00,40.00,0.00,0.00,'WAREHOUSE',73,'PC'),(273,100130,'Dip Cup',40.00,40.00,0.00,0.00,'WAREHOUSE',73,'PC'),(274,100305,'Sachet - Desi Regular',2.00,2.00,0.00,0.00,'WAREHOUSE',74,'SACHET'),(275,100238,'Milk',10.00,10.00,0.00,0.00,'EXTERNAL',74,'L'),(276,100187,'Hot Cup 250 Ml',15.00,15.00,0.00,0.00,'WAREHOUSE',74,'PC'),(277,100186,'Hot Cup 150 Ml',20.00,20.00,0.00,0.00,'WAREHOUSE',74,'PC'),(278,100334,'Stirrer',20.00,20.00,0.00,0.00,'WAREHOUSE',74,'PACKET'),(279,100340,'Sugar Sachet - White',10.00,10.00,0.00,0.00,'WAREHOUSE',74,'SACHET'),(280,100347,'Take Away Bag - Large',2.00,2.00,0.00,0.00,'WAREHOUSE',74,'PC'),(281,100077,'Chai delivery  Box 1 Ltr',2.00,2.00,0.00,0.00,'WAREHOUSE',74,'PC'),(282,100353,'Chai delivery Pouch 1 L',2.00,2.00,0.00,0.00,'WAREHOUSE',74,'PC'),(283,100098,'Cold Cup 350 Ml',10.00,10.00,0.00,0.00,'WAREHOUSE',74,'PC'),(284,100099,'Cold Cup Lid',10.00,10.00,0.00,0.00,'WAREHOUSE',74,'PC'),(285,100372,'Tray Mat',5.00,5.00,0.00,0.00,'WAREHOUSE',74,'PC'),(286,100263,'Pav',10.00,10.00,0.00,0.00,'EXTERNAL',74,'PC'),(287,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,'KITCHEN',74,'PC'),(288,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,'KITCHEN',74,'KG'),(289,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,'KITCHEN',74,'KG'),(290,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,'WAREHOUSE',74,'PC'),(291,100130,'Dip Cup',10.00,10.00,0.00,0.00,'WAREHOUSE',74,'PC'),(292,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,'WAREHOUSE',75,'SACHET'),(293,100238,'Milk',53.67,53.67,0.00,0.00,'EXTERNAL',75,'L'),(294,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',75,'PC'),(295,100188,'Hot Cups 360 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',75,'PC'),(296,100098,'Cold Cup 350 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',75,'PC'),(297,100099,'Cold Cup Lid',200.00,200.00,0.00,0.00,'WAREHOUSE',75,'PC'),(298,100372,'Tray Mat',100.00,100.00,0.00,0.00,'WAREHOUSE',75,'PC'),(299,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',75,'PC'),(300,100263,'Pav',100.00,100.00,0.00,0.00,'EXTERNAL',75,'PC'),(301,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',75,'PC'),(302,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,'KITCHEN',75,'KG'),(303,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',75,'KG'),(304,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,'WAREHOUSE',75,'PC'),(305,100130,'Dip Cup',100.00,100.00,0.00,0.00,'WAREHOUSE',75,'PC'),(306,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,'WAREHOUSE',76,'SACHET'),(307,100238,'Milk',53.67,53.67,0.00,0.00,'EXTERNAL',76,'L'),(308,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',76,'PC'),(309,100188,'Hot Cups 360 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',76,'PC'),(310,100098,'Cold Cup 350 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',76,'PC'),(311,100099,'Cold Cup Lid',200.00,200.00,0.00,0.00,'WAREHOUSE',76,'PC'),(312,100372,'Tray Mat',100.00,100.00,0.00,0.00,'WAREHOUSE',76,'PC'),(313,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',76,'PC'),(314,100263,'Pav',100.00,100.00,0.00,0.00,'EXTERNAL',76,'PC'),(315,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',76,'PC'),(316,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,'KITCHEN',76,'KG'),(317,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',76,'KG'),(318,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,'WAREHOUSE',76,'PC'),(319,100130,'Dip Cup',100.00,100.00,0.00,0.00,'WAREHOUSE',76,'PC'),(320,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,'WAREHOUSE',77,'SACHET'),(321,100238,'Milk',18.67,18.67,0.00,0.00,'EXTERNAL',77,'L'),(322,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',77,'PC'),(323,100188,'Hot Cups 360 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',77,'PC'),(324,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,'WAREHOUSE',78,'SACHET'),(325,100238,'Milk',35.69,35.69,0.00,0.00,'EXTERNAL',78,'L'),(326,100187,'Hot Cup 250 Ml',245.00,245.00,0.00,0.00,'WAREHOUSE',78,'PC'),(327,100004,'250 Ml Sipper Lid',45.00,45.00,0.00,0.00,'WAREHOUSE',78,'PC'),(328,100372,'Tray Mat',47.50,47.50,0.00,0.00,'WAREHOUSE',78,'PC'),(329,100099,'Cold Cup Lid',50.00,50.00,0.00,0.00,'WAREHOUSE',78,'PC'),(330,100100,'Cold Cups 500 Ml',50.00,50.00,0.00,0.00,'WAREHOUSE',78,'PC'),(331,100263,'Pav',100.00,100.00,0.00,0.00,'EXTERNAL',78,'PC'),(332,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',78,'PC'),(333,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,'KITCHEN',78,'KG'),(334,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',78,'KG'),(335,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,'WAREHOUSE',78,'PC'),(336,100130,'Dip Cup',100.00,100.00,0.00,0.00,'WAREHOUSE',78,'PC'),(337,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,'WAREHOUSE',79,'SACHET'),(338,100238,'Milk',8.00,8.00,0.00,0.00,'EXTERNAL',79,'L'),(339,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',79,'PC'),(340,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,'WAREHOUSE',80,'SACHET'),(341,100238,'Milk',35.69,35.69,0.00,0.00,'EXTERNAL',80,'L'),(342,100187,'Hot Cup 250 Ml',245.00,245.00,0.00,0.00,'WAREHOUSE',80,'PC'),(343,100004,'250 Ml Sipper Lid',45.00,45.00,0.00,0.00,'WAREHOUSE',80,'PC'),(344,100372,'Tray Mat',47.50,47.50,0.00,0.00,'WAREHOUSE',80,'PC'),(345,100099,'Cold Cup Lid',50.00,50.00,0.00,0.00,'WAREHOUSE',80,'PC'),(346,100100,'Cold Cups 500 Ml',50.00,50.00,0.00,0.00,'WAREHOUSE',80,'PC'),(347,100263,'Pav',100.00,100.00,0.00,0.00,'EXTERNAL',80,'PC'),(348,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',80,'PC'),(349,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,'KITCHEN',80,'KG'),(350,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',80,'KG'),(351,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,'WAREHOUSE',80,'PC'),(352,100130,'Dip Cup',100.00,100.00,0.00,0.00,'WAREHOUSE',80,'PC'),(353,100305,'Sachet - Desi Regular',89.00,89.00,0.00,0.00,'WAREHOUSE',81,'SACHET'),(354,100238,'Milk',31.24,31.24,0.00,0.00,'EXTERNAL',81,'L'),(355,100187,'Hot Cup 250 Ml',156.00,156.00,0.00,0.00,'WAREHOUSE',81,'PC'),(356,100004,'250 Ml Sipper Lid',67.00,67.00,0.00,0.00,'WAREHOUSE',81,'PC'),(357,100372,'Tray Mat',63.50,63.50,0.00,0.00,'WAREHOUSE',81,'PC'),(358,100098,'Cold Cup 350 Ml',60.00,60.00,0.00,0.00,'WAREHOUSE',81,'PC'),(359,100099,'Cold Cup Lid',60.00,60.00,0.00,0.00,'WAREHOUSE',81,'PC'),(360,100263,'Pav',50.00,50.00,0.00,0.00,'EXTERNAL',81,'PC'),(361,100381,'Vada Pav Filling',50.00,50.00,0.00,0.00,'KITCHEN',81,'PC'),(362,100180,'Hari Chuttney',1.50,1.50,0.00,0.00,'KITCHEN',81,'KG'),(363,100209,'Lasoon Chutney',0.25,0.25,0.00,0.00,'KITCHEN',81,'KG'),(364,100061,'Butter Paper Veg',50.00,50.00,0.00,0.00,'WAREHOUSE',81,'PC'),(365,100130,'Dip Cup',50.00,50.00,0.00,0.00,'WAREHOUSE',81,'PC'),(366,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,'WAREHOUSE',82,'SACHET'),(367,100238,'Milk',15.34,15.34,0.00,0.00,'EXTERNAL',82,'L'),(368,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',82,'PC'),(369,100305,'Sachet - Desi Regular',17.00,17.00,0.00,0.00,'WAREHOUSE',83,'SACHET'),(370,100238,'Milk',1.74,1.74,0.00,0.00,'EXTERNAL',83,'L'),(371,100187,'Hot Cup 250 Ml',9.00,9.00,0.00,0.00,'WAREHOUSE',83,'PC'),(372,100188,'Hot Cups 360 Ml',4.00,4.00,0.00,0.00,'WAREHOUSE',83,'PC'),(373,100186,'Hot Cup 150 Ml',24.00,24.00,0.00,0.00,'WAREHOUSE',83,'PC'),(374,100350,'Chai delivery Box 400 Ml',6.00,6.00,0.00,0.00,'WAREHOUSE',83,'PC'),(375,100354,'Chai delivery Pouch 400 Ml',6.00,6.00,0.00,0.00,'WAREHOUSE',83,'PC'),(376,100334,'Stirrer',24.00,24.00,0.00,0.00,'WAREHOUSE',83,'PACKET'),(377,100340,'Sugar Sachet - White',12.00,12.00,0.00,0.00,'WAREHOUSE',83,'SACHET'),(378,100347,'Take Away Bag - Large',6.00,6.00,0.00,0.00,'WAREHOUSE',83,'PC'),(379,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,'WAREHOUSE',84,'SACHET'),(380,100238,'Milk',16.00,16.00,0.00,0.00,'EXTERNAL',84,'L'),(381,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',84,'PC'),(382,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,'WAREHOUSE',85,'SACHET'),(383,100238,'Milk',16.00,16.00,0.00,0.00,'EXTERNAL',85,'L'),(384,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',85,'PC'),(385,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(386,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,'WAREHOUSE',86,'PC'),(387,100294,'Red Chilli Mayo Sauce',117.20,117.20,0.00,0.00,'KITCHEN',86,'PC'),(388,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,'WAREHOUSE',86,'PACKET'),(389,100130,'Dip Cup',900.00,900.00,0.00,0.00,'WAREHOUSE',86,'PC'),(390,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,'KITCHEN',86,'KG'),(391,100250,'Multigrain Bread',200.00,200.00,0.00,0.00,'EXTERNAL',86,'PC'),(392,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(393,100372,'Tray Mat',1000.00,1000.00,0.00,0.00,'WAREHOUSE',86,'PC'),(394,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,'WAREHOUSE',86,'PC'),(395,100114,'Croissant',200.00,200.00,0.00,0.00,'EXTERNAL',86,'PC'),(396,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(397,100155,'Focaccia',100.00,100.00,0.00,0.00,'EXTERNAL',86,'PC'),(398,100254,'Napoli Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(399,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(400,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(401,100147,'English Oven Bun',400.00,400.00,0.00,0.00,'KITCHEN',86,'PC'),(402,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(403,100030,'Bhujia',10.00,10.00,0.00,0.00,'WAREHOUSE',86,'KG'),(404,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,'KITCHEN',86,'KG'),(405,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(406,100258,'Onion',6.50,6.50,0.00,0.00,'KITCHEN',86,'KG'),(407,100067,'Capsicum',6.00,6.00,0.00,0.00,'KITCHEN',86,'KG'),(408,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(409,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(410,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(411,100081,'Chat Masala',0.40,0.40,0.00,0.00,'WAREHOUSE',86,'KG'),(412,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,'WAREHOUSE',86,'SACHET'),(413,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(414,100369,'Tomato',4.50,4.50,0.00,0.00,'WAREHOUSE',86,'KG'),(415,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,'KITCHEN',86,'KG'),(416,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(417,100263,'Pav',200.00,200.00,0.00,0.00,'EXTERNAL',86,'PC'),(418,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(419,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(420,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',86,'PC'),(421,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',86,'KG'),(422,100056,'Butter',2.00,2.00,0.00,0.00,'WAREHOUSE',86,'KG'),(423,100039,'Blueberry Cake Whole',12.50,12.50,0.00,0.00,'KITCHEN',86,'PC'),(424,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,'WAREHOUSE',86,'PACKET'),(425,100277,'Poha Plate',100.00,100.00,0.00,0.00,'WAREHOUSE',86,'PC'),(426,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(427,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,'WAREHOUSE',87,'PC'),(428,100294,'Red Chilli Mayo Sauce',117.20,117.20,0.00,0.00,'KITCHEN',87,'PC'),(429,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,'WAREHOUSE',87,'PACKET'),(430,100130,'Dip Cup',900.00,900.00,0.00,0.00,'WAREHOUSE',87,'PC'),(431,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,'KITCHEN',87,'KG'),(432,100250,'Multigrain Bread',200.00,200.00,0.00,0.00,'EXTERNAL',87,'PC'),(433,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(434,100372,'Tray Mat',1000.00,1000.00,0.00,0.00,'WAREHOUSE',87,'PC'),(435,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,'WAREHOUSE',87,'PC'),(436,100114,'Croissant',200.00,200.00,0.00,0.00,'EXTERNAL',87,'PC'),(437,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(438,100155,'Focaccia',100.00,100.00,0.00,0.00,'EXTERNAL',87,'PC'),(439,100254,'Napoli Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(440,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(441,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(442,100147,'English Oven Bun',400.00,400.00,0.00,0.00,'KITCHEN',87,'PC'),(443,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(444,100030,'Bhujia',10.00,10.00,0.00,0.00,'WAREHOUSE',87,'KG'),(445,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,'KITCHEN',87,'KG'),(446,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(447,100258,'Onion',6.50,6.50,0.00,0.00,'KITCHEN',87,'KG'),(448,100067,'Capsicum',6.00,6.00,0.00,0.00,'KITCHEN',87,'KG'),(449,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(450,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(451,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(452,100081,'Chat Masala',0.40,0.40,0.00,0.00,'WAREHOUSE',87,'KG'),(453,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,'WAREHOUSE',87,'SACHET'),(454,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(455,100369,'Tomato',4.50,4.50,0.00,0.00,'WAREHOUSE',87,'KG'),(456,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,'KITCHEN',87,'KG'),(457,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(458,100263,'Pav',200.00,200.00,0.00,0.00,'EXTERNAL',87,'PC'),(459,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(460,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(461,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',87,'PC'),(462,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',87,'KG'),(463,100056,'Butter',2.00,2.00,0.00,0.00,'WAREHOUSE',87,'KG'),(464,100039,'Blueberry Cake Whole',12.50,12.50,0.00,0.00,'KITCHEN',87,'PC'),(465,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,'WAREHOUSE',87,'PACKET'),(466,100277,'Poha Plate',100.00,100.00,0.00,0.00,'WAREHOUSE',87,'PC'),(467,100305,'Sachet - Desi Regular',800.00,800.00,0.00,0.00,'WAREHOUSE',88,'SACHET'),(468,100187,'Hot Cup 250 Ml',300.00,300.00,0.00,0.00,'WAREHOUSE',88,'PC'),(469,100188,'Hot Cups 360 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',88,'PC'),(470,100186,'Hot Cup 150 Ml',2800.00,2800.00,0.00,0.00,'WAREHOUSE',88,'PC'),(471,100350,'Chai delivery Box 400 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',88,'PC'),(472,100354,'Chai delivery Pouch 400 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',88,'PC'),(473,100334,'Stirrer',2800.00,2800.00,0.00,0.00,'WAREHOUSE',88,'PACKET'),(474,100340,'Sugar Sachet - White',1400.00,1400.00,0.00,0.00,'WAREHOUSE',88,'SACHET'),(475,100347,'Take Away Bag - Large',400.00,400.00,0.00,0.00,'WAREHOUSE',88,'PC'),(476,100077,'Chai delivery  Box 1 Ltr',200.00,200.00,0.00,0.00,'WAREHOUSE',88,'PC'),(477,100353,'Chai delivery Pouch 1 L',200.00,200.00,0.00,0.00,'WAREHOUSE',88,'PC'),(478,100004,'250 Ml Sipper Lid',100.00,100.00,0.00,0.00,'WAREHOUSE',88,'PC'),(479,100372,'Tray Mat',1250.00,1250.00,0.00,0.00,'WAREHOUSE',88,'PC'),(480,100210,'Lemon',0.60,0.60,0.00,0.00,'KITCHEN',88,'KG'),(481,100125,'Desi Chai Patti',0.26,0.26,0.00,0.00,'WAREHOUSE',88,'KG'),(482,100337,'Sugar',0.28,0.28,0.00,0.00,'WAREHOUSE',88,'KG'),(483,100078,'Chai Masala',0.20,0.20,0.00,0.00,'KITCHEN',88,'KG'),(484,100086,'Chocolate Syrup',4.00,4.00,0.00,0.00,'WAREHOUSE',88,'L'),(485,100098,'Cold Cup 350 Ml',400.00,400.00,0.00,0.00,'WAREHOUSE',88,'PC'),(486,100099,'Cold Cup Lid',500.00,500.00,0.00,0.00,'WAREHOUSE',88,'PC'),(487,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',88,'PC'),(488,100363,'Thandai',10.00,10.00,0.00,0.00,'WAREHOUSE',88,'L'),(489,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(490,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,'WAREHOUSE',88,'PC'),(491,100294,'Red Chilli Mayo Sauce',118.00,118.00,0.00,0.00,'KITCHEN',88,'PC'),(492,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,'WAREHOUSE',88,'PACKET'),(493,100130,'Dip Cup',900.00,900.00,0.00,0.00,'WAREHOUSE',88,'PC'),(494,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,'KITCHEN',88,'KG'),(495,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(496,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,'WAREHOUSE',88,'PC'),(497,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(498,100254,'Napoli Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(499,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(500,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(501,100147,'English Oven Bun',400.00,400.00,0.00,0.00,'KITCHEN',88,'PC'),(502,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(503,100030,'Bhujia',10.00,10.00,0.00,0.00,'WAREHOUSE',88,'KG'),(504,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,'KITCHEN',88,'KG'),(505,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(506,100258,'Onion',6.50,6.50,0.00,0.00,'KITCHEN',88,'KG'),(507,100067,'Capsicum',6.00,6.00,0.00,0.00,'KITCHEN',88,'KG'),(508,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(509,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(510,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(511,100081,'Chat Masala',0.40,0.40,0.00,0.00,'WAREHOUSE',88,'KG'),(512,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,'WAREHOUSE',88,'SACHET'),(513,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(514,100369,'Tomato',4.50,4.50,0.00,0.00,'WAREHOUSE',88,'KG'),(515,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,'KITCHEN',88,'KG'),(516,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(517,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(518,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(519,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',88,'PC'),(520,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',88,'KG'),(521,100056,'Butter',2.00,2.00,0.00,0.00,'WAREHOUSE',88,'KG'),(522,100039,'Blueberry Cake Whole',13.00,13.00,0.00,0.00,'KITCHEN',88,'PC'),(523,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,'WAREHOUSE',88,'PACKET'),(524,100277,'Poha Plate',100.00,100.00,0.00,0.00,'WAREHOUSE',88,'PC'),(525,100305,'Sachet - Desi Regular',800.00,800.00,0.00,0.00,'WAREHOUSE',89,'SACHET'),(526,100187,'Hot Cup 250 Ml',300.00,300.00,0.00,0.00,'WAREHOUSE',89,'PC'),(527,100188,'Hot Cups 360 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',89,'PC'),(528,100186,'Hot Cup 150 Ml',2800.00,2800.00,0.00,0.00,'WAREHOUSE',89,'PC'),(529,100350,'Chai delivery Box 400 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',89,'PC'),(530,100354,'Chai delivery Pouch 400 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',89,'PC'),(531,100334,'Stirrer',2800.00,2800.00,0.00,0.00,'WAREHOUSE',89,'PACKET'),(532,100340,'Sugar Sachet - White',1400.00,1400.00,0.00,0.00,'WAREHOUSE',89,'SACHET'),(533,100347,'Take Away Bag - Large',400.00,400.00,0.00,0.00,'WAREHOUSE',89,'PC'),(534,100077,'Chai delivery  Box 1 Ltr',200.00,200.00,0.00,0.00,'WAREHOUSE',89,'PC'),(535,100353,'Chai delivery Pouch 1 L',200.00,200.00,0.00,0.00,'WAREHOUSE',89,'PC'),(536,100004,'250 Ml Sipper Lid',100.00,100.00,0.00,0.00,'WAREHOUSE',89,'PC'),(537,100372,'Tray Mat',1250.00,1250.00,0.00,0.00,'WAREHOUSE',89,'PC'),(538,100210,'Lemon',0.60,0.60,0.00,0.00,'KITCHEN',89,'KG'),(539,100125,'Desi Chai Patti',0.26,0.26,0.00,0.00,'WAREHOUSE',89,'KG'),(540,100337,'Sugar',0.28,0.28,0.00,0.00,'WAREHOUSE',89,'KG'),(541,100078,'Chai Masala',0.20,0.20,0.00,0.00,'KITCHEN',89,'KG'),(542,100086,'Chocolate Syrup',4.00,4.00,0.00,0.00,'WAREHOUSE',89,'L'),(543,100098,'Cold Cup 350 Ml',400.00,400.00,0.00,0.00,'WAREHOUSE',89,'PC'),(544,100099,'Cold Cup Lid',500.00,500.00,0.00,0.00,'WAREHOUSE',89,'PC'),(545,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',89,'PC'),(546,100363,'Thandai',10.00,10.00,0.00,0.00,'WAREHOUSE',89,'L'),(547,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(548,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,'WAREHOUSE',89,'PC'),(549,100294,'Red Chilli Mayo Sauce',118.00,118.00,0.00,0.00,'KITCHEN',89,'PC'),(550,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,'WAREHOUSE',89,'PACKET'),(551,100130,'Dip Cup',900.00,900.00,0.00,0.00,'WAREHOUSE',89,'PC'),(552,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,'KITCHEN',89,'KG'),(553,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(554,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,'WAREHOUSE',89,'PC'),(555,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(556,100254,'Napoli Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(557,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(558,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(559,100147,'English Oven Bun',400.00,400.00,0.00,0.00,'KITCHEN',89,'PC'),(560,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(561,100030,'Bhujia',10.00,10.00,0.00,0.00,'WAREHOUSE',89,'KG'),(562,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,'KITCHEN',89,'KG'),(563,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(564,100258,'Onion',6.50,6.50,0.00,0.00,'KITCHEN',89,'KG'),(565,100067,'Capsicum',6.00,6.00,0.00,0.00,'KITCHEN',89,'KG'),(566,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(567,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(568,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(569,100081,'Chat Masala',0.40,0.40,0.00,0.00,'WAREHOUSE',89,'KG'),(570,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,'WAREHOUSE',89,'SACHET'),(571,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(572,100369,'Tomato',4.50,4.50,0.00,0.00,'WAREHOUSE',89,'KG'),(573,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,'KITCHEN',89,'KG'),(574,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(575,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(576,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(577,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',89,'PC'),(578,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',89,'KG'),(579,100056,'Butter',2.00,2.00,0.00,0.00,'WAREHOUSE',89,'KG'),(580,100039,'Blueberry Cake Whole',13.00,13.00,0.00,0.00,'KITCHEN',89,'PC'),(581,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,'WAREHOUSE',89,'PACKET'),(582,100277,'Poha Plate',100.00,100.00,0.00,0.00,'WAREHOUSE',89,'PC'),(583,100305,'Sachet - Desi Regular',800.00,800.00,0.00,0.00,'WAREHOUSE',90,'SACHET'),(584,100187,'Hot Cup 250 Ml',300.00,300.00,0.00,0.00,'WAREHOUSE',90,'PC'),(585,100188,'Hot Cups 360 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',90,'PC'),(586,100186,'Hot Cup 150 Ml',2800.00,2800.00,0.00,0.00,'WAREHOUSE',90,'PC'),(587,100350,'Chai delivery Box 400 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',90,'PC'),(588,100354,'Chai delivery Pouch 400 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',90,'PC'),(589,100334,'Stirrer',2800.00,2800.00,0.00,0.00,'WAREHOUSE',90,'PACKET'),(590,100340,'Sugar Sachet - White',1400.00,1400.00,0.00,0.00,'WAREHOUSE',90,'SACHET'),(591,100347,'Take Away Bag - Large',400.00,400.00,0.00,0.00,'WAREHOUSE',90,'PC'),(592,100077,'Chai delivery  Box 1 Ltr',200.00,200.00,0.00,0.00,'WAREHOUSE',90,'PC'),(593,100353,'Chai delivery Pouch 1 L',200.00,200.00,0.00,0.00,'WAREHOUSE',90,'PC'),(594,100004,'250 Ml Sipper Lid',100.00,100.00,0.00,0.00,'WAREHOUSE',90,'PC'),(595,100372,'Tray Mat',1250.00,1250.00,0.00,0.00,'WAREHOUSE',90,'PC'),(596,100210,'Lemon',0.60,0.60,0.00,0.00,'KITCHEN',90,'KG'),(597,100125,'Desi Chai Patti',0.26,0.26,0.00,0.00,'WAREHOUSE',90,'KG'),(598,100337,'Sugar',0.28,0.28,0.00,0.00,'WAREHOUSE',90,'KG'),(599,100078,'Chai Masala',0.20,0.20,0.00,0.00,'KITCHEN',90,'KG'),(600,100086,'Chocolate Syrup',4.00,4.00,0.00,0.00,'WAREHOUSE',90,'L'),(601,100098,'Cold Cup 350 Ml',400.00,400.00,0.00,0.00,'WAREHOUSE',90,'PC'),(602,100099,'Cold Cup Lid',500.00,500.00,0.00,0.00,'WAREHOUSE',90,'PC'),(603,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',90,'PC'),(604,100363,'Thandai',10.00,10.00,0.00,0.00,'WAREHOUSE',90,'L'),(605,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(606,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,'WAREHOUSE',90,'PC'),(607,100294,'Red Chilli Mayo Sauce',118.00,118.00,0.00,0.00,'KITCHEN',90,'PC'),(608,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,'WAREHOUSE',90,'PACKET'),(609,100130,'Dip Cup',900.00,900.00,0.00,0.00,'WAREHOUSE',90,'PC'),(610,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,'KITCHEN',90,'KG'),(611,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(612,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,'WAREHOUSE',90,'PC'),(613,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(614,100254,'Napoli Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(615,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(616,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(617,100147,'English Oven Bun',400.00,400.00,0.00,0.00,'KITCHEN',90,'PC'),(618,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(619,100030,'Bhujia',10.00,10.00,0.00,0.00,'WAREHOUSE',90,'KG'),(620,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,'KITCHEN',90,'KG'),(621,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(622,100258,'Onion',6.50,6.50,0.00,0.00,'KITCHEN',90,'KG'),(623,100067,'Capsicum',6.00,6.00,0.00,0.00,'KITCHEN',90,'KG'),(624,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(625,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(626,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(627,100081,'Chat Masala',0.40,0.40,0.00,0.00,'WAREHOUSE',90,'KG'),(628,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,'WAREHOUSE',90,'SACHET'),(629,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(630,100369,'Tomato',4.50,4.50,0.00,0.00,'WAREHOUSE',90,'KG'),(631,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,'KITCHEN',90,'KG'),(632,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(633,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(634,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(635,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',90,'PC'),(636,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',90,'KG'),(637,100056,'Butter',2.00,2.00,0.00,0.00,'WAREHOUSE',90,'KG'),(638,100039,'Blueberry Cake Whole',13.00,13.00,0.00,0.00,'KITCHEN',90,'PC'),(639,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,'WAREHOUSE',90,'PACKET'),(640,100277,'Poha Plate',100.00,100.00,0.00,0.00,'WAREHOUSE',90,'PC'),(641,100305,'Sachet - Desi Regular',800.00,800.00,0.00,0.00,'WAREHOUSE',91,'SACHET'),(642,100187,'Hot Cup 250 Ml',300.00,300.00,0.00,0.00,'WAREHOUSE',91,'PC'),(643,100188,'Hot Cups 360 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',91,'PC'),(644,100186,'Hot Cup 150 Ml',2800.00,2800.00,0.00,0.00,'WAREHOUSE',91,'PC'),(645,100350,'Chai delivery Box 400 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',91,'PC'),(646,100354,'Chai delivery Pouch 400 Ml',200.00,200.00,0.00,0.00,'WAREHOUSE',91,'PC'),(647,100334,'Stirrer',2800.00,2800.00,0.00,0.00,'WAREHOUSE',91,'PACKET'),(648,100340,'Sugar Sachet - White',1400.00,1400.00,0.00,0.00,'WAREHOUSE',91,'SACHET'),(649,100347,'Take Away Bag - Large',400.00,400.00,0.00,0.00,'WAREHOUSE',91,'PC'),(650,100077,'Chai delivery  Box 1 Ltr',200.00,200.00,0.00,0.00,'WAREHOUSE',91,'PC'),(651,100353,'Chai delivery Pouch 1 L',200.00,200.00,0.00,0.00,'WAREHOUSE',91,'PC'),(652,100004,'250 Ml Sipper Lid',100.00,100.00,0.00,0.00,'WAREHOUSE',91,'PC'),(653,100372,'Tray Mat',1250.00,1250.00,0.00,0.00,'WAREHOUSE',91,'PC'),(654,100210,'Lemon',0.60,0.60,0.00,0.00,'KITCHEN',91,'KG'),(655,100125,'Desi Chai Patti',0.26,0.26,0.00,0.00,'WAREHOUSE',91,'KG'),(656,100337,'Sugar',0.28,0.28,0.00,0.00,'WAREHOUSE',91,'KG'),(657,100078,'Chai Masala',0.20,0.20,0.00,0.00,'KITCHEN',91,'KG'),(658,100086,'Chocolate Syrup',4.00,4.00,0.00,0.00,'WAREHOUSE',91,'L'),(659,100098,'Cold Cup 350 Ml',400.00,400.00,0.00,0.00,'WAREHOUSE',91,'PC'),(660,100099,'Cold Cup Lid',500.00,500.00,0.00,0.00,'WAREHOUSE',91,'PC'),(661,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,'WAREHOUSE',91,'PC'),(662,100363,'Thandai',10.00,10.00,0.00,0.00,'WAREHOUSE',91,'L'),(663,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(664,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,'WAREHOUSE',91,'PC'),(665,100294,'Red Chilli Mayo Sauce',118.00,118.00,0.00,0.00,'KITCHEN',91,'PC'),(666,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,'WAREHOUSE',91,'PACKET'),(667,100130,'Dip Cup',900.00,900.00,0.00,0.00,'WAREHOUSE',91,'PC'),(668,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,'KITCHEN',91,'KG'),(669,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(670,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,'WAREHOUSE',91,'PC'),(671,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(672,100254,'Napoli Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(673,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(674,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(675,100147,'English Oven Bun',400.00,400.00,0.00,0.00,'KITCHEN',91,'PC'),(676,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(677,100030,'Bhujia',10.00,10.00,0.00,0.00,'WAREHOUSE',91,'KG'),(678,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,'KITCHEN',91,'KG'),(679,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(680,100258,'Onion',6.50,6.50,0.00,0.00,'KITCHEN',91,'KG'),(681,100067,'Capsicum',6.00,6.00,0.00,0.00,'KITCHEN',91,'KG'),(682,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(683,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(684,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(685,100081,'Chat Masala',0.40,0.40,0.00,0.00,'WAREHOUSE',91,'KG'),(686,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,'WAREHOUSE',91,'SACHET'),(687,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(688,100369,'Tomato',4.50,4.50,0.00,0.00,'WAREHOUSE',91,'KG'),(689,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,'KITCHEN',91,'KG'),(690,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(691,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(692,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(693,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,'KITCHEN',91,'PC'),(694,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,'KITCHEN',91,'KG'),(695,100056,'Butter',2.00,2.00,0.00,0.00,'WAREHOUSE',91,'KG'),(696,100039,'Blueberry Cake Whole',13.00,13.00,0.00,0.00,'KITCHEN',91,'PC'),(697,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,'WAREHOUSE',91,'PACKET'),(698,100277,'Poha Plate',100.00,100.00,0.00,0.00,'WAREHOUSE',91,'PC');
/*!40000 ALTER TABLE `REFERENCE_ORDER_SCM_ITEM` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `REQUEST_ORDER`
--

DROP TABLE IF EXISTS `REQUEST_ORDER`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `REQUEST_ORDER` (
  `REQUEST_ORDER_ID` int(11) NOT NULL AUTO_INCREMENT,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `REQUEST_UNIT_ID` int(11) NOT NULL,
  `FULFILLMENT_UNIT_ID` int(11) NOT NULL,
  `GENERATED_BY` int(11) NOT NULL,
  `REQUEST_ORDER_STATUS` varchar(30) NOT NULL,
  `REFERENCE_ORDER_ID` int(11) DEFAULT NULL,
  `FULFILLMENT_DATE` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `COMMENT` varchar(1000) DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`REQUEST_ORDER_ID`),
  KEY `REFERENCE_ORDER_ID` (`REFERENCE_ORDER_ID`),
  CONSTRAINT `REQUEST_ORDER_ibfk_1` FOREIGN KEY (`REFERENCE_ORDER_ID`) REFERENCES `REFERENCE_ORDER` (`REFERENCE_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=174 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `REQUEST_ORDER`
--

LOCK TABLES `REQUEST_ORDER` WRITE;
/*!40000 ALTER TABLE `REQUEST_ORDER` DISABLE KEYS */;
INSERT INTO `REQUEST_ORDER` VALUES (1,'2016-06-26 13:44:15',10000,10000,100000,'ACKNOWLEDGED',1,'2016-06-29 05:30:00',NULL,'2016-06-26 13:44:15'),(2,'2016-06-26 13:44:15',10000,13001,100000,'CREATED',1,'2016-06-29 05:30:00',NULL,'2016-06-26 13:44:15'),(3,'2016-06-26 13:44:27',10000,10000,100000,'ACKNOWLEDGED',2,'2016-06-29 05:30:00',NULL,'2016-06-26 13:44:27'),(4,'2016-06-26 13:44:27',10000,13001,100000,'CREATED',2,'2016-06-29 05:30:00',NULL,'2016-06-26 13:44:27'),(5,'2016-06-26 13:46:05',10000,10000,100000,'ACKNOWLEDGED',3,'2016-06-29 05:30:00',NULL,'2016-06-26 13:46:05'),(6,'2016-06-26 13:46:05',10000,13001,100000,'CREATED',3,'2016-06-29 05:30:00',NULL,'2016-06-26 13:46:05'),(7,'2016-06-26 13:46:05',10000,14001,100000,'CREATED',3,'2016-06-29 05:30:00',NULL,'2016-06-26 13:46:05'),(8,'2016-06-26 13:58:11',10000,10000,100000,'SETTLED',4,'2016-06-29 05:30:00',NULL,'2016-06-26 13:58:11'),(9,'2016-06-26 13:58:11',10000,13001,100000,'CREATED',4,'2016-06-29 05:30:00',NULL,'2016-06-26 13:58:11'),(10,'2016-06-26 13:58:11',10000,14001,100000,'CREATED',4,'2016-06-29 05:30:00',NULL,'2016-06-26 13:58:11'),(11,'2016-06-26 15:14:54',10006,10006,100000,'CREATED',5,'2016-06-30 05:30:00',NULL,'2016-06-26 15:14:54'),(12,'2016-06-26 15:14:54',10006,13001,100000,'CREATED',5,'2016-06-30 05:30:00',NULL,'2016-06-26 15:14:54'),(13,'2016-06-26 15:14:54',10006,14001,100000,'CREATED',5,'2016-06-30 05:30:00',NULL,'2016-06-26 15:14:54'),(14,'2016-06-26 15:36:59',10006,10006,100000,'CREATED',6,'2016-06-30 05:30:00',NULL,'2016-06-26 15:36:59'),(15,'2016-06-26 15:36:59',10006,13001,100000,'CREATED',6,'2016-06-30 05:30:00',NULL,'2016-06-26 15:36:59'),(16,'2016-06-26 15:43:00',10006,10006,100000,'CREATED',7,'2016-06-30 05:30:00',NULL,'2016-06-26 15:43:00'),(17,'2016-06-26 15:43:00',10006,13001,100000,'CREATED',7,'2016-06-30 05:30:00',NULL,'2016-06-26 15:43:00'),(18,'2016-06-26 15:56:13',10012,10000,100000,'CREATED',NULL,'2016-06-27 05:30:00',NULL,'2016-06-26 15:56:13'),(19,'2016-06-26 16:20:27',12015,12015,100000,'CREATED',8,'2016-06-28 05:30:00',NULL,'2016-06-26 16:20:27'),(20,'2016-06-26 16:20:27',12015,13001,100000,'CREATED',8,'2016-06-28 05:30:00',NULL,'2016-06-26 16:20:27'),(21,'2016-06-26 16:20:27',12015,14001,100000,'CREATED',8,'2016-06-28 05:30:00',NULL,'2016-06-26 16:20:27'),(22,'2016-06-26 16:28:57',12015,12015,100000,'CREATED',9,'2016-06-27 05:30:00',NULL,'2016-06-26 16:28:57'),(23,'2016-06-26 16:28:57',12015,13001,100000,'CREATED',9,'2016-06-27 05:30:00',NULL,'2016-06-26 16:28:57'),(24,'2016-06-26 16:28:57',12015,14001,100000,'CREATED',9,'2016-06-27 05:30:00',NULL,'2016-06-26 16:28:57'),(25,'2016-06-26 16:30:16',12015,13001,100000,'CREATED',NULL,'2016-06-28 05:30:00',NULL,'2016-06-26 16:30:16'),(26,'2016-06-27 10:50:14',14001,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','edsaa','2016-06-27 10:50:14'),(27,'2016-06-27 11:13:46',10011,13001,100000,'CREATED',10,'2016-06-29 05:30:00',NULL,'2016-06-27 11:13:46'),(28,'2016-06-27 11:13:46',10011,10011,100000,'CREATED',10,'2016-06-29 05:30:00',NULL,'2016-06-27 11:13:46'),(29,'2016-06-27 11:25:00',10000,13001,100000,'CREATED',11,'2016-06-28 05:30:00',NULL,'2016-06-27 11:25:00'),(30,'2016-06-27 11:25:00',10000,10000,100000,'SETTLED',11,'2016-06-28 05:30:00',NULL,'2016-06-27 11:25:00'),(31,'2016-06-27 11:54:52',10006,13001,100000,'CREATED',12,'2016-06-30 05:30:00',NULL,'2016-06-27 11:54:52'),(32,'2016-06-27 11:54:52',10006,10006,100000,'CREATED',12,'2016-06-30 05:30:00',NULL,'2016-06-27 11:54:52'),(33,'2016-06-27 11:54:52',10006,14001,100000,'CREATED',12,'2016-06-30 05:30:00',NULL,'2016-06-27 11:54:52'),(34,'2016-06-27 11:57:27',10006,13001,100000,'CREATED',13,'2016-06-30 05:30:00',NULL,'2016-06-27 11:57:27'),(35,'2016-06-27 11:57:27',10006,10006,100000,'CREATED',13,'2016-06-30 05:30:00',NULL,'2016-06-27 11:57:27'),(36,'2016-06-27 11:57:27',10006,14001,100000,'CREATED',13,'2016-06-30 05:30:00',NULL,'2016-06-27 11:57:27'),(37,'2016-06-27 12:05:42',10006,13001,100000,'CREATED',14,'2016-06-30 05:30:00',NULL,'2016-06-27 12:05:42'),(38,'2016-06-27 12:05:42',10006,10006,100000,'CREATED',14,'2016-06-30 05:30:00',NULL,'2016-06-27 12:05:42'),(39,'2016-06-27 12:05:42',10006,14001,100000,'CREATED',14,'2016-06-30 05:30:00',NULL,'2016-06-27 12:05:42'),(40,'2016-06-27 12:09:57',10006,13001,100000,'CREATED',15,'2016-06-30 05:30:00',NULL,'2016-06-27 12:09:57'),(41,'2016-06-27 12:09:57',10006,10006,100000,'CREATED',15,'2016-06-30 05:30:00',NULL,'2016-06-27 12:09:57'),(42,'2016-06-27 12:09:57',10006,14001,100000,'CREATED',15,'2016-06-30 05:30:00',NULL,'2016-06-27 12:09:57'),(43,'2016-06-27 12:15:21',10006,13001,100000,'CREATED',16,'2016-06-30 05:30:00',NULL,'2016-06-27 12:15:21'),(44,'2016-06-27 12:15:21',10006,10006,100000,'CREATED',16,'2016-06-30 05:30:00',NULL,'2016-06-27 12:15:21'),(45,'2016-06-27 12:15:21',10006,14001,100000,'CREATED',16,'2016-06-30 05:30:00',NULL,'2016-06-27 12:15:21'),(46,'2016-06-27 12:17:43',10006,13001,100000,'CREATED',17,'2016-06-30 05:30:00',NULL,'2016-06-27 12:17:43'),(47,'2016-06-27 12:17:43',10006,10006,100000,'CREATED',17,'2016-06-30 05:30:00',NULL,'2016-06-27 12:17:43'),(50,'2016-06-27 19:27:09',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:09'),(51,'2016-06-27 19:27:23',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:23'),(52,'2016-06-27 19:27:25',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:25'),(53,'2016-06-27 19:27:25',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:25'),(54,'2016-06-27 19:27:25',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:25'),(55,'2016-06-27 19:27:26',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:26'),(56,'2016-06-27 19:27:26',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:26'),(57,'2016-06-27 19:27:26',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:26'),(58,'2016-06-27 19:27:27',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:27'),(59,'2016-06-27 19:27:27',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:27'),(60,'2016-06-27 19:27:34',10000,10004,100000,'CREATED',NULL,'2016-06-29 05:30:00','Hola!!','2016-06-27 19:27:34'),(61,'2016-06-27 19:28:27',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:27'),(62,'2016-06-27 19:28:30',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:30'),(63,'2016-06-27 19:28:30',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:30'),(64,'2016-06-27 19:28:31',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:31'),(65,'2016-06-27 19:28:31',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:31'),(66,'2016-06-27 19:28:31',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:31'),(67,'2016-06-27 19:28:32',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:32'),(68,'2016-06-27 19:28:35',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:35'),(69,'2016-06-27 19:28:36',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:36'),(70,'2016-06-27 19:28:36',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:36'),(71,'2016-06-27 19:28:37',10000,10000,100000,'CREATED',NULL,'2016-06-29 05:30:00','231','2016-06-27 19:28:37'),(72,'2016-06-27 19:31:39',10000,10000,100000,'CREATED',55,'2016-06-29 05:30:00','Hello','2016-06-27 19:31:39'),(73,'2016-06-27 19:31:39',10000,13001,100000,'CREATED',55,'2016-06-29 05:30:00','Hello','2016-06-27 19:31:39'),(74,'2016-06-27 20:42:04',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','qwegyjk.','2016-06-27 20:42:04'),(75,'2016-06-27 20:45:55',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjmk,l','2016-06-27 20:45:55'),(76,'2016-06-27 20:45:59',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:45:59'),(77,'2016-06-27 20:46:00',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:46:00'),(78,'2016-06-27 20:46:00',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:46:00'),(79,'2016-06-27 20:46:00',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:46:00'),(80,'2016-06-27 20:46:01',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:46:01'),(81,'2016-06-27 20:46:02',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:46:02'),(82,'2016-06-27 20:46:02',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:46:02'),(83,'2016-06-27 20:46:02',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:46:02'),(84,'2016-06-27 20:46:03',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00','zesxgvbhjm','2016-06-27 20:46:03'),(85,'2016-06-27 21:06:13',10000,10000,100000,'SETTLED',59,'2016-07-27 05:30:00','send fast','2016-06-27 21:06:13'),(86,'2016-06-27 21:06:13',10000,13001,100000,'SETTLED',59,'2016-07-27 05:30:00','send fast','2016-06-27 21:06:13'),(87,'2016-06-27 23:03:46',10006,10006,100000,'SETTLED',60,'2016-06-28 05:30:00','Urgent delivery','2016-06-27 23:03:46'),(88,'2016-06-27 23:03:46',10006,13001,100000,'CREATED',60,'2016-06-28 05:30:00','Urgent delivery','2016-06-27 23:03:46'),(89,'2016-06-27 23:03:46',10006,14001,100000,'CREATED',60,'2016-06-28 05:30:00','Urgent delivery','2016-06-27 23:03:46'),(90,'2016-06-27 23:05:39',10006,10006,100000,'CREATED',61,'2016-06-29 05:30:00',NULL,'2016-06-27 23:05:39'),(91,'2016-06-27 23:05:39',10006,13001,100000,'CREATED',61,'2016-06-29 05:30:00',NULL,'2016-06-27 23:05:39'),(92,'2016-06-27 23:05:39',10006,14001,100000,'CREATED',61,'2016-06-29 05:30:00',NULL,'2016-06-27 23:05:39'),(93,'2016-06-27 23:07:29',10006,10006,100000,'CREATED',NULL,'2016-06-28 05:30:00',NULL,'2016-06-27 23:07:29'),(94,'2016-06-27 23:08:13',10006,10006,100000,'CREATED',NULL,'2016-06-29 05:30:00',NULL,'2016-06-27 23:08:13'),(95,'2016-06-27 23:11:56',10000,10000,100000,'CREATED',NULL,'2016-06-30 05:30:00',NULL,'2016-06-27 23:11:56'),(96,'2016-06-28 09:32:30',10000,10000,100000,'CREATED',62,'2016-06-29 05:30:00',NULL,'2016-06-28 09:32:30'),(97,'2016-06-28 09:32:30',10000,13001,100000,'CREATED',62,'2016-06-29 05:30:00',NULL,'2016-06-28 09:32:30'),(98,'2016-06-28 09:32:30',10000,14001,100000,'CREATED',62,'2016-06-29 05:30:00',NULL,'2016-06-28 09:32:30'),(99,'2016-06-28 09:40:58',10000,10000,100000,'CREATED',NULL,'2016-06-28 05:30:00',NULL,'2016-06-28 09:40:58'),(100,'2016-06-28 10:19:36',10000,10000,100000,'CREATED',63,'2016-06-28 05:30:00',NULL,'2016-06-28 10:19:36'),(101,'2016-06-28 10:19:36',10000,13001,100000,'CREATED',63,'2016-06-28 05:30:00',NULL,'2016-06-28 10:19:36'),(102,'2016-06-28 13:15:33',10000,13001,100000,'CREATED',64,'2016-06-28 05:30:00',NULL,'2016-06-28 13:15:33'),(103,'2016-06-28 13:15:33',10000,10000,100000,'CREATED',64,'2016-06-28 05:30:00',NULL,'2016-06-28 13:15:33'),(104,'2016-06-28 13:15:58',10000,13001,100000,'CREATED',65,'2016-06-28 05:30:00',NULL,'2016-06-28 13:15:58'),(105,'2016-06-28 13:15:58',10000,10000,100000,'CREATED',65,'2016-06-28 05:30:00',NULL,'2016-06-28 13:15:58'),(106,'2016-06-28 16:45:45',10001,10001,100000,'CREATED',66,'2016-06-30 05:30:00','Yes','2016-06-28 16:45:45'),(107,'2016-06-28 16:45:45',10001,13001,100000,'CREATED',66,'2016-06-30 05:30:00','Yes','2016-06-28 16:45:45'),(108,'2016-06-28 18:13:17',10000,10000,100000,'CREATED',67,'2016-06-29 05:30:00','asap','2016-06-28 18:13:17'),(109,'2016-06-28 18:13:17',10000,13001,100000,'CREATED',67,'2016-06-29 05:30:00','asap','2016-06-28 18:13:17'),(110,'2016-06-28 18:13:53',10006,10006,100000,'CREATED',68,'2016-06-30 05:30:00',NULL,'2016-06-28 18:13:53'),(111,'2016-06-28 18:13:53',10006,13001,100000,'CREATED',68,'2016-06-30 05:30:00',NULL,'2016-06-28 18:13:53'),(112,'2016-06-28 18:13:53',10006,14001,100000,'CREATED',68,'2016-06-30 05:30:00',NULL,'2016-06-28 18:13:53'),(113,'2016-06-28 18:15:00',10006,10006,100000,'CREATED',69,'2016-06-30 05:30:00',NULL,'2016-06-28 18:15:00'),(114,'2016-06-28 18:15:00',10006,13001,100000,'CREATED',69,'2016-06-30 05:30:00',NULL,'2016-06-28 18:15:00'),(115,'2016-06-28 18:15:00',10006,14001,100000,'CREATED',69,'2016-06-30 05:30:00',NULL,'2016-06-28 18:15:00'),(116,'2016-06-28 18:16:20',10000,10000,100000,'CREATED',70,'2016-06-28 05:30:00',NULL,'2016-06-28 18:16:20'),(117,'2016-06-28 18:16:20',10000,13001,100000,'CREATED',70,'2016-06-28 05:30:00',NULL,'2016-06-28 18:16:20'),(118,'2016-06-28 18:16:20',10000,14001,100000,'CREATED',70,'2016-06-28 05:30:00',NULL,'2016-06-28 18:16:20'),(119,'2016-06-28 18:17:21',10000,10000,100000,'CREATED',71,'2016-06-30 05:30:00',NULL,'2016-06-28 18:17:21'),(120,'2016-06-28 18:17:21',10000,13001,100000,'CREATED',71,'2016-06-30 05:30:00',NULL,'2016-06-28 18:17:21'),(121,'2016-06-28 18:17:24',10000,13001,100000,'CREATED',72,'2016-06-28 05:30:00',NULL,'2016-06-28 18:17:24'),(122,'2016-06-28 18:17:24',10000,14001,100000,'CREATED',72,'2016-06-28 05:30:00',NULL,'2016-06-28 18:17:24'),(123,'2016-06-28 18:19:32',10002,10002,100000,'CREATED',73,'2016-06-29 05:30:00',NULL,'2016-06-28 18:19:32'),(124,'2016-06-28 18:19:32',10002,13001,100000,'CREATED',73,'2016-06-29 05:30:00',NULL,'2016-06-28 18:19:32'),(125,'2016-06-28 18:19:32',10002,14001,100000,'CREATED',73,'2016-06-29 05:30:00',NULL,'2016-06-28 18:19:32'),(126,'2016-06-28 18:21:41',10006,10006,100000,'CREATED',74,'2016-06-30 05:30:00',NULL,'2016-06-28 18:21:41'),(127,'2016-06-28 18:21:41',10006,13001,100000,'CREATED',74,'2016-06-30 05:30:00',NULL,'2016-06-28 18:21:41'),(128,'2016-06-28 18:21:41',10006,14001,100000,'CREATED',74,'2016-06-30 05:30:00',NULL,'2016-06-28 18:21:41'),(129,'2016-06-28 18:21:57',10006,10006,100000,'CREATED',75,'2016-06-30 05:30:00',NULL,'2016-06-28 18:21:57'),(130,'2016-06-28 18:21:57',10006,13001,100000,'CREATED',75,'2016-06-30 05:30:00',NULL,'2016-06-28 18:21:57'),(131,'2016-06-28 18:21:57',10006,14001,100000,'CREATED',75,'2016-06-30 05:30:00',NULL,'2016-06-28 18:21:57'),(132,'2016-06-28 18:22:37',10006,10006,100000,'CREATED',76,'2016-06-30 05:30:00',NULL,'2016-06-28 18:22:37'),(133,'2016-06-28 18:22:37',10006,13001,100000,'CREATED',76,'2016-06-30 05:30:00',NULL,'2016-06-28 18:22:37'),(134,'2016-06-28 18:22:37',10006,14001,100000,'CREATED',76,'2016-06-30 05:30:00',NULL,'2016-06-28 18:22:37'),(135,'2016-06-28 18:23:22',10000,10000,100000,'CREATED',77,'2016-06-30 05:30:00',NULL,'2016-06-28 18:23:22'),(136,'2016-06-28 18:23:22',10000,13001,100000,'CREATED',77,'2016-06-30 05:30:00',NULL,'2016-06-28 18:23:22'),(137,'2016-06-28 18:25:55',10002,10002,100000,'CREATED',78,'2016-06-29 05:30:00',NULL,'2016-06-28 18:25:55'),(138,'2016-06-28 18:25:55',10002,13001,100000,'CREATED',78,'2016-06-29 05:30:00',NULL,'2016-06-28 18:25:55'),(139,'2016-06-28 18:25:55',10002,14001,100000,'CREATED',78,'2016-06-29 05:30:00',NULL,'2016-06-28 18:25:55'),(140,'2016-06-28 18:26:04',10006,10006,100000,'CREATED',79,'2016-06-30 05:30:00',NULL,'2016-06-28 18:26:04'),(141,'2016-06-28 18:26:05',10006,13001,100000,'CREATED',79,'2016-06-30 05:30:00',NULL,'2016-06-28 18:26:05'),(142,'2016-06-28 18:26:09',10002,10002,100000,'CREATED',80,'2016-06-29 05:30:00',NULL,'2016-06-28 18:26:09'),(143,'2016-06-28 18:26:09',10002,13001,100000,'CREATED',80,'2016-06-29 05:30:00',NULL,'2016-06-28 18:26:09'),(144,'2016-06-28 18:26:09',10002,14001,100000,'CREATED',80,'2016-06-29 05:30:00',NULL,'2016-06-28 18:26:09'),(145,'2016-06-28 18:27:13',10006,12015,100000,'CREATED',NULL,'2016-06-28 05:30:00',NULL,'2016-06-28 18:27:13'),(146,'2016-06-28 18:27:37',10000,10000,100000,'CREATED',81,'2016-06-28 05:30:00',NULL,'2016-06-28 18:27:37'),(147,'2016-06-28 18:27:37',10000,13001,100000,'CREATED',81,'2016-06-28 05:30:00',NULL,'2016-06-28 18:27:37'),(148,'2016-06-28 18:27:37',10000,14001,100000,'CREATED',81,'2016-06-28 05:30:00',NULL,'2016-06-28 18:27:37'),(149,'2016-06-28 18:36:02',10004,10004,100000,'CREATED',82,'2016-06-30 05:30:00',NULL,'2016-06-28 18:36:02'),(150,'2016-06-28 18:36:02',10004,13001,100000,'CREATED',82,'2016-06-30 05:30:00',NULL,'2016-06-28 18:36:02'),(151,'2016-06-28 18:43:52',10000,10000,120063,'CREATED',83,'2016-06-29 05:30:00',NULL,'2016-06-28 18:43:52'),(152,'2016-06-28 18:43:52',10000,13001,120063,'CREATED',83,'2016-06-29 05:30:00',NULL,'2016-06-28 18:43:52'),(153,'2016-06-28 18:44:29',10000,10000,100000,'CREATED',84,'2016-06-28 05:30:00',NULL,'2016-06-28 18:44:29'),(154,'2016-06-28 18:44:29',10000,13001,100000,'CREATED',84,'2016-06-28 05:30:00',NULL,'2016-06-28 18:44:29'),(155,'2016-06-28 18:48:58',10000,10000,100000,'CREATED',85,'2016-06-30 05:30:00','done','2016-06-28 18:48:58'),(156,'2016-06-28 18:48:58',10000,13001,100000,'CREATED',85,'2016-06-30 05:30:00','done','2016-06-28 18:48:58'),(157,'2016-06-29 21:29:22',10000,14001,100026,'CREATED',86,'2016-06-30 05:30:00','ds','2016-06-29 21:29:22'),(158,'2016-06-29 21:29:22',10000,10000,100026,'CREATED',86,'2016-06-30 05:30:00','ds','2016-06-29 21:29:22'),(159,'2016-06-29 21:29:22',10000,13001,100026,'CREATED',86,'2016-06-30 05:30:00','ds','2016-06-29 21:29:22'),(160,'2016-06-29 21:29:31',10000,14001,100026,'CREATED',87,'2016-06-30 05:30:00','fs','2016-06-29 21:29:31'),(161,'2016-06-29 21:29:31',10000,10000,100026,'CREATED',87,'2016-06-30 05:30:00','fs','2016-06-29 21:29:31'),(162,'2016-06-29 21:29:31',10000,13001,100026,'CREATED',87,'2016-06-30 05:30:00','fs','2016-06-29 21:29:31'),(163,'2016-06-29 21:35:50',10000,10000,100026,'CREATED',NULL,'2016-06-30 05:30:00','fd','2016-06-29 21:35:50'),(164,'2016-06-30 10:46:21',10000,13001,100000,'ACKNOWLEDGED',88,'2016-07-02 00:00:00',NULL,'2016-06-30 10:46:21'),(165,'2016-06-30 10:46:21',10000,14001,100000,'CREATED',88,'2016-07-02 00:00:00',NULL,'2016-06-30 10:46:21'),(166,'2016-06-30 10:46:40',10000,13001,100000,'ACKNOWLEDGED',89,'2016-07-02 00:00:00',NULL,'2016-06-30 10:46:40'),(167,'2016-06-30 10:46:40',10000,14001,100000,'CREATED',89,'2016-07-02 00:00:00',NULL,'2016-06-30 10:46:40'),(168,'2016-06-30 10:47:43',10000,13001,100000,'ACKNOWLEDGED',90,'2016-07-02 00:00:00',NULL,'2016-06-30 10:47:43'),(169,'2016-06-30 10:47:43',10000,14001,100000,'CREATED',90,'2016-07-02 00:00:00',NULL,'2016-06-30 10:47:43'),(170,'2016-06-30 10:48:54',10000,13001,100000,'ACKNOWLEDGED',91,'2016-07-02 00:00:00','rocking','2016-06-30 10:48:54'),(171,'2016-06-30 10:48:54',10000,14001,100000,'CREATED',91,'2016-07-02 00:00:00','rocking','2016-06-30 10:48:54'),(172,'2016-06-30 10:48:54',10000,10002,100000,'CREATED',NULL,'2016-06-30 00:00:00',NULL,'2016-06-30 10:48:54'),(173,'2016-07-01 12:36:01',10006,10000,100000,'CREATED',NULL,'2016-07-01 00:00:00','jaldi karo','2016-07-01 12:36:01');
/*!40000 ALTER TABLE `REQUEST_ORDER` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `REQUEST_ORDER_ITEM`
--

DROP TABLE IF EXISTS `REQUEST_ORDER_ITEM`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `REQUEST_ORDER_ITEM` (
  `REQUEST_ORDER_ITEM_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PRODUCT_ID` int(11) NOT NULL,
  `PRODUCT_NAME` varchar(255) NOT NULL,
  `REQUESTED_QUANTITY` decimal(10,2) NOT NULL,
  `REQUESTED_ABSOLUTE_QUANTITY` decimal(10,2) NOT NULL,
  `TRANSFERRED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `RECEIVED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `REQUEST_ORDER_ID` int(11) NOT NULL,
  `UNIT_OF_MEASURE` varchar(10) NOT NULL,
  `VENDOR_ID` int(11) DEFAULT NULL,
  PRIMARY KEY (`REQUEST_ORDER_ITEM_ID`),
  KEY `REQUEST_ORDER_ID` (`REQUEST_ORDER_ID`),
  CONSTRAINT `REQUEST_ORDER_ITEM_ibfk_1` FOREIGN KEY (`REQUEST_ORDER_ID`) REFERENCES `REQUEST_ORDER` (`REQUEST_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=717 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `REQUEST_ORDER_ITEM`
--

LOCK TABLES `REQUEST_ORDER_ITEM` WRITE;
/*!40000 ALTER TABLE `REQUEST_ORDER_ITEM` DISABLE KEYS */;
INSERT INTO `REQUEST_ORDER_ITEM` VALUES (1,100238,'Milk',11.51,11.51,0.00,0.00,1,'L',NULL),(2,100305,'Sachet - Desi Regular',150.00,150.00,0.00,0.00,2,'SACHET',NULL),(3,100187,'Hot Cup 250 Ml',150.00,150.00,0.00,0.00,2,'PC',NULL),(4,100238,'Milk',11.51,11.51,0.00,0.00,3,'L',NULL),(5,100305,'Sachet - Desi Regular',150.00,150.00,0.00,0.00,4,'SACHET',NULL),(6,100187,'Hot Cup 250 Ml',150.00,150.00,0.00,0.00,4,'PC',NULL),(7,100263,'Pav',1.00,1.00,0.00,0.00,5,'PC',NULL),(8,100061,'Butter Paper Veg',1.00,1.00,0.00,0.00,6,'PC',NULL),(9,100130,'Dip Cup',1.00,1.00,0.00,0.00,6,'PC',NULL),(10,100381,'Vada Pav Filling',1.00,1.00,0.00,0.00,7,'PC',NULL),(11,100180,'Hari Chuttney',0.03,0.03,0.00,0.00,7,'KG',NULL),(12,100209,'Lasoon Chutney',0.00,0.00,0.00,0.00,7,'KG',NULL),(13,100263,'Pav',1.00,1.00,0.00,0.00,8,'PC',NULL),(14,100061,'Butter Paper Veg',1.00,1.00,0.00,0.00,9,'PC',NULL),(15,100130,'Dip Cup',1.00,1.00,0.00,0.00,9,'PC',NULL),(16,100381,'Vada Pav Filling',1.00,1.00,0.00,0.00,10,'PC',NULL),(17,100180,'Hari Chuttney',0.03,0.03,0.00,0.00,10,'KG',NULL),(18,100209,'Lasoon Chutney',0.00,0.00,0.00,0.00,10,'KG',NULL),(19,100263,'Pav',5.00,5.00,0.00,0.00,11,'PC',NULL),(20,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,12,'PC',NULL),(21,100130,'Dip Cup',5.00,5.00,0.00,0.00,12,'PC',NULL),(22,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,13,'PC',NULL),(23,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,13,'KG',NULL),(24,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,13,'KG',NULL),(25,100238,'Milk',7.67,7.67,0.00,0.00,14,'L',NULL),(26,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,15,'SACHET',NULL),(27,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,15,'PC',NULL),(28,100238,'Milk',7.67,7.67,0.00,0.00,16,'L',NULL),(29,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,17,'SACHET',NULL),(30,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,17,'PC',NULL),(31,100002,'2 Minutes Filling',5.00,5.00,0.00,0.00,18,'PC',NULL),(32,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,18,'PC',NULL),(33,100351,'Tea Fun Box',10.00,10.00,0.00,0.00,18,'PC',NULL),(34,100238,'Milk',9.69,9.69,0.00,0.00,19,'L',NULL),(35,100263,'Pav',5.00,5.00,0.00,0.00,19,'PC',NULL),(36,100305,'Sachet - Desi Regular',45.00,45.00,0.00,0.00,20,'SACHET',NULL),(37,100187,'Hot Cup 250 Ml',27.00,27.00,0.00,0.00,20,'PC',NULL),(38,100188,'Hot Cups 360 Ml',16.00,16.00,0.00,0.00,20,'PC',NULL),(39,100186,'Hot Cup 150 Ml',70.00,70.00,0.00,0.00,20,'PC',NULL),(40,100350,'Chai delivery Box 400 Ml',5.00,5.00,0.00,0.00,20,'PC',NULL),(41,100354,'Chai delivery Pouch 400 Ml',5.00,5.00,0.00,0.00,20,'PC',NULL),(42,100334,'Stirrer',70.00,70.00,0.00,0.00,20,'PACKET',NULL),(43,100340,'Sugar Sachet - White',35.00,35.00,0.00,0.00,20,'SACHET',NULL),(44,100347,'Take Away Bag - Large',10.00,10.00,0.00,0.00,20,'PC',NULL),(45,100077,'Chai delivery  Box 1 Ltr',5.00,5.00,0.00,0.00,20,'PC',NULL),(46,100353,'Chai delivery Pouch 1 L',5.00,5.00,0.00,0.00,20,'PC',NULL),(47,100004,'250 Ml Sipper Lid',7.00,7.00,0.00,0.00,20,'PC',NULL),(48,100372,'Tray Mat',11.00,11.00,0.00,0.00,20,'PC',NULL),(49,100098,'Cold Cup 350 Ml',5.00,5.00,0.00,0.00,20,'PC',NULL),(50,100099,'Cold Cup Lid',9.00,9.00,0.00,0.00,20,'PC',NULL),(51,100100,'Cold Cups 500 Ml',4.00,4.00,0.00,0.00,20,'PC',NULL),(52,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,20,'PC',NULL),(53,100130,'Dip Cup',5.00,5.00,0.00,0.00,20,'PC',NULL),(54,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,21,'PC',NULL),(55,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,21,'KG',NULL),(56,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,21,'KG',NULL),(57,100238,'Milk',8.04,8.04,0.00,0.00,22,'L',NULL),(58,100263,'Pav',20.00,20.00,0.00,0.00,22,'PC',NULL),(59,100305,'Sachet - Desi Regular',55.00,55.00,0.00,0.00,23,'SACHET',NULL),(60,100187,'Hot Cup 250 Ml',15.00,15.00,0.00,0.00,23,'PC',NULL),(61,100188,'Hot Cups 360 Ml',5.00,5.00,0.00,0.00,23,'PC',NULL),(62,100186,'Hot Cup 150 Ml',110.00,110.00,0.00,0.00,23,'PC',NULL),(63,100350,'Chai delivery Box 400 Ml',15.00,15.00,0.00,0.00,23,'PC',NULL),(64,100354,'Chai delivery Pouch 400 Ml',15.00,15.00,0.00,0.00,23,'PC',NULL),(65,100334,'Stirrer',110.00,110.00,0.00,0.00,23,'PACKET',NULL),(66,100340,'Sugar Sachet - White',55.00,55.00,0.00,0.00,23,'SACHET',NULL),(67,100347,'Take Away Bag - Large',20.00,20.00,0.00,0.00,23,'PC',NULL),(68,100077,'Chai delivery  Box 1 Ltr',5.00,5.00,0.00,0.00,23,'PC',NULL),(69,100353,'Chai delivery Pouch 1 L',5.00,5.00,0.00,0.00,23,'PC',NULL),(70,100098,'Cold Cup 350 Ml',10.00,10.00,0.00,0.00,23,'PC',NULL),(71,100099,'Cold Cup Lid',15.00,15.00,0.00,0.00,23,'PC',NULL),(72,100372,'Tray Mat',7.50,7.50,0.00,0.00,23,'PC',NULL),(73,100100,'Cold Cups 500 Ml',5.00,5.00,0.00,0.00,23,'PC',NULL),(74,100061,'Butter Paper Veg',20.00,20.00,0.00,0.00,23,'PC',NULL),(75,100130,'Dip Cup',20.00,20.00,0.00,0.00,23,'PC',NULL),(76,100381,'Vada Pav Filling',20.00,20.00,0.00,0.00,24,'PC',NULL),(77,100180,'Hari Chuttney',0.60,0.60,0.00,0.00,24,'KG',NULL),(78,100209,'Lasoon Chutney',0.10,0.10,0.00,0.00,24,'KG',NULL),(79,100007,'A4 Paper Khanna',1.00,1.00,0.00,0.00,25,'PACKET',NULL),(80,100012,'Acrylic Milk Bottle',1.00,1.00,0.00,0.00,25,'PC',NULL),(81,100305,'Sachet - Desi Regular',50.00,50.00,0.00,0.00,27,'SACHET',NULL),(82,100187,'Hot Cup 250 Ml',15.00,15.00,0.00,0.00,27,'PC',NULL),(83,100188,'Hot Cups 360 Ml',5.00,5.00,0.00,0.00,27,'PC',NULL),(84,100186,'Hot Cup 150 Ml',80.00,80.00,0.00,0.00,27,'PC',NULL),(85,100350,'Chai delivery Box 400 Ml',5.00,5.00,0.00,0.00,27,'PC',NULL),(86,100354,'Chai delivery Pouch 400 Ml',5.00,5.00,0.00,0.00,27,'PC',NULL),(87,100334,'Stirrer',80.00,80.00,0.00,0.00,27,'PACKET',NULL),(88,100340,'Sugar Sachet - White',40.00,40.00,0.00,0.00,27,'SACHET',NULL),(89,100347,'Take Away Bag - Large',11.00,11.00,0.00,0.00,27,'PC',NULL),(90,100077,'Chai delivery  Box 1 Ltr',6.00,6.00,0.00,0.00,27,'PC',NULL),(91,100353,'Chai delivery Pouch 1 L',6.00,6.00,0.00,0.00,27,'PC',NULL),(92,100238,'Milk',4.39,4.39,0.00,0.00,28,'L',NULL),(93,100305,'Sachet - Desi Regular',10.00,10.00,0.00,0.00,29,'SACHET',NULL),(94,100187,'Hot Cup 250 Ml',10.00,10.00,0.00,0.00,29,'PC',NULL),(95,100238,'Milk',0.77,0.77,0.00,0.00,30,'L',NULL),(96,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,31,'PC',NULL),(97,100130,'Dip Cup',5.00,5.00,0.00,0.00,31,'PC',NULL),(98,100263,'Pav',5.00,5.00,0.00,0.00,32,'PC',NULL),(99,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,33,'PC',NULL),(100,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,33,'KG',NULL),(101,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,33,'KG',NULL),(102,100305,'Sachet - Desi Regular',20.00,20.00,0.00,0.00,34,'SACHET',NULL),(103,100187,'Hot Cup 250 Ml',32.00,32.00,0.00,0.00,34,'PC',NULL),(104,100004,'250 Ml Sipper Lid',12.00,12.00,0.00,0.00,34,'PC',NULL),(105,100372,'Tray Mat',9.00,9.00,0.00,0.00,34,'PC',NULL),(106,100098,'Cold Cup 350 Ml',6.00,6.00,0.00,0.00,34,'PC',NULL),(107,100099,'Cold Cup Lid',6.00,6.00,0.00,0.00,34,'PC',NULL),(108,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,34,'PC',NULL),(109,100130,'Dip Cup',5.00,5.00,0.00,0.00,34,'PC',NULL),(110,100238,'Milk',5.19,5.19,0.00,0.00,35,'L',NULL),(111,100263,'Pav',5.00,5.00,0.00,0.00,35,'PC',NULL),(112,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,36,'PC',NULL),(113,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,36,'KG',NULL),(114,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,36,'KG',NULL),(115,100305,'Sachet - Desi Regular',20.00,20.00,0.00,0.00,37,'SACHET',NULL),(116,100187,'Hot Cup 250 Ml',25.00,25.00,0.00,0.00,37,'PC',NULL),(117,100004,'250 Ml Sipper Lid',5.00,5.00,0.00,0.00,37,'PC',NULL),(118,100372,'Tray Mat',5.00,5.00,0.00,0.00,37,'PC',NULL),(119,100098,'Cold Cup 350 Ml',5.00,5.00,0.00,0.00,37,'PC',NULL),(120,100099,'Cold Cup Lid',5.00,5.00,0.00,0.00,37,'PC',NULL),(121,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,37,'PC',NULL),(122,100130,'Dip Cup',10.00,10.00,0.00,0.00,37,'PC',NULL),(123,100238,'Milk',3.43,3.43,0.00,0.00,38,'L',NULL),(124,100263,'Pav',10.00,10.00,0.00,0.00,38,'PC',NULL),(125,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,39,'PC',NULL),(126,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,39,'KG',NULL),(127,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,39,'KG',NULL),(128,100061,'Butter Paper Veg',5.00,5.00,0.00,0.00,40,'PC',NULL),(129,100130,'Dip Cup',5.00,5.00,0.00,0.00,40,'PC',NULL),(130,100263,'Pav',5.00,5.00,0.00,0.00,41,'PC',NULL),(131,100381,'Vada Pav Filling',5.00,5.00,0.00,0.00,42,'PC',NULL),(132,100180,'Hari Chuttney',0.15,0.15,0.00,0.00,42,'KG',NULL),(133,100209,'Lasoon Chutney',0.03,0.03,0.00,0.00,42,'KG',NULL),(134,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,43,'PC',NULL),(135,100130,'Dip Cup',10.00,10.00,0.00,0.00,43,'PC',NULL),(136,100263,'Pav',10.00,10.00,0.00,0.00,44,'PC',NULL),(137,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,45,'PC',NULL),(138,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,45,'KG',NULL),(139,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,45,'KG',NULL),(140,100098,'Cold Cup 350 Ml',8.00,8.00,0.00,0.00,46,'PC',NULL),(141,100099,'Cold Cup Lid',8.00,8.00,0.00,0.00,46,'PC',NULL),(142,100372,'Tray Mat',4.00,4.00,0.00,0.00,46,'PC',NULL),(143,100238,'Milk',1.20,1.20,0.00,0.00,47,'L',NULL),(144,100001,'1 Sink Unit',-7.00,-7.00,0.00,0.00,50,'PC',NULL),(145,100004,'250 Ml Sipper Lid',7.00,7.00,0.00,0.00,50,'PC',NULL),(146,100007,'A4 Paper Khanna',10.00,10.00,0.00,0.00,50,'PACKET',NULL),(147,100238,'Milk',1.65,1.65,0.00,0.00,72,'L',NULL),(148,100305,'Sachet - Desi Regular',20.00,20.00,0.00,0.00,73,'SACHET',NULL),(149,100187,'Hot Cup 250 Ml',2.00,2.00,0.00,0.00,73,'PC',NULL),(150,100188,'Hot Cups 360 Ml',1.00,1.00,0.00,0.00,73,'PC',NULL),(151,100186,'Hot Cup 150 Ml',38.00,38.00,0.00,0.00,73,'PC',NULL),(152,100350,'Chai delivery Box 400 Ml',2.00,2.00,0.00,0.00,73,'PC',NULL),(153,100354,'Chai delivery Pouch 400 Ml',2.00,2.00,0.00,0.00,73,'PC',NULL),(154,100334,'Stirrer',38.00,38.00,0.00,0.00,73,'PACKET',NULL),(155,100340,'Sugar Sachet - White',19.00,19.00,0.00,0.00,73,'SACHET',NULL),(156,100347,'Take Away Bag - Large',5.00,5.00,0.00,0.00,73,'PC',NULL),(157,100077,'Chai delivery  Box 1 Ltr',3.00,3.00,0.00,0.00,73,'PC',NULL),(158,100353,'Chai delivery Pouch 1 L',3.00,3.00,0.00,0.00,73,'PC',NULL),(159,100238,'Milk',1.53,1.53,0.00,0.00,85,'L',NULL),(160,100305,'Sachet - Desi Regular',20.00,20.00,0.00,0.00,86,'SACHET',NULL),(161,100187,'Hot Cup 250 Ml',20.00,20.00,0.00,0.00,86,'PC',NULL),(162,100238,'Milk',6.75,6.75,0.00,0.00,87,'L',NULL),(163,100263,'Pav',10.00,10.00,0.00,0.00,87,'PC',NULL),(164,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,88,'SACHET',NULL),(165,100187,'Hot Cup 250 Ml',15.00,15.00,0.00,0.00,88,'PC',NULL),(166,100188,'Hot Cups 360 Ml',5.00,5.00,0.00,0.00,88,'PC',NULL),(167,100186,'Hot Cup 150 Ml',70.00,70.00,0.00,0.00,88,'PC',NULL),(168,100350,'Chai delivery Box 400 Ml',5.00,5.00,0.00,0.00,88,'PC',NULL),(169,100354,'Chai delivery Pouch 400 Ml',5.00,5.00,0.00,0.00,88,'PC',NULL),(170,100334,'Stirrer',70.00,70.00,0.00,0.00,88,'PACKET',NULL),(171,100340,'Sugar Sachet - White',35.00,35.00,0.00,0.00,88,'SACHET',NULL),(172,100347,'Take Away Bag - Large',10.00,10.00,0.00,0.00,88,'PC',NULL),(173,100077,'Chai delivery  Box 1 Ltr',5.00,5.00,0.00,0.00,88,'PC',NULL),(174,100353,'Chai delivery Pouch 1 L',5.00,5.00,0.00,0.00,88,'PC',NULL),(175,100098,'Cold Cup 350 Ml',5.00,5.00,0.00,0.00,88,'PC',NULL),(176,100099,'Cold Cup Lid',15.00,15.00,0.00,0.00,88,'PC',NULL),(177,100372,'Tray Mat',7.50,7.50,0.00,0.00,88,'PC',NULL),(178,100100,'Cold Cups 500 Ml',10.00,10.00,0.00,0.00,88,'PC',NULL),(179,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,88,'PC',NULL),(180,100130,'Dip Cup',10.00,10.00,0.00,0.00,88,'PC',NULL),(181,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,89,'PC',NULL),(182,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,89,'KG',NULL),(183,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,89,'KG',NULL),(184,100238,'Milk',8.56,8.56,0.00,0.00,90,'L',NULL),(185,100263,'Pav',10.00,10.00,0.00,0.00,90,'PC',NULL),(186,100305,'Sachet - Desi Regular',45.00,45.00,0.00,0.00,91,'SACHET',NULL),(187,100187,'Hot Cup 250 Ml',50.00,50.00,0.00,0.00,91,'PC',NULL),(188,100188,'Hot Cups 360 Ml',7.00,7.00,0.00,0.00,91,'PC',NULL),(189,100186,'Hot Cup 150 Ml',70.00,70.00,0.00,0.00,91,'PC',NULL),(190,100350,'Chai delivery Box 400 Ml',5.00,5.00,0.00,0.00,91,'PC',NULL),(191,100354,'Chai delivery Pouch 400 Ml',5.00,5.00,0.00,0.00,91,'PC',NULL),(192,100334,'Stirrer',70.00,70.00,0.00,0.00,91,'PACKET',NULL),(193,100340,'Sugar Sachet - White',35.00,35.00,0.00,0.00,91,'SACHET',NULL),(194,100347,'Take Away Bag - Large',10.00,10.00,0.00,0.00,91,'PC',NULL),(195,100077,'Chai delivery  Box 1 Ltr',5.00,5.00,0.00,0.00,91,'PC',NULL),(196,100353,'Chai delivery Pouch 1 L',5.00,5.00,0.00,0.00,91,'PC',NULL),(197,100004,'250 Ml Sipper Lid',5.00,5.00,0.00,0.00,91,'PC',NULL),(198,100372,'Tray Mat',11.00,11.00,0.00,0.00,91,'PC',NULL),(199,100098,'Cold Cup 350 Ml',5.00,5.00,0.00,0.00,91,'PC',NULL),(200,100099,'Cold Cup Lid',15.00,15.00,0.00,0.00,91,'PC',NULL),(201,100100,'Cold Cups 500 Ml',10.00,10.00,0.00,0.00,91,'PC',NULL),(202,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,91,'PC',NULL),(203,100130,'Dip Cup',10.00,10.00,0.00,0.00,91,'PC',NULL),(204,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,92,'PC',NULL),(205,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,92,'KG',NULL),(206,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,92,'KG',NULL),(207,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,93,'PC',NULL),(208,100103,'Container 1.5L',1.00,1.00,0.00,0.00,93,'PC',NULL),(209,100001,'1 Sink Unit',1.00,1.00,0.00,0.00,94,'PC',NULL),(210,100009,'AC',1.00,1.00,0.00,0.00,94,'PC',NULL),(211,100238,'Milk',1.00,1.00,0.00,0.00,95,'L',NULL),(212,100238,'Milk',0.31,0.31,0.00,0.00,96,'L',NULL),(213,100263,'Pav',1.00,1.00,0.00,0.00,96,'PC',NULL),(214,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,97,'SACHET',NULL),(215,100187,'Hot Cup 250 Ml',2.00,2.00,0.00,0.00,97,'PC',NULL),(216,100004,'250 Ml Sipper Lid',1.00,1.00,0.00,0.00,97,'PC',NULL),(217,100372,'Tray Mat',0.50,0.50,0.00,0.00,97,'PC',NULL),(218,100061,'Butter Paper Veg',1.00,1.00,0.00,0.00,97,'PC',NULL),(219,100130,'Dip Cup',1.00,1.00,0.00,0.00,97,'PC',NULL),(220,100381,'Vada Pav Filling',1.00,1.00,0.00,0.00,98,'PC',NULL),(221,100180,'Hari Chuttney',0.03,0.03,0.00,0.00,98,'KG',NULL),(222,100209,'Lasoon Chutney',0.00,0.00,0.00,0.00,98,'KG',NULL),(223,100381,'Vada Pav Filling',2.00,2.00,0.00,0.00,99,'PC',NULL),(224,100238,'Milk',2.00,2.00,0.00,0.00,100,'L',NULL),(225,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,101,'SACHET',NULL),(226,100187,'Hot Cup 250 Ml',1.00,1.00,0.00,0.00,101,'PC',NULL),(227,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,102,'SACHET',NULL),(228,100187,'Hot Cup 250 Ml',1.00,1.00,0.00,0.00,102,'PC',NULL),(229,100238,'Milk',0.08,0.08,0.00,0.00,103,'L',NULL),(230,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,104,'SACHET',NULL),(231,100187,'Hot Cup 250 Ml',1.00,1.00,0.00,0.00,104,'PC',NULL),(232,100238,'Milk',0.08,0.08,0.00,0.00,105,'L',NULL),(233,100238,'Milk',0.08,0.08,0.00,0.00,106,'L',NULL),(234,100305,'Sachet - Desi Regular',1.00,1.00,0.00,0.00,107,'SACHET',NULL),(235,100187,'Hot Cup 250 Ml',1.00,1.00,0.00,0.00,107,'PC',NULL),(236,100238,'Milk',27.60,27.60,0.00,0.00,108,'L',NULL),(237,100004,'250 Ml Sipper Lid',120.00,120.00,0.00,0.00,109,'PC',NULL),(238,100187,'Hot Cup 250 Ml',120.00,120.00,0.00,0.00,109,'PC',NULL),(239,100372,'Tray Mat',60.00,60.00,0.00,0.00,109,'PC',NULL),(240,100238,'Milk',7.67,7.67,0.00,0.00,110,'L',NULL),(241,100263,'Pav',100.00,100.00,0.00,0.00,110,'PC',NULL),(242,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,111,'SACHET',NULL),(243,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,111,'PC',NULL),(244,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,111,'PC',NULL),(245,100130,'Dip Cup',100.00,100.00,0.00,0.00,111,'PC',NULL),(246,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,112,'PC',NULL),(247,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,112,'KG',NULL),(248,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,112,'KG',NULL),(249,100238,'Milk',50.00,50.00,0.00,0.00,113,'L',NULL),(250,100263,'Pav',100.00,100.00,0.00,0.00,113,'PC',NULL),(251,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,114,'SACHET',NULL),(252,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,114,'PC',NULL),(253,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,114,'PC',NULL),(254,100130,'Dip Cup',100.00,100.00,0.00,0.00,114,'PC',NULL),(255,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,115,'PC',NULL),(256,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,115,'KG',NULL),(257,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,115,'KG',NULL),(258,100250,'Multigrain Bread',5.00,5.00,0.00,0.00,116,'PC',NULL),(259,100114,'Croissant',4.00,4.00,0.00,0.00,116,'PC',NULL),(260,100155,'Focaccia',8.00,8.00,0.00,0.00,116,'PC',NULL),(261,100060,'Butter Paper Non-Veg',15.00,15.00,0.00,0.00,117,'PC',NULL),(262,100366,'Tissue Paper',17.15,17.15,0.00,0.00,117,'PACKET',NULL),(263,100130,'Dip Cup',27.00,27.00,0.00,0.00,117,'PC',NULL),(264,100372,'Tray Mat',8.50,8.50,0.00,0.00,117,'PC',NULL),(265,100061,'Butter Paper Veg',12.00,12.00,0.00,0.00,117,'PC',NULL),(266,100271,'Plastic Spoon',0.10,0.10,0.00,0.00,117,'PACKET',NULL),(267,100277,'Poha Plate',5.00,5.00,0.00,0.00,117,'PC',NULL),(268,100313,'Sicilian Chicken Filling',10.00,10.00,0.00,0.00,118,'PC',NULL),(269,100294,'Red Chilli Mayo Sauce',10.00,10.00,0.00,0.00,118,'PC',NULL),(270,100184,'Honey Garlic Mayo Sauce',0.34,0.34,0.00,0.00,118,'KG',NULL),(271,100170,'Green Chicken Filling',5.00,5.00,0.00,0.00,118,'PC',NULL),(272,100266,'Pepper Chicken Filling',4.00,4.00,0.00,0.00,118,'PC',NULL),(273,100254,'Napoli Filling',8.00,8.00,0.00,0.00,118,'PC',NULL),(274,100039,'Blueberry Cake Whole',0.63,0.63,0.00,0.00,118,'PC',NULL),(275,100238,'Milk',9.00,9.00,0.00,0.00,119,'L',NULL),(276,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,120,'SACHET',NULL),(277,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,120,'PC',NULL),(278,100366,'Tissue Paper',0.12,0.12,0.00,0.00,121,'PACKET',NULL),(279,100271,'Plastic Spoon',0.24,0.24,0.00,0.00,121,'PACKET',NULL),(280,100277,'Poha Plate',12.00,12.00,0.00,0.00,121,'PC',NULL),(281,100039,'Blueberry Cake Whole',1.50,1.50,0.00,0.00,122,'PC',NULL),(282,100263,'Pav',40.00,40.00,0.00,0.00,123,'PC',NULL),(283,100061,'Butter Paper Veg',40.00,40.00,0.00,0.00,124,'PC',NULL),(284,100130,'Dip Cup',40.00,40.00,0.00,0.00,124,'PC',NULL),(285,100381,'Vada Pav Filling',40.00,40.00,0.00,0.00,125,'PC',NULL),(286,100180,'Hari Chuttney',1.20,1.20,0.00,0.00,125,'KG',NULL),(287,100209,'Lasoon Chutney',0.20,0.20,0.00,0.00,125,'KG',NULL),(288,100238,'Milk',10.00,10.00,0.00,0.00,126,'L',NULL),(289,100263,'Pav',10.00,10.00,0.00,0.00,126,'PC',NULL),(290,100305,'Sachet - Desi Regular',2.00,2.00,0.00,0.00,127,'SACHET',NULL),(291,100187,'Hot Cup 250 Ml',15.00,15.00,0.00,0.00,127,'PC',NULL),(292,100186,'Hot Cup 150 Ml',20.00,20.00,0.00,0.00,127,'PC',NULL),(293,100334,'Stirrer',20.00,20.00,0.00,0.00,127,'PACKET',NULL),(294,100340,'Sugar Sachet - White',10.00,10.00,0.00,0.00,127,'SACHET',NULL),(295,100347,'Take Away Bag - Large',2.00,2.00,0.00,0.00,127,'PC',NULL),(296,100077,'Chai delivery  Box 1 Ltr',2.00,2.00,0.00,0.00,127,'PC',NULL),(297,100353,'Chai delivery Pouch 1 L',2.00,2.00,0.00,0.00,127,'PC',NULL),(298,100098,'Cold Cup 350 Ml',10.00,10.00,0.00,0.00,127,'PC',NULL),(299,100099,'Cold Cup Lid',10.00,10.00,0.00,0.00,127,'PC',NULL),(300,100372,'Tray Mat',5.00,5.00,0.00,0.00,127,'PC',NULL),(301,100061,'Butter Paper Veg',10.00,10.00,0.00,0.00,127,'PC',NULL),(302,100130,'Dip Cup',10.00,10.00,0.00,0.00,127,'PC',NULL),(303,100381,'Vada Pav Filling',10.00,10.00,0.00,0.00,128,'PC',NULL),(304,100180,'Hari Chuttney',0.30,0.30,0.00,0.00,128,'KG',NULL),(305,100209,'Lasoon Chutney',0.05,0.05,0.00,0.00,128,'KG',NULL),(306,100238,'Milk',53.67,53.67,0.00,0.00,129,'L',NULL),(307,100263,'Pav',100.00,100.00,0.00,0.00,129,'PC',NULL),(308,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,130,'SACHET',NULL),(309,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,130,'PC',NULL),(310,100188,'Hot Cups 360 Ml',100.00,100.00,0.00,0.00,130,'PC',NULL),(311,100098,'Cold Cup 350 Ml',100.00,100.00,0.00,0.00,130,'PC',NULL),(312,100099,'Cold Cup Lid',200.00,200.00,0.00,0.00,130,'PC',NULL),(313,100372,'Tray Mat',100.00,100.00,0.00,0.00,130,'PC',NULL),(314,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,130,'PC',NULL),(315,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,130,'PC',NULL),(316,100130,'Dip Cup',100.00,100.00,0.00,0.00,130,'PC',NULL),(317,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,131,'PC',NULL),(318,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,131,'KG',NULL),(319,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,131,'KG',NULL),(320,100238,'Milk',53.67,53.67,0.00,0.00,132,'L',NULL),(321,100263,'Pav',100.00,100.00,0.00,0.00,132,'PC',NULL),(322,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,133,'SACHET',NULL),(323,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,133,'PC',NULL),(324,100188,'Hot Cups 360 Ml',100.00,100.00,0.00,0.00,133,'PC',NULL),(325,100098,'Cold Cup 350 Ml',100.00,100.00,0.00,0.00,133,'PC',NULL),(326,100099,'Cold Cup Lid',200.00,200.00,0.00,0.00,133,'PC',NULL),(327,100372,'Tray Mat',100.00,100.00,0.00,0.00,133,'PC',NULL),(328,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,133,'PC',NULL),(329,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,133,'PC',NULL),(330,100130,'Dip Cup',100.00,100.00,0.00,0.00,133,'PC',NULL),(331,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,134,'PC',NULL),(332,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,134,'KG',NULL),(333,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,134,'KG',NULL),(334,100238,'Milk',18.67,18.67,0.00,0.00,135,'L',NULL),(335,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,136,'SACHET',NULL),(336,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,136,'PC',NULL),(337,100188,'Hot Cups 360 Ml',100.00,100.00,0.00,0.00,136,'PC',NULL),(338,100238,'Milk',35.69,35.69,0.00,0.00,137,'L',NULL),(339,100263,'Pav',100.00,100.00,0.00,0.00,137,'PC',NULL),(340,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,138,'SACHET',NULL),(341,100187,'Hot Cup 250 Ml',245.00,245.00,0.00,0.00,138,'PC',NULL),(342,100004,'250 Ml Sipper Lid',45.00,45.00,0.00,0.00,138,'PC',NULL),(343,100372,'Tray Mat',47.50,47.50,0.00,0.00,138,'PC',NULL),(344,100099,'Cold Cup Lid',50.00,50.00,0.00,0.00,138,'PC',NULL),(345,100100,'Cold Cups 500 Ml',50.00,50.00,0.00,0.00,138,'PC',NULL),(346,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,138,'PC',NULL),(347,100130,'Dip Cup',100.00,100.00,0.00,0.00,138,'PC',NULL),(348,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,139,'PC',NULL),(349,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,139,'KG',NULL),(350,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,139,'KG',NULL),(351,100238,'Milk',8.00,8.00,0.00,0.00,140,'L',NULL),(352,100305,'Sachet - Desi Regular',100.00,100.00,0.00,0.00,141,'SACHET',NULL),(353,100187,'Hot Cup 250 Ml',100.00,100.00,0.00,0.00,141,'PC',NULL),(354,100238,'Milk',35.69,35.69,0.00,0.00,142,'L',NULL),(355,100263,'Pav',100.00,100.00,0.00,0.00,142,'PC',NULL),(356,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,143,'SACHET',NULL),(357,100187,'Hot Cup 250 Ml',245.00,245.00,0.00,0.00,143,'PC',NULL),(358,100004,'250 Ml Sipper Lid',45.00,45.00,0.00,0.00,143,'PC',NULL),(359,100372,'Tray Mat',47.50,47.50,0.00,0.00,143,'PC',NULL),(360,100099,'Cold Cup Lid',50.00,50.00,0.00,0.00,143,'PC',NULL),(361,100100,'Cold Cups 500 Ml',50.00,50.00,0.00,0.00,143,'PC',NULL),(362,100061,'Butter Paper Veg',100.00,100.00,0.00,0.00,143,'PC',NULL),(363,100130,'Dip Cup',100.00,100.00,0.00,0.00,143,'PC',NULL),(364,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,144,'PC',NULL),(365,100180,'Hari Chuttney',3.00,3.00,0.00,0.00,144,'KG',NULL),(366,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,144,'KG',NULL),(367,100275,'Poha Dry',10.00,10.00,0.00,0.00,145,'KG',NULL),(368,100238,'Milk',31.24,31.24,0.00,0.00,146,'L',NULL),(369,100263,'Pav',50.00,50.00,0.00,0.00,146,'PC',NULL),(370,100305,'Sachet - Desi Regular',89.00,89.00,0.00,0.00,147,'SACHET',NULL),(371,100187,'Hot Cup 250 Ml',156.00,156.00,0.00,0.00,147,'PC',NULL),(372,100004,'250 Ml Sipper Lid',67.00,67.00,0.00,0.00,147,'PC',NULL),(373,100372,'Tray Mat',63.50,63.50,0.00,0.00,147,'PC',NULL),(374,100098,'Cold Cup 350 Ml',60.00,60.00,0.00,0.00,147,'PC',NULL),(375,100099,'Cold Cup Lid',60.00,60.00,0.00,0.00,147,'PC',NULL),(376,100061,'Butter Paper Veg',50.00,50.00,0.00,0.00,147,'PC',NULL),(377,100130,'Dip Cup',50.00,50.00,0.00,0.00,147,'PC',NULL),(378,100381,'Vada Pav Filling',50.00,50.00,0.00,0.00,148,'PC',NULL),(379,100180,'Hari Chuttney',1.50,1.50,0.00,0.00,148,'KG',NULL),(380,100209,'Lasoon Chutney',0.25,0.25,0.00,0.00,148,'KG',NULL),(381,100238,'Milk',15.34,15.34,0.00,0.00,149,'L',NULL),(382,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,150,'SACHET',NULL),(383,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,150,'PC',NULL),(384,100238,'Milk',1.74,1.74,0.00,0.00,151,'L',NULL),(385,100305,'Sachet - Desi Regular',17.00,17.00,0.00,0.00,152,'SACHET',NULL),(386,100187,'Hot Cup 250 Ml',9.00,9.00,0.00,0.00,152,'PC',NULL),(387,100188,'Hot Cups 360 Ml',4.00,4.00,0.00,0.00,152,'PC',NULL),(388,100186,'Hot Cup 150 Ml',24.00,24.00,0.00,0.00,152,'PC',NULL),(389,100350,'Chai delivery Box 400 Ml',6.00,6.00,0.00,0.00,152,'PC',NULL),(390,100354,'Chai delivery Pouch 400 Ml',6.00,6.00,0.00,0.00,152,'PC',NULL),(391,100334,'Stirrer',24.00,24.00,0.00,0.00,152,'PACKET',NULL),(392,100340,'Sugar Sachet - White',12.00,12.00,0.00,0.00,152,'SACHET',NULL),(393,100347,'Take Away Bag - Large',6.00,6.00,0.00,0.00,152,'PC',NULL),(394,100238,'Milk',16.00,16.00,0.00,0.00,153,'L',NULL),(395,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,154,'SACHET',NULL),(396,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,154,'PC',NULL),(397,100238,'Milk',16.00,16.00,0.00,0.00,155,'L',NULL),(398,100305,'Sachet - Desi Regular',200.00,200.00,0.00,0.00,156,'SACHET',NULL),(399,100187,'Hot Cup 250 Ml',200.00,200.00,0.00,0.00,156,'PC',NULL),(400,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(401,100294,'Red Chilli Mayo Sauce',117.20,117.20,0.00,0.00,157,'PC',NULL),(402,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,157,'KG',NULL),(403,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(404,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(405,100254,'Napoli Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(406,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(407,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(408,100147,'English Oven Bun',400.00,400.00,0.00,0.00,157,'PC',NULL),(409,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(410,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,157,'KG',NULL),(411,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(412,100258,'Onion',6.50,6.50,0.00,0.00,157,'KG',NULL),(413,100067,'Capsicum',6.00,6.00,0.00,0.00,157,'KG',NULL),(414,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(415,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(416,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(417,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(418,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,157,'KG',NULL),(419,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(420,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(421,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(422,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,157,'PC',NULL),(423,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,157,'KG',NULL),(424,100039,'Blueberry Cake Whole',12.50,12.50,0.00,0.00,157,'PC',NULL),(425,100250,'Multigrain Bread',200.00,200.00,0.00,0.00,158,'PC',NULL),(426,100114,'Croissant',200.00,200.00,0.00,0.00,158,'PC',NULL),(427,100155,'Focaccia',100.00,100.00,0.00,0.00,158,'PC',NULL),(428,100263,'Pav',200.00,200.00,0.00,0.00,158,'PC',NULL),(429,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,159,'PC',NULL),(430,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,159,'PACKET',NULL),(431,100130,'Dip Cup',900.00,900.00,0.00,0.00,159,'PC',NULL),(432,100372,'Tray Mat',1000.00,1000.00,0.00,0.00,159,'PC',NULL),(433,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,159,'PC',NULL),(434,100030,'Bhujia',10.00,10.00,0.00,0.00,159,'KG',NULL),(435,100081,'Chat Masala',0.40,0.40,0.00,0.00,159,'KG',NULL),(436,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,159,'SACHET',NULL),(437,100369,'Tomato',4.50,4.50,0.00,0.00,159,'KG',NULL),(438,100056,'Butter',2.00,2.00,0.00,0.00,159,'KG',NULL),(439,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,159,'PACKET',NULL),(440,100277,'Poha Plate',100.00,100.00,0.00,0.00,159,'PC',NULL),(441,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(442,100294,'Red Chilli Mayo Sauce',117.20,117.20,0.00,0.00,160,'PC',NULL),(443,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,160,'KG',NULL),(444,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(445,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(446,100254,'Napoli Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(447,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(448,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(449,100147,'English Oven Bun',400.00,400.00,0.00,0.00,160,'PC',NULL),(450,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(451,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,160,'KG',NULL),(452,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(453,100258,'Onion',6.50,6.50,0.00,0.00,160,'KG',NULL),(454,100067,'Capsicum',6.00,6.00,0.00,0.00,160,'KG',NULL),(455,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(456,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(457,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(458,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(459,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,160,'KG',NULL),(460,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(461,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(462,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(463,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,160,'PC',NULL),(464,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,160,'KG',NULL),(465,100039,'Blueberry Cake Whole',12.50,12.50,0.00,0.00,160,'PC',NULL),(466,100250,'Multigrain Bread',200.00,200.00,0.00,0.00,161,'PC',NULL),(467,100114,'Croissant',200.00,200.00,0.00,0.00,161,'PC',NULL),(468,100155,'Focaccia',100.00,100.00,0.00,0.00,161,'PC',NULL),(469,100263,'Pav',200.00,200.00,0.00,0.00,161,'PC',NULL),(470,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,162,'PC',NULL),(471,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,162,'PACKET',NULL),(472,100130,'Dip Cup',900.00,900.00,0.00,0.00,162,'PC',NULL),(473,100372,'Tray Mat',1000.00,1000.00,0.00,0.00,162,'PC',NULL),(474,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,162,'PC',NULL),(475,100030,'Bhujia',10.00,10.00,0.00,0.00,162,'KG',NULL),(476,100081,'Chat Masala',0.40,0.40,0.00,0.00,162,'KG',NULL),(477,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,162,'SACHET',NULL),(478,100369,'Tomato',4.50,4.50,0.00,0.00,162,'KG',NULL),(479,100056,'Butter',2.00,2.00,0.00,0.00,162,'KG',NULL),(480,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,162,'PACKET',NULL),(481,100277,'Poha Plate',100.00,100.00,0.00,0.00,162,'PC',NULL),(482,100004,'250 Ml Sipper Lid',12.00,12.00,0.00,0.00,163,'PC',NULL),(483,100305,'Sachet - Desi Regular',800.00,800.00,0.00,0.00,164,'SACHET',NULL),(484,100187,'Hot Cup 250 Ml',300.00,300.00,0.00,0.00,164,'PC',NULL),(485,100188,'Hot Cups 360 Ml',200.00,200.00,0.00,0.00,164,'PC',NULL),(486,100186,'Hot Cup 150 Ml',2800.00,2800.00,0.00,0.00,164,'PC',NULL),(487,100350,'Chai delivery Box 400 Ml',200.00,200.00,0.00,0.00,164,'PC',NULL),(488,100354,'Chai delivery Pouch 400 Ml',200.00,200.00,0.00,0.00,164,'PC',NULL),(489,100334,'Stirrer',2800.00,2800.00,0.00,0.00,164,'PACKET',NULL),(490,100340,'Sugar Sachet - White',1400.00,1400.00,0.00,0.00,164,'SACHET',NULL),(491,100347,'Take Away Bag - Large',400.00,400.00,0.00,0.00,164,'PC',NULL),(492,100077,'Chai delivery  Box 1 Ltr',200.00,200.00,0.00,0.00,164,'PC',NULL),(493,100353,'Chai delivery Pouch 1 L',200.00,200.00,0.00,0.00,164,'PC',NULL),(494,100004,'250 Ml Sipper Lid',100.00,100.00,0.00,0.00,164,'PC',NULL),(495,100372,'Tray Mat',1250.00,1250.00,0.00,0.00,164,'PC',NULL),(496,100125,'Desi Chai Patti',0.26,0.26,0.00,0.00,164,'KG',NULL),(497,100337,'Sugar',0.28,0.28,0.00,0.00,164,'KG',NULL),(498,100086,'Chocolate Syrup',4.00,4.00,0.00,0.00,164,'L',NULL),(499,100098,'Cold Cup 350 Ml',400.00,400.00,0.00,0.00,164,'PC',NULL),(500,100099,'Cold Cup Lid',500.00,500.00,0.00,0.00,164,'PC',NULL),(501,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,164,'PC',NULL),(502,100363,'Thandai',10.00,10.00,0.00,0.00,164,'L',NULL),(503,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,164,'PC',NULL),(504,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,164,'PACKET',NULL),(505,100130,'Dip Cup',900.00,900.00,0.00,0.00,164,'PC',NULL),(506,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,164,'PC',NULL),(507,100030,'Bhujia',10.00,10.00,0.00,0.00,164,'KG',NULL),(508,100081,'Chat Masala',0.40,0.40,0.00,0.00,164,'KG',NULL),(509,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,164,'SACHET',NULL),(510,100369,'Tomato',4.50,4.50,0.00,0.00,164,'KG',NULL),(511,100056,'Butter',2.00,2.00,0.00,0.00,164,'KG',NULL),(512,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,164,'PACKET',NULL),(513,100277,'Poha Plate',100.00,100.00,0.00,0.00,164,'PC',NULL),(514,100210,'Lemon',0.60,0.60,0.00,0.00,165,'KG',NULL),(515,100078,'Chai Masala',0.20,0.20,0.00,0.00,165,'KG',NULL),(516,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(517,100294,'Red Chilli Mayo Sauce',118.00,118.00,0.00,0.00,165,'PC',NULL),(518,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,165,'KG',NULL),(519,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(520,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(521,100254,'Napoli Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(522,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(523,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(524,100147,'English Oven Bun',400.00,400.00,0.00,0.00,165,'PC',NULL),(525,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(526,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,165,'KG',NULL),(527,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(528,100258,'Onion',6.50,6.50,0.00,0.00,165,'KG',NULL),(529,100067,'Capsicum',6.00,6.00,0.00,0.00,165,'KG',NULL),(530,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(531,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(532,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(533,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(534,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,165,'KG',NULL),(535,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(536,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(537,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(538,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,165,'PC',NULL),(539,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,165,'KG',NULL),(540,100039,'Blueberry Cake Whole',13.00,13.00,0.00,0.00,165,'PC',NULL),(541,100305,'Sachet - Desi Regular',800.00,800.00,0.00,0.00,166,'SACHET',NULL),(542,100187,'Hot Cup 250 Ml',300.00,300.00,0.00,0.00,166,'PC',NULL),(543,100188,'Hot Cups 360 Ml',200.00,200.00,0.00,0.00,166,'PC',NULL),(544,100186,'Hot Cup 150 Ml',2800.00,2800.00,0.00,0.00,166,'PC',NULL),(545,100350,'Chai delivery Box 400 Ml',200.00,200.00,0.00,0.00,166,'PC',NULL),(546,100354,'Chai delivery Pouch 400 Ml',200.00,200.00,0.00,0.00,166,'PC',NULL),(547,100334,'Stirrer',2800.00,2800.00,0.00,0.00,166,'PACKET',NULL),(548,100340,'Sugar Sachet - White',1400.00,1400.00,0.00,0.00,166,'SACHET',NULL),(549,100347,'Take Away Bag - Large',400.00,400.00,0.00,0.00,166,'PC',NULL),(550,100077,'Chai delivery  Box 1 Ltr',200.00,200.00,0.00,0.00,166,'PC',NULL),(551,100353,'Chai delivery Pouch 1 L',200.00,200.00,0.00,0.00,166,'PC',NULL),(552,100004,'250 Ml Sipper Lid',100.00,100.00,0.00,0.00,166,'PC',NULL),(553,100372,'Tray Mat',1250.00,1250.00,0.00,0.00,166,'PC',NULL),(554,100125,'Desi Chai Patti',0.26,0.26,0.00,0.00,166,'KG',NULL),(555,100337,'Sugar',0.28,0.28,0.00,0.00,166,'KG',NULL),(556,100086,'Chocolate Syrup',4.00,4.00,0.00,0.00,166,'L',NULL),(557,100098,'Cold Cup 350 Ml',400.00,400.00,0.00,0.00,166,'PC',NULL),(558,100099,'Cold Cup Lid',500.00,500.00,0.00,0.00,166,'PC',NULL),(559,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,166,'PC',NULL),(560,100363,'Thandai',10.00,10.00,0.00,0.00,166,'L',NULL),(561,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,166,'PC',NULL),(562,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,166,'PACKET',NULL),(563,100130,'Dip Cup',900.00,900.00,0.00,0.00,166,'PC',NULL),(564,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,166,'PC',NULL),(565,100030,'Bhujia',10.00,10.00,0.00,0.00,166,'KG',NULL),(566,100081,'Chat Masala',0.40,0.40,0.00,0.00,166,'KG',NULL),(567,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,166,'SACHET',NULL),(568,100369,'Tomato',4.50,4.50,0.00,0.00,166,'KG',NULL),(569,100056,'Butter',2.00,2.00,0.00,0.00,166,'KG',NULL),(570,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,166,'PACKET',NULL),(571,100277,'Poha Plate',100.00,100.00,0.00,0.00,166,'PC',NULL),(572,100210,'Lemon',0.60,0.60,0.00,0.00,167,'KG',NULL),(573,100078,'Chai Masala',0.20,0.20,0.00,0.00,167,'KG',NULL),(574,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(575,100294,'Red Chilli Mayo Sauce',118.00,118.00,0.00,0.00,167,'PC',NULL),(576,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,167,'KG',NULL),(577,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(578,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(579,100254,'Napoli Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(580,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(581,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(582,100147,'English Oven Bun',400.00,400.00,0.00,0.00,167,'PC',NULL),(583,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(584,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,167,'KG',NULL),(585,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(586,100258,'Onion',6.50,6.50,0.00,0.00,167,'KG',NULL),(587,100067,'Capsicum',6.00,6.00,0.00,0.00,167,'KG',NULL),(588,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(589,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(590,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(591,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(592,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,167,'KG',NULL),(593,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(594,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(595,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(596,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,167,'PC',NULL),(597,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,167,'KG',NULL),(598,100039,'Blueberry Cake Whole',13.00,13.00,0.00,0.00,167,'PC',NULL),(599,100305,'Sachet - Desi Regular',800.00,800.00,0.00,0.00,168,'SACHET',NULL),(600,100187,'Hot Cup 250 Ml',300.00,300.00,0.00,0.00,168,'PC',NULL),(601,100188,'Hot Cups 360 Ml',200.00,200.00,0.00,0.00,168,'PC',NULL),(602,100186,'Hot Cup 150 Ml',2800.00,2800.00,0.00,0.00,168,'PC',NULL),(603,100350,'Chai delivery Box 400 Ml',200.00,200.00,0.00,0.00,168,'PC',NULL),(604,100354,'Chai delivery Pouch 400 Ml',200.00,200.00,0.00,0.00,168,'PC',NULL),(605,100334,'Stirrer',2800.00,2800.00,0.00,0.00,168,'PACKET',NULL),(606,100340,'Sugar Sachet - White',1400.00,1400.00,0.00,0.00,168,'SACHET',NULL),(607,100347,'Take Away Bag - Large',400.00,400.00,0.00,0.00,168,'PC',NULL),(608,100077,'Chai delivery  Box 1 Ltr',200.00,200.00,0.00,0.00,168,'PC',NULL),(609,100353,'Chai delivery Pouch 1 L',200.00,200.00,0.00,0.00,168,'PC',NULL),(610,100004,'250 Ml Sipper Lid',100.00,100.00,0.00,0.00,168,'PC',NULL),(611,100372,'Tray Mat',1250.00,1250.00,0.00,0.00,168,'PC',NULL),(612,100125,'Desi Chai Patti',0.26,0.26,0.00,0.00,168,'KG',NULL),(613,100337,'Sugar',0.28,0.28,0.00,0.00,168,'KG',NULL),(614,100086,'Chocolate Syrup',4.00,4.00,0.00,0.00,168,'L',NULL),(615,100098,'Cold Cup 350 Ml',400.00,400.00,0.00,0.00,168,'PC',NULL),(616,100099,'Cold Cup Lid',500.00,500.00,0.00,0.00,168,'PC',NULL),(617,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,168,'PC',NULL),(618,100363,'Thandai',10.00,10.00,0.00,0.00,168,'L',NULL),(619,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,168,'PC',NULL),(620,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,168,'PACKET',NULL),(621,100130,'Dip Cup',900.00,900.00,0.00,0.00,168,'PC',NULL),(622,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,168,'PC',NULL),(623,100030,'Bhujia',10.00,10.00,0.00,0.00,168,'KG',NULL),(624,100081,'Chat Masala',0.40,0.40,0.00,0.00,168,'KG',NULL),(625,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,168,'SACHET',NULL),(626,100369,'Tomato',4.50,4.50,0.00,0.00,168,'KG',NULL),(627,100056,'Butter',2.00,2.00,0.00,0.00,168,'KG',NULL),(628,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,168,'PACKET',NULL),(629,100277,'Poha Plate',100.00,100.00,0.00,0.00,168,'PC',NULL),(630,100210,'Lemon',0.60,0.60,0.00,0.00,169,'KG',NULL),(631,100078,'Chai Masala',0.20,0.20,0.00,0.00,169,'KG',NULL),(632,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(633,100294,'Red Chilli Mayo Sauce',118.00,118.00,0.00,0.00,169,'PC',NULL),(634,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,169,'KG',NULL),(635,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(636,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(637,100254,'Napoli Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(638,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(639,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(640,100147,'English Oven Bun',400.00,400.00,0.00,0.00,169,'PC',NULL),(641,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(642,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,169,'KG',NULL),(643,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(644,100258,'Onion',6.50,6.50,0.00,0.00,169,'KG',NULL),(645,100067,'Capsicum',6.00,6.00,0.00,0.00,169,'KG',NULL),(646,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(647,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(648,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(649,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(650,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,169,'KG',NULL),(651,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(652,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(653,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(654,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,169,'PC',NULL),(655,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,169,'KG',NULL),(656,100039,'Blueberry Cake Whole',13.00,13.00,0.00,0.00,169,'PC',NULL),(657,100305,'Sachet - Desi Regular',800.00,800.00,0.00,0.00,170,'SACHET',NULL),(658,100187,'Hot Cup 250 Ml',300.00,300.00,0.00,0.00,170,'PC',NULL),(659,100188,'Hot Cups 360 Ml',200.00,200.00,0.00,0.00,170,'PC',NULL),(660,100186,'Hot Cup 150 Ml',2800.00,2800.00,0.00,0.00,170,'PC',NULL),(661,100350,'Chai delivery Box 400 Ml',200.00,200.00,0.00,0.00,170,'PC',NULL),(662,100354,'Chai delivery Pouch 400 Ml',200.00,200.00,0.00,0.00,170,'PC',NULL),(663,100334,'Stirrer',2800.00,2800.00,0.00,0.00,170,'PACKET',NULL),(664,100340,'Sugar Sachet - White',1400.00,1400.00,0.00,0.00,170,'SACHET',NULL),(665,100347,'Take Away Bag - Large',400.00,400.00,0.00,0.00,170,'PC',NULL),(666,100077,'Chai delivery  Box 1 Ltr',200.00,200.00,0.00,0.00,170,'PC',NULL),(667,100353,'Chai delivery Pouch 1 L',200.00,200.00,0.00,0.00,170,'PC',NULL),(668,100004,'250 Ml Sipper Lid',100.00,100.00,0.00,0.00,170,'PC',NULL),(669,100372,'Tray Mat',1250.00,1250.00,0.00,0.00,170,'PC',NULL),(670,100125,'Desi Chai Patti',0.26,0.26,0.00,0.00,170,'KG',NULL),(671,100337,'Sugar',0.28,0.28,0.00,0.00,170,'KG',NULL),(672,100086,'Chocolate Syrup',4.00,4.00,0.00,0.00,170,'L',NULL),(673,100098,'Cold Cup 350 Ml',400.00,400.00,0.00,0.00,170,'PC',NULL),(674,100099,'Cold Cup Lid',500.00,500.00,0.00,0.00,170,'PC',NULL),(675,100100,'Cold Cups 500 Ml',100.00,100.00,0.00,0.00,170,'PC',NULL),(676,100363,'Thandai',10.00,10.00,0.00,0.00,170,'L',NULL),(677,100060,'Butter Paper Non-Veg',600.00,600.00,0.00,0.00,170,'PC',NULL),(678,100366,'Tissue Paper',1502.00,1502.00,0.00,0.00,170,'PACKET',NULL),(679,100130,'Dip Cup',900.00,900.00,0.00,0.00,170,'PC',NULL),(680,100061,'Butter Paper Veg',1200.00,1200.00,0.00,0.00,170,'PC',NULL),(681,100030,'Bhujia',10.00,10.00,0.00,0.00,170,'KG',NULL),(682,100081,'Chat Masala',0.40,0.40,0.00,0.00,170,'KG',NULL),(683,100201,'Ketchup Sachet',100.00,100.00,0.00,0.00,170,'SACHET',NULL),(684,100369,'Tomato',4.50,4.50,0.00,0.00,170,'KG',NULL),(685,100056,'Butter',2.00,2.00,0.00,0.00,170,'KG',NULL),(686,100271,'Plastic Spoon',2.00,2.00,0.00,0.00,170,'PACKET',NULL),(687,100277,'Poha Plate',100.00,100.00,0.00,0.00,170,'PC',NULL),(688,100210,'Lemon',0.60,0.60,0.00,0.00,171,'KG',NULL),(689,100078,'Chai Masala',0.20,0.20,0.00,0.00,171,'KG',NULL),(690,100313,'Sicilian Chicken Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(691,100294,'Red Chilli Mayo Sauce',118.00,118.00,0.00,0.00,171,'PC',NULL),(692,100184,'Honey Garlic Mayo Sauce',8.00,8.00,0.00,0.00,171,'KG',NULL),(693,100170,'Green Chicken Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(694,100266,'Pepper Chicken Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(695,100254,'Napoli Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(696,100001,'1 Sink Unit',1.00,1.00,0.00,0.00,172,'PC',NULL),(697,100320,'Spinach Corn Cheese Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(698,100026,'Balsamico Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(699,100147,'English Oven Bun',400.00,400.00,0.00,0.00,171,'PC',NULL),(700,100084,'Chocolate Bun Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(701,100172,'Green Chilli Mint Mayo Sauce',21.20,21.20,0.00,0.00,171,'KG',NULL),(702,100058,'Butter Chicken Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(703,100258,'Onion',6.50,6.50,0.00,0.00,171,'KG',NULL),(704,100067,'Capsicum',6.00,6.00,0.00,0.00,171,'KG',NULL),(705,100253,'Mutton Lazeez Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(706,100196,'Kadhai Paneer Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(707,100082,'Chatpata Kebab Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(708,100002,'2 Minutes Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(709,100180,'Hari Chuttney',7.00,7.00,0.00,0.00,171,'KG',NULL),(710,100182,'Homestyle Aloo Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(711,100199,'Keema Pav Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(712,100144,'Egg Bun Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(713,100381,'Vada Pav Filling',100.00,100.00,0.00,0.00,171,'PC',NULL),(714,100209,'Lasoon Chutney',0.50,0.50,0.00,0.00,171,'KG',NULL),(715,100039,'Blueberry Cake Whole',13.00,13.00,0.00,0.00,171,'PC',NULL),(716,100001,'1 Sink Unit',10.00,10.00,0.00,0.00,173,'PC',NULL);
/*!40000 ALTER TABLE `REQUEST_ORDER_ITEM` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RO_MENU_ITEM_VARIANT`
--

DROP TABLE IF EXISTS `RO_MENU_ITEM_VARIANT`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `RO_MENU_ITEM_VARIANT` (
  `RO_MENU_VARIANT_ID` int(11) NOT NULL AUTO_INCREMENT,
  `VARIANT_NAME` varchar(255) NOT NULL,
  `VARIANT_CONVERSION_QUANTITY` decimal(10,2) NOT NULL,
  `VARIANT_ORDERED_QUANTITY` decimal(10,2) NOT NULL,
  `MENU_ITEM_ID` int(11) NOT NULL,
  PRIMARY KEY (`RO_MENU_VARIANT_ID`),
  KEY `MENU_ITEM_ID` (`MENU_ITEM_ID`),
  CONSTRAINT `RO_MENU_ITEM_VARIANT_ibfk_1` FOREIGN KEY (`MENU_ITEM_ID`) REFERENCES `REFERENCE_ORDER_MENU_ITEM` (`MENU_ITEM_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RO_MENU_ITEM_VARIANT`
--

LOCK TABLES `RO_MENU_ITEM_VARIANT` WRITE;
/*!40000 ALTER TABLE `RO_MENU_ITEM_VARIANT` DISABLE KEYS */;
/*!40000 ALTER TABLE `RO_MENU_ITEM_VARIANT` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SCM_ORDER_PACKAGING`
--

DROP TABLE IF EXISTS `SCM_ORDER_PACKAGING`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SCM_ORDER_PACKAGING` (
  `SCM_ORDER_PACKAGING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PACKAGING_ID` int(11) NOT NULL,
  `NUMBER_OF_UNITS_PACKED` int(11) NOT NULL,
  `NUMBER_OF_UNITS_RECEIVED` int(11) DEFAULT NULL,
  `TRANSFERRED_QUANTITY` decimal(10,2) NOT NULL,
  `RECEIVED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `GOODS_RECEIVED_ITEM_ID` int(11) DEFAULT NULL,
  `TRANSFER_ORDER_ITEM_ID` int(11) NOT NULL,
  PRIMARY KEY (`SCM_ORDER_PACKAGING_ID`),
  KEY `PACKAGING_ID` (`PACKAGING_ID`),
  KEY `TRANSFER_ORDER_ITEM_ID` (`TRANSFER_ORDER_ITEM_ID`),
  KEY `SCM_ORDER_PACKAGING_ibfk_3` (`GOODS_RECEIVED_ITEM_ID`),
  CONSTRAINT `SCM_ORDER_PACKAGING_ibfk_3` FOREIGN KEY (`GOODS_RECEIVED_ITEM_ID`) REFERENCES `GOODS_RECEIVED_ITEM` (`GOODS_RECEIVED_ITEM_ID`),
  CONSTRAINT `SCM_ORDER_PACKAGING_ibfk_1` FOREIGN KEY (`PACKAGING_ID`) REFERENCES `PACKAGING_DEFINITION` (`PACKAGING_ID`),
  CONSTRAINT `SCM_ORDER_PACKAGING_ibfk_2` FOREIGN KEY (`TRANSFER_ORDER_ITEM_ID`) REFERENCES `TRANSFER_ORDER_ITEM` (`TRANSFER_ORDER_ITEM_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SCM_ORDER_PACKAGING`
--

LOCK TABLES `SCM_ORDER_PACKAGING` WRITE;
/*!40000 ALTER TABLE `SCM_ORDER_PACKAGING` DISABLE KEYS */;
INSERT INTO `SCM_ORDER_PACKAGING` VALUES (19,102,3,3,1.50,1.50,18,18),(20,3,20,20,20.00,20.00,19,19),(21,28,1,1,1000.00,1000.00,20,20),(22,1,1,1,1.00,1.00,21,21),(23,102,20,NULL,10.00,0.00,22,22),(24,102,5,NULL,2.50,0.00,23,23),(25,3,1,1,1.00,1.00,24,24),(26,3,1,NULL,1.00,0.00,25,25),(27,1,12,12,12.00,12.00,26,26),(28,3,10,10,10.00,10.00,27,27);
/*!40000 ALTER TABLE `SCM_ORDER_PACKAGING` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SKU_ATTRIBUTE_VALUE`
--

DROP TABLE IF EXISTS `SKU_ATTRIBUTE_VALUE`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SKU_ATTRIBUTE_VALUE` (
  `SKU_ATTRIBUTE_VALUE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SKU_ID` int(11) NOT NULL,
  `ATTRIBUTE_ID` int(11) NOT NULL,
  `ATTRIBUTE_VALUE_ID` int(11) NOT NULL,
  `MAPPING_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`SKU_ATTRIBUTE_VALUE_ID`),
  UNIQUE KEY `SKU_ID` (`SKU_ID`,`ATTRIBUTE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SKU_ATTRIBUTE_VALUE`
--

LOCK TABLES `SKU_ATTRIBUTE_VALUE` WRITE;
/*!40000 ALTER TABLE `SKU_ATTRIBUTE_VALUE` DISABLE KEYS */;
/*!40000 ALTER TABLE `SKU_ATTRIBUTE_VALUE` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SKU_DEFINITION`
--

DROP TABLE IF EXISTS `SKU_DEFINITION`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SKU_DEFINITION` (
  `SKU_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SKU_NAME` varchar(255) NOT NULL,
  `SKU_DESCRIPTION` varchar(1000) DEFAULT NULL,
  `SUPPORTS_LOOSE_ORDERING` varchar(1) NOT NULL,
  `CREATION_DATE` timestamp NULL DEFAULT NULL,
  `CREATED_BY` int(11) NOT NULL,
  `HAS_INNER` varchar(1) NOT NULL,
  `HAS_CASE` varchar(1) NOT NULL,
  `LINKED_PRODUCT_ID` int(11) NOT NULL,
  `SHELF_LIFE_IN_DAYS` int(11) NOT NULL,
  `SKU_STATUS` varchar(15) NOT NULL,
  `UNIT_OF_MEASURE` varchar(15) NOT NULL,
  `SKU_IMAGE` varchar(255) DEFAULT NULL,
  `UNIT_PRICE` decimal(10,2) DEFAULT NULL,
  `NEGOTIATED_UNIT_PRICE` decimal(10,2) DEFAULT NULL,
  `PRICE_LAST_UPDATED` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `TORQUS_ID` varchar(300) DEFAULT NULL,
  PRIMARY KEY (`SKU_ID`),
  UNIQUE KEY `SKU_NAME` (`SKU_NAME`),
  KEY `PRODUCT_DEFINITION_LINKED_PRODUCT_ID` (`LINKED_PRODUCT_ID`),
  CONSTRAINT `SKU_DEFINITION_ibfk_1` FOREIGN KEY (`LINKED_PRODUCT_ID`) REFERENCES `PRODUCT_DEFINITION` (`PRODUCT_ID`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=520 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SKU_DEFINITION`
--

LOCK TABLES `SKU_DEFINITION` WRITE;
/*!40000 ALTER TABLE `SKU_DEFINITION` DISABLE KEYS */;
INSERT INTO `SKU_DEFINITION` VALUES (1,'1 Sink Unit','1 Sink Unit','Y','2016-06-27 00:00:00',100000,'N','N',100001,1,'ACTIVE','PC','',12000.00,12000.00,'2016-06-27 00:00:00',''),(2,'2 Minutes Filling Chaayos','2 Minutes Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100002,1,'ACTIVE','PC','',6.03,6.03,'2016-06-28 00:00:00',''),(3,'2 Sink Unit','2 Sink Unit','Y','2016-06-27 00:00:00',100000,'N','N',100003,1,'ACTIVE','PC','',12500.00,12500.00,'2016-06-29 00:00:00',''),(4,'250 Ml Sipper Lid','250 Ml Sipper Lid','Y','2016-06-27 00:00:00',100000,'Y','Y',100004,1,'ACTIVE','PC','',0.91,0.91,'2016-06-30 00:00:00',''),(5,'3 Sink Unit','3 Sink Unit','Y','2016-06-27 00:00:00',100000,'N','N',100005,1,'ACTIVE','PC','',15000.00,15000.00,'2016-07-01 00:00:00',''),(6,'360 Ml Sipper Lid','360 Ml Sipper Lid','Y','2016-06-27 00:00:00',100000,'Y','Y',100006,1,'ACTIVE','PC','',0.91,0.91,'2016-07-02 00:00:00',''),(7,'A4 Paper Khanna','A4 Paper Khanna','Y','2016-06-27 00:00:00',100000,'Y','Y',100007,1,'ACTIVE','PC','',128.00,128.00,'2016-07-03 00:00:00',''),(8,'Aam Papad Rasella','Aam Papad Rasella','Y','2016-06-27 00:00:00',100000,'Y','Y',100008,1,'ACTIVE','KG','',36.05,36.05,'2016-07-04 00:00:00',''),(9,'Ac Daikin','Ac Daikin','Y','2016-06-27 00:00:00',100000,'N','N',100009,1,'ACTIVE','PC','',37200.00,37200.00,'2016-07-05 00:00:00',''),(10,'Achari Butter Chaayos','Achari Butter Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100010,1,'ACTIVE','KG','',1.00,1.00,'2016-07-06 00:00:00',''),(11,'Achari Chicken Samosa Chaayos','Achari Chicken Samosa Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100011,1,'ACTIVE','PC','',84.00,84.00,'2016-07-07 00:00:00',''),(12,'Acrylic Milk Bottle Snail','Acrylic Milk Bottle Snail','Y','2016-06-27 00:00:00',100000,'Y','Y',100012,1,'ACTIVE','PC','',495.00,495.00,'2016-07-08 00:00:00',''),(13,'Acrylic Water Glass Canford','Acrylic Water Glass Canford','Y','2016-06-27 00:00:00',100000,'Y','Y',100013,1,'ACTIVE','PC','',25.00,25.00,'2016-07-09 00:00:00',''),(14,'Add On Stand - 4','Add On Stand - 4','Y','2016-06-27 00:00:00',100000,'Y','Y',100014,1,'ACTIVE','PC','',1800.00,1800.00,'2016-07-10 00:00:00',''),(15,'Add On Stand - 8','Add On Stand - 8','Y','2016-06-27 00:00:00',100000,'Y','Y',100015,1,'ACTIVE','PC','',2400.00,2400.00,'2016-07-11 00:00:00',''),(16,'Add On Box Sunpat','Add Ons Box Sunpat','Y','2016-06-27 00:00:00',100000,'Y','Y',100016,1,'ACTIVE','PC','',50.00,50.00,'2016-07-12 00:00:00',''),(17,'Ajwain','Ajwain','Y','2016-06-27 00:00:00',100000,'Y','Y',100017,1,'ACTIVE','KG','',180.00,180.00,'2016-07-13 00:00:00',''),(18,'Badam','Badam','Y','2016-06-27 00:00:00',100000,'Y','Y',100019,1,'ACTIVE','KG','',790.00,790.00,'2016-07-14 00:00:00',''),(19,'Bain Marie','Bain Marie','Y','2016-06-27 00:00:00',100000,'N','N',100020,1,'ACTIVE','PC','',78750.00,78750.00,'2016-07-15 00:00:00',''),(20,'Bain Marie Basket','Bain Marie Basket','Y','2016-06-27 00:00:00',100000,'Y','Y',100021,1,'ACTIVE','PC','',3500.00,3500.00,'2016-07-16 00:00:00',''),(21,'Bain Marie Display Stand','Bain Marie Display Stand','Y','2016-06-27 00:00:00',100000,'Y','Y',100022,1,'ACTIVE','PC','',10.00,10.00,'2016-07-17 00:00:00',''),(22,'Baking Powder Weikfiled','Baking Powder Weikfiled','Y','2016-06-27 00:00:00',100000,'Y','Y',100023,1,'ACTIVE','KG','',154.00,154.00,'2016-07-18 00:00:00',''),(23,'Ball Pen Natraj','Ball Pen Natraj','Y','2016-06-27 00:00:00',100000,'Y','Y',100024,1,'ACTIVE','PC','',1.90,1.90,'2016-07-19 00:00:00',''),(24,'Balsamico Filling Chaayos','Balsamico Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100025,1,'ACTIVE','PC','',14.77,14.77,'2016-07-20 00:00:00',''),(25,'Banana Cake Whole','Banana Cake Whole','Y','2016-06-27 00:00:00',100000,'Y','Y',100026,1,'ACTIVE','PC','',306.36,306.36,'2016-07-21 00:00:00',''),(26,'Banana Filling Chaayos','Banana Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100027,1,'ACTIVE','PC','',7.50,7.50,'2016-07-22 00:00:00',''),(27,'Banking Envelope','Banking Envelope','Y','2016-06-27 00:00:00',100000,'Y','Y',100028,1,'ACTIVE','PC','',3.50,3.50,'2016-07-23 00:00:00',''),(28,'Bhujia Haldiram','Bhujia Haldiram','Y','2016-06-27 00:00:00',100000,'Y','Y',100029,1,'ACTIVE','KG','',155.56,155.56,'2016-07-24 00:00:00',''),(29,'Bin Liner 36*55 - Large','Bin Liner 36*55 - Large','Y','2016-06-27 00:00:00',100000,'Y','Y',100030,1,'ACTIVE','PC','',90.00,90.00,'2016-07-25 00:00:00',''),(30,'Bin Liner Medium','Bin Liner Medium','Y','2016-06-27 00:00:00',100000,'Y','Y',100031,1,'ACTIVE','KG','',90.00,90.00,'2016-07-26 00:00:00',''),(31,'Bin Liner Small','Bin Liner Small','Y','2016-06-27 00:00:00',100000,'Y','Y',100032,1,'ACTIVE','KG','',90.00,90.00,'2016-07-27 00:00:00',''),(32,'Black Board','Black Board','Y','2016-06-27 00:00:00',100000,'Y','Y',100033,1,'ACTIVE','PC','',550.00,550.00,'2016-07-28 00:00:00',''),(33,'Black Grape Filling Chaayos','Black Grape Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100034,1,'ACTIVE','PC','',23.61,23.61,'2016-07-29 00:00:00',''),(34,'Black Pepper','Black Pepper','Y','2016-06-27 00:00:00',100000,'Y','Y',100035,1,'ACTIVE','KG','',790.00,790.00,'2016-07-30 00:00:00',''),(35,'Black Salt','Black Salt','Y','2016-06-27 00:00:00',100000,'Y','Y',100036,1,'ACTIVE','KG','',18.00,18.00,'2016-07-31 00:00:00',''),(36,'Blackboard Stand','Blackboard Stand','Y','2016-06-27 00:00:00',100000,'Y','Y',100037,1,'ACTIVE','PC','',750.00,750.00,'2016-08-01 00:00:00',''),(37,'Blueberry Cake Whole','Blueberry Cake Whole','Y','2016-06-27 00:00:00',100000,'Y','Y',100038,1,'ACTIVE','PC','',357.42,357.42,'2016-08-02 00:00:00',''),(38,'Boiler Element Pradeep','Boiler Element Pradeep','Y','2016-06-27 00:00:00',100000,'Y','Y',100039,1,'ACTIVE','PC','',1850.00,1850.00,'2016-08-03 00:00:00',''),(39,'Bolt Pronto Oven Bolt','Bolt Pronto Oven Bolt','Y','2016-06-27 00:00:00',100000,'N','N',100040,1,'ACTIVE','PC','',130000.00,130000.00,'2016-08-04 00:00:00',''),(40,'Bolt Tray Bolt','Bolt Tray Bolt','Y','2016-06-27 00:00:00',100000,'Y','Y',100041,1,'ACTIVE','PC','',1.00,1.00,'2016-08-05 00:00:00',''),(41,'Bottle Cleaning Brush','Bottle Cleaning Brush','Y','2016-06-27 00:00:00',100000,'Y','Y',100042,1,'ACTIVE','PC','',20.00,20.00,'2016-08-06 00:00:00',''),(42,'Bottle Stand - Cold Station','Bottle Stand - Cold Station','Y','2016-06-27 00:00:00',100000,'Y','Y',100043,1,'ACTIVE','PC','',1800.00,1800.00,'2016-08-07 00:00:00',''),(43,'Bournvita Cadburry','Bournvita Cadburry','Y','2016-06-27 00:00:00',100000,'Y','Y',100044,1,'ACTIVE','KG','',380.00,380.00,'2016-08-08 00:00:00',''),(44,'Box File Shipra','Box File Shipra','Y','2016-06-27 00:00:00',100000,'Y','Y',100045,1,'ACTIVE','PC','',135.00,135.00,'2016-08-09 00:00:00',''),(45,'Bread Box','Bread Box','Y','2016-06-27 00:00:00',100000,'Y','Y',100046,1,'ACTIVE','PC','',386.00,386.00,'2016-08-10 00:00:00',''),(46,'Bread Knife Glare','Bread Knife Glare','Y','2016-06-27 00:00:00',100000,'Y','Y',100047,1,'ACTIVE','PC','',130.00,130.00,'2016-08-11 00:00:00',''),(47,'Broom Hard','Broom Hard','Y','2016-06-27 00:00:00',100000,'Y','Y',100048,1,'ACTIVE','PC','',20.00,20.00,'2016-08-12 00:00:00',''),(48,'Brown Tape','Brown Tape','Y','2016-06-27 00:00:00',100000,'Y','Y',100049,1,'ACTIVE','PC','',26.00,26.00,'2016-08-13 00:00:00',''),(49,'Bubble Wrap Sheet','Bubble Wrap Sheet','Y','2016-06-27 00:00:00',100000,'Y','Y',100050,1,'ACTIVE','PC','',800.00,800.00,'2016-08-14 00:00:00',''),(50,'Built Too : Cold Station Setup','Built Too : Cold Station Setup','Y','2016-06-27 00:00:00',100000,'Y','Y',100051,1,'ACTIVE','PC','',10.00,10.00,'2016-08-15 00:00:00',''),(51,'Built Too : Food Station Setup','Built Too : Food Station Setup','Y','2016-06-27 00:00:00',100000,'Y','Y',100052,1,'ACTIVE','PC','',10.00,10.00,'2016-08-16 00:00:00',''),(52,'Built Too : Service Area','Built Too : Service Area','Y','2016-06-27 00:00:00',100000,'Y','Y',100053,1,'ACTIVE','PC','',10.00,10.00,'2016-08-17 00:00:00',''),(53,'Built Too: Hot Station Setup','Built Too: Hot Station Setup','Y','2016-06-27 00:00:00',100000,'Y','Y',100054,1,'ACTIVE','PC','',10.00,10.00,'2016-08-18 00:00:00',''),(54,'Butter Amul','Butter Amul','Y','2016-06-27 00:00:00',100000,'Y','Y',100055,1,'ACTIVE','KG','',302.22,302.22,'2016-08-19 00:00:00',''),(55,'Butter Bowl Fable','Butter Bowl Fable','Y','2016-06-27 00:00:00',100000,'Y','Y',100056,1,'ACTIVE','PC','',1.00,1.00,'2016-08-20 00:00:00',''),(56,'Butter Chicken Filling Chaayos','Butter Chicken Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100057,1,'ACTIVE','PC','',25.76,25.76,'2016-08-21 00:00:00',''),(57,'Butter Knife Sujata','Butter Knife Sujata','Y','2016-06-27 00:00:00',100000,'Y','Y',100058,1,'ACTIVE','PC','',28.00,28.00,'2016-08-22 00:00:00',''),(58,'Butter Paper Non-Veg','Butter Paper Non-Veg','Y','2016-06-27 00:00:00',100000,'Y','Y',100059,1,'ACTIVE','PC','',0.40,0.40,'2016-08-23 00:00:00',''),(59,'Butter Paper Veg','Butter Paper Veg','Y','2016-06-27 00:00:00',100000,'Y','Y',100060,1,'ACTIVE','PC','',0.40,0.40,'2016-08-24 00:00:00',''),(60,'C Fold Dispenser','C Fold Dispenser','Y','2016-06-27 00:00:00',100000,'Y','Y',100061,1,'ACTIVE','PC','',150.00,150.00,'2016-08-25 00:00:00',''),(61,'C Fold Tissue','C/Fold Tissue','Y','2016-06-27 00:00:00',100000,'Y','Y',100062,1,'ACTIVE','PC','',45.00,45.00,'2016-08-26 00:00:00',''),(62,'Cake Brush','Cake Brush','Y','2016-06-27 00:00:00',100000,'Y','Y',100063,1,'ACTIVE','PC','',90.00,90.00,'2016-08-27 00:00:00',''),(63,'Calculator Chrome','Calculator Chrome','Y','2016-06-27 00:00:00',100000,'Y','Y',100064,1,'ACTIVE','PC','',320.00,320.00,'2016-08-28 00:00:00',''),(64,'Candle','Candle','Y','2016-06-27 00:00:00',100000,'Y','Y',100065,1,'ACTIVE','PC','',140.00,140.00,'2016-08-29 00:00:00',''),(65,'Capsicum Julienne','Capsicum','Y','2016-06-27 00:00:00',100000,'Y','Y',100066,1,'ACTIVE','KG','',115.35,115.35,'2016-08-30 00:00:00',''),(66,'Carbon Paper Royal','Carbon Paper Royal','Y','2016-06-27 00:00:00',100000,'Y','Y',100067,1,'ACTIVE','PC','',135.00,135.00,'2016-08-31 00:00:00',''),(67,'Cardamom Black','Cardamom Black','Y','2016-06-27 00:00:00',100000,'Y','Y',100068,1,'ACTIVE','KG','',1700.00,1700.00,'2016-09-01 00:00:00',''),(68,'Cardamom Green Catch','Cardamom Green Catch','Y','2016-06-27 00:00:00',100000,'Y','Y',100069,1,'ACTIVE','KG','',1000.00,1000.00,'2016-09-02 00:00:00',''),(69,'Carrot Cake Whole','Carrot Cake Whole','Y','2016-06-27 00:00:00',100000,'Y','Y',100070,1,'ACTIVE','PC','',328.08,328.08,'2016-09-03 00:00:00',''),(70,'Cash Drawer','Cash Drawer','Y','2016-06-27 00:00:00',100000,'N','N',100071,1,'ACTIVE','PC','',4725.00,4725.00,'2016-09-04 00:00:00',''),(71,'Cash Handover Sheet & Daily Cash Pickup Register','Cash Handover Sheet & Daily Cash Pickup Register','Y','2016-06-27 00:00:00',100000,'Y','Y',100072,1,'ACTIVE','PC','',130.00,130.00,'2016-09-05 00:00:00',''),(72,'Cash Voucher Pad','Cash Voucher Pad','Y','2016-06-27 00:00:00',100000,'Y','Y',100073,1,'ACTIVE','PC','',60.00,60.00,'2016-09-06 00:00:00',''),(73,'Ceramic Mug Medium','Ceramic Mug Medium','Y','2016-06-27 00:00:00',100000,'Y','Y',100074,1,'ACTIVE','PC','',35.00,35.00,'2016-09-07 00:00:00',''),(74,'Ceramic Mug Small','Ceramic Mug Small','Y','2016-06-27 00:00:00',100000,'Y','Y',100075,1,'ACTIVE','PC','',30.00,30.00,'2016-09-08 00:00:00',''),(75,'Chai Delivery  Box 1 L Nutech','Chai delivery  Box 1 Ltr Nutech','Y','2016-06-27 00:00:00',100000,'Y','Y',100076,1,'ACTIVE','PC','',10.00,10.00,'2016-09-09 00:00:00',''),(76,'Chai Masala Chaayos','Chai Masala Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100077,1,'ACTIVE','KG','',480.00,480.00,'2016-09-10 00:00:00',''),(77,'Chai Sachet Container','Chai Sachet Container','Y','2016-06-27 00:00:00',100000,'Y','Y',100078,1,'ACTIVE','PC','',144.00,144.00,'2016-09-11 00:00:00',''),(78,'Chalk Omex','Chalk Omex','Y','2016-06-27 00:00:00',100000,'Y','Y',100079,1,'ACTIVE','PC','',26.00,26.00,'2016-09-12 00:00:00',''),(79,'Chat Masala Mdh','Chat Masala Mdh','Y','2016-06-27 00:00:00',100000,'Y','Y',100080,1,'ACTIVE','KG','',475.57,475.57,'2016-09-13 00:00:00',''),(80,'Chatpata Kebab Filling Chaayos','Chatpata Kebab Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100081,1,'ACTIVE','PC','',5.93,5.93,'2016-09-14 00:00:00',''),(81,'Chikoo Filling Chaayos','Chikoo Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100082,1,'ACTIVE','PC','',8.92,8.92,'2016-09-15 00:00:00',''),(82,'Chocolate Bun Filling Chaayos','Chocolate Bun Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100083,1,'ACTIVE','PC','',37.20,37.20,'2016-09-16 00:00:00',''),(83,'Chocolate Drinking Mix','Chocolate Drinking Mix','Y','2016-06-27 00:00:00',100000,'Y','Y',100084,1,'ACTIVE','KG','',127.50,127.50,'2016-09-17 00:00:00',''),(84,'Chocolate Syrup Harshey','Chocolate Syrup Harshey','Y','2016-06-27 00:00:00',100000,'Y','Y',100085,1,'ACTIVE','L','',148.62,148.62,'2016-09-18 00:00:00',''),(85,'Chopping Board - Non Veg Hdpl','Chopping Board - Non Veg Hdpl','Y','2016-06-27 00:00:00',100000,'Y','Y',100086,1,'ACTIVE','PC','',989.00,989.00,'2016-09-19 00:00:00',''),(86,'Chopping Board - Veg Hdpl','Chopping Board - Veg Hdpl','Y','2016-06-27 00:00:00',100000,'Y','Y',100087,1,'ACTIVE','PC','',989.00,989.00,'2016-09-20 00:00:00',''),(87,'Cinnamon Roll','Cinnamon Roll','Y','2016-06-27 00:00:00',100000,'Y','Y',100088,1,'ACTIVE','KG','',650.00,650.00,'2016-09-21 00:00:00',''),(88,'Citronella Oil Aroma','Citronella Oil Aroma','Y','2016-06-27 00:00:00',100000,'Y','Y',100089,1,'ACTIVE','L','',1600.00,1600.00,'2016-09-22 00:00:00',''),(89,'Clear Tape 1\"','Clear Tape 1\"','Y','2016-06-27 00:00:00',100000,'Y','Y',100090,1,'ACTIVE','PC','',13.00,13.00,'2016-09-23 00:00:00',''),(90,'Clear Tape 2\"','Clear Tape 2\"','Y','2016-06-27 00:00:00',100000,'Y','Y',100091,1,'ACTIVE','PC','',26.00,26.00,'2016-09-24 00:00:00',''),(91,'Cling Wrap','Cling Wrap','Y','2016-06-27 00:00:00',100000,'Y','Y',100092,1,'ACTIVE','PC','',250.00,250.00,'2016-09-25 00:00:00',''),(92,'Clove Whole','Clove Whole','Y','2016-06-27 00:00:00',100000,'Y','Y',100093,1,'ACTIVE','KG','',1020.00,1020.00,'2016-09-26 00:00:00',''),(93,'Cobra File','Cobra File','Y','2016-06-27 00:00:00',100000,'Y','Y',100094,1,'ACTIVE','PC','',9.75,9.75,'2016-09-27 00:00:00',''),(94,'Coffee Delight Bayers','Coffee Delight Bayers','Y','2016-06-27 00:00:00',100000,'Y','Y',100095,1,'ACTIVE','L','',180.00,180.00,'2016-09-28 00:00:00',''),(95,'Coffee Powder Nestlle','Coffee Powder Nestlle','Y','2016-06-27 00:00:00',100000,'Y','Y',100096,1,'ACTIVE','KG','',1528.00,1528.00,'2016-09-29 00:00:00',''),(96,'Cold Cup 350 Ml','Cold Cup 350 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100097,1,'ACTIVE','PC','',1.76,1.76,'2016-09-30 00:00:00',''),(97,'Cold Cup Lid','Cold Cup Lid','Y','2016-06-27 00:00:00',100000,'Y','Y',100098,1,'ACTIVE','PC','',0.78,0.78,'2016-10-01 00:00:00',''),(98,'Cold Cups 500 Ml','Cold Cups 500 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100099,1,'ACTIVE','PC','',2.27,2.27,'2016-10-02 00:00:00',''),(99,'Condiment Box','Condiment Box','Y','2016-06-27 00:00:00',100000,'Y','Y',100100,1,'ACTIVE','PC','',850.00,850.00,'2016-10-03 00:00:00',''),(100,'Container 1 L Smart','Container 1 L Smart','Y','2016-06-27 00:00:00',100000,'Y','Y',100101,1,'ACTIVE','PC','',34.00,34.00,'2016-10-04 00:00:00',''),(101,'Container 1.5L Smart','Container 1.5L Smart','Y','2016-06-27 00:00:00',100000,'Y','Y',100102,1,'ACTIVE','PC','',45.00,45.00,'2016-10-05 00:00:00',''),(102,'Container 10 L Smart','Container 10 L Smart','Y','2016-06-27 00:00:00',100000,'Y','Y',100103,1,'ACTIVE','PC','',185.00,185.00,'2016-10-06 00:00:00',''),(103,'Container 2 L Smart','Container 2 L Smart','Y','2016-06-27 00:00:00',100000,'Y','Y',100104,1,'ACTIVE','PC','',695.00,695.00,'2016-10-07 00:00:00',''),(104,'Container 200 Ml Smart','Container 200 Ml Smart','Y','2016-06-27 00:00:00',100000,'Y','Y',100105,1,'ACTIVE','PC','',13.75,13.75,'2016-10-08 00:00:00',''),(105,'Container 3.8 L','Container 3.8 L','Y','2016-06-27 00:00:00',100000,'Y','Y',100106,1,'ACTIVE','PC','',104.00,104.00,'2016-10-09 00:00:00',''),(106,'Container 5 L Smart','Container 5 L Smart','Y','2016-06-27 00:00:00',100000,'Y','Y',100107,1,'ACTIVE','PC','',125.00,125.00,'2016-10-10 00:00:00',''),(107,'Container 500 Ml Smart','Container 500Ml Smart','Y','2016-06-27 00:00:00',100000,'Y','Y',100108,1,'ACTIVE','PC','',22.00,22.00,'2016-10-11 00:00:00',''),(108,'Conveyer Toaster Akasa','Conveyer Toaster Akasa','Y','2016-06-27 00:00:00',100000,'N','N',100109,1,'ACTIVE','PC','',24189.30,24189.30,'2016-10-12 00:00:00',''),(110,'Correction Pen Tample','Correction Pen Tample','Y','2016-06-27 00:00:00',100000,'Y','Y',100110,1,'ACTIVE','PC','',21.00,21.00,'2016-10-13 00:00:00',''),(111,'Counter Top','Counter Top','Y','2016-06-27 00:00:00',100000,'Y','Y',100111,1,'ACTIVE','PC','',4000.00,4000.00,'2016-10-14 00:00:00',''),(112,'Croissant','Croissant','Y','2016-06-27 00:00:00',100000,'Y','Y',100112,1,'ACTIVE','PC','',20.00,20.00,'2016-10-15 00:00:00',''),(113,'Cucumber Slices','Cucumber','Y','2016-06-27 00:00:00',100000,'Y','Y',100113,1,'ACTIVE','KG','',77.64,77.64,'2016-10-16 00:00:00',''),(114,'Cup Holder','Cup Holder','Y','2016-06-27 00:00:00',100000,'Y','Y',100114,1,'ACTIVE','PC','',5.23,5.23,'2016-10-17 00:00:00',''),(115,'Cup Stand - Cold Station','Cup Stand - Cold Station','Y','2016-06-27 00:00:00',100000,'Y','Y',100115,1,'ACTIVE','PC','',1600.00,1600.00,'2016-10-18 00:00:00',''),(116,'Cup Stand - Hot Station','Cup Stand - Hot Station','Y','2016-06-27 00:00:00',100000,'Y','Y',100116,1,'ACTIVE','PC','',2000.00,2000.00,'2016-10-19 00:00:00',''),(117,'Cutting Chai Glass Plus','Cutting Chai Glass Plus','Y','2016-06-27 00:00:00',100000,'Y','Y',100117,1,'ACTIVE','PC','',6.63,6.63,'2016-10-20 00:00:00',''),(118,'D10 Taski','D10 Taski','Y','2016-06-27 00:00:00',100000,'Y','Y',100118,1,'ACTIVE','L','',166.36,166.36,'2016-10-21 00:00:00',''),(119,'Daily Checklist Book (Gold Standard)','Daily Checklist Book (Gold Standard)','Y','2016-06-27 00:00:00',100000,'Y','Y',100119,1,'ACTIVE','PC','',206.00,206.00,'2016-10-22 00:00:00',''),(120,'Darjeeling First Flush Patti','Darjeeling First Flush Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100120,1,'ACTIVE','KG','',7.39,7.39,'2016-10-23 00:00:00',''),(121,'Day Part Plan','Day Part Plan','Y','2016-06-27 00:00:00',100000,'Y','Y',100121,1,'ACTIVE','PC','',65.00,65.00,'2016-10-24 00:00:00',''),(122,'Desi Chai Can Chaayos','Desi Chai Can Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100122,1,'ACTIVE','PC','',47.11,47.11,'2016-10-25 00:00:00',''),(123,'Desi Chai Patti Tata','Desi Chai Patti Tata','Y','2016-06-27 00:00:00',100000,'Y','Y',100123,1,'ACTIVE','KG','',9.22,9.22,'2016-10-26 00:00:00',''),(124,'Diesel Hp','Diesel Hp','Y','2016-06-27 00:00:00',100000,'Y','Y',100124,1,'ACTIVE','L','',45.62,45.62,'2016-10-27 00:00:00',''),(125,'Diffuser Aroma','Diffuser Aroma','Y','2016-06-27 00:00:00',100000,'Y','Y',100125,1,'ACTIVE','PC','',50.00,50.00,'2016-10-28 00:00:00',''),(126,'Dip Bottle','Dip Bottle','Y','2016-06-27 00:00:00',100000,'Y','Y',100126,1,'ACTIVE','PC','',0.48,0.48,'2016-10-29 00:00:00',''),(127,'Dip Container With Lid','Dip Container With Lid','Y','2016-06-27 00:00:00',100000,'Y','Y',100127,1,'ACTIVE','PC','',375.00,375.00,'2016-10-30 00:00:00',''),(128,'Dip Cup','Dip Cup','Y','2016-06-27 00:00:00',100000,'Y','Y',100128,1,'ACTIVE','PC','',0.30,0.30,'2016-10-31 00:00:00',''),(129,'Display Basket','Display Basket','Y','2016-06-27 00:00:00',100000,'Y','Y',100129,1,'ACTIVE','PC','',875.00,875.00,'2016-11-01 00:00:00',''),(130,'Disposable Cap','Disposable Cap','Y','2016-06-27 00:00:00',100000,'Y','Y',100130,1,'ACTIVE','PC','',72.00,72.00,'2016-11-02 00:00:00',''),(131,'To Go Tray Mat','TO GO Tray Mat','Y','2016-06-27 00:00:00',100000,'Y','Y',100131,1,'ACTIVE','PC','',1.40,1.40,'2016-11-03 00:00:00',''),(132,'Dome Cover Canford','Dome Cover Canford','Y','2016-06-27 00:00:00',100000,'Y','Y',100132,1,'ACTIVE','PC','',375.00,375.00,'2016-11-04 00:00:00',''),(133,'Doormat','Doormat','Y','2016-06-27 00:00:00',100000,'Y','Y',100133,1,'ACTIVE','PC','',200.00,200.00,'2016-11-05 00:00:00',''),(134,'Double Side Tape 1\"','Double Side Tape 1\"','Y','2016-06-27 00:00:00',100000,'Y','Y',100134,1,'ACTIVE','PC','',26.00,26.00,'2016-11-06 00:00:00',''),(135,'Dust Control Mop Head','Dust Control Mop Head','Y','2016-06-27 00:00:00',100000,'Y','Y',100135,1,'ACTIVE','PC','',192.31,192.31,'2016-11-07 00:00:00',''),(136,'Dust Control Mop Rod','Dust Control Mop Rod','Y','2016-06-27 00:00:00',100000,'Y','Y',100136,1,'ACTIVE','PC','',100.00,100.00,'2016-11-08 00:00:00',''),(137,'Dust Pan','Dust Pan','Y','2016-06-27 00:00:00',100000,'Y','Y',100137,1,'ACTIVE','PC','',20.00,20.00,'2016-11-09 00:00:00',''),(138,'Dustbin Padal','Dustbin Padal','Y','2016-06-27 00:00:00',100000,'Y','Y',100138,1,'ACTIVE','PC','',160.00,160.00,'2016-11-10 00:00:00',''),(139,'Duster','Duster','Y','2016-06-27 00:00:00',100000,'Y','Y',100139,1,'ACTIVE','PC','',9.20,9.20,'2016-11-11 00:00:00',''),(140,'Dustpan With Broom Grassland','Dustpan With Broom Grassland','Y','2016-06-27 00:00:00',100000,'Y','Y',100140,1,'ACTIVE','PC','',670.00,670.00,'2016-11-12 00:00:00',''),(141,'Earl Grey Patti','Earl Grey Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100141,1,'ACTIVE','KG','',900.00,900.00,'2016-11-13 00:00:00',''),(142,'Egg Bun Filling Chaayos','Egg Bun Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100142,1,'ACTIVE','PC','',4.21,4.21,'2016-11-14 00:00:00',''),(143,'Electronic Safe Godrej','Electronic Safe Godrej','Y','2016-06-27 00:00:00',100000,'N','N',100143,1,'ACTIVE','PC','',5778.00,5778.00,'2016-11-15 00:00:00',''),(144,'English Breakfast Patti','English Breakfast Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100144,1,'ACTIVE','KG','',714.00,714.00,'2016-11-16 00:00:00',''),(145,'English Oven Bun Cremica','English Oven Bun Cremica','Y','2016-06-27 00:00:00',100000,'Y','Y',100145,1,'ACTIVE','PC','',20.00,20.00,'2016-11-17 00:00:00',''),(146,'Eraser Natraj','Eraser Natraj','Y','2016-06-27 00:00:00',100000,'Y','Y',100146,1,'ACTIVE','PC','',0.88,0.88,'2016-11-18 00:00:00',''),(147,'Extension Board','Extension Board','Y','2016-06-27 00:00:00',100000,'Y','Y',100147,1,'ACTIVE','PC','',240.00,240.00,'2016-11-19 00:00:00',''),(148,'Fire Extinguisher 2Kg Fire Killer','Fire Extinguisher 2Kg Fire Killer','Y','2016-06-27 00:00:00',100000,'N','N',100148,1,'ACTIVE','PC','',1350.00,1350.00,'2016-11-20 00:00:00',''),(149,'Fire Extinguisher 4Kg Fire Killer','Fire Extinguisher 4Kg Fire Killer','Y','2016-06-27 00:00:00',100000,'N','N',100149,1,'ACTIVE','PC','',1800.00,1800.00,'2016-11-21 00:00:00',''),(150,'Fly Catcher Pad','Fly Catcher Pad','Y','2016-06-27 00:00:00',100000,'Y','Y',100150,1,'ACTIVE','PC','',408.50,408.50,'2016-11-22 00:00:00',''),(151,'Fly Catcher Tubelight Philips','Fly Catcher Tubelight Philips','Y','2016-06-27 00:00:00',100000,'Y','Y',100151,1,'ACTIVE','PC','',110.00,110.00,'2016-11-23 00:00:00',''),(152,'Fly Killer Machine','Fly Killer Machine','Y','2016-06-27 00:00:00',100000,'Y','Y',100152,1,'ACTIVE','PC','',4100.00,4100.00,'2016-11-24 00:00:00',''),(153,'Focaccia','Focaccia','Y','2016-06-27 00:00:00',100000,'Y','Y',100153,1,'ACTIVE','PC','',14.00,14.00,'2016-11-25 00:00:00',''),(154,'Food Temperature Thermometer','Food Temperature Thermometer','Y','2016-06-27 00:00:00',100000,'Y','Y',100154,1,'ACTIVE','PC','',360.00,360.00,'2016-11-26 00:00:00',''),(155,'Fridge Lg','Fridge Lg','Y','2016-06-27 00:00:00',100000,'N','N',100155,1,'ACTIVE','PC','',10500.00,10500.00,'2016-11-27 00:00:00',''),(156,'Frozen Tortilla','Frozen Tortilla','Y','2016-06-27 00:00:00',100000,'Y','Y',100156,1,'ACTIVE','PC','',92.40,92.40,'2016-11-28 00:00:00',''),(157,'Funnel','Funnel','Y','2016-06-27 00:00:00',100000,'Y','Y',100157,1,'ACTIVE','PC','',35.00,35.00,'2016-11-29 00:00:00',''),(158,'Gental Soap','Gental Soap','Y','2016-06-27 00:00:00',100000,'Y','Y',100158,1,'ACTIVE','PC','',300.00,300.00,'2016-11-30 00:00:00',''),(159,'Ginger Slices','Ginger','Y','2016-06-27 00:00:00',100000,'Y','Y',100159,1,'ACTIVE','KG','',151.00,151.00,'2016-12-01 00:00:00',''),(160,'Ginger Bits','Ginger Bits','Y','2016-06-27 00:00:00',100000,'Y','Y',100160,1,'ACTIVE','KG','',909.50,909.50,'2016-12-02 00:00:00',''),(161,'Glass Cleaning Handle Invanta','Glass Cleaning Handle Invanta','Y','2016-06-27 00:00:00',100000,'Y','Y',100161,1,'ACTIVE','PC','',1790.00,1790.00,'2016-12-03 00:00:00',''),(162,'Glass Cleaning Kit Invanta','Glass Cleaning Kit Invanta','Y','2016-06-27 00:00:00',100000,'Y','Y',100162,1,'ACTIVE','PC','',2310.00,2310.00,'2016-12-04 00:00:00',''),(163,'Glass Jar','Glass Jar','Y','2016-06-27 00:00:00',100000,'Y','Y',100163,1,'ACTIVE','PC','',250.00,250.00,'2016-12-05 00:00:00',''),(164,'Coloured Glasses','Coloured glasses','Y','2016-06-27 00:00:00',100000,'Y','Y',100164,1,'ACTIVE','PC','',30.00,30.00,'2016-12-06 00:00:00',''),(165,'Glue Stick Artline','Glue Stick Artline','Y','2016-06-27 00:00:00',100000,'Y','Y',100165,1,'ACTIVE','PC','',23.50,23.50,'2016-12-07 00:00:00',''),(166,'Gods Chai Patti','Gods Chai Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100166,1,'ACTIVE','KG','',1750.00,1750.00,'2016-12-08 00:00:00',''),(167,'Green Chicken Filling Chaayos','Green Chicken Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100167,1,'ACTIVE','PC','',25.08,25.08,'2016-12-09 00:00:00',''),(168,'Green Chilli 1 inch','Green Chilli','Y','2016-06-27 00:00:00',100000,'Y','Y',100168,1,'ACTIVE','KG','',122.30,122.30,'2016-12-10 00:00:00',''),(169,'Green Chilli Mint Mayo Sauce Chaayos','Green Chilli Mint Mayo Sauce Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100169,1,'ACTIVE','KG','',19.03,19.03,'2016-12-11 00:00:00',''),(170,'Green Tea Can Chaayos','Green Tea Can Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100170,1,'ACTIVE','PC','',40.00,40.00,'2016-12-12 00:00:00',''),(171,'Green Tea Patti','Green Tea Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100171,1,'ACTIVE','KG','',950.00,950.00,'2016-12-13 00:00:00',''),(172,'Griller Akasa','Griller Akasa','Y','2016-06-27 00:00:00',100000,'N','N',100172,1,'ACTIVE','PC','',10800.00,10800.00,'2016-12-14 00:00:00',''),(173,'Grooming Kit','Grooming Kit','Y','2016-06-27 00:00:00',100000,'Y','Y',100173,1,'ACTIVE','PC','',180.00,180.00,'2016-12-15 00:00:00',''),(174,'Hand Gloves','Hand Gloves','Y','2016-06-27 00:00:00',100000,'Y','Y',100174,1,'ACTIVE','PC','',13.00,13.00,'2016-12-16 00:00:00',''),(175,'Hand Wash Taski','Hand Wash Taski','Y','2016-06-27 00:00:00',100000,'Y','Y',100175,1,'ACTIVE','L','',134.80,134.80,'2016-12-17 00:00:00',''),(176,'Handheld Menu Stand','Handheld Menu Stand','Y','2016-06-27 00:00:00',100000,'Y','Y',100176,1,'ACTIVE','PC','',200.00,200.00,'2016-12-18 00:00:00',''),(177,'Hari Chuttney Chaayos','Hari Chuttney Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100177,1,'ACTIVE','KG','',28.63,28.63,'2016-12-19 00:00:00',''),(178,'Toilet Cleaner','Toilet cleaner','Y','2016-06-27 00:00:00',100000,'Y','Y',100360,1,'ACTIVE','PC','',35.00,35.00,'2016-12-20 00:00:00',''),(179,'Homestyle Aloo Filling Chaayos','Homestyle Aloo Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100179,1,'ACTIVE','PC','',2.34,2.34,'2016-12-21 00:00:00',''),(180,'Honey Dabur','Honey Dabur','Y','2016-06-27 00:00:00',100000,'Y','Y',100180,1,'ACTIVE','KG','',296.79,296.79,'2016-12-22 00:00:00',''),(181,'Honey Garlic Mayo Sauce Chaayos','Honey Garlic Mayo Sauce Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100181,1,'ACTIVE','KG','',131.09,131.09,'2016-12-23 00:00:00',''),(182,'Honey Lemon Butter Chaayos','Honey Lemon Butter Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100182,1,'ACTIVE','KG','',1.00,1.00,'2016-12-24 00:00:00',''),(183,'Hot Cup 150 Ml','Hot Cup 150 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100183,1,'ACTIVE','PC','',0.48,0.48,'2016-12-25 00:00:00',''),(184,'Hot Cup 250 Ml','Hot Cup 250 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100184,1,'ACTIVE','PC','',1.33,1.33,'2016-12-26 00:00:00',''),(185,'Hot Cups 360 Ml','Hot Cups 360 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100185,1,'ACTIVE','PC','',2.02,2.02,'2016-12-27 00:00:00',''),(186,'Ice','Ice','Y','2016-06-27 00:00:00',100000,'Y','Y',100186,1,'ACTIVE','KG','',10.00,10.00,'2016-12-28 00:00:00',''),(187,'Ice Machine Elan Pro','Ice Machine Elan Pro','Y','2016-06-27 00:00:00',100000,'N','N',100187,1,'ACTIVE','PC','',55750.00,55750.00,'2016-12-29 00:00:00',''),(188,'Ice Tray','Ice Tray','Y','2016-06-27 00:00:00',100000,'Y','Y',100188,1,'ACTIVE','PC','',47.50,47.50,'2016-12-30 00:00:00',''),(189,'Induction Plate Glen','Induction Plate Glen','Y','2016-06-27 00:00:00',100000,'Y','Y',100189,1,'ACTIVE','PC','',2025.00,2025.00,'2016-12-31 00:00:00',''),(190,'Italian Burger Bun','Italian Burger Bun','Y','2016-06-27 00:00:00',100000,'Y','Y',100190,1,'ACTIVE','PC','',18.54,18.54,'2017-01-01 00:00:00',''),(191,'Jasmine Tea Patti Chaniese','Jasmine Tea Patti Chaniese','Y','2016-06-27 00:00:00',100000,'Y','Y',100191,1,'ACTIVE','KG','',1557.27,1557.27,'2017-01-02 00:00:00',''),(192,'Jumbo Bread','Jumbo Bread','Y','2016-06-27 00:00:00',100000,'Y','Y',100192,1,'ACTIVE','PC','',3.47,3.47,'2017-01-03 00:00:00',''),(193,'Kadhai Paneer Filling Chayaaos','Kadhai Paneer Filling Chayaaos','Y','2016-06-27 00:00:00',100000,'Y','Y',100193,1,'ACTIVE','PC','',15.80,15.80,'2017-01-04 00:00:00',''),(194,'Kashmiri Kahwa Patti','Kashmiri Kahwa Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100194,1,'ACTIVE','KG','',850.00,850.00,'2017-01-05 00:00:00',''),(195,'Keema Pav Filling Chaayos','Keema Pav Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100195,1,'ACTIVE','PC','',12.31,12.31,'2017-01-06 00:00:00',''),(196,'Kesar Mogra','Kesar Mogra','Y','2016-06-27 00:00:00',100000,'Y','Y',100196,1,'ACTIVE','PC','',191.11,191.11,'2017-01-07 00:00:00',''),(197,'Ketchup Sachet Delmonta','Ketchup Sachet Delmonta','Y','2016-06-27 00:00:00',100000,'Y','Y',100197,1,'ACTIVE','SACHET','',0.74,0.74,'2017-01-08 00:00:00',''),(198,'Key Ring','Key Ring','Y','2016-06-27 00:00:00',100000,'Y','Y',100198,1,'ACTIVE','PC','',30.00,30.00,'2017-01-09 00:00:00',''),(199,'Keyboard','Keyboard','Y','2016-06-27 00:00:00',100000,'N','N',100199,1,'ACTIVE','PC','',300.00,300.00,'2017-01-10 00:00:00',''),(200,'Kitchen Knife','Kitchen Knife','Y','2016-06-27 00:00:00',100000,'Y','Y',100200,1,'ACTIVE','PC','',275.00,275.00,'2017-01-11 00:00:00',''),(201,'Kiwi Filling Chaayos','Kiwi Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100201,1,'ACTIVE','PC','',32.89,32.89,'2017-01-12 00:00:00',''),(202,'Kulhad 120 Ml','Kulhad 120 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100202,1,'ACTIVE','PC','',2.50,2.50,'2017-01-13 00:00:00',''),(203,'Kulhad 250 Ml','Kulhad 250 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100203,1,'ACTIVE','PC','',3.30,3.30,'2017-01-14 00:00:00',''),(204,'Landline','Landline','Y','2016-06-27 00:00:00',100000,'N','N',100204,1,'ACTIVE','PC','',695.00,695.00,'2017-01-15 00:00:00',''),(205,'Lasoon Chutney Chaayos','Lasoon Chutney Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100205,1,'ACTIVE','KG','',50.36,50.36,'2017-01-16 00:00:00',''),(206,'Lemon Whole','Lemon','Y','2016-06-27 00:00:00',100000,'Y','Y',100206,1,'ACTIVE','KG','',110.04,110.04,'2017-01-17 00:00:00',''),(207,'Lemon Cake Whole','Lemon Cake Whole','Y','2016-06-27 00:00:00',100000,'Y','Y',100207,1,'ACTIVE','PC','',264.00,264.00,'2017-01-18 00:00:00',''),(208,'Lemon Grass Patti','Lemon Grass Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100208,1,'ACTIVE','KG','',800.00,800.00,'2017-01-19 00:00:00',''),(209,'Lemon Juice Machine','Lemon Juice Machine','Y','2016-06-27 00:00:00',100000,'N','N',100209,1,'ACTIVE','PC','',110.00,110.00,'2017-01-20 00:00:00',''),(210,'Lemon Powder','Lemon Powder','Y','2016-06-27 00:00:00',100000,'Y','Y',100210,1,'ACTIVE','PC','',148.10,148.10,'2017-01-21 00:00:00',''),(211,'Lopchu Patti','Lopchu Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100211,1,'ACTIVE','KG','',1100.00,1100.00,'2017-01-22 00:00:00',''),(212,'M Fold Hand Towel','M Fold Hand Towel','Y','2016-06-27 00:00:00',100000,'Y','Y',100212,1,'ACTIVE','PC','',30.00,30.00,'2017-01-23 00:00:00',''),(213,'Magic Broom','Magic Broom','Y','2016-06-27 00:00:00',100000,'Y','Y',100213,1,'ACTIVE','PC','',60.00,60.00,'2017-01-24 00:00:00',''),(214,'Manager Laptop Acer','Manager Laptop Acer','Y','2016-06-27 00:00:00',100000,'N','N',100214,1,'ACTIVE','PC','',40000.00,40000.00,'2017-01-25 00:00:00',''),(215,'Manager Shirt','Manager Shirt','Y','2016-06-27 00:00:00',100000,'Y','Y',100215,1,'ACTIVE','PC','',400.00,400.00,'2017-01-26 00:00:00',''),(216,'Mango Filling Chaayos','Mango Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100216,1,'ACTIVE','PC','',8.34,8.34,'2017-01-27 00:00:00',''),(217,'Manual Bill Book','Manual Bill Book','Y','2016-06-27 00:00:00',100000,'Y','Y',100217,1,'ACTIVE','PC','',26.00,26.00,'2017-01-28 00:00:00',''),(218,'Masala Chai Can Chaayos','Masala Chai Can Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100218,1,'ACTIVE','PC','',0.33,0.33,'2017-01-29 00:00:00',''),(219,'Measuring Beaker 100Ml','Measuring Beaker 100Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100219,1,'ACTIVE','PC','',16.62,16.62,'2017-01-30 00:00:00',''),(220,'Measuring Beaker 115 Ml','Measuring Beaker 115 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100220,1,'ACTIVE','PC','',16.62,16.62,'2017-01-31 00:00:00',''),(221,'Measuring Beaker 165 Ml','Measuring Beaker 165 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100221,1,'ACTIVE','PC','',75.00,75.00,'2017-02-01 00:00:00',''),(222,'Measuring Beaker 230 Ml','Measuring Beaker 230 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100222,1,'ACTIVE','PC','',105.00,105.00,'2017-02-02 00:00:00',''),(223,'Measuring Beaker 250Ml','Measuring Beaker 250Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100223,1,'ACTIVE','PC','',39.25,39.25,'2017-02-03 00:00:00',''),(224,'Measuring Beaker 330 Ml','Measuring Beaker 330 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100224,1,'ACTIVE','PC','',115.00,115.00,'2017-02-04 00:00:00',''),(225,'Measuring Beaker 460 Ml','Measuring Beaker 460 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100225,1,'ACTIVE','PC','',155.00,155.00,'2017-02-05 00:00:00',''),(226,'Measuring Beaker 50 Ml','Measuring Beaker 50 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100226,1,'ACTIVE','PC','',12.00,12.00,'2017-02-06 00:00:00',''),(227,'Measuring Beaker 575 Ml','Measuring Beaker 575 Ml','Y','2016-06-27 00:00:00',100000,'Y','Y',100227,1,'ACTIVE','PC','',185.00,185.00,'2017-02-07 00:00:00',''),(228,'Measuring Jar 1 L','Measuring Jar 1 Ltr','Y','2016-06-27 00:00:00',100000,'Y','Y',100228,1,'ACTIVE','PC','',55.00,55.00,'2017-02-08 00:00:00',''),(229,'Measuring Jar 2 L','Measuring Jar 2 Ltr','Y','2016-06-27 00:00:00',100000,'Y','Y',100229,1,'ACTIVE','PC','',65.00,65.00,'2017-02-09 00:00:00',''),(230,'Measuring Spoon Set','Measuring Spoon Set','Y','2016-06-27 00:00:00',100000,'Y','Y',100230,1,'ACTIVE','PC','',96.97,96.97,'2017-02-10 00:00:00',''),(231,'Microwave Oven Lg','MicroWave oven Lg','Y','2016-06-27 00:00:00',100000,'N','Y',100231,1,'ACTIVE','PC','',4685.08,4685.08,'2017-02-11 00:00:00',''),(232,'Microwave Bowl - Non Veg Fable','Microwave Bowl - Non Veg Fable','Y','2016-06-27 00:00:00',100000,'Y','Y',100232,1,'ACTIVE','PC','',25.00,25.00,'2017-02-12 00:00:00',''),(233,'Microwave Bowl - Veg Fable','Microwave Bowl - Veg Fable','Y','2016-06-27 00:00:00',100000,'Y','Y',100233,1,'ACTIVE','PC','',25.00,25.00,'2017-02-13 00:00:00',''),(234,'Milk Amul','Milk Amul','Y','2016-06-27 00:00:00',100000,'Y','Y',100234,1,'ACTIVE','L','',38.00,38.00,'2017-02-14 00:00:00',''),(235,'Mineral Water Jar 20 Ltr','Mineral Water Jar 20 Ltr','Y','2016-06-27 00:00:00',100000,'Y','Y',100235,1,'ACTIVE','PC','',70.00,70.00,'2017-02-15 00:00:00',''),(236,'Mint And Jalapeno Butter Chaayos','Mint And Jalapeno Butter Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100236,1,'ACTIVE','KG','',1.00,1.00,'2017-02-16 00:00:00',''),(237,'Mint Green Patti','Mint Green Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100237,1,'ACTIVE','KG','',1050.00,1050.00,'2017-02-17 00:00:00',''),(238,'Mint Leaves','Mint Leaves','Y','2016-06-27 00:00:00',100000,'Y','Y',100238,1,'ACTIVE','KG','',259.17,259.17,'2017-02-18 00:00:00',''),(239,'Mixer Grinder Sujata','Mixer Grinder Sujata','Y','2016-06-27 00:00:00',100000,'N','N',100239,1,'ACTIVE','PC','',3487.42,3487.42,'2017-02-19 00:00:00',''),(240,'Mobile','Mobile','Y','2016-06-27 00:00:00',100000,'N','N',100240,1,'ACTIVE','PC','',5000.00,5000.00,'2017-02-20 00:00:00',''),(241,'Passion Fruit Puree Monin','Passion Fruit Puree Monin','Y','2016-06-27 00:00:00',100000,'Y','Y',100241,1,'ACTIVE','L','',900.00,900.00,'2017-02-21 00:00:00',''),(242,'Peach Puree Monin','Peach Puree Monin','Y','2016-06-27 00:00:00',100000,'Y','Y',100242,1,'ACTIVE','L','',900.00,900.00,'2017-02-22 00:00:00',''),(243,'Mop Trolly Grassland','Mop Trolly Grassland','Y','2016-06-27 00:00:00',100000,'Y','N',100243,1,'ACTIVE','PC','',2500.00,2500.00,'2017-02-23 00:00:00',''),(244,'Mouse','Mouse','Y','2016-06-27 00:00:00',100000,'N','N',100244,1,'ACTIVE','PC','',250.00,250.00,'2017-02-24 00:00:00',''),(245,'Mud Sticker Nutech','Mud Sticker Nutech','Y','2016-06-27 00:00:00',100000,'Y','Y',100245,1,'ACTIVE','PC','',0.25,0.25,'2017-02-25 00:00:00',''),(246,'Multigrain Bread','Multigrain bread','Y','2016-06-27 00:00:00',100000,'Y','Y',100246,1,'ACTIVE','PC','',10.00,10.00,'2017-02-26 00:00:00',''),(247,'Muscatel Patti','Muscatel Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100247,1,'ACTIVE','KG','',2250.00,2250.00,'2017-02-27 00:00:00',''),(248,'Music System Sony','Music System Sony','Y','2016-06-27 00:00:00',100000,'Y','Y',100248,1,'ACTIVE','PC','',17800.00,17800.00,'2017-02-28 00:00:00',''),(249,'Music System Philips','Music System Sony','Y','2016-06-27 00:00:00',100000,'Y','Y',100248,1,'ACTIVE','PC','',1500.00,1500.00,'2017-03-01 00:00:00',''),(250,'Mutton Lazeez Filling Chaayos','Mutton Lazeez Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100249,1,'ACTIVE','PC','',34.20,34.20,'2017-03-02 00:00:00',''),(251,'Napoli Filling Chaayos','Napoli Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100250,1,'ACTIVE','PC','',25.28,25.28,'2017-03-03 00:00:00',''),(252,'Room Freshner Bar - Odonil','Room Freshner bar','Y','2016-06-27 00:00:00',100000,'Y','Y',100251,1,'ACTIVE','PC','',100.00,100.00,'2017-03-04 00:00:00',''),(253,'Oil & Grease Trap','Oil & Grease Trap','Y','2016-06-27 00:00:00',100000,'Y','Y',100252,1,'ACTIVE','PC','',7500.00,7500.00,'2017-03-05 00:00:00',''),(254,'Olive And Sundried Tomato Butter Chaayos','Olive And Sundried Tomato Butter Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100253,1,'ACTIVE','KG','',1.00,1.00,'2017-03-06 00:00:00',''),(255,'Onion Julienne','Onion','Y','2016-06-27 00:00:00',100000,'Y','Y',100254,1,'ACTIVE','KG','',72.57,72.57,'2017-03-07 00:00:00',''),(256,'Orange Pekoe Patti','Orange Pekoe Patti','Y','2016-06-27 00:00:00',100000,'Y','Y',100255,1,'ACTIVE','KG','',850.00,850.00,'2017-03-08 00:00:00',''),(257,'Paneer Khurchan Samosa Chaayos','Paneer Khurchan Samosa Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100256,1,'ACTIVE','PC','',84.00,84.00,'2017-03-09 00:00:00',''),(258,'Parfait Spoon','Parfait Spoon','Y','2016-06-27 00:00:00',100000,'Y','Y',100257,1,'ACTIVE','PC','',85.00,85.00,'2017-03-10 00:00:00',''),(259,'Pastry Tong','Pastry Tong','Y','2016-06-27 00:00:00',100000,'Y','Y',100258,1,'ACTIVE','PC','',125.00,125.00,'2017-03-11 00:00:00',''),(260,'Pav','Pav','Y','2016-06-27 00:00:00',100000,'Y','Y',100259,1,'ACTIVE','PC','',3.33,3.33,'2017-03-12 00:00:00',''),(261,'Pen Drive 8 Gb Sanddisk','Pen Drive 8 Gb Sanddisk','Y','2016-06-27 00:00:00',100000,'N','N',100260,1,'ACTIVE','PC','',350.00,350.00,'2017-03-13 00:00:00',''),(262,'Pencil Natraj','Pencil Natraj','Y','2016-06-27 00:00:00',100000,'Y','Y',100261,1,'ACTIVE','PC','',30.50,30.50,'2017-03-14 00:00:00',''),(263,'Pepper Chicken Filling Chaayos','Pepper Chicken Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100262,1,'ACTIVE','PC','',42.45,42.45,'2017-03-15 00:00:00',''),(264,'Permanet Marker Artline','Permanet Marker Artline','Y','2016-06-27 00:00:00',100000,'Y','Y',100263,1,'ACTIVE','PC','',14.50,14.50,'2017-03-16 00:00:00',''),(265,'Phool Broom','Phool Broom','Y','2016-06-27 00:00:00',100000,'Y','Y',100264,1,'ACTIVE','PC','',55.00,55.00,'2017-03-17 00:00:00',''),(266,'Plastic Chair','Plastic Chair','Y','2016-06-27 00:00:00',100000,'Y','Y',100265,1,'ACTIVE','PC','',541.00,541.00,'2017-03-18 00:00:00',''),(267,'Plastic Poha Container Aristo','Plastic Poha Container Aristo','Y','2016-06-27 00:00:00',100000,'Y','Y',100266,1,'ACTIVE','PC','',67.00,67.00,'2017-03-19 00:00:00',''),(268,'Plastic Spoon','Plastic Spoon','Y','2016-06-27 00:00:00',100000,'Y','Y',100267,1,'ACTIVE','PC','',40.00,40.00,'2017-03-20 00:00:00',''),(269,'Pocha','Pocha','Y','2016-06-27 00:00:00',100000,'Y','Y',100268,1,'ACTIVE','PC','',9.16,9.16,'2017-03-21 00:00:00',''),(270,'Poha Box','Poha Box','Y','2016-06-27 00:00:00',100000,'Y','Y',100269,1,'ACTIVE','PC','',1.46,1.46,'2017-03-22 00:00:00',''),(271,'Poha Box Lid','Poha Box Lid','Y','2016-06-27 00:00:00',100000,'Y','Y',100270,1,'ACTIVE','PC','',1.08,1.08,'2017-03-23 00:00:00',''),(272,'Poha Dry Rajdhani','Poha Dry Rajdhani','Y','2016-06-27 00:00:00',100000,'Y','Y',100271,1,'ACTIVE','KG','',63.00,63.00,'2017-03-24 00:00:00',''),(273,'Poha Masala Chaayos','Poha Masala Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100272,1,'ACTIVE','PC','',1.00,1.00,'2017-03-25 00:00:00',''),(274,'Poha Plate Ecoware','Poha Plate Ecoware','Y','2016-06-27 00:00:00',100000,'Y','Y',100273,1,'ACTIVE','PC','',2.00,2.00,'2017-03-26 00:00:00',''),(275,'Polythene Pp Grv','Polythene Pp Grv','Y','2016-06-27 00:00:00',100000,'Y','Y',100274,1,'ACTIVE','KG','',145.00,145.00,'2017-03-27 00:00:00',''),(276,'Pos Laptop','Pos Laptop','Y','2016-06-27 00:00:00',100000,'N','N',100275,1,'ACTIVE','PC','',17500.00,17500.00,'2017-03-28 00:00:00',''),(277,'Printer Epson','Printer Epson','Y','2016-06-27 00:00:00',100000,'N','N',100276,1,'ACTIVE','PC','',10400.00,10400.00,'2017-03-29 00:00:00',''),(278,'Printer Roll','Printer Roll','Y','2016-06-27 00:00:00',100000,'Y','Y',100277,1,'ACTIVE','PC','',38.00,38.00,'2017-03-30 00:00:00',''),(279,'Pullover - Large','Pullover (Large)','Y','2016-06-27 00:00:00',100000,'Y','Y',100278,1,'ACTIVE','PC','',425.00,425.00,'2017-03-31 00:00:00',''),(280,'Pullover - Medium','Pullover (Medium)','Y','2016-06-27 00:00:00',100000,'Y','Y',100279,1,'ACTIVE','PC','',425.00,425.00,'2017-04-01 00:00:00',''),(281,'Pullover - Small','Pullover (Small)','Y','2016-06-27 00:00:00',100000,'Y','Y',100280,1,'ACTIVE','PC','',425.00,425.00,'2017-04-02 00:00:00',''),(282,'Pullover - Xl','Pullover (Xl)','Y','2016-06-27 00:00:00',100000,'Y','Y',100281,1,'ACTIVE','PC','',415.00,415.00,'2017-04-03 00:00:00',''),(283,'Pump 10 Ml Monin','Pump 10 Ml Monin','Y','2016-06-27 00:00:00',100000,'Y','Y',100282,1,'ACTIVE','PC','',170.00,170.00,'2017-04-04 00:00:00',''),(284,'Pump 15Ml Monin','Pump 15Ml Monin','Y','2016-06-27 00:00:00',100000,'Y','Y',100283,1,'ACTIVE','PC','',225.00,225.00,'2017-04-05 00:00:00',''),(285,'Punching Machine Kangaro','Punching Machine Kangaro','Y','2016-06-27 00:00:00',100000,'Y','Y',100284,1,'ACTIVE','PC','',49.00,49.00,'2017-04-06 00:00:00',''),(286,'R1 Taski','R1 Taski','Y','2016-06-27 00:00:00',100000,'Y','Y',100285,1,'ACTIVE','PC','',181.33,181.33,'2017-04-07 00:00:00',''),(287,'R2 - Floor Cleaner Taski','R2 - Floor Cleaner Taski','Y','2016-06-27 00:00:00',100000,'Y','Y',100286,1,'ACTIVE','L','',167.00,167.00,'2017-04-08 00:00:00',''),(288,'R3 Taski','R3 Taski','Y','2016-06-27 00:00:00',100000,'Y','Y',100287,1,'ACTIVE','L','',223.00,223.00,'2017-04-09 00:00:00',''),(289,'R4 Taski','R4 Taski','Y','2016-06-27 00:00:00',100000,'Y','Y',100288,1,'ACTIVE','L','',334.65,334.65,'2017-04-10 00:00:00',''),(290,'Red Chilli Mayo Sauce Chaayos','Red Chilli Mayo Sauce Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100289,1,'ACTIVE','KG','',20.91,20.91,'2017-04-11 00:00:00',''),(291,'Register Trison','Register Trison','Y','2016-06-27 00:00:00',100000,'Y','Y',100290,1,'ACTIVE','PC','',30.00,30.00,'2017-04-12 00:00:00',''),(292,'Ro - 15 L Kent','RO - 15 L Kent','Y','2016-06-27 00:00:00',100000,'N','N',100291,1,'ACTIVE','PC','',14933.30,14933.30,'2017-04-13 00:00:00',''),(293,'Ro - 25 L Kent','RO - 25 L Kent','Y','2016-06-27 00:00:00',100000,'N','N',100292,1,'ACTIVE','PC','',29000.00,29000.00,'2017-04-14 00:00:00',''),(294,'Rooh Afza Hamdard','Rooh Afza Hamdard','Y','2016-06-27 00:00:00',100000,'Y','N',100394,1,'ACTIVE','L','',125.00,125.00,'2017-04-15 00:00:00',''),(295,'Room Freshner','Room Freshner','Y','2016-06-27 00:00:00',100000,'Y','Y',100293,1,'ACTIVE','L','',195.58,195.58,'2017-04-16 00:00:00',''),(296,'Rose Water Dabur','Rose Water Dabur','Y','2016-06-27 00:00:00',100000,'Y','Y',100294,1,'ACTIVE','PC','',50.50,50.50,'2017-04-17 00:00:00',''),(297,'Rubber  Stamp','Rubber  Stamp','Y','2016-06-27 00:00:00',100000,'Y','Y',100295,1,'ACTIVE','PC','',22.00,22.00,'2017-04-18 00:00:00',''),(298,'Rubber Band','Rubber Band','Y','2016-06-27 00:00:00',100000,'Y','Y',100296,1,'ACTIVE','PC','',22.00,22.00,'2017-04-19 00:00:00',''),(299,'Rusk Pack','Rusk Pack','Y','2016-06-27 00:00:00',100000,'Y','Y',100297,1,'ACTIVE','PC','',40.00,40.00,'2017-04-20 00:00:00',''),(300,'Sachet - Desi Regular Chaayos','Sachet - Desi Regular Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100298,1,'ACTIVE','SACHET','',2.73,2.73,'2017-04-21 00:00:00',''),(301,'Saunf','Saunf','Y','2016-06-27 00:00:00',100000,'Y','Y',100299,1,'ACTIVE','KG','',175.00,175.00,'2017-04-22 00:00:00',''),(302,'Scissor','Scissor','Y','2016-06-27 00:00:00',100000,'Y','Y',100300,1,'ACTIVE','PC','',250.00,250.00,'2017-04-23 00:00:00',''),(303,'Scoop 1/4','Scoop 1/4','Y','2016-06-27 00:00:00',100000,'Y','Y',100301,1,'ACTIVE','PC','',50.00,50.00,'2017-04-24 00:00:00',''),(304,'Scotch Bright Scotch Bright','Scotch Bright Scotch Bright','Y','2016-06-27 00:00:00',100000,'Y','Y',100302,1,'ACTIVE','PC','',24.00,24.00,'2017-04-25 00:00:00',''),(305,'Service Tray Kenford','Service Tray Kenford','Y','2016-06-27 00:00:00',100000,'Y','Y',100303,1,'ACTIVE','PC','',115.78,115.78,'2017-04-26 00:00:00',''),(306,'Sharpner Natraj','Sharpner Natraj','Y','2016-06-27 00:00:00',100000,'Y','Y',100304,1,'ACTIVE','PC','',2.66,2.66,'2017-04-27 00:00:00',''),(307,'Shikanji Masala Jain','Shikanji Masala Jain','Y','2016-06-27 00:00:00',100000,'Y','Y',100305,1,'ACTIVE','KG','',174.96,174.96,'2017-04-28 00:00:00',''),(308,'Sicilian Chicken Filling Chaayos','Sicilian Chicken Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100306,1,'ACTIVE','PC','',29.13,29.13,'2017-04-29 00:00:00',''),(309,'Sign Board - Wet Floor','Sign Board - Wet Floor','Y','2016-06-27 00:00:00',100000,'Y','Y',100307,1,'ACTIVE','PC','',600.00,600.00,'2017-04-30 00:00:00',''),(310,'Silver Cutting','Silver Cutting','Y','2016-06-27 00:00:00',100000,'Y','Y',100308,1,'ACTIVE','PC','',50.00,50.00,'2017-05-01 00:00:00',''),(311,'Silver Foil Hindustan','Silver Foil Hindustan','Y','2016-06-27 00:00:00',100000,'Y','Y',100309,1,'ACTIVE','PC','',245.00,245.00,'2017-05-02 00:00:00',''),(312,'Sink Mesh Jali','Sink Mesh Jali','Y','2016-06-27 00:00:00',100000,'Y','Y',100310,1,'ACTIVE','PC','',2500.00,2500.00,'2017-05-03 00:00:00',''),(313,'Soap Dispenser','Soap Dispenser','Y','2016-06-27 00:00:00',100000,'Y','Y',100311,1,'ACTIVE','PC','',750.00,750.00,'2017-05-04 00:00:00',''),(314,'Soda Kingfisher','Soda Kingfisher','Y','2016-06-27 00:00:00',100000,'Y','Y',100312,1,'ACTIVE','L','',10.00,10.00,'2017-05-05 00:00:00',''),(315,'Spinach Corn Cheese Filling Chaayos','Spinach Corn Cheese Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100313,1,'ACTIVE','PC','',13.12,13.12,'2017-05-06 00:00:00',''),(316,'Spiral Note Book Falcon','Spiral Note Book Falcon','Y','2016-06-27 00:00:00',100000,'Y','Y',100314,1,'ACTIVE','PC','',18.05,18.05,'2017-05-07 00:00:00',''),(317,'Sponge Wipe Scotch Bright','Sponge Wipe Scotch Bright','Y','2016-06-27 00:00:00',100000,'Y','Y',100315,1,'ACTIVE','PC','',35.00,35.00,'2017-05-08 00:00:00',''),(318,'Spoon Ss Big Solo','Spoon Ss Big Solo','Y','2016-06-27 00:00:00',100000,'Y','Y',100316,1,'ACTIVE','PC','',6.14,6.14,'2017-05-09 00:00:00',''),(319,'Spoon Ss Small Solo','Spoon Ss Small Solo','Y','2016-06-27 00:00:00',100000,'Y','Y',100317,1,'ACTIVE','PC','',12.63,12.63,'2017-05-10 00:00:00',''),(320,'Spray','Spray','Y','2016-06-27 00:00:00',100000,'Y','Y',100318,1,'ACTIVE','PC','',75.00,75.00,'2017-05-11 00:00:00',''),(321,'Spray Bottle Conta','Spray Bottle Conta','Y','2016-06-27 00:00:00',100000,'Y','Y',100319,1,'ACTIVE','PC','',73.13,73.13,'2017-05-12 00:00:00',''),(322,'Sprinkler','Sprinkler','Y','2016-06-27 00:00:00',100000,'Y','Y',100320,1,'ACTIVE','PC','',150.00,150.00,'2017-05-13 00:00:00',''),(323,'Square Dustbin Supreme','Square Dustbin Supreme','Y','2016-06-27 00:00:00',100000,'Y','Y',100321,1,'ACTIVE','PC','',1713.60,1713.60,'2017-05-14 00:00:00',''),(324,'Squeeze Bottle','Squeeze Bottle','Y','2016-06-27 00:00:00',100000,'Y','Y',100322,1,'ACTIVE','PC','',50.00,50.00,'2017-05-15 00:00:00',''),(325,'Steel Scale','Steel Scale','Y','2016-06-27 00:00:00',100000,'Y','Y',100323,1,'ACTIVE','PC','',19.50,19.50,'2017-05-16 00:00:00',''),(326,'Steel Scruber Scotch Bright','Steel Scruber Scotch Bright','Y','2016-06-27 00:00:00',100000,'Y','Y',100324,1,'ACTIVE','PC','',15.00,15.00,'2017-05-17 00:00:00',''),(327,'Sticker - Non Veg Nutech','Sticker - Non Veg Nutech','Y','2016-06-27 00:00:00',100000,'Y','Y',100325,1,'ACTIVE','PC','',0.04,0.04,'2017-05-18 00:00:00',''),(328,'Sticker - Veg Nutech','Sticker - Veg Nutech','Y','2016-06-27 00:00:00',100000,'Y','Y',100326,1,'ACTIVE','PC','',0.40,0.40,'2017-05-19 00:00:00',''),(329,'Stirrer','Stirrer','Y','2016-06-27 00:00:00',100000,'Y','Y',100327,1,'ACTIVE','PC','',25.00,25.00,'2017-05-20 00:00:00',''),(330,'Straw','Straw','Y','2016-06-27 00:00:00',100000,'Y','Y',100328,1,'ACTIVE','PC','',23.00,23.00,'2017-05-21 00:00:00',''),(331,'Strawberry Filling Chaayos','Strawberry Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100329,1,'ACTIVE','PC','',20.71,20.71,'2017-05-22 00:00:00',''),(332,'Sugar','Sugar','Y','2016-06-27 00:00:00',100000,'Y','Y',100330,1,'ACTIVE','KG','',41.00,41.00,'2017-05-23 00:00:00',''),(333,'Sugar Free Sachet','Sugar Free Sachet','Y','2016-06-27 00:00:00',100000,'Y','Y',100331,1,'ACTIVE','SACHET','',1.05,1.05,'2017-05-24 00:00:00',''),(334,'Sugar Sachet - Brown','Sugar Sachet - Brown','Y','2016-06-27 00:00:00',100000,'Y','Y',100332,1,'ACTIVE','SACHET','',0.59,0.59,'2017-05-25 00:00:00',''),(335,'Sugar Sachet - White','Sugar Sachet - White','Y','2016-06-27 00:00:00',100000,'Y','Y',100333,1,'ACTIVE','SACHET','',0.39,0.39,'2017-05-26 00:00:00',''),(336,'Chutney Jar Sujata','Chutney Jar Sujata','Y','2016-06-27 00:00:00',100000,'Y','Y',100334,1,'ACTIVE','PC','',335.00,335.00,'2017-05-27 00:00:00',''),(337,'Grinder Jaar Sujata','Grinder Jaar Sujata','Y','2016-06-27 00:00:00',100000,'Y','N',100335,1,'ACTIVE','PC','',3487.42,3487.42,'2017-05-28 00:00:00',''),(338,'Suma Det Taski','Suma Det Taski','Y','2016-06-27 00:00:00',100000,'Y','Y',100336,1,'ACTIVE','L','',89.72,89.72,'2017-05-29 00:00:00',''),(339,'Suma Grill Taski','Suma Grill Taski','Y','2016-06-27 00:00:00',100000,'Y','Y',100337,1,'ACTIVE','PC','',1244.60,1244.60,'2017-05-30 00:00:00',''),(340,'Tab Samsung','Tab Samsung','Y','2016-06-27 00:00:00',100000,'N','Y',100338,1,'ACTIVE','PC','',15285.00,15285.00,'2017-05-31 00:00:00',''),(341,'Tab Stand 10 Inches','Tab Stand 10 Inches','Y','2016-06-27 00:00:00',100000,'Y','Y',100339,1,'ACTIVE','PC','',500.00,500.00,'2017-06-01 00:00:00',''),(342,'Take Away Bag - Large','Take Away Bag - Large','Y','2016-06-27 00:00:00',100000,'Y','Y',100340,1,'ACTIVE','PC','',3.82,3.82,'2017-06-02 00:00:00',''),(343,'Take Away Bag - Small','Take Away Bag - Small','Y','2016-06-27 00:00:00',100000,'Y','Y',100341,1,'ACTIVE','PC','',1.55,1.55,'2017-06-03 00:00:00',''),(344,'Tape Dispenser','Tape Dispenser','Y','2016-06-27 00:00:00',100000,'Y','Y',100342,1,'ACTIVE','PC','',43.92,43.92,'2017-06-04 00:00:00',''),(345,'Chai Delivery Box 400 Ml Nutech ','Chai delivery Box 400 Ml Nutech ','Y','2016-06-27 00:00:00',100000,'Y','Y',100076,1,'ACTIVE','PC','',8.25,8.25,'2017-06-05 00:00:00',''),(346,'Tea Fun Box Nutech ','Tea Fun Box Nutech ','Y','2016-06-27 00:00:00',100000,'Y','Y',100344,1,'ACTIVE','PC','',4.75,4.75,'2017-06-06 00:00:00',''),(347,'Tea Pan 6 L Vinod','Tea Pan 6 L Vinod','Y','2016-06-27 00:00:00',100000,'Y','Y',100345,1,'ACTIVE','PC','',846.00,846.00,'2017-06-07 00:00:00',''),(348,'Chai Delivery Pouch 1 L Ppl','Chai delivery Pouch 1 L Ppl','Y','2016-06-27 00:00:00',100000,'Y','Y',100346,1,'ACTIVE','PC','',9.54,9.54,'2017-06-08 00:00:00',''),(349,'Chai Delivery Pouch 400 Ml Ppl','Chai delivery Pouch 400 Ml Ppl','Y','2016-06-27 00:00:00',100000,'Y','Y',100347,1,'ACTIVE','PC','',6.89,6.89,'2017-06-09 00:00:00',''),(350,'Tea Saucepan Medium Vinod','Tea Saucepan Medium Vinod','Y','2016-06-27 00:00:00',100000,'Y','Y',100348,1,'ACTIVE','PC','',1319.88,1319.88,'2017-06-10 00:00:00',''),(351,'Tea Saucepan Small Vinod','Tea Saucepan Small Vinod','Y','2016-06-27 00:00:00',100000,'Y','Y',100349,1,'ACTIVE','PC','',967.98,967.98,'2017-06-11 00:00:00',''),(352,'Tea Strainer Large Local','Tea Strainer Large Local','Y','2016-06-27 00:00:00',100000,'Y','Y',100350,1,'ACTIVE','PC','',127.50,127.50,'2017-06-12 00:00:00',''),(353,'Tea Strainer Medium Local','Tea Strainer Medium Local','Y','2016-06-27 00:00:00',100000,'Y','Y',100351,1,'ACTIVE','PC','',55.00,55.00,'2017-06-13 00:00:00',''),(354,'Tea Strainer Small Local','Tea Strainer Small Local','Y','2016-06-27 00:00:00',100000,'Y','Y',100352,1,'ACTIVE','PC','',76.50,76.50,'2017-06-14 00:00:00',''),(355,'Temperature And Humidity Meter','Temperature And Humidity Meter','Y','2016-06-27 00:00:00',100000,'Y','Y',100353,1,'ACTIVE','PC','',320.00,320.00,'2017-06-15 00:00:00',''),(356,'Tempered Glass - Customer Screen Tab Samsung','Tempered Glass - Customer Screen Tab Samsung','Y','2016-06-27 00:00:00',100000,'N','N',100354,1,'ACTIVE','PC','',357.00,357.00,'2017-06-16 00:00:00',''),(357,'Tempered Glass - Workstation Tab Samsung','Tempered Glass - Workstation Tab Samsung','Y','2016-06-27 00:00:00',100000,'N','N',100355,1,'ACTIVE','PC','',100.00,100.00,'2017-06-17 00:00:00',''),(358,'Thandai Guruji','Thandai Guruji','Y','2016-06-27 00:00:00',100000,'Y','Y',100356,1,'ACTIVE','L','',235.80,235.80,'2017-06-18 00:00:00',''),(359,'Timer Sunmex','Timer Sunmex','Y','2016-06-27 00:00:00',100000,'Y','Y',100357,1,'ACTIVE','PC','',350.00,350.00,'2017-06-19 00:00:00',''),(360,'Tissue Holder','Tissue Holder','Y','2016-06-27 00:00:00',100000,'Y','Y',100358,1,'ACTIVE','PC','',150.00,150.00,'2017-06-20 00:00:00',''),(361,'Tissue Paper','Tissue Paper','Y','2016-06-27 00:00:00',100000,'Y','Y',100359,1,'ACTIVE','PC','',15.00,15.00,'2017-06-21 00:00:00',''),(362,'Toilet Brush','Toilet Brush','Y','2016-06-27 00:00:00',100000,'Y','Y',100360,1,'ACTIVE','PC','',35.00,35.00,'2017-06-22 00:00:00',''),(363,'Toilet Roll','Toilet Roll','Y','2016-06-27 00:00:00',100000,'Y','Y',100361,1,'ACTIVE','PC','',22.00,22.00,'2017-06-23 00:00:00',''),(364,'Tomato','Tomato','Y','2016-06-27 00:00:00',100000,'Y','Y',100362,1,'ACTIVE','KG','',25.00,25.00,'2017-06-24 00:00:00',''),(365,'Tong','Tong','Y','2016-06-27 00:00:00',100000,'Y','Y',100363,1,'ACTIVE','PC','',60.00,60.00,'2017-06-25 00:00:00',''),(366,'Tool Kit','Tool Kit','Y','2016-06-27 00:00:00',100000,'Y','Y',100364,1,'ACTIVE','PC','',424.50,424.50,'2017-06-26 00:00:00',''),(367,'Tray Mat Nutech ','Tray Mat Nutech ','Y','2016-06-27 00:00:00',100000,'Y','Y',100365,1,'ACTIVE','PC','',1.07,1.07,'2017-06-27 00:00:00',''),(368,'T-Shirt Large','T-Shirt Large','Y','2016-06-27 00:00:00',100000,'Y','Y',100366,1,'ACTIVE','PC','',473.00,473.00,'2017-06-28 00:00:00',''),(369,'T-Shirt Medium','T-Shirt Medium','Y','2016-06-27 00:00:00',100000,'Y','Y',100367,1,'ACTIVE','PC','',473.00,473.00,'2017-06-29 00:00:00',''),(370,'T-Shirt Small','T-Shirt Small','Y','2016-06-27 00:00:00',100000,'Y','Y',100368,1,'ACTIVE','PC','',473.00,473.00,'2017-06-30 00:00:00',''),(371,'T-Shirt Xl','T-Shirt Xl','Y','2016-06-27 00:00:00',100000,'Y','Y',100369,1,'ACTIVE','PC','',265.00,265.00,'2017-07-01 00:00:00',''),(372,'Tulsi','Tulsi','Y','2016-06-27 00:00:00',100000,'Y','Y',100370,1,'ACTIVE','KG','',454.75,454.75,'2017-07-02 00:00:00',''),(373,'Tulsi Adrak Chai Can Chaayos','Tulsi Adrak Chai Can Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100371,1,'ACTIVE','PC','',2.31,2.31,'2017-07-03 00:00:00',''),(374,'Uniform Cap','Uniform Cap','Y','2016-06-27 00:00:00',100000,'Y','Y',100372,1,'ACTIVE','PC','',142.00,142.00,'2017-07-04 00:00:00',''),(375,'Ups Intex','Ups Intex','Y','2016-06-27 00:00:00',100000,'N','N',100373,1,'ACTIVE','PC','',1400.00,1400.00,'2017-07-05 00:00:00',''),(376,'Vada Pav Filling Chaayos','Vada Pav Filling Chaayos','Y','2016-06-27 00:00:00',100000,'Y','Y',100374,1,'ACTIVE','PC','',2.13,2.13,'2017-07-06 00:00:00',''),(377,'Visi Cooler -110 L Elen Pro','Visi Cooler -110 L Elen Pro','Y','2016-06-27 00:00:00',100000,'N','N',100375,1,'ACTIVE','PC','',13500.00,13500.00,'2017-07-07 00:00:00',''),(378,'Vitamix Rinsomatic Vitamix','Vitamix Rinsomatic Vitamix','Y','2016-06-27 00:00:00',100000,'N','N',100376,1,'ACTIVE','PC','',18000.00,18000.00,'2017-07-08 00:00:00',''),(379,'Washroom Cleaning Checklist','Washroom Cleaning Checklist','Y','2016-06-27 00:00:00',100000,'Y','Y',100377,1,'ACTIVE','PC','',45.00,45.00,'2017-07-09 00:00:00',''),(380,'Wastage Basket','Wastage Basket','Y','2016-06-27 00:00:00',100000,'Y','Y',100378,1,'ACTIVE','PC','',200.00,200.00,'2017-07-10 00:00:00',''),(381,'Water Boiler 20 Ltr Pradeep','Water Boiler 20 Ltr Pradeep','Y','2016-06-27 00:00:00',100000,'Y','N',100379,1,'ACTIVE','PC','',7475.00,7475.00,'2017-07-11 00:00:00',''),(382,'Water Bottle Poly Pro','Water Bottle Poly Pro','Y','2016-06-27 00:00:00',100000,'Y','N',100380,1,'ACTIVE','PC','',24.00,24.00,'2017-07-12 00:00:00',''),(383,'Water Cooler Blue Star','Water Cooler Blue Star','Y','2016-06-27 00:00:00',100000,'N','N',100381,1,'ACTIVE','PC','',18000.00,18000.00,'2017-07-13 00:00:00',''),(384,'Water Tank 1000 L Sintex','Water Tank 1000 L Sintex','Y','2016-06-27 00:00:00',100000,'N','N',100382,1,'ACTIVE','PC','',7288.89,7288.89,'2017-07-14 00:00:00',''),(385,'Weighing Scale Cas','Weighing Scale Cas','Y','2016-06-27 00:00:00',100000,'N','N',100383,1,'ACTIVE','PC','',5200.00,5200.00,'2017-07-15 00:00:00',''),(386,'Wet Mop Head','Wet Mop Head','Y','2016-06-27 00:00:00',100000,'Y','N',100384,1,'ACTIVE','PC','',140.00,140.00,'2017-07-16 00:00:00',''),(387,'Wet Mop Rod','Wet Mop Rod','Y','2016-06-27 00:00:00',100000,'Y','N',100385,1,'ACTIVE','PC','',100.00,100.00,'2017-07-17 00:00:00',''),(388,'White Board Duster Omex','White Board Duster Omex','Y','2016-06-27 00:00:00',100000,'Y','N',100386,1,'ACTIVE','PC','',44.44,44.44,'2017-07-18 00:00:00',''),(389,'White Board Marker Artline','White Board Marker Artline','Y','2016-06-27 00:00:00',100000,'Y','N',100387,1,'ACTIVE','PC','',22.50,22.50,'2017-07-19 00:00:00',''),(390,'White Envelop Medium','White Envelop Medium','Y','2016-06-27 00:00:00',100000,'Y','N',100388,1,'ACTIVE','PC','',1.99,1.99,'2017-07-20 00:00:00',''),(391,'White Envelop Small','White Envelop Small','Y','2016-06-27 00:00:00',100000,'Y','N',100389,1,'ACTIVE','PC','',1.25,1.25,'2017-07-21 00:00:00',''),(392,'Wiper','Wiper','Y','2016-06-27 00:00:00',100000,'Y','N',100390,1,'ACTIVE','PC','',70.00,70.00,'2017-07-22 00:00:00',''),(393,'Wiper Small','Wiper Small','Y','2016-06-27 00:00:00',100000,'Y','N',100391,1,'ACTIVE','PC','',145.00,145.00,'2017-07-23 00:00:00',''),(394,'Wooden Cake Board','Wooden Cake Board','Y','2016-06-27 00:00:00',100000,'Y','N',100392,1,'ACTIVE','PC','',306.00,306.00,'2017-07-24 00:00:00',''),(395,'Writing Pad Flacon','Writing Pad Flacon','Y','2016-06-27 00:00:00',100000,'Y','N',100393,1,'ACTIVE','PC','',19.00,19.00,'2017-07-25 00:00:00',''),(396,'Badam Pista Cookies Pieces Chaayos','Badam pista cookies pieces Chaayos','Y','2016-06-27 00:00:00',100000,'Y','N',100395,1,'ACTIVE','PC','',8.16,8.16,'2017-07-26 00:00:00',''),(397,'Oatmeal Cookies Pieces Chaayos','Oatmeal cookies pieces Chaayos','Y','2016-06-27 00:00:00',100000,'Y','N',100396,1,'ACTIVE','PC','',9.18,9.18,'2017-07-27 00:00:00',''),(398,'Jeera Cookies Pieces Chaayos','Jeera cookies pieces Chaayos','Y','2016-06-27 00:00:00',100000,'Y','N',100018,1,'ACTIVE','PC','',4.66,4.66,'2017-07-28 00:00:00',''),(399,'Wooden Table Top - Regular','Wooden Table Top - Regular','Y','2016-06-27 00:00:00',100000,'N','N',100397,-1,'ACTIVE','PC','',1050.00,1050.00,'2017-07-29 00:00:00',''),(400,'Wooden Table Top - Big','Wooden Table Top - Big','Y','2016-06-27 00:00:00',100000,'N','N',100398,-1,'ACTIVE','PC','',1950.00,1950.00,'2017-07-30 00:00:00',''),(401,'Community Table - 8 Feet','Community Table - 8 Feet','Y','2016-06-27 00:00:00',100000,'N','N',100399,-1,'ACTIVE','PC','',15000.00,15000.00,'2017-07-31 00:00:00',''),(402,'Community Table - 10 Feet','Community Table - 10 Feet','Y','2016-06-27 00:00:00',100000,'N','N',100400,-1,'ACTIVE','PC','',17000.00,17000.00,'2017-08-01 00:00:00',''),(403,'Community Table - Custom Made','Community Table - Custom made','Y','2016-06-27 00:00:00',100000,'N','N',100401,-1,'ACTIVE','PC','',17000.00,17000.00,'2017-08-02 00:00:00',''),(404,'High Table - 10 Feet','High Table - 10 feet','Y','2016-06-27 00:00:00',100000,'N','N',100402,-1,'ACTIVE','PC','',13500.00,13500.00,'2017-08-03 00:00:00',''),(405,'High Table - 8 Feet','High Table - 8 feet','Y','2016-06-27 00:00:00',100000,'N','N',100403,-1,'ACTIVE','PC','',12000.00,12000.00,'2017-08-04 00:00:00',''),(406,'High Table - Custom Made - 5\'10\"X22\"X42\"','High Table - Custom Made - 5\'10\"x22\"x42\"','Y','2016-06-27 00:00:00',100000,'N','N',100404,-1,'ACTIVE','PC','',12000.00,12000.00,'2017-08-05 00:00:00',''),(407,'High Table - Custom Made - 5\'10\"X16\"X42\"','High Table - Custom Made - 5\'10\"x16\"x42\"','Y','2016-06-27 00:00:00',100000,'N','N',100405,-1,'ACTIVE','PC','',11000.00,11000.00,'2017-08-06 00:00:00',''),(408,'Ms Chair - Crossback - Upholtsered With Brown Canvas - Green','MS Chair - CrossBack - upholtsered with Brown Canvas - Green','Y','2016-06-27 00:00:00',100000,'N','N',100406,-1,'ACTIVE','PC','',2200.00,2200.00,'2017-08-07 00:00:00',''),(409,'Ms Chair - Crossback - Upholtsered With Brown Canvas - White','MS Chair - CrossBack - upholtsered with Brown Canvas - White','Y','2016-06-27 00:00:00',100000,'N','N',100407,-1,'ACTIVE','PC','',2200.00,2200.00,'2017-08-08 00:00:00',''),(410,'Ms Chair - Wire Mesh - Teal','MS Chair - Wire Mesh - Teal','Y','2016-06-27 00:00:00',100000,'N','N',100408,-1,'ACTIVE','PC','',2300.00,2300.00,'2017-08-09 00:00:00',''),(411,'Low Seater Chindi Weave','Low Seater Chindi weave','Y','2016-06-27 00:00:00',100000,'N','N',100409,-1,'ACTIVE','PC','',1650.00,1650.00,'2017-08-10 00:00:00',''),(412,'Chindi Chair','Chindi Chair','Y','2016-06-27 00:00:00',100000,'N','N',100410,-1,'ACTIVE','PC','',2300.00,2300.00,'2017-08-11 00:00:00',''),(413,'Jute Chair','Jute chair','Y','2016-06-27 00:00:00',100000,'N','N',100411,-1,'ACTIVE','PC','',1850.00,1850.00,'2017-08-12 00:00:00',''),(414,'Sofa Chair','Sofa Chair','Y','2016-06-27 00:00:00',100000,'N','N',100412,-1,'ACTIVE','PC','',7800.00,7800.00,'2017-08-13 00:00:00',''),(415,'Jute Stool - Tall - Black Color Base (Powder Coated)','Jute stool - tall - Black color base (powder coated)','Y','2016-06-27 00:00:00',100000,'N','N',100413,-1,'ACTIVE','PC','',1300.00,1300.00,'2017-08-14 00:00:00',''),(416,'Jute Stool - Black Color Base (Powder Coated)','Jute Stool - Black color base (powder coated)','Y','2016-06-27 00:00:00',100000,'N','N',100414,-1,'ACTIVE','PC','',1150.00,1150.00,'2017-08-15 00:00:00',''),(417,'Wooden Bench With Back Support','Wooden Bench with back support','Y','2016-06-27 00:00:00',100000,'N','N',100415,-1,'ACTIVE','PC','',8600.00,8600.00,'2017-08-16 00:00:00',''),(418,'Wooden Bench','Wooden Bench','Y','2016-06-27 00:00:00',100000,'N','N',100416,-1,'ACTIVE','PC','',7000.00,7000.00,'2017-08-17 00:00:00',''),(419,'Wooden Running Table','Wooden Running Table','Y','2016-06-27 00:00:00',100000,'N','N',100417,-1,'ACTIVE','PC','',11500.00,11500.00,'2017-08-18 00:00:00',''),(420,'Canteen Table With Swivel Stools','Canteen table with swivel stools','Y','2016-06-27 00:00:00',100000,'N','N',100418,-1,'ACTIVE','PC','',24000.00,24000.00,'2017-08-19 00:00:00',''),(421,'Dustbin Cabinet','Dustbin cabinet','Y','2016-06-27 00:00:00',100000,'N','N',100419,-1,'ACTIVE','PC','',7200.00,7200.00,'2017-08-20 00:00:00',''),(422,'2 Seater Table With Wooden Wheels','2 Seater table with wooden wheels','Y','2016-06-27 00:00:00',100000,'N','N',100420,-1,'ACTIVE','PC','',7800.00,7800.00,'2017-08-21 00:00:00',''),(423,'School Bench And Table (4-Seater)','School Bench and table (4-seater)','Y','2016-06-27 00:00:00',100000,'N','N',100421,-1,'ACTIVE','PC','',17500.00,17500.00,'2017-08-22 00:00:00',''),(424,'Outdoor - Bench (2-Seater)','Outdoor - Bench (2-Seater)','Y','2016-06-27 00:00:00',100000,'N','N',100422,-1,'ACTIVE','PC','',4000.00,4000.00,'2017-08-23 00:00:00',''),(425,'Outdoor - Bench With Back (2-Seater)','Outdoor - Bench with back (2-Seater)','Y','2016-06-27 00:00:00',100000,'N','N',100423,-1,'ACTIVE','PC','',4800.00,4800.00,'2017-08-24 00:00:00',''),(426,'Outdoor - Table (4-Seater)','Outdoor - Table (4-Seater)','Y','2016-06-27 00:00:00',100000,'N','N',100424,-1,'ACTIVE','PC','',6800.00,6800.00,'2017-08-25 00:00:00',''),(427,'Wooden Table Top - Round 22\"','Wooden Table Top - Round 22\"','Y','2016-06-27 00:00:00',100000,'N','N',100425,-1,'ACTIVE','PC','',1050.00,1050.00,'2017-08-26 00:00:00',''),(428,'Wooden Table Top - Round 20\"','Wooden Table Top - Round 20\"','Y','2016-06-27 00:00:00',100000,'N','N',100426,-1,'ACTIVE','PC','',1050.00,1050.00,'2017-08-27 00:00:00',''),(429,'Community Table Herringbone Top - 6\'','Community Table Herringbone Top - 6\'','Y','2016-06-27 00:00:00',100000,'N','N',100427,-1,'ACTIVE','PC','',13500.00,13500.00,'2017-08-28 00:00:00',''),(430,'Community Table Herringbone Top - 7\'6\"','Community Table Herringbone Top - 7\'6\"','Y','2016-06-27 00:00:00',100000,'N','N',100428,-1,'ACTIVE','PC','',15000.00,15000.00,'2017-08-29 00:00:00',''),(431,'Double Seater Sofa - Multicolour Patch','Double seater sofa - multicolour patch','Y','2016-06-27 00:00:00',100000,'N','N',100429,-1,'ACTIVE','PC','',17500.00,17500.00,'2017-08-30 00:00:00',''),(432,'Coffee Table With Wheels','Coffee Table with Wheels','Y','2016-06-27 00:00:00',100000,'N','N',100430,-1,'ACTIVE','PC','',7800.00,7800.00,'2017-08-31 00:00:00',''),(433,'Wall Seating Table & Stool Set','Wall seating Table & Stool set','Y','2016-06-27 00:00:00',100000,'N','N',100431,-1,'ACTIVE','PC','',7500.00,7500.00,'2017-09-01 00:00:00',''),(434,'Chaayos \"C\" Logo 22\"','Chaayos \"C\" Logo 22\"','Y','2016-06-27 00:00:00',100000,'N','N',100432,-1,'ACTIVE','PC','',900.00,900.00,'2017-09-02 00:00:00',''),(435,'Chaayos 20\"','Chaayos 20\"','Y','2016-06-27 00:00:00',100000,'N','N',100433,-1,'ACTIVE','PC','',12000.00,12000.00,'2017-09-03 00:00:00',''),(436,'Experiments With Chai 4\"','Experiments with chai 4\"','Y','2016-06-27 00:00:00',100000,'N','N',100434,-1,'ACTIVE','PC','',3400.00,3400.00,'2017-09-04 00:00:00',''),(437,'Cup Cutout 18\"','Cup cutout 18\"','Y','2016-06-27 00:00:00',100000,'N','N',100435,-1,'ACTIVE','PC','',1800.00,1800.00,'2017-09-05 00:00:00',''),(438,'Wooden Crates For Planters','Wooden Crates for Planters','Y','2016-06-27 00:00:00',100000,'N','N',100436,-1,'ACTIVE','PC','',1150.00,1150.00,'2017-09-06 00:00:00',''),(439,'Batti Gul','Batti Gul','Y','2016-06-27 00:00:00',100000,'N','N',100437,-1,'ACTIVE','PC','',500.00,500.00,'2017-09-07 00:00:00',''),(440,'Cello Chair - Green','CELLO CHAIR - GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100438,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-08 00:00:00',''),(441,'Cello Chair - Brown','CELLO CHAIR - BROWN','Y','2016-06-27 00:00:00',100000,'N','N',100439,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-09 00:00:00',''),(442,'Cello Chair - Grey','CELLO CHAIR - GREY','Y','2016-06-27 00:00:00',100000,'N','N',100440,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-10 00:00:00',''),(443,'Cello Chair - Yellow','CELLO CHAIR - YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100441,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-11 00:00:00',''),(444,'Cello Chair - White','CELLO CHAIR - WHITE','Y','2016-06-27 00:00:00',100000,'N','N',100442,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-12 00:00:00',''),(445,'Cello Chair Wooden Hm Seat - Yellow','CELLO CHAIR WOODEN HM SEAT - YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100443,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-13 00:00:00',''),(446,'Cello Chair Wooden Hm Seat - Green','CELLO CHAIR WOODEN HM SEAT - GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100444,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-14 00:00:00',''),(447,'Cello Chair Wooden Hm Seat - White D/Green','CELLO CHAIR WOODEN HM SEAT - WHITE D/GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100445,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-15 00:00:00',''),(448,'Cello Chair Wooden Hm - Grey D/Green','CELLO CHAIR WOODEN HM - GREY D/GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100446,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-16 00:00:00',''),(449,'Cello Chair Wooden Hm - Brown','CELLO CHAIR WOODEN HM - BROWN','Y','2016-06-27 00:00:00',100000,'N','N',100447,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-17 00:00:00',''),(450,'Arm Chair Wooden Hm Seat - Red','ARM CHAIR WOODEN HM SEAT - RED','Y','2016-06-27 00:00:00',100000,'N','N',100448,-1,'ACTIVE','PC','',2200.00,2200.00,'2017-09-18 00:00:00',''),(451,'Arm Chair Wooden Hm Seat - White D/Green','ARM CHAIR WOODEN HM SEAT - WHITE D/GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100449,-1,'ACTIVE','PC','',2200.00,2200.00,'2017-09-19 00:00:00',''),(452,'Arm Chair Wooden Hm Seat - Brown','ARM CHAIR WOODEN HM SEAT - BROWN','Y','2016-06-27 00:00:00',100000,'N','N',100450,-1,'ACTIVE','PC','',2200.00,2200.00,'2017-09-20 00:00:00',''),(453,'Arm Chair Wooden Hm Seat - Green','ARM CHAIR WOODEN HM SEAT - GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100451,-1,'ACTIVE','PC','',2200.00,2200.00,'2017-09-21 00:00:00',''),(454,'Arm Chair Wooden Hm Seat - Yellow','ARM CHAIR WOODEN HM SEAT - YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100452,-1,'ACTIVE','PC','',2200.00,2200.00,'2017-09-22 00:00:00',''),(455,'Cross Back Chair Green','CROSS BACK CHAIR GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100453,-1,'ACTIVE','PC','',1700.00,1700.00,'2017-09-23 00:00:00',''),(456,'Cross Back Chair Brown','CROSS BACK CHAIR BROWN','Y','2016-06-27 00:00:00',100000,'N','N',100454,-1,'ACTIVE','PC','',1700.00,1700.00,'2017-09-24 00:00:00',''),(457,'Cross Back Chair White','CROSS BACK CHAIR WHITE','Y','2016-06-27 00:00:00',100000,'N','N',100455,-1,'ACTIVE','PC','',1700.00,1700.00,'2017-09-25 00:00:00',''),(458,'Cinema Chair Crackle Green','CINEMA CHAIR CRACKLE GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100456,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-26 00:00:00',''),(459,'Cinema Chair Crackle Orange','CINEMA CHAIR CRACKLE ORANGE','Y','2016-06-27 00:00:00',100000,'N','N',100457,-1,'ACTIVE','PC','',1500.00,1500.00,'2017-09-27 00:00:00',''),(460,'Folding Iron Chair ','FOLDING IRON CHAIR ','Y','2016-06-27 00:00:00',100000,'N','N',100458,-1,'ACTIVE','PC','',1650.00,1650.00,'2017-09-28 00:00:00',''),(461,'Cello High Chair Wooden Hm Seat - Yellow','CELLO HIGH CHAIR WOODEN HM SEAT - YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100459,-1,'ACTIVE','PC','',3100.00,3100.00,'2017-09-29 00:00:00',''),(462,'Cello High Chair Wooden Hm Seat - White','CELLO HIGH CHAIR WOODEN HM SEAT - WHITE','Y','2016-06-27 00:00:00',100000,'N','N',100460,-1,'ACTIVE','PC','',3100.00,3100.00,'2017-09-30 00:00:00',''),(463,'Chindi High Chair','CHINDI HIGH CHAIR','Y','2016-06-27 00:00:00',100000,'N','N',100461,-1,'ACTIVE','PC','',3000.00,3000.00,'2017-10-01 00:00:00',''),(464,'Chindi Bench','CHINDI BENCH','Y','2016-06-27 00:00:00',100000,'N','N',100462,-1,'ACTIVE','PC','',3500.00,3500.00,'2017-10-02 00:00:00',''),(465,'Cello Stool W-Seat Green','CELLO STOOL W-SEAT GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100463,-1,'ACTIVE','PC','',1200.00,1200.00,'2017-10-03 00:00:00',''),(466,'Cello Stool W-Seat Yellow','CELLO STOOL W-SEAT YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100464,-1,'ACTIVE','PC','',1200.00,1200.00,'2017-10-04 00:00:00',''),(467,'Cello Stool W-Seat White','CELLO STOOL W-SEAT WHITE','Y','2016-06-27 00:00:00',100000,'N','N',100465,-1,'ACTIVE','PC','',1200.00,1200.00,'2017-10-05 00:00:00',''),(468,'Tea Strainer Light','TEA STRAINER LIGHT','Y','2016-06-27 00:00:00',100000,'N','N',100466,-1,'ACTIVE','PC','',3500.00,3500.00,'2017-10-06 00:00:00',''),(469,'Wire Kettle Lamp Green','WIRE KETTLE LAMP GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100467,-1,'ACTIVE','PC','',800.00,800.00,'2017-10-07 00:00:00',''),(470,'Wire Kettle Lamp Yellow','WIRE KETTLE LAMP YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100468,-1,'ACTIVE','PC','',800.00,800.00,'2017-10-08 00:00:00',''),(471,'Yellow Spoke Light - Large','YELLOW SPOKE LIGHT - LARGE','Y','2016-06-27 00:00:00',100000,'N','N',100469,-1,'ACTIVE','PC','',550.00,550.00,'2017-10-09 00:00:00',''),(472,'Yellow Spoke Light - Small','YELLOW SPOKE LIGHT - SMALL','Y','2016-06-27 00:00:00',100000,'N','N',100470,-1,'ACTIVE','PC','',550.00,550.00,'2017-10-10 00:00:00',''),(473,'Rect Cheeka No Hanlde Black (7 Hole)','RECT CHEEKA NO HANLDE BLACK (7 HOLE)','Y','2016-06-27 00:00:00',100000,'N','N',100471,-1,'ACTIVE','PC','',120.00,120.00,'2017-10-11 00:00:00',''),(474,'Rect Cheeka No Hanlde Yellow (7 Hole)','RECT CHEEKA NO HANLDE YELLOW (7 HOLE)','Y','2016-06-27 00:00:00',100000,'N','N',100472,-1,'ACTIVE','PC','',120.00,120.00,'2017-10-12 00:00:00',''),(475,'Rect Cheeka W/Handle Black','RECT CHEEKA W/HANDLE BLACK','Y','2016-06-27 00:00:00',100000,'N','N',100473,-1,'ACTIVE','PC','',120.00,120.00,'2017-10-13 00:00:00',''),(476,'Rect Cheeka W/Handle Yellow','RECT CHEEKA W/HANDLE YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100474,-1,'ACTIVE','PC','',120.00,120.00,'2017-10-14 00:00:00',''),(477,'Round Cheeka Black','ROUND CHEEKA BLACK','Y','2016-06-27 00:00:00',100000,'N','N',100475,-1,'ACTIVE','PC','',150.00,150.00,'2017-10-15 00:00:00',''),(478,'S/6 Colored Glasses','S/6 COLORED GLASSES','Y','2016-06-27 00:00:00',100000,'N','N',100476,-1,'ACTIVE','PC','',180.00,180.00,'2017-10-16 00:00:00',''),(479,'Wall Scone Green','WALL SCONE GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100477,-1,'ACTIVE','PC','',900.00,900.00,'2017-10-17 00:00:00',''),(480,'Nipple','NIPPLE','Y','2016-06-27 00:00:00',100000,'N','N',100478,-1,'ACTIVE','PC','',5.00,5.00,'2017-10-18 00:00:00',''),(481,'Naaki Black','NAAKI BLACK','Y','2016-06-27 00:00:00',100000,'N','N',100479,-1,'ACTIVE','PC','',10.00,10.00,'2017-10-19 00:00:00',''),(482,'Key Rings Black','KEY RINGS BLACK','Y','2016-06-27 00:00:00',100000,'N','N',100480,-1,'ACTIVE','PC','',5.00,5.00,'2017-10-20 00:00:00',''),(483,'Connector Black','CONNECTOR BLACK','Y','2016-06-27 00:00:00',100000,'N','N',100481,-1,'ACTIVE','PC','',5.00,5.00,'2017-10-21 00:00:00',''),(484,'Pipe 6\" Black','PIPE 6\" BLACK','Y','2016-06-27 00:00:00',100000,'N','N',100482,-1,'ACTIVE','PC','',15.00,15.00,'2017-10-22 00:00:00',''),(485,'Pipe 12\" Black','PIPE 12\" BLACK','Y','2016-06-27 00:00:00',100000,'N','N',100483,-1,'ACTIVE','PC','',20.00,20.00,'2017-10-23 00:00:00',''),(486,'Pipe 24\" Black','PIPE 24\" BLACK','Y','2016-06-27 00:00:00',100000,'N','N',100484,-1,'ACTIVE','PC','',25.00,25.00,'2017-10-24 00:00:00',''),(487,'Small Kettle Red','SMALL KETTLE RED','Y','2016-06-27 00:00:00',100000,'N','N',100485,-1,'ACTIVE','PC','',350.00,350.00,'2017-10-25 00:00:00',''),(488,'Small Kettle White','SMALL KETTLE WHITE','Y','2016-06-27 00:00:00',100000,'N','N',100486,-1,'ACTIVE','PC','',350.00,350.00,'2017-10-26 00:00:00',''),(489,'Small Kettle Grey','SMALL KETTLE GREY','Y','2016-06-27 00:00:00',100000,'N','N',100487,-1,'ACTIVE','PC','',350.00,350.00,'2017-10-27 00:00:00',''),(490,'Small Kettle Yellow','SMALL KETTLE YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100488,-1,'ACTIVE','PC','',350.00,350.00,'2017-10-28 00:00:00',''),(491,'Small Kettle Green','SMALL KETTLE GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100489,-1,'ACTIVE','PC','',350.00,350.00,'2017-10-29 00:00:00',''),(492,'Large Kettle Yellow','LARGE KETTLE YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100490,-1,'ACTIVE','PC','',500.00,500.00,'2017-10-30 00:00:00',''),(493,'Large Kettle Green','LARGE KETTLE GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100491,-1,'ACTIVE','PC','',500.00,500.00,'2017-10-31 00:00:00',''),(494,'Large Kettle Lamp Yellow','LARGE KETTLE LAMP YELLOW','Y','2016-06-27 00:00:00',100000,'N','N',100492,-1,'ACTIVE','PC','',500.00,500.00,'2017-11-01 00:00:00',''),(495,'Large Kettle Lamp Green','LARGE KETTLE LAMP GREEN','Y','2016-06-27 00:00:00',100000,'N','N',100493,-1,'ACTIVE','PC','',500.00,500.00,'2017-11-02 00:00:00',''),(496,'Large Kettle Lamp Nickle','LARGE KETTLE LAMP NICKLE','Y','2016-06-27 00:00:00',100000,'N','N',100494,-1,'ACTIVE','PC','',500.00,500.00,'2017-11-03 00:00:00',''),(497,'Wooden Wheel Small 12\"','Wooden wheel small 12\"','Y','2016-06-27 00:00:00',100000,'N','N',100509,-1,'ACTIVE','PC','',2000.00,2000.00,'2017-11-04 00:00:00',''),(498,'Wooden Wheel Big 18\"','Wooden wheel big 18\"','Y','2016-06-27 00:00:00',100000,'N','N',100510,-1,'ACTIVE','PC','',2000.00,2000.00,'2017-11-05 00:00:00',''),(499,'Coupons Free Chai','Coupons Free chai','Y','2016-06-27 00:00:00',100000,'N','N',100495,-1,'ACTIVE','PC','',0.48,0.48,'2017-11-06 00:00:00',''),(500,'Sampling Tray','Sampling Tray','Y','2016-06-27 00:00:00',100000,'N','N',100496,-1,'ACTIVE','PC','',1600.00,1600.00,'2017-11-07 00:00:00',''),(501,'Corporate Gift Box','Corporate Gift Box','Y','2016-06-27 00:00:00',100000,'N','N',100497,-1,'ACTIVE','PC','',24.00,24.00,'2017-11-08 00:00:00',''),(502,'Corporate Gift Box Sleeve','Corporate Gift Box Sleeve','Y','2016-06-27 00:00:00',100000,'N','N',100498,-1,'ACTIVE','PC','',40.00,40.00,'2017-11-09 00:00:00',''),(503,'Fliers A 5','Fliers A 5','Y','2016-06-27 00:00:00',100000,'N','N',100499,-1,'ACTIVE','PC','',0.35,0.35,'2017-11-10 00:00:00',''),(504,'Roll-Out Standee','Roll-Out Standee','Y','2016-06-27 00:00:00',100000,'N','N',100500,-1,'ACTIVE','PC','',900.00,900.00,'2017-11-11 00:00:00',''),(505,'Iron Sunboard Hanger','Iron Sunboard Hanger','Y','2016-06-27 00:00:00',100000,'N','N',100501,-1,'ACTIVE','PC','',1900.00,1900.00,'2017-11-12 00:00:00',''),(506,'Tent Card','Tent Card','Y','2016-06-27 00:00:00',100000,'N','N',100503,-1,'ACTIVE','PC','',15.00,15.00,'2017-11-13 00:00:00',''),(507,'Sunboard','Sunboard','Y','2016-06-27 00:00:00',100000,'N','N',100504,-1,'ACTIVE','PC','',900.00,900.00,'2017-11-14 00:00:00',''),(508,'Poster A3','Poster A3','Y','2016-06-27 00:00:00',100000,'N','N',100505,-1,'ACTIVE','PC','',18.00,18.00,'2017-11-15 00:00:00',''),(509,'Umbrella Chaayos','Umbrella','Y','2016-06-27 00:00:00',100000,'N','N',100507,-1,'ACTIVE','PC','',800.00,800.00,'2017-11-16 00:00:00',''),(510,'Coupons Twitter','Coupons Twitter','Y','2016-06-27 00:00:00',100000,'N','N',100495,-1,'ACTIVE','PC','',0.55,0.55,'2017-11-17 00:00:00',''),(511,'Poster A2','Poster A2','Y','2016-06-27 00:00:00',100000,'N','N',100505,-1,'ACTIVE','PC','',120.00,120.00,'2017-11-18 00:00:00',''),(512,'Poster Combo','Poster Combo','Y','2016-06-27 00:00:00',100000,'N','N',100505,-1,'ACTIVE','PC','',0.20,0.20,'2017-11-19 00:00:00',''),(513,'Sticker Bubble','Sticker Bubble','Y','2016-06-27 00:00:00',100000,'N','N',100506,-1,'ACTIVE','PC','',0.25,0.25,'2017-11-20 00:00:00',''),(514,'Arm Chair Upholstery Wooden Hm Seat - Red','Arm CHAIR upholstery WOODEN HM SEAT - RED','Y','2016-06-27 00:00:00',100000,'N','N',100508,-1,'ACTIVE','PC','',1150.00,1150.00,'2017-11-21 00:00:00',''),(515,'AC Superia Plus K+ Split','AC Superia Plus K+ Split','Y','2016-06-27 00:00:00',100000,'N','N',100009,1,'ACTIVE','PC','',1150.00,1150.00,'2017-11-22 00:00:00',''),(516,'Handheld Menu - High Street','Handheld Menu - High Street','Y','2016-06-27 00:00:00',100000,'N','N',100502,-1,'ACTIVE','PC','',160.00,160.00,'2017-11-23 00:00:00',''),(517,'Handheld Menu - Business Park','Handheld Menu - Business Park','Y','2016-06-27 00:00:00',100000,'N','N',100502,-1,'ACTIVE','PC','',160.00,160.00,'2017-11-24 00:00:00',''),(518,'Food Sticker - Non Veg','Food Sticker - Non Veg','Y','2016-06-27 00:00:00',100000,'Y','Y',100503,1,'ACTIVE','PC','',0.40,0.40,'2017-11-25 00:00:00',''),(519,'Food Sticker - Veg','Food Sticker - Veg','Y','2016-06-27 00:00:00',100000,'Y','Y',100504,1,'ACTIVE','PC','',0.40,0.40,'2017-11-26 00:00:00','');
/*!40000 ALTER TABLE `SKU_DEFINITION` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SKU_PACKAGING_MAPPING`
--

DROP TABLE IF EXISTS `SKU_PACKAGING_MAPPING`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SKU_PACKAGING_MAPPING` (
  `SKU_PACKAGING_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PACKAGING_ID` int(11) NOT NULL,
  `SKU_ID` int(11) NOT NULL,
  `MAPPING_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`SKU_PACKAGING_MAPPING_ID`),
  UNIQUE KEY `PACKAGING_ID` (`PACKAGING_ID`,`SKU_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=520 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SKU_PACKAGING_MAPPING`
--

LOCK TABLES `SKU_PACKAGING_MAPPING` WRITE;
/*!40000 ALTER TABLE `SKU_PACKAGING_MAPPING` DISABLE KEYS */;
INSERT INTO `SKU_PACKAGING_MAPPING` VALUES (1,3,1,'ACTIVE'),(2,3,2,'ACTIVE'),(3,3,3,'ACTIVE'),(4,3,4,'ACTIVE'),(5,3,5,'ACTIVE'),(6,3,6,'ACTIVE'),(7,3,7,'ACTIVE'),(8,2,8,'ACTIVE'),(9,3,9,'ACTIVE'),(10,2,10,'ACTIVE'),(11,3,11,'ACTIVE'),(12,3,12,'ACTIVE'),(13,3,13,'ACTIVE'),(14,3,14,'ACTIVE'),(15,3,15,'ACTIVE'),(16,3,16,'ACTIVE'),(17,2,17,'ACTIVE'),(18,2,18,'ACTIVE'),(19,3,19,'ACTIVE'),(20,3,20,'ACTIVE'),(21,3,21,'ACTIVE'),(22,2,22,'ACTIVE'),(23,3,23,'ACTIVE'),(24,3,24,'ACTIVE'),(25,3,25,'ACTIVE'),(26,3,26,'ACTIVE'),(27,3,27,'ACTIVE'),(28,2,28,'ACTIVE'),(29,2,29,'ACTIVE'),(30,2,30,'ACTIVE'),(31,2,31,'ACTIVE'),(32,3,32,'ACTIVE'),(33,3,33,'ACTIVE'),(34,2,34,'ACTIVE'),(35,2,35,'ACTIVE'),(36,3,36,'ACTIVE'),(37,3,37,'ACTIVE'),(38,3,38,'ACTIVE'),(39,3,39,'ACTIVE'),(40,3,40,'ACTIVE'),(41,3,41,'ACTIVE'),(42,3,42,'ACTIVE'),(43,2,43,'ACTIVE'),(44,3,44,'ACTIVE'),(45,3,45,'ACTIVE'),(46,3,46,'ACTIVE'),(47,3,47,'ACTIVE'),(48,3,48,'ACTIVE'),(49,3,49,'ACTIVE'),(50,3,50,'ACTIVE'),(51,3,51,'ACTIVE'),(52,3,52,'ACTIVE'),(53,3,53,'ACTIVE'),(54,2,54,'ACTIVE'),(55,3,55,'ACTIVE'),(56,3,56,'ACTIVE'),(57,3,57,'ACTIVE'),(58,3,58,'ACTIVE'),(59,3,59,'ACTIVE'),(60,3,60,'ACTIVE'),(61,3,61,'ACTIVE'),(62,3,62,'ACTIVE'),(63,3,63,'ACTIVE'),(64,3,64,'ACTIVE'),(65,2,65,'ACTIVE'),(66,3,66,'ACTIVE'),(67,2,67,'ACTIVE'),(68,2,68,'ACTIVE'),(69,3,69,'ACTIVE'),(70,3,70,'ACTIVE'),(71,3,71,'ACTIVE'),(72,3,72,'ACTIVE'),(73,3,73,'ACTIVE'),(74,3,74,'ACTIVE'),(75,3,75,'ACTIVE'),(76,2,76,'ACTIVE'),(77,3,77,'ACTIVE'),(78,3,78,'ACTIVE'),(79,2,79,'ACTIVE'),(80,3,80,'ACTIVE'),(81,3,81,'ACTIVE'),(82,3,82,'ACTIVE'),(83,2,83,'ACTIVE'),(84,1,84,'ACTIVE'),(85,3,85,'ACTIVE'),(86,3,86,'ACTIVE'),(87,2,87,'ACTIVE'),(88,1,88,'ACTIVE'),(89,3,89,'ACTIVE'),(90,3,90,'ACTIVE'),(91,3,91,'ACTIVE'),(92,2,92,'ACTIVE'),(93,3,93,'ACTIVE'),(94,1,94,'ACTIVE'),(95,99,95,'ACTIVE'),(96,3,96,'ACTIVE'),(97,3,97,'ACTIVE'),(98,3,98,'ACTIVE'),(99,3,99,'ACTIVE'),(100,3,100,'ACTIVE'),(101,3,101,'ACTIVE'),(102,3,102,'ACTIVE'),(103,3,103,'ACTIVE'),(104,3,104,'ACTIVE'),(105,3,105,'ACTIVE'),(106,3,106,'ACTIVE'),(107,3,107,'ACTIVE'),(108,3,108,'ACTIVE'),(109,3,109,'ACTIVE'),(110,3,110,'ACTIVE'),(111,3,111,'ACTIVE'),(112,3,112,'ACTIVE'),(113,2,113,'ACTIVE'),(114,3,114,'ACTIVE'),(115,3,115,'ACTIVE'),(116,3,116,'ACTIVE'),(117,3,117,'ACTIVE'),(118,1,118,'ACTIVE'),(119,3,119,'ACTIVE'),(120,2,120,'ACTIVE'),(121,3,121,'ACTIVE'),(122,3,122,'ACTIVE'),(123,2,123,'ACTIVE'),(124,1,124,'ACTIVE'),(125,3,125,'ACTIVE'),(126,3,126,'ACTIVE'),(127,3,127,'ACTIVE'),(128,3,128,'ACTIVE'),(129,3,129,'ACTIVE'),(130,3,130,'ACTIVE'),(131,3,131,'ACTIVE'),(132,3,132,'ACTIVE'),(133,3,133,'ACTIVE'),(134,3,134,'ACTIVE'),(135,3,135,'ACTIVE'),(136,3,136,'ACTIVE'),(137,3,137,'ACTIVE'),(138,3,138,'ACTIVE'),(139,3,139,'ACTIVE'),(140,3,140,'ACTIVE'),(141,2,141,'ACTIVE'),(142,3,142,'ACTIVE'),(143,3,143,'ACTIVE'),(144,2,144,'ACTIVE'),(145,3,145,'ACTIVE'),(146,3,146,'ACTIVE'),(147,3,147,'ACTIVE'),(148,3,148,'ACTIVE'),(149,3,149,'ACTIVE'),(150,3,150,'ACTIVE'),(151,3,151,'ACTIVE'),(152,3,152,'ACTIVE'),(153,3,153,'ACTIVE'),(154,3,154,'ACTIVE'),(155,3,155,'ACTIVE'),(156,3,156,'ACTIVE'),(157,3,157,'ACTIVE'),(158,3,158,'ACTIVE'),(159,2,159,'ACTIVE'),(160,2,160,'ACTIVE'),(161,3,161,'ACTIVE'),(162,3,162,'ACTIVE'),(163,3,163,'ACTIVE'),(164,3,164,'ACTIVE'),(165,3,165,'ACTIVE'),(166,2,166,'ACTIVE'),(167,3,167,'ACTIVE'),(168,2,168,'ACTIVE'),(169,2,169,'ACTIVE'),(170,3,170,'ACTIVE'),(171,2,171,'ACTIVE'),(172,3,172,'ACTIVE'),(173,3,173,'ACTIVE'),(174,3,174,'ACTIVE'),(175,1,175,'ACTIVE'),(176,3,176,'ACTIVE'),(177,2,177,'ACTIVE'),(178,3,178,'ACTIVE'),(179,3,179,'ACTIVE'),(180,2,180,'ACTIVE'),(181,2,181,'ACTIVE'),(182,2,182,'ACTIVE'),(183,3,183,'ACTIVE'),(184,3,184,'ACTIVE'),(185,3,185,'ACTIVE'),(186,2,186,'ACTIVE'),(187,3,187,'ACTIVE'),(188,3,188,'ACTIVE'),(189,3,189,'ACTIVE'),(190,3,190,'ACTIVE'),(191,2,191,'ACTIVE'),(192,3,192,'ACTIVE'),(193,3,193,'ACTIVE'),(194,2,194,'ACTIVE'),(195,3,195,'ACTIVE'),(196,3,196,'ACTIVE'),(197,112,197,'ACTIVE'),(198,3,198,'ACTIVE'),(199,3,199,'ACTIVE'),(200,3,200,'ACTIVE'),(201,3,201,'ACTIVE'),(202,3,202,'ACTIVE'),(203,3,203,'ACTIVE'),(204,3,204,'ACTIVE'),(205,2,205,'ACTIVE'),(206,2,206,'ACTIVE'),(207,3,207,'ACTIVE'),(208,2,208,'ACTIVE'),(209,3,209,'ACTIVE'),(210,99,210,'ACTIVE'),(211,2,211,'ACTIVE'),(212,3,212,'ACTIVE'),(213,3,213,'ACTIVE'),(214,3,214,'ACTIVE'),(215,3,215,'ACTIVE'),(216,3,216,'ACTIVE'),(217,3,217,'ACTIVE'),(218,3,218,'ACTIVE'),(219,3,219,'ACTIVE'),(220,3,220,'ACTIVE'),(221,3,221,'ACTIVE'),(222,3,222,'ACTIVE'),(223,3,223,'ACTIVE'),(224,3,224,'ACTIVE'),(225,3,225,'ACTIVE'),(226,3,226,'ACTIVE'),(227,3,227,'ACTIVE'),(228,3,228,'ACTIVE'),(229,3,229,'ACTIVE'),(230,3,230,'ACTIVE'),(231,3,231,'ACTIVE'),(232,3,232,'ACTIVE'),(233,3,233,'ACTIVE'),(234,1,234,'ACTIVE'),(235,3,235,'ACTIVE'),(236,2,236,'ACTIVE'),(237,2,237,'ACTIVE'),(238,2,238,'ACTIVE'),(239,3,239,'ACTIVE'),(240,3,240,'ACTIVE'),(241,1,241,'ACTIVE'),(242,1,242,'ACTIVE'),(243,3,243,'ACTIVE'),(244,3,244,'ACTIVE'),(245,3,245,'ACTIVE'),(246,3,246,'ACTIVE'),(247,2,247,'ACTIVE'),(248,3,248,'ACTIVE'),(249,3,249,'ACTIVE'),(250,3,250,'ACTIVE'),(251,3,251,'ACTIVE'),(252,3,252,'ACTIVE'),(253,3,253,'ACTIVE'),(254,2,254,'ACTIVE'),(255,2,255,'ACTIVE'),(256,2,256,'ACTIVE'),(257,3,257,'ACTIVE'),(258,3,258,'ACTIVE'),(259,3,259,'ACTIVE'),(260,3,260,'ACTIVE'),(261,3,261,'ACTIVE'),(262,3,262,'ACTIVE'),(263,3,263,'ACTIVE'),(264,3,264,'ACTIVE'),(265,3,265,'ACTIVE'),(266,3,266,'ACTIVE'),(267,3,267,'ACTIVE'),(268,3,268,'ACTIVE'),(269,3,269,'ACTIVE'),(270,3,270,'ACTIVE'),(271,3,271,'ACTIVE'),(272,2,272,'ACTIVE'),(273,3,273,'ACTIVE'),(274,3,274,'ACTIVE'),(275,2,275,'ACTIVE'),(276,3,276,'ACTIVE'),(277,3,277,'ACTIVE'),(278,3,278,'ACTIVE'),(279,3,279,'ACTIVE'),(280,3,280,'ACTIVE'),(281,3,281,'ACTIVE'),(282,3,282,'ACTIVE'),(283,3,283,'ACTIVE'),(284,3,284,'ACTIVE'),(285,3,285,'ACTIVE'),(286,3,286,'ACTIVE'),(287,1,287,'ACTIVE'),(288,1,288,'ACTIVE'),(289,1,289,'ACTIVE'),(290,2,290,'ACTIVE'),(291,3,291,'ACTIVE'),(292,3,292,'ACTIVE'),(293,3,293,'ACTIVE'),(294,1,294,'ACTIVE'),(295,1,295,'ACTIVE'),(296,3,296,'ACTIVE'),(297,3,297,'ACTIVE'),(298,3,298,'ACTIVE'),(299,3,299,'ACTIVE'),(300,112,300,'ACTIVE'),(301,2,301,'ACTIVE'),(302,3,302,'ACTIVE'),(303,3,303,'ACTIVE'),(304,3,304,'ACTIVE'),(305,3,305,'ACTIVE'),(306,3,306,'ACTIVE'),(307,2,307,'ACTIVE'),(308,3,308,'ACTIVE'),(309,3,309,'ACTIVE'),(310,3,310,'ACTIVE'),(311,3,311,'ACTIVE'),(312,3,312,'ACTIVE'),(313,3,313,'ACTIVE'),(314,1,314,'ACTIVE'),(315,3,315,'ACTIVE'),(316,3,316,'ACTIVE'),(317,3,317,'ACTIVE'),(318,3,318,'ACTIVE'),(319,3,319,'ACTIVE'),(320,3,320,'ACTIVE'),(321,3,321,'ACTIVE'),(322,3,322,'ACTIVE'),(323,3,323,'ACTIVE'),(324,3,324,'ACTIVE'),(325,3,325,'ACTIVE'),(326,3,326,'ACTIVE'),(327,3,327,'ACTIVE'),(328,3,328,'ACTIVE'),(329,3,329,'ACTIVE'),(330,3,330,'ACTIVE'),(331,3,331,'ACTIVE'),(332,2,332,'ACTIVE'),(333,112,333,'ACTIVE'),(334,112,334,'ACTIVE'),(335,112,335,'ACTIVE'),(336,3,336,'ACTIVE'),(337,3,337,'ACTIVE'),(338,1,338,'ACTIVE'),(339,3,339,'ACTIVE'),(340,3,340,'ACTIVE'),(341,3,341,'ACTIVE'),(342,3,342,'ACTIVE'),(343,3,343,'ACTIVE'),(344,3,344,'ACTIVE'),(345,3,345,'ACTIVE'),(346,3,346,'ACTIVE'),(347,3,347,'ACTIVE'),(348,3,348,'ACTIVE'),(349,3,349,'ACTIVE'),(350,3,350,'ACTIVE'),(351,3,351,'ACTIVE'),(352,3,352,'ACTIVE'),(353,3,353,'ACTIVE'),(354,3,354,'ACTIVE'),(355,3,355,'ACTIVE'),(356,3,356,'ACTIVE'),(357,3,357,'ACTIVE'),(358,1,358,'ACTIVE'),(359,3,359,'ACTIVE'),(360,3,360,'ACTIVE'),(361,3,361,'ACTIVE'),(362,3,362,'ACTIVE'),(363,3,363,'ACTIVE'),(364,2,364,'ACTIVE'),(365,3,365,'ACTIVE'),(366,3,366,'ACTIVE'),(367,3,367,'ACTIVE'),(368,3,368,'ACTIVE'),(369,3,369,'ACTIVE'),(370,3,370,'ACTIVE'),(371,3,371,'ACTIVE'),(372,56,372,'ACTIVE'),(373,3,373,'ACTIVE'),(374,3,374,'ACTIVE'),(375,3,375,'ACTIVE'),(376,3,376,'ACTIVE'),(377,3,377,'ACTIVE'),(378,3,378,'ACTIVE'),(379,3,379,'ACTIVE'),(380,3,380,'ACTIVE'),(381,3,381,'ACTIVE'),(382,3,382,'ACTIVE'),(383,3,383,'ACTIVE'),(384,3,384,'ACTIVE'),(385,3,385,'ACTIVE'),(386,3,386,'ACTIVE'),(387,3,387,'ACTIVE'),(388,3,388,'ACTIVE'),(389,3,389,'ACTIVE'),(390,3,390,'ACTIVE'),(391,3,391,'ACTIVE'),(392,3,392,'ACTIVE'),(393,3,393,'ACTIVE'),(394,3,394,'ACTIVE'),(395,3,395,'ACTIVE'),(396,77,396,'ACTIVE'),(397,77,397,'ACTIVE'),(398,77,398,'ACTIVE'),(399,3,399,'ACTIVE'),(400,3,400,'ACTIVE'),(401,3,401,'ACTIVE'),(402,3,402,'ACTIVE'),(403,3,403,'ACTIVE'),(404,3,404,'ACTIVE'),(405,3,405,'ACTIVE'),(406,3,406,'ACTIVE'),(407,3,407,'ACTIVE'),(408,3,408,'ACTIVE'),(409,3,409,'ACTIVE'),(410,3,410,'ACTIVE'),(411,3,411,'ACTIVE'),(412,3,412,'ACTIVE'),(413,3,413,'ACTIVE'),(414,3,414,'ACTIVE'),(415,3,415,'ACTIVE'),(416,3,416,'ACTIVE'),(417,3,417,'ACTIVE'),(418,3,418,'ACTIVE'),(419,3,419,'ACTIVE'),(420,3,420,'ACTIVE'),(421,3,421,'ACTIVE'),(422,3,422,'ACTIVE'),(423,3,423,'ACTIVE'),(424,3,424,'ACTIVE'),(425,3,425,'ACTIVE'),(426,3,426,'ACTIVE'),(427,3,427,'ACTIVE'),(428,3,428,'ACTIVE'),(429,3,429,'ACTIVE'),(430,3,430,'ACTIVE'),(431,3,431,'ACTIVE'),(432,3,432,'ACTIVE'),(433,3,433,'ACTIVE'),(434,3,434,'ACTIVE'),(435,3,435,'ACTIVE'),(436,3,436,'ACTIVE'),(437,3,437,'ACTIVE'),(438,3,438,'ACTIVE'),(439,3,439,'ACTIVE'),(440,3,440,'ACTIVE'),(441,3,441,'ACTIVE'),(442,3,442,'ACTIVE'),(443,3,443,'ACTIVE'),(444,3,444,'ACTIVE'),(445,3,445,'ACTIVE'),(446,3,446,'ACTIVE'),(447,3,447,'ACTIVE'),(448,3,448,'ACTIVE'),(449,3,449,'ACTIVE'),(450,3,450,'ACTIVE'),(451,3,451,'ACTIVE'),(452,3,452,'ACTIVE'),(453,3,453,'ACTIVE'),(454,3,454,'ACTIVE'),(455,3,455,'ACTIVE'),(456,3,456,'ACTIVE'),(457,3,457,'ACTIVE'),(458,3,458,'ACTIVE'),(459,3,459,'ACTIVE'),(460,3,460,'ACTIVE'),(461,3,461,'ACTIVE'),(462,3,462,'ACTIVE'),(463,3,463,'ACTIVE'),(464,3,464,'ACTIVE'),(465,3,465,'ACTIVE'),(466,3,466,'ACTIVE'),(467,3,467,'ACTIVE'),(468,3,468,'ACTIVE'),(469,3,469,'ACTIVE'),(470,3,470,'ACTIVE'),(471,3,471,'ACTIVE'),(472,3,472,'ACTIVE'),(473,3,473,'ACTIVE'),(474,3,474,'ACTIVE'),(475,3,475,'ACTIVE'),(476,3,476,'ACTIVE'),(477,3,477,'ACTIVE'),(478,3,478,'ACTIVE'),(479,3,479,'ACTIVE'),(480,3,480,'ACTIVE'),(481,3,481,'ACTIVE'),(482,3,482,'ACTIVE'),(483,3,483,'ACTIVE'),(484,3,484,'ACTIVE'),(485,3,485,'ACTIVE'),(486,3,486,'ACTIVE'),(487,3,487,'ACTIVE'),(488,3,488,'ACTIVE'),(489,3,489,'ACTIVE'),(490,3,490,'ACTIVE'),(491,3,491,'ACTIVE'),(492,3,492,'ACTIVE'),(493,3,493,'ACTIVE'),(494,3,494,'ACTIVE'),(495,3,495,'ACTIVE'),(496,3,496,'ACTIVE'),(497,3,497,'ACTIVE'),(498,3,498,'ACTIVE'),(499,3,499,'ACTIVE'),(500,3,500,'ACTIVE'),(501,3,501,'ACTIVE'),(502,3,502,'ACTIVE'),(503,3,503,'ACTIVE'),(504,3,504,'ACTIVE'),(505,3,505,'ACTIVE'),(506,3,506,'ACTIVE'),(507,3,507,'ACTIVE'),(508,3,508,'ACTIVE'),(509,3,509,'ACTIVE'),(510,3,510,'ACTIVE'),(511,3,511,'ACTIVE'),(512,3,512,'ACTIVE'),(513,3,513,'ACTIVE'),(514,3,514,'ACTIVE'),(515,3,515,'ACTIVE'),(516,3,516,'ACTIVE'),(517,3,517,'ACTIVE'),(518,3,518,'ACTIVE'),(519,3,519,'ACTIVE');
/*!40000 ALTER TABLE `SKU_PACKAGING_MAPPING` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `STOCK_INVENTORY`
--

DROP TABLE IF EXISTS `STOCK_INVENTORY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `STOCK_INVENTORY` (
  `STOCKING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `EVENT_TYPE` varchar(30) NOT NULL,
  `PRODUCT_ID` int(11) NOT NULL,
  `UNIT_ID` int(11) NOT NULL,
  `BUSINESS_DATE` date DEFAULT NULL,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `GENERATED_BY` int(11) DEFAULT NULL,
  `OPENING_STOCK` decimal(10,2) NOT NULL DEFAULT '0.00',
  `EXPECTED_CLOSING_VALUE` decimal(10,2) DEFAULT '0.00',
  `CLOSING_STOCK` decimal(10,2) DEFAULT '0.00',
  `VARIANCE` decimal(10,2) DEFAULT '0.00',
  `UOM` varchar(30) NOT NULL,
  `COMMENT` varchar(200) DEFAULT NULL,
  `STATUS` varchar(45) DEFAULT NULL,
  `LAST_EVENT_ID` int(11) DEFAULT NULL,
  `CURRENT_EVENT_ID` int(11) DEFAULT NULL,
  PRIMARY KEY (`STOCKING_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `STOCK_INVENTORY`
--

LOCK TABLES `STOCK_INVENTORY` WRITE;
/*!40000 ALTER TABLE `STOCK_INVENTORY` DISABLE KEYS */;
/*!40000 ALTER TABLE `STOCK_INVENTORY` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SUB_CATEGORY_DEFINITION`
--

DROP TABLE IF EXISTS `SUB_CATEGORY_DEFINITION`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SUB_CATEGORY_DEFINITION` (
  `SUB_CATEGORY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SUB_CATEGORY_NAME` varchar(30) NOT NULL,
  `SUB_CATEGORY_CODE` varchar(30) NOT NULL,
  `SUB_CATEGORY_DESCRIPTION` varchar(30) DEFAULT NULL,
  `SUB_CATEGORY_STATUS` varchar(30) NOT NULL,
  `LINKED_CATEGORY_ID` int(11) NOT NULL,
  PRIMARY KEY (`SUB_CATEGORY_ID`),
  KEY `LINKED_CATEGORY_ID` (`LINKED_CATEGORY_ID`),
  CONSTRAINT `SUB_CATEGORY_DEFINITION_ibfk_1` FOREIGN KEY (`LINKED_CATEGORY_ID`) REFERENCES `CATEGORY_DEFINITION` (`CATEGORY_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SUB_CATEGORY_DEFINITION`
--

LOCK TABLES `SUB_CATEGORY_DEFINITION` WRITE;
/*!40000 ALTER TABLE `SUB_CATEGORY_DEFINITION` DISABLE KEYS */;
INSERT INTO `SUB_CATEGORY_DEFINITION` VALUES (1,'Bakery','BAKERY','Bakery','ACTIVE',1),(2,'Tea','TEA','Tea','ACTIVE',1),(3,'Dairy','DAIRY','Dairy','ACTIVE',1),(4,'Disposables','DIPOSABLES','Disposables','ACTIVE',1),(5,'Dry Grocery','DRY_GROCERY','Dry Grocery','ACTIVE',1),(6,'Fruits','FRUITS','Fruits','ACTIVE',1),(7,'Meat','MEAT','Meat','ACTIVE',1),(8,'Others','OTHERS','Others','ACTIVE',1),(9,'Vegetables','VEGETABLES','Vegetables','ACTIVE',1),(10,'Cutlery','CUTLERY','Cutlery','ACTIVE',2),(11,'Equipment','EQUIPMENT','Equipment','ACTIVE',2),(12,'Stationary','STATIONERY','Stationary','ACTIVE',2),(13,'Uniform','UNIFORM','Uniform','ACTIVE',2),(14,'Utility','UTILITY','Utility','ACTIVE',2),(15,'Marketing','MARKETING','Marketing','ACTIVE',2),(16,'Kitchen equipment','KITCHEN_EQUIPMENT','Kitchen equipment','ACTIVE',3),(17,'IT','IT','IT','ACTIVE',3),(18,'Office Equipment ','OFFICE_EQUIPMENT','Office Equipment ','ACTIVE',3),(19,'Furniture','FURNITURE','Furniture','ACTIVE',3),(20,'Semi Finished','SEMI_FINISHED','Semi Finished','ACTIVE',4);
/*!40000 ALTER TABLE `SUB_CATEGORY_DEFINITION` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `TRANSFER_ORDER`
--

DROP TABLE IF EXISTS `TRANSFER_ORDER`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `TRANSFER_ORDER` (
  `TRANSFER_ORDER_ID` int(11) NOT NULL AUTO_INCREMENT,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `GENERATION_UNIT_ID` int(11) NOT NULL,
  `GENERATED_FOR_UNIT_ID` int(11) NOT NULL,
  `GENERATED_BY` int(11) NOT NULL,
  `TRANSFER_ORDER_STATUS` varchar(30) NOT NULL,
  `COMMENT` varchar(1000) DEFAULT NULL,
  `REQUEST_ORDER_ID` int(11) DEFAULT NULL,
  `PURCHASE_ORDER_ID` int(11) DEFAULT NULL,
  `INITIATION_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `LAST_UPDATE_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`TRANSFER_ORDER_ID`),
  KEY `REQUEST_ORDER_ID` (`REQUEST_ORDER_ID`),
  KEY `PURCHASE_ORDER_ID` (`PURCHASE_ORDER_ID`),
  CONSTRAINT `TRANSFER_ORDER_ibfk_1` FOREIGN KEY (`REQUEST_ORDER_ID`) REFERENCES `REQUEST_ORDER` (`REQUEST_ORDER_ID`),
  CONSTRAINT `TRANSFER_ORDER_ibfk_2` FOREIGN KEY (`PURCHASE_ORDER_ID`) REFERENCES `PURCHASE_ORDER` (`PURCHASE_ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `TRANSFER_ORDER`
--

LOCK TABLES `TRANSFER_ORDER` WRITE;
/*!40000 ALTER TABLE `TRANSFER_ORDER` DISABLE KEYS */;
INSERT INTO `TRANSFER_ORDER` VALUES (16,'2016-06-27 21:11:39',10000,10000,100000,'SETTLED','sending .03 short',85,16,'2016-06-27 21:11:39','2016-06-27 21:11:39'),(17,'2016-06-27 23:13:21',13001,10000,100000,'SETTLED',NULL,86,17,'2016-06-27 23:13:21','2016-06-27 23:13:21'),(18,'2016-06-27 23:41:23',10000,10000,100000,'SETTLED','hjkhkj',30,18,'2016-06-27 23:41:23','2016-06-27 23:41:23'),(19,'2016-06-27 23:49:19',10000,10000,100000,'CREATED',NULL,1,19,'2016-06-27 23:49:19','2016-06-27 23:49:19'),(20,'2016-06-27 23:55:46',10000,10000,100000,'CREATED',NULL,3,20,'2016-06-27 23:55:46','2016-06-27 23:55:46'),(21,'2016-06-27 23:56:06',10000,10000,100000,'SETTLED',NULL,8,21,'2016-06-27 23:56:06','2016-06-27 23:56:06'),(22,'2016-06-28 09:37:47',10000,10000,100000,'CREATED',NULL,5,22,'2016-06-28 09:37:47','2016-06-28 09:37:47'),(23,'2016-06-28 18:24:12',10006,10006,100000,'SETTLED','Checked',87,23,'2016-06-28 18:24:12','2016-06-28 18:24:12');
/*!40000 ALTER TABLE `TRANSFER_ORDER` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `TRANSFER_ORDER_ITEM`
--

DROP TABLE IF EXISTS `TRANSFER_ORDER_ITEM`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `TRANSFER_ORDER_ITEM` (
  `TRANSFER_ORDER_ITEM_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SKU_ID` int(11) NOT NULL,
  `SKU_NAME` varchar(255) NOT NULL,
  `REQUESTED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `REQUESTED_ABSOLUTE_QUANTITY` decimal(10,2) DEFAULT NULL,
  `TRANSFERRED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `UNIT_OF_MEASURE` varchar(10) NOT NULL,
  `UNIT_PRICE` decimal(10,2) DEFAULT NULL,
  `NEGOTIATED_UNIT_PRICE` decimal(10,2) DEFAULT NULL,
  `TRANSFER_ORDER_ID` int(11) NOT NULL,
  `RECEIVED_QUANTITY` decimal(10,2) DEFAULT NULL,
  `REQUEST_ORDER_ITEM_ID` int(11) NOT NULL,
  `PURCHASE_ORDER_ITEM_ID` int(11) DEFAULT NULL,
  PRIMARY KEY (`TRANSFER_ORDER_ITEM_ID`),
  KEY `TRANSFER_ORDER_ID` (`TRANSFER_ORDER_ID`),
  KEY `REQUEST_ORDER_ITEM_ID` (`REQUEST_ORDER_ITEM_ID`),
  KEY `PURCHASE_ORDER_ITEM_ID` (`PURCHASE_ORDER_ITEM_ID`),
  CONSTRAINT `TRANSFER_ORDER_ITEM_ibfk_5` FOREIGN KEY (`PURCHASE_ORDER_ITEM_ID`) REFERENCES `PURCHASE_ORDER_ITEM` (`PURCHASE_ORDER_ITEM_ID`),
  CONSTRAINT `TRANSFER_ORDER_ITEM_ibfk_1` FOREIGN KEY (`TRANSFER_ORDER_ID`) REFERENCES `TRANSFER_ORDER` (`TRANSFER_ORDER_ID`),
  CONSTRAINT `TRANSFER_ORDER_ITEM_ibfk_2` FOREIGN KEY (`REQUEST_ORDER_ITEM_ID`) REFERENCES `REQUEST_ORDER_ITEM` (`REQUEST_ORDER_ITEM_ID`),
  CONSTRAINT `TRANSFER_ORDER_ITEM_ibfk_3` FOREIGN KEY (`PURCHASE_ORDER_ITEM_ID`) REFERENCES `PURCHASE_ORDER_ITEM` (`PURCHASE_ORDER_ITEM_ID`),
  CONSTRAINT `TRANSFER_ORDER_ITEM_ibfk_4` FOREIGN KEY (`REQUEST_ORDER_ITEM_ID`) REFERENCES `REQUEST_ORDER_ITEM` (`REQUEST_ORDER_ITEM_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `TRANSFER_ORDER_ITEM`
--

LOCK TABLES `TRANSFER_ORDER_ITEM` WRITE;
/*!40000 ALTER TABLE `TRANSFER_ORDER_ITEM` DISABLE KEYS */;
INSERT INTO `TRANSFER_ORDER_ITEM` VALUES (18,1,'Milk Amul',0.00,0.00,1.50,'L',39.50,NULL,16,0.00,159,18),(19,22,'Sachet - Desi Regular',0.00,0.00,20.00,'KG',0.00,NULL,17,0.00,160,19),(20,19,'Hot Cup 250 ML',0.00,0.00,1000.00,'PC',0.00,NULL,17,0.00,161,20),(21,1,'Milk Amul',0.00,0.00,1.00,'L',39.50,NULL,18,0.00,95,21),(22,1,'Milk Amul',0.00,0.00,10.00,'L',39.50,NULL,19,0.00,1,22),(23,1,'Milk Amul',0.00,0.00,2.50,'L',39.50,NULL,20,0.00,4,23),(24,8,'Pav',0.00,0.00,1.00,'PC',0.00,NULL,21,0.00,13,24),(25,8,'Pav',0.00,0.00,1.00,'PC',0.00,NULL,22,0.00,7,25),(26,1,'Milk Amul',0.00,0.00,12.00,'L',39.50,NULL,23,0.00,162,26),(27,8,'Pav',0.00,0.00,10.00,'PC',0.00,NULL,23,0.00,163,27);
/*!40000 ALTER TABLE `TRANSFER_ORDER_ITEM` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UNIT_CATEGORY`
--

DROP TABLE IF EXISTS `UNIT_CATEGORY`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UNIT_CATEGORY` (
  `CATEGORY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `CATEGORY_NAME` varchar(30) NOT NULL,
  `CATEGORY_CODE` varchar(30) NOT NULL,
  `CATEGORY_DESCRIPTION` varchar(255) DEFAULT NULL,
  `CATEGORY_STATUS` varchar(30) NOT NULL,
  PRIMARY KEY (`CATEGORY_ID`),
  UNIQUE KEY `CATEGORY_NAME` (`CATEGORY_NAME`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UNIT_CATEGORY`
--

LOCK TABLES `UNIT_CATEGORY` WRITE;
/*!40000 ALTER TABLE `UNIT_CATEGORY` DISABLE KEYS */;
INSERT INTO `UNIT_CATEGORY` VALUES (1,'Cafe','CAFE','Cafe','ACTIVE'),(2,'Delivery','DELIVERY','Delivery','ACTIVE'),(3,'Warehouse','WAREHOUSE','Warehouse','ACTIVE'),(4,'Kitchen','KITCHEN','Kitchen','ACTIVE'),(5,'Call Center','CALL_CENTER','Call Center','ACTIVE');
/*!40000 ALTER TABLE `UNIT_CATEGORY` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UNIT_DETAIL`
--

DROP TABLE IF EXISTS `UNIT_DETAIL`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UNIT_DETAIL` (
  `UNIT_ID` int(11) NOT NULL,
  `UNIT_NAME` varchar(255) NOT NULL,
  `UNIT_EMAIL` varchar(50) NOT NULL,
  `UNIT_STATUS` varchar(15) NOT NULL,
  `CATEGORY_ID` int(11) NOT NULL,
  `TIN_NUMBER` varchar(30) NOT NULL DEFAULT '0',
  PRIMARY KEY (`UNIT_ID`),
  KEY `CATEGORY_ID` (`CATEGORY_ID`),
  CONSTRAINT `UNIT_DETAIL_ibfk_1` FOREIGN KEY (`CATEGORY_ID`) REFERENCES `UNIT_CATEGORY` (`CATEGORY_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UNIT_DETAIL`
--

LOCK TABLES `UNIT_DETAIL` WRITE;
/*!40000 ALTER TABLE `UNIT_DETAIL` DISABLE KEYS */;
INSERT INTO `UNIT_DETAIL` VALUES (10000,'Good Earth City Centre','<EMAIL>','ACTIVE',1,'0'),(10001,'DLF Cyber City Building 5','<EMAIL>','ACTIVE',1,'0'),(10002,'Galaxy IT Park','<EMAIL>','ACTIVE',1,'0'),(10003,'Unitech Infospace','<EMAIL>','ACTIVE',1,'0'),(10004,'IHDP Business Park','<EMAIL>','ACTIVE',1,'0'),(10005,'Galleria Market','<EMAIL>','ACTIVE',1,'0'),(10006,'SDA Market','<EMAIL>','ACTIVE',1,'0'),(10007,'Punjabi Bagh','<EMAIL>','ACTIVE',1,'0'),(10008,'Hauz Khas Village','<EMAIL>','ACTIVE',1,'0'),(10009,'Chapel Road Bandra','<EMAIL>','ACTIVE',1,'0'),(10010,'Juhu','<EMAIL>','ACTIVE',1,'0'),(10011,'Grofers','<EMAIL>','ACTIVE',1,'0'),(10012,'New Friends Colony','<EMAIL>','ACTIVE',1,'0'),(10013,'Netaji Subhash Place','<EMAIL>','ACTIVE',1,'0'),(10014,'Google Office','<EMAIL>','ACTIVE',1,'0'),(11001,'Chai On Demand','<EMAIL>','ACTIVE',5,'0'),(12001,'DLF Phase 3','<EMAIL>','ACTIVE',2,'0'),(12002,'Udyog Vihar 5','<EMAIL>','ACTIVE',2,'0'),(12003,'Sector 45','<EMAIL>','ACTIVE',2,'0'),(12004,'MG Road','<EMAIL>','ACTIVE',2,'0'),(12005,'Noida Sector 9','<EMAIL>','ACTIVE',2,'0'),(12006,'Okhla Phase 4','<EMAIL>','ACTIVE',2,'0'),(12007,'Jasola','<EMAIL>','ACTIVE',2,'0'),(12008,'Jhandewalan','<EMAIL>','ACTIVE',2,'0'),(12009,'Lajpat Nagar','<EMAIL>','ACTIVE',2,'0'),(12010,'Ghitorni','<EMAIL>','ACTIVE',2,'0'),(12011,'Kirti Nagar','<EMAIL>','ACTIVE',1,'0'),(12012,'GIP Noida','<EMAIL>','ACTIVE',1,'0'),(12013,'KG Marg','<EMAIL>','ACTIVE',2,'0'),(12014,'Vatika Business Park','<EMAIL>','ACTIVE',1,'0'),(12015,'Connaught place- F Block','<EMAIL>','ACTIVE',1,'0'),(12016,'KG Marg - IRCTC','<EMAIL>','ACTIVE',1,'0'),(12017,'Zomato Office','<EMAIL>','ACTIVE',1,'0'),(12018,'DLF Mall of India','<EMAIL>','ACTIVE',1,'0'),(12019,'DLF Promenade Mall','<EMAIL>','ACTIVE',1,'0'),(12020,'Garden Galleria Mall','<EMAIL>','ACTIVE',1,'0'),(12021,'paytm Office','<EMAIL>','ACTIVE',1,'0'),(12022,'Sakinaka','<EMAIL>','ACTIVE',2,'0'),(12023,'Chai On Demand Mumbai','<EMAIL>','ACTIVE',5,'0'),(13001,'Warehouse Delhi','<EMAIL>','ACTIVE',3,'0'),(13002,'Warehouse Mumbai','<EMAIL>','ACTIVE',3,'0'),(14001,'Kitchen Delhi','<EMAIL>','ACTIVE',4,'0'),(14002,'Kitchen Mumbai','<EMAIL>','ACTIVE',4,'0');
/*!40000 ALTER TABLE `UNIT_DETAIL` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UNIT_PRODUCT_MAPPING`
--

DROP TABLE IF EXISTS `UNIT_PRODUCT_MAPPING`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UNIT_PRODUCT_MAPPING` (
  `UNIT_PRODUCT_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` int(11) NOT NULL,
  `PRODUCT_ID` int(11) NOT NULL,
  `VENDOR_ID` int(11) NOT NULL,
  `MAPPING_STATUS` varchar(15) NOT NULL,
  PRIMARY KEY (`UNIT_PRODUCT_MAPPING_ID`),
  UNIQUE KEY `UNIT_ID` (`UNIT_ID`,`PRODUCT_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UNIT_PRODUCT_MAPPING`
--

LOCK TABLES `UNIT_PRODUCT_MAPPING` WRITE;
/*!40000 ALTER TABLE `UNIT_PRODUCT_MAPPING` DISABLE KEYS */;
/*!40000 ALTER TABLE `UNIT_PRODUCT_MAPPING` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `VENDOR_DETAIL`
--

DROP TABLE IF EXISTS `VENDOR_DETAIL`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `VENDOR_DETAIL` (
  `VENDOR_ID` int(11) NOT NULL AUTO_INCREMENT,
  `VENDOR_NAME` varchar(255) NOT NULL,
  `VENDOR_DESCRIPTION` varchar(1000) DEFAULT NULL,
  `VENDOR_TIN` varchar(30) DEFAULT NULL,
  `VENDOR_COMPANY` varchar(255) DEFAULT NULL,
  `VENDOR_ADDRESS` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`VENDOR_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `VENDOR_DETAIL`
--

LOCK TABLES `VENDOR_DETAIL` WRITE;
/*!40000 ALTER TABLE `VENDOR_DETAIL` DISABLE KEYS */;
INSERT INTO `VENDOR_DETAIL` VALUES (1,'Polka','Polka','1111','No company','No address'),(2,'SANGWAN SALE CORPORATION','SANGWAN SALE CORPORATION','1111','No company','No address'),(3,'Harpal milk agency','harpal milk agency','1111','No company','No address'),(4,'Amul','Amul','1111','No company','No address'),(5,'Om Dairy','Om Dairy','1111','No company','No address'),(6,'Aditya juneja','Aditya juneja','1111','No company','No address'),(7,'Neelkanth enterprises','Neelkanth enterprises','1111','No company','No address'),(8,'Fresh & Fresh','Fresh & Fresh','1111','No company','No address'),(9,'Awasthi','Awasthi','1111','No company','No address'),(10,'Naveen Pawar Milk','Naveen Pawar Milk','1111','No company','No address'),(11,'Anmol Milk Distributors','Anmol Milk Distributors','1111','No company','No address'),(12,'Shiva sales corporation','Shiva sales corporation','1111','No company','No address'),(13,'Salim Milk Center','Salim Milk Center','1111','No company','No address'),(14,'Gutam Jee Dairy','Gutam Jee Dairy','1111','No company','No address'),(15,'Malik','Malik','1111','No company','No address'),(16,'Satish','Satish','1111','No company','No address'),(17,'RAM GEN. STORE','RAM GEN. STORE','1111','No company','No address'),(18,'ARADHY MILK AGENCY','ARADHY MILK AGENCY','1111','No company','No address'),(19,'Raja Ice','Raja Ice','1111','No company','No address'),(20,'Sonu dairy','Sonu dairy','1111','No company','No address'),(21,'Party Ice','Party Ice','1111','No company','No address'),(22,'Cash purchase','Cash purchase','1111','No company','No address');
/*!40000 ALTER TABLE `VENDOR_DETAIL` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `WASTAGE_EVENT`
--

DROP TABLE IF EXISTS `WASTAGE_EVENT`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `WASTAGE_EVENT` (
  `WASTAGE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `BUSINESS_DATE` date NOT NULL,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `STATUS` varchar(45) NOT NULL DEFAULT 'IN_ACTIVE',
  `PRODUCT_ID` int(11) NOT NULL,
  `QUANTITY` decimal(10,2) NOT NULL,
  `UNIT_ID` int(11) NOT NULL,
  `COMMENT` varchar(450) DEFAULT NULL,
  PRIMARY KEY (`WASTAGE_ID`),
  KEY `PRODUCT_ID` (`PRODUCT_ID`),
  CONSTRAINT `WASTAGE_EVENT_ibfk_1` FOREIGN KEY (`PRODUCT_ID`) REFERENCES `PRODUCT_DEFINITION` (`PRODUCT_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `WASTAGE_EVENT`
--

LOCK TABLES `WASTAGE_EVENT` WRITE;
/*!40000 ALTER TABLE `WASTAGE_EVENT` DISABLE KEYS */;
INSERT INTO `WASTAGE_EVENT` VALUES (1,'2016-06-25','2016-06-26 04:28:29','ACTIVE',100004,1.00,10000,'Went bad'),(2,'2016-06-26','2016-06-26 16:05:55','ACTIVE',100155,5.00,10006,'Expired'),(5,'2016-06-27','2016-06-27 18:59:01','ACTIVE',100009,15.00,10012,'Wasted yeah :P'),(6,'2016-06-27','2016-06-27 18:59:01','ACTIVE',100001,10.00,10012,'Wasted yeah :P'),(7,'2016-06-27','2016-06-27 18:59:01','ACTIVE',100003,-11.00,10012,'Wasted yeah :P'),(8,'2016-06-28','2016-06-28 10:15:19','ACTIVE',100001,1.00,10000,'Wasted'),(9,'2016-06-28','2016-06-28 11:44:36','ACTIVE',100195,190.00,10000,'Wasted'),(10,'2016-06-28','2016-06-28 13:48:13','ACTIVE',100263,10.00,10000,'Wasted'),(11,'2016-06-28','2016-06-28 17:48:58','ACTIVE',100263,10.00,10000,'Wasted'),(12,'2016-06-28','2016-06-28 17:48:58','ACTIVE',100250,5.00,10000,'Expired');
/*!40000 ALTER TABLE `WASTAGE_EVENT` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2016-07-01 15:04:21
