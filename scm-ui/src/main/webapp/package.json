{"name": "scm-service", "version": "1.0.0", "description": "Propreitary POS system build for Chaayos by Chaayos", "main": "index.js", "scripts": {"postinstall": "", "prestart": "", "start": "node server.js"}, "author": "Chaay<PERSON>", "license": "ISC", "devDependencies": {"browser-sync": "^2.26.12", "express": "^4.16.2", "gulp": "^4.0.2", "gulp-angular-filesort": "^1.1.1", "gulp-autoprefixer": "^7.0.1", "gulp-changed": "^4.0.2", "gulp-clean": "^0.3.2", "gulp-clean-css": "^4.3.0", "gulp-cli": "^2.3.0", "gulp-compile-handlebars": "^0.6.1", "gulp-concat": "^2.6.1", "gulp-hash": "^4.2.2", "gulp-htmlhint": "^1.0.0", "gulp-htmlmin": "^3.0.0", "gulp-jshint": "^2.0.4", "gulp-line-ending-corrector": "^1.0.3", "gulp-ng-html2js": "^0.2.2", "gulp-noop": "^1.0.0", "gulp-rename": "^1.2.2", "gulp-s3-upload": "^1.6.6", "gulp-sourcemaps": "^2.6.5", "gulp-uglify": "^3.0.2", "jshint": "^2.9.5", "jshint-stylish": "^2.2.1", "minimist": "^1.2.0", "run-sequence": "^2.2.0"}, "private": true, "dependencies": {}}