/*
 * Copyright (C) 2016, Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Written by Chaayos Technology Team, 2015
 */

body {
    /*background: #ffebee;*/
    max-width: 100%;
}

.custom-modal{
    width:75% !important;
}

*::-webkit-scrollbar {
    width: 8px;
}

*::-webkit-scrollbar-track {
    background-color: #ddd;
}

*::-webkit-scrollbar-thumb {
    background-color: darkgrey;
    outline: 1px solid slategrey;
}

a{
    cursor: pointer;
}

input[type=number] {
    -moz-appearance: textfield;
}

div#content {
    padding-left: 250px;
    margin-top: 75px;
}

@media only screen and (max-width: 992px) {
    div#content {
        padding-left: 10px;
    }
}

@media only screen and (max-width: 992px) {
    header {
        width: 100%;
    }
}

@-ms-viewport {
    width: device-width;
}

.nav-wrapper {
    padding-left: 15px;
}

div#page-title-div {
    padding: 0px 15px;
}

form.scm-form {
    padding: 10px;
}

.dkc-data {
	color: brown
}

.stpl-data {
	color: green
}

.row.margin0{
     margin-bottom:0px;
 }

.row.margin10{
    margin-bottom:10px;
}

.row.margin-10{
    margin:10px;
}

.bold{
    font-weight: 700;
}

.pointer{
    cursor: pointer;
}
.padding-left-10{
    padding-left: 10px;
}

.padding-bottom-10{
    padding-bottom: 10px;
}

.underline{
    text-decoration: underline dotted;
}

.padding-top-10{
    padding-top: 10px;
}

.margin-top-5{
    margin-top: 5px;
}

.margin-top-10{
    margin-top: 10px;
}

.margin-top-20{
    margin-top: 1.45rem;
}

.container{
    max-width:100%;
    margin: 0;
    padding:0;
    width: 100%;
}

.loginContainer{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
}

.login-tab{
    border-radius: 3px;
    text-align: center;
    color: #3e3b38;
    background-color: #d4d4d4;
    border: 1px solid;
    border-color: #c1b4b4;
    line-height: 25px;
}

.login-tab.active{
    color: #fff3e0;
    background-color: #337ab7;
    border-color: #2e6da4;
}

.loginCard{
    max-width: 400px;
    width: 100%;
    margin: auto;
}

.card-panel.slim {
	padding: 10px;
}
span.removeBtn{
    font-size: 20px;
    margin-top: 5px;
    line-height: 10px;
    cursor:pointer;
}

div.tab-detail{
    background-color: #f6f6f6;
    margin-bottom: 10px;
    border: 1px solid #d8d8d8;
    box-shadow: 0px 1px 0px 1px #eee;
}

.tabs .tab{
    cursor: pointer;
}

.custom-h6{
    font-size: 22px;
    line-height: 40px;
}

.sub-form{
    margin-left: 10px;
    padding-left: 10px;
    padding-right: 10px;
    background-color: #F1EFEF;
}

select {
    display: block;
    border: none;
    border-bottom: 1px solid #9e9e9e;
    outline: none;
    height: 3rem;
}

.scm-form input[type="text"] {
    color: #000 !important;
}

body .breadcrumb {
    color: rgba(255, 255, 255, 0.7);
}

body .breadcrumb.active {
    color: #fff;
}

.col.custom-listing {
    padding: 0px;
    padding-top: 20px;
}

.col.custom-listing.productListing {
    overflow-y: auto;
    max-height: 90%;
}

.col.custom-listing .custom-listing-li {
    border-bottom: 1px solid #ddd;
    cursor: pointer;
}

.col.custom-listing .custom-listing-li:last-child {
    margin-bottom: 0px;
}

.custom-listing-li.active {
    background-color: #00695c;
    color: white;
}

.product-detail {
    padding-left: 20px;
}

.chip.inventory-chip{
    line-height: 22px;
    height:20px;
}

.modal-small {
    width: 25%;
    max-height:90%;
}

.modal-medium {
    width: 40%;
    max-height:90%;
}

.modal-mx {
    width: 65%;
    max-height:90%;
}
.modal-mx-no-transform {
    width: 65%;
    max-height:90%;
    transform: none!important;
}
   

.modal-large{
    width:90%;
    max-height:90%;
}

.mappingBtn{
    margin:5px;
}
.actionBtn{
    margin:10px;
}

    #scm-spinner{
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 1000;
}

#scm-spinner-wrapper{
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}

#scm-spinner-wrapper #scm-spinner{
    position: relative;
    top: 0px;
    left: 0px;
    z-index: 1000;
    }


.overlay {
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    background-color: rgba(0,0,0, 0.1);
    transition: 0.5s;
}

.toast-overlay{
    height: 100%;
    width: 100%;
    position: fixed;
    z-index:10000;
}

.tabs .tab a {
    color:#963C40!important;
}

.tabs .indicator{
    background-color: #B95E62!important;
}

.collection {
    margin: 0.5rem 0 0rem 0;
}

#packagingView.collection span{
    text-transform: uppercase;
}

.overflow{
    overflow-y: auto;
    max-height: 550px;
}

.btn.btn-xs-small{
    width: 100px;
    font-size: 10px;
    padding: 5px;
    height: 24px;
    line-height: 16px;
}

.btn.btn-small{
    width: 100px;
    font-size: 12px;
    padding: 5px;
    height: 30px;
    line-height: 16px;
}

.btn.btn-medium{
    width: 175px;
    font-size: 12px;
    padding: 5px;
    height: 30px;
    line-height: 16px;
}

.collection .collection-header.custom-header{
    border: 1px solid #ddd;
    background-color: #926d6d;
    color: #fff;
    padding: 5px;
}

.custom-collection-header{
    border: 1px solid #afa7a7;
    background-color: #eae7e7;
    color: #000;
    padding: 5px;
}

.collapsible.no-border {
    border-top: none;
    border-right: none;
    border-left: none;
    margin: 0.5rem 0 1rem 0;
    box-shadow: none;
}

.collapsible.no-border table.bordered{
    border-right: 1px solid #ddd;
    border-left: 1px solid #ddd;
}

.collapsible.no-border .collapsible-body .row{
    margin-bottom:0px;
}

.collapsible.no-border li {
    margin-bottom:10px;
}

.orderStrip{
    margin-bottom:5px;
    background:#efefef;
    padding:5px;
}
.productBadge{
    background: green;
    color: #fff;
    padding: 2px 5px;
    margin: 2px 4px;
    border-radius: 2px;
    display: inline-block;
}



ul#menu > li{
    border-bottom: 1px solid #e7e7e7;
    color: #e73f09;
    border-left: #e73f09 3px solid;
}

ul#menu .collapsible-body ul.sub-menu li{
    line-height: 30px;
    max-height: 45px;
}

.addMargin{
    margin-top:65px;
}

ul#menu .collapsible-body ul.sub-menu li a{
    line-height: 45px;
    height: 45px;
}

.side-nav .collapsible-body li.active, .side-nav.fixed .collapsible-body li.active {
    background-color: transparent;
}

label {
    font-size: 14px;
    color: #444444;
    font-weight: bold;
    display: block;
    margin-bottom: 10px;
}

/******************************* form styling *********************************/
textarea{
    height: 85px;
    outline: none;
}

input[type=email], input[type=number], input[type=password], input[type=tel], input[type=text], input[type=url]
, select, textarea, .select2.select2-container{
    /*-webkit-appearance: none;*/
    /*appearance: none;*/
    display: inline-block;
    height:auto !important;
    margin: 0;
    padding: 5px 8px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-top: 1px solid #c0c0c0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-border-radius: 1px;
    -moz-border-radius: 1px;
    border-radius: 1px;
}

input[type=email]:focus, input[type=number]:focus, input[type=password]:focus, input[type=tel]:focus, input[type=text]:focus,
input[type=url]:focus , select:focus, textarea:focus, .select2.select2-container:focus{
    outline: none;
    border: 1px solid #26a69a !important;    /*#4d90fe*/
    box-shadow: inset 0 1px 2px rgba(0,0,0,.3) !important;
}

input[type=search]{
    border: none;
    border-top: none;
}

input[type=search]:focus {
    border-bottom: 0px !important;
    box-shadow: none !important;
}


textarea{
    height: 80px !important;
}

.form-element{
    margin: 0 0 1.5em;
}

label {
    font-size: 14px;
    color: #444444;
    font-weight: bold;
    display: block;
    margin-bottom: 10px;
}

/******************************* form styling *********************************/
textarea{
    height: 85px;
    outline: none;
}

input[type=email], input[type=number], input[type=password], input[type=tel], input[type=text], input[type=url]
, select, textarea, .select2.select2-container{
    /*-webkit-appearance: none;*/
    /*appearance: none;*/
    display: inline-block;
    height:auto !important;
    margin: 0;
    padding: 5px 8px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-top: 1px solid #c0c0c0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-border-radius: 1px;
    -moz-border-radius: 1px;
    border-radius: 1px;
}

input[type=email]:focus, input[type=number]:focus, input[type=password]:focus, input[type=tel]:focus, input[type=text]:focus,
input[type=url]:focus , select:focus, textarea:focus, .select2.select2-container:focus{
    outline: none;
    border: 1px solid #26a69a !important;    /*#4d90fe*/
    box-shadow: inset 0 1px 2px rgba(0,0,0,.3) !important;
}

input[type=search]{
    border: none;
    border-top: none;
}

input[type=search]:focus {
    border-bottom: 0px !important;
    box-shadow: none !important;
}


textarea{
    height: 80px !important;
}

.form-element{
    margin: 0 0 1.5em;
}

/********** Select2 customization *********/
.select2-selection--multiple{
    border: none !important;
    border-bottom: 1px solid #aaa !important;
    height: 30px !important;
}

.input-field  input.select2-search__field[type=search]:focus{
    background: none !important;
}

.select2-container {
    display: block !important;
    border: none;
    border-bottom: 1px solid #9e9e9e !important;
    outline: none !important;
    height: 30px !important;
    margin-bottom: 10px !important;
}

.select2-container--default .select2-selection--single {
    background-color: inherit !important;
    border: none !important;
    border-radius: 0px !important;
    height: 100%;
}

.select2.select2-container{
    height: 30px !important;
    padding: 0;
}

.select2-selection{
    outline: none;
}

.select2-container--default .select2-selection--multiple {
    border-radius: 0px;
}

.select2-container .select2-selection--multiple{
    min-height: auto !important;
}

.select2-search__field{
    height: 30px !important;
    margin: 0px !important;
    padding: 0px !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa !important;
}

.menuItemList .row{
    margin: 0px;
}

.categoryItem{
    font-size: 24px;
    background: #26a69a !important;
    color: #fff !important;
    padding: 10px !important;
    box-sizing: content-box !important;
}

.productList{
    padding:10px;
    box-shadow: #ccc 0 0 2px 0;
}

.productListHeader{
    border-top: 0;
    padding: 5px 5px;
    margin: 0px;
    background: hsla(231, 13%, 54%, 0.07);
    font-size: 20px;
    box-sizing: content-box;
}
.productListItem{
    border-top: 0;
    padding: 5px 5px;
    margin: 0px;
    background: #fff;
    font-size: 15px;
    box-sizing: content-box;
}

span.badge{
    background: #26a69a;
    padding: 4px 5px;
    border-radius: 3px;
    color: #fff;
    font-size: 12px;
    right:auto;
    position: static;
}

span.badge.active{
    background: #06a24c;
    padding: 4px 12px;
}

span.badge.inactive{
    background: #E57373;
}

.specialInstrLabel{
    padding: 2px 5px;
    border: #ccc 1px solid;
    border-radius: 4px;
    background: #ddd;
    margin-right: 5px;
    font-size: 14px;
    display: inline-block;
}

.planBody{
    border-top: #87b381 3px solid;
    border-bottom: #87b381 3px solid;
    margin: 0;
    float: left;
    width: 100%;
}

.planBody .col{
    padding:3px 0;
}

.planItem .col{
    padding:3px 0;
}

.planItem{
    margin: 0;
    background: #fff;
    font-weight: normal;
}

.planItemHead{
    font-weight: bold;
    background: #87b381;
    border-bottom: #87b381 5px solid;
}

.recipeNotes{
    margin: 0;
    background: #87b381;
    padding: 10px 0;
}

.modal .modal-content {
    padding: 10px;
}

.refOrderListTable tr{
    cursor: pointer;
}

.table-container{
    background: #f5f5f5;
    border: #ddd 1px solid;
    padding: 10px 20px;
    margin-bottom: 20px;
}

.collection-item input, .collection-item select{
    margin: 0;
}

.collection .collection-item{
    padding: 10px;
}
.collection.striped .collection-item:nth-child(odd) {
	background: #eee; 
}

.highlight-row {
	background: #ddd; 
}

.highlight-data {
	font-size: 20px !important; 
	font-weight: bold;
	color: red;
}


.highlight-last-created {
	background: #ff8040 !important; 
}


.row-head{
    font-weight: bold;
}

.list-head{
    background: #26a69a !important;
    color: #fff !important;
    font-size: 16px;
}

.list-head .row{
    margin-bottom: 0px;
}

.clickable{
    cursor: pointer;
}

.sku-pkg-row label{
    display: block;
    margin-bottom: 10px;
}

.externalCategoryColor {
	background: #F1EFAA;
}

.warehouseCategoryColor {
	background: #E1F1C2 !important;
}

.kitchenCategoryColor {
	background: #D1F1E1;
}

.multiSkuColor{
    background: #FFEB3B !important;
}

.redBg{
    background: red !important;
}

.invisible{
    visibility: hidden;
}

.orderingColumnHighlight{
    font-size: 14px;
    background: #fd9d96;
    margin: -20px 0;
    padding: 24px 8px !important;
    border-left: #000 1px solid;
    border-right: #000 1px solid;
}

nav{
    background: #546e7a;
}

.side-nav.fixed a {
    padding: 17px 10px;
    height: auto;
    line-height: normal;
    color: #e73f09;
}

.side-nav .collapsible-header, .side-nav.fixed .collapsible-header,
.side-nav .collapsible-header:hover, .side-nav.fixed .collapsible-header:hover{
    padding: 0 10px;
    padding: 17px 10px;
    height: auto;
    line-height: normal;
}

.side-nav .collapsible-body, .side-nav.fixed .collapsible-body {
    margin-left: -3px;
}

.side-nav .collapsible-body li a, .side-nav.fixed .collapsible-body li a {
    padding: 0 10px 0 20px;
    border-left: #308715 4px solid;
    border-bottom: #f1f1f1 1px solid;
    font-size: 12px;
    color: #308715;
}

.side-nav .collapsible-body li.active a, .side-nav.fixed .collapsible-body li.active a {
    color: #308715;
}

.side-nav li.active .collapsible-header{
    box-shadow: #a2a1a1 0px 1px 1px 0
}

.side-nav{
    height: calc(100% - 5px);
}

.sku-title-strip{
    background: #eee;
    padding: 10px 20px;
    margin: -20px;
    margin-bottom: 10px;
    border-bottom: #ddd 1px solid;
}

.ng-invalid.ng-dirty{
    border: #fa0909 1px solid !important;
}

.ng-valid.ng-dirty{
    border: #009005 1px solid !important;
}

label.ng-invalid.ng-dirty{
    border: none !important;
}

label.ng-valid.ng-dirty{
    border: none !important;
}

.errorMessage,p.help-block{
    color:#fa0909;
    font-size: 12px;
}


/*    disabling spinner on number inputs     */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
    cursor: pointer;
}

.inventory .packaging-detail.type{
    font-size:14px;
    width:10%;
    margin-bottom: 0px;
}

.inventory .packaging-detail.value{
    font-size:14px;
    width:25%;
}

.inventory .packaging-detail.packaging{
    font-size:14px;
    width:70%;
}

.inventory-table td{
    padding:3px 3px;
    border: 1px solid #ddd;
}

.inventory-table th{
    padding: 5px 3px;
    background-color: #ddd;
    color: #000;
    border: 1px solid #ccc;
    font-size: 12px;
}

.inventory-table .selected-detail{
    background: #2bbbad;
    display: inline-block;
    border-radius: 3px;
    color: #fff;
    padding: 4px 5px;
    margin-top: 3px;
    margin-right:5px;
}

.inventory-table span.packaging-value{
    font-size: 12px;
}

.inventory-table .selected-detail .packaging-text{
    background-color: #fff;
    color: #000;
    border-radius: 3px;
    font-size: 14px;
    padding: 0px 5px 0px 5px;
    max-width: 30px;
    display: inline-block;
    text-align: center;
}

#inventory-markers .marker{
    color: white;
    font-size: 16px;
    padding: 10px;
    border-radius: 3px;
    text-align: center;
    height:40px;
}

#inventory-markers .marker.error, .inventory-table td.error{
    background-color: #fb3838;
}
#inventory-markers .marker.noerror{
    background-color: #49c849;
}

.day-close-text{
    width: 150px;
    font-size: 11.35px;
    border: 1px solid #ddd;
    padding: 2px;
    background: #eee;
    box-shadow: 1px 1px 1px #ddd;
}

.remove-icon{
    cursor: pointer;
    font-size:12px;
}

.margin-bottom-15{
    margin-bottom:15px;
}

.popeye-modal-large{
    width: auto !important;
}

.margin-bottom-5{
    margin-bottom:5px;
}

.margin-right-5{
    margin-right: 5px;
}

.collapsible.popout>li {
    margin: 16px 0px !important;
}

.vBtn{
    width: auto !important;
    padding: 5px 20px !important;
}

ul.pagination{
    margin:0;
    padding:0;
}

ul.pagination li{
    cursor: pointer;
}

.text-center-disabled {
    font-size: 24px;
    text-align: center;
    padding: 2%;
    color: grey;	
}

p.no-negative-inventory{
    padding: 30px;
    text-align: center;
    color: gray;
    font-size: 20px;
}

.collapsible-header.poNumber,#expandedPOView div.poNumber{
    background-color: #CDDC39;
    border: 1px solid #9E9E9E;
}

#expandedPOView div.poNumber{
    text-transform: uppercase;
    line-height: 21px;
    padding:3px;
    font-weight:bold;
    font-size: 14px;
}

#expandedPOView div.poNumber span.chip{
    text-transform: uppercase;
    line-height: 22px;
    font-size:12px;
    height:21px;
}

#expandedPOView .itemsTable tr{
    border-left: 1px solid #d0d0d0;
    border-right: 1px solid #d0d0d0;
}

#expandedPOView .itemsTable th, #expandedPOView .itemsTable td{
    padding:5px;
    font-size:12px;
    border: #000 1px solid;
}

#expandedPOView .itemsTable.borderLess th, #expandedPOView .itemsTable.borderLess td{
    border-bottom: none 0px !important;
    border-top: none;
    padding: 3px 5px;
}

#expandedPOView td input{
    max-width:50px;
}

#expandedPOView .col{
    padding: 0px;
    margin:3px;
}

#expandedPOView .gr-form p, #expandedPOView .gr-form label{
    text-align: right;
}

#expandedPOView .gr-form p label, #expandedPOView p.extraCharges label{
    display:inline-block;
}

#expandedPOView .gr-form input, #expandedPOView p.extraCharges input{
    width:150px;
}

.deviationTag{
    border-radius: 2px;
    padding: 2px 5px;
    margin: 3px 5px;
    background: #efefef;
    border: #ddd 1px solid;
}

.deviationTag.CREATED{
    background: #efefef;
    border: #ddd 1px solid;
}

.deviationTag.ACCEPTED{
    background: #4db849;
    border: #2eb82f 1px solid;
}

.deviationTag.REJECTED{
    background: #ef9e4a;
    border: #ef8b1c 1px solid;
}

.deviationTag.REMOVED{
    background: #ef7154;
    border: #ef623c 1px solid;
}

.list-head.pad{
    background: #26a69a !important;
    color: #fff !important;
    font-size: 16px;
    padding: 10px;
    margin: 0;
}

.list-head.pad a{
    color:#fff;
}

.list-head.pad .row a{
    color: #039be5;
}

.list-item{
    padding:10px;
    border-bottom: #ddd 1px solid;
    background: #fff;
    color: #000;
}

.prItemMapping{
    border-radius: 2px;
    padding: 2px 5px;
    margin: 3px 5px;
    background: #efefef;
    border: #ddd 1px solid;
}

.prItemMapping.PROCESSED{
    background: #4db849;
    border: #2eb82f 1px solid;
}

.prItemMapping.IN_PROGRESS{
    background: #ef9e4a;
    border: #ef8b1c 1px solid;
}

.prItemMapping.PENDING{
    background: #ef7154;
    border: #ef623c 1px solid;
}

.vendorHeader{
    background: #26a69a;
    padding: 15px 10px;
    color: #fff;
    margin-bottom: 0;
}

.vendorPRContainer{
    border: #26a69a 3px solid;
    margin-bottom: 20px;
}

.materialModalErrorFooter{
    border: red 1px solid;
    padding: 5px 8px;
    border-radius: 5px;
    background: #e4b2b2;
}

.planPrinted{
    background: green !important;
    color:#fff;
}

.planHead{
    border: #ccc 1px solid;
    background: #ddd;
    padding: 5px;
    cursor: pointer;
}

.semifinishedItemsModal{
	width:80% !important;
}

#printSection table.small td{
    padding: 8px 5px;
}



/*       print styles        */
@media screen {
    #printSection {
        display: none;
    }
}

@page {
    size:A4;
    margin-top: 1cm !important;
}

@media print {
    body{
        margin-top: 5mm;
    }

    body * {
        visibility:hidden;
        height:0;
    }
    table {
        page-break-inside: avoid;
    }
    #printSection, #printSection * {
        visibility:visible;
        height:auto;
    }
    #printSection {
        padding: 15px;
        position:absolute;
        left:0;
        top:0;
        right:0;
        bottom: 0;
        orphans: 5;
        widows: 5;
    }
    #printHide{
        display: none;
    }
    #printSection:last-child {
        page-break-after: auto;
    }
    .print-border-table table{
        border-collapse: collapse;
        width: 100%;
    }
    .print-border-table table, .print-border-table th, .print-border-table td{
        border: 1px solid black;
    }
    .print-border-table th, .print-border-table td{
        vertical-align: center;
        padding: 3px 5px;
    }
    .page {
        page-break-after: always;
        page-break-inside: avoid;
    }
    .image{
        page-break-after: always;
        page-break-before: always;
        page-break-inside: avoid;
        margin:10px

    }
}

.date-label-prod-booking {
    font-weight: 600;
    color: red;
}

.data-error {
	color:red;
	font-weight: bold;
}

.data-ok {
	color:green;
	font-weight: bold;
}

.data-date {
	font-weight: bold;
}

.data-expiry{
    background: orange;
    padding: 7px;
    margin-bottom: 7px;
}

.data-division {
	background: rgba(0,0,0,0.03);
}

.frame {
    width: 80%;
    height: 80%;
    border: 3px solid #ccc;
    background: #eee;
    margin: auto;
    padding: 15px 25px;
}

.warning-box {
    padding: 20px;
    margin: 10px;
    background: #ffe633bf;
    border: #f1b42d 1px solid;
    border-radius: 8px;
    color: #a57305;
    font-size: 18px;
}
.margin-top-30{
    margin-top: 30px;
}

.mobileView {
    display: none;
}

.TableMobileView{
    display: none;
}
@media screen and (max-width: 991px) {
    .searchingCard {
        display: flex;
        flex-direction: column;
        margin:0px;
        width:100% !important;
        padding-top:15px;
    }
    .searchingCard .col {
        margin-left:0px !important;
        margin-right: 0px !important;
        width:100% !important;
    }

    .searchingCard .btn {
        width:100% !important;
    }

    .searchingCard .select2-container {
        width:100% !important;
    }
    .mobileView{
        display: flex !important;
        flex-direction: column;
        background: gray;

    }

    .TableMobileView{
        display: block !important;
        flex-direction: column;
        border-top:5px solid #444;
        background: #eee;
        padding-top: 10px;

    }

    .mobileView .col,.mobileViewMenu .col {
        margin-left: 0px !important;
        width: 100% !important;
        align-items: center;
    }

  .mobileViewMenu {
        flex-direction: column;
      height:180px;
      transition: all 0.4s ease-in-out;
      overflow: hidden;
    }

  .collapsedMenu {
      height:0px;
  }

    .standardView {
        display:none;
    }

    #dropdown-btn {
        float: right;
        font-size: 30px;
        line-height: 1.8;
    }

    .respTable {
        width:100%;
        overflow-x:auto;
    }

    .respTable .row {
        width:100%;
        min-width: 1250px;


    }

    .respTable::-webkit-scrollbar {
        width:5px;
        height:5px;
        background: #333;
    }

    .respTable::-webkit-scrollbar-thumb {
        color:#fff;
        background-color: #333;
    }

    .custom-modal {
        width:90% !important;
    }
    .popeye-modal{
        width:90% !important;
    }

    #materialModalCentered, #fileUploadModalCentered {
        margin:0px !important;
        width:90%;
    }

    #materialModal.show #materialModalCentered {
        transform: translate(-50%,-50%) scale(1) !important;
    }

    #materialModalContent {
        width: 100%;
    }

    .searchingCard .btn {
        margin-left: 0px !important;
    }

    .row .col.s5, .row .col .s6{
        width: 100% !important;
    }


    .TableMobileView .collection-item {
        margin-bottom:0px !important;
        transform: scale(0.9);
        box-shadow: 5px -6px 6px 0px #888;
        margin: 10px 0px;
       /*border: 2px solid #333;*/
    }

    .TableMobileView .collection-item {
        margin-bottom:0px !important;
        transform: scale(0.9);
        box-shadow:rgb(50 50 93 / 25%) 4px -4px 27px -5px, rgb(0 0 0 / 30%) -8px 12px 16px -8px;
        margin: 10px 0px;
        border-radius:10px;
        /*border: 2px solid #333 !important;*/
        transition: transform 0.5s ease;
    }

     .searchingCard .row .col {
        margin-top: 20px;
    }

    .searchingCard .row .col:nth-child(1){
        margin-top: 0px;
    }


    .TableMobileView .collection-item .row {
        display :flex;
        align-items: center;
        border-bottom: 1px solid #333;
        padding: 0px 3px 5px 3px;

    }
    .TableMobileView  .collection-item .row:last-child{
        border-bottom: 0px;

    }

    .TableMobileView .collection-item .row {
        margin:0px !important;
    }

    .TableMobileView .collection-item .row .col {
        font-weight: bolder;
        margin:0px !important;
    }

    .TableMobileView .collection-item .row .col:nth-child(1) {
        width:40% !important;
        background-color: #efe3e3 !important ;
    }

    .TableMobileView .Mobilemodal .collection-item .row .col:nth-child(1) {
        width:50% !important;
    }

    .TableMobileView .collection-item .row .col:nth-child(2) {
        border-left: 2px solid #333;
        font-weight: normal !important;
        width:70% !important;
    }

    .TableMobileView .Mobilemodal .collection-item .row .col:nth-child(2) {
        width:50% !important;
    }

    .TableMobileView .btn-xs-small {
        margin-top: 0px !important;
    }

    .checkboxRow {
        display:flex;
        width:100%;
        margin-top:20px;
    }
    .checkboxRow .col {
        text-align: left !important;
        margin-top: 0px !important;
    }

    #viewPODetails {
        top: 10% !important;
        width: 412px !important;
        transform: scaleX(1) !important;
    }


}