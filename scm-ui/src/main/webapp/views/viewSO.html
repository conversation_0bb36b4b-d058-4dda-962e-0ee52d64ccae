<style>
    .custom-modal{
        width: 60% !important;
    }

    .popeye-modal-container .popeye-modal{
    width: 900px;
	}

    @media screen and (max-width: 991px) {
        .searchingCard {
            display: flex;
            flex-direction: column;
            margin:0px;
            width:100% !important;
        }
        .searchingCard .col {
            margin-left:0px !important;
            margin-right: 0px !important;
            width:100% !important;
        }

        .searchingCard .btn {
            width:100% !important;
        }

        .custom-modal{
            width: 90% !important;
        }

    }
</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3">
            <div class="col s12">
                <h4 data-ng-if="!showViewActions">Pending Service Orders Requests</h4>
                <h4 data-ng-if="showViewActions">View Service Orders</h4>
            </div>
            <div class="searchingCard col margin-bottom-15">
                <div class="col s2">
                    <label>Select Start date</label>
                    <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select End date</label>
                    <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select Vendor</label>
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}"
                            ng-options="vendor as vendor.name for vendor in vendors"
                            data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                </div>
                <div class="col s2">
                    <label>Business Cost Center</label>
                    <select id="bcc" ui-select2="{allowClear:true, placeholder: 'Select Business Cost Center'}"
                            ng-options="bcc as bcc.name for bcc in bcc"
                            data-ng-change="selectBcc(bccSelected)" data-ng-model="bccSelected"></select>
                </div>
                <div class="col s2">
                    <label>Service Order ID</label>
                    <input type="text" ng-model="serviceOrderId"/>
                </div>
                <div class="col s1">
                    <button class="btn btn-small margin-top-20" data-ng-click="getSOs(true)" acl-action="VSRCVA">Find All</button>
                </div>
                <div class="col s1">
                    <button class="btn btn-small margin-top-20" data-ng-click="getSOs(false)">Find</button>
                </div>
            </div>
            <hr>
            <div class="col s12" data-ng-if="soRequestShort.length==0" style="padding:30px;color:gray;font-size: 20px;">
                <div class="row margin0 center" ng-if="!showViewActions">
                    No Pending Orders found for Approval
                </div>
                <div class="row margin0 center" ng-if="showViewActions">
                    No Service Orders found for the selected criteria
                </div>
            </div>
            <div class="row">
                <div class="col s9" style="margin-left:105px;">
                    <div class="row" style="margin-top: 30px;" ng-if="(vendorSelected != null) && (approvedAmount != null || pendingApprovalL1 != null|| pendingApprovalL2 != null || inProgessAmount != null)">
                        <table class="bordered striped">
                            <thead>
                            <tr>
                                <th>Approved Amount</th>
                                <th>In Progress Amount</th>
                                <th>Pending Approval L1</th>
                                <th>Pending Approval L2</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>{{approvedAmount.toFixed(2)}}</td>
                                <td>{{inProgessAmount.toFixed(2)}}</td>
                                <td>{{pendingApprovalL1.toFixed(2)}}</td>
                                <td>{{pendingApprovalL2.toFixed(2)}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col s12 respTable standardView" ng-if="soRequestShort.length>0">
                <div class="row">
                    <ul class="collection striped center" >
                         <li class="collection-item list-head">
                            <div class="row" style="font-size:12px">
                                <div class="col s1">ID</div>
                                <div class="col s2">Generation Time</div>
                                <div class="col s1">BCC</div>
                                <div class="col s1">Vendor Name</div>
                                <div class="col s1">Location Name</div>
                                <div class="col s1">Total Cost</div>
                                <div class="col s1">Total Tax</div>
                                <div class="col s1">Total Amount</div>
                                <div class="col s1">Status</div>
                                <div class="col s2">Actions</div>
                            </div>
                        </li>
                        <li class="collection-item " style="padding:5px;" data-ng-repeat="soR in soRequestShort track by $index">
                            <div class="row margin0" data-ng-class="{'red white-text': soR.id==createdPO}"  style="font-size:12px;">
                                <div class="col s1 pointer underline" data-ng-click="showDetailsSO(soR)">{{soR.id}}</div>
                                <div class="col s2">{{soR.generationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                                <div class="col s1" data-ng-if="soR.orderItems.length==1" >{{soR.orderItems[0].businessCostCenterName}}</div>
                                <div class="col s1" data-ng-if="soR.orderItems.length > 1" >Multiple [{{soR.orderItems.length}}]</div>
                                <div class="col s1">{{soR.vendorName}}</div>
                                <div class="col s1">{{soR.dispatchLocationCity}}</div>
                                <div class="col s1">{{soR.totalCost.toFixed(2)}}</div>
                                <div class="col s1">{{soR.totalTaxes.toFixed(2)}}</div>
                                <div class="col s1">{{soR.totalAmount.toFixed(2)}}</div>
                                <div class="col s1" style="word-break: break-all;">{{soR.status}}</div>

                                <div class="col s2" data-ng-if="!showViewActions && (soR.status.includes('PENDING_APPROVAL') || soR.status == 'CREATED') ">
                                    <!-- <button class="btn btn-xs-small vBtn margin-right-5"
                                            acl-action = "SOAPR" data-ng-click="approve(soR.id,$index)">Approve</button> -->
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL1" data-ng-if="soR.status == 'PENDING_APPROVAL_L1' || soR.status == 'CREATED'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L1</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL2" data-ng-if="soR.status == 'PENDING_APPROVAL_L2'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L2</button>
                                    <!-- <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL3" data-ng-if="soR.status == 'PENDING_APPROVAL_L3'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L3</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL4" data-ng-if="soR.status == 'PENDING_APPROVAL_L4'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L4</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL5" data-ng-if="soR.status == 'PENDING_APPROVAL_L5'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L5</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL6" data-ng-if="soR.status == 'PENDING_APPROVAL_L6'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L6</button> -->
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL1" data-ng-if="soR.status == 'PENDING_APPROVAL_L1' || soR.status == 'CREATED'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L1</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL2" data-ng-if="soR.status == 'PENDING_APPROVAL_L2'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L2</button>
<!--                                     <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL3" data-ng-if="soR.status == 'PENDING_APPROVAL_L3'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L3</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL4" data-ng-if="soR.status == 'PENDING_APPROVAL_L4'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L4</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL5" data-ng-if="soR.status == 'PENDING_APPROVAL_L5'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L5</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL6" data-ng-if="soR.status == 'PENDING_APPROVAL_L6'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L6</button> -->
                                    <button class="btn btn-xs-small vBtn margin-right-5" data-ng-if="soR.uploadedDocumentId!=null" style="margin-top: 5px"
                                        data-ng-click="downloadDocumentById(soR.uploadedDocumentId)">Download Document</button>
                                    <span data-ng-if="soR.status!='CREATED' && !soR.status.includes('PENDING_APPROVAL')">
                                        No Actions available for Order
                                    </span>
                                </div>
                                <div class="col s2" data-ng-if="showViewActions">
                                    <button class="btn btn-xs-small vBtn margin-right-5"
                                            data-ng-if="(soR.status=='CREATED' || soR.status=='APPROVED' || soR.status=='PENDING_APPROVAL_L1')"
                                            data-ng-click="cancel(soR,$index)" acl-action="SOCNCL">Cancel</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5"
                                            data-ng-if="!(soR.status=='CREATED'|| soR.status=='CANCELLED' || soR.status.includes('PENDING_APPROVAL_L1'))"
                                            data-ng-click="printInvoice(soR.soInvoiceDocument,$index)">Print</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5"
                                            data-ng-if="soR.status=='IN_PROGRESS'"
                                            data-ng-click="close(soR.id,$index)" acl-action="SOCLS">Close</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5" data-ng-if="soR.uploadedDocumentId !=null" style="margin-top: 5px"
                                            data-ng-click="downloadDocumentById(soR.uploadedDocumentId)">Download Document</button>
                                    <br>
                                    <span  data-ng-if="!(soR.status=='CREATED' || soR.status=='IN _PROGRESS' || soR.status=='APPROVED' || soR.status.includes('PENDING_APPROVAL_L1'))">
                                        No Actions available for this order
                                    </span>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="TableMobileView" ng-if="soRequestShort.length>0">
                <ul class="collection striped center">
                    <li class="collection-item" data-ng-repeat="soR in soRequestShort track by $index"
                        data-ng-class="{'red white-text': soR.id==createdPO}">
                        <div class="row">
                            <div class="col s1">ID</div>
                            <div class="col s1 pointer underline" data-ng-click="showDetailsSO(soR)">{{soR.id}}</div>
                        </div>
                        <div class="row">
                            <div class="col s2">Generation Time</div>
                            <div class="col s2">{{soR.generationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                        </div>
                        <div class="row">
                            <div class="col s2">Vendor Name</div>
                            <div class="col s2">{{soR.vendorName}}</div>
                        </div>
                        <div class="row">
                            <div class="col s1">Location Name</div>
                            <div class="col s1">{{soR.dispatchLocationCity}}</div>
                        </div>
                        <div class="row">
                            <div class="col s1">Total Cost</div>
                            <div class="col s1">{{soR.totalCost.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col s1">Total Tax</div>
                            <div class="col s1">{{soR.totalTaxes.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col s1">Total Amount</div>
                            <div class="col s1">{{soR.totalAmount.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col s1">Status</div>
                            <div class="col s1" style="word-break: break-all;">{{soR.status}}</div>
                        </div>
                        <div class="row">
                            <div class="col s2" align="center">Actions</div>
                            <div class="col s2"
                                 data-ng-if="!showViewActions && (soR.status.includes('PENDING_APPROVAL') || soR.status == 'CREATED') ">
                                <!-- <button class="btn btn-xs-small vBtn margin-right-5"
                                            acl-action = "SOAPR" data-ng-click="approve(soR.id,$index)">Approve</button> -->
                                <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL1"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L1' || soR.status == 'CREATED'"
                                        data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L1</button>
                                <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL2"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L2'"
                                        data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L2</button>
                                <!-- <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL3" data-ng-if="soR.status == 'PENDING_APPROVAL_L3'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L3</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL4" data-ng-if="soR.status == 'PENDING_APPROVAL_L4'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L4</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL5" data-ng-if="soR.status == 'PENDING_APPROVAL_L5'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L5</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL6" data-ng-if="soR.status == 'PENDING_APPROVAL_L6'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L6</button> -->
                                <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL1"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L1' || soR.status == 'CREATED'"
                                        style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L1</button>
                                <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL2"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L2'" style="margin-top:5px"
                                        data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L2</button>
                                <!--                                     <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL3" data-ng-if="soR.status == 'PENDING_APPROVAL_L3'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L3</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL4" data-ng-if="soR.status == 'PENDING_APPROVAL_L4'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L4</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL5" data-ng-if="soR.status == 'PENDING_APPROVAL_L5'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L5</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL6" data-ng-if="soR.status == 'PENDING_APPROVAL_L6'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L6</button> -->
                                <span data-ng-if="soR.status!='CREATED' && !soR.status.includes('PENDING_APPROVAL')">
                        No Actions available for Order
                    </span>
                            </div>
                            <div class="col s2" data-ng-if="showViewActions">
                                <button class="btn btn-xs-small vBtn margin-right-5"
                                        data-ng-if="(soR.status=='CREATED' || soR.status=='APPROVED' || soR.status=='PENDING_APPROVAL_L1')"
                                        data-ng-click="cancel(soR,$index)" acl-action="SOCNCL">Cancel</button>

                                <button class="btn btn-xs-small vBtn margin-right-5"
                                        data-ng-if="!(soR.status=='CREATED'|| soR.status=='CANCELLED' || soR.status.includes('PENDING_APPROVAL_L1'))"
                                        data-ng-click="printInvoice(soR.soInvoiceDocument,$index)">Print</button>

                                <button class="btn btn-xs-small vBtn margin-right-5" data-ng-if="soR.status=='IN_PROGRESS'"
                                        data-ng-click="close(soR.id,$index)" acl-action="SOCLS">Close</button>

                                <span
                                        data-ng-if="!(soR.status=='CREATED' || soR.status=='IN _PROGRESS' || soR.status=='APPROVED' || soR.status.includes('PENDING_APPROVAL_L1'))">
                        No Actions available for this order
                    </span>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<script type="text/ng-template" id="departmentModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="row">
                <h5 style="text-align-last: center;">SO# {{selectedSOR.id}} issued for {{selectedSOR.vendor.name}}
                    {{selectedSOR.dispatchLocation.city}}</h5>
                <div class="row standardView" style="max-height: 500px; overflow: auto;">
                    <table class="bordered striped">
                        <thead>
                        <tr>
                            <th>Cost Element</th>
                            <th>BCC</th>
                            <th>Service Description</th>
                            <th>Unit Of Measure</th>
                            <th>Unit Price</th>
                            <th>Requested Quantity</th>
                            <th>Tax Rate</th>
                            <th>Cost</th>
                            <th>Tax</th>
                            <th>Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="item in selectedSOR.orderItems">
                            <td>{{item.costElementName}}[{{item.ascCode}}]({{item.costElementDate| date:'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})
                            </td>
                            <td>{{item.businessCostCenterName}}</td>
                            <td style="word-break: break-all;">{{item.serviceDescription}}</td>
                            <td>{{item.unitOfMeasure}}</td>
                            <td>{{item.unitPrice}}</td>
                            <td>{{item.requestedQuantity}}</td>
                            <td>{{item.taxRate}}</td>
                            <td>{{item.totalCost.toFixed(2)}}</td>
                            <td>{{item.totalTax.toFixed(2)}}</td>
                            <td>{{item.amountPaid.toFixed(2)}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="TableMobileView">
                    <ul class="collection striped center">
                        <li class="collection-item" data-ng-repeat="item in selectedSOR.orderItems">
                            <div class="row">
                                <div class="col">Cost Element</div>
                                <div class="col">{{item.costElementName}}[{{item.ascCode}}]({{item.costElemendivate| date
                                    :'dd-MM-yyyy'}})</div>
                            </div>
                            <div class="row">
                                <div class="col">BCC</div>
                                <div class="col">{{item.businessCostCenterName}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Service Description</div>
                                <div class="col" style="word-break: break-all;">{{item.serviceDescription}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Unit Of Measure</div>
                                <div class="col">{{item.unitOfMeasure}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Unit Price</div>
                                <div class="col">{{item.unitPrice}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Requested Quantity</div>
                                <div class="col">{{item.requestedQuantity}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Tax Rate</div>
                                <div class="col">{{item.taxRate}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Cost</div>
                                <div class="col">{{item.totalCost.toFixed(2)}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Tax</div>
                                <div class="col">{{item.totalTax.toFixed(2)}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Amount</div>
                                <div class="col">{{item.amountPaid.toFixed(2)}}</div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="row right-align" style="line-height: 30px;">
            <b>Taxable Amount:</b> Rs.{{billAmount}}<br/>
            <b>Total Taxes:</b> Rs.{{totalTaxes}}<br/>
            <b>Paid Amount:</b> Rs.{{paidAmount}}
        </div>
        <div class="row">
            <h5 style="text-align-last: center;">Capex Budget Department Data</h5>
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Department Name</th>
                        <th>Total Amount</th>
                        <th>Original Amount</th>
                        <th>Budget Amount</th>
                        <th>Remaining Amount</th>
                        <th>Running Amount</th>
                        <th>Receiving Amount</th>
                        <th>Paid Amount</th>
                        <th>Allocated Cafe</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="items in summaryItem">
                        <td>{{items.departmentName}}</td>
                        <td>{{items.amountPaid.toFixed(2)}}</td>
                        <td>{{items.originalAmount.toFixed(2)}}</td>
                        <td>{{items.budgetAmount.toFixed(2)}}</td>
                        <td>{{items.remainingAmount.toFixed(2)}}</td>
                        <td>{{items.runningAmount.toFixed(2)}}</td>
                        <td>{{items.receivingAmount.toFixed(2)}}</td>
                        <td>{{items.paidAmount.toFixed(2)}}</td>
                        <td>{{items.businessCostCenterName}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <button data-ng-show="status" class="right btn" data-ng-click="submit()">Approv</button>
            <button data-ng-show="!status" class="right btn" data-ng-click="submit()">Reject</button>
            <button class="btn right margin-right-5" data-ng-click="cancel()">Cancel</button>
        </div>
    </div>
</script>
