<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Custom Reports</h3>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12">
            <div class="col s6">
                <label>Select Report*</label>
                <select data-ng-options="report for report in availableReportsList" data-ng-model="selectedReport"></select>
            </div>
            <div class="col s6" data-ng-if="selectedReport != null && selectedReport != 'Sales Vs Fountain 9'">
                <button class="btn btn-medium" data-ng-click="generateReport()" style="margin-top: 20px;">Generate Report</button>
            </div>
            <div class="col s6" data-ng-if="selectedReport != null && selectedReport == 'Sales Vs Fountain 9'">
                <button class="btn btn-medium" data-ng-click="generateF9SalesReport()" style="margin-top: 20px;">Generate Sales vs F9</button>
            </div>
        </div>
    </div>
</div>
