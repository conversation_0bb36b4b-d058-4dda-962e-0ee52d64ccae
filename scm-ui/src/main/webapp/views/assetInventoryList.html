<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .collapsible-header.poNumber, #expandedPOView div.poNumber {
        background-color: #9068be;
        border: 1px solid #9068be;
        box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.24), 0 12px 20px 0 rgba(0, 0, 0, 0.19);
    }
    .status{
        text-align: center;
    }
    .count{
        background-color: aliceblue;
        text-align: center;
    }
    .count_selected{
        background-color: deeppink;
        text-align: center;
    }
</style>
<div class="row" data-ng-init="init()">
    <div class="row white z-depth-3">
        <div class="col s12">
            <h3>Asset Inventory</h3>
        </div>
        <div class="row searchingCard">
            <div class="col s3">
                <label>Asset Status Filter</label>
                <select data-ng-model="selectedStatus"
                        data-ng-options="type as type for type in statusListForFilter"></select>
            </div>
            <div class="col s3">
                <label>SKU Filter</label>
                <select
                        ui-select2="selectOptions"
                        data-ng-model="selectedSkuId">
                    <option
                            data-ng-repeat="sku in skus"
                            value="{{sku.skuId}}">{{sku.skuName}}</option>
                </select>
            </div>
            <div class="col s3">
                <div>
                    <label>Select Unit</label>
                    <select data-ng-model="selectedUnitData"
                            data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
                </div>
            </div>
            <div class="col s3 margin-top-10">
                <button
                        class="btn btn-small"
                        ng-click="getAsset()"
                        style="margin-top: 14px;width: 115px;" acl-action="TRNPPV">Get Assets
                </button>
            </div>
        </div>
    </div>

    <div class="row white z-depth-3" data-ng-if="groupedAssetList.length > 0">
        <ul class="col s12" data-collapsible="accordion" watch>
            <li data-ng-repeat="groupedAsset in groupedAssetList | filter:skuFilter | filter:countFilter" class="row">
            <div class="collapsible-header poNumber waves-effect waves-light lighten-5 s12">
                <div class="row">
                    <div class="col s3" style="color: aliceblue;">
                        <span>SKU Id - </span><span>{{groupedAsset.skuId}}</span>
                    </div>
                    <div class="col s6" style="color: aliceblue;">
                        <span>SKU Name - </span>
                        <span>{{groupedAsset.details.skuName}} </span></div>
                    <div class="col s3" style="color: aliceblue;"> SKU Total Count - {{groupedAsset.details.total}} </div>
                    <div class="col s12" style="max-width: fit-content;background-color: #8bf0ba;padding-bottom: 10px;">
                        <div class="col s2" style="max-width: fit-content;" data-ng-repeat="status in statusList">
                          <div class="status">{{status}}</div>
                          <div class="count" ng-class="{count_selected : selectedStatus == 'ALL' || selectedStatus == status}" >
                              {{groupedAsset.details[status]}}
                          </div>
                        </div>
                    </div>
                </div>


            </div>
            <div class="collapsible-body">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th>Asset Id</th>
                    <th>Asset Status</th>
                    <th>Tag Value</th>
                    <th>Owner Id</th>
                    <th>Start Date</th>
                    <th>Sku Id</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="asset in groupedAsset.details.assetList | filter:statusFilter">
                    <td>{{asset.assetId}}</td>
                    <th>{{asset.assetStatus}}</th>
                    <td>{{asset.tagValue}}</td>
                    <td>{{asset.ownerId}}</td>
                    <td>{{asset.startDate | date: 'yyyy-MM-dd'}}</td>
                    <td>{{asset.skuId}}</td>
                </tr>
                </tbody>
            </table>
            </div>
            </li>

        </ul>


    </div>
    <div class="row white z-depth-3" data-ng-if="assetList.length == 0" style="padding: 16px;">
        No Asset Present at selected Unit
    </div>
</div>