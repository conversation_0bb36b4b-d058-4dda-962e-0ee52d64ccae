<style>
    .popeye-modal{
        width: 85% !important;
    }
</style>
<div class="row white z-depth-3 " data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4 class="left">Upload Units Projections</h4>
            </div>
        </div>
    </div>

    <div class="col s12">
        <div class="row">
            <div class="col s6 center">
                <button class="btn btn-primary center" style="margin-top: 20px;margin-left: 70px;" data-ng-click="uploadSheet()">Upload Sheet</button>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="scmInputView.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">List Of Units Uploaded</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>S.No</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Brand Code</th>
                        <th>Unit Id</th>
                        <th>Unit Name</th>
                        <th>Unit Cost Center</th>
                        <th>Status</th>
                        <th>Sales Cloned From</th>
                        <th>Cafe Opening Date</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="item in listOfUnits track by $index">
                        <td>{{$index+1}}</td>
                        <td>{{item.startDate | date:"dd-MM-yyyy"}}</td>
                        <td>{{item.endDate | date:"dd-MM-yyyy"}}</td>
                        <td>{{item.brandCode}}</td>
                        <td>{{item.unitId}}</td>
                        <td>{{item.unitName}}</td>
                        <td>{{item.unitCostCenter}}</td>
                        <td>{{item.status}}</td>
                        <td>{{item.salesClonedFrom}}</td>
                        <td>{{item.cafeOpeningDate | date:"dd-MM-yyyy"}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <button class="right btn"  data-ng-click="submit()">Submit</button>
            <button class="right btn" style="margin-right:5px;" data-ng-click="cancel()">Cancel</button>
        </div>
    </div>
</script>
