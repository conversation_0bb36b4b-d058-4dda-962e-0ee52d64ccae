<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3">
            <div class="col s12">
                <h4>Packaging Definitions</h4>
                <ul class="tabs" tabs>
                    <li data-ng-click="changeType(packagingTypes[0])" class="tab col s4"><a
                            data-ng-class="{active:packagingType == packagingTypes[0]}">CASE</a></li>
                    <li data-ng-click="changeType(packagingTypes[1])" class="tab col s4"><a
                            data-ng-class="{active:packagingType == packagingTypes[1]}">INNER</a></li>
                    <li data-ng-click="changeType(packagingTypes[2])" class="tab col s4"><a
                            data-ng-class="{active:packagingType == packagingTypes[2]}">LOOSE</a></li>
                </ul>
                <div class="row tab-detail">
                    <div class="col s12" data-ng-show="!checkEmpty(listing)">
                        <h5>Select a Profile</h5>

                        <div class="row">
                            <div class="input-field">
                                <select ng-model="packagingId" ui-select2="selectOptions"
                                        data-placeholder="Enter name of profile"
                                        data-ng-change="getProfile(packagingType,packagingId)">
                                    <option data-ng-repeat="def in listing"
                                            value="{{def.packagingId}}">{{def.packagingName}}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col s12">
                        <div class="left" data-ng-if="checkEmpty(selectedProfile)">
                            <h5 data-ng-if="checkEmpty(listing)" class="flow-text">No profile found</h5>
                            <h5 data-ng-if="!checkEmpty(listing)" class="flow-text">No profile selected</h5>
                        </div>
                        <div class="right mappingBtn">
                            <button class="btn" data-ng-click="addToCount()">Add New</button>
                        </div>
                    </div>
                    <div class="col s12" data-ng-if="!checkEmpty(selectedProfile)">
                        <table class="responsive-table centered">
                            <thead>
                                <tr>
                                    <th>
                                        ID
                                    </th>
                                    <th>
                                        Type
                                    </th>
                                    <th>
                                        Code
                                    </th>
                                    <th>
                                        Name
                                    </th>
                                    <th>
                                        Conversion Ratio
                                    </th>
                                    <th>
                                        Unit of Measure
                                    </th>
                                    <th>
                                        Status
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        {{selectedProfile.packagingId}}
                                    </td>
                                    <td>
                                        {{selectedProfile.packagingType}}
                                    </td>
                                    <td>
                                        {{selectedProfile.packagingCode}}
                                    </td>
                                    <td>
                                        {{selectedProfile.packagingName}}
                                    </td>
                                    <td>
                                        {{selectedProfile.conversionRatio}}
                                    </td>
                                    <td>
                                        {{selectedProfile.unitOfMeasure}}
                                    </td>
                                    <td>
                                        {{selectedProfile.packagingStatus}}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col s12">
                        <div class="row">
                            <form class="scm-form">
                                <div class="row" data-ng-if="countValue != 0"
                                     data-ng-repeat="index in getCount(countValue) track by $index">
                                    <h4 class="flow-text">Packaging Profile</h4>

                                    <div class="input-field col s3">
                                        <input id="{{profiles[$index].packagingCode}}" type="text"
                                               data-ng-model="profiles[$index].packagingCode"
                                               class="validate black-text"/>
                                        <label class="black-text" for="{{profiles[$index].packagingCode}}">Code</label>
                                    </div>
                                    <div class="input-field col s3">
                                        <input id="{{profiles[$index].packagingName}}" type="text"
                                               data-ng-model="profiles[$index].packagingName"
                                               class="validate black-text"/>
                                        <label class="black-text" for="{{profiles[$index].packagingName}}">Name</label>
                                    </div>
                                    <div class="input-field col s3">
                                        <input id="{{profiles[$index].conversionRatio}}" type="number"
                                               data-ng-model="profiles[$index].conversionRatio"
                                               class="validate black-text"/>
                                        <label class="black-text active" for="{{profiles[$index].conversionRatio}}">Conversion
                                            Ratio</label>
                                    </div>
                                    <div class="input-field col s3">
                                        <input id="{{profiles[$index].unitOfMeasure}}" type="text"
                                               data-ng-model="profiles[$index].unitOfMeasure"
                                               class="validate black-text"/>
                                        <label class="black-text active" for="{{profiles[$index].unitOfMeasure}}">Unit
                                            of Measure</label>
                                    </div>
                                    <div class="row">
                                        <div class="right">
                                            <button class="btn right actionBtn" data-ng-click="reset()">Cancel</button>
                                            <button class="btn right actionBtn" data-ng-click="submit()">Submit</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
