<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	<div class="col s12">
		<div class="row">
			<div class="right mappingBtn">
				<a class="btn" data-ng-click="addSubCategoryData()">Add</a>
			</div>
			<div class="mappingBtn" style="margin-top: 10px; font-size: 40px;">
				Sub Classification List</div>
		</div>
	</div>
	<hr>
	<div class="row">
		<div class="col s12">
			<div class="scm-form" style="padding: 10px 0;">
				<div class="col s12">
					<label class="black-text" for="requestForLable">List Type</label> <select
						ui-select2="selectedUnit3" ng-change="getListDetails(listSelected)"
						id="listSelectId"
						data-placeholder="Select value"
						data-ng-model="listSelected" data-ng-options="listType as listType for listType in listTypes" ></select>
				</div>
			</div>
		</div>
	</div>
	<div class="row" style="margin-top:-20px">
		<div class="col s12">
			<div class="scm-form" style="padding: 10px 0;">
				<div class="col s12">
					<label class="black-text" for="requestFor">Category</label> <select
						ui-select2="selectedUnit2" ng-change="getSubCategories(categorySelected)"
						 data-placeholder="Select value"
						data-ng-model="categorySelected" data-ng-options="list as list.name for list in categoryData" ></select>
				</div>
			</div>
		</div>
	</div>
	
	<div class="row">
		<div class="col s12" data-ng-show="subCategoryData">
			<ul class="collection menuItemList">
				<li class="collection-item z-depth-1 list-head">
					<div class="row">
						<div class="col s1">Id</div>
						<div class="col s3">Name</div>
						<div class="col s2">Code</div>
						<div class="col s3">description</div>
						<div class="col s2">Status</div>
						<div class="col s1">Action</div>
					</div>
				</li>
				<li class="collection-item z-depth-1" data-ng-repeat="item in subCategoryData track by item.listTypeId">
					<div class="row">
						<div class="col s1">{{item.listTypeId == null || item.listTypeId == "" ? "NA" : item.listTypeId}}</div>
						<div class="col s3">{{item.name == null || item.name == "" ? "NA" : item.name}}</div>
						<div class="col s2">{{item.code == null || item.code == "" ? "NA" : item.code}}</div>
						<div class="col s3">{{item.description == null || item.description == "" ? "NA" : item.description}}</div>
						<div class="col s2">{{item.status == null || item.status == "" ? "NA" : item.status}}</div>
						<div class="col s1"><a data-ng-click="subCatEditModal(item)"><span class="fa fa-pencil" style="color:black"></span></a></div>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>
<script type="text/ng-template" id="addSubCategoryModal.html"> 
<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Add Sub Category</h3>
    <hr>
</div>
<div class="modal-body" id="modal-body">
	<div class="col s12" style="margin-top: 15px;" data-ng-if="!editMode">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Type</label> <select ng-model="$parent.type" ng-change="getListDetail(type)"
								data-ng-options="listTypeMod as listTypeMod for listTypeMod in listTypeMods"></select>
						</div>
					</div>
				</div>
		</div>
		</div>
		<div class="col s12" data-ng-if="!editMode">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Category</label> <select ng-model="$parent.categorySelect"
								data-ng-options="list as list.name for list in categories"></select>
						</div>
					</div>
				</div>
		</div>
		</div>

	<div class="col s12" >
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Budget Category</label> <select ng-model="budgetCategory"
																   data-ng-options="category as category for category in budgetCategories"></select>
						</div>
					</div>
				</div>
			</div>
	</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Name</label> <input type="text"
								data-ng-model="name">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Code</label> <input type="text"
								ng-model="code">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Description</label> <input type="text" ng-model="description">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Alias</label> <input type="text" ng-model="alias">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Status</label> <select ng-model="status"
								data-ng-options="period as period for period in periods"></select>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

		<div class="modal-footer">
			<button class="btn btn-primary" type="button"
				ng-click="submitSubCat()" data-ng-if="showErrors != true">Submit</button>
			<button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
		</div>
		</script>

