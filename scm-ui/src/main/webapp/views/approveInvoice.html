<style>
    .chip {
        font-size: 11px;
    }

    .btn i {
        font-size: 1rem !important;
    }

    .btn.btn-xs-small.vBtn {
        width: 70px !important;
        font-size: 10px;
        padding: 5px !important;
        height: 40px;
        line-height: 16px;
        text-align: center;
    }

</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="searchingCard `row white z-depth-3">
            <div class="col s12">
                <h4 data-ng-if="!showViewActions">Pending Requests</h4>
                <h4 data-ng-if="showViewActions">View Invoice Requests</h4>
            </div>
            <div class="row">
                <div class="col s2">
                    <label>Select Start date</label>
                    <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s2">
                    <label>Select End date</label>
                    <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s2">
                    <label>Select Status</label>
                    <select data-ng-model="selectedStatus"
                            data-ng-options="type as type for type in txnStatus"></select>
                </div>

                <div class="col s2">
                    <label>Select Vendor</label>
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}"
                            ng-options="vendor as vendor.entityName for vendor in vendors"
                            data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                </div>

                <div class="col s2">
                    <label>Select Dispatch Unit</label>
                    <select id="units" ui-select2="{allowClear:true, placeholder: 'Select Unit'}"
                            ng-options="unit.id as unit.name for unit in units"
                            data-ng-change="selectUnit(selectedUnit)" data-ng-model="selectedUnit"></select>
                </div>
                <div class="col s2">
                    <button class="btn margin-top-20" data-ng-click="getInvoices()">Find</button>
                    <!--acl-action="VOMVPOV"-->
                </div>
            </div>
            <div class="row">
                <div class="col s4">
                    <label>Filter on Invoice Id</label>
                    <input type="text" placeholder="Invoice Id" id="invoiceid" ng-model="selectedInvoiceId"/>
                </div>
                <div class="col s4">
                    <label>Filter on Transaction Type</label>
                    <select id="types" name="types" data-ng-model="txnType">
                        <option data-ng-repeat="type in txnTypes" value="{{type}}">{{type}}</option>
                    </select>
                </div>
            </div>
            <hr>
            <div class="col s12" data-ng-if="invRequest.length==0" style="padding:30px;color:gray;font-size: 20px;">
                <div class="row margin0 center" ng-if="!showViewActions">
                    No Invoice(s) found for Approval
                </div>
                <div class="row margin0 center" ng-if="showViewActions">
                    No Invoice(s) found for the selected criteria
                </div>
            </div>
            <div class="col s3 right" data-ng-if="invRequest.length>0">
                <button class="btn margin-top-20" data-ng-click="downloadExcell()" >Download Excell</button>
            </div>
            <div class="col s12" ng-if="invRequest.length>0">
                <div class="row">
                    <ul class="collapsible popout" data-collapsible="accordion" watch>
                        <li class="row margin0" style="padding:5px;"
                            data-ng-repeat="invR in invRequest | filter:{type:txnType}| filter:{id:selectedInvoiceId} track by $index">
                            <div class="col s10 collapsible-header waves-effect waves-light lighten-3"
                                 style="border:1px  solid #ddd;padding:0;"
                                 data-ng-class="{'red white-text': invR.id==createdInvoice.id}">
                                <div class="col s11">
                                    <div class="row margin0">
                                        <div class="left">
                                            <span><b>Invoice# {{invR.id}}</b> for {{invR.vendor.name}} [{{invR.dispatchLocation.name}}]</span>
                                            <br><span><b>Delievery location:</b>{{invR.deliveryAddress}}</span>
                                        </div>
                                        <div class="right">
                                            <span class="chip blue-grey white-text">{{invR.type}}</span>
                                            <span class="chip blue-grey white-text">{{invR.invoiceType}}</span>
                                            <span class="chip blue-grey white-text">{{invR.status}}</span>
                                            <span class="chip blue-grey white-text">PO No: {{invR.purchasedOrderNumber}}</span>

                                        </div>
                                    </div>
                                    <div class="row margin0">
                                        <div class="left">
                                            <span class="chip red darken-1 white-text">{{invR.vehicle.transportMode}}</span>
                                            <span class="chip red darken-1 white-text">{{invR.vehicle.name}}</span>
                                            <span class="chip red darken-1 white-text">{{invR.vehicle.vehicleNumber}}</span>
                                            <span class="chip red darken-1 white-text"
                                                  data-ng-if="invR.vehicle.transportMode != 'ROAD'">{{invR.docketNumber}}</span>
                                        </div>
                                        <div class="right">
                                            <span class="chip grey white-text">By: {{invR.createdBy.name}}</span>
                                            <span class="chip grey white-text">Amount: Rs.{{(invR.totalAmount + invR.additionalCharges).toFixed(2)}}</span>
                                            <span class="chip grey white-text">Dispatch: {{invR.dispatchDate | date :'dd-MM-yyyy'}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col s1" style="padding-top: 1.5rem;">
                                    <i class="fa fa-caret-down right"></i>
                                </div>
                            </div>

                            <!--<div class="col s2"
                                 data-ng-if="!showViewActions && (invR.status=='PERFORMA_GENERATED' || invR.status=='PENDING_DISPATCH')">
                                <button class="btn btn-xs-small vBtn"
                                        data-ng-click="downloadExcel(invR.id)">
                                    <i class="fa fa-download"></i> Excel
                                </button>
                                <button class="btn btn-xs-small vBtn"
                                        data-ng-click="downloadJson(invR.id)">
                                    <i class="fa fa-download"></i> JSON
                                </button>
                                <button data-ng-if="(invR.totalAmount + invR.additionalCharges).toFixed(2) >= 50000 && (invR.status!='CLOSED' || invR.status!='CANCELLED')"
                                        class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-click="uploadEway(invR.id, $index)">
                                    <i class="fa fa-upload"></i> E-way
                                </button>
                                <button data-ng-if="invR.invoiceType=='INVOICE' && (invR.status!='CLOSED' || invR.status!='CANCELLED')"
                                        class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-click="uploadInvoice(invR.id, $index)">
                                    <i class="fa fa-upload"></i> Invoice
                                </button>
                            </div>-->

                            <!-- TODO B2B invoicing use this code -->
                            <div class="col s2" data-ng-if="!showViewActions">
                                <!--<button class="btn btn-xs-small vBtn margin-top-10"-->
                                        <!--data-ng-if="invR.status=='PERFORMA_GENERATED'"-->
                                        <!--data-ng-click="approve(invR, $index)">Approve Invoice-->
                                <!--</button>-->
                                <!--<button class="btn btn-xs-small vBtn margin-top-10"-->
                                        <!--data-ng-if="invR.status=='PERFORMA_GENERATED'"-->
                                        <!--data-ng-click="reject(invR, $index)">Reject Invoice-->
                                <!--</button>-->
                                <button class="btn btn-xs-small vBtn"
                                        data-ng-click="downloadExcel(invR.id)">
                                    <i class="fa fa-download"></i> Excel
                                </button>
                                <button class="btn btn-xs-small vBtn"
                                        data-ng-click="uploadDoc(invR.id)" data-ng-if="(invR.status=='APPROVED') && (invR.irnNo==null)">
                                    <i class="fa fa-upload"></i>Upload Excel
                                </button>
                                <!--<button class="btn btn-xs-small vBtn" data-ng-click="downloadJson(invR.id)"-->
                                        <!--data-ng-if="(invR.status=='APPROVED')">-->
                                    <!--<i class="fa fa-download"></i> JSON-->
                                <!--</button>-->
                                <button class="btn btn-xs-small vBtn"
                                    data-ng-if="(invR.status=='APPROVED')" data-ng-click="selectJsonInvoice(invR)" data-target="enterDistanceModal" modal>E-JSON
                                </button>

                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.status=='APPROVED' && invR.uploadDocId == null"
                                        data-ng-click="uploadDocInvoice(invR, $index)" data-target="updateDocNoModal" modal>UpdatedDocNo
                                </button>
                                <!--<button data-ng-if="(invR.totalAmount + invR.additionalCharges).toFixed(2) >= 50000 && invR.status=='APPROVED'"-->
                                        <!--class="btn btn-xs-small vBtn margin-top-10"-->
                                        <!--data-ng-click="uploadEway(invR.id, $index)">-->
                                    <!--<i class="fa fa-upload"></i> E-way-->
                                <!--</button>-->
                                <button data-ng-if="invR.invoiceType=='INVOICE' && invR.status=='APPROVED'"
                                        class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-click="uploadInvoice(invR.id, $index)">
                                    <i class="fa fa-upload"></i> Invoice
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.invoiceType=='SYSTEM_INVOICE' || invR.invoiceType=='DELIVERY_CHALLAN') && invR.status=='APPROVED' && invR.invoice==null && invR.irnNo!=null"
                                        data-ng-click="generateB2BInvoice(invR, $index)">Generate Invoice
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.status=='APPROVED')"
                                        data-ng-click="setReadyToDispatch(invR, $index)">Send for Dispatch
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.status=='PENDING_DISPATCH')"
                                        acl-action="VIMAIC"
                                        data-ng-click="cancelInvoice(invR.id,$index)">Cancel
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.status=='APPROVED')"
                                        acl-action="VIMAIC"
                                        data-ng-click="cancelInvoice(invR.id,$index)">Cancel
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.status=='CLOSED')"
                                        acl-action="VIDAIC" data-target="screenshotUploadModal" modal
                                        data-ng-click="cancelUploadDocument(invR.id,$index)">Cancel
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.invoice!=null"
                                        data-ng-click="printInvoice(invR, $index)"><i class="fa fa-print"></i> Invoice
                                </button>

                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.ewayBill!=null"
                                        data-ng-click="printEway(invR, $index)"><i class="fa fa-print"></i> Eway Bill
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.docPOId!=null"
                                        data-ng-click="printPO(invR, $index)"><i class="fa fa-print"></i> Print PO
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.cancelDocId!=null"
                                        data-ng-click="printInvCancelDoc(invR, $index)"><i class="fa fa-print"></i> CancelDoc
                                </button>
                                <button id="{{invR.id}}_printB2BInvoice" print-btn class="btn" data-ng-hide="true">Print
                                    Invoice
                                </button>
                            </div>

                            <div class="col s2" data-ng-if="showViewActions">
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.status=='PERFORMA_GENERATED'"
                                        acl-action="VIMAIN"
                                        data-ng-click="approve(invR, $index)">Approve Invoice
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.status=='PERFORMA_GENERATED'"
                                        acl-action="VIMAIN"
                                        data-ng-click="reject(invR, $index)">Reject Invoice
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.status=='PENDING_DISPATCH')"
                                        data-ng-click="dispatch(invR, $index)">Dispatch Invoice
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.status=='CLOSED')"
                                        data-ng-click="uploadDeliveredDocument(invR.id, $index)">
                                    <i class="fa fa-upload"></i>Delivered Doc
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.deliveredDocumentUrl!=null)"
                                        data-ng-click="printDeliverDoc(invR, $index)">
                                    <i class="fa fa-print"></i>Delivered Doc
                                </button>
                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.status=='PERFORMA_GENERATED') && invR.closureId == null"
                                        data-ng-click="cancelInvoice(invR.id,$index)">Cancel
                                </button>

                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.invoice!=null"
                                        data-ng-click="printInvoice(invR, $index)"><i class="fa fa-print"></i> Invoice
                                </button>

                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.ewayBill!=null"
                                        data-ng-click="printEway(invR, $index)"><i class="fa fa-print"></i> Eway Bill
                                </button>

                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="(invR.status=='CLOSED' || invR.status=='PENDING_DISPATCH') && invR.invoiceType=='DELIVERY_CHALLAN'"
                                        data-ng-click="printChallan(invR, $index)"><i class="fa fa-print"></i> Challan
                                </button>

                                <button id="{{invR.id}}_printDiv" print-btn class="btn" data-ng-hide="true">Print
                                    Challan
                                </button>

                                <button class="btn btn-xs-small vBtn margin-top-10"
                                        data-ng-if="invR.docPOId!=null"
                                        data-ng-click="printPO(invR, $index)"><i class="fa fa-print"></i> Print PO
                                </button>

                                <div style="color:#b39b9b; margin-top:24%; margin-left:5%; width:100%;"
                                     data-ng-if="(invR.status=='CANCELLED')">
                                    No Actions Available
                                </div>

                            </div>
                            <div class="collapsible-body">
                                <table class="bordered striped">
                                    <thead>
                                    <tr>
                                        <th class="center-align">SKU</th>
                                        <th class="center-align">UOM</th>
                                        <th class="center-align">Pkg</th>
                                        <th class="center-align">Pkg Qty</th>
                                        <th class="center-align">Ratio</th>
                                        <th class="center-align">Total Qty</th>
                                        <th class="center-align">Price</th>
                                        <th class="center-align" tooltipped
                                            data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*
                                        </th>
                                        <th class="center-align">Taxes</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="item in invR.items track by $index">
                                        <td class="center-align"><a
                                                data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.sku.name}}[{{item.sku.code}}]</a>
                                        </td>
                                        <td class="center-align">{{item.uom}}</td>
                                        <td class="center-align">{{item.pkg.name}}</td>
                                        <td class="center-align">{{item.pkgQty}}</td>
                                        <td class="center-align">{{item.ratio}}</td>
                                        <td class="center-align">{{item.qty.toFixed(2)}}</td>
                                        <td class="center-align">{{item.pkgPrice.toFixed(2)}}</td>
                                        <td class="center-align">{{(item.sellAmount + item.totalTax).toFixed(2)}}</td>
                                        <td class="center-align">{{item.totalTax.toFixed(2)}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-----------   Printable Invoice Section   ------------->
<div class="row" id="printSection">
    <div class="col s12">
        <p style="text-align: center;">
            <b><em><span style="font-family: 'Cambria', serif;">Form GST INV &ndash; 1</span></em></b>
            <br/>
            <b><span
                    style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br/></span></b><b>
            <span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[selectedInvoice.sendingCompany.id].name}}<br/></span></b>
            <span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.line1}},
						{{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.line2}},
                <br/> {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.city}},
						{{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.state}},
                <br/> {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.country}},
						{{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.zipCode}}
                <br/>
            </span>
            <b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Invoice</span></b>
        </p>
        <table style="border-collapse: collapse; border: none;" cellpadding="0cm 5.4pt">
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Company Details</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">

                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Vendor Details </span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Billed To/Shipped To</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.name}}</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vendor Name </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.vendor.name}}</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[selectedInvoice.sendingCompany.id].name}}</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.vendor.name}}</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                                {{selectedInvoice.dispatchAddress}}
                        </span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.deliveryAddress}}
                        </span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.from.name}}/{{selectedInvoice.from.code}}</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.to.name}}/{{selectedInvoice.to.code}}</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.tin}}</span>
                    </p>
                </td>
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.dispatchLocation.code}}</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[selectedInvoice.sendingCompany.id].cin}}</span>
                    </p>
                </td>
                <td style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.vendor.code!=null ? selectedInvoice.vendor.code : 'N/A'}}</span>
                    </p>
                </td>
            </tr>
            </tbody>
        </table>
        <p>
            <span style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
        </p>

        <p>
            <span style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
        </p>
        <table>
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="2">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Details</span></b>
                    </p>
                </td>
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice No.</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.generatedId}}</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">

                <td style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.items.length}}</span>
                    </p>
                </td>

                <td style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date of Invoice</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.dispatchDate| date:'dd-MM-yyyy':'+0530'}}</span>
                    </p>
                </td>
            </tr>

            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place Of Supply:</span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.city}}</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Mode of Transport : </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">By Road / By Train / By Air</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">

                </td>
                <td style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;"></p>
                </td>
                <td style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vehicle No :</span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">____________________</span>
                    </p>
                </td>
            </tr>
            </tbody>
        </table>
        <p>
            <span style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
        </p>

        <p>
            <span style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
        </p>
        <table style="border-collapse: collapse; border: none;" width="765">
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="100">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
                    </p>
                </td>
                <td style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="61">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
                    </p>
                </td>
                <td style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="49">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Qty.</span></b>
                    </p>
                </td>
                <td style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="49">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Uom</span></b>
                    </p>
                </td>
                <td style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="67">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
                    </p>
                </td>
                <td style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="84">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
                    </p>
                </td>
                <td style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>
                    </p>
                </td>
                <td style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                    width="{{132/availableTaxes.length}}">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Taxes</span></b>
                    </p>
                </td>
                <td style="width: 66.8pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total</span></b>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt;  page-break-inside: avoid;"
                data-ng-repeat="item in selectedInvoice.items track by $index">
                <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="100">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.sku.name}}</span>
                    </p>
                </td>
                <td style="width: 46.1pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="61">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.sku.code}}</span>
                    </p>
                </td>
                <td style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="49">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.qty}}</span>
                    </p>
                </td>
                <td style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="49">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.uom}}</span>
                    </p>
                </td>
                <td style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="67">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.sellPrice| currency :'': 2}}</span>
                    </p>
                </td>
                <td style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="84">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.sellAmount | currency :'': 2}}</span>
                    </p>
                </td>

                <td style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        {{item.pkgQty}} -- {{item.pkg.name}}
                    </p>
                </td>

                <td style="border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="{{132/item.taxes.length}}">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span data-ng-repeat="t in item.taxes"
                              style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t.value}} ({{t.type}}@{{t.percent}}%) </span><br/>
                    </p>
                </td>
                <td style="width: 66.8pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            {{(item.sellAmount + item.totalTax)| currency :'': 2}}
                        </span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="{{8 + availableTaxes.length}}" width="676">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>
                    </p>
                </td>
                <td style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.totalAmount| currency :'': 2}}</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="{{8 + availableTaxes.length}}" width="676">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif;">Additional Charges (In figure)</span></b>
                    </p>
                </td>
                <td style="width: 66.8pt; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedInvoice.additionalCharges | currency :'': 2}}</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="{{8 + availableTaxes.length}}" width="676">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif;">Total Value (In figure)</span></b>
                    </p>
                </td>
                <td style="width: 66.8pt; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            {{(selectedInvoice.totalAmount + selectedInvoice.additionalCharges)| currency :'': 2}}
                        </span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="{{9 + availableTaxes.length}}" width="765">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total Challan Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                        <b><span style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalAmountInWords}}</span></b>
                    </p>
                </td>
            </tr>
            </tbody>
        </table>
        <p style="margin: .0001pt 0; line-height: normal;">
            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
        </p>
        <p style="line-height: normal; margin: 0cm 0cm .0001pt">
            <b><span style="font-family: 'Cambria', serif;">Certified
                that the particulars and the amount indicated given above are
                true and correct </span></b>
        </p>
        <table style="border-collapse: collapse; border: none;" width="765">
            <tbody>
            <tr style="height: 20.00pt; page-break-inside: avoid;">
                <td style="width: 404.0pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                    width="520">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b>
                            <span style="font-size: 14.0pt; font-family: 'Cambria', serif; color: black;">TERMS OF SALE</span>
                        </b>
                    </p>
                </td>
                <td style="width: 6.0cm; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                    width="246">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-family: 'Cambria', serif; color: black;">
                            For {{companyMap[selectedInvoice.sendingCompany.id].name}}
                        </span></b>
                    </p>
                </td>
            </tr>
            <tr style="height: 73.6pt; page-break-inside: avoid;">
                <td style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                    width="510">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">1)Goods
                            once sold will not be taken back or exchanged&nbsp; </span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">2)Seller is
                            not responsible for any loss or damaged of goods in transit</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">3)Buyer
                            undertakes to submit prescribed declaration to sender on
                            demand.</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">4)Disputes if
										any will be subject to seller court jurisdiction</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                    </p>
                </td>
                <td style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                    width="256">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 17.45pt; page-break-inside: avoid;">
                <td style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
                    width="520">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                    </p>
                </td>
                <td style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
                    width="246">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-family: 'Cambria', serif;">Authorised Signatory</span></b>
                    </p>
                </td>
            </tr>
            </tbody>
        </table>
        <p>
            <span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                Reg. Address: {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.line1}},
                            {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.line2}},
                            {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.city}},
                            {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.state}},
                            {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.country}},
                            Pin No: {{companyMap[selectedInvoice.sendingCompany.id].registeredAddress.zipCode}} </span>
        </p>
    </div>
</div>

<div id="screenshotUploadModal" class="modal">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <label>Please Upload Screenshot to Cancel This Invoice!</label>
                <input type="button" value="Upload File" data-ng-click="uploadCancelInvoiceDoc()" class="btn btn-small" />
                <input type="button" value="DownloadInvoice" data-ng-if="uploadedDocData!=null"
                       data-ng-click="downloadCancelInvoice(uploadedDocData)" class="btn btn-small"/>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat" data-ng-click="closeModal()">close</button>
        <button class="modal-action modal-close waves-effect waves-green btn" data-ng-click="cancelPreview(cancelInvoiceId,cancelInvoiceIndex)">CancelInvoice</button>
    </div>
</div>
<div id="updateDocNoModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <label>Please Enter Uploaded Document Number</label>
                <input type="text" data-ng-model="UploadedDocumentNumber"/>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" ng-click="updateDocumentNumber(UploadedDocumentNumber)">Submit</button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>
<div id="enterDistanceModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <label>Please Enter Seller and Buyer Distance</label>
                <input type="text" data-ng-model="calculatedDistance"/>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" data-ng-click="downloadEportalJson(selectedJsonInvoice.id)">Download Json</button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;" data-ng-click="resetDistance()">Close</button>
    </div>
</div>
