<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
  
<style>
.data-unavailable {
    color: #ee6e73;
    border: 1px dashed;
}
</style>
<div class="row" data-ng-init="init()">
	<div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m6 l6">
					<h4>Current Pricing</h4>
				</div>
				<input type="button" data-ng-show="showGetPriceBtn && selectedUnitCombo" class="btn right margin-top-10"
                       value="View Prices" data-ng-click="getAllPricedetailsForUnit()"/>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m6 l6">
				<label>Select Unit:</label>
				<select ui-select2 ng-model="selectedUnitCombo" data-placeholder="Select Unit" data-ng-change="setSelectedUnit(selectedUnitCombo)" >
					<option value=""></option>
					<option ng-repeat="unit in scmUnitList" value="{{unit}}">{{unit.name}}</option>
				</select>
			</div>
			<div class="col s12 m4 l4" data-ng-if="!isWHOrKitchen">
				<label>Select Product:</label>
				<select ui-select2 ng-model="selectedProduct" data-placeholder="Enter name of product" data-ng-change="setSelectedProduct(selectedProduct)" id="currentPriceProductId">
					<option value=""></option>
					<option ng-repeat="product in scmProductDetails" value="{{product}}">{{product.productName}}</option>
				</select>
			</div>
			<div class="col s12 m4 l4" data-ng-if="isWHOrKitchen">
				<label>Select Sku :</label>
				<select ui-select2 ng-model="selectedSku" data-placeholder="Enter name of sku"  data-ng-change="setSelectedSku(selectedSku)">
					<option value=""></option>
					<option ng-repeat="sku in scmSkuDetails" value="{{sku}}">{{sku.skuName}}</option>
				</select>
			</div>
			<div class="col s12 m2 l2">
				<input type="button" data-ng-show="showGetPriceBtn" class="btn right margin-top-20" value="Get Price" data-ng-click="getPriceDetails()"/>
			</div>
		</div>
		<div data-ng-if="priceDetailList.length>0">
			<div class="row">
				<div class="col s12">
					<h5>Price Details For Unit {{selectedUnit.name}} for selected item</h5>
				</div>
			</div>
			<div class="row margin0">
				<div class="col s12">
					<ul class="collection striped">
						<li class="collection-item list-head">
							<div class="row margin0">
								<div class="col s1">Item Id</div>
								<div class="col s2">Type</div>
								<div class="col s2">Name</div>
								<div class="col s1">UOM</div>
								<div class="col s1">Qty</div>
								<div class="col s1">Price</div>
								<div class="col s2">Expire Date</div>
                                <div class="col s2 center" acl-action="UPDPRI">Action</div>
							</div>
						</li>
					</ul>
					<ul class="collection striped" style="max-height: 350px; overflow: auto;">
						<li class="collection-item" data-ng-class="{'red white-text':priceData.latest}"
                            data-ng-repeat="priceData in priceDetailList">
							<div class="row margin0">
								<div class="col s1"><a data-ng-click="showPreview($event, priceData.keyId,priceData.keyType)">{{priceData.keyId}}</a></div>
								<div class="col s2">{{priceData.keyType}}</div>
								<div class="col s2">{{priceData.name}}</div>
								<div class="col s1">{{priceData.uom}}</div>
								<div class="col s1">{{priceData.quantity}}</div>
								<div class="col s1">{{priceData.price}}</div>
								<div class="col s2">{{priceData.expiryDate | date:'dd/MM/yyyy HH:mm:ss'}}</div>
                                <div class="col s2" acl-action="UPDPRI">
                                    <button class="btn btn-xs-small right" data-ng-click="updateDetails(priceData)">Update Price</button>
                                </div>
							</div>
						</li>
                        <li class="collection-item">
                            <div class="row margin0">
                                <div class="col s2">Weighted Price</div>
                                <div class="col offset-s7 s1">{{weightedPrice}}</div>
                            </div>
                        </li>
					</ul>
				</div>
			</div>
		</div>
		<div class="row data-unavailable" data-ng-show="priceDetailList.length==0">
			<div class="col s12">
				<h5>Price details  not available for {{selectedUnit.name}} for selected item</h5>
			</div>
		</div>
		<div class="row margin0" data-ng-show="!showDetailsForm">
			<div class="col s12" style="margin: 7px;">
				<button class="btn right" acl-action="ADDPRI" data-ng-click="addDetails('ADD')">Add New</button>
			</div>
		</div>
		<div class="row" data-ng-show="showDetailsForm">
			<div class="col s12">
				<h5>Fill Details </h5>
			</div>
			<div class="row">
				<div class="col s3">
					<label>Price :</label>
					<input type="number" name="price" data-ng-model="costDetail.price"  required />
				</div>
				<div class="col s3">
					<label>Quantity :</label>
					<input type="number" name="quantity" data-ng-model="costDetail.quantity"  required />
				</div>
				<div class="col s3">
					<label>Expire Date :</label>
					<input input-date type="text" data-ng-model="costDetail.expiryDate" container="" format="yyyy-mm-dd" required/>
				</div>
				<div class="col s3">
					<label>Expire Time :</label>
					<select ng-model="costDetail.expiryTime" data-placeholder="Enter Expiry Date" >
						<option value="01">1 AM</option>
						<option value="13">1 PM</option>
					</select>
				</div>
			</div>
			<div class="row" >
				<div class="col s6 margin-top-10" >
					<button class="btn" data-ng-click="addPriceDetails(costDetail.price, costDetail.quantity, costDetail.expiryDate)">
	   Submit</button>
				</div>
				<div class="col s6 margin-top-10" >
					<button class="btn" data-ng-click="cancelAddDetails()">Cancel</button>
				</div>
			</div>
		</div>
	</div>
	<div  data-ng-show="showPricedetailsForUnit">
		<div class="row margin0" data-ng-if="gridOptions.data!=null && allPriceDetailListForUnit.length>0">
			<div
			class="col s12 grid"
			id="grid"
			ui-grid="gridOptions"></div>
		</div>
		<div class="row data-unavailable" data-ng-if="allPriceDetailListForUnit.length==0">
			<div class="col s12">
				<h5>Price details  not available for unit {{selectedUnit.name}} </h5>
			</div>
		</div>
	</div>
</div>
	
