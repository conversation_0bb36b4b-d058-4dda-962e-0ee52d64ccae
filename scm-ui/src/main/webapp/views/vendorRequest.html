<style>
    .sku-title-strip .col{
        word-break: break-all;
    }
</style>
<div data-ng-init="init()">
    <div class="row">
        <div class="col s6">
            <div class="left">
                <h5 style="margin:0px;">Vendor Request</h5>
            </div>
        </div>
    </div>


    <div class="row" id="vendorAddRequestDiv">
        <div class="col s12" style="margin-top: 20px">	<button data-ng-click="viewRequest()" class="btn right">BACK</button></div>
        <form id="Vendor" name="vendorRequestForm" class="white z-depth-3 scm-form" >
            <div class="row">
                <div class="col s12">
                    <label class="black-text" for="vendorTypeList">Vendor Type</label>
                    <select class="selectedUnit" ui-select2="selectedVendorType" id="vendorTypeList" name="vendorTypeList" data-ng-model="vendorTypeListData" data-ng-change="selectVendorType(vendorTypeListData)"
                            data-ng-options="vendorType as vendorType for vendorType in vendorTypes"></select>
                </div>
                <div class="col s12">
                    <label class="black-text" for="requestForList">Request For</label>
                    <select ui-select2="selectedUnit" id="requestForList" name="requestForList" data-ng-model="requestForListData"
                            data-ng-options="requestForName as requestForName.name for requestForName in activeUserList track by requestForName.id" data-ng-change="showRequestFor(requestForListData)"></select>
                </div>
                <div class="col s12">
                    <label class=" black-text" for="vendorRequestName">Name</label>
                    <input id="vendorRequestName" name="vendorRequestName"	data-ng-model="vendorRequestNameData" type="text" ng-maxlength="70">
                </div>
                <div class="col s12">
                    <label class="black-text" for="vendorRequestEmail">Email</label> <input id="vendorRequestEmail" name="vendorRequestEmail" type="text" data-ng-model="vendorRequestEmailData"/>
                </div>
                <div class="col s12">
                    <label class="black-text" for="vendorRequestCopyEmail">Copy Email</label> <input id="vendorRequestCopyEmail" name="vendorRequestCopyEmail" type="text" data-ng-model="vendorRequestCopyEmailData" />
                </div>
                <div class="col s12" style="margin-top: 20px">
                    <button data-ng-click="sendRequest()" class="btn right">Add</button>
                </div>
            </div>
        </form>
    </div>



    <div id="viewDateDiv">
        <div class="col s12" style="margin-top: 20px">	<button data-ng-click="showAddRequest()" class="btn right" acl-action="SVMVRA">Add</button></div>
         <div class="col s6">
                    <label class="black-text" for="selectedStatus">Search By Status</label>
                    <select ui-select2="selectedStatus" id="selectedStatus" name="selectedStatus" data-ng-model="selectedStatus"
                            data-ng-options="status for status in vendorStatusList" data-ng-change="showViewRequest()"></select>
         </div>
        <!--<div class="col s6" data-ng-if="sendRequestObj.length>0">
            <label class="black-text">Search By Entity Name</label>
            <input type="text" id="vendorName" name="vendorName" data-ng-model="reqName">
        </div>-->
       <!--  <div class="col s1" style="margin-top: 20px">	<button data-ng-click="showViewRequest()" class="btn right">View</button></div> -->
    </div>

    <div class="row" id="requestForViewDiv">
        <div class="col s12" style="font-size:12px">
            <ul class="collection" data-ng-hide="showMessage">
                <li class="collection-item list-head">
                    <div class="row" >
                        <div class="col s1">Vendor</div>
                        <div class="col s1">Type</div>
                        <div class="col s1">Requested On</div>
                        <div class="col s1">Request By</div>
                        <div class="col s1">Request For</div>
                        <div class="col s2">Vendor Email</div>
                        <div class="col s1">Detail</div>
                        <div class="col s2">Status</div>
                        <div class="col s1">Lead Time</div>
                        <div class="col s1">Action</div>
                    </div>
                </li>
                <li class="collection-item" data-ng-repeat="requestObj in sendRequestObj">
                    <div class="row sku-title-strip">
                        <div class="col s1">{{requestObj.vendorName}}</div>
                        <div class="col s1">{{requestObj.vendorType}}</div>
                        <div class="col s1">{{requestObj.requestDate | date}}</div>
                        <div class="col s1">{{requestObj.requestByName}}</div>
                        <div class="col s1">{{requestObj.requestForName}}</div>
                        <div class="col s2">{{requestObj.email}}</div>
                        <div class="col s1">
                            <span data-ng-if="requestObj.vendorId==null && requestObj.requestStatus != 'COMPLETED'">--</span>
                            <a class="pointer" data-ng-if="requestObj.vendorId!=null
                                    && (requestObj.requestStatus=='COMPLETED' || requestObj.requestStatus=='APPROVED')"
                               data-ng-click="viewDetails(requestObj.vendorId, requestObj.id)" acl-action="SVMVVI">Details</a>
                        </div>
                        <div class="col s2">{{requestObj.requestStatus}}</div>
                        <div class="col s1">
                            <button data-ng-if="requestObj.requestStatus=='INITIATED'"	class="btn-floating red" style="margin-bottom: 5px">
                                <div style="color: white; font-size: 25px" data-ng-click="requestStatusChange(requestObj.id)" acl-action="SVMVRC">X</div>
                            </button>
                            <div data-ng-if="requestObj.requestStatus=='COMPLETED' && showApproveButton">
                                <input type="number" data-ng-model="requestObj.leadTime" placeholder="Time" acl-sub-menu="VOMRPO" style="width:60px;">
                                <button class="btn btn-xs-small" style="margin-bottom: 5px; width:70px;"
                                        data-ng-click="addLeadTime(requestObj)" acl-sub-menu="VOMRPO">Add Time</button>

                            </div>
                        </div>
                        <div class="col s1">
                            <button data-ng-if="requestObj.requestStatus=='INITIATED'"	class="btn-floating red" style="margin-bottom: 5px">
                                <div style="color: white; font-size: 25px" data-ng-click="requestStatusChange(requestObj.id)" acl-action="SVMVRC">X</div>
                            </button>
                            <div data-ng-if="requestObj.requestStatus=='COMPLETED' && showApproveButton">
                                <input type="number" data-ng-model="requestObj.creditCycle" placeholder="Credit Days" style="width:60px;">
                                <button class="btn btn-xs-small" style="margin-bottom: 5px; width:70px;"
                                        data-ng-click="activateVendor(requestObj.creditCycle,requestObj)" acl-action="SVMVAP">Approve</button>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <div data-ng-show="showMessage" align="center"><h5>No record found.</h5></div>
        </div>
    </div>
</div>

<!--   <div id="confirm" class="modal modal-small">
  <div class="modal-content">
      <p>Are you sure?</p>
  </div>
  <div class="modal-footer">
      <button class="modal-action modal-close waves-effect waves-green btn-flat"
              data-ng-click="changeStatus(productDetail.productId)">Yes
      </button>
      <button class="modal-action modal-close waves-effect waves-green btn-flat">No</button>
  </div>
</div> -->
