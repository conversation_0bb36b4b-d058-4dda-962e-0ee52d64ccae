<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 ">
            <div class="col s8">

                <div id ="viewPackaging"  class="input-field margin-bottom-5" ng-show="viewPackages">
                    <h4>Search Packaging</h4>
                    <input type="text" placeholder="Search packaging" ng-model="searchPackaging"/>
                </div>
            </div>
            <div  class="col s4" ng-show="viewPackages">
                <input type="button" class="btn right margin-top-30" value="Add new packaging"
                       ng-click="onSelectAddNewPackaging()"/>
            </div>
            <div class="col s8">
                <div class="input-field margin-bottom-5" ng-show="createNewPackaging">
                    <h4>Product Packaging</h4>
                    <select ng-model="innerOrCase" ng-options="types for types in packageType"
                            ng-change="selectPackagingType()">
                    </select>
                </div>
            </div>
            <div class="col s4" ng-show="createNewPackaging">
                <input type="button" class="btn right margin-top-30"
                       value="View packaging"
                       ng-click="viewPackaging()">
            </div>


        </div>
    </div>
</div>
</div>


<div class="row overflow" ng-show="viewPackages">
    <div class="col s12">
        <div class="row ">
            <table class="bordered">
                <tr class="collection-item list-head">
                    <th>PACKAGING_ID</th>
                    <th>PACKAGING_TYPE</th>
                    <th>PACKAGING_CODE</th>
                    <th>PACKAGING_NAME</th>
                    <th>UNIT_OF_MEASURE</th>
                    <th>SUB_PACKAGING</th>
                </tr>
                <tr class="collection-item list-item" ng-repeat="fields in packagingArray|filter:searchPackaging">
                    <td style="text-align: center">{{fields.packagingId}}</td>
                    <td style="text-align: center">{{fields.packagingType}}</td>
                    <td>{{fields.packagingCode}}</td>
                    <td>{{fields.packagingName}}</td>
                    <td style="text-align: center">{{fields.unitOfMeasure}}</td>
                    <td style="text-align: center">{{fields.subPackagingId}}</td>
                </tr>
            </table>
        </div>
    </div>
</div>

<form id="package" name="productPackage" novalidate>
    <div class="row">
        <div class="col s12">
            <div class="form-element">
                <div class="input field" ng-show="isInner&&createNewPackaging">
                    <label for="innerName">INNER:</label>
                    <select name="innerName"
                            id="innerName"
                            ng-model="innerName"
                            ng-options="innerName for innerName in innerOrCaseName" required></select>
                    <p ng-show="productPackage.innerName.$error.required"
                       class="errorMessage">Package name is required</p>
                    <br>
                    <br>
                    <label for="innerUom">UOM:</label>
                    <select
                            name="innerUom"
                            id="innerUom"
                            ng-model="innerUom"
                            ng-options="innerUom.unitOfMeasure for innerUom in packagingArray|uniqueValueFilter:'unitOfMeasure'|orderBy:'unitOfMeasure'"
                            required></select>
                    <p ng-show="productPackage.innerUom.$error.required"
                       class="errorMessage">Package Unit of Measure is required</p>
                    <br>
                    <br>
                    <label for="innerQuantity">INNER QUANTITY:</label>
                    <input
                            name="innerQuantity"
                            id="innerQuantity"
                            type="number"
                            ng-model="innerQuantity"
                            placeholder="enter  number of inner required" required/>
                    <p ng-show="productPackage.innerQuantity.$error.required"
                       class="errorMessage">Quantity is required</p>
                    <br>
                    <br>
                    <div ng-show="innerQuantity">
                        <label>CONVERSION RATIO:</label>
                        <p>{{innerQuantity}}</p>
                    </div>
                    <br>
                    <br>
                    <label>PACKAGE NAME:</label>
                    <p>{{innerQuantity}} {{innerUom.unitOfMeasure}} {{innerName}}</p>
                    <br>
                    <br>
                    <div class="vBtn">
                        <input type="button" class="btn"
                               style="width:110px; length:250px"
                               value="Submit"
                               ng-click="onSubmittingInner(innerOrCase,innerName,innerQuantity,innerUom.unitOfMeasure,innerUom.packagingId)"/>


                    </div>
                </div>


                <div class="input field" ng-show="isCase&&createNewPackaging">
                    <label for="caseName">CASE:</label>
                    <select
                            name="caseName"
                            id="caseName"
                            ng-model="caseName"
                            ng-options="caseName for caseName in innerOrCaseName" required></select>
                    <p ng-show="productPackage.caseName.$error.required"
                       class="errorMessage">Package name is required</p>
                    <br>
                    <br>
                    <label for="caseUom">UOM:</label>
                    <select
                            name="caseUom"
                            id="caseUom"
                            ng-model="caseUom"
                            ng-options="uom.unitOfMeasure for uom in packagingArray |uniqueValueFilter:'unitOfMeasure'|orderBy:'unitOfMeasure'"
                            required></select>
                    <p ng-show="productPackage.caseUom.$error.required"
                       class="errorMessage">Package Unit of Measure is required</p>
                    <br>
                    <br>
                    <label for="innerFromDatabase">INNER:</label>
                    <select
                            name="innerFromDatabase"
                            id="innerFromDatabase"
                            ng-model="innerFromDatabase"
                            ng-options="packagings.packagingName for packagings in packagingArray |filter:'INNER'|filter:caseUom.unitOfMeasure"
                            required>
                    </select>
                    <p ng-show="productPackage.innerFromDatabase.$error.required"
                       class="errorMessage">Package inner packaging is required</p>
                    <br>
                    <br>
                    <label for="caseQuantity">CASE QUANTITY:</label>
                    <input
                            name="caseQuantity"
                            id="caseQuantity"
                            type="number"
                            ng-model="caseQuantity"
                            placeholder="enter number of case required" required/>

                    <p ng-show="productPackage.caseQuantity.$error.required && productPackage.caseQuantity.$pristine"
                       class="errorMessage">Quantity is required</p>
                    <br>
                    <br>
                    <div ng-show="caseQuantity">
                        <label>CONVERSION RATIO:</label>
                        <p>{{caseQuantity*innerFromDatabase.conversionRatio}}</p>
                    </div>
                    <br>

                    <label>PACKAGE NAME:</label>
                    <p>{{innerFromDatabase.conversionRatio}} {{caseUom.unitOfMeasure}} {{caseQuantity}}
                        {{innerFromDatabase.packagingName.split(" ").slice(-1)[0]}} {{caseName}}</p>
                    <br>
                    <div class="vBtn">
                        <input type="button" class="btn"
                               value="Submit"
                               style="width:110px; length:250px"
                               ng-click="onSubmittingCase(innerOrCase,caseName,caseQuantity,caseUom.unitOfMeasure,innerFromDatabase)"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>




