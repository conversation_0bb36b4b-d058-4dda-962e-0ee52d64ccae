<div class="row white z-depth-3 " data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 teal lighten-2 center-align">
            <div class="col s12">
                <h4 class="left">Product Shelf Life Defination</h4>
            </div>
        </div>
    </div>


    <div class="row margin-30" style="height: auto;" >
        <div class=" col s6 teal lighten-5 black-text center">
            <label>Select Category</label>
            <select class="categoryList" data-ng-model="selectedCategory"
                    data-placeholder="Select some Category" ui-select2>
                <option data-ng-repeat="category in categories "
                        value="{{category.id}}">{{category.name}}
                </option>
            </select>

        </div>
        <div data-ng-if="selectedCategory!=null" class="col s6 teal lighten-5 black-text center">
            <label>Select Sub Category</label>
            <select class="subCategoryList" data-ng-model="selectedSubCategory"
                    data-placeholder="Select Sub Category" data-ng-change="selectSubCategory(selectedSubCategory)" ui-select2>
                <option data-ng-repeat="subCategory in getSubCategories(selectedCategory) "
                        value="{{subCategory}}">{{subCategory.subCategoryName}}
                </option>
            </select>
        </div>
    </div>

    <div data-ng-if="show" class="row margin-30" style="height: auto;">
        <div>
            <div class="col s4  lime lighten-2 black-text">
                <label for="defaultValue">Default Value : </label>
                <input type="text" id="defaultValue" ng-model="values.defaultValue" data-ng-change="validate()"
                       required/>
            </div>
            <div class="col s4 lime lighten-2 black-text center">
                <label for="min">Min Range</label>
                <input type="text" id="min" name="minRange" data-ng-change="validate()"
                       data-ng-model="values.minRange" required/>
                <p data-ng-show="!isInrange" class="errorMessage">Default Value Not In Range</p>
            </div>
            <div class="col s4  lime lighten-2 black-text center">
                <label for="max">Max Range </label>
                <input type="text" id="max" name="maxRange" ng-model="values.maxRange"
                       data-ng-change="validate()"
                       required/>
                <p data-ng-show="!isValid" class="errorMessage">Max Range Can't Be Less Than Min</p>
                <p data-ng-show="!isInrange" class="errorMessage">Default Value Not In Range</p>
            </div>
        </div>
    </div>
    <div data-ng-if="isValid && show" class="right">
        <button class="btn btn-large fa fa-arrow-circle-right" data-ng-click="updateShelfLife()">Submit</button>
    </div>

</div>
