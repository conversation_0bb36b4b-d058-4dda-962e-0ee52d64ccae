<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Search Product</h4>
                <div class="input-field">
                    <select ui-select2="productSelectOptions" ng-model="productId" data-ng-change="selectProduct(productId)" data-placeholder="Enter name of a product">
                        <option ng-repeat="product in products track by product.productId" value="{{product.productId}}">{{product.productName}}</option>
                    </select>
                </div>
            </div>
        </div>
        <!--<div class="row white z-depth-3 custom-listing-li">-->
            <!--<div class="col s12">-->
                <!--<h4>Search Attribute Mappings</h4>-->

                <!--<div class="input-field">-->
                    <!--<select ui-select2="categorySelectOptions" ng-model="category"-->
                            <!--data-ng-change="getMappings(category)"-->
                            <!--data-placeholder="Enter name of a category">-->
                        <!--<option ng-repeat="category in categories"-->
                                <!--value="{{category}}">{{category.categoryName}}-->
                        <!--</option>-->
                    <!--</select>-->
                <!--</div>-->
            <!--</div>-->
        <!--</div>-->

        <div data-ng-if="selectedMappings.length != 0" class="row white z-depth-3">
            <div class="row">
                <div class="right mappingBtn">
                    <a class="btn" href="#add" data-ng-click="prepareAttributes(selectedMappings,false)" modal>Add</a>
                </div>
                <div class="flow-text mappingBtn">
                    List of Mappings
                </div>
            </div>
            <table class="responsive-table centered">
                <thead>
                <tr>
                    <th>Mandatory</th>
                    <th>Used in Naming</th>
                    <th>Attribute</th>
                    <th>Category</th>
                    <th>Mapping Order</th>
                    <th>Status</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="mapping in selectedMappings">
                    <td>{{mapping.mandatory ? 'YES' : 'NO'}}</td>
                    <td>{{mapping.usedInNaming ? 'YES' : 'NO'}}</td>
                    <td>{{mapping.attributeDefinition.name}}</td>
                    <td>{{mapping.categoryDefinition.name}}</td>
                    <td>{{mapping.mappingOrder}}</td>
                    <td>{{mapping.mappingStatus}}</td>
                    <td><a class="btn" href="#edit" data-ng-click="editMapping(mapping)" modal>Edit</a></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div data-ng-if="selectedMappings.length == 0" class="row white z-depth-1">
            <div class="right mappingBtn">
                <a class="btn" href="#add" data-ng-click="prepareAttributes(null,false)" modal>Add</a>
            </div>
            <div class="flow-text mappingBtn">
                No Mappings found for this category
            </div>
        </div>
    </div>
</div>

<!-- Modal Structure -->
<div id="add" class="modal modal-large">
    <div class="modal-content">
        <div class="row">
            <h4>Add Mappings for: {{getCategory(categoryId).categoryName}}</h4>
        </div>
        <div class="row">
            <div class="col s12" data-ng-repeat="(key,attributeList) in attributes">
                <label data-ng-if="key=='ASSET'" class="black-text">{{key}}</label>

                <div data-ng-if="key == 'ASSET'" class="row">
                    <div class="col s3" data-ng-repeat="attribute in attributeList">
                        <div class="row">
                            <div class="input-field">
                                <input id="{{attribute.attributeName}}" data-ng-model="attribute.checked"
                                       data-ng-init="attribute.checked=false"
                                       data-ng-click="changeState(attribute.checked,attribute)"
                                       type="checkbox" class="validate"/>
                                <label class="black-text"
                                       for="{{attribute.attributeName}}">{{attribute.attributeName}}</label>
                            </div>
                        </div>
                        <div class="sub-form row" data-ng-if="attribute.checked">
                            <form>
                                <div class="input-field">
                                    <input id="{{attribute.attributeName}}-isMandatory" type="checkbox"
                                           data-ng-model="attribute.mapping.mandatory"
                                           class="validate"/>
                                    <label class="black-text" for="{{attribute.attributeName}}-isMandatory">Is
                                        Mandatory</label>
                                </div>
                                <div class="input-field" style="margin-bottom:2rem;">
                                    <input id="{{attribute.attributeName}}-isUsedInNaming" type="checkbox"
                                           data-ng-model="attribute.mapping.usedInNaming"
                                           class="validate"/>
                                    <label class="black-text" for="{{attribute.attributeName}}-isUsedInNaming">Included
                                        in Naming</label>
                                </div>
                                <div class="input-field" >
                                    <input id="{{attribute.attributeName}}-mappingOrder" type="number"
                                           data-ng-model="attribute.mapping.mappingOrder"
                                           class="validate black-text"/>
                                    <label class="black-text active" for="{{attribute.attributeName}}-mappingOrder">Mapping
                                        Order</label>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat" data-ng-click="submitMappings(editMode, mappings)">Submit</button>
        <button class="modal-action modal-close waves-effect waves-green btn-flat" data-ng-click="reset()">Cancel</button>
    </div>
</div>

<!-- Modal Structure -->
<div id="edit" class="modal modal-small">
    <div class="modal-content">
        <div class="row">
            <h4>Edit Mapping for: {{getCategory(categoryId).categoryName}}</h4>
        </div>
        <div class="row">
            <div class="col s12">
                <div class="row">
                    <form>
                        <h4 class="flow-text">Mapping to {{mappingToEdit.attributeName}}</h4>
                        <div class="input-field">
                            <input id="{{mappingToEdit.attributeName}}-mandatory" type="checkbox"
                                   data-ng-model="mappingToEdit.mapping.mandatory"
                                   class="validate black-text"/>
                            <label class="black-text"
                                   for="{{mappingToEdit.attributeName}}-mandatory">Mandatory</label>
                        </div>
                        <div class="input-field" style="margin-bottom:2rem;">
                            <input id="{{mappingToEdit.attributeName}}-usedInNaming" type="checkbox"
                                   data-ng-model="mappingToEdit.mapping.usedInNaming"
                                   class="validate black-text"/>
                            <label class="black-text" for="{{mappingToEdit.attributeName}}-usedInNaming">Used in
                                Naming</label>
                        </div>
                        <div class="input-field" >
                            <input id="{{mappingToEdit.attributeName}}-mappingOrder" type="number"
                                   data-ng-model="mappingToEdit.mapping.mappingOrder"
                                   class="validate black-text"/>
                            <label class="black-text" for="{{mappingToEdit.attributeName}}-mappingOrder">Mapping Order</label>
                        </div>
                        <div class="input-field">
                            <select id="{{mappingToEdit.attributeName}}-mappingStatus" data-ng-model = "mappingToEdit.mapping.mappingStatus">
                                <option value="IN_ACTIVE">IN_ACTIVE</option>
                                <option value="ACTIVE" data-ng-selected = "mappingToEdit.status">ACTIVE</option>
                            </select>
                            <label class="black-text active" for="{{mappingToEdit.attributeName}}-mappingStatus">Mapping Status</label>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat" data-ng-click="submitMappings(true,[mappingToEdit.mapping])">Submit</button>
        <button class="modal-action modal-close waves-effect waves-green btn-flat">Cancel</button>
    </div>
</div>



