<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .select2.select2-container {
        width: 100% !important;
    }
</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3 class="left">Mail TDS Certificate To Vendor</h3>
            </div>
        </div>

        <div class="row">
            <div class="col s12">
                <div style="text-align: center">
                    <input type="button" class="btn" value="Upload Mailing Sheet"
                           data-ng-click="uploadDoc()"/>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <div style="text-align: center">
                    <!--<input type="button" class="btn" acl-action="VNUDB" value="Locate PDF Folder" />-->
                    <div data-ng-if="toggleUploadFilesBtn">
                        <span style="font-weight:bold">Select all certificates    </span>
                        <input type="file" class="btn" id="multi-tds-pdf"
                               name="files" multiple accept=".pdf,.docx"><br><br>
                    </div>

                </div>
            </div>
        </div>

        <div class="row" data-ng-if="excelEntryList.length>0">
            <div class="col s12">
                <table class="table striped" style="border:#ddd 1px solid;margin-bottom: 10px;">
                    <thead>
                    <tr>
                        <th>Name of Party</th>
                        <th>Pan Number</th>
                        <th>Primary Email Id of Customer</th>
                        <th>Secondary Email Id of Customer</th>
                        <th>Status</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="d in excelEntryList">
                        <td>{{d["Name of Party"]}}</td>
                        <td>{{d["Pan Number"]}}</td>
                        <td>{{d["Primary Email Id of Customer"]}}</td>
                        <td>{{d["Secondary Email Id of Customer"]}}</td>

                        <td style="color: green" data-ng-show="d.mailSent === true"><i class="material-icons">check</i></td>
                        <td style="color: red" data-ng-show="d.mailSent === false"><i class="material-icons">clear</i></td>
                    </tr>
                    </tbody>
                </table>
                <input style="text-align: center" type="button" data-ng-click="sendEmails()" value="Send To Vendor"
                       class="btn green"/>
            </div>
        </div>
    </div>

</div>
