<div class="row" style="margin-bottom: 0px; margin-top: -20px"
	data-ng-init="init()">
	<div class="col s12">
		<h4>New Cost Element</h4>
	</div>
</div>
<div class="row">
	<div class="row white z-depth-3 custom-listing-li">
		<div class="row">
			<div class="col s12 m6 l6" style="margin-top: 25px">
				<div class="row">
					<label class=" black-text">Service Name</label> <input type="text"
						data-ng-model="elementName"></input>
				</div>
				<div class="row">
					<label class=" black-text">Unit of Measure</label> <select
						data-ng-model="uom" style="width: 100%"
						data-ng-options="uom as uom for uom in uomMetadata"></select>
				</div>
				<div class="row">
					<label class=" black-text">SAC Code</label> <select ui-select2
						data-ng-model="taxCategory"
						data-ng-options="tax as (tax.code + '-'+ tax.desc) for tax in taxCodes track by tax.code"
						data-ng-change="onTaxCategoryChange()">
					</select>
				</div>
				<div class="row">
					<label class=" black-text">Division</label> <select
						data-ng-model="selectedDivision"
						data-ng-options="division as division.name for division in divisions track by division.listDetailId"></select>
				</div>
				<div class="row">
					<label class=" black-text">Description</label>
					<textarea data-ng-model="description" name="description"
						data-ng-model="cc" data-ng-maxlength="500"></textarea>
				</div>

				<div class="row">
					<label class=" black-text">IsPrice Editable</label> <select
						data-ng-model="selectedIsPrice"
						data-ng-options="capex as capex for capex in capexs"></select>
				</div>

			</div>
			<div class="col s12 m6 l6" style="margin-top: 25px">
				<div class="row">
					<label class=" black-text">Department</label> <select
						data-ng-model="selectedDepartment"
						data-ng-options="department as department.name for department in departments track by department.listDetailId"></select>
				</div>
				<div class="row">
					<label class=" black-text">Classification</label> <select
						data-ng-change="selectCategory(selectedCategory)"
						data-ng-model="selectedCategory"
						data-ng-options="category as category.name for category in categories track by category.listDetailId"></select>
				</div>
				<div class="row">
					<label class=" black-text">Sub Classification</label> <select
						data-ng-change="selectSubCategory(selectedSubCategory)"
						data-ng-model="selectedSubCategory"
						data-ng-options="subCategory as subCategory.name for subCategory in subCategories track by subCategory.listTypeId"></select>
				</div>
				<div class="row">
					<label class=" black-text">Sub-Sub Classification</label> <select
						data-ng-model="selectedSubSubCategory"
						data-ng-options="subSubCategory as subSubCategory.name for subSubCategory in subSubCategories track by subSubCategory.listDataId"></select>

				</div>
				<div class="row">
					<label class=" black-text">Capex</label> <select
						data-ng-model="selectedCapex"
						data-ng-options="capex as capex for capex in capexs"></select>
				</div>
				<div class="row">
					<label class=" black-text">Status</label> <select
						data-ng-model="selectedStatus"
						data-ng-options="period as period for period in periods"></select>
				</div>
				
				<button class="btn btn-primary right" style="margin-top: 15px;" type="button"
					ng-click="createCostElement()">Submit</button>
			</div>
		</div>
	</div>
</div>


