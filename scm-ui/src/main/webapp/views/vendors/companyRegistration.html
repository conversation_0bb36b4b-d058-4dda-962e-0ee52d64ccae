<div class="row" data-ng-init="init()" data-ng-class="{addMargin:!editMode}">
    <div id="companyDef" class="row scm-form">
        <div class="row">
            <div class="col s9">
                <div class="left">
                    <label>(Fields marked with * are mandatory. You will not be able to proceed if all mandatory fields are not filled)</label>
                </div>
            </div>
            <div data-ng-if="editMode" class="col s3">
                <div class="right">
                    <button class="btn" data-ng-click="goBack()">Back</button>
                </div>
            </div>
        </div>

        <form id="companyDetail" name="companyDetailForm" data-ng-model="companyDetail" class="white z-depth-3 scm-form">
            <div class="row">
                <div class="col s12 m6 l6">
                    <h5 style="margin:0px;">Enter Company Details </h5>
                    <div class="form-element row">
                        <label class="black-text" for="companyName">Company Name*</label>
                        <input id="companyName" name="companyName" data-ng-model="companyDetail.companyName" type="text"
                               validate="notnull"
                               maxlength="100" required>
                        <p class="help-block"></p>
                    </div>
                    <div class="form-element row">
                        <label class="black-text" for="registeredName">Registered Company Name*</label>
                        <input id="registeredName" name="registeredName" data-ng-model="companyDetail.registeredName" type="text"
                               validate="notnull"
                               maxlength="100" required>
                        <p class="help-block"></p>
                    </div>
                    <div class="form-element row">
                         <div class="col s6">
                            <label style="display: inline;" class=" black-text" for="msmeRegistered" title="Ministry of Micro, Small and Medium Enterprises">
                                Are you registered with MSME?
                            </label>
                         </div>
                         <div  class="col s6">
                            <select style="max-width: 100px; margin-left: 20px;" id="msmeRegistered"
                                    name="msmeRegistered" data-ng-model="companyDetail.msmeRegistered"
                                    data-ng-change="isMsmeRegistered(companyDetail.msmeRegistered)"
                                    validate="notnull" required>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                            </select>
                        </div>
                    </div>

					<div class="form-element row" data-ng-if="companyDetail.msmeRegistered=='true'">
                         <div class="col s6" ng-if="companyDetail.msmeDocument==null">
                         	 <label class="black-text" for="cin">Upload MSME Document*</label>
                             <button class="btn" data-ng-click="uploadMSME()">Upload MSME</button>
                         </div>
                         <div class="col s6" ng-if="companyDetail.msmeDocument!=null">
                             <span>{{companyDetail.msmeDocument.documentLink}}</span>
                             <i class="material-icons pointer" data-ng-click="companyDetail.msmeDocument=null">edit</i>
                         </div>
                   </div>

                    
                    <div class="form-element row">
                        <div class="col s6">
                            <label style="display: inline;" class=" black-text" for="exemptSupplier">
                                Are you a Exempted Goods or Services Supplier?
                            </label>
                        </div>
                        <div class="col s6">
                            <select style="max-width: 100px; margin-left: 20px;" id="exemptSupplier"
                                    name="exemptSupplier" data-ng-model="companyDetail.exemptSupplier"
                                    data-ng-change="isExemptSupplier(companyDetail.exemptSupplier)"
                                    validate="notnull" required>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-element row" data-ng-if="companyDetail.exemptSupplier=='false'">
                        <div class="col s6">
                            <label class=" black-text" for="entityType">Entity Type*</label>
                            <select id="entityType" name="entityType" data-ng-model="companyDetail.entityType"
                                    data-ng-options="type as type for type in companyTypes"
                                    data-ng-change="changeCompanyType(companyDetail.entityType)"
                                    validate="notnull"
                                    required></select>
                            <p class="help-block"></p>
                        </div>
                        <div class="col s6" data-ng-if="getCin">
                            <label class="black-text" for="cin">Enter CIN and upload document*</label>
                            <div class="col s6">
                                <input id="cin" name="cin" data-ng-model="companyDetail.cin" type="text"
                                       validate="notnull,cin"
                                       maxlength="21" required>
                                <p class="help-block"></p>
                            </div>
                            <div class="col s6" ng-if="companyDetail.cinDocument==null">
                                <button class="btn btn-small" data-ng-click="uploadCIN()">Upload CIN</button>
                            </div>
                            <div class="col s6" ng-if="companyDetail.cinDocument!=null">
                                <span>{{companyDetail.cinDocument.documentLink}}</span>
                                <i class="material-icons pointer" data-ng-click="companyDetail.cinDocument=null">edit</i>
                            </div>
                        </div>
                        <div class="col s6" data-ng-if="getArc">
                            <label class=" black-text" for="arc">Enter ARC and Upload Document*</label>
                            <div class="col s6">
                                <input id="arc" name="arc" data-ng-model="companyDetail.arc" type="text"
                                       validate="notnull" maxlength="50" required>
                                <p class="help-block"></p>
                            </div>
                            <div class="col s6" ng-if="companyDetail.arcDocument==null">
                                <button class="btn btn-medium" data-ng-click="uploadARC()">Select document</button>
                            </div>
                            <div class="col s6" ng-if="companyDetail.arcDocument!=null">
                                <span>{{companyDetail.arcDocument.documentLink}}</span>
                                <i class="material-icons pointer" data-ng-click="companyDetail.arcDocument=null">edit</i>
                            </div>
                        </div>
                    </div>

                    <div class="form-element row">
                        <label class=" black-text" for="arc">Enter PAN and Upload Document*</label>
                        <div class="row">
                            <div class="col s6">
                                <input id="pan" name="pan" data-ng-model="companyDetail.pan"
                                       type="text"
                                       validate="notnull,pan"
                                       maxlength="10" required>
                                <p class="help-block"></p>
                            </div>
                            <div class="col s6" ng-if="companyDetail.panDocument==null">
                                <button class="btn btn-medium" data-ng-click="uploadPAN()">Select Document</button>
                            </div>
                            <div class="col s6" ng-if="companyDetail.panDocument!=null">
                                <span>{{companyDetail.panDocument.documentLink}}</span>
                                <i class="material-icons pointer" data-ng-click="companyDetail.panDocument=null">edit</i>
                            </div>
                        </div>
                    </div>

                   <!-- <div class="form-element row" data-ng-if="companyDetail.exemptSupplier=='false'">
                        <label class=" black-text" for="cst">Enter CST and Upload CST Document*</label>
                        <div class="row">
                            <div class="col s6">
                                <input id="cst" name="cst" data-ng-model="companyDetail.cst"
                                       type="text"
                                       validate="notnull"
                                       maxlength="20" required>
                                <p class="help-block"></p>
                            </div>
                            <div class="col s6" ng-if="companyDetail.cstDocument==null">
                                <button class="btn btn-medium" data-ng-click="uploadCST()">Select Document</button>
                            </div>
                            <div class="col s6" ng-if="companyDetail.cstDocument!=null">
                                <span>{{companyDetail.cstDocument.documentLink}}</span>
                                <i class="material-icons pointer" data-ng-click="companyDetail.cstDocument=null">edit</i>
                            </div>
                        </div>
                    </div>-->


                    <div class="form-element row" data-ng-if="companyDetail.exemptSupplier=='false'">
                        <div class="col s6">
                            <label class=" black-text" for="businessType">Business Type*</label>
                            <select id="businessType" name="businessType" data-ng-model="companyDetail.businessType"
                                    data-ng-options="type as type for type in businessTypes"
                                    data-ng-change="changeBusinessType(companyDetail.businessType)"
                                    validate="notnull"
                                    required></select>
                            <p class="help-block"></p>
                        </div>
                        <!-- <div class="col s6 margin-top-20" data-ng-if="getST">
                            <div data-ng-if="companyDetail.serviceTaxDocument==null">
                                <label class="black-text" >Upload Service Tax Document*</label>
                                <button class="btn btn-medium" data-ng-click="uploadST()">Select</button>
                            </div>
                            <div ng-if="companyDetail.serviceTaxDocument!=null">
                                <span>{{companyDetail.serviceTaxDocument.documentLink}}</span>
                                <i class="material-icons pointer" data-ng-click="companyDetail.serviceTaxDocument=null">edit</i>
                            </div>
                        </div>
                        <div class="col s6 margin-top-20" data-ng-if="getVat">
                            <div data-ng-if="companyDetail.vatDocument==null">
                                <label class="black-text">Upload VAT Document*</label>
                                <button class="btn btn-medium" data-ng-click="uploadVAT()">Select</button>
                            </div>
                            <div ng-if="companyDetail.vatDocument!=null">
                                <span>{{companyDetail.vatDocument.documentLink}}</span>
                                <i class="material-icons pointer" data-ng-click="companyDetail.vatDocument=null">edit</i>
                            </div>
                        </div> -->
                    </div>
                </div>
                <div class="col s12 m6 l6">
                    <h5>Registered Address Details</h5>
                    <label>Address on which company is registered</label>

                    <div class="form-element">
                        <label class=" black-text" for="line1">Registered Address Line 1*</label>
                        <input type="text" id="line1" name="line1" data-ng-model="companyDetail.companyAddress.line1"
                               validate="notnull"
                               maxlength="150" required>
                        <p class="help-block"></p>
                    </div>
                    <div class="form-element">
                        <label class=" black-text" for="line2">Address Line 2</label>
                        <input type="text" id="line2" name="line2" data-ng-model="companyDetail.companyAddress.line2" maxlength="150">
                        <p class="help-block"></p>
                    </div>
                    <div class="form-element">
                        <label class="black-text" for="line3">Address Line 3</label>
                        <input type="text" id="line3" name="line3" data-ng-model="companyDetail.companyAddress.line3"
                               maxlength="150">
                        <p class="help-block"></p>
                    </div>

                    <div class="form-element" data-ng-if="companyDetail.companyAddress.city!=null">
                        <div class="row">
                            <label class="black-text margin-top-10 left" for="city">Select Location*</label>
                            <button class="btn btn-small right" data-ng-click="companyDetail.companyAddress.city=null;">Edit</button>
                        </div>
                        <div class="row">
                            <div class="col s6">
                                <label class="black-text">City</label>
                                <input type="text" ng-model="companyDetail.companyAddress.city"  disabled>
                            </div>
                            <div class="col s6">
                                <label class="black-text">State</label>
                                <input type="text" ng-model="companyDetail.companyAddress.state"  disabled>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s6">
                                <label class="black-text">Country</label>
                                <input type="text" ng-model="companyDetail.companyAddress.country" disabled>
                            </div>
                            <div class="col s6">
                                <label class="black-text">Zipcode</label>
                                <input type="text" ng-model="companyDetail.companyAddress.zipcode" validate="notnull">
                            </div>
                        </div>
                    </div>


                    <div class="form-element" data-ng-if="companyDetail.companyAddress.city==null">
                        <div class="row">
                            <div class="col s12">
                                <label class=" black-text" for="city">Select City*</label>
                                <select id="city" name="city" ui-select2="multiSelectOptions" ng-model="selectedCity"
                                        data-ng-change="changeCompanyLocation(selectedCity)" data-placeholder="Select City"
                                        validate="notnull"
                                        required>
                                    <option ng-repeat="city in cities track by city.id" value="{{city}}">{{city.name}}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="row" style="margin-top:10px;">
        <div class="col s12">
            <button data-ng-if="!editMode" data-ng-click="saveCompanyDetails()" class="btn right"> Save & Continue
                <i class="material-icons right">send</i>
            </button>
            <button data-ng-if="editMode" data-ng-click="saveCompanyDetails()" class="btn right"> Edit
                <i class="material-icons right">send</i>
            </button>
        </div>
    </div>
</div>
