<div class="row" data-ng-init="init()" data-ng-class="{addMargin:!editMode}">
    <div id="vendorDef" class="row scm-form">
        <div class="row">
            <div class="col s9">
                <div class="left">
                    <h5 style="margin:0px;">Enter Your Details </h5>
                    <label>(Fields marked with * are mandatory. You will not be able to proceed if all mandatory fields are not filled)</label>
                </div>
            </div>
            <div data-ng-if="editMode" class="col s3">
                <div class="right">
                    <button class="btn" data-ng-click="goBack()">Back</button>
                </div>
            </div>
        </div>
		<div class="row">
            <div class="col s12">
                <div class="left">
                    <h6 data-ng-if="basicDetail != null && basicDetail.vendorType != null" style="margin:0px;">Registration Type : {{basicDetail.vendorType}}</h6>
                    <h6 data-ng-if="basicDetail != null && basicDetail.vendorType == null && registrationRequest != null" style="margin:0px;">Registration Type : {{registrationRequest.vendorType}}</h6>
                </div>
            </div>
        </div>
        <form id="basicDetail" name="basicDetailForm" class="white z-depth-3 scm-form">
            <div class="row">
                <div class="col s12 m6 l6">
                    <h5>Personal Details</h5>
                    <div class="form-element">
                        <label class=" black-text" for="firstName">Owner's First Name*</label>
                        <input id="firstName" name="firstName" data-ng-model="basicDetail.firstName"
                               type="text" validate="notnull" maxlength="100" required>
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.firstName.$error.maxlength" class="errorMessage">First name is too large.</p>
                        <p ng-show="basicDetail.firstName.$error.required" class="errorMessage">First name is required.</p>-->
                    </div>
                    <div class="form-element">
                        <label class=" black-text" for="lastName">Owner's Last Name*</label>
                        <input id="lastName" name="lastName" data-ng-model="basicDetail.lastName"
                               type="text"
                               validate="notnull"
                               maxlength="100" required>
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.lastName.$error.maxlength" class="errorMessage">Last name is too large.</p>
                        <p ng-show="basicDetail.lastName.$error.required" class="errorMessage">Last name is required.</p>-->
                    </div>
                    <div class="form-element">
                        <label class=" black-text" for="entityName">Vendor's Company Name*</label>
                        <input id="entityName" name="entityName" data-ng-model="basicDetail.entityName"
                               validate="notnull"
                               type="text" maxlength="300" required>
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.entityName.$error.maxlength" class="errorMessage">Entity name is too large.</p>
                        <p ng-show="basicDetail.entityName.$error.required" class="errorMessage">Entity name is required.</p>-->
                    </div>
                    <div class="form-element">
                        <label class=" black-text" for="primaryContact">Primary Contact*</label>
                        <input id="primaryContact" name="primaryContact"
                               validate="notnull,phone"
                               data-ng-model="basicDetail.primaryContact" type="text" maxlength="10" required>
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.primaryContact.$error.maxlength" class="errorMessage">Primary Contact is too large.</p>
                        <p ng-show="basicDetail.primaryContact.$error.required" class="errorMessage">Primary Contact is required.</p>-->
                    </div>
                    <div class="form-element">
                        <label class="black-text" for="secondaryContact">Secondary Contact</label>
                        <input id="secondaryContact" name="secondaryContact" validate="phone"
                               data-ng-model="basicDetail.secondaryContact" type="text" maxlength="10">
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.secondaryContact.$error.maxlength" class="errorMessage">Secondary Contact is too large.</p>
                        <p ng-show="basicDetail.secondaryContact.$error.required" class="errorMessage">Secondary Contact is required.</p>-->
                    </div>
                    <div class="form-element">
                        <label class=" black-text" for="primaryEmail">Primary Email*</label>
                        <input id="primaryEmail" name="primaryEmail" data-ng-model="basicDetail.primaryEmail"
                               validate="notnull,email"
                               type="text" maxlength="100" required>
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.primaryEmail.$error.maxlength" class="errorMessage">Primary Email is too large.</p>
                        <p ng-show="basicDetail.primaryEmail.$error.required" class="errorMessage">Primary Email is required.</p>-->
                    </div>

                    <div class="form-element">
                        <label class=" black-text" for="secondaryEmail">Secondary Email</label>
                        <input id="secondaryEmail" name="secondaryEmail"
                               validate="email"
                               data-ng-model="basicDetail.secondaryEmail" type="text" maxlength="100">
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.secondaryEmail.$error.maxlength" class="errorMessage">Secondary Email is too large.</p>
                        <p ng-show="basicDetail.secondaryEmail.$error.required" class="errorMessage">Secondary Email is required.</p>-->
                    </div>
                </div>
                <div class="col s12 m6 l6">
                    <h5>Address Details</h5>

                    <div class="form-element">
                        <label class=" black-text" for="line1">Address Line 1*</label>
                        <input type="text" id="line1" name="line1" data-ng-model="basicDetail.vendorAddress.line1"
                               validate="notnull"
                               maxlength="150" required>
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.vendorAddress.line1.$error.maxlength" class="errorMessage">Address Line 1 is too large.</p>
                        <p ng-show="basicDetail.vendorAddress.line1.$error.required" class="errorMessage">Address Line 1 is required.</p>-->
                    </div>
                    <div class="form-element">
                        <label class=" black-text" for="line2">Address Line 2</label>
                        <input type="text" id="line2" name="line2" data-ng-model="basicDetail.vendorAddress.line2"
                               maxlength="150">
                        <p class="help-block"></p>
                        <!--<p ng-show="basicDetail.vendorAddress.line2.$error.maxlength" class="errorMessage">Address Line 2 is too large.</p>-->
                    </div>
                    <div class="form-element">
                        <label class=" black-text" for="line3">Address Line 3</label>
                        <input type="text" id="line3" name="line3" data-ng-model="basicDetail.vendorAddress.line3"
                               maxlength="150">

                        <p class="help-block"></p>
                    </div>

                    <div class="form-element" data-ng-if="basicDetail.vendorAddress.city!=null">
                        <div class="row">
                            <label class="black-text margin-top-10 left" for="city">Select Location*</label>
                            <button class="btn btn-small right" data-ng-click="basicDetail.vendorAddress.city=null;">Edit</button>
                        </div>
                        <div class="row">
                            <div class="col s6">
                                <label class="black-text">City</label>
                                <input type="text" ng-model="basicDetail.vendorAddress.city"  disabled>
                            </div>
                            <div class="col s6">
                                <label class="black-text">State</label>
                                <input type="text" ng-model="basicDetail.vendorAddress.state"  disabled>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s6">
                                <label class="black-text">Country</label>
                                <input type="text" ng-model="basicDetail.vendorAddress.country" disabled>
                            </div>
                            <div class="col s6">
                                <label class="black-text">Zipcode</label>
                                <input type="text" validate="notnull" maxlength="10" ng-model="basicDetail.vendorAddress.zipcode">
                                <p class="help-block"></p>
                            </div>
                        </div>
                    </div>


                    <div class="form-element" data-ng-if="basicDetail.vendorAddress.city==null">
                        <div class="row">
                            <div class="col s12">
                                <label class=" black-text" for="city">Select City*</label>
                                <select id="city" name="city" ui-select2="multiSelectOptions" ng-model="selectedCity"
                                        data-ng-change="changeVendorLocation(selectedCity)" data-placeholder="Select City"
                                        validate="notnull"
                                        required>
                                    <option ng-repeat="city in cities track by city.id" value="{{city}}">{{city.name}}</option>
                                </select>
                                <p class="help-block"></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col s12 m6 l6">
                    <h5>Legal Disclaimer</h5>
                    <div class="row">
                    <label> We hereby accept that we are registering as Vendor for both companies of Chaayos i.e. Sunshine Teahouse Private Limited and DKC Teahouse Private Limited (100% subsidiary of Sunshine Teahouse Private Limited). We will issue invoice in name of company for which we will receive Purchase Order/ Service Order.
                    </label>
                    <br/>
                    <div class="form-element">
						<input type="checkbox" id="disclaimer" data-ng-model="basicDetail.disclaimerAccepted"/>
      					<label for="disclaimer">I Agree</label>
      	           </div>
                </div>
                <!--<div class="form-element">
                    <label class=" black-text" for="city">Select State</label>
                    <select id="state" name="state" ui-select2="multiSelectOptions" ng-model="basicDetail.vendorAddress.city"
                            data-placeholder="Select a Fulfillment type" multiple required>
                        <option ng-repeat="city in cities"
                                value="{{city}}">{{city}}</option>
                    </select>
                </div>
                <div class="form-element">
                    <select id="city" name="city" ui-select2="multiSelectOptions" ng-model="basicDetail.vendorAddress.city"
                            data-placeholder="Select a Fulfillment type" multiple required>
                        <option ng-repeat="city in cities"
                                value="{{city}}">{{city}}</option>
                    </select>
                </div>-->
            </div>
        </form>
    </div>

    <div class="row" style="margin-top:10px;">
        <div class="col s12">
            <button data-ng-if="!editMode" data-ng-click="checkForAlreadySubmitted()" class="btn right"> Save & Continue
                <i class="material-icons right">send</i>
            </button>
            <button data-ng-if="editMode" data-ng-click="updateBasicDetails()" class="btn right"> Continue
                <i class="material-icons right">send</i>
            </button>
        </div>
    </div>
</div>
