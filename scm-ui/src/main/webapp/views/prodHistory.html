<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    @page{
        margin-top: 5px !important;
    }
    .modal-large{
        width:80% !important;
    }

    table#prodTable tr th,td {
        width: 65px;
    }

    table#prodTable tr input.packaging-qty {
        max-width: 75px;
    }

    div.packaging-div {
        display: block;
        word-break: keep-all;
        margin-bottom: 10px;
    }

    div.packaging-div span {
        vertical-align: middle;
    }

    div.packaging-div span.pointer {
        display: inline-block;
        background-color: #26a69a;
        color: #fff;
        border-radius: 20px;
        width: 20px;
        line-height: 18px;
        font-size: 20px;
        text-align: center;
    }

    input.waste-value {
        color: #000 !important;
    }

</style>
<div id="noPrint">
<div
        class="row white z-depth-3"
        data-ng-init="init()">
    <div class="col s12">
        <h3>Production Planning History</h3>
    </div>
    <div class="row">
        <div class="col s4">
            <label for="startDate">Select Start date</label>
            <input
                    input-date
                    type="text"
                    name="created"
                    id="startDate"
                    data-ng-model="startDate"
                    data-ng-change="reset()"
                    container=""
                    format="yyyy-mm-dd"
                    select-years="1"/>
        </div>
        <div class="col s4">
            <label for="endDate">Select End date</label>
            <input
                    input-date
                    type="text"
                    name="created"
                    id="endDate"
                    data-ng-model="endDate"
                    data-ng-change="reset()"
                    container=""
                    format="yyyy-mm-dd"
                    select-years="1"
                    min="{{startDate}}"/>
        </div>
        <div class="col s4 margin-top-10">
            <button
                    class="btn btn-small"
                    data-ng-click="getPlans()"
                    style="margin-top: 14px;" acl-action="TRNPHV">Search
            </button>
        </div>
    </div>
    <hr>
    <div
            class="row"
            data-ng-if="startDate != null && plans.length == 0">
		<span
                class="flow-text"
                style="margin-left: 10px;"> No request plans found for the
			selected date. </span>
    </div>
    <div
            class="row"
            data-ng-if="plans.length > 0">
        <div class="col s12">
            <ul
                    class="collapsible no-border"
                    data-collapsible="expandable"
                    watch>
                <li data-ng-repeat="plan in plans | orderBy: '-id'  ">
                    <div class="collapsible-header custom-collection-header">
                        <div class="row margin0">
                            <div class="col s12">
                                <div class="col s2 left-align">
                                    <label>Plan No. : {{plan.id}}</label>
                                </div>
                                <div class="col s3">
									<span class="right-align">
										<strong>Created On: </strong> {{plan.generationTime |
										date:'dd/MM/yyyy hh:mm:ss a'}}
									</span>
                                </div>
                                <div class="col s2">
									<span class="right-align">
										<strong>Created By: </strong> {{plan.generatedBy.name}}
									</span>
                                </div>
                                <div class="col s1">
                                    <span class="right-align"> {{plan.status}} </span>
                                </div>
                                <div class="col s4">
                                    <button
                                        class="btn btn-small"
                                        data-ng-click="downloadPlanorders(plan.id)"
                                        style="margin-top: 14px;" acl-action="TRNPHV">Download</button>
                                        <button data-ng-if="plan.status!='INITIATED' && plan.updated == false"
                                                class="btn btn-small"
                                                data-ng-click="previewExcessQuantity(plan.id)" style="margin-top: 14px; margin-left: 20px;"
                                                acl-action="TRNPHV">Upload Excess
                                        </button>
                                        <button class="btn btn-small" data-ng-if="plan.status!='INITIATED'"
                                                style="margin-top: 14px; margin-left: 20px;"
                                                data-ng-click="previewAdjustments(plan.id)">Adjustments
                                        </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="collapsible-body">
                        <div data-ng-if="isForAdjustments && plan.id == adjustmentPlanId && roItemsData.length > 0">
                            <br>
                            <div class="row">
                                <div class="col s10">
                                    <label>Select Product</label>
                                    <select ui-select2="{placeholder: 'Select Product'}" data-ng-model="selectedProduct" data-ng-change="filterDataByProduct(selectedProduct)"
                                            data-ng-options="product as product.productName for product in finalAdjustmentsRequestedList | orderBy:'productName' track by product.productId">
                                    </select>
                                </div>
                                <div class="col s2">
                                    <button data-ng-click="showInstructions()" tooltipped data-tooltip="Instructions for Adjustments"
                                            class="btn btn-floating grey"><i class="material-icons">info</i></button>
                                </div>
                            </div>
                            <div class=" row col s12" data-ng-if="displayConversions != {} && !isMultiPackaging">
                                <div class="col s6 left-align">
                                    <p class="yellow black-text" data-ng-if="displayConversions.packagingName != null">Packaging is : <b>{{displayConversions.packagingName}}</b></p>
                                </div>
                                <div class="col s6 left-align">
                                    <p class="yellow black-text" data-ng-if="displayConversions.conversionRatio != null">Please Enter in the multiples of <b>{{displayConversions.conversionRatio}}</b></p>
                                </div>
                                <br>
                            </div>
                            <div class="row" data-ng-if="selectedProduct != null">
                                <div class="col s12">
                                <table id="prodTable" class="bordered striped">
                                    <thead>
                                    <tr>
                                        <th><input id="checkAll" type="checkbox" data-ng-model="checkBoxModal.checkAll" data-ng-click="updateAll()">
                                            <label for="checkAll" style="width: 5px">Check All</label>
                                        </th>
                                        <th class="center-align" title="(R)-Regular Transfer,(B)-Bulk Transfer">RO Status</th>
                                        <th class="center-align" style="width: 5px">Unit Name</th>
                                        <th class="center-align" style="width: 5px">RO ID</th>
                                        <th class="center-align" style="width: 5px">RO Item ID</th>
                                        <th class="center-align">Requested Quantity</th>
                                        <th class="center-align">Original Requested Quantity</th>
                                        <th class="center-align" data-ng-if="!isMultiPackaging">
                                            <b>Final Quantity</b>
                                            <table>
                                                <tr>
                                                    <th>
                                                        <input type="number" data-ng-model="checkBoxModal.updatedFinalQuantity" style="width: 150px;height: 20px;"/>
                                                    </th>
                                                    <th>
                                                        <input id="itemFinalQuantity" type="checkbox" style="width: 30px; height: 20px;margin-left: 10px;margin-top: 10px"
                                                               data-ng-model="checkBoxModal.checkAllFinalQuantity" data-ng-click="changeAllQuantities()" class="center-align">
                                                        <label for="itemFinalQuantity"></label>
                                                    </th>
                                                </tr>
                                            </table>
                                            <span style="color: red" data-ng-if="checkBoxModal.updatedFinalQuantity <0 ">Enter a value Greater than or equal to 0</span>
                                        </th>
                                        <th>
                                            <input id="multiPackaging" type="checkbox" style="width: 30px; height: 20px;margin-left: 10px;margin-top: 10px"
                                                   data-ng-disabled="!warehouseOrKitchen"
                                                   data-ng-model="isMultiPackaging" data-ng-click="onOffMultiPackagings(isMultiPackaging,selectedProduct)" class="center-align">
                                            <label for="multiPackaging">Multi Packaging</label>
                                            <b data-ng-if="isMultiPackaging">Final Quantity</b>
                                            <table data-ng-if="isMultiPackaging">
                                                <tr>
                                                    <th style="width: 80px;">
                                                        <input class="input-field waste-value" type="number"
                                                               ng-model="checkBoxModal.updatedFinalQuantity" min="0" max="9999" data-ng-disabled="!isAutoMaticDistribution" required>
                                                    </th>
                                                    <th>
                                                        <input id="itemFinalQuantities" type="checkbox" style="width: 30px; height: 20px;margin-left: 10px;margin-top: 10px"
                                                               data-ng-model="checkBoxModal.checkAllFinalQuantity" data-ng-click="changeQuantities(selectedProduct)" class="center-align">
                                                        <label for="itemFinalQuantities"></label>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <input id="autoDistribution" type="checkbox" style="width: 30px; height: 20px;margin-left: 10px;margin-top: 10px" data-ng-model="isAutoMaticDistribution"
                                                        data-ng-change="setAutoDistribute(isAutoMaticDistribution,selectedProduct)">
                                                        <label for="autoDistribution">Auto Distribution</label>
                                                    </td>
                                                </tr>
                                            </table>
                                        </th>
                                        <th style="width: 200px;" data-ng-if="isMultiPackaging">
                                            <div class="row packaging-div" data-ng-repeat="mapping in selectedProduct.productPackagings">
                                                <input class="input-field" data-ng-disabled="mapping.packaging.quantity == null || isAutoMaticDistribution" type="checkbox" id="changeQuant-{{mapping.packaging.packagingId}}"
                                                       data-ng-model="mapping.packaging.checkPack" data-ng-click="changeQuantity(checkBoxModal.updatedFinalQuantity,selectedProduct,mapping,mapping.packaging.quantity,false)">
                                                <label for="changeQuant-{{mapping.packaging.packagingId}}">
                                                    <input class="input-field packaging-qty" type="number"
                                                           data-ng-disabled="mapping.packaging.checkPack || isAutoMaticDistribution"
                                                           data-ng-model="mapping.packaging.quantity">
                                                    <span class="badge">{{mapping.packaging.packagingName}}</span>
                                                </label>
                                            </div>
                                        </th>
                                        <th class="center-align">Adjusted Quantity</th>
                                        <th class="center-align">Adjusted Reason</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="item in listOfRos | orderBy:'item.unitName'"
                                        data-ng-class="{'green' : item.requestedQuantity > item.requestedAbsoluteQuantity,
                                                        'red' : item.requestedQuantity < item.requestedAbsoluteQuantity}">
                                        <td>
                                            <input id="itemCheck-{{item.roItemId}}" type="checkbox" data-ng-model='item.checked' data-ng-click="changeRow(item)"
                                                   data-ng-disabled="item.transferred == true">
                                            <label for="itemCheck-{{item.roItemId}}" style="width: 5px"></label>
                                        </td>
                                        <td class="center-align" style="width: 5px">{{item.status}}</td>
                                        <td class="center-align" style="width: 5px">{{item.unitName}}</td>
                                        <td class="center-align" style="width: 5px">{{item.roId}}</td>
                                        <td class="center-align" style="width: 5px">{{item.roItemId}}</td>
                                        <td class="center-align">{{item.requestedQuantity}}</td>
                                        <td class="center-align">{{item.requestedAbsoluteQuantity}}</td>
                                        <td class="left-align" data-ng-if="!isMultiPackaging"> <input type="text" data-ng-model="item.finalQuantity"
                                                                       style="width: 150px;height: 20px;" data-ng-change="updateAdjustedQuantity(item,false)"
                                                                       data-ng-disabled="item.transferred == true">
                                            <span style="color: red" data-ng-if="item.finalQuantity <0 ">Enter value Greater than or equal to 0</span>
                                        </td>
                                        <td data-ng-if="isMultiPackaging"><input class="input-field waste-value" type="number"
                                                   ng-model="item.finalQuantity" min="0" max="9999" required disabled>
                                        </td>
                                        <td data-ng-if="!isMultiPackaging">
                                            <div class="row" ng-if="productWithMultiPacks[item.roItemId]"
                                                 data-ng-repeat="mapping in item.productPackagings">
                                               <span data-ng-if="mapping.packaging.itemQuantity != undefined && mapping.packaging.itemQuantity != null"
                                                     style="background: #ccc; border-radius: 3px; margin: 3px; padding: 5px; cursor: pointer; display: inline-block;">
                                                    {{mapping.packaging.itemQuantity}} - {{mapping.packaging.packagingName}}</span>
                                            </div>
                                        </td>
                                        <td style="width: 200px;" data-ng-if="isMultiPackaging">
                                            <div class="row packaging-div" data-ng-repeat="mapping in item.productPackagings" data-ng-if="item.editPackagings">
                                                <input class="input-field" type="checkbox" data-ng-disabled="mapping.packaging.itemQuantity == null"
                                                       id="changeQuant-{{mapping.packaging.packagingId}}-{{item.roItemId}}"
                                                       data-ng-model="mapping.packaging.itemCheckPack"
                                                       data-ng-click="changeQuantity(item.finalQuantity,item,mapping,mapping.packaging.itemQuantity,true)">
                                                <label for="changeQuant-{{mapping.packaging.packagingId}}-{{item.roItemId}}">
                                                    <input class="input-field packaging-qty" type="number"
                                                           data-ng-disabled="mapping.packaging.itemCheckPack"
                                                           data-ng-model="mapping.packaging.itemQuantity">
                                                    <span class="badge">{{mapping.packaging.packagingName}}</span>
                                                </label>
                                            </div>
                                            <div class="row"
                                                 data-ng-repeat="mapping in item.productPackagings" data-ng-if="!item.editPackagings">
                                               <span data-ng-if="mapping.packaging.itemQuantity != undefined && mapping.packaging.itemQuantity != null"
                                                     style="background: #ccc; border-radius: 3px; margin: 3px; padding: 5px; cursor: pointer; display: inline-block;">
                                                    {{mapping.packaging.itemQuantity}} - {{mapping.packaging.packagingName}}</span>
                                            </div>
                                            <button data-ng-click="item.editPackagings = true" data-ng-if="!item.editPackagings" tooltipped data-tooltip="Edit Quantity"
                                                    class="btn btn-floating green"><i class="material-icons">edit</i></button>
                                            <button data-ng-click="item.editPackagings = false" data-ng-if="item.editPackagings" tooltipped data-tooltip="Update Quantity"
                                                    class="btn btn-floating green"><i class="material-icons">check</i></button>
                                        </td>
                                        <td class="center-align"><input type="text" min="0" disabled step="1" ng-model="item.adjustedQuantity"></td>
                                        <td class="center-align" ><select data-ng-model="item.adjustedReason" disabled>
                                            <option value="Adjustment - Excess">Adjustment - Excess</option>
                                            <option value="No - Adjustment">No - Adjustment</option>
                                            <option value="Adjustment - Short">Adjustment - Short</option>
                                        </select></td>
                                    </tr>
                                    </tbody>
                                </table>
                                <div class="row">
                                    <br>
                                    <div class="col s12">
                                        <div class="left-align col s6">
                                            <button class="btn btn-medium" data-ng-click="closeAdjustments()">Close Adjustments</button>
                                        </div>
                                        <div class="right-align col s6">
                                            <button class="btn btn-medium" data-ng-click="submitAdjustments(plan.id)">Submit Adjustments</button>
                                        </div>
                                    </div>
                                </div>
                               </div>
                            </div>
                            <br>
                        </div>
                        <div class="row">

                            <table
                                    class="bordered striped"
                                    data-ng-if="plan.requestItems.length > 0">
                                <tr>
                                    <div class="form-group">
                                        <label>Search</label>
                                        <input type="text" class="form-control" data-ng-model="searchByProduct"
                                               placeholder="Enter Product Name to search" style="color: red" />
                                        <hr>
                                    </div>
                                </tr>
                                <tr>
                                    <th>Product Id</th>
                                    <th>Product Name</th>
                                    <th>Unit Of Measure</th>
                                    <th>Total Quantity</th>
                                    <th>Available Qty</th>
                                    <th>To-be Procured</th>
                                    <th>Plan</th>
                                </tr>
                                <tr data-ng-repeat="roi in plan.requestItems | orderBy: 'productName' | filter:searchByProduct track by $index" data-ng-class="{'planPrinted':roi.printCount!=null}">
                                    <td>
                                        <span data-ng-if="roi.category=='SEMI_FINISHED'">*</span>
                                        <a data-ng-click="showPreview($event, roi.productId,'PRODUCT')">{{roi.productId}}</a>
                                    </td>
                                    <td data-ng-if="roi.printCount==null">{{roi.productName}}</td>
                                    <td data-ng-if="roi.printCount!=null">
                                        <a data-target="viewPlansModal" data-ng-click="getProductPlans(roi)" style="cursor:pointer;" modal>{{roi.productName}}</a>
                                    </td>
                                    <td>{{roi.unitOfMeasure}}</td>
                                    <td>{{roi.totalQuantity | number : 4}}</td>
                                    <td>{{roi.availableQuantity | number : 4 }}</td>
                                    <td>{{roi.totalQuantity - roi.availableQuantity > 0 ?
                                        roi.totalQuantity - roi.availableQuantity : 0 | number : 4
                                        }}
                                    </td>
                                    <td>
                                        <button class="btn btn-xs-small" acl-action="GRPHSH" data-ng-if="roi.category=='SEMI_FINISHED'"
                                                data-ng-click="setPlanProductId(roi)" data-target="semiFinishedPlanModal" modal>Get Recipe
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>

<div id="semiFinishedPlanModal" class="modal modal-mx">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <h5>Get Recipe Data</h5>
            </div>
        </div>
        <div class="row" data-ng-show="!showPrepPlan">
            <div class="col s12">
                <h6>{{selectedPlanProduct.productName}}</h6>
                <label>Preparation quantity:</label>
                <input type="number" data-ng-model="prepQuantity" style="margin-bottom: 10px;" />
                <button class="btn" data-ng-click="getPlanItemsForSemiFinishedProduct()">Get Recipe items</button>
            </div>
        </div>
        <div class="row" data-ng-show="showPrepPlan">
            <div class="col s12">
                <p><button class="btn btn-xs-small" data-ng-click="showPrepPlan=false">Back</button></p>
                <table class="table bordered striped" style="margin-bottom: 10px">
                    <tr>
                        <th>Item: <a data-ng-click="showPreview($event, prepPlanData.planOrderItem.id,'PRODUCT')">{{prepPlanData.planOrderItem.name}} [{{prepPlanData.planOrderItem.id}}]</a></th>
                        <th>Uom: {{selectedPlanProduct.unitOfMeasure}}</th>
                        <th>Recipe Id: {{prepPlanData.recipeId}}</th>
                        <th>Preparation Qty: {{prepPlanData.preparationQuantity}}</th>
                    </tr>
                </table>
                <div class="planBody">
                    <div class="row planItem planItemHead">
                        <div class="col s2">Steps</div>
                        <div class="col s4">Ingredients</div>
                        <div class="col s3">Quantity</div>
                        <div class="col s3">Instructions</div>
                    </div>
                    <div data-ng-class="{'row planItem':item.planOrderItemPrepItems.length==0, 'row planItem planItemHead':item.planOrderItemPrepItems.length>0}"
                         data-ng-repeat="item in prepPlanData.planOrderItemPrepItems track by $index">
                        <div class="col s2">Step {{item.stepIndex}}</div>
                        <div class="col s4"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                        <div class="col s3">{{item.quantity | formatuom :item.unitOfMeasure }}</div>
                        <div class="col s3">{{item.instructions}}</div>
                        <div data-ng-if="item.planOrderItemPrepItems.length>0" class="row planBody">
                            <div class="row planItem" data-ng-repeat="itemx in item.planOrderItemPrepItems">
                                <div class="col s2"></div>
                                <div class="col s4"><a data-ng-click="showPreview($event, itemx.productId,'PRODUCT')">{{itemx.productName}}</a></div>
                                <div class="col s3">{{itemx.quantity | formatuom :itemx.unitOfMeasure }}</div>
                                <div class="col s3">{{itemx.instructions}}</div>
                            </div>
                            <div class="row recipeNotes" data-ng-if="item.recipeNotes!=null">Step {{item.stepIndex}} - Instructions: <p ng-bind-html="item.recipeNotes"></p></div>
                        </div>
                    </div>
                    <div class="row recipeNotes" data-ng-if="prepPlanData.recipeNotes!=null">Instructions: <p ng-bind-html="prepPlanData.recipeNotes"></p></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <div class="row">
            <div class="col s12">
                <button class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px" data-ng-if="showPrepPlan" data-ng-click="savePlanForSemiFinished()">Save and Print
                </button>
                <button class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px">Close
                </button>
            </div>
        </div>
    </div>
</div>

<div id="viewPlansModal" class="modal modal-mx">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <h5>Plan Details</h5>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col s12">
            <div class="row" data-ng-repeat="plan in plansForView track by plan.id">
                <div class="col s10">
                    <div class="planContainer">
                        <div class="planHead" data-ng-click="expandPlan(plan)">
                            <div class="row" style="margin-bottom: 10px;">
                                <div class="col s4">Plan Id: {{plan.id}}</div>
                                <div class="col s4">Recipe Id: {{plan.recipeId}}</div>
                                <div class="col s4">Preparation Qty: {{plan.preparationQuantity}}</div>
                            </div>
                            <div class="row" style="margin-bottom: 0px;">
                                <div class="col s6">Requested By: {{plan.requestedBy.name}} [{{plan.requestedBy.id}}]</div>
                                <div class="col s6">Requesting time: {{plan.requestingTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                            </div>
                        </div>
                        <div class="planBody" data-ng-if="plan.expand==true">
                            <div class="row planItem planItemHead">
                                <div class="col s2">Steps</div>
                                <div class="col s4">Ingredients</div>
                                <div class="col s3">Quantity</div>
                                <div class="col s3">Instructions</div>
                            </div>
                            <div data-ng-class="{'row planItem':item.planOrderItemPrepItems.length==0, 'row planItem planItemHead':item.planOrderItemPrepItems.length>0}"
                                 data-ng-repeat="item in plan.planOrderItemPrepItems track by $index">
                                <div class="col s2">Step {{item.stepIndex}}</div>
                                <div class="col s4"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                                <div class="col s3">{{item.quantity | formatuom :item.unitOfMeasure }}</div>
                                <div class="col s3">{{item.instructions}}</div>
                                <div data-ng-if="item.planOrderItemPrepItems.length>0" class="row planBody">
                                    <div class="row planItem" data-ng-repeat="itemx in item.planOrderItemPrepItems">
                                        <div class="col s2"></div>
                                        <div class="col s4"><a data-ng-click="showPreview($event, itemx.productId,'PRODUCT')">{{itemx.productName}}</a></div>
                                        <div class="col s3">{{itemx.quantity | formatuom :itemx.unitOfMeasure}}</div>
                                        <div class="col s3">{{itemx.instructions}}</div>
                                    </div>
                                    <div class="row recipeNotes" data-ng-if="item.recipeNotes!=null">Step {{item.stepIndex}} - Instructions: <p ng-bind-html="item.recipeNotes"></p></div>
                                </div>
                            </div>
                            <div class="row recipeNotes" data-ng-if="plan.recipeNotes!=null">Instructions: <p ng-bind-html="plan.recipeNotes"></p></div>
                        </div>
                    </div>
                </div>
                <div class="col s2">
                    <button class="waves-effect waves-green btn btn-small right" acl-action="GRPHRP"
                            data-ng-click="rePrintPlan(plan)">Re-print</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <div class="row">
            <div class="col s12">
                <button class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px">Close
                </button>
            </div>
        </div>
    </div>
</div>
</div>
<!--   printable TO section   -->
<div style="width: 100%; font-size: 20px;" id="printSection">
    <h4 style="text-align: center">Semi Finished Item Preparation Plan</h4>
    <div class="row">
        <div class="col s12">
            <p><b>Plan Item:</b> {{printPlanData.planOrderItem.name}} [{{printPlanData.planOrderItem.id}}]</p>
            <p><b>Recipe Id:</b> {{printPlanData.recipeId}}</p>
            <p><b>Preparation Qty:</b> {{printPlanData.preparationQuantity}} {{printPlanData.planOrderItem.unitOfMeasure}}</p>
            <p><b>Requested By:</b> {{printPlanData.requestedBy.name}} [{{printPlanData.requestedBy.id}}]</p>
            <p><b>Requesting Time:</b> {{printPlanData.requestingTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</p>
        </div>
    </div>
    <div class="planBody">
        <div class="row planItem planItemHead" style="margin-bottom: 10px;">
            <div class="col s2">Steps</div>
            <div class="col s4">Ingredients</div>
            <div class="col s3">Quantity</div>
            <div class="col s3">Instructions</div>
        </div>
        <div data-ng-class="{'row planItem':item.planOrderItemPrepItems.length==0, 'row planItem planItemHead':item.planOrderItemPrepItems.length>0}"
             data-ng-repeat="item in printPlanData.planOrderItemPrepItems track by $index">
            <div class="col s2">Step {{item.stepIndex}}</div>
            <div class="col s4">{{item.productName}}</div>
            <div class="col s3" style="text-align: right;">{{item.quantity | formatuom :item.unitOfMeasure }}</div>
            <div class="col s3">{{item.instructions}}</div>
            <div data-ng-if="item.planOrderItemPrepItems.length>0" class="row planBody">
                <div class="row planItem" data-ng-repeat="itemx in item.planOrderItemPrepItems">
                    <div class="col s2"></div>
                    <div class="col s4">{{itemx.productName}}</div>
                    <div class="col s3" style="text-align: right;">{{itemx.quantity | formatuom :itemx.unitOfMeasure }}</div>
                    <div class="col s3">{{itemx.instructions}}</div>
                </div>
                <div class="row recipeNotes" data-ng-if="item.recipeNotes!=null">Step {{item.stepIndex}} - Instructions: <p ng-bind-html="item.recipeNotes"></p></div>
            </div>
        </div>
    </div>
    <div class="row recipeNotes" style="margin-top: 200px;background: none;" data-ng-if="printPlanData.recipeNotes!=null">Instructions: <p ng-bind-html="printPlanData.recipeNotes"></p></div>
</div>

<!--  Excess upload-->
<script type="text/ng-template" id="previewExcessQuantity.html" class="modal-large" >
    <div  id="previewExcessQuantity" data-ng-init="initExcessQuantity()" >
        <div class="modal-content" style="overflow-x: auto; max-height: 350px;">
            <div class="row">
                <h5>Fill  Excess Quantity  For <b >Plan No : {{planId}} </b></h5>
            </div>
            <div class="row" style="width:98%;">
                <table class="bordered striped standardView">
                    <thead>
                    <tr>
                        <th class="center-align">Product Id</th>
                        <th class="center-align">Product Name</th>
                        <th class="center-align">Unit Of Measure</th>
                        <th class="center-align">Requested Quantity</th>
                        <th class="center-align">Buffered Quantity</th>
<!--                        <th class="center-align">Total  Quantity</th>-->
                        <th class="center-align">Excess Quantity</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="id in items | orderBy:'+productId' ">
                        <td class="center-align">{{id.productId}}</td>
                        <td class="center-align">{{id.productName}}</td>
                        <td class="center-align">{{id.unitOfMeasure}}</td>
                        <td class="center-align">{{id.requestedQuantity}}</td>
                        <td class="center-align">{{id.bufferedQuantity}}</td>
<!--                        <td class="center-align" >{{id.totalQuantity}}</td>-->
                        <td class="center-align" > <input type="number" min="0" step="1"   oninput="this.value=(parseInt(this.value)||0)" placeholder="0-9" data-ng-model="id.excessQuantity" data-ng-change="updateData(id.productId,id.excessQuantity)"  /></td>
                    </tr>
                    </tbody>
                </table>
                <div class="bordered striped TableMobileView">
                </div>
                </div>
        </div>
        <div class="modal-footer right">
            <button class="waves-effect waves-green btn-flat" data-ng-click="close()">Cancel</button>
            <button class="waves-effect waves-green btn" data-ng-click="submit()">Submit</button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="previewFinalAdjustments.html" class="modal-large" >
        <div class="modal-content" style="overflow-x: auto; max-height: 350px;">
            <div class="row">
                <h5>Adjustment Quantity  For Plan No <b>: {{planId}} </b>  Product : <b>{{selectedProduct.productName}}</b></h5>
            </div>
            <div class="row" style="width:98%;">
                <table class="bordered striped">
                    <thead>
                    <tr>
                        <th class="center-align">S.No</th>
                        <th class="center-align">Unit Name</th>
                        <th class="center-align">RO ID</th>
                        <th class="center-align">RO Item ID</th>
                        <th class="center-align">Requested Quantity</th>
                        <th class="center-align">Final Quantity</th>
                        <th class="center-align">Packagings</th>
                        <th class="center-align">Adjusted Quantity</th>
                        <th class="center-align">Adjusted Reason</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="item in updatedList track by $index">
                        <td class="center-align">{{$index+1}}</td>
                        <td class="center-align">{{item.unitName}}</td>
                        <td class="center-align">{{item.roId}}</td>
                        <td class="center-align">{{item.roItemId}}</td>
                        <td class="center-align">{{item.requestedQuantity}}</td>
                        <td class="center-align">{{item.finalQuantity}}</td>
                        <td class="left-align">
                            <div class="col s12" data-ng-if="item.multiPackagingAdjustments.length > 0" data-ng-repeat="pack in item.multiPackagingAdjustments">
                                {{pack.itemQuantity}} - {{pack.packagingName}}
                            </div>
                            <div class="col s12" data-ng-if="item.multiPackagingAdjustments.length == 0">
                                {{item.finalQuantity}} - {{displayConversions.packagingName}}
                            </div>
                        </td>
                        <td class="center-align">{{item.adjustedQuantity}}</td>
                        <td class="center-align">{{item.adjustedReason}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        <div class="modal-footer right">
            <button class="waves-effect waves-green btn-flat" data-ng-click="close()">Cancel</button>
            <button class="waves-effect waves-green btn" data-ng-click="submit()">Submit</button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="instructionsModal.html" class="modal-large">
    <div class="modal-content" style="overflow-x: auto; max-height: 350px;">
        <div class="row">
            <h4>Instructions to use Multi Packaging Adjustments</h4>
        </div>
        <div class="row">
            <p>1.Select the checkbox of Multi packaging -> You will be able to see the list of packagings available for
                that particular product.Select all rows in which you want to send the Same adjustments.</p>
            <p>2.If you want to auto distribute the quantity Select the checkbox of Auto distribute and Enter the Final
                Quantity and select the checkbox right to it.</p>
            <p>3.If you want to distribute Manually then Enter the Quantity of each packaging and select the checkbox
                left to it(Check will be enabled only when you enter Quantity in packaging) after filling all the
                packagings quantity manually then select the checkbox right to the Final Quantity.</p>
            <p>4.You can edit each row packagings by clicking on <i class="btn btn-floating green material-icons">edit</i> and save it by clicking on
                <i class="btn btn-floating green material-icons">check</i></p>
            <p>5.Submit Adjustments and re-verify the packagings wise adjustment and submit it..</p>
        </div>
        <div class="modal-footer right">
            <button class="btn btn-medium red" data-ng-click="close()">Close</button>
        </div>
    </div>
</script>