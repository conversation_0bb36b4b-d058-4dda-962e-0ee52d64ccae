<div data-ng-if="previewObj.found == true">
    <img data-ng-if="previewObj.imageFound" data-ng-src="{{previewObj.image}}" style="max-width: 500px;"/>
    <p data-ng-if="!previewObj.imageFound" style="color: red;"> Image not available. </p>
    <p><b>Entity:</b> {{previewObj.type}}</p>
    <p><b>Name:</b> {{previewObj.name}}</p>
    <p><b>UOM:</b> {{previewObj.uom}}</p>
    <p><b>Description:</b> {{previewObj.description}}</p>
</div>
<div data-ng-if="previewObj.found == false">
    <p>Item not found</p>
</div>