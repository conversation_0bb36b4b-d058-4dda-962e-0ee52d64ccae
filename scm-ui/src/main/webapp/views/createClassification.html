<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="right mappingBtn">
                <a class="btn" data-ng-click="addListData()">Add</a>
            </div>
            <div class="mappingBtn" style="margin-top: 10px; font-size: 40px;">
                Classification List
            </div>
        </div>
    </div>
    <hr>
    <div class="row">
        <div class="col s12">
            <div class="scm-form" style="padding: 10px 0;">
                <div class="col s12">
                    <label class="black-text" for="requestForLable">List Type</label> <select
                        ui-select2="selectedUnit3" ng-change="getListDetails(listSelected)"
                        data-placeholder="Select value"
                        data-ng-model="listSelected"
                        data-ng-options="listType as listType for listType in listTypes"></select>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12" data-ng-show="listData">
            <ul class="collection menuItemList">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s1">Id</div>
                        <div class="col s2">Name</div>
                        <div class="col s2">Code</div>
                        <div class="col s2">Type</div>
                        <div class="col s2">description</div>
                        <div class="col s2">Status</div>
                        <div class="col s1">Action</div>
                    </div>
                </li>
                <li class="collection-item z-depth-1" data-ng-repeat="item in listData track by item.listDetailId">
                    <div class="row">
                        <div class="col s1">{{item.listDetailId == null || item.listDetailId == "" ? "NA" :
                            item.listDetailId}}
                        </div>
                        <div class="col s2">{{item.name == null || item.name == "" ? "NA" : item.name}}</div>
                        <div class="col s2">{{item.code == null || item.code == "" ? "NA" : item.code}}</div>
                        <div class="col s2">{{item.type == null || item.type == "" ? "NA" : item.type}}</div>
                        <div class="col s2">{{item.description == null || item.description == "" ? "NA" :
                            item.description}}
                        </div>
                        <div class="col s2">{{item.status == null || item.status == "" ? "NA" : item.status}}</div>
                        <div class="col s1"><a data-ng-click="listDetailEditModal(item)"><span class="fa fa-pencil"
                                                                                               style="color:black"></span></a>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>

<script type="text/ng-template" id="addListDetail.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Add New List Detail</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="col s12" style="margin-top: 10px;" ng-if="!editMode">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="row">
                        <div class="col s12">
                            <label>Type</label> <select ng-model="$parent.type"
                                                        data-ng-options="listType as listType for listType in listTypes"
                                                        data-ng-change="changeIsApplicable()"></select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col s12">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="row">
                        <div class="col s12">
                            <label>Name</label> <input type="text"
                                                       data-ng-model="name">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col s12">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="row">
                        <div class="col s12">
                            <label>Code</label> <input type="text"
                                                       ng-model="code">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col s12">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="row">
                        <div class="col s12">
                            <label>Description</label> <input type="text" ng-model="description">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col s12">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="row">
                        <div class="col s12">
                            <label>Alias</label> <input type="text" ng-model="alias">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col s12">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="row">
                        <div class="col s12">
                            <label>Status</label> <select ng-model="status"
                                                          data-ng-options="period as period for period in periods"></select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--<div class="col s12">-->
            <!--<div class="row">-->
                <!--<div class="col s12 m12 l12">-->
                    <!--<div class="row">-->
                        <!--<div class="col s12">-->
                            <!--<label class="black-text " for="isAccountable">Is Accountable</label>-->
                            <!--<input id="isAccountable" type="checkbox" data-ng-model="isAccountable"/>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</div>-->
        <!--</div>-->
        <!--<div class="col s12" ng-if="accountable">-->
            <!--<div class="row">-->
                <!--<div class="col s12 m12 l12">-->
                    <!--<div class="row">-->
                        <!--<div class="col s12">-->
                            <!--<label>Budget Category</label> <select ng-model="$parent.budgetCategory"-->
                                                          <!--data-ng-options="category as category for category in budgetCategories"></select>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</div>-->
        <!--</div>-->
    </div>

    <div class="modal-footer">
        <button class="btn btn-primary" type="button"
                ng-click="submitListDetail()" data-ng-if="showErrors != true">Submit
        </button>
        <button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
    </div>
</script>