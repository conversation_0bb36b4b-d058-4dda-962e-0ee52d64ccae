<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Search Attribute </h4>
                <div style="display: inline-block;">
                    <a class="waves-effect waves-light btn right"
                       data-ng-click="addAttributeSelected()" style="margin: 1rem;" acl-action="SPPUPP">
                        ADD ATTRIBUTE
                    </a>
                </div>
                <div class="input-field">
                    {{attributeId}}
                    <select ui-select2="selectOptions" ng-model="attributeId"
                            data-ng-change="getAttributeById(attributeId);addAttributeFlag = false; selected = true"
                            data-placeholder="Enter name of a attribute">
                        <option ng-repeat="attr in attributes"
                                value="{{attr.attributeId}}">{{attr.attributeName}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row" data-ng-if="addAttributeFlag == false && selected == true">
    <div class="row white z-depth-3 custom-listing-li" >

        <div class="col s12 "
             data-ng-if="currentAttribute.attributeStatus == 'ACTIVE'">
            <div class="row input-field right">
                <a id="profileActiveStatus"
                   class="waves-effect waves-light btn validate"
                   data-ng-click="changeStatus(currentAttribute, 'IN_ACTIVE')"
                   acl-action="SPPDPE">Deactivate
                </a>
            </div>
        </div>
        <div class="col s12 "
             data-ng-if="currentAttribute.attributeStatus == 'IN_ACTIVE'">
            <div class="row input-field right">
                <a id="atttributeStatus"
                   class="waves-effect waves-light btn validate"
                   data-ng-click="changeStatus(currentAttribute, 'ACTIVE')"
                   acl-action="SPPDPE">ACTIVATE
                </a>
            </div>
        </div>
        <div class="col s12">
            <div class="col s6">
                <!--<p data-ng-if="currentAttribute != null">{{currentAttribute}}</p>-->
                <p class="center-align" >Attribute Name : {{currentAttribute.attributeName}}</p>
                <p class="center-align" >Attribute Type : {{currentAttribute.attributeType}}</p>
                <p class="center-align" >Attribute Code : {{currentAttribute.attributeCode}}</p>
            </div>
            <div class="col s6">
                <!--<p data-ng-if="currentAttribute != null">{{currentAttribute}}</p>-->
                <p class="center-align" >Attribute Short Code : {{currentAttribute.attributeShortCode}}</p>
                <p class="center-align" >Attribute Description : {{currentAttribute.attributeDescription}}</p>
                <p class="center-align" >Attribute Status : {{currentAttribute.attributeStatus}}</p>
            </div>
        </div>
    </div>
</div>
<div data-ng-if="addAttributeFlag == true">
    <form id="attributeForm"  name="attributeForm" class="white z-depth-3 scm-form" novalidate>
        <div class="row">
            <div class="col s12 m6 l6">
                <div class="form-element">
                    <label class=" black-text" for="attributeName">Attribute Name</label>
                    <input id="attributeName" name="attributeName" data-ng-model="attribute.attributeName" type="text" ng-maxlength="100" required>
                    <p ng-show="attributeForm.attributeName.$error.maxlength" class="errorMessage">Attribute name is too large.</p>
                    <p ng-show="attributeForm.attributeName.$error.required" class="errorMessage">Attribute name is required.</p>
                </div>

                <div class="form-element">

                    <label for="toType">Select Attribute  type:</label>
                    <select id="toType" class="form-control" name="toType" ng-model="attribute.attributeType"
                            ng-options="attr as attr for attr in attributeType " required></select>
                    <p ng-show="attributeForm.attributeType.$error.required" class="errorMessage">Attribute type is required.</p>

                </div>

                <div class="form-element">
                    <label class=" black-text" for="attributeCode">Code</label>
                    <input id="attributeCode" name="attributeCode" data-ng-model="attribute.attributeCode" type="text" ng-maxlength="10" required>
                    <p ng-show="attributeForm.attributeCode.$error.maxlength" class="errorMessage">Attribute Code is too large.</p>
                    <p ng-show="attributeForm.attributeCode.$error.required" class="errorMessage">Attribute Code is required.</p>
                </div>

                <div class="form-element">
                    <label class=" black-text" for="attributeShortCode">Short Code</label>
                    <input id="attributeShortCode" name="attributeShortCode" data-ng-model="attribute.attributeShortCode" type="text" ng-maxlength="5" required>
                    <p ng-show="attributeForm.attributeShortCode.$error.maxlength" class="errorMessage">Attribute short Code is too large.</p>
                    <p ng-show="attributeForm.attributeShortCode.$error.required" class="errorMessage">Profile name is required.</p>
                </div>

                <div class="form-element">
                    <label class=" black-text" for="attributeDescription">Attribute Description</label>
                    <textarea id="attributeDescription" name="attributeDescription" data-ng-model="attribute.attributeDescription" data-ng-maxlength="300" required></textarea>
                    <p ng-show="attributeForm.attributeDescription.$error.maxlength" class="errorMessage">Attribute Description is too large.</p>
                    <p ng-show="attributeForm.attributeDescription.$error.required" class="errorMessage">Attribute Description is required.</p>
                </div>
                <div class="form-element">
                    <label>Attribute Status</label>
                    <input name="attributeStatus" type="radio" data-ng-model="attribute.attributeStatus"  value="ACTIVE" id="active" required>
                    <label class="form-check-label" for="active">
                        ACTIVE
                    </label>
                    <input name="attributeStatus" type="radio" data-ng-model="attribute.attributeStatus"  value="IN_ACTIVE" id="inActive" required>
                    <label class="form-check-label" for="inActive">
                        IN ACTIVE
                    </label>
                    <p ng-show="attributeForm.attributeStatus.$error.required" class="errorMessage">Attribute Description is required.</p>
                </div>
            </div>
        </div>
    </form>
    <div class="col s12">
        <button data-ng-if="attributeForm.$valid" data-ng-click="createAttribute()" class="btn right" acl-action="SPAAPD"> Create
            <i class="material-icons right">send</i>
        </button>
    </div>
</div>

