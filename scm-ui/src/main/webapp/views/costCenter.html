<div
	class="row white z-depth-3 custom-listing-li"
	data-ng-init="init()">
	<div class="col s12">
		<h4>Cost Center</h4>
	</div>
	<div Class="col s12">
		<div Class="row">
			<div class="col s12">
				<span
					data-ng-click="openAddNewCostCenterModal()"> <a
						href="#addNewCostCenter"
						class="btn right"
						style="margin-right: 10px" modal
					><b>+</b> Add New Cost Center</a>
				</span>
			</div>
		</div>
	</div>
</div>

<ul class="collection striped" data-ng-show="allCostCenters.length>0">
	<li class="collection-item list-head">
	    <div class="row">
	        <div class="col s1">Id</div>
	        <div class="col s2">Code</div>
	        <div class="col s2">Name</div>
	        <div class="col s3">Email Id</div>
	        <div class="col s2">Description</div>
	        <div class="col s2">Owner</div>
	    </div>
	</li>
      <li class="collection-item clickable"
                    data-ng-repeat="cc in allCostCenters">
         <div class="row" style="margin-bottom: 0px;">
			<div class="col s1"> {{cc.id}}</div>
			<div class="col s2"> {{cc.code}}</div>
			<div class="col s2"> {{cc.name}}</div>
			<div class="col s3"> {{cc.costCenterEmail}}</div>
			<div class="col s2"> {{cc.description}}</div>
			<div class="col s2"> {{cc.owner.name}}</div>
          </div>
     </li>
</ul>

<div
	id="addNewCostCenter"
	class="modal modal-mx">
	<form
		id="basicDetail"
		name="basicDetailsForm"
		class="white z-depth-3 scm-form"
		novalidate>
		<div class="modal-content">
			<div class="row">
				<div class="col s12 card-panel teal lighten-2">
					<h5 class="white-text center">Add Cost Center</h5>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Cost Center Name</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="text"
					data-ng-model="costCenter.name"
					required></input>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Email Id</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="email"
					data-ng-model="costCenter.costCenterEmail"
					required></input>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Cost Center Code</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="text"
					onkeyup="this.value = this.value.toUpperCase()"
					style='text-transform:uppercase'
					data-ng-model="costCenter.code"
					required></input>
			</div>
		</div>
		
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Description</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="text"
					data-ng-model="costCenter.description"
					required></input>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Owner</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<select
					ui-select2
					style="width: 100%;"
					id="ownerSelector"
					data-ng-model="costCenter.owner"
					data-ng-options="o as o.name for o in employees"
					required></select>
			</div>
		</div>
		<div class="modal-footer">
			<div class="row">
				<div class="col s12">
					<button
						class="modal-action waves-effect waves-green btn right"
						data-ng-class="{'disabled':!basicDetailsForm.$valid}"
						style="margin-right: 10px"
						data-ng-click="createCostCenter()"
						data-ng-disabled="!basicDetailsForm.$valid">Submit</button>
					<button
						class="modal-action modal-close waves-effect waves-green btn right"
						style="margin-right: 10px"
						data-ng-click="clearCostCenter()">Cancel</button>
				</div>
			</div>
		</div>
	</form>
</div>