<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	<div class="col s12">
		<div class="row">
                <div class="right mappingBtn">
                    <a class="btn" data-ng-click="vehicleModal()">Add new Vehicle</a>
                </div>
                <div class="mappingBtn" style="margin-top: 10px;font-size: 40px;">
                    Vehicle List
                </div>
            </div>
	</div>

	<div class="row">
		<div class="respTable col s12 standardView" data-ng-show="vehicleList">
			<ul class="row collection menuItemList">
				<li class="collection-item z-depth-1 list-head">
					<div class="row">
						<div class="col s2">Registration Number</div>
						<div class="col s2">Vehicle Name</div>
						<div class="col s2">Model</div>
						<div class="col s2">Vehicle's Brand</div>
						<div class="col s1">Transport Mode</div>
						<div class="col s1">Vehicle Status</div>
						<div class="col s1">Multi Dispatch</div>
						<div class="col s1">Action</div>
					</div>
				</li>
				<li class="collection-item z-depth-1" data-ng-repeat="item in vehicleList track by item.vehicleId">
					<div class="row">
						<div class="col s2">{{item.registrationNumber == null || item.registrationNumber == "" ? "NA" : item.registrationNumber}}</div>
						<div class="col s2">{{item.name == null || item.name == "" ? "NA" : item.name}}</div>
						<div class="col s2">{{item.model == null || item.model == "" ? "NA" : item.model}}</div>
						<div class="col s2">{{item.make == null || item.make == "" ? "NA" : item.make}}</div>
						<div class="col s1">{{item.transportMode == null || item.transportMode == "" ? "NA" : item.transportMode}}</div>
						<div class="col s1">{{item.status == null || item.status == "" ? "NA" : item.status}}</div>
						<div class="col s1">{{item.multiDispatch == null || item.multiDispatch == "" ? "NA" : item.multiDispatch}}</div>
						<div class="col s1"><a data-ng-click="editVehicleData(item.vehicleId)"><span class="fa fa-pencil" style="color:black"></span></a></div>
					</div>
				</li>
			</ul>
		</div>
		<div class="TableMobileView" data-ng-show="vehicleList">
			<ul class="row collection center">
				<li class="collection-item z-depth-1" data-ng-repeat="item in vehicleList track by item.vehicleId">
					<div class="row">
						<div class="col">Registration Number</div>
						<div class="col">{{item.registrationNumber == null || item.registrationNumber == "" ? "NA" : item.registrationNumber}}</div>
					</div>
					<div class="row">
						<div class="col">Vehicle Name</div>
						<div class="col">{{item.name == null || item.name == "" ? "NA" : item.name}}</div>
					</div>
					<div class="row">
						<div class="col">Model</div>
						<div class="col">{{item.model == null || item.model == "" ? "NA" : item.model}}</div>
					</div>
					<div class="row">
						<div class="col">Vehicle's Brand</div>
						<div class="col">{{item.make == null || item.make == "" ? "NA" : item.make}}</div>
					</div>
					<div class="row">
						<div class="col">Transport Mode</div>
						<div class="col">{{item.transportMode == null || item.transportMode == "" ? "NA" : item.transportMode}}</div>
					</div>
					<div class="row">
						<div class="col">Vehicle Status</div>
						<div class="col">{{item.status == null || item.status == "" ? "NA" : item.status}}</div>
					</div>
					<div class="row">
						<div class="col">Multi Dispatch</div>
						<div class="col">{{item.multiDispatch == null || item.multiDispatch == "" ? "NA" : item.multiDispatch}}</div>
					</div>
					<div class="row">
						<div class="col">Action</div>
						<div class="col"><a data-ng-click="editVehicleData(item.vehicleId)"><span class="fa fa-pencil" style="color:black"></span></a></div>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

<script type="text/ng-template" id="vehicleModal.html">
<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Add New Vehicle</h3>
    <hr>
</div>
<div class="modal-body" id="modal-body">
	<div class="row">
		<div class="col s12" style="margin-top: 10px;">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Vehicle Name</label> <input type="text"
								data-ng-model="vehicleName">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Registration Number</label> <input type="text"
								ng-model="registrationNumber">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Model</label> <input type="text" ng-model="model">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Vehicle's Brand</label> <input type="text" ng-model="make">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Transport Mode</label> <select ng-model="transportMode"
								data-ng-options="transportMode as transportMode for transportMode in transportModes"></select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Vehicle Status</label> <select ng-model="vehicleStatus"
								data-ng-options="period as period for period in periods"></select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Multi Dispatch</label> <select ng-model="multiDispatch"
								data-ng-options="multiDispatch as multiDispatch for multiDispatch in multiDispatchs"></select>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	</div>

		<div class="modal-footer">
			<button class="btn btn-primary" type="button"
				ng-click="submitVehicleData()" data-ng-if="showErrors != true">Submit</button>
			<button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
		</div>
		</script>