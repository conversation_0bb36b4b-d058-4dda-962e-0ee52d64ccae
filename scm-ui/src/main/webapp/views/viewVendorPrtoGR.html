<style>
    #viewGRList .collapsible-header.inverse{
        background-color: #CDDC39;
        color: white;
    }
    #viewGRList .collapsible-header{
        color: #654848;
        background-color: white;
        border: 1px solid #26a69a;
    }
    .scrollabe-table {
        display: block !important;
        overflow-x: auto !important;
        white-space: nowrap !important;
    }
    .scrollabe-table thead tr th{
        min-width: 150px;
    }
    .greyBackground {
        background-color: lightgrey;
    }
</style>
<div class="row" data-ng-init="init()">
    <div style="width=100%" id="printHide">
        <div class="col s12">
            <div class="searchingCard row white z-depth-3 custom-listing-li">
                <div class="row">
                    <div class="col s12">
                        <h4>View Vendor Purchase Order TO Goods Receivings</h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col s2">
                        <label>Select Start date</label>
                        <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
                    </div>
                    <div class="col s2">
                        <label>Select End date</label>
                        <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
                    </div>
                    <div class="col s2">
                        <label>Payment Request Id</label>
                        <input type="number" placeholder="PR ID" name="prId" id="prId" ng-model="prId"/>
                    </div>
                    <div class="col s2">
                        <label>Good Received Id</label>
                        <input type="number" placeholder="GR ID" name="grId" id="grId" ng-model="grId"/>
                    </div>
                    <div class="col s2">
                        <label>Select Vendor</label>
                        <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" ng-options="vendor as vendor.entityName for vendor in vendors"
                                data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                    </div>
                    <div class="col s2">
                        <label>Select SKU</label>
                        <select id="skuList" ui-select2="{allowClear:true, placeholder: 'Select SKU'}" ng-options="sku.id as sku.name for sku in skus"
                                data-ng-change="selectSKU(skuSelected)" data-ng-model="skuSelected"></select>
                    </div>
                    <div class="col s2">
                        <button class="btn margin-top-20" data-ng-click="getGRs()">Find</button>
                    </div>
                </div>
                <hr>
                <div class="row" style="padding:30px;color:gray;text-align: center;"
                     data-ng-show="grs==null || grs.length==0">
                    No Vendor Receivings found for the selected criteria
                </div>
                <div class="row" data-ng-show="grs!=null && grs.length>0">
                    <!--<div style="padding: 0 22px;">-->
                        <!--<input type="button" class="btn" value="No payment" acl-action="VGRDP" data-ng-click="setGRsForNoPayment()" />-->
                    <!--</div>-->
                    <ul id="viewGRList" class="col s12" data-collapsible="accordion" watch>
                        <li class="row" data-ng-repeat="gr in grs track by gr.id">
                            <div class="col s10 collapsible-header waves-effect waves-light lighten-5" data-ng-class="{'inverse':gr.id==grId}">
                                <div class="left">
                                    GR No.: <b>{{gr.id}}</b> for {{gr.generatedForVendor.name}} [{{gr.dispatchLocation.city}}]
                                    <span class="chip" data-ng-if="gr.invalid">INVALID</span>
                                </div>
                                <div class="right">
                                    <span class="chip grey white-text">{{gr.receiptType}}: {{gr.receiptNumber}}</span>
                                    <span class="chip grey white-text" data-ng-if="gr.paymentRequestId != null">PR Generated: {{gr.paymentRequestId}}</span>
                                    <span class="chip grey white-text" data-ng-if="gr.paymentRequestStatus != null">PR Status: {{gr.paymentRequestStatus}}</span>
                                    <span data-ng-class="{'chip green white-text':gr.toBePaid == true, 'chip red white-text':gr.toBePaid != true}">To be paid: {{gr.toBePaid?'Yes':'No'}}</span>
                                    <span class="chip grey white-text">
                                    {{gr.generationTime | date:'dd/MM/yyyy @ h:mma'}}
                                </span>
                                    <span class="chip grey white-text">GR Status: {{gr.status}}</span>
                                    <i class="fa fa-caret-down right"></i>
                                </div>
                            </div>
                            <div class="col s2">
                                <button data-ng-click="printGR(gr)" class="btn">Print</button>
                                <button id="printDiv" print-btn class="btn" data-ng-show="false">Print</button>
                            </div>
                            <div class="respTable collapsible-body">
                                <table class="row bordered striped">
                                    <thead>
                                    <tr>
                                        <th class="center-align">SKU</th>
                                        <th class="center-align">UOM</th>
                                        <th class="center-align">Pkg</th>
                                        <th class="center-align">Pkg Qty</th>
                                        <th class="center-align">Ratio</th>
                                        <th class="center-align">Total Qty</th>
                                        <th class="center-align">Price</th>
                                        <th class="center-align" tooltipped data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*</th>
                                        <th class="center-align">Taxes</th>

                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="item in gr.grItems track by $index">
                                        <td class="center-align">{{item.skuName}}[{{item.hsn}}]</td>
                                        <td class="center-align">{{item.unitOfMeasure}}</td>
                                        <td class="center-align">{{item.packagingName}}</td>
                                        <td class="center-align">{{item.packagingQty}}</td>
                                        <td class="center-align">{{item.conversionRatio}}</td>
                                        <td class="center-align">{{item.receivedQuantity.toFixed(2)}}</td>
                                        <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                        <td class="center-align">{{item.totalCost}}</td>
                                        <td class="center-align">{{item.totalTax}}</td>

                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div style="width:100%" id="printSection">
        <div class="col s12">
            <div class="row" style="margin-bottom: 5px;">
                <div class="col s12">
                    <p style="text-align: center;"><b><span
                            style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
                            style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[currentPrintGR.companyId].name}}<br />
					</span></b><span
                            style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[currentPrintGR.companyId].registeredAddress.line1}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.line2}}, <br /> {{companyMap[currentPrintGR.companyId].registeredAddress.city}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.state}}, <br /> {{companyMap[currentPrintGR.companyId].registeredAddress.country}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.zipCode}}<br />
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Goods Receipt (GR)</span></b>
                    </p>
                </div>
            </div>
            <p style="text-align:center;font-size:21px;margin:0;font-weight:bold;" data-ng-if="currentPrintGR.dispatchLocation.gstStatus!='REGISTERED'">Vendor Not Registered</p>
            <div id="expandedPOView" class="row custom-listing-li" style="padding:5px;">
                <div class="col s12" data-ng-if="currentPrintGR.grItems!=null">
                    <table style="margin-bottom: 20px;">
                        <thead class="itemsTable">
                        <tr>
                            <th style="width: 20%">Vendor Details</th>
                            <th style="width: 30%"></th>
                            <th style="width: 20%">Dispatch Details</th>
                            <th style="width: 30%"></th>
                        </tr>
                        </thead>
                        <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                        <tr>
                            <td>Vendor Name:</td>
                            <td>{{currentPrintGR.generatedForVendor.name}}</td>
                            <td>Dispatch Location</td>
                            <td>{{currentPrintGR.dispatchLocation.city}}</td>
                        </tr>
                        <tr>
                            <td>Vendor Id:</td>
                            <td>{{currentPrintGR.generatedForVendor.id}}</td>
                            <td>Dispatch Address</td>
                            <td>{{currentPrintGR.dispatchLocation.address.line1}}, {{currentPrintGR.dispatchLocation.address.line2}}, {{currentPrintGR.dispatchLocation.address.city}},
                                {{currentPrintGR.dispatchLocation.address.state}}, {{currentPrintGR.dispatchLocation.address.country}}, {{currentPrintGR.dispatchLocation.address.zipcode}}</td>
                        </tr>
                        <tr>
                            <td>Date of GR:</td>
                            <td>{{currentPrintGR.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                            <td>Dispatch State</td>
                            <td>{{currentPrintGR.dispatchLocation.state}}</td>
                        </tr>
                        <tr>
                            <td>GR No.:</td>
                            <td>{{currentPrintGR.id}}</td>
                            <td>Dispatch State Code</td>
                            <td>{{currentPrintGR.dispatchLocation.address.stateCode}}</td>
                        </tr>
                        </tbody>
                    </table>
                    <table style="margin-bottom: 20px;">
                        <thead class="itemsTable">
                        <tr>
                            <th style="width: 20%">Receiving Unit Name</th>
                            <th style="width: 20%">{{currentPrintGR.deliveryUnitId.name}}</th>
                            <th style="width: 20%">Receiving Company Name</th>
                            <th style="width: 40%">{{companyMap[currentPrintGR.companyId].name}}</th>
                        </tr>
                        <tr>
                            <th style="width: 20%">Receiving Details</th>
                            <th style="width: 30%"></th>
                            <th style="width: 20%">Document Details</th>
                            <th style="width: 30%"></th>
                        </tr>
                        </thead>
                        <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                        <tr>
                            <td>Received By:</td>
                            <td>{{currentPrintGR.generatedBy.name}}</td>
                            <td>Document Type</td>
                            <td>{{currentPrintGR.receiptType}}</td>
                        </tr>
                        <tr>
                            <td>Receiving Time:</td>
                            <td>{{currentPrintGR.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                            <td>Document No.</td>
                            <td>{{currentPrintGR.receiptNumber}}</td>
                        </tr>
                        <tr>
                            <td>Delivery State:</td>
                            <td>{{currentPrintGR.deliveryUnitId.state}}</td>
                            <td>Document Date</td>
                            <td>{{currentPrintGR.grDocumentDate | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                        </tr>
                        <tr>
                            <td>Delivery State Code:</td>
                            <td>{{currentPrintGR.deliveryUnitId.stateCode}}</td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <h5 style="margin-top:0px;font-size: 18px;">List of Items Received</h5>
                    <div class="row margin0 itemsTable">
                        <table class="bordered" style="border-top: 1px solid #d0d0d0;">
                            <thead>
                            <tr>
                                <th class="center-align">SKU ID</th>
                                <th class="center-align">SKU</th>
                                <th class="center-align">Category</th>
                                <th class="center-align">Sub Category</th>
                                <th class="center-align">Price</th>
                                <th class="center-align">Pkg</th>
                                <th class="center-align">Qty</th>
                                <th class="center-align">Total</th>
                                <th class="center-align">Taxes</th>
                                <th class="center-align">Amount</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="(skuId,item) in currentPrintGR.grItems track by $index">
                                <td>{{item.skuId}}</td>
                                <td>{{item.skuName}} [{{item.packagingName}}]</td>
                                <td class="center-align">{{item.category}}</td>
                                <td class="center-align">{{item.subCategory}}</td>
                                <td class="center-align">{{item.unitPrice}}</td>
                                <td class="center-align">{{item.packagingName}}</td>
                                <td class="center-align">{{(item.receivedQuantity/item.conversionRatio).toFixed(2)}}</td>
                                <td class="center-align">{{item.totalCost}}</td>
                                <td class="center-align">
                                    <span>{{item.totalTax}}</span>
                                    (<span ng-repeat="tax in item.taxes">{{tax.taxName}}@{{tax.percentage}}%</span>)
                                </td>
                                <td class="center-align">{{item.amountPaid}}</td>
                            </tr>
                            <tr>
                                <th></th>
                                <th><b>Extra Charges</b></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th class="center-align">{{currentPrintGR.extraCharges.toFixed(2)}}</th>
                            </tr>
                            <tr>
                                <th></th>
                                <th><b>Total</b></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th class="center-align">{{currentPrintGR.total}}</th>
                            </tr>
                            <tr>
                                <th></th>
                                <th><b>Created By</b></th>
                                <th>{{currentPrintGR.generatedBy.name}}</th>
                                <th><b>Approved By</b></th>
                                <th>{{currentPrintGR.approvedBy.name}}</th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <p style="text-align:right;margin-right:20px;font-weight:bold;">Amounts Match: {{currentPrintGR.amountMatched?'Yes':'No'}}</p>
                </div>

                <div class="col s12">
                    <div class="row margin0">
                        <ul class="col s12">
                            <h5 style="margin-top:0px;font-size: 18px;">List of PO</h5>
                            <li class="row margin0" data-ng-repeat="po in currentPrintGR.purchaseOrderList track by po.id">
                                <div class="poNumber">
                                    {{po.receiptNumber}}
                                    <span style="margin-left: 50px;font-weight:bold;">Date: {{po.initiationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</span>
                                    <span class="chip right">{{po.status}}</span>
                                </div>
                                <table class="bordered itemsTable" style="margin-bottom: 20px;">
                                    <thead>
                                    <tr>
                                        <th class="center-align">SKU ID</th>
                                        <th class="center-align">SKU</th>
                                        <th class="center-align">UOM</th>
                                        <th class="center-align">Pending</th>
                                        <th class="center-align">Received Packaging</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="item in po.orderItems | filter : filteredByReceivedQuantity track by $index ">
                                        <td>{{item.skuId}}</td>
                                        <td class="">{{item.skuName}} [{{item.packagingName}}]</td>
                                        <td class="center-align">{{item.unitOfMeasure}}</td>
                                        <td class="center-align">
                                            {{((item.requestedQuantity - item.receivedQuantity)/item.conversionRatio).toFixed(2)}}
                                        </td>
                                        <td class="center-align">
                                            {{item.receivedQuantity!=null ? (item.receivedQuantity/item.conversionRatio).toFixed(2) : 0}}
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </li>
                        </ul>
                    </div>
                </div>
                <p style="font-size:12px;">Certified that the particulars and the amount indicated given above are true and correct.</p>
                <div style="width: 250px;border:#000 1px solid;float:right;">
                    <div style="height:150px"></div>
                    <div style="border-top:#000 1px solid;text-align:center;">Authorised Signatory</div>
                </div>
            </div>
        </div>
    </div>
</div>