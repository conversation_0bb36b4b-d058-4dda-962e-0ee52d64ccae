<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h3>Update stock event calendar</h3>
        <div class="form-group">
            <button data-ng-click="newStock()"
                    class="btn right">New Stock
            </button>
        </div>
    </div>
</div>
<div ng-show="!addNewStockFlag">
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="black-text" for="unitList">Unit</label>
                <select ui-select2="selectedUnit3" id="unitList" name="unitList" data-placeholder="Select cafe"
                        data-ng-model="selectedUnit"
                        data-ng-options="unit as unit.name for unit in allUnitsList | orderBy: 'name' track by unit.id"></select>
            </div>
            <div class="form-group">
                <label class="black-text" for="unitList">Stock frequency</label>
                <select data-ng-options="frequency as frequency.name for frequency in stockFrequency"
                        data-ng-model="selectedFrequency"
                        data-ng-change="setSelectedFrequency(selectedFrequency)"></select>
            </div>
            <div class="form-group" style="margin-top: 20px">
                <button data-ng-click="getCalendarEvents()" data-tooltip="Get planned events"
                        class="btn left" tooltipped>Get Events
                </button>
            </div>
        </div>
    </div>
</div>

<div ng-show="!addNewStockFlag">
    <div class="row" data-ng-if="plannedEvents != null ">
        <div class="col-xs-12">
            <table class="table bordered striped" data-ng-if="plannedEvents.length>0">
                <tr>
                    <td>Scheduled Date</td>
                    <td>Status</td>
                    <td>Frequency</td>
                    <td>Updated at</td>
                    <td>Updated By</td>
                    <td>Change Date</td>
                </tr>
                <tr data-ng-repeat="item in plannedEvents">
                    <td>{{item.scheduledDate | date:'yyyy-MM-dd'}}</td>
                    <td>{{item.status}}</td>
                    <td>{{item.eventFrequencyType}}</td>
                    <td>{{item.updatedAt != null ? (item.updatedAt | date:'yyyy-MM-dd HH:mm:ss'):""}}</td>
                    <td>{{item.updatedBy}}</td>
                    <td>
                        <input input-date type="text" name="created" id="inputCreated"
                               ng-model="item.newScheduledDate"
                               container="" format="yyyy-mm-dd" select-years="1"
                               min="{{minDate}}" max="{{maxDate}}"
                               data-ng-change="setEventDate(item.newScheduledDate, item)"/>
                    </td>
                </tr>
            </table>
            <button data-ng-if="plannedEvents.length>0" data-ng-click="updateEvents()" data-tooltip="Update Event"
                    style="margin-top: 20px" class="btn right" tooltipped>Update Events
            </button>
            <div data-ng-if="plannedEvents.length==0" style="padding: 20px;">
                No planned events found.
            </div>
        </div>
    </div>
</div>
<div ng-show="addNewStockFlag">
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="black-text" for="unitList">Stock Type</label>
                <select
                        data-ng-model="selectedStockTake"
                        data-ng-options="frequency.name for frequency in stockFrequency"></select>
            </div>
            <div class="form-group">
                <label class="black-text">Unit</label>
                <select data-ng-options=" unit.name for unit in allUnitsList | orderBy: 'name'"
                        data-ng-model="selectedNewUnit"
                ></select>
            </div>
            <div class="form-group">
                <label class="black-text">Date</label>
                <input input-date type="text"
                       ng-model="scheduledDate"
                       container="" format="yyyy-mm-dd" select-years="1"
                       min="{{minDate}}" max="{{maxDate}}"/>
                <!--data-ng-change="setEventDate(item.newScheduledDate, item)"/>-->
            </div>
            <div class="row">
                <div class="form-group" style="margin-top: 20px">
                    <button data-ng-click="submitStock(selectedStockTake,selectedNewUnit,scheduledDate)"
                            class="btn left">Submit
                    </button>
                </div>
                <div class="form-group" style="margin-top: 20px">
                    <button data-ng-click="cancelStockMenu()"
                            class="btn right">Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
