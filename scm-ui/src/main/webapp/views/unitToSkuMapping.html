<style>
.grid {
	height: 500px;
	padding: 0 !important;
}

.select2.select2-container {
	width: auto !important;
}
</style>
<div
	class="row white z-depth-3"
	data-ng-init="init()">
	<div class="col s12">
		<h4>Unit to SKU Mapping</h4>
	</div>
	<div
		class="row"
		data-ng-show="!updateMappingRequested"
		id="mappingDivDisplay">
		<div class="col s12">
			<div
				class="scm-form"
				style="padding: 10px 0;">
				<div class="col s6">
					<label
						class="black-text"
						for="requestForLable">Mapping Type</label>
					<select
						ui-select2="selectedUnit1"
						id="requestForListData"
						name="requestForListData"
						data-placeholder="Select mapping type"
						data-ng-model="unitMappingView"
						data-ng-options="unitMappingName as unitMappingName.name for unitMappingName in mappingList track by unitMappingName.id"
						data-ng-change="showRequestForMapping(unitMappingView.id)"></select>
					<label
						class="black-text"
						for="requestForLable">Values</label>
					<select
						ui-select2="selectedUnit3"
						id="mappingDisplayData"
						name="mappingValuesShow"
						data-placeholder="Select value"
						data-ng-model="mappingValuesDisplay"
						data-ng-options="unit as unit.name for unit in UnitListDetails | orderBy: 'name' track by unit.id"
						data-ng-change="searchMappingShow()"></select>
					<button
						data-ng-click="searchMappingShow()"
						data-tooltip="Search Mappings"
						class="btn left" tooltipped acl-action="SMMUSVI">Search</button>
				</div>
				<div
					class="col s6"
					data-ng-hide="gridDataId == null">
					<label
						class="black-text"
						for="cloneDisplayData">Clone From</label>
					<select
						ui-select2="selectedUnit2"
						id="cloneDisplayData"
						name="mappingValuesShow"
						data-ng-model="cloningMapping"
						data-placeholder="Select clone value"
						data-ng-options="clone as clone.name for clone in UnitListDetails | orderBy: 'name' | filter : filterSelectedUnit track by clone.id"></select>
					<button
						data-ng-click="sendCloneRequest()"
						data-tooltip="Clone Mappings from the selected Unit"
						class="btn right" tooltipped acl-action="SMMUSUP">Clone</button>
				</div>
			</div>
		</div>
	</div>
</div>
<div
	class="scm-form"
	data-ng-if="!updateMappingRequested && gridDataId != null">
	<div
		class="row margin0"
		data-ng-hide="mappingValuesDisplay == null">
		<div class="col s4">
			<h6
				class="card-panel slim red lighten-2 white-text "
				align="left">
				<b>Updating Mappings for {{mappingValuesDisplay.name}}</b>
			</h6>
		</div>
		<div class="col s4">
			<h6 data-ng-show="!typesofClone"></h6>
			<h6
				align="left"
				data-ng-show="typesofClone"
				class="card-panel slim red lighten-2 white-text">
				<b>Mappings Cloned from {{cloningMapping.name}}</b>
			</h6>
		</div>
		<div
			class="col s4"
			align="right">
			<button
				id="reset"
				type="button"
				class="btn btn-success"
				data-tooltip="Reset Mappings"
				data-ng-click="searchMappingShow()" tooltipped acl-action="SMMUSUP">Reset</button>
			<button
				id="save"
				type="button"
				class="btn btn-success"
				data-tooltip="Update Mappings"
				data-ng-click="submitMapping()" tooltipped acl-action="SMMUSUP">Submit</button>
		</div>
	</div>
	<div class="row margin0">
		<div
			class="col s12 grid"
			id="grid"
			ui-grid="gridOptions"
			ui-grid-save-state
			ui-grid-edit
			ui-grid-selection
			ui-grid-resize-columns
			ui-grid-move-columns></div>
	</div>
</div>
<div
	data-ng-if="updateMappingRequested"
	id="mappingSummaryViewDiv">
	<div class="row margin0">
		<div
			class="col s7 card-panel red lighten-2"
			align="left">
			<h6 class="white-text center">
				<b>Updating Mappings for {{mappingValuesDisplay.name}}</b>
			</h6>
		</div>
		<div
			class="col s7"
			align="right">
			<button
				class="btn btn-success"
				data-ng-click="backPreview()">Back</button>
			<button
				class="btn btn-success red lighten-2"
				data-ng-click="updateMapping()">update</button>
		</div>
	</div>
	<div
		data-ng-if="allGridViewShow.length == 0"
		class="text-center-disabled">
		No Active Mappings to display. <br>Click Update to mark all
		mappings as Inactive
	</div>
	<div
		class="row margin0"
		data-ng-if="allGridViewShow.length > 0">
		<div class="col s12">
			<ul class="collection striped">
				<li class="collection-item list-head">
					<div class="row">
						<div class="col s1">Id</div>
						<div class="col s2">Name</div>
						<div class="col s2">Category</div>
						<div class="col s2">Sub Category</div>
						<div class="col s1">Status</div>
						<div class="col s1">Mapping Status</div>
						<div class="col s1">InventoryID</div>
						<div class="col s1">Production Unit</div>
						<div class="col s1">Packaging</div>
					</div>
				</li>
			</ul>
			<ul
				class="collection striped"
				style="max-height: 350px; overflow: auto;">
				<li
					class="collection-item clickable"
					data-ng-repeat="allGVS in allGridViewShow | orderBy : 'id' track by $index">
					<div
						class="row"
						style="margin-bottom: 0px;">
						<div class="col s1">&nbsp;{{allGVS.id}}</div>
						<div class="col s2">&nbsp;{{allGVS.name }}</div>
						<div class="col s2">&nbsp;{{allGVS.category}}</div>
						<div class="col s2">&nbsp;{{allGVS.subCategory}}</div>
						<div class="col s1">&nbsp;{{allGVS.status}}</div>
						<div class="col s1">&nbsp;{{allGVS.mappingStatus}}</div>
						<div class="col s1">&nbsp;{{allGVS.inventoryList}}</div>
						<div class="col s1">&nbsp;{{allGVS.productionUnit}}</div>
						<div class="col s1">&nbsp;{{allGVS.skuPackaging}}</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

<script
		type="text/ng-template"
		id="skuIdTemplate.html">
	<div class="ui-grid-cell-contents">
		<a style="cursor:pointer;" data-ng-click="grid.appScope.showPreview($event, row.entity.id,'SKU')">{{row.entity.id}}</a>
	</div>
</script>