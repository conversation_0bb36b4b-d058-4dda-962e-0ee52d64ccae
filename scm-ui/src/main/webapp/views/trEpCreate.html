<style>
    .amount-rows{
        padding: 5px;
        font-size: 20px;
    }

    .modal-large{
        width:70%;
    }
</style>

<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Create Transfer for Vendor</h4>
            </div>
            <div class="row">
                <div class="col s6">
                    <div class="row">
                        <div class="col s4">
                            <b>Generation Unit</b>
                        </div>
                        <div class="col s8">
                            <div class="left">{{currentUnit.name}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col s5">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Vendor</b>
                        </div>
                        <div class="col s8">
                            <select ui-select2="selectedVendor" id="vendorList" name="vendorList" data-ng-model="selectedVendor"
                                    data-ng-options="vendor as vendor.entityName for vendor in vendorList track by vendor.vendorId"
                                    data-ng-change="selectVendor(selectedVendor)"></select>
                        </div>
                    </div>
                </div>
                <div class="col s7" data-ng-if="locationList.length>0">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Dispatch Location</b>
                        </div>
                        <div class="col s6">
                            <select ui-select2="selectedUnit" id="locationList" name="locationList" data-ng-model="selectedLocation"
                                    data-ng-options="location as location.city for location in locationList track by location.dispatchId"
                                    data-ng-change="selectDispatchLocation(selectedLocation)"></select>
                        </div>
                        <div class="col s2">
                            <button data-ng-click="getSkusMapped()" style="width: 75px;" class="btn btn-xs-small">Search</button>
                        </div>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row" data-ng-if="selectedDispatchLocation.dispatchId != null">
                <div class="col s12">
                    <div class="row">
                        <div class="col s8">
                            <h5 style="margin-top:5px;">Add Items to Transfer</h5>
                        </div>
                        <div class="col s4">
                            <a href="#previewTrEp" modal class="btn btn-large right"
                               data-ng-if="transferOrderItems!=null" style="margin-top:10px;">Preview & Submit</a>
                        </div>
                    </div>
                </div>
                <div class="col s12">
                    <div class="row">
                        <div class="col s3">
                            <label>Select SKU</label>
                            <select data-ng-model="selectedSku" data-ng-change="selectSku(selectedSku)"
                                    data-ng-options="sku as sku.name for sku in skuList track by sku.id"></select>
                        </div>
                        <div class="col s3">
                            <label>Select Packaging</label>
                            <select data-ng-model="selectedPkg" data-ng-change="selectPkg(selectedPkg)" data-ng-disabled="selectedSku==null"
                                    data-ng-options="pkg as pkg.name for pkg in packagingList track by pkg.id"></select>
                        </div>
                        <div class="col s2">
                            <label>Select Quantity</label>
                            <input type="number" min="0" step="0.001" ng-model="selectedQty">
                        </div>
                        <div class="col s2">
                            <label>Unit Packaging Price</label>
                            <input type="number" min="0" step="0.001" ng-model="unitPrice">
                        </div>
                        <div class="col s2" style="padding-top: 20px;">
                            <button class="btn" data-ng-click="addTOItem(selectedSku,selectedPackaging,selectedQty,unitPrice)">Add</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" data-ng-show="transferOrderItems!=null">
                <table id="toItemTable" class="bordered striped to-table">
                    <thead>
                    <tr fsm-sticky-header scroll-body="#toItemTable" scroll-stop='65'>
                        <th class="center-align">SKU</th>
                        <th class="center-align">UOM</th>
                        <th class="center-align">Pkg Qty</th>
                        <th class="center-align">Ratio</th>
                        <th class="center-align">Total Qty</th>
                        <th class="center-align">Price</th>
                        <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                        <th class="center-align" tooltipped data-tooltip="Remove SKU Item">Actions*</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="(id,item) in transferOrderItems track by $index">
                        <td class="center-align">
                            <a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}} [{{item.packagingDetails[0].packagingDefinitionData.packagingName}}]</a>
                        </td>
                        <td class="center-align">{{item.unitOfMeasure}}</td>
                        <td class="center-align">{{item.packagingDetails[0].numberOfUnitsPacked.toFixed(2)}}</td>
                        <td class="center-align">{{item.packagingDetails[0].conversionRatio.toFixed(2)}}</td>
                        <td class="center-align">{{item.transferredQuantity.toFixed(2)}}</td>
                        <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                        <td class="center-align">{{item.total.toFixed(2)}}</td>
                        <td class="center-align">
                            <button ng-click="removeTrItem(id)" tooltipped data-tooltip="Remove SKU from List"
                                    class="btn btn-floating red"><i class="material-icons">close</i></button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>



<!-- Modal to Preview Transfer Order -->
<!-- Modal Structure -->
<div id="previewTrEp" class="modal modal-large">
    <div class="modal-content">
        <div class="row">
            <h4>Preview Transfer Order</h4>
            <br/>
            <span style="font-weight:700;">{{selectedVendor.entityName}}-{{selectedDispatchLocation.locationName}}</span>
        </div>
        <div class="row" style="width: 98%;">
            <table class="bordered striped to-table">
                <thead>
                <tr>
                    <th class="center-align">SKU</th>
                    <th class="center-align">UOM</th>
                    <th class="center-align">Pkg Qty</th>
                    <th class="center-align">Ratio</th>
                    <th class="center-align">Total Qty</th>
                    <th class="center-align">Price</th>
                    <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="(id,item) in transferOrderItems track by $index">
                    <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}} [{{item.packagingDetails[0].packagingDefinitionData.packagingName}}]</a></td>
                    <td class="center-align">{{item.unitOfMeasure}}</td>
                    <td class="center-align">{{item.packagingDetails[0].numberOfUnitsPacked.toFixed(2)}}</td>
                    <td class="center-align">{{item.packagingDetails[0].conversionRatio.toFixed(2)}}</td>
                    <td class="center-align">{{item.transferredQuantity.toFixed(2)}}</td>
                    <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                    <td class="center-align">{{item.total.toFixed(2)}}</td>
                </tr>
                </tbody>
            </table>
            <hr>
            <div class="row">
                <div class="right">
                    <div class="amount-rows"><strong>Taxable Cost:</strong>&nbsp;<div class="right">Rs.{{transferOrderDetail.totalAmount.toFixed(2)}}</div></div>
                    <div class="amount-rows"><strong>Total Taxes:</strong>&nbsp;<div class="right">Rs.{{transferOrderDetail.tax.toFixed(2)}}</div></div>
                    <div class="amount-rows"><strong>Total Amount:</strong>&nbsp;<div class="right">Rs.{{transferOrderDetail.totalAmount.toFixed(2)}}</div></div>
                </div>
            </div>
            <div class="row">
                <div class="col s12 form-element">
                    <label>Comment(optional):</label>
                    <textarea data-ng-model="transferOrderDetail.comment"></textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat">Cancel</button>
        <button class="modal-action modal-close waves-effect waves-green btn"
                data-ng-if="transferOrderDetail.comment!=null && transferOrderDetail.comment.trim().length>0"
                data-ng-click="createTransfer()">Submit</button>
    </div>
</div>
