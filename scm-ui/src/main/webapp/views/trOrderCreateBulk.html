<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .fixedElement {
        background-color: #c0c0c0;
        position:static;
        top:0;
        width:100%;
        z-index:100;
    }
</style>

<script>
    $(window).scroll(function(e){
        var $el = $('.fixedElement');
        var isPositionFixed = ($el.css('position') == 'fixed');
        if ($(this).scrollTop() > 200 && !isPositionFixed){
            $el.css({'position': 'fixed', 'top': '0px' ,  'width':'80%'});
        }
        if ($(this).scrollTop() < 200 && isPositionFixed){
            $el.css({'position': 'static', 'top': '0px',  'width':'100%'});
        }
    });
</script>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3 class="left">Create Bulk Transfer Order</h3>
                <input type="button" data-ng-click="backTransfer()" class="btn right" value="Back" data-ng-hide="showPendingRequest" style="margin-top: 35px;" />
            </div>
        </div>
        <div class="row" data-ng-hide="requestOrderList.length>0 && !showPendingRequest">
            <div class="col s7">
                <label for="inputCreated">Select fulfillment date</label>
                <input input-date type="text" name="created" id="inputCreated" ng-model="selectedDate"
                       data-ng-change="reset()"
                       container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                       max="{{maxDate}}" />
            </div>
            <div class="col s3 margin-top-10">
                <button class="btn btn-small" ng-click="resetValues()" style="margin-top: 14px;" acl-action="TRNCTV">Get Orders</button>
            </div>
            <div class=" col s2 right-align" style="padding:7px;"><span style="color: dodgerblue">* RO's of Fixed Asset is not displayed In Bulk Transfer </span>
            </div>
        </div>
        <hr>
    </div>


    <div class="row" data-ng-show="!showPreviewScreen">

        <!--<div class="col s12" data-ng-show="showPendingRequest">
            <button class="btn pull-left col s2" data-ng-click="selectAllROs(requestOrderList)" data-ng-if="!isEmpty(requestOrderList)">Select All</button>
            &lt;!&ndash;<div class="right-align" style="padding:7px; margin: 10px;"><span style="color: dodgerblue">* RO's of Fixed Asset is not displayed In Bulk Transfer </span>
            </div>&ndash;&gt;
            <div class="center-align col s8"  data-ng-hide="showMessage" style="line-height: 40px; font-weight: bold; ">RO Count : <span style="color:green; margin-right: 5px;">{{selectedROCount}}</span>     Units Count : <span style="color: green;">{{getSelectedUnitCount()}}</span></div>
            <button class="btn pull-right col s2"  data-ng-click="clubAllROs()" data-ng-if="!isEmpty(clubbedRoIds) && !isEmpty(requestOrderList) &&   showClubROBtn">Club & Create TO</button>
        </div>-->
        <div class="col s12" data-ng-show="showPendingRequest">
            <ul class="collection menuItemList" data-ng-hide="showMessage" >
                <div class="fixedElement">
                <div class="col s12" data-ng-show="showPendingRequest" style="background-color : whitesmoke ;">
                    <button class="btn pull-left col s2" data-ng-click="selectAllROs(requestOrderList)" data-ng-if="!isEmpty(requestOrderList)">Select All</button>
                    <!--<div class="right-align" style="padding:7px; margin: 10px;"><span style="color: dodgerblue">* RO's of Fixed Asset is not displayed In Bulk Transfer </span>
                    </div>-->
                    <div class="center-align col s8"  data-ng-hide="showMessage" style="line-height: 40px; font-weight: bold; ">RO Count : <span style="color:green; margin-right: 5px;">{{selectedROCount}}</span>     Units Count : <span style="color: green;">{{getSelectedUnitCount()}}</span></div>
                    <button class="btn pull-right col s2"  data-ng-click="clubAllROs()" data-ng-if="!isEmpty(clubbedRoIds) && !isEmpty(requestOrderList) &&   showClubROBtn">Club & Create TO</button>
                </div>
                <li class="collection-item z-depth-1 list-head" >
                    <div class="row">
                        <div class="col s2">Id</div>
                        <div class="col s2">RO Type</div>
                        <div class="col s2">Request Unit</div>
                        <div class="col s2">Generated By</div>
                        <div class="col s2">Update time</div>
                        <div class="col s2">Fulfillment Date</div>
                    </div>
                </li>
                </div>
                <li class="collection-item z-depth-1 clickable " data-ng-repeat="request in requestOrderList track by request.id"    >
                    <div class="row">
                        <div class="col s2">
                            <input id="RO-{{request.id}}"
                                   data-ng-hide="requestOrderList.length<2"
                                   data-ng-if="request.requestUnit.id!=request.fulfillmentUnit.id"
                                   data-ng-model="request.checked"
                                   data-ng-change="selectOpenRo(request,false)" type="checkbox"/>
                            <label for="RO-{{request.id}}">{{request.id}}</label>
                        </div>
                        <div class="col s2" >{{(request.requestOrderType ? "Regular Order" : "Ad-hoc Order")}}</div>
                        <div class="col s2"><span data-ng-show="request.isFirst && !allSelected">
                            <input data-ng-show="request.isFirst && !allSelected" data-ng-change="selectAllUnitRos(request.id,$index)" data-ng-model="request.firstChecked" id ="RO_FIRST-{{request.id}}"  type="checkbox"/>
                            <label for="RO_FIRST-{{request.id}}">{{request.requestUnit.name}} </label>
                        </span>
                            <span data-ng-hide="request.isFirst && !allSelected">
                                {{request.requestUnit.name}}
                            </span>
                        </div>
                        <div class="col s2">{{request.generatedBy.name}}</div>
                        <div class="col s2">{{request.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                        <div class="col s2">{{request.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                    </div>
                </li>
            </ul>
            <div data-ng-show="showMessage">No pending requests found.</div>
        </div>

        <div class="col s12" data-ng-hide="showPendingRequest" data-ng-class="initSKU(productSkuObjects)">
            <form name="trForm" novalidate> <div class="row">
                <div class="col s4"><label>Fulfillment Date:</label>{{fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                <div class="col s4"><label>RO Count:</label>{{selectedROCount}}</div>
                <div class="col s4"><label>Units Count:</label>{{getSelectedUnitCount()}}</div>
            </div>


                <ul  class="collection z-depth-1-half" style="margin-bottom: 50px;">
                    <li class="collection-item list-head">
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s4">Product Name</div>
                            <div class="col s4">Unit Of Measure</div>
                            <div class="col s4">Select SKU</div>

                        </div>
                    </li>

                    <li style="margin-bottom: 10px;border:#ddd 1px solid;border-left:0px;border-right:0px;"
                        data-ng-repeat="item in productSkuObjects | orderBy: 'productId' track by $index"
                        data-ng-show="item.skuList.length>1"
                        data-ng-class="{multiSkuColor:item.skuList.length>1}" >
                        <div class="row" data-ng-class="{redBg:!item.isDefaultSet}" style="padding: 10px;">
                        <!--<div  style="padding: 10px; background: #d8d8d8;border-bottom: #ddd 1px solid;">-->
                            <div data-ng-show="item.isDefaultSet">
                               <div class="col s4">{{productToSkuMapping[item.productId].linkedProduct.name}}</a></div>
                               <div class="col s4">{{productToSkuMapping[item.productId].unitOfMeasure}}</div>
                               <div class="col s4">
                                   <select data-ng-model="item.selectedSku"
                                        data-ng-change="onSkuChanged(item)"
                                        data-ng-options='sku as (sku.skuName + isDefault(sku.isDefault)) for sku in item.skuList | filter : bySKUs track by sku.skuId'>

                                   </select>
                               </div>
                            </div>
                            <div  data-ng-hide="item.isDefaultSet" >
                                <div class="col s4">{{item.name}}</a></div>
                                <div class="col s4">{{item.unitOfMeasure}}</div>
                                <div class="col s4">
                                    No Default Sku Set For This Product
                                </div>

                            </div>

                        </div>
                    </li>
                </ul>
                <div data-ng-hide="skuShow" class="collection z-depth-1-half" style="margin-bottom: 50px;">No item with multiple skus found.</div>
                <div class="row">
                    <div class="col s12 form-element">
                        <!--<input type="button" data-ng-click="showPendingRequest=true;" class="btn" value="Back" />-->
                        <input type="button" data-ng-disabled="submitted"
                               data-ng-if="trForm.$valid && isInvoiceRo" data-ng-click="createTransferOrderObject()" class="btn right" value="Submit Invoice" acl-action="TRNCTA" />
                        <input type="button" data-ng-disabled="submitted || isNotDefaultSkuFound"
                               data-ng-class="{redBg:isNotDefaultSkuFound}" data-ng-if="trForm.$valid && !isInvoiceRo" data-ng-click="showNextScreen()" class="btn right" value="Preview Screen" acl-action="TRNCTA" />
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div data-ng-show="showPreviewScreen">
        <div class="col s12">
            <div class="row">
                <div class="col s3"><label>Fulfillment Date:</label>{{fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                <div class="col s3"><label>RO Count:</label>{{selectedROCount}}</div>
                <div class="col s3"><label>Units Count:</label>{{getSelectedUnitCount()}}</div>
                <div class="col s3"><button class="btn right" data-ng-if="trForm.$valid && !isInvoiceRo" data-ng-click="sendClubbedROs()" type="submit" name="action"  acl-action="TRNCTA">Submit Bulk Transfers
                    <i class="material-icons right">send</i>
                </button>
                </div>
            </div>
            <ul class="collection menuItemList">
                <li class="collection-item z-depth-1 list-head fixedElement">
                    <div class="row">
                        <div class="col s2">PRODUCT NAME</div>
                        <div class="col s2">REQUESTED QUANTITY</div>
                        <div class="col s2">REQUESTED ABSOLUTE QUANTITY</div>
                        <div class="col s3">SKU SELECTED</div>
                        <div class="col s3">DRILLDOWN</div>
                    </div>
                </li>
                <li class="collection-item z-depth-1 clickable" data-ng-repeat="product in productSkuObjects track by product.productId">
                    <div class="row" data-ng-class="{multiSkuColor:product.skuList.length>1}">
                        <div class="col s2">{{product.selectedSku.linkedProduct.name}}({{product.selectedSku.unitOfMeasure}})</div>
                        <div class="col s2">{{getAggregateQuantity(product.productId)}}</div>
                        <div class="col s2">{{getAggregateAbsoluteQuantity(product.productId)}}</div>
                        <div class="col s3">{{product.selectedSku.skuName}}</div>
                        <div class="col s3">
                        <input type="button" data-ng-disabled="submitted"
                                   data-ng-if="trForm.$valid && !isInvoiceRo" data-ng-click="showDetailsTO(product.productId)" class="btn-small  right " value="Show preview for {{getUnitsCount(product.productId)}} units" acl-action="TRNCTA" />
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <Br/>
        <button class="btn right" data-ng-if="trForm.$valid && !isInvoiceRo" data-ng-click="sendClubbedROs()" type="submit" name="action"  acl-action="TRNCTA">Submit Bulk Transfers
            <i class="material-icons right">send</i>
        </button>
    </div>
</div>

<script type="text/ng-template" id="showPreviewToModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="row">
                <h5 style="text-align-last: center;">Product : {{productToSkuMapping[productId].linkedProduct.name}} transferred for {{unitIds.length}} Units</h5>
                <div class="row standardView" style="max-height: 500px; overflow: auto;">
                    <table class="bordered striped">
                        <thead>
                        <tr>
                            <th>Unit Id</th>
                           <th>Unit Name</th>
                            <th>Product Name</th>
                            <th>Requesting Quantity</th>
                            <th>Requesting Absolute Quantity</th>
                            <th>Transferred Quantity</th>
                            <th>Packaging Detail</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="unitId in unitIds">
                            <td>{{unitId}}</td>
                            <td>{{getUnitName(unitId)}}</td>
                            <td>{{requestOrderItemsList[unitId].productName}}</td>
                            <td style="word-break: break-all;">{{requestOrderItemsList[unitId].requestedQuantity}}</td>
                            <td style="word-break: break-all;">{{requestOrderItemsList[unitId].requestedAbsoluteQuantity}}</td>
                            <td>{{requestOrderItemsList[unitId].transferredQuantity}}</td>
                            <td data-ng-show="requestOrderItemsList[unitId].trPackaging.length>0" ><span data-ng-repeat="packaging in requestOrderItemsList[unitId].trPackaging[0].packagingDetails">{{packaging.packagingDefinitionData.packagingName}}({{packaging.numberOfUnitsPacked}}) ,</span></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="TableMobileView">
                    <ul class="collection striped center">
                        <li class="collection-item" data-ng-repeat="unitId in unitIds">
                            <div class="row">
                                <div class="col">Unit Id</div>
                                <div class="col">{{unitId}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Unit Name</div>
                                <div class="col">{{getUnitName(unitId)}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Product Name</div>
                                <div class="col">{{requestOrderItemsList[unitId].productName}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Requesting Quantity</div>
                                <div class="col">{{requestOrderItemsList[unitId].requestedQuantity}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Requesting Absolute Quantity</div>
                                <div class="col">{{requestOrderItemsList[unitId].requestedAbsoluteQuantity}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Transferred Quantity</div>
                                <div class="col">{{requestOrderItemsList[unitId].transferredQuantity}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Sku selected</div>
                                <div class="col">{{productToSkuMapping[productId].skuName}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Packaging Detail</div>
                                <div class="col" data-ng-show="requestOrderItemsList[unitId].trPackaging.length>0"><span data-ng-repeat="packaging in requestOrderItemsList[unitId].trPackaging[0].packagingDetails">{{packaging.packagingDefinitionData.packagingName}}({{packaging.numberOfUnitsPacked}}) ,</span></div>
                            </div>

                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</script>
