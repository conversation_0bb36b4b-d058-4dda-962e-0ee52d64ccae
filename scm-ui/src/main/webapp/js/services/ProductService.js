/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 24-05-2016.
 */
angular.module('scmApp').service('productService', ['$rootScope', '$http', 'appUtil', 'apiJson','metaDataService',
    function ($rootScope, $http, appUtil, apiJson,metaDataService) {

    var service = {};

    service.getAllProducts = function (callback) {
        var productList = appUtil.getScmProductDetails();
        if (appUtil.isEmptyObject(productList)) {
            metaDataService.getScmProductDetails().then(function (products) {
                callback(products);
            }, function (response) {
                console.log("Got error during fetching :::", response);
            }).finally(function () {
                callback(appUtil.getScmProductDetails());
            });
        } else {
            callback(productList);
        }
    };

    service.getProduct = function(id){
        var productList = appUtil.getScmProductDetails();
        for(var index in productList){
           var product = productList[index];
           if(product.productId == id){
             return product;
           }
        }
    };

    service.getSkuList = function (callback) {
        var skuList = appUtil.getSkuProductMap();
        if(appUtil.isEmptyObject(skuList)){
            metaDataService.getSkuProductMap().then(function(skuList){
                callback(skuList);
            },function(response){
                console.log("Got error during fetching :::",response);
            }).finally(function(){
                callback(appUtil.getSkuProductMap());
            });
        }else{
            callback(skuList);
        }
    };

    service.getAllCategoryValues = function(callback){
        var categoryAttrValues = appUtil.getCategoryAttributeValues();
        if(appUtil.isEmptyObject(categoryAttrValues)){
            metaDataService.getCategoryAttributeValues().then(function(categoryAttrValues){
                callback(categoryAttrValues);
            },function(response){
                console.log("Got error during fetching :::",response);
            }).finally(function(){
                callback(appUtil.getCategoryAttributeValues());
            });
        }else{
            callback(categoryAttrValues);
        }
    };

    service.generateSKU = function(product,attributes,callback){
        var categoryWiseMappings = appUtil.getMetadata().categoryAttributeMappings;
        var name = "";
        if(appUtil.isEmptyObject(categoryWiseMappings)){
            metaDataService.getCategoryAttributeMappings(function(mappings){
                name = getSkuName(product,attributes,mappings);
                callback(name);
            });
        }else{
            name = getSkuName(product,attributes,categoryWiseMappings);
            callback(name);
        }
    };

    function getSkuName(product,attributes,categoryWiseMappings){
        var skuName = product.productName;
        var nameArray = {};

        categoryWiseMappings = categoryWiseMappings[product.categoryDefinitionId].filter(function(mapping){
            return mapping.usedInNaming && mapping.mappingStatus == "ACTIVE";
        });
        categoryWiseMappings = categoryWiseMappings.sort(function(mapping1,mapping2){
            return mapping2.mappingOrder - mapping1.mappingOrder;
        });

        categoryWiseMappings.forEach(function(mapping){
            nameArray = updateAttributes(mapping.categoryAttributeMappingId,attributes,mapping.mappingOrder,nameArray,mapping.attributeDefinitionId);
        });

        var mappingKeys = Object.keys(nameArray).sort();
        for(var i = 0 ; i < mappingKeys.length ;i++){
            skuName += " " + nameArray[mappingKeys[i]];
        }
        console.log("ProductService :::",skuName);
        return skuName;
    }


    function updateAttributes(categoryAttributeMappingId,attributes,order,orderedList,attributeId) {
        for(var index = 0 ; index < attributes.length; index++){
            try{
                var attribute = JSON.parse(attributes[index]);
                if(attribute.categoryAttributeMappingId == categoryAttributeMappingId){
                    attribute.attributeId = attributeId;
                    var concatText = attribute.attributeValue.name + " ";
                    orderedList[order] = orderedList[order] == undefined ? concatText : orderedList[order] + concatText;
                    attributes[index] = JSON.stringify(attribute);
                }
            }catch(e){
                console.log("Encountered error:::",e);
            }
        }
        return orderedList;
    }

    return service;
}]);

