/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by Rahul on 07-12-2017.
 */
angular.module('scmApp').service('TokenUtil', [function () {

    var service = this;

    service.pageApiTokenMapping = {
        "menu.trOrderCreate": ["scm-service.transfer-order-management.transfer-order"]
    };

    service.apiTokenMap = {};

    service.getPageApiTokenMapping = getPageApiTokenMapping;
    service.getApiToken = getApiToken;
    service.setTokenToApi = setTokenToApi;

    function getPageApiTokenMapping(page) {
        return service.pageApiTokenMapping[page];
    };


    function setTokenToApi(api, token) {
        service.apiTokenMap[api] = token;
    };

    function getApiToken(api) {
        return service.apiTokenMap[api];
    };

    return service;
}]);