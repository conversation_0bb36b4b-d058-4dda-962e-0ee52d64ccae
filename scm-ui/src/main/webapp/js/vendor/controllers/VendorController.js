scmApp.controller('vendorCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
                  $alertService, $location, authService, $state) {

            function getVendorDetail(vendorId) {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendor + "/" + vendorId
                }).then(function (response) {
                    $scope.basicDetail = response.data;
                });
            }

            $scope.checkForAlreadySubmitted = function () {
                var search = $location.search();
                if(appUtil.isEmptyObject($scope.basicDetail.vendorId)){
                    if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.vendorManagement.validateRequest,
                            data : {token : search.id}
                        }).then(function (response) {
                            console.log("response : ", response.data);
                            if(appUtil.isEmptyObject(response.data.vendorId)){
                                $scope.saveBasicDetails();
                            }else{
                                $alertService.alert("Already Submitted !!","Details Are Already Filled In Using This Link . Please Refresh The Page" ,function () {}, false);
                            }
                        });
                    }
                }else{
                    $scope.saveBasicDetails();
                }


            }

            function validateRequest(id) {
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.validateRequest,
                    data: {token: id}
                }).then(function (response) {
                    var result = response.data;
                    $scope.registrationRequest = result;
                    if (result.vendorId != null && result.vendorId > 0 && (result.requestStatus == "INITIATED" || result.requestStatus == "IN_PROCESS")) {
                        getVendorDetail(result.vendorId);
                    }
                }, function (response) {
                    console.log("Not a valid request");
                    $state.go("vendorError");
                });
            }

            function validateBasicDetails() {
                var flag = validateForm($scope.basicDetailForm.$name);
                return flag;
            }


            $scope.duplicateNameCheck = function (callback) {

                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.checkDuplicateVendorName,
                    params: {
                        vendorName : $scope.basicDetail.entityName,
                        vendorType: $scope.registrationRequest.vendorType,
                        city :  $scope.basicDetail.vendorAddress.city,
                        state : $scope.basicDetail.vendorAddress.state
                    }
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Duplicate Vendor Name. Please enter unique Vendor Name");
                        return;
                    } else {
                       callback();
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };


            function checkDisclaimer() {
                if ($scope.basicDetail.disclaimerAccepted == true) {
                    return true;
                } else {
                    $toastService.create("Please accept the legal disclaimer to proceed.");
                }
                return false;
            }

            function getAuth() {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getAuth
                }).then(function (response) {
                    authService.setAuthorization(response.data);
                    appUtil.getCities(function (cities) {
                        $scope.cities = cities;
                    });
                    var search = $location.search();
                    if (!appUtil.isEmptyObject(search) && !appUtil.isEmptyObject(search.id)) {
                        validateRequest(encodeURIComponent(search.id));
                    }
                });
            }

            $scope.init = function () {
                $scope.basicDetail = {};
                getAuth();
                $scope.editMode = false;
            };


            $scope.changeVendorLocation = function (location) {
                if (!appUtil.isEmptyObject(location)) {
                    location = JSON.parse(location);
                    var address = $scope.basicDetail["vendorAddress"];
                    if (appUtil.isEmptyObject(address)) {
                        address = {};
                    }
                    address["city"] = location.name;
                    address["state"] = location.state.name;
                    address["locationId"] = location.code;
                    address["stateCode"] = location.state.code;
                    address["country"] = location.country.name; //TODO fix this wrt to countries.js

                    $scope.basicDetail["vendorAddress"] = address;
                }
            };

            $scope.saveBasicDetails = function () {

                if (validateBasicDetails()) {
                    if (checkDisclaimer()) {
                        $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                            if (result) {
                               $scope.duplicateNameCheck(saveDetails);
                            }
                        });
                    }
                } else {
                    $toastService.create("Please enter all details properly before submitting");
                }

            };

            function saveDetails(){
                $scope.basicDetail.registrationId = $scope.registrationRequest.id;
                $scope.basicDetail.type = $scope.registrationRequest.vendorType;
                $scope.basicDetail.vendorAddress.addressType = "VENDOR_ADDRESS";
                $scope.basicDetail.vendorAddress.addressContact = $scope.basicDetail.primaryContact;
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.saveBasicDetails,
                    data: $scope.basicDetail
                }).then(function (response) {
                    if (response.data.vendorId != null) {
                        $state.go("vendorCompany", {basicDetail: response.data})
                    }
                }, function (response) {
                    $toastService.create("Couldn't save details. Please try again!");
                    console.log("Got error while loading attribute values", response);
                });
            }

            $scope.goBack = function () {

            };
        }
    ]
).controller('vendorCompanyCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state', '$stateParams', '$fileUploadService',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
                  $alertService, $location, authService, $state, $stateParams, $fileUploadService) {

            $scope.init = function () {
                $scope.basicDetail = $stateParams.basicDetail;
                if (appUtil.isEmptyObject($scope.basicDetail)) {
                    $alertService.alert("Link Broken!", "Please click on the link sent in your mail to continue registration process");
                }
                $scope.companyDetail = !appUtil.isEmptyObject($scope.basicDetail.companyDetails) ? $scope.basicDetail.companyDetails : {};
                if (appUtil.isEmptyObject($scope.companyDetail)) {
                    $scope.companyDetail['exemptSupplier'] = "false";
                } else {
                    $scope.companyDetail['exemptSupplier'] = $scope.companyDetail['exemptSupplier'].toString();
                }

                appUtil.getCities(function (cities) {
                    $scope.cities = cities;
                });
                $scope.editMode = false;
                $scope.companyTypes = ['PRIVATE_LTD', 'PUBLIC_LTD', 'INDIVIDUAL', 'HUF', 'PROPRIETARY', 'LLP', 'PARTNERSHIP', 'AJP'];
                $scope.businessTypes = ["GOODS", "SERVICES", "GOODS_AND_SERVICES"];
                $scope.docUploadType = "COMPANY";
                $scope.changeCompanyType($scope.companyTypes[0]);
                $scope.changeBusinessType($scope.businessTypes[0]);
            };


            $scope.isExemptSupplier = function (value) {
                $scope.companyDetail.exemptSupplier = value;
            };

            $scope.isMsmeRegistered = function (value) {
                $scope.companyDetail.msmeRegistered = value;
                if (!value == 'true') {
                    $scope.companyDetail.msmeDocument = null;
                }
            };

            $scope.changeBusinessType = function (type) {
                $scope.companyDetail.businessType = type;
                if (type == "GOODS") {
                    $scope.getST = false;
                    $scope.getVat = true;
                } else {
                    $scope.getST = true;
                    $scope.getVat = false;
                }
            };

            $scope.changeCompanyType = function (type) {
                $scope.companyDetail.entityType = type;
                switch (type) {
                    case 'PUBLIC_LTD':
                    case 'PRIVATE_LTD':
                        $scope.getCin = true;
                        $scope.getArc = false;
                        break;
                    case 'LLP':
                        $scope.getArc = true;
                        $scope.getCin = false;
                        break;
                    default :
                        $scope.getArc = false;
                        $scope.getCin = false;
                        break;
                }
            };


            $scope.uploadMSME = function () {
                $fileUploadService.openFileModal("Upload MSME scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "MSME", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.msmeDocument = doc;
                    });
                });
            };

            $scope.uploadCIN = function () {
                $fileUploadService.openFileModal("Upload CIN scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "CIN", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.cinDocument = doc;
                    });
                });
            };

            $scope.uploadPAN = function () {
                $fileUploadService.openFileModal("Upload PAN scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "PAN", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.panDocument = doc;
                    });
                });
            };

            $scope.uploadARC = function () {
                $fileUploadService.openFileModal("Upload ARC scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "ARC", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.arcDocument = doc;
                    });
                });
            };

            $scope.uploadST = function () {
                $fileUploadService.openFileModal("Upload Service Tax Certificate Copy", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "SERVICE_TAX", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.serviceTaxDocument = doc;
                    });
                });
            };

            $scope.uploadVAT = function () {
                $fileUploadService.openFileModal("Upload VAT Certificate Copy", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "VAT", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.vatDocument = doc;
                    });
                });
            };

            $scope.uploadCST = function () {
                $fileUploadService.openFileModal("Upload CST Certificate copy", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "CST", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.companyDetail.cstDocument = doc;
                    });
                });
            };

            function validateCompany(companyForm) {
                return validateForm(companyForm.$name);
            }

            $scope.saveCompanyDetails = function () {
                if ($scope.basicDetail.companyDetails != null) {
                    $scope.saveCompanyDetail();
                } else {
                    checkIfAlreadyFilled();
                }
            };

            function checkIfAlreadyFilled(){
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendor + "/" + $scope.basicDetail.vendorId
                }).then(function (response) {
                    if(!appUtil.isEmptyObject(response.data.companyDetails)){
                        $alertService.alert("Already Submitted !!","Details Are Already Filled In Using This Link . " +
                            "Please click on the link sent in your mail to continue registration process" ,function () {}, false);
                    }else{
                        $scope.duplicatePanCheck();
                    }
                });
            }



            $scope.duplicatePanCheck = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.checkDuplicatePanNumber,
                    params: {
                        vendorType: $scope.basicDetail.type,
                        vendorPan: $scope.companyDetail.pan
                    }
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Duplicate PAN Number. Please enter unique pan number.");
                        return;
                    } else {
                        $scope.saveCompanyDetail();
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };


            $scope.saveCompanyDetail = function () {
                if ($scope.companyDetail.panDocument == null) {
                    $toastService.create("You need to upload PAN document before submitting Company Details");
                    return;
                }

                if ($scope.companyDetail.msmeRegistered == 'true' && $scope.companyDetail.msmeDocument == null) {
                    $toastService.create("You need to upload MSME document before submitting Company Details");
                    return;
                }

                if (!$scope.companyDetail.exemptSupplier) {
                    if ($scope.getCin && $scope.companyDetail.cinDocument == null) {
                        $toastService.create("You need to upload CIN document before submitting Company Details");
                        return;
                    }

                    if ($scope.getArc && $scope.companyDetail.arcDocument == null) {
                        $toastService.create("You need to upload ARC document before submitting Company Details");
                        return;
                    }

                    if ($scope.companyDetail.cstDocument == null) {
                        $toastService.create("You need to upload CST document before submitting Company Details");
                        return;
                    }

                    if ($scope.getVat && $scope.companyDetail.vatDocument == null) {
                        $toastService.create("You need to upload VAT document before submitting Company Details");
                        return;
                    }

                    if ($scope.getST && $scope.companyDetail.serviceTaxDocument == null) {
                        $toastService.create("You need to upload Service Tax document before submitting Company Details");
                        return;
                    }
                }

                if (validateCompany($scope.companyDetailForm)) {
                    $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                        if (result) {
                            $scope.companyDetail.vendorDetail = appUtil.createVendor($scope.basicDetail);
                            $scope.companyDetail.companyAddress.addressType = "REGISTERED_ADDRESS";
                            $scope.companyDetail.companyAddress.addressContact = $scope.basicDetail.primaryContact;
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.saveCompanyDetails,
                                data: $scope.companyDetail
                            }).then(function (response) {
                                if (response.data.vendorId != null) {
                                    $scope.basicDetail.companyDetails = response.data;
                                    if ($scope.basicDetail.type == 'CUSTOMER') {
                                        $state.go("vendorLocation", {basicDetail: $scope.basicDetail})
                                    } else {
                                        $state.go("vendorAccount", {basicDetail: $scope.basicDetail})
                                    }

                                }
                            }, function (response) {
                                $toastService.create("Couldn't save details. Please try again!");
                                console.log("Got error while loading attribute values", response);
                            });
                        }
                    });
                } else {
                    $toastService.create("Please fill in all the details correctly");
                }
            };

            $scope.changeCompanyLocation = function (location) {
                if (!appUtil.isEmptyObject(location)) {
                    location = JSON.parse(location);
                    var address = $scope.companyDetail["companyAddress"];
                    if (appUtil.isEmptyObject(address)) {
                        address = {};
                    }
                    address["city"] = location.name;
                    address["state"] = location.state.name;
                    address["country"] = location.country.name;
                    address["locationId"] = location.code;
                    address["stateCode"] = location.state.code;
                    $scope.companyDetail["companyAddress"] = address;
                }
            };


            $scope.goBack = function () {
                $state.go("registerVendor");
            };
        }
    ]
).controller('vendorAccountCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state', '$stateParams', '$fileUploadService',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
                  $alertService, $location, authService, $state, $stateParams, $fileUploadService) {

            $scope.init = function () {
                $scope.basicDetail = $stateParams.basicDetail;

                if (appUtil.isEmptyObject($scope.basicDetail)) {
                    $alertService.alert("Link Broken!", "Please click on the link sent in your mail to continue registration process");
                }

                $scope.accountDetail = !appUtil.isEmptyObject($scope.basicDetail.accountDetails) ? $scope.basicDetail.accountDetails : {};
                appUtil.getCities(function (cities) {
                    $scope.cities = cities;
                });

                $scope.editMode = false;
                $scope.accountTypes = ["SAVINGS", "CURRENT"];
                $scope.docUploadType = "ACCOUNT";
                $scope.changeAccountType($scope.accountTypes[0]);
            };

            $scope.changeAccountType = function (type) {
                $scope.accountDetail.kindOfAccount = type;
            };

            $scope.checkIfAlreadyFilled = function (){
                if(appUtil.isEmptyObject($scope.basicDetail.accountDetails)){
                    $http({
                        method: "GET",
                        url: apiJson.urls.vendorManagement.getVendor + "/" + $scope.basicDetail.vendorId
                    }).then(function (response) {
                        if(!appUtil.isEmptyObject(response.data.accountDetails)){
                            $alertService.alert("Already Submitted !!","Details Are Already Filled In Using This Link . " +
                                "Please click on the link sent in your mail to continue registration process" ,function () {}, false);
                        }else{
                            $scope.saveAccountDetails();
                        }
                    });
                }else{
                    $scope.saveAccountDetails();
                }

            }

            $scope.uploadCheque = function () {
                $fileUploadService.openFileModal("Upload Cancelled Cheque scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "CHEQUE", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.accountDetail.cancelledCheque = doc;
                    });
                });
            };

            function validateAccountDetails() {
                return validateForm($scope.accountDetailForm.$name);
            }

            $scope.saveAccountDetails = function () {

                if ($scope.accountDetail.cancelledCheque == null) {
                    $toastService.create("You need to upload Cancelled Cheque Scanned Document before submitting Account Details");
                    return;
                }

                if (validateAccountDetails()) {
                    $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                        if (result) {
                            $scope.accountDetail.vendorDetail = appUtil.createVendor($scope.basicDetail);
                            $scope.accountDetail.paymentCycle = "MONTHLY";
                            $scope.accountDetail.accountType = "PRIMARY";
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.saveAccountDetails,
                                data: $scope.accountDetail
                            }).then(function (response) {
                                if (response.data.vendorId != null) {
                                    $scope.basicDetail.accountDetails = response.data;
                                    $state.go("vendorLocation", {basicDetail: $scope.basicDetail})
                                }
                            }, function (response) {
                                $toastService.create("Couldn't save details. Please try again!");
                                console.log("Got error while loading attribute values", response);
                            });
                        }
                    });
                } else {
                    $toastService.create("Please fill in all the details correctly");
                }
            };

            $scope.goBack = function () {
                $state.go("vendorCompany");
            };
        }
    ]
).controller('vendorLocationCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state', '$stateParams', '$fileUploadService',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
                  $alertService, $location, authService, $state, $stateParams, $fileUploadService) {

            $scope.init = function () {
                $scope.basicDetail = $stateParams.basicDetail;
                $scope.location = {};
                $scope.addressTypes = ["DELIVERY","DELIVERY_BILLING","BILLING"];
                $scope.selectedAddressType = "DELIVERY_BILLING";
                if (appUtil.isEmptyObject($scope.basicDetail)) {
                    $alertService.alert("Link Broken!", "Please click on the link sent in your mail to continue registration process");
                }


                $scope.dispatchLocations = !appUtil.isEmptyObject($scope.basicDetail.dispatchLocations)
                    ? $scope.basicDetail.dispatchLocations : [];

                appUtil.getCities(function (cities) {
                    $scope.cities = cities;
                });

                $scope.editMode = false;
                $scope.gstStatusTypes = ["REGISTERED", "UNREGISTERED", "APPLIED_FOR"];
                $scope.notificationTypes = ["SMS", "EMAIL"];
                $scope.docUploadType = "GSTIN";

            };

            $scope.checkIfAlreadyFilled = function (){
                if(appUtil.isEmptyObject($scope.basicDetail.dispatchLocations)){
                    $http({
                        method: "GET",
                        url: apiJson.urls.vendorManagement.getVendor + "/" + $scope.basicDetail.vendorId
                    }).then(function (response) {
                        if(!appUtil.isEmptyObject(response.data.dispatchLocations)){
                            $alertService.alert("Already Submitted !!","Details Are Already Filled In Using This Link . " +
                                "Please click on the link sent in your mail to continue registration process" ,function () {}, false);
                        }else{
                            $scope.saveLocations();
                        }
                    });
                }else{
                    $scope.saveLocations();
                }

            }

            $scope.changeAddressType =  function (type){
                $scope.selectedAddressType = type;
            }


            $scope.uploadGSTIN = function () {
                $fileUploadService.openFileModal("Upload GSTIN scan copy or pdf", "Find", function (file) {
                    appUtil.uploadFile($scope.docUploadType, "GSTIN", $scope.basicDetail.vendorId, file, function (doc) {
                        $scope.location.gstinDocument = doc;
                    });
                });
            };

            function validateLocation() {

                if ($scope.location.gstStatus == "REGISTERED") {
                    if ($scope.location.gstinDocument == null) {
                        $toastService.create("Please upload GSTIN document");
                        return false;
                    }
                    if ($scope.location.gstin == null) {
                        $toastService.create("Please provide GSTIN number");
                        return false;
                    }
                }

                if (!appUtil.isEmptyObject($scope.location.notificationType) && $scope.location.notificationType.length > 0) {
                    return validateForm($scope.locationDetailForm.$name);
                } else {
                    $toastService.create("No notification type selected.Please select properly!");
                    return false;
                }
            }

            function containsLocation(find, locations) {
                locations.filter(function (location) {
                    return location.locationName == find.locationName;
                }).length > 0;
            }

            $scope.addToLocations = function (location) {
                if (validateLocation()) {
                    location.address.addressType = "DISPATCH_ADDRESS";
                    if (!containsLocation(location, $scope.dispatchLocations)) {
                        location.locationType= $scope.selectedAddressType;
                        $scope.dispatchLocations.push(angular.copy(location));
                        $scope.location = {};
                    } else {
                        $toastService.create("Location already added");
                    }
                } else {
                    $toastService.create("Please make sure you have entered correct location details");
                }
            };

            $scope.changeTaxStatus = function (status) {
                $scope.location.applyTax = status;
            };

            $scope.edit = function (index) {
                if (index != null && index != -1) {
                    $scope.location = angular.copy($scope.dispatchLocations[index]);
                    $scope.dispatchLocations.splice(index, 1);
                    $scope.dispatchLocations.push($scope.location);
                   // $scope.removeLocation(index);
                }
            };

            $scope.removeLocation = function (index) {
                if ($scope.dispatchLocations[index].dispatchId != null) {
                    if ($scope.removedLocations == undefined || $scope.removedLocations == null) {
                        $scope.removedLocations = [];
                    }
                    $scope.removedLocations.push($scope.dispatchLocations[index].dispatchId);
                }
                $scope.dispatchLocations.splice(index, 1);
            };

            $scope.changeGstStatus = function (status) {
                if (appUtil.isEmptyObject(status) || status != "REGISTERED") {
                    delete $scope.location.gstin;
                }
            };

            $scope.saveLocations = function () {
                if ($scope.dispatchLocations.length != 0 && $scope.basicDetail != null && $scope.basicDetail.vendorId != null) {
                    $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                        if (result) {
                            var reqObj = {
                                vendorId: $scope.basicDetail.vendorId,
                                locations: $scope.dispatchLocations
                            };
                            if ($scope.removedLocations != undefined) {
                                reqObj.removedLocations = $scope.removedLocations;
                            }
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.saveLocations,
                                data: reqObj
                            }).then(function (response) {
                                if (response.data.vendorId != null) {
                                    $scope.basicDetail.accountDetails = response.data;
                                    $state.go("vendorSuccess");
                                } else {
                                    $toastService.create("Got error while saving locations. Please try again!");
                                }
                            }, function (response) {
                                $toastService.create("Couldn't save details. Please try again!");
                                console.log("Got error while saving location details", response);
                            });
                        }
                    });
                } else {
                    $toastService.create("Please make sure you have more than one locations entered to save.");
                }
            };

            $scope.changeDispatchLocation = function (location) {
                if (!appUtil.isEmptyObject(location)) {
                    location = JSON.parse(location);
                    var address = $scope.location["address"];
                    if (appUtil.isEmptyObject(address)) {
                        address = {};
                    }
                    address["city"] = location.name;
                    address["state"] = location.state.name;
                    address["country"] = location.country.name;
                    address["locationId"] = location.code;
                    address["stateCode"] = location.state.code;
                    $scope.location["address"] = address;
                }
            };

            $scope.addToNotificationTypes = function (type, checked) {
                if ($scope.location == undefined || $scope.location == null) {
                    $scope.location = {};
                }
                if (appUtil.isEmptyObject($scope.location.notificationType)) {
                    $scope.location.notificationType = [];
                }

                var index = $scope.location.notificationType.indexOf(type);
                if (checked && index == -1) { //if checked and not found
                    $scope.location.notificationType.push(type);
                }
                if (!checked && index != -1) { //if not checked and found
                    $scope.location.notificationType.splice(index, 1);
                }
            };

            $scope.goBack = function () {
                $state.go("vendorAccount");
            };
        }
    ]
).controller('vendorCompletionCtrl', ['$rootScope', '$scope', '$toastService', '$location',
        function ($rootScope, $scope, $toastService, $location) {
            $scope.init = function () {
                $scope.showSuccess = $location.url() == "/vendorSuccess";
                $scope.showError = $location.url() == "/vendorError";
            };
        }
    ]
);
