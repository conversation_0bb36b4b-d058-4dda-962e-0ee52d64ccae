angular.module('vendorApp').service('authService', ['$cookieStore', function ($cookieStore) {
    var service = this;
    service.authorization = null;
    service.getAuthorization = getAuthorization;
    service.setAuthorization = setAuthorization;

    function getAuthorization() {
        if (this.authorization == null || angular.isUndefined(this.authorization)) {
            service.authorization = $cookieStore.get("registrationToken");
        }
        return service.authorization;
    };

    function setAuthorization(response) {
        $cookieStore.put('registrationToken', response);
        this.authorization = response;
    };

    return service;
}]);