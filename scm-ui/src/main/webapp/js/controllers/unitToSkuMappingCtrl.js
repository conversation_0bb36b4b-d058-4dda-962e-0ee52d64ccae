scmApp.controller(
    'unitToSkuMappingCtrl',
    [
        '$rootScope',
        '$scope',
        '$interval',
        'apiJson',
        '$http',
        'appUtil',
        '$toastService',
        'uiGridConstants',
        '$alertService',
        '$timeout',
        'metaDataService',
        'previewModalService',
        '$state',
        'packagingService',
        function ($rootScope, $scope, $interval, apiJson, $http, appUtil, $toastService, $uiGridConstants,
                  $alertService, $timeout, metaDataService,previewModalService, $state , packagingService) {

            $scope.init = function () {
                $scope.skuProductMap =  createproductSkuMap();
                $scope.mappingList = [{
                    id: 1,
                    name: 'Unit to Sku Mapping'
                }, {
                    id: 2,
                    name: 'Sku to Unit Mapping'
                }];
                $scope.initializeGridOptions();
                $scope.allSkuList = [];
                $scope.allUnitsList = [];
                $scope.skuMappings = [];
                $scope.reset();
                $scope.getAllUnitList();
                $scope.getAllSkus();
                $scope.getRecipeProfiles();
                $scope.showPreview = previewModalService.showPreview;
                getAllMappings();
                getAllPackagingProfiles();
                $scope.productInventoryList = {};
                $scope.unitInventoryListMap = {};
            };

            function createproductSkuMap(){
                var skuProductMap = {};
                var productMap = appUtil.getSkuProductMap();
                Object.keys(productMap).forEach(function (productId){
                    productMap[productId].forEach(function (sku){
                        skuProductMap[sku.skuId] = productId;
                    })
                });
                return skuProductMap;
            }

            function getAllPackagingProfiles() {
                packagingService.getAllProfiles(function (profiles) {
                    $scope.definitions.lists = profiles;
                });
            }

            function getAllMappings(callback) {
                $http({
                    method: "GET",
                    url: apiJson.urls.productManagement.skuPackagingMappings
                }).then(function (response) {
                    $scope.skuMappings = response.data;
                    if (callback != undefined) {
                        callback();
                    }
                }, function (response) {
                    console.log("Error while getting mappings", response);
                });
            }

            $scope.reset = function () {
                $scope.typesofClone = false;
                $scope.gridDataId = null;
                $scope.updateMappingRequested = false;
                $timeout(function () {
                    $('#mappingDisplayData').val('').trigger('change');
                    $('#cloneDisplayData').val('').trigger('change');
                });
                $scope.gridOptions.data = null;
            };


            $scope.initializeGridOptions = function () {
                $scope.inventoryListMapping = [
                    {code: 'NO LIST', inventoryList: 'NO LIST'},
                    {code: 'MONDAY 1', inventoryList: 'MONDAY 1'},
                    {code: 'TUESDAY 1', inventoryList: 'TUESDAY 1'},
                    {code: 'WEDNESDAY 1', inventoryList: 'WEDNESDAY 1'},
                    {code: 'THURSDAY 1', inventoryList: 'THURSDAY 1'},
                    {code: 'FRIDAY 1', inventoryList: 'FRIDAY 1'},
                    {code: 'SATURDAY 1', inventoryList: 'SATURDAY 1'},
                    {code: 'SUNDAY 1', inventoryList: 'SUNDAY 1'},
                    {code: 'MONDAY 2', inventoryList: 'MONDAY 2'},
                    {code: 'TUESDAY 2', inventoryList: 'TUESDAY 2'},
                    {code: 'WEDNESDAY 2', inventoryList: 'WEDNESDAY 2'},
                    {code: 'THURSDAY 2', inventoryList: 'THURSDAY 2'},
                    {code: 'FRIDAY 2', inventoryList: 'FRIDAY 2'},
                    {code: 'SATURDAY 2', inventoryList: 'SATURDAY 2'},
                    {code: 'SUNDAY 2', inventoryList: 'SUNDAY 2'},
                    {code: 'ALL DAYS', inventoryList: 'ALL DAYS'}

                ];

                $scope.productionUnitMapping = [
                    {code: 'Chaayos', productionUnit:'Chaayos'},
                    {code: 'GnT', productionUnit:'GnT'}
                ];

                $scope.gridOptions = {
                    enableColumnMenus: false,
                    saveFocus: false,
                    enableRowSelection: true,
                    enableFiltering: true,
                    saveScroll: true,
                    enableSelectAll: true,
                    multiSelect: true,
                    enableColumnResizing : true,
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.edit.on.afterCellEdit($scope, function(rowEntity, colDef, newValue, oldValue) {
                            if(colDef.displayName == "InventoryList" || colDef.displayName == "Inventory List"){
                                $alertService.confirm("Are you sure? , All Skus Inventory List Id Associated With This Product For This Unit will Also Be Changed","",function(result){
                                    if(result) {
                                        if($scope.mappingTypeData == "unitToSku"){
                                            changeAllSkusInventoryList($scope.skuProductMap[rowEntity.id],newValue );
                                        }else{
                                            rowEntity.inventoryList = newValue;
                                        }
                                    }else{
                                        rowEntity.inventoryList = oldValue;
                                    }
                                    $scope.$apply();
                                });

                            }
                        });


                        gridApi.selection.on.rowSelectionChanged($scope, function (row) {
                            if (row.isSelected) {
                                row.entity.selectData = true;
                                row.entity.mappingStatus = 'ACTIVE';
                                row.entity.check = true;
                                var productId = $scope.skuProductMap[row.entity.id];
                                if($scope.mappingTypeData == "unitToSku"  && !appUtil.isEmptyObject($scope.productInventoryList[productId])){
                                    row.entity.inventoryList = $scope.productInventoryList[productId];
                                }else if ($scope.mappingTypeData == "skuToUnit"  && !appUtil.isEmptyObject($scope.unitInventoryListMap[row.entity.id])){
                                    row.entity.inventoryList = $scope.inventoryListMapping[$scope.unitInventoryListMap[row.entity.id] - 1 ].inventoryList;
                                }else{
                                    if($scope.mappingTypeData == "unitToSku"){
                                        $scope.productInventoryList[productId]  = row.entity.inventoryList;
                                    }
                                }
                            } else {
                                row.entity.selectData = false;
                                row.entity.check = false;
                                row.entity.mappingStatus = 'IN_ACTIVE'
                            }
                        });
                        gridApi.selection.on.rowSelectionChangedBatch($scope, function (rows) {
                            rows.forEach(function (row) {
                                if (row.isSelected) {
                                    row.entity.selectData = true;
                                    row.entity.mappingStatus = 'ACTIVE';
                                    row.entity.check = false;
                                    var productId = $scope.skuProductMap[row.entity.id];
                                    if($scope.mappingTypeData == "unitToSku"  && !appUtil.isEmptyObject($scope.productInventoryList[productId])){
                                        row.entity.inventoryList = $scope.productInventoryList[productId];
                                    }else if ($scope.mappingTypeData == "skuToUnit"  && !appUtil.isEmptyObject($scope.unitInventoryListMap[row.entity.id])){
                                        row.entity.inventoryList = $scope.inventoryListMapping[$scope.unitInventoryListMap[row.entity.id] - 1 ].inventoryList;
                                    }
                                } else {
                                    row.entity.selectData = false;
                                    row.entity.check = false;
                                    row.entity.mappingStatus = 'IN_ACTIVE'
                                }
                            });
                        });
                    }
                };
                $scope.gridOptions.multiSelect = true;
            };

            function changeAllSkusInventoryList (productId,inventoryList){
                        $scope.gridOptions.data.forEach(function (row, index) {
                            if ($scope.skuProductMap[row.id] == productId) {
                                $scope.gridOptions.data[index].inventoryList = inventoryList;
                                $scope.productInventoryList[productId] = inventoryList;
                            }
                        });
                        $scope.$apply();
            }

            $scope.unitToSkuGridColumns = function () {
                return [{
                    field: 'id',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'sku id',
                    cellTemplate: 'skuIdTemplate.html'
                }, {
                    field: 'name',
                    name: 'name',
                    enableCellEdit: false,
                    displayName: 'sku name'
                }, {
                    field: 'category',
                    name: 'category',
                    enableCellEdit: false,
                    displayName: 'Category'
                }, {
                    field: 'subCategory',
                    name: 'subCategory',
                    enableCellEdit: false,
                    displayName: 'Subcategory'
                }, {
                    field: 'status',
                    name: 'status',
                    enableCellEdit: false,
                    displayName: 'sku status'
                }, {
                    field: 'mappingStatus',
                    name: 'mappingStatus',
                    enableCellEdit: false,
                    displayName: 'mapping status'
                }, {
                    field: 'profile',
                    name: 'profile',
                    enableCellEdit: false,
                    displayName: 'profile'
                }, {
                    field: 'inventoryList',
                    name: 'inventoryList',
                    enableCellEdit: true,
                    editableCellTemplate: 'ui-grid/dropdownEditor',
                    editDropdownOptionsArray: $scope.inventoryListMapping,
                    editDropdownIdLabel: 'code',
                    editDropdownValueLabel: 'inventoryList',
                    displayName: 'InventoryList'
                },{
                    field: 'productionUnit',
                    name: 'productionUnit',
                    enableCellEdit: true,
                    editableCellTemplate: 'ui-grid/dropdownEditor',
                    editDropdownOptionsArray: $scope.productionUnitMapping,
                    editDropdownIdLabel: 'code',
                    editDropdownValueLabel: 'productionUnit',
                    displayName: 'Production Unit'
                },
                {
                    field: 'skuPackaging',
                    name: 'skuPackaging',
                    enableCellEdit: true,
                    editableCellTemplate: 'ui-grid/dropdownEditor',
                    editDropdownValueLabel: 'value',
                    editDropdownRowEntityOptionsArrayPath: 'skuPackagingList',
                    editDropdownIdLabel: 'value',
                    displayName: 'Sku Packaging'
                }

                ];
            };

            $scope.skuToUnitGridColumns = function () {
                return [{
                    field: 'id',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'Unit Id'
                }, {
                    field: 'name',
                    name: 'name',
                    enableCellEdit: false,
                    displayName: 'Unit Name'
                }, {
                    field: 'category',
                    name: 'category',
                    enableCellEdit: false,
                    displayName: 'Category'
                }, {
                    field: 'status',
                    name: 'status',
                    enableCellEdit: false,
                    displayName: 'status'
                }, {
                    field: 'mappingStatus',
                    name: 'mappingStatus',
                    enableCellEdit: false,
                    displayName: 'mapping status'
                }, {
                    field: 'inventoryList',
                    name: 'inventoryList',
                    enableCellEdit: true,
                    editableCellTemplate: 'ui-grid/dropdownEditor',
                    editDropdownOptionsArray: $scope.inventoryListMapping,
                    editDropdownIdLabel: 'code',
                    editDropdownValueLabel: 'inventoryList',
                    displayName: 'Inventory List'
                }, {
                    field: 'productionUnit',
                    name: 'ProductionUnit',
                    enableCellEdit: true,
                    editableCellTemplate: 'ui-grid/dropdownEditor',
                    editDropdownOptionsArray: $scope.productionUnitMapping,
                    editDropdownIdLabel: 'code',
                    editDropdownValueLabel: 'productionUnit',
                    displayName: 'Production Unit'
                },
                {
                    field: 'skuPackaging',
                    name: 'skuPackaging',
                    enableCellEdit: true,
                    editableCellTemplate: 'ui-grid/dropdownEditor',
                    editDropdownValueLabel: 'value',
                    editDropdownRowEntityOptionsArrayPath: 'skuPackagingList',
                    editDropdownIdLabel: 'value',
                    displayName: 'Sku Packaging'
                }
                ];
            };

            $scope.getAllSkus = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllSku
                }).then(function success(response) {
                    $scope.allSkuList = response.data
                }, function (response) {
                    console.log("got error", response);
                });
            };

            $scope.getAllUnitList = function () {
                metaDataService.getUnitList(function (response) {
                    $scope.allUnitsList = response;/*.filter(function (value) {
                        return value.code == 'WAREHOUSE' || value.code == 'KITCHEN' || value.code == 'OFFICE';
                    });*/
                });
            };

            $scope.filterSelectedUnit = function (value) {
                if ($scope.mappingValuesDisplay == undefined || $scope.mappingValuesDisplay == null) {
                    return true;
                }
                return value.id != $scope.mappingValuesDisplay.id;
            };

            $scope.searchMappingShow = function () {
                if ($scope.typesofClone) {
                    $alertService.alert("Alert", "Mapping created via clone request is cleared.", function () {
                        $scope.typesofClone = false;
                        $scope.cloningMapping = null;
                        $('#cloneDisplayData').val('').trigger('change');
                        $scope.displayId = $scope.mappingValuesDisplay.id;
                        $scope.searchData('search', $scope.mappingValuesDisplay.id);
                    });

                } else {
                    $scope.cloningMapping = null;
                    if ($scope.mappingValuesDisplay != undefined && $scope.mappingValuesDisplay != null) {
                        $scope.displayId = $scope.mappingValuesDisplay.id;
                        $scope.searchData('search', $scope.mappingValuesDisplay.id);
                    }
                }
            };

            $scope.deactivateAllSkuList = function () {
                for (var i = 0; i < $scope.allSkuList.length; i++) {
                    $scope.allSkuList[i].selectData = false;
                    $scope.allSkuList[i].check = false;
                    $scope.allSkuList[i].mappingStatus = 'IN_ACTIVE';
                    $scope.allSkuList[i].profile = null;
                }
            };

            $scope.deactivateAllUnitList = function () {
                for (var i = 0; i < $scope.allUnitsList.length; i++) {
                    $scope.allUnitsList[i].selectData = false;
                    $scope.allUnitsList[i].check = false;
                    $scope.allUnitsList[i].mappingStatus = 'IN_ACTIVE';
                }
            };

            $scope.preSelectData = function () {
                $scope.gridApi.grid.modifyRows($scope.gridOptions.data);
                $scope.gridOptions.data.forEach(function (row, index) {
                    if (row.selectData) {
                        $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                    } else {
                        $scope.gridApi.selection.unSelectRow($scope.gridOptions.data[index]);
                    }
                });
                // this is required to show filter box as it fails to show
                $(".ui-grid-header-canvas").css("height","54px");
            };


            function createSkuInventoryMap(skuForUnitList){
                var skipProduct = [];
                $scope.productInventoryList = {};
                skuForUnitList.forEach(function (sku){
                    if(sku.mappingStatus == "ACTIVE"){
                        var productId = $scope.skuProductMap[sku.id];
                        if(skipProduct.indexOf(productId) != -1){
                            return;
                        }
                        var mapData = $scope.productInventoryList[productId];
                        if(!appUtil.isEmptyObject(mapData) && mapData != sku.inventoryList ){
                            delete $scope.productInventoryList[productId];
                            skipProduct.push(productId);
                        }else{
                            $scope.productInventoryList[productId] = sku.inventoryList;
                        }
                    }

                });

            }

            $scope.refreshSkuListAndGetSkusForUnit = function () {
                metaDataService.getSkuListForUnit($scope.displayId,function(skuForUnitList){
                    createSkuInventoryMap(skuForUnitList);
                    $scope.deactivateAllSkuList();
                    for (var i = 0; i < $scope.allSkuList.length; i++) {
                        if($scope.allSkuList[i].recipeRequired==false) {
                            $scope.allSkuList[i].productionUnit = 'NA';
                        }
                        else {
                            $scope.allSkuList[i].productionUnit=null;
                        }

                        var mappings = $scope.skuMappings[$scope.allSkuList[i].id];
                        if (!appUtil.checkEmpty(mappings)) {
                            var mappedProfiles = [];
                            var id = 0;
                            for (var index in mappings) {
                                var profile = packagingService.getProfile(mappings[index].packagingId);
                                if (mappings[index].isDefault) {
                                    $scope.allSkuList[i].skuPackaging = profile.packagingName;
                                }
                                var obj = {
                                    id: id,
                                    value: profile.packagingName
                                };
                                if(mappings[index].mappingStatus == "ACTIVE"){
                                    mappedProfiles.push(obj);
                                }
                                id++;
                            }
                            $scope.allSkuList[i].skuPackagingList = mappedProfiles;
                        }
                    }
                    for (var j = 0; j < skuForUnitList.length; j++) {
                        for (var i = 0; i < $scope.allSkuList.length; i++) {
                            if ($scope.allSkuList[i].id == skuForUnitList[j].id) {
                                if ('ACTIVE' == skuForUnitList[j].mappingStatus) {
                                    $scope.allSkuList[i].check = true;
                                    $scope.allSkuList[i].selectData = true;
                                    $scope.allSkuList[i].mappingStatus = skuForUnitList[j].mappingStatus;
                                    $scope.allSkuList[i].profile = skuForUnitList[j].profile;
                                    $scope.allSkuList[i].inventoryList = skuForUnitList[j].inventoryList;
                                    $scope.allSkuList[i].recipeRequired = skuForUnitList[j].recipeRequired;
                                    if (skuForUnitList[j].productionUnit != 'NULL') {
                                        $scope.allSkuList[i].productionUnit = skuForUnitList[j].productionUnit;
                                    }
                                    if (skuForUnitList[j].packagingId != null) {
                                        var profile = packagingService.getProfile(skuForUnitList[j].packagingId);
                                        $scope.allSkuList[i].skuPackaging = profile.packagingName;
                                    }
                                }
                                break;
                            }
                        }
                    }
                    $scope.gridOptions.columnDefs = $scope.unitToSkuGridColumns();
                    $scope.gridOptions.data = $scope.allSkuList;
                    $scope.preSelectData();
                });
            };

             function getSiblingsSkusInventoryList(units , skuId){
                var unitIds = [];
                for(var i=0;i<units.length;i++){
                    unitIds.push(units[i].id);
                }
                $http({
                    url: apiJson.urls.skuMapping.getSiblingSkusInventoryList,
                    method: 'POST',
                    data: unitIds,
                    params : {
                        skuId : skuId
                    }
                }).then(function (response) {
                    console.log("unit inventory map : ", response.data);
                     $scope.unitInventoryListMap = response.data;
                }, function (response) {
                    console.log("got error", response);
                });
            }


            $scope.refreshUnitListAndGetUnitForSku = function () {
                $http({
                    url: apiJson.urls.skuMapping.getUnitForSku,
                    method: 'POST',
                    data: $scope.displayId
                }).then(function (response) {
                    var skuToUnitList = response.data;
                    $scope.deactivateAllUnitList();
                    var allUnitsList = appUtil.filterUnitList($scope.allUnitsList);
                    getSiblingsSkusInventoryList(allUnitsList,$scope.displayId);
                    $scope.filteredUnits = [];
                    var recipeRequire=defaultCheck($scope.displayId);
                    console.log(recipeRequire);
                    for (var k = 0; k < allUnitsList.length; k++) {
                        allUnitsList[k].inventoryList = $scope.mappingValuesDisplay.inventoryList;
                        allUnitsList[k].recipeRequired=recipeRequire;
                        //allUnitsList[k].productionUnit=null;
                        if(allUnitsList[k].recipeRequired==false){
                            allUnitsList[k].productionUnit="NA";
                        }else {
                            allUnitsList[k].productionUnit=null;
                        }

                        var mappings = $scope.skuMappings[$scope.displayId];
                        if (!appUtil.checkEmpty(mappings)) {
                            var mappedProfiles = [];
                            var id = 0;
                            for (var index in mappings) {
                                var profile = packagingService.getProfile(mappings[index].packagingId);
                                if (mappings[index].isDefault) {
                                    allUnitsList[k].skuPackaging = profile.packagingName;
                                }
                                var obj = {
                                    id: id,
                                    value: profile.packagingName
                                };
                                mappedProfiles.push(obj);
                                id++;
                            }
                            allUnitsList[k].skuPackagingList = mappedProfiles;
                            console.log(allUnitsList[k].skuPackagingList);
                        }
                    }
                    for (var j = 0; j < skuToUnitList.length; j++) {
                        for (var i = 0; i < allUnitsList.length; i++) {
                            if (allUnitsList[i].id == skuToUnitList[j].id) {
                                if ('ACTIVE' == skuToUnitList[j].mappingStatus) {
                                    allUnitsList[i].selectData = true;
                                    allUnitsList[i].check = true;
                                    allUnitsList[i].mappingStatus = skuToUnitList[j].mappingStatus;
                                    allUnitsList[i].inventoryList = skuToUnitList[j].inventoryList;
                                    if (skuToUnitList[j].productionUnit != "NULL") {
                                        allUnitsList[i].productionUnit = skuToUnitList[j].productionUnit;
                                    }
                                    if (skuToUnitList[j].packagingId != null) {
                                        var profile = packagingService.getProfile(skuToUnitList[j].packagingId);
                                        allUnitsList[i].skuPackaging = profile.packagingName;
                                    }
                                }
                                break;
                            }
                        }
                        if($rootScope.mappedUnits.indexOf(skuToUnitList[j].id)===-1 && appUtil.showFilteredUnitList() &&
                            'ACTIVE' === skuToUnitList[j].mappingStatus){
                            skuToUnitList[j].selectData = true;
                            skuToUnitList[j].check = true;
                            $scope.filteredUnits.push(skuToUnitList[j]);
                        }
                    }
                    $scope.gridOptions.columnDefs = $scope.skuToUnitGridColumns();
                    $scope.gridOptions.data = allUnitsList;
                    $scope.preSelectData();
                }, function (response) {
                    console.log("got error", response);
                });
            };

            function  defaultCheck(id){
                for(var index in $scope.skuData){
                    if($scope.skuData[index].id == id){
                        if($scope.skuData[index].recipeRequired == true){
                            return true;
                        }
                    }
                }
                return false;
            }

            $scope.searchData = function (type, id) {
                if (type == 'search') {
                    $scope.gridDataId = id;
                }
                $scope.displayId = id;
                $scope.skuProductMap =  createproductSkuMap();
                if ($scope.mappingTypeData == "unitToSku") {
                    $scope.refreshSkuListAndGetSkusForUnit();
                } else if ($scope.mappingTypeData == "skuToUnit") {
                    $scope.refreshUnitListAndGetUnitForSku();
                }
            };

            $scope.submitMapping = function () {
                $scope.allGridViewShow = [];
                $scope.gridOptions.data.forEach(function (row) {
                    if (row.selectData == true) {
                        if (row.inventoryList == null) {
                            $toastService.create("Please Inter InventoryList for skuId :" + row.id);
                        }
                        else if(row.recipeRequired==true && row.productionUnit==null ){
                            row.productionUnit="Chaayos";
                            $scope.allGridViewShow.push(row);
                            //$toastService.create("Please Inter ProductionUnit  for skuId :" + row.id);
                        }
                        else{
                            $scope.allGridViewShow.push(row);
                        }
                    }
                });
                if($scope.allGridViewShow)
                $scope.allGridViewShow = $scope.allGridViewShow.concat($scope.filteredUnits);
                $scope.updateMappingRequested = true;
            };

            $scope.showRequestForMapping = function (mappingTypeId) {
                $scope.reset();
                if (mappingTypeId == 1) {
                    $scope.mappingTypeData = "unitToSku";
                    $http({
                        method: "GET",
                        dataType: 'json',
                        data: '',
                        headers: {
                            "Content-Type": "application/json"
                        },
                        url: apiJson.urls.skuMapping.getAllUnit,
                    }).then(
                        function success(response) {
                            $scope.UnitListDetails = appUtil.filterUnitList(response.data);
                           /*.filter(function (value) {
                                return value.code == 'WAREHOUSE' || value.code == 'KITCHEN'
                                    || value.code == 'OFFICE';
                            });*/
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                } else if (mappingTypeId == 2) {
                    $scope.mappingTypeData = "skuToUnit";
                    $http({
                        method: "GET",
                        dataType: 'json',
                        data: '',
                        headers: {
                            "Content-Type": "application/json"
                        },
                        url: apiJson.urls.skuMapping.getAllSku
                    }).then(function success(response) {
                        $scope.UnitListDetails = response.data;
                        $scope.skuData= response.data;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.backPreview = function () {
                $scope.updateMappingRequested = false;
                $timeout(function () {
                    $scope.preSelectData();
                });
            };

            $scope.updateMapping = function () {
                $alertService.confirm("Are you sure?",
                    "Requested mappings will be marked as active, others will be marked as Inactive. ",
                    function (yes) {
                        if (yes) {
                            $scope.submitUpdateMappingRequest();
                        }
                    });
            };

            $scope.getRecipeProfiles = function (){
                $http({
                    method: 'GET',
                    url: apiJson.urls.recipeManagement.recipeProfiles,
                    params: {type: "SCM_R"}
                }).then(function success(response) {
                    $scope.recipeProfiles = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.submitUpdateMappingRequest = function () {
                var currentUser = appUtil.getCurrentUser();
                var idsList = [];
                var UpdatedInventoryList = {};
                var updateProductionUnit={};
                var updatedPackagingId={};
                $scope.allGridViewShow.forEach(function (ListUnitToSkuShow) {
                    if (ListUnitToSkuShow != null && ListUnitToSkuShow.selectData == true) {
                        console.log(ListUnitToSkuShow);
                        idsList.push(ListUnitToSkuShow.id);
                        UpdatedInventoryList[ListUnitToSkuShow.id] = ListUnitToSkuShow.inventoryList;
                        if(ListUnitToSkuShow.productionUnit!="NA") {
                            updateProductionUnit[ListUnitToSkuShow.id] = ListUnitToSkuShow.productionUnit;
                        }
                        updatedPackagingId[ListUnitToSkuShow.id] = ListUnitToSkuShow.skuPackaging;
                        //set default Chaayos if recipe is true;
                    }
                });
                var payload = {
                    id: $scope.mappingValuesDisplay.id,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name,
                    mappingIds: idsList,
                    mappedSkuInventorys: UpdatedInventoryList,
                    mappedProductionUnit: updateProductionUnit,
                    mappedPackagingId: updatedPackagingId
                };
                var targetUrl = null;
                if ($scope.unitMappingView.name == "Unit to Sku Mapping") {
                    targetUrl = apiJson.urls.skuMapping.updateSkuForUnit;
                } else if ($scope.unitMappingView.name == "Sku to Unit Mapping") {
                    targetUrl = apiJson.urls.skuMapping.updateUnitForSku;
                }
                if (targetUrl != null) {
                    $http({
                        url: targetUrl,
                        method: 'POST',
                        data: payload
                    }).then(function (response) {
                        $toastService.create($scope.unitMappingView.name + " Updated Successfully!");
                        $scope.reset();
                        $state.go("menu.vendorToSkuMapping");
                    }, function (response) {
                        console.log("got error", response);
                    });
                } else {
                    console.log("No Mapping Assigned");
                }
            };

            $scope.sendCloneRequest = function () {
                if ($scope.gridDataId == undefined || $scope.gridDataId == null) {
                    $toastService.create("Please select a mapping request first");
                    return;
                }
                if ($scope.cloningMapping == undefined || $scope.cloningMapping == "") {
                    $toastService.create("Please select clone from mapping");
                }
                if ($scope.unitMappingView.name == "Sku to Unit Mapping") {
                    $scope.typesofClone = true;
                    $scope.mappingTypeData = "skuToUnit";
                    $scope.searchData('clone', $scope.cloningMapping.id);
                } else if ($scope.unitMappingView.name == "Unit to Sku Mapping") {
                    $scope.typesofClone = true;
                    $scope.mappingTypeData = "unitToSku";
                    $scope.searchData('clone', $scope.cloningMapping.id);
                }
            };
        }
    ]
).controller('profileTemplate', ['$scope','recipeprofiles',
    function ($scope, recipeProfiles) {
        $scope.init = function()
        {
            $scope.templateRecipeProfiles = recipeProfiles;
        }
    }
]);
