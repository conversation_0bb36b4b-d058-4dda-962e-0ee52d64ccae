/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('vendorRequestCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
        '$location', '$toastService', '$alertService','$state',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, $alertService,$state) {
            $scope.init = function () {
                $scope.select2Options = {'width': '100%'};
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.userId = $scope.currentUser.userId;
                $scope.userName = $scope.currentUser.user.name;
                $scope.showApproveButton = $scope.currentUser.user.department.id == 105 || $scope.userId==120063;
                $scope.vendorTypes = [];
                $scope.showPendingCheck = true;
                $scope.selectedVendorType = null;
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.vendorType
                }).then(function success(response) {
                    $scope.vendorTypes = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendorStatus
                }).then(function success(response) {
                    $scope.vendorStatusList = response.data;
                    if(!appUtil.isEmptyObject($scope.vendorStatusList)){
                        var excluded = ["ACTIVE","IN_ACTIVE"];
                        $scope.vendorStatusList = $scope.vendorStatusList.filter(function(status){
                            return excluded.indexOf(status)==-1;
                        });
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: "GET",
                    url: apiJson.urls.users.activeUsers
                }).then(function success(response) {
                    $scope.activeUserList = response.data;
                    console.log("vendorUser", $scope.vendorUser)
                }, function error(response) {
                    console.log("error:" + response);
                });

                $("#vendorAddRequestDiv").hide();
                $("#requestForViewDiv").hide();
                $("#viewDateDiv").show();
            };


            $scope.viewDetails = function(vendorId,reqId){
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.vendor,
                    params:{
                        vendorId:vendorId
                    }
                }).then(function(response) {
                    if(!appUtil.isEmptyObject(response.data)){
                        $state.go('menu.viewVendor',{edit:false,reqId:reqId, vendor:response.data});
                    }else{
                        $toastService.create("Could not fetch vendor details! Please try again...");
                    }
                }, function(response) {
                    console.log("error:" + response);
                });
            };


            $scope.selectedUnit = {width: '100%'};
            $scope.showRequestFor = function (ids) {
                $scope.userActiveId = ids.id;
                $scope.userActiveName = ids.name;
            };

            $scope.selectVendorType = function (vendorType) {
                $scope.selectedVendorType = vendorType;
            };

            $scope.sendRequest = function () {
                if ($scope.requestForListData == null || $scope.requestForListData == "") {
                    $toastService.create("Please Select Request For");
                    return false;
                }
                if ($scope.vendorRequestNameData == null || $scope.vendorRequestNameData == "") {
                    $toastService.create("Please Enter Vendor Name");
                    return false;
                }
                if($scope.selectedVendorType == null || $scope.selectedVendorType == ""){
                    $toastService.create("Please Enter Vendor Type");
                    return false;
                }
                if ($scope.vendorRequestEmailData == null || !validateEmail($scope.vendorRequestEmailData)) {
                    $toastService.create("Email can not empty or Invalid");
                    return;
                }
                if ($scope.vendorRequestCopyEmailData == null || !validateEmail($scope.vendorRequestCopyEmailData)) {
                    $toastService.create("Copy Email can not empty or Invalid");
                    return;
                }

                var sendRequestObj = {
                    vendorName: $scope.vendorRequestNameData,
                    requestDate: null,
                    requestForId: $scope.userActiveId,
                    requestForName: $scope.userActiveName,
                    requestByName: $scope.userName,
                    requestById: $scope.userId,
                    email: $scope.vendorRequestEmailData,
                    copyEmails: $scope.vendorRequestCopyEmailData,
                    vendorType: $scope.selectedVendorType
                };

                $http({
                    url: apiJson.urls.vendorManagement.vendorRequest,
                    method: 'POST',
                    data: sendRequestObj
                }).then(function (response) {
                        $scope.sendRequestObj = response.data;
                        $toastService.create("Vendor Request Successfully added!");

                        $("#vendorAddRequestDiv").hide();
                        $scope.showViewRequest();
                        $("#requestForViewDiv").show();
                        $("#viewDateDiv").show();


                    //console.log(response.data);
                }, function (response) {
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg,function () {}, true);
                    console.log("got error", response);

                });


            };
            $scope.viewRequest = function () {

                $("#vendorAddRequestDiv").hide();
                $("#requestForViewDiv").hide();
                $("#viewDateDiv").show();

            };

            $scope.showAddRequest = function () {
                $("#vendorAddRequestDiv").show();
                $("#requestForViewDiv").hide();
                $("#viewDateDiv").hide();
            };


            $scope.showViewRequest = function () {
                if ($scope.selectedStatus == undefined || $scope.selectedStatus == null) {
                    return;
                }
                var url = apiJson.urls.vendorManagement.vendorRegistrationByStatus + '/' + $scope.selectedStatus
                $http({
                    url: url,
                    method: 'POST'
                }).then(function (response) {
                    console.log(response.data);
                    $scope.sendRequestObj = response.data;
                    $scope.sendRequestObj.length == 0 ? $scope.showMessage = true : $scope.showMessage = false;
                }, function (response) {
                    console.log("got error", response);
                });
                $("#requestForViewDiv").show();
                $("#viewDateDiv").show();
            };

            function validateEmail(email) {
                var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(email);
            }

            $scope.changeStatus = function (status, requestOn, requestBy) {
                console.log(status + "--" + requestOn + "-" + requestBy);
            };

            $scope.requestStatusChange = function (id) {
                $alertService.confirm("Approve", "Are You Sure You Wish To CANCEL The Vendor Registration Request?", function (result) {
                    if (result) {
                        $http({
                            url: apiJson.urls.vendorManagement.vendorRequestCancel,
                            method: 'POST',
                            data: id
                        }).then(function (response) {

                            $scope.sendRequestObj.forEach(function (vendorRequestList) {
                                if (vendorRequestList.id == id) {
                                    vendorRequestList.requestStatus = "CANCELLED"
                                }
                            });

                            console.log(response.data);
                        }, function (response) {
                            console.log("got error", response);
                        });
                    } else {
                        return;
                    }
                });

            };

            $scope.addLeadTime = function(vendorReq)
            {
                if(vendorReq.leadTime==null || vendorReq.leadTime==0)
                {
                    $toastService.create("Please select Lead Time first");
                    return;
                }
                else
                    $alertService.alert("Lead Time", "Successfully Added Lead TIme");
            }

            $scope.activateVendor = function (creditCycle, vendorReq) {

                if(vendorReq.leadTime==null || vendorReq.leadTime==0){
                    $toastService.create("Please select Lead Time first");
                    return;
                }
                if (!appUtil.isEmptyObject(creditCycle)) {
                    $http({
                        url: apiJson.urls.vendorManagement.approveVendor,
                        method: 'POST',
                        data: {
                            leadTime: vendorReq.leadTime,
                            requestId: vendorReq.id,
                            creditCycle: creditCycle,
                            userId: appUtil.getCurrentUser().userId
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response) && response.data) {
                            vendorReq.requestStatus = "APPROVED";
                            $toastService.create("Vendor Approved successfully!", function () {
                                $scope.showViewRequest();
                            });
                        }
                    }, function (response) {
                        console.log("got error", response);
                    });
                } else {
                    $toastService.create("Please enter credit cycle before approving vendor!");
                }
            };

        }
    ]
);
