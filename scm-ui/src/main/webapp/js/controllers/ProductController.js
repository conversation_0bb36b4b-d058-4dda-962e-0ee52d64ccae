/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('productCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state',
        '$stateParams', 'apiJson', 'appUtil', '$http', '$toastService','Popeye','metaDataService','$alertService',
        function ($rootScope, $scope, authService, $location, $state,
                  $stateParams, apiJson, appUtil, $http, $toastService, Popeye, metaDataService,$alertService) {

            $scope.productDefinition = $stateParams.productDef;
            $scope.editMode = false;
            console.log($scope.productDefinition);

            $scope.multiSelectOptions = {
                'width': '100%',
                'multiple': true
            };

            $scope.byType = function(type){
                return type != "DERIVED" && type != "SELF";
            };

            $scope.removeSelf = function (type) {
                return type != "SELF";
            }

            var oldName='';
            $scope.init = function () {
                $scope.loginType =  $rootScope.loginType=="sumo"?"GOODS":"SERVICE";
                $scope.categories = appUtil.getMetadata().categoryDefinitions;
                $scope.taxCodes = appUtil.getTaxProfiles();
                console.log($scope.categories);
                $scope.frequencies = appUtil.getMetadata().stockTakeType.filter(function(freq){
                    return freq != "WEEKLY";
                });
                $scope.fulfillmentTypes = appUtil.getMetadata().fulFillmentTypes;
                $scope.varianceTypes = appUtil.getMetadata().varianceTypes;
                $scope.uomMetadata = appUtil.getMetadata().uomMetadata;
                $scope.profileDefinitions  = appUtil.getMetadata().profileDefinitions;
                $scope.classifications=[];
                $scope.getListMap();

                if (appUtil.isEmptyObject($stateParams.productDef)) {
                    $scope.editMode = false;
                    $scope.productDefinition = {};
                    $scope.selectedFulfillments = [];
                    $scope.selectCategory($scope.categories[0]);
                    $scope.productDefinition.stockKeepingFrequency = $scope.frequencies[0];
                    $scope.productDefinition.productStatus = "IN_ACTIVE";
                    $scope.productDefinition.hasCase = false;
                    $scope.productDefinition.hasInner = false;
                    $scope.productDefinition.recipeRequired = false;
                    $scope.productDefinition.shelfLifeInDays = -1;
                    $scope.productDefinition.supportsLooseOrdering = false;
                    $scope.productDefinition.variantLevelOrdering = false;
                    $scope.productDefinition.participatesInRecipe = false;
                    $scope.productDefinition.participatesInCafeRecipe = false;
                    $scope.productDefinition.supportsSpecialOrdering = false;
                    $scope.productDefinition.assetOrdering = false;
                    $scope.productDefinition.createdBy = appUtil.createGeneratedBy();
                    $scope.productDefinition.bulkGRAllowed = false;
                    $scope.productDefinition.profileId = null;
                    console.log('inside ' + $stateParams.productDef);
                } else {
                    $scope.editMode = true;
                    console.log($scope.productDefinition)
                    $scope.selectFulfilmentType($scope.productDefinition.fulfillmentType);
                    setTaxCode($scope.productDefinition.taxCode);
                    oldName=$scope.productDefinition.productName;
                    $scope.hasShelfLife = $scope.productDefinition.shelfLifeInDays != -1;
                    $scope.productDefinition.categoryDefinitionId = $scope.productDefinition.categoryDefinition.id;
                    $scope.selectedProductCategory();
                }
            };

            $scope.selectedProductCategory = function () {
                $scope.categories.forEach(function (cat) {
                    if($scope.productDefinition.categoryDefinitionId == cat.categoryId){
                        $scope.subCategories = cat.subCategories;
                        $scope.productDefinition.subCategoryDefinitionId = $scope.productDefinition.subCategoryDefinition.id;
                    }
                });
            }

            function setTaxCode(code){
                for(var i in $scope.taxCodes){
                    if($scope.taxCodes[i].code == code){
                        $scope.taxCategory = $scope.taxCodes[i];
                        return;
                    }
                }
            }

            $scope.selectFulfilmentType = function(type){
                $scope.selectedFulfillment = type;
                if(type=="DERIVED"){
                    $scope.showDefaultType = true;
                }else{
                    $scope.showDefaultType = false;
                }
            };

            $scope.showTaxDetail = function(){
                if($scope.taxCategory != null){
                    showTaxCategoryDetail($scope.taxCategory.code);
                }
            };

            function showTaxCategoryDetail(code){
                var data = appUtil.getTaxProfile(code);
                if(data == undefined || data == null){
            	return;
                }
                metaDataService.getTaxHsnCodes(data.id,1,1).then(function(data){
                	var codes = appUtil.getTaxHsnList();
                    $alertService.alert('Tax Category Info for ' + data.code,'<b>Code :</b> '+data.code +'<br> <b>CGST :</b> '+codes.cgst +'<b>%</b><br><b>SGST :</b> '+codes.sgst +'<b>%</b><br> <b>IGST :</b> '+codes.igst +'<b>%</b><br>  <b>Description :</b> '+data.desc+'<br> <b>Internal Description :</b> '+data.intDesc );
                });


            }

            $scope.clickHasShelfLife = function(){
        	if($scope.hasShelfLife){
                $scope.updateShelfLifeRange($scope.productDefinition.subCategoryDefinitionId)
        	}else{
        	    $scope.productDefinition.shelfLifeInDays = -1;
                $scope.productDefinition.minRange = -1;
                $scope.productDefinition.maxRange = -1;
        	}
            }
            $scope.onTaxCategoryChange = function() {
        	    $scope.productDefinition.taxCode = $scope.taxCategory.code;
            };

            $scope.updateShelfLifeRange = function (subCategoryId){
                $scope.subCategories.forEach(function (subCategory){
                    if(subCategory.subCategoryId === subCategoryId){
                        if($scope.hasShelfLife){
                            if(!appUtil.isEmptyObject(subCategory.shelfLifeRange)){
                                $scope.productDefinition.minRange = subCategory.shelfLifeRange.split(",")[0];
                                $scope.productDefinition.maxRange = subCategory.shelfLifeRange.split(",")[1];
                            }else{
                                $scope.productDefinition.minRange = null;
                                $scope.productDefinition.maxRange = null;
                            }
                            $scope.productDefinition.shelfLifeInDays = subCategory.shelfLifeInDays;
                        }
                        return;
                    }
                });
            }


            $scope.selectCategory = function () {
                $scope.categories.forEach(function (cat) {
                    if($scope.productDefinition.categoryDefinitionId == cat.categoryId){
                        $scope.subCategories = cat.subCategories;
                        $scope.productDefinition.subCategoryDefinitionId = $scope.subCategories[0].subCategoryId;
                    }
                    if($scope.productDefinition.categoryDefinitionId != 1 && $scope.productDefinition.categoryDefinitionId != 2){
                	$scope.productDefinition.participatesInRecipe = false;
                	$scope.productDefinition.participatesInCafeRecipe = false;
                    }
                });
                changeStockTakeType();
            };

            function changeStockTakeType () {
                if($scope.productDefinition.categoryDefinitionId == 3){
                    $scope.productDefinition.stockKeepingFrequency = 'FIXED_ASSETS'
                }
            }

            $scope.logout = function () {
                $rootScope.logout();
            };

            function isProductlinked(prodID){
            	var promise= $http({
            		method: 'GET',
            		url: apiJson.urls.recipeManagement.getLinkedRecipe,
            		params:{productId : prodID}
            	}).then(function success(response) {
            		return response.data;
            	}, function error(response) {
            		console.log("error:"+response);
            	});
            	return promise;
            };


            $scope.setProduct = function () {
                if($scope.productDefinition.recipeRequired == true && $scope.productDefinition.shelfLifeInDays < 0){
                    $toastService.create("Please select a shelf life for recipe product greater than or equals to 0!");
                    return;
                }
                $scope.productDefinition.unitPrice=0;
                $scope.productDefinition.negotiatedUnitPrice=0;
                console.log("new product",$scope.productDefinition)
            	if($scope.editMode && oldName != $scope.productDefinition.productName){
            		isProductlinked($scope.productDefinition.productId).then(function(data){
            			console.log("linked recipe  "+data.length);
            			if(data.length>0){
            				var msg='As this product is linked with these '+data.length +' recipes : </br>';
            				for(var i=0;i<data.length;i++){
            					msg = msg +"  "+data[i].name;
            					if(i<data.length-1){
            						msg = msg +"</br>";
            					}else{
            						msg = msg +".";
            					}
            				}
            				$alertService.confirm("Are you sure, You want to change name of this product?",msg,function(result){
            					console.log("result ",result);
            					if(result){
            						setProductDetails();
            					}
            				});
            			}else{
            				setProductDetails();
            			}
            		});
            	}else{
            		setProductDetails();
            	}
            };

            function setProductDetails(){
            	  var reqMethod = "POST";
                  if($scope.editMode){
                      reqMethod = "PUT";
                  }
                  $scope.categories.forEach(function (cat) {
                      if(cat.categoryId==$scope.productDefinition.categoryDefinitionId){
                          $scope.productDefinition.categoryDefinition = {
                              id:cat.categoryId,
                              code:cat.categoryCode,
                              name:cat.categoryName
                          };
                      }
                  });
                  appUtil.getMetadata().subCategoryDefinitions.forEach(function (scat) {
                      if(scat.subCategoryId==$scope.productDefinition.subCategoryDefinitionId){
                          $scope.productDefinition.subCategoryDefinition = {
                              id:scat.subCategoryId,
                              code:scat.subCategoryCode,
                              name:scat.subCategoryName
                          };
                      }
                  });
                  //code for the new fulfillment type selection flow
                  if(appUtil.isEmptyObject($scope.productDefinition.fulfillmentType)){
                      $toastService.create("Please select a fulfillment type for this product");
                      return;
                  }

                  if($scope.productDefinition.fulfillmentType=="DERIVED") {
                      if(appUtil.isEmptyObject($scope.productDefinition.defaultFulfillmentType)){
                          $toastService.create("Please select a default fulfillment type for this product");
                          return;
                      }
                      }else{
                      if(!appUtil.isEmptyObject($scope.productDefinition.derivedMappings)){
                          delete $scope.productDefinition.derivedMappings;
                      }
                  }
                  sendRequest(reqMethod, apiJson.urls.productManagement.productDetail, $scope.productDefinition, $scope.editMode);
            }

            function sendRequest(method, url, data, editMode) {
                $http({
                    method: method,
                    url: url,
                    data: data
                }).then(function success(response) {
                    console.log(response);
                    var status = editMode ? "updated" : "created";
                    var message = "";
                    if (!appUtil.isEmptyObject(response.data)) {
                        message = "Product " + status + " successfully";
                        if(editMode){
                            appUtil.updateProductInCache(response.data);
                        }else{
                            appUtil.addProductToCache(response.data);
                        }
                    } else {
                        message = "Product could not be " + status;
                    }
                    $toastService.create(message, function () {
                        $state.go('menu.productList');
                    });
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.goBack = function (productId) {
                $state.go('menu.productList', {product: productId});
            };

            $scope.mapDerivedMapping = function(defaultFulfillmentType){
                $scope.productDefinition.defaultFulfillmentType = defaultFulfillmentType;
                $scope.openDerivedMappingModal(defaultFulfillmentType);
            };


            $scope.openDerivedMappingModal = function(defaultFulfillmentType){
                if(!appUtil.isEmptyObject(defaultFulfillmentType)){
                    var mappingModal = Popeye.openModal({
                        templateUrl: "derivedMapping.html",
                        controller: "derivedMappingCtrl",
                        resolve:{
                            defaultType:function(){
                                return defaultFulfillmentType;
                            },
                            existingMappings: function(){
                                return $scope.productDefinition.derivedMappings;
                            }
                        },
                        click:false,
                        keyboard:false
                    });

                    mappingModal.closed.then(function(mappings){
                        $scope.productDefinition.derivedMappings = mappings;
                    });
                }else{
                    $toastService.create("Please select a default mapping first!");
                }
            };

            $scope.getListMap = function (){
                console.log("searching response")
                $http({
                    method : 'GET',
                    url : apiJson.urls.serviceOrderManagement.getListData+"?baseType="+$scope.loginType,
                }).then(function success(response) {
                    if(response.data != null){
                        console.log("response is ",response.data)
                        var  map = response.data;
                        $scope.departments = map["Department"];
                        $scope.divisions = map["Division"];
                        $scope.classifications = map["Classification"];

                        if($scope.productDefinition.classificationId!=null){
                            $scope.selectedClassification($scope.productDefinition.classificationId);
                        }
                    }
                }, function error(response) {
                    console.log("error:" + response);
                })
            }

            $scope.selectedClassification=function (selectedClassification) {
                console.log(selectedClassification)
                for(var i = 0 ; i < $scope.classifications.length ; i++){
                    if($scope.classifications[i].listDetailId == selectedClassification){
                        $scope.subClassifications = $scope.classifications[i].listType;
                    }
                }
            }

        }
    ]
).controller('derivedMappingCtrl', ['$rootScope', '$scope', 'appUtil', '$toastService','metaDataService','defaultType',
        'existingMappings','Popeye',
        function ($rootScope, $scope, appUtil, $toastService,metaDataService,defaultType,existingMappings,Popeye) {

            function arrangeByRegion(units) {
                $scope.unitsByRegion = {};
                units.forEach(function (unit) {
                    if (!appUtil.isEmptyObject(unit.subCategory)) {
                        if (appUtil.isEmptyObject($scope.unitsByRegion[unit.subCategory])) {
                            $scope.unitsByRegion[unit.subCategory] = [];
                            $scope.mappingsByRegion[unit.subCategory] = defaultType;
                        }
                        $scope.unitsByRegion[unit.subCategory].push(unit);
                    }
                });
            }

            $scope.changeMappingsForRegion = function (region, units, type) {
                units.forEach(function (unit) {
                    $scope.changeMappings(unit.id, type);
                });
            };

            $scope.initMappings = function () {
                $scope.mappings = {};
                $scope.mappingsByRegion = {};

                metaDataService.getUnitList(function (units) {
                    $scope.units = units;
                    arrangeByRegion($scope.units);
                    $scope.fulfillmentTypes = appUtil.getMetadata().fulFillmentTypes;
                    $scope.fulfillTypeWithNull = [null].concat($scope.fulfillmentTypes);

                    if (!appUtil.isEmptyObject(existingMappings)) {
                        existingMappings.forEach(function (mapping) {
                            $scope.changeMappings(mapping.unit, mapping.type);
                        });
                    }

                    $scope.units.forEach(function (unit) {
                        if (appUtil.isEmptyObject($scope.mappings[unit.id])) {
                            $scope.changeMappings(unit.id, defaultType);
                        }
                    });
                });
            };

            $scope.changeMappings = function (unitId, mapping) {
                $scope.mappings[unitId] = {
                    unit: unitId,
                    type: mapping
                };
            };

            $scope.submitMappings = function () {
                Popeye.closeCurrentModal(Object.values($scope.mappings));
            };

        }

    ]
);
