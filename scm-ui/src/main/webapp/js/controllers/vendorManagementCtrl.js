/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('vendorManagementCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', '$state', '$alertService', 'metaDataService', '$fileUploadService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, $state, $alertService, metaDataService, $fileUploadService) {

            $scope.init = function () {
                $scope.getVendorStatus();
                $scope.currentStatus = null;
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.searchText = {text: ""};
                $scope.paymentCycle = [
                    {id: "DAILY", name: "Daily"},
                    {id: "WEEKLY", name: "Weekly"},
                    {id: "FORTNIGHTLY", name: "Fortnightly"},
                    {id: "MONTHLY", name: "Monthly"}
                ];

                $scope.showViewRequest();

            };

            $scope.changeSearchText = function (searchText) {
              $scope.searchText.text = searchText;
            };

            $scope.byEntityName = function (vendor) {
                if($scope.searchText.text.trim().length <= 3){
                    return true;
                }else{
                    return vendor.entityName.toLowerCase().indexOf($scope.searchText.text.toLowerCase()) != -1;
                }
            };

            $scope.getVendorStatus = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendorStatus
                }).then(function success(response) {
                    $scope.vendorStatusList = response.data;
                    $scope.vendorStatusList.push("ALL");
                    if(!appUtil.isEmptyObject($scope.vendorStatusList)){
                        var excluded = ["APPROVED","FAILED","CANCELLED","EXPIRED"];
                        $scope.vendorStatusList = $scope.vendorStatusList.filter(function(status){
                            return excluded.indexOf(status)==-1;
                        });
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            // $scope.getVendors = function (status) {
            // 	$scope.currentStatus = status;
            //     $http({
            //         method: "POST",
            //         url: apiJson.urls.vendorManagement.vendorByStatus + '/' + status
            //     }).then(function success(response) {
            //         $scope.vendorList = response.data;
            //         $scope.changeSearchText("");
            //     }, function error(response) {
            //         console.log("error:" + response);
            //     });
            // };
            $scope.getSelectedVendor = function (selectedVendor) {
                $scope.vendorList = {};
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.vendorDetails,
                    params : {
                        vendorId: selectedVendor.id
                    }
                }).then(function success(response) {
                    $scope.vendorDetail = response.data;
                    $scope.changeSearchText("");
                }, function error(response) {
                    console.log("error:" + response);
                });
            };


            $scope.showViewRequest = function () {
                var url = apiJson.urls.vendorManagement.allVendorName
                $http({
                    url: url,
                    method: 'GET'
                }).then(function (response) {
                    console.log(response.data);
                    $scope.vendors = response.data;
                }, function (response) {
                    console.log("got error", response);
                });
            };

            $scope.getVendors = function (status) {
                $scope.vendorDetail = null;
                $scope.vendorSelected = null;
                $scope.currentStatus = status;
                $http({
                    method: "POST",
                    url: apiJson.urls.vendorManagement.vendorByShort + '/' + status
                }).then(function success(response) {
                    $scope.vendorList = response.data;
                    $scope.changeSearchText("");
                    $scope.currentPage = 1; // current page
                    $scope.entryLimit = 50; // max no of items to display in a page
                }, function error(response) {
                    console.log("error:" + response);
                });
            };
            $scope.editLeadTime = function (vendorId,LeadTime) {
                var reqobj = {
                    requestId: vendorId,
                    leadTime: LeadTime
                }
                $alertService.confirm("Are you sure?", "You are going to edit this vendor Lead Time. Please be sure!",
                    function (result) {
                        if (result) {
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.saveLeadTime,
                                data: reqobj
                            }).then(function success(response) {
                                $toastService.create("Lead Time is updated successfully");
                                //$scope.getVendors($scope.currentStatus)
                            }, function error(response) {
                                console.log("error:" + response);
                            });
                        }
                    });
            }
            $scope.editCreditCycle = function (companyId, creditCycle) {
                $alertService.confirm("Are you sure?", "You are going to edit this vendor Credit Cycle. Please be sure!",
                    function (result) {
                        if (result) {
                            $http({
                                method: "GET",
                                url: apiJson.urls.vendorManagement.saveCreditCycle,
                                params: {
                                    vendorcompanyId: companyId,
                                    vendorCompCreditCycle: creditCycle
                                }
                            }).then(function success(response) {
                                $toastService.create("Credit Cycle is updated successfully");
                                //$scope.getVendors($scope.currentStatus)
                            }, function error(response) {
                                console.log("error:" + response);
                            });
                        }
                    });
            };

            $scope.sort_by = function (predicate) {
                $scope.predicate = predicate;
                $scope.reverse = !$scope.reverse;
            };

            $scope.isTdsApplicable = function (value, index) {
                $scope.vendorList[index].tds = value;
                if (!value == 'true') {
                    $scope.vendorList[index].tdsDocument = null;
                }
            };

            $scope.uploadTDS = function (index) {
                console.log(index);
                $fileUploadService.openFileModal("Upload TDS scan copy or pdf", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }

                    var fileName = file.name;
                    var fileExt = getFileExtension(fileName);
                    if (fileExt.toLowerCase() == 'pdf' || isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "TDS");
                        fd.append('docType', "VENDOR_TDS");
                        fd.append('mimeType', mimeType);
                        fd.append('vendorId', $scope.vendorList[index].vendorId);
                        fd.append('file', file);
                        fd.append('tdsStatus',$scope.vendorList[index].tds);
                        console.log($scope.vendorList[index].vendorId);
                        $rootScope.showFullScreenLoader = true;
                        $http.post(apiJson.urls.vendorManagement.uploadTdsDocument, fd, {
                            transformRequest: angular.identity,
                            headers: {
                                'Content-Type': undefined
                            }
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!$scope.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.vendorList[index].tdsDocument = response;
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                });
            };

            function isImage(fileExt) {
                return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png";
            };

            function getFileExtension(fileName) {
                var re = /(?:\.([^.]+))?$/;
                return re.exec(fileName)[1];
            }

            $scope.isEmptyObject = function (obj) {
                if (obj != undefined && obj != null) {
                    if (typeof obj == 'string' || typeof obj == 'number')
                        return obj.toString().length == 0;
                    else
                        return Object.keys(obj).length == 0;
                }
                return true;
            };
            $scope.generateEditRequest = function (vd) {
                $alertService.confirm("Are you sure?", "You are going to edit this vendor. Please bbe sure!",
                    function (result) {
                        if (result) {
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.vendorEdit,
                                data: {
                                    vendorId: vd.vendorId,
                                    userId: appUtil.getCurrentUser().userId
                                }
                            }).then(function success(response) {
                                $scope.getVendors($scope.selectedStatus);
                            }, function error(response) {
                                console.log("error:" + response);
                            });
                        }
                    }
                );

            };

            $scope.viewVendor = function (vendor) {
                $state.go('menu.viewVendor', {edit: false, vendor: vendor});
            };

            $scope.sendRONotification = function (){
                console.log("inside send Ro notification");
                $http({
                    method: "GET",
                    url: apiJson.urls.requestOrderManagement.sendVendorRONotification,
                }).then(function success(response) {
                        $toastService.create("Notification sent successfully");
                }, function (error) {
                    console.log(error);
                    $toastService.create("Error in sending notifications");
                })
            }

            $scope.showEditVendor = function (vendor) {
                $state.go('menu.editVendor', {edit: true, vendor: vendor});
            };

            $scope.activateVendor = function () {
                $http({
                    method: "PUT",
                    url: apiJson.urls.vendorManagement.vendorActivate,
                    data: $scope.vendorToEdit.vendorId
                }).then(function success(response) {
                    if (response.data == true) {
                        $scope.vendorToEdit.vendorStatus = "ACTIVE";
                        $scope.vendorList.forEach(function (item) {
                            if (item.vendorId == $scope.vendorToEdit.vendorId) {
                                item = $scope.vendorToEdit;
                            }
                        });
                    } else {
                        $toastService.create("Something went wrong. Please try again.");
                    }
                }, function error(response) {
                    console.log("error:", response);
                });
            };

            $scope.deActivateVendor = function () {
                $http({
                    method: "PUT",
                    url: apiJson.urls.vendorManagement.vendorDeactivate,
                    data: $scope.vendorToEdit.vendorId
                }).then(function success(response) {
                    if (response.data == true) {
                        $scope.vendorToEdit.vendorStatus = "IN_ACTIVE";
                        $scope.vendorList.forEach(function (item) {
                            if (item.vendorId == $scope.vendorToEdit.vendorId) {
                                item = $scope.vendorToEdit;
                            }
                        });
                    } else {
                        $toastService.create("Something went wrong. Please try again.");
                    }
                }, function error(response) {
                    console.log("error:", response);
                });
            };

        }]);

angular.module('scmApp').filter('startFrom', function () {
    return function (input, start) {
        if (input) {
            start = +start; // parse to int
            return input.slice(start);
        }
        return [];
    };
});
