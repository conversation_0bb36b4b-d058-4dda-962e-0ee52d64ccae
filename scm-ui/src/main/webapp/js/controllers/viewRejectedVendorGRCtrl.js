
'use strict';
angular.module('scmApp')
    .controller('viewRejectedVendorGRCtrl', ['$rootScope', '$scope','$stateParams', 'apiJson', '$http', 'appUtil','metaDataService','$fileUploadService',
            '$toastService','$alertService','$timeout','previewModalService', 'PrintService','productService',
            function ($rootScope, $scope,$stateParams, apiJson, $http, appUtil, metaDataService,
                      $fileUploadService,$toastService, $alertService,$timeout, previewModalService, PrintService,productService ) {

                $scope.attributes = [];
                $scope.selectedGRItem = null;
                $scope.init = function (){
                    $scope.availableType = ["REGULAR_ORDER","FIXED_ASSET_ORDER"];
                    $scope.grStatus = ["INITIATED", "CREATED"];
                    $scope.selectedType = null;
                    $scope.selectedStatus = null;
                    productService.getAllProducts(function (products) {
                        $scope.products = products;
                    });
                    var currentDate = appUtil.getCurrentBusinessDate();
                    if(!appUtil.isEmptyObject(currentDate)){
                        $scope.currentUser = appUtil.getCurrentUser();
                        $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                        $scope.endDate = $scope.startDate;
                        $scope.selectedVendor = $stateParams.vendor;
                        $scope.selectedDispatchLocation = $stateParams.dispatchLocation;
                        $scope.grId = $stateParams.grId;
                        $scope.grs = [];
                        $scope.getGRs();
                        $scope.unitData = appUtil.getUnitData();

                    }
                    metaDataService.getVendorsForUnit($scope.currentUser.unitId,function(vendorsForUnit){
                        $scope.vendors = vendorsForUnit;
                    });
                };

                $scope.selectVendor = function (vendor) {
                    $scope.selectedVendor = vendor;
                };

                $scope.getGRs = function(){
                    var params = {
                        deliveryUnitId:$scope.currentUser.unitId,
                        startDate:$scope.startDate,
                        endDate:$scope.endDate,
                        goodReceivedId:$scope.grId,
                    };

                    if(appUtil.isEmptyObject($scope.startDate)){
                        $toastService.create("Please select a start date first");
                        return;
                    }
                    if(appUtil.isEmptyObject($scope.endDate)){
                        $toastService.create("Please select a end date first");
                        return;
                    }

                    if(!appUtil.isEmptyObject($scope.selectedVendor)){
                        params["vendorId"] = $scope.selectedVendor.vendorId;
                    }

                    $http({
                        method:'GET',
                        url:apiJson.urls.goodsReceivedManagement.findVendorReceivingsRejected,
                        params:params
                    }).then(function(response){
                        if (!appUtil.isEmptyObject(response.data)) {
                            // $scope.grs = response.data;
                            $scope.grsList = response.data;
                            $scope.grs = $scope.grsList.filter(function (gr) {
                                return gr.vendorPoGRItems != null;
                            });
                        } else {
                            $scope.grs = [];
                            $scope.grsList = [];
                        }
                    },function(error){
                        console.log(error);
                    });
                };
            }
        ]
    );