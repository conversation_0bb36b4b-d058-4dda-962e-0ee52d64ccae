

'use strict';
angular.module('scmApp')
    .controller('viewVendorGRCtrl', ['$rootScope', '$scope','$stateParams', 'apiJson', '$http', 'appUtil','metaDataService','$fileUploadService',
        '$toastService','$alertService','$timeout','previewModalService', 'PrintService','productService',
        function ($rootScope, $scope,$stateParams, apiJson, $http, appUtil, metaDataService,
                                $fileUploadService,$toastService, $alertService,$timeout, previewModalService, PrintService,productService ) {

            $scope.attributes = [];
            $scope.selectedGRItem = null;
            $scope.init = function () {
                $scope.availableType = ["REGULAR_ORDER", "FIXED_ASSET_ORDER"];
                $scope.grStatus = ["INITIATED", "CREATED"];
                $scope.selectedType = null;
                $scope.selectedStatus = null;
                $scope.selectedUser = null;
                $scope.selectedUserId = null;
                $scope.createdByList = [];
                $scope.createdByIdList = [];
                // JsBarcode(".barcode").init();
                productService.getAllProducts(function (products) {
                    $scope.products = products;
                });
                var currentDate = appUtil.getCurrentBusinessDate();
                $scope.attribute = {};
                $scope.attribute.fontSize = 0;
                $scope.attribute.height = 0;
                $scope.attribute.width = 0;
                $scope.attribute.marginRight = 0;
                $scope.attribute.marginLeft = 0;


                if (!appUtil.isEmptyObject(currentDate)) {
                    $scope.currentUser = appUtil.getCurrentUser();
                    $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                    $scope.endDate = $scope.startDate;
                    $scope.selectedVendor = $stateParams.vendor;
                    $scope.selectedDispatchLocation = $stateParams.dispatchLocation;
                    $scope.grId = $stateParams.grId;
                    $scope.grs = [];
                    $scope.getGRs();
                    $scope.unitData = appUtil.getUnitData();

                }
                metaDataService.getVendorsForUnit($scope.currentUser.unitId, function (vendorsForUnit) {
                    $scope.vendors = vendorsForUnit;
                });

                metaDataService.getSkuListForUnit($scope.currentUser.unitId, function (skuForUnitList) {
                    $scope.skus = skuForUnitList;
                });
                $scope.showPreview = previewModalService.showPreview;
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.lifeTimeType = [
                    "DAYS",
                    "MONTHS",
                    "YEARS"
                ];
                getAttrValues();
                metaDataService.getAttrDefinitions(function (attributes) {
                    var data = attributes;
                    for (var key in data) {
                        var subArray = data[key];
                        $scope.attributes = $scope.attributes.concat(subArray);
                    }
                });
                PrintService.loadQZPrinter();
                $scope.isBulkGRAllowed = false;
                productService.getAllProducts(function (products) {
                    $scope.products = products;
                });
            };

            $scope.downloadExcell = function (){
                var params = {
                    className : "com.stpl.tech.scm.domain.model.VendorGR"
                }
                var jsonStrings = [];
                for(var i = 0;i<$scope.grsList.length;i++){
                    if($scope.grsList[i].selected){
                        jsonStrings.push(JSON.stringify($scope.grsList[i])); 
                    }
                }
                metaDataService. downloadExcell(jsonStrings,params);
            }


            $scope.printCode = function (val) {
                var codeObj = {}
                var code = {}
                code.assetName = "Rahul Singh";
                code.subCategoryCode = "Technology";
                code.assetTagValue = "1511118";
                PrintService.printBarCode(code);
            };

            $scope.selectGRItem = function (grItem) {
                $scope.selectedGRItem = grItem;
                $scope.selectedAssets = grItem.selectedAsset;
                getIsBulkGRAllowed();
            };

            function getAttrValues() {
                metaDataService.getAttributeValues(function (values) {
                    $scope.attributeValueMap = values;
                });
            }

            $scope.getAttributeName = function (id) {
                for (var i in $scope.attributes) {
                    if ($scope.attributes[i].attributeId == id) {
                        return $scope.attributes[i].attributeName;
                    }
                }
            };

            $scope.selectAll = function (value) {
                for (var index in $scope.selectedAssets) {
                    if ($scope.selectedAssets[index].assetStatus == 'INITIATED') {
                        $scope.selectedAssets[index].selected = value;
                    }
                }
            };

            $scope.filterData = function () {
                console.log("Selected Filters are : ",$scope.selectedType,$scope.selectedStatus,$scope.selectedUser,$scope.selectedUserId);
                var finalGrs = [];
                var check = false;
                if ($scope.selectedType != null) {
                    check = true;
                    finalGrs = $scope.grs.filter(function (gr) {
                        return gr.vendorGrType == $scope.selectedType;
                    });
                }
                if ($scope.selectedStatus != null) {
                    if (check) {
                        finalGrs = finalGrs.filter(function (gr) {
                            return gr.status == $scope.selectedStatus;
                        });
                    }
                    else {
                        finalGrs = $scope.grs.filter(function (gr) {
                            return gr.status == $scope.selectedStatus;
                        });
                    }
                    check = true;
                }
                if ($scope.selectedUser != null) {
                    if (check) {
                        finalGrs = finalGrs.filter(function (gr) {
                            return gr.generatedBy.name == $scope.selectedUser;
                        });
                    }
                    else {
                        finalGrs = $scope.grs.filter(function (gr) {
                            return gr.generatedBy.name == $scope.selectedUser;
                        });
                    }
                    check = true;
                }
                if ($scope.selectedUserId != null) {
                    if (check) {
                        finalGrs = finalGrs.filter(function (gr) {
                            return gr.generatedBy.id == $scope.selectedUserId;
                        });
                    }
                    else {
                        finalGrs = $scope.grs.filter(function (gr) {
                            return gr.generatedBy.id == $scope.selectedUserId;
                        });
                    }
                    check = true;
                }
                $scope.grsList = check == false ? $scope.grs : finalGrs;
             };

            $scope.setValueForAll = function(fieldName, fieldValue, isInternal, internalFieldValue) {
                if(isInternal) {
                    for(var index in $scope.selectedAssets) {
                        if($scope.selectedAssets[index].assetStatus == 'INITIATED' && $scope.selectedAssets[index].selected == true){
                            for(var i in $scope.selectedAssets[index].entityAttributeValueMappings) {
                                if($scope.selectedAssets[index].entityAttributeValueMappings[i].attributeId == internalFieldValue ) {
                                    $scope.selectedAssets[index].entityAttributeValueMappings[i].attributeValueId = fieldValue;
                                }
                            }
                        }
                    }
                } else {
                    for(var index in $scope.selectedAssets) {
                        if($scope.selectedAssets[index].assetStatus == 'INITIATED' && $scope.selectedAssets[index].selected == true){
                            $scope.selectedAssets[index][fieldName] = fieldValue;
                            // $scope.changeTypeAndValue($scope.selectedAssets[index].assetId);
                        }
                    }
                }

            };

            $scope.selectVendor = function (vendor) {
                $scope.selectedVendor = vendor;
            };

            $scope.createAsset = function (grItem,gr) {
                $scope.selectedGRItem = grItem;
                $scope.selectedGR = gr;
                var assetDefinitionArray = [];
                var index = 0;
                for(index = 0 ; index < grItem.receivedQuantity ; index++) {
                    var assetDefinition = createAssetObj();
                    assetDefinition.assetName = $scope.selectedGRItem.skuName;
                    assetDefinition.unitId = $scope.selectedGR.deliveryUnitId.id;
                    assetDefinition.unitType = $scope.selectedGR.deliveryUnitId.category;
                    if( $scope.selectedGR.deliveryUnitId.category == "WAREHOUSE") {
                        assetDefinition.assetStatus = "INITIATED";
                    } else if ($scope.selectedGR.deliveryUnitId.category == "CAFE") {
                        assetDefinition.assetStatus = "INITIATED";
                    } else {
                        assetDefinition.assetStatus = "INITIATED";
                    }
                    assetDefinition.skuId =  $scope.selectedGRItem.skuId;
                    assetDefinition.grId = $scope.selectedGR.id;
                    assetDefinition.grItemId =  $scope.selectedGRItem.id;
                    assetDefinition.vendorId = $scope.selectedGR.generatedForVendor.id;
                    assetDefinition.vendorName = $scope.selectedGR.generatedForVendor.name;
                    // making unit(location) and owner same
                    assetDefinition.ownerType = $scope.selectedGR.deliveryUnitId.category;
                    assetDefinition.ownerId = $scope.selectedGR.deliveryUnitId.id;
                    assetDefinition.firstOwnerType = $scope.selectedGR.deliveryUnitId.category;
                    assetDefinition.firstOwnerId = $scope.selectedGR.deliveryUnitId.id;
                    assetDefinition.tagType = "QR_CODE";
                    assetDefinition.tagValue = null;
                    assetDefinition.tagPrintCount = 0;
                    assetDefinition.lastTagPrintDate = null;
                    assetDefinition.lastTagPrintedBy = null;
                    assetDefinition.price = $scope.selectedGRItem.unitPrice;
                    assetDefinition.tax = $scope.selectedGRItem.totalTax / $scope.selectedGRItem.receivedQuantity;
                    assetDefinition.quantity = 1;
                    assetDefinition.taxPercentage = ($scope.selectedGRItem.totalTax * 100)/$scope.selectedGRItem.totalCost;
                    assetDefinition.createdBy = appUtil.createGeneratedBy();
                    assetDefinitionArray.push(assetDefinition);
                }
                generateAsset(assetDefinitionArray,grItem);
            };

            function generateAsset(assetDefinitionArray,grItem) {
                $http({
                    url: apiJson.urls.assetManagement.addAssets,
                    method: 'POST',
                    data: assetDefinitionArray,
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    $scope.selectedAssets = response.data;
                    for(var index in $scope.selectedAssets){
                        var profileAttributeMapping = $scope.selectedAssets[index].profileAttributeMappingList;
                        var resultantMapping = [];
                        $scope.selectedAssets[index].entityAttributeValueMappings = [];
                        for(var i in profileAttributeMapping){
                            if((profileAttributeMapping[i].definedAtAsset || profileAttributeMapping[i].overridableAtSKU )
                                && profileAttributeMapping[i].status == 'ACTIVE') {
                                resultantMapping.push(profileAttributeMapping[i]);
                                var valueList = angular.copy($scope.attributeValueMap[profileAttributeMapping[i].attributeId]);
                                profileAttributeMapping[i].valueList = valueList;
                                $scope.selectedAssets[index].entityAttributeValueMappings[i] = generateEntityAttributeValueMapping(profileAttributeMapping[i],
                                    $scope.selectedAssets[index], valueList);
                            }
                        }
                        $scope.selectedAssets[index].profileAttributeMappingList = resultantMapping;
                        $scope.selectedAssets[index].lifeTimeType=$scope.lifeTimeType[1];
                        $scope.selectedAssets[index].lifeTimeValue=$scope.selectedAssets[index].lifeTimeCategoryMonths;

                    }
                    grItem.selectedAsset = $scope.selectedAssets;
                    getIsBulkGRAllowed();
                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create(response.data.errorMessage);
                });
            }

            function getIsBulkGRAllowed(){
                if($scope.selectedAssets == null || $scope.selectedAssets.length == 0){
                    $scope.isBulkGRAllowed = false;
                }else {
                    var productId = $scope.selectedAssets[0].productId;
                    for(var index in $scope.products){
                        if($scope.products[index].productId == productId){
                            $scope.isBulkGRAllowed = true;
                            return;
                        }
                    }
                }
                $scope.isBulkGRAllowed = false;
            }

            $scope.isFixedAssetGR = function(gr) {

                var counter = 0;
                for(var i in gr.grItems) {
                    if(gr.grItems[i].category == 'Fixed Assets'){

                        counter++;
                    }
                }
                if(counter > 0){
                    return true;
                }
                return false;
            };

            $scope.changeTypeAndValue = function(assetId){
                for(var i in $scope.selectedAssets) {
                    if($scope.selectedAssets[i].assetId == assetId){
                        if($scope.selectedAssets[i].lifeTimeType==$scope.lifeTimeType[1]){
                            $scope.selectedAssets[i].lifeTimeValue = parseInt($scope.selectedAssets[i].lifeTimeCategoryDays/30);}
                        else if($scope.selectedAssets[i].lifeTimeType==$scope.lifeTimeType[2]){
                            $scope.selectedAssets[i].lifeTimeValue = $scope.selectedAssets[i].lifeTimeCategoryDays/365}
                        else if($scope.selectedAssets[i].lifeTimeType==$scope.lifeTimeType[0]){
                            $scope.selectedAssets[i].lifeTimeValue = $scope.selectedAssets[i].lifeTimeCategoryDays;}
                    }
                }
                console.log($scope.selectedAssets);
            }


            $scope.selectGr = function(gr) {
                $scope.selectedGR = gr;
                for(var i in gr.grItems) {
                    $scope.getGRItemStatus(gr, gr.grItems[i]);
                }
            };

            $scope.getGRItemStatus = function(selectedGR, grItem) {
                $http({
                    url: apiJson.urls.assetManagement.getAssetsAssociatedWithGRItem,
                    method: 'GET',
                    params : {
                        grItemId : grItem.id
                    },
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    if(response.data.length == 0){
                        grItem.status = 'Create Asset';
                    } else if (response.data.length > 0){
                        var counter = 0;
                        $scope.selectedAssets = response.data;
                        for(var index in $scope.selectedAssets){

                            // TODO ABHISHEK - REFACTOR THIS SHIT
                            if($scope.selectedAssets[index].assetStatus == 'INITIATED'){
                                /*
                                    Allot mapping
                                 */
                                var profileAttributeMapping = $scope.selectedAssets[index].profileAttributeMappingList;
                                var validProfileAttributeMapping = [];
                                $scope.selectedAssets[index].entityAttributeValueMappings = [];
                                for(var i in profileAttributeMapping){
                                    if((profileAttributeMapping[i].definedAtAsset || profileAttributeMapping[i].overridableAtSKU )
                                        && profileAttributeMapping[i].status == 'ACTIVE') {
                                        validProfileAttributeMapping.push(profileAttributeMapping[i]);
                                        var valueList = angular.copy($scope.attributeValueMap[profileAttributeMapping[i].attributeId])
                                        profileAttributeMapping[i].valueList = valueList;
                                        $scope.selectedAssets[index].entityAttributeValueMappings[i] = generateEntityAttributeValueMapping(profileAttributeMapping[i],
                                            $scope.selectedAssets[index], valueList);
                                    }
                                }
                                $scope.selectedAssets[index].profileAttributeMappingList = validProfileAttributeMapping;
                                counter ++;
                            } else {
                                /*
                                    For already generated assets
                                 */
                                if($scope.selectedAssets[index].warrantyLastDate != null){
                                    $scope.selectedAssets[index].warrantyLastDate = new Date($scope.selectedAssets[index].warrantyLastDate);
                                }

                                if($scope.selectedAssets[index].insuranceLastDate != null) {
                                    $scope.selectedAssets[index].insuranceLastDate = new Date($scope.selectedAssets[index].insuranceLastDate);
                                }

                                if($scope.selectedAssets[index].amcLastDate != null){
                                    $scope.selectedAssets[index].amcLastDate = new Date($scope.selectedAssets[index].amcLastDate);
                                }

                                var profileAttributeMapping = $scope.selectedAssets[index].profileAttributeMappingList;
                                var entityAttributeValueMappings = $scope.selectedAssets[index].entityAttributeValueMappings != null ?
                                    $scope.selectedAssets[index].entityAttributeValueMappings : [];
                                for(var i in profileAttributeMapping){
                                    var found = false;
                                    for(var j in entityAttributeValueMappings) {
                                        if(profileAttributeMapping[i].attributeId == entityAttributeValueMappings[j].attributeId) {
                                            var valueList = angular.copy($scope.attributeValueMap[entityAttributeValueMappings[j].attributeId])
                                            profileAttributeMapping[i].valueList = valueList;
                                            $scope.selectedAssets[index].entityAttributeValueMappings[j].valueList = valueList;
                                            found = true;
                                            break;
                                        }
                                    }
                                    if(!found) {
                                        var valueList = angular.copy($scope.attributeValueMap[profileAttributeMapping[i].attributeId])
                                        profileAttributeMapping[i].valueList = valueList;
                                        var emptyMapping = generateEntityAttributeValueMapping(profileAttributeMapping[i],
                                            $scope.selectedAssets[index], valueList);
                                        entityAttributeValueMappings.splice(i,0,emptyMapping);

                                    }
                                }
                                $scope.selectedAssets[index].entityAttributeValueMappings = entityAttributeValueMappings;

                            }
                            $scope.selectedAssets[index].lifeTimeType=$scope.lifeTimeType[1];
                            $scope.selectedAssets[index].lifeTimeValue=$scope.selectedAssets[index].lifeTimeCategoryMonths;
                        }
                        grItem.status = counter > 0 ? 'Continue' : 'Generated';
                        grItem.selectedAsset = $scope.selectedAssets;
                        $scope.selectedAssets = null;

                    }
                }, function error(response) {
                    console.log("error:" + response);
                    grItem.status = 'Create Asset';
                });
            };

            function generateEntityAttributeValueMapping(profileAttributeMapping, asset, valueList) {
                var mapping = {};
                mapping.entityAttributeValueMappingId = null;
                mapping.profileId =  profileAttributeMapping.profileId;
                mapping.attributeId = profileAttributeMapping.attributeId;
                mapping.attributeValueId = null;
                mapping.profileAttributeMappingId = profileAttributeMapping.profileAttributeMappingId;
                mapping.entityType = "ASSET";
                mapping.entityId = asset.assetId;
                mapping.creationDate = null;
                mapping.createdBy =  appUtil.createGeneratedBy();
                mapping.status = "ACTIVE";
                mapping.valueList = valueList; // temporary property to store list of values associated with particular attribute
                mapping.isMandatory = profileAttributeMapping.mandatoryAtAsset;
                mapping.standAlone = profileAttributeMapping.standAlone;
                mapping.attributeValue = null;// this field will be used in case if profile attribute mapping is standalone
                return mapping;
            }

            function createAssetObj() {
                var assetDefinition = {};
                assetDefinition.assetId = null;
                assetDefinition.assetName = null;
                assetDefinition.unitId = null;
                assetDefinition.unitType = null;
                assetDefinition.assetStatus = null;
                assetDefinition.skuId = null;
                assetDefinition.grId = null;
                assetDefinition.grItemId = null;
                assetDefinition.vendorId = null
                assetDefinition.vendorName = null;
                assetDefinition.ownerType = null;
                assetDefinition.ownerId = null;
                assetDefinition.firstOwnerType = null;
                assetDefinition.firstOwnerId = null;
                assetDefinition.tagType = null;
                assetDefinition.tagValue = null;
                assetDefinition.tagPrintCount = 0;
                assetDefinition.lastTagPrintDate = null;
                assetDefinition.lastTagPrintedBy = null;
                assetDefinition.price = null;
                assetDefinition.tax = null;
                assetDefinition.taxPercentage = 0;
                assetDefinition.quantity = 0;
                assetDefinition.createdBy = null;
                return assetDefinition;
            }

            function validateEnteredValue(asset) {
                if(asset.lifeTimeType == null){
                    $toastService.create("Please select life time type");
                    return true;
                }
                if(asset.lifeTimeValue == null){
                    $toastService.create("Please select life time value");
                    return true;
                }
                if(asset.hasWarranty && asset.warrantyLastDate == null){
                    $toastService.create("Please select warranty last date first");
                    return true;
                }
                if(asset.hasAMC && asset.amcLastDate == null){
                    $toastService.create("Please select AMC last date first");
                    return true;
                }
                if(asset.hasInsurance && asset.insuranceLastDate == null){
                    $toastService.create("Please select insurance last date first");
                    return true;
                }
                if((asset.uniqueFieldName != null && asset.uniqueFieldName.length > 0)
                    && (asset.uniqueFieldValue == null || asset.uniqueFieldValue.length == 0)){
                    $toastService.create("Please enter unique Field value First");
                    return true;
                }
                for(var index in asset.entityAttributeValueMappings) {
                    if(asset.entityAttributeValueMappings[index].isMandatory
                        && asset.entityAttributeValueMappings[index].attributeValueId == null
                    && asset.entityAttributeValueMappings[index].standAlone == false){
                        $toastService.create("Please Select Attribute Value for "
                            + $scope.getAttributeName(asset.entityAttributeValueMappings[index].attributeId));
                        return true;
                    }
                }
                for(var index in asset.entityAttributeValueMappings) {
                    if((asset.entityAttributeValueMappings[index].attributeValue == null || asset.entityAttributeValueMappings[index].attributeValue.length == 0)
                        && asset.entityAttributeValueMappings[index].standAlone == true){
                        $toastService.create("Please Enter Attribute Value for "
                            + $scope.getAttributeName(asset.entityAttributeValueMappings[index].attributeId));
                        return true;
                    }
                }
            }

            $scope.printTag = function(asset){
                if(validateEnteredValue(asset)){
                    return;
                }
                asset.lastTagPrintedBy = appUtil.createGeneratedBy();
                for(var i in asset.entityAttributeValueMappings){
                    if(asset.entityAttributeValueMappings[i].isMandatory == false && asset.entityAttributeValueMappings[i].attributeValueId == null){

                    }
                }
              //  asset.assetStatus="CREATED";
                $http({
                    url: apiJson.urls.assetManagement.completeAssetGeneration,
                    method: 'PUT',
                    data: asset,
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    var recievedAsset = response.data;
                    asset.assetStatus = 'CREATED';
                    var code = {}
                    code.assetName = asset.assetName;
                    for(var i in $scope.products){
                        if($scope.products[i].productId == asset.productId){
                            code.subCategoryCode = $scope.products[i].subCategoryDefinition.code;
                        }
                    }
                    code.assetTagValue = asset.tagValue
                    PrintService.printBarCode(code);
                    $scope.approveVendorGR();
                }, function error(response) {
                    console.log("error:" + response);
                    asset.assetStatus = 'INITIATED';
                });
            };

            $scope.approveVendorGR = function() {
                var allAssetCreated = true;
                for(var i in $scope.selectedGR.grItems) {
                    var grItem = $scope.selectedGR.grItems[i];
                    if($scope.selectedGRItem.id == grItem.id) {
                        for(var j in $scope.selectedGR.grItems[i].selectedAsset) {
                            var asset = $scope.selectedGR.grItems[i].selectedAsset[j];
                            if(asset.assetStatus == 'INITIATED'){
                                allAssetCreated = false;
                            }
                        }
                    } else {
                        if(grItem.status == 'Create Asset' || grItem.status == 'Continue') {
                            allAssetCreated = false;
                            break;
                        }
                    }
                }

                if(allAssetCreated == true) {
                    $http({
                        url: apiJson.urls.goodsReceivedManagement.approveVendorGR,
                        method: 'POST',
                        params: {
                            grId : $scope.selectedGR.id,
                            userId : appUtil.createGeneratedBy().id
                        },
                        headers: {"Content-Type": "application/json"}
                    }).then(function success(response) {
                        if(response.data == true) {
                            $scope.selectedGR.status = 'CREATED';
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        if (response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                        }
                        else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    });
                }
            };

            $scope.reset = function() {
                document.getElementById("modal1closebtn").click();
                // $scope.selectedAssets = [];
            };

            $scope.selectSKU = function (sku) {
                $scope.selectedSKU = sku;
            };


            $scope.getGRs = function(){
                var params = {
                    deliveryUnitId:$scope.currentUser.unitId,
                    startDate:$scope.startDate,
                    endDate:$scope.endDate,
                    goodReceivedId:$scope.grId,
                };

                if(appUtil.isEmptyObject($scope.startDate)){
                    $toastService.create("Please select a start date first");
                    return;
                }
                if(appUtil.isEmptyObject($scope.endDate)){
                    $toastService.create("Please select a end date first");
                    return;
                }

                if(!appUtil.isEmptyObject($scope.selectedVendor)){
                    params["vendorId"] = $scope.selectedVendor.vendorId;
                }

                if(!appUtil.isEmptyObject($scope.selectedSKU)){
                    params["skus"] = [$scope.selectedSKU];
                }

                $http({
                    method:'GET',
                    url:apiJson.urls.goodsReceivedManagement.findVendorReceivings,
                    params:params
                }).then(function(response){
                    if(!appUtil.isEmptyObject(response.data)){
                        $scope.grs = response.data;
                        $scope.grsList = $scope.grs;
                        $scope.resetAndFilter($scope.grs);
                    }else{
                        $scope.grs = [];
                        $scope.grsList = [];
                    }
                },function(error){
                    console.log(error);
                });
            };

            $scope.createUsersList = function (grs) {
                $scope.createdByList = [];
                $scope.createdByIdList = [];
                angular.forEach(grs,function (gr) {
                    if ($scope.createdByList.indexOf(gr.generatedBy.name) < 0) {
                        $scope.createdByList.push(gr.generatedBy.name);
                    }
                    if ($scope.createdByIdList.indexOf(gr.generatedBy.id) < 0) {
                        $scope.createdByIdList.push(gr.generatedBy.id);
                    }
                });
            };

            $scope.resetAndFilter = function (grs) {
                $timeout(function() {
                    $('#selectedTypes').val(null).trigger('change');
                    $('#selectedUserId').val(null).trigger('change');
                    $('#selectedStatus').val(null).trigger('change');
                    $('#selectedUser').val(null).trigger('change');
                    $scope.createUsersList(grs);
                });
            };

            $scope.selectAllReceiving = function () {
                //$scope.selectAllGrs = !$scope.selectAllGrs;
                $scope.grs.map(function (gr) {
                    gr.selected = $scope.selectAllGrs;
                })
            };

            $scope.downloadDebitNote = function (debitaNote) {
                metaDataService.downloadDocument(debitaNote);
            };

            $scope.printGR = function(gr){
                $scope.currentPrintGR = gr;
                $scope.currentPrintGR.total = parseFloat(parseFloat($scope.currentPrintGR.billAmount)+
                    parseFloat($scope.currentPrintGR.extraCharges!=null ? $scope.currentPrintGR.extraCharges : 0)).toFixed(2);
                $timeout(function() {
                    angular.element('#printDiv').trigger('click');
                });
            };

            $scope.filteredByReceivedQuantity = function(item){
        	    return item.receivedQuantity != null && item.receivedQuantity > 0;
            };

            $scope.cancelGR = function(grId){
                $alertService.confirm("Are you sure?",
                    "You are going to cancel this GR.",
                    function(result){
                        if(result){
                            $http({
                                method:'POST',
                                url:apiJson.urls.goodsReceivedManagement.cancelVendorGR + "/" + grId + "/" + $scope.currentUser.userId
                            }).then(function(response){
                                if(!appUtil.isEmptyObject(response) && response.data){
                                    $toastService.create("Congratulations! GR cancelled successfully!", $scope.getGRs);
                                }
                            },function(error){
                                console.log(error);
                                if (error.data.errorMsg != null) {
                                    $alertService.alert(error.data.errorTitle, error.data.errorMsg, null, true);
                                }
                                else {
                                    $toastService.create("Something went wrong. Please try again!");
                                }
                            });
                        }
                    }
                );
            };

            $scope.bulkGenerate = function(){
                for(var index in $scope.selectedAssets) {
                    if($scope.selectedAssets[index].assetStatus == 'INITIATED'){
                        $scope.printTag($scope.selectedAssets[index]);
                    }
                }
            };

            $scope.setGRsForNoPayment = function () {
                var grIds = [];
                var error = false;
                $scope.grs.map(function (gr) {
                    if(gr.selected === true){
                        if (!error) {
                            if (gr.status !== "CREATED") {
                                $toastService.create("GR id: " + gr.id + " is not valid for non payment.");
                                error = true;
                            } else if(gr.paymentRequestId != null ){

                            }  else if(gr.toBePaid != true ){
                                /*$toastService.create("GR id: " + gr.id + " is already marked for no payment.");
                                error = true;*/
                            } else {
                                grIds.push(gr.id);
                            }
                        }
                    }
                });
                if(grIds.length == 0){
                    $toastService.create("No GR selected for non payment!");
                } else if(!error){
                    $http({
                        method:'POST',
                        url:apiJson.urls.goodsReceivedManagement.setVendorGRsForNoPayment,
                        data:{
                            ids:grIds,
                            actionBy:appUtil.createGeneratedBy().id
                        }
                    }).then(function(response){
                        if(response.data == true){
                            $toastService.create("GRs updated successfully!");
                            $scope.getGRs();
                            $scope.selectAllGrs = false;
                        }else{
                            $toastService.create("Error processing request. Please try again!");
                        }
                    },function(error){
                        if (error.data != null && error.data.errorMsg != null) {
                            $alertService.alert(error.data.errorTitle, error.data.errorMsg, null, true);
                        } else {
                            console.log(error);
                            $toastService.create("Error processing request. Please try again!");
                        }
                    });
                }
            }
        }
    ]
);
