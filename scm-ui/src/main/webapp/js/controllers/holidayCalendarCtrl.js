'use strict';

angular.module('scmApp')
    .controller(
        'holidayCalendarCtrl',
        [
            '$rootScope',
            '$scope',
            'apiJson',
            '$http',
            'appUtil',
            '$toastService',
            '$alertService',
            function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,
                    $alertService) {
                $scope.init = function () {
                    $scope.holidayTypes = ["Bank Holiday","Company Holiday"];
                    $scope.selectedYear = null;
                    $scope.selectedAction=null;
                    $scope.selectedHolidayType=null;
                    $scope.selectedDate=null;
                    $("#holidayModal").hide();

                };

                $scope.setSelectedAction = function(action){
                    $scope.selectedAction = action;
                };

                $scope.setSelectedYear = function(year){
                  $scope.selectedYear = year;
                  console.log("selected year is : ",$scope.selectedYear);
                };

                $scope.setSelectedHolidayType = function(type){
                  $scope.selectedHolidayType = type;
                  console.log("holiday Type : ",$scope.selectedHolidayType);
                };

                $scope.setSelectedDate = function(date){
                  $scope.selectedDate=date;
                  console.log($scope.selectedDate);
                };

                $scope.addHoliday = function () {
                    $("#holidayModal").show();
                };

                $scope.mainScreen = function () {
                    $scope.selectedDate = null;
                    $scope.selectedHolidayType = null;
                    $("#holidayModal").hide();
                };

                $scope.clearSearch = function(){
                    $scope.selectedYear = null;
                    $scope.selectedAction=null;
                    $scope.holidaysList = [];
                };

                $scope.submitAddHoliday = function () {
                    if($scope.selectedHolidayType == null || $scope.selectedHolidayType == undefined){
                        $toastService.create("Please select the Holiday Type");
                        return;
                    }
                    if($scope.selectedDate == null || $scope.selectedDate == undefined){
                        $toastService.create("Please select the date");
                        return;
                    }
                    var date = new Date($scope.selectedDate);
                    console.log("date is : ",date);
                    var user = appUtil.createGeneratedBy();
                    var obj={
                        holidayType : $scope.selectedHolidayType,
                        holidayDate : date,
                        holidayYear : date.getFullYear(),
                        holidayMonth : date.getMonth()+1,
                        status : "ACTIVE",
                        createdBy : user.name + "["+user.id + "]"
                    };
                    console.log("obj is : ",obj);
                    $alertService.confirm("Are you sure?",
                        "Do you want to add : "+$scope.selectedHolidayType+" on "+date.getDate()+"/"+(date.getMonth()+1)+"/"+date.getFullYear() ,
                        function (yes) {
                            if (yes) {
                                $http({
                                    method: 'POST',
                                    url: apiJson.urls.paymentRequestManagement.addHoliday,
                                    data: obj
                                }).then(function success(response) {
                                    if (response.status === 200 && response.data === true) {
                                        $toastService.create("Holiday added to the database successfully!");
                                        $scope.listOfHolidays();
                                    } else {
                                        $toastService.create("Selected Date is already added as Holiday in Database!");
                                    }
                                    $rootScope.showFullScreenLoader = false;
                                }, function error(response) {
                                    $toastService.create("Error in getting response");
                                    console.log("Error in getting response:" + response);
                                    $rootScope.showFullScreenLoader = false;
                                });
                                $scope.selectedDate = null;
                                $scope.selectedHolidayType = null;
                                $("#holidayModal").hide();
                            } else{
                                return;
                            }
                        });
                };

                $scope.listOfHolidays = function(){
                    $http({
                        method: 'GET',
                        url: apiJson.urls.paymentRequestManagement.listOfHolidays,
                        params: {
                            holidayType : ($scope.selectedAction !=null || $scope.selectedAction !=undefined)?$scope.selectedAction:null,
                            year : ($scope.selectedYear !=null || $scope.selectedYear !=undefined)?$scope.selectedYear:null
                        }
                    }).then(function success(response) {
                        if (response.status === 200 ) {
                            if(response.data != null && response.data.length > 0) {
                                $scope.holidaysList = response.data;
                            } else{
                                $scope.holidaysList = [];
                                $toastService.create("No entries Found in the data base");
                            }

                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        $toastService.create("Error in getting response");
                        console.log("Error in getting response:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                };

                $scope.setDate = function (date) {
                  $scope.selectedDate = date;
                };

                $scope.changeHolidayStatus = function (d,s) {
                    var date = new Date(d);
                    console.log("date is change status: ",date);
                    console.log("status is : ",s);
                    var user = appUtil.createGeneratedBy();
                    var obj={
                        holidayDate: date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate(),
                        updatedBy: user.name + "["+user.id + "]",
                        activateOrDeactivate: s == "ACTIVE" ? false: true
                    };
                    console.log("object is :",obj);
                    $http({
                        method: 'PUT',
                        url: apiJson.urls.paymentRequestManagement.activateOrDeactivateHoliday,
                        data:obj
                    }).then(function success(response) {
                        if (response.status === 200 && response.data === true) {
                            $toastService.create("Status Updated successfully!");
                            $scope.listOfHolidays();
                        } else {
                            $toastService.create("Error in updating Status!");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        $toastService.create("Error in getting response");
                        console.log("Error in getting response:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                };

            }
        ]
);