/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('trOrderActionCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location','$toastService','$alertService','metaDataService','previewModalService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, $alertService,metaDataService,previewModalService) {

            $scope.init = function () {
                $scope.selectedTrOrderId = $rootScope.selectedTrOrderId;
                $scope.getTrOrderDetail();
                $scope.unitData = appUtil.getUnitData();
                //$scope.scmProductDetails = appUtil.getScmProductDetails();
                $scope.transferOrderDetail = null;
                $scope.ewayBillNumber = null;
                $scope.unitList = appUtil.getUnitList();
                $scope.isCafe = appUtil.isCafe();
                $scope.formatDate = appUtil.formatDate;
                $scope.today = new Date();
                $scope.generatedForUnitData = null;
                $scope.totalPrice = null;
                $scope.totalPriceInWords = '';
                $scope.showPreview = previewModalService.showPreview;
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.previousScreen
            };

            $scope.backToTrOrderMgt = function(){
                //$location.path("/menu/trOrderMgt");
                var previousUrl = appUtil.isEmptyObject($rootScope.previousScreen) ? "/menu/trOrderMgt" : $rootScope.previousScreen ;
                $location.path(
                    previousUrl
                );
            };

            $scope.acknowledge = function(tr){
                $alertService.confirm("Are you sure?","You are going to acknowledge external transfer.", function(response){
                    if(response){
                        $http({
                            method: "POST",
                            url: apiJson.urls.transferOrderManagement.approveExternalTransfer,
                            data:{
                                id:$scope.selectedTrOrderId,
                                userId:appUtil.getCurrentUser().userId
            }
                        }).then(function(response) {
                            if(!appUtil.isEmptyObject(response) && response.data){
                                $toastService.create("Successfully approved!");
                                $scope.transferOrderDetail.externalTransferDetail.status="ACKNOWLEDGED";
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    }
                });
            };

            $scope.getTrOrderDetail = function(){
            	$scope.getewayBill();
            }

            $scope.getTrOrderDetailed = function(){
            	//$scope.getewayBill();
                if(!angular.isUndefined($scope.selectedTrOrderId) && $scope.selectedTrOrderId!=null){
                    $http({
                        method: "GET",
                        url: apiJson.urls.transferOrderManagement.transferOrder+"?transferOrderId="+$scope.selectedTrOrderId
                    }).then(function success(response) {
                        console.log(response.data);
                        $scope.transferOrderDetail = response.data;
                        $scope.generatedForUnitData = appUtil.findUnitDetail($scope.transferOrderDetail.generatedForUnitId.id);
                        $scope.calculateTotalPrice();
                        //$scope.ewayBillNumber = $scope.getewayBill();
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.getewayBill = function(){
            	 if(!angular.isUndefined($scope.selectedTrOrderId) && $scope.selectedTrOrderId!=null){
                    $http({
                         method: "GET",
                         url: apiJson.urls.transferOrderManagement.getEwayBillNumber+"?transferOrderId="+$scope.selectedTrOrderId
                        }).then(function(response) {
                        	 $scope.ewayBillNumber = response.data;
                        	 $scope.getTrOrderDetailed();
                        }, function error(response) {
                            console.log("error:" + response);
                            $scope.getTrOrderDetailed();
                        });
                }
            }

            $scope.cancelTransferOrder = function(){
                if(!angular.isUndefined($scope.selectedTrOrderId) && $scope.selectedTrOrderId!=null){
                    $http({
                        method: "PUT",
                        url: apiJson.urls.transferOrderManagement.cancelTransferOrder,
                        data:{
                            transferOrderId:$scope.selectedTrOrderId,
                            updatedBy:appUtil.createGeneratedBy().id
                        }
                    }).then(function success(response) {
                        console.log(response.data);
                        if(response.data!=null && response.data==$scope.selectedTrOrderId){
                            $toastService.create("Transfer order with id "+response.data+" cancelled successfully!");
                            for(var i=0 ; i < $scope.transferOrderDetail.transferOrderItems.length; i++){
                            	if($scope.transferOrderDetail.transferOrderItems[i].productId==100217){
                            		var url=apiJson.urls.manualBillBookManagement. cancelManualBillBookEntry;
                            		metaDataService.cancelManualBillBookEntry(url,$scope.selectedTrOrderId).then(function(data){
                            			if(data){
                            				$toastService.create("Manual bill book of Transfer order with id "+response.data+" removed successfully!");
                            			}else{
                            				$toastService.create("Something went wrong.Manual bill book of Transfer order with id "+response.data+
                            				" cannot be removed!");
                            			}
                            		});
                            	}
                            }
                            $scope.backToTrOrderMgt();
                        }else{
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        if (response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    });
                }
            }

            $scope.calculateTotalPrice = function () {
                var total = 0;
                $scope.availableTaxes = [];
                $scope.transferOrderDetail.transferOrderItems.forEach(function (item) {
                    total += (item.total + item.tax);
                    $scope.calculateTaxes(item);
                });
                $scope.totalPrice = Math.round(total);
                $scope.totalPriceInWords = appUtil.inWords(Math.round(total));
            }

            $scope.calculateTaxes= function(item){
        	item.gst = 0;
        	item.gstPercentage = 0;
        	item.other = 0;
        	item.otherPercentage = 0;
        	if(item.taxes == null || item.taxes.length == 0){
        	    return;
        	}
        	for(var i in item.taxes){
        		if($scope.availableTaxes.indexOf(item.taxes[i].code) < 0){
        			$scope.availableTaxes.push(item.taxes[i].code);
        		}
        	    if(item.taxes[i].type == 'GST'){
        		item.gst = item.gst + item.taxes[i].value ;
        		item.gstPercentage = item.taxes[i].percentage;
        	    }else{
        		item.other = item.other + item.taxes[i].value
        	    }
        	}
        	if(item.total > 0){
        	    item.otherPercentage = item.other/item.total*100;
        	}
            }
            $scope.getTorqusTO = function(toID) {
                $http({
                    url: apiJson.urls.transferOrderManagement.getTorqusTO+"/"+toID,
                    method: 'POST',
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(function(data){
                    var fileName = "Transfer_Order_Sheet_" + toID + "_" +
                        $scope.transferOrderDetail.generatedForUnitId.name+ "_"+ appUtil.formatDate(Date.now(),"dd-MM-yyyy-hh-mm-ss") + ".xlsx";
                    var blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    },fileName);
                    saveAs(blob, fileName);
                }).error(function(err){
                    console.log("Error during getting data",err);
                });
            }

        }]);
