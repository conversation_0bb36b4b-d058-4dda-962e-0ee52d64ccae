'use strict';

angular.module('scmApp')
.controller('searchGatepassCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService','metaDataService','previewModalService', 'Popeye',
	function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,metaDataService,previewModalService, Popeye) {
	$scope.isWarehouse=appUtil.isWarehouseOrKitchen();
	$scope.init = function () {
		$scope.operationList = [
			{name : "QUALITY_CHECK", label : "QUALITY CHECK"},{name : "REPAIR", label : "REPAIR"},
			{name : "NPD", label : "NPD"},{name : "SAMPLE", label : "SAMPLE"},
			{name : "INTERNAL_TXN", label : "INTERNAL TXN"}];
		$scope.statusList = ["INITIATED", "PENDING_RETURN", "CLOSED", "CANCELLED"];
		$scope.showPreview = previewModalService.showPreview;
		$scope.searchGatepass = {};
		$scope.gatepass = [];
		$scope.showVendorFilter = false;
		$scope.isDetailView = false;
		$scope.actionType = null;
		$scope.skuQuantityDetails = {};
		$scope.companyMap = appUtil.getCompanyMap();
		$scope.unitData = appUtil.getUnitData();
		$scope.totalPrice = null;
		$scope.totalPriceInWords = '';
		$scope.vendors = null;
		$scope.vendorDetail = null;
		$scope.dispatchDetail = null;

		$scope.showVendors();
	};

	$scope.showVendors = function () {
			var url = apiJson.urls.vendorManagement.allVendorName
			$http({
				url: url,
				method: 'GET'
			}).then(function (response) {
				console.log(response.data);
				$scope.vendors = response.data;
			}, function (response) {
				console.log("got error", response);
			});
		};
	$scope.operationCheck = function (operationType){
		if(operationType === "REPAIR"){
			$scope.showVendorFilter = true;
		}
		else{
			$scope.showVendorFilter = false;
		}
	}
	$scope.getGatepass = function(){
		$scope.searchGatepass.unitId = appUtil.getCurrentUser().unitId;
		if(!$scope.searchGatepass.gatePassId) {
            if (appUtil.isEmptyObject($scope.searchGatepass.startDate) || appUtil.isEmptyObject($scope.searchGatepass.endDate)) {
                $toastService.create("Please fill  start date and end date to proceed!");
                return false;
            }
        }
		$http({
			method: "POST",
			url: apiJson.urls.gatepassManagement.searchGatepass,
			data : $scope.searchGatepass
		}).then(function success(response) {
			$scope.gatepass = response.data;
			updateSkuQuantityDetails($scope.gatepass);
		}, function error(response) {
			console.log("error:" + response);
		});
	};

	$scope.downloadExcell = function (){
			var params = {
				className : "com.stpl.tech.scm.domain.model.Gatepass"
			}
			var jsonStrings = [];
			for(var i = 0;i<$scope.gatepass.length;i++){
				jsonStrings.push(JSON.stringify($scope.gatepass[i]));
			}
			metaDataService. downloadExcell(jsonStrings,params);
		}



	$scope.gatepassDetail = function(gatepass, actionType){
		$scope.actionType = actionType;
		$scope.isDetailView = true;
		$scope.selectedGatepass = gatepass;
		if($scope.selectedGatepass.assetGatePass == true){
			for(var i in $scope.selectedGatepass.itemDatas) {
				var item = $scope.selectedGatepass.itemDatas[i]
				item.returnQuantity = item.quantity;
				$scope.updateQuantity($scope.selectedGatepass.id, item.sku.id, item);
			}
		}
		calculateTotalPrice();
		getVendorDetail();
	};

	function getVendorDetail() {
         $http({
             method: "GET",
             url: apiJson.urls.vendorManagement.vendor,
             params : { vendorId : $scope.selectedGatepass.vendor.id}
         }).then(function success(response) {
             $scope.vendorDetail = response.data;
             for (var i = 0; i < $scope.vendorDetail.dispatchLocations.length; i++) {
				if($scope.vendorDetail.dispatchLocations[i].dispatchId == $scope.selectedGatepass.dispatchLocation.id){
					$scope.dispatchDetail = $scope.vendorDetail.dispatchLocations[i];
					// console.log("$scope.dispatchDetail ",$scope.dispatchDetail);
					break;
				}
			}
         }, function error(response) {
        	 $toastService.create("Something went wrong while getting vendor detail. Please try again!");
             console.log("error:" + response);
         });
     };

	function calculateTotalPrice() {
		var total = $scope.selectedGatepass.totalCost + $scope.selectedGatepass.totalTax;
		$scope.totalPrice = Math.round(total);
		$scope.totalPriceInWords = appUtil.inWords(Math.round(total));
	}

	$scope.updateQuantity = function(gatepassId, skuId, item) {
		var data = $scope.skuQuantityDetails[gatepassId][skuId];
		var temp = data.returnQunatity + data.lostQuantity + item.returnQuantity;
		if(temp > data.transfer){
			var req = data.transfer -(data.returnQunatity + data.lostQuantity);
			$toastService.create("Quantity cannot be greater than " + req +" !");
            item.returnQuantity = 0;
		}
	};

	$scope.settleGatepass = function(){
		if($scope.actionType == 'Cancel'){
			cancelGatepass();
		}else{
			updateGatepass();
		}

	};

	$scope.getGatepassCancelStatus = function(itemData) {
		for (var i = 0; i < itemData.length; i++) {
			if(itemData[i].transType == 'RETURN' || itemData[i].transType == 'LOST'){
				return false;
			}
		}
		return true;
	};

	$scope.openDetail = function(item) {
		var mappingModal = Popeye.openModal({
			templateUrl : "detailModal.html",
			controller : "detailViewCtrl",
			resolve : {
				item : function() {
					return item;
				}
			},
			click : false,
			keyboard : false
		});

		mappingModal.closed
		.then(function(isSuccessful) {
			if (isSuccessful) {
			}
		});
	};

	function cancelGatepass(){
		$scope.selectedGatepass.cancelledBy = appUtil.createGeneratedBy();
		$http({
			method: "POST",
			url: apiJson.urls.gatepassManagement.cancelGatepass,
			data : $scope.selectedGatepass
		}).then(function success(response) {
			processResponse(response.data);
		}, function error(response) {
			console.log("error:" + response);
		});
	};

	function updateGatepass(){
		var itemData  = [];
		for (var i = 0; i < $scope.selectedGatepass.itemDatas.length; i++) {
			if($scope.selectedGatepass.itemDatas[i].returnQuantity != null || $scope.selectedGatepass.itemDatas[i].returnQuantity != undefined){
				if($scope.selectedGatepass.itemDatas[i].transType == 'TRANSFER'){
					itemData.push($scope.selectedGatepass.itemDatas[i]);
				}
			}
		}
		if(itemData.length == 0){
			$toastService.create("Quantity cannot be empty for all items type  " + $scope.actionType +" !");
			return false;
		}
		for (var i = 0; i < itemData.length; i++) {
			itemData[i].quantity = itemData[i].returnQuantity;
			if($scope.actionType == 'Return'){
				itemData[i].transType = 'RETURN';
			}else if($scope.actionType == 'Lost'){
				itemData[i].transType = 'LOST';
			}
		}
		delete $scope.selectedGatepass.itemDatas;
		$scope.selectedGatepass.createdBy = appUtil.createGeneratedBy();
		$scope.selectedGatepass.itemDatas = itemData;
	//	console.log("$scope.selectedGatepass",$scope.selectedGatepass);
		$http({
			method: "POST",
			url: apiJson.urls.gatepassManagement.updateGatepass,
			data : $scope.selectedGatepass
		}).then(function success(response) {
			processResponse(response.data);
		}, function error(response) {
			console.log("error:" , response);
		});
	};

	function processResponse(result){
		if(result === true){
			$toastService.create("Gatepass " + $scope.actionType +" successfully !");
			$scope.init();
		}else{
			$toastService.create("Something went wrong. Please try again!");
		}
	}

	function updateSkuQuantityDetails(gatepass){
		for (var j = 0; j < gatepass.length; j++) {
			var gatepassId = gatepass[j].id;
			if(angular.isUndefined($scope.skuQuantityDetails[gatepassId])){
				$scope.skuQuantityDetails[gatepassId] = {};
			}
			for (var i = 0; i < gatepass[j].itemDatas.length; i++) {
				var skuId = gatepass[j].itemDatas[i].sku.id;
				if(angular.isUndefined($scope.skuQuantityDetails[gatepassId][skuId])){
					$scope.skuQuantityDetails[gatepassId][skuId] ={ transfer : 0,
							returnQunatity : 0,
							lostQuantity : 0};
				}
				switch (gatepass[j].itemDatas[i].transType) {
				case 'TRANSFER':
					$scope.skuQuantityDetails[gatepassId][skuId].transfer += gatepass[j].itemDatas[i].quantity;
					break;
				case 'RETURN':
					$scope.skuQuantityDetails[gatepassId][skuId].returnQunatity += gatepass[j].itemDatas[i].quantity;
					break;
				case 'LOST':
					$scope.skuQuantityDetails[gatepassId][skuId].lostQuantity += gatepass[j].itemDatas[i].quantity;
					break;
				}
				$scope.skuQuantityDetails[gatepassId][skuId].avlQty = $scope.skuQuantityDetails[gatepassId][skuId].transfer -
				($scope.skuQuantityDetails[gatepassId][skuId].returnQunatity + $scope.skuQuantityDetails[gatepassId][skuId].lostQuantity);
			}
		}
	};

}]).controller('detailViewCtrl', ['$scope','item','appUtil','$toastService','apiJson','$http','Popeye',
	function ($scope, item,appUtil,$toastService,apiJson,$http,Popeye) {
	$scope.item = item;

	$scope.cancel=function(){
		Popeye.closeCurrentModal();
	};

}]);;
