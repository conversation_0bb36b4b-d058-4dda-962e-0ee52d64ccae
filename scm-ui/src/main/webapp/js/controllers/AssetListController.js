/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('assetListCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson',
            'appUtil', '$http', '$stateParams', 'productService','$alertService','Popeye', '$fileUploadService', '$toastService', 'metaDataService', 'PrintService',
            function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, $http, $stateParams,
                      productService,$alertService, Popeye, $fileUploadService, $toastService, metaDataService, PrintService) {

                $scope.init = function () {
                    productService.getAllProducts(function (products) {
                        $scope.products = products;
                    });
                    $scope.assetId = null;
                    $scope.assets = [];
                    $scope.assetDetail = {};
                    $scope.currentUnit = appUtil.getUnitData();
                    // getAllAssets();
                };

                $scope.findAssets = function () {
                    $scope.assetName = null;
                    getAllAssets();
                }

                $scope.searchAssetByName = function (name) {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.assetManagement.getAssetNameSlimWithUnit,
                        params: {
                            unitId : $scope.currentUnit.id,
                            assetName : name
                        }
                    }).then(function success(response) {
                        $scope.assets = response.data;
                    }, function error(response) {
                        console.log("Encountered an error",response);
                    });
                }

                function getUserUnits(){
                    $http({
                        method: 'POST',
                        url: apiJson.urls.unitManagement.userUnits,
                        data: {
                            employeeId: appUtil.createGeneratedBy().id,
                            onlyActive: true
                        }
                    }).then(function success(response) {
                        $scope.userUnitArray = response.data;

                    }, function error(response) {
                        console.log("Encountered an error",response);
                    });

                }

                function getAllAssets(){
                    $http({
                        method: 'GET',
                        url: apiJson.urls.assetManagement.getAssetSlimWithUnit,
                        params: {
                            unitId : $scope.currentUnit.id,
                        }
                    }).then(function success(response) {
                        $scope.assets = response.data;
                        getUserUnits();
                    }, function error(response) {
                        console.log("Encountered an error",response);
                    });

                }

                $scope.employeeAssetFilter = function(asset){

                    for(var i in $scope.userUnitArray){
                        if(asset.ownerId == $scope.userUnitArray[i].id){
                            return true;
                        }
                    }
                    return false;
                }

                function getAssetDetail(assetId, assets) {

                    for (var index in assets) {
                        if (assets[index].assetId == assetId) {
                            return assets[index];
                        }
                    }
                    //return null;
                }

                $scope.selectAsset = function (assetId) {

                     if (assetId != null && assetId != "") {
                         $scope.assetId = assetId;
                         $scope.assetDetail = getAssetDetail(assetId, $scope.assets);
                     }
                };

                $scope.printAgain  = function(){
                    $alertService.confirm("Are you sure?","",function(result) {
                        console.log(appUtil.createGeneratedBy());
                        if (result) {
                            $http({
                                method: 'POST',
                                url: apiJson.urls.assetManagement.assetPrintTag,
                                params: {
                                    assetId : $scope.assetDetail.assetId,
                                    printedBy : appUtil.createGeneratedBy().id
                                }
                            }).then(function success(response) {
                                $scope.assetDetail.lastTagPrintedBy = appUtil.createGeneratedBy();
                                $scope.assetDetail.tagPrintCount = $scope.assetDetail.tagPrintCount + 1;
                                var code = {}
                                code.assetName = $scope.assetDetail.assetName;
                                for(var i in $scope.products){
                                    if($scope.products[i].productId == $scope.assetDetail.productId){
                                        code.subCategoryCode = $scope.products[i].subCategoryDefinition.code;
                                    }
                                }
                                code.assetTagValue = $scope.assetDetail.tagValue
                                PrintService.printBarCode(code);
                            }, function error(response) {
                                console.log("Encountered an error",response);
                                alert('Error Printing asset tag , contact support');
                            });
                        }
                    });
                }

                $scope.changeStatus = function(status) {
                    $alertService.confirm("Are you sure?","",function(result) {
                        if (result) {
                            $http({
                                method: 'GET',
                                url: apiJson.urls.assetManagement.alterAssetStatus,
                                params: {
                                    assetId : $scope.assetDetail.assetId,
                                    assetStatus : status
                                }
                            }).then(function success(response) {
                                $scope.assetDetail.assetStatus = status;
                            }, function error(response) {
                                console.log("Encountered an error",response);
                                alert('Error changing asset status , contact support')
                            });
                        }
                    });

                }

            }
        ]
    )