/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('scmApp')
    .controller('sendTDSMailToVendorCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService',
        '$fileUploadService', '$alertService', 'metaDataService', '$timeout',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, $fileUploadService, $alertService, metaDataService, $timeout) {

            $scope.init = function () {
                $scope.toggleUploadFilesBtn = false;
                $scope.toggleSendMailSheetBtn = false;
                $scope.excelEntryList = [];
                $scope.mailDataMap = {};
                $scope.trackArray = [];
                $scope.trackIndex = 0;
            };

            $scope.uploadDoc = function () {
                $fileUploadService.openFileModal("Upload Mailing Sheet", "Find", function (file) {
                    $scope.uploadMailingSheet(file);
                });
            };

            $scope.uploadMailingSheet = function (file) {
                $scope.$apply(function () {
                    $scope.init();
                });
                var extName = metaDataService.getFileExtension(file.name).toLowerCase();
                if (extName === 'xls' || extName === 'xlsx') {
                    if (typeof (FileReader) != undefined) {
                        var reader = new FileReader();
                        //For Browsers other than IE.
                        if (reader.readAsBinaryString) {
                            reader.onload = function (e) {
                                $scope.processExcel(e.target.result);
                            };
                            reader.readAsBinaryString(file);
                        }
                    } else {
                        $toastService.create("This browser does not support HTML5.");
                    }
                } else {
                    $toastService.create("Please upload a valid Excel file.");
                }
            };

            $scope.processExcel = function (data) {
                //Read the Excel File data.
                var workbook = XLSX.read(data, {
                    type: 'binary'
                });

                //Fetch the name of First Sheet.
                if (!appUtil.isEmptyObject(workbook.SheetNames)) {
                    var firstSheet = workbook.SheetNames[0];

                    //Read all rows from First Sheet into an JSON array.
                    var rowArray = XLSX.utils.sheet_to_row_object_array(workbook.Sheets[firstSheet]);

                    if (rowArray && rowArray.length > 0) {
                        $scope.$apply(function () {
                            rowArray.forEach(function (r) {
                                $scope.mailDataMap[r["Pan Number"]] = r;
                            });
                            $scope.excelEntryList = rowArray;
                            $scope.toggleUploadFilesBtn = true;
                            console.log("list ", $scope.excelEntryList);
                            console.log("mailDataMap is", $scope.mailDataMap);
                        });
                    }
                }
            };

            $scope.sendEmails = function () {
                $scope.trackIndex = 0;
                $scope.trackArray = [];
                var htmlEle = document.getElementById("multi-tds-pdf");
                var filesArr = htmlEle.files;
                if (filesArr.length > 0) {
                    for (var i = 0; i < filesArr.length; i++) {
                        var words = filesArr[i].name.split('_');
                        //attaching file to mail map
                        var mapVal = $scope.mailDataMap[words[0]];
                        if (!appUtil.isEmptyObject(mapVal)) {
                            mapVal.toUpload = filesArr[i];
                            mapVal["Quarter"] = words[1];
                        }
                    }

                    $scope.trackArray = Object.keys($scope.mailDataMap);
                    $scope.emailTrigger();
                }
            };

            $scope.emailTrigger = function () {
                if ($scope.trackIndex < $scope.trackArray.length) {
                    var dat = $scope.mailDataMap[$scope.trackArray[$scope.trackIndex]];
                    $scope.sendMail(dat, dat.toUpload);
                    $scope.trackIndex++;
                    $timeout(function () {
                        $scope.emailTrigger();
                    }, 3000);
                } else {
                    //clearing state
                    $scope.mailDataMap = {};
                    $scope.trackArray = [];
                    $scope.trackIndex = 0;
                }
            };

            $scope.sendMail = function (data, file) {
                if (file == null) {
                    $toastService.create('Corresponding file was not found!');
                    setFlagToRow(false, data);
                    return;
                }
                if (file.size > 307200) {
                    $toastService.create('File size should not be greater than 300 kb.');
                    return;
                }

                var tempEmails = '';
                var primEmail = data["Primary Email Id of Customer"];
                var secEmail = data["Secondary Email Id of Customer"];
                if (!appUtil.isEmptyObject(primEmail) && isvalidEmail(primEmail)) {
                    tempEmails = tempEmails + primEmail;
                }
                if (!appUtil.isEmptyObject(secEmail) && isvalidEmail(secEmail)) {
                    if (tempEmails === '') {
                        tempEmails = secEmail;
                    } else {
                        tempEmails = tempEmails + ',' + secEmail;
                    }
                }
                if (tempEmails === '') {
                    $toastService.create('Invalid email address found ');
                    return;
                }

                var fileExt = metaDataService.getFileExtension(file.name);
                if (fileExt.toLowerCase() == 'pdf') {
                    var fd = new FormData();
                    fd.append('email', tempEmails);
                    fd.append('fy', data["Financial Year"]);
                    fd.append('mimeType', fileExt.toUpperCase());
                    fd.append('vendorName', data["Name of Party"]);
                    fd.append('panNumber', data["Pan Number"]);
                    fd.append('quarter', data["Quarter"]);
                    fd.append('file', file);
                    $http({
                        url: apiJson.urls.vendorManagement.uploadTDSMailFile,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $toastService.create("Mail sent to " + data["Primary Email Id of Customer"]);
                        setFlagToRow(true, data);
                    }).error(function (error) {
                        console.log("Error ", error);
                    });
                } else {
                    $toastService.create('Upload Failed, File Format not Supported');
                }
            };

            function findIndexInArray(arr, num) {
                return arr.findIndex(function (ele) {
                    return ele["Pan Number"] === num;
                });
            }

            function setFlagToRow(flag, data) {
                var panN = data["Pan Number"];
                var index = findIndexInArray($scope.excelEntryList, panN);
                if (index !== -1) {
                    $scope.excelEntryList[index].mailSent = flag;
                }
            }

            function isvalidEmail(email) {
                var regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return regex.test(email);
            }

        }]
    );
