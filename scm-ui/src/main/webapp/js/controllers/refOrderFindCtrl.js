/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('refOrderFindCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location', '$toastService',
        function ($rootScope, $scope, apiJson, $http, appUtil,$location, $toastService) {

            $scope.init = function () {
                $scope.today = appUtil.getDate(0);
                var refOrderListObj = appUtil.getRefOrderListObj();
                $scope.scmOrderStatusList = [];
                $scope.scmOrderStatusList.push("");
                $scope.scmOrderStatusList = $scope.scmOrderStatusList.concat(appUtil.getMetadata().scmOrderStatus);
                $scope.status = refOrderListObj.status==null?$scope.scmOrderStatusList[0]:refOrderListObj.status;
                $scope.referenceOrderList = refOrderListObj.referenceOrderList;
                $scope.referenceOrderId = refOrderListObj.referenceOrderId;
                $scope.startDate = refOrderListObj.startDate==null?appUtil.formatDate(new Date(), "yyyy-MM-dd"):refOrderListObj.startDate;
                $scope.endDate = refOrderListObj.endDate==null?appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd"):refOrderListObj.endDate;
                $scope.unitList = [];
                $scope.unitList.push({
                    unitId: null,
                    unitName: ""
                });
                $scope.unitList = $scope.unitList.concat(appUtil.getUnitList());
                $scope.requestingUnit = refOrderListObj.requestingUnit==null?$scope.unitList[0]:refOrderListObj.requestingUnit;
                $scope.referenceOrderList = refOrderListObj.referenceOrderList;
                $scope.findReferenceOrders();
            }

            $scope.findReferenceOrders = function(){
            	 if($scope.startDate==null || $scope.startDate.trim() == ''|| $scope.endDate==null || $scope.endDate.trim() == ''){
            		 $toastService.create("Please fill start date and end date properly!");
                    return false;
                }else if($scope.startDate != null && $scope.endDate != null && $scope.startDate > $scope.endDate) {
	           		 $toastService.create("Please fill end date greater than or equal to start date properly!");
	                 return false;
                }else{
                    var url = apiJson.urls.referenceOrderManagement.referenceOrderFind+"?requestingUnitId="+
                        appUtil.getCurrentUser().unitId+"&startDate="+$scope.startDate+"&endDate="+$scope.endDate;
                    if($scope.referenceOrderId!=null){
                        url+="&referenceOrderId="+$scope.referenceOrderId;
                    }
                    if($scope.status!=null && $scope.status!=""){
                        url+="&status="+$scope.status;
                    }
                    $http({
                        method: "GET",
                        url: url
                    }).then(function success(response) {
                        $scope.referenceOrderList = response.data;
                        //console.log($scope.referenceOrderList)
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            }

            $scope.openRefOrderAction = function(roId){
                $rootScope.selectedRefOrderId = roId;
                appUtil.setRefOrderListObj({
                    startDate: $scope.startDate,
                    endDate: $scope.endDate,
                    referenceOrderId: $scope.referenceOrderId,
                    requestingUnit: $scope.requestingUnit,
                    status: $scope.status,
                    referenceOrderList: $scope.referenceOrderList
                });
                $location.path("/menu/refOrderAction");
            }

        }]);
