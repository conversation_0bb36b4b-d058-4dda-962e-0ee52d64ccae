'use strict';

angular.module('scmApp').controller('productProjectionsCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService',
    '$alertService','metaDataService','Popeye', function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $alertService, metaDataService, Popeye) {
                $scope.init = function () {
                    $scope.startDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
                    $scope.endDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
                    $scope.minDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
                };

                $scope.checkStartDate = function() {
                     if (new Date($scope.startDate) > new Date($scope.endDate)) {
                         $toastService.create("Start Date Can not be greater than End Date..!");
                         $scope.init();
                         return false;
                     }
                };

                $scope.checkEndDate = function(endDate) {
                    if (new Date($scope.endDate) < new Date($scope.startDate)) {
                        $toastService.create("End Date can not be less than Start Date..!");
                        $scope.init();
                        return false;
                    }
                };

                $scope.generateProjections = function () {
                    var currentUser = appUtil.getCurrentUser();
                    $http({
                        url: apiJson.urls.productProjectionsManagement.generateProductProjections,
                        method: 'POST',
                        data: {
                            startDate: $scope.startDate,
                            endDate: $scope.endDate,
                            generatedBy: currentUser.user.name+"["+currentUser.user.id+"]"
                        }
                    }).then(function success(response) {
                        $toastService.create("Projections Generated Successfully.Please Check the mail.");
                    }, function error(response) {
                        console.log("error:" + response);
                        $toastService.create("(Error) Could not generate Projections... Please try again later");
                    });
                };
            }]);