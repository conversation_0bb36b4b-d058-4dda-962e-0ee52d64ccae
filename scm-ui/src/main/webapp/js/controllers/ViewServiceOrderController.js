'use strict';
angular.module('scmApp').controller('viewServiceOrderCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http',
    'appUtil', '$toastService','$alertService','metaDataService','Popeye', '$window',
    function ($rootScope, $stateParams, $scope, apiJson, $http, appUtil, $toastService,$alertService,metaDataService, Popeye, $window) {

        function getCreatedSOs(view,startDate,endDate) {
            $scope.fetchSOs(view,startDate,endDate,false,null);
        }

        function getCreatedSOsShort(view,startDate,endDate) {
            $scope.fetchSOs(view,startDate,endDate,true,null);
        }

        $scope.fetchSOs = function (view,startDate,endDate,isShort , callback){
            $scope.pendingApprovalL1 = null;
            $scope.pendingApprovalL2 = null;
            $scope.approvedAmount = null;
            $scope.inProgessAmount = null;
            if(appUtil.isEmptyObject(startDate)){
                $toastService.create("Please select a start date first");
                return;
            }
            if(appUtil.isEmptyObject(endDate)){
                $toastService.create("Please select a end date first");
                return;
            }

            var params = {
                isView:view,
                startDate:startDate,
                endDate:endDate,
                userId: appUtil.getCurrentUser().userId,
                showAll : $scope.showAll
            };

            if(!appUtil.isEmptyObject($scope.vendorSelected)){
                params["vendorId"] = $scope.vendorSelected.id;
            }
            if(!appUtil.isEmptyObject($scope.bccSelected)){
                params["bccId"] = $scope.bccSelected.id;
            }

            if(!appUtil.isEmptyObject($scope.serviceOrderId)){
                params["serviceOrderId"] = $scope.serviceOrderId;
            }
            if(callback!=null){
                params["serviceOrderId"] = $scope.selectedSoId;
            }

            $scope.endpoint = apiJson.urls.serviceOrderManagement.createdOrders;
            if(isShort){
                $scope.endpoint =  apiJson.urls.serviceOrderManagement.createdOrdersShort;
            }
            $http({
                method: "GET",
                url: $scope.endpoint,
                params: params
            }).then(function (response) {
                console.log(response);
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Orders found!");
                } else {
                    if(callback!=null){
                        callback(response.data[0]);
                    }
                    else{
                        if(isShort){
                            $scope.soRequestShort = response.data.serviceOrderShortList;
                            if(!appUtil.isEmptyObject($scope.vendorSelected)){
                                $scope.pendingApprovalL1 = response.data.pendingApprovalL1;
                                $scope.pendingApprovalL2 = response.data.pendingApprovalL2;
                                $scope.approvedAmount = response.data.approvedAmount;
                                $scope.inProgessAmount = response.data.inProgessAmount;
                            }
                        }
                        else
                            $scope.soRequest = response.data;
                    }
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });

        }

        function updateSO(url, callback) {
            $alertService.confirm("Are you sure?","",function(result){
                if(result){
                    $http({
                        method: "POST",
                        url: url
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response)) {
                            callback(response.data);
                        }
                    }, function (err) {
                        console.log("Encountered error at backend", err);
                    });
                }
            });

        }

        $scope.init = function () {
            var currentDate = appUtil.getCurrentBusinessDate();
            if(!appUtil.isEmptyObject(currentDate)){
                $scope.startDate = appUtil.formatDate(appUtil.calculatedDate(-30, currentDate), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.showViewActions = $stateParams.viewSO;
                $scope.createdSO = $stateParams.createdSO;
                $scope.soRequest = [];
                $scope.soRequestShort = [];
                $scope.bccIds = [];
                $scope.selectedSO = null;
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.showAll = false;

                metaDataService.getServiceVendors(function(vendorsForUnit){
                    $scope.vendors = vendorsForUnit;
                });

                metaDataService.getAllBusinessCostCenters(function(bcc){
                    $scope.bcc = bcc;
                });

                //getCreatedSOs($scope.showViewActions, $scope.startDate, $scope.endDate,$scope.selectedVendor);
            }
        };

        $scope.reset = function () {
          $scope.selectedSKU = null;
          $scope.selectedVendor = null;
        };

        $scope.selectVendor = function (vendor) {
          $scope.vendorSelected = vendor;
        };

        $scope.selectBcc = function (bcc) {
            $scope.bccSelected = bcc;
        };

        $scope.selectSKU = function (sku) {
            $scope.selectedSKU = sku;
        };

        $scope.downloadDocumentById = function (id){
            metaDataService.downloadDocumentById(id);
        }

        $scope.downloadSO = function(invoice){
            metaDataService.downloadDocument(invoice);
        };

        $scope.getSOs = function(showAll){
          $scope.showAll = showAll
          getCreatedSOsShort($scope.showViewActions,$scope.startDate,$scope.endDate);
        };

        $scope.showDetailsSO =  function  (so) {
            $scope.selectedSoId = so.id
            $scope.fetchSOs($scope.showViewActions,appUtil.convertToDateWithoutTime(so.generationTime) ,appUtil.convertToDateWithoutTime(so.generationTime),false,$scope.showDetails);

        };

        $scope.showDetails = function (so) {
            var viewDetailModal = Popeye.openModal({
                templateUrl: "viewSODetail.html",
                controller: "viewSODetailCtrl",
                resolve: {
                    so: function () {
                        return so;
                    },
                    check: function () {
                        return false;
                    },
                    status: function () {
                        return false;
                    }
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });
            viewDetailModal.closed.then(function (check) {
            });
        }

        $scope.changeStatusSO = function (item,status){
            $scope.selectedSoId = item.id
            $scope.fetchSOs($scope.showViewActions,appUtil.convertToDateWithoutTime(item.generationTime) ,appUtil.convertToDateWithoutTime(item.generationTime),
                false,function (item){
            $scope.changeStatus(item,status)
            } )
        }

        $scope.changeStatus = function(item, status){
          if(status == "APPROVED" && item.type != "OPEX"){
            var modalInstance = Popeye.openModal({
                templateUrl: 'departmentModal.html',
                controller: 'departmentModalCtrl',
                size: 'lg',
                windowClass: 'my-modal-popup',
                resolve: {
                	serviceItemsDept : function () {
                        return item;
                    },
                    status:function () {
                        return status == 'APPROVED' ? true : false;
                    }
                },
                click: true,
                keyboard: false
            });

            modalInstance.closed.then(function(check) {
                    if(!check){
                    	$scope.updateStatus(item, status);
                    }
            });
           }
           else{
        	   $scope.showDetailsStatus(item, status);
        	//	$scope.updateStatus(item, status);
           }
        };

        $scope.showDetailsStatus = function (so, status) {
            console.log(status)
            var viewDetailModal = Popeye.openModal({
                templateUrl: "viewSODetail.html",
                controller: "viewSODetailCtrl",
                resolve: {
                    so: function(){
                        return so;
                    },
                    check: function(){
                        return true;
                    },
                    status:function () {
                       return status == 'APPROVED' ? true : false;
                    }
                },
                modalClass:'custom-modal',
                click: false,
                keyboard: false
            });
            viewDetailModal.closed.then(function(state) {
            	if(state){
                	$scope.updateStatus(so, status);
            	}
        });
        };

        $scope.updateStatus = function(item, status){
            $http({
                method: "GET",
                url: apiJson.urls.serviceOrderManagement.changeStatus,
                params: {
                	soId: item.id,
                	newStatus: status,
                	currentStatus: item.status,
                	amount: item.totalAmount,
                	userId: appUtil.getCurrentUser().userId
                },
            }).then(function success(response) {
            	if(response.data){
            	$toastService.create("Status updated successfully.");
            	getCreatedSOsShort($scope.showViewActions,$scope.startDate,$scope.endDate);
            	} else {
                    $toastService.create("Error in updating Service Order Status.");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }


        $scope.approve = function (approveID) {
            var url = apiJson.urls.serviceOrderManagement.approveSO + "/" + approveID + "/" + $scope.currentUser.userId;
            updateSO(url, function (updated) {
                if(updated){
                    $alertService.alert("Congratulations!!",
                        "Service Order: "+approveID+" approved successfully <br> Vendor has also been notified",
                        function(result){
                            getCreatedSOsShort($scope.showViewActions,$scope.startDate,$scope.endDate);
                        });
                }else{
                    $toastService.create("Service Order approval failed! Please try again later..");
                }
            });
        };

        $scope.reject = function (approveID, index) {
            var url = apiJson.urls.serviceOrderManagement.rejectSO + "/" + approveID + "/" + $scope.currentUser.userId;
            updateSO(url, function (updated) {
                if(updated){
                    $toastService.create("Service Order rejected successfully");
                    $scope.soRequestShort[index].status = "REJECTED";
                }else{
                    $toastService.create("Service Order rejection failed! Please try again later..");
                }
            });
        };

        $scope.cancel = function (selectedOrder, index) {
            if(selectedOrder.accountedForInPnl == true){
                $toastService.create("This Service Order  cannot be cancelled is accounted for PNL!");
                return;
            }

            findProvisionalSrLinkedToSo(selectedOrder.id).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $alertService.confirm("These SR(s) would be CANCELLED",
                        "Some unapproved/provisional SR(s) were found <br><b>" + "SR ID's " + response.data + "</b>" +
                        "<br>Do you want to continue?",
                        function (isConfirmed) {
                            if (isConfirmed) {
                                sendCancelSORequest(selectedOrder, index);
                            }
                        });
                } else {
                    sendCancelSORequest(selectedOrder, index);
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });

        };

        $scope.close = function (closureId, index) {
            findProvisionalSrLinkedToSo(closureId).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $alertService.confirm("These SR(s) would be CANCELLED",
                        "Some unapproved/provisional SR(s) were found <br><b>" + "SR ID's " + response.data + "</b>" +
                        "<br>Do you want to continue?",
                        function (isConfirmed) {
                            if (isConfirmed) {
                                sendCloseSORequest(closureId, index);
                            }
                        });
                } else {
                    sendCloseSORequest(closureId, index);
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

        function sendCancelSORequest(selectedOrder, index) {
            var url = apiJson.urls.serviceOrderManagement.cancelSO + "/" + selectedOrder.id + "/" + $scope.currentUser.userId;
            updateSO(url, function (updated) {
                if(updated){
                    $toastService.create("Service Order cancelled successfully");
                    $scope.soRequestShort[index].status = "CANCELLED";
                }else{
                    $toastService.create("Service Order cancellation failed! Please try again later..");
                }
            });
        }

        function sendCloseSORequest(closureId, index) {
            var url = apiJson.urls.serviceOrderManagement.closeSO + "/" + closureId + "/" + $scope.currentUser.userId;
            updateSO(url, function (updated) {
                if (updated) {
                    $toastService.create("Service Order closed successfully");
                    $scope.soRequestShort[index].status = "CLOSED";
                } else {
                    $toastService.create("Service Order closure failed! Please try again later..");
                }
            });
        }

        $scope.printInvoice = function(soInvoiceDocument, index) {
            metaDataService.downloadDocument(soInvoiceDocument);
        };

        function findProvisionalSrLinkedToSo(soId) {
            var url = apiJson.urls.serviceOrderManagement.getSRIdsForSO + "/" + soId;
            return $http({
                method: "GET",
                params: {
                    srStatus: "PROVISIONAL"
                },
                url: url
            });
        }
    }
]).controller('viewSODetailCtrl', ['$scope','so', 'appUtil','apiJson','$http', 'Popeye', '$toastService','$alertService','check','status','metaDataService',
	function ($scope,so,appUtil,apiJson,$http, Popeye,$toastService,$alertService,check,status,metaDataService) {
    $scope.initViewModal = function () {
    	$scope.billAmount = 0;
    	$scope.totalTaxes = 0;
    	$scope.paidAmount = 0;
        $scope.so = so;
        $scope.check = check;
        $scope.status=status;
        $scope.getSummaryData();
    };

    $scope.getSummaryData = function (){
    	for(var x = 0 ; x < $scope.so.orderItems.length ; x++){
    		$scope.billAmount = $scope.so.orderItems[x].totalCost + $scope.billAmount;
    		$scope.totalTaxes = $scope.so.orderItems[x].totalTax + $scope.totalTaxes;
    		$scope.paidAmount = $scope.so.orderItems[x].amountPaid + $scope.paidAmount;
    	}
    }

    $scope.downloadDocument = function(document){
        metaDataService.downloadDocument(document);
    };

    $scope.downloadDocumentById = function (id){
            metaDataService.downloadDocumentById(id);
    }

    $scope.submit = function(){
 		$scope.closeModal(true);
 	}

 	$scope.cancel = function(){
 		$scope.closeModal(false);
 	}

 	 $scope.closeModal = function closeModal(status) {
         Popeye.closeCurrentModal(status);
     }


}]).controller('departmentModalCtrl', ['$scope', 'appUtil','apiJson','$http', 'Popeye', '$toastService','$alertService','serviceItemsDept','status',
     function ($scope, appUtil,apiJson,$http, Popeye,$toastService,$alertService,serviceItemsDept,status) {


 	$scope.init = function (){
        $scope.billAmount = 0;
        $scope.totalTaxes = 0;
        $scope.paidAmount = 0;
 		$scope.summaryItem = [];
 		$scope.status=status;
 		$scope.selectedSOR = serviceItemsDept;
 		$scope.getDepartmentData(serviceItemsDept);
        $scope.getSummaryData();
 	}

     $scope.getSummaryData = function (){
         for(var x = 0 ; x < $scope.selectedSOR.orderItems.length ; x++){
             $scope.billAmount = $scope.selectedSOR.orderItems[x].totalCost + $scope.billAmount;
             $scope.totalTaxes = $scope.selectedSOR.orderItems[x].totalTax + $scope.totalTaxes;
             $scope.paidAmount = $scope.selectedSOR.orderItems[x].amountPaid + $scope.paidAmount;
         }
     }


 	$scope.getDepartmentData = function(serviceItemsDeptData){
 	   $http({
           url: apiJson.urls.serviceOrderManagement.getDepartmentData,
           method: "POST",
           data : serviceItemsDeptData.orderItems
       }).then(function (response) {
           if(response.data){
        	   $scope.summaryItem = response.data
           }
       }, function (response) {
           console.log(response);
       });
 	}

 	$scope.submit = function(){
 		$scope.closeModal(false);
 	}

 	$scope.cancel = function(){
 		$scope.closeModal(true);
 	}

    $scope.closeModal = function closeModal(check) {
        Popeye.closeCurrentModal(check);
    }


}]);
