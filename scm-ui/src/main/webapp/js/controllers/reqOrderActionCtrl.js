/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('reqOrderActionCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService','previewModalService','$state',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,previewModalService, $state) {

            $scope.init = function () {
                $scope.selectedReqOrderId = $rootScope.selectedReqOrderId;
                $scope.getRefOrderDetail();
                $scope.unitData = appUtil.getUnitData();
                $scope.scmProductDetails = appUtil.getScmProductDetails();
                $scope.requestOrderDetail = null;
                $scope.unitList = appUtil.getUnitList();
                $scope.showPreview = previewModalService.showPreview;
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.generatedForUnitData = null;
            };

            $scope.backToReqOrderMgt = function(){
                if(appUtil.isEmptyObject($scope.parentRO)){
                    $state.go("menu.reqOrderMgt");
                }else{
                    $scope.getRefOrderDetail($scope.parentRO);
                }
            };

            $scope.addTag = function (ro, searchTag) {
                if(!appUtil.isEmptyObject(ro.searchTag)){
                    $toastService.create("RO already has a search tag associated");
                    return;
                }
                if(appUtil.isEmptyObject(searchTag)){
                    $toastService.create("Search tag cannot be empty. Please enter a valid search tag");
                    return;
                }
                if(searchTag.length > 20){
                    $toastService.create("Search tag cannot be more than 20 characters!");
                    return
                }
                $http({
                    method: "POST",
                    url: apiJson.urls.requestOrderManagement.updateTag,
                    data:{
                        roId:ro.id,
                        tag:searchTag
                    }
                }).then(function success(response) {
                    if(!appUtil.isEmptyObject(response) && response.data){
                        $scope.requestOrderDetail.searchTag = searchTag;
                    } else {
                        $toastService.create("Error while updating search tag for the request order");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getRefOrderDetail = function(reqId,parentRO){
                $scope.parentRO = appUtil.isEmptyObject(parentRO) ? undefined : parentRO;
                reqId = appUtil.isEmptyObject(reqId) ? $scope.selectedReqOrderId : reqId;
                if(!angular.isUndefined($scope.selectedReqOrderId) && $scope.selectedReqOrderId!=null){
                    $http({
                        method: "GET",
                        url: apiJson.urls.requestOrderManagement.requestOrder+"?requestOrderId="+ reqId
                    }).then(function success(response) {
                        $scope.requestOrderDetail = response.data;
                        $scope.requestOrderDetail.fulfillmentUnit.unitId = $scope.requestOrderDetail.fulfillmentUnit.id;
                        $scope.requestOrderDetail.fulfillmentUnit.unitName = $scope.requestOrderDetail.fulfillmentUnit.name;
                        $scope.generatedForUnitData = appUtil.findUnitDetail($scope.requestOrderDetail.fulfillmentUnit.id);
                        $scope.calculateTotalPrice();
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.calculateTotalPrice = function () {
                var total = 0;
                $scope.availableTaxes = [];
                $scope.requestOrderDetail.requestOrderItems.forEach(function (item) {
                    total += ((item.requestedQuantity * item.negotiatedUnitPrice)+item.tax);
                    $scope.calculateTaxes(item);
                });
                $scope.totalPrice = Math.round(total);
                $scope.totalPriceInWords = appUtil.inWords(Math.round(total));
            }


            $scope.calculateTaxes= function(item){
                item.gst = 0;
                item.gstPercentage = 0;
                item.other = 0;
                item.otherPercentage = 0;
                if(item.taxes == null || item.taxes.length == 0){
                    return;
                }
                for(var i in item.taxes){
                    if($scope.availableTaxes.indexOf(item.taxes[i].code) < 0){
                        $scope.availableTaxes.push(item.taxes[i].code);
                    }
                    if(item.taxes[i].type == 'GST'){
                        item.gst = item.gst + item.taxes[i].value ;
                        item.gstPercentage = item.taxes[i].percentage;
                    }else{
                        item.other = item.other + item.taxes[i].value
                    }
                }
                if(item.total > 0){
                    item.otherPercentage = item.other/item.total*100;
                }
            }

            $scope.cloneRO = function (ro) {
                $state.go("menu.adhocOrderCreate", {"clonedItems": ro.requestOrderItems, "fulfilmentUnit" : ro.fulfillmentUnit.name})
            };

            $scope.cancelRequestOrder = function(){
            	if (confirm("Are you sure you want to cancel the request order?")) {
	                if(!angular.isUndefined($scope.selectedReqOrderId) && $scope.selectedReqOrderId!=null){
	                    $http({
	                        method: "PUT",
	                        url: apiJson.urls.requestOrderManagement.cancelRequestOrder,
	                        data:{
                                orderId:$scope.selectedReqOrderId,
                                updatedBy:appUtil.createGeneratedBy().id
                            }
	                    }).then(function success(response) {
	                        console.log(response.data);
	                        if(response.data!=null && response.data==$scope.selectedReqOrderId){
	                            $toastService.create("Request order with id "+response.data+" cancelled successfully!");
	                            $scope.backToReqOrderMgt();
	                        }else{
	                            $toastService.create("Something went wrong. Please try again!");
	                        }
	                    }, function error(response) {
	                        console.log("error:" + response);
	                    });
	                	}
	            	}
            }
        }]);
