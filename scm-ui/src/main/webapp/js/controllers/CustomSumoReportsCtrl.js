
'use strict';

angular.module('scmApp')
    .controller('customSumoReportsCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService) {

            $scope.init = function () {
                $scope.selectedReport = null;
                $scope.availableReportsList = ["Variance Summary Kitchen Warehouse","Fulfilment Percentage Kitchen Warehouse",
                    "Wastage Details Kitchen Warehouse","Sales Vs Fountain 9"];
            };

            $scope.generateReport = function(){
                if ($scope.selectedReport == null) {
                    $toastService.create("Please Select the Report first..!");
                    return false;
                }
                $http({
                    method: "GET",
                    url: apiJson.urls.scmReportManagement.generateReport,
                    params: {
                        "reportName" : $scope.selectedReport
                    }
                }).then(function success(response) {
                    $toastService.create("Report Generated Successfully.Please Check the mail.");
                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create("Error Occurred While Generating Report..! Please Try again Later..!");
                });
            };

            $scope.generateF9SalesReport = function(){
                var unitIds = [];
                unitIds.push(appUtil.getCurrentUser().unitId);
                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.generateF9SalesReport,
                    params:{
                        "unitIds":unitIds
                    }
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Report Generated Successfully.Please Check the mail.");
                    }
                    else {
                        $toastService.create("Error Occurred While Generating Report..! Please Try again Later..!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create("Error Occurred While Generating Report..! Please Try again Later..!");
                });
            };

        }]);
