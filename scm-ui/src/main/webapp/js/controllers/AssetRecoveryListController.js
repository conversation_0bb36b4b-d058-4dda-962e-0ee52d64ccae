/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('assetRecoveryListCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson',
            'appUtil', '$http', '$stateParams', 'productService','$alertService','Popeye', '$fileUploadService', '$toastService', 'metaDataService',
            function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, $http, $stateParams, productService,$alertService, Popeye, $fileUploadService, $toastService, metaDataService) {

                $scope.init = function () {
                    $scope.recoveryStatusList = ['INITIATED', 'RECOVERED', 'PENDING_LOST'];
                    $scope.recoveryStatus = 'INITIATED';
                    $scope.unitList = appUtil.unitList;
                    $scope.selectedUnitData = appUtil.getUnitData();
                    $scope.assetRecoveryList = [];
                    $scope.assetRecoveryListType = null;
                    $scope.salaryAllDeductionDate = null;
                    $scope.checkBoxAll = null;
                    $scope.getAssetForRecovery();
                    $scope.toggleRecoverButton = false;
                };

                $scope.getAssetForRecovery = function() {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.assetManagement.getAssetRecoveryList,
                        params: {
                            unitId : $scope.selectedUnitData.id,
                            recoveryStatus : $scope.recoveryStatus
                        }
                    }).then(function success(response) {
                        if($scope.recoveryStatus == 'INITIATED'){
                            $scope.toggleRecoverButton = true;
                        }
                        if($scope.recoveryStatus == 'RECOVERED'){
                            $scope.toggleRecoverButton = false;
                        }
                        $scope.assetRecoveryListType = $scope.recoveryStatus;
                        $scope.assetRecoveryList = response.data;
                    }, function error(response) {
                        console.log("Encountered an error",response);
                        $scope.assetRecoveryList = [];
                    });
                };

                $scope.checkAllRecovered = function (checkBoxAll) {
                    $scope.assetRecoveryList.forEach(function (item) {
                        if (item.recoveryStatus == 'INITIATED' && checkBoxAll === true) {
                            item.recovered = true;
                        }
                        else{
                            item.recovered = false;
                            // item.salaryAllDeductionDate = null;
                        }
                    });
                }

                $scope.allDeductionDate = function(salaryAllDeductionDate){
                    if($scope.checkBoxAll==true) {
                        $scope.assetRecoveryList.forEach(function (itemSelected) {
                            if (itemSelected.recoveryStatus == 'INITIATED' && itemSelected.recovered==true) {
                                itemSelected.salaryDeductionDate = salaryAllDeductionDate;
                            }
                        });
                    }
                    else{
                        $scope.salaryAllDeductionDate = null;
                        $toastService.create('Please Enter Sallary reduction date');
                        return;
                    }
                }

                $scope.setAsRecovered = function () {
                    var selected = false;
                    var reductionDateNotEntered = false;
                    var recoveryList = [];
                    $scope.assetRecoveryList.map(function(mapping) {
                        if(mapping.recovered == true && mapping.recoveryStatus == 'INITIATED') {
                            if(mapping.salaryDeductionDate == null || mapping.salaryDeductionDate == ""){
                                reductionDateNotEntered = true;

                                return;
                            }
                            selected = true;
                            recoveryList.push(mapping);
                        }
                    });
                    if(reductionDateNotEntered){
                        $toastService.create('Please Enter Sallary reduction date');
                        return;
                    }
                    if( selected == false){
                        $toastService.create('Please check atleast one entry for recovery');
                        return;
                    }
                    recoveryList.map(function (mapping) {
                        // mapping.recoveredAmount = mapping.recoveryAmount; logic shifted to backend
                        mapping.recoveredBy = appUtil.createGeneratedBy();
                    });
                    $http({
                        method: 'POST',
                        url: apiJson.urls.assetManagement.recoverAssetList,
                        data: recoveryList
                    }).then(function success(response) {
                        recoveryList.map(function (mapping) {
                            mapping.recoveryStatus = 'RECOVERED';
                        })
                    }, function error(response) {
                        console.log("Encountered an error",response);
                        $toastService.create('Error recovering asset, please contact support');asset-recovery-unit-status
                    });
                };

                $scope.submitRecovery = function() {
                    $alertService.confirm("Are you sure?","",function(result){
                        if(result){
                            $scope.assetRecoveryList.map(function (mapping) {
                                mapping.approvedBy = appUtil.createGeneratedBy();
                                if(mapping.found != true){
                                    mapping.found = false;
                                }
                                // if(mapping.found == true){
                                //     mapping.found = false;
                                // }
                                // if(mapping.found == false){
                                //     mapping.found = true;
                                // }
                            })
                            $http({
                                method: 'POST',
                                url: apiJson.urls.assetManagement.approveAssetRecoveryList,
                                data: $scope.assetRecoveryList
                            }).then(function success(response) {
                                $scope.assetRecoveryList.map(function (mapping) {
                                    mapping.recoveryStatus = 'INITIATED';
                                })
                            }, function error(response) {
                                console.log("Encountered an error",response);
                                $toastService.create('Error approving assets, please contact support');
                            });
                        }
                    });
                }
            }
        ]
    );