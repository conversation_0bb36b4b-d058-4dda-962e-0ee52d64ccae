'use strict';
angular.module('scmApp')
    .controller('currentInventoryCtrl', ['$rootScope', '$scope', 'appUtil', 'apiJson', '$toastService', '$http', 'metaDataService',
            'pagerService', function ($rootScope, $scope, appUtil, apiJson, $toastService, $http, metaDataService, pagerService) {
                $scope.initStock = function () {
                    $scope.unitId = appUtil.getCurrentUser().unitId;
                    $scope.selectedSkus = [];
                    $scope.skuMap = {};
                    console.log(appUtil.getScmProductDetails())
                    Object.values(appUtil.getSkuProductMap()).concat().forEach(function (skuList) {
                        for (var i in skuList) {
                            var sku = skuList[i];
                            $scope.skuMap[sku.skuId] = sku;
                        }
                    });

                    metaDataService.getUnitToSkuMappings($scope.unitId, function (response) {
                        $scope.skuList = response;
                        console.log(response)
                        console.log($scope.skuMap)
                        $scope.selectList = Object.values($scope.skuMap).filter(function (sku) {

                            return $scope.skuList.indexOf(sku.skuId) != -1 && sku.skuStatus == "ACTIVE" && checkForFixedAsset(sku);
                        });
                    });

                };

                function checkForFixedAsset(sku) {
                    var flag = false;
                    var product=appUtil.getScmProductDetails();
                    for(var i in product) {
                        if (product[i].productId == sku.linkedProduct.id && product[i].categoryDefinition.id !=3) {
                            flag = true;
                            break;
                        }
                    }
                    return flag;

                }

                $scope.removeDuplicateKey = function(){
                    $http({
                        method: 'PUT',
                        url: apiJson.urls.stockManagement.removeDuplicateKeys
                    }).then(function success(response) {
                        if (response.status == 200 && response.data != null) {
                            if(response.data == true){
                                $toastService.create("Removed duplicate Keys successfully");
                            }
                            else{
                                $toastService.create("Unable to remove duplicate Keys");
                            }
                        }
                    }, function error(response) {
                        $toastService.create("Error in removing dupliocate keys");
                    });
                };

                $scope.getStock = function (skuList) {
                    if (appUtil.isEmptyObject(skuList)) {
                        skuList = $scope.skuList;
                    }
                    $http({
                        url: apiJson.urls.warehouseClosing.stockAtHand,
                        method: 'POST',
                        data: {
                            unitId: $scope.unitId,
                            skuIds: skuList
                        }
                    }).then(function (stock) {
                        if (appUtil.isEmptyObject(stock.data)) {
                            $toastService.create("could not fetch current stock at hand for this unit");
                        } else {
                            $scope.stock = stock.data;
                            setProducts();
                        }
                    }, function () {
                        $toastService.create("could not fetch current stock at hand for this unit");
                    });
                };


                function setProducts() {
                    if (!appUtil.isEmptyObject($scope.stock)) {
                        $scope.stock.forEach(function (item) {
                            item.name = $scope.skuMap[item.skuId].skuName;
                        });
                        $scope.setClosingDates($scope.stock);
                        $scope.sortItems($scope.stock);
                    }
                }

                function convertToCsv(objArray) {
                    var header = Object.keys(objArray[0]).filter(function (title) {
                        return ["inventoryId", "consumptionOrder", "expiryDate", "keyId", "variance", "varianceCost"].indexOf(title) == -1;
                    });
                    var str = header.join(",") + '\r\n';
                    for (var i = 0; i < objArray.length; i++) {
                        var line = '';
                        for (var j in header) {
                            if (line != '') {
                                line += ',';
                            }
                            line += objArray[i][header[j]];
                        }
                        str += line + '\r\n';
                    }
                    return str;
                }

                $scope.downloadStock = function () {
                    var fileName = "Current_Inventory_" + $scope.unitId + ".csv";
                    var data = convertToCsv($scope.stock);
                    var blob = new Blob([data], {type: 'text/csv'}, fileName);
                    saveAs(blob, fileName);
                };

                $scope.sortItems = function (stockItems) {
                    if ($scope.ascendingOrder == undefined) {
                        $scope.ascendingOrder = true;
                    }
                    stockItems.sort(function (item1, item2) {
                        if ($scope.ascendingOrder) {
                            return item1.stockValue - item2.stockValue;
                        } else {
                            return item2.stockValue - item1.stockValue;
                        }
                    });
                    $scope.ascendingOrder = !$scope.ascendingOrder;
                    $scope.setPage(1); // reset page of inventory items
                };

                $scope.setClosingDates = function (stock) {
                    var skuIds = stock.map(function (sku) {
                        return sku.skuId;
                    }).join(",");
                    $http({
                        url: apiJson.urls.warehouseClosing.closingDates,
                        method: 'POST',
                        data: {
                            unitId: $scope.unitId,
                            skus: skuIds
                        }
                    }).then(function (response) {
                        if (appUtil.isEmptyObject(response.data)) {
                            $toastService.create("Could not fetch closing dates for SKUs on the Unit");
                        } else {
                            var closingDates = response.data;
                            $scope.stock.forEach(function (sku) {
                                sku["closingDate"] = closingDates[sku.skuId];
                            });
                        }
                    }, function () {
                        $toastService.create("Could not fetch closing dates for SKUs on the Unit");
                    });
                };


                $scope.setPage = function (page) {
                    if ($scope.pager == undefined) {
                        $scope.pager = pagerService.getPager($scope.stock.length, page);
                        $scope.page = page;
                    } else {
                        if (page < 1 || page > $scope.pager.totalPages) {
                            return;
                        }
                        // get pager object from service
                        $scope.pager = pagerService.getPager($scope.stock.length, page);
                        $scope.page = $scope.pager.currentPage;
                        // get current page of items
                    }
                    $scope.inventoryItems = $scope.stock.slice($scope.pager.startIndex, $scope.pager.endIndex + 1);
                };

            }
        ]
    );