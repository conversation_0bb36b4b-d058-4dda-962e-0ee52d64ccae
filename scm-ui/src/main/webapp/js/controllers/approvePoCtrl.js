'use strict';
angular.module('scmApp').controller('approvePoCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http',
    'appUtil', '$toastService','$alertService','metaDataService','Popeye',
    function ($rootScope, $stateParams, $scope, apiJson, $http, appUtil, $toastService,$alertService,metaDataService,Popeye) {

        function getCreatedPOs(view,startDate,endDate,poId,unitId) {
            if(appUtil.isEmptyObject(startDate)){
                $toastService.create("Please select a start date first");
                return;
            }
            if(appUtil.isEmptyObject(endDate)){
                $toastService.create("Please select a end date first");
                return;
            }

            var params = {
                deliveryUnitId:$scope.currentUser.unitId,
                isView:view,
                startDate:startDate,
                endDate:endDate,
                purchaseOrderId:poId
            };

            if(!appUtil.isEmptyObject($scope.vendorSelected)){
                params["vendorId"] = $scope.vendorSelected.vendorId;
            }

            if(!appUtil.isEmptyObject($scope.selectedSKU)){
                params["skus"] = [$scope.selectedSKU];
            }

            if(!appUtil.isEmptyObject($scope.selectedStatus)){
                params["status"] = $scope.selectedStatus;
            }

            if(!appUtil.isEmptyObject($scope.unitSelected)){
                params["deliveryUnitId"] = $scope.unitSelected.id;
            }

            $http({
                method: "GET",
                url: apiJson.urls.requestOrderManagement.getCreatedOrders,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Orders found!");
                } else {
                    $scope.poRequest = response.data.filter($scope.byApprover);
                    // console.log($scope.poRequest);
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.downloadExcell = function (){
            var params = {
                className : "com.stpl.tech.scm.domain.model.PurchaseOrder"
            }
            var jsonStrings = [];
            for(var i = 0;i<$scope.poRequest.length;i++){
                jsonStrings.push(JSON.stringify($scope.poRequest[i]));
            }
            metaDataService. downloadExcell(jsonStrings,params);
        }


        function updatePO(url, callback) {
            $alertService.confirm("Are you sure?","",function(result){
                if(result){
                    $http({
                        method: "POST",
                        url: url
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response)) {
                            callback(response.data);
                        }
                    }, function (response) {
                        if (response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                        }
                        callback(false);
                        console.log("Encountered error at backend", response);
                    });
                }
            });

        }

        $scope.byApprover = function (poR) {
            if($scope.showViewActions){
                return true;
            }else{
                if($scope.approverLevel == 1){
                    return poR.paidAmount <= 50000;
                }

                if($scope.approverLevel == 2){
                   return true;
                }
            }
        };

        $scope.openViewChart = function (skuData) {
            if(appUtil.isEmptyObject($scope.approvedSku[skuData.skuId])) {
                $toastService.create("No consumption data found for the product");
                return;
            }
            var consumptionModal = Popeye.openModal({
                templateUrl : 'consumptionApprove.html',
                controller : "consumptionApproveCtrl",
                modalClass: 'modal',
                resolve : {
                    sku : function() {
                        return $scope.approvedSku[skuData.skuId] ;
                    },
                    selectedVendor: function () {
                        return $scope.selectedVendor;
                    },
                    selectedDispatchLocation: function () {
                        return $scope.selectedDispatchLocation;
                    },
                    currentUnit: function () {
                        return $scope.currentUnit;
                    },
                    pendingForSku:function () {
                        if(!appUtil.isEmptyObject($scope.skuLookUp) && !appUtil.isEmptyObject($scope.skuLookUp[skuData.skuId])){
                            return $scope.skuLookUp[skuData.skuId];
                        }else{
                            return null;
                        }
                    },
                },
                click : true,
                keyboard : true
            });
        };

        $scope.init = function () {
            var currentDate = appUtil.getCurrentBusinessDate();
            if(!appUtil.isEmptyObject(currentDate)){
                $scope.approvedSku={};
                $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.endDate = $scope.startDate;
                $scope.showViewActions = $stateParams.viewPO;
                $scope.currentUnit = appUtil.getUnitData();
                $scope.createdPO = $stateParams.createdPO;
                $scope.poRequest = [];
                $scope.availableReasons=['Raw material issue','Labour issue','Transport issue','Production issue','Holiday','Payment issue','Others'];
                $scope.userMappingUnits = appUtil.getUnitList();
                $scope.selectedPO = null;
                $scope.selectedUnit = null;
                $scope.txnStatus = ["","CREATED", "APPROVED", "REJECTED", "CLOSED", "CANCELLED","IN_PROGRESS"];
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.isPurchaser = true;
                $scope.approverLevel = 1;
                for(var i in $rootScope.purchaseRoles){
                    var role = $rootScope.purchaseRoles[i];
                    if(role.code.indexOf("_L2")!=-1){
                        $scope.approverLevel = 2;
                    }
                }

                metaDataService.getVendorsForUnit($scope.currentUser.unitId,function(vendorsForUnit){
                    $scope.vendors = vendorsForUnit;
                });

                metaDataService.getSkuListForUnit($scope.currentUser.unitId,function(skuForUnitList){
                    $scope.skus = skuForUnitList;
                });

                getCreatedPOs($scope.showViewActions, $scope.startDate, $scope.endDate, $scope.poId,$scope.currentUser.unitId);
            }
        };

        $scope.reset = function () {
          $scope.selectedSKU = null;
          $scope.selectedVendor = null;
        };

        $scope.selectVendor = function (vendor) {
          $scope.vendorSelected = vendor;
        };

        $scope.selectUnit = function (unit) {
            $scope.unitSelected = unit;
          };

        $scope.selectSKU = function (sku) {
            $scope.selectedSKU = sku;
        };

        $scope.downloadPO = function(poInvoice){
            metaDataService.downloadDocument(poInvoice);
        };

        $scope.getPOs = function(){
          getCreatedPOs($scope.showViewActions,$scope.startDate,$scope.endDate,$scope.poId,$scope.currentUser.unitId);
        };

        $scope.showDetails = function (index) {
            $scope.selectedPO = $scope.poRequest[index];
        };
        $scope.showApproveDetails = function (poR,index) {
            $scope.selectedPOR = $scope.poRequest[index];
            $scope.selectedVendor=$scope.selectedPOR.generatedForVendor;
            $scope.selectedDispatchLocation=$scope.selectedPOR.dispatchLocation;
            $scope.getPriceAndTaxData();
            $scope.getPendingOrders();
            console.log($scope.selectedPOR);


            $scope.selectedpoRId = poR;
            $scope.seletedpoRIndex = index;
        };

        // Approve consumption data//
        $scope.getPriceAndTaxData = function(){
            if(!appUtil.isEmptyObject($scope.selectedPOR.generatedForVendor.id)
                && !appUtil.isEmptyObject($scope.selectedPOR.dispatchLocation.dispatchId)){
                getSKUPriceAndTaxesForProfile()
            }else{
                $toastService.create("Please select Vendor Dispatch Location correctly");
            }
        };

        function getSKUPriceAndTaxesForProfile() {
            var roleIds = $rootScope.purchaseRoles.map(function (role) {return role.id;}).join(",");
            metaDataService.getSkuPricesAndTaxesForProfile($scope.selectedPOR.generatedForVendor.id,
                $scope.selectedPOR.dispatchLocation.dispatchId, $scope.currentUnit.id, roleIds, function(skuAndTaxData){
                    if(skuAndTaxData.length>0){
                        $scope.skuList = skuAndTaxData;
                        $scope.getConsumptionData($scope.skuList);
                    }else{
                        $toastService.create("Could not fetch prices for the dispatch location");
                        $scope.skuList = [];
                        $scope.packagingList = [];
                    }
                });
        }
        $scope.getConsumptionData = function (skuList) {
            var skuArr = skuList.map(function(sku){
                return sku.id;
            });
            var skuStr = skuArr.join(",");
            $http({
                method:"GET",
                url:apiJson.urls.requestOrderManagement.purchaseConsumption,
                params:{
                    skus: skuStr,
                    unitId: $scope.currentUnit.id,
                    days: 90
                }
            }).then(function(response){
                if(!appUtil.isEmptyObject(response.data)){
                    $scope.getCurrentStock(skuArr,response.data, addToSkuList);
                }
            },function(error){
                console.log(error);
            });
        };

        $scope.getCurrentStock = function (skuList,consumptionData, callback) {
            $http({
                url:apiJson.urls.warehouseClosing.stockAtHand,
                method:'POST',
                data:{
                    unitId:$scope.currentUnit.id,
                    skuIds:skuList
                }
            }).then(function(stock){
                if(appUtil.isEmptyObject(stock.data)){
                    $toastService.create("could not fetch current stock at hand for this unit");
                }else{
                    if (typeof callback == "function"){
                        callback(consumptionData, stock.data);
                    }
                }
            },function(){
                $toastService.create("could not fetch current stock at hand for this unit");
            });
        };

        function addToSkuList(response, currentStock) {

            var consumption = [];

            for(var i in response){
                if(appUtil.isEmptyObject(consumption[response[i].id])){
                    consumption[response[i].id] = {};
                    consumption[response[i].id]['total'] = 0;
                }
                consumption[response[i].id][response[i].date] = response[i].qty;
                consumption[response[i].id]['total'] += response[i].qty;
            }

            $scope.skuList.forEach(function (sku) {
                var consumed=0, total=0;
                if(!appUtil.isEmptyObject(consumption[sku.id])){
                    total = consumption[sku.id]['total'];
                    delete consumption[sku.id]['total'];
                    consumed = consumption[sku.id];
                }
                sku['consumed'] = consumed;
                sku['totalConsumed'] = total.toFixed(2);
                sku['currentStock'] = findInCurrentStock(currentStock,sku.id);
                $scope.approvedSku[sku.id] = sku;
            });
        }

        function makeSkuLookUp(pendingPOs) {
            $scope.skuLookUp = {};
            for(var i in pendingPOs){
                for(var j in pendingPOs[i].orderItems){
                    if(appUtil.isEmptyObject($scope.skuLookUp[pendingPOs[i].orderItems[j].skuId])){
                        $scope.skuLookUp[pendingPOs[i].orderItems[j].skuId] = {
                            poList:[],
                            received:0,
                            requested:0
                        };
                    }
                    var sku = $scope.skuLookUp[pendingPOs[i].orderItems[j].skuId];
                    if(sku.poList.indexOf(pendingPOs[i].receiptNumber) == -1){
                        sku.poList.push(pendingPOs[i].receiptNumber);
                        sku.received += pendingPOs[i].orderItems[j].receivedQuantity;
                        sku.requested += pendingPOs[i].orderItems[j].requestedAbsoluteQuantity;
                    }
                }
            }
        }

        $scope.getPendingOrders = function(){
            if($scope.selectedDispatchLocation==null){
                return;
            }
            $http({
                method:"GET",
                url:apiJson.urls.requestOrderManagement.getPendingPOs,
                params:{
                    vendorId:$scope.selectedVendor.id,
                    deliveryUnitId:$scope.currentUnit.id,
                    dispatchId:$scope.selectedDispatchLocation.dispatchId,
                    startDate:appUtil.formatDate(appUtil.getDate(-365), "yyyy-MM-dd"),
                    endDate:appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd"),
                    purchaseOrderId:$scope.poId,
                    status: $scope.selectedStatus
                }
            }).then(function(response){
                if(!appUtil.isEmptyObject(response.data)) {
                    makeSkuLookUp(response.data);
                }
            },function(error){
                console.log(error);
            });
        };
        function findInCurrentStock(skuList, skuId) {
            for(var i in skuList){
                if(skuList[i].skuId == skuId){
                    var value = skuList[i].stockValue;
                    console.log("stockvalue",value);
                    return skuList[i].stockValue;
                }
            }
            return 0;
        }


        $scope.getApprove = function (approveID, index) {
            var url = apiJson.urls.requestOrderManagement.approvePO + "/" + approveID + "/" + $scope.currentUser.userId;
            updatePO(url, function (updated) {
                if(updated){
                    $alertService.alert("Congratulations!!",
                        "Purchase Order: "+approveID+" approved successfully <br> Vendor has also been notified",
                        function(result){
                            $scope.poId = null;
                            getCreatedPOs($scope.showViewActions,$scope.startDate,$scope.endDate,$scope.poId,$scope.currentUser.unitId);
                        });
                }else{
                    $toastService.create("Purchase Order approval failed! Please try again later..");
                }
            });
        };

        $scope.getReject = function (approveID, index) {
            var url = apiJson.urls.requestOrderManagement.rejectPO + "/" + approveID + "/" + $scope.currentUser.userId;
            updatePO(url, function (updated) {
                if(updated){
                    $toastService.create("Purchase Order rejected successfully");
                    $scope.poRequest[index].status = "REJECTED";
                }else{
                    $toastService.create("Purchase Order rejection failed! Please try again later..");
                }
            });
        };

        $scope.cancelPO = function (approveID, index) {
            var url = apiJson.urls.requestOrderManagement.cancelPO + "/" + approveID + "/" + $scope.currentUser.userId;
            updatePO(url, function (updated) {
                if(updated){
                    $toastService.create("Purchase Order cancelled successfully");
                    $scope.poRequest[index].status = "CANCELLED";
                }else{
                    $toastService.create("Purchase Order cancellation failed! Please try again later..");
                }
            });
        };

        $scope.description = function (selectedpoR,index) {
            $scope.selectedpoR = selectedpoR;
            if (appUtil.formatDate(appUtil.calculatedDate(1, selectedpoR.expiryDate), "yyyy-MM-dd") < appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd")) {
                $scope.minDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
            }
            else {
                $scope.minDate = appUtil.formatDate(appUtil.calculatedDate(1, selectedpoR.expiryDate), "yyyy-MM-dd");
            }
            $scope.maxDate = appUtil.formatDate(appUtil.calculatedDate(selectedpoR.leadTime,selectedpoR.expiryDate),"yyyy-MM-dd");
        };

        $scope.changeSelectedDate = function(date){
            $scope.updatedExpiryDate = date;

        };

        $scope.extendPO = function (extendID,vendorID,extendsiondescription,selectedReason,index) {
            if(appUtil.isEmptyObject(selectedReason)){
                $toastService.create("Please select a reason first");
                return;
            }

            if(appUtil.isEmptyObject(extendsiondescription)){
                $toastService.create("Please provide a description first");
                return;
            }
            if(appUtil.isEmptyObject($scope.updatedExpiryDate)){
                $toastService.create("Please select a Extension Date first");
                return;
            }
            var reqObj = {
                purchaseOrderId : extendID,
                vendorId : vendorID,
                description :  extendsiondescription,
                extensionReason : selectedReason,
                updatedBy: $scope.currentUser.userId,
                updatedExpiryDate : $scope.updatedExpiryDate
            };

            $http({
                method: "POST",
                url: apiJson.urls.requestOrderManagement.extendPO,
                data: reqObj
            }).then(function success(response) {
                if (response.data) {
                    $toastService.create("Purchase Order Extended successfully");
                    $scope.selectedpoR.hideExtend = true;
                    $scope.selectedReason=null;
                    $scope.extensionDescription=null;
                    $scope.extendedDate=null;
                    $scope.selectedpoR.expiryDate=$scope.updatedExpiryDate;
                    return;
                } else{
                    $toastService.create("Purchase Order Extension failed! Please try again later..");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.closePO = function(closureId, index){
            var url = apiJson.urls.requestOrderManagement.closePO + "/" + closureId + "/" + $scope.currentUser.userId;
            updatePO(url, function (updated) {
                if(updated){
                    $toastService.create("Purchase Order closed successfully");
                    $scope.poRequest[index].status = "CLOSED";
                }else{
                    $toastService.create("Purchase Order closure failed! Please try again later..");
                }
            });
        };
    }
]).controller('consumptionApproveCtrl', ['$scope','sku','selectedVendor','selectedDispatchLocation','currentUnit', 'pendingForSku', 'appUtil',
    function ($scope, sku, selectedVendor, selectedDispatchLocation, currentUnit, pendingForSku, appUtil) {

        function createChartOptions(sku) {
            //var labels = Object.keys(sku['consumed']).map(function(label){return appUtil.formatDate(label,"yyyy-MM-dd");});
            var data = [];
            var labels = prepareRollingLabels();
            var dateWise = prepareDistribution(sku['consumed']);
            console.log("datewise",dateWise);
            for(var i in dateWise){
                data[i] = parseFloat(dateWise[i]);
            }
            return {
                chart: {type: 'column', marginLeft:70},
                title: {text: 'Consumption over last 90 days'},
                xAxis: {categories: labels},
                yAxis: {title: {text: 'Qty'}, labels:{enabled:true}, min:0},
                series: [{
                    name:sku.skuData.name,
                    data:data
                }]
            };
        }

        function prepareRollingLabels() {
            var date1= appUtil.formatDate(appUtil.getTimeInPast(90),"yyyy-MM-dd");
            var date2= appUtil.formatDate(appUtil.getTimeInPast(60),"yyyy-MM-dd");
            var date3= appUtil.formatDate(appUtil.getTimeInPast(30),"yyyy-MM-dd");
            var date4= appUtil.formatDate(appUtil.getTimeInPast(0),"yyyy-MM-dd");
            return [date1 +" to "+ date2, date2+" to "+date3, date3+" to "+date4];
        }

        function prepareDistribution(consumption) {
            var toDate = new Date();
            var distribution = [0,0,0];
            for(var i in consumption){
                var chunk = Math.abs(Math.ceil(appUtil.datediff(i, toDate) / 30) - 3);
                console.log(chunk);
                if(appUtil.isEmptyObject(distribution[chunk])){
                    distribution[chunk]=0;
                }
                distribution[chunk] = parseFloat(distribution[chunk]) + parseFloat(consumption[i]);
            }
            return distribution;
        }

        $scope.initChart = function () {
            $scope.sku = sku;
            $scope.chartOptions = createChartOptions(sku);
            $scope.selectedVendor = selectedVendor;
            $scope.selectedDispatchLocation = selectedDispatchLocation;
            $scope.currentUnit = currentUnit;
            $scope.pendingForSku = pendingForSku;
        };

    }
]);
