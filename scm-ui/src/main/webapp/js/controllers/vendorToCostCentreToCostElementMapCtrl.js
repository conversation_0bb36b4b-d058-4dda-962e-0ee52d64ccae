angular.module('scmApp').controller(
    'vendorToCostCentreToCostElementMapCtrl',
    ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
                  previewModalService, Popeye, $timeout, $window) {

            $scope.init = function () {
            	$scope.mappingTable = [];
                $scope.selectVendorIdToClone=null;
            	$scope.vendors = [];
            	 metaDataService.getServiceVendors(function(serviceVendors){
                     $scope.vendors = serviceVendors;
                 });
            }

            $scope.getCostCentreList = function(){
        		$http({
        			url : apiJson.urls.serviceMappingManagement.getCostCentreList,
        			method : 'GET'
        		}).then(function success(response) {
        			if(response.data != null){
        				$scope.costCentreList = response.data;
        			}
        		}, function error(response) {
                       console.log("error:" + response);
                  })
        	}
            
            $scope.searchMappings = function () {
                if ($scope.selectedVendor == undefined || $scope.selectedVendor == null
                    || $scope.selectedVendor.id == 0 || $scope.selectedCostCenter == undefined || $scope.selectedCostCenter == null
                    || $scope.selectedCostCenter.id == 0) {
                    return;
                }
                $scope.getVendorCostCenterCostElementMappingsGrid();
            }
            
            $scope.getVendorCostCenterCostElementMappingsGrid = function () {
                $scope.gridOptions = $scope.costElementGridOptions();
                $scope.getAllCostElementMappings();
            }
            
            $scope.getAllCostElementMappings = function(){
            	$http({
        			url : apiJson.urls.serviceMappingManagement.getPricedCostElements,
        			method : 'GET',
        			params : {
        				vendorId: $scope.selectedVendor.id,
        				costCenterId: $scope.selectedCostCenter.id
        			}
        		}).then(function success(response) {
        			if(response.data != null){
        				$scope.gridOptions.data = response.data;
        			}
        		}, function error(response) {
                       console.log("error:" + response);
                  })
            }
            
            
            $scope.costElementGridOptions = function () {
                return {
                	 enableColumnMenus: false,
                     enableFiltering: true,
                     enableCellEditOnFocus: true,
                     enableColumnResizing: true,
                     /*cellEditableCondition: function ($scope) {
                         return $scope.row.entity.status === 'ACTIVE';
                     },*/
                    columnDefs: [{
                        field: 'id',
                        displayName: 'Cost Element Id',
                        enableCellEdit: false
                    }, {
                        field: 'name',
                        displayName: 'Cost Element Name',
                        enableCellEdit: false
                    },{
                        field: 'currentPrice',
                        displayName: 'Current Price',
                        enableCellEdit: false
                    },{
                        field: 'updatedPrice',
                        displayName: 'New Price',
                        enableCellEdit: true,
                        type: 'number'
                    }, {
                        field: 'status',
                        displayName: 'Cost Element Status',
                        enableCellEdit: false,
                        cellTemplate: 'statusBatch.html'
                    }, {
                        field: 'code',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeButton.html'
                    }],
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                            // to display update
                            if (newValue) {
                                rowEntity.update = true;
                            }
                            console.log(JSON.stringify(rowEntity));
                            $scope.$apply();
                        });
                    }
                };
            }
            
            $scope.addRow = function (value) {
            	var currentUser = appUtil.getCurrentUser();
            	 $http({
                     url: apiJson.urls.serviceMappingManagement.addCostelementPriceMapping,
                     method: 'GET',
                     params:{
                    	 costElementId: value.id,
                    	 vendorId: $scope.selectedVendor.id,
         				 costCenterId: $scope.selectedCostCenter.id,
                    	 price: value.updatedPrice,
                    	 employeeId: currentUser.userId,
                         employeeName: currentUser.user.name
                     }
                 }).then(function (response) {
                     if (response.data == true) {
                         $toastService.create("Mapping Added Successfully");
                         $scope.updatePriceGridRow(value);
                     }
                 }, function (response) {
                     console.log("error", response);
                 });
            }

            $scope.changeStatus = function (value) {
                var costElementPriceId = null;
                var status = value.status == 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
                costElementPriceId = value.costElementPriceId;
                var currentUser = appUtil.getCurrentUser();
                var payload = {
                	costElementId: costElementPriceId,
                    status: status,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }

                $http({
                    url: apiJson.urls.serviceMappingManagement.updateStatusCostelementPriceMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data == true) {
                        $scope.updateGridRowStatus(value, status);
                        var msg = 'Mapping '
                        msg = msg + (status == 'ACTIVE' ? 'Activated' : 'Deactivated')
                        msg = msg + ' Successfully'
                        $toastService.create(msg);
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };
            
            $scope.updateGridRowStatus = function (value, status) {
                var x = null;
                var id = null;
                for (x in $scope.gridOptions.data) {
                    if ($scope.gridOptions.data[x].id == value.id) {
                        id = x;
                    }
                }
                if (id != null) {
                    $scope.gridOptions.data[id].status = status;
                }
            }
            
            $scope.updateRow = function (value) {
                if (value.updatedPrice == null) {
                    var msg = "Please fill updated price."
                    $toastService.create(msg);
                    return;
                }
                var currentUser = appUtil.getCurrentUser();
                $http({
                    url: apiJson.urls.serviceMappingManagement.updatePriceCostElementMapping,
                    method: 'GET',
                    params: {
                    	costElementMappingId:value.costElementPriceId,
                        price: value.updatedPrice,
                        employeeId: currentUser.userId,
                        employeeName: currentUser.user.name
                    }
                }).then(function (response) {
                    if (response.data) {
                    	//$scope.getAllCostElementMappings();
                        //value.update = null;
                        $scope.updatePriceGridRow(value);
                        var msg = 'Update Submitted Successfully';
                        $toastService.create(msg);
                        //$scope.showPricing();
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.updatePriceGridRow = function (value) {
                var x = null;
                var id = null;
                for (x in $scope.gridOptions.data) {
                    if ($scope.gridOptions.data[x].id == value.id) {
                        id = x;
                    }
                }
                if (id != null) {
                    $scope.gridOptions.data[id].currentPrice = value.updatedPrice;
                    $scope.gridOptions.data[id].updatedPrice = null;
                    $scope.gridOptions.data[id].status = "ACTIVE";
                }
            };

            $scope.getClonedPriceFromVendor = function (){
                if($scope.gridOptions == null || $scope.gridOptions.data == null){
                    $toastService.create("Select Vendor Name and Cost Center Name");
                    return;
                }
                if($scope.gridOptions.data.length == 0){
                    $toastService.create("No mapping found for price cloning");
                    return;
                }
                if($scope.selectVendorIdToClone == null){
                    $toastService.create("Select A vendor to clone pricing");
                    return;
                }
                if($scope.selectedVendor.id == $scope.selectVendorIdToClone.id){
                    $toastService.create("Cannot clone from same vendor");
                    return;
                }
                var costElementIdZeroPrice = [];
                for(var i in $scope.gridOptions.data){
                    if($scope.gridOptions.data[i].currentPrice == 0 && $scope.gridOptions.data[i].updatedPrice==null){
                        costElementIdZeroPrice.push($scope.gridOptions.data[i].id);
                    }
                }
                if(costElementIdZeroPrice.length == 0){
                    $toastService.create("No mapping found for price cloning");
                    return;
                }
                $http({
                    url: apiJson.urls.serviceMappingManagement.clonePricingFromVendor,
                    method: 'GET',
                    params: {
                        vendorId: $scope.selectVendorIdToClone.id,
                        costElementIds: costElementIdZeroPrice.join(","),
                        costCenterId: $scope.selectedCostCenter.id
                    }
                }).then(function (response) {
                    if(response.data != null){
                        if(response.data.length > 0){
                            $scope.setNewPricingFromCloneVendor(response.data, costElementIdZeroPrice);
                        }else{
                            $toastService.create("No valid price mapping found");
                        }
                    }
                }, function (response) {
                    console.log("error", response);
                    $toastService.create("Unable to Clone Pricing!");
                });
            }

            $scope.setNewPricingFromCloneVendor = function (data){
                for(var i in data){
                    for(var j in $scope.gridOptions.data){
                        if($scope.gridOptions.data[j].id == data[i].id){
                            $scope.gridOptions.data[j].updatedPrice=data[i].currentPrice;
                        }
                    }
                }
            }


    }]);
