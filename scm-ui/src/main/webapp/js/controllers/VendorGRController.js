'use strict';

angular.module('scmApp')
    .controller('vendorGrCreateCtrl', ['$rootScope', '$scope','$state', 'apiJson', '$http', 'appUtil','metaDataService','$fileUploadService',
        '$toastService','$alertService','previewModalService', function ($rootScope, $scope, $state, apiJson, $http, appUtil, metaDataService,
                                $fileUploadService,$toastService, $alertService, previewModalService) {

            function getMinDate(poList){
                if(!appUtil.isEmptyObject(poList) && poList.length>0){
                    return poList[poList.length-1].generationTime;
                }else{
                    return appUtil.getDate(-365);
                }
            }


            $scope.init = function (){
                $scope.maxDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
                $scope.minDate = appUtil.formatDate(getMinDate(), "yyyy-MM-dd");
                $scope.locationList = [];
                $scope.pendingPOs = [];
                $scope.clearData();
                $scope.currentUnit = appUtil.getUnitData();
                $scope.vendorRegCheck = true;
                metaDataService.getVendorsForUnitTrimmed($scope.currentUnit.id,function(vendorsForUnit){
                        $scope.vendorList = vendorsForUnit;
                    });
                $scope.showPreview = previewModalService.showPreview;
                $scope.selectedPOType = 'REGULAR_ORDER';
                if(appUtil.isCafe()){
                    $scope.selectedPOType = 'REGULAR_ORDER';
                }
                $scope.disableEnableGRType = false;
                $scope.selectedType = null;
                $scope.resetCapexOpex();
                $scope.disableCapex = false;
                $scope.disableOpex = false;
                $scope.duplicateSelectedPo = [];
            };

            $scope.grSelected = function() {
                if(!appUtil.isEmptyObject($scope.pendingPOs)){
                    angular.forEach($scope.pendingPOs, function(po, index) {
                        if(po.checked != null){
                            po.checked = false;
                        }
                    })
                    $scope.disableCapex = false;
                    $scope.disableOpex = false;
                    $scope.duplicateSelectedPo = [];
                }
            }

            $scope.getPendingPoByType = function () {
                if(!appUtil.isEmptyObject($scope.pendingPOs)){
                    angular.forEach($scope.pendingPOs, function(po) {
                        if (po.orderType == 'REGULAR_ORDER') {
                            if(po.type == "CAPEX") {
                                $scope.pendingCapexRegularPo = $scope.pendingCapexRegularPo + 1;
                            } else {
                                $scope.pendingOpexRegularPo =  $scope.pendingOpexRegularPo + 1;
                            }
                        }
                        if (po.orderType == 'FIXED_ASSET_ORDER') {
                            if(po.type == "CAPEX") {
                                $scope.pendingCapexAssetPo = $scope.pendingCapexAssetPo + 1;
                            } else {
                                $scope.pendingOpexAssetPo =  $scope.pendingOpexAssetPo + 1;
                            }
                        }
                    })
                }
            };

            $scope.disableSelection = function ($event,type) {
                console.log("duplicate arr is ",$scope.duplicateSelectedPo);
                if ($scope.duplicateSelectedPo.length == 0) {
                    if ($event.target.checked) {
                        $scope.duplicateSelectedPo.push(type);
                        if (type == "CAPEX") {
                            $scope.disableOpex = true;
                        }
                        else {
                            $scope.disableCapex = true;
                        }
                    }
                }
                else {
                    if ($event.target.checked) {
                        $scope.duplicateSelectedPo.push(type);
                        if (type == "CAPEX") {
                            $scope.disableOpex = true;
                        }
                        else {
                            $scope.disableCapex = true;
                        }
                    }
                    else {
                        $scope.duplicateSelectedPo.pop();
                        if ($scope.duplicateSelectedPo.length == 0) {
                            $scope.disableOpex = false;
                            $scope.disableCapex = false;
                        }
                    }
                }
                console.log("duplicate arr is ",$scope.duplicateSelectedPo);
            };

            $scope.clearData = function(){
                $scope.selectedOrders = [];
                $scope.showExpandedView = false;
                $scope.grItems = null;
                $scope.extraGrItems = undefined;
                $scope.gr = null;
                $scope.showGrItems = false;
            };

            $scope.selectVendor = function(vendor){
                metaDataService.getVendorDetail(vendor.id,function(vendor){
                	$scope.selectedVendor = vendor;
	                $scope.locationList = vendor.dispatchLocations.filter(function (loc) {
	                    return loc.status == "ACTIVE";
	                })
                });
                $scope.pendingPOs = null;
            };

            $scope.selectDispatchLocation = function(location){
                $scope.selectedDispatchLocation = location;
                if($scope.selectedDispatchLocation.gstin == null){
                	$scope.vendorRegCheck = false;
                }
                $scope.getCurrentPrices();
            };

            function priceExists(skuId) {
                var list = $scope.skuPriceList.filter(function(skuTax){
                    return skuId == skuTax.id;
                });
                return list.length>0;
            }

            $scope.getCurrentPrices = function(){
                if(appUtil.isEmptyObject($scope.selectedDispatchLocation)){
                    $toastService.create("Select Dispatch Location first");
                    return;
                }
                if(appUtil.isEmptyObject($scope.selectedPOType)){
                    $toastService.create("Select GR type");
                    return;
                }
                metaDataService.getSkuPricesAndTaxes($scope.selectedVendor.vendorId,
                    $scope.selectedDispatchLocation.dispatchId,
                    $scope.currentUnit.id,
                    function(skuAndTaxData){
                        if(skuAndTaxData.length>0){
                            $scope.skuPriceList = skuAndTaxData;
                            $scope.getPendingOrders();
                        }else{
                            $toastService.create("Could not fetch prices & taxes for the dispatch location");
                        }
                    }
                );
            };

            $scope.resetCapexOpex = function () {
                $scope.pendingCapexRegularPo = 0;
                $scope.pendingCapexAssetPo = 0;
                $scope.pendingOpexRegularPo = 0;
                $scope.pendingOpexAssetPo = 0;
            };

            $scope.getPendingOrders = function(){
                $scope.resetCapexOpex();
                $http({
                    method:"GET",
                    url:apiJson.urls.requestOrderManagement.getPendingPOs,
                    params:{
                        vendorId:$scope.selectedVendor.vendorId,
                        deliveryUnitId:$scope.currentUnit.id,
                        dispatchId:$scope.selectedDispatchLocation.dispatchId,
                        startDate:appUtil.formatDate(appUtil.getDate(-365), "yyyy-MM-dd"),
                        endDate:appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd")
                    }
                }).then(function(response){
                    $scope.pendingPOs = response.data;
                    $scope.getPendingPoByType();
                    if(appUtil.isEmptyObject($scope.pendingPOs)){
                        $toastService.create("Could not find pending Purchase Orders!");
                    }
                },function(error){
                    console.log(error);
                });
            };

            $scope.changeTotalAmount = function(){
                if($scope.gr.extraCharges>=parseFloat(1)){
                    $scope.gr.totalCharged = $scope.gr.totalAmount + $scope.gr.extraCharges;
                }else{
                    $scope.gr.totalCharged = $scope.gr.totalAmount;
                    $toastService.create("Extra Charges cannot be less than 1");
                }
            };

            $scope.goBack = function () {
                $scope.pendingPOs.forEach(function(po){
                   po.checked = false;
                });
                $scope.clearData();
                $scope.disableEnableGRType = false;
                $scope.disableCapex = false;
                $scope.disableOpex = false;
                $scope.duplicateSelectedPo = [];
            };

            function getSkus(skusOnly){
                var skus = {};
                $scope.selectedOrders.forEach(function(order){
                    order.orderItems.forEach(function(item){
                        if(item.receivedQuantity<item.requestedQuantity){
                            var tillNow = (appUtil.isEmptyObject(item.received) ? 0 : (item.received * item.conversionRatio));
                            var received = parseFloat(item.receivedQuantity + tillNow);
                        if(Object.keys(skus).indexOf(item.skuId)==-1){
                            skus[item.skuId] =  {};
                            skus[item.skuId][item.packagingId] = {
                                requested:parseFloat(item.requestedQuantity),
                                received:parseFloat(received)
                            };
                        }else{
                            if(skus[item.skuId][item.packagingId] != undefined){
                                skus[item.skuId][item.packagingId].requested += parseFloat(item.requestedQuantity);
                                skus[item.skuId][item.packagingId].received += received;
                            }else{
                                skus[item.skuId][item.packagingId] = {
                                    requested:parseFloat(item.requestedQuantity),
                                    received:parseFloat(received)
                                };
                            }
                        }
                        }
                    });
                });
                return skusOnly ? Object.keys(skus) : skus;
            }

            function validateExtraGrOption(){
                $scope.extraGrSkus = {};
                $scope.showExtraGRItems = false;
                if($scope.selectedOrders.length>0){
                    $http({
                        method:"POST",
                        url:apiJson.urls.requestOrderManagement.extraGrEligibility,
                        data:{
                            unitId:$scope.currentUnit.id,
                            vendorId:$scope.selectedVendor.vendorId,
                            dispatchId:$scope.selectedDispatchLocation.dispatchId,
                            skus:getSkus(true),
                            poIds:$scope.selectedOrders.map(function(po){
                                return po.id;
                            })
                        }
                    }).then(function(response){
                        if(!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)){
                            var filteredSkus = response.data;
                            $scope.selectedOrders.forEach(function(order){
                                order.orderItems.forEach(function(item){
                                    if(filteredSkus.indexOf(item.skuId)!=-1){
                                    $scope.extraGrSkus[getKey(item)] = angular.copy(item);
                                        $scope.extraGrSkus[getKey(item)].packagingQty = undefined;
                                        getSkus(false);
                                    }
                                });
                            });
                            $scope.extraGrSkus = Object.values($scope.extraGrSkus);
                            $scope.showExtraGRItems = ($scope.extraGrSkus.length > 0 && $scope.selectedPOType != 'FIXED_ASSET_ORDER');
                        }
                    },function(error){
                        console.log(error);
                    });
                }
            }

            function validateSamePrices(selectedOrders){
                var itemPrices = [];
                for(var i in selectedOrders){
                    var order = selectedOrders[i];
                    for(var j in order.orderItems){
                        var item = order.orderItems[j];
                        if(appUtil.isEmptyObject(itemPrices[getKey(item)])){
                            itemPrices[getKey(item)] = item.unitPrice;
                        }else{
                            if(itemPrices[getKey(item)] != item.unitPrice){
                                return false;
                            }
                        }
                    }
                }
                return true;
            }

            $scope.selectPOs=function(){
                var selected = $scope.pendingPOs.filter(function(po){
                    return po.checked!=undefined && po.checked;
                });

                if(selected.length>0){
                    if(validateSamePrices(selected)){
                    $scope.showExpandedView = true;
                        $scope.selectedOrders = angular.copy(selected);
                    validateExtraGrOption();
                    $scope.minDate = appUtil.formatDate(getMinDate($scope.selectedOrders), "yyyy-MM-dd");
                    $scope.disableEnableGRType = true;
                    }else{
                        $toastService.create("Only POs having same prices for their SKUs can be merged together.");
                    }
                }else{
                    $toastService.create("Select at least one Purchase Order before moving forward.");
                }
            };

            function getKey(item){
                return item.skuId + "_" + item.packagingId;
            }

            function getTotalReceived(orders,key){
                var usedPOItems = {};
                var received = 0;
                for(var i in orders){
                    var order = orders[i];
                    for(var j in order.orderItems){
                        var item = order.orderItems[j];
                        if(getKey(item)==key && !appUtil.isEmptyObject(item.received)){
                            received += item.received;
                            usedPOItems[item.id] = item.received;
                            break;
                        }
                    }
                }
                return {usedPOItems:usedPOItems, received:received};
            }

            $scope.updateGRQty = function(item){
                // key based on sku id and packaging id ot the item to make it unique
                var key = getKey(item);

                if(appUtil.isEmptyObject(item.received) || item.received==0.00 || item.received > ((item.requestedQuantity - item.receivedQuantity)/item.conversionRatio).
                toFixed(2)){
                    item.received=null;
                    //$toastService.create("You cannot Input Receive Quantity 0!!");
                    $toastService.create("Please Enter Valid Qty");
                    var keyValue = $scope.grItems[key];
                    if (keyValue != undefined && keyValue != null) {
                        console.log("Gr key is : ",keyValue);
                        var totalReceivedObj = getTotalReceived($scope.selectedOrders,key);
                        if(totalReceivedObj.received > 0){
                            $scope.grItems[key].received = totalReceivedObj.received;
                            $scope.grItems[key].usedPOItems = totalReceivedObj.usedPOItems;
                            keyValue = recalculate(keyValue);
                            $scope.grItems[key] = keyValue;
                        }else{
                            delete  $scope.grItems[key];
                            if(Object.keys($scope.grItems).length==0){
                                $scope.grItems=null;
                            }
                        }
                        updateGr($scope.grItems,$scope.extraGrItems);
                    }
                    return;
                }
                if(appUtil.isEmptyObject($scope.grItems)){
                    $scope.grItems = {};
                }
                if(!appUtil.isEmptyObject(item.received)){
                    var receivedTillNow = (item.receivedQuantity/item.conversionRatio) + item.received;
                    if(receivedTillNow <= item.packagingQty){
                        var found = $scope.grItems[key];
                        if(appUtil.isEmptyObject(found)){
                            $scope.grItems[key] = updateGRItem(item);  //conversionRatio for conversion into base UOM
                        }else{
                            var totalReceivedObj = getTotalReceived($scope.selectedOrders,key);
                            found.received = totalReceivedObj.received;
                            found.usedPOItems = totalReceivedObj.usedPOItems;
                            found = recalculate(found);
                            $scope.grItems[key] = found;
                        }
                    }else{
                        $toastService.create("You cannot receive quantity greater than the requested quantity");
                    }
                }else if(!priceExists(item.skuId)){
                    $toastService.create("Please check if the SKU prices for "+item.skuName+" for the selected location exists.");
                }else{
                    var totalReceivedObj = getTotalReceived($scope.selectedOrders,key);
                    if(totalReceivedObj.received > 0){
                        $scope.grItems[key].received = totalReceivedObj.received;
                        $scope.grItems[key].usedPOItems = totalReceivedObj.usedPOItems;
                    }else{
                        delete  $scope.grItems[key];
                        if(Object.keys($scope.grItems).length==0){
                            $scope.grItems=null;
                        }
                    }
                }
                updateGr($scope.grItems,$scope.extraGrItems);
            };

            $scope.byNotFulfilled = function (sku){
                var skus = getSkus(false);
                if(appUtil.isEmptyObject(skus[sku.skuId])){
                    return false;
                }

                var skuItem = skus[sku.skuId][sku.packagingId];
                return (!appUtil.isEmptyObject(skuItem) && skuItem.received == skuItem.requested);
            };

            function validateExtraGrItem(extraGrItem){
                var item = $scope.grItems ? $scope.grItems[getKey(extraGrItem)] : null;
                var receivedQuantity = null;
                if(item==null){
                    //in case the gr item for the selected item is not there, check from the elapsed quantity
                    receivedQuantity = extraGrItem.receivedQuantity;
                }else{
                    receivedQuantity = item.received;
                }
                return receivedQuantity!=null ? parseFloat(extraGrItem.received/receivedQuantity) <= 0.05 : false;
            }

            $scope.addExtraGrItems = function(extraGrItem){
                if(validateExtraGrItem(extraGrItem)){
                    if(appUtil.isEmptyObject($scope.extraGrItems)){
                        $scope.extraGrItems = {};
                    }
                    $scope.extraGrItems[getKey(extraGrItem)] = updateGRItem(extraGrItem,true);
                    updateGr($scope.grItems,$scope.extraGrItems);
                }else{
                    $toastService.create("Only 5% of the requested quantity is allowed for Extra GR");
                }
            };

            $scope.uploadDoc = function(){
                $fileUploadService.openFileModal("Upload GR Document","Find",function(file){
                    metaDataService.uploadFile("OTHERS","GR",file, function(doc){
                        $scope.uploadedDocument = doc;
                    });
                });
            };

            function updateGRItem(item, isExtraGrItem){
                var toModify = {
                    sku:null,
                    pkg:null,
                    received:item.received,
                    totalAmount:0,
                    totalTax:0,
                    paidAmount:0,
                    usedPOItems:{}
                };

                toModify.sku = $scope.skuPriceList.filter(function(skuTax){
                    return item.skuId == skuTax.id;
                })[0];

                if(toModify.sku!=null){
                    toModify.pkg = toModify.sku.skuData.packagings.filter(function(pkg){
                        return item.packagingId == pkg.id;
                    })[0];

                    // set PO price in pkg price
                    toModify.pkg.price = item.unitPrice;

                    toModify.usedPOItems[item.id] = item.received;
                    if(isExtraGrItem){
                        delete toModify.usedPOItems;
                    }
                    return recalculate(toModify);
                }
            }

            function recalculate(toModify){
                var amountAndTax = getAmount(toModify.pkg,toModify.received,toModify.sku.taxData);
                toModify.totalAmount = amountAndTax.amount;
                toModify.totalTax = amountAndTax.tax;
                toModify.paidAmount = (amountAndTax.amount + amountAndTax.tax);
                return  toModify;
            }

            function updateGr(grItems, extraGrItems){
                $scope.gr = appUtil.isEmptyObject($scope.gr) ? {totalPrice:0,totalAmount:0,totalTax:0} : $scope.gr;
                var totalPrice = 0;
                var totalAmount = 0;
                var totalTax = 0;
                if(grItems!=null){
                    for(var i in grItems){
                        var grItem = grItems[i];
                        totalPrice += grItem.totalAmount;
                        totalTax += grItem.totalTax;
                        totalAmount += grItem.paidAmount;
                    }
                }

                if(!appUtil.isEmptyObject(extraGrItems)){
                    for(var i in extraGrItems){
                        var extraGrItem = extraGrItems[i];
                        totalPrice += extraGrItem.totalAmount;
                        totalTax += extraGrItem.totalTax;
                        totalAmount += extraGrItem.paidAmount;
                    }
                }

                $scope.gr.totalTax = totalTax;
                $scope.gr.totalPrice = totalPrice;
                $scope.gr.totalAmount = totalAmount;
                $scope.gr.totalCharged = totalAmount;
            }

            function validateGR(){
                return $scope.gr.docDate!=null && $scope.gr.docNumber!=null
                    && $scope.gr.docType!=null && Object.keys($scope.grItems).length>0;
            }

            $scope.submit = function(force){
                if(validateGR()){
                    $alertService.confirm("Are you sure?","",function(result){
                        if(result){
                            sendRequestForGR(force);
                        }
                    });
                }else{
                    $toastService.create("Please fill in the document type, document date and document number properly before submitting");
                }
            };


            function getReceivedItems(order){
                return order.orderItems.filter(function(item){
                    return (!appUtil.isEmptyObject(item.received) && item.received>0);
                }).map(function(item){
                   return {
                       id:item.skuId,
                       pkg:item.packagingId,
                       qty:(item.received * item.conversionRatio)
                   };
                });
            }

            function sendRequestForGR(force){
                var reqObj = {
                    userId:appUtil.getCurrentUser().userId,
                    deliveryUnitId:$scope.currentUnit.id,
                    dispatchId:$scope.selectedDispatchLocation.dispatchId,
                    vendorId:$scope.selectedVendor.vendorId,
                    usedPOList: $scope.selectedOrders.map(function(po){
                        return {
                            id:po.id,
                            skus:getReceivedItems(po)
                        };
                    }),
                    extraCharges:$scope.gr.extraCharges,
                    docType:$scope.gr.docType,
                    docNumber:$scope.gr.docNumber,
                    docDate:$scope.gr.docDate,
                    creationType:"MANUAL",
                    amountMatched:angular.isUndefined($scope.gr.amountMatched) ? false : $scope.gr.amountMatched,
                    items:$scope.prepareGRItems($scope.grItems),
                    extraGrItems:$scope.prepareGRItems($scope.extraGrItems),
                    vendorGrType : $scope.selectedPOType,
                    forceSummit : force,
                    type: $scope.selectedOrders[0].type
                };

                if(reqObj.items.length>0){
                    $http({
                        method:"POST",
                        url:apiJson.urls.goodsReceivedManagement.createVendorGR,
                        data:reqObj
                    }).then(function(response){
                        if(response.data!=null){
                            $scope.init();
                            $toastService.create("GR with ID: " + response.data + " created", function(){
                                $state.go('menu.viewVendorGR',{vendor:$scope.selectedVendor, dispatchLocation:$scope.selectedDispatchLocation,grId:response.data});
                            });
                        }else {
                            $toastService.create("GR could not be created due to some error!!");
                        }
                    },function(error){
                        console.log(error);
                        $alertService.alert("GR could not be created due to some error!!", error.data.errorMessage);
                    });
                }
            }

            function getTaxValue(category, taxValue, amount) {
                return {
                    taxName:category,
                    taxCategory:category,
                    value:(amount*taxValue)/100,
                    percentage:taxValue
                };
            }

            function getAmount(pkg, qty, tax) {
                var price = pkg.price;
                var ratio = pkg.ratio;
                var otherTaxes = 0;
                var amount = parseFloat(price * qty);
                var obj = {
                    amount:amount,
                    taxes:[],
                    tax:0
                };
                if(!$scope.vendorRegCheck){
                	return obj;
                }

                if($scope.currentUnit.location.state.code.toString() != $scope.selectedDispatchLocation.address.stateCode.toString()){
                    var taxObj = getTaxValue("IGST",tax.state.igst,amount);
                    obj.taxes.push(taxObj);
                    obj.tax += taxObj.value;
                }else{
                    var taxObj1 = getTaxValue("CGST",tax.state.cgst,amount);
                    var taxObj2 = getTaxValue("SGST",tax.state.sgst,amount);
                    obj.taxes.push(taxObj1);
                    obj.taxes.push(taxObj2);
                    obj.tax += (taxObj1.value + taxObj2.value);
                }

                if(tax.others.length>0){
                    for(var index in tax.others){
                        var otherTax = tax.others[index];
                        var valueAndTax = getTaxValue(otherTax.type, otherTax.tax,
                                                getApplicableAmount(otherTax.applicability, obj));
                        obj.taxes.push(valueAndTax);
                        otherTaxes += valueAndTax.value;
                    }
                    obj.tax += otherTaxes; // adding other taxes to the total tax
                }
                return obj;
            }

            function getApplicableAmount(applicability,obj){
                if(applicability == "ON_TAX"){
                    return getValue(obj,"CGST") + getValue(obj,"IGST") + getValue(obj,"SGST");
                }else{
                    return obj.amount;
                }
            }

            function getValue(taxObj, type){
                var amount = 0;
                taxObj.taxes.forEach(function(tax){
                    if(type==tax.taxCategory){
                        amount = appUtil.isEmptyObject(tax.value) ? 0 : tax.value;
                        return false;
                    }
                });
                return amount;
            }


            $scope.prepareGRItems = function(items){
                if(appUtil.isEmptyObject(items)){
                    return null;
                }
                var obj = Object.values(items).map(function (item) {
                    var amountAndTax = getAmount(item.pkg, item.received, item.sku.taxData);
                    var grItem = {
                        "skuId": item.sku.skuData.id,
                        "skuName": item.sku.skuData.name,
                        "hsn": item.sku.skuData.hsn,
                        "receivedQuantity": (item.received * item.pkg.ratio),
                        "unitOfMeasure": item.sku.skuData.uom,
                        "unitPrice": item.pkg.price,
                        "totalCost": amountAndTax.amount,
                        "amountPaid": (amountAndTax.amount + amountAndTax.tax),
                        "packagingId": item.pkg.id,
                        "packagingName": item.pkg.name,
                        "conversionRatio": item.pkg.ratio,
                        "packagingQty": item.received,
                        "totalTax": amountAndTax.tax,
                        "taxes":amountAndTax.taxes,
                        "usedPOItems":item.usedPOItems
                    };
                    return grItem;
                });

                return obj;
            };
        }
    ]
);
