'use strict';

angular.module('scmApp')
.controller('trOrderShortExpiryCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService','metaDataService',
        	'data','Popeye','$alertService',
            function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, metaDataService, data, Popeye, $alertService) {
        		
        		
        		$scope.init = function(){
        			$scope.modelData = {};
            		$scope.modelData.semiFinishedProducts = data;
            		$scope.modelData.dataStatus = 'CANCEL';
            		$scope.currentTab = 1;
					var unit = appUtil.getUnitData();
            		$scope.isCafe = appUtil.isCafe(unit);
	        		var i = 0;
	            	var pArray = $scope.modelData.semiFinishedProducts;
	            	for(i in pArray){
	            		var product = pArray[i];
	            		product.shortExpiry = 0;
	            		//product.freshQuantity = product.requestedQuantity;
	            	}
            	
        		};
        		
        		$scope.switchtab = function (val) {
                    $scope.currentTab = $scope.currentTab + val;
                };
        		
        		$scope.submitData = function(){
        			Popeye.closeCurrentModal($scope.modelData);
                };
                
                // LOGIC HERE
                $scope.calculateTransferConsumption = function(){
                	
                	$scope.expiryData = [];
                	$scope.expiryDisplayData = [];
                	var payload = {};
                	payload.unitId = appUtil.getCurrentUser().unitId;
                	payload.inventoryType = $scope.isCafe ? 'PRODUCT' :'SKU';
                	payload.inventoryItems = [];
                	var i = 0;
                	var pArray = $scope.modelData.semiFinishedProducts;
                	for(i in pArray){
                		var product = pArray[i];
                		if(!$scope.checkData(product)){
                			$toastService.create("Please fill correct values.");
                			return;
                		}
                		
                		if(product.shortExpiry > 0){
	                		payload.inventoryItems.push(createReqObj(product,product.shortExpiry,"SHORT_EXPIRY"));
                		}

                		if(product.freshQuantity > 0){
	                		payload.inventoryItems.push(createReqObj(product,product.freshQuantity,"FRESH","LIFO"));
                		}
                		
                	}
                	
                	var url = apiJson.urls.transferOrderManagement.transferOrderExpiryCheck;
                	$http({
                        method: "POST",
                        url: url,
                        data: payload
                    }).then(function success(response) {
                    	if(response.data!=null){
                            	$scope.expiryData = response.data
                            	$scope.expiryDisplayData = [];
                            	var i = 0;
                            	var j = 0;
                            	for(i in $scope.expiryData.inventoryItems){
                            		var item = $scope.expiryData.inventoryItems[i];
                            		for(j in item.drillDowns){
                            			var d = item.drillDowns[j];
                            			var dd = {
                            				productName:item.keyName,
                            				uom:item.uom,
                            				expiryType:item.expiryType,
                            				quantity:d.quantity,
                            				expiryDate:d.expiryDate
                            			};
                            			$scope.expiryDisplayData.push(dd);
                            			
                            		}
                            	}
                            $scope.modelData.expiryData = $scope.expiryData;
                            $toastService.create("Expiries Calculated Successfully.");
                    	}
                    	$scope.switchtab(1);
                    }, function error(response) {
                        if(response.data.errorCode!=null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg,function () {}, true);
                        }else{
                            $toastService.create("Could not check Expiry. Please try again later");
                            console.log("error:" + response);
                        }
                    });

                };


                function createReqObj(product, qty, type, consumptionOrder) {
					var a = product.skuList[0];
					var item = {};
					if($scope.isCafe){
						item = {
							keyId:product.productId,
							itemKeyId : product.productId,
							keyName:product.productName,
							keyType:'PRODUCT',
							uom:product.unitOfMeasure,
							expiryType: type,
							quantity:qty,
							price:null,
							expiryDate:null
						};
					}else{
						item = {
							keyId:a.skuId,
							itemKeyId : a.skuId,
							keyName:a.skuName,
							keyType:'SKU',
							uom:a.unitOfMeasure,
							expiryType: type,
							quantity:qty,
							price:null,
							expiryDate:null
						};
					}

					if(!appUtil.isEmptyObject(consumptionOrder)){
						item["consumptionOrder"]= 'LIFO';
					}
					return item;
				}

                
                $scope.checkData = function(product){
            		
                	if(product.shortExpiry < 0 || product.freshQuantity < 0){
						 return false;
	           		}
	           		
	           		var value1 = product.shortExpiry == null || product.shortExpiry == undefined ? 0 : product.shortExpiry;
	           		var value2 = product.freshQuantity == null || product.freshQuantity == undefined ? 0 : product.freshQuantity;
	           		
	           		if(value1 + value2 < 0){
	          			return false;
	           		}
	           		
	           		return true;
                };
                
                $scope.cancelShortExpiryModal = function(){
                	$scope.modelData = null;
                	$scope.submitData();
                };
                
                $scope.submitShortExpiryModal = function(){
                	$scope.modelData.dataStatus = 'SUCCESS';
                	$scope.submitData();
                };
                
                $scope.calculateFreshQuantity = function(item){
                	if(item.shortExpiry == null || item.shortExpiry == undefined){
                		item.freshQuantity = item.requestedQuantity;
                	} else {
						var value = item.requestedQuantity - item.shortExpiry
						value = isNaN(value) ? 0 : value;
                		item.freshQuantity =  value < 0 ? 0 : value;
                	}
                };
                
            }
        ]
    )