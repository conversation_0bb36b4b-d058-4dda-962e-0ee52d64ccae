/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('searchDispatchCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye','$timeout','$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService,
                  $fileUploadService, $alertService, previewModalService, Popeye, $timeout, $window) {

            $scope.init = function () {
                $scope.showSearchDispatchScreen = true;
                $scope.showCreateDispatchScreen = false;
                $scope.showConsignmentScreen = false;
                $scope.showConsignmentDetailScreen = false;
                $scope.transportModes = [];
                $scope.getTransportModes();
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.unitList = appUtil.getUnitList();
            };

            $scope.goToSearchDispatchScreen = function () {
                $scope.showSearchDispatchScreen = true;
                $scope.showCreateDispatchScreen = false;
                $scope.showCreateConsignmentScreen = false;
                $scope.showConsignmentDetailScreen = false;
            };

            $scope.goToCreateDispatchScreen = function () {
                $scope.showSearchDispatchScreen = false;
                $scope.showCreateDispatchScreen = true;
                $scope.showCreateConsignmentScreen = false;
                $scope.showConsignmentDetailScreen = false;
            };

            $scope.goToConsignmentDetailScreen = function () {
                $scope.showSearchDispatchScreen = false;
                $scope.showCreateDispatchScreen = false;
                $scope.showCreateConsignmentScreen = false;
                $scope.showConsignmentDetailScreen = true;
            };

            $scope.goToCreateConsignmentScreen = function () {
                $scope.showSearchDispatchScreen = false;
                $scope.showCreateDispatchScreen = false;
                $scope.showCreateConsignmentScreen = true;
                $scope.showConsignmentDetailScreen = false;
            };

            $scope.getTransportModes = function () {
                $http({
                    url: apiJson.urls.transportManagement.transportModes,
                    method: 'GET',
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        response.map(function (item, index) {
                            $scope.transportModes.push({id: index, name: item});
                        });
                    } else {
                        $toastService.create("Error loading transport modes.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error loading transport modes", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.getVehicles = function () {
                $http({
                    url: apiJson.urls.transportManagement.vehiclesByTransportMode,
                    method: 'GET',
                    params: {transportMode: $scope.selectedTransportMode.name}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.vehicles = response;
                    } else {
                        $toastService.create("Error loading vehicles.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error loading vehicles", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.getDispatchList = function () {
                if($scope.selectedVehicle == null){
                    $toastService.create("Please select transport mode and vehicle.");
                    return;
                }
                if($scope.startDate == null || $scope.endDate == null){
                    $toastService.create("Please select start date and end date.");
                    return;
                }
                $http({
                    url: apiJson.urls.transportManagement.dispatchHistory,
                    method: 'GET',
                    params: {startDate: $scope.startDate, endDate:$scope.endDate, vehicleId: $scope.selectedVehicle.vehicleId}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        if(response.length > 0){
                            $scope.dispatchList = response;
                            $scope.goToCreateDispatchScreen();
                        } else {
                            $toastService.create("No dispatches found.");
                        }
                    } else {
                        $toastService.create("Error loading dispatch data.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error loading dispatch data", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.downloadEwayFile = function (dispatchData) {
                $scope.dispatchData = dispatchData;
                if (!$scope.dispatchData) {
                    $toastService.create("Please add dispatch;");
                    return;
                }
                if ($scope.dispatchData.consignmentList.length === 0) {
                    $toastService.create("Please add consignment;");
                    return;
                }
                $http({
                    url: apiJson.urls.transportManagement.downloadEwayFile,
                    method: 'GET',
                    params: {dispatchId: $scope.dispatchData.dispatchId},
                    responseType: 'arraybuffer'
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        var blob = new Blob([response], {type: appUtil.mimeTypes["JSON"]}, "E-WayBill_JSON" + ".json");
                        saveAs(blob, "dispatch_" + $scope.dispatchData.dispatchId + ".json");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.uploadEwayFile = function (dispatchData) {
                $scope.dispatchData = dispatchData;
                $scope.openModal = false;
                $fileUploadService.openFileModal("Upload Eway Bill Sheet", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    if (file.size > 307200) {
                        $toastService.create('File size should not be greater than 300 kb.');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (fileExt.toLowerCase() === 'xls' || fileExt.toLowerCase() === 'xlsx' || fileExt.toLowerCase() === 'csv'
                        || fileExt.toLowerCase() === 'zip' || fileExt.toLowerCase() === 'rar') {
                        var mimeType = fileExt.toUpperCase(),
                            fd = new FormData();
                        fd.append('dispatchId', $scope.dispatchData.dispatchId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.transportManagement.uploadEwayFile,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.uploadedDocData = response;
                                openEwaySheetPreviewModal(response);
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                });
            };

            function openEwaySheetPreviewModal(uploadedDocData) {
                var mappingModal = Popeye.openModal({
                    templateUrl: "ewaySheetPreviewModal.html",
                    controller: "ewaySheetPreviewModalCtrl",
                    resolve: {
                        uploadedDocData: function () {
                            return uploadedDocData;
                        }
                    },
                    modalClass: "popeye-modal-large",
                    click: false,
                    keyboard: false
                });

                mappingModal.closed.then(function (data) {
                    if (data === "SUBMIT") {
                        $scope.updateEwayBillNumber();
                    }
                });
            };

            $scope.updateEwayBillNumber = function () {
                if (!$scope.uploadedDocData) {
                    $toastService.create("Please verify data.");
                    return;
                }
                var errors = 0;
                $scope.uploadedDocData.map(function (item) {
                    if (item.error === true) {
                        errors++;
                    }
                });
                if (errors > 0) {
                    $toastService.create("Error in the uploaded sheet.");
                    return;
                }
                $http({
                    url: apiJson.urls.transportManagement.updateEwayBillNumber,
                    method: 'POST',
                    data: $scope.uploadedDocData
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null && response === true) {
                        $scope.getDispatchList();
                        $toastService.create("EWay Bill updated successfully.");
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.getDispatchDetailForPrint = function (dispatchData) {
                $scope.dispatchData = dispatchData;
                $http({
                    url: apiJson.urls.transportManagement.getDispatchDetail,
                    method: 'GET',
                    params: {dispatchId: $scope.dispatchData.dispatchId}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.dispatchData = response;
                        decorateObjectForPrinting();
                        $timeout(function () {
                            $window.print();
                        }, 0);
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            function decorateObjectForPrinting() {
                var unitMap = {};
                $scope.unitList.map(function (item) {
                    unitMap[item.id] = item;
                });
                $scope.dispatchData.consignmentList.map(function (consignment) {
                    consignment.ewaybills.map(function (ewb) {
                        ewb.transferOrder.generationUnitId.address = unitMap[ewb.transferOrder.generationUnitId.id].address;
                        ewb.transferOrder.generatedForUnitId.address = unitMap[ewb.transferOrder.generatedForUnitId.id].address;
                        ewb.transferOrder.generationUnitId.tin = unitMap[ewb.transferOrder.generationUnitId.id].tin;
                        ewb.transferOrder.generatedForUnitId.tin = unitMap[ewb.transferOrder.generatedForUnitId.id].tin;
                        ewb.transferOrder.generationUnitId.city = unitMap[ewb.transferOrder.generationUnitId.id].city;
                        ewb.transferOrder.generatedForUnitId.city = unitMap[ewb.transferOrder.generatedForUnitId.id].city;
                        var total = 0,
                            availableTaxes = [];
                        ewb.transferOrder.transferOrderItems.map(function (item) {
                            total += (item.total + item.tax);
                            $scope.calculateTaxes(item, availableTaxes);
                        });
                        ewb.transferOrder.totalPrice = Math.round(total);
                        ewb.transferOrder.totalPriceInWords = appUtil.inWords(Math.round(total));
                        ewb.transferOrder.availableTaxes = availableTaxes;
                    });
                });
            }

            $scope.calculateTaxes = function (item, availableTaxes) {
                item.gst = 0;
                item.gstPercentage = 0;
                item.other = 0;
                item.otherPercentage = 0;
                if (item.taxes == null || item.taxes.length === 0) {
                    return;
                }
                for (var i in item.taxes) {
                    if (availableTaxes.indexOf(item.taxes[i].code) < 0) {
                        availableTaxes.push(item.taxes[i].code);
                    }
                    if (item.taxes[i].type === 'GST') {
                        item.gst = item.gst + item.taxes[i].value;
                        item.gstPercentage = item.taxes[i].percentage;
                    } else {
                        item.other = item.other + item.taxes[i].value
                    }
                }
                if (item.total > 0) {
                    item.otherPercentage = item.other / item.total * 100;
                }
            };

            $scope.viewConsignmentData = function (item) {
                $scope.viewConsignmentItem = item;
                $scope.goToConsignmentDetailScreen();
            };

            $scope.getTODetails = function (id) {
                $http({
                    url: apiJson.urls.transferOrderManagement.transferOrder,
                    method: 'GET',
                    params: {transferOrderId: id}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.viewEwayBill = {type: "TO", transferOrder: response};
                        openEwayBillPreviewModal();
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.getEwayBillDetails = function (id) {
                $http({
                    url: apiJson.urls.transportManagement.getEwayBillDetails,
                    method: 'GET',
                    params: {ewayId: id}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.viewEwayBill = response;
                        $scope.viewEwayBill.type = "EWAY";
                        openEwayBillPreviewModal();
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            function openEwayBillPreviewModal() {
                var mappingModal = Popeye.openModal({
                    templateUrl: "ewayBillPreviewModal.html",
                    controller: "ewayBillPreviewModalCtrl",
                    resolve: {
                        viewEwayBill: function () {
                            return $scope.viewEwayBill;
                        }
                    },
                    modalClass: "popeye-modal-large",
                    click: false,
                    keyboard: false
                });

                mappingModal.closed.then(function (data) {

                });
            };


        }]
    )
    .controller('ewaySheetPreviewModalCtrl', ['$scope', 'uploadedDocData', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye',
            function ($scope, uploadedDocData, appUtil, $toastService, apiJson, $http, Popeye) {
                $scope.uploadedDocData = uploadedDocData;
                $scope.closeModal = function (type) {
                    Popeye.closeCurrentModal(type);
                };
            }
        ]
    );
