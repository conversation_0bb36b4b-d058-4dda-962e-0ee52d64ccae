/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('settlePaymentRequestsCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', '$fileUploadService','$alertService','$timeout',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, $fileUploadService, $alertService,$timeout) {

            $scope.init = function () {
                $scope.bankName = null;
                $scope.showNoPR = false;
                $scope.companyList =  appUtil.getCompanyList();
                $scope.selectedCompany =appUtil.getDefaultCompany();
                getBanksOfCompany();
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.bankList = [];
                $scope.selectedBank;
            };
            
            $scope.changeCompany = function(){
            	console.log("selected company",$scope.selectedCompany);
            	$rootScope.showFullScreenLoader = true;
            	getBanksOfCompany();
            	
            };
            
            function getBanksOfCompany (){
            	 $http({
                     url: apiJson.urls.paymentRequestManagement.banksOfCompany,
                     method: 'POST',
                     data: $scope.selectedCompany.id,
                 }).success(function (response) {
                     $rootScope.showFullScreenLoader = false;
                     $scope.bankList = response;
                 }).error(function (response) {
                     $rootScope.showFullScreenLoader = false;
                     if (response.errorMsg != null) {
                         $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                     } else {
                         $toastService.create("Could not update balances");
                     }
                 });
            }
            
            $scope.changeBank = function(){
            	if($scope.selectedBank!=null ){
            		$scope.bankName = $scope.selectedBank.code;
            	}
            };
            
            
            $scope.uploadDoc = function () {
                if($scope.bankName==null){
                    $toastService.create("Please select bank.");
                }else{
                    $fileUploadService.openFileModal("Upload Payment Sheet", "Find", function (file) {
                        $scope.uploadPaymentsSheet(file);
                    });
                }
            };
            
            $scope.uploadPaymentsSheet = function (file) {
                $rootScope.showFullScreenLoader = true;
                $scope.showNoPR = false;
                var fd = new FormData(document.forms[0]);
                fd.append("file", file);
                fd.append("bankName", $scope.bankName);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadPaymentsSheet,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $scope.prs = response;
                    } else {
                        $scope.prs = [];
                        $scope.showNoPR = true;
                        $toastService.create("Could not find any valid payments from sheet.");
                    }
                    $scope.bankName = null;
                    resetBank();
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error in uploaded sheet.");
                    }
                    $scope.bankName = null;
                    resetBank();
                });
            };
            
            function resetBank(){
            	 $timeout(function () {
            		 $('#selectedBankForSettleMent').val('').trigger('change');
            	 });
            }

            $scope.submitPayments = function (isForceUpload) {
                $rootScope.showFullScreenLoader = true;
                $scope.prs.map(function (pr) {
                    pr.paymentDetail.createdBy = appUtil.createGeneratedBy();
                });
                var user = appUtil.createGeneratedBy();
                var nameId = user.name + "["+user.id + "]";
                $http({
                    url: apiJson.urls.paymentRequestManagement.paymentRequestsSettle,
                    method: 'POST',
                   // data: $scope.prs, $scope.selectedCompany,
                    params:{
                        isForceUploaded: isForceUpload
                    },
                    data:{
                    	paymentRequests:$scope.prs,
                    	selectedCompany:$scope.selectedCompany.name,
                        utrUploadedBy: nameId
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response!=null && response==true) {
                        $toastService.create("Payments settled successfully.");
                        $scope.prs=[];
                        $scope.bankName = null;
                    } else {
                        $toastService.create("Could not settle payments");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Could not settle payments");
                    }
                });
            }
        }]
    );
