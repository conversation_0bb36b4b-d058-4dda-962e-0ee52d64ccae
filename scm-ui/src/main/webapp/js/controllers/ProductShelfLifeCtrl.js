'use strict';

angular.module('scmApp').controller('ProductShelfLifeCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService',
    '$alertService','metaDataService','Popeye', function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $alertService, metaDataService, Popeye) {
        $scope.init = function () {
            $scope.subCategoriesMap = getCategoryToSubCategoryMap(appUtil.getMetadata().categoryDefinitions);
            $scope.categories = [
                {name :"COGS" , id : 1 , selected : false , subCategories : getCategoryToSubCategoryMap(1)},
                {name :"CONSUMABLES", id : 2 , selected: false  , selectedSubCategories : getCategoryToSubCategoryMap(2) },
                {name :"FIXED_ASSETS", id : 3 , selected: false , selectedSubCategories : getCategoryToSubCategoryMap(3)},
                {name :"SEMI_FINISHED" , id : 4 , selected: false , selectedSubCategories : getCategoryToSubCategoryMap(4)}
            ];
            $scope.selectedCategory = null;
            $scope.selectedSubCategory = null;
            $scope.values = {
                maxRange : null,
                minRange : null,
                defaultValue : null,
            }
            $scope.isValid = true;
            $scope.isInrange = true;
            $scope.show = false;

        };

        $scope.selectSubCategory = function (subCategory){
            $scope.selectedSubCategory = JSON.parse(subCategory);
            $scope.show = true;
            if(!appUtil.isEmptyObject($scope.selectedSubCategory.shelfLifeRange)){
                var range = $scope.selectedSubCategory.shelfLifeRange.split(",");
                $scope.values.minRange = range[0];
                $scope.values.maxRange = range[1];
            }else{
                $scope.values.minRange = null;
                $scope.values.maxRange = null;
            }
            $scope.values.defaultValue = $scope.selectedSubCategory.shelfLifeInDays;
            $scope.validate();

        }

        $scope.validate = function (){
            $scope.values.maxRange = parseInt($scope.values.maxRange);
            $scope.values.minRange = parseInt($scope.values.minRange);
            $scope.values.defaultValue = parseInt($scope.values.defaultValue);
            if($scope.values.maxRange < $scope.values.minRange){
                $scope.isValid = false;
            }else{
                $scope.isValid = true;
            }
            if($scope.values.defaultValue >= $scope.values.minRange &&
                $scope.values.defaultValue <= $scope.values.maxRange){
                $scope.isInrange = true;
            } else{
                $scope.isInrange = false;
                $scope.isValid = false;
            }
            if($scope.values.minRange == null || $scope.values.maxRange == null || $scope.values.defaultValue == null){
                $scope.isValid = false;
            }
            if( isNaN($scope.values.minRange)  || isNaN($scope.values.maxRange) || isNaN($scope.values.defaultValue)){
                $scope.isValid = false;
            }

        }

        function getCategoryToSubCategoryMap(categories){
            var subCategoryMap = new Map();
            for(var i = 0;i<categories.length ; i++){
                subCategoryMap.set(categories[i].categoryId,categories[i].subCategories);
            }
            return subCategoryMap;
        }

        $scope.getSubCategories = function (categoryId){
            return $scope.subCategoriesMap.get(parseInt(categoryId));
        }

        $scope.updateShelfLife = function (){
            $scope.validate();
            if(!$scope.isValid){
                $toastService.create("Please Fill All Values");
            }
            $http({
                url: apiJson.urls.productManagement.updateSubCategoryShelfLife,
                method: 'POST',
                params : {
                    id : $scope.selectedSubCategory.subCategoryId,
                    shelfLife : $scope.values.defaultValue,
                    range : $scope.values.minRange + "," + $scope.values.maxRange
                }
            }).success(function (response) {
                $toastService.create("Succesfully updated Shelf Life ");
                metaDataService.getSCMMetaData($scope.init,true);
            }).error(function (response) {
                $toastService.create("Something Went Wrong");
            });
        }


    }]);
