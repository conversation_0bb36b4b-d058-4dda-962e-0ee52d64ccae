/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 20-02-2017.
 */

angular.module('scmApp').controller('SKUPriceUpdateCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
    '$location', 'fileService', '$alertService','$toastService',
    function ($rootScope, $scope, apiJson, $http, appUtil, $location, fileService, $alertService,$toastService) {
        $scope.init = function () {
            $scope.edit = false;
            $scope.skuPriceUpdate = false;
            $scope.previewSKUPriceList=false;
            $scope.showEventList=true;
            $scope.priceUpdateLists = [];

            $http({
                url: apiJson.urls.productManagement.skuPriceUpdateEventGet,
                method: 'GET',
                data: '',
                headers: {"Content-Type": "application/json"}
            }).then(function success(response) {
                $scope.skuUpdatePriceList = response.data;
                $scope.skuItemLength = $scope.skuUpdatePriceList.entries.length;
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.editNow = function (viewListSKU) {
            $rootScope.showFullScreenLoader = true;
            $scope.edit = true;
            $rootScope.showFullScreenLoader = false;
        };

        $scope.cancelNow = function (viewListSKU) {
            $rootScope.showFullScreenLoader = true;
            $scope.edit = false;
            $rootScope.showFullScreenLoader = false;
        };

        $scope.viewPriceUpdateDetail = function (eventDetails, eventId) {
            $scope.skuCategoryList = [];
            $scope.productCategoryList = [];
            $scope.receipeCategoryList = [];
            $scope.edit = false;
            $http({
                url: apiJson.urls.productManagement.skuPriceUpdateEventGetEntries,
                method: 'POST',
                data: eventId,
                headers: {"Content-Type": "application/json"}
            }).then(function success(response) {
                $scope.existingUpdatePrice = response.data;
                $scope.viewUpdateList = response.data;
                $scope.priceUpdateLists = response.data.entries;
                $scope.priceUpdateLists.forEach(function (priceCategory) {
                    if (priceCategory.keyType == "SKU") {
                        $scope.skuCategoryList.push(priceCategory)
                    }
                    if (priceCategory.keyType == "PRODUCT") {
                        $scope.productCategoryList.push(priceCategory)
                    }
                    if (priceCategory.keyType.indexOf("RECIPE_") > -1) {
                        $scope.receipeCategoryList.push(priceCategory)
                    }
                });
            }, function error(response) {
                console.log("error:" + response);
            });
            console.log("DrillProduct", $scope.productCategoryList);
            console.log("DrillReceipe", $scope.receipeCategoryList);
            $scope.skuPriceUpdate = true;
            $scope.previewSKUPriceList=false;
            $scope.showEventList=false;
        };

        $scope.backSkuPrice = function () {
            $scope.skuPriceUpdate = false;
            $scope.previewSKUPriceList=false;
            $scope.showEventList=true;
        };

        $scope.downloadEventTemplate = function () {
            window.location = apiJson.urls.productManagement.skuPriceDataDownload
        };

        $scope.downloadEventTemplate = function () {
            $http({
                url: apiJson.urls.productManagement.skuPriceDataDownload,
                method: 'GET',
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            }).success(function (data) {
                var fileName = "SCM SKU Price data.xls";
                var blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}, fileName);
                saveAs(blob, fileName);
            }).error(function (err) {
                console.log("Error during getting data", err);
            });
        };
        $scope.showModal = function () {
            $scope.fileUploadHere = null;
        };
        $scope.preview = function () {
            if ($scope.edit == false) {
                $toastService.create('Please use Edit Button before Preview View');
                return false;
            }
            $scope.previewSkuUpdateList = [];
            $scope.viewUpdateList.entries.forEach(function (previewPriceUpdateList) {
                if (previewPriceUpdateList.keyType == 'SKU') {
                    if (previewPriceUpdateList.editedUnitPrice != 0 || previewPriceUpdateList.editedUnitPrice != null || previewPriceUpdateList.editedUnitPrice != "") {
                        if (previewPriceUpdateList.updatedUnitPrice != previewPriceUpdateList.editedUnitPrice) {
                            $scope.previewSkuUpdateList.push(previewPriceUpdateList);
                        }
                    }
                    else {
                        $toastService.create('Edited Price value can not empty or zero');
                    }
                }
            });
            if ($scope.previewSkuUpdateList.length == 0) {
                $toastService.create('No Changes in Edited Price Value');
                return false;
            }
            $scope.showEventList=false;
            $scope.previewSKUPriceList = true;
            $scope.skuPriceUpdate = false;
        };

        $scope.backFromPreview = function () {
            $scope.previewSKUPriceList = false;
            $scope.skuPriceUpdate = false;
            $scope.showEventList=true;
        };

        $scope.assignValueCancel = function (cancelEventId) {
            $alertService.confirm("CANCELLATION", "Are you sure?", function (result) {
                if (result) {
                    $scope.cancelId = "";
                    $scope.eventInfo = "cancel";
                    $scope.cancelId = cancelEventId;
                    console.log($scope.cancelId);
                    console.log("assignValueCancel called");
                    $scope.cancelConfirmation(result);
                }
            });
        };

        $scope.assignValueReject = function (rejectEventId) {
        	
            $alertService.confirm("REJECT", "Are you sure?", function (result) {
                if (result) {
                    console.log(rejectEventId);
                    $scope.rejectId = "";
                    $scope.eventInfo = "reject";
                    $scope.rejectId = rejectEventId;
                    $scope.rejectConfirmation(result);
                }
            });
        };

        $scope.assignValueApprove = function (approveEventId) {
            $alertService.confirm("Approve", "Are you sure?", function (result) {
                if (result) {
                    $scope.eventInfo = "approve";
                    $scope.approveId = approveEventId;
                    $scope.approveConfirmation(result);
                }
            });
        };

        $scope.updateSKUPrice = function () {
            $scope.viewUpdateList.entries = $scope.previewSkuUpdateList;
            $rootScope.showFullScreenLoader = true;
            var currentUser = appUtil.getCurrentUser();
            $http({
                url: apiJson.urls.productManagement.skuPriceUpdateEventUpdate,
                method: 'POST',
                headers: {
                    'Content-type': 'application/json',
                    'userId': currentUser.userId,
                    'userName': currentUser.user.name
                },
                data: $scope.viewUpdateList
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                $scope.init();


                if (!response) {
                    alert("Error");
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                alert("Error");
            });
        };

        $scope.cancelConfirmation = function (result) {
            console.log("cancelId=", $scope.cancelId);
            
                if (result) {
                    var currentUser = appUtil.getCurrentUser();
                    $http({
                        url: apiJson.urls.productManagement.skuPriceUpdateEventCancel,
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json',
                            'userId': currentUser.userId,
                            'userName': currentUser.user.name
                        }, data: $scope.cancelId
                    }).success(function (response) {
                        console.log(response);
                        $rootScope.showFullScreenLoader = false;
                        $scope.init();
                        if (!response) {
                            $toastService.create("Error occurred while updating");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $toastService.create("Error occurred while updating");
                    });
                }
            

        };
        $scope.approveConfirmation = function (result) {
        	if (result) {
                    var currentUser = appUtil.getCurrentUser();
                    $http({
                        url: apiJson.urls.productManagement.skuPriceUpdateEventApprove,
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json',
                            'userId': currentUser.userId,
                            'userName': currentUser.user.name
                        },
                        data: $scope.approveId
                    }).success(function (response) {
                        console.log(response);
                        $rootScope.showFullScreenLoader = false;
                        $scope.init();
                       $scope.skuPriceUpdate=false;
                       $scope.showEventList=true;

                        if (!response) {
                            $toastService.create("Error occurred while updating");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $toastService.create("Error occurred while updating");
                    });
                }
        };

        $scope.rejectConfirmation = function (result) {
        		if (result) {
                    var currentUser = appUtil.getCurrentUser();
                    $http({
                        url: apiJson.urls.productManagement.skuPriceUpdateEventReject,
                        method: 'POST',
                        headers: {
                            'Content-type': 'application/json',
                            'userId': currentUser.userId,
                            'userName': currentUser.user.name
                        },
                        data: $scope.rejectId
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $scope.backSkuPrice();
                        $scope.init();
                        if (!response) {
                            alert("Error");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        alert("Error");
                    });
                }
        	
        };
        
        $scope.viewDrillDowns = function (drillDownlList) {
            $scope.drillDownListView = "";
            $scope.drillDownListView = drillDownlList;
            console.log($scope.drillDownListView);
        };
        $scope.showDrillError = function (drillErrorList) {
            $scope.productDrilledErrorsList = "";
            $scope.productDrilledErrorsList = drillErrorList;
        };
        $scope.viewProductErrors = function (productErrorListDetails) {
            $scope.productErrorsList = "";
            $scope.productErrorsList = productErrorListDetails;
        };

        $scope.uploadFile = function () {
            var file = fileService.getFile();
            if (file == null) {
                $toastService.create('File cannot be empty');
                return false;
            }
            console.log("TT=", file);
            var fileName = file.name;
            console.log("kk-", fileName);
            var filtSplit = fileName.split(".");
            var str = filtSplit[1].replace(/^[ ]+|[ ]+$/g, '');
            if (str == 'xls' || str == 'xlsx') {
                var fd = new FormData();
                var currentUser = appUtil.getCurrentUser();
                fd.append('userId', currentUser.userId);
                fd.append('userName', currentUser.user.name);
                fd.append('file', file);
                $http.post(apiJson.urls.productManagement.skuPriceUpdateEventUpload, fd, {
                    transformRequest: angular.identity,
                    headers: {
                        'Content-Type': undefined,
                        'userId': currentUser.userId,
                        'userName': currentUser.user.name
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    console.log(response);
                    $scope.init();
                    if (!response) {
                        $scope.init();
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                    $scope.init();
                });
            } else {
                $scope.init();
                $toastService.create('Upload Failed , File Format not Supported');
                return false;
            }
        };
        //console.log($scope.skuUpdatePriceList);
    }]);
