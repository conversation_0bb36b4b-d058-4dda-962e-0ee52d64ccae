angular.module('scmApp')
    .controller('productPackagingCtrl', ['$scope', '$http', 'apiJson', '$toastService', function ($scope, $http, apiJson, $toastService) {

        $scope.init = function () {
            $scope.getAllPackagings();
            $scope.packageType = ["INNER", "CASE"];
            $scope.innerOrCaseName = ["PACKET", "BOTTLE", "BOX"];
            $scope.viewPackages=true;
        };
        $scope.onSelectAddNewPackaging= function(){
            $scope.viewPackages=false;
            $scope.innerOrCase="";
            $scope.searchPackaging="";
            $scope.createNewPackaging=true;
            $scope.clearInsertedValues();
        };
        $scope.viewPackaging= function(){
            $scope.getAllPackagings();
            $scope.clearInsertedValues();
            $scope.viewPackages=true;
            $scope.searchPackaging="";
            $scope.innerOrCase="";
            $scope.isInner=false;
            $scope.isCase=false;
            $scope.createNewPackaging=false;
            $scope.productPackage.$setPristine();

        };
        $scope.selectPackagingType = function () {
            if (($scope.innerOrCase == 'INNER')) {
                $scope.isInner = true;
                $scope.isCase = false;
                $scope.clearInsertedValues();
            }
            if (($scope.innerOrCase == 'CASE')) {
                $scope.getAllPackagings();
                $scope.isInner = false;
                $scope.isCase = true;
                $scope.clearInsertedValues();
            }

        };
        $scope.getAllPackagings = function () {
            $http({
                method: 'GET',
                url: apiJson.urls.productManagement.packagings
            }).then(function (response) {
                console.log(response.data);
                $scope.packagingArray = response.data;
                console.log(JSON.stringify(response.data));
            }, function (response) {
                console.log(response);
            }).finally(function () {
                callback(appUtil.getMetadata().productPackagings);
            });

        };
        $scope.clearInsertedValues = function () {

            $scope.innerName = "";
            $scope.innerUom = "";
            $scope.innerQuantity = "";
            $scope.innerSubPackaging = "";
            $scope.caseSubPackaging = "";
            $scope.caseName = "";
            $scope.caseUom = "";
            $scope.caseQuantity = "";
            $scope.innerFromDatabase = "";
        };
        $scope.onSubmittingInner = function (packagingType, innerOrCasePackagingName, quantity, unitOfMeasure, subPackaging) {
            if (!$scope.innerName || !$scope.innerUom || !$scope.innerQuantity) {
                $toastService.create("Fill all the fields");
            } else {
                if (innerOrCasePackagingName == "PACKET") {
                    $scope.shortPackagingCode = "PKT"
                }
                else {
                    $scope.shortPackagingCode = innerOrCasePackagingName;
                }
                $scope.innerPackagingCode = quantity + "_" + unitOfMeasure + "_" + $scope.shortPackagingCode;
                $scope.innerPackagingName = quantity + " " + unitOfMeasure + " " + innerOrCasePackagingName;
                var innerPackagingData = [{
                    packagingType: packagingType,
                    packagingCode: $scope.innerPackagingCode,
                    packagingName: $scope.innerPackagingName,
                    packagingStatus: "ACTIVE",
                    conversionRatio: quantity,
                    unitOfMeasure: unitOfMeasure,
                   subPackagingId:subPackaging
                }];
                $http({
                    method: 'POST',
                    url: apiJson.urls.productManagement.packaging,
                    data: innerPackagingData
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Packaging added successfully ");
                        $scope.productPackage.$setPristine();
                    }
                }, function error(response) {
                    if (response.data != null && response.data.errorMessage != null) {
                        $toastService.create(response.data.errorMessage);
                    } else {
                        $toastService.create("Error adding packaging");
                    }
                });
                $scope.clearInsertedValues();
            }
        };
        $scope.onSubmittingCase = function (packagingType, innerOrCasePackagingName, quantity, unitOfMeasure, innerFromDatabase) {
            if (!$scope.caseName || !$scope.caseUom || !$scope.caseQuantity || !$scope.innerFromDatabase) {
                $toastService.create("Fill all the fields");
            } else {
                if (innerOrCasePackagingName == "PACKET") {
                    $scope.shortPackagingCode = "PKT"
                }
                else {
                    $scope.shortPackagingCode = innerOrCasePackagingName;
                }
                $scope.casePackagingCode = innerFromDatabase.conversionRatio + "_" + unitOfMeasure + "_" + quantity + "_" + innerFromDatabase.packagingCode.split("_").slice(-1)[0] + "_" + $scope.shortPackagingCode;
                $scope.casePackagingName = innerFromDatabase.conversionRatio + " " + unitOfMeasure + " " + quantity + " " + innerFromDatabase.packagingName.split(" ").slice(-1)[0] + " " + innerOrCasePackagingName;
                var casePackagingData = [{
                    packagingType: packagingType,
                    packagingCode: $scope.casePackagingCode,
                    packagingName: $scope.casePackagingName,
                    packagingStatus: "ACTIVE",
                    conversionRatio: quantity * innerFromDatabase.conversionRatio,
                    unitOfMeasure: unitOfMeasure,
                    subPackagingId: innerFromDatabase.packagingId
                }];
                $http({
                    method: 'POST',
                    url: apiJson.urls.productManagement.packaging,
                    data: casePackagingData
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Packaging added successfully");

                        $scope.viewPackaging();
                        $window.scrollTo(0,0);


                    }
                }, function error(response) {
                    if (response.data != null && response.data.errorMessage != null) {
                        $toastService.create(response.data.errorMessage);
                    } else {
                        $toastService.create("Error adding packaging");
                    }
                });

                $scope.clearInsertedValues();
            }
        }
    }]).filter("uniqueValueFilter", function () {
    return function (collection, keyname) {
        var output = [], keys = [];
        angular.forEach(collection, function (item) {
            var key = item[keyname];
            if (keys.indexOf(key) === -1) {
                keys.push(key);
                output.push(item);
            }
        });
        return output;
    };
});