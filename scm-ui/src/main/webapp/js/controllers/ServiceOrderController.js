'use strict';
angular.module('scmApp')
    .controller('serviceOrderCtrl', ['$rootScope', '$scope', '$state', 'apiJson', '$http', 'appUtil', 'metaDataService',
        '$toastService','$alertService','previewModalService','Popeye',
        function ($rootScope, $scope, $state, apiJson, $http, appUtil, metaDataService,
                                                   $toastService, $alertService, previewModalService, Popeye) {

            $scope.init = function (){
                $scope.locationList = [];
                $scope.serviceItems = null;
                $scope.periods = ["ONE TIME","DAILY","WEEKLY","MONTHLY","YEARLY"];
                metaDataService.getServiceVendors(function(serviceVendors){
                    $scope.vendorList = serviceVendors;
                });
                metaDataService.getCostCenters(function (costCenters) {
                    $scope.costCenters = costCenters;
                });
            };

            $scope.calculateAmount = function () {
                var totalAmount = 0;
                if (!appUtil.isEmptyObject($scope.serviceItems) && $scope.serviceItems.length > 0) {
                    for (var i in $scope.serviceItems) {
                        totalAmount = parseFloat(totalAmount) + parseFloat($scope.serviceItems[i].amountPaid);
                    }
                }
                return totalAmount.toFixed(2);
            };

            $scope.selectCenter = function(selectedCenter){
                $scope.selectedCenter = selectedCenter;
                $scope.elements = angular.copy($scope.selectedCenter.elements);
            };

            $scope.changePeriodicityText = function (periodicity) {
                switch (periodicity){
                    case "DAILY": $scope.periodicityText = "days"; break;
                    case "WEEKLY": $scope.periodicityText = "weeks"; break;
                    case "MONTHLY": $scope.periodicityText = "months"; break;
                    case "YEARLY": $scope.periodicityText = "years"; break;
                    default: $scope.periodicityText = "days"; break;
                }
            };


            $scope.selectVendor = function(vendor){
                $scope.serviceItems = null;
                $scope.selectedDispatchLocation = null;
                $scope.selectedVendor = vendor;
                metaDataService.getVendorLocations($scope.selectedVendor.id, function (locations) {
                    $scope.locationList = locations;
                });
            };

            $scope.selectDispatchLocation = function(location){
                $scope.selectedDispatchLocation = location;
            };

            $scope.selectCostCenter = function(center){
                $scope.selectedCostCenter = center;
                $scope.costElementList = $scope.selectedCostCenter.elements;
            };

            $scope.submit = function(){
                $alertService.confirm("Are you sure?","",function(result){
                   if(result){
                       sendRequestForSO($scope.serviceItems);
                   }
                });
            };

            function sendRequestForSO(items){

                var reqObj = {
                    dispatchLocationId:$scope.selectedDispatchLocation.id,
                    vendorId:$scope.selectedVendor.id,
                    costCenterId: $scope.selectedCenter.id,
                    userId:appUtil.getCurrentUser().userId,
                    items:items
                };

                if(reqObj.items.length>0){
                    $http({
                        method:"POST",
                        url:apiJson.urls.serviceOrderManagement.createServiceOrder,
                        data:reqObj
                    }).then(function(response){
                        if(response.data!=null){
                            $scope.init();
                            $alertService.alert("Congratulations!!",
                                "Service Order with ID: <b>" + response.data + "</b> created successfully! <br>",
                                function(){
                                    $state.go("menu.viewSO",{createdSO:response.data,viewSO:true});
                                }
                            );
                        }else{
                            $toastService.create("Service Order could not be created due to some error!!");
                        }
                    },function(error){
                        console.log(error);
                         if (error.data.errorMsg != null) {
                          $alertService.alert(error.data.errorTitle, error.data.errorMsg, null, true);
                             }
                             else
                             {
                        $toastService.create("Service Order could not be created due to some error!!");
                        }
                    });
                }
            }

            $scope.calculateCost = function (element) {
                if(appUtil.isEmptyObject(element.price) || appUtil.isEmptyObject(element.qty)){
                    return;
                }
                element.cost = parseFloat(parseFloat(element.price) * parseFloat(element.qty));
                element.tax = parseFloat(parseFloat(element.cost) * (parseFloat(element.taxRate)/100)).toFixed(2);
                element.amountPaid = parseFloat(parseFloat(element.cost) + parseFloat(element.tax)).toFixed(2);
            };

            function validateElement(element) {
                var flag = true;
                if(appUtil.isEmptyObject(element.price)){
                    $toastService.create("Please select a valid unit price!");
                    flag = false;
                }
                if(appUtil.isEmptyObject(element.qty)){
                    $toastService.create("Please select a valid quantity!");
                    flag = false;
                }
                if(appUtil.isEmptyObject(element.pkg)){
                    $toastService.create("Please select a valid Unit Of Measure!");
                    flag = false;
                }

                if(appUtil.isEmptyObject(element.description) || element.description.length>500){
                    $toastService.create("Please ensure description is not empty and within 500 characters!");
                    flag = false;
                }
                return flag;
            }

            $scope.addItem = function (costElement){
                if(!validateElement(costElement)){
                    return;
                }
                var element = angular.copy(costElement);
                var iterations = appUtil.isEmptyObject(element.iterations) ? 1 : element.iterations;
                for(var i=0; i < iterations; i++){
                    var item = {
                        ascCode: element.code,
                        costElementId: element.id,
                        costElementName: element.name,
                        serviceDescription: element.description,
                        requestedQuantity: element.qty,
                        unitOfMeasure: element.pkg,
                        unitPrice: element.price,
                        totalCost: element.cost,
                        totalTax: element.tax,
                        amountPaid: element.amountPaid,
                        taxRate: element.taxRate
                    };

                    if($scope.serviceItems==null){
                        $scope.serviceItems=[];
                    }
                    $scope.serviceItems.push(item);
                }
            };

            $scope.removeItem = function(items, index){
                items.splice(index,1);
            };
        }
    ]
);