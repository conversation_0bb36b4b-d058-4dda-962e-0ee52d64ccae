<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.2</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.stpl.tech.kettle.scm</groupId>
    <artifactId>kettle-scm</artifactId>
    <packaging>pom</packaging>
    <version>4.1.0-SNAPSHOT</version>

    <name>kettle-scm</name>
    <url>http://maven.apache.org</url>

    <properties>
        <java.version>17</java.version>
    </properties>

    <distributionManagement>
        <snapshotRepository>
            <id>artifact-registry</id>
            <url>artifactregistry://asia-south1-maven.pkg.dev/titanium-atlas-363108/chaayos</url>
        </snapshotRepository>
        <repository>
            <id>artifact-registry</id>
            <url>artifactregistry://asia-south1-maven.pkg.dev/titanium-atlas-363108/chaayos</url>
        </repository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>jenkins</id>
            <repositories>
                <repository>
                    <id>artifact-registry</id>
                    <url>artifactregistry://asia-south1-maven.pkg.dev/titanium-atlas-363108/chaayos
                    </url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <checkModificationExcludes>
                        <checkModificationExclude>**/pom.xml</checkModificationExclude>
                        <checkModificationExclude>release.properties</checkModificationExclude>
                    </checkModificationExcludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.8.1</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-install</id>
                        <configuration>
                            <skip>${deployOnly}</skip>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-test</id>
                        <configuration>
                            <skip>${deployOnly}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <skipMain>${deployOnly}</skipMain>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <extensions>
            <extension>
                <groupId>com.google.cloud.artifactregistry</groupId>
                <artifactId>artifactregistry-maven-wagon</artifactId>
                <version>2.2.0</version>
            </extension>
        </extensions>
    </build>

    <modules>
        <module>scm-db</module>
        <module>scm-domain</module>
        <module>scm-core</module>
        <module>scm-service</module>
<!--        <module>scm-ui</module>-->
    </modules>
</project>
