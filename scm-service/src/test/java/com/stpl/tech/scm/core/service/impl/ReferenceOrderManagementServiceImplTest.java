package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.model.StrategyType;
import com.stpl.tech.scm.data.dao.SalesForecastingInputDataDao;
import com.stpl.tech.scm.data.dao.SuggestiveOrderingStrategyDao;
import com.stpl.tech.scm.data.model.SalesForecastingInputData;
import com.stpl.tech.scm.data.model.StrategyMeanMetaData;
import com.stpl.tech.scm.data.model.SuggestiveOrderingStrategyMetadata;
import com.stpl.tech.scm.domain.model.MenuProductSalesAverageDto;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ReferenceOrderManagementServiceImplTest {

    @Mock
    private SuggestiveOrderingStrategyDao suggestiveOrderingStrategyDao;

    @Mock
    private SalesForecastingInputDataDao salesForecastingInputDataDao;

    @InjectMocks
    private ReferenceOrderManagementServiceImpl referenceOrderManagementService;

    private static final int UNIT_ID = 26277;
    private static final String BRAND_NAME = "CHAAYOS";
    private List<LocalDate> daysList;
    private SuggestiveOrderingStrategyMetadata strategy;
    private StrategyMeanMetaData strategyMeanMetaData;

    @BeforeEach
    void setUp() {
        daysList = Arrays.asList(
                LocalDate.of(2025, 6, 15),
                LocalDate.of(2025, 6, 16)
        );

        strategyMeanMetaData = new StrategyMeanMetaData();
        strategyMeanMetaData.setStartWeek(2);
        strategyMeanMetaData.setEndWeek(4);
        strategyMeanMetaData.setPercentage(BigDecimal.valueOf(100.00));

        strategy = new SuggestiveOrderingStrategyMetadata();
        strategy.setUnitId(UNIT_ID);
        strategy.setStrategyType(StrategyType.GM);
        strategy.setStatus(SwitchStatus.ACTIVE);
        strategy.setSafetyWeeks(2);
        strategy.setNoOfWeeks(4);
        strategy.setStrategyMeanMetaDataSet(Set.of());
    }

    @Test
    void testGetMenuProductsConsumptionAverage_Success() throws SumoException {
        // Given
        when(suggestiveOrderingStrategyDao.findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE))
                .thenReturn(strategy);

        when(salesForecastingInputDataDao.findByUnitIdAndDateIn(eq(UNIT_ID), anyList()))
                .thenReturn(createMockSalesData());

        // When
        Map<String, List<MenuProductSalesAverageDto>> result = 
                referenceOrderManagementService.getMenuProductsConsumptionAverage(UNIT_ID, BRAND_NAME, daysList);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.containsKey("2025-06-15"));
        assertTrue(result.containsKey("2025-06-16"));

        List<MenuProductSalesAverageDto> day1Products = result.get("2025-06-16");
        assertNotNull(day1Products);
        assertFalse(!day1Products.isEmpty());

        MenuProductSalesAverageDto product = day1Products.get(0);
        assertEquals(1001, product.getProductId());
        assertEquals("Green Tea", product.getProductName());
        assertEquals("None", product.getDimension());
        assertTrue(product.getAverageSalesQuantity() >= 0);

        verify(suggestiveOrderingStrategyDao, times(1))
                .findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE);
    }

    @Test
    void testGetMenuProductsConsumptionAverage_NoStrategyFound() {
        // Given
        when(suggestiveOrderingStrategyDao.findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE))
                .thenReturn(null);

        // When & Then
        SumoException exception = assertThrows(SumoException.class, () -> {
            referenceOrderManagementService.getMenuProductsConsumptionAverage(UNIT_ID, BRAND_NAME, daysList);
        });

        assertEquals("Suggestive Ordering Strategy is not available for unit :: " + UNIT_ID, 
                exception.getMessage());

        verify(suggestiveOrderingStrategyDao, times(1))
                .findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE);
    }

    @Test
    void testGetMenuProductsConsumptionAverage_EmptyDaysList() throws SumoException {
        // Given
        List<LocalDate> emptyDaysList = new ArrayList<>();
        when(suggestiveOrderingStrategyDao.findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE))
                .thenReturn(strategy);

        // When
        Map<String, List<MenuProductSalesAverageDto>> result = 
                referenceOrderManagementService.getMenuProductsConsumptionAverage(UNIT_ID, BRAND_NAME, emptyDaysList);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(suggestiveOrderingStrategyDao, times(1))
                .findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE);
    }

    @Test
    void testGetMenuProductsConsumptionAverage_NoSalesData() throws SumoException {
        // Given
        when(suggestiveOrderingStrategyDao.findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE))
                .thenReturn(strategy);

        when(salesForecastingInputDataDao.findByUnitIdAndDateIn(eq(UNIT_ID), anyList()))
                .thenReturn(Collections.emptyList());

        // When
        Map<String, List<MenuProductSalesAverageDto>> result = 
                referenceOrderManagementService.getMenuProductsConsumptionAverage(UNIT_ID, BRAND_NAME, daysList);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Should have entries for both dates
        assertNotNull(result.get("2025-01-15"));
        assertTrue(result.get("2025-01-15").isEmpty());
        assertNotNull(result.get("2025-01-16"));
        assertTrue(result.get("2025-01-16").isEmpty());

        verify(suggestiveOrderingStrategyDao, times(1))
                .findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE);
    }

    @Test
    void testGetMenuProductsConsumptionAverage_DatabaseException() {
        // Given
        when(suggestiveOrderingStrategyDao.findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            referenceOrderManagementService.getMenuProductsConsumptionAverage(UNIT_ID, BRAND_NAME, daysList);
        });

        assertTrue(exception.getMessage().contains("Database connection failed"));

        verify(suggestiveOrderingStrategyDao, times(1))
                .findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE);
    }

    @Test
    void testGetMenuProductsConsumptionAverage_InvalidUnitId() {
        // Given
        int invalidUnitId = -1;
        when(suggestiveOrderingStrategyDao.findByUnitIdAndStatus(invalidUnitId, SwitchStatus.ACTIVE))
                .thenReturn(null);

        // When & Then
        SumoException exception = assertThrows(SumoException.class, () -> {
            referenceOrderManagementService.getMenuProductsConsumptionAverage(invalidUnitId, BRAND_NAME, daysList);
        });

        assertEquals("Suggestive Ordering Strategy is not available for unit :: " + invalidUnitId, 
                exception.getMessage());
    }

    @Test
    void testGetMenuProductsConsumptionAverage_MultipleProducts() throws SumoException {
        // Given
        when(suggestiveOrderingStrategyDao.findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE))
                .thenReturn(strategy);

        when(salesForecastingInputDataDao.findByUnitIdAndDateIn(eq(UNIT_ID), anyList()))
                .thenReturn(createMockMultipleSalesForSameProductSameDate());

        // When
        Map<String, List<MenuProductSalesAverageDto>> result =
                referenceOrderManagementService.getMenuProductsConsumptionAverage(UNIT_ID, BRAND_NAME, daysList);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());

        List<MenuProductSalesAverageDto> day1Products = result.get("2025-01-15");
        assertNotNull(day1Products);
        assertEquals(2, day1Products.size()); // Should have 2 products

        // Verify product details
        MenuProductSalesAverageDto product1 = day1Products.stream()
                .filter(p -> p.getProductId() == 1001)
                .findFirst()
                .orElse(null);
        assertNotNull(product1);
        assertEquals("Green Tea", product1.getProductName());

        MenuProductSalesAverageDto product2 = day1Products.stream()
                .filter(p -> p.getProductId() == 1002)
                .findFirst()
                .orElse(null);
        assertNotNull(product2);
        assertEquals("Black Coffee", product2.getProductName());
    }

    @Test
    void testGetMenuProductsConsumptionAverage_ZeroSalesQuantity() throws SumoException {
        // Given
        when(suggestiveOrderingStrategyDao.findByUnitIdAndStatus(UNIT_ID, SwitchStatus.ACTIVE))
                .thenReturn(strategy);

        when(salesForecastingInputDataDao.findByUnitIdAndDateIn(eq(UNIT_ID), anyList()))
                .thenReturn(createMockZeroSalesData());

        // When
        Map<String, List<MenuProductSalesAverageDto>> result = 
                referenceOrderManagementService.getMenuProductsConsumptionAverage(UNIT_ID, BRAND_NAME, daysList);

        // Then
        assertNotNull(result);
        List<MenuProductSalesAverageDto> day1Products = result.get("2025-01-15");
        assertNotNull(day1Products);
        assertFalse(day1Products.isEmpty());

        MenuProductSalesAverageDto product = day1Products.get(0);
        assertEquals(0, product.getAverageSalesQuantity());
    }


    private List<SalesForecastingInputData> createMockMultipleSalesForSameProductSameDate() {
        List<SalesForecastingInputData> dataList = new ArrayList<>();

        // Product 1001, Date 2025-01-15, multiple sales entries
        SalesForecastingInputData data1 = new SalesForecastingInputData();
        data1.setProductId(1001);
        data1.setProductName("Green Tea");
        data1.setDimension("None");
        data1.setTotalSaleQuantity(5);
        data1.setDateOfOrdering(LocalDate.of(2025, 1, 15));
        dataList.add(data1);

        SalesForecastingInputData data2 = new SalesForecastingInputData();
        data2.setProductId(1001);
        data2.setProductName("Green Tea");
        data2.setDimension("None");
        data2.setTotalSaleQuantity(4);
        data2.setDateOfOrdering(LocalDate.of(2025, 1, 15));
        dataList.add(data2);

        // Product 1002, Date 2025-01-15, multiple sales entries
        SalesForecastingInputData data3 = new SalesForecastingInputData();
        data3.setProductId(1002);
        data3.setProductName("Black Coffee");
        data3.setDimension("Large");
        data3.setTotalSaleQuantity(3);
        data3.setDateOfOrdering(LocalDate.of(2025, 1, 15));
        dataList.add(data3);

        SalesForecastingInputData data4 = new SalesForecastingInputData();
        data4.setProductId(1002);
        data4.setProductName("Black Coffee");
        data4.setDimension("Large");
        data4.setTotalSaleQuantity(6);
        data4.setDateOfOrdering(LocalDate.of(2025, 1, 15));
        dataList.add(data4);

        return dataList;
    }

    private List<SalesForecastingInputData> createMockZeroSalesData() {
        SalesForecastingInputData data1 = new SalesForecastingInputData();
        data1.setProductId(1001);
        data1.setProductName("Green Tea");
        data1.setDimension("None");
        data1.setTotalSaleQuantity(0);
        data1.setDateOfOrdering(LocalDate.of(2025, 1, 15));

        SalesForecastingInputData data2 = new SalesForecastingInputData();
        data2.setProductId(1001);
        data2.setProductName("Green Tea");
        data2.setDimension("None");
        data2.setTotalSaleQuantity(0);
        data2.setDateOfOrdering(LocalDate.of(2025, 1, 15));

        return Arrays.asList(data1, data2);
    }

    private List<SalesForecastingInputData> createMockSalesData() {
        SalesForecastingInputData data1 = new SalesForecastingInputData();
        data1.setProductId(670);
        data1.setProductName("Vada Pav");
        data1.setDimension("None");
        data1.setTotalSaleQuantity(5);
        data1.setDateOfOrdering(LocalDate.of(2025, 6, 15));

        SalesForecastingInputData data2 = new SalesForecastingInputData();
        data2.setProductId(1201);
        data2.setProductName("Samosa Matar Chaat");
        data2.setDimension("None");
        data2.setTotalSaleQuantity(3);
        data2.setDateOfOrdering(LocalDate.of(2025, 6, 16));

        return Arrays.asList(data1, data2);
    }
}
