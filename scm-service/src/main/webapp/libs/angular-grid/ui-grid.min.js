/*!
 * ui-grid - v4.11.1 - 2022-02-23
 * Copyright (c) 2022 ; License: MIT
 */


!function(){"use strict";angular.module("ui.grid.i18n",[]),angular.module("ui.grid",["ui.grid.i18n"])}(),function(){"use strict";angular.module("ui.grid.autoResize",["ui.grid"]).directive("uiGridAutoResize",["gridUtil",function(i){return{require:"uiGrid",scope:!1,link:function(e,n,t,o){var r;r=i.debounce(function(e,t,r,i){null!==n[0].offsetParent&&(o.grid.gridWidth=r,o.grid.gridHeight=i,o.grid.queueGridRefresh().then(function(){o.grid.api.core.raise.gridDimensionChanged(t,e,i,r)}))},400),e.$watchCollection(function(){return{width:i.elementWidth(n),height:i.elementHeight(n)}},function(e,t){angular.equals(e,t)||r(t.width,t.height,e.width,e.height)})}}}])}(),function(){"use strict";var e=angular.module("ui.grid.cellNav",["ui.grid"]);e.constant("uiGridCellNavConstants",{FEATURE_NAME:"gridCellNav",CELL_NAV_EVENT:"cellNav",direction:{LEFT:0,RIGHT:1,UP:2,DOWN:3,PG_UP:4,PG_DOWN:5},EVENT_TYPE:{KEYDOWN:0,CLICK:1,CLEAR:2}}),e.factory("uiGridCellNavFactory",["gridUtil","uiGridConstants","uiGridCellNavConstants","GridRowColumn","$q",function(e,t,i,l,r){var n=function(e,t,r,i){this.rows=e.visibleRowCache,this.columns=t.visibleColumnCache,this.leftColumns=r?r.visibleColumnCache:[],this.rightColumns=i?i.visibleColumnCache:[],this.bodyContainer=e};return n.prototype.getFocusableCols=function(){return this.leftColumns.concat(this.columns,this.rightColumns).filter(function(e){return e.colDef.allowCellFocus})},n.prototype.getFocusableRows=function(){return this.rows.filter(function(e){return!1!==e.allowCellFocus})},n.prototype.getNextRowCol=function(e,t,r){switch(e){case i.direction.LEFT:return this.getRowColLeft(t,r);case i.direction.RIGHT:return this.getRowColRight(t,r);case i.direction.UP:return this.getRowColUp(t,r);case i.direction.DOWN:return this.getRowColDown(t,r);case i.direction.PG_UP:return this.getRowColPageUp(t,r);case i.direction.PG_DOWN:return this.getRowColPageDown(t,r)}},n.prototype.initializeSelection=function(){var e=this.getFocusableCols(),t=this.getFocusableRows();return 0===e.length||0===t.length?null:new l(t[0],e[0])},n.prototype.getRowColLeft=function(e,t){var r=this.getFocusableCols(),i=this.getFocusableRows(),n=r.indexOf(t),o=i.indexOf(e);-1===n&&(n=1);var a=0===n?r.length-1:n-1;return new l(n<=a?0===o?e:i[o-1]:e,r[a])},n.prototype.getRowColRight=function(e,t){var r=this.getFocusableCols(),i=this.getFocusableRows(),n=r.indexOf(t),o=i.indexOf(e);-1===n&&(n=0);var a=n===r.length-1?0:n+1;return a<=n?o===i.length-1?new l(e,r[a]):new l(i[o+1],r[a]):new l(e,r[a])},n.prototype.getRowColDown=function(e,t){var r=this.getFocusableCols(),i=this.getFocusableRows(),n=r.indexOf(t),o=i.indexOf(e);return-1===n&&(n=0),o===i.length-1?new l(e,r[n]):new l(i[o+1],r[n])},n.prototype.getRowColPageDown=function(e,t){var r=this.getFocusableCols(),i=this.getFocusableRows(),n=r.indexOf(t),o=i.indexOf(e);-1===n&&(n=0);var a=this.bodyContainer.minRowsToRender();return o>=i.length-a?new l(i[i.length-1],r[n]):new l(i[o+a],r[n])},n.prototype.getRowColUp=function(e,t){var r=this.getFocusableCols(),i=this.getFocusableRows(),n=r.indexOf(t),o=i.indexOf(e);return-1===n&&(n=0),new l(0===o?e:i[o-1],r[n])},n.prototype.getRowColPageUp=function(e,t){var r=this.getFocusableCols(),i=this.getFocusableRows(),n=r.indexOf(t),o=i.indexOf(e);-1===n&&(n=0);var a=this.bodyContainer.minRowsToRender();return new l(o-a<0?i[0]:i[o-a],r[n])},n}]),e.service("uiGridCellNavService",["gridUtil","uiGridConstants","uiGridCellNavConstants","$q","uiGridCellNavFactory","GridRowColumn","ScrollEvent",function(e,t,r,i,n,o,a){var l={initializeGrid:function(i){i.registerColumnBuilder(l.cellNavColumnBuilder),i.cellNav={},i.cellNav.lastRowCol=null,i.cellNav.focusedCells=[],l.defaultGridOptions(i.options);var e={events:{cellNav:{navigate:function(e,t){},viewPortKeyDown:function(e,t){},viewPortKeyPress:function(e,t){}}},methods:{cellNav:{scrollToFocus:function(e,t){return l.scrollToFocus(i,e,t)},getFocusedCell:function(){return i.cellNav.lastRowCol},getCurrentSelection:function(){return i.cellNav.focusedCells},rowColSelectIndex:function(e){for(var t=-1,r=0;r<i.cellNav.focusedCells.length;r++)if(i.cellNav.focusedCells[r].col.uid===e.col.uid&&i.cellNav.focusedCells[r].row.uid===e.row.uid){t=r;break}return t}}}};i.api.registerEventsFromObject(e.events),i.api.registerMethodsFromObject(e.methods)},defaultGridOptions:function(e){e.modifierKeysToMultiSelectCells=!0===e.modifierKeysToMultiSelectCells,e.keyDownOverrides=e.keyDownOverrides||[]},decorateRenderContainers:function(e){var t=e.hasRightContainer()?e.renderContainers.right:null,r=e.hasLeftContainer()?e.renderContainers.left:null;null!==r&&(e.renderContainers.left.cellNav=new n(e.renderContainers.body,r,t,e.renderContainers.body)),null!==t&&(e.renderContainers.right.cellNav=new n(e.renderContainers.body,t,e.renderContainers.body,r)),e.renderContainers.body.cellNav=new n(e.renderContainers.body,e.renderContainers.body,r,t)},getDirection:function(e){return e.keyCode===t.keymap.LEFT||e.keyCode===t.keymap.TAB&&e.shiftKey?r.direction.LEFT:e.keyCode===t.keymap.RIGHT||e.keyCode===t.keymap.TAB?r.direction.RIGHT:e.keyCode===t.keymap.UP||e.keyCode===t.keymap.ENTER&&e.shiftKey?r.direction.UP:e.keyCode===t.keymap.PG_UP?r.direction.PG_UP:e.keyCode===t.keymap.DOWN||e.keyCode===t.keymap.ENTER&&!e.ctrlKey&&!e.altKey?r.direction.DOWN:e.keyCode===t.keymap.PG_DOWN?r.direction.PG_DOWN:null},cellNavColumnBuilder:function(e,t,r){return e.allowCellFocus=void 0===e.allowCellFocus||e.allowCellFocus,i.all([])},scrollToFocus:function(t,e,r){var i=null,n=null;return null!=e&&(i=t.getRow(e)),null!=r&&(n=t.getColumn(r.name?r.name:r.field)),t.api.core.scrollToIfNecessary(i,n).then(function(){var e={row:i,col:n};null!==i&&null!==n&&t.cellNav.broadcastCellNav(e,null,null)})},getLeftWidth:function(e,t){var r=0;if(!t)return r;var i=e.renderContainers.body.visibleColumnCache.indexOf(t);e.renderContainers.body.visibleColumnCache.forEach(function(e,t){t<i&&(r+=e.drawnWidth)});var n=0===i?0:(i+1)/e.renderContainers.body.visibleColumnCache.length;return r+=t.drawnWidth*n}};return l}]),e.directive("uiGridCellnav",["gridUtil","uiGridCellNavService","uiGridCellNavConstants","uiGridConstants","GridRowColumn","$timeout","$compile","i18nService",function(e,d,c,u,g,t,a,f){return{replace:!0,priority:-150,require:"^uiGrid",scope:!1,controller:function(){},compile:function(){return{pre:function(e,t,r,l){var i=e,s=l.grid;d.initializeGrid(s),l.cellNav={},l.cellNav.makeRowCol=function(e){return e instanceof g||(e=new g(e.row,e.col)),e},l.cellNav.getActiveCell=function(){var e=t[0].getElementsByClassName("ui-grid-cell-focus");if(0<e.length)return e[0]},l.cellNav.broadcastCellNav=s.cellNav.broadcastCellNav=function(e,t,r){t=!(void 0===t||!t),e=l.cellNav.makeRowCol(e),l.cellNav.broadcastFocus(e,t,r),i.$broadcast(c.CELL_NAV_EVENT,e,t,r)},l.cellNav.clearFocus=s.cellNav.clearFocus=function(){s.cellNav.focusedCells=[],i.$broadcast(c.CELL_NAV_EVENT)},l.cellNav.broadcastFocus=function(e,t,r){t=!(void 0===t||!t);var i=(e=l.cellNav.makeRowCol(e)).row,n=e.col,o=l.grid.api.cellNav.rowColSelectIndex(e);if(null===s.cellNav.lastRowCol||-1===o||s.cellNav.lastRowCol.col===n&&s.cellNav.lastRowCol.row===i){var a=new g(i,n);(null===s.cellNav.lastRowCol||s.cellNav.lastRowCol.row!==a.row||s.cellNav.lastRowCol.col!==a.col||s.options.enableCellEditOnFocus)&&(s.api.cellNav.raise.navigate(a,s.cellNav.lastRowCol,r),s.cellNav.lastRowCol=a),l.grid.options.modifierKeysToMultiSelectCells&&t?s.cellNav.focusedCells.push(e):s.cellNav.focusedCells=[e]}else s.options.modifierKeysToMultiSelectCells&&t&&0<=o&&s.cellNav.focusedCells.splice(o,1)},l.cellNav.handleKeyDown=function(e){var t=d.getDirection(e);if(null===t)return null;var r="body";e.uiGridTargetRenderContainerId&&(r=e.uiGridTargetRenderContainerId);var i=l.grid.api.cellNav.getFocusedCell();if(i){var n=l.grid.renderContainers[r].cellNav.getNextRowCol(t,i.row,i.col),o=l.grid.renderContainers[r].cellNav.getFocusableCols(),a=l.grid.api.cellNav.rowColSelectIndex(n);return t===c.direction.LEFT&&n.col===o[o.length-1]&&n.row===i.row&&e.keyCode===u.keymap.TAB&&e.shiftKey?(s.cellNav.focusedCells.splice(a,1),l.cellNav.clearFocus(),!0):t!==c.direction.RIGHT||n.col!==o[0]||n.row!==i.row||e.keyCode!==u.keymap.TAB||e.shiftKey?(s.scrollToIfNecessary(n.row,n.col).then(function(){l.cellNav.broadcastCellNav(n,null,e)}),e.stopPropagation(),e.preventDefault(),!1):(s.cellNav.focusedCells.splice(a,1),l.cellNav.clearFocus(),!0)}}},post:function(e,t,r,i){var n,g,p=i.grid,o=!0;try{angular.module("ngAria")}catch(e){o=!1}o&&(n='<div id="'+p.id+'-aria-speakable" class="ui-grid-a11y-ariascreenreader-speakable ui-grid-offscreen" aria-live="assertive" role="alert" aria-atomic="true" aria-hidden="false" aria-relevant="additions" >&nbsp;</div>',g=a(n)(e),t.prepend(g),e.$on(c.CELL_NAV_EVENT,function(e,t,r,i){if(!i||"focus"!==i.type){for(var n,o,a,l,s=[],d=p.api.cellNav.getCurrentSelection(),c=0;c<d.length;c++){var u=(o=d[c],a=void 0,a="","selectionRowHeaderCol"===o.col.field&&(a=(o.row.isSelected?f.getSafeText("search.aria.selected"):f.getSafeText("search.aria.notSelected"))+", "),a+p.getCellDisplayValue(o.row,o.col)+(n=d[c].col,", "+f.getSafeText("headerCell.aria.column")+" "+n.displayName));s.push(u)}(l=s.toString())!==g.text().trim()&&(g[0].style.clip="rect(0px,0px,0px,0px)",g[0].innerHTML="",g[0].style.visibility="hidden",g[0].style.visibility="visible",""!==l&&(g[0].style.clip="auto",g[0].appendChild(document.createTextNode(l+" ")),g[0].style.visibility="hidden",g[0].style.visibility="visible"))}}))}}}}}]),e.directive("uiGridRenderContainer",["$timeout","$document","gridUtil","uiGridConstants","uiGridCellNavService","$compile","uiGridCellNavConstants",function(u,e,g,t,p,f,m){return{replace:!0,priority:-99999,require:["^uiGrid","uiGridRenderContainer","?^uiGridCellnav"],scope:!1,compile:function(){return{post:function(e,t,r,i){var n=i[0],o=i[1],a=i[2];if(n.grid.api.cellNav){var l=o.containerId,s=n.grid;if(p.decorateRenderContainers(s),"body"===l){n.grid.options.modifierKeysToMultiSelectCells?t.attr("aria-multiselectable",!0):t.attr("aria-multiselectable",!1);var d=f('<div class="ui-grid-focuser" role="region" aria-live="assertive" aria-atomic="false" tabindex="0" aria-controls="'+s.id+"-aria-speakable "+s.id+'-grid-container" aria-owns="'+s.id+'-grid-container"></div>')(e);t.append(d),d.on("focus",function(e){e.uiGridTargetRenderContainerId=l;var t=n.grid.api.cellNav.getFocusedCell();null===t&&(t=n.grid.renderContainers[l].cellNav.getNextRowCol(m.direction.DOWN,null,null)).row&&t.col&&n.cellNav.broadcastCellNav(t)}),a.setAriaActivedescendant=function(e){t.attr("aria-activedescendant",e)},a.removeAriaActivedescendant=function(e){t.attr("aria-activedescendant")===e&&t.attr("aria-activedescendant","")},n.focus=function(){g.focus.byElement(d[0])};var c=null;d.on("keydown",function(r){r.uiGridTargetRenderContainerId=l;var e=n.grid.api.cellNav.getFocusedCell();null===(n.grid.options.keyDownOverrides.some(function(t){return Object.keys(t).every(function(e){return t[e]===r[e]})})?null:n.cellNav.handleKeyDown(r))&&(n.grid.api.cellNav.raise.viewPortKeyDown(r,e,n.cellNav.handleKeyDown),c=e)}),d.on("keypress",function(e){c&&(u(function(){n.grid.api.cellNav.raise.viewPortKeyPress(e,c)},4),c=null)}),e.$on("$destroy",function(){d.off()})}}}}}}}]),e.directive("uiGridViewport",function(){return{replace:!0,priority:-99999,require:["^uiGrid","^uiGridRenderContainer","?^uiGridCellnav"],scope:!1,compile:function(){return{pre:function(e,t,r,i){},post:function(e,t,r,i){var n=i[0],o=i[1];if(n.grid.api.cellNav&&"body"===o.containerId){var a=n.grid;a.api.core.on.scrollBegin(e,function(){var e=n.grid.api.cellNav.getFocusedCell();null!==e&&o.colContainer.containsColumn(e.col)&&n.cellNav.clearFocus()}),a.api.core.on.scrollEnd(e,function(e){var t=n.grid.api.cellNav.getFocusedCell();null!==t&&o.colContainer.containsColumn(t.col)&&n.cellNav.broadcastCellNav(t)}),a.api.cellNav.on.navigate(e,function(){n.focus()})}}}}}}),e.directive("uiGridCell",["$timeout","$document","uiGridCellNavService","gridUtil","uiGridCellNavConstants","uiGridConstants","GridRowColumn",function(e,t,r,i,u,g,p){return{priority:-150,restrict:"A",require:["^uiGrid","?^uiGridCellnav"],scope:!1,link:function(r,t,e,i){var n=i[0],o=i[1];if(n.grid.api.cellNav&&r.col.colDef.allowCellFocus){var a=n.grid;r.focused=!1,t.attr("tabindex",-1),t.find("div").on("click",function(e){n.cellNav.broadcastCellNav(new p(r.row,r.col),e.ctrlKey||e.metaKey,e),e.stopPropagation(),r.$apply()}),t.on("mousedown",s),n.grid.api.edit&&(n.grid.api.edit.on.beginCellEdit(r,function(){t.off("mousedown",s)}),n.grid.api.edit.on.afterCellEdit(r,function(){t.on("mousedown",s)}),n.grid.api.edit.on.cancelCellEdit(r,function(){t.on("mousedown",s)})),d(),t.on("focus",function(e){n.cellNav.broadcastCellNav(new p(r.row,r.col),!1,e),e.stopPropagation(),r.$apply()}),r.$on(u.CELL_NAV_EVENT,d);var l=n.grid.registerDataChangeCallback(function(e){c(),r.$applyAsync(d)},[g.dataChange.ROW]);r.$on("$destroy",function(){l(),t.find("div").off(),t.off()})}function s(e){e.preventDefault()}function d(){a.cellNav.focusedCells.some(function(e,t){return e.row===r.row&&e.col===r.col})?function(){if(!r.focused){var e=t.find("div");e.addClass("ui-grid-cell-focus"),t.attr("aria-selected",!0),o.setAriaActivedescendant(t.attr("id")),r.focused=!0}}():c()}function c(){r.focused&&(t.find("div").removeClass("ui-grid-cell-focus"),t.attr("aria-selected",!1),o.removeAriaActivedescendant(t.attr("id")),r.focused=!1)}}}}])}(),function(){"use strict";angular.module("ui.grid").constant("uiGridConstants",{LOG_DEBUG_MESSAGES:!0,LOG_WARN_MESSAGES:!0,LOG_ERROR_MESSAGES:!0,CUSTOM_FILTERS:/CUSTOM_FILTERS/g,COL_FIELD:/COL_FIELD/g,MODEL_COL_FIELD:/MODEL_COL_FIELD/g,TOOLTIP:/title=\"TOOLTIP\"/g,DISPLAY_CELL_TEMPLATE:/DISPLAY_CELL_TEMPLATE/g,TEMPLATE_REGEXP:/<.+>/,FUNC_REGEXP:/(\([^)]*\))?$/,DOT_REGEXP:/\./g,APOS_REGEXP:/'/g,BRACKET_REGEXP:/^(.*)((?:\s*\[\s*\d+\s*\]\s*)|(?:\s*\[\s*"(?:[^"\\]|\\.)*"\s*\]\s*)|(?:\s*\[\s*'(?:[^'\\]|\\.)*'\s*\]\s*))(.*)$/,COL_CLASS_PREFIX:"ui-grid-col",ENTITY_BINDING:"$$this",events:{GRID_SCROLL:"uiGridScroll",COLUMN_MENU_SHOWN:"uiGridColMenuShown",ITEM_DRAGGING:"uiGridItemDragStart",COLUMN_HEADER_CLICK:"uiGridColumnHeaderClick"},keymap:{TAB:9,STRG:17,CAPSLOCK:20,CTRL:17,CTRLRIGHT:18,CTRLR:18,SHIFT:16,RETURN:13,ENTER:13,BACKSPACE:8,BCKSP:8,ALT:18,ALTR:17,ALTRIGHT:17,SPACE:32,WIN:91,MAC:91,FN:null,PG_UP:33,PG_DOWN:34,UP:38,DOWN:40,LEFT:37,RIGHT:39,ESC:27,DEL:46,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123},ASC:"asc",DESC:"desc",filter:{STARTS_WITH:2,ENDS_WITH:4,EXACT:8,CONTAINS:16,GREATER_THAN:32,GREATER_THAN_OR_EQUAL:64,LESS_THAN:128,LESS_THAN_OR_EQUAL:256,NOT_EQUAL:512,SELECT:"select",INPUT:"input"},aggregationTypes:{sum:2,count:4,avg:8,min:16,max:32},CURRENCY_SYMBOLS:["¤","؋","Ar","Ƀ","฿","B/.","Br","Bs.","Bs.F.","GH₵","¢","c","Ch.","₡","C$","D","ден","دج",".د.ب","د.ع","JD","د.ك","ل.د","дин","د.ت","د.م.","د.إ","Db","$","₫","Esc","€","ƒ","Ft","FBu","FCFA","CFA","Fr","FRw","G","gr","₲","h","₴","₭","Kč","kr","kn","MK","ZK","Kz","K","L","Le","лв","E","lp","M","KM","MT","₥","Nfk","₦","Nu.","UM","T$","MOP$","₱","Pt.","£","ج.م.","LL","LS","P","Q","q","R","R$","ر.ع.","ر.ق","ر.س","៛","RM","p","Rf.","₹","₨","SRe","Rp","₪","Ksh","Sh.So.","USh","S/","SDR","сом","৳\t","WS$","₮","VT","₩","¥","zł"],scrollDirection:{UP:"up",DOWN:"down",LEFT:"left",RIGHT:"right",NONE:"none"},dataChange:{ALL:"all",EDIT:"edit",ROW:"row",COLUMN:"column",OPTIONS:"options"},scrollbars:{NEVER:0,ALWAYS:1,WHEN_NEEDED:2}})}(),angular.module("ui.grid").directive("uiGridCell",["$compile","$parse","gridUtil","uiGridConstants",function(a,e,l,s){return{priority:0,scope:!1,require:"?^uiGrid",compile:function(){return{pre:function(t,r,e,i){if(i&&t.col.compiledElementFn)(0,t.col.compiledElementFn)(t,function(e,t){r.append(e)});else if(i&&!t.col.compiledElementFn)t.col.getCompiledElementFn().then(function(e){e(t,function(e,t){r.append(e)})}).catch(angular.noop);else{var n=t.col.cellTemplate.replace(s.MODEL_COL_FIELD,"row.entity."+l.preEval(t.col.field)).replace(s.COL_FIELD,"grid.getCellValue(row, col)"),o=a(n)(t);r.append(o)}},post:function(i,n){var o,a=i.col.getColClass(!1);function l(e){var t=n;o&&(t.removeClass(o),o=null),o=angular.isFunction(i.col.cellClass)?i.col.cellClass(i.grid,i.row,i.col,i.rowRenderIndex,i.colRenderIndex):i.col.cellClass,t.addClass(o)}n.addClass(a),i.col.cellClass&&l();var e=i.grid.registerDataChangeCallback(l,[s.dataChange.COLUMN,s.dataChange.EDIT]);var t=i.$watch("row",function(e,t){if(e!==t){(o||i.col.cellClass)&&l();var r=i.col.getColClass(!1);r!==a&&(n.removeClass(a),n.addClass(r),a=r)}});function r(){e(),t()}i.$on("$destroy",r),n.on("$destroy",r)}}}}}]),angular.module("ui.grid").service("uiGridColumnMenuService",["i18nService","uiGridConstants","gridUtil",function(e,r,g){var i={initialize:function(e,t){e.grid=t.grid,(t.columnMenuScope=e).menuShown=!1},setColMenuItemWatch:function(t){var e=t.$watch("col.menuItems",function(e){void 0!==e&&e&&angular.isArray(e)?(e.forEach(function(e){void 0!==e.context&&e.context||(e.context={}),e.context.col=t.col}),t.menuItems=t.defaultMenuItems.concat(e)):t.menuItems=t.defaultMenuItems});t.$on("$destroy",e)},getGridOption:function(e,t){return void 0!==e.grid&&e.grid&&e.grid.options&&e.grid.options[t]},sortable:function(e){return Boolean(this.getGridOption(e,"enableSorting")&&void 0!==e.col&&e.col&&e.col.enableSorting)},isActiveSort:function(e,t){return Boolean(void 0!==e.col&&void 0!==e.col.sort&&void 0!==e.col.sort.direction&&e.col.sort.direction===t)},suppressRemoveSort:function(e){return Boolean(e.col&&e.col.suppressRemoveSort)},hideable:function(e){return Boolean(this.getGridOption(e,"enableHiding")&&void 0!==e.col&&e.col&&(e.col.colDef&&!1!==e.col.colDef.enableHiding||!e.col.colDef)||!this.getGridOption(e,"enableHiding")&&e.col&&e.col.colDef&&e.col.colDef.enableHiding)},getDefaultMenuItems:function(t){return[{title:function(){return e.getSafeText("sort.ascending")},icon:"ui-grid-icon-sort-alt-up",action:function(e){e.stopPropagation(),t.sortColumn(e,r.ASC)},shown:function(){return i.sortable(t)},active:function(){return i.isActiveSort(t,r.ASC)}},{title:function(){return e.getSafeText("sort.descending")},icon:"ui-grid-icon-sort-alt-down",action:function(e){e.stopPropagation(),t.sortColumn(e,r.DESC)},shown:function(){return i.sortable(t)},active:function(){return i.isActiveSort(t,r.DESC)}},{title:function(){return e.getSafeText("sort.remove")},icon:"ui-grid-icon-cancel",action:function(e){e.stopPropagation(),t.unsortColumn()},shown:function(){return i.sortable(t)&&void 0!==t.col&&void 0!==t.col.sort&&void 0!==t.col.sort.direction&&null!==t.col.sort.direction&&!i.suppressRemoveSort(t)}},{title:function(){return e.getSafeText("column.hide")},icon:"ui-grid-icon-cancel",shown:function(){return i.hideable(t)},action:function(e){e.stopPropagation(),t.hideColumn()}}]},getColumnElementPosition:function(e,t,r){var i={};return i.left=r[0].offsetLeft,i.top=r[0].offsetTop,i.parentLeft=r[0].offsetParent.offsetLeft,i.offset=0,t.grid.options.offsetLeft&&(i.offset=t.grid.options.offsetLeft),i.height=g.elementHeight(r,!0),i.width=g.elementWidth(r,!0),i},repositionMenu:function(e,t,r,i,n){var o=i[0].querySelectorAll(".ui-grid-menu"),a=g.closestElm(n,".ui-grid-render-container"),l=a.getBoundingClientRect().left-e.grid.element[0].getBoundingClientRect().left,s=a.querySelectorAll(".ui-grid-viewport")[0].scrollLeft,d=g.elementWidth(o,!0),c=t.lastMenuPaddingRight?t.lastMenuPaddingRight:e.lastMenuPaddingRight?e.lastMenuPaddingRight:10;0!==o.length&&0!==o[0].querySelectorAll(".ui-grid-menu-mid").length&&(c=parseInt(g.getStyles(angular.element(o)[0]).paddingRight,10),e.lastMenuPaddingRight=c,t.lastMenuPaddingRight=c);var u=r.left+l-s+r.parentLeft+r.width+c;u<r.offset+d&&(u=Math.max(r.left-s+r.parentLeft-c+d,r.offset+d)),i.css("left",u+"px"),i.css("top",r.top+r.height+"px")}};return i}]).directive("uiGridColumnMenu",["$timeout","gridUtil","uiGridConstants","uiGridColumnMenuService","$document",function(r,a,l,s,d){return{priority:0,scope:!0,require:"^uiGrid",templateUrl:"ui-grid/uiGridColumnMenu",replace:!0,link:function(o,i,e,t){function n(e){o.col&&(o.col.menuShown=e)}s.initialize(o,t),o.defaultMenuItems=s.getDefaultMenuItems(o),o.menuItems=o.defaultMenuItems,s.setColMenuItemWatch(o),o.showMenu=function(e,t,r){n(!1),o.col=e,n(!0);var i=s.getColumnElementPosition(o,e,t);o.menuShown?(o.colElement=t,o.colElementPosition=i,o.hideThenShow=!0,o.$broadcast("hide-menu",{originalEvent:r})):(o.menuShown=!0,o.colElement=t,o.colElementPosition=i,o.$broadcast("show-menu",{originalEvent:r}))},o.hideMenu=function(e){n(o.menuShown=!1),e||o.$broadcast("hide-menu")},o.$on("menu-hidden",function(){var e=angular.element(i[0].querySelector(".ui-grid-menu-items"))[0];i[0].removeAttribute("style"),o.hideThenShow?(delete o.hideThenShow,o.$broadcast("show-menu"),o.menuShown=!0):(o.hideMenu(!0),o.col&&o.col.visible&&a.focus.bySelector(d,".ui-grid-header-cell."+o.col.getColClass()+" .ui-grid-column-menu-button",o.col.grid,!1).catch(angular.noop)),e&&(e.onkeydown=null,angular.forEach(e.children,function(e){e.onkeydown=null}))}),o.$on("menu-shown",function(){r(function(){s.repositionMenu(o,o.col,o.colElementPosition,i,o.colElement),o.menuItems.some(function(e){return e.shown()})&&a.focus.bySelector(d,".ui-grid-menu-items .ui-grid-menu-item:not(.ng-hide)",!0).catch(angular.noop),delete o.colElementPosition,delete o.columnElement,function(){var e,t=angular.element(i[0].querySelector(".ui-grid-menu-items"))[0],n=[];function r(e,t,r,i){e.keyCode===l.keymap.TAB&&(t?e.preventDefault():r&&(e.preventDefault(),n[i].focus()))}t&&(t.onkeydown=function(e){e.keyCode===l.keymap.ESC&&(e.preventDefault(),o.hideMenu())},e=t.querySelectorAll(".ui-grid-menu-item:not(.ng-hide)"),angular.forEach(e,function(e){null!==e.offsetParent&&this.push(e)},n),n.length&&(1===n.length?n[0].onkeydown=function(e){r(e,!0)}:(n[0].onkeydown=function(e){r(e,!1,e.shiftKey,n.length-1)},n[n.length-1].onkeydown=function(e){r(e,!1,!e.shiftKey,0)})))}()})}),o.sortColumn=function(e,t){e.stopPropagation(),o.grid.sortColumn(o.col,t,!0).then(function(){o.grid.refresh(),o.hideMenu()}).catch(angular.noop)},o.unsortColumn=function(){o.col.unsort(),o.grid.refresh(),o.hideMenu()},o.hideColumn=function(){o.col.colDef.visible=!1,o.col.visible=!1,o.grid.queueGridRefresh(),o.hideMenu(),o.grid.api.core.notifyDataChange(l.dataChange.COLUMN),o.grid.api.core.raise.columnVisibilityChanged(o.col),r(function(){var r,i,t=function(){return a.focus.byId("grid-menu",o.grid)};if(o.grid.columns.some(function(e,t){if(angular.equals(e,o.col))return r=t,!0}),o.grid.columns.some(function(e,t){if(!e.visible)return!1;if(t<r)i=e;else{if(r<t&&!i)return i=e,!0;if(r<t&&i)return!0}}),i){var e=i.getColClass();a.focus.bySelector(d,".ui-grid-header-cell."+e+" .ui-grid-header-cell-primary-focus",!0).then(angular.noop,function(e){if("canceled"!==e)return t()}).catch(angular.noop)}else t()})}},controller:["$scope",function(e){var t=this;e.$watch("menuItems",function(e){t.menuItems=e})}]}}]),function(){"use strict";angular.module("ui.grid").directive("uiGridFilter",["$compile","$templateCache","i18nService","gridUtil",function(n,e,t,i){return{compile:function(){return{pre:function(r,i){r.col.updateFilters=function(e){if(i.children().remove(),e){var t=r.col.filterHeaderTemplate;void 0===t&&""!==r.col.providedFilterHeaderTemplate?r.col.filterHeaderTemplatePromise&&r.col.filterHeaderTemplatePromise.then(function(){t=r.col.filterHeaderTemplate,i.append(n(t)(r))}):i.append(n(t)(r))}},r.$on("$destroy",function(){delete r.col.filterable,delete r.col.updateFilters})},post:function(e,r){e.aria=t.getSafeText("headerCell.aria"),e.removeFilter=function(e,t){e.term=null,i.focus.bySelector(r,".ui-grid-filter-input-"+t)}}}}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridFooterCell",["$timeout","gridUtil","uiGridConstants","$compile",function(e,t,s,i){return{priority:0,scope:{col:"=",row:"=",renderIndex:"="},replace:!0,require:"^uiGrid",compile:function(){return{pre:function(e,t){var r=e.col.footerCellTemplate;void 0===r&&""!==e.col.providedFooterCellTemplate?e.col.footerCellTemplatePromise&&e.col.footerCellTemplatePromise.then(function(){r=e.col.footerCellTemplate,t.append(i(r)(e))}):t.append(i(r)(e))},post:function(t,r,e,i){t.grid=i.grid;var n,o=t.col.getColClass(!1);r.addClass(o);var a=function(){var e=r;n&&(e.removeClass(n),n=null),n=angular.isFunction(t.col.footerCellClass)?t.col.footerCellClass(t.grid,t.row,t.col,t.rowRenderIndex,t.colRenderIndex):t.col.footerCellClass,e.addClass(n)};t.col.footerCellClass&&a(),t.col.updateAggregationValue();var l=t.grid.registerDataChangeCallback(a,[s.dataChange.COLUMN]);t.grid.api.core.on.rowsRendered(t,t.col.updateAggregationValue),t.grid.api.core.on.rowsRendered(t,a),t.$on("$destroy",l)}}}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridFooter",["$templateCache","$compile","uiGridConstants","gridUtil","$timeout",function(e,l,t,s,r){return{restrict:"EA",replace:!0,require:["^uiGrid","^uiGridRenderContainer"],scope:!0,compile:function(e,t){return{pre:function(n,o,e,t){var r=t[0],a=t[1];n.grid=r.grid,n.colContainer=a.colContainer,a.footer=o;var i=n.grid.options.footerTemplate;s.getTemplate(i).then(function(e){var t=angular.element(e),r=l(t)(n);if(o.append(r),a){var i=o[0].getElementsByClassName("ui-grid-footer-viewport")[0];i&&(a.footerViewport=i)}}).catch(angular.noop)},post:function(e,t,r,i){var n=i[0],o=i[1];n.grid;s.disableAnimations(t);var a=(o.footer=t)[0].getElementsByClassName("ui-grid-footer-viewport")[0];a&&(o.footerViewport=a)}}}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridGridFooter",["$templateCache","$compile","uiGridConstants","gridUtil",function(e,o,t,a){return{restrict:"EA",replace:!0,require:"^uiGrid",scope:!0,compile:function(){return{pre:function(i,n,e,t){i.grid=t.grid;var r=i.grid.options.gridFooterTemplate;a.getTemplate(r).then(function(e){var t=angular.element(e),r=o(t)(i);n.append(r)}).catch(angular.noop)}}}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridHeaderCell",["$compile","$timeout","$window","$document","gridUtil","uiGridConstants","ScrollEvent","i18nService","$rootScope",function(i,f,e,m,t,h,r,v,C){return{priority:0,scope:{col:"=",row:"=",renderIndex:"="},require:["^uiGrid","^uiGridRenderContainer"],replace:!0,compile:function(){return{pre:function(e,t){var r=e.col.headerCellTemplate;void 0===r&&""!==e.col.providedHeaderCellTemplate?e.col.headerCellTemplatePromise&&e.col.headerCellTemplatePromise.then(function(){r=e.col.headerCellTemplate,t.append(i(r)(e))}):t.append(i(r)(e))},post:function(r,i,e,t){var n=t[0],o=t[1];r.i18n={headerCell:v.getSafeText("headerCell"),sort:v.getSafeText("sort")},r.isSortPriorityVisible=function(){return r.col&&r.col.sort&&angular.isNumber(r.col.sort.priority)&&r.grid.columns.some(function(e,t){return angular.isNumber(e.sort.priority)&&e!==r.col})},r.getSortDirectionAriaLabel=function(){var e=r.col,t=e.sort&&e.sort.direction===h.ASC?r.i18n.sort.ascending:e.sort&&e.sort.direction===h.DESC?r.i18n.sort.descending:r.i18n.sort.none;return r.isSortPriorityVisible()&&(t=t+". "+r.i18n.headerCell.priority+" "+(e.sort.priority+1)),t},r.grid=n.grid,r.renderContainer=n.grid.renderContainers[o.containerId];var a=r.col.getColClass(!1);i.addClass(a),r.menuShown=!1,r.col.menuShown=!1,r.asc=h.ASC,r.desc=h.DESC;var l,s,d=angular.element(i[0].querySelectorAll(".ui-grid-cell-contents")),c=[];r.downFn=function(e){e.stopPropagation(),void 0!==e.originalEvent&&void 0!==e.originalEvent&&(e=e.originalEvent),e.button&&0!==e.button||(s=e.pageX,r.mousedownStartTime=(new Date).getTime(),r.mousedownTimeout=f(function(){},500),r.mousedownTimeout.then(function(){r.colMenu&&n.columnMenuScope.showMenu(r.col,i,e)}).catch(angular.noop),n.fireEvent(h.events.COLUMN_HEADER_CLICK,{event:e,columnName:r.col.colDef.name}),r.offAllEvents(),"touchstart"===e.type?(m.on("touchend",r.upFn),m.on("touchmove",r.moveFn)):"mousedown"===e.type&&(m.on("mouseup",r.upFn),m.on("mousemove",r.moveFn)))},r.upFn=function(e){e.stopPropagation(),f.cancel(r.mousedownTimeout),r.offAllEvents(),r.onDownEvents(e.type),500<(new Date).getTime()-r.mousedownStartTime||r.sortable&&r.handleClick(e)},r.handleKeyDown=function(e){32!==e.keyCode&&13!==e.keyCode||(e.preventDefault(),r.handleClick(e))},r.moveFn=function(e){0!==e.pageX-s&&(f.cancel(r.mousedownTimeout),r.offAllEvents(),r.onDownEvents(e.type))},r.clickFn=function(e){e.stopPropagation(),d.off("click",r.clickFn)},r.offAllEvents=function(){d.off("touchstart",r.downFn),d.off("mousedown",r.downFn),m.off("touchend",r.upFn),m.off("mouseup",r.upFn),m.off("touchmove",r.moveFn),m.off("mousemove",r.moveFn),d.off("click",r.clickFn)},r.onDownEvents=function(e){switch(e){case"touchmove":case"touchend":d.on("click",r.clickFn),d.on("touchstart",r.downFn),f(function(){d.on("mousedown",r.downFn)},500);break;case"mousemove":case"mouseup":d.on("click",r.clickFn),d.on("mousedown",r.downFn),f(function(){d.on("touchstart",r.downFn)},500);break;default:d.on("click",r.clickFn),d.on("touchstart",r.downFn),d.on("mousedown",r.downFn)}};var u=function(e){e&&(void 0!==r.col.updateFilters&&r.col.updateFilters(r.col.filterable),r.col.filterable?(r.col.filters.forEach(function(e,t){c.push(r.$watch("col.filters["+t+"].term",function(e,t){e!==t&&(n.grid.api.core.raise.filterChanged(r.col),n.grid.api.core.notifyDataChange(h.dataChange.COLUMN),n.grid.queueGridRefresh())}))}),r.$on("$destroy",function(){c.forEach(function(e){e()})})):c.forEach(function(e){e()}))},g=function(){var e=i;l&&(e.removeClass(l),l=null),l=angular.isFunction(r.col.headerCellClass)?r.col.headerCellClass(r.grid,r.row,r.col,r.rowRenderIndex,r.colRenderIndex):r.col.headerCellClass,e.addClass(l),r.$applyAsync(function(){var e=r.grid.renderContainers.right&&r.grid.renderContainers.right.visibleColumnCache.length?r.grid.renderContainers.right:r.grid.renderContainers.body;r.isLastCol=n.grid.options&&n.grid.options.enableGridMenu&&r.col===e.visibleColumnCache[e.visibleColumnCache.length-1]}),r.sortable=Boolean(r.col.enableSorting);var t=r.col.filterable;r.col.filterable=Boolean(n.grid.options.enableFiltering&&r.col.enableFiltering),u(t!==r.col.filterable),r.colMenu=r.col.grid.options&&!1!==r.col.grid.options.enableColumnMenus&&r.col.colDef&&!1!==r.col.colDef.enableColumnMenu,r.offAllEvents(),(r.sortable||r.colMenu)&&(r.onDownEvents(),r.$on("$destroy",function(){r.offAllEvents()}))};g(),"columnMenu"===r.col.filterContainer&&r.col.filterable&&C.$on("menu-shown",function(){r.$applyAsync(function(){u(r.col.filterable)})});var p=r.grid.registerDataChangeCallback(g,[h.dataChange.COLUMN]);r.$on("$destroy",p),r.handleClick=function(e){n.grid.sortColumn(r.col,e.shiftKey).then(function(){n.columnMenuScope&&n.columnMenuScope.hideMenu(),n.grid.refresh()}).catch(angular.noop)},r.headerCellArrowKeyDown=function(e){e.keyCode!==h.keymap.SPACE&&e.keyCode!==h.keymap.ENTER||(e.preventDefault(),r.toggleMenu(e))},r.toggleMenu=function(e){e.stopPropagation(),n.columnMenuScope.menuShown&&n.columnMenuScope.col===r.col?n.columnMenuScope.hideMenu():n.columnMenuScope.showMenu(r.col,i)}}}}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridHeader",["$templateCache","$compile","uiGridConstants","gridUtil","$timeout","ScrollEvent",function(e,d,t,c,r,u){return{restrict:"EA",replace:!0,require:["^uiGrid","^uiGridRenderContainer"],scope:!0,compile:function(){return{pre:function(n,o,e,t){var r,i=t[0],a=t[1];function l(){a.header=a.colContainer.header=o;var e=o[0].getElementsByClassName("ui-grid-header-canvas");0<e.length?a.headerCanvas=a.colContainer.headerCanvas=e[0]:a.headerCanvas=null}function s(){if(!i.grid.isScrollingHorizontally){var e=c.normalizeScrollLeft(a.headerViewport,i.grid),t=a.colContainer.scrollHorizontal(e),r=new u(i.grid,null,a.colContainer,u.Sources.ViewPortScroll);r.newScrollLeft=e,-1<t&&(r.x={percentage:t}),i.grid.scrollContainers(null,r)}}n.grid=i.grid,n.colContainer=a.colContainer,l(),r=n.grid.options.showHeader?n.grid.options.headerTemplate?n.grid.options.headerTemplate:"ui-grid/ui-grid-header":"ui-grid/ui-grid-no-header",c.getTemplate(r).then(function(e){var t=angular.element(e),r=d(t)(n);if(o.replaceWith(r),o=r,l(),a){var i=o[0].getElementsByClassName("ui-grid-header-viewport")[0];i&&(a.headerViewport=i,angular.element(i).on("scroll",s),n.$on("$destroy",function(){angular.element(i).off("scroll",s)}))}n.grid.queueRefresh()}).catch(angular.noop)},post:function(e,t,r,i){var n=i[0],o=i[1];n.grid;c.disableAnimations(t);var a=(o.header=t)[0].getElementsByClassName("ui-grid-header-viewport")[0];a&&(o.headerViewport=a),n&&n.grid.registerStyleComputation({priority:15,func:function(){var e=o.colContainer.visibleColumnCache,t="",r=0;return e.forEach(function(e){t+=e.getColClassDefinition(),r+=e.drawnWidth}),o.colContainer.canvasWidth=r,t}})}}}}}])}(),angular.module("ui.grid").service("uiGridGridMenuService",["gridUtil","i18nService","uiGridConstants",function(n,a,t){var l={initialize:function(e,t){(t.gridMenuScope=e).grid=t,e.registeredMenuItems=[],e.$on("$destroy",function(){e.grid&&e.grid.gridMenuScope&&(e.grid.gridMenuScope=null),e.grid&&(e.grid=null),e.registeredMenuItems&&(e.registeredMenuItems=null)}),e.registeredMenuItems=[],t.api.registerMethod("core","addToGridMenu",l.addToGridMenu),t.api.registerMethod("core","removeFromGridMenu",l.removeFromGridMenu)},addToGridMenu:function(e,t){angular.isArray(t)?e.gridMenuScope?(e.gridMenuScope.registeredMenuItems=e.gridMenuScope.registeredMenuItems?e.gridMenuScope.registeredMenuItems:[],e.gridMenuScope.registeredMenuItems=e.gridMenuScope.registeredMenuItems.concat(t)):n.logError("Asked to addToGridMenu, but gridMenuScope not present.  Timing issue?  Please log issue with ui-grid"):n.logError("addToGridMenu: menuItems must be an array, and is not, not adding any items")},removeFromGridMenu:function(e,r){var i=-1;e&&e.gridMenuScope&&e.gridMenuScope.registeredMenuItems.forEach(function(e,t){e.id===r&&(-1<i?n.logError("removeFromGridMenu: found multiple items with the same id, removing only the last"):i=t)}),-1<i&&e.gridMenuScope.registeredMenuItems.splice(i,1)},getMenuItems:function(t){var e=[];t.grid.options.gridMenuCustomItems&&(angular.isArray(t.grid.options.gridMenuCustomItems)?e=e.concat(t.grid.options.gridMenuCustomItems):n.logError("gridOptions.gridMenuCustomItems must be an array, and is not"));var r=[{title:a.getSafeText("gridMenu.clearAllFilters"),action:function(e){t.grid.clearAllFilters()},shown:function(){return t.grid.options.enableFiltering},order:100}];return e=(e=e.concat(r)).concat(t.registeredMenuItems),!1!==t.grid.options.gridMenuShowHideColumns&&(e=e.concat(l.showHideColumns(t))),e.sort(function(e,t){return e.order-t.order}),e},showHideColumns:function(i){var n=[];if(!i.grid.options.columnDefs||0===i.grid.options.columnDefs.length||0===i.grid.columns.length)return n;function o(e){return!0===(t=e).visible||void 0===t.visible?"ui-grid-icon-ok":"ui-grid-icon-cancel";var t}return i.grid.options.gridMenuTitleFilter=i.grid.options.gridMenuTitleFilter?i.grid.options.gridMenuTitleFilter:function(e){return e},i.grid.options.columnDefs.forEach(function(e,t){if(!1!==i.grid.options.enableHiding&&!1!==e.enableHiding||e.enableHiding){var r={icon:o(e),action:function(e){e.stopPropagation(),l.toggleColumnVisibility(this.context.gridCol),e.target&&e.target.firstChild&&("I"===angular.element(e.target)[0].nodeName?e.target.className=o(this.context.gridCol.colDef):e.target.firstChild.className=o(this.context.gridCol.colDef))},shown:function(){return!1!==this.context.gridCol.colDef.enableHiding},context:{gridCol:i.grid.getColumn(e.name||e.field)},leaveOpen:!0,order:301+t};l.setMenuItemTitle(r,e,i.grid),n.push(r)}}),n.length&&n.unshift({title:a.getSafeText("gridMenu.columns"),order:300,templateUrl:"ui-grid/ui-grid-menu-header-item"}),n},setMenuItemTitle:function(t,e,r){var i=r.options.gridMenuTitleFilter(e.displayName||n.readableColumnName(e.name)||e.field);"string"==typeof i?t.title=i:i.then?(t.title="",i.then(function(e){t.title=e},function(e){t.title=e}).catch(angular.noop)):(n.logError("Expected gridMenuTitleFilter to return a string or a promise, it has returned neither, bad config"),t.title="badconfig")},toggleColumnVisibility:function(e){e.colDef.visible=!(!0===e.colDef.visible||void 0===e.colDef.visible),e.grid.refresh(),e.grid.api.core.notifyDataChange(t.dataChange.COLUMN),e.grid.api.core.raise.columnVisibilityChanged(e)}};return l}]).directive("uiGridMenuButton",["gridUtil","uiGridConstants","uiGridGridMenuService","i18nService",function(o,a,l,s){return{priority:0,scope:!0,require:["^uiGrid"],templateUrl:"ui-grid/ui-grid-menu-button",replace:!0,link:function(t,e,r,i){var n=i[0];t.i18n={aria:s.getSafeText("gridMenu.aria")},l.initialize(t,n.grid),t.shown=!1,t.toggleOnKeydown=function(e){(e.keyCode===a.keymap.ENTER||e.keyCode===a.keymap.SPACE||e.keyCode===a.keymap.ESC&&t.shown)&&t.toggleMenu()},t.toggleMenu=function(){t.shown?(t.$broadcast("hide-menu"),t.shown=!1):(t.menuItems=l.getMenuItems(t),t.$broadcast("show-menu"),t.shown=!0)},t.$on("menu-hidden",function(){t.shown=!1,o.focus.bySelector(e,".ui-grid-icon-container")})}}}]),angular.module("ui.grid").directive("uiGridMenu",["$compile","$timeout","$window","$document","gridUtil","uiGridConstants","i18nService",function(l,s,d,e,c,u,g){return{priority:0,scope:{menuItems:"=",autoHide:"=?",col:"=?"},require:"?^uiGrid",templateUrl:"ui-grid/uiGridMenu",replace:!1,link:function(o,a,e,r){if(o.dynamicStyles="",r&&r.grid&&r.grid.options&&r.grid.options.gridMenuTemplate){var t=r.grid.options.gridMenuTemplate;c.getTemplate(t).then(function(e){var t=angular.element(e),r=l(t)(o);a.replaceWith(r)}).catch(angular.noop)}var n=function(e){var t=e-r.grid.headerHeight-20;o.dynamicStyles=[".grid"+r.grid.id+" .ui-grid-menu-mid {","max-height: "+t+"px;","}"].join(" ")};r&&(n(r.grid.gridHeight),r.grid.api.core.on.gridDimensionChanged(o,function(e,t,r,i){n(r)})),o.i18n={close:g.getSafeText("columnMenu.close")},o.showMenu=function(e,t){o.shown?o.shownMid||(o.shownMid=!0,o.$emit("menu-shown")):(o.shown=!0,s(function(){o.shownMid=!0,o.$emit("menu-shown")}));var r="click";t&&t.originalEvent&&t.originalEvent.type&&"touchstart"===t.originalEvent.type&&(r=t.originalEvent.type),angular.element(document).off("click touchstart",i),s(function(){angular.element(document).on(r,i)})},o.hideMenu=function(e){o.shown&&(o.shownMid=!1,s(function(){o.shownMid||(o.shown=!1,o.$emit("menu-hidden"))},40)),angular.element(document).off("click touchstart",i)},o.$on("hide-menu",function(e,t){o.hideMenu(e,t)}),o.$on("show-menu",function(e,t){o.showMenu(e,t)});var i=function(e){if(o.shown){if(o.col&&"columnMenu"===o.col.filterContainer){var t=document.querySelector(".ui-grid-column-menu").querySelector("[ui-grid-filter]");if(t&&t.contains(e.target))return!1}o.$apply(function(){o.hideMenu()})}};o.checkKeyDown=function(t){var e=function(e){e.focus(),t.preventDefault()};if(t.keyCode===u.keymap.ESC)o.hideMenu();else if(t.keyCode===u.keymap.TAB){var r,i,n=a[0].querySelectorAll("button:not(.ng-hide)");0<n.length&&(r=n[0],i=n[n.length-1],t.target.parentElement.id!==i.parentElement.id||t.shiftKey?t.target.parentElement.id===r.parentElement.id&&t.shiftKey&&e(i):e(r))}},void 0!==o.autoHide&&void 0!==o.autoHide||(o.autoHide=!0),o.autoHide&&angular.element(d).on("resize",i),o.$on("$destroy",function(){angular.element(d).off("resize",i),angular.element(document).off("click touchstart",i)}),r&&o.$on("$destroy",r.grid.api.core.on.scrollBegin(o,i)),o.$on("$destroy",o.$on(u.events.ITEM_DRAGGING,i))}}}]).directive("uiGridMenuItem",["gridUtil","$compile","i18nService",function(a,o,i){return{priority:0,scope:{name:"=",active:"=",action:"=",icon:"=",shown:"=",context:"=",templateUrl:"=",leaveOpen:"=",screenReaderOnly:"="},require:["?^uiGrid"],templateUrl:"ui-grid/uiGridMenuItem",replace:!1,compile:function(){return{pre:function(i,n){i.templateUrl&&a.getTemplate(i.templateUrl).then(function(e){var t=angular.element(e),r=o(t)(i);n.replaceWith(r)}).catch(angular.noop)},post:function(n,e,t,r){var o=r[0];void 0!==n.shown&&null!==n.shown||(n.shown=function(){return!0}),n.itemShown=function(){var e={};return n.context&&(e.context=n.context),void 0!==o&&o&&(e.grid=o.grid),n.shown.call(e)},n.itemAction=function(e,t){if(e.stopPropagation(),"function"==typeof n.action){var r={};if(n.context&&(r.context=n.context),void 0!==o&&o&&(r.grid=o.grid),n.action.call(r,e,t),n.leaveOpen){var i=e.target.parentElement;"I"===angular.element(e.target)[0].nodeName&&(i=i.parentElement),a.focus.bySelector(i,"button[type=button]",!0)}else n.$emit("hide-menu")}},n.label=function(){var e=n.name;return"function"==typeof n.name&&(e=n.name.call()),e},n.i18n=i.get()}}}}}]),function(){"use strict";var t=angular.module("ui.grid");angular.forEach([{tag:"Src",method:"attr"},{tag:"Text",method:"text"},{tag:"Href",method:"attr"},{tag:"Class",method:"addClass"},{tag:"Html",method:"html"},{tag:"Alt",method:"attr"},{tag:"Style",method:"css"},{tag:"Value",method:"attr"},{tag:"Id",method:"attr"},{tag:"Id",directiveName:"IdGrid",method:"attr",appendGridId:!0},{tag:"Title",method:"attr"},{tag:"Label",method:"attr",aria:!0},{tag:"Labelledby",method:"attr",aria:!0},{tag:"Labelledby",directiveName:"LabelledbyGrid",appendGridId:!0,method:"attr",aria:!0},{tag:"Describedby",method:"attr",aria:!0},{tag:"Describedby",directiveName:"DescribedbyGrid",appendGridId:!0,method:"attr",aria:!0}],function(d){var e="uiGridOneBind",c=(d.aria?e+"Aria":e)+(d.directiveName?d.directiveName:d.tag);t.directive(c,["gridUtil",function(s){return{restrict:"A",require:["?uiGrid","?^uiGrid"],link:function(n,o,e,a){var l=n.$watch(e[c],function(e){if(e){if(d.appendGridId){var t=null;angular.forEach(e.split(" "),function(e){t=(t?t+" ":"")+function(e){var t;if(n.grid)t=n.grid;else if(n.col&&n.col.grid)t=n.col.grid;else if(!a.some(function(e){if(e&&e.grid)return t=e.grid,!0}))throw s.logError("["+c+"] A valid grid could not be found to bind id. Are you using this directive within the correct scope? Trying to generate id: [gridID]-"+e),new Error("No valid grid could be found");t&&(new RegExp(t.id.toString()).test(e)||(e=t.id.toString()+"-"+e));return e}(e)}),e=t}switch(d.method){case"attr":d.aria?o[d.method]("aria-"+d.tag.toLowerCase(),e):o[d.method](d.tag.toLowerCase(),e);break;case"addClass":if(angular.isObject(e)&&!angular.isArray(e)){var r=[],i=!1;if(angular.forEach(e,function(e,t){null!=e&&(i=!0,e&&r.push(t))}),!i)return;e=r}if(!e)return;o.addClass(angular.isArray(e)?e.join(" "):e);break;default:o[d.method](e)}l()}},!0)}}}])})}(),function(){"use strict";var e=angular.module("ui.grid");e.directive("uiGridRenderContainer",["$timeout","$document","uiGridConstants","gridUtil","ScrollEvent",function(e,t,r,m,h){return{replace:!0,transclude:!0,templateUrl:"ui-grid/uiGridRenderContainer",require:["^uiGrid","uiGridRenderContainer"],scope:{containerId:"=",rowContainerName:"=",colContainerName:"=",bindScrollHorizontal:"=",bindScrollVertical:"=",enableVerticalScrollbar:"=",enableHorizontalScrollbar:"="},controller:"uiGridRenderContainer as RenderContainer",compile:function(){return{pre:function(e,t,r,i){var n,o,a=i[0],l=i[1],s=e.grid=a.grid;if(!e.rowContainerName)throw new Error("No row render container name specified");if(!e.colContainerName)throw new Error("No column render container name specified");if(!s.renderContainers[e.rowContainerName])throw new Error('Row render container "'+e.rowContainerName+'" is not registered.');if(!s.renderContainers[e.colContainerName])throw new Error('Column render container "'+e.colContainerName+'" is not registered.');n=e.rowContainer=s.renderContainers[e.rowContainerName],o=e.colContainer=s.renderContainers[e.colContainerName],l.containerId=e.containerId,l.rowContainer=n,l.colContainer=o},post:function(s,t,e,r){var d=r[0],a=r[1],c=d.grid,u=a.rowContainer,g=a.colContainer,l=null,p=null,f=c.renderContainers[s.containerId];t.addClass("ui-grid-render-container-"+s.containerId),m.on.mousewheel(t,function(e){var t=new h(c,u,g,h.Sources.RenderContainerMouseWheel);if(0!==e.deltaY){var r=-1*e.deltaY*e.deltaFactor;l=a.viewport[0].scrollTop,t.verticalScrollLength=u.getVerticalScrollLength();var i=(l+r)/t.verticalScrollLength;1<=i&&l<t.verticalScrollLength&&(a.viewport[0].scrollTop=t.verticalScrollLength),i<0?i=0:1<i&&(i=1),t.y={percentage:i,pixels:r}}if(0!==e.deltaX){var n=e.deltaX*e.deltaFactor;p=m.normalizeScrollLeft(a.viewport,c),t.horizontalScrollLength=g.getCanvasWidth()-g.getViewportWidth();var o=(p+n)/t.horizontalScrollLength;o<0?o=0:1<o&&(o=1),t.x={percentage:o,pixels:n}}0!==e.deltaY&&(t.atTop(l)||t.atBottom(l))||0!==e.deltaX&&(t.atLeft(p)||t.atRight(p))||(e.preventDefault(),e.stopPropagation(),t.fireThrottledScrollingEvent("",t))}),t.bind("$destroy",function(){t.unbind("keydown"),["touchstart","touchmove","touchend","keydown","wheel","mousewheel","DomMouseScroll","MozMousePixelScroll"].forEach(function(e){t.unbind(e)})}),d.grid.registerStyleComputation({priority:6,func:function(){var e,t,r="",i=g.canvasWidth,n=g.getViewportWidth(),o=u.getCanvasHeight(),a=u.getViewportHeight();if(g.needsHScrollbarPlaceholder()&&(a-=c.scrollbarHeight),e=t=g.getHeaderViewportWidth(),r+="\n .grid"+d.grid.id+" .ui-grid-render-container-"+s.containerId+" .ui-grid-canvas { width: "+i+"px; height: "+o+"px; }",r+="\n .grid"+d.grid.id+" .ui-grid-render-container-"+s.containerId+" .ui-grid-header-canvas { width: "+(i+c.scrollbarWidth)+"px; }",f.explicitHeaderCanvasHeight){var l=document.querySelector(".grid"+d.grid.id+" .ui-grid-render-container-body .ui-grid-header-canvas");l&&(f.explicitHeaderCanvasHeight=l.offsetHeight),r+="\n .grid"+d.grid.id+" .ui-grid-render-container-"+s.containerId+" .ui-grid-header-canvas { height: "+f.explicitHeaderCanvasHeight+"px; }"}else r+="\n .grid"+d.grid.id+" .ui-grid-render-container-"+s.containerId+" .ui-grid-header-canvas { height: inherit; }";return r+="\n .grid"+d.grid.id+" .ui-grid-render-container-"+s.containerId+" .ui-grid-viewport { width: "+n+"px; height: "+a+"px; }",r+="\n .grid"+d.grid.id+" .ui-grid-render-container-"+s.containerId+" .ui-grid-header-viewport { width: "+e+"px; }",r+="\n .grid"+d.grid.id+" .ui-grid-render-container-"+s.containerId+" .ui-grid-footer-canvas { width: "+(i+c.scrollbarWidth)+"px; }",r+="\n .grid"+d.grid.id+" .ui-grid-render-container-"+s.containerId+" .ui-grid-footer-viewport { width: "+t+"px; }"}})}}}}}]),e.controller("uiGridRenderContainer",["$scope","gridUtil",function(e,t){}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridRow",function(){return{replace:!0,require:["^uiGrid","^uiGridRenderContainer"],scope:{row:"=uiGridRow",rowRenderIndex:"="},compile:function(){return{pre:function(t,i,e,r){var n,o,a=r[0],l=r[1];function s(){t.row.getRowTemplateFn.then(function(e){var r=t.$new();e(r,function(e,t){n&&(n.remove(),o.$destroy()),i.empty().append(e),n=e,o=r})}).catch(angular.noop)}t.grid=a.grid,t.colContainer=l.colContainer,s(),t.$watch("row.getRowTemplateFn",function(e,t){e!==t&&s()})},post:function(e,t){e.row.element=t}}}}})}(),angular.module("ui.grid").directive("uiGridStyle",["gridUtil","$interpolate",function(e,i){return{link:function(e,r){var t=i(r.text(),!0);t&&e.$watch(t,function(e){for(var t=0;t<r.length;t++)r[t].textContent=e})}}}]),function(){"use strict";angular.module("ui.grid").directive("uiGridViewport",["gridUtil","ScrollEvent",function(c,u){return{replace:!0,scope:{},controllerAs:"Viewport",templateUrl:"ui-grid/uiGridViewport",require:["^uiGrid","^uiGridRenderContainer"],link:function(o,a,e,t){var r=t[0],i=t[1],l=(o.containerCtrl=i).rowContainer,s=i.colContainer,d=r.grid;o.grid=r.grid,o.rowContainer=i.rowContainer,o.colContainer=i.colContainer,i.viewport=a,d&&d.options&&d.options.customScroller?d.options.customScroller(a,n):a.on("scroll",n);function n(){var e=a[0].scrollTop,t=c.normalizeScrollLeft(a,d),r=l.scrollVertical(e),i=s.scrollHorizontal(t),n=new u(d,l,s,u.Sources.ViewPortScroll);n.newScrollLeft=t,n.newScrollTop=e,-1<i&&(n.x={percentage:i}),-1<r&&(n.y={percentage:r}),d.scrollContainers(o.$parent.containerId,n)}o.$parent.bindScrollVertical&&d.addVerticalScrollSync(o.$parent.containerId,function(e){i.prevScrollArgs=e,a[0].scrollTop=e.getNewScrollTop(l,i.viewport)}),o.$parent.bindScrollHorizontal&&(d.addHorizontalScrollSync(o.$parent.containerId,function(e){var t=(i.prevScrollArgs=e).getNewScrollLeft(s,i.viewport);a[0].scrollLeft=c.denormalizeScrollLeft(i.viewport,t,d)}),d.addHorizontalScrollSync(o.$parent.containerId+"header",function(e){var t=e.getNewScrollLeft(s,i.viewport);i.headerViewport&&(i.headerViewport.scrollLeft=c.denormalizeScrollLeft(i.viewport,t,d))}),d.addHorizontalScrollSync(o.$parent.containerId+"footer",function(e){var t=e.getNewScrollLeft(s,i.viewport);i.footerViewport&&(i.footerViewport.scrollLeft=c.denormalizeScrollLeft(i.viewport,t,d))})),o.$on("$destroy",function(){a.off()})},controller:["$scope",function(n){this.rowStyle=function(){var e=n.rowContainer,t=n.colContainer,r={};if(0!==e.currentTopRow){var i="translateY("+e.currentTopRow*e.grid.options.rowHeight+"px)";r.transform=i,r["-webkit-transform"]=i,r["-ms-transform"]=i}return 0!==t.currentFirstColumn&&(t.grid.isRTL()?r["margin-right"]=t.columnOffset+"px":r["margin-left"]=t.columnOffset+"px"),r}}]}}])}(),angular.module("ui.grid").directive("uiGridVisible",function(){return function(e,t,r){e.$watch(r.uiGridVisible,function(e){t[e?"removeClass":"addClass"]("ui-grid-invisible")})}}),function(){"use strict";function e(g,p,f){return{templateUrl:"ui-grid/ui-grid",scope:{uiGrid:"="},replace:!0,transclude:!0,controller:"uiGridController",compile:function(){return{post:function(l,s,e,t){var d=t.grid;t.scrollbars=[],d.element=s;var r,i,n=100,o=20,a=0;function c(){d.gridWidth=l.gridWidth=p.elementWidth(s),d.canvasWidth=t.grid.gridWidth,d.gridHeight=l.gridHeight=p.elementHeight(s),d.gridHeight-d.scrollbarHeight<=d.options.rowHeight&&d.options.enableMinHeightCheck&&function(){var e=d.options.minRowsToShow*d.options.rowHeight,t=d.options.showHeader?d.options.headerRowHeight:0,r=d.calcFooterHeight(),i=0;d.options.enableHorizontalScrollbar===f.scrollbars.ALWAYS&&(i=p.getScrollbarWidth());var n=0;if(angular.forEach(d.options.columnDefs,function(e){e.hasOwnProperty("filter")?n<1&&(n=1):e.hasOwnProperty("filters")&&n<e.filters.length&&(n=e.filters.length)}),d.options.enableFiltering&&!n){var o=d.options.columnDefs.length&&d.options.columnDefs.every(function(e){return!1===e.enableFiltering});o||(n=1)}var a=t+e+r+i+n*t;s.css("height",a+"px"),d.gridHeight=l.gridHeight=p.elementHeight(s)}(),d.refreshCanvas(!0)}function u(){p.isVisible(s)&&(d.gridWidth=l.gridWidth=p.elementWidth(s),d.gridHeight=l.gridHeight=p.elementHeight(s),d.refreshCanvas(!0))}angular.element(g).on("resize",u),s.on("$destroy",function(){angular.element(g).off("resize",u),r(),i()}),r=l.$watch(function(){return d.hasLeftContainer()},function(e,t){e!==t&&d.refreshCanvas(!0)}),i=l.$watch(function(){return d.hasRightContainer()},function(e,t){e!==t&&d.refreshCanvas(!0)}),c(),d.renderingComplete(),function e(){s[0].offsetWidth<=0&&a<o?(setTimeout(e,n),a++):l.$applyAsync(c)}()}}}}}angular.module("ui.grid").controller("uiGridController",["$scope","$element","$attrs","gridUtil","$q","uiGridConstants","gridClassFactory","$parse","$compile",function(i,e,n,t,o,a,r,l,s){var d,c=this,u=[];function g(e){return e?e.length:0}function p(e,t){e&&e!==t&&(c.grid.options.columnDefs=i.uiGrid.columnDefs,c.grid.callDataChangeCallbacks(a.dataChange.COLUMN,{orderByColumnDefs:!0,preCompileCellTemplates:!0}))}function f(e){var t=[];if(c.grid.options.fastWatch&&(e=angular.isString(i.uiGrid.data)?c.grid.appScope.$eval(i.uiGrid.data):i.uiGrid.data),d=e){var r=c.grid.columns.length>(c.grid.rowHeaderColumns?c.grid.rowHeaderColumns.length:0);!r&&!n.uiGridColumns&&0===c.grid.options.columnDefs.length&&0<e.length&&c.grid.buildColumnDefsFromData(e),!r&&(0<c.grid.options.columnDefs.length||0<e.length)&&t.push(c.grid.buildColumns().then(function(){c.grid.preCompileCellTemplates()}).catch(angular.noop)),o.all(t).then(function(){c.grid.modifyRows(d).then(function(){c.grid.redrawInPlace(!0),i.$evalAsync(function(){c.grid.refreshCanvas(!0),c.grid.callDataChangeCallbacks(a.dataChange.ROW)})}).catch(angular.noop)}).catch(angular.noop)}}c.grid=r.createGrid(i.uiGrid),c.grid.appScope=c.grid.appScope||i.$parent,e.addClass("grid"+c.grid.id),c.grid.rtl="rtl"===t.getStyles(e[0]).direction,i.grid=c.grid,n.uiGridColumns&&u.push(n.$observe("uiGridColumns",function(e){c.grid.options.columnDefs=angular.isString(e)?angular.fromJson(e):e,c.grid.buildColumns().then(function(){c.grid.preCompileCellTemplates(),c.grid.refreshCanvas(!0)}).catch(angular.noop)})),c.grid.options.fastWatch?(c.uiGrid=i.uiGrid,angular.isString(i.uiGrid.data)?(u.push(i.$parent.$watch(i.uiGrid.data,f)),u.push(i.$parent.$watch(function(){return c.grid.appScope[i.uiGrid.data]?c.grid.appScope[i.uiGrid.data].length:void 0},f))):(u.push(i.$parent.$watch(function(){return i.uiGrid.data},f)),u.push(i.$parent.$watch(function(){return g(i.uiGrid.data)},function(){f(i.uiGrid.data)}))),u.push(i.$parent.$watch(function(){return i.uiGrid.columnDefs},p)),u.push(i.$parent.$watch(function(){return g(i.uiGrid.columnDefs)},function(){p(i.uiGrid.columnDefs)}))):(angular.isString(i.uiGrid.data)?u.push(i.$parent.$watchCollection(i.uiGrid.data,f)):u.push(i.$parent.$watchCollection(function(){return i.uiGrid.data},f)),u.push(i.$parent.$watchCollection(function(){return i.uiGrid.columnDefs},p)));var m=i.$watch(function(){return c.grid.styleComputations},function(){c.grid.refreshCanvas(!0)});i.$on("$destroy",function(){u.forEach(function(e){e()}),m()}),c.fireEvent=function(e,t){t=t||{},angular.isUndefined(t.grid)&&(t.grid=c.grid),i.$broadcast(e,t)},c.innerCompile=function(e){s(e)(i)}}]),angular.module("ui.grid").directive("uiGrid",e),e.$inject=["$window","gridUtil","uiGridConstants"]}(),function(){"use strict";angular.module("ui.grid").directive("uiGridPinnedContainer",["gridUtil",function(e){return{restrict:"EA",replace:!0,template:'<div class="ui-grid-pinned-container"><div ui-grid-render-container container-id="side" row-container-name="\'body\'" col-container-name="side" bind-scroll-vertical="true" class="{{ side }} ui-grid-render-container-{{ side }}"></div></div>',scope:{side:"=uiGridPinnedContainer"},require:"^uiGrid",compile:function(){return{post:function(n,t,e,r){var o=r.grid,i=0;function a(){if("left"===n.side||"right"===n.side){for(var e=o.renderContainers[n.side].visibleColumnCache,t=0,r=0;r<e.length;r++){var i=e[r];t+=i.drawnWidth||i.width||0}return t}}t.addClass("ui-grid-pinned-container-"+n.side),"left"!==n.side&&"right"!==n.side||(o.renderContainers[n.side].getViewportWidth=function(){var t=0;this.visibleColumnCache.forEach(function(e){t+=e.drawnWidth});var e=this.getViewportAdjustment();return t+=e.width}),o.renderContainers.body.registerViewportAdjuster(function(e){return i=a(),e.width-=i,e.side=n.side,e}),o.registerStyleComputation({priority:15,func:function(){var e="";return"left"!==n.side&&"right"!==n.side||(i=a(),t.attr("style",null),e+=".grid"+o.id+" .ui-grid-pinned-container-"+n.side+", .grid"+o.id+" .ui-grid-pinned-container-"+n.side+" .ui-grid-render-container-"+n.side+" .ui-grid-viewport { width: "+i+"px; } "),e}})}}}}}])}(),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("en",{headerCell:{aria:{defaultFilterLabel:"Filter for column",removeFilter:"Remove Filter",columnMenuButtonLabel:"Column Menu",column:"Column"},priority:"Priority:",filterLabel:"Filter for column: "},aggregate:{label:"items"},groupPanel:{description:"Drag a column header here and drop it to group by that column."},search:{aria:{selected:"Row selected",notSelected:"Row not selected"},placeholder:"Search...",showingItems:"Showing Items:",selectedItems:"Selected Items:",totalItems:"Total Items:",size:"Page Size:",first:"First Page",next:"Next Page",previous:"Previous Page",last:"Last Page"},selection:{aria:{row:"Row"},selectAll:"Select All",displayName:"Row Selection Checkbox"},menu:{text:"Choose Columns:"},sort:{ascending:"Sort Ascending",descending:"Sort Descending",none:"Sort None",remove:"Remove Sort"},column:{hide:"Hide Column"},aggregation:{count:"total rows: ",sum:"total: ",avg:"avg: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Pin Left",pinRight:"Pin Right",unpin:"Unpin"},columnMenu:{close:"Close"},gridMenu:{aria:{buttonLabel:"Grid Menu"},columns:"Columns:",importerTitle:"Import file",exporterAllAsCsv:"Export all data as csv",exporterVisibleAsCsv:"Export visible data as csv",exporterSelectedAsCsv:"Export selected data as csv",exporterAllAsPdf:"Export all data as pdf",exporterVisibleAsPdf:"Export visible data as pdf",exporterSelectedAsPdf:"Export selected data as pdf",exporterAllAsExcel:"Export all data as excel",exporterVisibleAsExcel:"Export visible data as excel",exporterSelectedAsExcel:"Export selected data as excel",clearAllFilters:"Clear all filters"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."},pagination:{aria:{pageToFirst:"Page to first",pageBack:"Page back",pageSelected:"Selected page",pageForward:"Page forward",pageToLast:"Page to last"},sizes:"items per page",totalItems:"items",through:"through",of:"of"},grouping:{group:"Group",ungroup:"Ungroup",aggregate_count:"Agg: Count",aggregate_sum:"Agg: Sum",aggregate_max:"Agg: Max",aggregate_min:"Agg: Min",aggregate_avg:"Agg: Avg",aggregate_remove:"Agg: Remove"},validate:{error:"Error:",minLength:"Value should be at least THRESHOLD characters long.",maxLength:"Value should be at most THRESHOLD characters long.",required:"A value is needed."}}),e}])}]),angular.module("ui.grid").factory("Grid",["$q","$compile","$parse","gridUtil","uiGridConstants","GridOptions","GridColumn","GridRow","GridApi","rowSorter","rowSearcher","GridRenderContainer","$timeout","ScrollEvent",function(S,r,o,m,s,d,c,u,g,p,t,f,i,x){var e=function(e){var r=this;if(void 0===e||void 0===e.id||!e.id)throw new Error("No ID provided. An ID must be given when creating a grid.");if(!/^[_a-zA-Z0-9-]+$/.test(e.id))throw new Error("Grid id '"+e.id+'" is invalid. It must follow CSS selector syntax rules.');function t(e){r.isScrollingVertically=!1,r.api.core.raise.scrollEnd(e),r.scrollDirection=s.scrollDirection.NONE}r.id=e.id,delete e.id,r.options=d.initialize(e),r.appScope=r.options.appScopeProvider,r.headerHeight=r.options.headerRowHeight,r.footerHeight=r.calcFooterHeight(),r.columnFooterHeight=r.calcColumnFooterHeight(),r.rtl=!1,r.gridHeight=0,r.gridWidth=0,r.columnBuilders=[],r.rowBuilders=[],r.rowsProcessors=[],r.columnsProcessors=[],r.styleComputations=[],r.viewportAdjusters=[],r.rowHeaderColumns=[],r.dataChangeCallbacks={},r.verticalScrollSyncCallBackFns={},r.horizontalScrollSyncCallBackFns={},r.renderContainers={},r.renderContainers.body=new f("body",r),r.cellValueGetterCache={},r.getRowTemplateFn=null,r.rows=[],r.columns=[],r.isScrollingVertically=!1,r.isScrollingHorizontally=!1,r.scrollDirection=s.scrollDirection.NONE,r.disableScrolling=!1;var i=m.debounce(t,r.options.scrollDebounce),n=m.debounce(t,0);function o(e){r.isScrollingHorizontally=!1,r.api.core.raise.scrollEnd(e),r.scrollDirection=s.scrollDirection.NONE}var a=m.debounce(o,r.options.scrollDebounce),l=m.debounce(o,0);r.flagScrollingVertically=function(e){r.isScrollingVertically||r.isScrollingHorizontally||r.api.core.raise.scrollBegin(e),r.isScrollingVertically=!0,0!==r.options.scrollDebounce&&e.withDelay?i(e):n(e)},r.flagScrollingHorizontally=function(e){r.isScrollingVertically||r.isScrollingHorizontally||r.api.core.raise.scrollBegin(e),r.isScrollingHorizontally=!0,0!==r.options.scrollDebounce&&e.withDelay?a(e):l(e)},r.scrollbarHeight=0,r.scrollbarWidth=0,r.options.enableHorizontalScrollbar!==s.scrollbars.NEVER&&(r.scrollbarHeight=m.getScrollbarWidth()),r.options.enableVerticalScrollbar!==s.scrollbars.NEVER&&(r.scrollbarWidth=m.getScrollbarWidth()),r.api=new g(r),r.api.registerMethod("core","refresh",this.refresh),r.api.registerMethod("core","queueGridRefresh",this.queueGridRefresh),r.api.registerMethod("core","refreshRows",this.refreshRows),r.api.registerMethod("core","queueRefresh",this.queueRefresh),r.api.registerMethod("core","handleWindowResize",this.handleWindowResize),r.api.registerMethod("core","addRowHeaderColumn",this.addRowHeaderColumn),r.api.registerMethod("core","scrollToIfNecessary",function(e,t){return r.scrollToIfNecessary(e,t)}),r.api.registerMethod("core","scrollTo",function(e,t){return r.scrollTo(e,t)}),r.api.registerMethod("core","registerRowsProcessor",this.registerRowsProcessor),r.api.registerMethod("core","registerColumnsProcessor",this.registerColumnsProcessor),r.api.registerMethod("core","sortHandleNulls",p.handleNulls),r.api.registerEvent("core","sortChanged"),r.api.registerEvent("core","columnVisibilityChanged"),r.api.registerMethod("core","notifyDataChange",this.notifyDataChange),r.api.registerMethod("core","clearAllFilters",this.clearAllFilters),r.registerDataChangeCallback(r.columnRefreshCallback,[s.dataChange.COLUMN]),r.registerDataChangeCallback(r.processRowsCallback,[s.dataChange.EDIT]),r.registerDataChangeCallback(r.updateFooterHeightCallback,[s.dataChange.OPTIONS]),r.registerStyleComputation({priority:10,func:r.getFooterStyles})};e.prototype.calcFooterHeight=function(){if(!this.hasFooter())return 0;var e=0;return this.options.showGridFooter&&(e+=this.options.gridFooterHeight),e+=this.calcColumnFooterHeight()},e.prototype.calcColumnFooterHeight=function(){var e=0;return this.options.showColumnFooter&&(e+=this.options.columnFooterHeight),e},e.prototype.getFooterStyles=function(){var e=".grid"+this.id+" .ui-grid-footer-aggregates-row { height: "+this.options.columnFooterHeight+"px; }";return e+=" .grid"+this.id+" .ui-grid-footer-info { height: "+this.options.gridFooterHeight+"px; }"},e.prototype.hasFooter=function(){return this.options.showGridFooter||this.options.showColumnFooter},e.prototype.isRTL=function(){return this.rtl},e.prototype.registerColumnBuilder=function(e){this.columnBuilders.push(e)},e.prototype.buildColumnDefsFromData=function(e){this.options.columnDefs=m.getColumnsFromData(e,this.options.excludeProperties)},e.prototype.registerRowBuilder=function(e){this.rowBuilders.push(e)},e.prototype.registerDataChangeCallback=function(e,t,r){var i=this,n=m.nextUid();return t||(t=[s.dataChange.ALL]),Array.isArray(t)||m.logError("Expected types to be an array or null in registerDataChangeCallback, value passed was: "+t),this.dataChangeCallbacks[n]={callback:e,types:t,_this:r},function(){delete i.dataChangeCallbacks[n]}},e.prototype.callDataChangeCallbacks=function(r,i){angular.forEach(this.dataChangeCallbacks,function(e,t){-1===e.types.indexOf(s.dataChange.ALL)&&-1===e.types.indexOf(r)&&r!==s.dataChange.ALL||(e._this?e.callback.apply(e._this,this,i):e.callback(this,i))},this)},e.prototype.notifyDataChange=function(e){var t=s.dataChange;e===t.ALL||e===t.COLUMN||e===t.EDIT||e===t.ROW||e===t.OPTIONS?this.callDataChangeCallbacks(e):m.logError("Notified of a data change, but the type was not recognised, so no action taken, type was: "+e)},e.prototype.columnRefreshCallback=function(e,t){e.buildColumns(t),e.queueGridRefresh()},e.prototype.processRowsCallback=function(e){e.queueGridRefresh()},e.prototype.updateFooterHeightCallback=function(e){e.footerHeight=e.calcFooterHeight(),e.columnFooterHeight=e.calcColumnFooterHeight()},e.prototype.getColumn=function(t){return n(this.columns,function(e){return e.colDef.name===t})},e.prototype.getColDef=function(t){return n(this.options.columnDefs,function(e){return e.name===t})},e.prototype.assignTypes=function(){var n=this;n.options.columnDefs.forEach(function(e,t){if(!e.type){var r=new c(e,t,n),i=0<n.rows.length?n.rows[0]:null;e.type=i?m.guessType(n.getCellValue(i,r)):"string"}})},e.prototype.isRowHeaderColumn=function(e){return-1!==this.rowHeaderColumns.indexOf(e)},e.prototype.addRowHeaderColumn=function(e,t,r){var i=this;void 0===t&&(t=0);var n=new c(e,m.nextUid(),i);n.isRowHeader=!0,i.isRTL()?(i.createRightContainer(),n.renderContainer="right"):(i.createLeftContainer(),n.renderContainer="left"),i.columnBuilders[0](e,n,i.options).then(function(){n.enableFiltering=!1,n.enableSorting=!1,n.enableHiding=!1,n.headerPriority=t,i.rowHeaderColumns.push(n),i.rowHeaderColumns=i.rowHeaderColumns.sort(function(e,t){return e.headerPriority-t.headerPriority}),r||i.buildColumns().then(function(){i.preCompileCellTemplates(),i.queueGridRefresh()}).catch(angular.noop)}).catch(angular.noop)},e.prototype.getOnlyDataColumns=function(){var t=this,r=[];return t.columns.forEach(function(e){-1===t.rowHeaderColumns.indexOf(e)&&r.push(e)}),r},e.prototype.buildColumns=function(e){var t={orderByColumnDefs:!1};angular.extend(t,e);var r,i=this,n=[],o=i.rowHeaderColumns.length;for(r=0;r<i.columns.length;r++)i.getColDef(i.columns[r].name)||(i.columns.splice(r,1),r--);for(var a=i.rowHeaderColumns.length-1;0<=a;a--)i.columns.unshift(i.rowHeaderColumns[a]);if(i.options.columnDefs.forEach(function(t,e){i.preprocessColDef(t);var r=i.getColumn(t.name);r?r.updateColumnDef(t,!1):(r=new c(t,m.nextUid(),i),i.columns.splice(e+o,0,r)),i.columnBuilders.forEach(function(e){n.push(e.call(i,t,r,i.options))})}),t.orderByColumnDefs){var l=i.columns.slice(0),s=Math.min(i.options.columnDefs.length,i.columns.length);for(r=0;r<s;r++)i.columns[r+o].name!==i.options.columnDefs[r].name?l[r+o]=i.getColumn(i.options.columnDefs[r].name):l[r+o]=i.columns[r+o];i.columns.length=0,Array.prototype.splice.apply(i.columns,[0,0].concat(l))}return S.all(n).then(function(){0<i.rows.length&&i.assignTypes(),t.preCompileCellTemplates&&i.preCompileCellTemplates()}).catch(angular.noop)},e.prototype.preCompileCellTemplate=function(e){var t=e.cellTemplate.replace(s.MODEL_COL_FIELD,this.getQualifiedColField(e));t=t.replace(s.COL_FIELD,"grid.getCellValue(row, col)"),e.compiledElementFn=r(t),e.compiledElementFnDefer&&e.compiledElementFnDefer.resolve(e.compiledElementFn)},e.prototype.preCompileCellTemplates=function(){var t=this;t.columns.forEach(function(e){e.cellTemplate?t.preCompileCellTemplate(e):e.cellTemplatePromise&&e.cellTemplatePromise.then(function(){t.preCompileCellTemplate(e)}).catch(angular.noop)})},e.prototype.getQualifiedColField=function(e){var t="row.entity";return e.field===s.ENTITY_BINDING?t:m.preEval(t+"."+e.field)},e.prototype.createLeftContainer=function(){this.hasLeftContainer()||(this.renderContainers.left=new f("left",this,{disableColumnOffset:!0}))},e.prototype.createRightContainer=function(){this.hasRightContainer()||(this.renderContainers.right=new f("right",this,{disableColumnOffset:!0}))},e.prototype.hasLeftContainer=function(){return void 0!==this.renderContainers.left},e.prototype.hasRightContainer=function(){return void 0!==this.renderContainers.right},e.prototype.preprocessColDef=function(e){if(!e.field&&!e.name)throw new Error("colDef.name or colDef.field property is required");if(void 0===e.name&&void 0!==e.field){for(var t=e.field,r=2;this.getColumn(t);)t=e.field+r.toString(),r++;e.name=t}},e.prototype.newInN=function(e,t,r,i){for(var n=[],o=0;o<t.length;o++){for(var a=i?t[o][i]:t[o],l=!1,s=0;s<e.length;s++){var d=r?e[s][r]:e[s];if(this.options.rowEquality(a,d)){l=!0;break}}l||n.push(a)}return n};var n=function(e,t){if(e&&e.length){if(angular.isFunction(e.find))return e.find(t)||null;var r=null;return e.every(function(e){return!t(e)||(r=e,!1)}),r}return null};e.prototype.getRow=function(t,e){var r=this;return e=null==e?this.rows:e,n(e,function(e){return r.options.rowEquality(e.entity,t)})},e.prototype.getRowsByKey=function(e,t,r,i){if(null==t)return null;var n=e?function(e){return null!=e.entity&&e.entity.hasOwnProperty(t)&&e.entity[t]===r}:function(e){return e.hasOwnProperty(t)&&e[t]===r};return(i=null==i?this.rows:i).filter(n)},e.prototype.findRowByKey=function(e,t,r,i){var n=null;if(null!=t){var o=e?function(e){return null==e.entity||!e.entity.hasOwnProperty(t)||e.entity[t]!==r||(n=e,!1)}:function(e){return!e.hasOwnProperty(t)||e[t]!==r||(n=e,!1)};(i=null==i?this.rows:i).every(o)}return n},e.prototype.modifyRows=function(e){var n=this,o=n.rows.slice(0),a=n.rowHashMap||n.createRowHashMap(),l=!0;n.rowHashMap=n.createRowHashMap(),n.rows.length=0,e.forEach(function(e,t){var r,i;(i=n.options.enableRowHashing?a.get(e):n.getRow(e,o))&&((r=i).entity=e),r||(r=n.processRowBuilders(new u(e,t,n))),n.rows.push(r),n.rowHashMap.put(e,r),r.isSelected||(l=!1)}),n.selection&&n.rows.length&&(n.selection.selectAll=l),n.assignTypes();var t=S.when(n.processRowsProcessors(n.rows)).then(function(e){return n.setVisibleRows(e)}).catch(angular.noop),r=S.when(n.processColumnsProcessors(n.columns)).then(function(e){return n.setVisibleColumns(e)}).catch(angular.noop);return S.all([t,r])},e.prototype.addRows=function(e){for(var t=this,r=t.rows.length,i=0;i<e.length;i++){var n=t.processRowBuilders(new u(e[i],i+r,t));if(t.options.enableRowHashing){var o=t.rowHashMap.get(n.entity);o&&(o.row=n)}t.rows.push(n)}},e.prototype.processRowBuilders=function(t){var r=this;return r.rowBuilders.forEach(function(e){e.call(r,t,r.options)}),t},e.prototype.registerStyleComputation=function(e){this.styleComputations.push(e)},e.prototype.registerRowsProcessor=function(e,t){if(!angular.isFunction(e))throw"Attempt to register non-function rows processor: "+e;this.rowsProcessors.push({processor:e,priority:t}),this.rowsProcessors.sort(function(e,t){return e.priority-t.priority})},e.prototype.removeRowsProcessor=function(r){var i=-1;this.rowsProcessors.forEach(function(e,t){e.processor===r&&(i=t)}),-1!==i&&this.rowsProcessors.splice(i,1)},e.prototype.processRowsProcessors=function(e){var n=this,t=e.slice(0);if(0===n.rowsProcessors.length)return S.when(t);var o=S.defer();return function t(r,e){var i=n.rowsProcessors[r].processor;return S.when(i.call(n,e,n.columns)).then(function(e){if(!e)throw"Processor at index "+r+" did not return a set of renderable rows";if(!angular.isArray(e))throw"Processor at index "+r+" did not return an array";if(++r<=n.rowsProcessors.length-1)return t(r,e);o.resolve(e)}).catch(function(e){throw e})}(0,t),o.promise},e.prototype.setVisibleRows=function(e){var t=this;for(var r in t.renderContainers){var i=t.renderContainers[r];i.canvasHeightShouldUpdate=!0,void 0===i.visibleRowCache?i.visibleRowCache=[]:i.visibleRowCache.length=0}for(var n=0;n<e.length;n++){var o=e[n],a=void 0!==o.renderContainer&&o.renderContainer?o.renderContainer:"body";o.visible&&t.renderContainers[a].visibleRowCache.push(o)}t.api.core.raise.rowsVisibleChanged(this.api),t.api.core.raise.rowsRendered(this.api)},e.prototype.registerColumnsProcessor=function(e,t){if(!angular.isFunction(e))throw"Attempt to register non-function rows processor: "+e;this.columnsProcessors.push({processor:e,priority:t}),this.columnsProcessors.sort(function(e,t){return e.priority-t.priority})},e.prototype.removeColumnsProcessor=function(e){var t=this.columnsProcessors.indexOf(e);void 0!==t&&void 0!==t&&this.columnsProcessors.splice(t,1)},e.prototype.processColumnsProcessors=function(e){var n=this,o=e.slice(0);if(0===n.columnsProcessors.length)return S.when(o);var a=S.defer();return function t(r,e){var i=n.columnsProcessors[r].processor;return S.when(i.call(n,e,n.rows)).then(function(e){if(!e)throw"Processor at index "+r+" did not return a set of renderable rows";if(!angular.isArray(e))throw"Processor at index "+r+" did not return an array";if(++r<=n.columnsProcessors.length-1)return t(r,o);a.resolve(o)}).catch(angular.noop)}(0,o),a.promise},e.prototype.setVisibleColumns=function(e){for(var t in this.renderContainers)this.renderContainers[t].visibleColumnCache.length=0;for(var r=0;r<e.length;r++){var i=e[r];i.visible&&(void 0!==i.renderContainer&&i.renderContainer?this.renderContainers[i.renderContainer].visibleColumnCache.push(i):this.renderContainers.body.visibleColumnCache.push(i))}},e.prototype.handleWindowResize=function(e){var t=this;return t.gridWidth=m.elementWidth(t.element),t.gridHeight=m.elementHeight(t.element),t.queueRefresh()},e.prototype.queueRefresh=function(){var e=this;return e.refreshCanceller&&i.cancel(e.refreshCanceller),e.refreshCanceller=i(function(){e.refreshCanvas(!0)}),e.refreshCanceller.then(function(){e.refreshCanceller=null}).catch(angular.noop),e.refreshCanceller},e.prototype.queueGridRefresh=function(){var e=this;return e.gridRefreshCanceller&&i.cancel(e.gridRefreshCanceller),e.gridRefreshCanceller=i(function(){e.refresh(!0)}),e.gridRefreshCanceller.then(function(){e.gridRefreshCanceller=null}).catch(angular.noop),e.gridRefreshCanceller},e.prototype.updateCanvasHeight=function(){for(var e in this.renderContainers)this.renderContainers.hasOwnProperty(e)&&(this.renderContainers[e].canvasHeightShouldUpdate=!0)},e.prototype.buildStyles=function(){var r=this;r.customStyles="",r.styleComputations.sort(function(e,t){return null===e.priority?1:null===t.priority?-1:null===e.priority&&null===t.priority?0:e.priority-t.priority}).forEach(function(e){var t=e.func.call(r);angular.isString(t)&&(r.customStyles+="\n"+t)})},e.prototype.minColumnsToRender=function(){var n=this,o=this.getViewportWidth(),a=0,l=0;return n.columns.forEach(function(e,t){if(l<o)l+=e.drawnWidth,a++;else{for(var r=0,i=t;t-a<=i;i--)r+=n.columns[i].drawnWidth;r<o&&a++}}),a},e.prototype.getBodyHeight=function(){return this.getViewportHeight()},e.prototype.getViewportHeight=function(){var e=this.gridHeight-this.headerHeight-this.footerHeight;return 0<(e+=this.getViewportAdjustment().height)?e:0},e.prototype.getViewportWidth=function(){var e=this.gridWidth;return e+=this.getViewportAdjustment().width},e.prototype.getHeaderViewportWidth=function(){return this.getViewportWidth()},e.prototype.addVerticalScrollSync=function(e,t){this.verticalScrollSyncCallBackFns[e]=t},e.prototype.addHorizontalScrollSync=function(e,t){this.horizontalScrollSyncCallBackFns[e]=t},e.prototype.scrollContainers=function(e,t){if(t.y){var r=["body","left","right"];this.flagScrollingVertically(t),"body"===e?r=["left","right"]:"left"===e?r=["body","right"]:"right"===e&&(r=["body","left"]);for(var i=0;i<r.length;i++){var n=r[i];this.verticalScrollSyncCallBackFns[n]&&this.verticalScrollSyncCallBackFns[n](t)}}if(t.x){var o=["body","bodyheader","bodyfooter"];this.flagScrollingHorizontally(t),"body"===e&&(o=["bodyheader","bodyfooter"]);for(var a=0;a<o.length;a++){var l=o[a];this.horizontalScrollSyncCallBackFns[l]&&this.horizontalScrollSyncCallBackFns[l](t)}}},e.prototype.registerViewportAdjuster=function(e){this.viewportAdjusters.push(e)},e.prototype.removeViewportAdjuster=function(e){var t=this.viewportAdjusters.indexOf(e);void 0!==t&&void 0!==t&&this.viewportAdjusters.splice(t,1)},e.prototype.getViewportAdjustment=function(){var t={height:0,width:0};return this.viewportAdjusters.forEach(function(e){t=e.call(this,t)}),t},e.prototype.getVisibleRowCount=function(){return this.renderContainers.body.visibleRowCache.length},e.prototype.getVisibleRows=function(){return this.renderContainers.body.visibleRowCache},e.prototype.getVisibleColumnCount=function(){return this.renderContainers.body.visibleColumnCache.length},e.prototype.searchRows=function(e){return t.search(this,e,this.columns)},e.prototype.sortByColumn=function(e){return p.sort(this,e,this.columns)},e.prototype.getCellValue=function(e,t){return void 0!==e.entity["$$"+t.uid]?e.entity["$$"+t.uid].rendered:this.options.flatEntityAccess&&void 0!==t.field?e.entity[t.field]:(t.cellValueGetterCache||(t.cellValueGetterCache=o(e.getEntityQualifiedColField(t))),t.cellValueGetterCache(e))},e.prototype.getCellDisplayValue=function(e,t){if(!t.cellDisplayGetterCache){var r=t.cellFilter?" | "+t.cellFilter:"";if(void 0!==e.entity["$$"+t.uid])t.cellDisplayGetterCache=o(e.entity["$$"+t.uid].rendered+r);else if(this.options.flatEntityAccess&&void 0!==t.field){var i=t.field.replace(/(')|(\\)/g,"\\$&");t.cellDisplayGetterCache=o("entity['"+i+"']"+r)}else t.cellDisplayGetterCache=o(e.getEntityQualifiedColField(t)+r)}var n=angular.extend({},e,{col:t});return t.cellDisplayGetterCache(n)},e.prototype.getNextColumnSortPriority=function(){var t=0;return this.columns.forEach(function(e){e.sort&&void 0!==e.sort.priority&&e.sort.priority>=t&&(t=e.sort.priority+1)}),t},e.prototype.resetColumnSorting=function(t){this.columns.forEach(function(e){e===t||e.suppressRemoveSort||(e.sort={})})},e.prototype.getColumnSorting=function(){var t=[];return this.columns.slice(0).sort(p.prioritySort).forEach(function(e){e.sort&&void 0!==e.sort.direction&&e.sort.direction&&(e.sort.direction===s.ASC||e.sort.direction===s.DESC)&&t.push(e)}),t},e.prototype.sortColumn=function(e,t,r){var i=this,n=null;if(void 0===e||!e)throw new Error("No column parameter provided");if("boolean"==typeof t?r=t:n=t,!r||i.options&&i.options.suppressMultiSort?(i.resetColumnSorting(e),e.sort.priority=void 0,e.sort.priority=i.getNextColumnSortPriority()):void 0===e.sort.priority&&(e.sort.priority=i.getNextColumnSortPriority()),n)e.sort.direction=n;else{var o=e.sortDirectionCycle.indexOf(e.sort&&e.sort.direction?e.sort.direction:null);o=(o+1)%e.sortDirectionCycle.length,e.colDef&&e.suppressRemoveSort&&!e.sortDirectionCycle[o]&&(o=(o+1)%e.sortDirectionCycle.length),e.sortDirectionCycle[o]?e.sort.direction=e.sortDirectionCycle[o]:a(e,i)}return i.api.core.raise.sortChanged(i,i.getColumnSorting()),S.when(e)};var a=function(t,e){e.columns.forEach(function(e){e.sort&&void 0!==e.sort.priority&&e.sort.priority>t.sort.priority&&(e.sort.priority-=1)}),t.sort={}};function l(e,t){return e||0<t?t:null}function h(e,t){var r=e/t;return r<=1?r:1}function E(e,t,r){if(h(e,t)!==r)return{percentage:h(e,t)}}function A(e,t,r){var i=e/t;if((i=1<i?1:i)!==r)return{percentage:i}}function v(){}return e.prototype.renderingComplete=function(){angular.isFunction(this.options.onRegisterApi)&&this.options.onRegisterApi(this.api),this.api.core.raise.renderingComplete(this.api)},e.prototype.createRowHashMap=function(){var e=new v;return e.grid=this,e},e.prototype.refresh=function(e){var t=this,r=t.processRowsProcessors(t.rows).then(function(e){t.setVisibleRows(e)}).catch(angular.noop),i=t.processColumnsProcessors(t.columns).then(function(e){t.setVisibleColumns(e)}).catch(angular.noop);return S.all([r,i]).then(function(){t.refreshCanvas(!0),t.redrawInPlace(e)}).catch(angular.noop)},e.prototype.refreshRows=function(){var t=this;return t.processRowsProcessors(t.rows).then(function(e){t.setVisibleRows(e),t.redrawInPlace(),t.refreshCanvas(!0)}).catch(angular.noop)},e.prototype.refreshCanvas=function(u){var g=this,p=S.defer(),f=[];for(var e in g.renderContainers)if(g.renderContainers.hasOwnProperty(e)){var t=g.renderContainers[e];if(null===t.canvasWidth||isNaN(t.canvasWidth))continue;(t.header||t.headerCanvas)&&(t.explicitHeaderHeight=t.explicitHeaderHeight||null,t.explicitHeaderCanvasHeight=t.explicitHeaderCanvasHeight||null,f.push(t))}return u&&g.buildStyles(),0<f.length?i(function(){var e,t,r=!1,i=0,n=0,o=function(e,t){return e!==t&&(r=!0),t};for(e=0;e<f.length;e++)if(null!==(t=f[e]).canvasWidth&&!isNaN(t.canvasWidth)){if(t.header){var a=t.headerHeight=o(t.headerHeight,m.outerElementHeight(t.header)),l=m.getBorderSize(t.header,"top"),s=m.getBorderSize(t.header,"bottom"),d=parseInt(a-l-s,10);d=d<0?0:d,t.innerHeaderHeight=d,!t.explicitHeaderHeight&&i<d&&(i=d)}if(t.headerCanvas){var c=t.headerCanvasHeight=o(t.headerCanvasHeight,parseInt(m.outerElementHeight(t.headerCanvas),10));!t.explicitHeaderCanvasHeight&&n<c&&(n=c)}}for(e=0;e<f.length;e++)t=f[e],0<i&&void 0!==t.headerHeight&&null!==t.headerHeight&&(t.explicitHeaderHeight||t.headerHeight<i)&&(t.explicitHeaderHeight=o(t.explicitHeaderHeight,i)),0<n&&void 0!==t.headerCanvasHeight&&null!==t.headerCanvasHeight&&(t.explicitHeaderCanvasHeight||t.headerCanvasHeight<n)&&(t.explicitHeaderCanvasHeight=o(t.explicitHeaderCanvasHeight,n));u&&r&&g.buildStyles(),p.resolve()}):i(function(){p.resolve()}),p.promise},e.prototype.redrawInPlace=function(e){for(var t in this.renderContainers){var r=this.renderContainers[t],i=l(e,r.prevScrollTop),n=l(e,r.prevScrollLeft),o=e||0<i?null:r.prevScrolltopPercentage;r.adjustRows(i,o),r.adjustColumns(n)}},e.prototype.hasLeftContainerColumns=function(){return this.hasLeftContainer()&&0<this.renderContainers.left.renderedColumns.length},e.prototype.hasRightContainerColumns=function(){return this.hasRightContainer()&&0<this.renderContainers.right.renderedColumns.length},e.prototype.scrollToIfNecessary=function(e,t){var r=this,i=new x(r,"uiGrid.scrollToIfNecessary"),n=r.renderContainers.body.visibleRowCache,o=r.renderContainers.body.visibleColumnCache,a=r.renderContainers.body.prevScrollTop;a=a<0?0:a;var l=r.renderContainers.body.prevScrollLeft,s=r.renderContainers.body.prevScrollTop+r.gridHeight-r.renderContainers.body.headerHeight-r.footerHeight-r.scrollbarHeight,d=r.renderContainers.body.prevScrollLeft+Math.ceil(r.renderContainers.body.getViewportWidth());if(null!==e){var c,u=n.indexOf(e),g=r.renderContainers.body.getCanvasHeight()-r.renderContainers.body.getViewportHeight(),p=u*r.options.rowHeight;(p=p<0?0:p)<Math.floor(a)?(c=r.renderContainers.body.prevScrollTop-(a-p),t&&t.colDef&&t.colDef.enableCellEditOnFocus&&(c=c-r.footerHeight-r.scrollbarHeight),i.y=E(c,g,r.renderContainers.body.prevScrolltopPercentage)):p>Math.ceil(s)&&(c=p-s+r.renderContainers.body.prevScrollTop,i.y=E(c+r.options.rowHeight,g,r.renderContainers.body.prevScrolltopPercentage))}if(null!==t){for(var f=o.indexOf(t),m=r.renderContainers.body.getCanvasWidth()-r.renderContainers.body.getViewportWidth(),h=0,v=0;v<f;v++)h+=o[v].drawnWidth;var C,w=(h=h<0?0:h)+t.drawnWidth;w=w<0?0:w,h<l?(C=r.renderContainers.body.prevScrollLeft-(l-h),i.x=A(C,m,r.renderContainers.body.prevScrollleftPercentage)):d<w&&(C=w-d+r.renderContainers.body.prevScrollLeft,i.x=A(C,m,r.renderContainers.body.prevScrollleftPercentage))}var b=S.defer();if(i.y||i.x){i.withDelay=!1,r.scrollContainers("",i);var y=r.api.core.on.scrollEnd(null,function(){b.resolve(i),y()})}else b.resolve();return b.promise},e.prototype.scrollTo=function(e,t){var r=null,i=null;return null!=e&&(r=this.getRow(e)),null!=t&&(i=this.getColumn(t.name?t.name:t.field)),this.scrollToIfNecessary(r,i)},e.prototype.clearAllFilters=function(e,t,r){if(void 0===e&&(e=!0),void 0===t&&(t=!1),void 0===r&&(r=!1),this.columns.forEach(function(e){e.filters.forEach(function(e){e.term=void 0,t&&(e.condition=void 0),r&&(e.flags=void 0)})}),e)return this.refreshRows()},v.prototype={put:function(e,t){this[this.grid.options.rowIdentity(e)]=t},get:function(e){return this[this.grid.options.rowIdentity(e)]},remove:function(e){var t=this[e=this.grid.options.rowIdentity(e)];return delete this[e],t}},e}]),angular.module("ui.grid").factory("GridApi",["$q","$rootScope","gridUtil","uiGridConstants","GridRow",function(e,t,d,r,i){var n=function(e){this.grid=e,this.listeners=[],this.registerEvent("core","renderingComplete"),this.registerEvent("core","filterChanged"),this.registerMethod("core","setRowInvisible",i.prototype.setRowInvisible),this.registerMethod("core","clearRowInvisible",i.prototype.clearRowInvisible),this.registerMethod("core","getVisibleRows",this.grid.getVisibleRows),this.registerEvent("core","rowsVisibleChanged"),this.registerEvent("core","rowsRendered"),this.registerEvent("core","scrollBegin"),this.registerEvent("core","scrollEnd"),this.registerEvent("core","canvasHeightChanged"),this.registerEvent("core","gridDimensionChanged")};function c(e,r,i,n){return t.$on(e,function(e){var t=Array.prototype.slice.call(arguments);t.splice(0,1),r.apply(n||i.api,t)})}return n.prototype.suppressEvents=function(e,t){var r=this,i=angular.isArray(e)?e:[e],n=r.listeners.filter(function(t){return i.some(function(e){return t.handler===e})});n.forEach(function(e){e.dereg()}),t(),n.forEach(function(e){e.dereg=c(e.eventId,e.handler,r.grid,e._this)})},n.prototype.registerEvent=function(o,a){var l=this;l[o]||(l[o]={});var e=l[o];e.on||(e.on={},e.raise={});var s=l.grid.id+o+a;e.raise[a]=function(){t.$emit.apply(t,[s].concat(Array.prototype.slice.call(arguments)))},e.on[a]=function(e,t,r){if(null===e||void 0!==e.$on){var i={handler:t,dereg:c(s,t,l.grid,r),eventId:s,scope:e,_this:r};l.listeners.push(i);var n=function(){i.dereg();var e=l.listeners.indexOf(i);l.listeners.splice(e,1)};return e&&e.$on("$destroy",function(){n()}),n}d.logError("asked to listen on "+o+".on."+a+" but scope wasn't passed in the input parameters.  It is legitimate to pass null, but you've passed something else, so you probably forgot to provide scope rather than did it deliberately, not registering")}},n.prototype.registerEventsFromObject=function(e){var r=this,i=[];angular.forEach(e,function(e,t){var r={name:t,events:[]};angular.forEach(e,function(e,t){r.events.push(t)}),i.push(r)}),i.forEach(function(t){t.events.forEach(function(e){r.registerEvent(t.name,e)})})},n.prototype.registerMethod=function(e,t,r,i){this[e]||(this[e]={}),this[e][t]=d.createBoundedWrapper(i||this.grid,r)},n.prototype.registerMethodsFromObject=function(e,r){var i=this,n=[];angular.forEach(e,function(e,t){var r={name:t,methods:[]};angular.forEach(e,function(e,t){r.methods.push({name:t,fn:e})}),n.push(r)}),n.forEach(function(t){t.methods.forEach(function(e){i.registerMethod(t.name,e.name,e.fn,r)})})},n}]),angular.module("ui.grid").factory("GridColumn",["gridUtil","uiGridConstants","i18nService",function(d,c,e){function t(e,t,r){var n=this;n.grid=r,n.uid=t,n.updateColumnDef(e,!0),n.aggregationValue=void 0,n.updateAggregationValue=function(){if(n.aggregationType){var t=0,e=n.grid.getVisibleRows(),r=function(){var i=[];return e.forEach(function(e){var t=n.grid.getCellValue(e,n),r=Number(t);isNaN(r)||i.push(r)}),i};angular.isFunction(n.aggregationType)?n.aggregationValue=n.aggregationType(e,n):n.aggregationType===c.aggregationTypes.count?n.aggregationValue=n.grid.getVisibleRowCount():n.aggregationType===c.aggregationTypes.sum?(r().forEach(function(e){t+=e}),n.aggregationValue=t):n.aggregationType===c.aggregationTypes.avg?(r().forEach(function(e){t+=e}),t/=r().length,n.aggregationValue=t):n.aggregationType===c.aggregationTypes.min?n.aggregationValue=Math.min.apply(null,r()):n.aggregationType===c.aggregationTypes.max?n.aggregationValue=Math.max.apply(null,r()):n.aggregationValue=" "}else n.aggregationValue=void 0},this.getAggregationValue=function(){return n.aggregationValue}}function u(e){return void 0===e.displayName?d.readableColumnName(e.name):e.displayName}return t.prototype.hideColumn=function(){this.colDef.visible=!1},t.prototype.setPropertyOrDefault=function(e,t,r){var i=this;void 0!==e[t]&&e[t]?i[t]=e[t]:void 0!==i[t]?i[t]=i[t]:i[t]=r||{}},t.prototype.updateColumnDef=function(i,e){var n=this;if(void 0===(n.colDef=i).name)throw new Error("colDef.name is required for column at index "+n.grid.options.columnDefs.indexOf(i));if(n.displayName=u(i),!angular.isNumber(n.width)||!n.hasCustomWidth||i.allowCustomWidthOverride){var t=i.width,r="Cannot parse column width '"+t+"' for column named '"+i.name+"'";if(n.hasCustomWidth=!1,angular.isString(t)||angular.isNumber(t))if(angular.isString(t))if(d.endsWith(t,"%")){var o=t.replace(/%/g,""),a=parseInt(o,10);if(isNaN(a))throw new Error(r);n.width=t}else if(t.match(/^(\d+)$/))n.width=parseInt(t.match(/^(\d+)$/)[1],10);else{if(!t.match(/^\*+$/))throw new Error(r);n.width=t}else n.width=t;else n.width="*"}function l(e){return angular.isString(e)||angular.isNumber(e)}["minWidth","maxWidth"].forEach(function(e){var t=i[e],r="Cannot parse column "+e+" '"+t+"' for column named '"+i.name+"'";if("minWidth"===e&&!l(t)&&angular.isDefined(n.grid.options.minimumColumnSize)&&(t=n.grid.options.minimumColumnSize),l(t))if(angular.isString(t)){if(!t.match(/^(\d+)$/))throw new Error(r);n[e]=parseInt(t.match(/^(\d+)$/)[1],10)}else n[e]=t;else n[e]="minWidth"===e?30:9e3}),n.field=void 0===i.field?i.name:i.field,"string"!=typeof n.field&&d.logError("Field is not a string, this is likely to break the code, Field is: "+n.field),n.name=i.name,n.displayName=u(i),n.aggregationType=angular.isDefined(i.aggregationType)?i.aggregationType:null,n.footerCellTemplate=angular.isDefined(i.footerCellTemplate)?i.footerCellTemplate:null,void 0===i.cellTooltip||!1===i.cellTooltip?n.cellTooltip=!1:!0===i.cellTooltip?n.cellTooltip=function(e,t){return n.grid.getCellValue(e,t)}:"function"==typeof i.cellTooltip?n.cellTooltip=i.cellTooltip:n.cellTooltip=function(e,t){return t.colDef.cellTooltip},void 0===i.headerTooltip||!1===i.headerTooltip?n.headerTooltip=!1:!0===i.headerTooltip?n.headerTooltip=function(e){return e.displayName}:"function"==typeof i.headerTooltip?n.headerTooltip=i.headerTooltip:n.headerTooltip=function(e){return e.colDef.headerTooltip},n.footerCellClass=i.footerCellClass,n.cellClass=i.cellClass,n.headerCellClass=i.headerCellClass,n.cellFilter=i.cellFilter?i.cellFilter:"",n.sortCellFiltered=!!i.sortCellFiltered,n.filterCellFiltered=!!i.filterCellFiltered,n.headerCellFilter=i.headerCellFilter?i.headerCellFilter:"",n.footerCellFilter=i.footerCellFilter?i.footerCellFilter:"",n.visible=d.isNullOrUndefined(i.visible)||i.visible,n.headerClass=i.headerClass,n.enableSorting=void 0!==i.enableSorting?i.enableSorting:n.grid.options.enableSorting,n.sortingAlgorithm=i.sortingAlgorithm,n.sortDirectionCycle=void 0!==i.sortDirectionCycle?i.sortDirectionCycle:[null,c.ASC,c.DESC],void 0===n.suppressRemoveSort&&(n.suppressRemoveSort=void 0!==i.suppressRemoveSort&&i.suppressRemoveSort),n.enableFiltering=void 0===i.enableFiltering||i.enableFiltering,n.filterContainer=void 0!==i.filterContainer?i.filterContainer:n.grid.options.filterContainer,n.setPropertyOrDefault(i,"menuItems",[]),e&&n.setPropertyOrDefault(i,"sort"),n.setPropertyOrDefault(i,"defaultSort");var s=[];i.filter?s.push(i.filter):i.filters?s=i.filters:s.push({}),e?(n.setPropertyOrDefault(i,"filter"),n.setPropertyOrDefault(i,"extraStyle"),n.setPropertyOrDefault(i,"filters",s)):n.filters.length===s.length&&n.filters.forEach(function(e,t){void 0!==s[t].placeholder&&(e.placeholder=s[t].placeholder),void 0!==s[t].ariaLabel&&(e.ariaLabel=s[t].ariaLabel),void 0!==s[t].flags&&(e.flags=s[t].flags),void 0!==s[t].type&&(e.type=s[t].type),void 0!==s[t].selectOptions&&(e.selectOptions=s[t].selectOptions)})},t.prototype.unsort=function(){var t=this.sort.priority;this.grid.columns.forEach(function(e){e.sort&&void 0!==e.sort.priority&&e.sort.priority>t&&(e.sort.priority-=1)}),this.sort={},this.grid.api.core.raise.sortChanged(this.grid,this.grid.getColumnSorting())},t.prototype.getColClass=function(e){var t=c.COL_CLASS_PREFIX+this.uid;return e?"."+t:t},t.prototype.isPinnedLeft=function(){return"left"===this.renderContainer},t.prototype.isPinnedRight=function(){return"right"===this.renderContainer},t.prototype.getColClassDefinition=function(){return" .grid"+this.grid.id+" "+this.getColClass(!0)+" { min-width: "+this.drawnWidth+"px; max-width: "+this.drawnWidth+"px; }"},t.prototype.getRenderContainer=function(){var e=this.renderContainer;return null!==e&&""!==e&&void 0!==e||(e="body"),this.grid.renderContainers[e]},t.prototype.showColumn=function(){this.colDef.visible=!0},t.prototype.getAggregationText=function(){if(this.colDef.aggregationHideLabel)return"";if(this.colDef.aggregationLabel)return this.colDef.aggregationLabel;switch(this.colDef.aggregationType){case c.aggregationTypes.count:return e.getSafeText("aggregation.count");case c.aggregationTypes.sum:return e.getSafeText("aggregation.sum");case c.aggregationTypes.avg:return e.getSafeText("aggregation.avg");case c.aggregationTypes.min:return e.getSafeText("aggregation.min");case c.aggregationTypes.max:return e.getSafeText("aggregation.max");default:return""}},t.prototype.getCellTemplate=function(){return this.cellTemplatePromise},t.prototype.getCompiledElementFn=function(){return this.compiledElementFnDefer.promise},t}]),angular.module("ui.grid").factory("GridOptions",["gridUtil","uiGridConstants",function(t,r){return{initialize:function(e){return e.onRegisterApi=e.onRegisterApi||angular.noop(),e.data=e.data||[],e.columnDefs=e.columnDefs||[],e.excludeProperties=e.excludeProperties||["$$hashKey"],e.enableRowHashing=!1!==e.enableRowHashing,e.rowIdentity=e.rowIdentity||function(e){return t.hashKey(e)},e.getRowIdentity=e.getRowIdentity||function(e){return e.$$hashKey},e.flatEntityAccess=!0===e.flatEntityAccess,e.showHeader=void 0===e.showHeader||e.showHeader,e.showHeader?e.headerRowHeight=void 0!==e.headerRowHeight?e.headerRowHeight:30:e.headerRowHeight=0,"string"==typeof e.rowHeight?e.rowHeight=parseInt(e.rowHeight)||30:e.rowHeight=e.rowHeight||30,e.minRowsToShow=void 0!==e.minRowsToShow?e.minRowsToShow:10,e.showGridFooter=!0===e.showGridFooter,e.showColumnFooter=!0===e.showColumnFooter,e.columnFooterHeight=void 0!==e.columnFooterHeight?e.columnFooterHeight:30,e.gridFooterHeight=void 0!==e.gridFooterHeight?e.gridFooterHeight:30,e.columnWidth=void 0!==e.columnWidth?e.columnWidth:50,e.maxVisibleColumnCount=void 0!==e.maxVisibleColumnCount?e.maxVisibleColumnCount:200,e.virtualizationThreshold=void 0!==e.virtualizationThreshold?e.virtualizationThreshold:20,e.columnVirtualizationThreshold=void 0!==e.columnVirtualizationThreshold?e.columnVirtualizationThreshold:10,e.excessRows=void 0!==e.excessRows?e.excessRows:4,e.scrollThreshold=void 0!==e.scrollThreshold?e.scrollThreshold:4,e.excessColumns=void 0!==e.excessColumns?e.excessColumns:4,e.aggregationCalcThrottle=void 0!==e.aggregationCalcThrottle?e.aggregationCalcThrottle:500,e.wheelScrollThrottle=void 0!==e.wheelScrollThrottle?e.wheelScrollThrottle:70,e.scrollDebounce=void 0!==e.scrollDebounce?e.scrollDebounce:300,e.enableHiding=!1!==e.enableHiding,e.enableSorting=!1!==e.enableSorting,e.suppressMultiSort=!0===e.suppressMultiSort,e.enableFiltering=!0===e.enableFiltering,e.filterContainer=void 0!==e.filterContainer?e.filterContainer:"headerCell",e.enableColumnMenus=!1!==e.enableColumnMenus,e.enableVerticalScrollbar=void 0!==e.enableVerticalScrollbar?e.enableVerticalScrollbar:r.scrollbars.ALWAYS,e.enableHorizontalScrollbar=void 0!==e.enableHorizontalScrollbar?e.enableHorizontalScrollbar:r.scrollbars.ALWAYS,e.enableMinHeightCheck=!1!==e.enableMinHeightCheck,e.minimumColumnSize=void 0!==e.minimumColumnSize?e.minimumColumnSize:30,e.rowEquality=e.rowEquality||function(e,t){return e===t},e.headerTemplate=e.headerTemplate||null,e.footerTemplate=e.footerTemplate||"ui-grid/ui-grid-footer",e.gridFooterTemplate=e.gridFooterTemplate||"ui-grid/ui-grid-grid-footer",e.rowTemplate=e.rowTemplate||"ui-grid/ui-grid-row",e.gridMenuTemplate=e.gridMenuTemplate||"ui-grid/uiGridMenu",e.menuButtonTemplate=e.menuButtonTemplate||"ui-grid/ui-grid-menu-button",e.menuItemTemplate=e.menuItemTemplate||"ui-grid/uiGridMenuItem",e.appScopeProvider=e.appScopeProvider||null,e}}}]),angular.module("ui.grid").factory("GridRenderContainer",["gridUtil","uiGridConstants",function(b,n){function e(e,t,r){var i=this;i.name=e,i.grid=t,i.visibleRowCache=[],i.visibleColumnCache=[],i.renderedRows=[],i.renderedColumns=[],i.prevScrollTop=0,i.prevScrolltopPercentage=0,i.prevRowScrollIndex=0,i.prevScrollLeft=0,i.prevScrollleftPercentage=0,i.prevColumnScrollIndex=0,i.columnStyles="",i.viewportAdjusters=[],i.hasHScrollbar=!1,i.hasVScrollbar=!1,i.canvasHeightShouldUpdate=!0,i.$$canvasHeight=0,r&&angular.isObject(r)&&angular.extend(i,r),t.registerStyleComputation({priority:5,func:function(){return i.updateColumnWidths(),i.columnStyles}})}return e.prototype.reset=function(){this.visibleColumnCache.length=0,this.visibleRowCache.length=0,this.renderedRows.length=0,this.renderedColumns.length=0},e.prototype.containsColumn=function(e){return-1!==this.visibleColumnCache.indexOf(e)},e.prototype.minRowsToRender=function(){for(var e=0,t=0,r=this.getViewportHeight(),i=this.visibleRowCache.length-1;t<r&&0<=i;i--)t+=this.visibleRowCache[i].height,e++;return e},e.prototype.minColumnsToRender=function(){for(var e=this.getViewportWidth(),t=0,r=0,i=0;i<this.visibleColumnCache.length;i++){var n=this.visibleColumnCache[i];if(r<e)r+=n.drawnWidth?n.drawnWidth:0,t++;else{for(var o=0,a=i;i-t<=a;a--)o+=this.visibleColumnCache[a].drawnWidth?this.visibleColumnCache[a].drawnWidth:0;o<e&&t++}}return t},e.prototype.getVisibleRowCount=function(){return this.visibleRowCache.length},e.prototype.registerViewportAdjuster=function(e){this.viewportAdjusters.push(e)},e.prototype.removeViewportAdjuster=function(e){var t=this.viewportAdjusters.indexOf(e);-1<t&&this.viewportAdjusters.splice(t,1)},e.prototype.getViewportAdjustment=function(){var t={height:0,width:0};return this.viewportAdjusters.forEach(function(e){t=e.call(this,t)}),t},e.prototype.getMargin=function(r){var i=0;return this.viewportAdjusters.forEach(function(e){var t=e.call(this,{height:0,width:0});t.side&&t.side===r&&(i+=-1*t.width)}),i},e.prototype.getViewportHeight=function(){var e=this,t=e.headerHeight?e.headerHeight:e.grid.headerHeight,r=e.grid.gridHeight-t-e.grid.footerHeight;return 0<(r+=e.getViewportAdjustment().height)?r:0},e.prototype.getViewportWidth=function(){var e=this.grid.gridWidth;return e+=this.getViewportAdjustment().width},e.prototype.getHeaderViewportWidth=function(){return this.getViewportWidth()},e.prototype.getCanvasHeight=function(){var t=this;if(!t.canvasHeightShouldUpdate)return t.$$canvasHeight;var e=t.$$canvasHeight;return t.$$canvasHeight=0,t.visibleRowCache.forEach(function(e){t.$$canvasHeight+=e.height}),t.canvasHeightShouldUpdate=!1,t.grid.api.core.raise.canvasHeightChanged(e,t.$$canvasHeight),t.$$canvasHeight},e.prototype.getVerticalScrollLength=function(){return this.getCanvasHeight()-this.getViewportHeight()+this.grid.scrollbarHeight!==0?this.getCanvasHeight()-this.getViewportHeight()+this.grid.scrollbarHeight:-1},e.prototype.getHorizontalScrollLength=function(){return this.getCanvasWidth()-this.getViewportWidth()+this.grid.scrollbarWidth!==0?this.getCanvasWidth()-this.getViewportWidth()+this.grid.scrollbarWidth:-1},e.prototype.getCanvasWidth=function(){return this.canvasWidth},e.prototype.setRenderedRows=function(e){this.renderedRows.length=e.length;for(var t=0;t<e.length;t++)this.renderedRows[t]=e[t]},e.prototype.setRenderedColumns=function(e){this.renderedColumns.length=e.length;for(var t=0;t<e.length;t++)this.renderedColumns[t]=e[t];this.updateColumnOffset()},e.prototype.updateColumnOffset=function(){for(var e=0,t=0;t<this.currentFirstColumn;t++)e+=this.visibleColumnCache[t].drawnWidth;this.columnOffset=e},e.prototype.scrollVertical=function(e){var t=-1;if(e!==this.prevScrollTop){var r=e-this.prevScrollTop;return 0<r&&(this.grid.scrollDirection=n.scrollDirection.DOWN),r<0&&(this.grid.scrollDirection=n.scrollDirection.UP),1<(t=e/this.getVerticalScrollLength())&&(t=1),t<0&&(t=0),this.adjustScrollVertical(e,t),t}},e.prototype.scrollHorizontal=function(e){var t=-1;if(e!==this.prevScrollLeft){var r=e-this.prevScrollLeft;0<r&&(this.grid.scrollDirection=n.scrollDirection.RIGHT),r<0&&(this.grid.scrollDirection=n.scrollDirection.LEFT);var i=this.getHorizontalScrollLength();return t=0!==i?e/i:0,this.adjustScrollHorizontal(e,t),t}},e.prototype.adjustScrollVertical=function(e,t,r){(this.prevScrollTop!==e||r)&&(void 0!==e&&null!=e||(e=(this.getCanvasHeight()-this.getViewportHeight())*t),this.adjustRows(e,t,!1),this.prevScrollTop=e,this.prevScrolltopPercentage=t,this.grid.queueRefresh())},e.prototype.adjustScrollHorizontal=function(e,t,r){(this.prevScrollLeft!==e||r)&&(void 0!==e&&null!=e||(e=(this.getCanvasWidth()-this.getViewportWidth())*t),this.adjustColumns(e),this.prevScrollLeft=e,this.prevScrollleftPercentage=t,this.grid.queueRefresh())},e.prototype.adjustRows=function(e,t,r){var i=this,n=i.minRowsToRender(),o=i.visibleRowCache,a=o.length-n;null==t&&e&&(t=e/i.getVerticalScrollLength());var l=Math.ceil(Math.min(a,a*t));a<l&&(l=a);var s=[];if(o.length>i.grid.options.virtualizationThreshold){if(null!=e){if(!i.grid.suppressParentScrollDown&&i.prevScrollTop<e&&l<i.prevRowScrollIndex+i.grid.options.scrollThreshold&&l<a)return;if(!i.grid.suppressParentScrollUp&&i.prevScrollTop>e&&l>i.prevRowScrollIndex-i.grid.options.scrollThreshold&&l<a)return}s=[Math.max(0,l-i.grid.options.excessRows),Math.min(o.length,l+n+i.grid.options.excessRows)]}else{var d=i.visibleRowCache.length;s=[0,Math.max(d,n+i.grid.options.excessRows)]}i.updateViewableRowRange(s),i.prevRowScrollIndex=l},e.prototype.adjustColumns=function(e){var t=this,r=t.minColumnsToRender(),i=t.visibleColumnCache,n=i.length-r,o=Math.min(n,t.getLeftIndex(e)),a=[];if(i.length>t.grid.options.columnVirtualizationThreshold&&t.getCanvasWidth()>t.getViewportWidth())a=[Math.max(0,o-t.grid.options.excessColumns),Math.min(i.length,o+r+t.grid.options.excessColumns)];else{var l=t.visibleColumnCache.length;a=[0,Math.max(l,r+t.grid.options.excessColumns)]}t.updateViewableColumnRange(a),t.prevColumnScrollIndex=o},e.prototype.getLeftIndex=function(e){for(var t=0,r=0;r<this.visibleColumnCache.length&&!(this.visibleColumnCache[r]&&this.visibleColumnCache[r].visible&&e<=(t+=this.visibleColumnCache[r].drawnWidth));r++);return r},e.prototype.updateViewableRowRange=function(e){var t=this.visibleRowCache.slice(e[0],e[1]);this.currentTopRow=e[0],this.setRenderedRows(t)},e.prototype.updateViewableColumnRange=function(e){var t=this.visibleColumnCache.slice(e[0],e[1]);this.currentFirstColumn=e[0],this.setRenderedColumns(t)},e.prototype.headerCellWrapperStyle=function(){if(0===this.currentFirstColumn)return null;var e=this.columnOffset;return this.grid.isRTL()?{"margin-right":e+"px"}:{"margin-left":e+"px"}},e.prototype.updateColumnWidths=function(){var i=this,n=[],o=0,a=0,t="",l=!1,s=[],d=[],c=0,u=i.grid.getViewportWidth()-i.grid.scrollbarWidth,r=[];angular.forEach(i.grid.renderContainers,function(e){r=r.concat(e.visibleColumnCache)}),r.forEach(function(e){var t=0;if(e.visible)if(l&&(u+=i.grid.scrollbarWidth),!l&&e.colDef.pinnedRight&&(l=!0),angular.isNumber(e.width))t=e.colDef.allowFloatWidth?parseFloat(e.width):parseInt(e.width,10),a+=t,e.drawnWidth=t,s.push(e);else if(b.endsWith(e.width,"%")){var r=parseInt(e.width.replace(/%/g,""),10);(t=e.colDef.allowFloatWidth?parseFloat(r/100*u):parseInt(r/100*u,10))>e.maxWidth&&(t=e.maxWidth),t<e.minWidth&&(t=e.minWidth),a+=t,e.drawnWidth=t,c+=r,d.push(e)}else angular.isString(e.width)&&-1!==e.width.indexOf("*")&&(o+=e.width.length,n.push(e))});var e,g=u-a;if(0<n.length){var p=g/o;n.forEach(function(e){var t=parseInt(e.width.length*p,10);e.colDef.allowFloatWidth&&(t=parseFloat(e.width.length*p)),t>e.maxWidth&&(t=e.maxWidth),t<e.minWidth&&(t=e.minWidth),a+=t,e.drawnWidth=t})}if(0<n.length?e=n:0<d.length&&0===s.length&&100===c&&(e=d),!angular.isUndefined(e)){for(var f=function(e){e.drawnWidth<e.maxWidth&&0<m&&(e.drawnWidth++,a++,m--,h=!0)},m=u-a,h=!0;0<m&&h;)h=!1,e.forEach(f);var v=function(e){e.drawnWidth>e.minWidth&&0<C&&(e.drawnWidth--,a--,C--,h=!0)},C=a-u;for(h=!0;0<C&&h;)h=!1,e.forEach(v)}var w=0;i.visibleColumnCache.forEach(function(e){e.visible&&(w+=e.drawnWidth)}),r.forEach(function(e){t+=e.getColClassDefinition()}),i.canvasWidth=w,this.columnStyles=t},e.prototype.needsHScrollbarPlaceholder=function(){var e;return("left"===this.name||"right"===this.name&&!this.hasHScrollbar&&!this.grid.disableScrolling)&&(this.grid.options.enableHorizontalScrollbar===n.scrollbars.ALWAYS||(e=this.grid.element[0].querySelector(".ui-grid-render-container-body .ui-grid-viewport")).scrollWidth>e.offsetWidth)},e.prototype.getViewportStyle=function(){var e=this,t={},r={};return r[n.scrollbars.ALWAYS]="scroll",r[n.scrollbars.WHEN_NEEDED]="auto",e.hasHScrollbar=!1,e.hasVScrollbar=!1,e.grid.disableScrolling?(t["overflow-x"]="hidden",t["overflow-y"]="hidden"):("body"===e.name?(e.hasHScrollbar=e.grid.options.enableHorizontalScrollbar!==n.scrollbars.NEVER,e.grid.isRTL()?e.grid.hasLeftContainerColumns()||(e.hasVScrollbar=e.grid.options.enableVerticalScrollbar!==n.scrollbars.NEVER):e.grid.hasRightContainerColumns()||(e.hasVScrollbar=e.grid.options.enableVerticalScrollbar!==n.scrollbars.NEVER)):"left"===e.name?e.hasVScrollbar=!!e.grid.isRTL()&&e.grid.options.enableVerticalScrollbar!==n.scrollbars.NEVER:e.hasVScrollbar=!e.grid.isRTL()&&e.grid.options.enableVerticalScrollbar!==n.scrollbars.NEVER,t["overflow-x"]=e.hasHScrollbar?r[e.grid.options.enableHorizontalScrollbar]:"hidden",t["overflow-y"]=e.hasVScrollbar?r[e.grid.options.enableVerticalScrollbar]:"hidden"),t},e}]),angular.module("ui.grid").factory("GridRow",["gridUtil","uiGridConstants",function(i,t){function e(e,t,r){this.grid=r,this.entity=e,this.index=t,this.uid=i.nextUid(),this.visible=!0,this.isSelected=!1,this.$$height=r.options.rowHeight}return Object.defineProperty(e.prototype,"height",{get:function(){return this.$$height},set:function(e){e!==this.$$height&&(this.grid.updateCanvasHeight(),this.$$height=e)}}),e.prototype.getQualifiedColField=function(e){return"row."+this.getEntityQualifiedColField(e)},e.prototype.getEntityQualifiedColField=function(e){return e.field===t.ENTITY_BINDING?"entity":i.preEval("entity."+e.field)},e.prototype.setRowInvisible=function(e){e&&e.setThisRowInvisible&&e.setThisRowInvisible("user")},e.prototype.clearRowInvisible=function(e){e&&e.clearThisRowInvisible&&e.clearThisRowInvisible("user")},e.prototype.setThisRowInvisible=function(e,t){this.invisibleReason||(this.invisibleReason={}),this.invisibleReason[e]=!0,this.evaluateRowVisibility(t)},e.prototype.clearThisRowInvisible=function(e,t){void 0!==this.invisibleReason&&delete this.invisibleReason[e],this.evaluateRowVisibility(t)},e.prototype.evaluateRowVisibility=function(e){var r=!0;void 0!==this.invisibleReason&&angular.forEach(this.invisibleReason,function(e,t){e&&(r=!1)}),void 0!==this.visible&&this.visible===r||(this.visible=r,e||(this.grid.queueGridRefresh(),this.grid.api.core.raise.rowsVisibleChanged(this)))},e}]),function(){"use strict";angular.module("ui.grid").factory("GridRowColumn",["$parse","$filter",function(e,t){var r=function e(t,r){if(!(this instanceof e))throw"Using GridRowColumn as a function insead of as a constructor. Must be called with `new` keyword";this.row=t,this.col=r};return r.prototype.getIntersectionValueRaw=function(){return e(this.row.getEntityQualifiedColField(this.col))(this.row)},r}])}(),angular.module("ui.grid").factory("ScrollEvent",["gridUtil",function(a){function e(e,t,r,i){var n=this;if(!e)throw new Error("grid argument is required");n.grid=e,n.source=i,n.withDelay=!0,n.sourceRowContainer=t,n.sourceColContainer=r,n.newScrollLeft=null,n.newScrollTop=null,n.x=null,n.y=null,n.verticalScrollLength=-9999999,n.horizontalScrollLength=-999999,n.fireThrottledScrollingEvent=a.throttle(function(e){n.grid.scrollContainers(e,n)},n.grid.options.wheelScrollThrottle,{trailing:!0})}return e.prototype.getNewScrollLeft=function(e,t){var r=this;if(r.newScrollLeft)return r.newScrollLeft;var i,n=e.getCanvasWidth()-e.getViewportWidth(),o=a.normalizeScrollLeft(t,r.grid);if(void 0!==r.x.percentage&&void 0!==r.x.percentage)i=r.x.percentage;else{if(void 0===r.x.pixels||void 0===r.x.pixels)throw new Error("No percentage or pixel value provided for scroll event X axis");i=r.x.percentage=(o+r.x.pixels)/n}return Math.max(0,i*n)},e.prototype.getNewScrollTop=function(e,t){var r=this;if(r.newScrollTop)return r.newScrollTop;var i,n=e.getVerticalScrollLength(),o=t[0].scrollTop;if(void 0!==r.y.percentage&&void 0!==r.y.percentage)i=r.y.percentage;else{if(void 0===r.y.pixels||void 0===r.y.pixels)throw new Error("No percentage or pixel value provided for scroll event Y axis");i=r.y.percentage=(o+r.y.pixels)/n}return Math.max(0,i*n)},e.prototype.atTop=function(e){return this.y&&(0===this.y.percentage||this.verticalScrollLength<0)&&0===e},e.prototype.atBottom=function(e){return this.y&&(1===this.y.percentage||0===this.verticalScrollLength)&&0<e},e.prototype.atLeft=function(e){return this.x&&(0===this.x.percentage||this.horizontalScrollLength<0)&&0===e},e.prototype.atRight=function(e){return this.x&&(1===this.x.percentage||0===this.horizontalScrollLength)&&0<e},e.Sources={ViewPortScroll:"ViewPortScroll",RenderContainerMouseWheel:"RenderContainerMouseWheel",RenderContainerTouchMove:"RenderContainerTouchMove",Other:99},e}]),function(){"use strict";angular.module("ui.grid").service("gridClassFactory",["gridUtil","$q","$compile","$templateCache","uiGridConstants","Grid","GridColumn","GridRow",function(d,i,n,e,c,o,t,r){var a={createGrid:function(e){(e=void 0!==e?e:{}).id=d.newId();var t=new o(e);if(t.options.rowTemplate){var r=i.defer();t.getRowTemplateFn=r.promise,d.getTemplate(t.options.rowTemplate).then(function(e){var t=n(e);r.resolve(t)},function(){throw new Error("Couldn't fetch/use row template '"+t.options.rowTemplate+"'")}).catch(angular.noop)}return t.registerColumnBuilder(a.defaultColumnBuilder),t.registerRowBuilder(a.rowTemplateAssigner),t.registerRowsProcessor(function(e){return e.forEach(function(e){e.evaluateRowVisibility(!0)}),e},50),t.registerColumnsProcessor(function(e){return e.forEach(function(e){e.visible=!angular.isDefined(e.colDef.visible)||e.colDef.visible}),e},50),t.registerRowsProcessor(t.searchRows,100),t.options.externalSort&&angular.isFunction(t.options.externalSort)?t.registerRowsProcessor(t.options.externalSort,200):t.registerRowsProcessor(t.sortByColumn,200),t},defaultColumnBuilder:function(a,l,e){var s=[],t=function(r,e,t,i,n){a[r]?l[e]=a[r]:l[e]=t;var o=d.getTemplate(l[e]).then(function(e){angular.isFunction(e)&&(e=e());var t="cellTooltip"===n?"col.cellTooltip(row,col)":"col.headerTooltip(col)";n&&!1===l[n]?e=e.replace(c.TOOLTIP,""):n&&l[n]&&(e=e.replace(c.TOOLTIP,'title="{{'+t+' CUSTOM_FILTERS }}"')),l[r]=i?e.replace(c.CUSTOM_FILTERS,function(){return l[i]?"|"+l[i]:""}):e},function(){throw new Error("Couldn't fetch/use colDef."+r+" '"+a[r]+"'")}).catch(angular.noop);return s.push(o),o};return l.cellTemplatePromise=t("cellTemplate","providedCellTemplate","ui-grid/uiGridCell","cellFilter","cellTooltip"),l.headerCellTemplatePromise=t("headerCellTemplate","providedHeaderCellTemplate","ui-grid/uiGridHeaderCell","headerCellFilter","headerTooltip"),l.footerCellTemplatePromise=t("footerCellTemplate","providedFooterCellTemplate","ui-grid/uiGridFooterCell","footerCellFilter"),l.filterHeaderTemplatePromise=t("filterHeaderTemplate","providedFilterHeaderTemplate","ui-grid/ui-grid-filter"),l.compiledElementFnDefer=i.defer(),i.all(s)},rowTemplateAssigner:function(e){if(e.rowTemplate){var r=i.defer();e.getRowTemplateFn=r.promise,d.getTemplate(e.rowTemplate).then(function(e){var t=n(e);r.resolve(t)},function(){throw new Error("Couldn't fetch/use row template '"+e.rowTemplate+"'")})}else e.rowTemplate=this.options.rowTemplate,e.getRowTemplateFn=this.getRowTemplateFn;return e.getRowTemplateFn}};return a}])}(),function(){function o(e){return e.replace(/[|\\{}()[\]^$+?*.]/g,"\\$&").replace(/-/g,"\\x2d")}angular.module("ui.grid").service("rowSearcher",["gridUtil","uiGridConstants",function(d,s){var n=s.filter.CONTAINS,c={getTerm:function(e){if(void 0===e.term)return e.term;var t=e.term;return"string"==typeof t&&(t=t.trim()),t},stripTerm:function(e){var t=c.getTerm(e);return"string"==typeof t?o(t.replace(/(^\*|\*$)/g,"")):t},guessCondition:function(e){if(void 0===e.term||!e.term)return n;var t=c.getTerm(e);if(/\*/.test(t)){var r=e.flags&&e.flags.caseSensitive?"":"i",i=(t=o(t)).replace(/\\\*/g,".*?");return new RegExp("^"+i+"$",r)}return n},setupFilters:function(e){for(var t=[],r=0;r<e.length;r++){var i=e[r];if(i.noTerm||!d.isNullOrUndefined(i.term)){var n={},o="";switch(i.flags&&i.flags.caseSensitive||(o+="i"),d.isNullOrUndefined(i.term)||(i.rawTerm?n.term=i.term:n.term=c.stripTerm(i)),n.noTerm=i.noTerm,n.condition=i.condition||c.guessCondition(i),n.flags=angular.extend({caseSensitive:!1,date:!1},i.flags),n.condition){case s.filter.STARTS_WITH:n.startswithRE=new RegExp("^"+n.term,o);break;case s.filter.ENDS_WITH:n.endswithRE=new RegExp(n.term+"$",o);break;case s.filter.EXACT:n.exactRE=new RegExp("^"+n.term+"$",o);break;case s.filter.CONTAINS:n.containsRE=new RegExp(n.term,o)}t.push(n)}}return t},runColumnFilter:function(e,t,r,i){var n=typeof i.condition,o=i.term,a=r.filterCellFiltered?e.getCellDisplayValue(t,r):e.getCellValue(t,r);if(null==a&&(a=""),i.condition instanceof RegExp)return i.condition.test(a);if("function"===n)return i.condition(o,a,t,r);if(i.startswithRE)return i.startswithRE.test(a);if(i.endswithRE)return i.endswithRE.test(a);if(i.containsRE)return i.containsRE.test(a);if(i.exactRE)return i.exactRE.test(a);if(i.condition===s.filter.NOT_EQUAL)return!new RegExp("^"+o+"$").test(a);if("number"==typeof a&&"string"==typeof o){var l=parseFloat(o.replace(/\\\./,".").replace(/\\\-/,"-"));isNaN(l)||(o=l)}switch(!0===i.flags.date&&(a=new Date(a),o=new Date(o.replace(/\\/g,""))),i.condition){case s.filter.GREATER_THAN:return o<a;case s.filter.GREATER_THAN_OR_EQUAL:return o<=a;case s.filter.LESS_THAN:return a<o;case s.filter.LESS_THAN_OR_EQUAL:return a<=o}return!0},searchColumn:function(e,t,r,i){if(e.options.useExternalFiltering)return!0;for(var n=0;n<i.length;n++){var o=i[n];if((!d.isNullOrUndefined(o.term)&&""!==o.term||o.noTerm)&&!c.runColumnFilter(e,t,r,o))return!1}return!0},search:function(e,l,t){if(l){if(!e.options.enableFiltering)return l;for(var r=[],i=function(e){var t=!1;return e.forEach(function(e){(!d.isNullOrUndefined(e.term)&&""!==e.term||e.noTerm)&&(t=!0)}),t},n=0;n<t.length;n++){var o=t[n];void 0!==o.filters&&i(o.filters)&&r.push({col:o,filters:c.setupFilters(o.filters)})}if(0<r.length){for(var a=function(e,t){for(var r=0;r<l.length;r++)i=e,n=l[r],o=t.col,a=t.filters,n.visible&&!c.searchColumn(i,n,o,a)&&(n.visible=!1);var i,n,o,a},s=0;s<r.length;s++)a(e,r[s]);e.api.core.raise.rowsVisibleChanged&&e.api.core.raise.rowsVisibleChanged()}return l}}};return c}])}(),angular.module("ui.grid").service("rowSorter",["uiGridConstants",function(h){var v={colSortFnCache:{}};function l(e){return/^\s*-?Infinity\s*$/.test(e)?parseFloat(e):parseFloat(e.replace(/[^0-9.eE-]/g,""))}return v.guessSortFn=function(e){switch(e){case"number":return v.sortNumber;case"numberStr":return v.sortNumberStr;case"boolean":return v.sortBool;case"string":return v.sortAlpha;case"date":return v.sortDate;case"object":return v.basicSort;default:throw new Error("No sorting function found for type: "+e)}},v.handleNulls=function(e,t){return null==e||null==t?null==e&&null==t?0:null==e?1:-1:null},v.basicSort=function(e,t){var r=v.handleNulls(e,t);return null!==r?r:e===t?0:e<t?-1:1},v.sortNumber=function(e,t){var r=v.handleNulls(e,t);return null!==r?r:e-t},v.sortNumberStr=function(e,t){var r=v.handleNulls(e,t);if(null!==r)return r;var i=l(e),n=l(t),o=isNaN(i),a=isNaN(n);return o||a?o&&a?0:o?1:-1:i-n},v.sortAlpha=function(e,t){var r=v.handleNulls(e,t);if(null!==r)return r;var i=e.toString().toLowerCase(),n=t.toString().toLowerCase();return i===n?0:i.localeCompare(n)},v.sortDate=function(e,t){var r=v.handleNulls(e,t);if(null!==r)return r;var i=e instanceof Date?e.getTime():new Date(e).getTime(),n=t instanceof Date?t.getTime():new Date(t).getTime();return i===n?0:i<n?-1:1},v.sortBool=function(e,t){var r=v.handleNulls(e,t);return null!==r?r:e&&t||!e&&!t?0:e?1:-1},v.getSortFn=function(e){if(v.colSortFnCache[e.colDef.name])return v.colSortFnCache[e.colDef.name];if(null!=e.sortingAlgorithm)return v.colSortFnCache[e.colDef.name]=e.sortingAlgorithm,e.sortingAlgorithm;if(e.sortCellFiltered&&e.cellFilter)return v.colSortFnCache[e.colDef.name]=v.sortAlpha,v.sortAlpha;var t=v.guessSortFn(e.colDef.type);return t?v.colSortFnCache[e.colDef.name]=t:v.sortAlpha},v.prioritySort=function(e,t){return e.sort&&void 0!==e.sort.priority&&t.sort&&void 0!==t.sort.priority?e.sort.priority<t.sort.priority?-1:e.sort.priority===t.sort.priority?0:1:e.sort&&void 0!==e.sort.priority?-1:t.sort&&void 0!==t.sort.priority?1:0},v.sort=function(g,e,t){if(e){if(g.options.useExternalSorting)return e;var p,f,m=[],r=[];if(t.forEach(function(e){!e.sort||e.sort.ignoreSort||!e.sort.direction||e.sort.direction!==h.ASC&&e.sort.direction!==h.DESC?e.defaultSort&&e.defaultSort.direction&&(e.defaultSort.direction===h.ASC||e.defaultSort.direction===h.DESC)&&r.push({col:e,sort:e.defaultSort}):m.push({col:e,sort:e.sort})}),m=m.sort(v.prioritySort),r=r.sort(v.prioritySort),0===(m=m.concat(r)).length)return e;e.forEach(function(e,t){e.entity.$$uiGridIndex=t});var i=e.sort(function(e,t){for(var r,i=0,n=0;0===i&&n<m.length;){p=m[n].col,f=m[n].sort.direction,r=v.getSortFn(p);var o=(a=g,l=e,s=t,u=c=void 0,u=(d=p).sortCellFiltered?(c=a.getCellDisplayValue(l,d),a.getCellDisplayValue(s,d)):(c=a.getCellValue(l,d),a.getCellValue(s,d)),[c,u]);i=r(o[0],o[1],e,t,f,p),n++}var a,l,s,d,c,u;return 0===i?e.entity.$$uiGridIndex-t.entity.$$uiGridIndex:f===h.ASC?i:0-i});return e.forEach(function(e,t){delete e.entity.$$uiGridIndex}),i}},v}]),function(){var C,e=angular.module("ui.grid");function w(e){var t=e;return void 0!==t.length&&t.length&&(t=e[0]),t.ownerDocument.defaultView.getComputedStyle(t,null)}"function"!=typeof Function.prototype.bind&&(C=function(){var i=Array.prototype.slice;return function(e){var t=this,r=i.call(arguments,1);return r.length?function(){return arguments.length?t.apply(e,r.concat(i.call(arguments))):t.apply(e,r)}:function(){return arguments.length?t.apply(e,arguments):t.call(e)}}});var l=new RegExp("^("+/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source+")(?!px)[a-z%]+$","i"),b=/^(block|none|table(?!-c[ea]).+)/,y={position:"absolute",visibility:"hidden",display:"block"};function S(e,t,r,i,n){for(var o=r===(i?"border":"content")?4:"width"===t?1:0,a=0,l=["Top","Right","Bottom","Left"];o<4;o+=2){var s=l[o];if("margin"===r){var d=parseFloat(n[r+s]);isNaN(d)||(a+=d)}if(i){if("content"===r){var c=parseFloat(n["padding"+s]);isNaN(c)||(a-=c)}if("margin"!==r){var u=parseFloat(n["border"+s+"Width"]);isNaN(u)||(a-=u)}}else{var g=parseFloat(n["padding"+s]);if(isNaN(g)||(a+=g),"padding"!==r){var p=parseFloat(n["border"+s+"Width"]);isNaN(p)||(a+=p)}}}return a}function x(e,t,r){var i,n=!0,o=w(e),a="border-box"===o.boxSizing;if(i<=0||null==i){if(((i=o[t])<0||null==i)&&(i=e.style[t]),l.test(i))return i;n=a&&!0,i=parseFloat(i)||0}return i+S(0,t,r||(a?"border":"content"),n,o)}var E=["0","0","0","0"],A="uiGrid-";e.service("gridUtil",["$log","$window","$document","$http","$templateCache","$timeout","$interval","$injector","$q","$interpolate","uiGridConstants",function(t,n,r,e,i,s,d,o,a,l,c){var u,g={augmentWidthOrHeight:S,getStyles:w,createBoundedWrapper:function(e,t){return function(){return t.apply(e,arguments)}},readableColumnName:function(e){return void 0===e||null==e?e:("string"!=typeof e&&(e=String(e)),e.replace(/_+/g," ").replace(/^[A-Z]+$/,function(e){return e.toLowerCase()}).replace(/([\w\u00C0-\u017F]+)/g,function(e){return e.charAt(0).toUpperCase()+e.slice(1)}).replace(/(\w+?(?=[A-Z]))/g,"$1 "))},getColumnsFromData:function(e,r){var i=[];if(!e||void 0===e[0]||void 0===e[0])return[];angular.isUndefined(r)&&(r=[]);var t=e[0];return angular.forEach(t,function(e,t){-1===r.indexOf(t)&&i.push({name:t})}),i},newId:(u=(new Date).getTime(),function(){return u+=1}),getTemplate:function(r){if(i.get(r))return g.postProcessTemplate(i.get(r));if(angular.isFunction(r.then))return r.then(g.postProcessTemplate).catch(angular.noop);try{if(0<angular.element(r).length)return a.when(r).then(g.postProcessTemplate).catch(angular.noop)}catch(e){}return e({method:"GET",url:r}).then(function(e){var t=e.data.trim();return i.put(r,t),t},function(e){throw new Error("Could not get template "+r+": "+e)}).then(g.postProcessTemplate).catch(angular.noop)},postProcessTemplate:function(e){var t=l.startSymbol(),r=l.endSymbol();return"{{"===t&&"}}"===r||(e=(e=e.replace(/\{\{/g,t)).replace(/\}\}/g,r)),a.when(e)},guessType:function(e){var t=typeof e;switch(t){case"number":case"boolean":case"string":return t;default:return angular.isDate(e)?"date":"object"}},elementWidth:function(e){},elementHeight:function(e){},isVisible:function(e){return!!(e[0].offsetWidth||e[0].offsetHeight||e[0].getClientRects().length)},getScrollbarWidth:function(){var e=document.createElement("div");e.style.visibility="hidden",e.style.width="100px",e.style.msOverflowStyle="scrollbar",document.body.appendChild(e);var t=e.offsetWidth;e.style.overflow="scroll",e.style.position="absolute";var r=document.createElement("div");r.style.width="100%",e.appendChild(r);var i=r.offsetWidth;return e.parentNode.removeChild(e),t-i},swap:function(e,t,r,i){var n,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];for(o in n=r.apply(e,i||[]),t)e.style[o]=a[o];return n},fakeElement:function(e,t,r,i){var n,o,a=angular.element(e).clone()[0];for(o in t)a.style[o]=t[o];return angular.element(document.body).append(a),n=r.call(a,a),angular.element(a).remove(),n},normalizeWheelEvent:function(e){var t,r,i,n,o,a=e||window.event,l=([].slice.call(arguments,1),0),s=0,d=0;return a.originalEvent&&(a=a.originalEvent),a.wheelDelta&&(l=a.wheelDelta),a.detail&&(l=-1*a.detail),d=l,void 0!==a.axis&&a.axis===a.HORIZONTAL_AXIS&&(d=0,s=-1*l),a.deltaY&&(l=d=-1*a.deltaY),a.deltaX&&(l=-1*(s=a.deltaX)),void 0!==a.wheelDeltaY&&(d=a.wheelDeltaY),void 0!==a.wheelDeltaX&&(s=a.wheelDeltaX),i=Math.abs(l),(!t||i<t)&&(t=i),n=Math.max(Math.abs(d),Math.abs(s)),(!r||n<r)&&(r=n),o=0<l?"floor":"ceil",{delta:l=Math[o](l/t),deltaX:s=Math[o](s/r),deltaY:d=Math[o](d/r)}},isTouchEnabled:function(){var e;return("ontouchstart"in n||n.DocumentTouch&&r instanceof DocumentTouch)&&(e=!0),e},isNullOrUndefined:function(e){return null==e},endsWith:function(e,t){return!(!e||!t||"string"!=typeof e)&&-1!==e.indexOf(t,e.length-t.length)},arrayContainsObjectWithProperty:function(e,t,r){var i=!1;return angular.forEach(e,function(e){e[t]===r&&(i=!0)}),i},numericAndNullSort:function(e,t){return null===e?1:null===t?-1:null===e&&null===t?0:e-t},disableAnimations:function(e){var t;try{t=o.get("$animate"),1<angular.version.major||1===angular.version.major&&4<=angular.version.minor?t.enabled(e,!1):t.enabled(!1,e)}catch(e){}},enableAnimations:function(e){var t;try{return t=o.get("$animate"),1<angular.version.major||1===angular.version.major&&4<=angular.version.minor?t.enabled(e,!0):t.enabled(!0,e),t}catch(e){}},nextUid:function(){for(var e,t=E.length;t;){if(57===(e=E[--t].charCodeAt(0)))return E[t]="A",A+E.join("");if(90!==e)return E[t]=String.fromCharCode(e+1),A+E.join("");E[t]="0"}return E.unshift("0"),A+E.join("")},hashKey:function(e){var t,r=typeof e;return"object"===r&&null!==e?"function"==typeof(t=e.$$hashKey)?t=e.$$hashKey():void 0!==e.$$hashKey&&e.$$hashKey?t=e.$$hashKey:void 0===t&&(t=e.$$hashKey=g.nextUid()):t=e,r+": "+t},resetUids:function(){E=["0","0","0"]},logError:function(e){c.LOG_ERROR_MESSAGES&&t.error(e)},logWarn:function(e){c.LOG_WARN_MESSAGES&&t.warn(e)},logDebug:function(){c.LOG_DEBUG_MESSAGES&&t.debug.apply(t,arguments)}};g.focus={queue:[],byId:function(r,i){this._purgeQueue();var e=s(function(){var e=(i&&i.id?i.id+"-":"")+r,t=n.document.getElementById(e);t?t.focus():g.logWarn("[focus.byId] Element id "+e+" was not found.")},0,!1);return this.queue.push(e),e},byElement:function(e){if(!angular.isElement(e))return g.logWarn("Trying to focus on an element that isn't an element."),a.reject("not-element");e=angular.element(e),this._purgeQueue();var t=s(function(){e&&e[0].focus()},0,!1);return this.queue.push(t),t},bySelector:function(t,r,e){var i=this;if(!angular.isElement(t))throw new Error("The parent element is not an element.");t=angular.element(t);var n=function(){var e=t[0].querySelector(r);return i.byElement(e)};if(this._purgeQueue(),e){var o=s(n,0,!1);return this.queue.push(o),o}return n()},_purgeQueue:function(){this.queue.forEach(function(e){s.cancel(e)}),this.queue=[]}},["width","height"].forEach(function(n){var r=n.charAt(0).toUpperCase()+n.substr(1);g["element"+r]=function(e,t){var r=e;if(r&&void 0!==r.length&&r.length&&(r=e[0]),r&&null!==r){var i=w(r);return 0===r.offsetWidth&&b.test(i.display)?g.swap(r,y,function(){return x(r,n,t)}):x(r,n,t)}return null},g["outerElement"+r]=function(e,t){return e?g["element"+r].call(this,e,t?"margin":"border"):null}}),g.closestElm=function(e,t){var r,i;for(void 0!==e.length&&e.length&&(e=e[0]),["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"].some(function(e){return"function"==typeof document.body[e]&&(r=e,!0)});null!==e;){if(null!==(i=e.parentElement)&&i[r](t))return i;e=i}return null},g.type=function(e){return Function.prototype.toString.call(e.constructor).match(/function (.*?)\(/)[1]},g.getBorderSize=function(e,t){void 0!==e.length&&e.length&&(e=e[0]);var r=w(e);t=t?"border"+t.charAt(0).toUpperCase()+t.slice(1):"border",t+="Width";var i=parseInt(r[t],10);return isNaN(i)?0:i},g.detectBrowser=function(){var e=n.navigator.userAgent,t={chrome:/chrome/i,safari:/safari/i,firefox:/firefox/i,ie:/internet explorer|trident\//i};for(var r in t)if(t[r].test(e))return r;return"unknown"},g.rtlScrollType=function e(){if(e.type)return e.type;var t=angular.element('<div dir="rtl" style="width: 1px; height: 1px; position: fixed; top: 0px; left: 0px; overflow: hidden"><div style="width: 2px"><span style="display: inline-block; width: 1px"></span><span style="display: inline-block; width: 1px"></span></div></div>')[0],r="reverse";return document.body.appendChild(t),0<t.scrollLeft?r="default":"undefined"!=typeof Element&&Element.prototype.scrollIntoView?(t.children[0].children[1].scrollIntoView(),t.scrollLeft<0&&(r="negative")):(t.scrollLeft=1,0===t.scrollLeft&&(r="negative")),angular.element(t).remove(),e.type=r},g.normalizeScrollLeft=function(e,t){void 0!==e.length&&e.length&&(e=e[0]);var r=e.scrollLeft;if(t.isRTL())switch(g.rtlScrollType()){case"default":return e.scrollWidth-r-e.clientWidth;case"negative":return Math.abs(r);case"reverse":return r}return r},g.denormalizeScrollLeft=function(e,t,r){if(void 0!==e.length&&e.length&&(e=e[0]),r.isRTL())switch(g.rtlScrollType()){case"default":return e.scrollWidth-e.clientWidth-t;case"negative":return-1*t;case"reverse":return t}return t},g.preEval=function(e){var t=c.BRACKET_REGEXP.exec(e);if(t)return(t[1]?g.preEval(t[1]):t[1])+t[2]+(t[3]?g.preEval(t[3]):t[3]);var r=(e=e.replace(c.APOS_REGEXP,"\\'")).split(c.DOT_REGEXP),i=[r.shift()];return angular.forEach(r,function(e){i.push(e.replace(c.FUNC_REGEXP,"']$1"))}),i.join("['")},g.debounce=function(t,r,i){var n,o,a,l;function e(){a=this,o=arguments;var e=i&&!n;return n&&s.cancel(n),n=s(function(){n=null,i||(l=t.apply(a,o))},r,!1),e&&(l=t.apply(a,o)),l}return e.cancel=function(){s.cancel(n),n=null},e},g.throttle=function(t,r,i){i=i||{};var n,o,a=0,l=null;function s(e){a=+new Date,t.apply(n,o),d(function(){l=null},0,1,!1)}return function(){if(n=this,o=arguments,null===l){var e=+new Date-a;r<e?s():i.trailing&&(l=d(s,r-e,1,!1))}}},g.on={},g.off={},g._events={},g.addOff=function(i){g.off[i]=function(e,t){var r=g._events[i].indexOf(t);0<r&&g._events[i].removeAt(r)}};var p,f,m="onwheel"in document||9<=document.documentMode?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"];function h(e,t){var r,i=angular.element(this),n=0,o=0,a=0;if(t.originalEvent&&(t=t.originalEvent),"detail"in t&&(a=-1*t.detail),"wheelDelta"in t&&(a=t.wheelDelta),"wheelDeltaY"in t&&(a=t.wheelDeltaY),"wheelDeltaX"in t&&(o=-1*t.wheelDeltaX),"axis"in t&&t.axis===t.HORIZONTAL_AXIS&&(o=-1*a,a=0),n=0===a?o:a,"deltaY"in t&&(n=a=-1*t.deltaY),"deltaX"in t&&(o=t.deltaX,0===a&&(n=-1*o)),0!==a||0!==o){if(1===t.deltaMode){var l=i.data("mousewheel-line-height");n*=l,a*=l,o*=l}else if(2===t.deltaMode){var s=i.data("mousewheel-page-height");n*=s,a*=s,o*=s}var d;r=Math.max(Math.abs(a),Math.abs(o)),(!f||r<f)&&(d=f=r,"mousewheel"===t.type&&d%120==0&&(f/=40)),n=Math[1<=n?"floor":"ceil"](n/f),o=Math[1<=o?"floor":"ceil"](o/f),a=Math[1<=a?"floor":"ceil"](a/f);var c={originalEvent:t,deltaX:o,deltaY:a,deltaFactor:f,preventDefault:function(){t.preventDefault()},stopPropagation:function(){t.stopPropagation()}};p&&clearTimeout(p),p=setTimeout(v,200),e.call(i[0],c)}}function v(){f=null}return g.on.mousewheel=function(e,t){if(e&&t){var r,i,n=angular.element(e);n.data("mousewheel-line-height",(r=n,(i=(r=angular.element(r)[0]).parentElement)||(i=document.getElementsByTagName("body")[0]),parseInt(w(i).fontSize)||parseInt(w(r).fontSize)||16)),n.data("mousewheel-page-height",g.elementHeight(n)),n.data("mousewheel-callbacks")||n.data("mousewheel-callbacks",{});var o=n.data("mousewheel-callbacks");o[t]=(Function.prototype.bind||C).call(h,n[0],t);for(var a=m.length;a;)n.on(m[--a],o[t]);n.on("$destroy",function(){for(var e=m.length;e;)n.off(m[--e],o[t])})}},g.off.mousewheel=function(e,t){var r=angular.element(e),i=r.data("mousewheel-callbacks"),n=i[t];if(n)for(var o=m.length;o;)r.off(m[--o],n);delete i[t],0===Object.keys(i).length&&(r.removeData("mousewheel-line-height"),r.removeData("mousewheel-page-height"),r.removeData("mousewheel-callbacks"))},g}]),e.filter("px",function(){return function(e){return e.match(/^[\d\.]+$/)?e+"px":e}})}(),function(){var g=["uiT","uiTranslate"],t=angular.module("ui.grid.i18n");function r(c,u){return{restrict:"EA",compile:function(){return{pre:function(e,r,t){var i,n,o,a=g[0],l=g[1],s=t[a]||t[l]||r.html();function d(e){var t=c.getSafeText(e);r.html(t)}t.$$observers&&(o=t[a]?a:l,n=t.$observe(o,function(e){e&&d(e)})),i=e.$on(u.UPDATE_EVENT,function(){n?n(t[a]||t[l]):d(s)}),e.$on("$destroy",i),d(s)}}}}}function i(r){return function(e,t){return r.getSafeText(e,t)}}t.constant("i18nConstants",{MISSING:"[MISSING]",UPDATE_EVENT:"$uiI18n",LOCALE_DIRECTIVE_ALIAS:"uiI18n",DEFAULT_LANG:"en"}),t.service("i18nService",["$log","$parse","i18nConstants","$rootScope",function(t,o,r,i){var a={_langs:{},current:null,fallback:r.DEFAULT_LANG,get:function(e){var t=this,r=t.getFallbackLang();return e!==t.fallback?angular.extend({},t._langs[r],t._langs[e.toLowerCase()]):t._langs[e.toLowerCase()]},add:function(e,t){var r=e.toLowerCase();this._langs[r]||(this._langs[r]={}),angular.merge(this._langs[r],t)},getAllLangs:function(){var e=[];if(!this._langs)return e;for(var t in this._langs)e.push(t);return e},setCurrent:function(e){this.current=e.toLowerCase()},setFallback:function(e){this.fallback=e.toLowerCase()},getCurrentLang:function(){return this.current},getFallbackLang:function(){return this.fallback.toLowerCase()}};function l(e){return t.warn(r.MISSING+e),""}var s={add:function(e,t){"object"==typeof e?angular.forEach(e,function(e){e&&a.add(e,t)}):a.add(e,t)},getAllLangs:function(){return a.getAllLangs()},get:function(e){var t=e||s.getCurrentLang();return a.get(t)},getSafeText:function(e,t){var r=t||s.getCurrentLang(),i=a.get(r),n=o(e);return i&&n(i)||l(e)},setCurrentLang:function(e){e&&(a.setCurrent(e),i.$broadcast(r.UPDATE_EVENT))},setFallbackLang:function(e){e&&a.setFallback(e)},getCurrentLang:function(){var e=a.getCurrentLang();return e||(e=r.DEFAULT_LANG,a.setCurrent(e)),e},getFallbackLang:function(){return a.getFallbackLang()}};return s}]),t.directive("uiI18n",["i18nService","i18nConstants",function(o,a){return{compile:function(){return{pre:function(e,t,r){var i=a.LOCALE_DIRECTIVE_ALIAS,n=e.$eval(r[i]);n?e.$watch(r[i],function(){o.setCurrentLang(n)}):r.$$observers&&r.$observe(i,function(){o.setCurrentLang(r[i]||a.DEFAULT_LANG)})}}}}}]),angular.forEach(g,function(e){t.directive(e,["i18nService","i18nConstants",r])}),angular.forEach(["t","uiTranslate"],function(e){t.filter(e,["i18nService",i])})}(),function(){"use strict";var e=angular.module("ui.grid.edit",["ui.grid"]);e.constant("uiGridEditConstants",{EDITABLE_CELL_TEMPLATE:/EDITABLE_CELL_TEMPLATE/g,EDITABLE_CELL_DIRECTIVE:/editable_cell_directive/g,events:{BEGIN_CELL_EDIT:"uiGridEventBeginCellEdit",END_CELL_EDIT:"uiGridEventEndCellEdit",CANCEL_CELL_EDIT:"uiGridEventCancelCellEdit"}}),e.service("uiGridEditService",["$q","uiGridConstants","gridUtil",function(n,t,o){var r={initializeGrid:function(e){r.defaultGridOptions(e.options),e.registerColumnBuilder(r.editColumnBuilder),e.edit={};e.api.registerEventsFromObject({edit:{afterCellEdit:function(e,t,r,i){},beginCellEdit:function(e,t,r){},cancelCellEdit:function(e,t){}}})},defaultGridOptions:function(e){e.cellEditableCondition=void 0===e.cellEditableCondition||e.cellEditableCondition,e.enableCellEditOnFocus=void 0!==e.enableCellEditOnFocus&&e.enableCellEditOnFocus},editColumnBuilder:function(t,r,e){var i=[];return t.enableCellEdit=void 0===t.enableCellEdit?void 0===e.enableCellEdit?"object"!==t.type:e.enableCellEdit:t.enableCellEdit,t.cellEditableCondition=void 0===t.cellEditableCondition?e.cellEditableCondition:t.cellEditableCondition,t.enableCellEdit&&(t.editableCellTemplate=t.editableCellTemplate||e.editableCellTemplate||"ui-grid/cellEditor",i.push(o.getTemplate(t.editableCellTemplate).then(function(e){r.editableCellTemplate=e},function(e){throw new Error("Couldn't fetch/use colDef.editableCellTemplate '"+t.editableCellTemplate+"'")}))),t.enableCellEditOnFocus=void 0===t.enableCellEditOnFocus?e.enableCellEditOnFocus:t.enableCellEditOnFocus,n.all(i)},isStartEditKey:function(e){return!(e.metaKey||e.keyCode===t.keymap.ESC||e.keyCode===t.keymap.SHIFT||e.keyCode===t.keymap.CTRL||e.keyCode===t.keymap.ALT||e.keyCode===t.keymap.WIN||e.keyCode===t.keymap.CAPSLOCK||e.keyCode===t.keymap.LEFT||e.keyCode===t.keymap.TAB&&e.shiftKey||e.keyCode===t.keymap.RIGHT||e.keyCode===t.keymap.TAB||e.keyCode===t.keymap.UP||e.keyCode===t.keymap.ENTER&&e.shiftKey||e.keyCode===t.keymap.DOWN||e.keyCode===t.keymap.ENTER)}};return r}]),e.directive("uiGridEdit",["gridUtil","uiGridEditService",function(e,n){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){n.initializeGrid(i.grid)},post:function(e,t,r,i){}}}}}]),e.directive("uiGridViewport",["uiGridEditConstants",function(o){return{replace:!0,priority:-99998,require:["^uiGrid","^uiGridRenderContainer"],scope:!1,compile:function(){return{post:function(e,t,r,i){var n=i[0];n.grid.api.edit&&n.grid.api.cellNav&&("body"===i[1].containerId&&(e.$on(o.events.CANCEL_CELL_EDIT,function(){n.focus()}),e.$on(o.events.END_CELL_EDIT,function(){n.focus()})))}}}}}]),e.directive("uiGridCell",["$compile","$injector","$timeout","uiGridConstants","uiGridEditConstants","gridUtil","$parse","uiGridEditService","$rootScope","$q",function(S,e,x,E,A,R,F,g,T,P){if(e.has("uiGridCellNavService"))e.get("uiGridCellNavService");return{priority:-100,restrict:"A",scope:!1,require:"?^uiGrid",link:function(p,f,e,i){var m,h,v,t,C,w=!1;if(p.col.colDef.enableCellEdit){var r=function(){},n=function(){},o=function(){p.col.colDef.enableCellEdit&&!1!==p.row.enableCellEdit?p.beginEditEventsWired||l():p.beginEditEventsWired&&b()};o();var a=p.$watch("row",function(e,t){e!==t&&o()});p.$on("$destroy",function(){a(),f.off()})}function l(){f.on("dblclick",u),f.on("touchstart",s),i&&i.grid.api.cellNav&&(n=i.grid.api.cellNav.on.viewPortKeyDown(p,function(e,t){null!==t&&(t.row!==p.row||t.col!==p.col||p.col.colDef.enableCellEditOnFocus||c(e))}),r=i.grid.api.cellNav.on.navigate(p,function(e,t,r){p.col.colDef.enableCellEditOnFocus&&(e.row!==p.row||e.col!==p.col||null!==r&&(!r||"click"!==r.type&&"keydown"!==r.type)||x(function(){u(r)}))})),p.beginEditEventsWired=!0}function s(e){void 0!==e.originalEvent&&void 0!==e.originalEvent&&(e=e.originalEvent),f.on("touchend",d),(t=x(function(){},500)).then(function(){setTimeout(u,0),f.off("touchend",d)}).catch(angular.noop)}function d(){x.cancel(t),f.off("touchend",d)}function b(){f.off("dblclick",u),f.off("keydown",c),f.off("touchstart",s),r(),n(),p.beginEditEventsWired=!1}function c(e){g.isStartEditKey(e)&&u(e)}function u(e){p.grid.api.core.scrollToIfNecessary(p.row,p.col).then(function(){!function(e){if(w)return;if(t=p.col,r=p.row,i=e,r.isSaving||(angular.isFunction(t.colDef.cellEditableCondition)?!t.colDef.cellEditableCondition(p,i):!t.colDef.cellEditableCondition))return;var t,r,i;var n=p.row.getQualifiedColField(p.col);p.col.colDef.editModelField&&(n=R.preEval("row.entity."+p.col.colDef.editModelField));v=F(n),h=v(p),m=(m=(m=p.col.editableCellTemplate).replace(E.MODEL_COL_FIELD,n)).replace(E.COL_FIELD,"grid.getCellValue(row, col)");var o=p.col.colDef.editDropdownFilter?"|"+p.col.colDef.editDropdownFilter:"";m=m.replace(E.CUSTOM_FILTERS,o);var a="text";switch(p.col.colDef.type){case"boolean":a="checkbox";break;case"number":a="number";break;case"date":a="date"}m=m.replace("INPUT_TYPE",a);var l=p.col.colDef.editDropdownOptionsFunction;if(l)P.when(l(p.row.entity,p.col.colDef)).then(function(e){p.editDropdownOptionsArray=e});else{var s=p.col.colDef.editDropdownRowEntityOptionsArrayPath;p.editDropdownOptionsArray=s?function(e,t){var r=(t=(t=t.replace(/\[(\w+)\]/g,".$1")).replace(/^\./,"")).split(".");for(;r.length;){var i=r.shift();if(!(i in e))return;e=e[i]}return e}(p.row.entity,s):p.col.colDef.editDropdownOptionsArray}p.editDropdownIdLabel=p.col.colDef.editDropdownIdLabel?p.col.colDef.editDropdownIdLabel:"id",p.editDropdownValueLabel=p.col.colDef.editDropdownValueLabel?p.col.colDef.editDropdownValueLabel:"value";var d=function(){w=!0,b();var e=angular.element(m);f.append(e),C=p.$new(),S(e)(C);var t=angular.element(f.children()[0]);t.addClass("ui-grid-cell-contents-hidden")};T.$$phase?d():p.$apply(d);var c=p.col.grid.api.core.on.scrollBegin(p,function(){p.grid.disableScrolling||(y(),p.grid.api.edit.raise.afterCellEdit(p.row.entity,p.col.colDef,v(p),h),c(),u(),g())}),u=p.$on(A.events.END_CELL_EDIT,function(){y(),p.grid.api.edit.raise.afterCellEdit(p.row.entity,p.col.colDef,v(p),h),u(),c(),g()}),g=p.$on(A.events.CANCEL_CELL_EDIT,function(){!function(){if(p.grid.disableScrolling=!1,!w)return;v.assign(p,h),p.$apply(),p.grid.api.edit.raise.cancelCellEdit(p.row.entity,p.col.colDef),y()}(),g(),c(),u()});p.$broadcast(A.events.BEGIN_CELL_EDIT,e),x(function(){p.grid.api.edit.raise.beginCellEdit(p.row.entity,p.col.colDef,e)})}(e)})}function y(){if(p.grid.disableScrolling=!1,w){i&&i.grid.api.cellNav&&i.focus();var e=angular.element(f.children()[0]);C.$destroy();for(var t=f.children(),r=1;r<t.length;r++)angular.element(t[r]).remove();e.removeClass("ui-grid-cell-contents-hidden"),w=!1,l(),p.grid.api.core.notifyDataChange(E.dataChange.EDIT)}}}}}]),e.directive("uiGridEditor",["gridUtil","uiGridConstants","uiGridEditConstants","$timeout","uiGridEditService",function(e,l,s,d,c){return{scope:!0,require:["?^uiGrid","?^uiGridRenderContainer","ngModel"],compile:function(){return{pre:function(e,t,r){},post:function(t,r,e,i){var n,o,a;i[0]&&(n=i[0]),i[1]&&(o=i[1]),i[2]&&(a=i[2]),t.$on(s.events.BEGIN_CELL_EDIT,function(){if(d(function(){if(r[0].focus(),!r[0].select||!t.col.colDef.enableCellEditOnFocus&&n&&n.grid.api.cellNav)try{r[0].setSelectionRange(r[0].value.length,r[0].value.length)}catch(e){}else r[0].select()}),n&&n.grid.api.cellNav)var i=n.grid.api.cellNav.on.viewPortKeyPress(t,function(e,t){if(c.isStartEditKey(e)){var r="number"==typeof e.which?e.which:e.keyCode;0<r&&(a.$setViewValue(String.fromCharCode(r),e),a.$render())}i()});r.on("mousedown",function(e){"checkbox"===r[0].type&&(r.off("blur",t.stopEdit),d(function(){r[0].focus(),r.on("blur",t.stopEdit)}))}),r[0]&&r[0].focus(),r.on("blur",t.stopEdit)}),t.deepEdit=!1,t.stopEdit=function(e){t.inputForm&&!t.inputForm.$valid?(e.stopPropagation(),t.$emit(s.events.CANCEL_CELL_EDIT)):t.$emit(s.events.END_CELL_EDIT),t.deepEdit=!1},r.on("click",function(e){"checkbox"!==r[0].type&&(t.deepEdit=!0,t.$applyAsync(function(){t.grid.disableScrolling=!0}))}),r.on("keydown",function(e){switch(e.keyCode){case l.keymap.ESC:e.stopPropagation(),t.$emit(s.events.CANCEL_CELL_EDIT)}if(!t.deepEdit||e.keyCode!==l.keymap.LEFT&&e.keyCode!==l.keymap.RIGHT&&e.keyCode!==l.keymap.UP&&e.keyCode!==l.keymap.DOWN)if(n&&n.grid.api.cellNav)e.uiGridTargetRenderContainerId=o.containerId,null!==n.cellNav.handleKeyDown(e)&&t.stopEdit(e);else switch(e.keyCode){case l.keymap.ENTER:case l.keymap.TAB:e.stopPropagation(),e.preventDefault(),t.stopEdit(e)}else e.stopPropagation();return!0}),t.$on("$destroy",function(){r.off()})}}}}}]),e.directive("uiGridEditor",["$filter",function(n){return{priority:-100,require:"?ngModel",link:function(e,t,r,i){2===angular.version.minor&&r.type&&"date"===r.type&&i&&(i.$formatters.push(function(e){return i.$setValidity(null,!e||!isNaN(e.getTime())),n("date")(e,"yyyy-MM-dd")}),i.$parsers.push(function(e){if(e&&0<e.length){var t=function(e){if(void 0===e||""===e)return null;var t=e.split("-");if(3!==t.length)return null;var r=parseInt(t[0],10),i=parseInt(t[1],10),n=parseInt(t[2],10);return i<1||r<1||n<1?null:new Date(r,i-1,n)}(e);return i.$setValidity(null,t&&!isNaN(t.getTime())),t}return i.$setValidity(null,!0),null}))}}}]),e.directive("uiGridEditDropdown",["uiGridConstants","uiGridEditConstants","$timeout",function(a,l,s){return{require:["?^uiGrid","?^uiGridRenderContainer"],scope:!0,compile:function(){return{pre:function(e,t,r){},post:function(t,e,r,i){var n=i[0],o=i[1];t.$on(l.events.BEGIN_CELL_EDIT,function(){s(function(){e[0].focus()}),e[0].style.width=e[0].parentElement.offsetWidth-1+"px",e.on("blur",function(e){t.stopEdit(e)})}),t.stopEdit=function(e){t.$emit(l.events.END_CELL_EDIT)},e.on("keydown",function(e){switch(e.keyCode){case a.keymap.ESC:e.stopPropagation(),t.$emit(l.events.CANCEL_CELL_EDIT)}if(n&&n.grid.api.cellNav)e.uiGridTargetRenderContainerId=o.containerId,null!==n.cellNav.handleKeyDown(e)&&t.stopEdit(e);else switch(e.keyCode){case a.keymap.ENTER:case a.keymap.TAB:e.stopPropagation(),e.preventDefault(),t.stopEdit(e)}return!0}),t.$on("$destroy",function(){e.off()})}}}}}]),e.directive("uiGridEditFileChooser",["gridUtil","uiGridConstants","uiGridEditConstants",function(o,e,a){return{scope:!0,require:["?^uiGrid","?^uiGridRenderContainer"],compile:function(){return{pre:function(e,t,r){},post:function(i,n){n[0].addEventListener("change",function e(t){var r=t.srcElement||t.target;r&&r.files&&0<r.files.length?("function"==typeof i.col.colDef.editFileChooserCallback?i.col.colDef.editFileChooserCallback(i.row,i.col,r.files):o.logError("You need to set colDef.editFileChooserCallback to use the file chooser"),r.form.reset(),i.$emit(a.events.END_CELL_EDIT)):i.$emit(a.events.CANCEL_CELL_EDIT),n[0].removeEventListener("change",e,!1)},!1),i.$on(a.events.BEGIN_CELL_EDIT,function(){n[0].focus(),n[0].select(),n.on("blur",function(){i.$emit(a.events.END_CELL_EDIT),n.off()})})}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.emptyBaseLayer",["ui.grid"]);e.service("uiGridBaseLayerService",["gridUtil","$compile",function(e,t){return{initializeGrid:function(e,t){!(e.baseLayer={emptyRows:[]})!==e.options.enableEmptyGridBaseLayer&&(e.options.enableEmptyGridBaseLayer=!t)},setNumberOfEmptyRows:function(e,t){var r=t.options.rowHeight,i=Math.ceil(e/r);if(0<i){t.baseLayer.emptyRows=[];for(var n=0;n<i;n++)t.baseLayer.emptyRows.push({})}}}}]),e.directive("uiGridEmptyBaseLayer",["gridUtil","uiGridBaseLayerService","$parse",function(e,a,o){return{require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){var n=!1===o(r.uiGridEmptyBaseLayer)(e);a.initializeGrid(i.grid,n)},post:function(e,t,r,i){if(i.grid.options.enableEmptyGridBaseLayer){var n=i.grid.renderContainers.body,o=n.getViewportHeight();i.grid.registerStyleComputation({func:function(){var e,t;return(e=n.getViewportHeight())!==o&&(o=e,1)&&a.setNumberOfEmptyRows(o,i.grid),t=o,".grid"+i.grid.id+" .ui-grid-render-container .ui-grid-empty-base-layer-container.ui-grid-canvas { height: "+t+"px; }"}})}}}}}}]),e.directive("uiGridViewport",["$compile","gridUtil","$templateCache",function(e,t,r){return{priority:-200,scope:!1,compile:function(e){var t=r.get("ui-grid/emptyBaseLayerContainer");return e.prepend(t),{pre:function(e,t,r,i){},post:function(e,t,r,i){}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.expandable",["ui.grid"]);e.service("uiGridExpandableService",["gridUtil",function(t){var n={initializeGrid:function(i){i.expandable={},i.expandable.expandedAll=!1,i.options.enableOnDblClickExpand=!1!==i.options.enableOnDblClickExpand,i.options.enableExpandable=!1!==i.options.enableExpandable,i.options.showExpandAllButton=!1!==i.options.showExpandAllButton,i.options.expandableRowHeight=i.options.expandableRowHeight||150,i.options.expandableRowHeaderWidth=i.options.expandableRowHeaderWidth||40,i.options.enableExpandable&&!i.options.expandableRowTemplate&&(t.logError("You have not set the expandableRowTemplate, disabling expandable module"),i.options.enableExpandable=!1);var e={events:{expandable:{rowExpandedBeforeStateChanged:function(e,t,r){},rowExpandedStateChanged:function(e,t,r){},rowExpandedRendered:function(e,t,r){}}},methods:{expandable:{toggleRowExpansion:function(e,t){var r=i.getRow(e);null!==r&&n.toggleRowExpansion(i,r,t)},expandAllRows:function(){n.expandAllRows(i)},collapseAllRows:function(){n.collapseAllRows(i)},toggleAllRows:function(){n.toggleAllRows(i)},expandRow:function(e){var t=i.getRow(e);null===t||t.isExpanded||n.toggleRowExpansion(i,t)},collapseRow:function(e){var t=i.getRow(e);null!==t&&t.isExpanded&&n.toggleRowExpansion(i,t)},getExpandedRows:function(){return n.getExpandedRows(i).map(function(e){return e.entity})}}}};i.api.registerEventsFromObject(e.events),i.api.registerMethodsFromObject(e.methods)},toggleRowExpansion:function(t,r,i){t.api.expandable.raise.rowExpandedBeforeStateChanged(r),r.isExpanded=!r.isExpanded,angular.isUndefined(r.expandedRowHeight)&&(r.expandedRowHeight=t.options.expandableRowHeight),r.isExpanded?(r.height=r.grid.options.rowHeight+r.expandedRowHeight,t.expandable.expandedAll=n.getExpandedRows(t).length===t.rows.length):(r.height=r.grid.options.rowHeight,t.expandable.expandedAll=!1),t.api.expandable.raise.rowExpandedStateChanged(r,i),function e(){r.expandedRendered?t.api.expandable.raise.rowExpandedRendered(r,i):window.setTimeout(e,100)}()},expandAllRows:function(t){t.renderContainers.body.visibleRowCache.forEach(function(e){e.isExpanded||e.entity.subGridOptions&&e.entity.subGridOptions.disableRowExpandable||n.toggleRowExpansion(t,e)}),t.expandable.expandedAll=!0,t.queueGridRefresh()},collapseAllRows:function(t){t.renderContainers.body.visibleRowCache.forEach(function(e){e.isExpanded&&n.toggleRowExpansion(t,e)}),t.expandable.expandedAll=!1,t.queueGridRefresh()},toggleAllRows:function(e){e.expandable.expandedAll?n.collapseAllRows(e):n.expandAllRows(e)},getExpandedRows:function(e){return e.rows.filter(function(e){return e.isExpanded})}};return n}]),e.directive("uiGridExpandable",["uiGridExpandableService","$templateCache",function(o,a){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){if(o.initializeGrid(i.grid),i.grid.options.enableExpandable&&!1!==i.grid.options.enableExpandableRowHeader){var n={name:"expandableButtons",displayName:"",exporterSuppressExport:!0,enableColumnResizing:!1,enableColumnMenu:!1,width:i.grid.options.expandableRowHeaderWidth||30};n.cellTemplate=a.get("ui-grid/expandableRowHeader"),n.headerCellTemplate=a.get("ui-grid/expandableTopRowHeader"),i.grid.addRowHeaderColumn(n,-90)}},post:function(e,t,r,i){}}}}}]),e.directive("uiGrid",function(){return{replace:!0,priority:599,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){i.grid.api.core.on.renderingComplete(e,function(){e.row&&e.row.grid&&e.row.grid.options&&e.row.grid.options.enableExpandable&&(i.grid.parentRow=e.row)})},post:function(e,t,r,i){}}}}}),e.directive("uiGridExpandableRow",["uiGridExpandableService","$compile","uiGridConstants","gridUtil",function(e,a,t,r){return{replace:!1,priority:0,scope:!1,compile:function(){return{pre:function(n,o){r.getTemplate(n.grid.options.expandableRowTemplate).then(function(e){if(n.grid.options.expandableRowScope){var t=n.grid.options.expandableRowScope;for(var r in t)t.hasOwnProperty(r)&&(n[r]=t[r])}var i=angular.element(e);i=a(i)(n),o.append(i),n.row.element=o,n.row.expandedRendered=!0})},post:function(e,t){e.row.element=t,e.$on("$destroy",function(){e.row.expandedRendered=!1})}}}}}]),e.directive("uiGridRow",function(){return{priority:-200,scope:!1,compile:function(){return{pre:function(t,e){t.grid.options.enableExpandable&&(t.expandableRow={},t.expandableRow.shouldRenderExpand=function(){return"body"===t.colContainer.name&&!1!==t.grid.options.enableExpandable&&t.row.isExpanded&&(!t.grid.isScrollingVertically||t.row.expandedRendered)},t.expandableRow.shouldRenderFiller=function(){return t.row.isExpanded&&("body"!==t.colContainer.name||t.grid.isScrollingVertically&&!t.row.expandedRendered)},t.grid.options.enableOnDblClickExpand&&e.on("dblclick",function(e){t.grid.api.expandable.toggleRowExpansion(t.row.entity,e)}))},post:function(e,t,r,i){}}}}}),e.directive("uiGridViewport",["$compile","gridUtil","$templateCache",function(e,t,n){return{priority:-200,scope:!1,compile:function(e){var t=angular.element(e.children().children()[0]),r=n.get("ui-grid/expandableScrollFiller"),i=n.get("ui-grid/expandableRow");return t.append(i),t.append(r),{pre:function(e,t,r,i){},post:function(e,t,r,i){}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.exporter",["ui.grid"]);e.constant("uiGridExporterConstants",{featureName:"exporter",rowHeaderColName:"treeBaseRowHeaderCol",selectionRowHeaderColName:"selectionRowHeaderCol",ALL:"all",VISIBLE:"visible",SELECTED:"selected",CSV_CONTENT:"CSV_CONTENT",BUTTON_LABEL:"BUTTON_LABEL",FILE_NAME:"FILE_NAME"}),e.service("uiGridExporterService",["$filter","$q","uiGridExporterConstants","gridUtil","$compile","$interval","i18nService",function(s,n,p,c,e,t,r){var i={delay:100,initializeGrid:function(r){r.exporter={},this.defaultGridOptions(r.options);var e={events:{exporter:{}},methods:{exporter:{csvExport:function(e,t){i.csvExport(r,e,t)},pdfExport:function(e,t){i.pdfExport(r,e,t)},excelExport:function(e,t){i.excelExport(r,e,t)}}}};r.api.registerEventsFromObject(e.events),r.api.registerMethodsFromObject(e.methods),r.api.core.addToGridMenu?i.addToMenu(r):t(function(){r.api.core.addToGridMenu&&i.addToMenu(r)},this.delay,1)},defaultGridOptions:function(e){e.exporterSuppressMenu=!0===e.exporterSuppressMenu,e.exporterMenuLabel=e.exporterMenuLabel?e.exporterMenuLabel:"Export",e.exporterSuppressColumns=e.exporterSuppressColumns?e.exporterSuppressColumns:[],e.exporterCsvColumnSeparator=e.exporterCsvColumnSeparator?e.exporterCsvColumnSeparator:",",e.exporterCsvFilename=e.exporterCsvFilename?e.exporterCsvFilename:"download.csv",e.exporterPdfFilename=e.exporterPdfFilename?e.exporterPdfFilename:"download.pdf",e.exporterExcelFilename=e.exporterExcelFilename?e.exporterExcelFilename:"download.xlsx",e.exporterExcelSheetName=e.exporterExcelSheetName?e.exporterExcelSheetName:"Sheet1",e.exporterOlderExcelCompatibility=!0===e.exporterOlderExcelCompatibility,e.exporterIsExcelCompatible=!0===e.exporterIsExcelCompatible,e.exporterMenuItemOrder=e.exporterMenuItemOrder?e.exporterMenuItemOrder:200,e.exporterPdfDefaultStyle=e.exporterPdfDefaultStyle?e.exporterPdfDefaultStyle:{fontSize:11},e.exporterPdfTableStyle=e.exporterPdfTableStyle?e.exporterPdfTableStyle:{margin:[0,5,0,15]},e.exporterPdfTableHeaderStyle=e.exporterPdfTableHeaderStyle?e.exporterPdfTableHeaderStyle:{bold:!0,fontSize:12,color:"black"},e.exporterPdfHeader=e.exporterPdfHeader?e.exporterPdfHeader:null,e.exporterPdfFooter=e.exporterPdfFooter?e.exporterPdfFooter:null,e.exporterPdfOrientation=e.exporterPdfOrientation?e.exporterPdfOrientation:"landscape",e.exporterPdfPageSize=e.exporterPdfPageSize?e.exporterPdfPageSize:"A4",e.exporterPdfMaxGridWidth=e.exporterPdfMaxGridWidth?e.exporterPdfMaxGridWidth:720,e.exporterMenuAllData=void 0===e.exporterMenuAllData||e.exporterMenuAllData,e.exporterMenuVisibleData=void 0===e.exporterMenuVisibleData||e.exporterMenuVisibleData,e.exporterMenuSelectedData=void 0===e.exporterMenuSelectedData||e.exporterMenuSelectedData,e.exporterMenuCsv=void 0===e.exporterMenuCsv||e.exporterMenuCsv,e.exporterMenuPdf=void 0===e.exporterMenuPdf||e.exporterMenuPdf,e.exporterMenuExcel=void 0===e.exporterMenuExcel||e.exporterMenuExcel,e.exporterPdfCustomFormatter=e.exporterPdfCustomFormatter&&"function"==typeof e.exporterPdfCustomFormatter?e.exporterPdfCustomFormatter:function(e){return e},e.exporterHeaderFilterUseName=!0===e.exporterHeaderFilterUseName,e.exporterFieldCallback=e.exporterFieldCallback?e.exporterFieldCallback:o,e.exporterFieldFormatCallback=e.exporterFieldFormatCallback?e.exporterFieldFormatCallback:function(e,t,r,i){return null},e.exporterExcelCustomFormatters=e.exporterExcelCustomFormatters?e.exporterExcelCustomFormatters:function(e,t,r){return r},e.exporterExcelHeader=e.exporterExcelHeader?e.exporterExcelHeader:function(e,t,r,i){return null},e.exporterColumnScaleFactor=e.exporterColumnScaleFactor?e.exporterColumnScaleFactor:3.5,e.exporterFieldApplyFilters=!0===e.exporterFieldApplyFilters,e.exporterAllDataFn=e.exporterAllDataFn?e.exporterAllDataFn:null,null===e.exporterAllDataFn&&e.exporterAllDataPromise&&(e.exporterAllDataFn=e.exporterAllDataPromise)},addToMenu:function(e){e.api.core.addToGridMenu(e,[{title:r.getSafeText("gridMenu.exporterAllAsCsv"),action:function(){e.api.exporter.csvExport(p.ALL,p.ALL)},shown:function(){return e.options.exporterMenuCsv&&e.options.exporterMenuAllData},order:e.options.exporterMenuItemOrder},{title:r.getSafeText("gridMenu.exporterVisibleAsCsv"),action:function(){e.api.exporter.csvExport(p.VISIBLE,p.VISIBLE)},shown:function(){return e.options.exporterMenuCsv&&e.options.exporterMenuVisibleData},order:e.options.exporterMenuItemOrder+1},{title:r.getSafeText("gridMenu.exporterSelectedAsCsv"),action:function(){e.api.exporter.csvExport(p.SELECTED,p.VISIBLE)},shown:function(){return e.options.exporterMenuCsv&&e.options.exporterMenuSelectedData&&e.api.selection&&0<e.api.selection.getSelectedRows().length},order:e.options.exporterMenuItemOrder+2},{title:r.getSafeText("gridMenu.exporterAllAsPdf"),action:function(){e.api.exporter.pdfExport(p.ALL,p.ALL)},shown:function(){return e.options.exporterMenuPdf&&e.options.exporterMenuAllData},order:e.options.exporterMenuItemOrder+3},{title:r.getSafeText("gridMenu.exporterVisibleAsPdf"),action:function(){e.api.exporter.pdfExport(p.VISIBLE,p.VISIBLE)},shown:function(){return e.options.exporterMenuPdf&&e.options.exporterMenuVisibleData},order:e.options.exporterMenuItemOrder+4},{title:r.getSafeText("gridMenu.exporterSelectedAsPdf"),action:function(){e.api.exporter.pdfExport(p.SELECTED,p.VISIBLE)},shown:function(){return e.options.exporterMenuPdf&&e.options.exporterMenuSelectedData&&e.api.selection&&0<e.api.selection.getSelectedRows().length},order:e.options.exporterMenuItemOrder+5},{title:r.getSafeText("gridMenu.exporterAllAsExcel"),action:function(){e.api.exporter.excelExport(p.ALL,p.ALL)},shown:function(){return e.options.exporterMenuExcel&&e.options.exporterMenuAllData},order:e.options.exporterMenuItemOrder+6},{title:r.getSafeText("gridMenu.exporterVisibleAsExcel"),action:function(){e.api.exporter.excelExport(p.VISIBLE,p.VISIBLE)},shown:function(){return e.options.exporterMenuExcel&&e.options.exporterMenuVisibleData},order:e.options.exporterMenuItemOrder+7},{title:r.getSafeText("gridMenu.exporterSelectedAsExcel"),action:function(){e.api.exporter.excelExport(p.SELECTED,p.VISIBLE)},shown:function(){return e.options.exporterMenuExcel&&e.options.exporterMenuSelectedData&&e.api.selection&&0<e.api.selection.getSelectedRows().length},order:e.options.exporterMenuItemOrder+8}])},csvExport:function(n,o,a){var l=this;this.loadAllDataIfNeeded(n,o,a).then(function(){var e=n.options.showHeader?l.getColumnHeaders(n,a):[],t=l.getData(n,o,a),r=l.formatAsCsv(e,t,n.options.exporterCsvColumnSeparator),i=angular.isFunction(n.options.exporterCsvFilename)?n.options.exporterCsvFilename(n,o,a):n.options.exporterCsvFilename;l.downloadFile(i,r,n.options.exporterCsvColumnSeparator,n.options.exporterOlderExcelCompatibility,n.options.exporterIsExcelCompatible)})},loadAllDataIfNeeded:function(t,e,r){if(e===p.ALL&&t.rows.length!==t.options.totalItems&&t.options.exporterAllDataFn)return t.options.exporterAllDataFn().then(function(e){t.modifyRows(e)});var i=n.defer();return i.resolve(),i.promise},getColumnHeaders:function(r,e){var t,i=[];if(e===p.ALL)t=r.columns;else{var n=r.renderContainers.left?r.renderContainers.left.visibleColumnCache.filter(function(e){return e.visible}):[],o=r.renderContainers.body?r.renderContainers.body.visibleColumnCache.filter(function(e){return e.visible}):[],a=r.renderContainers.right?r.renderContainers.right.visibleColumnCache.filter(function(e){return e.visible}):[];t=n.concat(o,a)}return t.forEach(function(e){if(!0!==e.colDef.exporterSuppressExport&&"$$hashKey"!==e.field&&-1===r.options.exporterSuppressColumns.indexOf(e.name)){var t={name:e.field,displayName:function(e,t){if(e.options.exporterHeaderFilter)return e.options.exporterHeaderFilterUseName?e.options.exporterHeaderFilter(t.name):e.options.exporterHeaderFilter(t.displayName);return t.headerCellFilter?s(t.headerCellFilter)(t.displayName):t.displayName}(r,e),width:e.drawnWidth?e.drawnWidth:e.width,align:e.colDef.align?e.colDef.align:"number"===e.colDef.type?"right":"left"};i.push(t)}}),i},getRowsFromNode:function(e){var t=[],r=e?Object.keys(e):["children"];if((1<r.length||"children"!=r[0])&&t.push(e),e&&e.children&&0<e.children.length)for(var i=0;i<e.children.length;i++)t=t.concat(this.getRowsFromNode(e.children[i]));return t},getDataSorted:function(e){if(!e.treeBase||0===e.treeBase.numberLevels)return e.rows;for(var t=[],r=0;r<e.treeBase.tree.length;r++)for(var i=this.getRowsFromNode(e.treeBase.tree[r]),n=0;n<i.length;n++)t.push(i[n].row);return t},getData:function(a,e,l,s){var t,r,i=[];switch(e){case p.ALL:t=this.getDataSorted(a,e,l,s);break;case p.VISIBLE:t=a.getVisibleRows();break;case p.SELECTED:a.api.selection?t=a.api.selection.getSelectedGridRows():c.logError("selection feature must be enabled to allow selected rows to be exported")}if(l===p.ALL)r=a.columns;else{var n=a.renderContainers.left?a.renderContainers.left.visibleColumnCache.filter(function(e){return e.visible}):[],o=a.renderContainers.body?a.renderContainers.body.visibleColumnCache.filter(function(e){return e.visible}):[],d=a.renderContainers.right?a.renderContainers.right.visibleColumnCache.filter(function(e){return e.visible}):[];r=n.concat(o,d)}return t.forEach(function(n){if(!1!==n.exporterEnableExporting){var o=[];r.forEach(function(e){if((e.visible||l===p.ALL)&&!0!==e.colDef.exporterSuppressExport&&"$$hashKey"!==e.field&&-1===a.options.exporterSuppressColumns.indexOf(e.name)){var t=s?a.getCellDisplayValue(n,e):a.getCellValue(n,e),r={value:a.options.exporterFieldCallback(a,n,e,t)},i=a.options.exporterFieldFormatCallback(a,n,e,t);i&&Object.assign(r,i),e.colDef.exporterPdfAlign&&(r.alignment=e.colDef.exporterPdfAlign),o.push(r)}}),i.push(o)}}),i},formatAsCsv:function(e,t,r){var i=e.map(function(e){return{value:e.displayName}}),n=0<i.length?this.formatRowAsCsv(this,r)(i)+"\n":"";return n+=t.map(this.formatRowAsCsv(this,r)).join("\n")},formatRowAsCsv:function(t,r){return function(e){return e.map(t.formatFieldAsCsv).join(r)}},formatFieldAsCsv:function(e){return null==e.value?"":"number"==typeof e.value?e.value:"boolean"==typeof e.value?e.value?"TRUE":"FALSE":"string"==typeof e.value?'"'+e.value.replace(/"/g,'""')+'"':"object"!=typeof e.value||e.value instanceof Date?JSON.stringify(e.value):'"'+JSON.stringify(e.value).replace(/"/g,'""')+'"'},isIE:function(){var e=!1;return-1!==navigator.userAgent.search(/(?:Edge|MSIE|Trident\/.*; rv:)/)&&(e=!0),e},downloadFile:function(e,t,r,i,n){var o,a=document,l=a.createElement("a"),s="application/octet-stream;charset=utf-8",d=this.isIE();if(n&&(t="sep="+r+"\r\n"+t),navigator.msSaveBlob)return navigator.msSaveOrOpenBlob(new Blob([i?"\ufeff":"",t],{type:s}),e);if(d){var c=a.createElement("iframe");return document.body.appendChild(c),c.contentWindow.document.open("text/html","replace"),c.contentWindow.document.write(t),c.contentWindow.document.close(),c.contentWindow.focus(),c.contentWindow.document.execCommand("SaveAs",!0,e),document.body.removeChild(c),!0}if("download"in l){var u=new Blob([i?"\ufeff":"",t],{type:s});o=URL.createObjectURL(u),l.setAttribute("download",e)}else o="data: "+s+","+encodeURIComponent(t),l.setAttribute("target","_blank");l.href=o,l.setAttribute("style","display:none;"),a.body.appendChild(l),setTimeout(function(){if(l.click)l.click();else if(document.createEvent){var e=document.createEvent("MouseEvents");e.initEvent("click",!0,!0),l.dispatchEvent(e)}a.body.removeChild(l)},this.delay)},pdfExport:function(n,o,a){var l=this;this.loadAllDataIfNeeded(n,o,a).then(function(){var e=l.getColumnHeaders(n,a),t=l.getData(n,o,a),r=l.prepareAsPdf(n,e,t);if(l.isIE()||-1!==navigator.appVersion.indexOf("Edge")){var i=angular.isFunction(n.options.exporterPdfFilename)?n.options.exporterPdfFilename(n,o,a):n.options.exporterPdfFilename;l.downloadPDF(i,r)}else pdfMake.createPdf(r).open()})},downloadPDF:function(r,e){var i,n,o=document;o.createElement("a");i=this.isIE(),pdfMake.createPdf(e).getBuffer(function(e){if(n=new Blob([e]),navigator.msSaveBlob)return navigator.msSaveBlob(n,r);if(i){var t=o.createElement("iframe");return document.body.appendChild(t),t.contentWindow.document.open("text/html","replace"),t.contentWindow.document.write(n),t.contentWindow.document.close(),t.contentWindow.focus(),t.contentWindow.document.execCommand("SaveAs",!0,r),document.body.removeChild(t),!0}})},prepareAsPdf:function(e,t,r){var i=this.calculatePdfHeaderWidths(e,t),n=t.map(function(e){return{text:e.displayName,style:"tableHeader"}}),o=r.map(this.formatRowAsPdf(this)),a=[n].concat(o),l={pageOrientation:e.options.exporterPdfOrientation,pageSize:e.options.exporterPdfPageSize,content:[{style:"tableStyle",table:{headerRows:1,widths:i,body:a}}],styles:{tableStyle:e.options.exporterPdfTableStyle,tableHeader:e.options.exporterPdfTableHeaderStyle},defaultStyle:e.options.exporterPdfDefaultStyle};return e.options.exporterPdfLayout&&(l.layout=e.options.exporterPdfLayout),e.options.exporterPdfHeader&&(l.header=e.options.exporterPdfHeader),e.options.exporterPdfFooter&&(l.footer=e.options.exporterPdfFooter),e.options.exporterPdfCustomFormatter&&(l=e.options.exporterPdfCustomFormatter(l)),l},calculatePdfHeaderWidths:function(t,e){var r=0;e.forEach(function(e){"number"==typeof e.width&&(r+=e.width)});var i=0;e.forEach(function(e){if("*"===e.width&&(i+=100),"string"==typeof e.width&&e.width.match(/(\d)*%/)){var t=parseInt(e.width.match(/(\d)*%/)[0]);e.width=r*t/100,i+=e.width}});var n=r+i;return e.map(function(e){return"*"===e.width?e.width:e.width*t.options.exporterPdfMaxGridWidth/n})},formatRowAsPdf:function(t){return function(e){return e.map(t.formatFieldAsPdfString)}},formatFieldAsPdfString:function(e){var t;return t=null==e.value?"":"number"==typeof e.value?e.value.toString():"boolean"==typeof e.value?e.value?"TRUE":"FALSE":"string"==typeof e.value?e.value.replace(/"/g,'""'):e.value instanceof Date?JSON.stringify(e.value).replace(/^"/,"").replace(/"$/,""):"object"==typeof e.value?e.value:JSON.stringify(e.value).replace(/^"/,"").replace(/"$/,""),e.alignment&&"string"==typeof e.alignment&&(t={text:t,alignment:e.alignment}),t},formatAsExcel:function(e,t,r,i,n){for(var o=e.map(function(e){return{value:e.displayName}}),a=[],l=[],s=0;s<o.length;s++){var d="header";switch(e[s].align){case"center":d="headerCenter";break;case"right":d="headerRight"}var c=n.styles&&n.styles[d]?{style:n.styles[d].id}:null;l.push({value:o[s].value,metadata:c})}a.push(l);for(var u=t.map(this.formatRowAsExcel(this,r,i)),g=0;g<u.length;g++)a.push(u[g]);return a},formatRowAsExcel:function(n,o,a){return function(e){for(var t=[],r=0;r<e.length;r++){var i=n.formatFieldAsExcel(e[r],o,a);t.push({value:i,metadata:e[r].metadata})}return t}},formatFieldAsExcel:function(e,t,r,i){return null==e.value?"":"number"==typeof e.value||"string"==typeof e.value?e.value:"boolean"==typeof e.value?e.value?"TRUE":"FALSE":JSON.stringify(e.value)},prepareAsExcel:function(e,t,r){var i={styles:{}};if(e.options.exporterExcelCustomFormatters&&(i=e.options.exporterExcelCustomFormatters(e,t,i)),e.options.exporterExcelHeader)if(angular.isFunction(e.options.exporterExcelHeader))e.options.exporterExcelHeader(e,t,r,i);else{var n=e.options.exporterExcelHeader.text,o=e.options.exporterExcelHeader.style;r.data.push([{value:n,metadata:{style:i.styles[o].id}}])}return i},excelExport:function(d,c,u){var g=this;this.loadAllDataIfNeeded(d,c,u).then(function(){var e=d.options.showHeader?g.getColumnHeaders(d,u):[],t="Sheet1";d.options.exporterExcelSheetName&&(t=angular.isFunction(d.options.exporterExcelSheetName)?d.options.exporterExcelSheetName(d,c,u):d.options.exporterExcelSheetName);var r=new ExcelBuilder.Worksheet({name:t}),i=new ExcelBuilder.Workbook;i.addWorksheet(r);for(var n=g.prepareAsExcel(d,i,r),o=[],a=d.treeBase?d.treeBase.numberLevels:d.enableRowSelection?1:0;a<d.columns.length;a++)d.columns[a].field!==p.rowHeaderColName&&d.columns[a].field!==p.selectionRowHeaderColName&&o.push({width:d.columns[a].drawnWidth/d.options.exporterColumnScaleFactor});r.setColumns(o);var l=g.getData(d,c,u,d.options.exporterFieldApplyFilters),s=g.formatAsExcel(e,l,i,r,n);r.setData(r.data.concat(s)),ExcelBuilder.Builder.createFile(i,{type:"blob"}).then(function(e){var t=angular.isFunction(d.options.exporterExcelFilename)?d.options.exporterExcelFilename(d,c,u):d.options.exporterExcelFilename;g.downloadFile(t,e,d.options.exporterCsvColumnSeparator,d.options.exporterOlderExcelCompatibility)})})}};function o(e,t,r,i){var n,o,a,l;return r.cellFilter?(o=(n=r.cellFilter.match(/(?:[^:"]+|"[^"]*")+/g))[0]?n[0].replace(/[\'\"\s]/g,""):null,a=n[1]?n[1].replace(/[\'\"]/g,"").trim():null,l=n[2]?n[2].replace(/[\'\"\s]/g,""):null,s(o)(i,a,l)):i}return i}]),e.directive("uiGridExporter",["uiGridExporterConstants","uiGridExporterService","gridUtil","$compile",function(e,n,t,r){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,link:function(e,t,r,i){n.initializeGrid(i.grid),i.grid.exporter.$scope=e}}}])}(),function(){"use strict";var e=angular.module("ui.grid.grouping",["ui.grid","ui.grid.treeBase"]);e.constant("uiGridGroupingConstants",{featureName:"grouping",rowHeaderColName:"treeBaseRowHeaderCol",EXPANDED:"expanded",COLLAPSED:"collapsed",aggregation:{COUNT:"count",SUM:"sum",MAX:"max",MIN:"min",AVG:"avg"}}),e.service("uiGridGroupingService",["$q","uiGridGroupingConstants","gridUtil","rowSorter","GridRow","gridClassFactory","i18nService","uiGridConstants","uiGridTreeBaseService",function(e,l,s,d,g,p,c,i,u){var f={initializeGrid:function(n,e){u.initializeGrid(n,e),n.grouping={},n.grouping.groupHeaderCache={},f.defaultGridOptions(n.options),n.registerRowsProcessor(f.groupRows,400),n.registerColumnBuilder(f.groupingColumnBuilder),n.registerColumnsProcessor(f.groupingColumnProcessor,400);var t={events:{grouping:{aggregationChanged:{},groupingChanged:{}}},methods:{grouping:{getGrouping:function(e){var t=f.getGrouping(n);return t.grouping.forEach(function(e){e.colName=e.col.name,delete e.col}),t.aggregations.forEach(function(e){e.colName=e.col.name,delete e.col}),t.aggregations=t.aggregations.filter(function(e){return!e.aggregation.source||"grouping"!==e.aggregation.source}),e&&(t.rowExpandedStates=f.getRowExpandedStates(n.grouping.groupingHeaderCache)),t},setGrouping:function(e){f.setGrouping(n,e)},groupColumn:function(e){var t=n.getColumn(e);f.groupColumn(n,t)},ungroupColumn:function(e){var t=n.getColumn(e);f.ungroupColumn(n,t)},clearGrouping:function(){f.clearGrouping(n)},aggregateColumn:function(e,t,r){var i=n.getColumn(e);f.aggregateColumn(n,i,t,r)}}}};n.api.registerEventsFromObject(t.events),n.api.registerMethodsFromObject(t.methods),n.api.core.on.sortChanged(e,f.tidyPriorities)},defaultGridOptions:function(e){e.enableGrouping=!1!==e.enableGrouping,e.groupingShowCounts=!1!==e.groupingShowCounts,e.groupingNullLabel=void 0===e.groupingNullLabel?"Null":e.groupingNullLabel,e.enableGroupHeaderSelection=!0===e.enableGroupHeaderSelection},groupingColumnBuilder:function(e,i,t){if(!1!==e.enableGrouping){void 0===i.grouping&&void 0!==e.grouping?(i.grouping=angular.copy(e.grouping),void 0!==i.grouping.groupPriority&&-1<i.grouping.groupPriority&&(i.treeAggregationFn=u.nativeAggregations()[l.aggregation.COUNT].aggregationFn,i.treeAggregationFinalizerFn=f.groupedFinalizerFn)):void 0===i.grouping&&(i.grouping={}),void 0!==i.grouping&&void 0!==i.grouping.groupPriority&&0<=i.grouping.groupPriority&&(i.suppressRemoveSort=!0);var r={name:"ui.grid.grouping.group",title:c.get().grouping.group,icon:"ui-grid-icon-indent-right",shown:function(){return void 0===this.context.col.grouping||void 0===this.context.col.grouping.groupPriority||this.context.col.grouping.groupPriority<0},action:function(){f.groupColumn(this.context.col.grid,this.context.col)}},n={name:"ui.grid.grouping.ungroup",title:c.get().grouping.ungroup,icon:"ui-grid-icon-indent-left",shown:function(){return void 0!==this.context.col.grouping&&void 0!==this.context.col.grouping.groupPriority&&0<=this.context.col.grouping.groupPriority},action:function(){f.ungroupColumn(this.context.col.grid,this.context.col)}},o={name:"ui.grid.grouping.aggregateRemove",title:c.get().grouping.aggregate_remove,shown:function(){return void 0!==this.context.col.treeAggregationFn},action:function(){f.aggregateColumn(this.context.col.grid,this.context.col,null)}},a=function(e,t){t=t||c.get().grouping["aggregate_"+e]||e;var r={name:"ui.grid.grouping.aggregate"+e,title:t,shown:function(){return void 0===this.context.col.treeAggregation||void 0===this.context.col.treeAggregation.type||this.context.col.treeAggregation.type!==e},action:function(){f.aggregateColumn(this.context.col.grid,this.context.col,e)}};s.arrayContainsObjectWithProperty(i.menuItems,"name","ui.grid.grouping.aggregate"+e)||i.menuItems.push(r)};!1!==i.colDef.groupingShowGroupingMenu&&(s.arrayContainsObjectWithProperty(i.menuItems,"name","ui.grid.grouping.group")||i.menuItems.push(r),s.arrayContainsObjectWithProperty(i.menuItems,"name","ui.grid.grouping.ungroup")||i.menuItems.push(n)),!1!==i.colDef.groupingShowAggregationMenu&&(angular.forEach(u.nativeAggregations(),function(e,t){a(t)}),angular.forEach(t.treeCustomAggregations,function(e,t){a(t,e.menuTitle)}),s.arrayContainsObjectWithProperty(i.menuItems,"name","ui.grid.grouping.aggregateRemove")||i.menuItems.push(o))}},groupingColumnProcessor:function(e,t){return e=f.moveGroupColumns(this,e,t)},groupedFinalizerFn:function(e){void 0!==e.groupVal?(e.rendered=e.groupVal,this.grid.options.groupingShowCounts&&"date"!==this.colDef.type&&"object"!==this.colDef.type&&(e.rendered+=" ("+e.value+")")):e.rendered=null},moveGroupColumns:function(e,t){return!1===e.options.moveGroupColumns||(t.forEach(function(e,t){e.groupingPosition=t}),t.sort(function(e,t){var r,i;return r=e.isRowHeader?e.headerPriority:void 0===e.grouping||void 0===e.grouping.groupPriority||e.grouping.groupPriority<0?null:e.grouping.groupPriority,i=t.isRowHeader?t.headerPriority:void 0===t.grouping||void 0===t.grouping.groupPriority||t.grouping.groupPriority<0?null:t.grouping.groupPriority,null!==r&&null===i?-1:null!==i&&null===r?1:null!==r&&null!==i?r-i:e.groupingPosition-t.groupingPosition}),t.forEach(function(e){delete e.groupingPosition})),t},groupColumn:function(e,t){void 0===t.grouping&&(t.grouping={});var r=f.getGrouping(e);t.grouping.groupPriority=r.grouping.length,t.previousSort=angular.copy(t.sort),t.sort?void 0!==t.sort.direction&&null!==t.sort.direction||(t.sort.direction=i.ASC):t.sort={direction:i.ASC},t.treeAggregation={type:l.aggregation.COUNT,source:"grouping"},t.colDef&&angular.isFunction(t.colDef.customTreeAggregationFn)?t.treeAggregationFn=t.colDef.customTreeAggregationFn:t.treeAggregationFn=u.nativeAggregations()[l.aggregation.COUNT].aggregationFn,t.treeAggregationFinalizerFn=f.groupedFinalizerFn,e.api.grouping.raise.groupingChanged(t),e.api.core.raise.sortChanged(e,e.getColumnSorting()),e.queueGridRefresh()},ungroupColumn:function(e,t){void 0!==t.grouping&&(delete t.grouping.groupPriority,delete t.treeAggregation,delete t.customTreeAggregationFinalizer,t.previousSort&&(t.sort=t.previousSort,delete t.previousSort),f.tidyPriorities(e),e.api.grouping.raise.groupingChanged(t),e.api.core.raise.sortChanged(e,e.getColumnSorting()),e.queueGridRefresh())},aggregateColumn:function(e,t,r,i){void 0!==t.grouping&&void 0!==t.grouping.groupPriority&&0<=t.grouping.groupPriority&&f.ungroupColumn(e,t);var n={};void 0!==e.options.treeCustomAggregations[r]?n=e.options.treeCustomAggregations[r]:void 0!==u.nativeAggregations()[r]&&(n=u.nativeAggregations()[r]),t.treeAggregation={type:r,label:"string"==typeof i?i:c.get().aggregation[n.label]||n.label},t.treeAggregationFn=n.aggregationFn,t.treeAggregationFinalizerFn=n.finalizerFn,e.api.grouping.raise.aggregationChanged(t),e.queueGridRefresh()},setGrouping:function(r,e){void 0!==e&&(f.clearGrouping(r),e.grouping&&e.grouping.length&&0<e.grouping.length&&e.grouping.forEach(function(e){var t=r.getColumn(e.colName);t&&f.groupColumn(r,t)}),e.aggregations&&e.aggregations.length&&e.aggregations.forEach(function(e){var t=r.getColumn(e.colName);t&&f.aggregateColumn(r,t,e.aggregation.type)}),e.rowExpandedStates&&f.applyRowExpandedStates(r.grouping.groupingHeaderCache,e.rowExpandedStates))},clearGrouping:function(t){var e=f.getGrouping(t);0<e.grouping.length&&e.grouping.forEach(function(e){e.col||(e.col=t.getColumn(e.colName)),f.ungroupColumn(t,e.col)}),0<e.aggregations.length&&e.aggregations.forEach(function(e){e.col||(e.col=t.getColumn(e.colName)),f.aggregateColumn(t,e.col,null)})},tidyPriorities:function(e){void 0!==e&&void 0===e.grid||void 0===this.grid||(e=this.grid);var r=[],i=[];e.columns.forEach(function(e,t){void 0!==e.grouping&&void 0!==e.grouping.groupPriority&&0<=e.grouping.groupPriority?r.push(e):void 0!==e.sort&&void 0!==e.sort.priority&&0<=e.sort.priority&&i.push(e)}),r.sort(function(e,t){return e.grouping.groupPriority-t.grouping.groupPriority}),r.forEach(function(e,t){e.grouping.groupPriority=t,e.suppressRemoveSort=!0,void 0===e.sort&&(e.sort={}),e.sort.priority=t});var t=r.length;i.sort(function(e,t){return e.sort.priority-t.sort.priority}),i.forEach(function(e){e.sort.priority=t,e.suppressRemoveSort=e.colDef.suppressRemoveSort,t++})},groupRows:function(i){if(0===i.length)return i;var n=this;n.grouping.oldGroupingHeaderCache=n.grouping.groupingHeaderCache||{},n.grouping.groupingHeaderCache={};for(var o=f.initialiseProcessingState(n),e=function(e,t){var r=n.getCellValue(l,e.col);e.initialised&&0===d.getSortFn(e.col)(r,e.currentValue)||(f.insertGroupHeader(n,i,a,o,t),a++)},a=0;a<i.length;a++){var l=i[a];l.visible&&o.forEach(e)}return delete n.grouping.oldGroupingHeaderCache,i},initialiseProcessingState:function(e){var r=[];return f.getGrouping(e).grouping.forEach(function(e,t){r.push({fieldName:e.field,col:e.col,initialised:!1,currentValue:null,currentRow:null})}),r},getGrouping:function(e){var t=[],r=[];return e.columns.forEach(function(e){e.grouping&&void 0!==e.grouping.groupPriority&&0<=e.grouping.groupPriority&&t.push({field:e.field,col:e,groupPriority:e.grouping.groupPriority,grouping:e.grouping}),e.treeAggregation&&e.treeAggregation.type&&r.push({field:e.field,col:e,aggregation:e.treeAggregation})}),t.sort(function(e,t){return e.groupPriority-t.groupPriority}),t.forEach(function(e,t){e.grouping.groupPriority=t,e.groupPriority=t,delete e.grouping}),{grouping:t,aggregations:r}},insertGroupHeader:function(e,t,r,i,n){var o=i[n].col,a=e.getCellValue(t[r],o),l=a;function s(e){return angular.isObject(e)?JSON.stringify(e):e}null==a&&(l=e.options.groupingNullLabel);for(var d,c=e.grouping.oldGroupingHeaderCache,u=0;u<n;u++)c&&c[s(i[u].currentValue)]&&(c=c[s(i[u].currentValue)].children);for(c&&c[s(a)]?(d=c[s(a)].row).entity={}:(d=new g({},null,e),p.rowTemplateAssigner.call(e,d)),d.entity["$$"+i[n].col.uid]={groupVal:l},d.treeLevel=n,d.groupHeader=!0,d.internalRow=!0,d.enableCellEdit=!1,d.enableSelection=e.options.enableGroupHeaderSelection,i[n].initialised=!0,i[n].currentValue=a,i[n].currentRow=d,f.finaliseProcessingState(i,n+1),t.splice(r,0,d),c=e.grouping.groupingHeaderCache,u=0;u<n;u++)c=c[s(i[u].currentValue)].children;c[s(a)]={row:d,children:{}}},finaliseProcessingState:function(e,t){for(var r=t;r<e.length;r++)e[r].initialised=!1,e[r].currentRow=null,e[r].currentValue=null},getRowExpandedStates:function(e){if(void 0===e)return{};var r={};return angular.forEach(e,function(e,t){r[t]={state:e.row.treeNode.state},e.children?r[t].children=f.getRowExpandedStates(e.children):r[t].children={}}),r},applyRowExpandedStates:function(r,e){void 0!==e&&angular.forEach(e,function(e,t){r[t]&&(r[t].row.treeNode.state=e.state,e.children&&r[t].children&&f.applyRowExpandedStates(r[t].children,e.children))})}};return f}]),e.directive("uiGridGrouping",["uiGridGroupingConstants","uiGridGroupingService",function(e,n){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){!1!==i.grid.options.enableGrouping&&n.initializeGrid(i.grid,e)},post:function(e,t,r,i){}}}}}])}(),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("ar",{headerCell:{aria:{defaultFilterLabel:"التصفيه بالعمود",removeFilter:"محو التصفيه",columnMenuButtonLabel:"قاءمه الاعمده"},priority:"أولويه : ",filterLabel:"تصفيه بالاعمده :"},aggregate:{label:"العناصر"},groupPanel:{description:"اسحب رأس العمود هنا وأسقطه لإنشاء مجموعه"},search:{placeholder:"بحث  ...",showingItems:"العناصر الظاهره :",selectedItems:"العناصر المحدده :",totalItems:"عدد العناصر :",size:"حجم الصفحه :",first:"اول صفحه",next:"الصفحه التاليه",previous:"الصفحه الصابقه",last:"الصفحه الاخيره"},menu:{text:"اختيار العمود :"},sort:{ascending:"ترتيب تصاعدى",descending:"ترتيب تنازلى",none:"عدم التحديد",remove:"حذف الترتيب"},column:{hide:"إخفاء عمود"},aggregation:{count:"عدد الصفوف: ",sum:"جمع: ",avg:"المتوسط الحسابى: ",min:"الادنى: ",max:"الاقصى: "},pinning:{pinLeft:"تثبيت لليسار",pinRight:"تثبيت لليمين",unpin:"فك التثبيت"},columnMenu:{close:"غلق"},gridMenu:{aria:{buttonLabel:"قائمه الجدول"},columns:"الاعمده:",importerTitle:"إدخال ملف",exporterAllAsCsv:"إخراج كل البيانات ك(csv)",exporterVisibleAsCsv:"إخراج كل البيانات الواضحه ك (csv)",exporterSelectedAsCsv:"إخراج كل البيانات المحدده ك (csv)",exporterAllAsPdf:"إخراج كل البيانات ك(pdf)",exporterVisibleAsPdf:"إخراج كل البيانات الواضحه ك (pdf)",exporterSelectedAsPdf:"إخراج كل البيانات المحدده ك (pdf)",clearAllFilters:"محو كل الترشيح"},importer:{noHeaders:"اسماء هؤلاء الاعمده غير واضحه، هل يوجد رأس للملف؟",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"الملف غير قادر على الاتمام ، هل ال (CSV) صحيح؟",invalidJson:"الملف غير قادر على الاتمام ، هل ال (JSON) صحيح؟",jsonNotArray:"Imported json file must contain an array, aborting."},pagination:{aria:{pageToFirst:"الصفحه الاولى",pageBack:"الصفه السابقه",pageSelected:"الصفحه المحدده",pageForward:"الصفحه التاليه",pageToLast:"الصفحه الاخيره"},sizes:"عدد العناصر فى الصفحه",totalItems:"عناصر",through:"إلى",of:"من"},grouping:{group:"جمع",ungroup:"فك الجمع",aggregate_count:"جمله : العدد",aggregate_sum:"جمله : الحاصل",aggregate_max:"جمله : الاقصى",aggregate_min:"جمله : الاقل",aggregate_avg:"جمله :المتوسط ",aggregate_remove:"جمله : حذف"},validate:{error:"خطأ :",minLength:"القيمه لابد ان لا تقل عن THRESHOLD حرف.",maxLength:"القيمه لابد ان لا تزيد عن THRESHOLD حرف.",required:"مطلوب قيمه"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("bg",{headerCell:{aria:{defaultFilterLabel:"Филттър за колоната",removeFilter:"Премахни филтър",columnMenuButtonLabel:"Меню на колоната"},priority:"Приоритет:",filterLabel:"Филтър за колоната: "},aggregate:{label:"обекти"},search:{placeholder:"Търсене...",showingItems:"Показани обекти:",selectedItems:"избрани обекти:",totalItems:"Общо:",size:"Размер на страницата:",first:"Първа страница",next:"Следваща страница",previous:"Предишна страница",last:"Последна страница"},menu:{text:"Избери колони:"},sort:{ascending:"Сортиране по възходящ ред",descending:"Сортиране по низходящ ред",none:"Без сортиране",remove:"Премахни сортирането"},column:{hide:"Скрий колоната"},aggregation:{count:"Общо редове: ",sum:"общо: ",avg:"средно: ",min:"най-малко: ",max:"най-много: "},pinning:{pinLeft:"Прикрепи вляво",pinRight:"Прикрепи вдясно",unpin:"Премахване"},columnMenu:{close:"Затвори"},gridMenu:{aria:{buttonLabel:"Меню на таблицата"},columns:"Колони:",importerTitle:"Импортиране на файл",exporterAllAsCsv:"Експортиране на данните като csv",exporterVisibleAsCsv:"Експортиране на видимите данни като csv",exporterSelectedAsCsv:"Експортиране на избраните данни като csv",exporterAllAsPdf:"Експортиране на данните като pdf",exporterVisibleAsPdf:"Експортиране на видимите данни като pdf",exporterSelectedAsPdf:"Експортиране на избраните данни като pdf",clearAllFilters:"Премахни всички филтри"},importer:{noHeaders:"Имената на колоните не успяха да бъдат извлечени, файлът има ли хедър?",noObjects:"Обектите не успяха да бъдат извлечени, файлът съдържа ли данни, различни от хедър?",invalidCsv:"Файлът не може да бъде обработеб, уверете се, че е валиден CSV файл",invalidJson:"Файлът не може да бъде обработеб, уверете се, че е валиден JSON файл",jsonNotArray:"Импортираният JSON файл трябва да съдържа масив, прекратяване."},pagination:{aria:{pageToFirst:"Към първа страница",pageBack:"Страница назад",pageSelected:"Избрана страница",pageForward:"Страница напред",pageToLast:"Към последна страница"},sizes:"обекта на страница",totalItems:"обекта",through:"до",of:"от"},grouping:{group:"Групиране",ungroup:"Премахване на групирането",aggregate_count:"Сбор: Брой",aggregate_sum:"Сбор: Сума",aggregate_max:"Сбор: Максимум",aggregate_min:"Сбор: Минимум",aggregate_avg:"Сбор: Средно",aggregate_remove:"Сбор: Премахване"},validate:{error:"Грешка:",minLength:"Стойността трябва да съдържа поне THRESHOLD символа.",maxLength:"Стойността не трябва да съдържа повече от THRESHOLD символа.",required:"Необходима е стойност."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){var t={aggregate:{label:"položky"},groupPanel:{description:"Přesuňte záhlaví zde pro vytvoření skupiny dle sloupce."},search:{placeholder:"Hledat...",showingItems:"Zobrazuji položky:",selectedItems:"Vybrané položky:",totalItems:"Celkem položek:",size:"Velikost strany:",first:"První strana",next:"Další strana",previous:"Předchozí strana",last:"Poslední strana"},menu:{text:"Vyberte sloupec:"},sort:{ascending:"Seřadit od A-Z",descending:"Seřadit od Z-A",remove:"Odebrat seřazení"},column:{hide:"Schovat sloupec"},aggregation:{count:"celkem řádků: ",sum:"celkem: ",avg:"avg: ",min:"min.: ",max:"max.: "},pinning:{pinLeft:"Zamknout vlevo",pinRight:"Zamknout vpravo",unpin:"Odemknout"},gridMenu:{columns:"Sloupce:",importerTitle:"Importovat soubor",exporterAllAsCsv:"Exportovat všechna data do csv",exporterVisibleAsCsv:"Exportovat viditelná data do csv",exporterSelectedAsCsv:"Exportovat vybraná data do csv",exporterAllAsPdf:"Exportovat všechna data do pdf",exporterVisibleAsPdf:"Exportovat viditelná data do pdf",exporterSelectedAsPdf:"Exportovat vybraná data do pdf",exporterAllAsExcel:"Exportovat všechna data do excel",exporterVisibleAsExcel:"Exportovat viditelná data do excel",exporterSelectedAsExcel:"Exportovat vybraná data do excel",clearAllFilters:"Odstranit všechny filtry"},importer:{noHeaders:"Názvy sloupců se nepodařilo získat, obsahuje soubor záhlaví?",noObjects:"Data se nepodařilo zpracovat, obsahuje soubor řádky mimo záhlaví?",invalidCsv:"Soubor nelze zpracovat, jedná se o CSV?",invalidJson:"Soubor nelze zpracovat, je to JSON?",jsonNotArray:"Soubor musí obsahovat json. Ukončuji.."},pagination:{sizes:"položek na stránku",totalItems:"položek"},grouping:{group:"Seskupit",ungroup:"Odebrat seskupení",aggregate_count:"Agregace: Count",aggregate_sum:"Agregace: Sum",aggregate_max:"Agregace: Max",aggregate_min:"Agregace: Min",aggregate_avg:"Agregace: Avg",aggregate_remove:"Agregace: Odebrat"}};return e.add("cs",t),e.add("cz",t),e.add("cs-cz",t),e.add("cs-CZ",t),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("da",{aggregate:{label:"artikler"},groupPanel:{description:"Grupér rækker udfra en kolonne ved at trække dens overskift hertil."},search:{placeholder:"Søg...",showingItems:"Viste rækker:",selectedItems:"Valgte rækker:",totalItems:"Rækker totalt:",size:"Side størrelse:",first:"Første side",next:"Næste side",previous:"Forrige side",last:"Sidste side"},menu:{text:"Vælg kolonner:"},sort:{ascending:"Sorter stigende",descending:"Sorter faldende",none:"Sorter ingen",remove:"Fjern sortering"},column:{hide:"Skjul kolonne"},aggregation:{count:"antal rækker: ",sum:"sum: ",avg:"gns: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Fastgør til venstre",pinRight:"Fastgør til højre",unpin:"Frigør"},gridMenu:{columns:"Kolonner:",importerTitle:"Importer fil",exporterAllAsCsv:"Eksporter alle data som csv",exporterVisibleAsCsv:"Eksporter synlige data som csv",exporterSelectedAsCsv:"Eksporter markerede data som csv",exporterAllAsPdf:"Eksporter alle data som pdf",exporterVisibleAsPdf:"Eksporter synlige data som pdf",exporterSelectedAsPdf:"Eksporter markerede data som pdf",exporterAllAsExcel:"Eksporter alle data som excel",exporterVisibleAsExcel:"Eksporter synlige data som excel",exporterSelectedAsExcel:"Eksporter markerede data som excel",clearAllFilters:"Clear all filters"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."},pagination:{aria:{pageToFirst:"Gå til første",pageBack:"Gå tilbage",pageSelected:"Valgte side",pageForward:"Gå frem",pageToLast:"Gå til sidste"},sizes:"genstande per side",totalItems:"genstande",through:"gennem",of:"af"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("de",{headerCell:{aria:{defaultFilterLabel:"Filter für Spalte",removeFilter:"Filter löschen",columnMenuButtonLabel:"Spaltenmenü",column:"Spalte"},priority:"Priorität:",filterLabel:"Filter für Spalte: "},aggregate:{label:"Eintrag"},groupPanel:{description:"Ziehen Sie eine Spaltenüberschrift hierhin, um nach dieser Spalte zu gruppieren."},search:{aria:{selected:"Zeile markiert",notSelected:"Zeile nicht markiert"},placeholder:"Suche...",showingItems:"Zeige Einträge:",selectedItems:"Ausgewählte Einträge:",totalItems:"Einträge gesamt:",size:"Einträge pro Seite:",first:"Erste Seite",next:"Nächste Seite",previous:"Vorherige Seite",last:"Letzte Seite"},selection:{aria:{row:"Zeile"},selectAll:"Alle auswählen",displayName:"Zeilenauswahlkasten"},menu:{text:"Spalten auswählen:"},sort:{ascending:"aufsteigend sortieren",descending:"absteigend sortieren",none:"keine Sortierung",remove:"Sortierung zurücksetzen"},column:{hide:"Spalte ausblenden"},aggregation:{count:"Zeilen insgesamt: ",sum:"gesamt: ",avg:"Durchschnitt: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Links anheften",pinRight:"Rechts anheften",unpin:"Lösen"},columnMenu:{close:"Schließen"},gridMenu:{aria:{buttonLabel:"Tabellenmenü"},columns:"Spalten:",importerTitle:"Datei importieren",exporterAllAsCsv:"Alle Daten als CSV exportieren",exporterVisibleAsCsv:"Sichtbare Daten als CSV exportieren",exporterSelectedAsCsv:"Markierte Daten als CSV exportieren",exporterAllAsPdf:"Alle Daten als PDF exportieren",exporterVisibleAsPdf:"Sichtbare Daten als PDF exportieren",exporterSelectedAsPdf:"Markierte Daten als PDF exportieren",exporterAllAsExcel:"Alle Daten als Excel exportieren",exporterVisibleAsExcel:"Sichtbare Daten als Excel exportieren",exporterSelectedAsExcel:"Markierte Daten als Excel exportieren",clearAllFilters:"Alle Filter zurücksetzen"},importer:{noHeaders:"Es konnten keine Spaltennamen ermittelt werden. Sind in der Datei Spaltendefinitionen enthalten?",noObjects:"Es konnten keine Zeileninformationen gelesen werden, Sind in der Datei außer den Spaltendefinitionen auch Daten enthalten?",invalidCsv:"Die Datei konnte nicht eingelesen werden, ist es eine gültige CSV-Datei?",invalidJson:"Die Datei konnte nicht eingelesen werden. Enthält sie gültiges JSON?",jsonNotArray:"Die importierte JSON-Datei muß ein Array enthalten. Breche Import ab."},pagination:{aria:{pageToFirst:"Zum Anfang",pageBack:"Seite zurück",pageSelected:"Ausgewählte Seite",pageForward:"Seite vor",pageToLast:"Zum Ende"},sizes:"Einträge pro Seite",totalItems:"Einträgen",through:"bis",of:"von"},grouping:{group:"Gruppieren",ungroup:"Gruppierung aufheben",aggregate_count:"Agg: Anzahl",aggregate_sum:"Agg: Summe",aggregate_max:"Agg: Maximum",aggregate_min:"Agg: Minimum",aggregate_avg:"Agg: Mittelwert",aggregate_remove:"Aggregation entfernen"},validate:{error:"Fehler:",minLength:"Der Wert sollte mindestens THRESHOLD Zeichen lang sein.",maxLength:"Der Wert sollte maximal THRESHOLD Zeichen lang sein.",required:"Ein Wert wird benötigt."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("es-ct",{headerCell:{aria:{defaultFilterLabel:"Filtre per columna",removeFilter:"Elimina el filtre",columnMenuButtonLabel:"Menú de Columna",column:"Columna"},priority:"Priority:",filterLabel:"Filtre per columna: "},aggregate:{label:"items"},groupPanel:{description:"Arrossegueu una capçalera de columna aquí i deixeu-lo anar per agrupar per aquesta columna."},search:{aria:{selected:"Fila seleccionada",notSelected:"Fila no seleccionada"},placeholder:"Cerca...",showingItems:"Ítems Mostrats:",selectedItems:"Ítems Seleccionats:",totalItems:"Ítems Totals:",size:"Mida de la pàgina:",first:"Primera Pàgina",next:"Propera Pàgina",previous:"Pàgina Anterior",last:"Última Pàgina"},selection:{aria:{row:"Fila"},selectAll:"Seleccionar Todo",displayName:"Seleccionar Fila"},menu:{text:"Triar Columnes:"},sort:{ascending:"Ordena Ascendent",descending:"Ordena Descendent",none:"Sense Ordre",remove:"Eliminar Ordre"},column:{hide:"Amaga la Columna"},aggregation:{count:"Files Totals: ",sum:"total: ",avg:"mitjà: ",min:"mín: ",max:"màx: "},pinning:{pinLeft:"Fixar a l'Esquerra",pinRight:"Fixar a la Dreta",unpin:"Treure Fixació"},columnMenu:{close:"Tanca"},gridMenu:{aria:{buttonLabel:"Menú de Quadrícula"},columns:"Columnes:",importerTitle:"Importa el fitxer",exporterAllAsCsv:"Exporta tot com CSV",exporterVisibleAsCsv:"Exporta les dades visibles com a CSV",exporterSelectedAsCsv:"Exporta les dades seleccionades com a CSV",exporterAllAsPdf:"Exporta tot com PDF",exporterVisibleAsPdf:"Exporta les dades visibles com a PDF",exporterSelectedAsPdf:"Exporta les dades seleccionades com a PDF",exporterAllAsExcel:"Exporta tot com Excel",exporterVisibleAsExcel:"Exporta les dades visibles com Excel",exporterSelectedAsExcel:"Exporta les dades seleccionades com Excel",clearAllFilters:"Netejar tots els filtres"},importer:{noHeaders:"No va ser possible derivar els noms de les columnes, té encapçalats l'arxiu?",noObjects:"No va ser possible obtenir registres, conté dades l'arxiu, a part de les capçaleres?",invalidCsv:"No va ser possible processar l'arxiu, ¿és un CSV vàlid?",invalidJson:"No va ser possible processar l'arxiu, ¿és un JSON vàlid?",jsonNotArray:"El fitxer json importat ha de contenir una matriu, avortant."},pagination:{aria:{pageToFirst:"Page to first",pageBack:"Page back",pageSelected:"Selected page",pageForward:"Page forward",pageToLast:"Page to last"},sizes:"ítems per pàgina",totalItems:"ítems",through:"a",of:"de"},grouping:{group:"Agrupar",ungroup:"Desagrupar",aggregate_count:"Agr: Compte",aggregate_sum:"Agr: Sum",aggregate_max:"Agr: Máx",aggregate_min:"Agr: Mín",aggregate_avg:"Agr: Mitjà",aggregate_remove:"Agr: Treure"},validate:{error:"Error:",minLength:"El valor ha de tenir almenys caràcters THRESHOLD.",maxLength:"El valor ha de tenir com a màxim caràcters THRESHOLD.",required:"Un valor és necessari."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("es",{aggregate:{label:"Artículos"},groupPanel:{description:"Arrastre un encabezado de columna aquí y suéltelo para agrupar por esa columna."},search:{placeholder:"Buscar...",showingItems:"Artículos Mostrados:",selectedItems:"Artículos Seleccionados:",totalItems:"Artículos Totales:",size:"Tamaño de Página:",first:"Primera Página",next:"Página Siguiente",previous:"Página Anterior",last:"Última Página"},selection:{aria:{row:"Fila"},selectAll:"Seleccionar Todo",displayName:"Seleccionar Fila"},menu:{text:"Elegir columnas:"},sort:{ascending:"Orden Ascendente",descending:"Orden Descendente",remove:"Sin Ordenar"},column:{hide:"Ocultar la columna"},aggregation:{count:"filas totales: ",sum:"total: ",avg:"media: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Fijar a la Izquierda",pinRight:"Fijar a la Derecha",unpin:"Quitar Fijación"},gridMenu:{columns:"Columnas:",importerTitle:"Importar archivo",exporterAllAsCsv:"Exportar todo como csv",exporterVisibleAsCsv:"Exportar vista como csv",exporterSelectedAsCsv:"Exportar selección como csv",exporterAllAsPdf:"Exportar todo como pdf",exporterVisibleAsPdf:"Exportar vista como pdf",exporterSelectedAsPdf:"Exportar selección como pdf",exporterAllAsExcel:"Exportar todo como excel",exporterVisibleAsExcel:"Exportar vista como excel",exporterSelectedAsExcel:"Exportar selección como excel",clearAllFilters:"Limpiar todos los filtros"},importer:{noHeaders:"No fue posible derivar los nombres de las columnas, ¿tiene encabezados el archivo?",noObjects:"No fue posible obtener registros, ¿contiene datos el archivo, aparte de los encabezados?",invalidCsv:"No fue posible procesar el archivo, ¿es un CSV válido?",invalidJson:"No fue posible procesar el archivo, ¿es un Json válido?",jsonNotArray:"El archivo json importado debe contener un array, abortando."},pagination:{aria:{pageToFirst:"Página para primero",pageBack:"Página atrás",pageSelected:"Página seleccionada",pageForward:"Avance de página",pageToLast:"Página para durar"},through:"mediante",sizes:"registros por página",totalItems:"registros",of:"de"},grouping:{group:"Agrupar",ungroup:"Desagrupar",aggregate_count:"Agr: Cont",aggregate_sum:"Agr: Sum",aggregate_max:"Agr: Máx",aggregate_min:"Agr: Min",aggregate_avg:"Agr: Prom",aggregate_remove:"Agr: Quitar"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("fa",{aggregate:{label:"قلم"},groupPanel:{description:"عنوان یک ستون را بگیر و به گروهی از آن ستون رها کن."},search:{placeholder:"جستجو...",showingItems:"نمایش اقلام:",selectedItems:"قلم‌های انتخاب شده:",totalItems:"مجموع اقلام:",size:"اندازه‌ی صفحه:",first:"اولین صفحه",next:"صفحه‌ی‌بعدی",previous:"صفحه‌ی‌ قبلی",last:"آخرین صفحه"},menu:{text:"ستون‌های انتخابی:"},sort:{ascending:"ترتیب صعودی",descending:"ترتیب نزولی",remove:"حذف مرتب کردن"},column:{hide:"پنهان‌کردن ستون"},aggregation:{count:"تعداد: ",sum:"مجموع: ",avg:"میانگین: ",min:"کمترین: ",max:"بیشترین: "},pinning:{pinLeft:"پین کردن سمت چپ",pinRight:"پین کردن سمت راست",unpin:"حذف پین"},gridMenu:{columns:"ستون‌ها:",importerTitle:"وارد کردن فایل",exporterAllAsCsv:"خروجی تمام داده‌ها در فایل csv",exporterVisibleAsCsv:"خروجی داده‌های قابل مشاهده در فایل csv",exporterSelectedAsCsv:"خروجی داده‌های انتخاب‌شده در فایل csv",exporterAllAsPdf:"خروجی تمام داده‌ها در فایل pdf",exporterVisibleAsPdf:"خروجی داده‌های قابل مشاهده در فایل pdf",exporterSelectedAsPdf:"خروجی داده‌های انتخاب‌شده در فایل pdf",clearAllFilters:"پاک کردن تمام فیلتر"},importer:{noHeaders:"نام ستون قابل استخراج نیست. آیا فایل عنوان دارد؟",noObjects:"اشیا قابل استخراج نیستند. آیا به جز عنوان‌ها در فایل داده وجود دارد؟",invalidCsv:"فایل قابل پردازش نیست. آیا فرمت  csv  معتبر است؟",invalidJson:"فایل قابل پردازش نیست. آیا فرمت json   معتبر است؟",jsonNotArray:"فایل json وارد شده باید حاوی آرایه باشد. عملیات ساقط شد."},pagination:{sizes:"اقلام در هر صفحه",totalItems:"اقلام",of:"از"},grouping:{group:"گروه‌بندی",ungroup:"حذف گروه‌بندی",aggregate_count:"Agg: تعداد",aggregate_sum:"Agg: جمع",aggregate_max:"Agg: بیشینه",aggregate_min:"Agg: کمینه",aggregate_avg:"Agg: میانگین",aggregate_remove:"Agg: حذف"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("fi",{aggregate:{label:"rivit"},groupPanel:{description:"Raahaa ja pudota otsikko tähän ryhmittääksesi sarakkeen mukaan."},search:{placeholder:"Hae...",showingItems:"Näytetään rivejä:",selectedItems:"Valitut rivit:",totalItems:"Rivejä yht.:",size:"Näytä:",first:"Ensimmäinen sivu",next:"Seuraava sivu",previous:"Edellinen sivu",last:"Viimeinen sivu"},menu:{text:"Valitse sarakkeet:"},sort:{ascending:"Järjestä nouseva",descending:"Järjestä laskeva",remove:"Poista järjestys"},column:{hide:"Piilota sarake"},aggregation:{count:"Rivejä yht.: ",sum:"Summa: ",avg:"K.a.: ",min:"Min: ",max:"Max: "},pinning:{pinLeft:"Lukitse vasemmalle",pinRight:"Lukitse oikealle",unpin:"Poista lukitus"},gridMenu:{columns:"Sarakkeet:",importerTitle:"Tuo tiedosto",exporterAllAsCsv:"Vie tiedot csv-muodossa",exporterVisibleAsCsv:"Vie näkyvä tieto csv-muodossa",exporterSelectedAsCsv:"Vie valittu tieto csv-muodossa",exporterAllAsPdf:"Vie tiedot pdf-muodossa",exporterVisibleAsPdf:"Vie näkyvä tieto pdf-muodossa",exporterSelectedAsPdf:"Vie valittu tieto pdf-muodossa",exporterAllAsExcel:"Vie tiedot excel-muodossa",exporterVisibleAsExcel:"Vie näkyvä tieto excel-muodossa",exporterSelectedAsExcel:"Vie valittu tieto excel-muodossa",clearAllFilters:"Puhdista kaikki suodattimet"},importer:{noHeaders:"Sarakkeen nimiä ei voitu päätellä, onko tiedostossa otsikkoriviä?",noObjects:"Tietoja ei voitu lukea, onko tiedostossa muuta kuin otsikkot?",invalidCsv:"Tiedostoa ei voitu käsitellä, oliko se CSV-muodossa?",invalidJson:"Tiedostoa ei voitu käsitellä, oliko se JSON-muodossa?",jsonNotArray:"Tiedosto ei sisältänyt taulukkoa, lopetetaan."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("fr",{headerCell:{aria:{defaultFilterLabel:"Filtre de la colonne",removeFilter:"Supprimer le filtre",columnMenuButtonLabel:"Menu de la colonne"},priority:"Priorité:",filterLabel:"Filtre de la colonne: "},aggregate:{label:"éléments"},groupPanel:{description:"Faites glisser une en-tête de colonne ici pour créer un groupe de colonnes."},search:{placeholder:"Recherche...",showingItems:"Affichage des éléments :",selectedItems:"Éléments sélectionnés :",totalItems:"Nombre total d'éléments:",size:"Taille de page:",first:"Première page",next:"Page Suivante",previous:"Page précédente",last:"Dernière page"},selection:{aria:{row:"Ligne"},selectAll:"Tout Sélectionner",displayName:"Sélectionnez la ligne"},menu:{text:"Choisir des colonnes :"},sort:{ascending:"Trier par ordre croissant",descending:"Trier par ordre décroissant",none:"Aucun tri",remove:"Enlever le tri"},column:{hide:"Cacher la colonne"},aggregation:{count:"lignes totales: ",sum:"total: ",avg:"moy: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Épingler à gauche",pinRight:"Épingler à droite",unpin:"Détacher"},columnMenu:{close:"Fermer"},gridMenu:{aria:{buttonLabel:"Menu du tableau"},columns:"Colonnes:",importerTitle:"Importer un fichier",exporterAllAsCsv:"Exporter toutes les données en CSV",exporterVisibleAsCsv:"Exporter les données visibles en CSV",exporterSelectedAsCsv:"Exporter les données sélectionnées en CSV",exporterAllAsPdf:"Exporter toutes les données en PDF",exporterVisibleAsPdf:"Exporter les données visibles en PDF",exporterSelectedAsPdf:"Exporter les données sélectionnées en PDF",exporterAllAsExcel:"Exporter toutes les données en Excel",exporterVisibleAsExcel:"Exporter les données visibles en Excel",exporterSelectedAsExcel:"Exporter les données sélectionnées en Excel",clearAllFilters:"Nettoyez tous les filtres"},importer:{noHeaders:"Impossible de déterminer le nom des colonnes, le fichier possède-t-il une en-tête ?",noObjects:"Aucun objet trouvé, le fichier possède-t-il des données autres que l'en-tête ?",invalidCsv:"Le fichier n'a pas pu être traité, le CSV est-il valide ?",invalidJson:"Le fichier n'a pas pu être traité, le JSON est-il valide ?",jsonNotArray:"Le fichier JSON importé doit contenir un tableau, abandon."},pagination:{aria:{pageToFirst:"Aller à la première page",pageBack:"Page précédente",pageSelected:"Page sélectionnée",pageForward:"Page suivante",pageToLast:"Aller à la dernière page"},sizes:"éléments par page",totalItems:"éléments",through:"à",of:"sur"},grouping:{group:"Grouper",ungroup:"Dégrouper",aggregate_count:"Agg: Compter",aggregate_sum:"Agg: Somme",aggregate_max:"Agg: Max",aggregate_min:"Agg: Min",aggregate_avg:"Agg: Moy",aggregate_remove:"Agg: Retirer"},validate:{error:"Erreur:",minLength:"La valeur doit être supérieure ou égale à THRESHOLD caractères.",maxLength:"La valeur doit être inférieure ou égale à THRESHOLD caractères.",required:"Une valeur est nécéssaire."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("he",{aggregate:{label:"items"},groupPanel:{description:"גרור עמודה לכאן ושחרר בכדי לקבץ עמודה זו."},search:{placeholder:"חפש...",showingItems:"מציג:",selectedItems:'סה"כ נבחרו:',totalItems:'סה"כ רשומות:',size:"תוצאות בדף:",first:"דף ראשון",next:"דף הבא",previous:"דף קודם",last:"דף אחרון"},menu:{text:"בחר עמודות:"},sort:{ascending:"סדר עולה",descending:"סדר יורד",remove:"בטל"},column:{hide:"טור הסתר"},aggregation:{count:"total rows: ",sum:"total: ",avg:"avg: ",min:"min: ",max:"max: "},gridMenu:{columns:"Columns:",importerTitle:"Import file",exporterAllAsCsv:"Export all data as csv",exporterVisibleAsCsv:"Export visible data as csv",exporterSelectedAsCsv:"Export selected data as csv",exporterAllAsPdf:"Export all data as pdf",exporterVisibleAsPdf:"Export visible data as pdf",exporterSelectedAsPdf:"Export selected data as pdf",exporterAllAsExcel:"Export all data as excel",exporterVisibleAsExcel:"Export visible data as excel",exporterSelectedAsExcel:"Export selected data as excel",clearAllFilters:"Clean all filters"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("hy",{aggregate:{label:"տվյալներ"},groupPanel:{description:"Ըստ սյան խմբավորելու համար քաշեք և գցեք վերնագիրն այստեղ։"},search:{placeholder:"Փնտրում...",showingItems:"Ցուցադրված տվյալներ՝",selectedItems:"Ընտրված:",totalItems:"Ընդամենը՝",size:"Տողերի քանակը էջում՝",first:"Առաջին էջ",next:"Հաջորդ էջ",previous:"Նախորդ էջ",last:"Վերջին էջ"},menu:{text:"Ընտրել սյուները:"},sort:{ascending:"Աճման կարգով",descending:"Նվազման կարգով",remove:"Հանել "},column:{hide:"Թաքցնել սյունը"},aggregation:{count:"ընդամենը տող՝ ",sum:"ընդամենը՝ ",avg:"միջին՝ ",min:"մին՝ ",max:"մաքս՝ "},pinning:{pinLeft:"Կպցնել ձախ կողմում",pinRight:"Կպցնել աջ կողմում",unpin:"Արձակել"},gridMenu:{columns:"Սյուներ:",importerTitle:"Ներմուծել ֆայլ",exporterAllAsCsv:"Արտահանել ամբողջը CSV",exporterVisibleAsCsv:"Արտահանել երևացող տվյալները CSV",exporterSelectedAsCsv:"Արտահանել ընտրված տվյալները CSV",exporterAllAsPdf:"Արտահանել PDF",exporterVisibleAsPdf:"Արտահանել երևացող տվյալները PDF",exporterSelectedAsPdf:"Արտահանել ընտրված տվյալները PDF",exporterAllAsExcel:"Արտահանել excel",exporterVisibleAsExcel:"Արտահանել երևացող տվյալները excel",exporterSelectedAsExcel:"Արտահանել ընտրված տվյալները excel",clearAllFilters:"Մաքրել բոլոր ֆիլտրերը"},importer:{noHeaders:"Հնարավոր չեղավ որոշել սյան վերնագրերը։ Արդյո՞ք ֆայլը ունի վերնագրեր։",noObjects:"Հնարավոր չեղավ կարդալ տվյալները։ Արդյո՞ք ֆայլում կան տվյալներ։",invalidCsv:"Հնարավոր չեղավ մշակել ֆայլը։ Արդյո՞ք այն վավեր CSV է։",invalidJson:"Հնարավոր չեղավ մշակել ֆայլը։ Արդյո՞ք այն վավեր Json է։",jsonNotArray:"Ներմուծված json ֆայլը պետք է պարունակի զանգված, կասեցվում է։"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("is",{headerCell:{aria:{defaultFilterLabel:"Sía fyrir dálk",removeFilter:"Fjarlægja síu",columnMenuButtonLabel:"Dálkavalmynd"},priority:"Forgangsröðun:",filterLabel:"Sía fyrir dálka: "},aggregate:{label:"hlutir"},groupPanel:{description:"Dragðu dálkhaus hingað til að flokka saman eftir þeim dálki."},search:{placeholder:"Leita...",showingItems:"Sýni hluti:",selectedItems:"Valdir hlutir:",totalItems:"Hlutir alls:",size:"Stærð síðu:",first:"Fyrsta síða",next:"Næsta síða",previous:"Fyrri síða",last:"Síðasta síða"},menu:{text:"Veldu dálka:"},sort:{ascending:"Raða hækkandi",descending:"Raða lækkandi",none:"Engin röðun",remove:"Fjarlægja röðun"},column:{hide:"Fela dálk"},aggregation:{count:"fjöldi raða: ",sum:"summa: ",avg:"meðaltal: ",min:"lágmark: ",max:"hámark: "},pinning:{pinLeft:"Festa til vinstri",pinRight:"Festa til hægri",unpin:"Losa"},columnMenu:{close:"Loka"},gridMenu:{aria:{buttonLabel:"Töflu valmynd"},columns:"Dálkar:",importerTitle:"Flytja inn skjal",exporterAllAsCsv:"Flytja út gögn sem csv",exporterVisibleAsCsv:"Flytja út sýnileg gögn sem csv",exporterSelectedAsCsv:"Flytja út valin gögn sem csv",exporterAllAsPdf:"Flytja út öll gögn sem pdf",exporterVisibleAsPdf:"Flytja út sýnileg gögn sem pdf",exporterSelectedAsPdf:"Flytja út valin gögn sem pdf",clearAllFilters:"Hreinsa allar síur"},importer:{noHeaders:"Ekki hægt að vinna dálkanöfn úr skjalinu, er skjalið örugglega með haus?",noObjects:"Ekki hægt að vinna hluti úr skjalinu, voru örugglega gögn í skjalinu önnur en hausinn?",invalidCsv:"Tókst ekki að vinna skjal, er það örggulega gilt CSV?",invalidJson:"Tókst ekki að vinna skjal, er það örugglega gilt Json?",jsonNotArray:"Innflutt json skjal verður að innihalda fylki, hætti við."},pagination:{aria:{pageToFirst:"Fletta að fyrstu",pageBack:"Fletta til baka",pageSelected:"Valin síða",pageForward:"Fletta áfram",pageToLast:"Fletta að síðustu"},sizes:"hlutir á síðu",totalItems:"hlutir",through:"gegnum",of:"af"},grouping:{group:"Flokka",ungroup:"Sundurliða",aggregate_count:"Fjöldi: ",aggregate_sum:"Summa: ",aggregate_max:"Hámark: ",aggregate_min:"Lágmark: ",aggregate_avg:"Meðaltal: ",aggregate_remove:"Fjarlægja: "},validate:{error:"Villa:",minLength:"Gildi ætti að vera a.m.k. THRESHOLD stafa langt.",maxLength:"Gildi ætti að vera í mesta lagi THRESHOLD stafa langt.",required:"Þarf að hafa gildi."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("it",{aggregate:{label:"elementi"},groupPanel:{description:"Trascina un'intestazione all'interno del gruppo della colonna."},search:{placeholder:"Ricerca...",showingItems:"Mostra:",selectedItems:"Selezionati:",totalItems:"Totali:",size:"Tot Pagine:",first:"Prima",next:"Prossima",previous:"Precedente",last:"Ultima"},selection:{aria:{row:"Riga"},selectAll:"Seleccionar Todo",displayName:"Seleziona Riga"},menu:{text:"Scegli le colonne:"},sort:{ascending:"Asc.",descending:"Desc.",remove:"Annulla ordinamento"},column:{hide:"Nascondi"},aggregation:{count:"righe totali: ",sum:"tot: ",avg:"media: ",min:"minimo: ",max:"massimo: "},pinning:{pinLeft:"Blocca a sx",pinRight:"Blocca a dx",unpin:"Blocca in alto"},gridMenu:{columns:"Colonne:",importerTitle:"Importa",exporterAllAsCsv:"Esporta tutti i dati in CSV",exporterVisibleAsCsv:"Esporta i dati visibili in CSV",exporterSelectedAsCsv:"Esporta i dati selezionati in CSV",exporterAllAsPdf:"Esporta tutti i dati in PDF",exporterVisibleAsPdf:"Esporta i dati visibili in PDF",exporterSelectedAsPdf:"Esporta i dati selezionati in PDF",exporterAllAsExcel:"Esporta tutti i dati in excel",exporterVisibleAsExcel:"Esporta i dati visibili in excel",exporterSelectedAsExcel:"Esporta i dati selezionati in excel",clearAllFilters:"Pulire tutti i filtri"},importer:{noHeaders:"Impossibile reperire i nomi delle colonne, sicuro che siano indicati all'interno del file?",noObjects:"Impossibile reperire gli oggetti, sicuro che siano indicati all'interno del file?",invalidCsv:"Impossibile elaborare il file, sicuro che sia un CSV?",invalidJson:"Impossibile elaborare il file, sicuro che sia un JSON valido?",jsonNotArray:"Errore! Il file JSON da importare deve contenere un array."},pagination:{aria:{pageToFirst:"Prima",pageBack:"Indietro",pageSelected:"Pagina selezionata",pageForward:"Avanti",pageToLast:"Ultima"},sizes:"elementi per pagina",totalItems:"elementi",through:"a",of:"di"},grouping:{group:"Raggruppa",ungroup:"Separa",aggregate_count:"Agg: N. Elem.",aggregate_sum:"Agg: Somma",aggregate_max:"Agg: Massimo",aggregate_min:"Agg: Minimo",aggregate_avg:"Agg: Media",aggregate_remove:"Agg: Rimuovi"},validate:{error:"Errore:",minLength:"Lunghezza minima pari a THRESHOLD caratteri.",maxLength:"Lunghezza massima pari a THRESHOLD caratteri.",required:"Necessario inserire un valore."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("ja",{headerCell:{aria:{defaultFilterLabel:"列のフィルター",removeFilter:"フィルターの解除",columnMenuButtonLabel:"列のメニュー"},priority:"優先度:",filterLabel:"列フィルター: "},aggregate:{label:"項目"},groupPanel:{description:"ここに列ヘッダをドラッグアンドドロップして、その列でグループ化します。"},search:{placeholder:"検索...",showingItems:"表示中の項目:",selectedItems:"選択した項目:",totalItems:"項目の総数:",size:"ページサイズ:",first:"最初のページ",next:"次のページ",previous:"前のページ",last:"前のページ"},menu:{text:"列の選択:"},sort:{ascending:"昇順に並べ替え",descending:"降順に並べ替え",none:"並べ替え無し",remove:"並べ替えの解除"},column:{hide:"列の非表示"},aggregation:{count:"行数: ",sum:"合計: ",avg:"平均: ",min:"最小: ",max:"最大: "},pinning:{pinLeft:"左に固定",pinRight:"右に固定",unpin:"固定解除"},columnMenu:{close:"閉じる"},gridMenu:{aria:{buttonLabel:"グリッドメニュー"},columns:"列の表示/非表示:",importerTitle:"ファイルのインポート",exporterAllAsCsv:"すべてのデータをCSV形式でエクスポート",exporterVisibleAsCsv:"表示中のデータをCSV形式でエクスポート",exporterSelectedAsCsv:"選択したデータをCSV形式でエクスポート",exporterAllAsPdf:"すべてのデータをPDF形式でエクスポート",exporterVisibleAsPdf:"表示中のデータをPDF形式でエクスポート",exporterSelectedAsPdf:"選択したデータをPDF形式でエクスポート",clearAllFilters:"すべてのフィルタをクリア"},importer:{noHeaders:"列名を取得できません。ファイルにヘッダが含まれていることを確認してください。",noObjects:"オブジェクトを取得できません。ファイルにヘッダ以外のデータが含まれていることを確認してください。",invalidCsv:"ファイルを処理できません。ファイルが有効なCSV形式であることを確認してください。",invalidJson:"ファイルを処理できません。ファイルが有効なJSON形式であることを確認してください。",jsonNotArray:"インポートしたJSONファイルには配列が含まれている必要があります。処理を中止します。"},pagination:{aria:{pageToFirst:"最初のページ",pageBack:"前のページ",pageSelected:"現在のページ",pageForward:"次のページ",pageToLast:"最後のページ"},sizes:"件/ページ",totalItems:"件",through:"から",of:"件/全"},grouping:{group:"グループ化",ungroup:"グループ化の解除",aggregate_count:"集計表示: 行数",aggregate_sum:"集計表示: 合計",aggregate_max:"集計表示: 最大",aggregate_min:"集計表示: 最小",aggregate_avg:"集計表示: 平均",aggregate_remove:"集計表示: 解除"},validate:{error:"Error:",minLength:"THRESHOLD 文字以上で入力してください。",maxLength:"THRESHOLD 文字以下で入力してください。",required:"値が必要です。"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("ko",{aggregate:{label:"아이템"},groupPanel:{description:"컬럼으로 그룹핑하기 위해서는 컬럼 헤더를 끌어 떨어뜨려 주세요."},search:{placeholder:"검색...",showingItems:"항목 보여주기:",selectedItems:"선택 항목:",totalItems:"전체 항목:",size:"페이지 크기:",first:"첫번째 페이지",next:"다음 페이지",previous:"이전 페이지",last:"마지막 페이지"},menu:{text:"컬럼을 선택하세요:"},sort:{ascending:"오름차순 정렬",descending:"내림차순 정렬",remove:"소팅 제거"},column:{hide:"컬럼 제거"},aggregation:{count:"전체 갯수: ",sum:"전체: ",avg:"평균: ",min:"최소: ",max:"최대: "},pinning:{pinLeft:"왼쪽 핀",pinRight:"오른쪽 핀",unpin:"핀 제거"},gridMenu:{columns:"컬럼:",importerTitle:"파일 가져오기",exporterAllAsCsv:"csv로 모든 데이터 내보내기",exporterVisibleAsCsv:"csv로 보이는 데이터 내보내기",exporterSelectedAsCsv:"csv로 선택된 데이터 내보내기",exporterAllAsPdf:"pdf로 모든 데이터 내보내기",exporterVisibleAsPdf:"pdf로 보이는 데이터 내보내기",exporterSelectedAsPdf:"pdf로 선택 데이터 내보내기",clearAllFilters:"모든 필터를 청소"},importer:{noHeaders:"컬럼명이 지정되어 있지 않습니다. 파일에 헤더가 명시되어 있는지 확인해 주세요.",noObjects:"데이터가 지정되어 있지 않습니다. 데이터가 파일에 있는지 확인해 주세요.",invalidCsv:"파일을 처리할 수 없습니다. 올바른 csv인지 확인해 주세요.",invalidJson:"파일을 처리할 수 없습니다. 올바른 json인지 확인해 주세요.",jsonNotArray:"json 파일은 배열을 포함해야 합니다."},pagination:{sizes:"페이지당 항목",totalItems:"전체 항목"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("nl",{aggregate:{label:"items"},groupPanel:{description:"Sleep hier een kolomnaam heen om op te groeperen."},search:{placeholder:"Zoeken...",showingItems:"Getoonde items:",selectedItems:"Geselecteerde items:",totalItems:"Totaal aantal items:",size:"Items per pagina:",first:"Eerste pagina",next:"Volgende pagina",previous:"Vorige pagina",last:"Laatste pagina"},menu:{text:"Kies kolommen:"},sort:{ascending:"Sorteer oplopend",descending:"Sorteer aflopend",remove:"Verwijder sortering"},column:{hide:"Verberg kolom"},aggregation:{count:"Aantal rijen: ",sum:"Som: ",avg:"Gemiddelde: ",min:"Min: ",max:"Max: "},pinning:{pinLeft:"Zet links vast",pinRight:"Zet rechts vast",unpin:"Maak los"},gridMenu:{columns:"Kolommen:",importerTitle:"Importeer bestand",exporterAllAsCsv:"Exporteer alle data als csv",exporterVisibleAsCsv:"Exporteer zichtbare data als csv",exporterSelectedAsCsv:"Exporteer geselecteerde data als csv",exporterAllAsPdf:"Exporteer alle data als pdf",exporterVisibleAsPdf:"Exporteer zichtbare data als pdf",exporterSelectedAsPdf:"Exporteer geselecteerde data als pdf",exporterAllAsExcel:"Exporteer alle data als excel",exporterVisibleAsExcel:"Exporteer zichtbare data als excel",exporterSelectedAsExcel:"Exporteer alle data als excel",clearAllFilters:"Alle filters wissen"},importer:{noHeaders:"Kolomnamen kunnen niet worden afgeleid. Heeft het bestand een header?",noObjects:"Objecten kunnen niet worden afgeleid. Bevat het bestand data naast de headers?",invalidCsv:"Het bestand kan niet verwerkt worden. Is het een valide csv bestand?",invalidJson:"Het bestand kan niet verwerkt worden. Is het valide json?",jsonNotArray:"Het json bestand moet een array bevatten. De actie wordt geannuleerd."},pagination:{sizes:"items per pagina",totalItems:"items",of:"van de"},grouping:{group:"Groepeer",ungroup:"Groepering opheffen",aggregate_count:"Agg: Aantal",aggregate_sum:"Agg: Som",aggregate_max:"Agg: Max",aggregate_min:"Agg: Min",aggregate_avg:"Agg: Gem",aggregate_remove:"Agg: Verwijder"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("no",{headerCell:{aria:{defaultFilterLabel:"Filter for kolonne",removeFilter:"Fjern filter",columnMenuButtonLabel:"Kolonnemeny"},priority:"Prioritet:",filterLabel:"Filter for kolonne: "},aggregate:{label:"elementer"},groupPanel:{description:"Trekk en kolonneoverskrift hit og slipp den for å gruppere etter den kolonnen."},search:{placeholder:"Søk...",showingItems:"Viste elementer:",selectedItems:"Valgte elementer:",totalItems:"Antall elementer:",size:"Sidestørrelse:",first:"Første side",next:"Neste side",previous:"Forrige side",last:"Siste side"},menu:{text:"Velg kolonner:"},sort:{ascending:"Sortere stigende",descending:"Sortere fallende",none:"Ingen sortering",remove:"Fjern sortering"},column:{hide:"Skjul kolonne"},aggregation:{count:"antall rader: ",sum:"total: ",avg:"gjennomsnitt: ",min:"minimum: ",max:"maksimum: "},pinning:{pinLeft:"Fest til venstre",pinRight:"Fest til høyre",unpin:"Løsne"},columnMenu:{close:"Lukk"},gridMenu:{aria:{buttonLabel:"Grid Menu"},columns:"Kolonner:",importerTitle:"Importer fil",exporterAllAsCsv:"Eksporter alle data som csv",exporterVisibleAsCsv:"Eksporter synlige data som csv",exporterSelectedAsCsv:"Eksporter utvalgte data som csv",exporterAllAsPdf:"Eksporter alle data som pdf",exporterVisibleAsPdf:"Eksporter synlige data som pdf",exporterSelectedAsPdf:"Eksporter utvalgte data som pdf",exporterAllAsExcel:"Eksporter alle data som excel",exporterVisibleAsExcel:"Eksporter synlige data som excel",exporterSelectedAsExcel:"Eksporter utvalgte data som excel",clearAllFilters:"Clear all filters"},importer:{noHeaders:"Kolonnenavn kunne ikke avledes. Har filen en overskrift?",noObjects:"Objekter kunne ikke avledes. Er der andre data i filen enn overskriften?",invalidCsv:"Filen kunne ikke behandles. Er den gyldig CSV?",invalidJson:"Filen kunne ikke behandles. Er den gyldig JSON?",jsonNotArray:"Importert JSON-fil må inneholde en liste. Avbryter."},pagination:{aria:{pageToFirst:"Gå til første side",pageBack:"Gå til forrige side",pageSelected:"Valgte side",pageForward:"Gå til neste side",pageToLast:"Gå til siste side"},sizes:"elementer per side",totalItems:"elementer",through:"til",of:"av"},grouping:{group:"Gruppere",ungroup:"Fjerne gruppering",aggregate_count:"Agr: Antall",aggregate_sum:"Agr: Sum",aggregate_max:"Agr: Maksimum",aggregate_min:"Agr: Minimum",aggregate_avg:"Agr: Gjennomsnitt",aggregate_remove:"Agr: Fjern"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("pl",{headerCell:{aria:{defaultFilterLabel:"Filtr dla kolumny",removeFilter:"Usuń filtr",columnMenuButtonLabel:"Opcje kolumny",column:"Kolumna"},priority:"Priorytet:",filterLabel:"Filtr dla kolumny: "},aggregate:{label:"pozycji"},groupPanel:{description:"Przeciągnij nagłówek kolumny tutaj, aby pogrupować według niej."},search:{aria:{selected:"Wiersz zaznaczony",notSelected:"Wiersz niezaznaczony"},placeholder:"Szukaj...",showingItems:"Widoczne pozycje:",selectedItems:"Zaznaczone pozycje:",totalItems:"Wszystkich pozycji:",size:"Rozmiar strony:",first:"Pierwsza strona",next:"Następna strona",previous:"Poprzednia strona",last:"Ostatnia strona"},menu:{text:"Wybierz kolumny:"},sort:{ascending:"Sortuj rosnąco",descending:"Sortuj malejąco",none:"Brak sortowania",remove:"Wyłącz sortowanie"},column:{hide:"Ukryj kolumnę"},aggregation:{count:"Razem pozycji: ",sum:"Razem: ",avg:"Średnia: ",min:"Min: ",max:"Max: "},pinning:{pinLeft:"Przypnij do lewej",pinRight:"Przypnij do prawej",unpin:"Odepnij"},columnMenu:{close:"Zamknij"},gridMenu:{aria:{buttonLabel:"Opcje tabeli"},columns:"Kolumny:",importerTitle:"Importuj plik",exporterAllAsCsv:"Eksportuj wszystkie dane do csv",exporterVisibleAsCsv:"Eksportuj widoczne dane do csv",exporterSelectedAsCsv:"Eksportuj zaznaczone dane do csv",exporterAllAsPdf:"Eksportuj wszystkie dane do pdf",exporterVisibleAsPdf:"Eksportuj widoczne dane do pdf",exporterSelectedAsPdf:"Eksportuj zaznaczone dane do pdf",exporterAllAsExcel:"Eksportuj wszystkie dane do excel",exporterVisibleAsExcel:"Eksportuj widoczne dane do excel",exporterSelectedAsExcel:"Eksportuj zaznaczone dane do excel",clearAllFilters:"Wyczyść filtry"},importer:{noHeaders:"Nie udało się wczytać nazw kolumn. Czy plik posiada nagłówek?",noObjects:"Nie udalo się wczytać pozycji. Czy plik zawiera dane?",invalidCsv:"Nie udało się przetworzyć pliku. Czy to prawidłowy plik CSV?",invalidJson:"Nie udało się przetworzyć pliku. Czy to prawidłowy plik JSON?",jsonNotArray:"Importowany plik JSON musi zawierać tablicę. Importowanie przerwane."},pagination:{aria:{pageToFirst:"Pierwsza strona",pageBack:"Poprzednia strona",pageSelected:"Wybrana strona",pageForward:"Następna strona",pageToLast:"Ostatnia strona"},sizes:"pozycji na stronę",totalItems:"pozycji",through:"do",of:"z"},grouping:{group:"Grupuj",ungroup:"Rozgrupuj",aggregate_count:"Zbiorczo: Razem",aggregate_sum:"Zbiorczo: Suma",aggregate_max:"Zbiorczo: Max",aggregate_min:"Zbiorczo: Min",aggregate_avg:"Zbiorczo: Średnia",aggregate_remove:"Zbiorczo: Usuń"},validate:{error:"Błąd:",minLength:"Wartość powinna składać się z co najmniej THRESHOLD znaków.",maxLength:"Wartość powinna składać się z przynajmniej THRESHOLD znaków.",required:"Wartość jest wymagana."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("pt-br",{headerCell:{aria:{defaultFilterLabel:"Filtro por coluna",removeFilter:"Remover filtro",columnMenuButtonLabel:"Menu coluna",column:"Coluna"},priority:"Prioridade:",filterLabel:"Filtro por coluna: "},aggregate:{label:"itens"},groupPanel:{description:"Arraste e solte uma coluna aqui para agrupar por essa coluna"},search:{aria:{selected:"Linha selecionada",notSelected:"Linha não está selecionada"},placeholder:"Procurar...",showingItems:"Mostrando os Itens:",selectedItems:"Items Selecionados:",totalItems:"Total de Itens:",size:"Tamanho da Página:",first:"Primeira Página",next:"Próxima Página",previous:"Página Anterior",last:"Última Página"},selection:{aria:{row:"Linha"},selectAll:"Selecionar Tudo",displayName:"Caixa de Seleção da Linha"},menu:{text:"Selecione as colunas:"},sort:{ascending:"Ordenar Ascendente",descending:"Ordenar Descendente",none:"Nenhuma Ordem",remove:"Remover Ordenação"},column:{hide:"Esconder coluna"},aggregation:{count:"total de linhas: ",sum:"total: ",avg:"med: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Fixar Esquerda",pinRight:"Fixar Direita",unpin:"Desprender"},columnMenu:{close:"Fechar"},gridMenu:{aria:{buttonLabel:"Menu Grid"},columns:"Colunas:",importerTitle:"Importar arquivo",exporterAllAsCsv:"Exportar todos os dados como csv",exporterVisibleAsCsv:"Exportar dados visíveis como csv",exporterSelectedAsCsv:"Exportar dados selecionados como csv",exporterAllAsPdf:"Exportar todos os dados como pdf",exporterVisibleAsPdf:"Exportar dados visíveis como pdf",exporterSelectedAsPdf:"Exportar dados selecionados como pdf",exporterAllAsExcel:"Exportar todos os dados como excel",exporterVisibleAsExcel:"Exportar dados visíveis como excel",exporterSelectedAsExcel:"Exportar dados selecionados como excel",clearAllFilters:"Limpar todos os filtros"},importer:{noHeaders:"Nomes de colunas não puderam ser derivados. O arquivo tem um cabeçalho?",noObjects:"Objetos não puderam ser derivados. Havia dados no arquivo, além dos cabeçalhos?",invalidCsv:"Arquivo não pode ser processado. É um CSV válido?",invalidJson:"Arquivo não pode ser processado. É um Json válido?",jsonNotArray:"Arquivo json importado tem que conter um array. Abortando."},pagination:{aria:{pageToFirst:"Primeira página",pageBack:"Página anterior",pageSelected:"Página Selecionada",pageForward:"Proxima",pageToLast:"Anterior"},sizes:"itens por página",totalItems:"itens",through:"através dos",of:"de"},grouping:{group:"Agrupar",ungroup:"Desagrupar",aggregate_count:"Agr: Contar",aggregate_sum:"Agr: Soma",aggregate_max:"Agr: Max",aggregate_min:"Agr: Min",aggregate_avg:"Agr: Med",aggregate_remove:"Agr: Remover"},validate:{error:"Erro:",minLength:"O valor deve ter, no minimo, THRESHOLD caracteres.",maxLength:"O valor deve ter, no máximo, THRESHOLD caracteres.",required:"Um valor é necessário."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("pt",{headerCell:{aria:{defaultFilterLabel:"Filtro por coluna",removeFilter:"Remover filtro",columnMenuButtonLabel:"Menu coluna",column:"Coluna"},priority:"Prioridade:",filterLabel:"Filtro por coluna: "},aggregate:{label:"itens"},groupPanel:{description:"Arraste e solte uma coluna aqui para agrupar por essa coluna"},search:{aria:{selected:"Linha selecionada",notSelected:"Linha não está selecionada"},placeholder:"Procurar...",showingItems:"Mostrando os Itens:",selectedItems:"Itens Selecionados:",totalItems:"Total de Itens:",size:"Tamanho da Página:",first:"Primeira Página",next:"Próxima Página",previous:"Página Anterior",last:"Última Página"},selection:{aria:{row:"Linha"},selectAll:"Selecionar Tudo",displayName:"Caixa de Seleção da Linha"},menu:{text:"Selecione as colunas:"},sort:{ascending:"Ordenar Ascendente",descending:"Ordenar Descendente",none:"Nenhuma Ordem",remove:"Remover Ordenação"},column:{hide:"Esconder coluna"},aggregation:{count:"total de linhas: ",sum:"total: ",avg:"med: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Fixar Esquerda",pinRight:"Fixar Direita",unpin:"Desprender"},columnMenu:{close:"Fechar"},gridMenu:{aria:{buttonLabel:"Menu Grid"},columns:"Colunas:",importerTitle:"Importar ficheiro",exporterAllAsCsv:"Exportar todos os dados como csv",exporterVisibleAsCsv:"Exportar dados visíveis como csv",exporterSelectedAsCsv:"Exportar dados selecionados como csv",exporterAllAsPdf:"Exportar todos os dados como pdf",exporterVisibleAsPdf:"Exportar dados visíveis como pdf",exporterSelectedAsPdf:"Exportar dados selecionados como pdf",exporterAllAsExcel:"Exportar todos os dados como excel",exporterVisibleAsExcel:"Exportar dados visíveis como excel",exporterSelectedAsExcel:"Exportar dados selecionados como excel",clearAllFilters:"Limpar todos os filtros"},importer:{noHeaders:"Nomes de colunas não puderam ser derivados. O ficheiro tem um cabeçalho?",noObjects:"Objetos não puderam ser derivados. Havia dados no ficheiro, além dos cabeçalhos?",invalidCsv:"Ficheiro não pode ser processado. É um CSV válido?",invalidJson:"Ficheiro não pode ser processado. É um Json válido?",jsonNotArray:"Ficheiro json importado tem que conter um array. Interrompendo."},pagination:{aria:{pageToFirst:"Primeira página",pageBack:"Página anterior",pageSelected:"Página Selecionada",pageForward:"Próxima",pageToLast:"Anterior"},sizes:"itens por página",totalItems:"itens",through:"a",of:"de"},grouping:{group:"Agrupar",ungroup:"Desagrupar",aggregate_count:"Agr: Contar",aggregate_sum:"Agr: Soma",aggregate_max:"Agr: Max",aggregate_min:"Agr: Min",aggregate_avg:"Agr: Med",aggregate_remove:"Agr: Remover"},validate:{error:"Erro:",minLength:"O valor deve ter, no minimo, THRESHOLD caracteres.",maxLength:"O valor deve ter, no máximo, THRESHOLD caracteres.",required:"Um valor é necessário."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("ro",{headerCell:{aria:{defaultFilterLabel:"Filtru pentru coloana",removeFilter:"Sterge filtru",columnMenuButtonLabel:"Column Menu"},priority:"Prioritate:",filterLabel:"Filtru pentru coloana:"},aggregate:{label:"Elemente"},groupPanel:{description:"Trage un cap de coloana aici pentru a grupa elementele dupa coloana respectiva"},search:{placeholder:"Cauta...",showingItems:"Arata elementele:",selectedItems:"Elementele selectate:",totalItems:"Total elemente:",size:"Marime pagina:",first:"Prima pagina",next:"Pagina urmatoare",previous:"Pagina anterioara",last:"Ultima pagina"},menu:{text:"Alege coloane:"},sort:{ascending:"Ordoneaza crescator",descending:"Ordoneaza descrescator",none:"Fara ordonare",remove:"Sterge ordonarea"},column:{hide:"Ascunde coloana"},aggregation:{count:"total linii: ",sum:"total: ",avg:"medie: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Pin la stanga",pinRight:"Pin la dreapta",unpin:"Sterge pinul"},columnMenu:{close:"Inchide"},gridMenu:{aria:{buttonLabel:"Grid Menu"},columns:"Coloane:",importerTitle:"Incarca fisier",exporterAllAsCsv:"Exporta toate datele ca csv",exporterVisibleAsCsv:"Exporta datele vizibile ca csv",exporterSelectedAsCsv:"Exporta datele selectate ca csv",exporterAllAsPdf:"Exporta toate datele ca pdf",exporterVisibleAsPdf:"Exporta datele vizibile ca pdf",exporterSelectedAsPdf:"Exporta datele selectate ca csv pdf",clearAllFilters:"Sterge toate filtrele"},importer:{noHeaders:"Numele coloanelor nu a putut fi incarcat, acest fisier are un header?",noObjects:"Datele nu au putut fi incarcate, exista date in fisier in afara numelor de coloane?",invalidCsv:"Fisierul nu a putut fi procesat, ati incarcat un CSV valid ?",invalidJson:"Fisierul nu a putut fi procesat, ati incarcat un Json valid?",jsonNotArray:"Json-ul incarcat trebuie sa contina un array, inchidere."},pagination:{aria:{pageToFirst:"Prima pagina",pageBack:"O pagina inapoi",pageSelected:"Pagina selectata",pageForward:"O pagina inainte",pageToLast:"Ultima pagina"},sizes:"Elemente per pagina",totalItems:"elemente",through:"prin",of:"of"},grouping:{group:"Grupeaza",ungroup:"Opreste gruparea",aggregate_count:"Agg: Count",aggregate_sum:"Agg: Sum",aggregate_max:"Agg: Max",aggregate_min:"Agg: Min",aggregate_avg:"Agg: Avg",aggregate_remove:"Agg: Remove"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("rs-lat",{headerCell:{aria:{defaultFilterLabel:"Filter za kolonu",removeFilter:"Ukloni Filter",columnMenuButtonLabel:"Meni Kolone",column:"Kolona"},priority:"Prioritet:",filterLabel:"Filter za kolonu: "},aggregate:{label:"stavke"},groupPanel:{description:"Ovde prevuci zaglavlje kolone i spusti do grupe pored te kolone."},search:{aria:{selected:"Red odabran",notSelected:"Red nije odabran"},placeholder:"Pretraga...",showingItems:"Prikazane Stavke:",selectedItems:"Odabrane Stavke:",totalItems:"Ukupno Stavki:",size:"Veličina Stranice:",first:"Prva Stranica",next:"Sledeća Stranica",previous:"Prethodna Stranica",last:"Poslednja Stranica"},menu:{text:"Odaberite kolonu:"},sort:{ascending:"Sortiraj po rastućem redosledu",descending:"Sortiraj po opadajućem redosledu",none:"Bez Sortiranja",remove:"Ukloni Sortiranje"},column:{hide:"Sakrij Kolonu"},aggregation:{count:"ukupno redova: ",sum:"ukupno: ",avg:"prosecno: ",min:"minimum: ",max:"maksimum: "},pinning:{pinLeft:"Zakači Levo",pinRight:"Zakači Desno",unpin:"Otkači"},columnMenu:{close:"Zatvori"},gridMenu:{aria:{buttonLabel:"Rešetkasti Meni"},columns:"Kolone:",importerTitle:"Importuj fajl",exporterAllAsCsv:"Eksportuj sve podatke kao csv",exporterVisibleAsCsv:"Eksportuj vidljive podatke kao csv",exporterSelectedAsCsv:"Eksportuj obeležene podatke kao csv",exporterAllAsPdf:"Eksportuj sve podatke kao pdf",exporterVisibleAsPdf:"Eksportuj vidljive podake kao pdf",exporterSelectedAsPdf:"Eksportuj odabrane podatke kao pdf",exporterAllAsExcel:"Eksportuj sve podatke kao excel",exporterVisibleAsExcel:"Eksportuj vidljive podatke kao excel",exporterSelectedAsExcel:"Eksportuj odabrane podatke kao excel",clearAllFilters:"Obriši sve filtere"},importer:{noHeaders:"Kolone se nisu mogle podeliti, da li fajl poseduje heder?",noObjects:"Objecti nisu mogli biti podeljeni, da li je bilo i drugih podataka sem hedera?",invalidCsv:"Fajl nije bilo moguće procesirati, da li je ispravni CSV?",invalidJson:"Fajl nije bilo moguće procesirati, da li je ispravni JSON",jsonNotArray:"Importovani json fajl mora da sadrži niz, prekidam operaciju."},pagination:{aria:{pageToFirst:"Prva stranica",pageBack:"Stranica pre",pageSelected:"Odabrana stranica",pageForward:"Sledeća stranica",pageToLast:"Poslednja stranica"},sizes:"stavki po stranici",totalItems:"stavke",through:"kroz",of:"od"},grouping:{group:"Grupiši",ungroup:"Odrupiši",aggregate_count:"Agg: Broj",aggregate_sum:"Agg: Suma",aggregate_max:"Agg: Maksimum",aggregate_min:"Agg: Minimum",aggregate_avg:"Agg: Prosečna",aggregate_remove:"Agg: Ukloni"},validate:{error:"Greška:",minLength:"Vrednost bi trebala da bude duga bar THRESHOLD karaktera.",maxLength:"Vrednost bi trebalo da bude najviše duga THRESHOLD karaktera.",required:"Portreba je vrednost."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("ru",{headerCell:{aria:{defaultFilterLabel:"Фильтр столбца",removeFilter:"Удалить фильтр",columnMenuButtonLabel:"Меню столбца"},priority:"Приоритет:",filterLabel:"Фильтр столбца: "},aggregate:{label:"элементы"},groupPanel:{description:"Для группировки по столбцу перетащите сюда его название."},search:{placeholder:"Поиск...",showingItems:"Показать элементы:",selectedItems:"Выбранные элементы:",totalItems:"Всего элементов:",size:"Размер страницы:",first:"Первая страница",next:"Следующая страница",previous:"Предыдущая страница",last:"Последняя страница"},menu:{text:"Выбрать столбцы:"},sort:{ascending:"По возрастанию",descending:"По убыванию",none:"Без сортировки",remove:"Убрать сортировку"},column:{hide:"Спрятать столбец"},aggregation:{count:"всего строк: ",sum:"итого: ",avg:"среднее: ",min:"мин: ",max:"макс: "},pinning:{pinLeft:"Закрепить слева",pinRight:"Закрепить справа",unpin:"Открепить"},columnMenu:{close:"Закрыть"},gridMenu:{aria:{buttonLabel:"Меню"},columns:"Столбцы:",importerTitle:"Импортировать файл",exporterAllAsCsv:"Экспортировать всё в CSV",exporterVisibleAsCsv:"Экспортировать видимые данные в CSV",exporterSelectedAsCsv:"Экспортировать выбранные данные в CSV",exporterAllAsPdf:"Экспортировать всё в PDF",exporterVisibleAsPdf:"Экспортировать видимые данные в PDF",exporterSelectedAsPdf:"Экспортировать выбранные данные в PDF",exporterAllAsExcel:"Экспортировать всё в Excel",exporterVisibleAsExcel:"Экспортировать видимые данные в Excel",exporterSelectedAsExcel:"Экспортировать выбранные данные в Excel",clearAllFilters:"Очистить все фильтры"},importer:{noHeaders:"Не удалось получить названия столбцов, есть ли в файле заголовок?",noObjects:"Не удалось получить данные, есть ли в файле строки кроме заголовка?",invalidCsv:"Не удалось обработать файл, это правильный CSV-файл?",invalidJson:"Не удалось обработать файл, это правильный JSON?",jsonNotArray:"Импортируемый JSON-файл должен содержать массив, операция отменена."},pagination:{aria:{pageToFirst:"Первая страница",pageBack:"Предыдущая страница",pageSelected:"Выбранная страница",pageForward:"Следующая страница",pageToLast:"Последняя страница"},sizes:"строк на страницу",totalItems:"строк",through:"по",of:"из"},grouping:{group:"Группировать",ungroup:"Разгруппировать",aggregate_count:"Группировать: Количество",aggregate_sum:"Для группы: Сумма",aggregate_max:"Для группы: Максимум",aggregate_min:"Для группы: Минимум",aggregate_avg:"Для группы: Среднее",aggregate_remove:"Для группы: Пусто"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("sk",{headerCell:{aria:{defaultFilterLabel:"Filter pre stĺpec",removeFilter:"Odstrániť filter",columnMenuButtonLabel:"Menu pre stĺpec",column:"Stĺpec"},priority:"Priorita:",filterLabel:"Filter pre stĺpec: "},aggregate:{label:"položky"},groupPanel:{description:"Pretiahni sem názov stĺpca pre zoskupenie podľa toho stĺpca."},search:{aria:{selected:"Označený riadok",notSelected:"Neoznačený riadok"},placeholder:"Hľadaj...",showingItems:"Zobrazujem položky:",selectedItems:"Vybraté položky:",totalItems:"Počet položiek:",size:"Počet:",first:"Prvá strana",next:"Ďalšia strana",previous:"Predchádzajúca strana",last:"Posledná strana"},menu:{text:"Vyberte stĺpce:"},sort:{ascending:"Zotriediť vzostupne",descending:"Zotriediť zostupne",none:"Nezotriediť",remove:"Vymazať triedenie"},column:{hide:"Skryť stĺpec"},aggregation:{count:"počet riadkov: ",sum:"spolu: ",avg:"avg: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Pripnúť vľavo",pinRight:"Pripnúť vpravo",unpin:"Odopnúť"},columnMenu:{close:"Zavrieť"},gridMenu:{aria:{buttonLabel:"Grid Menu"},columns:"Stĺpce:",importerTitle:"Importovať súbor",exporterAllAsCsv:"Exportovať všetky údaje ako CSV",exporterVisibleAsCsv:"Exportovť viditeľné údaje ako CSV",exporterSelectedAsCsv:"Exportovať označené údaje ako CSV",exporterAllAsPdf:"Exportovať všetky údaje ako pdf",exporterVisibleAsPdf:"Exportovať viditeľné údaje ako pdf",exporterSelectedAsPdf:"Exportovať označené údaje ako pdf",exporterAllAsExcel:"Exportovať všetky údaje ako excel",exporterVisibleAsExcel:"Exportovať viditeľné údaje ako excel",exporterSelectedAsExcel:"Exportovať označené údaje ako excel",clearAllFilters:"Zrušiť všetky filtre"},importer:{noHeaders:"Názvy stĺpcov sa nedali odvodiť, má súbor hlavičku?",noObjects:"Objekty nebolo možné odvodiť, existovali iné údaje v súbore ako hlavičky?",invalidCsv:"Súbor sa nepodarilo spracovať, je to platný súbor CSV?",invalidJson:"Súbor nebolo možné spracovať, je to platný súbor typu Json?",jsonNotArray:"Importovaný súbor json musí obsahovať pole, ukončujem."},pagination:{aria:{pageToFirst:"Strana na začiatok",pageBack:"Strana dozadu",pageSelected:"Označená strana",pageForward:"Strana dopredu",pageToLast:"Strana na koniec"},sizes:"položky na stranu",totalItems:"položky spolu",through:"do konca",of:"z"},grouping:{group:"Zoskupiť",ungroup:"Zrušiť zoskupenie",aggregate_count:"Agg: Počet",aggregate_sum:"Agg: Suma",aggregate_max:"Agg: Max",aggregate_min:"Agg: Min",aggregate_avg:"Agg: Avg",aggregate_remove:"Agg: Zrušiť"},validate:{error:"Chyba:",minLength:"Hodnota by mala mať aspoň THRESHOLD znakov dlhá.",maxLength:"Hodnota by mala byť maximálne THRESHOLD znakov dlhá.",required:"Vyžaduje sa hodnota."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("sv",{headerCell:{aria:{defaultFilterLabel:"Kolumnfilter",removeFilter:"Ta bort filter",columnMenuButtonLabel:"Kolumnmeny",column:"Kolumn"},priority:"Prioritet:",filterLabel:"Filter för kolumn: "},aggregate:{label:"Poster"},groupPanel:{description:"Dra en kolumnrubrik hit och släpp den för att gruppera efter den kolumnen."},search:{aria:{selected:"Rad är vald",notSelected:"Rad är inte vald"},placeholder:"Sök...",showingItems:"Visar:",selectedItems:"Valda:",totalItems:"Antal:",size:"Sidstorlek:",first:"Första sidan",next:"Nästa sida",previous:"Föregående sida",last:"Sista sidan"},menu:{text:"Välj kolumner:"},sort:{ascending:"Sortera stigande",descending:"Sortera fallande",none:"Ingen sortering",remove:"Inaktivera sortering"},column:{hide:"Göm kolumn"},aggregation:{count:"Antal rader: ",sum:"Summa: ",avg:"Genomsnitt: ",min:"Min: ",max:"Max: "},pinning:{pinLeft:"Fäst vänster",pinRight:"Fäst höger",unpin:"Lösgör"},columnMenu:{close:"Stäng"},gridMenu:{aria:{buttonLabel:"Meny"},columns:"Kolumner:",importerTitle:"Importera fil",exporterAllAsCsv:"Exportera all data som CSV",exporterVisibleAsCsv:"Exportera synlig data som CSV",exporterSelectedAsCsv:"Exportera markerad data som CSV",exporterAllAsPdf:"Exportera all data som PDF",exporterVisibleAsPdf:"Exportera synlig data som PDF",exporterSelectedAsPdf:"Exportera markerad data som PDF",exporterAllAsExcel:"Exportera all data till Excel",exporterVisibleAsExcel:"Exportera synlig data till Excel",exporterSelectedAsExcel:"Exportera markerad data till Excel",clearAllFilters:"Nollställ alla filter"},importer:{noHeaders:"Kolumnnamn kunde inte härledas. Har filen ett sidhuvud?",noObjects:"Objekt kunde inte härledas. Har filen data undantaget sidhuvud?",invalidCsv:"Filen kunde inte behandlas, är den en giltig CSV?",invalidJson:"Filen kunde inte behandlas, är den en giltig JSON?",jsonNotArray:"Importerad JSON-fil måste innehålla ett fält. Import avbruten."},pagination:{aria:{pageToFirst:"Gå till första sidan",pageBack:"Gå en sida bakåt",pageSelected:"Vald sida",pageForward:"Gå en sida framåt",pageToLast:"Gå till sista sidan"},sizes:"Poster per sida",totalItems:"Poster",through:"genom",of:"av"},grouping:{group:"Gruppera",ungroup:"Dela upp",aggregate_count:"Agg: Antal",aggregate_sum:"Agg: Summa",aggregate_max:"Agg: Max",aggregate_min:"Agg: Min",aggregate_avg:"Agg: Genomsnitt",aggregate_remove:"Agg: Ta bort"},validate:{error:"Error:",minLength:"Värdet borde vara minst THRESHOLD tecken långt.",maxLength:"Värdet borde vara max THRESHOLD tecken långt.",required:"Ett värde krävs."}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("ta",{aggregate:{label:"உருப்படிகள்"},groupPanel:{description:"ஒரு பத்தியை குழுவாக அமைக்க அப்பத்தியின் தலைப்பை இங்கே  இழுத்து வரவும் "},search:{placeholder:"தேடல் ...",showingItems:"உருப்படிகளை காண்பித்தல்:",selectedItems:"தேர்ந்தெடுக்கப்பட்ட  உருப்படிகள்:",totalItems:"மொத்த உருப்படிகள்:",size:"பக்க அளவு: ",first:"முதல் பக்கம்",next:"அடுத்த பக்கம்",previous:"முந்தைய பக்கம் ",last:"இறுதி பக்கம்"},menu:{text:"பத்திகளை தேர்ந்தெடு:"},sort:{ascending:"மேலிருந்து கீழாக",descending:"கீழிருந்து மேலாக",remove:"வரிசையை நீக்கு"},column:{hide:"பத்தியை மறைத்து வை "},aggregation:{count:"மொத்த வரிகள்:",sum:"மொத்தம்: ",avg:"சராசரி: ",min:"குறைந்தபட்ச: ",max:"அதிகபட்ச: "},pinning:{pinLeft:"இடதுபுறமாக தைக்க ",pinRight:"வலதுபுறமாக தைக்க",unpin:"பிரி"},gridMenu:{columns:"பத்திகள்:",importerTitle:"கோப்பு : படித்தல்",exporterAllAsCsv:"எல்லா தரவுகளையும் கோப்பாக்கு: csv",exporterVisibleAsCsv:"இருக்கும் தரவுகளை கோப்பாக்கு: csv",exporterSelectedAsCsv:"தேர்ந்தெடுத்த தரவுகளை கோப்பாக்கு: csv",exporterAllAsPdf:"எல்லா தரவுகளையும் கோப்பாக்கு: pdf",exporterVisibleAsPdf:"இருக்கும் தரவுகளை கோப்பாக்கு: pdf",exporterSelectedAsPdf:"தேர்ந்தெடுத்த தரவுகளை கோப்பாக்கு: pdf",clearAllFilters:"Clear all filters"},importer:{noHeaders:"பத்தியின் தலைப்புகளை பெற இயலவில்லை, கோப்பிற்கு தலைப்பு உள்ளதா?",noObjects:"இலக்குகளை உருவாக்க முடியவில்லை, கோப்பில் தலைப்புகளை தவிர தரவு ஏதேனும் உள்ளதா? ",invalidCsv:"சரிவர நடைமுறை படுத்த இயலவில்லை, கோப்பு சரிதானா? - csv",invalidJson:"சரிவர நடைமுறை படுத்த இயலவில்லை, கோப்பு சரிதானா? - json",jsonNotArray:"படித்த கோப்பில் வரிசைகள் உள்ளது, நடைமுறை ரத்து செய் : json"},pagination:{sizes:"உருப்படிகள் / பக்கம்",totalItems:"உருப்படிகள் "},grouping:{group:"குழு",ungroup:"பிரி",aggregate_count:"மதிப்பீட்டு : எண்ணு",aggregate_sum:"மதிப்பீட்டு : கூட்டல்",aggregate_max:"மதிப்பீட்டு : அதிகபட்சம்",aggregate_min:"மதிப்பீட்டு : குறைந்தபட்சம்",aggregate_avg:"மதிப்பீட்டு : சராசரி",aggregate_remove:"மதிப்பீட்டு : நீக்கு"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("tr",{headerCell:{aria:{defaultFilterLabel:"Sütun için filtre",removeFilter:"Filtreyi Kaldır",columnMenuButtonLabel:"Sütun Menüsü"},priority:"Öncelik:",filterLabel:"Sütun için filtre: "},aggregate:{label:"kayıtlar"},groupPanel:{description:"Sütuna göre gruplamak için sütun başlığını buraya sürükleyin ve bırakın."},search:{placeholder:"Arama...",showingItems:"Gösterilen Kayıt:",selectedItems:"Seçili Kayıt:",totalItems:"Toplam Kayıt:",size:"Sayfa Boyutu:",first:"İlk Sayfa",next:"Sonraki Sayfa",previous:"Önceki Sayfa",last:"Son Sayfa"},menu:{text:"Sütunları Seç:"},sort:{ascending:"Artan Sırada Sırala",descending:"Azalan Sırada Sırala",none:"Sıralama Yapma",remove:"Sıralamayı Kaldır"},column:{hide:"Sütunu Gizle"},aggregation:{count:"toplam satır: ",sum:"toplam: ",avg:"ort: ",min:"min: ",max:"maks: "},pinning:{pinLeft:"Sola Sabitle",pinRight:"Sağa Sabitle",unpin:"Sabitlemeyi Kaldır"},columnMenu:{close:"Kapat"},gridMenu:{aria:{buttonLabel:"Tablo Menü"},columns:"Sütunlar:",importerTitle:"Dosya içeri aktar",exporterAllAsCsv:"Bütün veriyi CSV olarak dışarı aktar",exporterVisibleAsCsv:"Görünen veriyi CSV olarak dışarı aktar",exporterSelectedAsCsv:"Seçili veriyi CSV olarak dışarı aktar",exporterAllAsPdf:"Bütün veriyi PDF olarak dışarı aktar",exporterVisibleAsPdf:"Görünen veriyi PDF olarak dışarı aktar",exporterSelectedAsPdf:"Seçili veriyi PDF olarak dışarı aktar",clearAllFilters:"Bütün filtreleri kaldır"},importer:{noHeaders:"Sütun isimleri üretilemiyor, dosyanın bir başlığı var mı?",noObjects:"Nesneler üretilemiyor, dosyada başlıktan başka bir veri var mı?",invalidCsv:"Dosya işlenemedi, geçerli bir CSV dosyası mı?",invalidJson:"Dosya işlenemedi, geçerli bir Json dosyası mı?",jsonNotArray:"Alınan Json dosyasında bir dizi bulunmalıdır, işlem iptal ediliyor."},pagination:{aria:{pageToFirst:"İlk sayfaya",pageBack:"Geri git",pageSelected:"Seçili sayfa",pageForward:"İleri git",pageToLast:"Sona git"},sizes:"Sayfadaki nesne sayısı",totalItems:"kayıtlar",through:"",of:""},grouping:{group:"Grupla",ungroup:"Gruplama",aggregate_count:"Yekun: Sayı",aggregate_sum:"Yekun: Toplam",aggregate_max:"Yekun: Maks",aggregate_min:"Yekun: Min",aggregate_avg:"Yekun: Ort",aggregate_remove:"Yekun: Sil"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("ua",{headerCell:{aria:{defaultFilterLabel:"Фільтр стовпчика",removeFilter:"Видалити фільтр",columnMenuButtonLabel:"Меню ствпчика"},priority:"Пріоритет:",filterLabel:"Фільтр стовпчика: "},aggregate:{label:"елементи"},groupPanel:{description:"Для групування за стовпчиком перетягніть сюди його назву."},search:{placeholder:"Пошук...",showingItems:"Показати елементи:",selectedItems:"Обрані елементи:",totalItems:"Усього елементів:",size:"Розмір сторінки:",first:"Перша сторінка",next:"Наступна сторінка",previous:"Попередня сторінка",last:"Остання сторінка"},menu:{text:"Обрати ствпчики:"},sort:{ascending:"За зростанням",descending:"За спаданням",none:"Без сортування",remove:"Прибрати сортування"},column:{hide:"Приховати стовпчик"},aggregation:{count:"усього рядків: ",sum:"ітого: ",avg:"середнє: ",min:"мін: ",max:"макс: "},pinning:{pinLeft:"Закріпити ліворуч",pinRight:"Закріпити праворуч",unpin:"Відкріпити"},columnMenu:{close:"Закрити"},gridMenu:{aria:{buttonLabel:"Меню"},columns:"Стовпчики:",importerTitle:"Імпортувати файл",exporterAllAsCsv:"Експортувати все в CSV",exporterVisibleAsCsv:"Експортувати видимі дані в CSV",exporterSelectedAsCsv:"Експортувати обрані дані в CSV",exporterAllAsPdf:"Експортувати все в PDF",exporterVisibleAsPdf:"Експортувати видимі дані в PDF",exporterSelectedAsPdf:"Експортувати обрані дані в PDF",clearAllFilters:"Очистити всі фільтри"},importer:{noHeaders:"Не вдалося отримати назви стовпчиків, чи є в файлі заголовок?",noObjects:"Не вдалося отримати дані, чи є в файлі рядки окрім заголовка?",invalidCsv:"Не вдалося обробити файл, чи це коректний CSV-файл?",invalidJson:"Не вдалося обробити файл, чи це коректний JSON?",jsonNotArray:"JSON-файл що імпортується повинен містити масив, операцію скасовано."},pagination:{aria:{pageToFirst:"Перша сторінка",pageBack:"Попередня сторінка",pageSelected:"Обрана сторінка",pageForward:"Наступна сторінка",pageToLast:"Остання сторінка"},sizes:"рядків на сторінку",totalItems:"рядків",through:"по",of:"з"},grouping:{group:"Групувати",ungroup:"Розгрупувати",aggregate_count:"Групувати: Кількість",aggregate_sum:"Для групи: Сума",aggregate_max:"Для групи: Максимум",aggregate_min:"Для групи: Мінімум",aggregate_avg:"Для групи: Серднє",aggregate_remove:"Для групи: Пусто"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("zh-cn",{headerCell:{aria:{defaultFilterLabel:"列过滤器",removeFilter:"移除过滤器",columnMenuButtonLabel:"列菜单"},priority:"优先级:",filterLabel:"列过滤器: "},aggregate:{label:"行"},groupPanel:{description:"拖曳表头到此处进行分组"},search:{placeholder:"查找",showingItems:"已显示行数：",selectedItems:"已选择行数：",totalItems:"总行数：",size:"每页显示行数：",first:"首页",next:"下一页",previous:"上一页",last:"末页"},menu:{text:"选择列："},sort:{ascending:"升序",descending:"降序",none:"无序",remove:"取消排序"},column:{hide:"隐藏列"},aggregation:{count:"计数：",sum:"求和：",avg:"均值：",min:"最小值：",max:"最大值："},pinning:{pinLeft:"左侧固定",pinRight:"右侧固定",unpin:"取消固定"},columnMenu:{close:"关闭"},gridMenu:{aria:{buttonLabel:"表格菜单"},columns:"列：",importerTitle:"导入文件",exporterAllAsCsv:"导出全部数据到CSV",exporterVisibleAsCsv:"导出可见数据到CSV",exporterSelectedAsCsv:"导出已选数据到CSV",exporterAllAsPdf:"导出全部数据到PDF",exporterVisibleAsPdf:"导出可见数据到PDF",exporterSelectedAsPdf:"导出已选数据到PDF",clearAllFilters:"清除所有过滤器"},importer:{noHeaders:"无法获取列名，确定文件包含表头？",noObjects:"无法获取数据，确定文件包含数据？",invalidCsv:"无法处理文件，确定是合法的CSV文件？",invalidJson:"无法处理文件，确定是合法的JSON文件？",jsonNotArray:"导入的文件不是JSON数组！"},pagination:{aria:{pageToFirst:"第一页",pageBack:"上一页",pageSelected:"当前页",pageForward:"下一页",pageToLast:"最后一页"},sizes:"行每页",totalItems:"行",through:"至",of:"共"},grouping:{group:"分组",ungroup:"取消分组",aggregate_count:"合计: 计数",aggregate_sum:"合计: 求和",aggregate_max:"合计: 最大",aggregate_min:"合计: 最小",aggregate_avg:"合计: 平均",aggregate_remove:"合计: 移除"}}),e}])}]),angular.module("ui.grid").config(["$provide",function(e){e.decorator("i18nService",["$delegate",function(e){return e.add("zh-tw",{aggregate:{label:"行"},groupPanel:{description:"拖曳表頭到此處進行分組"},search:{placeholder:"查找",showingItems:"已顯示行數：",selectedItems:"已選擇行數：",totalItems:"總行數：",size:"每頁顯示行數：",first:"首頁",next:"下壹頁",previous:"上壹頁",last:"末頁"},menu:{text:"選擇列："},sort:{ascending:"升序",descending:"降序",remove:"取消排序"},column:{hide:"隱藏列"},aggregation:{count:"計數：",sum:"求和：",avg:"均值：",min:"最小值：",max:"最大值："},pinning:{pinLeft:"左側固定",pinRight:"右側固定",unpin:"取消固定"},gridMenu:{columns:"列：",importerTitle:"導入文件",exporterAllAsCsv:"導出全部數據到CSV",exporterVisibleAsCsv:"導出可見數據到CSV",exporterSelectedAsCsv:"導出已選數據到CSV",exporterAllAsPdf:"導出全部數據到PDF",exporterVisibleAsPdf:"導出可見數據到PDF",exporterSelectedAsPdf:"導出已選數據到PDF",clearAllFilters:"清除所有过滤器"},importer:{noHeaders:"無法獲取列名，確定文件包含表頭？",noObjects:"無法獲取數據，確定文件包含數據？",invalidCsv:"無法處理文件，確定是合法的CSV文件？",invalidJson:"無法處理文件，確定是合法的JSON文件？",jsonNotArray:"導入的文件不是JSON數組！"},pagination:{sizes:"行每頁",totalItems:"行"}}),e}])}]),function(){"use strict";var e=angular.module("ui.grid.importer",["ui.grid"]);e.constant("uiGridImporterConstants",{featureName:"importer"}),e.service("uiGridImporterService",["$q","uiGridConstants","uiGridImporterConstants","gridUtil","$compile","$interval","i18nService","$window",function(e,i,t,n,r,o,a,l){var s={initializeGrid:function(e,t){t.importer={$scope:e},this.defaultGridOptions(t.options);var r={events:{importer:{}},methods:{importer:{importFile:function(e){s.importThisFile(t,e)}}}};t.api.registerEventsFromObject(r.events),t.api.registerMethodsFromObject(r.methods),t.options.enableImporter&&t.options.importerShowMenu&&(t.api.core.addToGridMenu?s.addToMenu(t):o(function(){t.api.core.addToGridMenu&&s.addToMenu(t)},100,1))},defaultGridOptions:function(e){e.enableImporter||void 0===e.enableImporter?l.hasOwnProperty("File")&&l.hasOwnProperty("FileReader")&&l.hasOwnProperty("FileList")&&l.hasOwnProperty("Blob")?e.enableImporter=!0:(n.logError("The File APIs are not fully supported in this browser, grid importer cannot be used."),e.enableImporter=!1):e.enableImporter=!1,e.importerProcessHeaders=e.importerProcessHeaders||s.processHeaders,e.importerHeaderFilter=e.importerHeaderFilter||function(e){return e},e.importerErrorCallback&&"function"==typeof e.importerErrorCallback||delete e.importerErrorCallback,!0!==e.enableImporter||e.importerDataAddCallback||(n.logError("You have not set an importerDataAddCallback, importer is disabled"),e.enableImporter=!1),e.importerShowMenu=!1!==e.importerShowMenu,e.importerObjectCallback=e.importerObjectCallback||function(e,t){return t}},addToMenu:function(e){e.api.core.addToGridMenu(e,[{title:a.getSafeText("gridMenu.importerTitle"),order:150},{templateUrl:"ui-grid/importerMenuItemContainer",action:function(){this.grid.api.importer.importAFile(e)},order:151}])},importThisFile:function(e,t){if(t){var r=new FileReader;switch(t.type){case"application/json":r.onload=s.importJsonClosure(e);break;default:r.onload=s.importCsvClosure(e)}r.readAsText(t)}else n.logError("No file object provided to importThisFile, should be impossible, aborting")},importJsonClosure:function(n){return function(e){var t,r=[],i=s.parseJson(n,e);null!==i&&(i.forEach(function(e){t=s.newObject(n),angular.extend(t,e),t=n.options.importerObjectCallback(n,t),r.push(t)}),s.addObjects(n,r))}},parseJson:function(t,r){var e;try{e=JSON.parse(r.target.result)}catch(e){return void s.alertError(t,"importer.invalidJson","File could not be processed, is it valid json? Content was: ",r.target.result)}return Array.isArray(e)?e:(s.alertError(t,"importer.jsonNotarray","Import failed, file is not an array, file was: ",r.target.result),[])},importCsvClosure:function(i){return function(e){var t=s.parseCsv(e);if(!t||t.length<1)s.alertError(i,"importer.invalidCsv","File could not be processed, is it valid csv? Content was: ",e.target.result);else{var r=s.createCsvObjects(i,t);r&&0!==r.length?s.addObjects(i,r):s.alertError(i,"importer.noObjects","Objects were not able to be derived, content was: ",e.target.result)}}},parseCsv:function(e){var t=e.target.result;return CSV.parse(t)},createCsvObjects:function(t,e){var r=t.options.importerProcessHeaders(t,e.shift());if(!r||0===r.length)return s.alertError(t,"importer.noHeaders","Column names could not be derived, content was: ",e),[];var i,n=[];return e.forEach(function(e){i=s.newObject(t),null!==e&&e.forEach(function(e,t){null!==r[t]&&(i[r[t]]=e)}),i=t.options.importerObjectCallback(t,i),n.push(i)}),n},processHeaders:function(e,t){var r=[];if(e.options.columnDefs&&0!==e.options.columnDefs.length){var i=s.flattenColumnDefs(e,e.options.columnDefs);return t.forEach(function(e){i[e]?r.push(i[e]):i[e.toLowerCase()]?r.push(i[e.toLowerCase()]):r.push(null)}),r}return t.forEach(function(e){r.push(e.replace(/[^0-9a-zA-Z\-_]/g,"_"))}),r},flattenColumnDefs:function(t,e){var r={};return e.forEach(function(e){e.name&&(r[e.name]=e.field||e.name,r[e.name.toLowerCase()]=e.field||e.name),e.field&&(r[e.field]=e.field||e.name,r[e.field.toLowerCase()]=e.field||e.name),e.displayName&&(r[e.displayName]=e.field||e.name,r[e.displayName.toLowerCase()]=e.field||e.name),e.displayName&&t.options.importerHeaderFilter&&(r[t.options.importerHeaderFilter(e.displayName)]=e.field||e.name,r[t.options.importerHeaderFilter(e.displayName).toLowerCase()]=e.field||e.name)}),r},addObjects:function(e,t){if(e.api.rowEdit){var r=e.registerDataChangeCallback(function(){e.api.rowEdit.setRowsDirty(t),r()},[i.dataChange.ROW]);e.importer.$scope.$on("$destroy",r)}e.importer.$scope.$apply(e.options.importerDataAddCallback(e,t))},newObject:function(e){return void 0!==e.options&&void 0!==e.options.importerNewObject?new e.options.importerNewObject:{}},alertError:function(e,t,r,i){e.options.importerErrorCallback?e.options.importerErrorCallback(e,t,r,i):(l.alert(a.getSafeText(t)),n.logError(r+i))}};return s}]),e.directive("uiGridImporter",["uiGridImporterConstants","uiGridImporterService","gridUtil","$compile",function(e,n,t,r){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,link:function(e,t,r,i){n.initializeGrid(e,i.grid)}}}]),e.directive("uiGridImporterMenuItem",["uiGridImporterConstants","uiGridImporterService","gridUtil","$compile",function(e,a,l,t){return{replace:!0,priority:0,require:"?^uiGrid",scope:!1,templateUrl:"ui-grid/importerMenuItem",link:function(e,t,r,i){var n;var o=t[0].querySelectorAll(".ui-grid-importer-file-chooser");1!==o.length?l.logError("Found > 1 or < 1 file choosers within the menu item, error, cannot continue"):o[0].addEventListener("change",function(e){var t=e.srcElement||e.target;if(t&&t.files&&1===t.files.length){var r=t.files[0];void 0!==i&&i?(n=i.grid,a.importThisFile(n,r),t.form.reset()):l.logError("Could not import file because UI Grid was not found.")}},!1)}}}])}(),function(){"use strict";var e=angular.module("ui.grid.infiniteScroll",["ui.grid"]);e.service("uiGridInfiniteScrollService",["gridUtil","$compile","$rootScope","uiGridConstants","ScrollEvent","$q",function(e,t,l,s,a,r){var d={initializeGrid:function(r,e){if(d.defaultGridOptions(r.options),r.options.enableInfiniteScroll){r.infiniteScroll={dataLoading:!1},d.setScrollDirections(r,r.options.infiniteScrollUp,r.options.infiniteScrollDown),r.api.core.on.scrollEnd(e,d.handleScroll);var t={events:{infiniteScroll:{needLoadMoreData:function(e,t){},needLoadMoreDataTop:function(e,t){}}},methods:{infiniteScroll:{dataLoaded:function(e,t){return d.setScrollDirections(r,e,t),d.adjustScroll(r).then(function(){r.infiniteScroll.dataLoading=!1})},resetScroll:function(e,t){d.setScrollDirections(r,e,t),d.adjustInfiniteScrollPosition(r,0)},saveScrollPercentage:function(){r.infiniteScroll.prevScrollTop=r.renderContainers.body.prevScrollTop,r.infiniteScroll.previousVisibleRows=r.getVisibleRowCount()},dataRemovedTop:function(e,t){d.dataRemovedTop(r,e,t)},dataRemovedBottom:function(e,t){d.dataRemovedBottom(r,e,t)},setScrollDirections:function(e,t){d.setScrollDirections(r,e,t)}}}};r.api.registerEventsFromObject(t.events),r.api.registerMethodsFromObject(t.methods)}},defaultGridOptions:function(e){e.enableInfiniteScroll=!1!==e.enableInfiniteScroll,e.infiniteScrollRowsFromEnd=e.infiniteScrollRowsFromEnd||20,e.infiniteScrollUp=!0===e.infiniteScrollUp,e.infiniteScrollDown=!1!==e.infiniteScrollDown},setScrollDirections:function(e,t,r){e.infiniteScroll.scrollUp=!0===t,e.suppressParentScrollUp=!0===t,e.infiniteScroll.scrollDown=!1!==r,e.suppressParentScrollDown=!1!==r},handleScroll:function(e){if(!(e.grid.infiniteScroll&&e.grid.infiniteScroll.dataLoading||"ui.grid.adjustInfiniteScrollPosition"===e.source)&&e.y)if(0===e.y.percentage)e.grid.scrollDirection=s.scrollDirection.UP,d.loadData(e.grid);else if(1===e.y.percentage)e.grid.scrollDirection=s.scrollDirection.DOWN,d.loadData(e.grid);else{var t=e.grid.options.infiniteScrollRowsFromEnd/e.grid.renderContainers.body.visibleRowCache.length;e.grid.scrollDirection===s.scrollDirection.UP?e.y.percentage<=t&&d.loadData(e.grid):e.grid.scrollDirection===s.scrollDirection.DOWN&&1-e.y.percentage<=t&&d.loadData(e.grid)}},loadData:function(e){e.infiniteScroll.previousVisibleRows=e.renderContainers.body.visibleRowCache.length,e.infiniteScroll.direction=e.scrollDirection,delete e.infiniteScroll.prevScrollTop,e.scrollDirection===s.scrollDirection.UP&&e.infiniteScroll.scrollUp?(e.infiniteScroll.dataLoading=!0,e.api.infiniteScroll.raise.needLoadMoreDataTop()):e.scrollDirection===s.scrollDirection.DOWN&&e.infiniteScroll.scrollDown&&(e.infiniteScroll.dataLoading=!0,e.api.infiniteScroll.raise.needLoadMoreData())},adjustScroll:function(o){var a=r.defer();return l.$applyAsync(function(){var e,t,r,i;e=o.getViewportHeight()+o.headerHeight-o.renderContainers.body.headerHeight-o.scrollbarHeight,t=o.options.rowHeight,void 0===o.infiniteScroll.direction&&d.adjustInfiniteScrollPosition(o,0);var n=t*(r=o.getVisibleRowCount());o.infiniteScroll.scrollDown&&n<e&&o.api.infiniteScroll.raise.needLoadMoreData(),o.infiniteScroll.direction===s.scrollDirection.UP&&(i=(o.infiniteScroll.prevScrollTop||0)+(r-o.infiniteScroll.previousVisibleRows)*t,d.adjustInfiniteScrollPosition(o,i),l.$applyAsync(function(){a.resolve()})),o.infiniteScroll.direction===s.scrollDirection.DOWN&&(i=o.infiniteScroll.prevScrollTop||o.infiniteScroll.previousVisibleRows*t-e,d.adjustInfiniteScrollPosition(o,i),l.$applyAsync(function(){a.resolve()}))},0),a.promise},adjustInfiniteScrollPosition:function(e,t){var r=new a(e,null,null,"ui.grid.adjustInfiniteScrollPosition"),i=e.getVisibleRowCount(),n=e.getViewportHeight()+e.headerHeight-e.renderContainers.body.headerHeight-e.scrollbarHeight,o=i*e.options.rowHeight-n;0===t&&e.infiniteScroll.scrollUp?r.y={pixels:1}:r.y={percentage:t/o},e.scrollContainers("",r)},dataRemovedTop:function(e,t,r){var i,n,o,a;d.setScrollDirections(e,t,r),i=e.renderContainers.body.visibleRowCache.length,n=e.infiniteScroll.prevScrollTop,a=e.options.rowHeight,o=n-(e.infiniteScroll.previousVisibleRows-i)*a,d.adjustInfiniteScrollPosition(e,o)},dataRemovedBottom:function(e,t,r){var i;d.setScrollDirections(e,t,r),i=e.infiniteScroll.prevScrollTop,d.adjustInfiniteScrollPosition(e,i)}};return d}]),e.directive("uiGridInfiniteScroll",["uiGridInfiniteScrollService",function(n){return{priority:-200,scope:!1,require:"^uiGrid",compile:function(){return{pre:function(e,t,r,i){n.initializeGrid(i.grid,e)},post:function(e,t,r){}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.moveColumns",["ui.grid"]);e.service("uiGridMoveColumnService",["$q","$rootScope","$log","ScrollEvent","uiGridConstants","gridUtil",function(i,d,e,t,c,s){var u={initializeGrid:function(e){this.registerPublicApi(e),this.defaultGridOptions(e.options),e.moveColumns={orderCache:[]},e.registerColumnBuilder(this.movableColumnBuilder),e.registerDataChangeCallback(this.verifyColumnOrder,[c.dataChange.COLUMN])},registerPublicApi:function(a){var l=this,e={events:{colMovable:{columnPositionChanged:function(e,t,r){}}},methods:{colMovable:{moveColumn:function(e,t){var i=a.columns;if(angular.isNumber(e)&&angular.isNumber(t)){for(var r=0,n=0;n<i.length;n++)(angular.isDefined(i[n].colDef.visible)&&!1===i[n].colDef.visible||!0===i[n].isRowHeader)&&r++;if(e>=i.length-r||t>=i.length-r)s.logError("MoveColumn: Invalid values for originalPosition, finalPosition");else{var o=function(e){for(var t=e,r=0;r<=t;r++)angular.isDefined(i[r])&&(angular.isDefined(i[r].colDef.visible)&&!1===i[r].colDef.visible||!0===i[r].isRowHeader)&&t++;return t};l.redrawColumnAtPosition(a,o(e),o(t))}}else s.logError("MoveColumn: Please provide valid values for originalPosition and finalPosition")}}}};a.api.registerEventsFromObject(e.events),a.api.registerMethodsFromObject(e.methods)},defaultGridOptions:function(e){e.enableColumnMoving=!1!==e.enableColumnMoving},movableColumnBuilder:function(e,t,r){return e.enableColumnMoving=void 0===e.enableColumnMoving?r.enableColumnMoving:e.enableColumnMoving,i.all([])},updateColumnCache:function(e){e.moveColumns.orderCache=e.getOnlyDataColumns()},verifyColumnOrder:function(i){var n,o=i.rowHeaderColumns.length;angular.forEach(i.moveColumns.orderCache,function(e,t){if(-1!==(n=i.columns.indexOf(e))&&n-o!==t){var r=i.columns.splice(n,1)[0];i.columns.splice(t+o,0,r)}})},redrawColumnAtPosition:function(e,t,r){var i=e.columns;if(t!==r){for(var n=t<r?t+1:t-1,o=Math.min(n,r);o<=Math.max(n,r)&&!i[o].visible;o++);if(!(o>Math.max(n,r))){var a=i[t];if(a.colDef.enableColumnMoving){if(r<t)for(var l=t;r<l;l--)i[l]=i[l-1];else if(t<r)for(var s=t;s<r;s++)i[s]=i[s+1];i[r]=a,u.updateColumnCache(e),e.queueGridRefresh(),d.$applyAsync(function(){e.api.core.notifyDataChange(c.dataChange.COLUMN),e.api.colMovable.raise.columnPositionChanged(a.colDef,t,r)})}}}}};return u}]),e.directive("uiGridMoveColumns",["uiGridMoveColumnService",function(n){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){n.initializeGrid(i.grid)},post:function(e,t,r,i){}}}}}]),e.directive("uiGridHeaderCell",["$q","gridUtil","uiGridMoveColumnService","$document","$log","uiGridConstants","ScrollEvent",function(e,t,b,y,r,i,S){return{priority:-10,require:"^uiGrid",compile:function(){return{post:function(c,i,e,u){if(c.col.colDef.enableColumnMoving){var g,n,p,f,m,h,t=angular.element(i[0].querySelectorAll(".ui-grid-cell-contents")),d=!1,v=!1,r=function(e){g=c.grid.element[0].getBoundingClientRect().left,c.grid.hasLeftContainer()&&(g+=c.grid.renderContainers.left.header[0].getBoundingClientRect().width),n=e.pageX||(e.originalEvent?e.originalEvent.pageX:0),p=0,f=g+c.grid.getViewportWidth(),"mousedown"===e.type?(y.on("mousemove",o),y.on("mouseup",a)):"touchstart"===e.type&&(y.on("touchmove",o),y.on("touchend",a))},o=function(e){var t=e.pageX||(e.originalEvent?e.originalEvent.pageX:0),r=t-n;0!==r&&(document.onselectstart=function(){return!1},v=!0,d?d&&(s(r),n=t):l())},a=function(e){if(document.onselectstart=null,m&&(m.remove(),d=!1),w(),C(),v){for(var t,r=c.grid.columns,i=0,n=0;n<r.length&&r[n].colDef.name!==c.col.colDef.name;n++)i++;if(p<0){var o,a=0;if(c.grid.isRTL()){for(o=i+1;o<r.length;o++)if((angular.isUndefined(r[o].colDef.visible)||!0===r[o].colDef.visible)&&(a+=r[o].drawnWidth||r[o].width||r[o].colDef.width)>Math.abs(p)){b.redrawColumnAtPosition(c.grid,i,o-1);break}}else for(o=i-1;0<=o;o--)if((angular.isUndefined(r[o].colDef.visible)||!0===r[o].colDef.visible)&&(a+=r[o].drawnWidth||r[o].width||r[o].colDef.width)>Math.abs(p)){b.redrawColumnAtPosition(c.grid,i,o+1);break}a<Math.abs(p)&&(t=0,c.grid.isRTL()&&(t=r.length-1),b.redrawColumnAtPosition(c.grid,i,t))}else if(0<p){var l,s=0;if(c.grid.isRTL()){for(l=i-1;0<l;l--)if((angular.isUndefined(r[l].colDef.visible)||!0===r[l].colDef.visible)&&(s+=r[l].drawnWidth||r[l].width||r[l].colDef.width,p<s)){b.redrawColumnAtPosition(c.grid,i,l);break}}else for(l=i+1;l<r.length;l++)if((angular.isUndefined(r[l].colDef.visible)||!0===r[l].colDef.visible)&&(s+=r[l].drawnWidth||r[l].width||r[l].colDef.width,p<s)){b.redrawColumnAtPosition(c.grid,i,l-1);break}s<p&&(t=r.length-1,c.grid.isRTL()&&(t=0),b.redrawColumnAtPosition(c.grid,i,t))}}},C=function(){t.on("touchstart",r),t.on("mousedown",r)},w=function(){t.off("touchstart",r),t.off("mousedown",r),y.off("mousemove",o),y.off("touchmove",o),y.off("mouseup",a),y.off("touchend",a)};C();var l=function(){d=!0,m=i.clone(),i.parent().append(m),m.addClass("movingColumn");var e={};e.left=i[0].offsetLeft+"px";var t=c.grid.element[0].getBoundingClientRect().right,r=i[0].getBoundingClientRect().right;t<r&&(h=c.col.drawnWidth+(t-r),e.width=h+"px"),m.css(e)},s=function(e){for(var t=c.grid.columns,r=0,i=0;i<t.length;i++)(angular.isUndefined(t[i].colDef.visible)||!0===t[i].colDef.visible)&&(r+=t[i].drawnWidth||t[i].width||t[i].colDef.width);var n,o=m[0].getBoundingClientRect().left-1,a=m[0].getBoundingClientRect().right;if(n=(n=o-g+e)<f?n:f,(g<=o||0<e)&&(a<=f||e<0))m.css({visibility:"visible",left:m[0].offsetLeft+(n<f?e:f-o)+"px"});else if(r>Math.ceil(u.grid.gridWidth)){e*=8;var l=new S(c.col.grid,null,null,"uiGridHeaderCell.moveElement");l.x={pixels:e},l.grid.scrollContainers("",l)}for(var s=0,d=0;d<t.length;d++)if(angular.isUndefined(t[d].colDef.visible)||!0===t[d].colDef.visible){if(t[d].colDef.name===c.col.colDef.name)break;s+=t[d].drawnWidth||t[d].width||t[d].colDef.width}void 0===c.newScrollLeft?p+=e:p=c.newScrollLeft+n-s,h<c.col.drawnWidth&&(h+=Math.abs(e),m.css({width:h+"px"}))};c.$on("$destroy",w)}}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.pagination",["ng","ui.grid"]);e.service("uiGridPaginationService",["gridUtil",function(t){var e={initializeGrid:function(o){e.defaultGridOptions(o.options);var a={events:{pagination:{paginationChanged:function(e,t){}}},methods:{pagination:{getPage:function(){return o.options.enablePagination?o.options.paginationCurrentPage:null},getFirstRowIndex:function(){return o.options.useCustomPagination?o.options.paginationPageSizes.reduce(function(e,t,r){return r<o.options.paginationCurrentPage-1?e+t:e},0):(o.options.paginationCurrentPage-1)*o.options.paginationPageSize},getLastRowIndex:function(){return o.options.useCustomPagination?a.methods.pagination.getFirstRowIndex()+o.options.paginationPageSizes[o.options.paginationCurrentPage-1]-1:Math.min(o.options.paginationCurrentPage*o.options.paginationPageSize,o.options.totalItems)-1},getTotalPages:function(){return o.options.enablePagination?o.options.useCustomPagination?o.options.paginationPageSizes.length:0===o.options.totalItems?1:Math.ceil(o.options.totalItems/o.options.paginationPageSize):null},nextPage:function(){o.options.enablePagination&&(0<o.options.totalItems?o.options.paginationCurrentPage=Math.min(o.options.paginationCurrentPage+1,a.methods.pagination.getTotalPages()):o.options.paginationCurrentPage++)},previousPage:function(){o.options.enablePagination&&(o.options.paginationCurrentPage=Math.max(o.options.paginationCurrentPage-1,1))},seek:function(e){if(o.options.enablePagination){if(!angular.isNumber(e)||e<1)throw"Invalid page number: "+e;o.options.paginationCurrentPage=Math.min(e,a.methods.pagination.getTotalPages())}}}}};o.api.registerEventsFromObject(a.events),o.api.registerMethodsFromObject(a.methods);o.registerRowsProcessor(function(e){if(o.options.useExternalPagination||!o.options.enablePagination)return e;var t=parseInt(o.options.paginationPageSize,10),r=(parseInt(o.options.paginationCurrentPage,10),e.filter(function(e){return e.visible}));o.options.totalItems=r.length;var i=a.methods.pagination.getFirstRowIndex(),n=a.methods.pagination.getLastRowIndex();return i>r.length&&(i=((o.options.paginationCurrentPage=1)-1)*t),r.slice(i,n+1)},900)},defaultGridOptions:function(e){e.enablePagination=!1!==e.enablePagination,e.enablePaginationControls=!1!==e.enablePaginationControls,e.useExternalPagination=!0===e.useExternalPagination,e.useCustomPagination=!0===e.useCustomPagination,t.isNullOrUndefined(e.totalItems)&&(e.totalItems=0),t.isNullOrUndefined(e.paginationPageSizes)&&(e.paginationPageSizes=[250,500,1e3]),t.isNullOrUndefined(e.paginationPageSize)&&(0<e.paginationPageSizes.length?e.paginationPageSize=e.paginationPageSizes[0]:e.paginationPageSize=0),t.isNullOrUndefined(e.paginationCurrentPage)&&(e.paginationCurrentPage=1),t.isNullOrUndefined(e.paginationTemplate)&&(e.paginationTemplate="ui-grid/pagination")},onPaginationChanged:function(e,t,r){e.api.pagination.raise.paginationChanged(t,r),e.options.useExternalPagination||e.queueGridRefresh()}};return e}]),e.directive("uiGridPagination",["gridUtil","uiGridPaginationService",function(n,o){return{priority:-200,scope:!1,require:"uiGrid",link:{pre:function(e,r,t,i){o.initializeGrid(i.grid),n.getTemplate(i.grid.options.paginationTemplate).then(function(e){var t=angular.element(e);r.append(t),i.innerCompile(t)})}}}}]),e.directive("uiGridPager",["uiGridPaginationService","uiGridConstants","gridUtil","i18nService","i18nConstants",function(d,c,u,g,p){return{priority:-200,scope:!0,require:"^uiGrid",link:function(r,t,e,i){r.aria=g.getSafeText("pagination.aria");var n=function(){r.paginationApi=i.grid.api.pagination,r.sizesLabel=g.getSafeText("pagination.sizes"),r.totalItemsLabel=g.getSafeText("pagination.totalItems"),r.paginationOf=g.getSafeText("pagination.of"),r.paginationThrough=g.getSafeText("pagination.through")};n(),r.$on(p.UPDATE_EVENT,n);var o=i.grid.options;i.grid.renderContainers.body.registerViewportAdjuster(function(e){return o.enablePaginationControls&&(e.height=e.height-u.elementHeight(t,"padding")),e});var a=i.grid.registerDataChangeCallback(function(e){e.options.useExternalPagination||(e.options.totalItems=e.rows.length)},[c.dataChange.ROW]);r.$on("$destroy",a);var l=r.$watch("grid.options.paginationCurrentPage + grid.options.paginationPageSize",function(e,t){e!==t&&void 0!==t&&(!angular.isNumber(o.paginationCurrentPage)||o.paginationCurrentPage<1?o.paginationCurrentPage=1:0<o.totalItems&&o.paginationCurrentPage>r.paginationApi.getTotalPages()?o.paginationCurrentPage=r.paginationApi.getTotalPages():d.onPaginationChanged(r.grid,o.paginationCurrentPage,o.paginationPageSize))});r.$on("$destroy",function(){l()}),r.cantPageForward=function(){return r.paginationApi.getTotalPages()?r.cantPageToLast():o.data.length<1},r.cantPageToLast=function(){var e=r.paginationApi.getTotalPages();return!e||o.paginationCurrentPage>=e},r.cantPageBackward=function(){return o.paginationCurrentPage<=1};var s=function(e){e&&u.focus.bySelector(t,".ui-grid-pager-control-input")};r.pageFirstPageClick=function(){r.paginationApi.seek(1),s(r.cantPageBackward())},r.pagePreviousPageClick=function(){r.paginationApi.previousPage(),s(r.cantPageBackward())},r.pageNextPageClick=function(){r.paginationApi.nextPage(),s(r.cantPageForward())},r.pageLastPageClick=function(){r.paginationApi.seek(r.paginationApi.getTotalPages()),s(r.cantPageToLast())}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.pinning",["ui.grid"]);e.constant("uiGridPinningConstants",{container:{LEFT:"left",RIGHT:"right",NONE:""}}),e.service("uiGridPinningService",["gridUtil","GridRenderContainer","i18nService","uiGridPinningConstants",function(a,e,l,s){var d={initializeGrid:function(r){d.defaultGridOptions(r.options),r.registerColumnBuilder(d.pinningColumnBuilder);var e={events:{pinning:{columnPinned:function(e,t){}}},methods:{pinning:{pinColumn:function(e,t){d.pinColumn(r,e,t)}}}};r.api.registerEventsFromObject(e.events),r.api.registerMethodsFromObject(e.methods)},defaultGridOptions:function(e){e.enablePinning=!1!==e.enablePinning,e.hidePinLeft=e.enablePinning&&e.hidePinLeft,e.hidePinRight=e.enablePinning&&e.hidePinRight},pinningColumnBuilder:function(e,t,r){if(e.enablePinning=void 0===e.enablePinning?r.enablePinning:e.enablePinning,e.hidePinLeft=void 0===e.hidePinLeft?r.hidePinLeft:e.hidePinLeft,e.hidePinRight=void 0===e.hidePinRight?r.hidePinRight:e.hidePinRight,e.pinnedLeft?(t.renderContainer="left",t.grid.createLeftContainer()):e.pinnedRight&&(t.renderContainer="right",t.grid.createRightContainer()),e.enablePinning){var i={name:"ui.grid.pinning.pinLeft",title:l.get().pinning.pinLeft,icon:"ui-grid-icon-left-open",shown:function(){return void 0===this.context.col.renderContainer||!this.context.col.renderContainer||"left"!==this.context.col.renderContainer},action:function(){d.pinColumn(this.context.col.grid,this.context.col,s.container.LEFT)}},n={name:"ui.grid.pinning.pinRight",title:l.get().pinning.pinRight,icon:"ui-grid-icon-right-open",shown:function(){return void 0===this.context.col.renderContainer||!this.context.col.renderContainer||"right"!==this.context.col.renderContainer},action:function(){d.pinColumn(this.context.col.grid,this.context.col,s.container.RIGHT)}},o={name:"ui.grid.pinning.unpin",title:l.get().pinning.unpin,icon:"ui-grid-icon-cancel",shown:function(){return void 0!==this.context.col.renderContainer&&null!==this.context.col.renderContainer&&"body"!==this.context.col.renderContainer},action:function(){d.pinColumn(this.context.col.grid,this.context.col,s.container.NONE)}};e.hidePinLeft||a.arrayContainsObjectWithProperty(t.menuItems,"name","ui.grid.pinning.pinLeft")||t.menuItems.push(i),e.hidePinRight||a.arrayContainsObjectWithProperty(t.menuItems,"name","ui.grid.pinning.pinRight")||t.menuItems.push(n),a.arrayContainsObjectWithProperty(t.menuItems,"name","ui.grid.pinning.unpin")||t.menuItems.push(o)}},pinColumn:function(e,t,r){r===s.container.NONE?(t.renderContainer=null,t.colDef.pinnedLeft=t.colDef.pinnedRight=!1):(t.renderContainer=r)===s.container.LEFT?e.createLeftContainer():r===s.container.RIGHT&&e.createRightContainer(),e.refresh().then(function(){e.api.pinning.raise.columnPinned(t.colDef,r)})}};return d}]),e.directive("uiGridPinning",["gridUtil","uiGridPinningService",function(e,n){return{require:"uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){n.initializeGrid(i.grid)},post:function(e,t,r,i){}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.resizeColumns",["ui.grid"]);e.service("uiGridResizeColumnsService",["gridUtil","$q","$rootScope",function(i,n,o){return{defaultGridOptions:function(e){e.enableColumnResizing=!1!==e.enableColumnResizing,!1===e.enableColumnResize&&(e.enableColumnResizing=!1)},colResizerColumnBuilder:function(e,t,r){return e.enableColumnResizing=void 0===e.enableColumnResizing?r.enableColumnResizing:e.enableColumnResizing,!1===e.enableColumnResize&&(e.enableColumnResizing=!1),n.all([])},registerPublicApi:function(e){e.api.registerEventsFromObject({colResizable:{columnSizeChanged:function(e,t){}}})},fireColumnSizeChanged:function(e,t,r){o.$applyAsync(function(){e.api.colResizable?e.api.colResizable.raise.columnSizeChanged(t,r):i.logError("The resizeable api is not registered, this may indicate that you've included the module but not added the 'ui-grid-resize-columns' directive to your grid definition.  Cannot raise any events.")})},findTargetCol:function(e,t,r){var i=e.getRenderContainer();if("left"!==t)return e;var n=i.visibleColumnCache.indexOf(e);return 0===n?i.visibleColumnCache[0]:i.visibleColumnCache[n-1*r]}}}]),e.directive("uiGridResizeColumns",["gridUtil","uiGridResizeColumnsService",function(e,n){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){n.defaultGridOptions(i.grid.options),i.grid.registerColumnBuilder(n.colResizerColumnBuilder),n.registerPublicApi(i.grid)},post:function(e,t,r,i){}}}}}]),e.directive("uiGridHeaderCell",["gridUtil","$templateCache","$compile","$q","uiGridResizeColumnsService","uiGridConstants",function(e,o,c,t,u,g){return{priority:-10,require:"^uiGrid",compile:function(){return{post:function(a,l,e,t){var r=t.grid;if(r.options.enableColumnResizing){var s=o.get("ui-grid/columnResizer"),d=1;r.isRTL()&&(a.position="left",d=-1);var i=function(){for(var e=l[0].getElementsByClassName("ui-grid-column-resizer"),t=0;t<e.length;t++)angular.element(e[t]).remove();var r=u.findTargetCol(a.col,"left",d),i=a.col.getRenderContainer();if(r&&0!==i.visibleColumnCache.indexOf(a.col)&&!1!==r.colDef.enableColumnResizing){var n=angular.element(s).clone();n.attr("position","left"),l.prepend(n),c(n)(a)}if(!1!==a.col.colDef.enableColumnResizing){var o=angular.element(s).clone();o.attr("position","right"),l.append(o),c(o)(a)}};i();var n=r.registerDataChangeCallback(function(){a.$applyAsync(i)},[g.dataChange.COLUMN]);a.$on("$destroy",n)}}}}}}]),e.directive("uiGridColumnResizer",["$document","gridUtil","uiGridConstants","uiGridResizeColumnsService",function(h,v,C,w){var b=angular.element('<div class="ui-grid-resize-overlay"></div>');return{priority:0,scope:{col:"=",position:"@",renderIndex:"="},require:"?^uiGrid",link:function(l,s,e,d){var o=0,a=0,c=0,u=1;function g(e){d.grid.refreshCanvas(!0).then(function(){d.grid.queueGridRefresh()})}function p(e,t){var r=t;return e.minWidth&&r<e.minWidth?r=e.minWidth:e.maxWidth&&r>e.maxWidth&&(r=e.maxWidth),r}function r(e,t){e.originalEvent&&(e=e.originalEvent),e.preventDefault(),(a=(e.targetTouches?e.targetTouches[0]:e).clientX-c)<0?a=0:a>d.grid.gridWidth&&(a=d.grid.gridWidth);var r=w.findTargetCol(l.col,l.position,u);if(!1!==r.colDef.enableColumnResizing){d.grid.element.hasClass("column-resizing")||d.grid.element.addClass("column-resizing");var i=a-o,n=parseInt(r.drawnWidth+i*u,10);a+=(p(r,n)-n)*u,b.css({left:a+"px"}),d.fireEvent(C.events.ITEM_DRAGGING)}}function i(e){e.originalEvent&&(e=e.originalEvent),e.preventDefault(),d.grid.element.removeClass("column-resizing"),b.remove();var t=(a=(e.changedTouches?e.changedTouches[0]:e).clientX-c)-o;if(0===t)return m(),void f();var r=w.findTargetCol(l.col,l.position,u);if(!1!==r.colDef.enableColumnResizing){var i=parseInt(r.drawnWidth+t*u,10);r.width=p(r,i),r.hasCustomWidth=!0,g(),w.fireColumnSizeChanged(d.grid,r.colDef,t),m(),f()}}d.grid.isRTL()&&(l.position="left",u=-1),"left"===l.position?s.addClass("left"):"right"===l.position&&s.addClass("right");var n=function(e,t){e.originalEvent&&(e=e.originalEvent),e.stopPropagation(),c=d.grid.element[0].getBoundingClientRect().left,o=(e.targetTouches?e.targetTouches[0]:e).clientX-c,d.grid.element.append(b),b.css({left:o}),"touchstart"===e.type?(h.on("touchend",i),h.on("touchmove",r),s.off("mousedown",n)):(h.on("mouseup",i),h.on("mousemove",r),s.off("touchstart",n))},f=function(){s.on("mousedown",n),s.on("touchstart",n)},m=function(){h.off("mouseup",i),h.off("touchend",i),h.off("mousemove",r),h.off("touchmove",r),s.off("mousedown",n),s.off("touchstart",n)};f();var t=function(e,t){e.stopPropagation();var r=w.findTargetCol(l.col,l.position,u);if(!1!==r.colDef.enableColumnResizing){var n=0,i=v.closestElm(s,".ui-grid-render-container").querySelectorAll("."+C.COL_CLASS_PREFIX+r.uid+" .ui-grid-cell-contents");Array.prototype.forEach.call(i,function(e){var i;angular.element(e).parent().hasClass("ui-grid-header-cell")&&(i=angular.element(e).parent()[0].querySelectorAll(".ui-grid-column-menu-button")),v.fakeElement(e,{},function(e){var t=angular.element(e);t.attr("style","float: left");var r=v.elementWidth(t)+2;i&&(r+=v.elementWidth(i));n<r&&(n=r)})});var o=p(r,n),a=o-r.drawnWidth;r.width=o,r.hasCustomWidth=!0,g(),w.fireColumnSizeChanged(d.grid,r.colDef,a)}};s.on("dblclick",t),s.on("$destroy",function(){s.off("dblclick",t),m()})}}}])}(),function(){"use strict";var e=angular.module("ui.grid.rowEdit",["ui.grid","ui.grid.edit","ui.grid.cellNav"]);e.constant("uiGridRowEditConstants",{}),e.service("uiGridRowEditService",["$interval","$q","uiGridConstants","uiGridRowEditConstants","gridUtil",function(i,e,t,r,a){var l={initializeGrid:function(t,r){r.rowEdit={};var e={events:{rowEdit:{saveRow:function(e){}}},methods:{rowEdit:{setSavePromise:function(e,t){l.setSavePromise(r,e,t)},getDirtyRows:function(){return r.rowEdit.dirtyRows?r.rowEdit.dirtyRows:[]},getErrorRows:function(){return r.rowEdit.errorRows?r.rowEdit.errorRows:[]},flushDirtyRows:function(){return l.flushDirtyRows(r)},setRowsDirty:function(e){l.setRowsDirty(r,e)},setRowsClean:function(e){l.setRowsClean(r,e)}}}};r.api.registerEventsFromObject(e.events),r.api.registerMethodsFromObject(e.methods),r.api.core.on.renderingComplete(t,function(e){r.api.edit.on.afterCellEdit(t,l.endEditCell),r.api.edit.on.beginCellEdit(t,l.beginEditCell),r.api.edit.on.cancelCellEdit(t,l.cancelEditCell),r.api.cellNav&&r.api.cellNav.on.navigate(t,l.navigate)})},defaultGridOptions:function(e){},saveRow:function(t,r){var i=this;return function(){if(r.isSaving=!0,r.rowEditSavePromise)return r.rowEditSavePromise;var e=t.api.rowEdit.raise.saveRow(r.entity);return r.rowEditSavePromise?r.rowEditSavePromise.then(i.processSuccessPromise(t,r),i.processErrorPromise(t,r)):a.logError("A promise was not returned when saveRow event was raised, either nobody is listening to event, or event handler did not return a promise"),e}},setSavePromise:function(e,t,r){e.getRow(t).rowEditSavePromise=r},processSuccessPromise:function(e,t){var r=this;return function(){delete t.isSaving,delete t.isDirty,delete t.isError,delete t.rowEditSaveTimer,delete t.rowEditSavePromise,r.removeRow(e.rowEdit.errorRows,t),r.removeRow(e.rowEdit.dirtyRows,t)}},processErrorPromise:function(e,t){return function(){delete t.isSaving,delete t.rowEditSaveTimer,delete t.rowEditSavePromise,t.isError=!0,e.rowEdit.errorRows||(e.rowEdit.errorRows=[]),l.isRowPresent(e.rowEdit.errorRows,t)||e.rowEdit.errorRows.push(t)}},removeRow:function(r,i){null!=r&&r.forEach(function(e,t){e.uid===i.uid&&r.splice(t,1)})},isRowPresent:function(e,r){var i=!1;return e.forEach(function(e,t){e.uid===r.uid&&(i=!0)}),i},flushDirtyRows:function(t){var r=[];return t.api.rowEdit.getDirtyRows().forEach(function(e){l.cancelTimer(t,e),l.saveRow(t,e)(),r.push(e.rowEditSavePromise)}),e.all(r)},endEditCell:function(e,t,r,i){var n=this.grid,o=n.getRow(e);o?(r!==i||o.isDirty)&&(n.rowEdit.dirtyRows||(n.rowEdit.dirtyRows=[]),o.isDirty||(o.isDirty=!0,n.rowEdit.dirtyRows.push(o)),delete o.isError,l.considerSetTimer(n,o)):a.logError("Unable to find rowEntity in grid data, dirty flag cannot be set")},beginEditCell:function(e,t){var r=this.grid,i=r.getRow(e);i?l.cancelTimer(r,i):a.logError("Unable to find rowEntity in grid data, timer cannot be cancelled")},cancelEditCell:function(e,t){var r=this.grid,i=r.getRow(e);i?l.considerSetTimer(r,i):a.logError("Unable to find rowEntity in grid data, timer cannot be set")},navigate:function(e,t){var r=this.grid;e.row.rowEditSaveTimer&&l.cancelTimer(r,e.row),t&&t.row&&t.row!==e.row&&l.considerSetTimer(r,t.row)},considerSetTimer:function(e,t){if(l.cancelTimer(e,t),t.isDirty&&!t.isSaving&&-1!==e.options.rowEditWaitInterval){var r=e.options.rowEditWaitInterval?e.options.rowEditWaitInterval:2e3;t.rowEditSaveTimer=i(l.saveRow(e,t),r,1)}},cancelTimer:function(e,t){t.rowEditSaveTimer&&!t.isSaving&&(i.cancel(t.rowEditSaveTimer),delete t.rowEditSaveTimer)},setRowsDirty:function(r,e){var i;e.forEach(function(e,t){(i=r.getRow(e))?(r.rowEdit.dirtyRows||(r.rowEdit.dirtyRows=[]),i.isDirty||(i.isDirty=!0,r.rowEdit.dirtyRows.push(i)),delete i.isError,l.considerSetTimer(r,i)):a.logError("requested row not found in rowEdit.setRowsDirty, row was: "+e)})},setRowsClean:function(r,e){var i;e.forEach(function(e,t){(i=r.getRow(e))?(delete i.isDirty,l.removeRow(r.rowEdit.dirtyRows,i),l.cancelTimer(r,i),delete i.isError,l.removeRow(r.rowEdit.errorRows,i)):a.logError("requested row not found in rowEdit.setRowsClean, row was: "+e)})}};return l}]),e.directive("uiGridRowEdit",["gridUtil","uiGridRowEditService","uiGridEditConstants",function(e,n,t){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){n.initializeGrid(e,i.grid)},post:function(e,t,r,i){}}}}}]),e.directive("uiGridViewport",["$compile","uiGridConstants","gridUtil","$parse",function(e,t,r,i){return{priority:-200,scope:!1,compile:function(e,t){var r=angular.element(e.children().children()[0]),i=r.attr("ng-class"),n="";return n=i?i.slice(0,-1)+", 'ui-grid-row-dirty': row.isDirty, 'ui-grid-row-saving': row.isSaving, 'ui-grid-row-error': row.isError}":"{'ui-grid-row-dirty': row.isDirty, 'ui-grid-row-saving': row.isSaving, 'ui-grid-row-error': row.isError}",r.attr("ng-class",n),{pre:function(e,t,r,i){},post:function(e,t,r,i){}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.saveState",["ui.grid","ui.grid.selection","ui.grid.cellNav","ui.grid.grouping","ui.grid.pinning","ui.grid.treeView"]);e.constant("uiGridSaveStateConstants",{featureName:"saveState"}),e.service("uiGridSaveStateService",function(){var l={initializeGrid:function(r){r.saveState={},this.defaultGridOptions(r.options);var e={events:{saveState:{}},methods:{saveState:{save:function(){return l.save(r)},restore:function(e,t){return l.restore(r,e,t)}}}};r.api.registerEventsFromObject(e.events),r.api.registerMethodsFromObject(e.methods)},defaultGridOptions:function(e){e.saveWidths=!1!==e.saveWidths,e.saveOrder=!1!==e.saveOrder,e.saveScroll=!0===e.saveScroll,e.saveFocus=!0!==e.saveScroll&&!1!==e.saveFocus,e.saveVisible=!1!==e.saveVisible,e.saveSort=!1!==e.saveSort,e.saveFilter=!1!==e.saveFilter,e.saveSelection=!1!==e.saveSelection,e.saveGrouping=!1!==e.saveGrouping,e.saveGroupingExpandedStates=!0===e.saveGroupingExpandedStates,e.savePinning=!1!==e.savePinning,e.saveTreeView=!1!==e.saveTreeView},save:function(e){var t={};return t.columns=l.saveColumns(e),t.scrollFocus=l.saveScrollFocus(e),t.selection=l.saveSelection(e),t.grouping=l.saveGrouping(e),t.treeView=l.saveTreeView(e),t.pagination=l.savePagination(e),t},restore:function(e,t,r){return r.columns&&l.restoreColumns(e,r.columns),r.scrollFocus&&l.restoreScrollFocus(e,t,r.scrollFocus),r.selection&&l.restoreSelection(e,r.selection),r.grouping&&l.restoreGrouping(e,r.grouping),r.treeView&&l.restoreTreeView(e,r.treeView),r.pagination&&l.restorePagination(e,r.pagination),e.refresh()},saveColumns:function(r){var i=[];return r.getOnlyDataColumns().forEach(function(e){var t={};t.name=e.name,r.options.saveVisible&&(t.visible=e.visible),r.options.saveWidths&&(t.width=e.width),r.options.saveSort&&(t.sort=angular.copy(e.sort)),r.options.saveFilter&&(t.filters=[],e.filters.forEach(function(e){var r={};angular.forEach(e,function(e,t){"condition"!==t&&"$$hashKey"!==t&&"placeholder"!==t&&(r[t]=e)}),t.filters.push(r)})),r.api.pinning&&r.options.savePinning&&(t.pinned=e.renderContainer?e.renderContainer:""),i.push(t)}),i},saveScrollFocus:function(e){if(!e.api.cellNav)return{};var t={};if(e.options.saveFocus){t.focus=!0;var r=e.api.cellNav.getFocusedCell();null!==r&&(null!==r.col&&(t.colName=r.col.colDef.name),null!==r.row&&(t.rowVal=l.getRowVal(e,r.row)))}return(e.options.saveScroll||e.options.saveFocus&&!t.colName&&!t.rowVal)&&(t.focus=!1,e.renderContainers.body.prevRowScrollIndex&&(t.rowVal=l.getRowVal(e,e.renderContainers.body.visibleRowCache[e.renderContainers.body.prevRowScrollIndex])),e.renderContainers.body.prevColScrollIndex&&(t.colName=e.renderContainers.body.visibleColumnCache[e.renderContainers.body.prevColScrollIndex].name)),t},saveSelection:function(t){return t.api.selection&&t.options.saveSelection?t.api.selection.getSelectedGridRows().map(function(e){return l.getRowVal(t,e)}):[]},saveGrouping:function(e){return e.api.grouping&&e.options.saveGrouping?e.api.grouping.getGrouping(e.options.saveGroupingExpandedStates):{}},savePagination:function(e){return e.api.pagination&&e.options.paginationPageSize?{paginationCurrentPage:e.options.paginationCurrentPage,paginationPageSize:e.options.paginationPageSize}:{}},saveTreeView:function(e){return e.api.treeView&&e.options.saveTreeView?e.api.treeView.getTreeView():{}},getRowVal:function(e,t){if(!t)return null;var r={};return e.options.saveRowIdentity?(r.identity=!0,r.row=e.options.saveRowIdentity(t.entity)):(r.identity=!1,r.row=e.renderContainers.body.visibleRowCache.indexOf(t)),r},restoreColumns:function(o,e){var a=!1;e.forEach(function(e,t){var r=o.getColumn(e.name);if(r&&!o.isRowHeaderColumn(r)){!o.options.saveVisible||r.visible===e.visible&&r.colDef.visible===e.visible||(r.visible=e.visible,r.colDef.visible=e.visible,o.api.core.raise.columnVisibilityChanged(r)),o.options.saveWidths&&r.width!==e.width&&(r.width=e.width,r.hasCustomWidth=!0),!o.options.saveSort||angular.equals(r.sort,e.sort)||void 0===r.sort&&angular.isEmpty(e.sort)||(r.sort=angular.copy(e.sort),a=!0),o.options.saveFilter&&!angular.equals(r.filters,e.filters)&&(e.filters.forEach(function(e,t){angular.extend(r.filters[t],e),void 0!==e.term&&null!==e.term||delete r.filters[t].term}),o.api.core.raise.filterChanged(r)),o.api.pinning&&o.options.savePinning&&r.renderContainer!==e.pinned&&o.api.pinning.pinColumn(r,e.pinned);var i=o.getOnlyDataColumns().indexOf(r);if(-1!==i&&o.options.saveOrder&&i!==t){var n=o.columns.splice(i+o.rowHeaderColumns.length,1)[0];o.columns.splice(t+o.rowHeaderColumns.length,0,n)}}}),a&&o.api.core.raise.sortChanged(o,o.getColumnSorting())},restoreScrollFocus:function(e,t,r){if(e.api.cellNav){var i,n;if(r.colName){var o=e.options.columnDefs.filter(function(e){return e.name===r.colName});0<o.length&&(i=o[0])}r.rowVal&&r.rowVal.row&&(n=r.rowVal.identity?l.findRowByIdentity(e,r.rowVal):e.renderContainers.body.visibleRowCache[r.rowVal.row]);var a=n&&n.entity?n.entity:null;(i||a)&&(r.focus?e.api.cellNav.scrollToFocus(a,i):e.scrollTo(a,i))}},restoreSelection:function(r,e){r.api.selection&&(r.api.selection.clearSelectedRows(),e.forEach(function(e){if(e.identity){var t=l.findRowByIdentity(r,e);t&&r.api.selection.selectRow(t.entity)}else r.api.selection.selectRowByVisibleIndex(e.row)}))},restoreGrouping:function(e,t){e.api.grouping&&null!=t&&!angular.equals(t,{})&&e.api.grouping.setGrouping(t)},restoreTreeView:function(e,t){e.api.treeView&&null!=t&&!angular.equals(t,{})&&e.api.treeView.setTreeView(t)},restorePagination:function(e,t){e.api.pagination&&e.options.paginationPageSize&&(e.options.paginationCurrentPage=t.paginationCurrentPage,e.options.paginationPageSize=t.paginationPageSize)},findRowByIdentity:function(t,r){if(!t.options.saveRowIdentity)return null;var e=t.rows.filter(function(e){return t.options.saveRowIdentity(e.entity)===r.row});return 0<e.length?e[0]:null}};return l}),e.directive("uiGridSaveState",["uiGridSaveStateConstants","uiGridSaveStateService","gridUtil","$compile",function(e,n,t,r){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,link:function(e,t,r,i){n.initializeGrid(i.grid)}}}])}(),function(){"use strict";var e=angular.module("ui.grid.selection",["ui.grid"]);e.constant("uiGridSelectionConstants",{featureName:"selection",selectionRowHeaderColName:"selectionRowHeaderCol"}),angular.module("ui.grid").config(["$provide",function(e){e.decorator("GridRow",["$delegate",function(e){return e.prototype.setSelected=function(e){e!==this.isSelected&&(this.isSelected=e,this.grid.selection.selectedCount+=e?1:-1)},e.prototype.setFocused=function(e){e!==this.isFocused&&(this.grid.selection.focusedRow&&(this.grid.selection.focusedRow.isFocused=!1),this.grid.selection.focusedRow=e?this:null,this.isFocused=e)},e}])}]),e.service("uiGridSelectionService",function(){var c={initializeGrid:function(a){a.selection={lastSelectedRow:null,focusedRow:null,selectAll:!1},a.selection.selectedCount=0,c.defaultGridOptions(a.options);var e={events:{selection:{rowFocusChanged:function(e,t,r){},rowSelectionChanged:function(e,t,r){},rowSelectionChangedBatch:function(e,t,r){}}},methods:{selection:{toggleRowSelection:function(e,t){var r=a.getRow(e);null!=r&&null!==r&&c.toggleRowSelection(a,r,t,a.options.multiSelect,a.options.noUnselect,!0)},selectRow:function(e,t){var r=a.getRow(e);null==r||null===r||r.isSelected||c.toggleRowSelection(a,r,t,a.options.multiSelect,a.options.noUnselect,!0)},selectRowByVisibleIndex:function(e,t){var r=a.renderContainers.body.visibleRowCache[e];null==r||null==r||r.isSelected||c.toggleRowSelection(a,r,t,a.options.multiSelect,a.options.noUnselect,!1)},selectRowByKey:function(e,t,r,i,n){var o=a.findRowByKey(e,t,r,n);null==o||null==o||o.isSelected||c.toggleRowSelection(a,o,i,a.options.multiSelect,a.options.noUnselect,!1)},unSelectRow:function(e,t){var r=a.getRow(e);null!=r&&null!==r&&r.isSelected&&c.toggleRowSelection(a,r,t,a.options.multiSelect,a.options.noUnselect,!0)},unSelectRowByVisibleIndex:function(e,t){var r=a.renderContainers.body.visibleRowCache[e];null!=r&&null!=r&&r.isSelected&&c.toggleRowSelection(a,r,t,a.options.multiSelect,a.options.noUnselect,!1)},unSelectRowByKey:function(e,t,r,i,n){var o=a.findRowByKey(e,t,r,n);null!=o&&null!=o&&o.isSelected&&c.toggleRowSelection(a,o,i,a.options.multiSelect,a.options.noUnselect,!1)},selectAllRows:function(t){if(!1!==a.options.multiSelect){var r=[];a.rows.forEach(function(e){e.isSelected||!1===e.enableSelection||!1===a.options.isRowSelectable(e)||(e.setSelected(!0),c.decideRaiseSelectionEvent(a,e,r,t))}),a.selection.selectAll=!0,c.decideRaiseSelectionBatchEvent(a,r,t)}},selectAllVisibleRows:function(t){if(!1!==a.options.multiSelect){var r=[];a.rows.forEach(function(e){e.visible?e.isSelected||!1===e.enableSelection||!1===a.options.isRowSelectable(e)||(e.setSelected(!0),c.decideRaiseSelectionEvent(a,e,r,t)):e.isSelected&&(e.setSelected(!1),c.decideRaiseSelectionEvent(a,e,r,t))}),a.selection.selectAll=!0,c.decideRaiseSelectionBatchEvent(a,r,t)}},clearSelectedRows:function(e){c.clearSelectedRows(a,e)},getSelectedRows:function(){return c.mapAndFilterRowsByEntity(c.getSelectedRows(a))},getUnSelectedRows:function(){return c.mapAndFilterRowsByEntity(c.getUnSelectedRows(a))},getSelectedGridRows:function(){return c.getSelectedRows(a)},getUnSelectedGridRows:function(){return c.getUnSelectedRows(a)},getSelectedCount:function(){return a.selection.selectedCount},setMultiSelect:function(e){a.options.multiSelect=e},setModifierKeysToMultiSelect:function(e){a.options.modifierKeysToMultiSelect=e},getSelectAllState:function(){return a.selection.selectAll}}}};a.api.registerEventsFromObject(e.events),a.api.registerMethodsFromObject(e.methods)},defaultGridOptions:function(e){e.enableRowSelection=!1!==e.enableRowSelection,e.multiSelect=!1!==e.multiSelect,e.noUnselect=!0===e.noUnselect,e.modifierKeysToMultiSelect=!0===e.modifierKeysToMultiSelect,e.enableRowHeaderSelection=!1!==e.enableRowHeaderSelection,void 0===e.enableFullRowSelection&&(e.enableFullRowSelection=!e.enableRowHeaderSelection),e.enableFocusRowOnRowHeaderClick=!1!==e.enableFocusRowOnRowHeaderClick||!e.enableRowHeaderSelection,e.enableSelectRowOnFocus=!1!==e.enableSelectRowOnFocus,e.enableSelectAll=!1!==e.enableSelectAll,e.enableSelectionBatchEvent=!1!==e.enableSelectionBatchEvent,e.selectionRowHeaderWidth=angular.isDefined(e.selectionRowHeaderWidth)?e.selectionRowHeaderWidth:30,e.enableFooterTotalSelected=!1!==e.enableFooterTotalSelected,e.isRowSelectable=angular.isDefined(e.isRowSelectable)?e.isRowSelectable:angular.noop},toggleRowSelection:function(e,t,r,i,n,o){if(!1!==t.enableSelection){void 0===o&&(o=!0);var a=t.isSelected;i||(a?1<c.getSelectedRows(e).length&&(a=!1,c.clearSelectedRows(e,r)):c.clearSelectedRows(e,r)),a&&n||!o&&!t.visible||(t.setSelected(!a),!0===t.isSelected&&(e.selection.lastSelectedRow=t),e.selection.selectAll=e.rows.length===c.getSelectedRows(e).length,e.api.selection.raise.rowSelectionChanged(t,r))}},shiftSelect:function(e,t,r,i){if(i){var n=0<c.getSelectedRows(e).length?e.renderContainers.body.visibleRowCache.indexOf(e.selection.lastSelectedRow):0,o=e.renderContainers.body.visibleRowCache.indexOf(t);if(o<n){var a=n;n=o,o=a}for(var l=[],s=n;s<=o;s++){var d=e.renderContainers.body.visibleRowCache[s];d&&(d.isSelected||!1===d.enableSelection||(d.setSelected(!0),e.selection.lastSelectedRow=d,c.decideRaiseSelectionEvent(e,d,l,r)))}c.decideRaiseSelectionBatchEvent(e,l,r)}},getSelectedRows:function(e){return e.rows.filter(function(e){return e.isSelected})},getUnSelectedRows:function(e){return e.rows.filter(function(e){return!e.isSelected})},mapAndFilterRowsByEntity:function(e){return"function"==typeof e.reduce?e.reduce(function(e,t){return!t.entity.hasOwnProperty("$$hashKey")&&angular.isObject(t.entity)||e.push(t.entity),e},[]):e.filter(function(e){return e.entity.hasOwnProperty("$$hashKey")||!angular.isObject(e.entity)}).map(function(e){return e.entity})},clearSelectedRows:function(t,r){var i=[];c.getSelectedRows(t).forEach(function(e){e.isSelected&&!1!==e.enableSelection&&(e.setSelected(!1),c.decideRaiseSelectionEvent(t,e,i,r))}),t.selection.selectAll=!1,t.selection.selectedCount=0,c.decideRaiseSelectionBatchEvent(t,i,r)},decideRaiseSelectionEvent:function(e,t,r,i){e.options.enableSelectionBatchEvent?r.push(t):e.api.selection.raise.rowSelectionChanged(t,i)},decideRaiseSelectionBatchEvent:function(e,t,r){0<t.length&&e.api.selection.raise.rowSelectionChangedBatch(t,r)}};return c}),e.directive("uiGridSelection",["i18nService","uiGridSelectionConstants","uiGridSelectionService","uiGridConstants",function(d,c,u,g){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){if(u.initializeGrid(i.grid),i.grid.options.enableRowHeaderSelection){var n={name:c.selectionRowHeaderColName,displayName:d.getSafeText("selection.displayName"),width:i.grid.options.selectionRowHeaderWidth,minWidth:10,cellTemplate:"ui-grid/selectionRowHeader",headerCellTemplate:"ui-grid/selectionHeaderCell",enableColumnResizing:!1,enableColumnMenu:!1,exporterSuppressExport:!0,allowCellFocus:!0};i.grid.addRowHeaderColumn(n,0)}var o=!1,a=function(e){return e.forEach(function(e){e.enableSelection=i.grid.options.isRowSelectable(e)}),e},l=function(){i.grid.options.isRowSelectable!==angular.noop&&!0!==o&&(i.grid.registerRowsProcessor(a,500),o=!0)};l();var s=i.grid.registerDataChangeCallback(l,[g.dataChange.OPTIONS]);e.$on("$destroy",s)},post:function(e,t,r,i){}}}}}]),e.directive("uiGridSelectionRowHeaderButtons",["$templateCache","uiGridSelectionService","gridUtil",function(e,a,l){return{replace:!0,restrict:"E",template:e.get("ui-grid/selectionRowHeaderButtons"),scope:!0,require:"^uiGrid",link:function(e,t,r,i){var n=i.grid;function o(e,t){if(t.stopPropagation(),t.shiftKey)a.shiftSelect(n,e,t,n.options.multiSelect);else if(t.ctrlKey||t.metaKey)a.toggleRowSelection(n,e,t,n.options.multiSelect,n.options.noUnselect,!1);else if(e.groupHeader){a.toggleRowSelection(n,e,t,n.options.multiSelect,n.options.noUnselect,!1);for(var r=0;r<e.treeNode.children.length;r++)a.toggleRowSelection(n,e.treeNode.children[r].row,t,n.options.multiSelect,n.options.noUnselect,!1)}else a.toggleRowSelection(n,e,t,n.options.multiSelect&&!n.options.modifierKeysToMultiSelect,n.options.noUnselect,!1);n.options.enableFocusRowOnRowHeaderClick&&e.setFocused(!e.isFocused)&&n.api.selection.raise.rowFocusChanged(e,t)}e.selectButtonClick=o,e.selectButtonKeyDown=function(e,t){32!==t.keyCode&&13!==t.keyCode||(t.preventDefault(),o(e,t))},"ie"===l.detectBrowser()&&t.on("mousedown",function(e){(e.ctrlKey||e.shiftKey)&&(e.target.onselectstart=function(){return!1},window.setTimeout(function(){e.target.onselectstart=null},0))}),e.$on("$destroy",function(){t.off()})}}}]),e.directive("uiGridSelectionSelectAllButtons",["$templateCache","uiGridSelectionService",function(e,i){return{replace:!0,restrict:"E",template:e.get("ui-grid/selectionSelectAllButtons"),scope:!1,link:function(t){var r=t.col.grid;t.headerButtonKeyDown=function(e){32!==e.keyCode&&13!==e.keyCode||(e.preventDefault(),t.headerButtonClick(e))},t.headerButtonClick=function(e){r.selection.selectAll?(i.clearSelectedRows(r,e),r.options.noUnselect&&r.api.selection.selectRowByVisibleIndex(0,e),r.selection.selectAll=!1):r.options.multiSelect&&(r.api.selection.selectAllVisibleRows(e),r.selection.selectAll=!0)}}}}]),e.directive("uiGridViewport",function(){return{priority:-200,scope:!1,compile:function(e){var t=angular.element(e[0].querySelector(".ui-grid-canvas:not(.ui-grid-empty-base-layer-container)").children[0]),r="'ui-grid-row-selected': row.isSelected, 'ui-grid-row-focused': row.isFocused}",i=t.attr("ng-class");return r=i?i.slice(0,-1)+","+r:"{"+r,t.attr("ng-class",r),{pre:function(e,t,r,i){},post:function(e,t,r,i){}}}}}),e.directive("uiGridCell",["uiGridConstants","uiGridSelectionService",function(u,g){return{priority:-200,restrict:"A",require:"?^uiGrid",scope:!1,link:function(r,a,e,t){var l=0,s={};t.grid.api.cellNav&&t.grid.api.cellNav.on.viewPortKeyDown(r,function(e,t){null!==t&&t.row===r.row&&t.col===r.col&&e.keyCode===u.keymap.SPACE&&"selectionRowHeaderCol"===r.col.colDef.name&&(e.preventDefault(),g.toggleRowSelection(r.grid,r.row,e,r.grid.options.multiSelect&&!r.grid.options.modifierKeysToMultiSelect,r.grid.options.noUnselect,!1),r.$apply())});var d=function(e){"ui-grid-icon-minus-squared"!==e.target.className&&"ui-grid-icon-plus-squared"!==e.target.className&&(a.off("touchend",n),e.shiftKey?g.shiftSelect(r.grid,r.row,e,r.grid.options.multiSelect):e.ctrlKey||e.metaKey?g.toggleRowSelection(r.grid,r.row,e,r.grid.options.multiSelect,r.grid.options.noUnselect,!1):r.grid.options.enableSelectRowOnFocus&&g.toggleRowSelection(r.grid,r.row,e,r.grid.options.multiSelect&&!r.grid.options.modifierKeysToMultiSelect,r.grid.options.noUnselect,!1),r.row.setFocused(!r.row.isFocused),r.grid.api.selection.raise.rowFocusChanged(r.row,e),r.$apply(),window.setTimeout(function(){a.on("touchend",n)},300))},i=function(e){l=(new Date).getTime(),s=e.changedTouches[0],a.off("click",d)},n=function(e){var t=(new Date).getTime(),r=e.changedTouches[0],i=t-l,n=Math.abs(s.clientX-r.clientX),o=Math.abs(s.clientY-r.clientY);n<100&&o<100&&i<300&&d(e),window.setTimeout(function(){a.on("click",d)},300)};function o(){r.grid.options.enableRowSelection&&r.grid.options.enableFullRowSelection&&"selectionRowHeaderCol"!==r.col.colDef.name&&(a.addClass("ui-grid-disable-selection"),a.on("touchstart",i),a.on("touchend",n),a.on("click",d),r.registered=!0)}o();var c=r.grid.registerDataChangeCallback(function(){r.grid.options.enableRowSelection&&r.grid.options.enableFullRowSelection&&!r.registered?o():r.grid.options.enableRowSelection&&r.grid.options.enableFullRowSelection||!r.registered||r.registered&&(a.removeClass("ui-grid-disable-selection"),a.off("touchstart",i),a.off("touchend",n),a.off("click",d),r.registered=!1)},[u.dataChange.OPTIONS]);a.on("$destroy",c)}}}]),e.directive("uiGridGridFooter",["$compile","gridUtil",function(o,r){return{restrict:"EA",replace:!0,priority:-1e3,require:"^uiGrid",scope:!0,compile:function(){return{pre:function(i,n,e,t){t.grid.options.showGridFooter&&r.getTemplate("ui-grid/gridFooterSelectedItems").then(function(e){var t=angular.element(e),r=o(t)(i);angular.element(n[0].getElementsByClassName("ui-grid-grid-footer")[0]).append(r)})},post:function(e,t,r,i){}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.treeBase",["ui.grid"]);e.constant("uiGridTreeBaseConstants",{featureName:"treeBase",rowHeaderColName:"treeBaseRowHeaderCol",EXPANDED:"expanded",COLLAPSED:"collapsed",aggregation:{COUNT:"count",SUM:"sum",MAX:"max",MIN:"min",AVG:"avg"}}),e.service("uiGridTreeBaseService",["$q","uiGridTreeBaseConstants","gridUtil","GridRow","gridClassFactory","i18nService","uiGridConstants","rowSorter",function(e,l,t,r,i,n,o,a){var s={initializeGrid:function(r){r.treeBase={},r.treeBase.numberLevels=0,r.treeBase.expandAll=!1,r.treeBase.tree=[],s.defaultGridOptions(r.options),r.registerRowsProcessor(s.treeRows,410),r.registerColumnBuilder(s.treeBaseColumnBuilder),s.createRowHeader(r);var e={events:{treeBase:{rowExpanded:{},rowCollapsed:{}}},methods:{treeBase:{expandAllRows:function(){s.expandAllRows(r)},collapseAllRows:function(){s.collapseAllRows(r)},toggleRowTreeState:function(e){s.toggleRowTreeState(r,e)},expandRow:function(e,t){s.expandRow(r,e,t)},expandRowChildren:function(e){s.expandRowChildren(r,e)},collapseRow:function(e){s.collapseRow(r,e)},collapseRowChildren:function(e){s.collapseRowChildren(r,e)},getTreeExpandedState:function(){return{expandedState:s.getTreeState(r)}},setTreeState:function(e){s.setTreeState(r,e)},getRowChildren:function(e){return e.treeNode.children.map(function(e){return e.row})}}}};r.api.registerEventsFromObject(e.events),r.api.registerMethodsFromObject(e.methods)},defaultGridOptions:function(e){e.treeRowHeaderBaseWidth=e.treeRowHeaderBaseWidth||30,e.treeIndent=null!=e.treeIndent?e.treeIndent:10,e.showTreeRowHeader=!1!==e.showTreeRowHeader,e.showTreeExpandNoChildren=!1!==e.showTreeExpandNoChildren,e.treeRowHeaderAlwaysVisible=!1!==e.treeRowHeaderAlwaysVisible,e.treeCustomAggregations=e.treeCustomAggregations||{},e.enableExpandAll=!1!==e.enableExpandAll},treeBaseColumnBuilder:function(e,t,r){void 0!==e.customTreeAggregationFn&&(t.treeAggregationFn=e.customTreeAggregationFn),void 0!==e.treeAggregationType&&(t.treeAggregation={type:e.treeAggregationType},void 0!==r.treeCustomAggregations[e.treeAggregationType]?(t.treeAggregationFn=r.treeCustomAggregations[e.treeAggregationType].aggregationFn,t.treeAggregationFinalizerFn=r.treeCustomAggregations[e.treeAggregationType].finalizerFn,t.treeAggregation.label=r.treeCustomAggregations[e.treeAggregationType].label):void 0!==s.nativeAggregations()[e.treeAggregationType]&&(t.treeAggregationFn=s.nativeAggregations()[e.treeAggregationType].aggregationFn,t.treeAggregation.label=s.nativeAggregations()[e.treeAggregationType].label)),void 0!==e.treeAggregationLabel&&(void 0===t.treeAggregation&&(t.treeAggregation={}),t.treeAggregation.label=e.treeAggregationLabel),t.treeAggregationUpdateEntity=!1!==e.treeAggregationUpdateEntity,void 0===t.customTreeAggregationFinalizerFn&&(t.customTreeAggregationFinalizerFn=e.customTreeAggregationFinalizerFn)},createRowHeader:function(e){var t={name:l.rowHeaderColName,displayName:"",width:e.options.treeRowHeaderBaseWidth,minWidth:10,cellTemplate:"ui-grid/treeBaseRowHeader",headerCellTemplate:"ui-grid/treeBaseHeaderCell",enableColumnResizing:!1,enableColumnMenu:!1,exporterSuppressExport:!0,allowCellFocus:!0};t.visible=e.options.treeRowHeaderAlwaysVisible,e.addRowHeaderColumn(t,-100)},expandAllRows:function(t){t.treeBase.tree.forEach(function(e){s.setAllNodes(t,e,l.EXPANDED)}),t.treeBase.expandAll=!0,t.queueGridRefresh()},collapseAllRows:function(t){t.treeBase.tree.forEach(function(e){s.setAllNodes(t,e,l.COLLAPSED)}),t.treeBase.expandAll=!1,t.queueGridRefresh()},setAllNodes:function(t,e,r){void 0!==e.state&&e.state!==r&&((e.state=r)===l.EXPANDED?t.api.treeBase.raise.rowExpanded(e.row):t.api.treeBase.raise.rowCollapsed(e.row)),e.children&&e.children.forEach(function(e){s.setAllNodes(t,e,r)})},toggleRowTreeState:function(e,t){void 0===t.treeLevel||null===t.treeLevel||t.treeLevel<0||(t.treeNode.state===l.EXPANDED?s.collapseRow(e,t):s.expandRow(e,t,!1),e.queueGridRefresh())},expandRow:function(e,t,r){if(r){for(var i=[];t&&void 0!==t.treeLevel&&null!==t.treeLevel&&0<=t.treeLevel&&t.treeNode.state!==l.EXPANDED;)i.push(t),t=t.treeNode.parentRow;if(0<i.length){for(t=i.pop();t;)t.treeNode.state=l.EXPANDED,e.api.treeBase.raise.rowExpanded(t),t=i.pop();e.treeBase.expandAll=s.allExpanded(e.treeBase.tree),e.queueGridRefresh()}}else{if(void 0===t.treeLevel||null===t.treeLevel||t.treeLevel<0)return;t.treeNode.state!==l.EXPANDED&&(t.treeNode.state=l.EXPANDED,e.api.treeBase.raise.rowExpanded(t),e.treeBase.expandAll=s.allExpanded(e.treeBase.tree),e.queueGridRefresh())}},expandRowChildren:function(e,t){void 0===t.treeLevel||null===t.treeLevel||t.treeLevel<0||(s.setAllNodes(e,t.treeNode,l.EXPANDED),e.treeBase.expandAll=s.allExpanded(e.treeBase.tree),e.queueGridRefresh())},collapseRow:function(e,t){void 0===t.treeLevel||null===t.treeLevel||t.treeLevel<0||t.treeNode.state!==l.COLLAPSED&&(t.treeNode.state=l.COLLAPSED,e.treeBase.expandAll=!1,e.api.treeBase.raise.rowCollapsed(t),e.queueGridRefresh())},collapseRowChildren:function(e,t){void 0===t.treeLevel||null===t.treeLevel||t.treeLevel<0||(s.setAllNodes(e,t.treeNode,l.COLLAPSED),e.treeBase.expandAll=!1,e.queueGridRefresh())},allExpanded:function(e){var t=!0;return e.forEach(function(e){s.allExpandedInternal(e)||(t=!1)}),t},allExpandedInternal:function(e){if(e.children&&0<e.children.length){if(e.state===l.COLLAPSED)return!1;var t=!0;return e.children.forEach(function(e){s.allExpandedInternal(e)||(t=!1)}),t}return!0},treeRows:function(e){return 0===e.length?(s.updateRowHeaderWidth(this),e):(this.treeBase.tree=s.createTree(this,e),s.updateRowHeaderWidth(this),s.sortTree(this),s.fixFilter(this),s.renderTree(this.treeBase.tree))},updateRowHeaderWidth:function(e){var t=e.getColumn(l.rowHeaderColName),r=e.options.treeRowHeaderBaseWidth+e.options.treeIndent*Math.max(e.treeBase.numberLevels-1,0);t&&r!==t.width&&(t.width=r,e.queueRefresh());var i=!0;!1===e.options.showTreeRowHeader&&(i=!1),!1===e.options.treeRowHeaderAlwaysVisible&&e.treeBase.numberLevels<=0&&(i=!1),t&&t.visible!==i&&(t.visible=i,t.colDef.visible=i,e.queueGridRefresh())},renderTree:function(e){var t=[];return e.forEach(function(e){e.row.visible&&t.push(e.row),e.state===l.EXPANDED&&e.children&&0<e.children.length&&(t=t.concat(s.renderTree(e.children)))}),t},createTree:function(r,e){var i=-1,n={},o=[];r.treeBase.tree=[],r.treeBase.numberLevels=0;var a=s.getAggregations(r);for(e.forEach(function(e){if(e.internalRow||e.treeLevel===e.entity.$$treeLevel||(e.treeLevel=e.entity.$$treeLevel),e.treeLevel<=i){for(;e.treeLevel<=i;){var t=o.pop();s.finaliseAggregations(t),i--}0<o.length?s.setCurrentState(o):l.EXPANDED}void 0!==e.treeLevel&&null!==e.treeLevel&&0<=e.treeLevel&&n.hasOwnProperty(e.uid)&&o.push(n[e.uid]),(void 0===e.treeLevel||null===e.treeLevel||e.treeLevel<0)&&e.visible&&s.aggregate(r,e,o),n.hasOwnProperty(e.uid)||s.addOrUseNode(r,e,o,a),void 0!==e.treeLevel&&null!==e.treeLevel&&0<=e.treeLevel&&(n.hasOwnProperty(e.uid)||(n[e.uid]=e,o.push(e)),i++,s.setCurrentState(o)),r.treeBase.numberLevels<e.treeLevel+1&&(r.treeBase.numberLevels=e.treeLevel+1)});0<o.length;){var t=o.pop();s.finaliseAggregations(t)}return r.treeBase.tree},addOrUseNode:function(e,t,r,i){var n=[];i.forEach(function(e){n.push(s.buildAggregationObject(e.col))});var o={state:l.COLLAPSED,row:t,parentRow:null,aggregations:n,children:[]};t.treeNode&&(o.state=t.treeNode.state),0<r.length&&(o.parentRow=r[r.length-1]),t.treeNode=o,0===r.length?e.treeBase.tree.push(o):r[r.length-1].treeNode.children.push(o)},setCurrentState:function(e){var t=l.EXPANDED;return e.forEach(function(e){e.treeNode.state===l.COLLAPSED&&(t=l.COLLAPSED)}),t},sortTree:function(e){e.columns.forEach(function(e){e.sort&&e.sort.ignoreSort&&delete e.sort.ignoreSort}),e.treeBase.tree=s.sortInternal(e,e.treeBase.tree)},sortInternal:function(t,e){var r=e.map(function(e){return e.row}),i=(r=a.sort(t,r,t.columns)).map(function(e){return e.treeNode});return i.forEach(function(e){e.state===l.EXPANDED&&e.children&&0<e.children.length&&(e.children=s.sortInternal(t,e.children))}),i},fixFilter:function(e){var t;e.treeBase.tree.forEach(function(e){e.children&&0<e.children.length&&(t=e.row.visible,s.fixFilterInternal(e.children,t))})},fixFilterInternal:function(e,t){return e.forEach(function(e){e.row.visible&&!t&&(s.setParentsVisible(e),t=!0),e.children&&0<e.children.length&&s.fixFilterInternal(e.children,t&&e.row.visible)&&(t=!0)}),t},setParentsVisible:function(e){for(;e.parentRow;)e.parentRow.visible=!0,e=e.parentRow.treeNode},buildAggregationObject:function(e){var t={col:e};return e.treeAggregation&&e.treeAggregation.type&&(t.type=e.treeAggregation.type),e.treeAggregation&&e.treeAggregation.label&&(t.label=e.treeAggregation.label),t},getAggregations:function(t){var r=[];return t.columns.forEach(function(e){void 0!==e.treeAggregationFn&&(r.push(s.buildAggregationObject(e)),t.options.showColumnFooter&&void 0===e.colDef.aggregationType&&e.treeAggregation&&(e.treeFooterAggregation=s.buildAggregationObject(e),e.aggregationType=s.treeFooterAggregationType))}),r},aggregate:function(n,o,e){0===e.length&&o.treeNode&&o.treeNode.aggregations&&o.treeNode.aggregations.forEach(function(e){if(void 0!==e.col.treeFooterAggregation){var t=n.getCellValue(o,e.col),r=Number(t);e.col.treeAggregationFn?e.col.treeAggregationFn(e.col.treeFooterAggregation,t,r,o):e.col.treeFooterAggregation.value=void 0}}),e.forEach(function(e,i){e.treeNode.aggregations&&e.treeNode.aggregations.forEach(function(e){var t=n.getCellValue(o,e.col),r=Number(t);e.col.treeAggregationFn(e,t,r,o),0===i&&void 0!==e.col.treeFooterAggregation&&(e.col.treeAggregationFn?e.col.treeAggregationFn(e.col.treeFooterAggregation,t,r,o):e.col.treeFooterAggregation.value=void 0)})})},nativeAggregations:function(){return{count:{label:n.get().aggregation.count,menuTitle:n.get().grouping.aggregate_count,aggregationFn:function(e,t,r){void 0===e.value?e.value=1:e.value++}},sum:{label:n.get().aggregation.sum,menuTitle:n.get().grouping.aggregate_sum,aggregationFn:function(e,t,r){isNaN(r)||(void 0===e.value?e.value=r:e.value+=r)}},min:{label:n.get().aggregation.min,menuTitle:n.get().grouping.aggregate_min,aggregationFn:function(e,t,r){void 0===e.value?e.value=t:null!=t&&(t<e.value||null===e.value)&&(e.value=t)}},max:{label:n.get().aggregation.max,menuTitle:n.get().grouping.aggregate_max,aggregationFn:function(e,t,r){void 0===e.value?e.value=t:null!=t&&(t>e.value||null===e.value)&&(e.value=t)}},avg:{label:n.get().aggregation.avg,menuTitle:n.get().grouping.aggregate_avg,aggregationFn:function(e,t,r){void 0===e.count?e.count=1:e.count++,isNaN(r)||(void 0===e.value||void 0===e.sum?(e.value=r,e.sum=r):(e.sum+=r,e.value=e.sum/e.count))}}}},finaliseAggregation:function(e,t){t.col.treeAggregationUpdateEntity&&void 0!==e&&void 0!==e.entity["$$"+t.col.uid]&&angular.extend(t,e.entity["$$"+t.col.uid]),"function"==typeof t.col.treeAggregationFinalizerFn&&t.col.treeAggregationFinalizerFn(t),"function"==typeof t.col.customTreeAggregationFinalizerFn&&t.col.customTreeAggregationFinalizerFn(t),void 0===t.rendered&&(t.rendered=t.label?t.label+t.value:t.value)},finaliseAggregations:function(e){null!=e&&void 0!==e.treeNode.aggregations&&e.treeNode.aggregations.forEach(function(r){if(s.finaliseAggregation(e,r),r.col.treeAggregationUpdateEntity){var i={};angular.forEach(r,function(e,t){r.hasOwnProperty(t)&&"col"!==t&&(i[t]=e)}),e.entity["$$"+r.col.uid]=i}})},treeFooterAggregationType:function(e,t){return s.finaliseAggregation(void 0,t.treeFooterAggregation),void 0===t.treeFooterAggregation.value||null===t.treeFooterAggregation.rendered?"":t.treeFooterAggregation.rendered}};return s}]),e.directive("uiGridTreeBaseRowHeaderButtons",["$templateCache","uiGridTreeBaseService",function(e,o){return{replace:!0,restrict:"E",template:e.get("ui-grid/treeBaseRowHeaderButtons"),scope:!0,require:"^uiGrid",link:function(r,e,t,i){var n=i.grid;r.treeButtonClass=function(e){if(n.options.showTreeExpandNoChildren&&-1<e.treeLevel||e.treeNode.children&&0<e.treeNode.children.length){if("expanded"===e.treeNode.state)return"ui-grid-icon-minus-squared";if("collapsed"===e.treeNode.state)return"ui-grid-icon-plus-squared"}},r.treeButtonClick=function(e,t){t.stopPropagation(),o.toggleRowTreeState(n,e,t)},r.treeButtonKeyDown=function(e,t){32!==t.keyCode&&13!==t.keyCode||r.treeButtonClick(e,t)}}}}]),e.directive("uiGridTreeBaseExpandAllButtons",["$templateCache","uiGridTreeBaseService",function(e,i){return{replace:!0,restrict:"E",template:e.get("ui-grid/treeBaseExpandAllButtons"),scope:!1,link:function(t){var r=t.col.grid;t.headerButtonClass=function(){return 0<r.treeBase.numberLevels&&r.treeBase.expandAll?"ui-grid-icon-minus-squared":0<r.treeBase.numberLevels&&!r.treeBase.expandAll?"ui-grid-icon-plus-squared":void 0},t.headerButtonClick=function(e,t){r.treeBase.expandAll?i.collapseAllRows(r,t):i.expandAllRows(r,t)},t.headerButtonKeyDown=function(e){32!==e.keyCode&&13!==e.keyCode||t.headerButtonClick(r,e)}}}}]),e.directive("uiGridViewport",function(){return{priority:-200,scope:!1,compile:function(e){var t=angular.element(e.children().children()[0]),r=t.attr("ng-class"),i="";return i=r?r.slice(0,-1)+",'ui-grid-tree-header-row': row.treeLevel > -1}":"{'ui-grid-tree-header-row': row.treeLevel > -1}",t.attr("ng-class",i),{pre:function(e,t,r,i){},post:function(e,t,r,i){}}}}})}(),function(){"use strict";var e=angular.module("ui.grid.treeView",["ui.grid","ui.grid.treeBase"]);e.constant("uiGridTreeViewConstants",{featureName:"treeView",rowHeaderColName:"treeBaseRowHeaderCol",EXPANDED:"expanded",COLLAPSED:"collapsed",aggregation:{COUNT:"count",SUM:"sum",MAX:"max",MIN:"min",AVG:"avg"}}),e.service("uiGridTreeViewService",["$q","uiGridTreeViewConstants","uiGridTreeBaseConstants","uiGridTreeBaseService","gridUtil","GridRow","gridClassFactory","i18nService","uiGridConstants",function(e,t,r,n,i,o,a,l,s){var d={initializeGrid:function(e,t){n.initializeGrid(e,t),e.treeView={},e.registerRowsProcessor(d.adjustSorting,60);var r={treeView:{}},i={treeView:{}};e.api.registerEventsFromObject(r),e.api.registerMethodsFromObject(i)},defaultGridOptions:function(e){e.enableTreeView=!1!==e.enableTreeView},adjustSorting:function(e){return this.columns.forEach(function(e){e.sort&&(e.sort.ignoreSort=!0)}),e}};return d}]),e.directive("uiGridTreeView",["uiGridTreeViewConstants","uiGridTreeViewService","$templateCache",function(e,n,t){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){!1!==i.grid.options.enableTreeView&&n.initializeGrid(i.grid,e)},post:function(e,t,r,i){}}}}}])}(),function(){"use strict";var e=angular.module("ui.grid.validate",["ui.grid"]);e.service("uiGridValidateService",["$sce","$q","$http","i18nService","uiGridConstants",function(n,c,e,o,t){var u={validatorFactories:{},setExternalFactoryFunction:function(e){u.externalFactoryFunction=e},clearExternalFactory:function(){delete u.externalFactoryFunction},getValidatorFromExternalFactory:function(e,t){return u.externalFactoryFunction(e,t).validatorFactory(t)},getMessageFromExternalFactory:function(e,t){return u.externalFactoryFunction(e,t).messageFunction(t)},setValidator:function(e,t,r){u.validatorFactories[e]={validatorFactory:t,messageFunction:r}},getValidator:function(e,t){if(u.externalFactoryFunction){var r=u.getValidatorFromExternalFactory(e,t);if(r)return r}if(!u.validatorFactories[e])throw"Invalid validator name: "+e;return u.validatorFactories[e].validatorFactory(t)},getMessage:function(e,t){if(u.externalFactoryFunction){var r=u.getMessageFromExternalFactory(e,t);if(r)return r}return u.validatorFactories[e].messageFunction(t)},isInvalid:function(e,t){return e["$$invalid"+t.name]},setInvalid:function(e,t){e["$$invalid"+t.name]=!0},setValid:function(e,t){delete e["$$invalid"+t.name]},setError:function(e,t,r){e["$$errors"+t.name]||(e["$$errors"+t.name]={}),e["$$errors"+t.name][r]=!0},clearError:function(e,t,r){e["$$errors"+t.name]&&r in e["$$errors"+t.name]&&delete e["$$errors"+t.name][r]},getErrorMessages:function(e,t){var r=[];return e["$$errors"+t.name]&&0!==Object.keys(e["$$errors"+t.name]).length&&Object.keys(e["$$errors"+t.name]).sort().forEach(function(e){r.push(u.getMessage(e,t.validators[e]))}),r},getFormattedErrors:function(e,t){var r="",i=u.getErrorMessages(e,t);if(i.length)return i.forEach(function(e){r+=e+"<br/>"}),n.trustAsHtml("<p><b>"+o.getSafeText("validate.error")+"</b></p>"+r)},getTitleFormattedErrors:function(e,t){var r="",i=u.getErrorMessages(e,t);if(i.length)return i.forEach(function(e){r+=e+"\n"}),n.trustAsHtml(o.getSafeText("validate.error")+"\n"+r)},runValidators:function(e,t,n,o,a){if(n!==o){if(void 0===t.name||!t.name)throw new Error("colDef.name is required to perform validation");u.setValid(e,t);var r=function(t,r,i){return function(e){e||(u.setInvalid(t,r),u.setError(t,r,i),a&&a.api.validate.raise.validationFailed(t,r,n,o))}},i=[];for(var l in t.validators){u.clearError(e,t,l);var s=u.getValidator(l,t.validators[l]),d=c.when(s(o,n,e,t)).then(r(e,t,l));i.push(d)}return c.all(i)}},createDefaultValidators:function(){u.setValidator("minLength",function(r){return function(e,t){return null==t||""===t||t.length>=r}},function(e){return o.getSafeText("validate.minLength").replace("THRESHOLD",e)}),u.setValidator("maxLength",function(r){return function(e,t){return null==t||""===t||t.length<=r}},function(e){return o.getSafeText("validate.maxLength").replace("THRESHOLD",e)}),u.setValidator("required",function(r){return function(e,t){return!r||!(null==t||""===t)}},function(){return o.getSafeText("validate.required")})},initializeGrid:function(e,n){n.validate={isInvalid:u.isInvalid,getErrorMessages:u.getErrorMessages,getFormattedErrors:u.getFormattedErrors,getTitleFormattedErrors:u.getTitleFormattedErrors,runValidators:u.runValidators};var t={events:{validate:{validationFailed:function(e,t,r,i){}}},methods:{validate:{isInvalid:function(e,t){return n.validate.isInvalid(e,t)},getErrorMessages:function(e,t){return n.validate.getErrorMessages(e,t)},getFormattedErrors:function(e,t){return n.validate.getFormattedErrors(e,t)},getTitleFormattedErrors:function(e,t){return n.validate.getTitleFormattedErrors(e,t)}}}};n.api.registerEventsFromObject(t.events),n.api.registerMethodsFromObject(t.methods),n.edit&&n.api.edit.on.afterCellEdit(e,function(e,t,r,i){n.validate.runValidators(e,t,r,i,n)}),u.createDefaultValidators()}};return u}]),e.directive("uiGridValidate",["gridUtil","uiGridValidateService",function(e,n){return{priority:0,replace:!0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(e,t,r,i){n.initializeGrid(e,i.grid)},post:function(e,t,r,i){}}}}}])}(),angular.module("ui.grid").run(["$templateCache",function(e){"use strict";e.put("ui-grid/ui-grid-filter",'<div class="ui-grid-filter-container" ng-style="col.extraStyle" ng-repeat="colFilter in col.filters" ng-class="{\'ui-grid-filter-cancel-button-hidden\' : colFilter.disableCancelFilterButton === true }"><div ng-if="colFilter.type !== \'select\'"><input type="text" class="ui-grid-filter-input ui-grid-filter-input-{{$index}}" ng-model="colFilter.term" ng-attr-placeholder="{{colFilter.placeholder || \'\'}}" aria-label="{{colFilter.ariaLabel || aria.defaultFilterLabel}}"><div role="button" class="ui-grid-filter-button" ng-click="removeFilter(colFilter, $index)" ng-if="!colFilter.disableCancelFilterButton" ng-disabled="colFilter.term === undefined || colFilter.term === null || colFilter.term === \'\'" ng-show="colFilter.term !== undefined && colFilter.term !== null && colFilter.term !== \'\'"><i class="ui-grid-icon-cancel" ui-grid-one-bind-aria-label="aria.removeFilter">&nbsp;</i></div></div><div ng-if="colFilter.type === \'select\'"><select class="ui-grid-filter-select ui-grid-filter-input-{{$index}}" ng-model="colFilter.term" ng-show="colFilter.selectOptions.length > 0" ng-attr-placeholder="{{colFilter.placeholder || aria.defaultFilterLabel}}" aria-label="{{colFilter.ariaLabel || \'\'}}" ng-options="option.value as option.label for option in colFilter.selectOptions"><option value=""></option></select><div role="button" class="ui-grid-filter-button-select" ng-click="removeFilter(colFilter, $index)" ng-if="!colFilter.disableCancelFilterButton" ng-disabled="colFilter.term === undefined || colFilter.term === null || colFilter.term === \'\'" ng-show="colFilter.term !== undefined && colFilter.term != null"><i class="ui-grid-icon-cancel" ui-grid-one-bind-aria-label="aria.removeFilter">&nbsp;</i></div></div></div>'),e.put("ui-grid/ui-grid-footer",'<div class="ui-grid-footer-panel ui-grid-footer-aggregates-row">\x3c!-- tfooter --\x3e<div class="ui-grid-footer ui-grid-footer-viewport"><div class="ui-grid-footer-canvas"><div class="ui-grid-footer-cell-wrapper" ng-style="colContainer.headerCellWrapperStyle()"><div role="row" class="ui-grid-footer-cell-row"><div ui-grid-footer-cell role="gridcell" ng-repeat="col in colContainer.renderedColumns track by col.uid" col="col" render-index="$index" class="ui-grid-footer-cell ui-grid-clearfix"></div></div></div></div></div></div>'),e.put("ui-grid/ui-grid-grid-footer",'<div class="ui-grid-footer-info ui-grid-grid-footer"><span>{{\'search.totalItems\' | t}} {{grid.rows.length}}</span> <span ng-if="grid.renderContainers.body.visibleRowCache.length !== grid.rows.length" class="ngLabel">({{"search.showingItems" | t}} {{grid.renderContainers.body.visibleRowCache.length}})</span></div>'),e.put("ui-grid/ui-grid-header",'<div role="rowgroup" class="ui-grid-header">\x3c!-- theader --\x3e<div class="ui-grid-top-panel"><div class="ui-grid-header-viewport"><div class="ui-grid-header-canvas"><div class="ui-grid-header-cell-wrapper" ng-style="colContainer.headerCellWrapperStyle()"><div role="row" class="ui-grid-header-cell-row"><div role="columnheader" class="ui-grid-header-cell ui-grid-clearfix" ng-repeat="col in colContainer.renderedColumns track by col.uid" ui-grid-header-cell col="col" render-index="$index"></div></div></div></div></div></div></div>'),e.put("ui-grid/ui-grid-menu-button",'<div class="ui-grid-menu-button"><div role="button" ui-grid-one-bind-id-grid="\'grid-menu\'" ui-grid-one-bind-aria-label="i18n.aria.buttonLabel" tabindex="0" class="ui-grid-icon-container" ng-click="toggleMenu()" ng-keydown="toggleOnKeydown($event)" aria-expanded="{{shown}}" aria-haspopup="true"><i class="ui-grid-icon-menu" ui-grid-one-bind-aria-label="i18n.aria.buttonLabel">&nbsp;</i></div><div ui-grid-menu menu-items="menuItems"></div></div>'),e.put("ui-grid/ui-grid-menu-header-item",'<li role="menuitem"><div class="ui-grid-menu-item" role="heading" aria-level="2" ng-show="itemShown()"><i aria-hidden="true">&nbsp; </i><span ng-bind="label()"></span></div></li>'),e.put("ui-grid/ui-grid-no-header",'<div class="ui-grid-top-panel"></div>'),e.put("ui-grid/ui-grid-row","<div ng-repeat=\"(colRenderIndex, col) in colContainer.renderedColumns track by col.uid\" ui-grid-one-bind-id-grid=\"rowRenderIndex + '-' + col.uid + '-cell'\" class=\"ui-grid-cell\" ng-class=\"{ 'ui-grid-row-header-cell': col.isRowHeader }\" role=\"{{col.isRowHeader ? 'rowheader' : 'gridcell'}}\" ui-grid-cell></div>"),e.put("ui-grid/ui-grid",'<div ui-i18n="en" class="ui-grid">\x3c!-- TODO (c0bra): add "scoped" attr here, eventually? --\x3e<style ui-grid-style>.grid{{ grid.id }} {\n      /* Styles for the grid */\n    }\n\n    .grid{{ grid.id }} .ui-grid-row, .grid{{ grid.id }} .ui-grid-cell, .grid{{ grid.id }} .ui-grid-cell .ui-grid-vertical-bar {\n      height: {{ grid.options.rowHeight }}px;\n    }\n\n    .grid{{ grid.id }} .ui-grid-row:last-child .ui-grid-cell {\n      border-bottom-width: {{ (((grid.getVisibleRowCount() * grid.options.rowHeight) < grid.getViewportHeight()) && \'1\') || \'0\' }}px;\n    }\n\n    {{ grid.verticalScrollbarStyles }}\n    {{ grid.horizontalScrollbarStyles }}\n\n    /*\n    .ui-grid[dir=rtl] .ui-grid-viewport {\n      padding-left: {{ grid.verticalScrollbarWidth }}px;\n    }\n    */\n\n    {{ grid.customStyles }}</style><div class="ui-grid-contents-wrapper" role="grid"><div ui-grid-menu-button ng-if="grid.options.enableGridMenu"></div><div ng-if="grid.hasLeftContainer()" style="width: 0" ui-grid-pinned-container="\'left\'"></div><div ui-grid-render-container container-id="\'body\'" col-container-name="\'body\'" row-container-name="\'body\'" bind-scroll-horizontal="true" bind-scroll-vertical="true" enable-horizontal-scrollbar="grid.options.enableHorizontalScrollbar" enable-vertical-scrollbar="grid.options.enableVerticalScrollbar"></div><div ng-if="grid.hasRightContainer()" style="width: 0" ui-grid-pinned-container="\'right\'"></div><div ui-grid-grid-footer ng-if="grid.options.showGridFooter"></div><div ui-grid-column-menu ng-if="grid.options.enableColumnMenus"></div><div ng-transclude></div></div></div>'),e.put("ui-grid/uiGridCell",'<div class="ui-grid-cell-contents" title="TOOLTIP">{{COL_FIELD CUSTOM_FILTERS}}</div>'),e.put("ui-grid/uiGridColumnMenu",'<div class="ui-grid-column-menu"><div ui-grid-menu menu-items="menuItems" col="col">\x3c!-- <div class="ui-grid-column-menu">\n    <div class="inner" ng-show="menuShown">\n      <ul>\n        <div ng-show="grid.options.enableSorting">\n          <li ng-click="sortColumn($event, asc)" ng-class="{ \'selected\' : col.sort.direction == asc }"><i class="ui-grid-icon-sort-alt-up"></i> Sort Ascending</li>\n          <li ng-click="sortColumn($event, desc)" ng-class="{ \'selected\' : col.sort.direction == desc }"><i class="ui-grid-icon-sort-alt-down"></i> Sort Descending</li>\n          <li ng-show="col.sort.direction" ng-click="unsortColumn()"><i class="ui-grid-icon-cancel"></i> Remove Sort</li>\n        </div>\n      </ul>\n    </div>\n  </div> --\x3e</div></div>'),e.put("ui-grid/uiGridFooterCell",'<div class="ui-grid-cell-contents" col-index="renderIndex"><div>{{ col.getAggregationText() + ( col.getAggregationValue() CUSTOM_FILTERS ) }}</div></div>'),e.put("ui-grid/uiGridHeaderCell",'<div role="columnheader" ng-class="{ \'sortable\': sortable, \'ui-grid-header-cell-last-col\': isLastCol }" ui-grid-one-bind-aria-labelledby-grid="col.uid + \'-header-text \' + col.uid + \'-sortdir-text\'" aria-sort="{{col.sort.direction == asc ? \'ascending\' : ( col.sort.direction == desc ? \'descending\' : (!col.sort.direction ? \'none\' : \'other\'))}}"><div role="button" tabindex="0" ng-keydown="handleKeyDown($event)" class="ui-grid-cell-contents ui-grid-header-cell-primary-focus" col-index="renderIndex" title="TOOLTIP"><span class="ui-grid-header-cell-label" ui-grid-one-bind-id-grid="col.uid + \'-header-text\'">{{ col.displayName CUSTOM_FILTERS }}</span> <span ui-grid-one-bind-id-grid="col.uid + \'-sortdir-text\'" ui-grid-visible="col.sort.direction" aria-label="{{getSortDirectionAriaLabel()}}"><i ng-class="{ \'ui-grid-icon-up-dir\': col.sort.direction == asc, \'ui-grid-icon-down-dir\': col.sort.direction == desc, \'ui-grid-icon-blank\': !col.sort.direction }" title="{{isSortPriorityVisible() ? i18n.headerCell.priority + \' \' + ( col.sort.priority + 1 )  : null}}" aria-hidden="true"></i> <sub ui-grid-visible="isSortPriorityVisible()" class="ui-grid-sort-priority-number">{{col.sort.priority + 1}}</sub></span></div><div role="button" tabindex="0" ui-grid-one-bind-id-grid="col.uid + \'-menu-button\'" class="ui-grid-column-menu-button" ng-if="grid.options.enableColumnMenus && !col.isRowHeader  && col.colDef.enableColumnMenu !== false" ng-click="toggleMenu($event)" ng-keydown="headerCellArrowKeyDown($event)" ui-grid-one-bind-aria-label="i18n.headerCell.aria.columnMenuButtonLabel" aria-expanded="{{col.menuShown}}" aria-haspopup="true"><i class="ui-grid-icon-angle-down" aria-hidden="true">&nbsp;</i></div><div ui-grid-filter ng-hide="col.filterContainer === \'columnMenu\'"></div></div>'),e.put("ui-grid/uiGridMenu",'<div class="ui-grid-menu" ng-keydown="checkKeyDown($event)" ng-show="shown"><style ui-grid-style>{{dynamicStyles}}</style><div class="ui-grid-menu-mid" ng-show="shownMid"><div class="ui-grid-menu-inner" ng-if="shown"><ul role="menu" class="ui-grid-menu-items"><li ng-repeat="item in menuItems" role="menuitem" ui-grid-menu-item ui-grid-one-bind-id="\'menuitem-\'+$index" action="item.action" name="item.title" active="item.active" icon="item.icon" shown="item.shown" context="item.context" template-url="item.templateUrl" leave-open="item.leaveOpen" screen-reader-only="item.screenReaderOnly"></li><li ng-if="col.filterable && col.filterContainer === \'columnMenu\'"><div ui-grid-filter></div></li></ul></div></div></div>'),e.put("ui-grid/uiGridMenuItem",'<button type="button" class="ui-grid-menu-item" ng-click="itemAction($event, title)" ng-show="itemShown()" ng-class="{ \'ui-grid-menu-item-active\': active(), \'ui-grid-sr-only\': (!focus && screenReaderOnly) }" aria-pressed="{{active()}}" tabindex="0" ng-focus="focus=true" ng-blur="focus=false"><i ng-class="icon" aria-hidden="true">&nbsp; </i>{{ label() }}</button>'),e.put("ui-grid/uiGridRenderContainer","<div role=\"presentation\" ui-grid-one-bind-id-grid=\"containerId + '-grid-container'\" class=\"ui-grid-render-container\" ng-style=\"{ 'margin-left': colContainer.getMargin('left') + 'px', 'margin-right': colContainer.getMargin('right') + 'px' }\">\x3c!-- All of these dom elements are replaced in place --\x3e<div ui-grid-header></div><div ui-grid-viewport></div><div ng-if=\"colContainer.needsHScrollbarPlaceholder()\" class=\"ui-grid-scrollbar-placeholder\" ng-style=\"{height: colContainer.grid.scrollbarHeight + 'px'}\"></div><ui-grid-footer ng-if=\"grid.options.showColumnFooter\"></ui-grid-footer></div>"),e.put("ui-grid/uiGridViewport",'<div role="rowgroup" class="ui-grid-viewport" ng-style="colContainer.getViewportStyle()">\x3c!-- tbody --\x3e<div class="ui-grid-canvas"><div ng-repeat="(rowRenderIndex, row) in rowContainer.renderedRows track by $index" class="ui-grid-row" ng-style="Viewport.rowStyle(rowRenderIndex)"><div role="row" ui-grid-row="row" row-render-index="rowRenderIndex"></div></div></div></div>'),e.put("ui-grid/cellEditor",'<div><form name="inputForm"><input type="INPUT_TYPE" ng-class="\'colt\' + col.uid" ui-grid-editor ng-model="MODEL_COL_FIELD"></form></div>'),e.put("ui-grid/dropdownEditor",'<div><form name="inputForm"><select ng-class="\'colt\' + col.uid" ui-grid-edit-dropdown ng-model="MODEL_COL_FIELD" ng-options="field[editDropdownIdLabel] as field[editDropdownValueLabel] CUSTOM_FILTERS for field in editDropdownOptionsArray"></select></form></div>'),e.put("ui-grid/fileChooserEditor",'<div><form name="inputForm"><input ng-class="\'colt\' + col.uid" ui-grid-edit-file-chooser type="file" id="files" name="files[]" ng-model="MODEL_COL_FIELD"></form></div>'),e.put("ui-grid/emptyBaseLayerContainer",'<div class="ui-grid-empty-base-layer-container ui-grid-canvas"><div class="ui-grid-row" ng-repeat="(rowRenderIndex, row) in grid.baseLayer.emptyRows track by $index" ng-style="Viewport.rowStyle(rowRenderIndex)"><div><div><div ng-repeat="(colRenderIndex, col) in colContainer.renderedColumns track by col.colDef.name" class="ui-grid-cell {{ col.getColClass(false) }}"></div></div></div></div></div>'),e.put("ui-grid/expandableRow",'<div ui-grid-expandable-row ng-if="expandableRow.shouldRenderExpand()" class="expandableRow" style="float:left; margin-top: 1px; margin-bottom: 1px" ng-style="{width: (grid.renderContainers.body.getCanvasWidth()) + \'px\', height: row.expandedRowHeight + \'px\'}"></div>'),e.put("ui-grid/expandableRowHeader",'<div class="ui-grid-row-header-cell ui-grid-expandable-buttons-cell"><div class="ui-grid-cell-contents"><i class="clickable" ng-if="!(row.groupHeader==true || row.entity.subGridOptions.disableRowExpandable)" ng-class="{ \'ui-grid-icon-plus-squared\' : !row.isExpanded, \'ui-grid-icon-minus-squared\' : row.isExpanded }" ng-click="grid.api.expandable.toggleRowExpansion(row.entity, $event)" aria-expanded="{{!!row.isExpanded}}"></i></div></div>'),e.put("ui-grid/expandableScrollFiller","<div ng-if=\"expandableRow.shouldRenderFiller()\" ng-class=\"{scrollFiller: true, scrollFillerClass:(colContainer.name === 'body')}\" ng-style=\"{ width: (grid.getViewportWidth()) + 'px', height: row.expandedRowHeight + 2 + 'px', 'margin-left': grid.options.rowHeader.rowHeaderWidth + 'px' }\">&nbsp;</div>"),e.put("ui-grid/expandableTopRowHeader",'<div class="ui-grid-row-header-cell ui-grid-expandable-buttons-cell"><div class="ui-grid-cell-contents"><span class="ui-grid-cell-empty" ng-if="!grid.options.showExpandAllButton"></span> <button type="button" class="ui-grid-icon-button clickable" ng-if="grid.options.showExpandAllButton" ng-class="{ \'ui-grid-icon-plus-squared\' : !grid.expandable.expandedAll, \'ui-grid-icon-minus-squared\' : grid.expandable.expandedAll }" ng-click="grid.api.expandable.toggleAllRows()" aria-expanded="{{grid.expandable.expandedAll}}"></button></div></div>'),e.put("ui-grid/csvLink",'<span class="ui-grid-exporter-csv-link-span"><a href="data:text/csv;charset=UTF-8,CSV_CONTENT" download="FILE_NAME">LINK_LABEL</a></span>'),e.put("ui-grid/importerMenuItem",'<li class="ui-grid-menu-item"><form><input class="ui-grid-importer-file-chooser" type="file" id="files" name="files[]"></form></li>'),e.put("ui-grid/importerMenuItemContainer","<div ui-grid-importer-menu-item></div>"),e.put("ui-grid/pagination",'<div class="ui-grid-pager-panel" ui-grid-pager ng-show="grid.options.enablePaginationControls"><div role="navigation" class="ui-grid-pager-container"><div class="ui-grid-pager-control"><button type="button" class="ui-grid-pager-first" ui-grid-one-bind-title="aria.pageToFirst" ui-grid-one-bind-aria-label="aria.pageToFirst" ng-click="pageFirstPageClick()" ng-disabled="cantPageBackward()"><div ng-class="grid.isRTL() ? \'last-triangle\' : \'first-triangle\'"><div ng-class="grid.isRTL() ? \'last-bar-rtl\' : \'first-bar\'"></div></div></button> <button type="button" class="ui-grid-pager-previous" ui-grid-one-bind-title="aria.pageBack" ui-grid-one-bind-aria-label="aria.pageBack" ng-click="pagePreviousPageClick()" ng-disabled="cantPageBackward()"><div ng-class="grid.isRTL() ? \'last-triangle prev-triangle\' : \'first-triangle prev-triangle\'"></div></button> <input type="number" ui-grid-one-bind-title="aria.pageSelected" ui-grid-one-bind-aria-label="aria.pageSelected" class="ui-grid-pager-control-input" ng-model="grid.options.paginationCurrentPage" min="1" max="{{ paginationApi.getTotalPages() }}" step="1" required> <span class="ui-grid-pager-max-pages-number" ng-show="paginationApi.getTotalPages() > 0"><abbr ui-grid-one-bind-title="paginationOf">/ </abbr>{{ paginationApi.getTotalPages() }} </span><button type="button" class="ui-grid-pager-next" ui-grid-one-bind-title="aria.pageForward" ui-grid-one-bind-aria-label="aria.pageForward" ng-click="pageNextPageClick()" ng-disabled="cantPageForward()"><div ng-class="grid.isRTL() ? \'first-triangle next-triangle\' : \'last-triangle next-triangle\'"></div></button> <button type="button" class="ui-grid-pager-last" ui-grid-one-bind-title="aria.pageToLast" ui-grid-one-bind-aria-label="aria.pageToLast" ng-click="pageLastPageClick()" ng-disabled="cantPageToLast()"><div ng-class="grid.isRTL() ? \'first-triangle\' : \'last-triangle\'"><div ng-class="grid.isRTL() ? \'first-bar-rtl\' : \'last-bar\'"></div></div></button></div><div class="ui-grid-pager-row-count-picker" ng-if="grid.options.paginationPageSizes.length > 1 && !grid.options.useCustomPagination"><select ui-grid-one-bind-aria-labelledby-grid="\'items-per-page-label\'" ng-model="grid.options.paginationPageSize" ng-options="o as o for o in grid.options.paginationPageSizes"></select> <span ui-grid-one-bind-id-grid="\'items-per-page-label\'" class="ui-grid-pager-row-count-label">&nbsp;{{sizesLabel}}</span></div><span ng-if="grid.options.paginationPageSizes.length <= 1" class="ui-grid-pager-row-count-label">{{grid.options.paginationPageSize}}&nbsp;{{sizesLabel}}</span></div><div class="ui-grid-pager-count-container"><div class="ui-grid-pager-count"><span ng-show="grid.options.totalItems > 0">{{ 1 + paginationApi.getFirstRowIndex() }} <abbr ui-grid-one-bind-title="paginationThrough">- </abbr>{{ 1 + paginationApi.getLastRowIndex() }} {{paginationOf}} {{grid.options.totalItems}} {{totalItemsLabel}}</span></div></div></div>'),e.put("ui-grid/columnResizer",'<div ui-grid-column-resizer ng-if="grid.options.enableColumnResizing" class="ui-grid-column-resizer" col="col" position="right" render-index="renderIndex" unselectable="on"></div>'),e.put("ui-grid/gridFooterSelectedItems",'<span ng-if="grid.selection.selectedCount !== 0 && grid.options.enableFooterTotalSelected">({{"search.selectedItems" | t}} {{grid.selection.selectedCount}})</span>'),e.put("ui-grid/selectionHeaderCell",'<div>\x3c!-- <div class="ui-grid-vertical-bar">&nbsp;</div> --\x3e<div class="ui-grid-cell-contents" col-index="renderIndex"><ui-grid-selection-select-all-buttons ng-if="grid.options.enableSelectAll" role="checkbox" ng-model="grid.selection.selectAll"></ui-grid-selection-select-all-buttons></div></div>'),e.put("ui-grid/selectionRowHeader",'<div class="ui-grid-cell-contents ui-grid-disable-selection clickable"><ui-grid-selection-row-header-buttons></ui-grid-selection-row-header-buttons></div>'),e.put("ui-grid/selectionRowHeaderButtons",'<div class="ui-grid-selection-row-header-buttons ui-grid-icon-ok clickable" ng-class="{\'ui-grid-row-selected\': row.isSelected}" tabindex="0" ng-click="selectButtonClick(row, $event)" ng-keydown="selectButtonKeyDown(row, $event)" ng-attr-aria-label="{{(\'selection.aria.row\' | t) + \' \' + (row.index + 1) + \', \' + col.displayName}}" aria-checked="{{row.isSelected}}" role="checkbox" ng-model="row.isSelected">&nbsp;</div>'),e.put("ui-grid/selectionSelectAllButtons",'<div role="checkbox" tabindex="0" class="ui-grid-selection-row-header-buttons ui-grid-icon-ok" ui-grid-one-bind-aria-label="\'selection.selectAll\' | t" aria-checked="{{grid.selection.selectAll}}" ng-class="{\'ui-grid-all-selected\': grid.selection.selectAll}" ng-click="headerButtonClick($event)" ng-keydown="headerButtonKeyDown($event)"></div>'),e.put("ui-grid/treeBaseExpandAllButtons",'<div class="ui-grid-tree-base-row-header-buttons" tabindex="0" ng-class="headerButtonClass()" ng-click="headerButtonClick($event)" ng-keydown="headerButtonKeyDown($event)"></div>'),e.put("ui-grid/treeBaseHeaderCell",'<div><div class="ui-grid-cell-contents" col-index="renderIndex"><ui-grid-tree-base-expand-all-buttons ng-if="grid.options.enableExpandAll"></ui-grid-tree-base-expand-all-buttons></div></div>'),e.put("ui-grid/treeBaseRowHeader",'<div class="ui-grid-cell-contents"><ui-grid-tree-base-row-header-buttons></ui-grid-tree-base-row-header-buttons></div>'),e.put("ui-grid/treeBaseRowHeaderButtons",'<div class="ui-grid-tree-base-row-header-buttons" ng-class="{\'ui-grid-tree-base-header\': row.treeLevel > -1 }" tabindex="0" ng-keydown="treeButtonKeyDown(row, $event)" ng-click="treeButtonClick(row, $event)"><i ng-class="treeButtonClass(row)" ng-style="{\'padding-left\': grid.options.treeIndent * row.treeLevel + \'px\'}"></i> &nbsp;</div>'),e.put("ui-grid/cellTitleValidator",'<div class="ui-grid-cell-contents" ng-class="{invalid:grid.validate.isInvalid(row.entity,col.colDef)}" title="{{grid.validate.getTitleFormattedErrors(row.entity,col.colDef)}}">{{COL_FIELD CUSTOM_FILTERS}}</div>'),e.put("ui-grid/cellTooltipValidator",'<div class="ui-grid-cell-contents" ng-class="{invalid:grid.validate.isInvalid(row.entity,col.colDef)}" tooltip-html-unsafe="{{grid.validate.getFormattedErrors(row.entity,col.colDef)}}" tooltip-enable="grid.validate.isInvalid(row.entity,col.colDef)" tooltip-append-to-body="true" tooltip-placement="top" title="TOOLTIP">{{COL_FIELD CUSTOM_FILTERS}}</div>')}]);
