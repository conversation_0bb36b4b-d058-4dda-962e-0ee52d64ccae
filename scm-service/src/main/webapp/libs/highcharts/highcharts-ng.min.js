/**
 * highcharts-ng
 * @version v1.1.0 - 2017-03-27
 * @link https://github.com/pablojim/highcharts-ng
 * <AUTHOR> <>
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"undefined"!=typeof module&&"undefined"!=typeof exports&&module.exports===exports&&(module.exports="highcharts-ng"),function(){"use strict";function a(a,f){var g=0,h=this,i={},j={},k=h.changeDetection||angular.equals;this.$onInit=function(){h.config.getChartObj=function(){return h.chart},i=angular.merge({},h.config),j=b(a,h.config,g),h.chart=new(e[c(j)])(j);var d=a[0].clientWidth,k=a[0].clientHeight;f(function(){a[0].clientWidth===d&&a[0].clientHeight===k||h.chart.reflow()},0,!1)},this.$doCheck=function(){if(!k(h.config,i)){i=angular.merge({},h.config),j=b(a,h.config,g);var c=d(j.series,g);if(j.series){for(var e=h.chart.series.length-1;e>=0;e--){var f=h.chart.series[e];"highcharts-navigator-series"!==f.options.id&&c.indexOf(f.options.id)<0&&f.remove(!1)}angular.forEach(h.config.series,function(a){h.chart.get(a.id)||h.chart.addSeries(a)})}h.chart.update(j,!0)}},this.$onDestroy=function(){if(h.chart){try{h.chart.destroy()}catch(b){}f(function(){a.remove()},0)}}}function b(a,b,c){var e={},f={chart:{events:{}},title:{},subtitle:{},series:[],credits:{},plotOptions:{},navigator:{}};return b?(b.series&&d(b.series,c),e=angular.merge(f,b)):e=f,e.chart.renderTo=a[0],e}function c(a){return void 0===a||void 0===a.chartType?"Chart":f[(""+a.chartType).toLowerCase()]}function d(a,b){var c=[];return angular.forEach(a,function(a){angular.isDefined(a.id)||(a.id="series-"+b++),c.push(a.id)}),c}var e=null;window&&window.Highcharts?e=window.Highcharts:module&&"highcharts-ng"===module.exports&&(e=require("highcharts")),angular.module("highcharts-ng",[]).component("highchart",{bindings:{config:"<",changeDetection:"<"},controller:a}),a.$inject=["$element","$timeout"];var f={stock:"StockChart",map:"Map",chart:"Chart"}}();