!function(e,t){var n=!!(window&&"global_scanner"in window)&&window.global_scanner;!n&&"undefined"!=typeof module&&module.exports?module.exports=t():!n&&"function"==typeof define&&define.amd?define(t):e.scanner=t()}(this,function(){var e,t=(new Date).getTime()+"-"+Math.floor(1001*Math.random()+0),n=function(e,t,n,i,s,r){this.mimeType=e,this.srcIsBase64=t,this.src=n,this.imageInfo=i,this.selectedAction=s,this.selectedFileName=r};n.prototype.getWidth=function(){return"object"==typeof this.imageInfo?this.imageInfo.ImageWidth:void 0},n.prototype.getHeight=function(){return"object"==typeof this.imageInfo?this.imageInfo.ImageLength:void 0},n.prototype.getBitsPerPixel=function(){return"object"==typeof this.imageInfo?this.imageInfo.BitsPerPixel:void 0},n.prototype.isColor=function(){return"object"==typeof this.imageInfo?2==this.imageInfo.PixelType:void 0},n.prototype.isGray=function(){return"object"==typeof this.imageInfo?1==this.imageInfo.PixelType:void 0},n.prototype.isBlackWhite=function(){return"object"==typeof this.imageInfo?0==this.imageInfo.PixelType:void 0},n.prototype.getResolution=function(){return"object"==typeof this.imageInfo?Math.floor(this.imageInfo.XResolution):void 0},n.prototype.getBase64NoPrefix=function(){if(this.srcIsBase64)return this.srcIsBase64?this.base64ToBlob(this.src):void 0},n.prototype.toString=function(){var e="";this.isColor()?e+="Color ":this.isGray()?e+="Gray ":this.isBlackWhite()&&(e+="Black/white "),e+="image:";var t=this.getWidth(),n=this.getHeight(),i=this.getBitsPerPixel();this.getResolution();return t>0&&n>0&&(e+=" "+t+" x "+n),i>0&&(e+=" "+i+"-bit"),e};var i=[],s="^(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)(?:[_\\.](\\d+))?)?)?$",r={version:"2.05",MIME_TYPE_BINARY:"application/octet-stream",MIME_TYPE_BMP:"image/bmp",MIME_TYPE_GIF:"image/png",MIME_TYPE_JPEG:"image/jpeg",MIME_TYPE_PNG:"image/tiff",MIME_TYPE_PDF:"application/pdf",scanAppletEnabled:!0,scanAppletCodeBase:void 0,scanAppletCodeBaseFallbackIfNoScannerJs:"https://asprise.azureedge.net/scannerjs/",scanAppEnabled:!0,scanAppDownloadUrl:"http://asprise.azureedge.net/scanapp/scan-setup.exe",scanAppMinVersionRequired:"2.04",scanAppLicense:void 0,skipLoadDefaultCss:!1,scanWebSocketServerRangeLowest:9713,scanWebSocketServerRangeHighest:9716,scanWebSocketServerPort:void 0,scanWebSocket:void 0,scanWebSocketConnectAttemptTime:void 0,requestStatus:void 0,requestStatusSetOn:void 0,REQUEST_STATUS_PENDING:"PENDING",REQUEST_STATUS_PROCESSING:"PROCESSING",REQUEST_STATUS_COMPLETE:"COMPLETE",funcCallsCancelled:[],requestFuncCall:void 0,requestCallback:void 0,formFieldNameForImagesObjects:"com_asprise_scannerjs_images[]",formFieldNameForImagesUrls:"com_asprise_scannerjs_images_urls[]",isInitialized:!1,eagerInitialization:!(window&&"scannerjs_eager_init"in window)||window.scannerjs_eager_init,fileCount:0,displayInstallScanAppFunc:void 0,displayScanReadyPopupFunc:void 0,scannerJsEventListener:window&&"scannerjs_event_listener"in window?window.scannerjs_event_listener:void 0,systemResponseCallbackFunc:window&&"scannerjs_sys_response_callback_func"in window?window.scannerjs_sys_response_callback_func:void 0,initialize:function(){if(!this.isInitialized){this.scannerJsEventListener&&this.scannerJsEventListener("pre-init"),this.logToConsole("Initializing Scanner.js ...");var e=!1,t=this.getLastConnectedTime(!0);t>0&&(new Date).getTime()-t<6048e5&&(window&&"scannerjs_force_java"in window&&window.scannerjs_force_java||(e=!0)),!e&&this.canUseJavaAppletImpl()&&this.appendDeployJavaElementsToBody(),this.skipLoadDefaultCss||this.loadScannerCssIfNotPresent(),this.logToConsole(this.bowser.name+" "+this.bowser.version+" supports NPAPI? "+this.doesCurrentBrowserSupportNPAPI());try{!e&&this.canUseJavaAppletImpl()&&this.doesCurrentBrowserSupportNPAPI()&&this.addScanAppletToDomIfNotExists(null,1,1)}catch(e){this.logToConsole("Failed to add applet element: "+e,!0)}this.canUseWebSocketImpl()&&(e||!this.hasJava()||this.bowser.firefox?window.setTimeout(function(){scanner.connectToScanWebSocketServer()},2e5):this.logToConsole("Java: "+this.deployJava.getJREs())),window.setInterval(function(){scanner.monitorFuncCallRequest()},4e3),this.logToConsole("Scanner.js initialized."),this.isInitialized=!0,this.scannerJsEventListener&&this.scannerJsEventListener("post-init")}},monitorFuncCallRequest:function(){void 0!==this.requestStatus&&void 0!==this.requestStatusSetOn&&this.requestStatus==this.REQUEST_STATUS_PENDING&&(new Date).getTime()-this.requestStatusSetOn>1e4&&this.displayInstallScanAppEnableJavaPopup(!0)},callFunction:function(e,t,n,i,s){if(arguments.length<2)return this.reportError("Invalid function call - func name is not specified"),!1;if("function"!=typeof e)return this.reportError("asprise_scanner_js_call_function requires a valid callbackFunc"),!1;if(this.requestStatus==this.REQUEST_STATUS_PENDING||this.requestStatus==this.REQUEST_STATUS_PROCESSING)return this.reportError("You can not submit a new request as the previous request has not completed yet."),!1;if(!JSON||"function"!=typeof JSON.stringify)return this.reportError("JSON.stringify is not supported by your browser."),!1;for(var r={funcCallId:(new Date).getTime()+"-"+Math.floor(1e4*Math.random()),funcName:t,time:(new Date).getTime(),userAgent:navigator.userAgent,isModernBrowser:this.isModernBrowser(),windowTitle:document.title,url:window.location.href,pageLoadId:this.getPageSessionId()},a=[],o=2;o<arguments.length;o++)a.push(arguments[o]);r.funcArgs=a;var l=JSON.stringify(r);return this.requestStatus=this.REQUEST_STATUS_PENDING,this.requestStatusSetOn=(new Date).getTime(),this.requestFuncCall=l,this.requestCallback=e,this.scannerJsEventListener&&this.scannerJsEventListener("func-call",l),this.funcCallToWebSocket(),!0},cancelFuncCalls:function(){if(this.requestStatus==this.REQUEST_STATUS_PENDING||this.requestStatus==this.REQUEST_STATUS_PROCESSING){var e=JSON.parse(this.requestFuncCall);this.funcCallsCancelled.push(e),this.logToConsole("Canceled func call: "+e.funcName+", call id: "+e.funcCallId),this.scannerJsEventListener&&this.scannerJsEventListener("func-canceled",e)}this.requestStatus=void 0},funcCallToWebSocket:function(){if(!this.isConnectedToScanWebSocket())return!1;if(this.requestStatus!=this.REQUEST_STATUS_PENDING)return!0;try{return this.scanWebSocket.send(this.requestFuncCall),this.requestStatus=this.REQUEST_STATUS_PROCESSING,this.requestStatusSetOn=(new Date).getTime(),!0}catch(e){return this.functionReturn(!1,"Failed: "+e,null,null,null,null,null),this.logToConsole(e,!0),!1}},funcCallResponseFromWebSocket:function(e,t){if(e){var n=null;if("string"==typeof t&&t.length>6&&"<error"==t.substr(0,6))throw t;if(t&&t.length>"SYSTEM/".length&&"SYSTEM/"==t.substr(0,"SYSTEM/".length)){try{if(this.systemResponseCallbackFunc){var i=t.indexOf(":");i>0?this.systemResponseCallbackFunc(t.substr(0,i),t.substr(i+1)):this.reportError("Invalid system response: "+t)}}catch(e){}return}try{n=JSON.parse(t)}catch(e){return this.reportError("Failed to parse JSON: "+t),void this.functionReturn(!1,"Failed to parse JSON: "+t,null,null,null,null,null)}n instanceof Array?this.functionReturn(n[0],n[1],n[2],n[3],n[4],n.length>5?n[5]:null,n.length>6?n[6]:null):this.functionReturn(!1,"JSON returned from WebSocket is not array",null,null,null,null,null)}else this.functionReturn(!1,"Failed due to WebSocket error",null,null,null,null,null)},functionReturn:function(e,t,n,i,s,r,a){if(null!=s&&1!=s&&this.reportError("resultCount should be null or 1, actual: "+s),a)for(var o=0;o<this.funcCallsCancelled.length;o++){var l=this.funcCallsCancelled[o];if(null!=l&&l.funcCallId==a)return void this.logToConsole("Response is ignored for cancelled func call: "+l.funcName+", call id: "+l.funcCallId+":\n"+r)}this.requestStatus=this.REQUEST_STATUS_COMPLETE,this.requestStatusSetOn=(new Date).getTime(),this.requestCallback(e,t,r),this.scannerJsEventListener&&this.scannerJsEventListener("func-return")},onAppletStarted:function(e){this.logToConsole("Applet started: "+e),this._onReady(!1)},scan:function(e,t,n,i){if("string"!=typeof t&&(!JSON||"function"!=typeof JSON.stringify))return this.reportError("JSON.stringify is not supported by your browser."),!1;var s="string"==typeof t?t:JSON.stringify(t);return this.callFunction(e,"scan",s,arguments.length>=3?n:null,arguments.length>=4?i:null)},listSources:function(e,t,n,i,s,r){return this.callFunction(e,"listSources",!(arguments.length>=2)||t,arguments.length>=3?n:null,arguments.length>=4&&i,!(arguments.length>=5)||s,arguments.length>=6&&null!=r?"string"==typeof r?r:JSON.stringify(r):"")},getSource:function(e,t,n,i,s,r,a,o){return this.callFunction(e,"getSource",arguments.length>=2?t:"select",!(arguments.length>=3)||n,arguments.length>=4?i:null,!(arguments.length>=5)||s,arguments.length>=6&&r,arguments.length>=7?a:null,arguments.length>=8&&null!=o?"string"==typeof o?o:JSON.stringify(o):"")},getSystemInfo:function(e){return this.callFunction(e,"asprise_scan_system_info")},submitFormWithImages:function(e,t,i){if(t instanceof Array&&0!=t.length){for(var s=0;s<t.length;s++){if(!((o=t[s])instanceof n))return this.logToConsole("Invalid image object: "+o,!0),void this.reportError("Invalid image object!")}var r=document.getElementById(e);if(null!=r){var a=new FormData(r);for(s=0;s<t.length;s++){var o;(o=t[s]).srcIsBase64?a.append(this.formFieldNameForImagesObjects,this.base64ToBlob(o.src,o.mimeType)):a.append(this.formFieldNameForImagesUrls,o.src)}this.logToConsole("POST images, count: "+t.length);var l=new XMLHttpRequest;return l.open("POST",r.getAttribute("action"),!0),l.onreadystatechange=function(){"function"==typeof i&&i(l)},l.send(a),this.logToConsole("Form posted: "+l),l}this.reportError("Form doesn't exist: "+e)}else this.reportError("no images - form submit cancelled.")},getScannedImages:function(e,t,i){var s=[];if(arguments.length<2&&(t=!0),arguments.length<3&&(i=!1),!t&&!i)return this.reportError("getImages: will return no image as neither includeOriginals nor includeThumbnails is true."),s;if("string"==typeof e){if(e.length>6&&"<error"==e.substr(0,6))throw e;try{e=JSON.parse(e)}catch(t){throw e}}if("object"==typeof e&&e.output instanceof Array)for(var r=e.output,a=0;a<r.length;a++){var o=r[a];if(t&&"return-base64"==o.type||i&&"return-base64-thumbnail"==o.type){var l=this.getMimeType(o.format);if(l==this.MIME_TYPE_BINARY){this.logToConsole("Unable to find mime type for "+o.format);continue}if(!(o.result instanceof Array)){this.logToConsole("Output contains no result records",!0);continue}for(var c=0;c<o.result.length;c++){var u=e.images instanceof Array&&c<e.images.length?e.images[c].image_info:void 0;s.push(new n(l,!0,"data:"+l+";base64,"+o.result[c],u,o.hasOwnProperty("selected_action")?o.selected_action:void 0,o.hasOwnProperty("selected_filename")?o.selected_filename:void 0))}}}return s},getUploadResponse:function(e){if("string"==typeof e){if(e.length>6&&"<error"==e.substr(0,6))throw e;try{e=JSON.parse(e)}catch(t){throw e}}if("object"==typeof e&&e.output instanceof Array)for(var t=e.output,n=0;n<t.length;n++){var i=t[n];if(("upload"==i.type||"upload-thumbnail"==i.type)&&i.result instanceof Array&&i.result.length>0)return i.result[0]}},getSaveResponse:function(e){if("string"==typeof e){if(e.length>6&&"<error"==e.substr(0,6))throw e;try{e=JSON.parse(e)}catch(t){throw e}}if("object"==typeof e&&e.output instanceof Array)for(var t=e.output,n=0;n<t.length;n++){var i=t[n];if("save"==i.type||"save-thumbnail"==i.type)return JSON.stringify(i.result)}},onScanWebSocketMesg:function(e,t){scanner.funcCallResponseFromWebSocket(!0,t.data)},isConnectedToScanWebSocket:function(){return null!=this.scanWebSocket},_onReady:function(e){try{var t="sjs_"+(e?"app":"applet"),n=new Date;this.writeToLocalStorage(t+"_last_connected",n.getTime().toString(10)),this.setCookie(t+"_last_connected",n.getTime().toString(10))}catch(e){}this.scannerJsEventListener&&this.scannerJsEventListener("ready",e)},getLastConnectedTime:function(e){try{var t="sjs_"+(e?"app":"applet"),n=this.readFromLocalStorage(t+"_last_connected");if(n||(n=this.getCookie(t+"_last_connected")),n)return parseInt(n,10)}catch(e){}return-1},onScanWebSocketOpen:function(e,t){scanner.scanWebSocket=e,scanner.logToConsole("Scan WebSocket server connected: "+scanner.scanWebSocket.url),scanner.cancelFuncCalls(),scanner.displayInstallScanAppEnableJavaPopup(!1),scanner.displayScanReadyPopup(),scanner._onReady(!0);var n=scanner.scanAppLicense;n||(n=window.com_asprise_scan_app_license),n&&e.send("LICENSE: "+n),scanner.scanAppMinVersionRequired&&e.send("MIN_VERSION: "+scanner.scanAppMinVersionRequired),scanner.funcCallToWebSocket()},onScanWebSocketClose:function(e,t){scanner.scanWebSocket=void 0,scanner.connectToScanWebSocketServer(!0)},failedToConnectToWebSocketServer:function(){this.logToConsole("Failed to connect to WebSocket server.",!0),this.scannerJsEventListener&&this.scannerJsEventListener("fail-to-connect"),this.doesCurrentBrowserSupportNPAPI()||this.displayInstallScanAppEnableJavaPopup(!0),!(this.maxConnectionAttempts>0)||this.connectionAttempts<this.maxConnectionAttempts?this.connectToScanWebSocketServer():(this.connectionAttempts=0,this.isAttemptingToConnect=!1,this.scannerJsEventListener&&this.scannerJsEventListener("fail-to-connect-final"))},getScanWsHost:function(){return null!=window.location.href&&"http:"==window.location.href.substr(0,5)?"ws://127.0.0.1":"wss://local.scannerjs.com"},connectionAttempts:0,maxConnectionAttempts:-1,isAttemptingToConnect:!1,connectToScanWebSocketServer:function(e){if(this.canUseWebSocketImpl()){if(this.scanWebSocket)return this.scanWebSocketConnectAttemptTime=void 0,void this.scanWebSocket.close();e||(this.scanWebSocketServerPort=void 0,this.connectionAttempts+=1),this.isAttemptingToConnect=!0;var t=this.getScanWsHost(),n=t.length>3&&"wss"==t.substr(0,3).toLowerCase();null==this.scanWebSocketServerPort?this.scanWebSocketServerPort=this.scanWebSocketServerRangeLowest:null!=this.scanWebSocketConnectAttemptTime&&(new Date).getTime()-this.scanWebSocketConnectAttemptTime<1e4&&(this.scanWebSocketServerPort+=1),this.scanWebSocketServerPort%2!=(n?0:1)&&(this.scanWebSocketServerPort+=1),this.scanWebSocketServerPort<this.scanWebSocketServerRangeLowest||this.scanWebSocketServerPort>this.scanWebSocketServerRangeHighest?this.failedToConnectToWebSocketServer():(this.scanWebSocketConnectAttemptTime=(new Date).getTime(),this.createWebSocket(t+":"+this.scanWebSocketServerPort+"/"+this.getPageSessionId()+"/"+encodeURIComponent(this.removeFragmentFromUrl(window.location.href)),this.onScanWebSocketOpen,this.onScanWebSocketClose,this.onScanWebSocketMesg,void 0))}else this.reportError("Cancelled as scan app implementation is disabled explicitly.")},canUseWebSocketImpl:function(){return this.scanAppEnabled},canUseJavaAppletImpl:function(){return this.scanAppletEnabled},getScannerJsBaseUrl:function(){for(var e=document.getElementsByTagName("script"),t=0;t<e.length;t++){var n=e.item(t);if(n.src&&n.src.match(/scanner\.js(\?\w*)?$/)){var i=n.src;return i.substring(0,i.lastIndexOf("/")+1)}}return"https://asprise.azureedge.net/scannerjs/"},addScanAppletToDomIfNotExists:function(e,t,n){var i=this.scanAppletCodeBase;i||(i=this.getScannerJsBaseUrl()),i||(i=this.scanAppletCodeBaseFallbackIfNoScannerJs),i.indexOf("FORCE_REFRESH")>-1&&window.location.href.indexOf("FORCE_REFRESH")>-1&&(i=i.replace(/FORCE_REFRESH/g,"FORCE_REFRESH_"+(new Date).getTime()));var s=document.getElementById("com_asprise_scan_applet");if(null!=s)return!0;if(s=this.createAppletDomElement("com_asprise_scan_applet",i,this.is7u45OrAbove()?"asprise_scan.jar":"asprise_scan-legacy.jar","com.asprise.imaging.scan.ui.web.ScanApplet",t,n),null==e){var r=document.getElementsByTagName("body"),a=null!=r&&r.length>0?r.item(0):null;if(null==a)return this.reportError('please do not execute com_asprise_scan_addScanAppletToDomIfNotExists in <header>. document.getElementsByTagName("body").length = '+document.getElementsByTagName("body").length),!1;a.appendChild(s)}else e.appendChild(s);return!0},displayInstallScanAppEnableJavaPopup:function(e){if("function"==typeof this.displayInstallScanAppFunc)return this.displayInstallScanAppFunc(e);if(!e&&null==document.getElementById("com_asprise_scan_prompt"))return!0;this.addInstallPromptDomElementIfNotExists();var t=document.getElementById("com_asprise_scan_prompt");if(null==t)return this.reportError("Unable to find prompt of id: com_asprise_scan_prompt"),!1;for(var n=this.doesCurrentBrowserSupportNPAPI(),i=t.querySelectorAll(".java"),s=0;s<i.length;s++)i[s].style.display=n?"block":"none";var r=t.querySelectorAll(".nojava");for(s=0;s<r.length;s++)r[s].style.display=n?"none":"block";t.style.display=e?"block":"none";var a=document.getElementById("com_asprise_scan_prompt_overlay");return null==a?this.logToConsole("Prompt overlay doesn't exist (id: com_asprise_scan_prompt_overlay)."):a.style.display=e?"block":"none",!0},displayScanReadyPopup:function(){if("function"==typeof this.displayScanReadyPopupFunc)return this.displayScanReadyPopupFunc();this.addScanReadyPromptDomElementIfNotExists();var e=document.getElementById("com_asprise_scan_app_ok");return null!=e&&(e.style.display="block",window.setTimeout(function(){scanner.setElementStyleDisplay(document.getElementById("com_asprise_scan_app_ok"),"none")},4e5),!0)},addInstallPromptDomElementIfNotExists:function(){var e=this.scanAppDownloadUrl;if(null!=document.getElementById("com_asprise_scan_prompt"))return!0;var t={name:"div",attributes:{id:"com_asprise_scan_prompt",class:"com_asprise_scan_prompt_class prompt-dialog",style:"display: none;"},children:[{name:"a",attributes:{class:"top-right-closer",title:"Dismiss this dialog",onclick:"scanner.displayInstallScanAppEnableJavaPopup(false);"}},{name:"h3",attributes:{style:"margin-top: 0px;",text:""}},{name:"div",attributes:{class:"java",style:"display: none;"},children:[{name:"p",attributes:{text:""}},{name:"table",attributes:{width:"100%"},children:[{name:"td",attributes:{width:"50%",align:"center",valign:"top"},children:[{name:"h4",attributes:{text:"Either Option 1:"}},{name:"a",attributes:{class:"icon-action icon-action-enable-java",href:"javascript:scanner.displayEnableJavaHelp();"}},{name:"p",attributes:{style:"vertical-align: middle; text-align: center;"},children:[{name:"a",attributes:{href:"javascript:scanner.displayEnableJavaHelp();",text:"Enable Java"}},{name:"span",attributes:{text:"; once done, please "}},{name:"button",attributes:{type:"button",class:"btn btn-default btn-xs",onclick:"document.location.reload(false);",style:"",text:"Refresh Page"}}]}]},{name:"td",attributes:{width:"10px"}},{name:"td",attributes:{width:"1px",style:"background-color: #cccccc;"}},{name:"td",attributes:{width:"10px"}},{name:"td",attributes:{align:"center",valign:"top"},children:[{name:"h4",attributes:{text:"Or Option 2:"}},{name:"a",attributes:{class:"icon-action icon-action-install-app",href:e,target:"_blank"}},{name:"p",children:[{name:"a",attributes:{href:e,target:"_blank",text:""}},{name:"span",attributes:{text:". No need to refresh page."}}]}]}]}]},{name:"div",attributes:{class:"nojava",style:"display: none;"},children:[{name:"p",attributes:{text:""}},{name:"a",attributes:{class:"icon-action icon-action-install-app",href:e,target:"_blank"}},{name:"p",attributes:{style:"vertical-align: middle; text-align: center;"},children:[{name:"a",attributes:{href:e,target:"_blank",text:"Download and run it"}},{name:"span",attributes:{text:". No need to refresh page."}}]}]}]},n=document.getElementsByTagName("body"),i=null!=n&&n.length>0?n.item(0):null;return null==i?(this.reportError("please do not execute com_asprise_scan_addInstallPromptDomElementIfNotExists in <header>!"),!1):(i.appendChild(this.createDomElementFromModel(t)),i.appendChild(this.createDomElementFromModel({name:"div",attributes:{id:"com_asprise_scan_prompt_overlay",class:"com_asprise_scan_prompt_overlay_class overlay-for-prompt",style:"display: none;"}})),!0)},addScanReadyPromptDomElementIfNotExists:function(){if(null!=document.getElementById("com_asprise_scan_app_ok"))return!0;var e=document.getElementsByTagName("body"),t=null!=e&&e.length>0?e.item(0):null;return null==t?(this.reportError("please do not execute addScanReadyPromptDomElementIfNotExists in <header>!"),!1):(t.appendChild(this.createDomElementFromModel({name:"div",attributes:{id:"com_asprise_scan_app_ok",class:"com_asprise_scan_app_ok_class prompt-dialog",style:"display: none;"},children:[{name:"a",attributes:{class:"top-right-closer",title:"Dismiss this dialog",onclick:"scanner.setElementStyleDisplay(document.getElementById('com_asprise_scan_app_ok'), 'none');"}},{name:"h3",attributes:{style:"text-align: center;"},children:[{name:"span",attributes:{style:"font-size: larger; color: #00A000;",text:""}},{name:"span",attributes:{text:""}}]},{name:"p",attributes:{style:"vertical-align: middle; text-align: center;",text:""}}]})),!0)},insertCss:function(e){var t=document.createElement("style");t.type="text/css",t.styleSheet?t.styleSheet.cssText=e:t.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(t)},loadCssFile:function(e){var t=document.createElement("link");t.setAttribute("rel","stylesheet"),t.setAttribute("type","text/css"),t.setAttribute("href",e),document.getElementsByTagName("head")[0].appendChild(t)},loadScannerCssIfNotPresent:function(){for(var e=!1,t=document.getElementsByTagName("link"),n=0;n<t.length;n++){var i=t.item(n);i.href&&i.href.match(/scannerjs\.css(\?\w*)?$/)&&(e=!0)}e||this.loadCssFile(this.getScannerJsBaseUrl()+"scannerjs.css")},createWebSocket:function(e,t,n,i,s){var r;if("WebSocket"in window)r=new WebSocket(e);else{if(!("MozWebSocket"in window))return;r=new MozWebSocket(e)}if("function"==typeof t?r.onopen=function(e){t(r,e)}:(this.logToConsole("No onOpenFunc specified for "+e),r.onopen=function(e){this.logToConsole("WebSocket ["+r.url+"] opens.")}),"function"==typeof n?r.onclose=function(e){n(r,e)}:(this.logToConsole("No onCloseFunc specified for "+e),r.onclose=function(e){this.logToConsole("WebSocket ["+r.url+"] closes: "+e.code+" / "+e.reason+" / clean? "+e.wasClean)}),"function"==typeof i)return r.onmessage=function(e){i(r,e)},"function"==typeof s?r.onerror=function(e){s(r,e)}:(r.onerror=function(e){scanner.logToConsole("WebSocket ["+r.url+"] error occurs:"+JSON.stringify(e))},scanner.scannerJsEventListener&&scanner.scannerJsEventListener("disconnected",!0)),r;this.reportError("You must specify onMesgFunc for "+e)},hasWebSocketSupport:function(){var e="WebSocket"in window&&null!=window.WebSocket||"MozWebSocket"in window;return e||this.reportError("WebSocket is not supported by this browser: "+this.bowser.name+" "+this.bowser.version),e},isModernBrowser:function(){return"function"==typeof atob&&"function"==typeof ArrayBuffer&&"function"==typeof Uint8Array&&"function"==typeof Blob&&"function"==typeof FormData},base64ToUint8Array:function(e){if(!this.isModernBrowser())return this.reportError("base64ToBlob() is not supported in legacy browsers."),null;if(null!=e&&0==e.indexOf("data:")){var t=e.indexOf("base64,");t>0&&(e=e.substr(t+"base64,".length))}e=e.replace(/(\r\n|\n|\r)/gm,"");for(var n=atob(e),i=n.length,s=new ArrayBuffer(i),r=new Uint8Array(s),a=0;a<i;a++)r[a]=n.charCodeAt(a);return r},base64ToBlob:function(e,t,n){if(!this.isModernBrowser())return this.reportError("base64ToBlob() is not supported in legacy browsers."),null;if(null!=e&&0==e.indexOf("data:")){var i=e.indexOf(";");!t&&i>0&&(t=e.substring("data:".length,i));var s=e.indexOf("base64,");s>0&&(e=e.substr(s+"base64,".length))}e=e.replace(/(\r\n|\n|\r)/gm,"");for(var r=atob(e),a=r.length,o=new ArrayBuffer(a),l=new Uint8Array(o),c=0;c<a;c++)l[c]=r.charCodeAt(c);var u=new Blob([l],{type:t});if(n)try{u.lastModifiedDate=new Date,u.name=n}catch(e){}return u},getPageSessionId:function(){return window.sessionStorage&&(sessionStorage.getItem("pageLoadId")?t=sessionStorage.getItem("pageLoadId"):sessionStorage.setItem("pageLoadId",t)),t},hasJava:function(){var e=this.deployJava.getJREs();return!(null==e||""==e||e instanceof Array&&0==e.length)},displayEnableJavaHelp:function(){window.open("https://java.com/en/download/help/enable_browser.xml","java-help")},is7u45:function(){return this.deployJava.versionCheck("1.7.0_45")},is7u45OrAbove:function(){return this.deployJava.versionCheck("1.7.0_45+")},doesCurrentBrowserSupportNPAPI:function(){if(!("version"in this.bowser))return this.reportError("Invalid bowser"),!1;var e=Math.floor(this.bowser.version);return this.bowser.chrome?e<45:this.bowser.firefox?e<53:!this.bowser.msedge&&!!this.bowser.msie},getMimeType:function(e){return null==e?this.MIME_TYPE_BINARY:e.toLowerCase().indexOf("bmp")>=0?"image/bmp":e.toLowerCase().indexOf("png")>=0?"image/png":e.toLowerCase().indexOf("jp")>=0?"image/jpeg":e.toLowerCase().indexOf("tif")>=0?"image/tiff":e.toLowerCase().indexOf("pdf")>=0?"application/pdf":"application/octet-stream"},getFileExtension:function(e){return null==e?"unknown":e.toLowerCase().indexOf("bmp")>=0?"bmp":e.toLowerCase().indexOf("png")>=0?"png":e.toLowerCase().indexOf("jp")>=0?"jpg":e.toLowerCase().indexOf("tif")>=0?"tif":e.toLowerCase().indexOf("pdf")>=0?"pdf":"unknown"},generateFileName:function(e){var t=this.getFileExtension(e);return"asprise-scannerjs-"+ ++this.fileCount+"."+t},removeFragmentFromUrl:function(e){if(null==e)return e;var t=e.indexOf("#");return t>=0?e.substr(0,t):e},createDomElement:function(e,t){var n=document.createElement(e);if(null!=t)for(var i in t)n.setAttribute(i,t[i]);var s=null!=t&&"text"in t?t.text:void 0;return s&&n.appendChild(document.createTextNode(s)),n},createDomElementFromModel:function(e){var t=this.createDomElement(e.name,e.attributes);if(e.children instanceof Array)for(var n=e.children,i=0;i<n.length;i++)n[i]&&t.appendChild(this.createDomElementFromModel(n[i]));return t},createAppletDomElement:function(e,t,n,i,s,r){var a={name:"applet",attributes:{id:e,codebase:t,archive:n,code:i,width:s,height:r,style:"z-index: -999;"},children:[{name:"param",attributes:{name:"permissions",value:"all-permissions"}},{name:"param",attributes:{name:"java_version",value:"1.6+"}},{name:"param",attributes:{name:"java_arguments",value:"-Xmx512m"}},{name:"param",attributes:{name:"separate_jvm",value:"true"}},{name:"param",attributes:{name:"codebase_lookup",value:"false"}},{name:"param",attributes:{name:"mayscript",value:"mayscript"}}]};return this.createDomElementFromModel(a)},setElementStyleDisplay:function(e,t){if(e){var n=e.getAttribute("style");n?e.setAttribute("style",n.replace(/display:\s*[^;]*/,"display: "+t)):e.setAttribute("style","display: "+t+";")}else this.reportError("Not an element: "+e)},logToConsole:function(e,t){if(window.console){var n=(new Date).toLocaleTimeString();t?console.error?console.error(n+" "+e):console.log(n+" ERROR: "+e):console.info?console.info(n+" "+e):console.log(n+" INFO: "+e)}else t&&window.alert&&alert("ERROR: "+e)},reportError:function(e){this.logToConsole(e,!0)},isHttps:function(){return"https:"==document.location.protocol},isWindows:function(){return"navigator"in window&&null!=window.navigator&&"platform"in window.navigator?navigator.platform.indexOf("Win")>-1:void 0},getCookie:function(e){var t=("; "+document.cookie).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},setCookie:function(e,t,n){document.cookie=e+"="+t+(n?"":"; expires=Fri, 31 Dec 2066 23:59:59 GMT")},writeToLocalStorage:function(e,t){if(!window.localStorage)return!1;try{return window.localStorage.setItem(e,t),!0}catch(e){return!1}},readFromLocalStorage:function(e){if(window.localStorage)try{return window.localStorage.getItem(e)}catch(e){return}},deployJava:function(){function t(e){l.debug&&(console.log?console.log(e):alert(e))}function n(e){var t="http://java.com/dt-redirect";return null==e||0==e.length?t:("&"==e.charAt(0)&&(e=e.substring(1,e.length)),t+"?"+e)}function r(e){return function(e,t){for(var n=e.length,i=0;n>i;i++)if(e[i]===t)return!0;return!1}(o,e.toLowerCase())}var a={core:["id","class","title","style"],i18n:["lang","dir"],events:["onclick","ondblclick","onmousedown","onmouseup","onmouseover","onmousemove","onmouseout","onkeypress","onkeydown","onkeyup"],applet:["codebase","code","name","archive","object","width","height","alt","align","hspace","vspace"],object:["classid","codebase","codetype","data","type","archive","declare","standby","height","width","usemap","name","tabindex","align","border","hspace","vspace"]},o=(a.object.concat(a.core,a.i18n,a.events),a.applet.concat(a.core)),l={debug:null,version:"20120801",firefoxJavaVersion:null,myInterval:null,preInstallJREList:null,returnPage:null,brand:null,locale:null,installType:null,EAInstallEnabled:!1,EarlyAccessURL:null,oldMimeType:"application/npruntime-scriptable-plugin;DeploymentToolkit",mimeType:"application/java-deployment-toolkit",launchButtonPNG:function(){var e="//java.com/js/webstart.png";try{return-1!=document.location.protocol.indexOf("http")?e:"http:"+e}catch(t){return"http:"+e}}(),browserName:null,browserName2:null,getJREs:function(){var e=new Array;if(this.isPluginInstalled())for(var n=this.getPlugin().jvms,i=0;i<n.getLength();i++)e[i]=n.get(i).version;else{var s=this.getBrowser();"MSIE"==s?this.testUsingActiveX("1.7.0")?e[0]="1.7.0":this.testUsingActiveX("1.6.0")?e[0]="1.6.0":this.testUsingActiveX("1.5.0")?e[0]="1.5.0":this.testUsingActiveX("1.4.2")?e[0]="1.4.2":this.testForMSVM()&&(e[0]="1.1"):"Netscape Family"==s&&(this.getJPIVersionUsingMimeType(),null!=this.firefoxJavaVersion?e[0]=this.firefoxJavaVersion:this.testUsingMimeTypes("1.7")?e[0]="1.7.0":this.testUsingMimeTypes("1.6")?e[0]="1.6.0":this.testUsingMimeTypes("1.5")?e[0]="1.5.0":this.testUsingMimeTypes("1.4.2")?e[0]="1.4.2":"Safari"==this.browserName2&&(this.testUsingPluginsArray("1.7.0")?e[0]="1.7.0":this.testUsingPluginsArray("1.6")?e[0]="1.6.0":this.testUsingPluginsArray("1.5")?e[0]="1.5.0":this.testUsingPluginsArray("1.4.2")&&(e[0]="1.4.2")))}if(this.debug)for(i=0;i<e.length;++i)t("[getJREs()] We claim to have detected Java SE "+e[i]);return e},installJRE:function(e,t){if(this.isPluginInstalled()&&this.isAutoInstallEnabled(e)){var n;return(n=this.isCallbackSupported()?this.getPlugin().installJRE(e,t):this.getPlugin().installJRE(e))&&(this.refresh(),null!=this.returnPage&&(document.location=this.returnPage)),n}return this.installLatestJRE()},isAutoInstallEnabled:function(e){return!!this.isPluginInstalled()&&(void 0===e&&(e=null),function(e){return"MSIE"!=deployJava.browserName||!!deployJava.compareVersionToPattern(deployJava.getPlugin().version,["10","0","0"],!1,!0)||null!=e&&!function(e,t){if(null==e||0==e.length)return!0;var n=e.charAt(e.length-1);if("+"!=n&&"*"!=n&&-1!=e.indexOf("_")&&"_"!=n&&(e+="*",n="*"),(e=e.substring(0,e.length-1)).length>0){var i=e.charAt(e.length-1);("."==i||"_"==i)&&(e=e.substring(0,e.length-1))}return"*"==n?0==t.indexOf(e):"+"==n&&t>=e}("1.6.0_33+",e)}(e))},isCallbackSupported:function(){return this.isPluginInstalled()&&this.compareVersionToPattern(this.getPlugin().version,["10","2","0"],!1,!0)},installLatestJRE:function(e){if(this.isPluginInstalled()&&this.isAutoInstallEnabled()){var t;return(t=this.isCallbackSupported()?this.getPlugin().installLatestJRE(e):this.getPlugin().installLatestJRE())&&(this.refresh(),null!=this.returnPage&&(document.location=this.returnPage)),t}var i=this.getBrowser(),s=navigator.platform.toLowerCase();return"true"==this.EAInstallEnabled&&-1!=s.indexOf("win")&&null!=this.EarlyAccessURL?(this.preInstallJREList=this.getJREs(),null!=this.returnPage&&(this.myInterval=setInterval("deployJava.poll()",3e3)),location.href=this.EarlyAccessURL,!1):"MSIE"==i?this.IEInstall():"Netscape Family"==i&&-1!=s.indexOf("win32")?this.FFInstall():(location.href=n((null!=this.returnPage?"&returnPage="+this.returnPage:"")+(null!=this.locale?"&locale="+this.locale:"")+(null!=this.brand?"&brand="+this.brand:"")),!1)},runApplet:function(e,n,i){("undefined"==i||null==i)&&(i="1.1");var r=i.match(s);(null==this.returnPage&&(this.returnPage=document.location),null!=r)?"?"!=this.getBrowser()?this.versionCheck(i+"+")?this.writeAppletTag(e,n):this.installJRE(i+"+")&&(this.refresh(),location.href=document.location,this.writeAppletTag(e,n)):this.writeAppletTag(e,n):t("[runApplet()] Invalid minimumVersion argument to runApplet():"+i)},writeAppletTag:function(e,t){var n="<applet ",i="",s=!0;for(var a in(null==t||"object"!=typeof t)&&(t=new Object),e)r(a)?(n+=" "+a+'="'+e[a]+'"',"code"==a&&(s=!1)):t[a]=e[a];var o=!1;for(var l in t)"codebase_lookup"==l&&(o=!0),("object"==l||"java_object"==l||"java_code"==l)&&(s=!1),i+='<param name="'+l+'" value="'+t[l]+'"/>';o||(i+='<param name="codebase_lookup" value="false"/>'),s&&(n+=' code="dummy"'),n+=">",document.write(n+"\n"+i+"\n</applet>")},versionCheck:function(e){var n=0,i=e.match("^(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)(?:[_\\.](\\d+))?)?)?(\\*|\\+)?$");if(null!=i){for(var s=!1,r=!1,a=new Array,o=1;o<i.length;++o)"string"==typeof i[o]&&""!=i[o]&&(a[n]=i[o],n++);"+"==a[a.length-1]?(r=!0,s=!1,a.length--):"*"==a[a.length-1]?(r=!1,s=!0,a.length--):a.length<4&&(r=!1,s=!0);var l=this.getJREs();for(o=0;o<l.length;++o)if(this.compareVersionToPattern(l[o],a,s,r))return!0;return!1}var c="Invalid versionPattern passed to versionCheck: "+e;return t("[versionCheck()] "+c),alert(c),!1},isWebStartInstalled:function(e){if("?"==this.getBrowser())return!0;("undefined"==e||null==e)&&(e="1.4.2");var n=!1;return null!=e.match(s)?n=this.versionCheck(e+"+"):(t("[isWebStartInstaller()] Invalid minimumVersion argument to isWebStartInstalled(): "+e),n=this.versionCheck("1.4.2+")),n},getJPIVersionUsingMimeType:function(){for(var e=0;e<navigator.mimeTypes.length;++e){var t=navigator.mimeTypes[e].type.match(/^application\/x-java-applet;jpi-version=(.*)$/);if(null!=t&&(this.firefoxJavaVersion=t[1],"Opera"!=this.browserName2))break}},launchWebStartApplication:function(e){if(navigator.userAgent.toLowerCase(),this.getJPIVersionUsingMimeType(),0==this.isWebStartInstalled("1.7.0")&&(0==this.installJRE("1.7.0+")||0==this.isWebStartInstalled("1.7.0")))return!1;var t=null;document.documentURI&&(t=document.documentURI),null==t&&(t=document.URL);var n,i=this.getBrowser();if("MSIE"==i?n='<object classid="clsid:8AD9C840-044E-11D1-B3E9-00805F499D93" width="0" height="0"><PARAM name="launchjnlp" value="'+e+'"><PARAM name="docbase" value="'+encodeURIComponent(t)+'"></object>':"Netscape Family"==i&&(n='<embed type="application/x-java-applet;jpi-version='+this.firefoxJavaVersion+'" width="0" height="0" launchjnlp="'+e+'"docbase="'+encodeURIComponent(t)+'" />'),"undefined"==document.body||null==document.body)document.write(n),document.location=t;else{var s=document.createElement("div");s.id="div1",s.style.position="relative",s.style.left="-10000px",s.style.margin="0px auto",s.className="dynamicDiv",s.innerHTML=n,document.body.appendChild(s)}},createWebStartLaunchButtonEx:function(e,t){null==this.returnPage&&(this.returnPage=e);var n="javascript:deployJava.launchWebStartApplication('"+e+"');";document.write('<a href="'+n+'" onMouseOver="window.status=\'\'; return true;"><img src="'+this.launchButtonPNG+'" border="0" /></a>')},createWebStartLaunchButton:function(e,t){null==this.returnPage&&(this.returnPage=e);var n="javascript:if (!deployJava.isWebStartInstalled(&quot;"+t+"&quot;)) {if (deployJava.installLatestJRE()) {if (deployJava.launch(&quot;"+e+"&quot;)) {}}} else {if (deployJava.launch(&quot;"+e+"&quot;)) {}}";document.write('<a href="'+n+'" onMouseOver="window.status=\'\'; return true;"><img src="'+this.launchButtonPNG+'" border="0" /></a>')},launch:function(e){return document.location=e,!0},isPluginInstalled:function(){var e=this.getPlugin();return!(!e||!e.jvms)},isAutoUpdateEnabled:function(){return!!this.isPluginInstalled()&&this.getPlugin().isAutoUpdateEnabled()},setAutoUpdateEnabled:function(){return!!this.isPluginInstalled()&&this.getPlugin().setAutoUpdateEnabled()},setInstallerType:function(e){return this.installType=e,!!this.isPluginInstalled()&&this.getPlugin().setInstallerType(e)},setAdditionalPackages:function(e){return!!this.isPluginInstalled()&&this.getPlugin().setAdditionalPackages(e)},setEarlyAccess:function(e){this.EAInstallEnabled=e},isPlugin2:function(){if(this.isPluginInstalled()&&this.versionCheck("1.6.0_10+"))try{return this.getPlugin().isPlugin2()}catch(e){}return!1},allowPlugin:function(){return this.getBrowser(),"Safari"!=this.browserName2&&"Opera"!=this.browserName2},getPlugin:function(){this.refresh();var e=null;return this.allowPlugin()&&(e=document.getElementById("deployJavaPlugin")),e},compareVersionToPattern:function(e,t,n,i){if(null==e||null==t)return!1;var r=e.match(s);if(null!=r){for(var a=0,o=new Array,l=1;l<r.length;++l)"string"==typeof r[l]&&""!=r[l]&&(o[a]=r[l],a++);var c=Math.min(o.length,t.length);if(i){for(l=0;c>l;++l){var u=parseInt(o[l]),p=parseInt(t[l]);if(p>u)return!1;if(u>p)return!0}return!0}for(l=0;c>l;++l)if(o[l]!=t[l])return!1;return!!n||o.length==t.length}return!1},getBrowser:function(){if(null==this.browserName){var e=navigator.userAgent.toLowerCase();t("[getBrowser()] navigator.userAgent.toLowerCase() -> "+e),-1!=e.indexOf("msie")&&-1==e.indexOf("opera")?(this.browserName="MSIE",this.browserName2="MSIE"):-1!=e.indexOf("trident")||-1!=e.indexOf("Trident")?(this.browserName="MSIE",this.browserName2="MSIE"):-1!=e.indexOf("iphone")?(this.browserName="Netscape Family",this.browserName2="iPhone"):-1!=e.indexOf("firefox")&&-1==e.indexOf("opera")?(this.browserName="Netscape Family",this.browserName2="Firefox"):-1!=e.indexOf("chrome")?(this.browserName="Netscape Family",this.browserName2="Chrome"):-1!=e.indexOf("safari")?(this.browserName="Netscape Family",this.browserName2="Safari"):-1!=e.indexOf("mozilla")&&-1==e.indexOf("opera")?(this.browserName="Netscape Family",this.browserName2="Other"):-1!=e.indexOf("opera")?(this.browserName="Netscape Family",this.browserName2="Opera"):(this.browserName="?",this.browserName2="unknown"),t("[getBrowser()] Detected browser name:"+this.browserName+", "+this.browserName2)}return this.browserName},testUsingActiveX:function(e){var n="JavaWebStart.isInstalled."+e+".0";if("undefined"==typeof ActiveXObject||!ActiveXObject)return t("[testUsingActiveX()] Browser claims to be IE, but no ActiveXObject object?"),!1;try{return null!=new ActiveXObject(n)}catch(e){return!1}},testForMSVM:function(){if("undefined"!=typeof oClientCaps){var e=oClientCaps.getComponentVersion("{08B0E5C0-4FCB-11CF-AAA5-00401C608500}","ComponentID");return""!=e&&"5,0,5000,0"!=e}return!1},testUsingMimeTypes:function(n){if(!navigator.mimeTypes)return t("[testUsingMimeTypes()] Browser claims to be Netscape family, but no mimeTypes[] array?"),!1;for(var i=0;i<navigator.mimeTypes.length;++i){var s=(e=navigator.mimeTypes[i].type).match(/^application\/x-java-applet\x3Bversion=(1\.8|1\.7|1\.6|1\.5|1\.4\.2)$/);if(null!=s&&this.compareVersions(s[1],n))return!0}return!1},testUsingPluginsArray:function(t){if(!navigator.plugins||!navigator.plugins.length)return!1;for(var n=navigator.platform.toLowerCase(),i=0;i<navigator.plugins.length;++i)if(-1!=(e=navigator.plugins[i].description).search(/^Java Switchable Plug-in (Cocoa)/)){if(this.compareVersions("1.5.0",t))return!0}else if(-1!=e.search(/^Java/)&&-1!=n.indexOf("win")&&(this.compareVersions("1.5.0",t)||this.compareVersions("1.6.0",t)))return!0;return!!this.compareVersions("1.5.0",t)},IEInstall:function(){return location.href=n((null!=this.returnPage?"&returnPage="+this.returnPage:"")+(null!=this.locale?"&locale="+this.locale:"")+(null!=this.brand?"&brand="+this.brand:"")),!1},done:function(e,t){},FFInstall:function(){return location.href=n((null!=this.returnPage?"&returnPage="+this.returnPage:"")+(null!=this.locale?"&locale="+this.locale:"")+(null!=this.brand?"&brand="+this.brand:"")+(null!=this.installType?"&type="+this.installType:"")),!1},compareVersions:function(e,t){for(var n=e.split("."),i=t.split("."),s=0;s<n.length;++s)n[s]=Number(n[s]);for(s=0;s<i.length;++s)i[s]=Number(i[s]);return 2==n.length&&(n[2]=0),n[0]>i[0]||!(n[0]<i[0])&&(n[1]>i[1]||!(n[1]<i[1])&&(n[2]>i[2]||!(n[2]<i[2])))},enableAlerts:function(){this.browserName=null,this.debug=!0},poll:function(){this.refresh();var e=this.getJREs();0==this.preInstallJREList.length&&0!=e.length&&(clearInterval(this.myInterval),null!=this.returnPage&&(location.href=this.returnPage)),0!=this.preInstallJREList.length&&0!=e.length&&this.preInstallJREList[0]!=e[0]&&(clearInterval(this.myInterval),null!=this.returnPage&&(location.href=this.returnPage))},writePluginTag:function(){var e=this.getBrowser();if("MSIE"==e){i.push({name:"object",attributes:{id:"deployJavaPlugin",classid:"clsid:CAFEEFAC-DEC7-0000-0001-ABCDEFFEDCBA",width:"0",height:"0"}})}else"Netscape Family"==e&&this.allowPlugin()&&this.writeEmbedTag()},refresh:function(){(navigator.plugins.refresh(!1),"Netscape Family"==this.getBrowser()&&this.allowPlugin())&&(null==document.getElementById("deployJavaPlugin")&&this.writeEmbedTag())},writeEmbedTag:function(){var e=!1;if(null!=navigator.mimeTypes){for(var t=0;t<navigator.mimeTypes.length;t++)if(navigator.mimeTypes[t].type==this.mimeType&&navigator.mimeTypes[t].enabledPlugin){var n={name:"embed",attributes:{id:"deployJavaPlugin",type:this.mimeType,hidden:"true"}};i.push(n),e=!0}if(!e)for(t=0;t<navigator.mimeTypes.length;t++)navigator.mimeTypes[t].type==this.oldMimeType&&navigator.mimeTypes[t].enabledPlugin&&(this.oldMimeType,i.push(oldMimeType))}}};if(l.writePluginTag(),null==l.locale){var c=null;if(null==c)try{c=navigator.userLanguage}catch(e){}if(null==c)try{c=navigator.systemLanguage}catch(e){}if(null==c)try{c=navigator.language}catch(e){}null!=c&&(c.replace("-","_"),l.locale=c)}return l}(),appendDeployJavaElementsToBody:function(){if(0!=i.length){var e=document.getElementsByTagName("body"),t=null!=e&&e.length>0?e.item(0):null;if(null==t)return this.reportError("Unexcepted error - unable to get body"),!1;for(var n=0;n<i.length;n++){var s=i[n];t.appendChild(this.createDomElementFromModel(s))}}},bowser:function(){function e(e){function n(t){var n=e.match(t);return n&&n.length>1&&n[1]||""}var i,s=n(/(ipod|iphone|ipad)/i).toLowerCase(),r=!/like android/i.test(e)&&/android/i.test(e),a=/CrOS/.test(e),o=n(/edge\/(\d+(\.\d+)?)/i),l=n(/version\/(\d+(\.\d+)?)/i),c=/tablet/i.test(e),u=!c&&/[^-]mobi/i.test(e);/opera|opr/i.test(e)?i={name:"Opera",opera:t,version:l||n(/(?:opera|opr)[\s\/](\d+(\.\d+)?)/i)}:/yabrowser/i.test(e)?i={name:"Yandex Browser",yandexbrowser:t,version:l||n(/(?:yabrowser)[\s\/](\d+(\.\d+)?)/i)}:/windows phone/i.test(e)?(i={name:"Windows Phone",windowsphone:t},o?(i.msedge=t,i.version=o):(i.msie=t,i.version=n(/iemobile\/(\d+(\.\d+)?)/i))):/msie|trident/i.test(e)?i={name:"Internet Explorer",msie:t,version:n(/(?:msie |rv:)(\d+(\.\d+)?)/i)}:a?i={name:"Chrome",chromeBook:t,chrome:t,version:n(/(?:chrome|crios|crmo)\/(\d+(\.\d+)?)/i)}:/chrome.+? edge/i.test(e)?i={name:"Microsoft Edge",msedge:t,version:o}:/chrome|crios|crmo/i.test(e)?i={name:"Chrome",chrome:t,version:n(/(?:chrome|crios|crmo)\/(\d+(\.\d+)?)/i)}:s?(i={name:"iphone"==s?"iPhone":"ipad"==s?"iPad":"iPod"},l&&(i.version=l)):/sailfish/i.test(e)?i={name:"Sailfish",sailfish:t,version:n(/sailfish\s?browser\/(\d+(\.\d+)?)/i)}:/seamonkey\//i.test(e)?i={name:"SeaMonkey",seamonkey:t,version:n(/seamonkey\/(\d+(\.\d+)?)/i)}:/firefox|iceweasel/i.test(e)?(i={name:"Firefox",firefox:t,version:n(/(?:firefox|iceweasel)[ \/](\d+(\.\d+)?)/i)},/\((mobile|tablet);[^\)]*rv:[\d\.]+\)/i.test(e)&&(i.firefoxos=t)):/silk/i.test(e)?i={name:"Amazon Silk",silk:t,version:n(/silk\/(\d+(\.\d+)?)/i)}:r?i={name:"Android",version:l}:/phantom/i.test(e)?i={name:"PhantomJS",phantom:t,version:n(/phantomjs\/(\d+(\.\d+)?)/i)}:/blackberry|\bbb\d+/i.test(e)||/rim\stablet/i.test(e)?i={name:"BlackBerry",blackberry:t,version:l||n(/blackberry[\d]+\/(\d+(\.\d+)?)/i)}:/(web|hpw)os/i.test(e)?(i={name:"WebOS",webos:t,version:l||n(/w(?:eb)?osbrowser\/(\d+(\.\d+)?)/i)},/touchpad\//i.test(e)&&(i.touchpad=t)):i=/bada/i.test(e)?{name:"Bada",bada:t,version:n(/dolfin\/(\d+(\.\d+)?)/i)}:/tizen/i.test(e)?{name:"Tizen",tizen:t,version:n(/(?:tizen\s?)?browser\/(\d+(\.\d+)?)/i)||l}:/safari/i.test(e)?{name:"Safari",safari:t,version:l}:{name:n(/^(.*)\/(.*) /),version:function(t){var n=e.match(t);return n&&n.length>1&&n[2]||""}(/^(.*)\/(.*) /)},!i.msedge&&/(apple)?webkit/i.test(e)?(i.name=i.name||"Webkit",i.webkit=t,!i.version&&l&&(i.version=l)):!i.opera&&/gecko\//i.test(e)&&(i.name=i.name||"Gecko",i.gecko=t,i.version=i.version||n(/gecko\/(\d+(\.\d+)?)/i)),i.msedge||!r&&!i.silk?s&&(i[s]=t,i.ios=t):i.android=t;var p="";i.windowsphone?p=n(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i):s?p=(p=n(/os (\d+([_\s]\d+)*) like mac os x/i)).replace(/[_\s]/g,"."):r?p=n(/android[ \/-](\d+(\.\d+)*)/i):i.webos?p=n(/(?:web|hpw)os\/(\d+(\.\d+)*)/i):i.blackberry?p=n(/rim\stablet\sos\s(\d+(\.\d+)*)/i):i.bada?p=n(/bada\/(\d+(\.\d+)*)/i):i.tizen&&(p=n(/tizen[\/\s](\d+(\.\d+)*)/i)),p&&(i.osversion=p);var d=p.split(".")[0];return c||"ipad"==s||r&&(3==d||4==d&&!u)||i.silk?i.tablet=t:(u||"iphone"==s||"ipod"==s||r||i.blackberry||i.webos||i.bada)&&(i.mobile=t),i.msedge||i.msie&&i.version>=10||i.yandexbrowser&&i.version>=15||i.chrome&&i.version>=20||i.firefox&&i.version>=20||i.safari&&i.version>=6||i.opera&&i.version>=10||i.ios&&i.osversion&&i.osversion.split(".")[0]>=6||i.blackberry&&i.version>=10.1?i.a=t:i.msie&&i.version<10||i.chrome&&i.version<20||i.firefox&&i.version<20||i.safari&&i.version<6||i.opera&&i.version<10||i.ios&&i.osversion&&i.osversion.split(".")[0]<6?i.c=t:i.x=t,i}var t=!0,n=e("undefined"!=typeof navigator?navigator.userAgent:"");return n.test=function(e){for(var t=0;t<e.length;++t){var i=e[t];if("string"==typeof i&&i in n)return!0}return!1},n._detect=e,n}()};return window&&(window.addEventListener?window.addEventListener("load",function(){r.eagerInitialization&&r.initialize()}):window.attachEvent&&window.attachEvent("onload",function(){r.eagerInitialization&&r.initialize()})),r});