angular.module('scmApp').service('toast', [ '$rootScope', '$timeout', function($rootScope, $timeout) {
    $rootScope.toasts = [];

    function createToast(message, type, duration) {
        if(duration === undefined || duration === null) {
            duration = 3000;
        }
        var toast = { message: message, type: type };
        $rootScope.toasts.push(toast);

        var toastDuration = angular.isNumber(duration) ? duration : 3000;

        if (toastDuration !== Infinity) {
            $timeout(function() {
                var index = $rootScope.toasts.indexOf(toast);
                if (index !== -1) {
                    $rootScope.toasts[index].fadeOut = true;

                    $timeout(function() {
                        $rootScope.$applyAsync(function() {
                            $rootScope.toasts.splice(index, 1);
                        });
                    }, 500);
                }
            }, toastDuration);
        }
    }

    this.create = function(message, duration) {
        createToast(message, 'create', duration);
    };

    this.success = function(message, duration) {
        createToast(message, 'success', duration);
    };

    this.error = function(message, duration) {
        createToast(message, 'error', duration);
    };

    this.warning = function(message, duration) {
        createToast(message, 'warning', duration);
    };
}]);
