angular.module('scmApp').service('toast', [ '$rootScope', '$timeout', function($rootScope, $timeout) {
    $rootScope.toasts = [];

    function createToast(message, type = 'create', duration = 3000) {
        const toast = { message, type };
        $rootScope.toasts.push(toast);

        const toastDuration = angular.isNumber(duration) ? duration : 3000;

        if (toastDuration !== Infinity) {
            $timeout(() => {
                const index = $rootScope.toasts.indexOf(toast);
                if (index !== -1) {
                    $rootScope.toasts[index].fadeOut = true;

                    $timeout(() => {
                        $rootScope.$applyAsync(() => {
                            $rootScope.toasts.splice(index, 1);
                        });
                    }, 500);
                }
            }, toastDuration);
        }
    }

    this.create = (message, duration) => createToast(message, 'create', duration);
    this.success = (message, duration) => createToast(message, 'success', duration);
    this.error = (message, duration) => createToast(message, 'error', duration);
    this.warning = (message, duration) => createToast(message, 'warning', duration);
}]);
