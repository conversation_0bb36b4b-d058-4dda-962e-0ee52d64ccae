angular.module('scmApp').controller(
    'createClassificationCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
                  previewModalService, Popeye, $timeout, $window) {

            $scope.init = function () {
                $scope.initialize();

            }

            $scope.initialize = function () {
                $scope.listTypes = ["Classification", "Division", "Department"];
                $scope.loginType = $rootScope.loginType == "sumo" ? "GOODS" : "SERVICE";
                console.log($rootScope.loginType);
                $scope.getListMap();
                var map = null;
                $scope.listData = [];
                $scope.budgetCategories = [];
                $scope.getBudgetCategories();
            }

            $scope.getBudgetCategories = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.serviceOrderManagement.budgetCategoryList,
                }).then(function success(response) {
                    if (response.data != null) {
                        $scope.budgetCategories = response.data;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                })
            }

            $scope.getListMap = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.serviceOrderManagement.getListData + "?baseType=" + $scope.loginType,
                }).then(function success(response) {
                    if (response.data != null) {
                        map = response.data;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                })
            }

            $scope.getListDetails = function (selectedList) {
                $scope.listData = map[selectedList];
            }

            $scope.addListData = function () {
                var modalInstance = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'addListDetail.html',
                    controller: 'addListDetailCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {
                        items: function () {
                            return {
                                mode: 'add'
                            }
                        }
                    }
                });
                modalInstance.closed
                    .then(function (isSuccessful) {
                        if (isSuccessful) {
                            $scope.initialize();
                        }
                    });
            };

            $scope.listDetailEditModal = function (listDetail) {
                var modalInstances = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'addListDetail.html',
                    controller: 'addListDetailCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {
                        items: function () {
                            return {
                                name: listDetail.name, code: listDetail.code,
                                alias: listDetail.alias, description: listDetail.description,
                                type: listDetail.type, status: listDetail.status,
                                listDetailId: listDetail.listDetailId,
                                accountable : listDetail.accountable,
                                budgetCategory : listDetail.budgetCategory , mode: 'edit',
                                email: listDetail.email
                            }
                        }
                    }
                });
                modalInstances.closed
                    .then(function (isSuccessful) {
                        if (isSuccessful) {
                            $scope.initialize();
                        }
                    });
            };


        }]
).controller('addListDetailCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'items', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
        function ($rootScope, $scope, apiJson, $http, items, appUtil, $location, $toastService, metaDataService,
                  $fileUploadService, $alertService, previewModalService, Popeye, $timeout, $window) {


            $scope.init = function () {
                $scope.periods = ["ACTIVE", "IN_ACTIVE"];
                $scope.listTypes = ["Classification", "Division", "Department"];
                $scope.editMode = false;
                console.log('type is ', $rootScope.loginType)
                $scope.loginType = $rootScope.loginType == "sumo" ? "GOODS" : "SERVICE";
                $scope.name = items.name;
                $scope.code = items.code;
                $scope.type = items.type;
                // $scope.make = items.make == "NA" ? "" : items.make;
                $scope.alias = items.alias;
                $scope.description = items.description;
                $scope.status = items.status;
                $scope.email = items.email ;
                $scope.accountable = items.accountable;
                $scope.budgetCategory = items.budgetCategory;
                if (items.mode == "add") {
                    $scope.listDetailId = null;
                    editMode = false;
                }
                else {
                    $scope.listDetailId = items.listDetailId;
                    editMode = true;
                }
            }

            $scope.submitListDetail = function () {
                if ($scope.listDetailId == null) {
                    $scope.addListDetail();
                }
                else {
                    $scope.updateListDetail();
                }
            }

            $scope.updateListDetail = function () {
                if ($scope.name == "" || $scope.name == null) {
                    alert("Please input  name.");
                    return;
                }
                if ($scope.code == "" || $scope.code == null) {
                    alert("Please input code.");
                    return;
                }
                if ($scope.description == "" || $scope.description == null) {
                    alert("Please input description.");
                    return;
                }
                if ($scope.alias == "" || $scope.alias == null) {
                    alert("Please input alias.");
                    return;
                }
                if ($scope.status == "" || $scope.status == null) {
                    alert("Please input status.");
                    return;
                }
                if ($scope.email == "" || $scope.email == null) {
                    alert("Please input email.");
                    return;
                }
                // if($scope.accountable){
                //     if (($scope.budgetCategory == "" || $scope.budgetCategory == null)) {
                //         alert("Please input budgetCategory.");
                //         return;
                //     }
                // } else {
                //     $scope.budgetCategory = null;
                // }

                var reqObj = {
                    name: $scope.name,
                    code: $scope.code,
                    description: $scope.description,
                    alias: $scope.alias,
                    status: $scope.status,
                    type: $scope.type,
                    baseType: $scope.loginType,
                    listDetailId: $scope.listDetailId,
                    accountable : $scope.accountable,
                    email:$scope.email ,
                    // budgetCategory : $scope.budgetCategory
                }
                $http({
                    method: 'POST',
                    url: apiJson.urls.serviceOrderManagement.updateListDetail,
                    data: reqObj
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("List Detail Updated Successfully!");
                        closeModal(true);
                    }
                    else {
                        $toastService.create("Error In Saving List Detail!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                })
            }

            $scope.addListDetail = function () {
                if ($scope.type == "" || $scope.type == null) {
                    alert("Please input type.");
                    return;
                }
                if ($scope.name == "" || $scope.name == null) {
                    alert("Please input name.");
                    return;
                }
                if ($scope.code == "" || $scope.code == null) {
                    alert("Please input code.");
                    return;
                }
                if ($scope.description == "" || $scope.description == null) {
                    alert("Please input description.");
                    return;
                }
                if ($scope.alias == "" || $scope.alias == null) {
                    alert("Please input alias.");
                    return;
                }
                if ($scope.status == "" || $scope.status == null) {
                    alert("Please input status.");
                    return;
                }
                if($scope.email == "" || $scope.email == null)
                {
                    alert("Please input email.");
                    return;
                }
                // if($scope.accountable){
                //     if (($scope.budgetCategory == "" || $scope.budgetCategory == null)) {
                //         alert("Please input budget Category.");
                //         return;
                //     }
                // } else {
                //     $scope.budgetCategory = null;
                // }

                var reqObj = {
                    name: $scope.name,
                    code: $scope.code,
                    description: $scope.description,
                    alias: $scope.alias,
                    status: $scope.status,
                    type: $scope.type,
                    baseType: $scope.loginType,
                    accountable : $scope.accountable,
                    email:$scope.email ,
                    // budgetCategory : $scope.budgetCategory
                }
                $http({
                    method: 'POST',
                    url: apiJson.urls.serviceOrderManagement.addListDetail,
                    data: reqObj
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("List Detail Added Successfully!");
                        closeModal(true);
                    }
                    else {
                        $toastService.create("Error In Saving List Detail!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                })
            }

            $scope.cancel = function () {
                closeModal(false);
            };


            $scope.changeIsApplicable = function () {
                if($scope.type == 'Classification'){
                    $scope.accountable = true;
                } else {
                    $scope.accountable = false;
                }
            }

            function closeModal(data) {
                Popeye.closeCurrentModal(data);
            }

        }
    ]
);
