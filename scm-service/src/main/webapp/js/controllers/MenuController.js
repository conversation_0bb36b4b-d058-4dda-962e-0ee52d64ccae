/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 27-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('menuCtrl', ['$rootScope','authService','$state','$scope','apiJson','appUtil','metaDataService',
        '$cookieStore','$http','$location','$window','$timeout',
        function($rootScope,authService,$state,$scope,apiJson,appUtil,metaDataService,$cookieStore,$http,$location,$window,$timeout){
            $scope.activeMenu = $rootScope.activeMenu;
            $scope.activateLoader = function(destination) {
                if($scope.activeMenu!==destination) {
                    $rootScope.showSpinner = true;
                }
                $scope.activeMenu = destination;
                setTimeout(function () {
                    $rootScope.showSpinner = false;
                },8000);

            }
            $scope.hasPermissions = function(){
        	    if($rootScope.aclData!=null){
                    return Object.keys($rootScope.aclData.menu).length > 0;
                }else {
        	        return false;
                }

            };

            $scope.hasAccess = function(key){
                if($rootScope.aclData!=null){
                    return Object.keys($rootScope.aclData.subMenu).length > 0 && $rootScope.aclData.subMenu[key] != undefined;
                }else {
                    return false;
                }
            };

            $scope.hasAccessToMenu = function (key){
                if($rootScope.aclData!=null){
                    return Object.keys($rootScope.aclData.menu).length > 0 && $rootScope.aclData.menu[key] != undefined;
                }else {
                    return false;
                }
            }

            $scope.init = function(){
                $rootScope.activeMenu = appUtil.checkEmpty($rootScope.activeMenu) ? $rootScope.activeMenu : "po";
                $(".button-collapse").sideNav();
                $(".collapsible").collapsible();
                $scope.showToAdmin = true; //appUtil.checkPermission('scm-service.*');
                $scope.isProductManager = true; //appUtil.checkPermission('scm-service.product-management.*');
                $scope.hasReportPrivilege = true; //appUtil.checkPermission("scm-service.report-management.*");
                $scope.hasSKUManagementPrivilege = true; //appUtil.checkPermission("scm-service.sku-price-management.view.*");
                $scope.hasSKUAdminPrivilege = true; //appUtil.checkPermission("scm-service.sku-price-management.approve.*");
                $scope.isVendorManager = true; //appUtil.checkPermission("scm-service.vendor-management.*");
                $scope.isWHOrKitchen  = appUtil.isWarehouseOrKitchen();
                $scope.isKitchen = appUtil.isKitchen();
                $scope.hasApprovePermissions = true; //appUtil.checkPermission("scm-service.purchase-order-management.approve-po");
                $scope.canCreatePO = true; //appUtil.checkPermission("scm-service.purchase-order-management.create-po");
                if(appUtil.getLoginType() == null || appUtil.getLoginType() == undefined){
                    $state.go('login');
                    return;
                } else if(appUtil.getLoginType() == "sumo" && $rootScope.loginType == undefined){
                    $state.go('login');
                    return;
                } else if ($rootScope.loginType == undefined){
                    $rootScope.loginType = appUtil.getLoginType();
                }
                $scope.setUnitName();
                $scope.userId = appUtil.getCurrentUser().userId;
                $scope.userName = appUtil.getCurrentUser().user.name;
                $scope.showVendorOrdering = appUtil.isWarehouseOrKitchen();
                console.log("menu reached");
                fetchOutletsForEmployee();
                if($rootScope.loginType=="sumo"){
                    $scope.showUnitLogin = true;
                    if ($rootScope.previousState.name == "metadata") {
                        if($scope.hasPermissions()){
                            if (appUtil.isWarehouseOrKitchen() && $scope.hasAccess('TRNPP')) {
                                $state.go('menu.prodPlanning');
                                metaDataService.getInitiatedDayCloseEvent(appUtil.getUnitData().id);
                            } else if(!appUtil.isWarehouseOrKitchen() && $scope.hasAccess('TRNAO')) {
                                $state.go('menu.acknowledgeRO');
                                $rootScope.restrictAll = false;
                                metaDataService.getRegularOrderingEvents(appUtil.getUnitData().id);
                            } else if($scope.hasAccessToMenu('SSG')){
                                $state.go('menu.b2bOutWardRegister')
                            }else{
                                $state.go('menu.supportLink');
                            }
                        }else{
                            $state.go('menu.supportLink');
                        }
                    }
                }else{
                    if($scope.hasPermissions() && $scope.hasAccess("APRSO")){
                                            $scope.showUnitLogin = false;
                                            $state.go("menu.approveSO",{createdSO:null, viewSO:false});
                     }else if($scope.hasPermissions() && $scope.hasAccess("VWSO")){
                        $scope.showUnitLogin = false;
                        $state.go("menu.viewSO",{createdSO:null, viewSO:true});
                    }else{
                        $state.go('menu.supportLink');
                    }
                }
            };

            $scope.goToNewRegularOrdering = function (events) {
                $state.go("menu.refOrderCreateV1", {orderingEvents : events});
            }

            function fetchOutletsForEmployee() {
                $scope.userUnitArray = [];
                $scope.responseArray = {};
                    $http({
                        method: 'POST',
                        url: apiJson.urls.unitManagement.userUnits,
                        data: {
                            employeeId: $scope.userId,
                            onlyActive: true
                        }
                    }).then(function success(response) {
                        $scope.responseArray = response.data;
                        $scope.responseArray.forEach(function (unit) {
                            if ((unit.category == "CAFE" || unit.category == "KITCHEN" || unit.category == "WAREHOUSE") && unit.status == "ACTIVE") {
                                $scope.userUnitArray.push(unit);
                            }
                        });

                    }, function error(response) {
                        console.log("Encountered an error",response);
                    });
            }

            $scope.showNavMenu = function() {
                if(document.getElementsByClassName("mobileViewMenu")[0].classList.contains("collapsedMenu")){
                    document.getElementsByClassName("mobileViewMenu")[0].classList.remove("collapsedMenu");
                }
                else{
                    document.getElementsByClassName("mobileViewMenu")[0].classList.add("collapsedMenu");
                }

            }

            $scope.setUnitName = function(){
                var data = appUtil.getUnitData();
                if(!appUtil.isEmptyObject(data) && $rootScope.loginType == "sumo"){
                    $scope.unitName = data.name + "("+data.company.shortCode+")";
                    $scope.outlet = data;
                }
            };

            $scope.relogin = function(outlet){
                $scope.outlet = outlet;
                var userObj = {
                    unitId : $scope.outlet.id,
                    applicationName: "SERVICE_ORDER"
                }

                if($rootScope.loginType=="sumo"){
                    userObj.applicationName="SCM_SERVICE";
                }
                $rootScope.switchUnitLogin(userObj);
            }
            // call this function whenever there is a need to refresh metadata definitions

            $rootScope.logout = function(reload){
                $rootScope.resetLocalStorage();
                window.location = window.location.href+'?eraseCache=true';
                authService.setAuthorization(null);
                window.location = window.location.href.split("#")[0] +"#/login";
                if(reload){
                	$window.location.reload();
                }
            };


        }
    ]
);
