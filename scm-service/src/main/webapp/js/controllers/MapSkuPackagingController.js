/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
'use strict';

angular.module('scmApp')
    .controller('mapSkuPackagingCtrl', ['$http', '$rootScope', '$scope', 'authService', '$location', '$state',
        'appUtil', '$stateParams', 'metaDataService', '$toastService', 'apiJson', 'packagingService', 'productService',
        function ($http, $rootScope, $scope, authService, $location, $state, appUtil, $stateParams, metaDataService,
                  $toastService, apiJson, packagingService, productService) {

            $scope.selectOptions = {width: '100%'};
            $scope.products = [];
            $scope.definitions = {
                selected: null,
                lists: {"CASE": [], "INNER": [], "LOOSE": []}
            };
            $scope.skuMappings = [];
            $scope.selectedProfiles = {};
            $scope.mappedProfiles = [];
            $scope.checkEmpty = appUtil.checkEmpty;

            function getAllProducts() {
                $scope.products = appUtil.getScmProductDetails();
                $scope.skuProductMap = appUtil.getSkuProductMap();
            }

            function getAllPackagingProfiles() {
                packagingService.getAllProfiles(function (profiles) {
                    $scope.definitions.lists = profiles;
                });
            }

            function getAllMappings(callback) {
                $http({
                    method: "GET",
                    url: apiJson.urls.productManagement.skuPackagingMappings
                }).then(function (response) {
                    $scope.skuMappings = response.data;
                    if(!$scope.checkEmpty($scope.productId)){
                        $scope.selectProduct($scope.productId);
                        $scope.selectSKU($scope.skuId);
                        if(callback!=undefined){
                            callback();
                        }
                    }
                }, function (response) {
                    console.log("Error while getting mappings", response);
                });

                metaDataService.getAllPackagingMappings(function (mappings) {
                    $scope.packagingMap = appUtil.getPackagingMap();
                    $scope.productPackagings = mappings;
                });
            }

            function prepareSKUMappings(selectedProfiles) {
                var mappingObject = [];
                angular.forEach(selectedProfiles, function (profile) {
                    mappingObject.push({
                        packagingId: profile.packagingId,
                        skuId: $scope.skuId,
                        mappingStatus: "ACTIVE"
                    });
                });
                return mappingObject;
            }

            $scope.init = function() {
                getAllProducts();
                getAllPackagingProfiles();
                getAllMappings();

            };

            $scope.markDefault =  function(mappingId){
                $http({
                    method:"PUT",
                    url:apiJson.urls.productManagement.defaultSkuPackaging,
                    data: mappingId
                }).then(function (response) {
                    var message = !response.data ? "Update failed. Try again later." : "Update Successful.";

                    $toastService.create(message, function () {
                        getAllMappings(function(){
                            appUtil.updateSkuMappingsInCache($scope.productId,$scope.skuId,$scope.mappedProfiles);
                        });
                    });
                }, function (response) {
                    console.log("Error in posting data", response);
                });
            };

            $scope.updateStatus = function(id,activate) {
                $http({
                    method:"PUT",
                    url: activate ? apiJson.urls.productManagement.activateSkuPackaging : apiJson.urls.productManagement.deactivateSkuPackaging,
                    data: id
                }).then(function (response) {
                    var message = !response.data ? "Update failed. Try again later." : "Update Successful.";
                    $toastService.create(message, function () {
                        getAllMappings(function(){
                            appUtil.updateSkuMappingsInCache($scope.productId,$scope.skuId,$scope.mappedProfiles);
                        });
                    });
                }, function (response) {
                    console.log("Error in posting data", response);
                });
            };

            $scope.selectProduct = function (productId) {
                $scope.productId = productId;
                $scope.skuList = $scope.skuProductMap[productId];
            };

            $scope.selectSKU =  function(skuId){
                $scope.mappedProfiles = [];
                var mappings = $scope.skuMappings[skuId];
                if (!appUtil.checkEmpty(mappings)) {
                    for (var index in mappings) {
                        $scope.mappedProfiles.push(mappings[index]);
                    }
                    console.log("Mapped Profiles after massaging is ", $scope.mappedProfiles);
                }
            };

            $scope.searchPackaging = function(item){
                if($scope.productId!=undefined){
                    return $scope.productPackagings[$scope.productId].filter(function(mapping){
                            return item.packagingId == mapping.packagingId;
                        }).length>0;
                }else{
                    return true;
                }
            };


            $scope.getProfileName = function(id){
                var profile = packagingService.getProfile(id);
                return profile.packagingName;
            };


            function checkInMappedProfiles(item, mappedProfiles) {
                if(item!=undefined){
                    return mappedProfiles.filter(function(profile){
                        return profile.packagingId == item.packagingId;
                    }).length>0;
                }else{
                    return false;
                }
            }

            $scope.onDrop = function (list, item) {
                if(!checkInMappedProfiles(item,$scope.mappedProfiles)){
                    list[item.packagingId] = item;
                }

                packagingService.addSubPackagings(list,item,$scope.onDrop);
            };

            $scope.checkIfExists = function (list, item, index) {
                var flag = true;
                for (var pos in list) {
                    if (list[pos].packagingId == item.packagingId) {
                        flag = false;
                    }
                }
                return flag;
            };

            $scope.removeFromSelected = function(profiles,id){
               delete profiles[id];
            };

            $scope.submit = function () {
                console.log("selected profiles ::::",$scope.selectedProfiles);
                if(!$scope.checkEmpty($scope.productId) && !$scope.checkEmpty($scope.selectedProfiles)){
                    $http({
                        method:"POST",
                        url: apiJson.urls.productManagement.skuPackagingMapping,
                        data: prepareSKUMappings($scope.selectedProfiles)
                    }).then(function (response) {
                        var message = !response.data ? "Update failed. Try again later." : "Update Successful.";
                        $toastService.create(message, function () {
                            $scope.selectedProfiles = {};
                            getAllMappings(function(){
                                appUtil.updateSkuMappingsInCache($scope.productId,$scope.skuId,$scope.mappedProfiles);
                            });
                        });
                    }, function (response) {
                        console.log("Error in posting data", response);
                    });
                }else{
                    if($scope.checkEmpty($scope.productId)){
                        $toastService.create("Please select a product first!");
                    }else if($scope.checkEmpty($scope.selectedProfiles)){
                        $toastService.create("Please select a profile first!");
                    }
                }
            };
        }
    ]
);