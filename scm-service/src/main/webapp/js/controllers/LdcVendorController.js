

angular.module('scmApp')
    .controller('LdcVendorController', ['$rootScope','$toastService' ,'PrintService','authService','$state','$scope','$stateParams','$location','apiJson','$cookieStore','appUtil','$http','metaDataService','$window','$timeout','Popeye',
        function ($rootScope,$toastService,PrintService,authService,$state,$scope,$stateParams,$location, apiJson, $cookieStore, appUtil, $http, metaDataService,$window,$timeout,Popeye){

            $scope.init = function(){

                    getVendorList();
                    $scope.vendorLdcData = {};
                    $scope.vendorSelected = null;
                    $scope.isAddLdc = false;
                    $scope.selectedCertificateNo = null;
                    $scope.selectedTdsSection = null;
                    $scope.selectedTdsRate = null;
                    $scope.selectedToDate = null;
                    $scope.selectedFromDate = null;
                    $scope.selectedLdcAmount = null;
                    $scope.vendorLdcDataSize = 0;
                    $scope.isUpdateLdc = {};
                    $scope.tdsSections = [
                    "TDS_RENT_ON_PLANT_&_MACHINERY",
                    "TDS_RENT_ON_LAND_&_BUILDING",
                    "TDS_INTEREST",
                    "TDS_CONTRACTOR_NON_CO",
                    "TDS_CONTRACTOR_CO",
                     "TDS_COMMISSION_OR_BROKERAGE",
                     "TDS_PROFESSIONAL_&_TECHNICAL_FEES" ]
            }

            $scope.toggleAddLdc = function(){
                    $scope.isAddLdc = !$scope.isAddLdc;
            }
            
            $scope.setFromDate = function(selectedFromDate){
                $scope.selectedFromDate = selectedFromDate;
            }
            
            $scope.setToDate = function(selectedToDate){
                $scope.selectedToDate = selectedToDate;
            }

            $scope.setTdsRate = function(selectedTdsRate){
                $scope.selectedTdsRate = selectedTdsRate;
            }

            $scope.setTdsSection = function(selectedTdsSection){
                $scope.selectedTdsSection = selectedTdsSection;
            }

            $scope.setCertificationNo = function(selectedCertificateNo){
                $scope.selectedCertificateNo = selectedCertificateNo;
            }
            $scope.setLdcAmount = function(selectedLdcAmount){
                $scope.selectedLdcAmount = selectedLdcAmount;
            }

            function getVendorList(){
                    var url = apiJson.urls.vendorManagement.allVendorName
                    $http({
                        url: url,
                        method: 'GET'
                    }).then(function (response) {
                        console.log(response.data);
                        $scope.vendors = response.data;
                        $scope.activateVendors = [];
                        for (var i = 0; i < response.data.length; i++) {
                            if(response.data[i].status === "ACTIVE") {
                                $scope.activateVendors.push(response.data[i]);
                            }
                        }
                    }, function (response) {
                        console.log("got error", response);
                    });
            };

           $scope.getLdcDataForVendor = function (selectedVendor) {
                if (selectedVendor != null) {
                    $scope.vendorSelected = selectedVendor;
                    $http({
                        method: "GET",
                        url: apiJson.urls.ldcVendorManagement.getLdcForVendor,
                        params: {
                            vendorId: selectedVendor.id
                        }
                    }).then(function success(response) {
                        $scope.vendorLdcData = response.data; 
                        $scope.vendorLdcDataSize = Object.keys($scope.vendorLdcData).length;
                        Object.keys($scope.vendorLdcData).forEach(function(key,idx){
                            $scope.vendorLdcData[key].ldcTenureFrom = new Date($scope.vendorLdcData[key].ldcTenureFrom);
                               $scope.vendorLdcData[key].ldcTenureTo = new Date($scope.vendorLdcData[key].ldcTenureTo); 
                        });
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                } else {
                    $scope.vendorLdcData = {};
                }
            };


            $scope.submitLdcData = function(){          
                if($scope.selectedFromDate===null){
                    $toastService.create("Please select From date.");
                    return;
                }
                if($scope.selectedToDate === null){
                    $toastService.create("Please select To date.");
                    return;
                }
                if($scope.selectedTdsRate === null ||  $scope.selectedTdsRate <=0 ){
                    $toastService.create("Please enter valid Tds Rate.");
                    return;
                }
                if($scope.selectedTdsSection === null){
                    $toastService.create("Please enter Tds Section.");
                    return;
                }
                if($scope.selectedCertificateNo === null ||$scope.selectedCertificateNo <= 0 ){
                    $toastService.create("Please enter valid Certificate No.");
                    return;
                }
                if($scope.selectedLdcAmount === null || $scope.selectedLdcAmount <= 0 ){
                    $toastService.create("Please enter valid LDC amount.");
                    return;
                }
                console.log($scope.selectedFromDate," ",$scope.selectedToDate," ",$scope.selectedTdsRate
                    ,' ',$scope.selectedTdsSection,' ',$scope.selectedCertificateNo,' ',$scope.selectedLdcAmount);

                    $http({
                        method: "POST",
                        url: apiJson.urls.ldcVendorManagement.addLdcData,
                        data: {
                            ldcLimit : $scope.selectedLdcAmount,
                            ldcTenureFrom : $scope.selectedFromDate,
                            ldcTenureTo : $scope.selectedToDate,
                            ldcTdsRate : $scope.selectedTdsRate,
                            ldcTdsSection : $scope.selectedTdsSection,
                            ldcCertificateNo : $scope.selectedCertificateNo,
                            vendorId : $scope.vendorSelected.id
                        }
                    }).then(function success(response) {
                        if(response.status===200 && response.data !=null){
                            console.log(response);
                            $toastService.create("Ldc Added for "+$scope.vendorSelected.name+" vendor.");
                            $scope.vendorLdcData[response.data.ldcId] = response.data;
                            $scope.isAddLdc = false;
                              $scope.selectedCertificateNo = null;
                              $scope.selectedTdsSection = null;
                              $scope.selectedTdsRate = null;
                              $scope.selectedToDate = null;
                               $scope.selectedFromDate = null;
                              $scope.selectedLdcAmount = null;
                        }
                        
                    }, function error(response) {
                       if(response.data !== null) alert("Error : "+response.data.errorMsg);
                        console.log("error:" + response);
                    });
            
                }

            
                $scope.openldcUpdateModal = function (key,value) {
                    var ldcUpdateModal = Popeye.openModal({
                        templateUrl: "ldcUpdateModal.html",
                        controller: "ldcUpdateCtrl",
                        resolve: {
                            ldcData: function () {
                                return value;
                            },
                            ldcId: function () {
                                return key;
                            },
                            vendorName : function(){
                                return $scope.vendorSelected.name;
                            },
                            vendorLdcData : function(){
                                return  $scope.vendorLdcData;
                            }
                        },
                        modalClass: 'custom-modal',
                        click: false,
                        keyboard: false
                    });
             
                }

             $scope.deleteLdcData = function(key){
                $http({
                    method: "POST",
                    url: apiJson.urls.ldcVendorManagement.deactivateLdcData,
                    params:{
                        ldcId : key
                    }
                }).then(function success(response) {
                    if(response.status===200 && response.data !=null){
                        console.log(response);
                        delete $scope.vendorLdcData[key];
                        $toastService.create("LDC Data Deleted Successfully !");
                    }
                    
                }, function error(response) {
                    if(response.data !== null) alert("Error : "+response.data.errorMsg);
                    console.log("error:" + response);
                });
             }   

        }]).controller('ldcUpdateCtrl', ['$scope', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window','$alertService','ldcData','ldcId','vendorName','vendorLdcData',
        function ($scope, appUtil, $toastService, apiJson, $http, Popeye, $window,$alertService,ldcData,ldcId,vendorName, vendorLdcData) {
        
            $scope.init = function () {
                $scope.value = {
                    ldcId : ldcData.ldcId,
                    ldcCertificateNo : ldcData.ldcCertificateNo,
                    ldcLimit : ldcData.ldcLimit,
                    ldcTdsRate : ldcData.ldcTdsRate,
                    ldcTdsSection : ldcData.ldcTdsSection,
                    ldcTenureFrom : ldcData.ldcTenureFrom,
                    ldcTenureTo : ldcData.ldcTenureTo,
                    status : ldcData.status,
                    vendorId : ldcData.vendorId
                };
                $scope.key = ldcId;
                $scope.vendorName = vendorName;
                $scope.vendorLdcData = vendorLdcData;
                $scope.tdsSections = [
                    "TDS_RENT_ON_PLANT_&_MACHINERY",
                    "TDS_RENT_ON_LAND_&_BUILDING",
                    "TDS_INTEREST",
                    "TDS_CONTRACTOR_NON_CO",
                    "TDS_CONTRACTOR_CO",
                     "TDS_COMMISSION_OR_BROKERAGE",
                     "TDS_PROFESSIONAL_&_TECHNICAL_FEES"]

            }

            $scope.updateFromDate = function(value,val){
                value.ldcTenureFrom = val;
            }
            
            $scope.updateFromDateToDate = function(value,val){
                value.ldcTenureTo = val;
            }

            $scope.updateLdcLimit = function(value,val){
                value.ldcLimit = val;
            }

            $scope.updateTdsSection = function(value,val){
                value.ldcTdsSection = val;
            }

            $scope.updateCertificationNo = function(value,val){
                value.ldcCertificateNo = val;
            }
            $scope.updateTdsRate = function(value,val){
                value.ldcTdsRate = val;
            }


            $scope.closeModal = function () {
                Popeye.closeCurrentModal();
            };

            // $scope.updateLdcData = function(value){
            //     if(value.ldcTenureFrom===null){
            //         $toastService.create("Please select From date.");
            //         return;
            //     }
            //     if(value.ldcTenureTo === null){
            //         $toastService.create("Please select To date.");
            //         return;
            //     }
            //     if(value.ldcTdsRate === null){
            //         $toastService.create("Please enter Tds Rate.");
            //         return;
            //     }
            //     if(value.ldcTdsSection === null){
            //         $toastService.create("Please enter Tds Section.");
            //         return;
            //     }
            //     if(value.ldcCertificateNo === null){
            //         $toastService.create("Please enter Certificate No.");
            //         return;
            //     }
            //     if(value.ldcLimit === null){
            //         $toastService.create("Please enter LDC amount.");
            //         return;
            //     }

            //         $http({
            //             method: "POST",
            //             url: apiJson.urls.ldcVendorManagement.updateLdcData,
            //             data: value
            //         }).then(function success(response) {
            //             if(response.status===200 && response.data !=null){
            //                 console.log(response);
            //                 $scope.vendorLdcData[ldcId] = response.data;
            //                 $toastService.create("LDC Data Updated Successfully !");
            //                 Popeye.closeCurrentModal();
            //             }
                        
            //         }, function error(response) {
            //             if(response.data !== null) alert("Error : "+response.data.errorMsg);
            //             console.log("error:" + response);
            //         });

            // }
        }]);