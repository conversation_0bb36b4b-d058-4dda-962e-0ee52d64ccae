'use strict';

angular.module('scmApp').controller('uploadUnitsProjectionsCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService',
    '$alertService','metaDataService','Popeye','$fileUploadService',
    function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $alertService, metaDataService, Popeye, $fileUploadService) {

        $scope.uploadSheet = function () {
            $fileUploadService.openFileModal("Upload Units Input Sheet", "Find", function (file) {
                $scope.readUploadFile(file);
            });
        };

        $scope.readUploadFile = function (file) {
            $scope.listOfUnits = [];
            var fd = new FormData(document.forms[0]);
            fd.append("file", file);
            $http({
                url: apiJson.urls.productProjectionsManagement.readUploadedFile,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                console.log("response is ",response);
                if (response.length != 0){
                    console.log("got data ",response);
                    $scope.listOfUnits = response;
                    $scope.showUploadedData();
                }
                else{
                    $toastService.create("No units present in the uploaded sheet..!");
                }
            }).error(function (response) {
                if (response.errorMsg != null) {
                    $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                } else {
                $toastService.create("Error in uploaded sheet.");
                }
            });
        };

        $scope.showUploadedData = function(){
            var modalInstance = Popeye.openModal({
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'scmInputView.html',
                controller: 'scmInputViewCtrl',
                backdrop: 'static',
                keyboard: false,
                scope:$scope,
                size: 'lg',
                resolve: {
                    listOfUnits : function () {
                        return $scope.listOfUnits;
                    }
                }
            });
            modalInstance.closed
                .then(function (isSuccessful) {
                    if (isSuccessful) {
                        $scope.generateProjectionsForUnits();
                    }
                });
        };

        $scope.generateProjectionsForUnits = function () {
            var currentUser = appUtil.getCurrentUser();
            $http({
                url: apiJson.urls.productProjectionsManagement.generateProjectionsForUnits,
                method: 'POST',
                data: {
                    unitsData: $scope.listOfUnits,
                    generatedBy: currentUser.user.name+"["+currentUser.user.id+"]"
                }
            }).then(function success(response) {
                $toastService.create("Projections Generated Successfully.Please Check the mail.");
            }, function error(response) {
                console.log("error:" + response);
                $toastService.create("(Error) Could not generate Projections... Please try again later");
            });
        };
        
    }]).controller('scmInputViewCtrl', ['$scope', 'appUtil', 'Popeye', 'listOfUnits',
        function ($scope, appUtil, Popeye, listOfUnits) {

            $scope.init = function (){
                $scope.listOfUnits = listOfUnits;
            };

            $scope.submit = function(){
                Popeye.closeCurrentModal(true);
            };

            $scope.cancel = function(){
                Popeye.closeCurrentModal(false);
            };

    }]);