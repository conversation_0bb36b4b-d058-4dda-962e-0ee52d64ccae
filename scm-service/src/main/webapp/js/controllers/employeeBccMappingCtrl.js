angular.module('scmApp').controller(
		'employeeBccMappingCtrl',
		[
				'$rootScope',
				'$scope',
				'apiJson',
				'$http',
				'appUtil',
				'$toastService',
				'metaDataService',
				function($rootScope, $scope, apiJson, $http, appUtil,
						$toastService, metaDataService) {
					
					$scope.init = function () {

						$scope.selectedBCC = [];
						$scope.selectedCC = [];
						$scope.getCostCenters();
						$scope.getEmployees();
					}

					$scope.multiSelectSetting = {
                        showEnableSearchButton: true, template: '<b> {{option.name}}</b>', scrollable: true,
                        scrollableHeight: '500px'
                    };

                    $scope.deactivateMapping = function(costCenter){
                        var list = [];
                        list.push(costCenter);
                        var payload = {
                           costCenter: $scope.employee,
                           businessCostCenter : list
                        };
                        $http({
                           url: apiJson.urls.serviceOrderManagement.deactivateEmployeeBusinessCostCenterMap,
                           method: 'POST',
                           data: payload
                        }).then(function (response) {
                           if(response.errorMessage != null){
                               $toastService.create("Mapping Deactivation Failed!");
                           } else {
                               $toastService.create("Mapping Deactivated!");
                               $scope.clear();
                               $scope.getCostCenters();
                               $scope.searchEmployeeMapping($scope.employee);
                           }
                        }, function error(response) {
                               console.log("got error", response);
                        });
                    }
					
					$scope.getCostCenters = function () {
                        $http({
                            url: apiJson.urls.serviceReceivedManagement.bcc,
                            method: "GET",
                        }).then(function (response) {
                            $scope.allCostCenters = response.data;
                            console.log($scope.allCostElements);
                        }, function (response) {
                            console.log(response);
                        });
					};
	                
					$scope.searchEmployeeMapping = function (employee) {
                        $http({
                            url: apiJson.urls.serviceOrderManagement.businessCostCenter,
                            method: "GET",
                            params : {empId: employee.id}
                        }).then(function (response) {
                            $scope.employeeMappings = response.data;
                        }, function (response) {
                            console.log(response);
                        });
					};
					
					$scope.getEmployees = function () {
	                    $http({
	                        method: "GET",
	                        url: apiJson.urls.serviceOrderManagement.costCentersAll
	                    }).then(function success(response) {
	                        $scope.costCentersAll = response.data;
	                    }, function error(response) {
	                        console.log("error:" + response);
	                    });
	                };
	                
	                $scope.createEmployeeCostCenterMap = function() {
	                     for(var i=0;i<$scope.selectedCC.length;i++){
	                        var payload = {
                            		 costCenter: $scope.selectedCC[i],
                             		 businessCostCenter : $scope.selectedBCC
                             };
                             $http({
                                 url: apiJson.urls.serviceOrderManagement.createBusinessCostCenterMap,
                                 method: 'POST',
                                 data: payload
                             }).then(function (response) {
                            	 if(response.errorMessage != null || response.status === 500) {
                            		 $toastService.create("Mapping Failed! for User "+ $scope.selectedCC[0]);
                            	 } else {
                                     $toastService.create("Mapping Added!");
                                     $scope.getBccCCMapping($scope.employee)
                                     $("#addNewMappingModal").closeModal();
                                     $("#addNewMappingModal").modal('hide');
                            	 }
                             }, function error(response) {
                            	 $toastService.create("Mapping Failed! for User "+ $scope.selectedCC[0]);
                                 console.log("got error", response);
                             });
	                     }
	                     $scope.init();
	                };
	                
	                $scope.clear = function() {
	                	 $scope.costCenter = {};
	                	 $scope.allCostElements = [];
	                };
					$scope.getBccCCMapping = function (ccId) {
	                    $http({
	                        method: "GET",
	                        url: apiJson.urls.serviceOrderManagement.getBccCCMapping,
                            params : {costCenterId : ccId.id }
	                    }).then(function success(response) {
	                        $scope.bccCCMapping = response.data;
	                    }, function error(response) {
	                        console.log("error:" + response);
	                    });
	                };
				} ]);