'use strict';
angular.module('scmApp').controller('viewCreditNoteCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http', '$state',
    'appUtil', '$toastService', '$alertService', 'metaDataService', '$fileUploadService', '$window', '$timeout', 'Popeye',
    function ($rootScope, $stateParams, $scope, apiJson, $http, $state, appUtil,
              $toastService, $alertService, metaDataService, $fileUploadService, $window, $timeout, Popeye) {

        $scope.init = function () {
            var currentDate = appUtil.getCurrentBusinessDate();
            $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
            $scope.endDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
            $scope.currentUser = appUtil.getCurrentUser();
            $scope.creditDebitNoteDetails = null;
            $scope.statusList = ["PENDING_APPROVAL_L1","PENDING_APPROVAL_L2", "CLOSED", "REJECTED"];
            metaDataService.getVendorsForUnit($scope.currentUser.unitId, function (vendorsForUnit) {
                $scope.vendors = vendorsForUnit;
            });
            $scope.vendorSelected = null;
            $scope.statusSelected = "PENDING_APPROVAL_L1";
        }

        $scope.selectVendor = function (vendor) {
            $scope.vendorSelected = vendor;

        };

        $scope.selectStatus = function (status) {
            $scope.statusSelected = status;

        };


        $scope.getCreditDebitNoteDetails = function () {
           if($scope.vendorSelected == null){
               $toastService.create("Please select vendor");
               return;
           }
            var reqParams = {
                startDate: $scope.startDate,
                endDate: $scope.endDate,
                vendorId : $scope.vendorSelected.vendorId,
                status: $scope.statusSelected
            }

            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.getCreditDebitNoteDetails,
                params: reqParams,
            }).then(function (response) {
                $scope.creditDebitNoteDetails = response.data;
            }, function (err) {
                console.log("Encountered error while fetching credit debit note details");
            });

        };

        $scope.getDetails = function (id) {
            var reqParams = {
                id: id
            }
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.getCreditNoteDetail,
                params: reqParams,
            }).then(function (response) {
                $scope.openCreditDetailsModal(response.data);
            }, function (err) {
                console.log("Encountered error while fetching credit debit note details");
            });

        };


        $scope.openCreditDetailsModal = function (creditNoteDetail) {
            var creditNoteDetailsModal = Popeye.openModal({
                templateUrl: "creditNoteDetailModal.html",
                controller: "creditNoteDetailModalCtrl",
                resolve: {
                    creditNoteDetail: function () {
                        return creditNoteDetail;
                    },
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });
            creditNoteDetailsModal.closed.then(function () {
            });
        }

        $scope.approveCreditNote = function (detail) {
            var reqParams = {
                userId : appUtil.getCurrentUser().userId
            }
            $http({
                method: "POST",
                url: apiJson.urls.invoiceManagement.approveCreditNote,
                data : detail,
                params: reqParams,
            }).then(function (response) {
                if(response.data){
                    $scope.getCreditDebitNoteDetails();
                    $toastService.create("Credit Note Approved");
                }else {
                    $toastService.create("Credit Note Not Approved");
                }
            }, function (err) {
                $toastService.create("Encountered error while approving credit note");
            });
        }

        $scope.downloadNote = function (detail) {
            $window.open(detail.creditNoteDocUrl, '_blank');
        }

        $scope.viewInvoice = function (detail) {
            $window.open(detail.invoiceDocUrl, '_blank');
        }

        $scope.rejectCreditNote = function (detail) {
            var reqParams = {
                userId : appUtil.getCurrentUser().userId
            }
            $http({
                method: "POST",
                url: apiJson.urls.invoiceManagement.rejectCreditNote,
                data : detail,
                params: reqParams,
            }).then(function (response) {
                if(response.data){
                    $scope.getCreditDebitNoteDetails();
                    $toastService.create("Credit Note Rejected");
                }else {
                    $toastService.create("Credit Note Not Rejected");
                }
            }, function (err) {
                $toastService.create("Encountered error while rejected credit note");
            });
        }

        $scope.openUrl = function (url) {
            if (!appUtil.isEmptyObject(url)) {
                $window.open(url, '_blank');
            }
        }



    }]).controller('creditNoteDetailModalCtrl', ['$scope', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window','creditNoteDetail',
        function ($scope, appUtil, $toastService, apiJson, $http, Popeye, $window ,creditNoteDetail) {

            $scope.init = function () {
                $scope.creditNoteDetail = creditNoteDetail;

            }


            $scope.closeModal = function () {
                Popeye.closeCurrentModal();
            };

        }
    ]
);
