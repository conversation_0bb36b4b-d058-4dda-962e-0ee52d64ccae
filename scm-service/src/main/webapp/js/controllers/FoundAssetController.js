angular.module('scmApp').controller('FoundAssetController', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
    '$location', 'fileService', '$alertService','$toastService','metaDataService','$timeout','$filter',
    function ($rootScope, $scope, apiJson, $http, appUtil, $location, fileService, $alertService,$toastService, metaDataService,$timeout, $filter) {

        $scope.init = function () {
            $scope.List = [];
            $scope.approvalList = [];
            $scope.currentUserId = appUtil.getCurrentUser().userId;
            $scope.currentUnit = appUtil.getUnitData();
            $scope.getPendingApprovals($scope.currentUnit.id);
        };

        $scope.getPendingApprovals = function (unitId) {
            $scope.imageURL = apiJson.skuBaseUrl;
            $http({
                url: apiJson.urls.stockManagement.checkFoundPendingApprovals,
                method: 'GET',
                params: {
                    unitId:unitId
                }
            }).success(function (response) {
                $scope.approvalList = response;
            }).error(function (response) {
                if (response.errorMsg != null) {
                    $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                } else {
                    $toastService.create("Could not fetch found asset pending approvals.");
                }
            });
        };

        $scope.processRequest = function (requestId, res) {
            $http({
                url: apiJson.urls.stockManagement.processRequest,
                method: 'POST',
                params: {
                    requestId: requestId,
                    userId: $scope.currentUserId,
                    res: res
                }
            }).success(function (response) {
                if(response == true){
                    $toastService.create("Found Asset Request Approved");
                }
                else if(response == false){
                    $toastService.create("Could Not Process Request");
                }
                $scope.getPendingApprovals($scope.currentUnit.id);
            }).error(function (response) {
                if (response.errorMsg != null) {
                    $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                } else {
                    $toastService.create("Error In Processing Request");
                }
            });
        };

    }]) ;