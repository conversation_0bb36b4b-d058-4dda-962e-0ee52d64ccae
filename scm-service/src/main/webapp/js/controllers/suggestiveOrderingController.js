/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by Shanmukh
 */

'use strict';

angular.module('scmApp')
    .controller('suggestiveOrderingController', ['$rootScope', '$scope', '$stateParams', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'recipeService', 'metaDataService', 'previewModalService', '$alertService', 'Popeye', '$q', '$timeout',
        function ($rootScope, $scope, $stateParams, apiJson, $http, appUtil, $toastService, $state, recipeService, metaDataService, previewModalService, $alertService, Popeye, $q, $timeout) {


            $scope.init = function () {
                $scope.showScmProductsList = false;
                $scope.productList = appUtil.getScmProductDetails();
                $scope.productMap = {};
                angular.forEach(appUtil.getScmProductDetails(), function (prod) {
                    $scope.productMap[prod.productId] = prod;
                });
                $scope.expiryProduct = {};
                $scope.warehouseordering = false;
                $scope.exceptionalProducts = [100779];
                $scope.expiryDataCheck = false;
                $scope.noOfDays = 2;
                $scope.getFulfilmentUnits();
                $scope.errorInInStock = false;
                $scope.brandList = appUtil.getBrandList();
                $scope.brandList.forEach(function (value) {
                    if (value.brandCode === 'CH') {
                        $scope.chaayosId = value.brandId;
                    } else if (value.brandCode === 'GNT') {
                        $scope.gntId = value.brandId;
                    } else if (value.brandCode === 'DC') {
                        $scope.dcId = value.brandId;
                    }
                });
                $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                $scope.comment = null;
                $scope.minRefOrderFulFillmentDate = appUtil.getDate(1);
                $scope.maxRefOrderFulFillmentDate = appUtil.getDate(7);
                console.log($scope.minRefOrderFulFillmentDate + "," + $scope.maxRefOrderFulFillmentDate);
                $scope.brandDetails = [{id: 1, brandName: "CHAAYOS"},
                    {id: 3, brandName: "GNT"},
                    {id: 6, brandName: "DOHFUL"},
                    {id: 0, brandName: "CHAAYOS_AND_GNT"}];
                $scope.selectedBrandDetails = $scope.brandDetails[0];
                $scope.showPreview = previewModalService.showPreview;
                $scope.raiseBy = false;
                $scope.currentUserId = appUtil.getCurrentUser().userId;
                $scope.showOnlyWarehouseItems = false;
                $scope.currentUser = appUtil.getCurrentUser().userId;
                $scope.orderingPercentages = [75,80,85,90,95,100,105,110,115,120,125,130,135,140,145,150];
            };

            $scope.setSelectedOrderingPercentage = function (entry, percentage) {
              entry.orderingPercentage = percentage;
            };

            $scope.getScmProductsConsumptionAverage = function () {
                $alertService.alert("Please Wait..!", "<b>Please Wait it May Take a Few Minutes To get the Data....!</b><br><b>This Will be Closed Automatically..!</b>", function (result) {
                }, false);
                $scope.scmProductsConsumptionAverageMap = {};
                $scope.dayWiseScmProductsConsumption = {};
                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    fulfillmentDate: $scope.dateformatting($scope.fulfillmentDate),
                    noOfDays: $scope.noOfDays,
                    brandName: $scope.selectedBrandDetails.brandName,
                    salesData: $scope.dataEntry,
                    requestedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };
                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getScmProductsConsumptionAverage,
                    data: inputData
                }).then(function success(response) {
                    $scope.scmProductsConsumptionAverageMap = response.data;
                    $scope.makeScmProductsDayWiseConsumption(angular.copy(response.data));
                    $scope.makeTotalScmSuggestions(angular.copy(response.data));
                    $scope.getScmProductsAfterExpiry($scope.scmProductsConsumptionAverageMap, $scope.dataEntry);
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.scmProductsConsumptionAverageMap = {};
                    if (response.data.errorMsg != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    } else {
                        $toastService.create("Something Went Wrong While Getting Scm Products Consumption Average");
                    }
                });
            };

            function getAdditionalSuggestionData(prod){
                return {
                   productId : prod.productId,
                   originalConsumption : prod.originalConsumption,
                   stockOutPercentage : prod.stockOutPercentage,
                   stockOutRaw : prod.stockOutRaw,
                   wastagePercentage : prod.wastagePercentage,
                   wastageRaw : prod.wastageRaw,
                   adjustedQuantity : prod.adjustedQuantity,
                   bufferQuantity : prod.bufferQuantity,
                   cafeTotalHours : prod.cafeTotalHours,
                   comments : prod.comments !=null ? prod.comments : "",
                   totalConsumption : prod.totalConsumption
                };
            }

            function addAdditionalSuggestionData(prod,additionalData){
             return {
                                productId : prod.productId,
                                originalConsumption : prod.originalConsumption + additionalData.originalConsumption ,
                                stockOutPercentage : prod.stockOutPercentage,
                                stockOutRaw : prod.stockOutRaw + additionalData.stockOutRaw,
                                wastagePercentage : prod.wastagePercentage,
                                wastageRaw : prod.wastageRaw + additionalData.wastageRaw,
                                adjustedQuantity : prod.adjustedQuantity + additionalData.adjustedQuantity,
                                bufferQuantity : prod.bufferQuantity ,
                                cafeTotalHours : prod.cafeTotalHours + additionalData.cafeTotalHours,
                                comments : prod.comments  + (additionalData.comments!=null ? " , " +  additionalData.comments : "" ) ,
                                totalConsumption : prod.totalConsumption + additionalData.totalConsumption
                             };
            }

            function checkOrderingDay(date){
               var orderingDay  = false;
               $scope.dataEntry.forEach(function(data){
                 if(data.date == date && data.dayType == "ORDERING_DAY"){
                    orderingDay = true;
                 }
               });
               return orderingDay;

            }

            function getNonEditableProducts(scmProducts) {
                $scope.nonEditableProducts = [];
                $http({
                    method: "GET",
                    url: apiJson.urls.referenceOrderManagement.getNonEditableProductsInSuggestiveOrdering,
                }).then(function success(response) {
                    if(response.data.success) {
                        $scope.nonEditableProducts = response.data.data;
                    } else {
                        console.log("No non editable products found");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.isOrderingNonDisable = function(productId) {
                for(var i = 0; i < $scope.nonEditableProducts.length; i++) {
                    if ($scope.nonEditableProducts[i] == productId) {
                        return true;
                    }
                }
                return false;
            }

            $scope.makeScmProductsDayWiseConsumption = function (scmConsumption) {
                $scope.scmProductsDayWiseConsumption = {};
                $scope.scmProductAdditionData = {};

                var uniqueProductIds  = [];
                angular.forEach(scmConsumption, function (dayListProducts, date) {
                    angular.forEach(dayListProducts, function (prod) {
                        if (uniqueProductIds.indexOf(prod.productId) === -1) {
                            uniqueProductIds.push(prod.productId);
                        }
                        prod.name = $scope.productMap[prod.productId].productName;
                        prod.unitOfMeasure = $scope.productMap[prod.productId].unitOfMeasure;
                        var key = date + "_" + prod.productId;
                        $scope.scmProductsDayWiseConsumption[key] = prod;
                        if(($scope.selectedBrandDetails.id == 3 || $scope.selectedBrandDetails.id == 0 ) && prod.originalConsumption != null && checkOrderingDay(date)){
                           if($scope.scmProductAdditionData[prod.productId]!=null){
                              $scope.scmProductAdditionData[prod.productId] = addAdditionalSuggestionData(prod,$scope.scmProductAdditionData[prod.productId]);
                           }else{
                             $scope.scmProductAdditionData[prod.productId] = getAdditionalSuggestionData(prod);
                           }

                        }
                    });
                });
                angular.forEach(uniqueProductIds, function (uniqueProductId) {
                     angular.forEach($scope.OrderingDaysFinal, function (date) {
                         var key = date + "_" + uniqueProductId;
                         if (!$scope.scmProductsDayWiseConsumption[key]) {
                             var prod = {};
                             prod.name = $scope.productMap[uniqueProductId].productName;
                             prod.productId = uniqueProductId;
                             prod.unitOfMeasure = $scope.productMap[uniqueProductId].unitOfMeasure;
                             $scope.scmProductsDayWiseConsumption[key] = prod;
                         }
                     });
                });

            };

            $scope.setBrand = function (brand) {
                $scope.selectedBrandDetails = brand;
                $scope.setDates($scope.fulfillmentDate, 2);
            };

            $scope.getFulfilmentUnits = function () {
                $scope.fulfilmentUnitsMap = {};
                $scope.fulfilmentUnitProductsMap = {};
                $http({
                    method: "GET",
                    url: apiJson.urls.referenceOrderManagement.getFulfilmentUnits,
                    params: {
                        "requestingUnitId": appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    $scope.fulfilmentUnitsMap = response.data;
                    angular.forEach($scope.fulfilmentUnitsMap, function (unitId, fulfilmentType) {
                        $scope.getAvailableProducts(unitId, fulfilmentType);
                    });
                    console.log("fulfilmentUnitsMap is : ", $scope.fulfilmentUnitsMap);
                    console.log("fulfilmentUnitProductsMap is : ", $scope.fulfilmentUnitProductsMap);
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.fulfilmentUnitsMap = {};
                    $scope.fulfilmentUnitProductsMap = {};
                });
            };

            $scope.getAvailableProducts = function (unitId, type) {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableProducts,
                    params: {
                        requestingUnit: appUtil.getCurrentUser().unitId,
                        fulfillmentUnit: unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.fulfilmentUnitProductsMap[type] = response.data;
                        console.log("Products Map = ", $scope.fulfilmentUnitProductsMap);
                    } else {
                        $scope.fulfilmentUnitProductsMap[type] = [-1];
                        $toastService.create("No products Mapped for Unit :: " + unitId);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create("Something went wrong While getting available Products. Please try again!");
                    $scope.fulfilmentUnitProductsMap[type] = [-1];
                });
            };

            function setBrandDetails(brand) {
                var result = {};
                for (var i = 0; i < $scope.brandDetails.length; i++) {
                    if ($scope.brandDetails[i].brandName == brand) {
                        result = $scope.brandDetails[i];
                        break;
                    }
                }
                return result;
            }

            $scope.getDayWiseExpiryProduct = function () {
                $scope.expiryDataCheck = false;
                return $http({
                    method: 'GET',
                    url: apiJson.urls.stockManagement.getDayWiseExpiryProduct,
                    params: {
                        "unitId": appUtil.getCurrentUser().unitId,
                        "dates": $scope.OrderingDaysFinal,
                        "isAggregated": false,
                        "firstOrderingDate": $scope.onlyOrderingDays[0]
                    }
                }).then(function success(response) {
                    console.log("response is of : ", response.data);
                    $scope.dayWiseExpiryProduct = response.data;
                    $scope.totalDayWiseExpiry = angular.copy(response.data["totalStock"]);
                    $scope.acknowledgedRoDayWiseExpiry = angular.copy(response.data["acknowledgedRoInTransit"]);
                    $scope.makeAcknowledgedRoDayWiseExpiry(angular.copy(response.data["acknowledgedRoInTransit"]));
                    $scope.copyOfTotalStock = angular.copy($scope.totalDayWiseExpiry);
                    $scope.expiryDataCheck = true;
                }, function error(response) {
                    console.log("error: ", response);
                    $scope.errorInInStock = true;
                    if (response.data.errorMsg != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    } else {
                        $toastService.create("Error Occurred While getting the Day wise Expiries ..!");
                    }
                });
            };

            $scope.makeAcknowledgedRoDayWiseExpiry = function (acknowledgedStock) {
                $scope.acknowledgedStockMap = {};
                $scope.acknowledgedStockQuantityMap = {};
                if (acknowledgedStock !== undefined && acknowledgedStock != null) {
                    angular.forEach($scope.OrderingDaysFinal, function (date) {
                        if (acknowledgedStock[date] !== undefined && acknowledgedStock[date] != null) {
                            angular.forEach(acknowledgedStock[date], function (productWithExpiries, productId) {
                                var key = productId + "_" + date;
                                $scope.acknowledgedStockMap[key] = productWithExpiries;
                                angular.forEach(productWithExpiries, function (quantity) {
                                     if ($scope.acknowledgedStockQuantityMap[productId] !== undefined && $scope.acknowledgedStockQuantityMap[productId] != null) {
                                         $scope.acknowledgedStockQuantityMap[productId] = quantity + $scope.acknowledgedStockQuantityMap[productId];
                                     } else {
                                         $scope.acknowledgedStockQuantityMap[productId] = quantity;
                                     }
                                });
                            });
                        }
                    });
                }
            };

            function makeDateString(date) {
                var newDate = new Date(date);
                var result = newDate.getFullYear() + "-" + (newDate.getMonth() + 1) + "-" + (newDate.getDate());
                console.log("result Date is : ", result);
                return result;
            }

            var debounceTimer = null;
            $scope.isApiInProgress = false;
            $scope.setDates = function (date, days) {
                if (debounceTimer) {
                    $timeout.cancel(debounceTimer);
                }
                debounceTimer = $timeout(function () {
                   try {
                         if ($scope.isApiInProgress) return;
                         $scope.isApiInProgress = true;
                         var brandName = $scope.selectedBrandDetails.brandName;
                         var isDohful = brandName === 'DOHFUL';
                         var maxAllowedDays = isDohful ? 7 : 3;
                         // Always clear first to ensure UI resets immediately
                         $scope.dataEntry = [];
                         $scope.OrderingDaysFinal = [];
                         $scope.remainingDaysFinal = [];
                         $scope.onlyOrderingDays = [];
                         $scope.stockLastingDate = null;
                         $scope.stockLastingDay = null;
                         if (!days || isNaN(days) || days <= 0) {
                            $toastService.create("Please Enter Ordering Days..!");
                            $scope.noOfDays = 0;
                            $scope.isApiInProgress = false;
                            return;
                         }
                         if (days > maxAllowedDays) {
                                $toastService.create("Ordering Days cannot be more than " + maxAllowedDays + "  for selected brand.");
                                console.log("maxAllowedDays, days is : ", maxAllowedDays, days);
                                $scope.noOfDays = 0;
                                $scope.isApiInProgress = false;
                                return;
                         }
                        var maxWindowDate = appUtil.getDate(7);
                        $scope.fulfillmentDate = $scope.regularOrderingEvent != null ? makeDateString($scope.regularOrderingEvent.fulfilmentDate) : makeDateString(date);
                        $scope.noOfDays = $scope.regularOrderingEvent != null ? $scope.regularOrderingEvent.orderingDays : days;
                        $scope.fulfillmentDay = $scope.getDayOfWeekFromStr($scope.fulfillmentDate);
                        $scope.stockLastingDate = appUtil.calculatedDate($scope.noOfDays - 1, $scope.fulfillmentDate);
                        $scope.stockLastingDay = $scope.getDayOfWeek($scope.stockLastingDate);

                       if (new Date($scope.stockLastingDate) > new Date(maxWindowDate)) {
                               $toastService.create(" Ordering days go outside the allowed window (from" + appUtil.formatDate($scope.minRefOrderFulFillmentDate, 'yyyy-MM-dd') + " to " + appUtil.formatDate($scope.maxRefOrderFulFillmentDate, 'yyyy-MM-dd'));
                               $scope.noOfDays = 0;
                               $scope.stockLastingDate = null;
                               $scope.stockLastingDay = null;
                               $scope.isApiInProgress = false;
                               return;
                       }

                        console.log(appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd"));
                        console.log("Fulfilment Date is : ", $scope.fulfillmentDate);
                        var regularOrderingDate = appUtil.getRegularOrderingDate();
                        $scope.remainingDays = appUtil.datediffRO(regularOrderingDate, $scope.fulfillmentDate);
                        for (var i = 1; i <= $scope.remainingDays - 1; i++) {
                            $scope.dataEntry.push({
                                dayType: 'REMAINING_DAY',
                                date: appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"),
                                brands: [{
                                    id: $scope.chaayosId,
                                    saleAmount: 0,
                                    deliverySalePercentage: 0
                                },
                                    {
                                        id: $scope.gntId,
                                        saleAmount: 0,
                                        deliverySalePercentage: 0
                                    },
                                    {
                                        id: $scope.dcId,
                                        saleAmount: 0,
                                        deliverySalePercentage: 0
                                    }],
                                orderingPercentage : 100

                            })
                            $scope.OrderingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                            $scope.remainingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                        }
                        for (var i = 0; i <= $scope.noOfDays - 1; i++) {
                            $scope.dataEntry.push({
                                dayType: 'ORDERING_DAY',
                                date: appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"),
                                brands: [{
                                    id: $scope.chaayosId,
                                    saleAmount: 0,
                                    deliverySalePercentage: 0
                                },
                                    {
                                        id: $scope.gntId,
                                        saleAmount: 0,
                                        deliverySalePercentage: 0
                                    },
                                    {
                                        id: $scope.dcId,
                                        saleAmount: 0,
                                        deliverySalePercentage: 0
                                    }],
                                orderingPercentage : 100
                            })
                            $scope.OrderingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                            $scope.onlyOrderingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                        }
                        console.log("data entry is :", $scope.dataEntry);
                        console.log("Ordering days final is  : ", $scope.OrderingDaysFinal);
                        if (appUtil.isEmptyObject($scope.noOfDays)) {
                            $toastService.create("Please Enter Ordering Days..!");
                            $scope.isApiInProgress = false;
                            return false;
                        } else {
                               $q.when($scope.getDayWiseExpiryProduct()).finally(function () {
                                  $scope.isApiInProgress = false;
                               });
                        }
                   } catch (e) {
                     console.log("Error in setDates: ", e);
                     $scope.isApiInProgress = false;
                   } finally {
                     $timeout(function() {
                         $scope.isApiInProgress = false;
                     });
                   }
                }, 500);
            };

            $scope.isFulfillmentInputInvalidOrLoading = function() {
                return $scope.isApiInProgress
                    || !$scope.fulfillmentDate
                    || $scope.noOfDays == null
                    || $scope.noOfDays <= 0;
            };


            $scope.getDayOfWeek = getDayOfWeek;
            $scope.getDayOfWeekFromStr = getDayOfWeekFromStr;
            $scope.getDaysOfWeek = getDaysOfWeek;

            $scope.getSuggestedQuantities = function () {
                if ($scope.selectedBrandDetails === null || $scope.selectedBrandDetails === undefined) {
                    $toastService.create("Select Brand");
                    return false;
                }
                if (!$scope.expiryDataCheck) {
                    $toastService.create("Wait to load Expiring products data ..!");
                    return;
                }
                if ($scope.fulfillmentDate === undefined || $scope.fulfillmentDate == null) {
                    $toastService.create("Please Select Fulfilment Date ..!");
                    return;
                }
                $scope.getScmProductsConsumptionAverage();
                getNonEditableProducts();
            };

            $scope.makeTotalScmSuggestions = function (scmSuggestions) {
                $scope.totalScmSuggestions = {};
                angular.forEach(scmSuggestions, function (scmAverages, key) {
                    if ($scope.remainingDaysFinal.indexOf(key) == -1) {
                        angular.forEach(scmAverages, function (scmAverage) {
                            if (appUtil.isEmptyObject($scope.totalScmSuggestions[scmAverage.productId])) {
                                $scope.totalScmSuggestions[scmAverage.productId] = scmAverage.averageConsumption;
                            } else {
                                $scope.totalScmSuggestions[scmAverage.productId] = $scope.totalScmSuggestions[scmAverage.productId] + scmAverage.averageConsumption;
                            }
                        });
                    }
                });
                console.log("Total of Scm Suggestions are : ", $scope.totalScmSuggestions);
            };

            $scope.checkProductInBeforeDaysList = function (productId, beforeDaysList) {
                for (var i = 0; i < beforeDaysList.length; i++) {
                    if ($scope.acknowledgedRoDayWiseExpiry[beforeDaysList[i]] !== undefined && $scope.acknowledgedRoDayWiseExpiry[beforeDaysList[i]] != null
                        && $scope.acknowledgedRoDayWiseExpiry[beforeDaysList[i]][productId] !== undefined && $scope.acknowledgedRoDayWiseExpiry[beforeDaysList[i]][productId] != null) {
                        return true;
                    }
                }
                return false;
            };

            $scope.getScmProductsAfterExpiry = function (scmProducts, dataEntryData) {
                $scope.scmProductWiseMap = {};
                $scope.productWiseFulfilment = {};
                console.log("entries are ss: ", dataEntryData);
                for (var i = 0; i < $scope.OrderingDaysFinal.length; i++) {
                    var prods = scmProducts[$scope.OrderingDaysFinal[i]];
                    if (prods !== undefined && prods !== null) {
                        var dup = angular.copy($scope.OrderingDaysFinal);
                        for (var j = 0; j < prods.length; j++) {
                            var arr = $scope.getArray(dup, i);
                            var beforeDaysList = $scope.getBeforeArray(dup, i);
                            if ($scope.productWiseFulfilment[prods[j].productId] === undefined || $scope.productWiseFulfilment[prods[j].productId] === null) {
                                $scope.productWiseFulfilment[prods[j].productId] = [];
                            }
                            var logs = $scope.productWiseFulfilment[prods[j].productId];
                            var log = "For Day : " + $scope.OrderingDaysFinal[i] + " Needed : " + prods[j].orderingQuantity;
                            logs.push(log);
                            $scope.productWiseFulfilment[prods[j].productId] = logs;
                            if ($scope.copyOfTotalStock[prods[j].productId] !== undefined && $scope.copyOfTotalStock[prods[j].productId] != null ||
                            ($scope.acknowledgedRoDayWiseExpiry !== undefined && $scope.acknowledgedRoDayWiseExpiry != null && $scope.checkProductInBeforeDaysList(prods[j].productId, beforeDaysList))) {
                                console.log("current prod is and date is  : ", prods[j], $scope.OrderingDaysFinal[i]);
                                var finalQty = checkForExpiry(prods[j], arr, i, beforeDaysList, $scope.OrderingDaysFinal[i]);
                                if ($scope.remainingDaysFinal.indexOf($scope.OrderingDaysFinal[i]) === -1) {
                                    if ($scope.scmProductWiseMap[prods[j].productId] === undefined || $scope.scmProductWiseMap[prods[j].productId] == null) {
                                        $scope.scmProductWiseMap[prods[j].productId] = parseFloat(finalQty);
                                    } else {
                                        var qty = $scope.scmProductWiseMap[prods[j].productId];
                                        $scope.scmProductWiseMap[prods[j].productId] = qty + parseFloat(finalQty);
                                    }
                                } else {
                                    console.log("Changed data for remaining days qty ..!");
                                }
                            } else {
                                if ($scope.remainingDaysFinal.indexOf($scope.OrderingDaysFinal[i]) === -1) {
                                    if ($scope.scmProductWiseMap[prods[j].productId] === undefined || $scope.scmProductWiseMap[prods[j].productId] == null) {
                                        $scope.scmProductWiseMap[prods[j].productId] = parseFloat(prods[j].averageConsumption);
                                    } else {
                                        var qty = $scope.scmProductWiseMap[prods[j].productId];
                                        $scope.scmProductWiseMap[prods[j].productId] = qty + parseFloat(prods[j].averageConsumption);
                                    }
                                } else {
                                    console.log("Changed data for remaining days qty ..!");
                                }
                            }
                        }
                    }
                }
                console.log("scm Product wise map is : ", $scope.scmProductWiseMap);
                $scope.getProductWiseStock();
            };

            $scope.getProductWiseStock = function () {
                $scope.productWiseStock = {};
                angular.forEach($scope.dayWiseExpiryProduct['inStock'], function (value, key) {
                    var total = 0;
                    angular.forEach($scope.dataEntry, function (entry) {
                        if (value[entry.date] != undefined && value[entry.date] != null) {
                            total = total + value[entry.date];
                            if (appUtil.isEmptyObject($scope.productWiseStock[key])) {
                                $scope.productWiseStock[key] = {};
                                $scope.productWiseStock[key].stockAtHand = {};
                                $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                            } else {
                                if (appUtil.isEmptyObject($scope.productWiseStock[key].stockAtHand)) {
                                    $scope.productWiseStock[key].stockAtHand = {}
                                    $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                    $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                                } else {
                                    $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].stockAtHand[entry.date])) {
                                        $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                                    } else {
                                        $scope.productWiseStock[key].stockAtHand[entry.date] = $scope.productWiseStock[key].stockAtHand[entry.date] + value[entry.date];
                                    }
                                }
                            }
                        }
                    });
                });

                angular.forEach($scope.dayWiseExpiryProduct['inTransit'], function (value, key) {
                    var total = 0;
                    angular.forEach($scope.dataEntry, function (entry) {
                        if (value[entry.date] != undefined && value[entry.date] != null) {
                            total = total + value[entry.date];
                            if (appUtil.isEmptyObject($scope.productWiseStock[key])) {
                                $scope.productWiseStock[key] = {};
                                $scope.productWiseStock[key].inTransit = {};
                                $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                            } else {
                                if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit)) {
                                    $scope.productWiseStock[key].inTransit = {};
                                    $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                    $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                                } else {
                                    $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit[entry.date])) {
                                        $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                                    } else {
                                        $scope.productWiseStock[key].inTransit[entry.date] = $scope.productWiseStock[key].inTransit[entry.date] + value[entry.date];
                                    }
                                }
                            }
                        }
                    });
                });

                // angular.forEach($scope.dayWiseExpiryProduct['acknowledgedRoInTransit'], function (value, key) {
                //     var total = 0;
                //     angular.forEach($scope.dataEntry, function (entry) {
                //         if (value[entry.date] != undefined && value[entry.date] != null) {
                //             total = total + value[entry.date];
                //             if (appUtil.isEmptyObject($scope.productWiseStock[key])) {
                //                 $scope.productWiseStock[key] = {};
                //                 $scope.productWiseStock[key].inTransit = {};
                //                 $scope.productWiseStock[key].inTransit["totalStock"] = total;
                //                 $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                //             } else {
                //                 if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit)) {
                //                     $scope.productWiseStock[key].inTransit = {};
                //                     $scope.productWiseStock[key].inTransit["totalStock"] = total;
                //                     $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                //                 } else {
                //                     if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit["totalStock"])) {
                //                         $scope.productWiseStock[key].inTransit["totalStock"] = total;
                //                     } else {
                //                         $scope.productWiseStock[key].inTransit["totalStock"] = $scope.productWiseStock[key].inTransit["totalStock"] + total;
                //                     }
                //
                //                     if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit[entry.date])) {
                //                         $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                //                     } else {
                //                         $scope.productWiseStock[key].inTransit[entry.date] = $scope.productWiseStock[key].inTransit[entry.date] + value[entry.date];
                //                     }
                //                 }
                //             }
                //         }
                //     });
                // });
                console.log("Product wise stock of Stock at hand and intransit is : ", $scope.productWiseStock);

                $scope.makeProductWiseExpiryData($scope.productWiseStock);
            };

            $scope.makeProductWiseExpiryData = function (pws) {
                console.log("pws is : ", pws);
                $scope.productWiseExpiryData = {};
                angular.forEach(pws, function (typeOfStock, productId) {
                    $scope.productWiseExpiryData[productId] = {};
                    var currentObj = $scope.productWiseExpiryData[productId];
                    angular.forEach(typeOfStock, function (dateWise, type) {
                        angular.forEach(dateWise, function (quantity, date) {
                            if (!date.startsWith('t')) {
                                var key = date + '_' + type;
                                currentObj[key] = quantity;
                                $scope.productWiseExpiryData[productId] = currentObj;
                            }
                        });
                    });
                });
                console.log("Final ProductWise data is : ", $scope.productWiseExpiryData);
                $scope.createSCMOrderingProductList();
            };

            $scope.getArray = function (dup, index) {
                var res = [];
                for (var i = 0; i < dup.length; i++) {
                    if (i >= index) {
                        res.push(dup[i]);
                    }
                }
                return res;
            }

            $scope.getBeforeArray = function (dup, index) {
                var res = [];
                for (var i = 0; i < dup.length; i++) {
                    if (i <= index) {
                        res.push(dup[i]);
                    }
                }
                return res;
            }

            $scope.showExpiryUsage = function (product) {
                $scope.selectedProductForLogs = product;
                $scope.selectedProductLogs = $scope.productWiseFulfilment[product.id] || [];
            };

            $scope.showAcknowledgedRoStock = function (product) {
                $scope.selectedProductForAcknowledgedRo = product;
            };

            function checkForExpiry(product, dayEntry, currentDateIndex, copyOfAllDays, currentDay) {
                var arrayOfLogs = $scope.productWiseFulfilment[product.productId];
                var currentQty = parseFloat(product.orderingQuantity);
                for (var i = 0; i < dayEntry.length; i++) {
                    var expiryProduct = $scope.copyOfTotalStock[product.productId];
                    if (expiryProduct !== undefined && expiryProduct != null) {
                        var dayExpiry = expiryProduct[dayEntry[i]];
                        if (dayExpiry !== undefined && dayExpiry != null) {
                            console.log("day expiry is : ", angular.copy(dayExpiry));
                            console.log("Before updating the expiry quantity is for product id: ", product.productId, angular.copy(expiryProduct));
                            console.log("Date is and current qty is : ", dayEntry[i], angular.copy(currentQty));
                            if (currentQty > 0) {
                                if (currentQty >= dayExpiry) {
                                    var log = "For Fulfilling " + currentDay + " Quantity Of " + parseFloat(currentQty.toFixed(6)) + " Using the (I,T) Quantity " + parseFloat(dayExpiry.toFixed(6)) + " Of " + dayEntry[i] + " Used Completely";
                                    arrayOfLogs.push(log);
                                    currentQty = parseFloat(currentQty.toFixed(6)) - parseFloat(dayExpiry.toFixed(6));
                                    expiryProduct[dayEntry[i]] = 0;
                                    product.orderingQuantity = parseFloat(currentQty.toFixed(6));
                                    log = "After Using (I,T) Quantity Current needed Quantity is : " + product.orderingQuantity;
                                    arrayOfLogs.push(log);
                                    console.log("After updating the expiry quantity is for product id: ", product.productId, angular.copy(expiryProduct));
                                    console.log("After updating the expiry quantity total expiry is :  ", angular.copy($scope.copyOfTotalStock));
                                    if (currentQty === 0) {
                                        break;
                                    }
                                } else {
                                    var log = "For Fulfilling " + currentDay + " Quantity Of " + parseFloat(currentQty.toFixed(6)) + " Using the (I,T) Quantity " + parseFloat(dayExpiry.toFixed(6)) + " Of " + dayEntry[i] + " Fulfilled Completely";
                                    arrayOfLogs.push(log);
                                    expiryProduct[dayEntry[i]] = parseFloat(dayExpiry.toFixed(6)) - parseFloat(currentQty.toFixed(6));
                                    currentQty = 0;
                                    product.orderingQuantity = parseFloat(currentQty.toFixed(6));
                                    log = "After Using (I,T) Quantity Current needed Quantity is : " + product.orderingQuantity;
                                    arrayOfLogs.push(log);
                                    console.log("After updating the expiry quantity is for product id: ", product.productId, angular.copy(expiryProduct));
                                    console.log("After updating the expiry quantity total expiry is :  ", angular.copy($scope.copyOfTotalStock));
                                    break;
                                }
                            } else {
                                break;
                            }
                        }
                    }

                    // checking the ack ro with same expiry date
                    if (currentQty > 0 && $scope.acknowledgedRoDayWiseExpiry !== undefined && $scope.acknowledgedRoDayWiseExpiry != null) {
                        for (var j = 0; j < copyOfAllDays.length; j++) {
                            var acknowledgedRoStock = $scope.acknowledgedRoDayWiseExpiry[copyOfAllDays[j]];
                            if (acknowledgedRoStock !== undefined && acknowledgedRoStock != null &&
                                acknowledgedRoStock[product.productId] !== undefined && acknowledgedRoStock[product.productId] != null) {
                                var acknowledgedStockOfProductForDay = acknowledgedRoStock[product.productId];
                                var dayExpiryRo = acknowledgedStockOfProductForDay[dayEntry[i]];
                                if (dayExpiryRo !== undefined && dayExpiryRo != null) {
                                    console.log("Inside the Acknowledged RO Before updating the expiry quantity is for product id: ", product.productId, angular.copy(acknowledgedStockOfProductForDay));
                                    console.log("Inside the Acknowledged RO Date is and current qty is : ", dayEntry[i], angular.copy(currentQty));
                                    if (currentQty > 0) {
                                        if (currentQty >= dayExpiryRo) {
                                            var log = "For Fulfilling " + currentDay + " Quantity Of " + parseFloat(currentQty.toFixed(6)) + " Using the (A) Quantity " + parseFloat(dayExpiryRo.toFixed(6)) + " Of " + dayEntry[i] + " Which Came On " + copyOfAllDays[j] + " Used Completely";
                                            arrayOfLogs.push(log);
                                            currentQty = parseFloat(currentQty.toFixed(6)) - parseFloat(dayExpiryRo.toFixed(6));
                                            acknowledgedStockOfProductForDay[dayEntry[i]] = 0;
                                            product.orderingQuantity = parseFloat(currentQty.toFixed(6));
                                            log = "After Using (A) Quantity Current needed Quantity is : " + product.orderingQuantity;
                                            arrayOfLogs.push(log);
                                            console.log("Inside the Acknowledged RO After updating the expiry quantity is for product id: ", product.productId, angular.copy(acknowledgedStockOfProductForDay));
                                            console.log("Inside the Acknowledged RO After updating the expiry quantity total expiry is :  ", angular.copy($scope.acknowledgedRoDayWiseExpiry));
                                            if (currentQty === 0) {
                                                break;
                                            }
                                        } else {
                                            var log = "For Fulfilling " + currentDay + " Quantity Of " + parseFloat(currentQty.toFixed(6)) + " Using the (A) Quantity " + parseFloat(dayExpiryRo.toFixed(6)) + " Of " + dayEntry[i] + " Which Came On " + copyOfAllDays[j] + " Fulfilled Completely";
                                            arrayOfLogs.push(log);
                                            acknowledgedStockOfProductForDay[dayEntry[i]] = parseFloat(dayExpiryRo.toFixed(6)) - parseFloat(currentQty.toFixed(6));
                                            currentQty = 0;
                                            product.orderingQuantity = parseFloat(currentQty.toFixed(6));
                                            log = "After Using (A) Quantity Current needed Quantity is : " + product.orderingQuantity;
                                            arrayOfLogs.push(log);
                                            console.log("Inside the Acknowledged RO After updating the expiry quantity is for product id: ", product.productId, angular.copy(acknowledgedStockOfProductForDay));
                                            console.log("Inside the Acknowledged RO After updating the expiry quantity total expiry is :  ", angular.copy($scope.acknowledgedRoDayWiseExpiry));
                                            break;
                                        }
                                    } else {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    if (currentQty === 0) {
                        console.log("Consumed Completely for product id: ", product.productId);
                        break;
                    }
                }
                $scope.productWiseFulfilment[product.productId] = arrayOfLogs;
                return product.orderingQuantity;
            }

            $scope.viewDetailedExpiries = function () {
                var expiriesModal = Popeye.openModal({
                    templateUrl: "expiriesModal.html",
                    controller: "expiriesModalController",
                    modalClass: 'custom-modal',
                    resolve: {
                        scmSuggested: function () {
                            return $scope.scmProductsDayWiseConsumption;
                        },
                        expiries: function () {
                            return $scope.productWiseStock;
                        },
                        dates: function () {
                            return $scope.dataEntry;
                        }
                    },
                    click: false,
                    keyboard: false
                });
            };

            $scope.goToFulfilmentSelection = function () {
                if (confirm("Are you sure?")) {
                    $scope.scmProductList = [];
                    $scope.comment = null;
                    $scope.getDayWiseExpiryProduct();
                    $scope.showScmProductsList = false;
                }
            };

            function getAllExpiry(item) {
                var msg = "(I,T) - ";
                if ($scope.totalDayWiseExpiry[item.id] !== undefined && $scope.totalDayWiseExpiry[item.id] != null) {
                    msg+=JSON.stringify($scope.totalDayWiseExpiry[item.id]);
                } else {
                    msg += "NA";
                }
                msg += " , ACK - ";
                if ($scope.acknowledgedStockQuantityMap[item.id] !== undefined && $scope.acknowledgedStockQuantityMap[item.id] != null) {
                    angular.forEach($scope.OrderingDaysFinal, function (date) {
                        var key = item.id + "_" + date;
                        if ($scope.acknowledgedStockMap[key] !== undefined && $scope.acknowledgedStockMap[key] != null) {
                            msg += " On Date : " + date + " Exp are : " + JSON.stringify($scope.acknowledgedStockMap[key]);
                        }
                    });
                } else {
                    msg += "NA";
                }
                return msg;
            }

            $scope.createSCMOrderingProductList = function () {
                $scope.scmProductList = [];
                $scope.specializedRoProducts = [];
                recipeService.getScmProductsFromSuggestions( $scope.scmProductWiseMap, $scope.productWiseStock, $scope.totalScmSuggestions, $scope.fulfilmentUnitProductsMap, $scope.productMap, function (itemList) {
                    var prods = [];
                    var allProds = [];
                    console.log("After All days conversion SCM Products are : ", itemList);
                    itemList.forEach(function (item) {
                        if (item.selectedFulfillmentType != null
                            && $scope.exceptionalProducts.indexOf(item.id) === -1 && item.productType != null && item.productType !== "DISCONTINUED") {
                            item.allExpiryData = getAllExpiry(item);
                            if (item.selectedFulfillmentType !== "EXTERNAL") {
                                prods.push(item);
                            }
                            if (item.supportsSpecialOrdering && item.id !== 100234 ) {
                                $scope.specializedRoProducts.push(item);
                            }
                            allProds.push(item);
                        }
                    });
                    console.log("Specialized Ro Products are : ", $scope.specializedRoProducts);
                    $scope.scmProductList = prods;
                    return allProds;
                });
                $alertService.closeAlert();
                $scope.showScmProductsList = true;
            };

            $scope.hideWareHouseItems = function (check) {
                $scope.warehouseordering = check;
            };

            $scope.updateOrderingQty = function (product) {
                if (product.critical) {
                    var qty = parseInt(product.packagingQuantity);
                    if (qty > product.duplicatePackagingQuantity) {
                        $toastService.create("Final Ordering Quantity can not be greater than Suggested for Critical Products..!");
                        product.packagingQuantity = product.duplicatePackagingQuantity;
                        product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                        product.reason = null;
                        return false;
                    } else {
                        product.packagingQuantity = parseInt(product.packagingQuantity);
                        product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                    }
                } else {
                    product.packagingQuantity = parseInt(product.packagingQuantity);
                    product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                }
            }


            function validateFulfillment(reqOrder) {
                var returnList = [];
                for (var i in reqOrder.referenceOrderScmItems) {
                    if (appUtil.isEmptyObject(reqOrder.referenceOrderScmItems[i].fulfillmentType)) {
                        returnList.push(reqOrder.referenceOrderScmItems[i].productName);
                    }
                }
                return returnList;
            }

            $scope.dateformatting = function (startDate) {
                var year = new Date(startDate).getFullYear();
                var month = new Date(startDate).getMonth() + 1;
                var day = new Date(startDate).getDate();
                if (day >= 1 && day < 10)
                    day = '0' + day;
                if (month >= 1 && month < 10)
                    month = '0' + month;
                return year + "-" + month + "-" + day;
            }

            $scope.sendReferenceOrder = function (action) {
                var data = createRoObject(action);
                var validateOrder = validateFulfillment(data);
                if ($scope.fulfillmentDate == null) {
                    $toastService.create('Please fill fulfillment date!');
                    return false;
                } else if (data.referenceOrderScmItems.length == 0) {
                    $toastService.create('Please select at least a few products to order!');
                    return false;
                } else if (validateOrder.length > 0) {
                    $alertService.alert('Products found without fulfillment types', validateOrder.join(","), null, true);
                    return false;
                } else {
                    data.estimationSalesDataRequests = $scope.dataEntry;
                    data.refOrderSource = "SUGGESTIVE_ORDERING";
                    if (confirm("Are you sure you want to create the order?")) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.referenceOrderManagement.newReferenceOrder,
                            data: data
                        }).then(function success(response) {
                            console.log(response);
                            if (response.data != null && response.data.referenceOrderId > 0) {
                                $toastService.create("Reference order with id " + response.data.referenceOrderId + " created successfully!");
                                if (response.data.regularOrderEvents != null && response.data.regularOrderEvents.length > 0) {
                                    $state.go("menu.refOrderCreateV1", {orderingEvents: response.data.regularOrderEvents});
                                } else {
                                    $rootScope.orderingEvents = [];
                                    $state.go("menu.reqOrderMgt");
                                }
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                            if (response.data.errorMsg != null) {
                                $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                            } else {
                                $toastService.create("Error Occurred While Placing Order..!");
                            }
                        });
                    }
                }
            }

            function createRoObject(status) {
                return {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    requestUnit: appUtil.createRequestUnit(),
                    generatedBy: appUtil.createGeneratedBy(),
                    fulfillmentUnit: null,
                    fulfillmentDate: new Date($scope.fulfillmentDate),
                    status: status,
                    comment: $scope.comment,
                    referenceOrderScmItems: getScmItems(),
                    numberOfDays: $scope.noOfDays,
                    raiseBy: $scope.raiseBy,
                    orderEvent: $scope.regularOrderingEvent,
                    refreshDate: $scope.refreshDate,
                    brand: $scope.selectedBrandDetails.brandName,
                    scmProductConsumptionAverages: $scope.scmProductsConsumptionAverageMap,
                    expiryUsageLogs: $scope.productWiseFulfilment
                }
            }

            function getExpiryDrillDown(productId) {
                if ($scope.productWiseExpiryData[productId] !== undefined && $scope.productWiseExpiryData[productId] !== null) {
                    return $scope.productWiseExpiryData[productId];
                }
                return null;
            }

            function getAcknowledgeDrillDown(productId) {
                var resultOfAckDrillDown = {};
                angular.forEach($scope.OrderingDaysFinal, function (date) {
                    var key = productId + "_" + date;
                    if ($scope.acknowledgedStockMap !== undefined && $scope.acknowledgedStockMap != null
                        && $scope.acknowledgedStockMap[key] !== undefined && $scope.acknowledgedStockMap[key] != null) {
                        resultOfAckDrillDown[date] = $scope.acknowledgedStockMap[key];
                    }
                });
                return resultOfAckDrillDown;
            }

            function makeProductObject(product) {
                if ($scope.scmProductAdditionData[product.productId] != null) {
                    return {
                        id: null,
                        productId: product.productId,
                        productName: product.productName,
                        suggestedQuantity: parseFloat(parseFloat(product.suggestedQuantity).toFixed(6)),
                        requestedQuantity: parseFloat(parseFloat(product.orderingQuantity).toFixed(6)),
                        requestedAbsoluteQuantity: parseFloat(parseFloat(product.orderingQuantity).toFixed(6)),
                        transferredQuantity: null,
                        receivedQuantity: null,
                        fulfillmentType: product.selectedFulfillmentType,
                        unitOfMeasure: product.unitOfMeasure,
                        predictedQuantity: parseFloat(parseFloat(product.predictedQuantity).toFixed(6)),
                        expiryDrillDown: getExpiryDrillDown(product.id),
                        acknowledgedRoExpiryDrillDown: getAcknowledgeDrillDown(product.id),
                        reason: !appUtil.isEmptyObject(product.reason) ? product.reason : null,
                        originalConsumption: $scope.scmProductAdditionData[product.productId].originalConsumption,
                        stockOutPercentage: $scope.scmProductAdditionData[product.productId].stockOutPercentage,
                        stockOutRaw: $scope.scmProductAdditionData[product.productId].stockOutRaw,
                        wastagePercentage: $scope.scmProductAdditionData[product.productId].wastagePercentage,
                        wastageRaw: $scope.scmProductAdditionData[product.productId].wastageRaw,
                        adjustedQuantity: product.adjustedQuantity !=null ? product.adjustedQuantity : $scope.scmProductAdditionData[product.productId].adjustedQuantity,
                        bufferQuantity: $scope.scmProductAdditionData[product.productId].bufferQuantity,
                        cafeTotalHours: $scope.scmProductAdditionData[product.productId].cafeTotalHours,
                        comments: $scope.scmProductAdditionData[product.productId].comments,
                        totalConsumption : $scope.scmProductAdditionData[product.productId].totalConsumption,
                        suggestedQuantityBeforeMoq: parseFloat(parseFloat(product.suggestedOrderingQuantity).toFixed(6))

                    };
                } else {
                    return {
                        id: null,
                        productId: product.productId,
                        productName: product.productName,
                        suggestedQuantity: parseFloat(parseFloat(product.suggestedQuantity).toFixed(6)),
                        requestedQuantity: parseFloat(parseFloat(product.orderingQuantity).toFixed(6)),
                        requestedAbsoluteQuantity: parseFloat(parseFloat(product.orderingQuantity).toFixed(6)),
                        suggestedQuantityBeforeMoq: parseFloat(parseFloat(product.suggestedOrderingQuantity).toFixed(6)),
                        transferredQuantity: null,
                        receivedQuantity: null,
                        fulfillmentType: product.selectedFulfillmentType,
                        unitOfMeasure: product.unitOfMeasure,
                        predictedQuantity: parseFloat(parseFloat(product.predictedQuantity).toFixed(6)),
                        expiryDrillDown: getExpiryDrillDown(product.id),
                        acknowledgedRoExpiryDrillDown: getAcknowledgeDrillDown(product.id),
                        reason: !appUtil.isEmptyObject(product.reason) ? product.reason : null,

                    };
                }

            }

            function getScmItems() {
                var scmProducts = [];
                $scope.scmProductList.forEach(function (product) {
                    if (product.orderingQuantity >= 0) {
                        if ($scope.warehouseordering) {
                            if (product.selectedFulfillmentType === 'KITCHEN' || product.selectedFulfillmentType === 'WAREHOUSE') {
                                scmProducts.push(makeProductObject(product));
                            }
                        } else {
                            if (product.selectedFulfillmentType === 'KITCHEN') {
                                scmProducts.push(makeProductObject(product));
                            }
                        }
                    }
                });
                return scmProducts;
            }


            function weekDays() {
                var days = [
                    {id: 1, value: 'Sunday'},
                    {id: 2, value: 'Monday'},
                    {id: 3, value: 'Tuesday'},
                    {id: 4, value: 'Wednesday'},
                    {id: 5, value: 'Thursday'},
                    {id: 6, value: 'Friday'},
                    {id: 7, value: 'Saturday'}];
                return days;
            }

            function getDayOfWeek(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                console.log('Selected Date', date.getDay());
                return weekDays()[date.getDay()].value;
            }

            function getDaysOfWeek() {
                return "NA";
            }

            function getDayOfWeekFromStr(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                return weekDays()[new Date(date).getDay()].value;
            }

            $scope.update = function () {
                $scope.raiseBy = !$scope.raiseBy;
            };

        }]).controller('expiriesModalController', ['$scope', 'Popeye', 'scmSuggested', 'expiries', 'dates', 'appUtil',
    function ($scope, Popeye, scmSuggested, expiries, dates, appUtil) {
        $scope.scmSuggested = scmSuggested;
        $scope.expiries = expiries;
        $scope.dates = dates;

        $scope.colHeaders = ["Product Name"];
        angular.forEach(dates, function (currentDate) {
            var currDate = new Date(currentDate.date);
            var col = currDate.getDate() + "/" + (currDate.getMonth() + 1) + " (Suggested, In-Stock, In-Transit) ";
            $scope.colHeaders.push(col);
        });

        $scope.mapOfRows = {};

        function generateObject(prod) {
            var obj = {};
            obj.productId = prod.productId;
            obj.productName = prod.name;
            obj.uom = prod.unitOfMeasure;
            angular.forEach($scope.dates, function (currDate) {
                var key = currDate.date + "_suggested";
                obj[key] = 0;
                key = currDate.date + "_ordering";
                obj[key] = 0;
                key = currDate.date + "_inStock";
                obj[key] = 0;
                key = currDate.date + "_inTransit";
                obj[key] = 0;
                obj.total = 0;
            });
            return obj;
        }

        angular.forEach($scope.scmSuggested, function (scmProduct, scmKey) {
            var productId = scmKey.split("_");
            if (appUtil.isEmptyObject($scope.mapOfRows[productId[1]])) {
                var obj = generateObject(scmProduct);
                var key = productId[0] + "_ordering";
                obj[key] = scmProduct.orderingQuantity || 0;
                key = productId[0] + "_suggested";
                obj[key] = scmProduct.suggestedQuantity || 0;
                key = productId[0] + "_inTransit";
                if (!appUtil.isEmptyObject($scope.expiries)) {
                    if (!appUtil.isEmptyObject($scope.expiries[productId[1]]) && !appUtil.isEmptyObject($scope.expiries[productId[1]]["inTransit"])) {
                        var inTransit = $scope.expiries[productId[1]]["inTransit"];
                        if (!appUtil.isEmptyObject(inTransit[productId[0]])) {
                            obj[key] = inTransit[productId[0]];
                        }
                    }
                }
                key = productId[0] + "_inStock";
                if (!appUtil.isEmptyObject($scope.expiries)) {
                    if (!appUtil.isEmptyObject($scope.expiries[productId[1]]) && !appUtil.isEmptyObject($scope.expiries[productId[1]]["stockAtHand"])) {
                        var inStock = $scope.expiries[productId[1]]["stockAtHand"];
                        if (!appUtil.isEmptyObject(inStock[productId[0]])) {
                            obj[key] = inStock[productId[0]];
                        }
                    }
                }
                $scope.mapOfRows[productId[1]] = obj;
            } else {
                // $scope.mapOfRows[productId[1]] = scmProduct;
                var obj = $scope.mapOfRows[productId[1]];
                var key = productId[0] + "_ordering";
                obj[key] = scmProduct.orderingQuantity || 0;
                key = productId[0] + "_suggested";
                obj[key] = scmProduct.suggestedQuantity || 0;
                key = productId[0] + "_inTransit";
                if (!appUtil.isEmptyObject($scope.expiries)) {
                    if (!appUtil.isEmptyObject($scope.expiries[productId[1]]) && !appUtil.isEmptyObject($scope.expiries[productId[1]]["inTransit"])) {
                        var inTransit = $scope.expiries[productId[1]]["inTransit"];
                        if (!appUtil.isEmptyObject(inTransit[productId[0]])) {
                            obj[key] = inTransit[productId[0]];
                        }
                        if (!appUtil.isEmptyObject(inTransit["totalStock"])) {
                            obj.total = inTransit["totalStock"];
                        }
                    }
                }
                key = productId[0] + "_inStock";
                if (!appUtil.isEmptyObject($scope.expiries)) {
                    if (!appUtil.isEmptyObject($scope.expiries[productId[1]]) && !appUtil.isEmptyObject($scope.expiries[productId[1]]["stockAtHand"])) {
                        var inStock = $scope.expiries[productId[1]]["stockAtHand"];
                        if (!appUtil.isEmptyObject(inStock[productId[0]])) {
                            obj[key] = inStock[productId[0]];
                        }
                        if (!appUtil.isEmptyObject(inStock["totalStock"])) {
                            obj.total = inStock["totalStock"];
                        }
                    }
                }
                $scope.mapOfRows[productId[1]] = obj;
            }
        });

        $scope.displayRows = Object.values($scope.mapOfRows);
        console.log("Rows are : ", $scope.displayRows);

        $scope.close = function () {
            Popeye.closeCurrentModal();
        };
    }]);
