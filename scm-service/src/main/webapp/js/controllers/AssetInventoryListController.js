/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('assetInventoryListCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson',
            'appUtil', '$http', '$stateParams', 'productService','$alertService','Popeye', '$fileUploadService', '$toastService', 'metaDataService',
            function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, $http, $stateParams, productService,$alertService, Popeye, $fileUploadService, $toastService, metaDataService) {

                $scope.init = function () {
                    $scope.currentUser = appUtil.getCurrentUser().userId;
                    $scope.unitList = appUtil.unitList;
                    $scope.selectedUnitData = appUtil.getUnitData();
                    $scope.statusList = [];
                    $scope.statusListForFilter = [];
                    getAssetStatusList();
                    $scope.assetList = [];
                    $scope.groupedAssetList = [];
                    $scope.selectedStatus = "";
                     $scope.skus = [];
                    // console.log($scope.skus);
                    productService.getSkuList(function (skusByProduct) {

                        for(var i in skusByProduct){
                            for(var j in skusByProduct[i]){
                                $scope.skus.push(skusByProduct[i][j]);
                            }
                        }
                    });
                    console.log($scope.skus);
                    var allObj = getSkuObject();
                    console.log(allObj);
                    $scope.selectedSkuId = allObj.skuId;
                    $scope.skus.unshift(allObj);

                    // $scope.skus.sort(function(a,b) {
                    //     var name1 = a.skuName.toLowerCase();
                    //     var name2 = b.skuName.toLowerCase();
                    //     var result = name1 - name2;
                    //     console.log(result)
                    //     return result;
                    // })
                    console.log($scope.skus)
                };

                function getSkuObject() {
                    var allObj = {};
                    allObj.skuName = 'ALL';
                    allObj.skuId = -1;
                    return allObj;
                }


                function getAssetStatusList() {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.assetManagement.assetStatusTypeList,
                    }).then(function success(response) {
                        if(!appUtil.isEmptyObject(response.data)){
                            response.data.push("IN_TRANSIT");
                        }
                        $scope.statusList = response.data;
                        $scope.statusListForFilter = angular.copy(response.data);
                        $scope.statusListForFilter.unshift("ALL");
                        $scope.selectedStatus = 'ALL';
                        $scope.getAsset();
                    }, function error(response) {
                        console.log("Encountered an error",response);
                        $toastService.create('Error getting asset status type contact support');
                    });
                }

                function setInTransit(assetList){
                    for(var i = 0 ;i<assetList.length;i++){
                        if(assetList[i].inTransit === true){
                            assetList[i].assetStatus = "IN_TRANSIT";
                        }
                    }
                    return assetList;
                }

                $scope.getAsset = function () {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.assetManagement.getAssetWithUnit,
                        params: {
                            unitId : $scope.selectedUnitData.id,
                        }
                    }).then(function success(response) {
                        $scope.assetList = setInTransit(response.data);
                        groupBySkuId();
                    }, function error(response) {
                        console.log("Encountered an error",response);
                        $toastService.create('Error getting asset status list for given unit, contact support');
                    });
                }

                $scope.resetAssetCacheForID = function(assetId) {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.assetManagement.resetAssetCache,
                        data: assetId
                    }).then(function success(response) {
                        console.log("RES{PONSE : ", response);
                        $toastService.create("ASSET CACHE REFRESHED SUCCESSFULLY ");
                    }, function error(response) {
                        console.error("Encountered an error",response);
                        $toastService.create('Error resetting asset cache ');
                    });
                }

                $scope.statusFilter = function(asset) {
                        if($scope.selectedStatus == 'ALL'){
                            return asset;
                        }
                        if(asset.assetStatus == $scope.selectedStatus){
                            return asset
                        }

                }

                $scope.skuFilter = function(obj){

                    if($scope.selectedSkuId == -1){
                        return obj;
                    }
                    if($scope.selectedSkuId == obj.skuId){
                        return obj;
                    }
                }

                $scope.getCount = function(status) {
                    var count = 0;
                    $scope.assetList.map(function(mapping) {
                        if(status == mapping.assetStatus )
                            count++;
                    })
                    return count;
                }

                function groupBySkuId(){
                    $scope.groupedAssetList = [];
                    var skuList = [];
                    for(var i in $scope.assetList){
                        var isPresent = skuList.find(function(skuId) {
                            return $scope.assetList[i].skuId == skuId;
                        });
                        if(skuList.indexOf($scope.assetList[i].skuId) < 0){
                            var groupedByAssetListObj = getGroupedByAssetListObj();
                            groupedByAssetListObj.skuId = $scope.assetList[i].skuId;
                            groupedByAssetListObj.details.skuName = $scope.assetList[i].assetName;
                            var count = groupedByAssetListObj.details[$scope.assetList[i].assetStatus]
                            groupedByAssetListObj.details[$scope.assetList[i].assetStatus] = count + 1;
                            groupedByAssetListObj.details.total++;
                            groupedByAssetListObj.details.assetList.push($scope.assetList[i]);
                            $scope.groupedAssetList.push(groupedByAssetListObj);
                            skuList.push($scope.assetList[i].skuId);
                        } else {
                            for(var j in $scope.groupedAssetList){
                                if($scope.groupedAssetList[j].skuId == $scope.assetList[i].skuId){
                                    $scope.groupedAssetList[j].details.total++;
                                    $scope.groupedAssetList[j].details[$scope.assetList[i].assetStatus]++;
                                    $scope.groupedAssetList[j].details.assetList.push($scope.assetList[i]);
                                }
                            }
                        }
                    }

                }

                function getGroupedByAssetListObj(){
                    var obj = {};
                    obj.skuId = null;
                    var innerObj = {};
                    innerObj.skuName = "";
                    $scope.statusList.map(function(status){
                        innerObj[status] = 0;
                    });
                    innerObj.total = 0;
                    innerObj.assetList = [];
                    obj.details = innerObj;
                    return obj;
                }

                $scope.setFilter = function(status) {
                    $scope.selectedStatus = status;
                }

                $scope.countFilter = function(obj) {
                    if($scope.selectedStatus == 'ALL'){
                        return obj;
                    }
                    if(obj.details[$scope.selectedStatus] > 0){
                        return obj;
                    }
                }

            }
        ]
    )
