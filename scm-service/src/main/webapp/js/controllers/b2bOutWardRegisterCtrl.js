angular.module('scmApp').controller('b2bOutWardRegisterCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http', '$state', 'appUtil',
    '$toastService', '$alertService', 'metaDataService', '$fileUploadService', '$window', 'previewModalService', '$timeout',
    function ($rootScope, $stateParams, $scope, apiJson, $http, $state, appUtil, $toastService, $alertService, metaDataService, $fileUploadService,
              $window, previewModalService, $timeout) {

    function getCreatedInvoiceForSelectedUnitAndToday(view, startDate, endDate) {
        if(appUtil.isEmptyObject(startDate))
        {
            $toastService.create("Please select a start date first");
            return;
        }
        if (appUtil.isEmptyObject(endDate)) {
            $toastService.create("Please select a end date first");
            return;
        }
        var unitId = appUtil.isEmptyObject($scope.selectedUnit) ? $scope.currentUser.unitId : $scope.selectedUnit;
        var params = {
            sendingUnit: unitId,
            isView: view,
            // startDate: "2023-01-12" ,//new Date().toJSON().slice(0, 10),
            // endDate: "2023-01-12" //new Date().toJSON().slice(0, 10),
            startDate: startDate,
            endDate: endDate
        };
        if (!appUtil.isEmptyObject($scope.selectedStatus)) {
            params["status"] = $scope.selectedStatus;
        }
        if (!appUtil.isEmptyObject($scope.vendorSelected)) {
            params["vendorId"] = $scope.vendorSelected.vendorId;
        }
        if (!appUtil.isEmptyObject($scope.locationSelected)) {
            params["dispatchId"] = $scope.locationSelected.dispatchId;
        }
        $http({
            method: "GET",
            url: apiJson.urls.invoiceManagement.getInvoices,
            params: params
        }).then(function (response) {
            if (appUtil.isEmptyObject(response)) {
                $toastService.create("No Orders Found for Today");
            } else {
                $scope.invoiceRequest = response.data;
                $scope.invoiceRequest = $scope.invoiceRequest.sort(function (a, b) {
                    return b.id - a.id;
                });
            }
        }, function (e) {
            console.log("Cannot get Invoice for Today and Selected Unit", e);
        });
    }


    $scope.init = function () {
        $scope.unitData = appUtil.getUnitData();
        $scope.companyMap = appUtil.getCompanyMap();
        var currentDate = appUtil.getCurrentBusinessDate();
        if (!appUtil.isEmptyObject(currentDate)) {
            $scope.selectedUnit = null;
            // $scope.txnStatus = ["PENDING_DISPATCH", "DELIVERED", "CLOSED"];
            // $scope.txnTypes = ["SCRAP", "RETURN_TO_VENDOR", "B2B_SALES", "ECOM"];
            $scope.startDate = new Date().toJSON().slice(0, 10);
            $scope.endDate = new Date().toJSON().slice(0, 10);
            $stateParams.viewInvoice = true;
            $scope.showViewActions = $stateParams.viewInvoice;
            $scope.invoiceRequest = [];
            $scope.createdInvoice = $stateParams.createdInvoice;
            $scope.selectedUnit = null;
            $scope.vendorSelected = null;
            $scope.locationSelected = null;
            $scope.currentUser = appUtil.getCurrentUser();
            $scope.selectedStatus = null;
            $scope.showPreview = previewModalService.showPreview;
            $scope.initTime = new Date().toLocaleTimeString().slice(0,4) +" "+ new Date().toLocaleTimeString().slice(8,10);
            $scope.initDate = new Date().toJSON().slice(0, 10);
            getUnits();
            getCreatedInvoiceForSelectedUnitAndToday($scope.showViewActions, $scope.startDate, $scope.endDate);
            $scope.formResponse =  {
                serialNumber : null,
                date : null,
                challanNo : null,
                time : null,
                addressOfBuyer : null,
                detailsOfArticle : null,
                quantity : null,
                amount : null,
                nameOfDeliverer : null ,
                signOfDeliverer : null,
                vehicleNoType : null,
                signatureOfSecurity :null,
                remarks : null,
                invoiceId : null,
                buisnessType : null
            }

        }
    };


    function getUnits() {
        $http({
            method: 'GET', url: apiJson.urls.unitMetadata.allUnitsList
        }).then(function (response) {
            if (response.data != null) {
                var units = response.data;
                $scope.units = units.filter(function (unit) {
                    return (unit.category == "WAREHOUSE" || unit.category == "KITCHEN") && unit.status == "ACTIVE";
                });
            }
        }, function (err) {
            console.log("Error in getting Unit List", err);
        });
    }

    $scope.selectInvoice = function (invoice){
        $scope.selectedInvoice  = invoice;

    }

    $scope.setNewEntry = function (invoiceRequest) {
        /*data={
            serialNumber : serialNo,
            date : rdate,
            challanNo : challanId,
            time : rtime,
            addressOfBuyer : senderName,
            detailsOfArticle : articleName,
            quantity : rquantity,
            Amount : amount,
            nameOfDeliverer : deliveryBoyName ,
            signOfDeliverer : designation,
            vehicleNoType : noPlate,
            signatureOfSecurity :securitySign,
            remarks : remarks
        }*/
        // angular.forEach($scope.selectedItem,function(id){
        //     if(id===)
        // })
        // $scope.selectedItem=invoiceRequest.id;
        // $scope.formResponse.buisnessType = $scope.selectedItem.buisnessType;
        // $scope.formResponse.invoiceId = $scope.selectedItem;
        $scope.formResponse.buisnessType = $scope.selectedInvoice.type;
        $scope.formResponse.invoiceId = $scope.selectedInvoice.id;
        // i++;
        $http({
            method: 'POST',
            url:apiJson.urls.invoiceManagement.saveOutwardRegisterEntry,
            data:$scope.formResponse,
        }).then(function success(response){
            if(response.data){
                var entry = response.data;
            }
        }, function failure(err){
            console.log("Error in saving forms data in Database",err);
        })
    }
        $scope.openClicked=function (invId){
        $scope.selectedItem= invId;
        }
    $scope.getInvoices = function () {
        getCreatedInvoiceForSelectedUnitAndToday($scope.showViewActions, $scope.startDate, $scope.endDate);
    };

    $scope.openForm=function (invoice) {
        $scope.selectedItem=invoice.id;
        if (invoice.expanded != null) {
            invoice.expanded = !invoice.expanded;
        } else {
            invoice.expanded = true;
        }
        if(invoice.expanded === false){
            $scope.selectedItem = null;
        }
    }

    $scope.closeForm=function (invoice){
        invoice.expanded=false;
    }

    $scope.closeModal=function(modal){
        $timeout(function (modal) {
            $('#modal').val('').trigger('change');
        });
    }
}]);