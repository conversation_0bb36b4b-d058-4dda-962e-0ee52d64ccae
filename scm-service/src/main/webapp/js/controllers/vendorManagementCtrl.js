/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('vendorManagementCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', '$state', '$alertService', 'metaDataService',
        '$fileUploadService','Popeye','$timeout',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, $state, $alertService, metaDataService, $fileUploadService, Popeye, $timeout) {

            $scope.init = function () {
                $scope.currentUserId = appUtil.getCurrentUser().userId;
                $scope.getVendorStatus();
                $scope.currentStatus = null;
                $scope.filterBlocked = false;
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.searchText = {text: ""};
                $scope.paymentCycle = [
                    {id: "DAILY", name: "Daily"},
                    {id: "WEEKLY", name: "Weekly"},
                    {id: "FORTNIGHTLY", name: "Fortnightly"},
                    {id: "MONTHLY", name: "Monthly"}
                ];

                $scope.showViewRequest();

            };

            $scope.changeSearchText = function (searchText) {
              $scope.searchText.text = searchText;
            };

            $scope.byEntityName = function (vendor) {
                if($scope.searchText.text.trim().length <= 3){
                    return true;
                }else{
                    return vendor.entityName.toLowerCase().indexOf($scope.searchText.text.toLowerCase()) != -1;
                }
            };

            $scope.setFilterBlocked = function (block) {
                $scope.filterBlocked = block;
            };

            $scope.byBlockStatus = function (vendor) {
                  if ($scope.filterBlocked) {
                      if (vendor.vendorBlocked == 'Y') {
                          return true;
                      }
                  } else {
                      return true;
                  }
            };

            $scope.getVendorStatus = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendorStatus
                }).then(function success(response) {
                    $scope.vendorStatusList = response.data;
                    $scope.vendorStatusList.push("ALL");
                    if(!appUtil.isEmptyObject($scope.vendorStatusList)){
                        var excluded = ["APPROVED","FAILED","CANCELLED","EXPIRED"];
                        $scope.vendorStatusList = $scope.vendorStatusList.filter(function(status){
                            return excluded.indexOf(status)==-1;
                        });
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            // $scope.getVendors = function (status) {
            // 	$scope.currentStatus = status;
            //     $http({
            //         method: "POST",
            //         url: apiJson.urls.vendorManagement.vendorByStatus + '/' + status
            //     }).then(function success(response) {
            //         $scope.vendorList = response.data;
            //         $scope.changeSearchText("");
            //     }, function error(response) {
            //         console.log("error:" + response);
            //     });
            // };
            $scope.getSelectedVendor = function (selectedVendor) {
                if (selectedVendor != null) {
                    $scope.vendorList = [];
                    $scope.vendorSelected = selectedVendor;
                    $http({
                        method: "POST",
                        url: apiJson.urls.vendorManagement.vendorDetails,
                        params: {
                            vendorId: selectedVendor.id
                        }
                    }).then(function success(response) {
                        $scope.vendorDetail = response.data;
                    $scope.vendorList = [];
                        $timeout(function () {
                            $('#selectedStatus').val('').trigger('change');
                        });
                        $scope.changeSearchText("");
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                } else {
                    $scope.vendorDetail = null;
                    $scope.vendorSelected = null;
                    $timeout(function () {
                        $('#selectedStatus').val('').trigger('change');
                    });
                }
            };


            $scope.showViewRequest = function () {
                var url = apiJson.urls.vendorManagement.allVendorName
                $http({
                    url: url,
                    method: 'GET'
                }).then(function (response) {
                    console.log(response.data);
                    $scope.vendors = response.data;
                    $scope.activateVendors = [];
                    for (var i = 0; i < response.data.length; i++) {
                        if(response.data[i].status === "ACTIVE") {
                            $scope.activateVendors.push(response.data[i]);
                        }
                    }
                }, function (response) {
                    console.log("got error", response);
                });
            };

            $scope.getVendors = function (status) {
                if (status != null) {
                    $scope.vendorDetail = null;
                    $scope.vendorSelected = null;
                    $scope.currentStatus = status;
                    $http({
                        method: "POST",
                        url: apiJson.urls.vendorManagement.vendorByShort + '/' + status
                    }).then(function success(response) {
                        $scope.vendorList = response.data;
                        $scope.changeSearchText("");
                        $scope.currentPage = 1; // current page
                        $scope.entryLimit = 50; // max no of items to display in a page
                    $scope.vendorDetail = null;
                        $scope.vendorSelected = null;
                    }, function error(response) {
                    console.log("error:" + response);
                });}
            };
            $scope.editLeadTime = function (vendorId,LeadTime) {
                var reqobj = {
                    requestId: vendorId,
                    leadTime: LeadTime
                }
                $alertService.confirm("Are you sure?", "You are going to edit this vendor Lead Time. Please be sure!",
                    function (result) {
                        if (result) {
                            $http({
                                method: "POST",
                                url: apiJson.urls.vendorManagement.saveLeadTime,
                                data: reqobj
                            }).then(function success(response) {
                                $toastService.create("Lead Time is updated successfully");
                                //$scope.getVendors($scope.currentStatus)
                            }, function error(response) {
                                console.log("error:" + response);
                            });
                        }
                    });
            }
            $scope.editCreditCycle = function (companyId, creditCycle) {
                if (appUtil.isEmptyObject(companyId)) {
                    $toastService.create("Please Check the Company Id for this Vendor..!");
                    return;
                }
                if (appUtil.isEmptyObject(creditCycle)) {
                    $toastService.create("Please Check the Credit Cycle for this Vendor..!");
                    return;
                }
                $alertService.confirm("Are you sure?", "You are going to edit this vendor Credit Cycle. Please be sure!",
                    function (result) {
                        if (result) {
                            $http({
                                method: "GET",
                                url: apiJson.urls.vendorManagement.saveCreditCycle,
                                params: {
                                    vendorcompanyId: companyId,
                                    vendorCompCreditCycle: creditCycle
                                }
                            }).then(function success(response) {
                                $toastService.create("Credit Cycle is updated successfully");
                                //$scope.getVendors($scope.currentStatus)
                            }, function error(response) {
                                console.log("error:" + response);
                            });
                        }
                    });
            };

            $scope.sort_by = function (predicate) {
                $scope.predicate = predicate;
                $scope.reverse = !$scope.reverse;
            };

            $scope.isTdsApplicable = function (value, index) {
                $scope.vendorList[index].tds = value;
                if (!value == 'true') {
                    $scope.vendorList[index].tdsDocument = null;
                }
            };

            $scope.uploadTDS = function (index) {
                console.log(index);
                $fileUploadService.openFileModal("Upload TDS scan copy or pdf", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }

                    var fileName = file.name;
                    var fileExt = getFileExtension(fileName);
                    if (fileExt.toLowerCase() == 'pdf' || isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "TDS");
                        fd.append('docType', "VENDOR_TDS");
                        fd.append('mimeType', mimeType);
                        fd.append('vendorId', $scope.vendorList[index].vendorId);
                        fd.append('file', file);
                        fd.append('tdsStatus',$scope.vendorList[index].tds);
                        console.log($scope.vendorList[index].vendorId);
                        $rootScope.showFullScreenLoader = true;
                        $http.post(apiJson.urls.vendorManagement.uploadTdsDocument, fd, {
                            transformRequest: angular.identity,
                            headers: {
                                'Content-Type': undefined
                            }
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!$scope.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.vendorList[index].tdsDocument = response;
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                });
            };

            function isImage(fileExt) {
                return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png";
            };

            function getFileExtension(fileName) {
                var re = /(?:\.([^.]+))?$/;
                return re.exec(fileName)[1];
            }

            $scope.isEmptyObject = function (obj) {
                if (obj != undefined && obj != null) {
                    if (typeof obj == 'string' || typeof obj == 'number')
                        return obj.toString().length == 0;
                    else
                        return Object.keys(obj).length == 0;
                }
                return true;
            };


            $scope.viewVendor = function (vendor,mode) {
                $state.go('menu.viewVendor', {edit: true, vendor: vendor, mode: mode});
            };

            $scope.sendRONotification = function (){
                console.log("inside send Ro notification");
                $http({
                    method: "GET",
                    url: apiJson.urls.requestOrderManagement.sendVendorRONotification,
                }).then(function success(response) {
                        $toastService.create("Notification sent successfully");
                }, function (error) {
                    console.log(error);
                    $toastService.create("Error in sending notifications");
                })
            }

            $scope.validateVendorCompliance = function () {
                var validateVendorComplianceModal = Popeye.openModal({
                    templateUrl: "validateVendorCompliance.html",
                    controller: "validateVendorComplianceCtrl",
                    modalClass: 'custom-modal',
                    resolve: {
                        vendorDetails : function () {
                            return $scope.activateVendors;
                        }
                    },
                    click: false,
                    keyboard: false
                });

                validateVendorComplianceModal.closed.then(function (result) {
                    if (result) {
                        if ($scope.vendorList.length > 0) {
                            $scope.getVendors($scope.selectedStatus);
                        } else {
                            $scope.getSelectedVendor($scope.vendorSelected);
                        }
                    }
                });
            };

            $scope.showEditVendor = function (vendor) {
                $state.go('menu.editVendor', {edit: true, vendor: vendor});
            };

            $scope.activateVendor = function () {
                $http({
                    method: "PUT",
                    url: apiJson.urls.vendorManagement.vendorActivate,
                    data: $scope.vendorToEdit.vendorId
                }).then(function success(response) {
                    if (response.data == true) {
                        $scope.vendorToEdit.vendorStatus = "ACTIVE";
                        $scope.vendorList.forEach(function (item) {
                            if (item.vendorId == $scope.vendorToEdit.vendorId) {
                                item = $scope.vendorToEdit;
                            }
                        });
                    } else {
                        $toastService.create("Something went wrong. Please try again.");
                    }
                }, function error(response) {
                    console.log("error:", response);
                });
            };

            $scope.deActivateVendor = function () {
                $http({
                    method: "PUT",
                    url: apiJson.urls.vendorManagement.vendorDeactivate,
                    data: $scope.vendorToEdit.vendorId
                }).then(function success(response) {
                    if (response.data == true) {
                        $scope.vendorToEdit.vendorStatus = "IN_ACTIVE";
                        $scope.vendorList.forEach(function (item) {
                            if (item.vendorId == $scope.vendorToEdit.vendorId) {
                                item = $scope.vendorToEdit;
                            }
                        });
                    } else {
                        $toastService.create("Something went wrong. Please try again.");
                    }
                }, function error(response) {
                    console.log("error:", response);
                });
            };

            $scope.openBlockUnblockVendorModal = function (vendorDetail, blockType) {
                $scope.pendingOrdersToBlockVendor = null;
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getPendingOrdersToBlockVendor,
                    params: {
                        vendorId: vendorDetail.vendorId
                    }
                }).then(function success(response) {
                    $scope.pendingOrdersToBlockVendor = response.data;
                    var blockUnBlockVendorModal = Popeye.openModal({
                        templateUrl: "blockVendorModal.html",
                        controller: "blockVendorModalCtrl",
                        modalClass: 'custom-modal',
                        resolve: {
                            vendor: function () {
                                return vendorDetail;
                            },
                            pendingOrders : function () {
                                return angular.copy($scope.pendingOrdersToBlockVendor);
                            },
                            blockType: function () {
                                return blockType;
                            }
                        },
                        click: false,
                        keyboard: false
                    });

                    blockUnBlockVendorModal.closed.then(function (result) {
                        if (result) {
                            if ($scope.vendorList.length > 0) {
                                $scope.getVendors($scope.selectedStatus);
                            } else {
                                $scope.getSelectedVendor($scope.vendorSelected);
                            }
                        }
                    });
                }, function error(response) {
                    $toastService.create("Error Occurred While getting the pending orders to block vendor..!");
                    $scope.pendingOrdersToBlockVendor = null;
                });
            };

            $scope.openAdvanceUnblockVendorModal = function (vendorDetail) {
                var unBlockVendorModal = Popeye.openModal({
                    templateUrl: "unBlockVendorModal.html",
                    controller: "unBlockVendorModalCtrl",
                    modalClass: 'custom-modal',
                    resolve: {
                        vendor: function () {
                            return vendorDetail;
                        },
                        pendingOrders : function () {
                            return angular.copy($scope.pendingOrdersToBlockVendor);
                        }
                    },
                    click: false,
                    keyboard: false
                });

                unBlockVendorModal.closed.then(function (result) {
                    if (result) {
                        $scope.getVendors($scope.selectedStatus);
                    }
                });
            };

        }]).controller('unBlockVendorModalCtrl', ['$scope', 'vendor', 'Popeye', '$http', 'apiJson', 'appUtil','$alertService','$toastService',
    function ($scope, vendor, Popeye, $http, apiJson, appUtil,$alertService,$toastService) {

        $scope.minDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
        $scope.vendor = vendor;
        $scope.unblockTillDate = null;

        $scope.setUnblockTillDate = function (date) {
            $scope.unblockTillDate = date;
        };

        $scope.submitUnblockTillDate = function (date) {
            $http({
                method: "POST",
                url: apiJson.urls.vendorManagement.updateUnblockTillDate,
                params: {
                    "vendorId" : $scope.vendor.vendorId,
                    "unblockTillDate" : $scope.unblockTillDate,
                    "updatedBy": appUtil.getCurrentUser().userId
                }
            }).then(function success(response) {
                if (response != null && response.data != null) {
                    if (response.data) {
                        $scope.close(true);
                    } else {
                        $toastService.create("Something went wrong while Unblocking the Vendor. Please try again...!");
                    }
                }
            }, function error(response) {
                console.log("error:" + response);
                $toastService.create("Something went wrong while Unblocking the Vendor. Please try again...!");
            });
        };

        $scope.close = function (val) {
            Popeye.closeCurrentModal(val);
        };

    }]).controller('blockVendorModalCtrl', ['$scope', 'vendor','pendingOrders','blockType', 'Popeye', '$http', 'apiJson', 'appUtil','$alertService','$toastService',
    function ($scope, vendor,pendingOrders,blockType, Popeye, $http, apiJson, appUtil,$alertService,$toastService) {

        $scope.blockType = blockType;
        $scope.gridPoOptions = appUtil.getGridOptions($scope);
        $scope.gridSoOptions = appUtil.getGridOptions($scope);
        $scope.selectedRow = null;

        $scope.pendingOrdersToBlockVendor = angular.copy(pendingOrders);

        $scope.gridPoColumns = function () {
            return [{
                field: 'poSoId',
                displayName: 'PO Id'
            }, {
                field: 'poSoStatus',
                displayName: 'PO Status'
            }, {
                field: 'poSoType',
                displayName: 'PO Type',
            }, {
                field: 'poSoCreateDate',
                type: 'date',
                displayName: 'PO Created Date',
                cellFilter: 'date:\'yyyy-MM-dd:HH:mm:ss\''
            }, {
                field: 'poSoCreatedBy',
                displayName: 'PO Created By'
            },  {
                name: 'vendorBlockGrSrs',
                enableCellEdit: false,
                displayName: 'PO-GR-PR\'s',
                cellTemplate: 'pendingPoGr.html',
            }
            ]
        };

        $scope.gridSoColumns = function () {
            return [{
                field: 'poSoId',
                displayName: 'SO Id'
            }, {
                field: 'poSoStatus',
                displayName: 'SO Status'
            }, {
                field: 'poSoType',
                displayName: 'SO Type',
            }, {
                field: 'poSoCreateDate',
                type: 'date',
                displayName: 'SO Created Date',
                cellFilter: 'date:\'yyyy-MM-dd:HH:mm:ss\''
            }, {
                field: 'poSoCreatedBy',
                displayName: 'SO Created By'
            },  {
                name: 'vendorBlockGrSrs',
                enableCellEdit: false,
                displayName: 'SO-SR-PR\'s',
                cellTemplate: 'pendingSoSr.html',
            }
            ]
        };

        function setColumns(poSoData,orderType) {
            angular.forEach(poSoData, function (poSo) {
                poSo.orderType = orderType;
            });
            return poSoData;
        }

        $scope.closeDetails = function () {
            $scope.selectedRow = null;
        };

        $scope.gridPoOptions.columnDefs = $scope.gridPoColumns();
        $scope.gridSoOptions.columnDefs = $scope.gridSoColumns();
        $scope.gridPoOptions.data = setColumns($scope.pendingOrdersToBlockVendor.details["PURCHASE_ORDER"], 'PURCHASE_ORDER');
        $scope.gridSoOptions.data = setColumns($scope.pendingOrdersToBlockVendor.details["SERVICE_ORDER"],'SERVICE_ORDER');


        $scope.gridSoSrPrColumns = function () {
            return [{
                field: 'grSrId',
                displayName: 'SR Id'
            }, {
                field: 'grSrStatus',
                displayName: 'SR Status'
            }, {
                field: 'prId',
                displayName: 'PR Id',
            },{
                field: 'prStatus',
                displayName: 'PR Status',
            }, {
                field: 'prCreatedBy',
                displayName: 'PR Created By'
            }, {
                field: 'prCreatedAt',
                type: 'date',
                displayName: 'PR Created Date',
                cellFilter: 'date:\'yyyy-MM-dd:HH:mm:ss\''
            }
            ]
        };

        $scope.gridPoGrPrColumns = function () {
            return [{
                field: 'grSrId',
                displayName: 'GR Id'
            }, {
                field: 'grSrStatus',
                displayName: 'GR Status'
            }, {
                field: 'prId',
                displayName: 'PR Id',
            }, {
                field: 'prStatus',
                displayName: 'PR Status',
            }, {
                field: 'prCreatedBy',
                displayName: 'PR Created By'
            }, {
                field: 'prCreatedAt',
                type: 'date',
                displayName: 'PR Created Date',
                cellFilter: 'date:\'yyyy-MM-dd:HH:mm:ss\''
            }
            ]
        };

        $scope.setRow = function (row, orderType) {
            $scope.gridGrSrPrOptions = appUtil.getGridOptions($scope);
            $scope.selectedRow = row;
            if (orderType == 'PURCHASE_ORDER') {
                $scope.gridGrSrPrOptions.columnDefs = $scope.gridPoGrPrColumns();
                $scope.gridGrSrPrOptions.data = $scope.selectedRow.vendorBlockGrSrs;
            } else {
                $scope.gridGrSrPrOptions.columnDefs = $scope.gridSoSrPrColumns();
                $scope.gridGrSrPrOptions.data = $scope.selectedRow.vendorBlockGrSrs;
            }
        };

        $scope.vendor = vendor;
        $scope.unblockTillDate = null;

        $scope.setUnblockTillDate = function (date) {
            $scope.unblockTillDate = date;
        };

        $scope.blockUnblockVendor = function () {
            var blockOrUnblock = blockType == 'BLOCK' ? true : false;
            $http({
                method: "POST",
                url: apiJson.urls.vendorManagement.blockUnblockVendor,
                params: {
                    "vendorId" : $scope.vendor.vendorId,
                    "updatedBy": appUtil.getCurrentUser().userId,
                    "isBlock" : blockOrUnblock
                }
            }).then(function success(response) {
                if (response != null && response.data != null) {
                    if (response.data) {
                        $scope.close(true);
                    } else {
                        $toastService.create("Something went wrong while"+ (blockOrUnblock ? "BLOCK" : "UN BLOCK") + " the Vendor. Please try again...!");
                    }
                }
            }, function error(response) {
                console.log("error:" + response);
                $toastService.create("Something went wrong while "+ (blockOrUnblock ? "BLOCK" : "UN BLOCK") + "  the Vendor. Please try again...!");
            });
        };

        $scope.submitUnblockTillDate = function (date) {
            $http({
                method: "POST",
                url: apiJson.urls.vendorManagement.updateUnblockTillDate,
                params: {
                    "vendorId" : $scope.vendor.vendorId,
                    "unblockTillDate" : $scope.unblockTillDate,
                    "updatedBy": appUtil.getCurrentUser().userId
                }
            }).then(function success(response) {
                if (response != null && response.data != null) {
                    if (response.data) {
                        $scope.close(true);
                    } else {
                        $toastService.create("Something went wrong while Unblocking the Vendor. Please try again...!");
                    }
                }
            }, function error(response) {
                console.log("error:" + response);
                $toastService.create("Something went wrong while Unblocking the Vendor. Please try again...!");
            });
        };

        $scope.close = function (val) {
            Popeye.closeCurrentModal(val);
        };

    }]).controller('validateVendorComplianceCtrl', ['$scope', 'vendorDetails', 'Popeye', '$http', 'apiJson', 'appUtil','$alertService','$toastService',
    function ($scope, vendorDetails, Popeye, $http, apiJson, appUtil,$alertService,$toastService) {

        $scope.forceRetry = "NO";
        $scope.vendorDetails = vendorDetails;
        $scope.selectedVendor = [];
        $scope.selectedComplianceType = [];
        $scope.multiSelectSettingVendor = {
            enableSearch: true, template: '<b> {{option.name}}</b>', scrollable: true,
            scrollableHeight: '250px',clearSearchOnClose: true
        };
        $scope.multiSelectSettingCompliance = {
            enableSearch: true, template: '<b> {{option}}</b>', scrollable: true,
            scrollableHeight: '250px',clearSearchOnClose: true
        };
        $scope.complianceTypes = ["GSTIN_VERIFICATION","GST_RETURN","SECTION_206"];

        $scope.setForceRetry = function (retry) {
            $scope.forceRetry = retry;
        };

        $scope.checkVendorCompliance = function () {
            var vendorIds = [];
            for (var i = 0; i < $scope.selectedVendor.length;i++) {
                vendorIds.push($scope.selectedVendor[i].id);
            }
            var compliances = [];
            for (var i = 0; i < $scope.selectedComplianceType.length;i++) {
                compliances.push($scope.selectedComplianceType[i]);
            }
            $http({
                method: "POST",
                url: apiJson.urls.vendorManagement.validateVendorCompliances,
                data: {
                    "vendorIds" : vendorIds,
                    "complianceTypes" : compliances,
                    "forceRetry" : $scope.forceRetry === "NO" ? false : true
                }
            }).then(function success(response) {
                if (response != null && response.data != null) {
                    if (response.data) {
                        $scope.close();
                        $toastService.create("Vendor Compliance Validated Successfully..!");
                    } else {
                        $toastService.create("Something went wrong while Validating Compliance Of Vendor. Please try again...!");
                    }
                }
            }, function error(response) {
                console.log("error:" + response);
                $toastService.create("Something went wrong while Validating Compliance Of Vendor. Please try again...!");
            });
        };

        $scope.close = function () {
            Popeye.closeCurrentModal();
        };

    }]);

angular.module('scmApp').filter('startFrom', function () {
    return function (input, start) {
        if (input) {
            start = +start; // parse to int
            return input.slice(start);
        }
        return [];
    };
});
