'use strict';
angular.module('scmApp').controller('viewCapexPoSoCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http',
    'appUtil', '$toastService', '$alertService', 'metaDataService', 'Popeye', '$window', '$timeout', 'uiGridConstants',
    function ($rootScope, $stateParams, $scope, apiJson, $http, appUtil, $toastService, $alertService, metaDataService, Popeye, $window, $timeout, $uiGridConstants) {
        $scope.init = function () {
            $scope.capexRequestId = null;
            $scope.summaryDepartmentList = [];
            $scope.capexSummaryShow = false;
            $scope.capexSummaryLoading = false;
            $scope.gridOptions = appUtil.getGridOptions($scope);
            $scope.emptyMsg = false;
        }

        $scope.changeCapexId = function(capexId) {
            $scope.capexRequestId = capexId;
            console.log("CapexId : " , $scope.capexRequestId);
        }

        $scope.fetchBudgetSummary = function () {
            $scope.poSoAmountDetails = null;
            $scope.summaryDepartmentList = [];
            $scope.capexSummaryShow = false;
            $scope.capexSummaryLoading = true;
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.fetchBudgetDetails,
                params: {
                    capexRequestId: $scope.capexRequestId
                }
            }).then(function success(response) {
                if (response) {
                    if (response != null && response.data != null && response.data.length > 0) {
                        $scope.summaryDepartmentList = response.data.filter(function (el) { return (el.totalSO != 0 || el.totalPO != 0) });
                        if($scope.summaryDepartmentList.length != 0) {
                            $scope.capexSummaryShow = true;
                            $scope.emptyMsg = false;
                        } else {
                            $scope.emptyMsg = true;
                        }
                    } else if (response.data.length == 0) {
                        $scope.emptyMsg = true;
                        $toastService.create("No Orders found!");
                    }
                } else {
                    $scope.emptyMsg = true;
                    $toastService.create("Error in showing Capex Summary.");
                }
                $scope.getSoPoSummary();
                $scope.capexSummaryLoading = false;
            }, function error(response) {
                console.log("error:" + response);
                $scope.emptyMsg = false;
                $scope.capexSummaryLoading = false;
                $toastService.create("Error while getting Capex Summary.");
            });
        }

        $scope.getSoPoSummary = function () {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getSoPoLevelSummary,
                params: {
                    capexId: $scope.capexRequestId
                }
            }).then(function success(response) {
                $scope.poSoAmountDetails = response.data;
            }), function error(response) {
                console.log("error : ", response);
            }
        }

        $scope.showSOLevelDetail = function (item) {
            if (item.expanded) {
                item.expanded = !item.expanded;
                $scope.selectedItem = null
                return;
            }
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getSoLevelSummaryByCapex,
                params: {
                    capexId: $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    if (response.data.length == 0) {
                        $toastService.create("No Data Found");
                        return;
                    }
                    $scope.soList = response.data;
                    $scope.selectedItem = item.departmentName + "_SO";
                    if (item.expanded != null) {
                        item.expanded = !item.expanded;
                    } else {
                        item.expanded = true;
                    }
                    var gridItemaArr = [];
                    $scope.soList.forEach(function (so) {
                        gridItemaArr.push(so);
                    });
                    if (item.expanded === true) {
                        $scope.gridOptions = {
                            showColumnFooter: true
                        };
                        $scope.gridOptions.data = gridItemaArr;
                        $scope.gridOptions.columnDefs = $scope.showSOLevel();
                    }
                    if (item.expanded === false) {
                        $scope.selectedItem = null;
                    }
                }
            })
        }

        $scope.isSoSelected = function (name) {
            return ($scope.selectedItem == name + "_SO");
        }

        $scope.isPoSelected = function (name) {
            return ($scope.selectedItem == name + "_PO");
        }

        $scope.isPoOrSoSelected = function (name) {
            return ($scope.selectedItem == name + "_PO" || $scope.selectedItem == name + "_SO");
        }

        $scope.showPOLevelDetail = function (item) {
            if (item.expanded) {
                item.expanded = !item.expanded;
                $scope.selectedItem = null
                return;
            }
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getPOLevelSummaryByCapex,
                params: {
                    capexId: $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    if (response.data.length == 0) {
                        $toastService.create("No Data Found");
                        return;
                    }
                    $scope.poList = response.data;
                    $scope.selectedItem = item.departmentName + "_PO";
                    if (item.expanded != null) {
                        item.expanded = !item.expanded;
                    } else {
                        item.expanded = true;
                    }
                    var gridItemaArr = [];
                    $scope.poList.forEach(function (po) {
                        gridItemaArr.push(po);
                    });
                    if (item.expanded === true) {
                        $scope.gridOptions = {
                            showColumnFooter: true
                        };
                        $scope.gridOptions.data = gridItemaArr;
                        $scope.gridOptions.columnDefs = $scope.showPOLevel();
                    }
                    if (item.expanded === false) {
                        $scope.selectedItem = null;
                    }
                }
            });

        }

        $scope.showPOLevel = function () {
            return [
                {
                    field: 'id',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'PO Id'
                },
                , {
                    field: 'dispatchLocation.vendorDetail.name',
                    name: 'dispatchLocation.vendorDetail.name',
                    enableCellEdit: false,
                    displayName: 'Vendor Name'
                }, {
                    field: 'billAmount',
                    name: 'billAmount',
                    enableCellEdit: false,
                    displayName: 'Cost',
                    cellFilter: 'number: 2'
                }, {
                    field: 'totalTaxes',
                    name: 'totalTaxes',
                    enableCellEdit: false,
                    displayName: 'Tax',
                    cellFilter: 'number: 2'
                },
                {
                    field: 'paidAmount',
                    name: 'paidAmount',
                    enableCellEdit: false,
                    displayName: 'Total Amount',
                    cellFilter: 'number: 2',
                    aggregationType: $uiGridConstants.aggregationTypes.sum

                },
                {
                    field: 'prAmount',
                    name: 'prAmount',
                    displayName: 'PR Amount',
                    enableCellEdit: false,
                    cellFilter: 'number: 2'
                },
                {
                    field: 'status',
                    name: 'status',
                    enableCellEdit: false,
                    displayName: 'Status',
                }
            ];
        };

        $scope.showSOLevel = function () {
            return [
                {
                    field: 'id',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'SO Id'
                },
                , {
                    field: 'vendor.name',
                    name: 'vendor.name',
                    enableCellEdit: false,
                    displayName: 'Vendor Name'
                }, {
                    field: 'totalCost',
                    name: 'totalCost',
                    enableCellEdit: false,
                    displayName: 'Cost',
                    cellFilter: 'number: 2'
                }, {
                    field: 'totalTaxes',
                    name: 'totalTaxes',
                    enableCellEdit: false,
                    displayName: 'Tax',
                    cellFilter: 'number: 2'
                },
                {
                    field: 'totalAmount',
                    name: 'totalAmount',
                    enableCellEdit: false,
                    displayName: 'Total Amount',
                    cellFilter: 'number: 2',
                    aggregationType: $uiGridConstants.aggregationTypes.sum

                },
                {
                    field: 'prAmount',
                    name: 'prAmount',
                    displayName: 'PR Amount',
                    enableCellEdit: false,
                    cellFilter: 'number: 2',
                    aggregationType: $uiGridConstants.aggregationTypes.sum
                },
                {
                    field: 'status',
                    name: 'status',
                    enableCellEdit: false,
                    displayName: 'Status',
                }
            ];
        };

        $scope.exportDataToSheetForSO = function (item) {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getSOByDepartment,
                params: {
                    capexRequestId: $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    $scope.soList = response.data;

                    var soItemLevelData = [];
                    $scope.soList.forEach(function (so) {
                        so.orderItems.forEach(function (soItem) {
                            var receivingPrice = soItem.receivedQuantity * soItem.unitPrice;
                            soItem.receivingAmount = (receivingPrice + (receivingPrice * soItem.taxRate) / 100);
                            soItem.vendorName = so.vendorName;
                            soItem.status = so.status;
                            soItemLevelData.push(soItem);
                        })
                    });
                    $scope.downloadSoItemData(soItemLevelData);

                }
            });

        }

        $scope.exportDataToSheetForPO = function (item) {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getPOByDepartment,
                params: {
                    capexId: $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    $scope.downloadPoItemData(response.data);

                }
            });

        }

        $scope.downloadPoItemData =function(data) {
                    var filename = 'data'
                    var arrHeader = ["purchaseOrderId","purchaseOrderItemId","skuId","skuName","requestedQuantity","amountPaid","goodsReceivedId","goodsReceivedItemId","totalAmount","currentStatus"];

                    var csvData = $scope.ConvertToCSVForPO(data, arrHeader);
                    var blob = new Blob(['\ufeff' + csvData], { type: 'text/csv;charset=utf-8;' });
                    var dwldLink = document.createElement("a");
                    var url = URL.createObjectURL(blob);
                    var isSafariBrowser = navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1;
                    if (isSafariBrowser) {  //if Safari open in new window to save file with random filename.
                        dwldLink.setAttribute("target", "_blank");
                    }
                    dwldLink.setAttribute("href", url);
                    dwldLink.setAttribute("download", "POItemLevelData.csv");
                    dwldLink.style.visibility = "hidden";
                    document.body.appendChild(dwldLink);
                    dwldLink.click();
                    document.body.removeChild(dwldLink);
                }

        $scope.ConvertToCSVForPO = function(objArray, headerList) {

                    var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
                    var str = '';
                    var row = 'S.No,';

                    var headerNames = ["Purchase Order Id","Purchase Order Item Id","Sku Id",,"Sku Name","Requested Quantity","Amount Paid","Goods Received Id","Goods Received Item Id","Total Amount","Current Status"];
                    for (var head in headerNames) {
                        row += headerNames[head] + ',';
                    }
                    console.log(array);
                    row = row.slice(0, -1);
                    str += row + '\r\n';
                    for (var i = 0; i < array.length; i++) {
                        var line = (i + 1) + '';
                        for (var index in headerList) {
                            var head = headerList[index];
                            line += ',' + array[i][head];
                        }
                        str += line + '\r\n';
                    }
                    return str;
                };

        $scope.downloadSoItemData =function(data) {
                    var filename = 'data'
                    var arrHeader = ["serviceOrderId","costElementName","vendorName","serviceDescription","totalCost","totalTax","amountPaid","receivingAmount","paidAmount","status"];

                    var csvData = $scope.ConvertToCSVForSo(data, arrHeader);
                    var blob = new Blob(['\ufeff' + csvData], { type: 'text/csv;charset=utf-8;' });
                    var dwldLink = document.createElement("a");
                    var url = URL.createObjectURL(blob);
                    var isSafariBrowser = navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1;
                    if (isSafariBrowser) {  //if Safari open in new window to save file with random filename.
                        dwldLink.setAttribute("target", "_blank");
                    }
                    dwldLink.setAttribute("href", url);
                    dwldLink.setAttribute("download", "SOItemLevelData.csv");
                    dwldLink.style.visibility = "hidden";
                    document.body.appendChild(dwldLink);
                    dwldLink.click();
                    document.body.removeChild(dwldLink);
                }

        $scope.ConvertToCSVForSo = function(objArray, headerList) {

                    var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
                    var str = '';
                    var row = 'S.No,';

                    var headerNames = ["SO Id","Cost Element Name","Vendor Name","Service Description","Cost","Tax","Total Amount","Receiving Amount","Paid Amount","Status"];
                    for (var head in headerNames) {
                        row += headerNames[head] + ',';
                    }
                    console.log(array);
                    row = row.slice(0, -1);
                    str += row + '\r\n';
                    for (var i = 0; i < array.length; i++) {
                        var line = (i + 1) + '';
                        for (var index in headerList) {
                            var head = headerList[index];
                            line += ',' + array[i][head];
                        }
                        str += line + '\r\n';
                    }
                    return str;
                };

    }]);