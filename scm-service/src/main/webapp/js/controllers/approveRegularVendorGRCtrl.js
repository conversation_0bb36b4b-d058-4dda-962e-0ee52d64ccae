'use strict';
angular.module('scmApp')
    .controller('approveRegularVendorGRCtrl', ['$rootScope', '$scope', '$stateParams', 'apiJson', '$http', 'appUtil', 'metaDataService',
            '$toastService', '$alertService', '$timeout', 'previewModalService', 'PrintService', 'productService', 'Popeye',
            function ($rootScope, $scope, $stateParams, apiJson, $http, appUtil, metaDataService,
                      $toastService, $alertService, $timeout, previewModalService, PrintService, productService, Popeye) {

                $scope.attributes = [];
                $scope.selectedGRItem = null;
                $scope.init = function () {
                    var currentDate = appUtil.getCurrentBusinessDate();
                    if (!appUtil.isEmptyObject(currentDate)) {
                        $scope.currentUser = appUtil.getCurrentUser();
                        $scope.currentDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                        $scope.currentUnit = appUtil.getUnitData();
                        $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                        $scope.endDate = $scope.startDate;
                        findReasons();
                        $scope.selectedVendor = $stateParams.vendor;
                        $scope.userMappingUnits = appUtil.getUnitList();
                        $scope.grs = [];
                        $scope.unitData = appUtil.getUnitData();

                    }
                    metaDataService.getVendorsForUnit($scope.currentUser.unitId, function (vendorsForUnit) {
                        $scope.vendors = vendorsForUnit;
                    });

                    metaDataService.getSkuListForUnit($scope.currentUser.unitId, function (skuForUnitList) {
                        $scope.skus = skuForUnitList;
                    });

                    $scope.companyMap = appUtil.getCompanyMap();
                };
                $scope.selectVendor = function (vendor) {

                    if (appUtil.isEmptyObject($scope.unitSelected)) {
                        $toastService.create("Unit Cannot be Null!");
                        $scope.vendorSelected = null;
                        return;
                    } else {
                        $scope.selectedVendor = vendor;
                    }
                };

                $scope.selectSKU = function (sku) {
                    $scope.selectedSKU = sku;
                };

                function findReasons() {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.goodsReceivedManagement.findUpdationReasons,
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.updationReason = response.data;
                        } else {
                            $toastService.create("Could Not Find Updation Reasons!!");
                        }
                    }, function (error) {
                        console.log(error);
                    });
                };


                $scope.getGRs = function () {
                    var params = {};
                    if (appUtil.isEmptyObject($scope.selectedVendor) && appUtil.isEmptyObject($scope.unitSelected)) {
                        $toastService.create("Please Enter Unit or You Can Enter Unit and Vendor both!");
                        return;
                    }
                    if (!appUtil.isEmptyObject($scope.unitSelected)) {
                        params["unitId"] = [$scope.unitSelected.id];
                    }
                    if (!appUtil.isEmptyObject($scope.selectedVendor)) {
                        params["vendorId"] = $scope.selectedVendor.vendorId;
                    }

                    $http({
                        method: 'GET',
                        url: apiJson.urls.goodsReceivedManagement.findRegularVendorGrs,
                        params: params
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.grs = response.data;
                            $scope.grs.forEach(function (gr) {
                                gr.vendorPoGRItems.forEach(function (grItem) {
                                    grItem.updatedQuantity = grItem.grItemAcceptedQuantity;
                                    grItem.poGrItemPackagingQuantity = grItem.grItemAcceptedQuantity;
                                })
                            })
                        } else {
                            $scope.grs = [];
                        }
                    }, function (error) {
                        console.log(error);
                    });
                };

                $scope.selectOpenGr = function (selectGr) {
                    $scope.selectedGr = selectGr;
                }

                $scope.previewQualityCheck = function (grId) {
                    if (appUtil.isEmptyObject($scope.selectedGr) || $scope.selectedGr.id != grId) {
                        $toastService.create("Please open Gr First");
                    }

                    else {
                        var previewModal = Popeye.openModal({
                            templateUrl: 'previewQualityCheck.html',
                            controller: "previewQualityCheckCtrl",
                            modalClass: 'modal-large',
                            resolve: {
                                items: function () {
                                    return $scope.selectedGr;
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        previewModal.closed.then(function (result) {
                            if (result) {
                                $scope.updateSelectedGr();
                            }
                        });
                    }
                };

                $scope.selectedReasons = function (item, reason) {
                    item.updationreason = reason.deviationReason;
                    console.log(reason);
                }

                $scope.changeItemQuantity = function (item) {
                    if (isEmpty(item.updatedQuantity) || item.updatedQuantity == null || item.updatedQuantity == undefined) {
                        item.grItemAcceptedQuantity = item.poGrItemPackagingQuantity;
                        $toastService.create("You Can't Enter Value Null Or Empty!!");
                        return;
                    }
                    else if (item.updatedQuantity > item.poGrItemPackagingQuantity) {
                        item.updatedQuantity = item.poGrItemPackagingQuantity;
                        $toastService.create("You Can't Enter Value Greater Than GR Requested Quantity !!");
                        return;
                    }
                    else if (!isEmpty(item.updatedQuantity) && item.updatedQuantity != null && item.updatedQuantity < item.poGrItemPackagingQuantity) {
                        item.grItemAcceptedQuantity = item.updatedQuantity;
                    }
                }

                // All the closed POs associated with this GR will be re-opened!
                $scope.cancelGR = function(grId){
                    $alertService.confirm("Are you sure?",
                        "You are going to cancel this GR.",
                        function(result){
                            if(result){
                                $http({
                                    method:'POST',
                                    url:apiJson.urls.goodsReceivedManagement.cancelVendorGR + "/" + grId + "/" + $scope.currentUser.userId
                                }).then(function(response){
                                    if(!appUtil.isEmptyObject(response) && response.data){
                                        $toastService.create("Congratulations! GR cancelled successfully!", $scope.getGRs);
                                    }
                                },function(error){
                                    console.log(error);
                                    if (error.data.errorMsg != null) {
                                        $alertService.alert(error.data.errorTitle, error.data.errorMsg, null, true);
                                    }
                                    else {
                                        $toastService.create("Something went wrong. Please try again!");
                                    }
                                });
                            }
                        }
                    );
                };

                $scope.updateSelectedGr = function () {
                    var reqObj = $scope.selectedGr;
                    $http({
                        method: "POST",
                        url: apiJson.urls.goodsReceivedManagement.approveRegularVendorGrs + "/" + $scope.currentUser.userId,
                        data: reqObj
                    }).then(function (response) {
                        if (response.data != null) {
                            $scope.getGRs();
                            $toastService.create("Successfully Updated GR");
                            $scope.selectedGr = null;
                        } else {
                            $toastService.create("GR could not be created due to some error!!");
                        }
                    }, function (error) {
                        console.log(error);
                        if (error.data.errorMsg != null) {
                            $alertService.alert(error.data.errorTitle, error.data.errorMsg, null, true);
                        }
                        else {
                            $alertService.alert("GR could not be created due to some error!!", error.data.errorMessage);
                        }
                    });
                }
            }
        ]
    ).controller('previewQualityCheckCtrl', ['$scope', 'items', 'Popeye',
    function ($scope, items, Popeye) {

        $scope.initPreview = function () {
            $scope.updatedGr = items;
        };

        $scope.close = function () {
            Popeye.closeCurrentModal(false);
        };

        $scope.submit = function () {
            Popeye.closeCurrentModal(true);
        };

    }
]);