angular.module('scmApp').controller(
    'serviceOrderNewCtrl',
    ['$rootScope', '$scope', '$state', 'apiJson', '$http', 'appUtil', 'metaDataService', '$fileUploadService',
        '$toastService', '$alertService', 'Popeye','$timeout', function ($rootScope, $scope, $state, apiJson, $http, appUtil, metaDataService,
                                                              $fileUploadService, $toastService, $alertService, Popeye,$timeout) {

        $scope.init = function () {
            $scope.getCostCenters();
            $scope.locationList = [];
            $scope.serviceItems = [];
            $scope.defaultUnits = [];
            $scope.defaultBulkUnits = {};
            $scope.costElements = [];
            $scope.showExpandedView = false;
            $scope.soType = ["CAPEX", "OPEX"];
            $scope.serviceBccItems = null;
            $scope.srItems = [];
            $scope.selectedTagName = null;
            $scope.isDisabled = true;
            $scope.uploadedDocData = null;
            $scope.previousShownSO = null;
            $scope.allocatedUnits=[];
            metaDataService.getCompanyList(function (companies) {
                $scope.companyList = companies;
            });
            
            $scope.isRegular = "REGULAR_SO";
            $scope.uploadedApprovalOfHod = null;
            $scope.isBackDatedSo = null;
            $scope.selectedVendorMap = {}
        }

        $scope.getVendorName = function(id){
            return $scope.selectedVendorMap[id];
        }

        $scope.setCapexOpex = function (selectedType) {
            $scope.selectedType=selectedType;
            if ($scope.selectedType != undefined || $scope.selectedType != null) {
                $scope.isRegular = "REGULAR_SO";
            }
            $scope.allocatedUnits = [];
            $scope.defaultUnits=null;
            $scope.srItems = null;
        };

        $scope.getCostCenters = function () {
            var currentUser = appUtil.getCurrentUser();
            $http({
                url: apiJson.urls.serviceOrderManagement.costCenters,
                method: "GET",
                params: {
                    empId: currentUser.userId
                }
            }).then(function (response) {
                $scope.costCenters = response.data;
                $scope.costCentersSize = $scope.costCenters.length;
            }, function (response) {
                console.log(response);
            });
        };

        $scope.getVendorList = function (selectedCenter) {
            metaDataService.getBusinessCostCenters(selectedCenter.id, function (bcc) {
                $scope.units = bcc;
            });
            $scope.tagList = [];
            $scope.selectedLocation = null;
            $scope.costElements = [];
            $scope.selectedElement = null;
            $scope.isDisabled = true;
            $scope.srItems = [];
            $scope.vendorList = [];
            $scope.locationList = [];
            $(".selectedQuantity").val('');
            $(".selectedUnitPrice").val('');
            $scope.uom = null;
            $scope.selectedLocation = null;
            var currentUser = appUtil.getCurrentUser();
            $http({
                url: apiJson.urls.serviceOrderManagement.getMappedVendorList,
                method: "GET",
                params: {
                    costCenterId: selectedCenter.id
                }
            }).then(function (response) {
                $scope.vendorList = filterByActiveVendors(response.data);
                $scope.vendorListSize = $scope.vendorList.length;
            }, function (response) {
                console.log(response);
            });
        };

        function filterByActiveVendors(vendorList){
            return vendorList.filter(function (vendor) {
                return  vendor.status == "ACTIVE";
            });
        }

        $scope.selectVendorLoc = function (vendor) {
            if (vendor.vendorBlocked != undefined && vendor.vendorBlocked != null && vendor.vendorBlocked == 'Y') {
                if (vendor.blockedReason == 'MANUAL') {
                    $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                        "<br><b>Please Contact Finance Team..!</b>", function () {
                    }, true);
                } else {
                    $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                        "<br><b>Please Settle the Vendor Advances related to advance payments of : " + vendor.blockedReason + "</b>", function () {}, true);
                }
                $scope.selectedVendor = null;
                $timeout(function () {
                    $('#vendorList').val('').trigger('change');
                });
                return;
            }
            $scope.tagList = [];
            $scope.isDisabled = true;
            $scope.selectedLocation = null;
            $scope.costElements = [];
            $scope.selectedElement = null;
            $scope.srItems = [];
            $(".selectedQuantity").val('');
            $(".selectedUnitPrice").val('');
            $scope.uom = null;
            $scope.serviceItems = [];
            $scope.selectedDispatchLocation = null;
            $scope.locationList = null;
            $scope.selectedVendor = vendor;
            metaDataService.getVendorLocations($scope.selectedVendor.vendorId, function (locations) {
                $scope.locationList = locations;
            });
            $scope.locationListSize = $scope.locationList.length;
        };

        $scope.getCostElementList = function (selectedLocation) {
            $scope.getTagNamesList();
            var currentUser = appUtil.getCurrentUser();
            $scope.selectedLocation = selectedLocation;
            $http({
                url: apiJson.urls.serviceOrderManagement.getMappedCostElements,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id,
                    vendorId: $scope.selectedVendor.vendorId
                }
            }).then(function (response) {
                $scope.costElements = response.data;
                $scope.costElementsSize = $scope.costElements.length;
            }, function (response) {
                console.log(response);
            });
        };

        $scope.getCloneTagList = function (tag) {
            $http({
                url: apiJson.urls.serviceOrderManagement.getTagList,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id,
                    tagName: tag
                }
            }).then(function (response) {
                $scope.tagList = response.data;
                $scope.tagListSize = $scope.tagList.length;
            }, function (response) {
                console.log(response);
            });
        }

        $scope.getTagNamesList = function () {
            $http({
                url: apiJson.urls.serviceOrderManagement.getTagNamesList,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id
                }
            }).then(function (response) {
                $scope.tagNamesList = response.data;
                $scope.tagNamesListSize = $scope.tagNamesList.length;
            }, function (response) {
                console.log(response);
            });
        };

        $scope.validateDate = function () {
            if ($(".costElementDate").val() == null || $(".costElementDate").val().trim() == '') {
                $toastService.create("Input Cost Element From Date ");
                return;
            }
            var fromDate = new Date($(".costElementDate").val());
            var toDate = new Date($(".costElementToDate").val());
            if (toDate < fromDate) {
                $toastService.create("To Date can not be less than From Date..!");
                $(".costElementToDate").val('');
                return false;
            }
        };

        $scope.uploadApprovalOfHod = function () {
            $fileUploadService.openFileModal("Upload Approval of HOD Document", "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                var fileLimit = fileExt.toLowerCase() == 'png' ? 1024000 : 5120000;
                if (file.size > fileLimit) {
                    var msg = ""
                    if (fileExt.toLowerCase() == 'png') {
                        msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
                    }
                    $toastService.create('File size should not be greater than ' + fileLimit / 1024000 + ' MB.' + msg);
                    return;
                }

                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                    var mimeType = fileExt.toUpperCase();
                    var fd = new FormData();
                    fd.append('type', "OTHERS");
                    fd.append('mimeType', fileExt.toUpperCase());
                    fd.append('userId', appUtil.getCurrentUser().userId);
                    fd.append('docType', "APPROVAL_OF_HOD");
                    fd.append('file', file);
                    fd.append('docName', "APPROVAL_OF_HOD");
                    $http({
                        url: apiJson.urls.serviceOrderManagement.uploadApprovalOfHod,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (!appUtil.isEmptyObject(response)) {
                            $scope.uploadedApprovalOfHod = response;
                        } else {
                            $toastService.create("Upload failed");
                            $scope.uploadedApprovalOfHod = null;
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, true)
                        } else {
                            $toastService.create("Upload failed");
                        }
                        $scope.uploadedApprovalOfHod = null;
                    });
                } else {
                    $toastService.create('Upload Failed , File Format not Supported');
                    $scope.uploadedApprovalOfHod = null;
                }
            });
        };

        $scope.setSoType = function (type) {
            $scope.isRegular = type;
            console.log("type is : ", $scope.isRegular);
            $scope.srItems = [];
            $scope.serviceItems = [];
            $scope.defaultBulkUnits = {};
            $scope.defaultUnits = [];
            $scope.receivings = [];
            $scope.selectedElement = null;
            $(".selectedUnitPrice").val('');
            $toastService.create("All the allocations and cost elements data is cleared..!");
        };

        $scope.downloadSampleBulkSO = function () {
            var soUnits = [];
            $scope.units.forEach(function (unit) {
                var obj = {
                    unitId : unit.code,
                    unitName : unit.name,
                    allocatedCost : 0
                };
                soUnits.push(obj);
            });

            $http({
                url: apiJson.urls.serviceOrderManagement.downloadSampleBilkSO,
                method: 'POST',
                data : soUnits,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            }).then(function success(response) {
                if (!appUtil.checkEmpty(response)) {
                    var fileName = "Sample_So"  + ".xlsx";
                    var blob = new Blob([response.data], {
                        type: 'c'
                    }, fileName);
                    saveAs(blob, fileName);
                } else {
                    $toastService.create("Could not generate Sample Bulk SO Sheet. Please try again later");
                }
            },function error(response){
                $toastService.create("Could not generate Sample Bulk SO Sheet... Please try again later");
            });
        };

        $scope.uploadBulkSo = function () {
            if ($scope.selectedElement == undefined || $scope.selectedElement == null) {
                $toastService.create("Please Select a Cost Element");
                return;
            }
            if ($scope.defaultBulkUnits[$scope.selectedElement.id] != undefined && $scope.defaultBulkUnits[$scope.selectedElement.id] != null) {
                $toastService.create("Selected Cost Element Allocations are already added..!");
                return;
            }
            if ($(".costElementDate").val() == null || $(".costElementDate").val().trim() == '') {
                $toastService.create("Input Cost Element From Date ");
                return;
            }
            if ($(".costElementToDate").val() == null || $(".costElementToDate").val().trim() == '') {
                $toastService.create("Input Cost Element To Date ");
                return;
            }
            if ($(".selectedUnitPrice").val() == null || $(".selectedUnitPrice").val().trim() == '' || isNaN($(".selectedUnitPrice").val())) {
                $toastService.create("Input Updated Price.");
                return;
            }
            $fileUploadService.openFileModal("Upload Bulk SO Sheet", "Find", function (file) {
                $scope.readUploadSOFile(file);
            });
        };

        $scope.readUploadSOFile = function (file) {
            $scope.listOfUnits = [];
            var fd = new FormData(document.forms[0]);
            fd.append("file", file);
            $http({
                url: apiJson.urls.serviceOrderManagement.readUploadedSo,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).then(function success(response) {
                if (response.data.length != 0){
                    console.log("got data ",response.data);
                    $scope.listOfUnits = response.data;
                    $scope.showUploadedSheet($scope.listOfUnits);
                }
                else{
                    $toastService.create("No units present in the uploaded sheet..!");
                }
            },function error(response) {
                if (response.data.errorMsg != null) {
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                } else {
                    $toastService.create("Error in uploaded sheet.");
                    console.log("response is ",response);
                }
            });
        };

        $scope.showUploadedSheet = function(units){
            var uploadedSheet = Popeye.openModal({
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'uploadedSoSheet.html',
                controller: 'uploadedSoSheetCtrl',
                backdrop: 'static',
                keyboard: false,
                scope:$scope,
                size: 'lg',
                resolve: {
                    result : function () {
                        return units;
                    },
                    costElement : function () {
                        return $scope.selectedElement;
                    },
                    costCenter : function(){
                        return $scope.selectedCenter;
                    },
                    defaultVendor : function(){
                        return $scope.selectedVendor;
                    },
                    defaultVendorLoc : function(){
                        return $scope.selectedLocation;
                    },
                    defaultLocList : function(){
                        return $scope.locationList;
                    }
                }
            });
            uploadedSheet.closed.then(function (obj) {
                $scope.result = [];
                $scope.costElVendorList = [];
                $scope.costElement = null;
                $scope.vendorLocalCache = {};
                if (obj.isSuccessful) {
                    $scope.addItemBulk(obj.soData,obj.totalOfAllocatedCost);
                }
            });
        };

        $scope.createBulkDefaultBcc = function (units) {
          var result = {};
            $scope.units.forEach(function (unit) {
             units.forEach(function (defaultUnit) {
                if (defaultUnit.unitId == unit.code) {
                    var tempUnit = unit;
                    unit.allocatedCost = defaultUnit.allocatedCost;
                    unit['vendor'] = defaultUnit.selectedVendor; 
                    unit['vendorLoc'] = defaultUnit.selectedLocation; 
                    result[unit.code] = unit;
                }
             });
          });
          return result;
        };

        $scope.addItemBulk = function (units,allocatedCost) {
            $scope.defaultBulkUnits[$scope.selectedElement.id] = $scope.createBulkDefaultBcc(units);
            var quantity = (allocatedCost/$scope.selectedCost.currentPrice);
            $scope.createItem(true,quantity,$scope.selectedCost.currentPrice,$scope.defaultBulkUnits[$scope.selectedElement.id]);
            $scope.receivings = $scope.calculateReceivings($scope.srItems);
        };

        $scope.selectCostElement = function (selectedElement) {
            $scope.isDisabled = true;
            $http({
                url: apiJson.urls.serviceOrderManagement.getVendorData,
                method: "GET",
                params: {
                    locationId: $scope.selectedLocation.id
                }
            }).then(function (response) {
                if (response.data) {
                    $scope.tax = selectedElement.taxRate;
                }
                else {
                    $scope.tax = 0;
                }
                $scope.selectedElement = selectedElement;
                $scope.uom = selectedElement.uom;
                if ($scope.selectedElement.isPriceUpdate == 'Yes') {
                    $scope.isDisabled = false;

                }
                $scope.getCostElementPrice($scope.selectedElement.id);
            }, function (response) {
                console.log(response);
            });
        }

        $scope.getCostElementPrice = function (costElementId) {
            $http({
                url: apiJson.urls.serviceOrderManagement.getSelectedCostElement,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id,
                    vendorId: $scope.selectedVendor.vendorId,
                    costElementId: costElementId
                }
            }).then(function (response) {
                $scope.selectedCost = response.data;
                //$scope.selectedUnitPrice = $scope.selectedCost.currentPrice;
                $(".selectedUnitPrice").val($scope.selectedCost.currentPrice);
            }, function (response) {
                console.log(response);
            });
        }

        $scope.removeItem = function (item, index, items) {
            Object.keys($scope.srItems).forEach(function (key) {
                if (key.startsWith(item.costElementId + "_")) {
                    delete $scope.srItems[key];
                }
            });
            items.splice(index, 1);
            if ($scope.defaultBulkUnits[item.costElementId] != undefined || $scope.defaultBulkUnits[item.costElementId] != null) {
                delete $scope.defaultBulkUnits[item.costElementId];
            }
            $scope.receivings = $scope.calculateReceivings($scope.srItems);
            $scope.checkForBackDatedSo();
        };

        $scope.addItem = function (selectedElement) {
            if ($scope.selectedType == "CAPEX") {
                if ($scope.defaultUnits == undefined || $scope.defaultUnits.length == 0) {
                    $toastService.create("Please Select Default Allocations Before Adding cost Element");
                    return;
                }
            }
            if ($scope.selectedElement == undefined || $scope.selectedElement == null) {
                $toastService.create("Please Select Cost Element..!");
                return;
            }
            if ($(".selectedQuantity").val() == null || $(".selectedQuantity").val().trim() == '' || isNaN($(".selectedQuantity").val())) {
                $toastService.create("Input Quantity Data.");
                return;
            }
            if ($(".selectedUnitPrice").val() == null || $(".selectedUnitPrice").val().trim() == '' || isNaN($(".selectedUnitPrice").val())) {
                $toastService.create("Input Updated Price.");
                return;
            }
            if ($(".costElementDate").val() == null || $(".costElementDate").val().trim() == '') {
                $toastService.create("Input Cost Element From Date ");
                return;
            }
            if ($(".costElementToDate").val() == null || $(".costElementToDate").val().trim() == '') {
                $toastService.create("Input Cost Element To Date ");
                return;
            }
            for (var x = 0; x < $scope.serviceItems.length; x++) {
                if ($scope.serviceItems[x].costElementId == selectedElement.id) {
                    $toastService.create("Same Cost Element Cannot Be Added. Change Quantity In Existing Cost Element.");
                    return;
                }
            }



            if($scope.selectedElement.isPriceUpdate === 'Yes' && $scope.selectedElement.lowerPriceRange!==0 && $scope.selectedElement.upperPriceRange!==0 ){
                if ( 
                    parseFloat($(".selectedUnitPrice").val()) !== $scope.selectedCost.currentPrice 
                    && parseFloat($(".selectedUnitPrice").val()) < $scope.selectedElement.lowerPriceRange
                     || parseFloat($(".selectedUnitPrice").val()) > $scope.selectedElement.upperPriceRange   ) {
                  $alertService.alert("Price out of range.","Enter valid unit price(Please check the price range).",null,true);
                  return;
              }
            }else if($scope.selectedElement.isPriceUpdate === 'Yes'){
                $alertService.alert("Price range not found.","Please add price range for this cost element.",null,true);
                 return;
            }

           
           
            $scope.createItem(false);
            /*var currentUser = appUtil.getCurrentUser();
            $http({
                url: apiJson.urls.serviceOrderManagement.getSelectedCostElement,
                method: "GET",
                params : {
                    costCenterId : $scope.selectedCenter.id,
                    vendorId : $scope.selectedVendor.vendorId,
                    costElementId : selectedElement.id
                }
            }).then(function (response) {
                $scope.selectedCost = response.data;
                $scope.createItem();
            }, function (response) {
                console.log(response);
            });*/
            $scope.receivings = $scope.calculateReceivings($scope.srItems);
        }

        $scope.checkForBackDatedSo = function () {
              $scope.isBackDatedSo = false;
              for (var i = 0; i < $scope.serviceItems.length;i++) {
                  $scope.uploadedApprovalOfHod = null;
                  var currentDate = new Date();
                  var soToDate = new Date($scope.serviceItems[i].costElementToDate);
                  var currentDateMonth = currentDate.getMonth() + 1;
                  var soToDateMonth = soToDate.getMonth() + 1;
                  if (soToDate < currentDate) {
                      if (soToDate.getFullYear() < currentDate.getFullYear()) {
                          $scope.isBackDatedSo = true;
                          break;
                      }
                      if (soToDateMonth < currentDateMonth) {
                          $scope.isBackDatedSo = true;
                          break;
                      }
                  }
              }
        };

        $scope.setSelectedTagName = function(tagName){
            $scope.selectedTagName = tagName;
            $scope.getCloneTagList(tagName);

        };

        $scope.cloneSOWithTag = function(){
            console.log("data is :",$scope.tagList);
            if($(".costElementDate").val() == null || $(".costElementDate").val().trim() == '') {
                $toastService.create("Input Cost Element From Date ");
                return;
            }
            if($(".costElementToDate").val() == null || $(".costElementToDate").val().trim() == '') {
                $toastService.create("Input Cost Element To Date ");
                return;
            }
            $scope.selectTag($scope.tagList[0]);
        };

        $scope.selectTag = function (selectedTag) {
            $scope.serviceItems = [];
            $scope.costELementCloneList = [];
            var map = new Map();
            for (var i = 0; i < selectedTag.orderItems.length; i++) {
                for (var x = 0; x < $scope.costElements.length; x++) {
                    if ($scope.costElements[x].id == selectedTag.orderItems[i].costElementId) {
                        if (map.has(selectedTag.orderItems[i].costElementId)) {
                            var listData = map.get(selectedTag.orderItems[i].costElementId);
                            var reqObj = {
                                costElementId: selectedTag.orderItems[i].costElementId,
                                ascCode: $scope.costElements[x].code,
                                costElementName: $scope.costElements[x].name,
                                serviceDescription: $scope.costElements[x].description,
                                unitOfMeasure: $scope.costElements[x].uom,
                                currentPrice: selectedTag.orderItems[i].unitPrice,
                                taxRate: $scope.costElements[x].taxRate,
                                quantity: selectedTag.orderItems[i].requestedQuantity + listData.quantity,
                                deptName: $scope.costElements[x].department.name,
                                costElementDate: $(".costElementDate").val(),
                                costElementToDate: $(".costElementToDate").val(),
                            };
                            map.delete(selectedTag.orderItems[i].costElementId);
                            map.set(selectedTag.orderItems[i].costElementId, reqObj);
                        }
                        else {
                            var reqObj = {
                                costElementId: selectedTag.orderItems[i].costElementId,
                                ascCode: $scope.costElements[x].code,
                                costElementName: $scope.costElements[x].name,
                                serviceDescription: $scope.costElements[x].description,
                                unitOfMeasure: $scope.costElements[x].uom,
                                currentPrice: selectedTag.orderItems[i].unitPrice,
                                taxRate: $scope.costElements[x].taxRate,
                                quantity: selectedTag.orderItems[i].requestedQuantity,
                                deptName: $scope.costElements[x].department.name,
                                costElementDate: $(".costElementDate").val(),
                                costElementToDate: $(".costElementToDate").val(),
                            };
                            map.set(selectedTag.orderItems[i].costElementId, reqObj);
                        }
                    }
                }
            }
            angular.forEach(map, function (value, key) {
                // $scope.costELementCloneList.push(value);
                //$scope.createItemForClone(value);
                $scope.findPrice(value);
            });
        }

        $scope.findPrice = function (selectedElement) {
            $http({
                url: apiJson.urls.serviceOrderManagement.getSelectedCostElement,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id,
                    vendorId: $scope.selectedVendor.vendorId,
                    costElementId: selectedElement.costElementId
                }
            }).then(function (response) {
                selectedElement.currentPrice = response.data.currentPrice;
                $scope.createItemForClone(selectedElement);
            }, function (response) {
                console.log(response);
            });
        }

        $scope.createItemForClone = function (costElement) {
            var totalCost = $scope.getTotalCost(costElement.quantity, costElement.currentPrice);
            var totalTax = $scope.getTotalTax(totalCost, costElement.taxRate);
            var amountPaid = $scope.getTotalAmount(totalCost, totalTax);
            var item = {
                ascCode: costElement.ascCode,
                costElementId: costElement.costElementId,
                costElementName: costElement.costElementName,
                departmentName: costElement.deptName,
                serviceDescription: costElement.serviceDescription,
                requestedQuantity: costElement.quantity,
                unitOfMeasure: costElement.unitOfMeasure,
                costElementDate: costElement.costElementDate,
                costElementToDate: costElement.costElementToDate,
                unitPrice: costElement.currentPrice,
                totalCost: totalCost,
                totalTax: totalTax,
                amountPaid: amountPaid,
                taxRate: costElement.taxRate,
                type: $scope.selectedType,
                edit: false
            };
            $scope.serviceItems.push(item);
            $scope.createDefaultBccs($scope.defaultUnits);
            $scope.checkForBackDatedSo();
        }

        $scope.createItem = function (isBulk, quantity, price, defaultUnits) {
            var totalcost = 0;
            if (isBulk) {
                totalCost = $scope.getTotalCost(quantity, price);
            } else {
                totalCost = $scope.getTotalCost($(".selectedQuantity").val(), $(".selectedUnitPrice").val());
            }
            var totalTax = $scope.getTotalTax(totalCost, $scope.tax);
            var amountPaid = $scope.getTotalAmount(totalCost, totalTax);
            var item = {
                ascCode: $scope.selectedElement.code,
                costElementId: $scope.selectedElement.id,
                costElementName: $scope.selectedElement.name,
                departmentName: $scope.selectedElement.department.name,
                departmentId : $scope.selectedElement.department.listDetailId,
                serviceDescription: $scope.selectedElement.description,
                requestedQuantity: isBulk ? quantity : parseFloat($(".selectedQuantity").val()),
                unitOfMeasure: $scope.selectedElement.uom,
                costElementDate: $(".costElementDate").val(),
                costElementToDate:$(".costElementToDate").val(),
                unitPrice: isBulk ? price :$(".selectedUnitPrice").val(),
                totalCost: totalCost,
                totalTax: totalTax,
                amountPaid: amountPaid,
                taxRate: $scope.tax,
                type: $scope.selectedType,
                edit: false
            };
            $scope.serviceItems.unshift(item);
            $scope.costElements = $scope.costElements;
            $(".selectedQuantity").val('');
            $(".selectedUnitPrice").val('');
            if (isBulk) {
                $scope.createDefaultBccItem(defaultUnits, item, true);
            } else {
                $scope.createDefaultBccItem($scope.defaultUnits, item, false);
            }
        };

        $scope.createDefaultBccItem = function (defaultUnits, item, isBulk) {
            var keys = Object.keys(defaultUnits);
            var len = keys.length;

            for (var key in defaultUnits) {
                if (appUtil.isEmptyObject($scope.srItems)) {
                    $scope.srItems = {};
                }
                if (isBulk) {
                    console.log("Allocated Cost is : ",defaultUnits[key].allocatedCost);
                    var quantity = defaultUnits[key].allocatedCost/item.unitPrice;
                    console.log("Quanity is : ",quantity);
                    defaultUnits[key].qty = quantity;
                }
                else {
                    var singleQuantity = (parseFloat(item.requestedQuantity) / len).toFixed(5);
                    console.log("checking quantity at 341: " + singleQuantity)
                    var singlePrice = parseFloat(parseFloat(item.totalCost) / len).toFixed(2);
                    defaultUnits[key].allocatedCost = singlePrice;
                    defaultUnits[key].qty = singleQuantity;
                }

                var qty = parseFloat(parseFloat(defaultUnits[key].allocatedCost) / parseFloat(item.unitPrice)).toFixed(5);
                console.log("checking quantity at 348: " + qty)
                var tax = parseFloat(parseFloat(defaultUnits[key].allocatedCost) * (parseFloat(item.taxRate) / 100)).toFixed(2);
                var keys = item.costElementId + "_" + defaultUnits[key].id;
                var obj = prepareObj(defaultUnits[key], item, qty, tax);
                if(isBulk){
                    obj['vendor'] = defaultUnits[key].vendor;
                    obj['vendorLoc'] = defaultUnits[key].vendorLoc;
                } 
                if (appUtil.isEmptyObject($scope.srItems[keys])) {
                    $scope.srItems[keys] = {};
                }
                // add item specific to SO item and to that specific unit
                $scope.srItems[keys] = obj;
            }
        }

        $scope.editItem = function (id) {
            $scope.serviceItems[id].edit = true;
        };

        $scope.updateItem = function (item, id) {
            //var tax = "18";
            var totalCost = $scope.getTotalCost(item.requestedQuantity, item.unitPrice);
            var totalTax = $scope.getTotalTax(totalCost, item.taxRate);
            var amountPaid = $scope.getTotalAmount(totalCost, totalTax);
            $scope.serviceItems[id] = {
                ascCode: item.ascCode,
                costElementId: item.costElementId,
                costElementName: item.costElementName,
                departmentName: item.departmentName,
                departmentId: item.departmentId,
                serviceDescription: item.serviceDescription,
                requestedQuantity: item.requestedQuantity,
                unitOfMeasure: item.unitOfMeasure,
                costElementDate: item.costElementDate,
                costElementToDate: item.costElementToDate,
                unitPrice: item.unitPrice,
                totalCost: totalCost,
                totalTax: totalTax,
                amountPaid: amountPaid,
                taxRate: item.taxRate,
                type: item.type,
                edit: false
            };
            if (!appUtil.isEmptyObject($scope.srItems)) {
                for (var key in $scope.defaultUnits) {
                    var srIt = $scope.srItems[$scope.serviceItems[id].costElementId + '_' + $scope.defaultUnits[key].id];
                    srIt.requestedQuantity = item.requestedQuantity;
                    srIt.totalCost = parseFloat(totalCost);
                    srIt.totalTax = parseFloat(totalTax);
                    srIt.totalAmount = parseFloat(amountPaid);
                }
            }
            $scope.receivings = $scope.calculateReceivings($scope.srItems);
            $scope.checkForBackDatedSo();
        };

        $scope.getTotalAmount = function (totalCost, totalTax) {
            return (parseFloat(totalCost) + parseFloat(totalTax)).toFixed(2);
            ;
        }

        $scope.getTotalCost = function (selectedQuantity, price) {
            return (selectedQuantity * price).toFixed(2);
            ;
        }

        $scope.getTotalTax = function (price, tax) {
            return ((price * tax) / 100).toFixed(2);
        }


        $scope.showAllocations = function () {
            if (appUtil.isEmptyObject($scope.selectedType)) {
                $toastService.create("Please select Type for Service Order Before Assigining Allocations");
                return;
            }
            var allocateCostModal = Popeye.openModal({
                templateUrl: "allocateCostModal.html",
                controller: "allocateCostModalCtrl",
                resolve: {

                    units: function () {
                        return angular.copy($scope.units);
                    },
                    companies: function () {
                        return angular.copy($scope.companyList);
                    },
                    type: function () {
                        return angular.copy($scope.selectedType);
                    },
                    allocateCostUnits: function (){
                        return angular.copy($scope.allocatedUnits);
                    }
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });

            allocateCostModal.closed.then(function (data) {
                $scope.allocatedUnits = data.units;
                $scope.defaultUnits = data.units.filter(function (unit){
                    return unit.checked == true;
                });
                $scope.createDefaultBccs($scope.defaultUnits);
                $scope.srItems = null;
                for(var i = 0;i<$scope.serviceItems.length;i++){
                    $scope.createDefaultBccItem($scope.defaultUnits, $scope.serviceItems[i], false);
                }
//                $scope.createDefaultBccs($scope.defaultUnits);
            });
        }

        $scope.createDefaultBccs = function (defaultUnits) {
            if ($scope.serviceItems != null) {
                var keys = Object.keys(defaultUnits);
                var len = keys.length;

                for (var key in defaultUnits) {
                    for (var i = 0; i < $scope.serviceItems.length; i++) {
                        var item = angular.copy($scope.serviceItems[i]);
                        if (appUtil.isEmptyObject($scope.srItems)) {
                            $scope.srItems = {};
                        }
                        var singleQuantity = (parseFloat(item.requestedQuantity) / len).toFixed(5);
                        console.log("checking quantity at 454: "+singleQuantity)
                        var singlePrice = parseFloat(parseFloat(item.totalCost) / len).toFixed(2);
                        defaultUnits[key].allocatedCost = singlePrice;
                        defaultUnits[key].qty = singleQuantity;



                        var qty = parseFloat(parseFloat(defaultUnits[key].allocatedCost) / parseFloat(item.unitPrice)).toFixed(5);
                        console.log("checking quantity at 462: "+qty)
                        var tax = parseFloat(parseFloat(defaultUnits[key].allocatedCost) * (parseFloat(item.taxRate) / 100)).toFixed(2);
                        var keys = item.costElementId + "_" + defaultUnits[key].id;
                        var obj = prepareObj(defaultUnits[key], item, qty, tax);
                        if (appUtil.isEmptyObject($scope.srItems[keys])) {
                            $scope.srItems[keys] = {};
                        }
                        // add item specific to SO item and to that specific unit
                        $scope.srItems[keys] = obj;

                    }
                }
            }
        }

        function prepareObj(bcc, item, qty, tax) {
            return {
                businessCostCenterId: bcc.id,
                businessCostCenterName: bcc.name,
                costElementId: item.costElementId,
                costElementName: item.costElementName,
                departmentName: item.departmentName,
                departmentId : item.departmentId,
                costElementDate: item.costElementDate,
                costElementToDate: item.costElementToDate,
                company: bcc.company,
                location: bcc.location,
                state: bcc.state,
                requestedQuantity: parseFloat(qty),
                ascCode: item.ascCode,
                serviceDescription: item.serviceDescription,
                unitOfMeasure: item.unitOfMeasure,
                unitPrice: item.unitPrice,
                totalCost: parseFloat(bcc.allocatedCost),
                totalAmount: parseFloat(parseFloat(bcc.allocatedCost) + parseFloat(tax)),
                taxRate: parseFloat(item.taxRate),
                totalTax: parseFloat(tax),
                type: item.type,
                unitId: bcc.code
            };
        }


        $scope.viewAllocatedUnits = function (index, itemSelected) {
            $scope.item = $scope.serviceItems[index];
            if ($scope.isRegular == "REGULAR_SO") {
                if ($scope.defaultUnits == undefined || $scope.defaultUnits.length == 0) {
                    $toastService.create("Please Select Default Allocations.");
                    return;
                }
            }
            var allocateCostModal = Popeye.openModal({
                templateUrl: "allocatedCostModal.html",
                controller: "allocatedCostModalCtrl",
                resolve: {

                    allUnits: function () {
                        return angular.copy($scope.units);
                    },
                    selectedUnits: function () {
                        return $scope.isRegular =="REGULAR_SO" ? angular.copy($scope.defaultUnits) : angular.copy($scope.defaultBulkUnits[itemSelected.costElementId]);
                    },
                    item: function () {
                        return angular.copy($scope.item);
                    },
                    srItems: function () {
                        return angular.copy($scope.srItems);
                    },
                    companies: function () {
                        return angular.copy($scope.companyList);
                    },
                    costElementId: function () {
                        return angular.copy(itemSelected.costElementId);
                    },
                    selectedType : function (){
                        return $scope.selectedType;
                    },
                    soType : function(){  return $scope.isRegular; }
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });

            allocateCostModal.closed.then(function (allItems) {
                if (!appUtil.isEmptyObject(allItems)) {
                    $scope.srItems = angular.copy(allItems);
                    $scope.receivings = $scope.calculateReceivings($scope.srItems);
                }
            });
        };

        $scope.downloadApprovalofHod = function () {
            metaDataService.downloadDocument($scope.uploadedApprovalOfHod);
        };

        $scope.nextPage = function () {
            if (appUtil.isEmptyObject($scope.srItems)) {
                $toastService.create("Please assign price to bcc's!");
                return;
            }
            if (appUtil.isEmptyObject($scope.selectedType)) {
                $toastService.create("Please select Type for Service Order");
                return;
            }
            $scope.showExpandedView = true;
            $scope.srItemsDup = angular.copy($scope.srItems);
            $scope.receivings = $scope.calculateReceivings($scope.srItems);
            $scope.checkForBackDatedSo();
        }

        $scope.calculateReceivings = function (srItems) {
            $scope.bccList = [];
            var receivings = {};
            for (var key in srItems) {
                addToMap(receivings, srItems[key]);
            }

            if($scope.isRegular === "BULK_SO"){
                angular.forEach(receivings, function (value, k) {
                    angular.forEach(value,function (val, key) {
                        var totalCost = 0;
                        var totalAmount = 0;
                        var totalTax = 0;
                        for (var x = 0; x < val.items.length; x++) {
                            totalCost = parseFloat(totalCost) + parseFloat(val.items[x].totalCost);
                            totalAmount = parseFloat(totalAmount) + parseFloat(val.items[x].totalAmount);
                            totalTax = parseFloat(totalTax) + parseFloat(val.items[x].totalTax);
                        }
                        val.totalCost = totalCost;
                        val.totalAmount = totalAmount;
                        val.totalTax = totalTax;
                        val.tagName = '';
                        val.tag = true; 
                        });   
    
                });
                return receivings;
            }
            angular.forEach(receivings, function (value, k) {
                    var totalCost = 0;
                    var totalAmount = 0;
                    var totalTax = 0;
                    for (var x = 0; x < value.items.length; x++) {
                        totalCost = parseFloat(totalCost) + parseFloat(value.items[x].totalCost);
                        totalAmount = parseFloat(totalAmount) + parseFloat(value.items[x].totalAmount);
                        totalTax = parseFloat(totalTax) + parseFloat(value.items[x].totalTax);
                    }
                    value.totalCost = totalCost;
                    value.totalAmount = totalAmount;
                    value.totalTax = totalTax;
                    value.tagName = '';
                    value.tag = true; 
            });
            
            return receivings;
        }

        $scope.getSummaryData = function (receivings) {
            var amount = 0;
            angular.forEach(receivings, function (value, key) {
                for (var x = 0; x < value.length; x++) {
                    amount += value[x].totalCost;
                }
            });
        }
       

        function addToMap(map, obj) {

           if($scope.isRegular==="BULK_SO"){
            if(obj.vendor && obj.vendorLoc){
                var vendorKey = obj.vendor.vendorId;
                if(appUtil.isEmptyObject(map[vendorKey])){
                    map[vendorKey] = {};      
                }
                var stateKey = obj.company.id + "_" + obj.state.id;
               
                if(appUtil.isEmptyObject(map[vendorKey][stateKey])){
                    map[vendorKey][stateKey] = {
                       company: obj.company,
                       location: obj.location,
                       state: obj.state,
                       items: [],
                       vendor : obj.vendor,
                       vendorLoc : obj.vendorLoc
                    }
                }
               map[vendorKey][stateKey].items.push(obj);
                
               $scope.bccList.push(obj);
               return;
            }
            console.log("vendor data and vendor loc issue !!!!");
            return;
        }
    

            var key = obj.company.id + "_" + obj.state.id;
            if(appUtil.isEmptyObject(map[key])) {
                map[key] = {
                    company: obj.company,
                    location: obj.location,
                    state: obj.state,
                    items: []
                };
            }
            map[key].items.push(obj);
            $scope.bccList.push(obj);
        }

        $scope.removeItemFromRcv = function (index, rcv, key) {
            var dup = rcv.items[index];
            console.log("duplicate item is : ",dup);
            for (var j in $scope.srItemsDup) {
                var item = $scope.srItemsDup[j];
                if (JSON.stringify(dup) == JSON.stringify(item)) {
                    delete $scope.srItemsDup[j];
                    break;
                }
            }
            rcv.items.splice(index, 1);
            if (rcv.items.length == 0) {
                delete $scope.receivings[key];
            }
            if (Object.keys($scope.receivings).length == 0) {
                $scope.receivings = undefined;
            }
            rcv.totalCost = 0;
            rcv.totalAmount = 0;
            rcv.totalTax = 0;
            for (var x = 0; x < rcv.items.length; x++) {
                rcv.totalCost = parseFloat(rcv.totalCost) + parseFloat(rcv.items[x].totalCost);
                rcv.totalAmount = parseFloat(rcv.totalAmount) + parseFloat(rcv.items[x].totalAmount);
                rcv.totalTax = parseFloat(rcv.totalTax) + parseFloat(rcv.items[x].totalTax);
            }
            $scope.receivings = $scope.calculateReceivings($scope.srItemsDup);
        };

        $scope.submitSo = function (receivings) {
            if ($scope.isBackDatedSo) {
                if (appUtil.isEmptyObject($scope.uploadedApprovalOfHod)) {
                    $toastService.create("Please Upload the Approval Of Hod As this a back dated Service Order..!");
                    return;
                }
            }
            console.log("items are : ",$scope.receivings);
            var enteredTagName = null;
            for(var receiving in $scope.receivings){
                console.log(receiving);
                console.log($scope.receivings[receiving]);
                console.log("tag is : ",$scope.receivings[receiving].tagName);
                enteredTagName = $scope.receivings[receiving].tagName;
            }
            if(enteredTagName !=null && enteredTagName != "") {
                if ($scope.tagNamesList.includes(enteredTagName)) {
                    $toastService.create("Tag name already exists in database. Please enter a new tag name!")
                    return false;
                }
            }
            $scope.getDepartmentSOModal();
        };

        $scope.getDepartmentSOModal = function () {
            var modalInstance = Popeye.openModal({
                templateUrl: 'departmentSOModal.html',
                controller: 'departmentSOModalCtrl',
                size: 'lg',
                modalClass: 'custom-modal',
                resolve: {
                    receivingList: function () {
                        return $scope.bccList;
                    },
                    type: function () {
                        return angular.copy($scope.selectedType);
                    }
                },
                click: true,
                keyboard: false
            });

            modalInstance.closed.then(function (obj) {
                if (!obj.isClosed) {
                    $scope.budgetCheck = obj.checked;
                    $scope.submitData();
                }
            });
        };

        $scope.submitData = function () {
            if ($scope.budgetCheck == undefined && $scope.selectedType == "CAPEX") {
                return;
            }
            if ($scope.budgetCheck && $scope.selectedType == "CAPEX") {
                $toastService.create("Cannot Create Service Order. Budget Exceeded!!!");
                return;
            }
            $alertService.confirm("Are you sure on saving Service Order?", "", function (result) {
                if (result) {
                    sendRequestForSO();
                }
            });
        };

        function prepareRcvngs(receivings) {
            var allRcvngs = [];
            if (!appUtil.isEmptyObject(receivings)) {

                if($scope.isRegular==="BULK_SO"){
                    for (var key in receivings) {
                    
                        for(var k in receivings[key] ){
                            var rcv = receivings[key][k];
                            allRcvngs.push({
                                dispatchLocationId: rcv.vendorLoc.id,
                                vendorId: rcv.vendor.vendorId,
                                costCenterId: $scope.selectedCenter.id,
                                type: $scope.selectedType,
                                userId: appUtil.getCurrentUser().userId,
                                items: rcv.items,
                                tagName: rcv.tagName
                            });
                        }
                       
                    }
                    return allRcvngs;
                }
                for (var key in receivings) {
                    var rcv = receivings[key];
                    allRcvngs.push({
                        dispatchLocationId: $scope.selectedLocation.id,
                        vendorId: $scope.selectedVendor.vendorId,
                        costCenterId: $scope.selectedCenter.id,
                        type: $scope.selectedType,
                        userId: appUtil.getCurrentUser().userId,
                        items: rcv.items,
                        tagName: rcv.tagName
                    });
                }  
            }
            return allRcvngs;
            }
            

        function sendRequestForSO() {
            var reqObj = prepareRcvngs($scope.receivings);
            console.log("Doc data is : ",$scope.uploadedDocData);

            if (reqObj.length > 0) {
                $http({
                    method: "POST",
                    url: apiJson.urls.serviceOrderManagement.createServiceOrder,
                    params:{
                        "documentId":$scope.uploadedDocData == null ? null : $scope.uploadedDocData.documentId,
                        "approvalOfHodDocId":$scope.isBackDatedSo ? $scope.uploadedApprovalOfHod.documentId : null
                    },
                    data: reqObj
                }).then(function (response) {
                    if (response.data != null) {
                        $scope.init();
                        $alertService.alert("Congratulations!!",
                            "Service Order with ID: <b>" + response.data + "</b> created successfully! <br>",
                            function () {
                                $state.go("menu.viewSO", {createdSO: response.data, viewSO: true});
                            }
                        );
                    } else {
                        $toastService.create("Service Order could not be created due to some error!!");
                    }
                }, function (error) {
                    console.log(error);
                    $alertService.alert(error.data.errorTitle, error.data.errorMsg,function () {}, true);
                    //$toastService.create("Service Order could not be created due to some error!!");
                });
            }
        }

        $scope.goBack = function () {
            $scope.showExpandedView = false;
            /* $scope.receivings = {};
             $scope.srItems = [];*/
        };

        $scope.addTag = function (r) {
            r.tag = false;
        }

        /////////////////////document upload methods/////////////////////////////////

        $scope.downloadSoDocument = function(doc){
            metaDataService.downloadDocument(doc);
        };

        $scope.resetScanModal = function () {
            $scope.imagesScanned = [];
            document.getElementById('images').innerHTML = "";
            var canvas = document.createElement('canvas');
            canvas.id = "scaleCanvas";
            document.getElementById('images').appendChild(canvas);
            $scope.uploadedDocData = null;
        };

        $scope.resetSnapModal = function () {
            $scope.snapRunning = false;
            if ($scope.localstream != null) {
                $scope.localstream.getTracks()[0].stop();
            }
            $scope.uploadedDocData = null;
            var canvas = document.getElementById('canvas');
            var context = canvas.getContext('2d');
            context.clearRect(0, 0, 640, 480);
        };

        $scope.startSnap = function () {
            var video = document.getElementById('video');
            // Get access to the camera!
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                // Not adding `{ audio: true }` since we only want video now
                navigator.mediaDevices.getUserMedia({video: true}).then(function (stream) {
                    video.src = window.URL.createObjectURL(stream);
                    $scope.localstream = stream;
                    video.play();
                });
            }
            $scope.snapRunning = true;
        };

        $scope.snapPicture = function () {
            var canvas = document.getElementById('canvas');
            var context = canvas.getContext('2d');
            var video = document.getElementById('video');
            context.drawImage(video, 0, 0, 640, 480);
            video.pause();
            video.src = "";
            $scope.localstream.getTracks()[0].stop();
            $scope.snapRunning = false;
        };

        function dataURItoBlob(dataURI) {
            var byteString = atob(dataURI.split(',')[1]);
            var ab = new ArrayBuffer(byteString.length);
            var ia = new Uint8Array(ab);
            for (var i = 0; i < byteString.length; i++) {
                ia[i] = byteString.charCodeAt(i);
            }
            return new Blob([ab], {type: 'image/png'});
        }

        $scope.uploadFile = function () {
            var canvas = document.getElementById('canvas');
            var blob = dataURItoBlob(canvas.toDataURL("image/png"));
            var fd = new FormData(document.forms[0]);
            fd.append("file", blob);
            fd.append('type', "OTHERS");
            fd.append('docType', "SERVICE_ORDER");
            fd.append('mimeType', "PNG");
            fd.append('userId', appUtil.getCurrentUser().userId);
            $http({
                url: apiJson.urls.serviceOrderManagement.uploadSoDocument,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (!appUtil.isEmptyObject(response)) {
                    $toastService.create("Upload successful");
                    $scope.uploadedDocData = response;
                } else {
                    $toastService.create("Upload failed");
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Upload failed");
            });
        };

        $scope.uploadDoc = function () {
            $fileUploadService.openFileModal("Upload Document", "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                if(file.size > 5120000){
                    $toastService.create('File size should not be greater than 5 mb.');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                    var mimeType = fileExt.toUpperCase();
                    var fd = new FormData();
                    fd.append('type', "OTHERS");
                    fd.append('docType', "SERVICE_ORDER");
                    fd.append('mimeType', fileExt.toUpperCase());
                    fd.append('userId', appUtil.getCurrentUser().userId);
                    fd.append('file', file);
                    $http({
                        url: apiJson.urls.serviceOrderManagement.uploadSoDocument,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (!appUtil.isEmptyObject(response)) {
                            $toastService.create("Upload successful");
                            $scope.uploadedDocData = response;
                        } else {
                            $toastService.create("Upload failed");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $toastService.create("Upload failed");
                    });
                } else {
                    $toastService.create('Upload Failed , File Format not Supported');
                }
                /*metaDataService.uploadFile("OTHERS","PAYMENT_REQUEST_INVOICE",file, function(doc){
                    $scope.uploadedDocData = doc;
                });*/
            });
        };

        $scope.scanToPng = function () {
            scanner.scan($scope.displayImagesOnPage,
                {
                    "output_settings": [
                        {
                            "type": "return-base64",
                            "format": "png"
                        }
                    ]
                }
            );
        };

        $scope.displayImagesOnPage = function (successful, mesg, response) {
            if (!successful) { // On error
                console.error('Failed: ' + mesg);
                return;
            }
            if (successful && mesg != null && mesg.toLowerCase().indexOf('user cancel') >= 0) { // User cancelled.
                console.info('User cancelled');
                return;
            }
            var scannedImages = scanner.getScannedImages(response, true, false); // returns an array of ScannedImage
            $scope.imagesScanned = [];
            $scope.processScannedImage(scannedImages[0]);
            /*for(var i = 0; (scannedImages instanceof Array) && i < scannedImages.length; i++) {
                var scannedImage = scannedImages[i];
                $scope.processScannedImage(scannedImage);
            }*/
        };

        $scope.processScannedImage = function (scannedImage) {
            $scope.imagesScanned.push(scannedImage);
            scaleImage(scannedImage.src);
        };

        function scaleImage(src) {
            var MAX_WIDTH = 1000;
            var image = new Image();
            var canvas = document.getElementById("scaleCanvas");
            image.onload = function () {
                //var canvas = document.getElementById("scaleCanvas");
                if (image.width > MAX_WIDTH) {
                    image.height *= MAX_WIDTH / image.width;
                    image.width = MAX_WIDTH;
                }
                var ctx = canvas.getContext("2d");
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                canvas.width = image.width;
                canvas.height = image.height;
                ctx.drawImage(image, 0, 0, image.width, image.height);
            };
            image.src = src;
        }

        $scope.uploadScannedFile = function () {
            var canvas = document.getElementById('scaleCanvas');
            var blob = dataURItoBlob(canvas.toDataURL("image/png"));
            var fd = new FormData(document.forms[0]);
            fd.append("file", blob);
            fd.append('type', "OTHERS");
            fd.append('docType', "SERVICE_ORDER");
            fd.append('mimeType', "PNG");
            fd.append('userId', appUtil.getCurrentUser().userId);
            $http({
                url: apiJson.urls.serviceOrderManagement.uploadSoDocument,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (!appUtil.isEmptyObject(response)) {
                    $toastService.create("Upload successful");
                    $scope.uploadedDocData = response;
                } else {
                    $toastService.create("Upload failed");
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Upload failed");
            });
        };


        $scope.previewSoDocument = function(doc){
            if(!appUtil.isEmptyObject(doc.documentLink)){
                $http({
                    method:"POST",
                    url:apiJson.urls.vendorManagement.downloadDocument,
                    data: doc,
                    responseType: 'arraybuffer'
                }).then(function(response){
                    var arrayBufferView = new Uint8Array( response.data );
                    var blob = new Blob( [ arrayBufferView ], { type: appUtil.mimeTypes[doc.mimeType] } );
                    var urlCreator = window.URL || window.webkitURL;
                    var imageUrl = urlCreator.createObjectURL( blob );
                    var preview = document.getElementById("documentPreview");
                    preview.innerHTML = "";
                    var img = new Image();
                    img.src = imageUrl;
                    preview.appendChild(img);
                },function(error){
                    $toastService.create("Could not download the document... Please try again");
                });
            }else{
                $toastService.create("Not a valid document... Please check");
            }
        };


    }]).controller("allocateCostModalCtrl",
    ['$rootScope', '$window', '$scope', 'appUtil', 'apiJson', '$http', '$toastService', 'Popeye', 'units', 'companies', 'type','$timeout','uiGridConstants','allocateCostUnits',
        function ($rootScope, $window, $scope, appUtil, apiJson, $http, $toastService, Popeye, units, companies, type,$timeout,uiGridConstants,allocateCostUnits) {


            $scope.initCostModal = function () {
                $scope.allocateCostFinalUnits=[];
                $scope.allocateCostFinalUnits=allocateCostUnits;
                $scope.uiGridViewFlag=true;
                $scope.previewUiGridFlag=false
                $scope.allSelectedRows = [];
                $scope.allUnSelectedRows = [];
                $scope.unitList = units;
                $scope.selectedRowsLengthInScope=0;
                for (var i = 0; i < $scope.unitList.length; i++) {
                    $scope.unitList[i].dataCheck = false;
                    $scope.unitList[i].checkBoxcheck = false;
                }
                $scope.units = $scope.unitList;
                $scope.companies = companies;
                $scope.type = type;
                $scope.selectedUnitForCapex = null;
                 if($scope.type == "CAPEX"){
                    var allocateUnitsArray = $scope.allocateCostFinalUnits.filter(function (unit){
                        return unit.checked == true;
                    });
                    $scope.selectedUnitForCapex = allocateUnitsArray[0];
                }
                $scope.gridOptions = appUtil.getGridOptions($scope);
                $scope.gridOptions['enableSelectAll'] = false;
                $scope.gridOptions['onRegisterApi'] = function (gridApi) {
                    $scope.gridApi = gridApi;

                    $scope.gridApi.selection.on.rowSelectionChanged($scope, function (row) {
                        if (row.isSelected) {
                           if($scope.type == "CAPEX"){
                                  $scope.selectedUnitForCapex = row.entity;
                           }
                            row.entity.checked = true;
                        } else {
                            row.entity.checked = false;
                        }
                        $scope.selectedRowsLength=$scope.gridApi.selection.getSelectedRows().length;
                        $timeout(function (){
                            $scope.$apply();
                        })

                    });
                    $scope.gridApi.core.on.filterChanged( $scope, function() {
                        $scope.filterBool=false;
                        $scope.gridOptions.columnDefs.forEach(function (column){
                        if(column.filter !=null &&   column.filter.term != null){
                            $scope.filterBool=true;
                        }
                        })
                    });

                    $scope.getVisibleRowsSize = function (){
                        $scope.selectedRowsLengthInScope=null;
                        $scope.gridApi.core.getVisibleRows($scope.gridApi.grid).forEach(function (row){
                            if (row.isSelected) {
                                $scope.selectedRowsLengthInScope++;
                            }
                        });
                        return ($scope.gridApi.core.getVisibleRows($scope.gridApi.grid)).length;
                    }
                }
                $scope.typeOfRegion = {};
                $scope.typeOfCategory= {};
                $scope.typeOfCity={};
                if($scope.type==="CAPEX"){
                    $scope.gridOptions.multiSelect = false;
                }
                if($scope.allocateCostFinalUnits.length > 0){
                    $scope.gridOptions.rowTemplate = 'rowView.html';
                    $scope.gridOptions.columnDefs = $scope.showUnitsForAllocate();
                    preSelectGridData($scope.allocateCostFinalUnits);
                }
                $scope.showCostAllocations();
            };

            var typeOfZone=[{ value: 'NORTH', label: 'NORTH' }, { value: 'WEST', label: 'WEST' }, { value: 'SOUTH', label: 'SOUTH'},
                { value: 'EAST', label: 'EAST'}];

            $scope.showCostAllocations = function () {
                var gridItemArray = [];
                $scope.masterUnitList = appUtil.getUnitList();
                $scope.unitMap = {};
                for(var i = 0;i<$scope.masterUnitList.length;i++){
                    $scope.unitMap[$scope.masterUnitList[i].id] = $scope.masterUnitList[i];
                }
                $scope.units.forEach(function (unit){
                        var masterUnitData = $scope.unitMap[parseInt(unit.code)];
                        if (masterUnitData != undefined && masterUnitData != null) {
                            unit.unitZone = masterUnitData.unitZone;
                            unit.region = masterUnitData.region;
                        }
                        else {
                            unit.unitZone = null;
                            unit.region = null;
                        }
                    var regionKey = unit['region'];
                    var regionObject = {
                        value:unit['region'],
                        label:unit['region']
                    }
                    if ( regionKey != undefined && regionKey != null  || ($scope.typeOfRegion[regionKey] === undefined || $scope.typeOfRegion[regionKey] ===null)) {
                        $scope.typeOfRegion[regionKey]= regionObject;
                    }

                    var categoryKey = unit['type'];
                    var categoryObject = {
                        value: unit['type'],
                        label: unit['type']
                    }
                    if ( categoryKey != undefined && categoryKey != null  || ($scope.typeOfCategory[categoryKey] === undefined || $scope.typeOfCategory[categoryKey] ===null)) {
                        $scope.typeOfCategory[categoryKey] = categoryObject;
                    }

                    var cityKey = unit.location.code;
                    var cityObject = {
                        value:unit.location.code,
                        label:unit.location.code
                    }
                    if ( cityKey != undefined && cityKey != null  || ($scope.typeOfCity[cityKey] === undefined || $scope.typeOfCity[cityKey] ===null)) {
                        $scope.typeOfCity[cityKey] = cityObject;
                    }
                    gridItemArray.push(unit);
                    });
                    if($scope.allocateCostFinalUnits.length ==0){
                        $scope.gridOptions.data=gridItemArray;
                    }
                    $scope.gridOptions.rowTemplate = 'rowView.html';
                    $scope.gridOptions.columnDefs=$scope.showUnitsForAllocate();
                    $scope.totalLength=$scope.gridOptions.data.length;
                };


            $scope.clearFilters = function() {
                $scope.gridApi.grid.clearAllFilters();
                $timeout(function (){
                    $scope.filterBool = false;
                    $scope.$apply();
                },100);
            };


            $scope.selectVisibleRows  = function() {
                $scope.visibleRows=($scope.gridApi.core.getVisibleRows($scope.gridApi.grid)).length;
                $scope.gridApi.core.getVisibleRows($scope.gridApi.grid).forEach(function(row,index){
                    $scope.gridApi.selection.selectRow(row.entity);
                    $scope.allSelectedRows.push(row.entity.id);
                });
                $scope.selection = $scope.gridApi.selection.getSelectedRows();
                $scope.selectedRowsLength=$scope.selection.length;
                $timeout(function (){
                    $scope.$apply();
                })
                $scope.selectedRows = [];
                $scope.saveStateUiGrid = function() {
                    $scope.state = $scope.gridApi.saveState.save();
                };
            };

            $scope.unSelectVisibleRows  = function() {
                $scope.gridApi.core.getVisibleRows($scope.gridApi.grid).forEach(function(row,index){
                    $scope.gridApi.selection.unSelectRow(row.entity);
                    $scope.allUnSelectedRows.push(row.entity.id);
                });
                $scope.allUnSelectedRows = [];
                $scope.selection = $scope.gridApi.selection.getSelectedRows();
                $scope.selectedRowsLength=$scope.selection.length;
                $timeout(function (){
                    $scope.$apply();
                })
            };

            $scope.setAllSelectedRows = function (){
                $scope.gridOptions.data.forEach(function (row, index) {
                    if($scope.allSelectedRows.indexOf(row.id) != -1){
                        $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                    }else{
                        $scope.gridApi.selection.unSelectRow($scope.gridOptions.data[index]);
                    }
                });
                $timeout(function (){
                     $scope.$apply();
                })
            }

            $scope.setAllUnSelectedRows = function (){
                $scope.gridOptions.data.forEach(function (row, index) {
                    if($scope.allSelectedRows.indexOf(row.id) != -1){
                        $scope.gridApi.selection.unSelectRow($scope.gridOptions.data[index]);
                    }else{
                        $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                    }
                });
                $timeout(function (){
                    $scope.$apply();
                })
            }

            $scope.showUnitsForAllocate = function () {
                return [
                    {
                        field: 'code',
                        name: 'code',
                        enableCellEdit: false,
                        displayName: 'Unit ID',
                        enableFiltering:true,
                        enableSelectAll: false,
                    },{
                        field: 'name',
                        name: 'name',
                        displayName: 'Unit Name',
                        enableFiltering:true,
                        enableSelectAll: false,
                    }
                    ,{
                        field: 'type',
                        name: 'type',
                        enableCellEdit: false,
                        displayName: 'Category',
                        enableFiltering:true,
                        enableSelectAll: false,
                        filter: { selectOptions: Object.values($scope.typeOfCategory), type: uiGridConstants.filter.SELECT}
                    }, {
                        field: 'status',
                        name: 'status',
                        enableCellEdit: false,
                        displayName: 'Status',
                        enableFiltering:true,
                        enableSelectAll: false,
                    },{
                        field: 'location.code',
                        name: 'location.code',
                        enableCellEdit: false,
                        displayName: 'City',
                        enableFiltering:true,
                        enableSelectAll: false,
                        filter: { selectOptions: Object.values($scope.typeOfCity), type: uiGridConstants.filter.SELECT}
                    }
                    ,{
                        field: 'region',
                        name: 'region',
                        enableCellEdit: false,
                        displayName: 'Region',
                        enableFiltering:true,
                        enableSelectAll: false,
                        filter: { selectOptions: Object.values($scope.typeOfRegion), type: uiGridConstants.filter.SELECT}
                    }, {
                        field: 'unitZone',
                        name: 'unitZone',
                        enableCellEdit: false,
                        displayName: 'Zone',
                        enableFiltering:true,
                        enableSelectAll: false,
                        filter: { selectOptions: typeOfZone, type: uiGridConstants.filter.SELECT}
                    }
                ];
            };

            $scope.previewUiGrid=function(){
                $scope.clearFilters();
                $scope.uiGridViewFlag=false;
                $scope.previewUiGridFlag=true;
                $scope.mainGridRows = angular.copy($scope.gridOptions.data);
                 $scope.previewRows = $scope.gridApi.selection.getSelectedRows();
                 $scope.gridOptions.data = $scope.previewRows;
            }

            function preSelectGridData (data){
                $scope.gridOptions.data = data;
                $timeout(function (){
                    $scope.gridApi.grid.modifyRows(data);
                });
                $timeout(function (){
                    $scope.gridOptions.data.forEach(function (row, index) {
                        if($scope.type =="CAPEX"){
                               if(!appUtil.isEmptyObject($scope.selectedUnitForCapex)  &&   row.code == $scope.selectedUnitForCapex.code){
                                    $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                               }else{
                                    $scope.gridApi.selection.unSelectRow($scope.gridOptions.data[index]);
                               }
                        }else{
                        if (row.checked) {
                            $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                            } else {
                                    $scope.gridApi.selection.unSelectRow($scope.gridOptions.data[index]);
                            }
                        }
                    });
                });
                $timeout(function (){
                    $scope.$apply();
                })
            }

            $scope.submitAllocateCost = function () {
            preSelectGridData($scope.mainGridRows);
            $scope.allUnitIdForSubmission=[];
            $scope.gridApi.selection.getSelectedRows().forEach(function(row){
            $scope.allUnitIdForSubmission.push(row);
            });
            var unitId;
            if ($scope.type == "CAPEX") {
                for (var key in $scope.units) {
                    unitId=$scope.units[key].code;
                }
                unitId = $scope.selectedUnitForCapex.code;
                $http({
                    url: apiJson.urls.serviceOrderManagement.checkUnitBudget,
                    method: "GET",
                    params: {
                        unitId: unitId
                    }
                    }).then(function (response) {
                        if (response.data) {
                            $scope.submitData();
                        }
                        else {
                            $toastService.create("No budget is Allocated For This Unit.");
                        }
                    }, function (response) {
                        console.log(response);
                    });
                }
                else {
                    $scope.submitData();
                }
            }

            $scope.submitData = function () {
                var returnResult = {};
                var tempList=[];
                for(var i in $scope.units){
                if($scope.type == "CAPEX"){
                          if($scope.gridOptions.data[i].code == $scope.selectedUnitForCapex.code){
                                $scope.gridOptions.data[i].checked = true;
                        }else{
                        $scope.gridOptions.data[i].checked = false;
                        }
                        tempList.push($scope.gridOptions.data[i]);
                }else{
                    returnResult[i]=$scope.units[i];
                    tempList.push($scope.gridOptions.data[i]);
                }}
                Popeye.closeCurrentModal({units : tempList});
                console.log("result is ",tempList);
            }

            $scope.backToUiGridViewFag=function(){
                preSelectGridData($scope.mainGridRows);
                $scope.uiGridViewFlag=true;
                $scope.previewUiGridFlag=false;
            }
            $scope.cancelAllocateCost = function () {
                Popeye.closeCurrentModal();
            };

        }]).controller("allocatedCostModalCtrl",
    ['$rootScope', '$scope', 'appUtil', '$toastService', 'metaDataService', 'Popeye', 'allUnits', 'selectedUnits', 'item', 'srItems', 'companies', 'costElementId','selectedType','soType',
        function ($rootScope, $scope, appUtil, $toastService, metaDataService, Popeye, allUnits, selectedUnits, item, srItems, companies, costElementId,selectedType,soType) {


            $scope.initCostModal = function () {
                $scope.unitList = allUnits;
                $scope.item = item;
                $scope.totalAmt = item.totalCost;
                $scope.selectedUnits = selectedUnits;
                $scope.allItems = srItems;
                $scope.companies = companies;
                $scope.isDisabled = true;
                $scope.mumbCheck = true;
                $scope.bangCheck = true;
                $scope.ncrCheck = true;
                $scope.puneCheck = true;
                $scope.naviMumbaiCheck = true;
                $scope.chennaiCheck = true;
                $scope.hyderabadCheck = true;
                $scope.costElementId = costElementId;
                $scope.createUnitList();
                $scope.updateUsedAmount();
                metaDataService.getAllBusinessCostCenters(function (bcc) {
                    $scope.oldUnitList = bcc;
                });
                $scope.selectedType = selectedType;
            };

            $scope.resetModel = function () {
                $scope.usedAmt = 0;
                $scope.remainingAmt = 0;
                $scope.isDisabled = false;
                //$scope.allItems = [];
                $scope.units = $scope.oldUnitList;
                $scope.unitList = $scope.oldUnitList;
                for (var key in $scope.allItems) {
                    if (key.startsWith($scope.item.costElementId + "_")) {
                        delete $scope.allItems[key];
                    }
                }
            }

            $scope.editModel = function () {
                $scope.isDisabled = false;
            }

            $scope.createUnitList = function () {
                var i = 0;
                for (var key in $scope.allItems) {
                    i++;
                    if ($scope.allItems[key].costElementId == $scope.costElementId) {
                        for (var j = 0; j < $scope.unitList.length; j++) {
                            if ($scope.allItems[key].businessCostCenterId == $scope.unitList[j].id) {
                                $scope.unitList[j].allocatedCost = $scope.allItems[key].totalCost;
                                $scope.unitList[j].qty = $scope.allItems[key].requestedQuantity;
                                //$scope.createDefaultBcc($scope.unitList[j]);
                            }
                            else {
                                if (i == 1) {
                                    $scope.unitList[j].allocatedCost = '';
                                    $scope.unitList[j].qty = 0;
                                }
                            }
                        }
                    }else{
                        for (var j = 0; j < $scope.unitList.length; j++) {
                            if($scope.unitList[j].code === $scope.allItems[key].unitId){
                                $scope.unitList[j].allocatedCost = '';
                                $scope.unitList[j].qty = 0;    
                            }
                        }
                }
                }
                $scope.units = $scope.unitList;
            }


            $scope.createUnitListOld = function () {
                var keys = Object.keys($scope.selectedUnits);
                var len = keys.length;
                var i = 0;
                for (var key in $scope.selectedUnits) {
                    i++;
                    for (var j = 0; j < $scope.unitList.length; j++) {
                        if ($scope.selectedUnits[key].id == $scope.unitList[j].id) {
                            $scope.unitList[j].allocatedCost = $scope.selectedUnits[key].allocatedCost;
                            $scope.unitList[j].qty = $scope.selectedUnits[key].qty;
                            //$scope.createDefaultBcc($scope.unitList[j]);
                        }
                        else {
                            if (i == 1) {
                                $scope.unitList[j].allocatedCost = '';
                                $scope.unitList[j].qty = 0;
                            }
                        }
                    }
                }
                $scope.units = $scope.unitList;
            }

            $scope.createDefaultBcc = function (unit) {
                var item = angular.copy($scope.item);
                if (appUtil.isEmptyObject($scope.allItems)) {
                    $scope.allItems = {};
                }
                var qty = parseFloat(parseFloat(unit.allocatedCost) / parseFloat(item.unitPrice)).toFixed(2);
                var tax = parseFloat(parseFloat(unit.allocatedCost) * (parseFloat(item.taxRate) / 100)).toFixed(2);
                var key = item.costElementId + "_" + unit.id;
                var obj = prepareObj(unit, item, qty, tax);
                if (appUtil.isEmptyObject($scope.allItems[key])) {
                    $scope.allItems[key] = {};
                }
                // add item specific to SO item and to that specific unit
                $scope.allItems[key] = obj;
            }

            $scope.updateUsedAmount = function () {
                $scope.usedAmt = 0;
                $scope.remainingAmt = 0;
                for (var key in $scope.allItems) {
                    if ($scope.allItems[key].costElementId == $scope.costElementId) {
                        for (var j = 0; j < $scope.unitList.length; j++) {
                            if ($scope.allItems[key].businessCostCenterId == $scope.unitList[j].id) {
                                $scope.usedAmt = (parseFloat($scope.usedAmt) + parseFloat($scope.unitList[j].allocatedCost == "" ? 0 : $scope.unitList[j].allocatedCost)).toFixed(2);
                            }
                        }
                    }
                }
                $scope.remainingAmt = (parseFloat($scope.totalAmt) - parseFloat($scope.usedAmt)).toFixed(2);
            }


            function getTotalCost() {
                var cost = 0;
                $scope.units.forEach(function (unit) {
                    cost += (appUtil.isEmptyObject(unit.allocatedCost) || isNaN(unit.allocatedCost))
                        ? 0 : parseFloat(unit.allocatedCost);
                });
                return cost;
            }

            function prepareObj(bcc, item, qty, tax) {
                console.log("checking quantity" + qty);
                return {
                    businessCostCenterId: bcc.id,
                    businessCostCenterName: bcc.name,
                    costElementId: item.costElementId,
                    costElementName: item.costElementName,
                    costElementDate: item.costElementDate,
                    costElementToDate: item.costElementToDate,
                    departmentName: item.departmentName,
                    departmentId : item.departmentId,
                    company: bcc.company,
                    location: bcc.location,
                    state: bcc.state,
                    requestedQuantity: parseFloat(qty),
                    ascCode: item.ascCode,
                    serviceDescription: item.serviceDescription,
                    unitOfMeasure: item.unitOfMeasure,
                    unitPrice: item.unitPrice,
                    totalCost: parseFloat(bcc.allocatedCost),
                    totalAmount: parseFloat(parseFloat(bcc.allocatedCost) + parseFloat(tax)),
                    taxRate: parseFloat(item.taxRate),
                    totalTax: parseFloat(tax),
                    type: item.type,
                    unitId: bcc.code,
                    vendorName: item.vendorName
                };
            }

            $scope.updateQty = function (unit) {
                var item = angular.copy($scope.item);
                var allocatedTillNow = getTotalCost(unit.id);
                if (!appUtil.isEmptyObject(item.totalCost) && !isNaN(item.totalCost)
                    && parseFloat(item.totalCost) >= allocatedTillNow) {
                    if (appUtil.isEmptyObject($scope.allItems)) {
                        $scope.allItems = {};
                    }
                    console.log("creating quantity" + unit.allocatedCost + "   " + item.unitPrice)
                    var qty = parseFloat(parseFloat(unit.allocatedCost) / parseFloat(item.unitPrice)).toFixed(2);
                    var tax = parseFloat(parseFloat(unit.allocatedCost) * (parseFloat(item.taxRate) / 100)).toFixed(2);
                    var key = item.costElementId + "_" + unit.id;
                    var obj = prepareObj(unit, item, qty, tax);
                    if(soType==="BULK_SO"){
                        obj['vendor'] = unit.vendor;
                        obj['vendorLoc'] = unit.vendorLoc;
                    }
                   
                    if (appUtil.isEmptyObject($scope.allItems[key])) {
                        $scope.allItems[key] = {};
                    }
                    // add item specific to SO item and to that specific unit
                    $scope.allItems[key] = obj;
                    unit.qty = isNaN(qty) ? 0 : qty;
                    $scope.totalAllocated = allocatedTillNow;
                    $scope.updateUsedAmount();
                } else {
                    if (appUtil.isEmptyObject(unit.allocatedCost)) {
                        $toastService.create("Select a valid cost to be allocated");
                    }
                    if (parseFloat(item.totalCost) < allocatedTillNow) {
                        $toastService.create("Total Allocated Cost cannot be less than cost across units");
                    }
                }
            };


            $scope.cancel = function () {
                Popeye.closeCurrentModal();
            };

            $scope.submit = function () {
                var isNeg = false;
                for(var i =0 ; i< $scope.units.length; i++){
                     if($scope.units[i].allocatedCost < 0){
                                $toastService.create("Allocated Cost cannot be negative for unit "+ $scope.units[i].name +", Please enter correct cost.");
                                isNeg = true;   
                     }           
                        }
                        
                        if(isNeg) return;
                var returnResult = {};
                for (var key in $scope.allItems) {
                    if (!appUtil.isEmptyObject($scope.allItems[key].totalCost)
                        && !isNaN($scope.allItems[key].totalCost)) {
                        returnResult[key] = $scope.allItems[key];
                    }
                }
                Popeye.closeCurrentModal(returnResult);
            };


        }]).controller('departmentSOModalCtrl', ['$scope', 'appUtil', 'apiJson', '$http', 'Popeye', 'receivingList', '$toastService', '$alertService', 'type',
    function ($scope, appUtil, apiJson, $http, Popeye, receivingList, $toastService, $alertService, type) {


        $scope.init = function () {
            $scope.summaryDepartmentList = [];
            $scope.summaryItem = [];
            $scope.list = receivingList;
            $scope.type = type;
            $scope.summaryList();
            $scope.isForAdjustments = false;
            $scope.endDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
            $scope.gridOptions = appUtil.getGridOptions();
        }

        $scope.summaryList = function () {
            var map = new Map();
                for (var x = 0; x < $scope.list.length; x++) {
                    if (map.has($scope.list[x].departmentName)) {
                        var listData = map.get($scope.list[x].departmentName);
                        $scope.list[x].totalAmountDup = listData.totalAmountDup + $scope.list[x].totalAmount;
                        $scope.list[x].totalTaxDup = listData.totalTaxDup + $scope.list[x].totalTax;
                        $scope.list[x].totalCostDup = listData.totalCostDup + $scope.list[x].totalCost;
                        $scope.list[x].costElementIds = listData.costElementIds;
                        $scope.list[x].costElementIds.push($scope.list[x].costElementId);
                        map.delete($scope.list[x].departmentName);
                        map.set($scope.list[x].departmentName, $scope.list[x]);
                    } else {
                        $scope.list[x].totalAmountDup = $scope.list[x].totalAmount;
                        $scope.list[x].totalTaxDup = $scope.list[x].totalTax;
                        $scope.list[x].totalCostDup = $scope.list[x].totalCost;
                        $scope.list[x].costElementIds = [];
                        $scope.list[x].costElementIds.push($scope.list[x].costElementId);
                        map.set($scope.list[x].departmentName, $scope.list[x]);
                    }
                }
            if ($scope.type == "CAPEX") {
                $scope.getDepartmentData(map);
            } else {
                angular.forEach(map, function (value, key) {
                    $scope.summaryItem.push(value);
                });
                $scope.closeModal({checked : false,
                    isClosed : false});
            }
            }

        $scope.showSOs = function () {
            return [
                {
                    field: 'soId',
                    name: 'serviceOrderId',
                    enableCellEdit: false,
                    displayName: 'SO Id'
                },{
                field: 'id',
                    name: 'serviceOrderItemId',
                    displayName: 'Service Order Item id'
                }
                ,{
                    field: 'costElementName',
                    name: 'costElementName',
                    enableCellEdit: false,
                    displayName: 'Cost Element Name'
                }, {
                    field: 'vendorName',
                    name: 'vendorName',
                    enableCellEdit: false,
                    displayName: 'Vendor Name'
                },{
                    field: 'serviceDescription',
                    name: 'serviceDescription',
                    enableCellEdit: false,
                    displayName: 'Service Description',
                }, {
                    field: 'totalCost',
                    name: 'totalCost',
                    enableCellEdit: false,
                    displayName: 'Cost'
                }, {
                    field: 'totalTax',
                    name: 'totalTax',
                    enableCellEdit: false,
                    displayName: 'Tax'
                }, {
                    field: 'amountPaid',
                    name: 'amountPaid',
                    enableCellEdit: false,
                    displayName: 'Total Amount'
                },{
                    field: 'receivingAmount',
                    name: 'receivingAmount',
                    enableCellEdit: false,
                    displayName: 'Receiving Amount'
                },{
                    field: 'paidAmount',
                    name: 'paidAmount',
                    enableCellEdit: false,
                    displayName: 'Paid Amount',
                },
                ,{
                    field: 'status',
                    name: 'status',
                    enableCellEdit: false,
                    displayName: 'Status',
                }
            ];
        };

        $scope.showPrevSOs = function (srItem) {
            $scope.selectedItem = srItem.departmentName;
            if (srItem.expanded != null) {
                srItem.expanded = !srItem.expanded;
            } else {
                srItem.expanded = true;
            }
            var gridItemaArr = [];
            srItem.serviceOrderShortList.forEach(function (so){
                so.orderItems.forEach(function (soItem){
                    gridItemaArr.push(soItem);
                })
            })
            if(srItem.expanded === true){
                $scope.gridOptions.data=gridItemaArr;
                $scope.gridOptions.columnDefs=$scope.showSOs();
            }
            if(srItem.expanded === false){
                $scope.selectedItem = null;
            }
        }

        $scope.isSelectedItem = function (srItem) {
            return srItem.expanded == true;
        }

        $scope.getDepartmentData = function (map) {
            angular.forEach(map, function (value, key) {
                    $scope.summaryDepartmentList.push(value);
            });
            $http({
                url: apiJson.urls.serviceOrderManagement.getDepartmentData,
                method: "POST",
                data: $scope.summaryDepartmentList
            }).then(function (response) {
                if (response.data) {
                    $scope.summaryItem = response.data;
                    angular.forEach($scope.summaryItem, function (item) {
                        var totSOAmt = 0;
                        var totPaidAmt = 0;
                        var totRecAmt = 0;
                        var vendorName = "";
                        var totalCost= 0;
                        var totalTax = 0;
                        angular.forEach(item.serviceOrderShortList, function (so) {
                            var receiveAmt = 0;
                            vendorName = so.vendorName;
                            angular.forEach(so.orderItems, function (orderItem) {
                                var receivingPrice = orderItem.receivedQuantity * orderItem.unitPrice;
                                var receiving = (receivingPrice + (receivingPrice * orderItem.taxRate) / 100);
                                receiveAmt += receiving;
                                orderItem.receivingAmount = receiving.toFixed(2);
                                orderItem.soId = so.id;
                                orderItem.vendorName = so.vendorName;
                                orderItem.status=so.status;
                                totSOAmt += orderItem.totalCost +orderItem.totalTax;
                                totPaidAmt += (orderItem.paidAmount === null ? 0 : orderItem.paidAmount);
                                totalCost +=(orderItem.totalCost=== null ? 0 : orderItem.totalCost);
                                totalTax +=(orderItem.totalTax === null ? 0 : orderItem.totalTax);
                            });
                            so.totalReceiveAmt = receiveAmt;
                            totRecAmt += ( so.totalReceiveAmt === null ? 0 : so.totalReceiveAmt );
                            so.vendorName = vendorName;
                        });
                        item.totalSosAmount = totSOAmt;
                        item.totalPaidAmount = totPaidAmt;
                        item.totalRecieveAmount = totRecAmt;
                        item.totalCost=totalCost;
                        item.totalTax=totalTax;
                    });
                }
            }, function (response) {
                console.log(response);
            });
        }


        $scope.filterBySelectedType = function (item) {
            return item.type === $scope.type;
        };

        $scope.submit = function () {
            var check = false;
            for (var x = 0; x < $scope.summaryItem.length; x++) {
                if ($scope.summaryItem[x].totalAmountDup > $scope.summaryItem[x].remainingAmount) {
                    $toastService.create("Budget Exceeded for " + $scope.summaryItem[x].departmentName + " Department.");
                    check = true;
                }
            }
            if (!check) {
                $scope.closeModal({
                    checked: check,
                    isClosed: false
                });
            }
        }

        $scope.cancel = function () {
            $scope.closeModal({
                checked: true,
                isClosed: true
            });
        }

        $scope.closeModal = function closeModal(obj) {
            Popeye.closeCurrentModal(obj);
        }


    }]).controller('uploadedSoSheetCtrl', ['$rootScope','$scope', 'appUtil','apiJson','$http' ,'Popeye','result','costElement','costCenter','defaultVendor','defaultVendorLoc','defaultLocList',"$toastService",'metaDataService',
    function ($rootScope,$scope, appUtil,apiJson,$http ,Popeye,result,costElement,costCenter,defaultVendor,defaultVendorLoc,defaultLocList,$toastService,metaDataService) {

        $scope.init = function (){
            $scope.result = result.map(function(el){
                el['selectedVendor'] = defaultVendor;
                el['selectedLocation'] = defaultVendorLoc;
                el['locationList'] = defaultLocList;
                return el;
            });

            $scope.costElVendorList = [];
            $scope.getCostElementVendorList(costElement.id,costCenter.id);
            $scope.totalOfAllocatedCost = $scope.getTotal(result);
            $scope.costElement = costElement;
            $scope.vendorLocalCache = {};
        };

        $scope.getCostElementVendorList = function(costElementId,costcenterId){
            $http({
                url: apiJson.urls.serviceOrderManagement.getCostElementVendorList,
                method: "GET",
                params: {
                    costCenterId: costcenterId,
                    costElementId : costElementId 
                }
            }).then(function (response) {
                $scope.costElVendorList = $scope.filterByActiveVendors(response.data);
            }, function (response) {
                console.log(response);
            });
        }

        $scope.filterByActiveVendors = function(vendorList){
            return vendorList.filter(function (vendor) {
                return  vendor.status == "ACTIVE";
            });
        }
        $scope.getTotal = function (units) {
            var total = 0;
            units.forEach(function (unit) {
                total = total + unit.allocatedCost;
            });
            return total;
        };

        $scope.selectVendorLocation = function (vendor,item) {
            if (vendor.vendorBlocked != undefined && vendor.vendorBlocked != null && vendor.vendorBlocked == 'Y') {
                if (vendor.blockedReason == 'MANUAL') {
                    $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                        "<br><b>Please Contact Finance Team..!</b>", function () {
                    }, true);
                } else {
                    $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                        "<br><b>Please Settle the Vendor Advances related to advance payments of : " + vendor.blockedReason + "</b>", function () {}, true);
                }
                $scope.selectedVendor = null;
                $timeout(function () {
                    $('#vendorList').val('').trigger('change');
                });
                return;
            }
            
            item.locationList = null;
            item.selectedVendor = vendor;
            item.selectedLocation = null;
            
            
            if($scope.vendorLocalCache[item.selectedVendor.vendorId]){
                item.locationList = $scope.vendorLocalCache[item.selectedVendor.vendorId];
               return;
            }
            
            metaDataService.getVendorLocations(item.selectedVendor.vendorId, function (locations) {
                item.locationList = locations;
                $scope.vendorLocalCache[item.selectedVendor.vendorId] = locations;
            });
        };

        $scope.cancel = function(){
            $scope.result = [];
            $scope.costElVendorList = [];

            $scope.costElement = null;
            $scope.vendorLocalCache = {};
            Popeye.closeCurrentModal({isSuccessful :false});
        }

        $scope.submit = function(){
                var isClose = true;
            $scope.result.forEach(function(el){
                 if(!el.selectedVendor || !el.selectedLocation){
                    isClose = false;
                    $toastService.create("Dispatch location or Vendor Missing for unitId  "+el.unitId);
                }
                $scope.selectedVendorMap[el.selectedVendor.vendorId] =  el.selectedVendor.entityName;
            });
            
            if(!isClose) return;
            

            Popeye.closeCurrentModal({isSuccessful : true,
                totalOfAllocatedCost : angular.copy($scope.totalOfAllocatedCost),
                soData : angular.copy($scope.result)
            });
        }
    }]);
