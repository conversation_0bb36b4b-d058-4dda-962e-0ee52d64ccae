/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('searchDebitNoteCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService', '$alertService','$timeout',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $alertService,$timeout) {

            $scope.init = function () {
                $scope.selectView = true;
                $scope.selectedStatus = null;
                $scope.unitList = [{id: null, name: ""}];
                $scope.selectedUnit = null;
                $scope.vendorList = [];
                $scope.selectedVendor = null;
                $scope.statusList = ["INITIATED","CREATED","REJECTED"];
                $scope.selectedStatus = null;
              //  $scope.getEmployeeMappedUnits();
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.companyList =  appUtil.getCompanyList();
                $scope.selectedCompany =appUtil.getDefaultCompany();
                $scope.getCompanyMappedUnits();
                $scope.companyMap = appUtil.getCompanyMap();
            };
            
            $scope.getCompanyMappedUnits = function() {
            	$scope.unitList = [{id: null, name: ""}];
            	$scope.selectedUnit = null;
            	appUtil.getUnitList().map(function (unit) {
            		if ($scope.selectedCompany.id == unit.companyId) {
            			$scope.unitList.push(unit);
            		}
            	});
            	if ($scope.unitList != null && $scope.unitList.length > 0) {
            		$scope.selectedUnit = $scope.unitList[0];
            	}
            	resetUnit();
            };
            
            function resetUnit(){
              	 $timeout(function () {
              		 $('#unitListSearchDebitNote').val('').trigger('change');
              	 });
            }

            $scope.getEmployeeMappedUnits = function () {
                appUtil.getUnitList().map(function (unit) {
                    if ($rootScope.mappedUnits.indexOf(unit.id) >= 0) {
                        $scope.unitList.push(unit);
                    }
                });
                if ($scope.unitList != null && $scope.unitList.length > 0) {
                    $scope.selectedUnit = $scope.unitList[0];
                }
            };

            $scope.selectUnit = function (unit) {
                $scope.selectedUnit = unit;
                $scope.getMappedVendors();
            };

            $scope.getMappedVendors = function () {
                console.log($scope.selectedUnit);
                if ($scope.selectedUnit.id !== null) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.vendorManagement.unitVendors + "?unitId=" + $scope.selectedUnit.id
                    }).then(function success(response) {
                        response.data.map(function (item) {
                            if (item.mappingStatus === "ACTIVE") {
                                $scope.vendorList.push(item.vendor);
                            }
                        })
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                } else {
                    $scope.vendorList = [{
                        id: null,
                        name: ""
                    }];
                    $scope.selectedVendor = $scope.vendorList[0];
                }

            };

            $scope.findDebitNotes = function () {
                $scope.showNoDN = false;
                if ($scope.startDate == null || $scope.startDate == "") {
                    $toastService.create("Please select start date");
                } else if ($scope.endDate == null || $scope.endDate == "") {
                    $toastService.create("Please select end date");
                } else {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.paymentRequestManagement.getDebitNotes,
                        params: {
                            unitId: $scope.selectedUnit == null ? null : $scope.selectedUnit.id,
                            startDate: $scope.startDate,
                            endDate: $scope.endDate,
                            vendorId: $scope.selectedVendor == null ? null : $scope.selectedVendor.id,
                            prId: $scope.prId,
                            dnId: $scope.dnId,
                            invoiceNumber: $scope.invoiceNumber,
                            companyId : $scope.selectedCompany.id,
                            status : $scope.selectedStatus
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.dns = response.data;
                        } else {
                            $scope.dns = [];
                            $scope.showNoDN = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            };

            $scope.settleDebitNote = function (dn) {
                $alertService.confirm("Are you sure?", "", function () {
                    $rootScope.showFullScreenLoader = true;
                    dn.lastUpdatedBy = appUtil.createGeneratedBy();
                    $http({
                        url: apiJson.urls.paymentRequestManagement.settleDebitNote,
                        method: 'POST',
                        data: dn
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null) {
                            $scope.findDebitNotes();
                        } else {
                            $toastService.create("Could not find debit note.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                        } else {
                            $toastService.create("Could not find debit note.");
                        }
                    });
                });
            };

        }]
    );
