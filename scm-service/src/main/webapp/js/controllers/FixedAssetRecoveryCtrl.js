angular.module('scmApp').controller('FixedAssetRecoveryCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
    '$location', 'fileService', '$alertService','$toastService','metaDataService','$timeout','$filter','$window','$toastService',
    function ($rootScope, $scope, apiJson, $http, appUtil, $location, fileService, $alertService,$toastService, metaDataService,$timeout, $filter,$window,$toastService) {

        $scope.init = function () {

            $scope.showScreen = 'LOST';
            $scope.selectedUnit = null;
            $scope.statusList = ['PENDING', 'RECOVERED', 'PARTIAL_RECOVERED'];
            $scope.selectedStatus = 'PENDING';
            $scope.selectedAssetName = null;
            $scope.selectedAssetId = null;

            var currentDate = appUtil.getCurrentBusinessDate();
//            $scope.endDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
            $scope.date={'startDate':null, 'endDate':appUtil.formatDate(currentDate, "yyyy-MM-dd")};
            $scope.unitList = null;
            $scope.assetList = null;
            $scope.statusAssetMap = null;
            $scope.getAllUnits();
            $scope.recoverFrom = {'name':null,'amount':null};
            $scope.insurance = {'name':null,'amount':null};
            $scope.allDone = {'insurance':false,'employee':false};
            $scope.writeOffAmount = 0;
            $scope.amountLeft = 0;
            $scope.employeeRecoveryList = [];
            $scope.insuranceRecoveryList = [];
            $scope.skipInsuranceRecovery = false;
            $scope.skipEmployeeRecovery = false;
            $scope.submitCompleted = false;
            $scope.maxDate = appUtil.getCurrentBusinessDate();

        };

            $scope.Add = function (type) {
                if(type == 'EMPLOYEE'){
                    var employee = {};
                    if($scope.recoverFrom.name != null && $scope.recoverFrom.name > 0 &&
                        $scope.recoverFrom.amount != null && $scope.recoverFrom.amount > 0){
                            employee.Name = $scope.recoverFrom.name;
                            employee.Amount = $scope.recoverFrom.amount;
                            $scope.employeeRecoveryList.push(employee);
                            $scope.amountLeft = $scope.amountLeft - employee.Amount;
                            if($scope.amountLeft < 0){
                                $scope.amountLeft = 0;
                            }
                            $scope.recoverFrom.amount = $scope.amountLeft;
                        }
                    else{
                        $toastService.create("Invalid values for Employee Id or Amount");
                        $scope.recoverFrom.amount = $scope.amountLeft;
                    }

                }else if(type == 'INSURANCE'){
                    var insurance = {};
                    if($scope.insurance.name != null && $scope.insurance.name != '' &&
                        $scope.insurance.amount != null && $scope.insurance.amount > 0){
                            insurance.Name = $scope.insurance.name;
                            insurance.Amount = $scope.insurance.amount;
                            $scope.insuranceRecoveryList.push(insurance);
//                            $scope.amountLeft =  $scope.amountLeft - insurance.Amount;
//                            if($scope.amountLeft < 0){
//                                $scope.amountLeft = 0;
//                            }
                        }
                    else{
                        $toastService.create("Invalid values for Insurance Name or Amount");
                    }
                }
                //Clear the TextBoxes.
                $scope.recoverFrom.name = null;
//                $scope.recoverFrom.amount = null;
                $scope.insurance.name = null;
//                $scope.insurance.amount = null;
            };

            $scope.Remove = function (index , type) {
                if(type == 'EMPLOYEE'){
                    //Find the record using Index from Array.
                    var name = $scope.employeeRecoveryList[index].Name;

                    if ($window.confirm("Do you want to delete: " + name)) {
                        $scope.amountLeft = $scope.amountLeft + $scope.employeeRecoveryList[index].Amount;
                        $scope.recoverFrom.amount = $scope.amountLeft;
                        //Remove the item from Array using Index.
                        $scope.employeeRecoveryList.splice(index, 1);
                    }
                }else if(type == 'INSURANCE'){
                    //Find the record using Index from Array.
                    var name = $scope.insuranceRecoveryList[index].Name;

                    if ($window.confirm("Do you want to delete: " + name)) {
//                    $scope.amountLeft = $scope.amountLeft + $scope.insuranceRecoveryList[index].Amount;
//                    $scope.insurance.amount = $scope.amountLeft;
                        $scope.insurance.amount = null;
                        $scope.insurance.name = null;
                        //Remove the item from Array using Index.
                        $scope.insuranceRecoveryList.splice(index, 1);
                    }
                }
            };

          $scope.goto = function(screen){
            if(screen == "EMPLOYEE"){
                $scope.showInsurance= false;
                $scope.showEmployee = true;
                $scope.recoverFrom.amount = $scope.amountLeft;
            }else if(screen == "INSURANCE"){
                $scope.showInsurance= true;
                $scope.showEmployee = false;
//                $scope.insurance.amount = $scope.amountLeft;
            }

          };

          $scope.initiateRecovery = function(recoveryItem){
                $scope.showInsurance= true;
                $scope.showEmployee = false;
                $scope.recoveryId = recoveryItem.recoveryId;
                $scope.amountLeft = recoveryItem.amountToRecover - (recoveryItem.employeeRecoveryAmount != null ? recoveryItem.employeeRecoveryAmount : 0);
//                if(recoveryItem.insuranceRecoveryStatus == 'PENDING'){
//                    $scope.insurance.amount = $scope.amountLeft;
//                }
//                else
                if(recoveryItem.insuranceRecoveryStatus == 'RECOVERED'){
                    $scope.allDone.insurance = true;
//                    $scope.recoverFrom.amount = $scope.amountLeft;
                }
                if(recoveryItem.employeeRecoveryStatus == 'RECOVERED'){
                    $scope.allDone.employee = true;
//                    $scope.insurance.amount = $scope.amountLeft;
                }
          };

          $scope.skipRecovery = function(type){
                if(type == 'EMPLOYEE'){
                    if( !$scope.skipEmployeeRecovery && $scope.employeeRecoveryList.length > 0){
                         for(var index = $scope.employeeRecoveryList.length-1 ; index >= 0 ;index--){
                            $scope.Remove(0,'EMPLOYEE');
                         }
                    }
                    $scope.skipEmployeeRecovery = !$scope.skipEmployeeRecovery;
                }else if(type == 'INSURANCE'){
                    if($scope.skipInsuranceRecovery && $scope.insuranceRecoveryList.length > 0){
                        $scope.Remove(0,'INSURANCE');
                    }
                    $scope.skipInsuranceRecovery = !$scope.skipInsuranceRecovery;
                }
          };

          $scope.backToHome = function (){
              $scope.findAssets();
              $scope.employeeRecoveryList = [];
              $scope.insuranceRecoveryList = [];
              $scope.recoveryDetailList = [];
              $scope.skipInsuranceRecovery = false;
              $scope.skipEmployeeRecovery = false;
              $scope.writeOffAmount = 0;
              $scope.amountLeft = 0;
              $scope.employeeRecoveryAmount = 0;
              $scope.insuranceRecoveryAmount = 0;
              $scope.allDone.insurance = false;
              $scope.allDone.employee = false;
          };

          $scope.submitRecovery = function(){

             if($scope.employeeRecoveryList.length ==0 && $scope.insuranceRecoveryList.length == 0){
                if(!$scope.skipInsuranceRecovery && !$scope.skipEmployeeRecovery){
                    $toastService.create("Please add an employee or insurance recovery.");
                    return;
                }
             }
             $scope.writeOffAmount = $scope.amountLeft;
             var totalAmount = 0;
             for(var i=0; i< $scope.employeeRecoveryList.length; i++){
                totalAmount = totalAmount + $scope.employeeRecoveryList[i].Amount;
             }
             $scope.employeeRecoveryAmount = totalAmount;

             $scope.recoveryDetailList = [];
             if(!$scope.skipInsuranceRecovery && !$scope.allDone.insurance && $scope.insuranceRecoveryList.length > 0){
                 $scope.recoveryDetailList.push({
                    'recoveryId':$scope.recoveryId,
                    'recoveryType': 'INSURANCE',
                    'recoveryStatus': 'RECOVERED',
                    'amountRecovered': $scope.insuranceRecoveryList[0].Amount,
                    'recoveredFrom':{'id': null, 'code': null, 'name': $scope.insuranceRecoveryList[0].Name}
                 });
                 $scope.allDone.insurance = true;
             } else if($scope.skipInsuranceRecovery && !$scope.allDone.insurance){
                  $scope.recoveryDetailList.push({
                     'recoveryId':$scope.recoveryId,
                     'recoveryType': 'INSURANCE',
                     'recoveryStatus': 'NOT_RECOVERING',
                     'amountRecovered': 0,
                     'recoveredFrom':{'id':null, 'code':null, 'name': null}
                  });
                  $scope.allDone.insurance = true;
             }
             if(!$scope.skipEmployeeRecovery && !$scope.allDone.employee && $scope.employeeRecoveryList.length > 0){
                  for(var i=0; i< $scope.employeeRecoveryList.length; i++){
                      $scope.recoveryDetailList.push({
                         'recoveryId':$scope.recoveryId,
                         'recoveryType': 'EMPLOYEE',
                         'recoveryStatus': 'RECOVERED',
                         'amountRecovered': $scope.employeeRecoveryList[i].Amount,
                         'recoveredFrom':{'id' : $scope.employeeRecoveryList[i].Name,'code':null,'name': null}
                      });
                  }
                  $scope.allDone.employee = true;
             } else if($scope.skipEmployeeRecovery && !$scope.allDone.employee){
                  $scope.recoveryDetailList.push({
                     'recoveryId' : $scope.recoveryId,
                     'recoveryType' : 'EMPLOYEE',
                     'recoveryStatus' : 'NOT_RECOVERING',
                     'amountRecovered' : 0,
                     'recoveredFrom' : {'id':null, 'code':null, 'name': null}
                  });
                  $scope.allDone.employee = true;
             }
             if($scope.writeOffAmount > 0 && $scope.allDone.employee && $scope.allDone.insurance){
                 $scope.recoveryDetailList.push({
                    recoveryDetailId:null,
                    recoveryId:$scope.recoveryId,
                    recoveryType: 'WRITE_OFF',
                    recoveryStatus: 'RECOVERED',
                    amountRecovered: $scope.writeOffAmount,
                    recoveredFrom:{id:null, code:null, name: null}
                 });
             }


            $http({
                url: apiJson.urls.assetManagement.submitRecovery,
                method: 'POST',
                params: {
                    recoveryId: $scope.recoveryId
                },
                data: $scope.recoveryDetailList

            }).then(function (response) {
                if(response.data == true){
                    $toastService.create("Recovery Submitted");
                }else if(response.data == false){
                    $toastService.create("Error Occurred, Recovery Not Submitted");
                }

            }, function (response) {
                console.log("error", response);
            });
          };


            $scope.getAllUnits = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllUnit
                }).then(function success(response) {
                    $scope.unitList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.setUnit = function (selectedUnit){
                $scope.selectedUnit = selectedUnit;
                if($scope.selectedUnit == null){
                    $scope.selectedAsset = null;
                    $scope.selectedAssetName = null;
                    $timeout(function(){
                        $('#assetNameSelect').val(null).trigger('change');
                        $('#assetSelect').val(null).trigger('change');
                    });
                    return;
                }
                $scope.statusAssetMap = null;
                $scope.getRecoveryAssetMap(selectedUnit);
            }

            $scope.getRecoveryAssetMap = function (unit) {
                $http({
                    url: apiJson.urls.assetManagement.getAssetRecoveryData,
                    method: 'GET',
                    params: {
                        unitId: unit.id
                    }
                }).then(function (response) {
                    $scope.statusAssetMap = response.data;
                    if($scope.selectedStatus != null){
                        $scope.setStatus($scope.selectedStatus);
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.setStatus = function (selectedStatus){
                $scope.selectedStatus = selectedStatus;
                $scope.recoveryList = null;
                    if($scope.selectedStatus == null){
                        $scope.selectedAsset = null;
                        return;
                    }
                $scope.assetList = [];
                $scope.assetNameIdList = [];
                $scope.assetNames = [];
                $scope.assetList = $scope.statusAssetMap[selectedStatus];
                for(var i =0 ; i < $scope.assetList.length ; i++ ){
                    if( $scope.assetNameIdList[$scope.assetList[i].assetDefinition.assetName] == null){
                        var asset = {};
                        $scope.assetNameIdList[$scope.assetList[i].assetDefinition.assetName] = [];
                        $scope.assetNameIdList[$scope.assetList[i].assetDefinition.assetName].push($scope.assetList[i].assetId);
                        $scope.assetNames.push($scope.assetList[i].assetDefinition.assetName);
                    }else{
                        $scope.assetNameIdList[$scope.assetList[i].assetDefinition.assetName].push($scope.assetList[i].assetId);
                    }
                };
            }

            $scope.setAssetName = function(selectedAssetName){
                $scope.selectedAssetName = selectedAssetName;
                $scope.assetIds = $scope.assetNameIdList[$scope.selectedAssetName];
            }

            $scope.setAsset = function(selectedAssetId){
                $scope.selectedAssetId = selectedAssetId;
                $scope.findAssets();
            }



            $scope.findAssets = function(){
                if($scope.selectedUnit){
                    $scope.selectedUnitId = $scope.selectedUnit.id;
                }else{
                    $scope.selectedUnitId = null;
                }
                $http({
                    url: apiJson.urls.assetManagement.getAssetRecoveryData,
                    method: 'GET',
                    params: {
                        unitId: $scope.selectedUnitId,
                        status: $scope.selectedStatus,
                        assetName: $scope.selectedAssetName,
                        assetId: $scope.selectedAssetId,
                        startDate: $scope.date.startDate,
                        endDate: $scope.date.endDate
                    }
                }).then(function (response) {
                    $scope.statusAssetMap = response.data;
                    $scope.recoveryList = $scope.statusAssetMap[$scope.selectedStatus];

                }, function (response) {
                    console.log("error", response);
                });
            }

    }]).filter("uniqueValueFilter", function () {
           return function (collection, keyname) {
               var output = [], keys = [];
               angular.forEach(collection, function (item) {
                   var key = item[keyname];
                   if (keys.indexOf(key) === -1) {
                       keys.push(key);
                       output.push(item);
                   }
               });
               return output;
           };
       });