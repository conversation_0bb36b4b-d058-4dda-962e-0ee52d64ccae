angular.module('scmApp').controller(
    'vendorToUnitToSkuMappingCtrl',
    [
        '$rootScope',
        '$scope',
        '$interval',
        'apiJson',
        '$http',
        'appUtil',
        '$toastService',
        'metaDataService',
        'previewModalService',
        '$state',
        function ($rootScope, $scope, $interval, apiJson, $http, appUtil, $toastService, metaDataService, previewModalService, $state) {

            $scope.init = function () {
                $scope.getAllVendors();
                $scope.getAllUnits();
                // this is required for ui-grid
                $scope.gridOptions = {};
                $scope.modalGridOptions = {};
                $scope.showPreview = previewModalService.showPreview;
            };

            $scope.getAllUnits = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllUnit
                }).then(function success(response) {
                    $scope.unitList = appUtil.filterUnitList(response.data);
                    /*.filter(function(value) {
                                    return value.code == 'WAREHOUSE' || value.code == 'KITCHEN' || value.code == 'OFFICE';
                                    });*/
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getAllVendors = function () {
                metaDataService.getAllVendors().then(function (vendors) {
                    if (vendors != null) {
                        $scope.vendorList = vendors;
                    } else {
                        $toastService.create("Could not fetch vendors");
                    }
                });
            };

            $scope.searchMappings = function () {
                if ($scope.vendorDataType == undefined || $scope.vendorDataType == null
                    || $scope.unitDataType == undefined || $scope.unitDataType == null) {
                    return;
                }
                $scope.gridOptions = $scope.getGridOptions();
                $scope.getMappings();
            };

            $scope.redirectToSkuPriceUpdate = function(){
                $state.go("menu.vendorToSkuPriceUpdate");
            }

            $scope.getGridOptions = function () {
                return {
                    enableFiltering: true,
                    enableColumnResizing: true,
                    enableColumnMenus: false,
                    columnDefs: [{
                        field: 'sku.id',
                        displayName: 'SKU Id',
                        cellTemplate: 'skuIdTemplate.html'
                    }, {
                        field: 'sku.name',
                        displayName: 'SKU Name'
                    }, {
                        field: 'status',
                        displayName: 'Mapping Status'
                    }, {
                        field: 'code',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeButton.html'
                    }]
                };
            };

            $scope.getMappings = function () {
                var input = {
                    unitId: $scope.unitDataType.id,
                    vendorId: $scope.vendorDataType.id
                };
                $http({
                    url: apiJson.urls.skuMapping.searchUnitSkuVendorMapping,
                    method: 'POST',
                    data: input
                }).then(function (response) {
                    $scope.gridOptions.data = response.data;
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.changeStatus = function (value) {
                var status = value.status == 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
                value.status = status;
                var currentUser = appUtil.getCurrentUser();
                var payload = {
                    mapping: value,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                };

                $http({
                    url: apiJson.urls.skuMapping.updateUnitSkuVendorMapping,
                    method: 'POST',
                    data: payload
                }).then(
                    function (response) {
                        $scope.gridOptions.data = response.data;
                        $toastService.create('Mapping '
                            + (status == 'ACTIVE' ? 'Activated' : 'Deactivated') + ' Successfully');
                    }, function (response) {
                        console.log("error", response);
                    });
            };

        }]);