angular.module('scmApp').controller('vendorToSkuPriceMappingCtrl', [
    '$rootScope',
    '$scope',
    '$interval',
    'apiJson',
    '$http',
    'appUtil',
    '$alertService',
    '$timeout',
    'previewModalService',
    'Popeye',
    '$filter',"toast",
    function ($rootScope, $scope, $interval, apiJson, $http, appUtil, $alertService, $timeout, previewModalService, Popeye, $filter, toast) {
        $scope.init = function () {
            $scope.categories = [
                { name: "COGS", id: 1, selected: false },
                { name: "CONSUMABLES", id: 2, selected: false },
                { name: "FIXED_ASSETS", id: 3, selected: false },
                //                { name: "SEMI_FINISHED", id: 4, selected: false }
            ];
            $scope.selectedLocation = [];
            $scope.selectedUnit = [];
            $scope.selectedVendorName = null;
            $scope.selectedDispatchLocation = null;
            $scope.selectedProductCategory = null;
            $scope.selectedSku = null;
            $scope.selectedPackaging = null;
            $scope.skuPrice = null;
            $scope.approvalFrom = null;
            $scope.showApproverField = false;
            $scope.searchEmpValues = null;
            $scope.workOrder = null;
            $scope.showEmpValue();
            $scope.getAllVendors();
            $scope.getAllDeliveryLocations();
            $scope.getSkusWrtCategory();
            $scope.showPreview = previewModalService.showPreview;
            $scope.updatedPriceRequest = new Map();
            $scope.priceGridOptions = $scope.locationGridOptions();
            $scope.priceGridOptions.data = null;
            $scope.oldPriceList = [];
            $scope.startDate = null;
            $scope.endDate = null;
            $scope.isContractInRunningState = true;
            $scope.isNewContractByForce = false;
        };

        $scope.multiSelectSettings = {
            enableSearch: true, template: '<b> {{option.name}} </b>' + ',' + '<b>{{option.state.name}}', scrollable: true,
            scrollableHeight: '250px', clearSearchOnClose: true, showCheckAll: false
        };

        $scope.multiSelectSettings2 = {
            enableSearch: true, template: '<b> {{option.value}} </b>', scrollable: true,
            idProperty: 'id',
            scrollableHeight: '250px', clearSearchOnClose: true, showCheckAll: false
        };

        $scope.getAllVendors = function () {
            $http({
                method: "GET",
                dataType: 'json',
                data: '',
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.getAllVendorsByBusinessType,
            }).then(function success(response) {
                $scope.vendorNameList = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.getPricingByVendor = function () {
            $http({
                url: apiJson.urls.skuMapping.getContractStatus + "?vendorId=" + $scope.selectedVendorName.id,
                method: 'GET',
            }).then(function (response) {
                $scope.isContractInRunningState = response.data;
                if ( !$scope.isContractInRunningState ) {
                    toast.warning("One contract is in pending, so please finish it first !.");
                }
                $scope.getVendorPricing();
            }, function (response) {
                console.log("error", response);
            });
        };

        $scope.previousWOState = function(status) {
            return status == 'APPLIED' || status == 'VENDOR_REJECTED' || status == 'APPROVER_REJECTED' 
                    || status == 'CANCELLED' || status == 'EXPIRED' || status == 'DEACTIVATED'
                    || status == 'REMOVED_ALL';
        }

        $scope.getVendorPricing = function () {
            if ($scope.searchEmpValues == null) {
                toast.create("Please wait while loading details");
                $timeout(function () {
                    $('#vendorNameId').val(null).trigger('change');
                });
                return;
            }
            $scope.showApproverField = false;
            $http({
                url: apiJson.urls.skuMapping.getSkuPricing + "?vendorId=" + $scope.selectedVendorName.id,
                method: 'GET',
            }).then(function (response) {
                if(response.data == null || response.data == "") {
                    toast.warning("No Sku Price Mapping found!");
                    return;
                }
                $scope.editedPriceList = [];
                $scope.oldPriceList = response.data.vendorContractItemDataVOS;
                $scope.workOrder = response.data;
                $scope.showApproverField = $scope.previousWOState($scope.workOrder.workOrderStatus) ? true : ($scope.workOrder.approvalRequestId == null ? true : $scope.oldPriceList.length == 0 ? true : false);
                $scope.approvalFrom = null;
                $scope.isNewContractByForce = $scope.workOrder.workOrderStatus == null ? true : false;

                if($scope.previousWOState($scope.workOrder.workOrderStatus)) {
                    $scope.startDate = $filter('date')(new Date(), 'yyyy-MM-dd');
                }else if($scope.workOrder.startDate != null) {
                    $scope.startDate = $filter('date')($scope.workOrder.startDate, 'yyyy-MM-dd');
                }

                if($scope.workOrder.endDate != null) {
                    $scope.endDate = $filter('date')($scope.workOrder.endDate, 'yyyy-MM-dd');
                }

                if(!$scope.showApproverField || $scope.previousWOState($scope.workOrder.workOrderStatus)) {
                    $scope.approvalFrom = $scope.getApprovalFrom($scope.workOrder.approvalRequestId)
                }
                for (var i in $scope.oldPriceList) {
                    var skuUpdatedPrice = $scope.oldPriceList[i].updatedPrice === undefined || $scope.oldPriceList[i].updatedPrice === null ? $scope.oldPriceList[i].currentPrice : $scope.oldPriceList[i].updatedPrice;
                    var skuPrices = {
                        "updated": true,
                        "price": skuUpdatedPrice,
                    }
                    var reason1 = $scope.oldPriceList[i].selectedRejectionReason;
                    var reason2 = $scope.oldPriceList[i].rejectionReason;
                    var rejectionReason = ((reason1 != null ? reason1 : "") + (reason2 != null ? ", " + reason2 : ""));
                    var data = {
                        "keyId": $scope.oldPriceList[i].keyId,
                        "contractItemId": $scope.oldPriceList[i].contractItemId,
                        "vendor": $scope.selectedVendorName,
                        "skuName": $scope.oldPriceList[i].skuId,
                        "dispatchLocation": $scope.oldPriceList[i].dispatchLocation,
                        "deliveryLocation": $scope.oldPriceList[i].deliveryLocation,
                        "units": $scope.convertUnitIdToUnit($scope.oldPriceList[i].unitIds, $scope.oldPriceList[i].deliveryLocationId),
                        "dispatchLocationId": $scope.oldPriceList[i].dispatchLocationId,
                        "deliveryLocationId": $scope.oldPriceList[i].deliveryLocationId,
                        "skuPackaging": $scope.oldPriceList[i].skuPackagingId,
                        "oldPrice": $scope.oldPriceList[i].currentPrice,
                        "status": $scope.oldPriceList[i].status,
                        "uom": $scope.oldPriceList[i].skuPackagingId.uom,
                        "skuPrice": skuPrices,
                        "rejectionReason": rejectionReason,
                        "skuPriceDataId": $scope.oldPriceList[i].skuPriceDataId,
                        "isStatusActive": $scope.oldPriceList[i].isStatusActive,
                        "oldPriceBackUp": skuPrices.price
                    }
                    $scope.editedPriceList.push(data);
                    if($scope.oldPriceList[i].updatedPrice === undefined || $scope.oldPriceList[i].updatedPrice === null && $scope.isNewContractByForce) {
                        $scope.updateRow(data);
                    }
                }
                $scope.priceGridOptions.data = $scope.editedPriceList;
                $scope.priceGridOptions.data.sort(function (a, b) {
                    if(a.status === "REJECTED") {
                        return -1;
                    }
                    if(b.status === "REJECTED") {
                        return 1;
                    }
                    return 0;
                });
                console.log($scope.priceGridOptions.data);
            }, function (response) {
                if(response.data.errorMsg != null) {
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, false);
                } else {
                    toast.error("Error Occurred While getting Sku Prices ");
                }
                $scope.priceGridOptions.data = [];
                console.log(response);
            });
        };

        $scope.convertUnitIdToUnit = function(unitIds, locationId) {
            if(unitIds === undefined || unitIds === null || unitIds.length === 0) {
                return [];
            }

            var list = $scope.listOfUnitsToLocationId[locationId];
            var sampleSelectedUnits = []
            if(list != undefined && list != null) {
                list.forEach(function(pair) {
                    if (unitIds.includes(pair.key)) {
                        sampleSelectedUnits.push({id: pair.key, value: pair.value, locationId: locationId});
                    }
                })
            }
            return sampleSelectedUnits;
        }

        $scope.getSkusWrtCategory = function () {
            $http({
                method: "GET",
                dataType: 'json',
                data: '',
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.getSkuWrtCategory,
            }).then(function success(response) {
                $scope.skus = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.showEmpValue = function () {
            $http({
                method: "GET",
                dataType: 'json',
                params: {},
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.getHodList
            }).then(function success(response) {
                $scope.searchEmpValues = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.getApprovalFrom = function(approverId) {
            for(var i in $scope.searchEmpValues) {
                if($scope.searchEmpValues[i].id == approverId) {
                    return $scope.searchEmpValues[i];
                }
            }
        }

        $scope.getAllDeliveryLocations = function () {
            $http({
                method: "GET",
                dataType: 'json',
                data: '',
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.scmMetadata.activeDeliveryLocations,
            }).then(function success(response) {
                $scope.allDeliveryLocationList = response.data;
                $scope.getAllUnitsAccordingToDeliveryLocation();

            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.getAllUnitsAccordingToDeliveryLocation = function() {
            $scope.allDeliveryLocationIDList = [];
            for( var i = 0; i < $scope.allDeliveryLocationList.length; i++ ) {
                $scope.allDeliveryLocationIDList.push($scope.allDeliveryLocationList[i].id);
            }

            $http({
                method: "GET",
                dataType: 'json',
                params: { locationIds: $scope.allDeliveryLocationIDList },
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.getUnitsByLocationId,
            }).then(function success(response) {
                $scope.listOfUnitsToLocationId = response.data;
                $scope.getAllUnits();
            }, function error(response) {
                console.log("error in getAllUnitsAccordingToDeliveryLocation :" , response);
            });
        }

        $scope.getAllUnits = function() {
            $scope.allUnits = [];
            for (var i = 0; i < $scope.allDeliveryLocationList.length; i++) {
                var list = $scope.listOfUnitsToLocationId[$scope.allDeliveryLocationList[i].id];
                if (list != undefined && list != null) {
                    for (var j = 0; j < list.length; j++) {
                        var pair = list[j];
                        $scope.allUnits.push({
                            id: pair.key,
                            value: pair.value,
                            locationId: $scope.allDeliveryLocationList[i].id
                        });
                    }
                }
            }

        }

        $scope.changeUnitIdList = function() {
            $scope.listOfUnits = [];
            $scope.sampleSelectedUnits = $scope.selectedUnit;
            $scope.selectedUnit = [];


            for(var i = 0; i < $scope.selectedLocation.length; i++) {
                var list = $scope.listOfUnitsToLocationId[$scope.selectedLocation[i].id];
                if(list != undefined && list != null) {
                    list.forEach(function(pair) {
                        $scope.listOfUnits.push({id: pair.key, value: pair.value, locationId: $scope.selectedLocation[i].id });
                        var existsInSample = $scope.sampleSelectedUnits.some(function(selectedPair) {
                            return selectedPair.id === pair.key;
                        });
                        if (existsInSample) {
                            $scope.selectedUnit.push({id: pair.key, value: pair.value, locationId: $scope.selectedLocation[i].id});
                        }
                    })
                }
            }
        }

        $scope.$watch('selectedUnit', function() {
            if($scope.selectedUnit != null && $scope.selectedUnit.length > 0) {
                $scope.sortUnitsBasedOnSelectedUnits();
            }
        }, true);

        $scope.sortUnitsBasedOnSelectedUnits = function() {
            var selectedUnitIds = new Set($scope.selectedUnit.map(function(unit) {
                return unit.id;
            }));
            $scope.listOfUnits.sort(function(a, b) {
                var aSelected = selectedUnitIds.has(a.id);
                var bSelected = selectedUnitIds.has(b.id);
                return bSelected - aSelected;
            });
        }

        $scope.sortLocationBasedOnSelectedLocation = function() {
            var selectedLocations = new Set($scope.selectedLocation.map(function(location) {
                return location.id;
            }));

            $scope.allDeliveryLocationList.sort(function(a, b) {
                var aSelected = selectedLocations.has(a.id);
                var bSelected = selectedLocations.has(b.id);
                return bSelected - aSelected;
            });

        }

        $scope.$watch('selectedLocation', function() {
            $scope.changeUnitIdList();
            if($scope.selectedLocation != null && $scope.selectedLocation.length > 0) {
                $scope.sortLocationBasedOnSelectedLocation();
            }
        }, true);

        $scope.showVendors = function (vendor) {
            $scope.selectedVendorName = vendor;
            $scope.updatedPriceRequest = new Map();
            $scope.isNewContractByForce = false;
            $scope.getVendorDispatchLocations($scope.selectedVendorName);
            $scope.getPricingByVendor();
            $scope.clearAddPriceData(true);
        }

        $scope.setNewContract = function(isNewContractByForce) {
            $scope.isNewContractByForce = isNewContractByForce;
            if( isNewContractByForce ) {
                $timeout(function() {
                    $('#inputCreated2').val(null).trigger('change');
                });
                $scope.approvalFrom = null;
                for (index = 0; index < $scope.priceGridOptions.data.length; index++) {
                    $scope.updateRow($scope.priceGridOptions.data[index]);
                }
            } else {
                for (index = 0; index < $scope.priceGridOptions.data.length; index++) {
                    $scope.cancelRow($scope.priceGridOptions.data[index]);
                }
            }
        }

        $scope.setDispatchLocation = function(location) {
            $scope.selectedDispatchLocation = location;
        }

        $scope.showSkus = function (selected) {
            $scope.selectedProductCategory = selected;
            $scope.getSkusForCategory($scope.selectedProductCategory);
        }

        $scope.showPackaging = function (selected) {
            $scope.selectedSku = selected;
            $scope.getSkuPackaging($scope.selectedSku);
        }

        $scope.showSelectedPackaging = function(selected) {
            $scope.selectedPackaging = selected;
        }

        $scope.showSelectedPrice = function(selected) {
            $scope.skuPrice = selected;
        }

        $scope.setEmployee = function (val) {
            $scope.approvalFrom = val;
            $scope.startDate = null;
            $scope.endDate = null;
            console.log("$scope.approvalFrom ==> ", $scope.approvalFrom);
        }

        $scope.setStartDate = function (val) {
            $scope.startDate = null;
            if( ($scope.previousWOState($scope.workOrder.workOrderStatus) && !$scope.isNewContractByForce) && (new Date(val) >= new Date($scope.endDate)) ){
                toast.warning("Start Date Should be less than Contract End Date");
                document.getElementById("inputCreated1").value = null;
                return;
            }
            $scope.startDate = val;
        }

        $scope.setEndDate = function (val) {
            $scope.endDate = null;
            if( new Date(val) <= new Date(appUtil.getCurrentBusinessDate()) ){
                toast.warning("End Date Should be greater than Current Date");
                document.getElementById("inputCreated3").value = null;
                return;
            }
            if (new Date(val) <= new Date($scope.startDate)) {
                toast.warning("End Date Should be greater than Start Date");
                document.getElementById("inputCreated3").value = null;
                return;
            }
            $scope.endDate = val;
        }

        $scope.getVendorDispatchLocations = function (vendor) {
            if (vendor == undefined || vendor == null) {
                return;
            }
            $http({
                method: "POST",
                dataType: 'json',
                data: vendor.id,
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.vendorSites,
            }).then(function success(response) {
                $scope.vendorDispatchLocations = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        };
        $scope.getSkusForCategory = function (category) {
            if (category == undefined || category == null) {
                return;
            }
            $scope.getSkus = $scope.skus[category.id];
        };

        $scope.addPricing = function () {
            if( !$scope.isContractInRunningState ) {
                toast.warning("One contract is in pending, so please finish it first !.");
                return;
            }
            $scope.newPriceList = [];
            if ($scope.priceGridOptions.data != undefined && $scope.priceGridOptions.data != null) {
                $scope.newPriceList = $scope.priceGridOptions.data;
            }
            if($scope.skuPrice <= 0) {
                toast.warning("Price should not be less than or equal ZERO");
                return;
            }
            for (var i in $scope.selectedLocation) {
                var skuPrice = {
                    "updated": true,
                    "price": $scope.skuPrice,
                }
                var data = {
                    "vendor": $scope.selectedVendorName,
                    "skuName": appUtil.getIdCodeName($scope.selectedSku.skuId, $scope.selectedSku.skuName),
                    "dispatchLocation": $scope.selectedDispatchLocation.code,
                    "deliveryLocation": ($scope.selectedLocation[i].name).toUpperCase(),
                    "dispatchLocationId": $scope.selectedDispatchLocation.id,
                    "deliveryLocationId": $scope.selectedLocation[i].id,
                    "units": $scope.SelectedUnitsOfALocationId($scope.selectedLocation[i].id),
                    "skuPackaging": appUtil.getIdCodeName($scope.selectedPackaging.packagingId, $scope.selectedPackaging.packagingName),
                    "status": "ADDED",
                    "newStatus": 'CREATED',
                    "uom": $scope.selectedSku.unitOfMeasure,
                    "oldPrice": $scope.selectedSku.negotiatedUnitPrice,
                    "skuPrice": skuPrice,
                    "isStatusActive": 'N',
                    "oldPriceBackUp": null,
                }
                if ($scope.alreadyExists(data)) {
                    var msg = "SKU price Mapping of " + data.skuName.name + " already exists for " + data.deliveryLocation + " ! Please verify."
                    toast.create(msg);
                } else {
                    $scope.newPriceList.push(data);
                    $scope.updateRow(data);
                }
            }
            $scope.priceGridOptions.data = $scope.newPriceList;
            $scope.showGrid();
            $scope.clearAddPriceData(false);
        }

        $scope.SelectedUnitsOfALocationId = function(id) {
            var list = $scope.listOfUnitsToLocationId[id];
            var selectedList = [];
            if(list != undefined && list != null) {
                list.forEach(function(pair) {
                    var existsInSample = $scope.selectedUnit.some(function(selectedPair) {
                        return selectedPair.id === pair.key;
                    });
                    if (existsInSample) {
                        selectedList.push({id: pair.key, value: pair.value, locationId: id});
                    }
                })
            }
            return selectedList;
        }

        $scope.checkForValidStatus = function (isValid) {
            if (isValid) {
                if ($scope.selectedLocation.length == 0 || ($scope.listOfUnits.length > 0 && $scope.selectedUnit.length == 0)) {
                    return true;
                }
                return false;
            } else {
                return true;
            }
        };


        $scope.getSkuPackaging = function (sku) {
            if (sku == undefined || sku == null) {
                return;
            }
            $http({
                method: "POST",
                dataType: 'json',
                data: sku.skuId,
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.skuPackaging,
            }).then(function success(response) {
                $scope.skuPkgs = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.locationGridOptions = function () {
            return {
                enableColumnMenus: false,
                enableFiltering: true,
                enableCellEditOnFocus: true,
                enableColumnResizing: true,
                //                    cellEditableCondition: function ($scope) {
                //                        return $scope.row.entity.status === 'ACTIVE';
                //                    },
                columnDefs: [{
                    field: 'vendor.name',
                    displayName: 'Vendor',
                    enableCellEdit: false,
                    width: 50
                }, {
                    field: 'skuName.name',
                    displayName: 'SKU',
                    enableCellEdit: false,
                    width: 80,
                }, {
                    field: 'dispatchLocation',
                    displayName: 'Dispatch Location',
                    enableCellEdit: false,
                    width: 90
                }, {
                    field: 'deliveryLocation',
                    displayName: 'Delivery Location',
                    enableCellEdit: false,
                    width: 90
                },{
                    displayName: 'Units',
                    field: 'units',
                    enableCellEdit: false,
                    cellTemplate: '<img src="img/plus-square.png" width="25" height="25" ng-click="grid.appScope.onPressUnits(row.entity)"/>',
                    width: 60
                },
                 {
                    field: 'skuPackaging.name',
                    displayName: 'Packaging',
                    enableCellEdit: false,
                    width: 90
                }, {
                    field: 'uom',
                    displayName: 'UOM',
                    enableCellEdit: false,
                    width: 50
                },
                {
                    field: 'status',
                    displayName: 'Status',
                    enableCellEdit: false,
                    cellTemplate: 'statusBatch.html',
                    width: 100
                }, {
                    field: 'oldPrice',
                    displayName: 'Negotiated Price',
                    enableCellEdit: false,
                    width: 80
                }, {
                    field: 'skuPrice.price',
                    displayName: 'Updated Price',
                    type: 'number',
                    enableCellEdit: true,
                    cellEditableCondition: function ($scope) {
                        return !$scope.row.entity.updated;
                    },
                    width: 80
                }, {
                    field: 'action',
                    displayName: 'Action',
                    cellTemplate: 'statusChangeButton.html',
                    enableCellEdit: false,
                }, {
                    field: 'rejectionReason',
                    displayName: 'Rejection Reason',
                    enableCellEdit: false,
                    width: 150,
                }],
                onRegisterApi: function (gridApi) {
                    $scope.gridApi = gridApi;
                    gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                        console.log(newValue);
                        // to display update
                        if (colDef.field == 'leadTime'.toString()) {
                            rowEntity.update = false;
                        }
                        else {
                            rowEntity.update = true;
                        }
                        console.log(JSON.stringify(rowEntity));
                        $scope.$apply();
                    });
                }
            };
        };

        $scope.showGrid = function () {
            return $scope.selectedVendorName != null
                && $scope.priceGridOptions.data != null && $scope.priceGridOptions.data.length > 0
        };

        $scope.updateRow = function (value) {
            var innerKey = value.skuName.id + '#' + value.skuPackaging.id + '#' + value.vendor.id + '#'
                + value.dispatchLocation + '#' + value.deliveryLocation;
            if (value.skuPrice.price <= 0 || value.skuPrice.price == null) {
                var msg = "Please fill the updated price";
                toast.create(msg);
                value.updated = null;
                return;
            }
            value.updated = true;
            value.removed = false;
            value.newStatus = 'CREATED';
            $scope.updatedPriceRequest.set(innerKey, value);
            return;
        };

        $scope.cancelRow = function (value) {
            var innerKey = value.skuName.id + '#' + value.skuPackaging.id + '#' + value.vendor.id + '#'
                + value.dispatchLocation + '#' + value.deliveryLocation;
            $scope.updatedPriceRequest.delete(innerKey);

            if(value.updated) {
                value.updated = false;
            }
            if(value.removed) {
                value.removed = false;
            }
            value.newStatus = null;
            return;
        };

        $scope.rejectRow = function (value) {
            var innerKey = value.skuName.id + '#' + value.skuPackaging.id + '#' + value.vendor.id + '#'
                + value.dispatchLocation + '#' + value.deliveryLocation;
            if (value.skuPrice.price <= 0 || value.skuPrice.price == null) {
                var msg = "Error in Metadata";
                toast.create(msg);
                value.updated = null;
                return;
            }
            value.updated = false;
            value.removed = true;
            value.newStatus = 'REMOVED';
            $scope.updatedPriceRequest.set(innerKey, value);
        }

        $scope.alreadyExists = function (payload) {
            var payloadKey = payload.skuName.id + '#' + payload.skuPackaging.id + '#'
                + payload.vendor.id + '#' + payload.dispatchLocation.toUpperCase() + '#'
                + payload.deliveryLocation.toUpperCase();

            if ($scope.priceGridOptions != null && $scope.priceGridOptions.data != null
                && $scope.priceGridOptions.data.length > 0) {
                var index = 0;

                for (index = 0; index < $scope.priceGridOptions.data.length; index++) {
                    var value = $scope.priceGridOptions.data[index];
                    var innerKey = value.skuName.id + '#' + value.skuPackaging.id + '#' + value.vendor.id + '#'
                        + value.dispatchLocation.toUpperCase() + '#' + value.deliveryLocation.toUpperCase();
                    if (payloadKey === innerKey) {
                        return true;
                    }
                }
            }
            return false;
        };


        $scope.clearAddPriceData = function (clear) {
            $scope.selectedProductCategory = null;
            $scope.selectedSku = null;
            $scope.selectedPackaging = null;
            $scope.skuPrice = null;
            if (clear) {
                $scope.selectedDispatchLocation = null;
                $scope.selectedLocation = [];
            }

            if (!$scope.$$phase) {
                $scope.$apply();
            }
            $timeout(function () {
                if (clear) {
                    $('#deliveryLocationId').val('').trigger('change');
                    $('#dispatchLocationId').val('').trigger('change');
                }
                $('#productCategoryId').val('').trigger('change');
                $('#skuId').val('').trigger('change');
                $('#skuPackagingId').val('').trigger('change');
                $('#skuPriceId').val('').trigger('change');
            });
        };

        $scope.isEntryFoundForUpdation = function () {
            if($scope.approvalFrom == null) {
                return false;
            }
            if($scope.priceGridOptions == null || $scope.priceGridOptions.data == null || $scope.updatedPriceRequest == undefined || $scope.updatedPriceRequest == null) {
                return false;
            }
            if($scope.priceGridOptions.data.length > 0 && ($scope.priceGridOptions.data[0].status == 'REJECTED' || $scope.priceGridOptions.data[0].status == 'ADDED')) {
                return $scope.updatedPriceRequest.size > 0 && $scope.isAllChecked();
            } else {
                if( $scope.isNewContractByForce ) {
                    return $scope.updatedPriceRequest.size == $scope.priceGridOptions.data.length;
                }
                return $scope.updatedPriceRequest.size > 0;
            }
        }

        $scope.dateNotNull = function() {
            if($scope.startDate == null || $scope.startDate == "" || $scope.endDate == null || $scope.endDate == "") {
                return false;
            }
            return true;
        }

        $scope.isAllChecked = function() {
            var count = 0;
            for(var i in $scope.priceGridOptions.data) {
                if($scope.priceGridOptions.data[i].status === "REJECTED" || $scope.priceGridOptions.data[i].status === "ADDED") {
                    count = count + 1;
                }
            }
            return $scope.updatedPriceRequest.size >= count;
        }

        $scope.addUpdatedPriceRequest = function() {
            console.log("addUpdatedPriceRequest 1", $scope.updatedPriceRequest);
            console.log("addUpdatedPriceRequest 2", $scope.priceGridOptions.data);
            $scope.priceGridOptions.data.forEach(function(item) {
                if(( !$scope.isNewContractByForce && item.isStatusActive == 'N') || item.status == 'APPROVED' || item.status == 'REJECTED') {
                    return;
                }
                if(item.updated || item.removed) {
                    if(item.oldPriceBackUp != item.skuPrice.price) {
                        item.newStatus = "CREATED";
                    } else {
                        item.newStatus = "APPROVED";
                    }
                    var key = item.skuName.id + '#' + item.skuPackaging.id + '#'
                            + item.vendor.id + '#' + item.dispatchLocation.toUpperCase() + '#'
                            + item.deliveryLocation.toUpperCase();

                    if($scope.updatedPriceRequest[key] == undefined) {
                        $scope.updatedPriceRequest.set(key, item);
                    }
                }
            })
        }


        $scope.previewUpdationModel = function () {
            $scope.addUpdatedPriceRequest();

            var dataTransfer = {};
            dataTransfer.approvalRequestFrom = $scope.approvalFrom;
            dataTransfer.startDate = $scope.startDate;
            dataTransfer.endDate = $scope.endDate;
            dataTransfer.vendor = $scope.selectedVendorName;
            dataTransfer.isNewContract = $scope.isNewContractByForce;

            var mappingModal = Popeye.openModal({
                templateUrl: "vendorToSkuPriceMappingModal.html",
                controller: "vendorToSkuPriceMappingUpdateModalCtrl",
                resolve: {
                    data: function () {
                        return $scope.updatedPriceRequest;
                    }, workOrderData: function () {
                        return $scope.workOrder;
                    }, dataTransfer: function() {
                        return dataTransfer;
                    }
                },
                click: false,
                keyboard: true
            });
            mappingModal.closed.then(function (result) {
                if (result) {
                    $timeout(function () {
                        $('#vendorNameId').val(null).trigger('change');
                    });
                    $scope.init();
                    $scope.updatedPriceRequest = new Map();
                }
            });
        }

        $scope.onPressUnits = function(value) {
            var mappingModal = Popeye.openModal({
                templateUrl: "UnitsModel.html",
                controller: "UnitsModelCtrl",
                resolve: {
                    row: function () {
                        return value;
                    },
                    listOfUnits: function () {
                        return $scope.allUnits;
                    },
                },
                click: false,
                keyboard: true
            });
        }

    }]).controller('vendorToSkuPriceMappingUpdateModalCtrl', ['$scope', 'data', 'workOrderData', 'dataTransfer', 'Popeye', '$http', 'apiJson', 'appUtil', '$fileUploadService', 'metaDataService','toast',
        function ($scope, data, workOrderData, dataTransfer, Popeye, $http, apiJson, appUtil, $fileUploadService, metaDataService, toast) {

            $scope.initSKUPriceUpdationModal = function () {
                var currentUser = appUtil.getCurrentUser();
                $scope.showGrid = $scope.showSelectedDataGrid();
                $scope.showGrid.data = [];
                $scope.finalData = [];
                $scope.finalPayload = [];
                $scope.vendor = dataTransfer.vendor;
                $scope.workOrderData = workOrderData;
                $scope.approvalFrom = dataTransfer.approvalRequestFrom;
                $scope.startDate = dataTransfer.startDate;
                $scope.endDate = dataTransfer.endDate;
                if ($scope.workOrderData == undefined || $scope.workOrderData == null) {
                    $scope.availableDocumentId =  null;
                }
                else {
                    $scope.availableDocumentId = $scope.workOrderData.workOrderDocId;
                }
                data.forEach(function (headerkey, headervalue) {
                    if(headerkey.status == "ADDED" && headerkey.newStatus == "REMOVED") {
                        return;
                    }
                    var payload = {
                        detail: headerkey,
                    };
                    $scope.showGrid.data.push(payload);
                    var unitIds = [];
                    for(var i = 0; i < headerkey.units.length; i++) {
                        unitIds.push(headerkey.units[i].id);
                    }
                    var finalPayload = {
                        keyId: headerkey.keyId,
                        workOrderId: headerkey.workOrderId,
                        contractItemId: headerkey.contractItemId,
                        vendorId: headerkey.vendor.id,
                        skuId: headerkey.skuName,
                        skuPackagingId: headerkey.skuPackaging,
                        deliveryLocationId: headerkey.deliveryLocationId,
                        dispatchLocationId: headerkey.dispatchLocationId,
                        currentPrice: headerkey.oldPrice,
                        updatedPrice: headerkey.skuPrice.price,
                        status: headerkey.newStatus,
                        unitIds: unitIds,
                    };
                    $scope.finalData.push(finalPayload);
                });
                if($scope.showGrid.data == null || $scope.showGrid.data.length == 0) {
                    toast.create("Please add SKU's to continue!");
                    return;
                }
            }

            $scope.showSelectedDataGrid = function () {
                return {
                    enableColumnMenus: false,
                    enableFiltering: true,
                    enableCellEditOnFocus: true,
                    enableColumnResizing: true,
                    columnDefs: [{
                        field: 'detail.vendor.name',
                        displayName: 'Vendor',
                        enableCellEdit: false,
                    }, {
                        field: 'detail.skuName.name',
                        displayName: 'SKU',
                        enableCellEdit: false,
                    }, {
                        field: 'detail.dispatchLocation',
                        displayName: 'Dispatch Location',
                        enableCellEdit: false,
                    }, {
                        field: 'detail.deliveryLocation',
                        displayName: 'Delivery Location',
                        enableCellEdit: false,
                    }, {
                        field: 'detail.skuPackaging.name',
                        displayName: 'Packaging',
                        enableCellEdit: false,
                    }, {
                        field: 'detail.oldPrice',
                        displayName: 'Current Price',
                        enableCellEdit: false,
                    }, {
                        field: 'detail.skuPrice.price',
                        displayName: 'Updated Price',
                        enableCellEdit: false,
                    }, {
                        field: 'detail.newStatus',
                        displayName: 'Status',
                        cellTemplate: 'statusButton.html',
                        enableCellEdit: false,
                    }],
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                            console.log(JSON.stringify(rowEntity));
                            $scope.$apply();
                        });
                    }
                };
            };
        
            $scope.close = function () {
                Popeye.closeCurrentModal(false);
            };

            $scope.downloadDoc = function () {
                if ( $scope.availableDocumentId === undefined || $scope.availableDocumentId === null) {
                    toast.error("Document is not present");
                    return;
                }
                metaDataService.downloadDocumentById($scope.availableDocumentId);
            }

            $scope.uploadDoc = function () {
                $fileUploadService.openFileModal("Upload Document", "Find", function (file) {
                    if (file == null) {
                        toast.error('File cannot be empty');
                        return;
                    }

                    var fileName = file.name;
                    var fileExt = fileName.split('.').pop().toLowerCase();
                    var mType = fileExt.toUpperCase();

                    var fd = new FormData();
                    fd.append('mimeType', mType);
                    fd.append('userId', appUtil.getCurrentUser().userId);
                    fd.append('file', file);
                    $http({
                        url: apiJson.urls.skuMapping.uploadDocument,
                        method: 'POST',
                        data: fd,
                        headers: {
                            'Content-Type': undefined
                        },
                        transformRequest: angular.identity
                    }).success(function (response) {
                        if (response !== undefined && response !== null) {
                            $scope.availableDocumentId = response;
                            toast.create(" File Uploaded successfully");
                        } else {
                            toast.warning(" File Upload failed");
                            return;
                        }
                    }).error(function () {
                        toast.error(" File Upload failed, Please try again");
                    });
                })
            }

            $scope.submit = function () {
                if ($scope.availableDocumentId === undefined || $scope.availableDocumentId === null) {
                    toast.warning("Please upload the document to continue");
                    return;
                }
                var resultData = {};
                resultData.vendorContractItemDataVOS = $scope.finalData;
                resultData.approvalRequestId = $scope.approvalFrom.id;
                resultData.vendorId = $scope.vendor.id;
                resultData.startDate = $scope.startDate;
                resultData.endDate = $scope.endDate;
                if( !dataTransfer.isNewContract ) {
                    resultData.workOrderId = $scope.workOrderData.workOrderId;
                    resultData.contractId = $scope.workOrderData.contractId;
                }
                resultData.workOrderDocId = $scope.availableDocumentId;

                $http({
                    url: apiJson.urls.skuMapping.saveByPassVendorContractItem,
                    method: 'POST',
                    data: resultData
                }).then(function success(response) {
                    if (response.data != null && response.status == 200) {
                        toast.success("Request submitted Successfully");
                        Popeye.closeCurrentModal(true);
                    }
                }, function error(response) {
                    toast.error("Error while submitting the data");
                    Popeye.closeCurrentModal(false);
                    $scope.init();
                });
            };
        }

    ]).controller('UnitsModelCtrl', ['$scope', 'Popeye', 'row', 'listOfUnits',
        function($scope, Popeye, row, listOfUnits) {
            $scope.initUnitModel = function() {
                $scope.row = row;
                $scope.listOfUnits = listOfUnits;
                $scope.selectedUnitForId = $scope.row.units;
                $scope.filteredUnits = [];
                $scope.filterUnits();
                if($scope.row.status === 'ADDED' || $scope.row.status === 'REJECTED' || $scope.row.status === 'ACTIVE') {
                    $scope.isViewOnly = false;
                }
                else {
                    $scope.isViewOnly = true;
                }
            }

            $scope.multiSelectSettings2 = {
                enableSearch: true,
                template: '<b> {{option.value}} </b>',
                scrollable: true,
                idProperty: 'id',
                scrollableHeight: '250px',
                clearSearchOnClose: true,
                showCheckAll: false,
            };

            $scope.filterUnits = function() {
                for(var i = 0; i<listOfUnits.length; i++) {
                    if(listOfUnits[i].locationId === row.deliveryLocationId) {
                        $scope.filteredUnits.push(listOfUnits[i]);
                    }
                }
            }

            $scope.$watch('selectedUnitForId', function() {
                if($scope.selectedUnitForId != null && $scope.selectedUnitForId.length > 0) {
                    $scope.sortUnitsBasedOnSelectedUnits();
                }
            }, true);

            $scope.sortUnitsBasedOnSelectedUnits = function() {
                var selectedUnitIds = new Set($scope.selectedUnitForId.map(function(unit) {
                    return unit.id;
                }));

                $scope.filteredUnits.sort(function(a, b) {
                    var aSelected = selectedUnitIds.has(a.id);
                    var bSelected = selectedUnitIds.has(b.id);
                    return bSelected - aSelected;
                });

            };

            $scope.close = function () {
                Popeye.closeCurrentModal(true);
            };
        }
    ])


