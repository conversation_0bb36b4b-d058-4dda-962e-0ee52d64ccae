/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('standaloneAssetTOCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService','metaDataService','previewModalService','Popeye','$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,metaDataService,previewModalService, Popeye,$alertService) {

    		var manualBookProductId=100217;
    		initializeBillBookParams();

            $scope.init = function () {
                $scope.unit = appUtil.getUnitData();
                var aclData = $rootScope.aclData;
                $scope.byPassChecks = false;
                if(aclData!=null){
                    if(aclData.action!=null && aclData.action["fatrnsfr"]!=null) {
                        $scope.byPassChecks = true;
                    }
                }
                //$scope.regionMaintenanceWarehouse = {"NCR" : 26521 , "MUMBAI" : 26530 , "BANGALORE" : 26531 };
                getMaintenanceWhMapping();
                $scope.avilableCategories= getCategories();
                $scope.scannedAssetTagValue = "";
                $scope.toTypeList = [
                    "FIXED_ASSET_TRANSFER",
                    "BROKEN_ASSET_TRANSFER",
                    "RENOVATION_ASSET_TRANSFER"
                ]
                //$scope.scmUnitList = appUtil.filterCurrentUnit(appUtil.getUnitList());
            	//$scope.getAvailableUnits();
            	$scope.assetCount = 0;
                $scope.scmProductDetails = appUtil.getActiveScmProducts();
                $scope.skuProductMap = appUtil.getSkuProductMap();
                $scope.packagingMap = appUtil.getPackagingMap();
                $scope.isWarehouse = appUtil.isWarehouse();
                $scope.TOProducts = [];
                $scope.comment = null;
                $scope.selectedUnitList=null;
                $scope.isManualBook={};
                $scope.transferOrderDetail = {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    generationUnitId : {id:appUtil.getCurrentUser().unitId,code:"",name:appUtil.findUnitDetail(appUtil.getCurrentUser().unitId).name},
                    generatedForUnitId: null,
                    generatedBy: appUtil.createGeneratedBy(),
                    lastUpdatedBy: appUtil.createGeneratedBy(),
                    status: "CREATED",
                    comment: "",
                    requestOrderId: null,
                    transferOrderItems: [],
                    toType : null
                };
                $scope.showPreview = previewModalService.showPreview;
                $scope.isKitchen = appUtil.isKitchen($scope.unit);
                $scope.availableAssets = [];
                $scope.addedProductIds = [];
                $scope.selectedType = null;

               //getAssetFromUnit(appUtil.getCurrentUser().unitId)

            };

            function getMaintenanceWhMapping (){
                $http({
                    method : "GET",
                    url : apiJson.urls.scmMetadata.maintenanceWhMapping
                }).then(function success(response) {
                    $scope.regionMaintenanceWarehouse = response.data;
                    $scope.getAvailableUnits();
                }, function error(response) {
                    console.log("error:" + response);
                });

            }

            function getCategories(){
                if(($scope.unit.family === "WAREHOUSE" && $scope.unit.subCategory === "MAINTENANCE") || ($scope.byPassChecks == true)){
                    return ['CAFE','WAREHOUSE','KITCHEN'];
                }else{
                    return ['WAREHOUSE','KITCHEN'];
                }
            }

            $scope.categoryFilter = function(product){

                    if(product.categoryDefinition.id == 3 ){
                        return product;
                    }

            }
            $scope.changeCategory = function(category)
            {   //var isCafe = !appUtil.isWarehouseOrKitchen($scope.unit);
                var nonWarehouse = !appUtil.isWarehouse($scope.unit);
                if(nonWarehouse && category =="WAREHOUSE" && $scope.byPassChecks == false){
                    $scope.selectedUnitList = $scope.scmUnitList.filter(function (unitData){
                        //return unitData.subCategory == "FA_WH_MAINTENANCE" && $scope.unit.unitZone == unitData.unitZone;
                        return $scope.regionMaintenanceWarehouse[$scope.unit.unitZone].indexOf(unitData.id) != -1
                    });
                }else{
                    $scope.selectedUnitList=$scope.scmUnitList.filter(function (unit) {
                        return unit.category===category ;
                    });
                }

                $scope.selectedType = null;
            }

            $scope.onScan = function() {
                if($scope.scannedAssetTagValue == null || $scope.scannedAssetTagValue.length != 6){
                    return ;
                }
                if($scope.TOProducts == null || $scope.TOProducts.length == 0){
                    $toastService.create("Please Select some product first");
                    resetScannedAssetTagValue();
                    return;
                }
                /*
                    Search for asset in available assets
                 */
                var assetId = null;
                var associatedSKUId = null;
                for(var index in $scope.availableAssets){
                    if($scope.availableAssets[index].tagValue == $scope.scannedAssetTagValue) {
                        assetId = $scope.availableAssets[index].assetId;
                        associatedSKUId = $scope.availableAssets[index].skuId;
                        break;
                    }
                }
                if(assetId == null) {
                    $toastService.create("Asset tag value is invalid");
                    resetScannedAssetTagValue();
                    return;
                }

                for(var i in $scope.TOProducts) {
                    var item = $scope.TOProducts[i];
                    if(item.selectedSku.skuId == associatedSKUId) {
                        if(item.associatedAssetId == assetId){
                            $toastService.create("Asset already added ");
                            resetScannedAssetTagValue();
                            return;
                        }
                    }
                }
                var foundSku = false;
                var added = false;
                for(var i in $scope.TOProducts) {
                    var item = $scope.TOProducts[i];

                    if(item.selectedSku.skuId == associatedSKUId) {
                        foundSku = true;
                        if(item.associatedAssetId == null || item.assetValidated == false) {
                            item.associatedAssetId = assetId;
                            item.assetValidated = true;
                            item.associatedAssetTagValue = $scope.scannedAssetTagValue;
                            added = true;
                            $toastService.create("Asset successfully added ");
                            resetScannedAssetTagValue();
                            return;
                        }
                    }

                }
                if(foundSku == false) {
                    $toastService.create("Could not found appropriate sku");
                    resetScannedAssetTagValue();
                }
                if(added == false){
                    $toastService.create("Assets are already associated for sku " + item.selectedSku.skuName);
                    resetScannedAssetTagValue();
                    return;
                }
                /*
                    After performing operation set it back to empty string
                */
                resetScannedAssetTagValue();
            }

            function resetScannedAssetTagValue() {
                $scope.scannedAssetTagValue = "";
            }

            $scope.getAvailableUnits = function() {
               // var isMaintenanceWarehouse = $scope.unit.subCategory == "FA_WH_MAINTENANCE";
                var isMaintenanceWarehouse = false ;
                Object.keys($scope.regionMaintenanceWarehouse).forEach(function (zone){
                    if($scope.regionMaintenanceWarehouse[zone].indexOf($scope.unit.id) != -1){
                        isMaintenanceWarehouse = true;
                    }
                });
                $http({
                        method : "GET",
                        url : apiJson.urls.filter.availableUnits,
                        params : {
                            unitId : appUtil.getCurrentUser().unitId
                        }
                    }).then(function success(response) {
                        if (response.data != null && response.data.length > 0) {
                            $scope.scmUnitList = [];
                            appUtil.getUnitList().map(function(unit) {
                                for (var i = 0; i < response.data.length; i++) {
                                    if (unit.id == response.data[i]) {
                                        if(isMaintenanceWarehouse && $scope.byPassChecks == false){
                                            if(unit.category === "WAREHOUSE" && unit.subCategory === "MAINTENANCE"){
                                                $scope.scmUnitList.push(unit);
                                            }
                                        }else{
                                            $scope.scmUnitList.push(unit);
                                        }
                                    }
                                }
                            });
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });


			};

			$scope.getAvailableProducts = function(selectedUnit) {
				$http({
					method : "GET",
					url : apiJson.urls.filter.availableProducts,
					params : {
                        fulfillmentUnit: appUtil.getCurrentUser().unitId,
                        requestingUnit: selectedUnit.id
					}
				}).then(function success(response) {
					if (response.data != null && response.data.length > 0) {
						$scope.availableProductList = response.data;
					} else {
						$toastService.create("Something went wrong. Please try again!");
					}
				}, function error(response) {
					console.log("error:" + response);
				});
			};

            $scope.setReceivingUnit = function (selectedUnit) {
            	selectedUnit = JSON.parse(selectedUnit);
                $scope.transferOrderDetail.generatedForUnitId = {
                    id:selectedUnit.id,
                    code:"",
                    name:selectedUnit.name
                };
                $scope.selectedType = null;
                $scope.getAvailableProducts(selectedUnit);
            };

			$scope.byProducts = function(product) {
				return product != null && product != undefined && $scope.availableProductList != undefined && $scope.availableProductList != null
						&& $scope.availableProductList.indexOf(product.productId) > -1;
			};

            $scope.validateHandOverDate = function(type) {
                console.log("selected unit is ",$scope.selectedUnit);
                console.log("selected cat is ",$scope.selectedCategory);
                if ($scope.selectedCategory == undefined || $scope.selectedCategory == null || $scope.selectedCategory == "") {
                    $toastService.create("Please select the Unit Type first ..!");
                    $scope.selectedType = null;
                    return
                }
                if ($scope.selectedUnit == undefined || $scope.selectedUnit == null || $scope.selectedUnit == "") {
                    $toastService.create("Please select the receiving unit first ..!");
                    $scope.selectedType = null;
                    return;
                }
                var selectedUnit = JSON.parse($scope.selectedUnit);
                if ($scope.selectedCategory == "CAFE") {
                    console.log("Unit data is ", selectedUnit, selectedUnit.handOverDate);
                    if (selectedUnit.handOverDate == null) {
                        if (type != "CAPEX") {
                            $toastService.create("Can not create transfer with type " + type + "  as hand over date is empty..!");
                            $scope.selectedType = null;
                            return;
                        }
                        else {
                            $scope.selectedType = type;
                            return;
                        }
                    }
                    else {
                        var date = new Date(selectedUnit.handOverDate);
                        console.log("date is ", date, selectedUnit.handOverDate);
                        date.setDate(date.getDate() + 15);
                        var calculatedHandOverDate = date;
                        console.log("hand over date is ", calculatedHandOverDate);
                        console.log("current date is :", new Date(appUtil.getCurrentBusinessDate()));

                        if (new Date(appUtil.getCurrentBusinessDate()) <= calculatedHandOverDate) {
                            if (type != "CAPEX") {
                                $toastService.create("Can not create transfer with type " + type + "as cafe is not hand overed yet ..!");
                                $scope.selectedType = null;
                                return;
                            }
                        }
                        else {
                            if (type != "OPEX") {
                                $toastService.create("Can not create transfer with type " + type + "as cafe is hand overed ..!");
                                $scope.selectedType = null;
                                return;
                            }
                        }
                    }
                }
                $scope.selectedType = type;
            };


            $scope.addNewTOItem = function(){
                if ($scope.selectedType == null) {
                    $toastService.create("Please select the Budget type..!");
                    return;
                }
                var selectedProduct = JSON.parse($scope.selectedProduct);
                var added = false;
                $scope.TOProducts.forEach(function (item) {
                    if(item.productId==selectedProduct.productId){
                        added = true;
                        return false;
                    }
                });
                if(added){
                    $toastService.create("Product already added!");
                }
                if(!added){
                    var productIds = [];
                    var isProductIdAdded = false;
                    for(var i = 0 ;i<$scope.addedProductIds.length;i++){
                        if($scope.addedProductIds[i] == selectedProduct.productId){
                            isProductIdAdded = true;
                            break;
                        }
                    }
                    if(!isProductIdAdded){
                        $scope.addedProductIds.push(selectedProduct.productId);
                        productIds.push(selectedProduct.productId)
                        getAssetFromUnitByProducts(appUtil.getCurrentUser().unitId , productIds);
                    }
                    for(var i = 0; i < $scope.assetCount; i++) {
                        var item = {
                            productId:selectedProduct.productId,
                            productName:selectedProduct.productName,
                            unitOfMeasure: selectedProduct.unitOfMeasure,
                            transferredQuantity:null,
                            skuList: setSkuToProduct(selectedProduct.productId),
                            selectedSku: null,
                            trPackaging: []
                        };
                        item.selectedSku = initializeSelectedSku(item.skuList);
                        $scope.TOProducts.push(item);
                        $scope.addPackaging(item);

                    }

                }
            };

            $scope.addPackaging = function (item) {
                $scope.addPackagingData(item);

                item.semifinished = $scope.checkSemiFinshedProducts(item);
                if (item.semifinished) {
                    $toastService.create("Update expiry for semi finished products");
                    var semiFinishedDataArray = [];
                    semiFinishedDataArray.push(item);
                    $scope.openShortExpiryModal(semiFinishedDataArray);
                }
                var trItem = item.trPackaging[0];
                var pgd = trItem.packagingDetails[0];
                pgd.numberOfUnitsPacked = 1;
                $scope.updatePackagingQty(pgd,trItem,item);

            };

            $scope.onSkuChanged = function(item ){
                item.associatedAssetTagValue = null;
                item.associatedAssetId = null;
                item.assetValidated = false;
                $scope.addPackaging(item);
            };

            $scope.validateAssetTagValue = function(associatedAssetTagValue,item) {
                if(associatedAssetTagValue == null || associatedAssetTagValue.length != 6){

                    item.assetValidated = false;
                    item.associatedAssetId = null;
                    return
                }
                if(item.selectedSku == null) {
                    $toastService.create("Please Select SKU first");
                    return;
                }
                var occuranceCounter = 0;
                for(var i in $scope.TOProducts) {
                    var item2 = $scope.TOProducts[i];
                        if(item2.associatedAssetTagValue == associatedAssetTagValue){
                            occuranceCounter++;
                        }
                }
                if(occuranceCounter > 1){
                    $toastService.create("Asset already added ");
                    item.associatedAssetTagValue = null;
                    return;
                }
                for(var index in $scope.availableAssets){
                    if($scope.availableAssets[index].tagValue == associatedAssetTagValue) {

                        if($scope.availableAssets[index].skuId ==item.selectedSku.skuId){
                            item.associatedAssetId = $scope.availableAssets[index].assetId;
                            item.assetValidated = true;
                            return;
                        }
                        break;
                    }
                }
                item.assetValidated = false;
                $toastService.create("Asset Tag Value Entered is not associated with selected SKU");
            }

            function getAssetFromUnit(unitId) {
                $http({
                    url: apiJson.urls.assetManagement.getTransferableAssetsFromUnit,
                    method: 'GET',
                    params : {
                        unitId : unitId
                    },
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    $scope.availableAssets = response.data;
                }, function error(response) {
                    $scope.availableAssets = [];
                    console.log("error:" + response);
                });
            }

            function getAssetFromUnitByProducts(unitId,productIds) {
                $http({
                    url: apiJson.urls.assetManagement.getTransferableAssetsFromUnitByProducts,
                    method: 'POST',
                    params : {
                        unitId : unitId,
                    },
                    data : productIds,
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    if(response.data!=null) {
                        response.data.forEach(function (asset){
                            $scope.availableAssets.push(asset);
                        })

                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }



            $scope.addPackagingData = function(item){
                var found = false;
                item.selectedPackaging=item.selectedSku.skuPackagings[0];
                item.trPackaging.forEach(function(trp){
                    if(trp.skuId==item.selectedSku.skuId){
                        var pfound = false;
                        trp.packagingDetails.forEach(function(pkgd){
                            if(pkgd.packagingDefinitionData.packagingId==item.selectedPackaging.packagingDefinition.packagingId){
                                alert("Packaging already added!");
                                pfound = true;
                                return false;
                            }
                        });
                        if(!pfound){
                            trp.packagingDetails.push({
                                id: null,
                                packagingDefinitionData: item.selectedPackaging.packagingDefinition,
                                numberOfUnitsPacked: null,
                                numberOfUnitsReceived: null,
                                transferredQuantity: null,
                                receivedQuantity:null
                            });
                        }
                        found = true;
                    }
                });

                if(!found){
                	if(item.productId==100217){
                		$scope.isManualBook[item.productId]=true;
                	}
                    item.trPackaging = [];
                    item.trPackaging.push({
                        id: null,
                        skuId: item.selectedSku.skuId,
                        skuName: item.selectedSku.skuName,
                        packagingDetails: [
                            {
                                id: null,
                                packagingDefinitionData: item.selectedPackaging.packagingDefinition,
                                numberOfUnitsPacked: null,
                                numberOfUnitsReceived: null,
                                transferredQuantity: null,
                                receivedQuantity:null
                            }
                        ],
                        requestedQuantity: null,
                        requestedAbsoluteQuantity: null,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: item.selectedSku.unitOfMeasure,
                        unitPrice: item.selectedSku.unitPrice,
                        negotiatedUnitPrice: item.selectedSku.negotiatedUnitPrice,
                        requestOrderItemId: null,
                        purchaseOrderItemId: null,
                        goodReceivedItemId: null
                    });
                }
                //console.log(item);
            };

            $scope.removePackaging = function(trItem, index, item){
            	if(item.productId==100217){
            		initializeBillBookParams();
            	}
                trItem.packagingDetails.splice(index,1);
                if(trItem.packagingDetails.length==0){
                    item.trPackaging.forEach(function (pkg, i) {
                        if(pkg.skuId==trItem.skuId){
                            item.trPackaging.splice(i, 1);
                        }
                    });
                }
                $scope.updateTrItemQty(trItem, item);
            };

            $scope.updatePackagingQty = function(pgd,trItem, roItem){
                pgd.numberOfUnitsPacked = 1;
                if(roItem.productId==100217){
                    pgd.numberOfUnitsPacked=1;
                }
                if(pgd.packagingDefinitionData.packagingType!="LOOSE"){
                    pgd.numberOfUnitsPacked = parseInt(pgd.numberOfUnitsPacked);
                }
                pgd.transferredQuantity = pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
                $scope.updateTrItemQty(trItem, roItem);
            };

            $scope.updateTrItemQty = function(trItem, roItem){
                var qty = null;
                trItem.packagingDetails.forEach(function (item) {
                    qty += item.transferredQuantity;
                });
                trItem.transferredQuantity = qty;
                $scope.updateRoItemQty(roItem);
            };

            $scope.updateRoItemQty = function(roItem){
                var qty = null;
                roItem.trPackaging.forEach(function (item) {
                    qty += item.transferredQuantity;
                });
                roItem.transferredQuantity = qty;
            };

            $scope.startTransferProcess = function (){
                if ($scope.selectedType == "CAPEX") {
                    $scope.getFixedAssetBudget();
                }
                else {
                    $scope.createTransferOrderObject();
                }
            };

            $scope.getFixedAssetBudget = function (){
                $scope.budgetDetails = {};
                $http({
                    url: apiJson.urls.requestOrderManagement.getDepartmentBudgetData,
                    method: "GET",
                    params: {
                        unitId: $scope.transferOrderDetail.generatedForUnitId.id,
                        isFixedAssetOrGoods: "FA_Equipment"
                    }
                }).then(function (response) {
                    if (response.status == 200) {
                        $scope.emptyCheck = appUtil.isEmptyObject(response.data);
                        $scope.budgetDetails = response.data;
                        $scope.createTransferOrderObject();
                    }
                }, function (response) {
                    console.log(response);
                    $toastService.create("Something Went Wrong Please Try again Later..!");
                });
            };


            $scope.createTransferOrderObject = function(){
                var falseStatus = false;
                if($scope.transferOrderDetail.toType == 'BROKEN_ASSET_TRANSFER'){
                    $scope.TOProducts.forEach(function(toi){
                        for(var index in $scope.availableAssets){
                            if($scope.availableAssets[index].assetId == toi.associatedAssetId){
                                if($scope.availableAssets[index].assetStatus != 'BROKEN') {
                                    $toastService.create("Asset Status of " + $scope.availableAssets[index].tagValue + " is not Broken, Mark it broken to continue");
                                    falseStatus = true;
                                }
                            }
                        }
                    })
                }
                if(falseStatus) {
                    return;
                }
            	if($scope.isBillBooksDetailFilled){
            		$toastService.create("Fill Manual bill book details to create Transfer order!");
            		return false;
            	}
                if($scope.transferOrderDetail.generatedForUnitId==null){
                    $toastService.create("Please select receiving unit!");
                    return false;
                }else if($scope.TOProducts.length==0){
                    $toastService.create("Please select items to be transferred!");
                    return false;
                }else{
            	    for(var i = 0; i < $scope.TOProducts.length; i++) {
            	        if($scope.TOProducts[i].assetValidated == false || $scope.TOProducts[i].associatedAssetId == null){
                            $toastService.create("Please enter valid asset tag value!");
                            return false;
                        }
                    }
                    filterTOItems();
            	    if ($scope.transferOrderDetail.budgetType == null || $scope.transferOrderDetail.budgetType == undefined ) {
            	        $toastService.create("Please select the Budget Type..!");
            	        return;
                    }
            	    if ($scope.selectedType == "CAPEX") {
                        var budgetModal = Popeye.openModal({
                            ariaLabelledBy: 'modal-title',
                            ariaDescribedBy: 'modal-body',
                            templateUrl: 'budgetViewModal.html',
                            controller: 'budgetDisplayModalCtrl',
                            backdrop: 'static',
                            scope: $scope,
                            resolve: {
                                transferOrderDetail: function () {
                                    return $scope.transferOrderDetail;
                                },
                                emptyCheck: function () {
                                    return $scope.emptyCheck;
                                },
                                budgetDetails: function () {
                                    return $scope.budgetDetails;
                                }
                            },
                            size: 'lg',
                            click: false,
                            keyboard: false
                        });

                        budgetModal.closed
                            .then(function (data) {
                                if (data) {
                                    $scope.createTransfer();
                                }
                            });
                    }
                    else {
                        $scope.createTransfer();
                    }
                }
            };

            $scope.createTransfer = function () {
                if($scope.transferOrderDetail.transferOrderItems.length > 0
                    && $scope.transferOrderDetail.transferOrderItems.length < 250){
                    $http({
                        method: "POST",
                        url: apiJson.urls.transferOrderManagement.transferOrder,
                        data: $scope.transferOrderDetail
                    }).then(function success(response) {
                        if (response.data != null) {
                            if (response.data != null && response.data > 0) {
                                $toastService.create("Transfer order with id " + response.data + " created successfully!");
                                if($scope.manualBillBookDetails!=undefined){
                                    $scope.manualBillBookDetails.transferOrderId=response.data;
                                    $scope.manualBillBookDetails.generatedForUnitId=$scope.transferOrderDetail.generatedForUnitId;
                                    var url=apiJson.urls.manualBillBookManagement. createManualBillBookEntry;
                                    metaDataService.createManualBillBookEntry(url,$scope.manualBillBookDetails).then(function(data){
                                        if(data){
                                            $toastService.create("Manual bill book with start no: " + $scope.manualBillBookDetails.startNo + " and end no : "
                                                +$scope.manualBillBookDetails.endNo +" created successfully!");
                                            initializeBillBookParams();
                                        }else{
                                            $toastService.create("Manual bill book with given start no and end no already exist!");
                                        }
                                    });
                                }
                                var unitList = $scope.selectedUnitList;
                                $scope.init();
                                $scope.selectedUnitList = unitList;
                                $scope.setReceivingUnit($scope.selectedUnit);
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }
                    }, function error(response) {
                        var data = response.data;
                        $alertService.alert(data.errorTitle, data.errorMsg ,function(){},true);
                        console.log("error:" + response);
                    });
                }else{
                    if($scope.transferOrderDetail.transferOrderItems.length == 0){
                        $toastService.create("Item quantity should not be 0!");
                    }

                    if($scope.transferOrderDetail.transferOrderItems.length >= 250){
                        $toastService.create("Items cannot be more than 249!");
                    }
                }
            };

            function setSkuToProduct(productId){
                var skuList = $scope.skuProductMap[productId];
                skuList.forEach(function(sku){
                    sku.skuPackagings.forEach(function(packaging){
                        packaging.packagingDefinition = $scope.packagingMap[packaging.packagingId];
                    });
                    sku.skuPackagings = filterLoosePackaging(sku.skuPackagings, sku.supportsLooseOrdering);
                });
                skuList = removeInactive(skuList);
                return skuList;
            }

            function initializeSelectedSku(skuList){
                var ret = null;
                if(skuList.length==1){
                    ret = skuList[0];
                }else{
                    skuList.forEach(function (item) {
                        if(item.isDefault){
                            ret = item;
                        }
                    });
                }
                return ret;
            }

            function filterLoosePackaging(pkgList, looseOrdering){
                var ret = pkgList;
                if(!looseOrdering){
                    var pkgs = [];
                    pkgList.forEach(function (pkg) {
                        if(pkg.packagingDefinition.packagingType!="LOOSE"){
                            pkgs.push(pkg);
                        }
                    });
                    ret = pkgs;
                }
                return ret;
            }

            function removeInactive(skuList){
                var skus = [];
                skuList.forEach(function(sku){
                    if(sku.skuStatus=='ACTIVE'){
                        var pkgs = [];
                        sku.skuPackagings.forEach(function(packaging){
                            if(packaging.mappingStatus=='ACTIVE' && packaging.packagingDefinition.packagingStatus=='ACTIVE'){
                                pkgs.push(packaging);
                            }
                        });
                        sku.skuPackagings = pkgs;
                        skus.push(sku);
                    }
                });
                return skus;
            }

            $scope.getTotalAmount = function (totalCost, totalTax) {
                return (parseFloat(totalCost) + parseFloat(totalTax)).toFixed(2);
            };

            $scope.getTotalCost = function (selectedQuantity, price) {
                return (selectedQuantity * price).toFixed(6);

            };

            $scope.getTotalTax = function (price,tax,quantity) {
                return ((price * (tax/100)) * quantity).toFixed(2);
            };

            $scope.getAmountAndTax = function(total_amount,total_tax) {
                return (total_amount + total_tax).toFixed(6);
            };

            function filterTOItems(){
                var trItems = [];
                $scope.totalCost = 0;
                $scope.TOProducts.forEach(function(toi){
                    toi.trPackaging.forEach(function (item) {
                        if(item.transferredQuantity!=null && item.transferredQuantity>0){
                            item.associatedAssetId = toi.associatedAssetId;
                            item.associatedAssetTagValue = toi.associatedAssetTagValue;
                            var currentValue;
                            var total_tax = 0;
                            for(var index in $scope.availableAssets){
                                if($scope.availableAssets[index].assetId == toi.associatedAssetId){
                                     currentValue = $scope.availableAssets[index].currentValueWithoutTax;
                                     var value = $scope.availableAssets[index].currentValueWithoutTax != null ? $scope.availableAssets[index].currentValueWithoutTax : 0;
                                     var total_amount = $scope.getTotalCost(item.transferredQuantity,value);
                                     console.log("total amount is : ",total_amount);
                                     var taxRate = $scope.availableAssets[index].taxPercentage != null ? $scope.availableAssets[index].taxPercentage : 0;
                                     total_tax = $scope.getTotalTax(value, taxRate, item.transferredQuantity);
                                     console.log("total tax is : ",total_tax);
                                     var amountAndTax = $scope.getAmountAndTax(parseFloat(total_amount),parseFloat(total_tax));
                                     $scope.totalCost = parseFloat(parseFloat($scope.totalCost) + parseFloat(amountAndTax)).toFixed(6);
                                     console.log("total cost is : ",$scope.totalCost);
                                }
                            }
                            item.unitPrice = currentValue;
                            item.negotiatedUnitPrice = currentValue;
                            item.itemTax = parseFloat(total_tax);
                            trItems.push(item);
                        }
                    })
                });
                $scope.transferOrderDetail.transferOrderItems = trItems;
                $scope.transferOrderDetail.totalCost = parseFloat($scope.totalCost);
                $scope.transferOrderDetail.budgetType = $scope.selectedType;
            }

            $scope.updateBillBookDetailsObject=function(billBookDetails){
            	$scope.manualBillBookDetails=billBookDetails;
            	$scope.isBillBooksDetailFilled=false;
            };

            $scope.toggleBillBookDetailsView=function(){
            	$scope.addBillBookDetails[manualBookProductId]=!$scope.addBillBookDetails[manualBookProductId];
            };

            $scope.fillManualBBDetails=function(){
            	$scope.toggleBillBookDetailsView();
            };

            $scope.manualDetailsRequired=function(){
            	if($scope.isBillBooksDetailFilled===undefined){
            		$scope.isBillBooksDetailFilled=true;
            	}
            };

            function initializeBillBookParams(){
            	$scope.manualBillBookDetails=undefined;
    			$scope.isBillBooksDetailFilled=undefined;
    			$scope.addBillBookDetails={};
                $scope.addBillBookDetails[manualBookProductId]=false;
            }

            $scope.checkSemiFinshedProducts = function(product){
            	var products = appUtil.getActiveScmProducts();
                var p = null;
                for(var i in products){
                    if(products[i].productId == product.productId){
                        p = products[i];
                        break;
                    }
                }
                if(p != null && p.categoryDefinition.code == 'SEMI_FINISHED'){
                    return true;
                }
                return false;
            };

             $scope.openShortExpiryModal = function(item){
                if(!appUtil.isEmptyObject(item)){
                    var mappingModal = Popeye.openModal({
                        templateUrl: "views/trOrderShortExpiry.html",
                        controller: "trOrderShortExpiryCtrl",
                        modalClass:"semifinishedItemsModal",
                        resolve:{
                        	data:function(){
                                return item;
                            }
                        },
                        click:false,
                        keyboard:false
                    });

                    mappingModal.closed.then(function(data){
                    	if(data== null || data.dataStatus == 'CANCEL'){
                            // do nothing here
                    	} else {
                            // chutiyapa happens here
                            $scope.updateItemWithShortExpiry(data.semiFinishedProducts, data.expiryData);
                    	}
                    });
                }else{
                    $toastService.create("Please add again!");
                }
            };

            $scope.updateItemWithShortExpiry = function(semiFinishedProducts, expiryData){

            	if(semiFinishedProducts == null || semiFinishedProducts.length == 0 ){
            		return;
            	}
            	for(var i in semiFinishedProducts){
            		var item = semiFinishedProducts[i];
            		for(var j in $scope.TOProducts){
            			var roItem = $scope.TOProducts[j];

            			if(item.productId == roItem.productId){
	            			//add packaging
            				var rOrderItem = $scope.TOProducts[j];
	            			var trItem = $scope.TOProducts[j].trPackaging[0];
	            			var pkg = trItem.packagingDetails[0];
	            			var value = 0;
	            			if(item.shortExpiry == null || item.shortExpiry == undefined){
	                    		value = item.freshQuantity;
	                    	} else {
	                    		value = item.freshQuantity + item.shortExpiry
	                    	}
	            			pkg.numberOfUnitsPacked = value;
	            			for(var e in expiryData.inventoryItems){
	            				var exp = expiryData.inventoryItems[e];
	            				if(trItem.skuId == exp.keyId){
	            					if(trItem.drillDowns == null){
	            						trItem.drillDowns =[];
	            					}
	            					trItem.drillDowns = trItem.drillDowns.concat(exp.drillDowns);
	            				}
	            			}
	            			$scope.updatePackagingQty(pkg,trItem,roItem);
            			}
            		}
            	}

            };

            $scope.hasDrillDown = function (trItem, semifinished){
            	return semifinished || (trItem.drillDowns != null && trItem.drillDowns != undefined && trItem.drillDowns.length > 0);
            };
    }]).controller('budgetDisplayModalCtrl', ['$scope', 'transferOrderDetail', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye','emptyCheck','budgetDetails',
        function ($scope, transferOrderDetail, appUtil, $toastService, apiJson, $http, Popeye, emptyCheck, budgetDetails) {

            $scope.transferOrderDetail = transferOrderDetail;
            $scope.emptyCheck = emptyCheck;
            $scope.budgetDetails = budgetDetails;

            $scope.cancel = function () {
                Popeye.closeCurrentModal(false);
            };

            $scope.submit = function () {
                if (appUtil.isEmptyObject($scope.budgetDetails)) {
                    $toastService.create("No budget Details Found Can not transfer the Asset");
                    return;
                }

                if( $scope.transferOrderDetail.totalCost > $scope.budgetDetails.remainingAmount){
                    $toastService.create("Exceeding Budget limit! Can not transfer the Asset !");
                    return;
                }
                Popeye.closeCurrentModal(true);
            };
        }
    ]
);
