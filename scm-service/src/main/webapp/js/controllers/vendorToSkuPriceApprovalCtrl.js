angular.module('scmApp').controller('vendorToSkuPriceApprovalCtrl', [
    '$rootScope',
    '$scope',
    '$interval',
    '$alertService',
    'apiJson',
    '$http',
    'appUtil',
    'toast',
    '$timeout',
    'previewModalService',
    'Popeye',
    'metaDataService',
    'Popeye',
    function ($rootScope, $scope, $interval, $alertService, apiJson, $http, appUtil, toast, $timeout, previewModalService, Popeye, metaDataService, Popeye) {
        $scope.init = function() {
            $scope.showContractItems = false;
            $scope.workOrders = $scope.showContractGrid();
            $scope.workOrders.data = [];
            $http({
                method: 'GET',
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.getWorkOrders,
            }).then(function success(response) {
                if(response.data == null || response.data.length == 0) {
                    toast.warning("No WorkOrders found in CREATED state!");
                    return;
                }
                $scope.workOrders.data = response.data;
            }, function error(response) {
                console.log("error:" + response);
                toast.error("Error while getting WorkOrders");
            });
        }

        $scope.init1 = function (workOrder){
            $scope.showContractItems = true;
            $scope.showPreview = previewModalService.showPreview;
            $scope.priceGridOptions = $scope.locationGridOptions();
            $scope.priceGridOptions.data = [];
            $scope.availableDocumentId = null;
            $scope.updatedPriceRequest = new Map();
            $scope.isApproveAll = false;
            $scope.isRejectAll = false;
            $scope.anySkuRejected = [];
            $scope.rejectionReasonForAll = null;
            $scope.selectedReasonForAll = null;
            $scope.isShowGraph = false;
            $scope.isByPass = null;
            $scope.vendorName = workOrder.vendorName;
            $scope.workOrderId = workOrder.workOrderId;
            $scope.showContrcatButtons = false;
            $scope.isSignatureCreated = false;
            $scope.signaturePad = null;
            $scope.signatureId = null;
            $scope.findSkuPriceMapping();
        }

        $scope.showContractGrid = function () {
            return {
                enableColumnMenus: false,
                enableFiltering: true,
                enableCellEditOnFocus: true,
                enableColumnResizing: true,
                rowHeight: 50,
                columnDefs: [
                {
                    field: 'workOrderId',
                    displayName: 'WO Id',
                    enableCellEdit: false,
                    width: 70 
                },{
                    field: 'vendorName',
                    displayName: 'Vendor',
                    enableCellEdit: false,
                    cellTemplate: '<span>{{row.entity.vendorName}}</span>',
                    width: 300
                }, {
                    field: 'pendingFrom',
                    displayName: 'Pending From',
                    cellTemplate: 'pendingDays.html',
                    enableCellEdit: false,
                }, {
                    field: 'createdByName',
                    displayName: 'Requested By',
                    enableCellEdit: false,
                }, {
                    field: 'createdAt',
                    displayName: 'Created At',
                    type: 'date',
                    cellFilter: 'date: \'yyyy-MM-dd\'',
                    enableCellEdit: false,
                }, {
                    field: 'startDate',
                    displayName: 'Start Date',
                    type: 'date',
                    cellFilter: 'date: \'yyyy-MM-dd\'',
                    enableCellEdit: false,
                }, {
                    field: 'action',
                    displayName: 'Action',
                    cellTemplate: '<button class="btn btn-xs-small active" ng-click="grid.appScope.init1(row.entity)">Approve/Reject</button>',
                    enableCellEdit: false,
                }],
                onRegisterApi: function (gridApi) {
                    $scope.gridApi = gridApi;
                    gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                        console.log(JSON.stringify(rowEntity));
                        $scope.$apply();
                    });
                }
            };
        };

        $scope.dateDifference = function(createdAt) {
            return Math.floor((new Date() - new Date(createdAt)) / (1000 * 60 * 60 * 24));
        }

        function dataURItoBlob(dataURI) {
            var parts = dataURI.split(",");
            var mimeType = parts[0].split(":")[1].split(";")[0];
            var byteString = atob(parts[1]);

            // Create a Uint8Array to hold the binary data
            var arrayBuffer = new ArrayBuffer(byteString.length);
            var uint8Array = new Uint8Array(arrayBuffer);

            // Populate the Uint8Array with binary data
            for (var i = 0; i < byteString.length; i++) {
                uint8Array[i] = byteString.charCodeAt(i);
            }

            // Create a Blob from the Uint8Array and return it
            return new Blob([uint8Array], { type: mimeType });
        }

        $scope.findSkuPriceMapping = function () {
            $http({
                url: apiJson.urls.skuMapping.findSkuPriceMapping + "?woId=" + $scope.workOrderId + "&status=CREATED",
                method: 'GET',
            }).then(function (response) {
                var workOrderData = response.data;
                if (workOrderData == null) {
                    var msg = "No Sku Price Mapping found!";
                    toast.warning(msg);
                    return;
                }
                if(workOrderData.workOrderStatus == 'CREATED' && workOrderData.vendorContractItemDataVOS.length == 0) {
                    toast.create("Please choose BYPASS or MAKE " + (workOrderData.workOrderType == 'DEFAULT' ? 'Contract' : 'Work Order' )+ "!");
                    $scope.showContrcatButtons = true;
                }
                $scope.availableDocumentId = workOrderData.workOrderDocId;
                $scope.currentWorkOrderData = workOrderData;
                $scope.priceGridOptions.data = workOrderData.vendorContractItemDataVOS;
            }, function (response) {
                toast.error("Error while getting data");
                console.log("error", response);
                $scope.init();
            });
        };

        $scope.showApprovedSkus = function() {
            $http({
                url: apiJson.urls.skuMapping.findSkuPriceMapping + "?woId=" + $scope.workOrderId + "&status=APPROVED",
                method: 'GET',
            }).then(function (response) {
                if (response.data == null || response.data == "" || response.data.vendorContractItemDataVOS == null ||  response.data.vendorContractItemDataVOS.length == 0) {
                    var msg = "No Approved SKU's found";
                    toast.warning(msg);
                    return;
                }
                Popeye.openModal({
                    templateUrl: "approvedSKUS.html",
                    controller: "approvedSKUSCtrl",
                    resolve: {
                        data: function () {
                            return response.data;
                        }
                    },
                    click: false,
                    keyboard: true
                });
            }, function (response) {
                console.log("error", response);
                toast.error("Error while getting approved SKU's");
            });
        }

        $scope.changeByPass = function(bypassValue) {
            $alertService.confirm("Are you sure want to " + (bypassValue === 'Y' ? "ByPass Contract" : "Make Contract") + " ?<br><br><br>", "", function (result) {
                if(result) {
                    $scope.isByPass = bypassValue;
                    $scope.submitPriceApprovals();
                }
            });
        }

        $scope.downloadDocument = function () {
            if ($scope.availableDocumentId === null) {
                toast.warning("Document is not present");
                return;
            }
            metaDataService.downloadDocumentById($scope.availableDocumentId);
        }

        $scope.showGrid = function () {
            return $scope.priceGridOptions.data != null && $scope.priceGridOptions.data.length > 0;
        };

        $scope.locationGridOptions = function () {
            return {
                enableColumnMenus: false,
                enableFiltering: true,
                enableCellEditOnFocus: true,
                enableColumnResizing: true,
                columnDefs: [{
                    field: 'contractItemId',
                    displayName: 'Item Id',
                    enableCellEdit: false,
                    width: 60
                }, {
                    field: 'skuId.name',
                    displayName: 'SKU',
                    enableCellEdit: false
                }, {
                    field: 'dispatchLocation',
                    displayName: 'Dispatch Location',
                    enableCellEdit: false
                }, {
                    field: 'deliveryLocation',
                    displayName: 'Delivery Location',
                    enableCellEdit: false
                }, {
                    field: 'skuPackagingId.name',
                    displayName: 'Packaging',
                    enableCellEdit: false
                }, {
                    field: 'currentPrice',
                    displayName: 'Negotiated Price',
                    enableCellEdit: false,
                    width: 80
                }, {
                    field: 'updatedPrice',
                    displayName: 'Updated Price',
                    type: 'number',
                    enableCellEdit: false,
                    width: 80
                }, {
                    field: 'showUnits',
                    displayName: 'Units',
                    width: 50,
                    enableCellEdit: false,
                    cellTemplate: '<img src="img/plus-square.png" width="25" height="25" ng-click="grid.appScope.showUnits(row.entity.unitIds)"/>'
                }, {
                    field: 'showGraph',
                    displayName: 'Show Graph',
                    width: 50,
                    enableCellEdit: false,
                    cellTemplate: '<img src="img/priceHistory.svg" width="25" height="25" ng-click="grid.appScope.showGraph(row.entity)"/>'
                }, {
                    field: 'action',
                    displayName: 'Action',
                    cellTemplate: 'statusChangeButton.html',
                    enableCellEdit: false,
                    width: 250
                }, {
                    field: 'dropDownRejectionReason',
                    displayName: 'Select Rejection Reason',
                    cellEditableCondition: function ($scope) {
                        return !$scope.row.entity.approved && !$scope.showGrid && !$scope.row.entity.rejected;
                    },
                    editableCellTemplate: 'ui-grid/dropdownEditor',
                    editDropdownOptionsArray: $scope.rejectionReasonOptions,
                    editDropdownValueLabel: 'reason',
                    cellFilter: 'dropDownRejectionReasonFilter',
                }, {
                    field: 'rejectionReason',
                    displayName: 'Rejection Comment',
                    type: 'text',
                    enableCellEdit: true,
                    cellEditableCondition: function ($scope) {
                        return !$scope.row.entity.approved && !$scope.showGrid && !$scope.row.entity.rejected;
                    },
                }],
                onRegisterApi: function (gridApi) {
                    $scope.gridApi = gridApi;
                    gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                        console.log(newValue);
                        // to display update
                        if (colDef.field == 'leadTime'.toString()) {
                            rowEntity.update = false;
                        }
                        else {
                            rowEntity.update = true;
                        }
                        console.log(JSON.stringify(rowEntity));
                        $scope.$apply();
                    });
                }
            };
        };

        // TODO >> make it into 1 list.
        $scope.rejectionReasonOptions = [
            { id: 1, reason: 'Price is too high' },
            { id: 2, reason: 'Price is too less' },
            { id: 3, reason: 'Price miss match' },
            { id: 4, reason: 'Others' },
        ];
        $scope.rejectionReasonOptions2 = ['Price is too high', 'Price is too less', 'Price miss match', 'Others'];

        $scope.showUnits = function(unitIds) {
            if(unitIds == null || unitIds.length == 0) {
                toast.warning("SKU has no unit mapping for this work-order!");
                return;
            }
            var units = "<b>";
            for(var i = 0; i<unitIds.length; i++) {
                units += unitIds[i] + "<br>"
            }
            units += "<b>";
            $alertService.alert("SKU Mapped for these units.", units, null, false);
        }

        $scope.showGraph = function (value) {
            var data = {
                skuId: value.skuId.id,
                deliveryLocationId: value.deliveryLocationId,
                packagingId: value.skuPackagingId.id
            }

            $http({
                method: "GET",
                params: data,
                url: apiJson.urls.skuMapping.getPreviousPricesOfSkuByLocation,
            }).then(function success(response) {
                if (response.data === null || response.data.length == 0) {
                    toast.warning("Previous price history not found!");
                    return;
                }
                $scope.previousPricesGraph = response.data;
                var info = {
                    packagingName: value.skuPackagingId.name,
                    skuName: value.skuId.name,
                    deliveryLocation: value.deliveryLocation
                }
                var mappingModal = Popeye.openModal({
                    templateUrl: "previousPriceHistoryOfSKU.html",
                    controller: "previousPriceHistoryOfSKUCtrl",
                    resolve: {
                        data: function () {
                            return $scope.previousPricesGraph;
                        }, info: function () {
                            return info;
                        }
                    },
                    click: false,
                    keyboard: true
                });
            }, function error(response) {
                console.log("error:" + response);
                toast.error("Error while finding the Previous Price History!");
                return;
            });
        };

        $scope.approvePrice = function (value) {
            var data = {
                contractItemId: value.contractItemId,
                status: "APPROVED",
                selectedRejectionReason: null,
                rejectionReason: null
            }
            var index = $scope.anySkuRejected.indexOf(value.contractItemId);
            if (index !== -1) {
                $scope.anySkuRejected.splice(index, 1);
            }
            value.dropDownRejectionReason = null;
            value.rejectionReason = null;
            value.approved = true;
            $scope.updatedPriceRequest.set(value.contractItemId, data);
            return;
        };

        $scope.cancelApprove = function (value) {
            value.approved = false;
            $scope.updatedPriceRequest.delete(value.contractItemId, null);
            return;
        }

        $scope.rejectPrice = function (value) {
            if (value.dropDownRejectionReason == null) {
                toast.warning("Please select the rejection reason first");
                return;
            }
            var data = {
                contractItemId: value.contractItemId,
                status: "REJECTED",
                rejectionReason: value.rejectionReason,
                selectedRejectionReason: value.dropDownRejectionReason
            }
            if (!$scope.anySkuRejected.includes(value.contractItemId)) {
                $scope.anySkuRejected.push(value.contractItemId);
            }
            value.rejected = true;
            $scope.updatedPriceRequest.set(value.contractItemId, data);
            return;
        }

        $scope.approveAll = function () {
            if ($scope.isRejectAll) {
                toast.create("Please use Reset and then use APPROVE ALL");
                return;
            }

            $alertService.confirm("Are you sure want to Approve All", "", function(result) {
                if(result) {
                    $timeout(function() {
                        for (var i = 0; i < $scope.priceGridOptions.data.length; i++) {
                            var value = $scope.priceGridOptions.data[i];
                            var data = {
                                contractItemId: value.contractItemId,
                                status: "APPROVED",
                                selectedRejectionReason: null,
                                rejectionReason: null
                            }
                            if (value.rejected) {
                                value.rejected = false;
                            }
                            var index = $scope.anySkuRejected.indexOf(value.contractItemId);
                            if (index !== -1) {
                                $scope.anySkuRejected.splice(index, 1);
                            }
                            value.dropDownRejectionReason = null;
                            value.rejectionReason = null;
                            value.approved = true;
                            $scope.updatedPriceRequest.set(value.contractItemId, data);
                        }
                        $scope.isApproveAll = true;
                        $scope.rejectionReasonForAll = null;
                        $scope.selectedReasonForAll = null;
                    })
                }
            });
        }

        $scope.rejectAll = function () {
            if ($scope.isApproveAll) {
                toast.create("Please use Reset and then use REJECT ALL");
                return;
            }
            if ($scope.selectedReasonForAll === null) {
                toast.create("Please select Rejection Reason to REJECT ALL");

                // Add validation feedback - highlight the rejection reason select with red border
                var rejectionSelect = document.getElementById("rejectionReasonSelect");
                if (rejectionSelect) {
                    rejectionSelect.classList.add("validation-error");
                    $timeout(function() {
                        rejectionSelect.classList.remove("validation-error");
                    }, 3000);
                }
                return;
            }

            $alertService.confirm("Are you sure want to Reject All", "", function(result) {
                if(result) {
                    $timeout(function() {
                        for (var i = 0; i < $scope.priceGridOptions.data.length; i++) {
                            var value = $scope.priceGridOptions.data[i];
                            value.dropDownRejectionReason = $scope.selectedReasonForAll;
                            value.rejectionReason = $scope.rejectionReasonForAll;
                            var data = {
                                contractItemId: value.contractItemId,
                                status: "REJECTED",
                                selectedRejectionReason: $scope.selectedReasonForAll,
                                rejectionReason: $scope.rejectionReasonForAll
                            }
                            if (!$scope.anySkuRejected.includes(value.contractItemId)) {
                                $scope.anySkuRejected.push(value.contractItemId);
                            }
                            if (value.approved) {
                                value.approved = false;
                            }
                            value.rejected = true;
                            $scope.updatedPriceRequest.set(value.contractItemId, data);
                        }
                        $scope.isRejectAll = true;
                    })
                }
            });
        }

        $scope.clearAll = function () {
            $scope.rejectionReasonForAll = null;
            $scope.selectedReasonForAll = null;
            for (var i = 0; i < $scope.priceGridOptions.data.length; i++) {
                var value = $scope.priceGridOptions.data[i];
                value.rejected = false;
                value.approved = false;
                value.dropDownRejectionReason = null;
                value.rejectionReason = null;
                $scope.updatedPriceRequest.delete(value.contractItemId, null);
            }
            $scope.isRejectAll = false;
            $scope.isApproveAll = false;
        }

        $scope.cancelReject = function (value) {
            value.rejectionReason = null;
            value.dropDownRejectionReason = null;
            value.rejected = false;
            $scope.updatedPriceRequest.delete(value.contractItemId, null);
            return;
        }

        $scope.isEntryFoundForApproval = function () {
            if($scope.updatedPriceRequest.size >= 0 && $scope.priceGridOptions.data.length === $scope.updatedPriceRequest.size) {
                return true;
            }
            return false;
        }

        $scope.showRejectionDiv = function() {
            if($scope.updatedPriceRequest.size == 0) {
                return true;
            }
            $scope.updatedPriceRequest.forEach(function (headerkey, headervalue) {
                if (headerkey.status == "REJECTED") {
                    return true;
                }
            });
            return false;
        }

        $scope.createSignBoard = function () {
            if (!$scope.isSignatureCreated) {
                var canvas = document.getElementById("signature");
                if(canvas) {
                    $scope.signaturePad = new SignaturePad(canvas, { backgroundColor: 'rgb(250,250,250)' });
                    $scope.isSignatureCreated = true;
                }
            }
        }

        $scope.resetSignature = function () {
            if ($scope.signaturePad) {
                $scope.signaturePad.clear();
            } else {
                // If signature pad doesn't exist yet, try to create it
                $scope.createSignBoard();
            }
        }

        $scope.saveSignature = function () {
            if (!$scope.signaturePad || $scope.signaturePad.isEmpty()) {
                toast.create("Signature Cannot Be Empty");
                return;
            }
            var imageURI = $scope.signaturePad.toDataURL();
            var blobObject = dataURItoBlob(imageURI);

            var fd = new FormData();
            fd.append('fileType', "image/png");
            fd.append('mimeType', ".png");
            fd.append('file', blobObject);
            fd.append('woId', $scope.currentWorkOrderData.workOrderId);
            $http({
                url: apiJson.urls.skuMapping.digitalSignature,
                method: 'POST',
                data: fd,
                headers: {
                    'Content-Type': undefined
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (!appUtil.isEmptyObject(response)) {
                    toast.create("Signature Saved Successfully");
                    $scope.signatureId = response;
                } else {
                    toast.warning("Sign Upload failed");
                }
            }).error(function (response) {
                if (response.errorMsg != null) {
                    toast.error(response.errorMsg);
                }
                $rootScope.showFullScreenLoader = false;
                toast.warning("Upload failed");
            });

        }

        $scope.setRejection = function (data, isSelectedReason) {
            if (isSelectedReason) {
                $scope.selectedReasonForAll = data;
            }
            else {
                $scope.rejectionReasonForAll = data;
            }
        }

        $scope.showContractButtons = function() {
            if($scope.isEntryFoundForApproval() && $scope.anySkuRejected.length == 0) {
                $scope.createSignBoard();
                return true;
            }
            $scope.isByPass = null;
        }

        $scope.submit = function() {
            $alertService.confirm("Are you sure want to Submit", "", function(result) {
                if(result) {
                    $scope.submitPriceApprovals();
                }
            });
        }

        $scope.submitPriceApprovals = function () {

            var data = {};
            if ($scope.currentWorkOrderData.workOrderType == 'DEFAULT' && $scope.isByPass != null) {
                if (!$scope.signaturePad || $scope.signaturePad.isEmpty()) {
                    toast.warning("Please sign the document to continue");
                    return;
                }
                if($scope.signatureId == null) {
                    toast.warning("Please save the signature to continue");
                    return;
                }
                data.authDigitalSignId = $scope.signatureId;
            }

            data.vendorContractItemDataVOS = [];
            data.workOrderId = $scope.currentWorkOrderData.workOrderId;

            if($scope.isByPass != null) {
                data.isByPassed = $scope.isByPass;
            }

            $scope.updatedPriceRequest.forEach(function (headerkey, headervalue) {

                if (headerkey.rejectionReason !== null && headerkey.rejectionReason.length === 0) {
                    headerkey.rejectionReason = null;
                }
                if(headerkey.selectedRejectionReason >= 1 && headerkey.selectedRejectionReason <= 4) {
                    headerkey.selectedRejectionReason = $scope.rejectionReasonOptions2[headerkey.selectedRejectionReason-1];
                }

                var payload = {
                    status: headerkey.status,
                    contractItemId: headerkey.contractItemId,
                    rejectionReason: headerkey.rejectionReason,
                    selectedRejectionReason: headerkey.selectedRejectionReason
                };
                data.vendorContractItemDataVOS.push(payload);
            });
            console.log("data", data);
            $http({
                url: apiJson.urls.skuMapping.savePriceApprovals,
                method: 'POST',
                data: data
            }).then(function success(response) {
                if (response.data != null && response.status == 200) {
                    toast.success("Request Sent Successfully");
                    $scope.init();
                }
            }, function error(response) {
                toast.error(response.data.errorMsg);
            });
        }

    }]).controller('previousPriceHistoryOfSKUCtrl', ['$scope', 'Popeye', 'data', 'info',
        function ($scope, Popeye, data, info) {
            $scope.initPreviousPriceHistory = function () {
                $scope.previousPricesGraphData = data;
                $scope.requiredInfo = info;
            }

            $scope.close = function () {
                Popeye.closeCurrentModal(true);
            };
        }
    ]).controller('approvedSKUSCtrl', ['$scope', 'toast', 'Popeye', 'data', '$alertService',
        function ($scope, toast, Popeye, data, $alertService) {
            $scope.initApprovedSkus = function () {
                $scope.approvedSkusGrid = $scope.approvedGridOptions();
                $scope.approvedSkusGrid.data = data.vendorContractItemDataVOS;
            }

            $scope.approvedGridOptions = function () {
                return {
                    enableColumnMenus: false,
                    enableFiltering: true,
                    enableCellEditOnFocus: true,
                    enableColumnResizing: true,
                    columnDefs: [{
                        field: 'skuId.name',
                        displayName: 'SKU',
                        enableCellEdit: false
                    }, {
                        field: 'dispatchLocation',
                        displayName: 'Dispatch Location',
                        enableCellEdit: false
                    }, {
                        field: 'deliveryLocation',
                        displayName: 'Delivery Location',
                        enableCellEdit: false
                    }, {
                        field: 'showUnits',
                        displayName: 'Units',
                        width: 50,
                        enableCellEdit: false,
                        cellTemplate: '<img src="img/plus-square.png" width="25" height="25" ng-click="grid.appScope.showUnits(row.entity.unitIds)"/>'
                    }, {
                        field: 'skuPackagingId.name',
                        displayName: 'Packaging',
                        enableCellEdit: false
                    }, {
                        field: 'currentPrice',
                        displayName: 'Negotiated Price',
                        enableCellEdit: false,
                    }, {
                        field: 'updatedPrice',
                        displayName: 'Updated Price',
                        type: 'number',
                        enableCellEdit: false,
                    }],
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                            console.log(newValue);
                            // to display update
                            if (colDef.field == 'leadTime'.toString()) {
                                rowEntity.update = false;
                            }
                            else {
                                rowEntity.update = true;
                            }
                            console.log(JSON.stringify(rowEntity));
                            $scope.$apply();
                        });
                    }
                };
            };
            $scope.showUnits = function(unitIds) {
                if(unitIds == null || unitIds.length == 0) {
                    toast.create("SKU has no unit mapping!");
                    return;
                }
                var units = "<b>";
                for(var i = 0; i<unitIds.length; i++) {
                    units += unitIds[i] + "<br>"
                }
                units += "<b>";
                $alertService.alert("SKU Mapped for these units.", units, null, false);
            }
            $scope.close = function () {
                Popeye.closeCurrentModal(true);
            };
        }
    ]).filter('dropDownRejectionReasonFilter', function () {
        var reasons = {
            1: 'Price is too high',
            2: 'Price is too less',
            3: 'Price miss match',
            4: 'Others'
        };

        return function (input) {
            if (!input) {
                return "select a reason";
            } else {
                return reasons[input];
            }
        };
    });