/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('standaloneGRCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService) {

            $scope.init = function () {
                $scope.scmProductDetails = appUtil.getScmProductDetails();
                $scope.skuProductMap = appUtil.getSkuProductMap();
                $scope.packagingMap = appUtil.getPackagingMap();
                $scope.GRProducts = [];
                $scope.comment = null;
                $scope.GRDetail = {
                    id: null,
                    generationTime: null,
                    lastUpdateTime: null,
                    generationUnitId : {id:appUtil.getCurrentUser().unitId,code:"",name:appUtil.findUnitDetail(appUtil.getCurrentUser().unitId).name},
                    generatedForUnitId: {id:appUtil.getCurrentUser().unitId,code:"",name:appUtil.findUnitDetail(appUtil.getCurrentUser().unitId).name},
                    generatedBy: appUtil.createGeneratedBy(),
                    receivedBy: null,
                    cancelledBy:null,
                    status: "CREATED",
                    comment: $scope.comment,
                    totalAmount:null,
                    requestOrderId: null,
                    purchaseOrderId:null,
                    transferOrderId:null,
                    autoGenerated: false,
                    parentGR:null,
                    goodsReceivedItems: []
                }
            };

            $scope.addNewGRItem = function(){
                var selectedProduct = JSON.parse($scope.selectedProduct);
                var added = false;
                $scope.GRProducts.forEach(function (item) {
                    if(item.productId==selectedProduct.productId){
                        $toastService.create("Product already added!");
                        added = true;
                        return false;
                    }
                });
                if(!added){
                    var item = {
                        productId:selectedProduct.productId,
                        productName:selectedProduct.productName,
                        unitOfMeasure: selectedProduct.unitOfMeasure,
                        receivedQuantity:null,
                        skuList: setSkuToProduct(selectedProduct.productId),
                        selectedSku: null,
                        grPackaging: []
                    }
                    item.selectedSku = initializeSelectedSku(item.skuList);
                    $scope.GRProducts.push(item);
                }
            }

            $scope.addPackaging = function(item){
                var found = false;
                item.grPackaging.forEach(function(trp){
                    if(trp.skuId==item.selectedSku.skuId){
                        var pfound = false;
                        trp.packagingDetails.forEach(function(pkgd){
                            if(pkgd.packagingDefinitionData.packagingId==item.selectedPackaging.packagingDefinition.packagingId){
                                alert("Packaging already added!");
                                pfound = true;
                                return false;
                            }
                        });
                        if(!pfound){
                            trp.packagingDetails.push({
                                id: null,
                                packagingDefinitionData: item.selectedPackaging.packagingDefinition,
                                numberOfUnitsPacked: null,
                                numberOfUnitsReceived: null,
                                transferredQuantity: null,
                                receivedQuantity:null
                            });
                        }
                        found = true;
                    }
                });
                if(!found){
                    item.grPackaging.push({
                        id: null,
                        skuId: item.selectedSku.skuId,
                        skuName: item.selectedSku.skuName,
                        packagingDetails: [
                            {
                                id: null,
                                packagingDefinitionData: item.selectedPackaging.packagingDefinition,
                                numberOfUnitsPacked: null,
                                numberOfUnitsReceived: null,
                                transferredQuantity: null,
                                receivedQuantity:null
                            }
                        ],
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: item.selectedSku.unitOfMeasure,
                        unitPrice: item.selectedSku.unitPrice,
                        negotiatedUnitPrice: item.selectedSku.negotiatedUnitPrice,
                        calculatedAmount:null,
                        requestOrderItemId: null,
                        purchaseOrderItemId: null,
                        transferOrderItemId: null
                    });
                }
                console.log(item);
            }

            $scope.removePackaging = function(trItem, index, item){
                trItem.packagingDetails.splice(index,1);
                if(trItem.packagingDetails.length==0){
                    item.grPackaging.forEach(function (pkg, i) {
                        if(pkg.skuId==trItem.skuId){
                            item.grPackaging.splice(i, 1);
                        }
                    });
                }
                $scope.updateGRItemQty(trItem, item);
            }

            $scope.updatePackagingQty = function(pgd,trItem, roItem){
                pgd.receivedQuantity = pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
                $scope.updateGRItemQty(trItem, roItem);
            }

            $scope.updateGRItemQty = function(trItem, roItem){
                var qty = null;
                trItem.packagingDetails.forEach(function (item) {
                    qty += item.receivedQuantity;
                });
                trItem.receivedQuantity = qty;
                $scope.updateRoItemQty(roItem);
            }

            $scope.updateRoItemQty = function(roItem){
                var qty = null;
                roItem.grPackaging.forEach(function (item) {
                    qty += item.receivedQuantity;
                });
                roItem.receivedQuantity = qty;
            }

            $scope.createGRObject = function(){
                if($scope.GRDetail.generatedForUnitId==null){
                    $toastService.create("Please select receiving unit!");
                    return false;
                }else if($scope.GRProducts.length==0){
                    $toastService.create("Please select items to be received!");
                    return false;
                }else{
                    filterGRItems();
                    console.log($scope.GRDetail);
                    /*$http({
                     method: "POST",
                     url: apiJson.urls.transferOrderManagement.transferOrder,
                     data: $scope.GRDetail
                     }).then(function success(response) {
                        if (response.data != null) {
                            if (response.data != null && response.data > 0) {
                                $toastService.create("Transfer order with id " + response.data + " created successfully!");
                                $scope.init();
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }
                     }, function error(response) {
                     console.log("error:" + response);
                     });*/
                }
            };

            function setSkuToProduct(productId){
                var skuList = $scope.skuProductMap[productId];
                skuList.forEach(function(sku){
                    sku.skuPackagings.forEach(function(packaging){
                        packaging.packagingDefinition = $scope.packagingMap[packaging.packagingId];
                    });
                    sku.skuPackagings = filterLoosePackaging(sku.skuPackagings, sku.supportsLooseOrdering);
                });
                skuList = removeInactive(skuList);
                return skuList;
            }

            function initializeSelectedSku(skuList){
                var ret = null;
                if(skuList.length==1){
                    ret = skuList[0];
                }else{
                    skuList.forEach(function (item) {
                        if(item.isDefault){
                            ret = item;
                        }
                    });
                }
                return ret;
            }

            function filterLoosePackaging(pkgList, looseOrdering){
                var ret = pkgList;
                if(!looseOrdering){
                    var pkgs = [];
                    pkgList.forEach(function (pkg) {
                        if(pkg.packagingDefinition.packagingType!="LOOSE"){
                            pkgs.push(pkg);
                        }
                    });
                    ret = pkgs;
                }
                return ret;
            }

            function removeInactive(skuList){
                var skus = [];
                skuList.forEach(function(sku){
                    if(sku.skuStatus=='ACTIVE'){
                        var pkgs = [];
                        sku.skuPackagings.forEach(function(packaging){
                            if(packaging.mappingStatus=='ACTIVE' && packaging.packagingDefinition.packagingStatus=='ACTIVE'){
                                pkgs.push(packaging);
                            }
                        });
                        sku.skuPackagings = pkgs;
                        skus.push(sku);
                    }
                });
                return skus;
            };

            function filterGRItems(){
                var trItems = [];
                $scope.GRProducts.forEach(function(gri){
                    gri.grPackaging.forEach(function (item) {
                        if(item.transferredQuantity!=null && item.transferredQuantity>0){
                            trItems.push(item);
                        }
                    })
                });
                $scope.GRDetail.transferOrderItems = trItems;
            }

        }]);