/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('refOrderCreateCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'recipeService', 'metaDataService', 'previewModalService', '$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $state, recipeService, metaDataService, previewModalService, $alertService) {

            $scope.init = function () {
                $scope.metadataCategories = appUtil.getMenuProductCategories();
                $scope.productList = appUtil.getScmProductDetails();
                $scope.selectedCategories = [];
                $scope.expiryProduct = {};
                $scope.getCategories();
                $scope.getExpiryProduct();
                $scope.noOfDays = 2;
                $scope.hasCategoryBuffer = false;
                $scope.showMenuItemList = false;
                $scope.brandList = appUtil.getBrandList();
                $scope.brandList.forEach(function (value) {
                    if (value.brandCode == 'CH') {
                        $scope.chaayosId = value.brandId;
                    } else if (value.brandCode == 'GNT') {
                        $scope.gntId = value.brandId;
                    } else if (value.brandCode == 'DC') {
                        $scope.dcId = value.brandId;
                    }
                })
                $scope.showFulfillmentDateSelection = true;
                $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                $scope.comment = null;
                $scope.minRefOrderFulFillmentDate = appUtil.getDate(1);
                $scope.maxRefOrderFulFillmentDate = appUtil.getDate(7);
                console.log($scope.minRefOrderFulFillmentDate + "," + $scope.maxRefOrderFulFillmentDate);
                metaDataService.getUnitProductData(function (unit) {
                    $scope.unitData = unit;
                    $scope.scmProductDetails = appUtil.getScmProductDetails();
                    $scope.menuCategories = recipeService.createMenuItemCategories();
                });
                $scope.showPreview = previewModalService.showPreview;
                $scope.getSalesPercentage();
                $scope.raiseBy = false;
                var aclData = $rootScope.aclData.action;
                //console.log(aclData);
                if (aclData["CEREFO"] == true) {
                    console.log("acl data ");
                    $scope.raiseBy = true;
                }
            };

            $scope.getCategories = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.unitMetadata.listTypes
                }).then(function success(response) {
                    $scope.categoryLists = response.data;
                    $scope.productCategory = $scope.categoryLists.CATEGORY;
                });
            }

            $scope.getExpiryProduct = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.referenceOrderManagement.expiryProductData
                }).then(function success(response) {
                    $scope.expiryProdEntry = response.data;
                    for (var i = 0; i < $scope.expiryProdEntry.length; i++) {
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id] = {};
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].id = $scope.expiryProdEntry[i].id;
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].productName = $scope.expiryProdEntry[i].code;
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].quantity = 0;
                    }
                    console.log(typeof $scope.expiryProduct);
                });
            }


            $scope.filterCategory = function (category) {
                // console.log(category)
                return category.detail.id != 8 // filter out combo(id==8)  categories
            }

            $scope.setDates = function (date, days) {
                $scope.dataEntry = [];
                $scope.fulfillmentDate = date;
                $scope.noOfDays = days;
                $scope.fulfillmentDay = $scope.getDayOfWeekFromStr($scope.fulfillmentDate);
                $scope.stockLastingDate = appUtil.calculatedDate($scope.noOfDays - 1, $scope.fulfillmentDate);
                $scope.stockLastingDay = $scope.getDayOfWeek($scope.stockLastingDate);
                console.log(appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd"));
                $scope.remainingDays = appUtil.datediffRO(appUtil.getDate(0), $scope.fulfillmentDate);
                for (var i = 1; i <= $scope.remainingDays - 1; i++) {
                    $scope.dataEntry.push({
                        dayType: 'REMAINING_DAY',
                        date: appUtil.formatDate(appUtil.calculatedDate(i, appUtil.getDate(0)), "yyyy-MM-dd"),
                        brands: [{
                            id: $scope.chaayosId,
                            saleAmount: 0,
                            deliverySalePercentage: 0
                        },
                            {
                                id: $scope.gntId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            },
                            {
                                id: $scope.dcId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            }],


                        // chaayos:{
                        //     id:$scope.chaayosId,
                        //     saleAmount:0
                        // },
                        // gnt:{
                        //     id:$scope.gntId,
                        //     saleAmount:0
                        // },
                        // desiCanteen:{
                        //     id:$scope.dcId,
                        //     saleAmount:0
                        // }
                    })
                }

                for (var i = 0; i <= $scope.noOfDays - 1; i++) {
                    $scope.dataEntry.push({
                        dayType: 'ORDERING_DAY',
                        date: appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"),
                        brands: [{
                            id: $scope.chaayosId,
                            saleAmount: 0,
                            deliverySalePercentage: 0
                        },
                            {
                                id: $scope.gntId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            },
                            {
                                id: $scope.dcId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            }]
                        // chaayos:{
                        //     id:$scope.chaayosId,
                        //     saleAmount:0
                        // },
                        // gnt:{
                        //     id:$scope.gntId,
                        //     saleAmount:0
                        // },
                        // desiCanteen:{
                        //     id:$scope.dcId,
                        //     saleAmount:0
                        // }
                    })
                }
                console.log($scope.dataEntry);
            };

            $scope.getDayOfWeek = getDayOfWeek;
            $scope.getDayOfWeekFromStr = getDayOfWeekFromStr;
            $scope.getDaysOfWeek = getDaysOfWeek;


            $scope.getSalesPercentage = function () {
                console.log("uit data",$scope.unitData)
                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    salesData: $scope.dataEntry,

                };


                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.salesPercentage,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        // $toastService.create("Reference order estimates calculated successfully!");
                        $scope.dataEntry = response.data.salesData;
                        $scope.dataEntry.forEach(function (data) {
                            data.date=$scope.dateformatting(data.date)
                        })
                        console.log($scope.dataEntry)
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }


            $scope.getReferenceQuantities = function () {

                if ($scope.hasCategoryBuffer) {
                    if (!$scope.categoryBuffer) {
                        alert("please enter category buffer percentage or unselect buffer box");
                        return;
                    }
                    if ($scope.selectedCategories.length == 0) {
                        alert("please select product categories on which buffer has to be applied");
                        return;
                    }
                }
                $scope.showFulfillmentDateSelection = false;
                $scope.showMenuItemList = true;
                var inputData = {
                    unitId: $scope.unitData.id,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    categoryList: $scope.menuCategories,
                    salesData: $scope.dataEntry,
                    bufferedCategoryList: $scope.selectedCategories,
                    categoryBufferPercentage: $scope.categoryBuffer
                };
                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.suggestingOrderEstimates,
                    // url: apiJson.urls.referenceOrderManagement.referenceOrderEstimates,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        $toastService.create("Reference order estimates calculated successfully!");
                        $scope.menuCategories = response.data.categoryList;
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.updateProductQuantity = function (product, variant) {
                var totalQuantity = 0;
                variant.orderedQuantity = parseInt(variant.orderedQuantity);
                product.variants.forEach(function (variant) {
                    totalQuantity += variant.orderedQuantity;
                });
                product.quantity = totalQuantity;
            }

            $scope.clearRequestOrder = function () {
                if (confirm("Are you sure?")) {
                    $scope.scmProductList = [];
                    $scope.comment = null;
                    $scope.menuCategories = recipeService.createMenuItemCategories();
                    $scope.showFulfillmentDateSelection = true;
                    $scope.showMenuItemList = false;
                    $scope.selectedCategories = [];
                    $scope.categoryBuffer = null;
                    $scope.hasCategoryBuffer = false;
                    $scope.dataEntry.forEach(function (data) {
                        data.brands.forEach(function (brand) {
                            brand.saleAmount = 0
                            brand.deliverySalePercentage=0
                        })
                    });

                }
            }
            $scope.resetMenuItem = function () {
                $scope.menuCategories = recipeService.createMenuItemCategories();
            }

            $scope.updateMenuProductQty = function (product) {
                product.requestedQuantity = parseInt(product.requestedQuantity);
            }

            $scope.createSCMProductList = function () {
                $scope.scmProductList = [];
                recipeService.getProductsFromCategoryListRegularOrdering($scope.unitData, $scope.menuCategories, $scope.expiryProduct, function (itemList) {
                    var prods = [];
                    itemList.forEach(function (item) {
                        if (item.selectedFulfillmentType != "EXTERNAL" && item.selectedFulfillmentType != null) {
                            item.stock = item.stockAtHand + item.inTransit;
                            // item.stockAtHand=parseFloat(item.stockAtHand).toFixed(3);
                            item.stockAtHand = item.stockAtHand == 0 ? item.stockAtHand : parseFloat(item.stockAtHand).toFixed(1);
                            item.inTransit = item.inTransit == 0 ? item.inTransit : parseFloat(item.inTransit).toFixed(1);
                            prods.push(item);
                        }
                    });
                    //prods = setMinOrderQuantity(prods);
                    $scope.scmProductList = prods;
                    console.log(prods);
                    return prods;
                });
                $scope.showMenuItemList = false;
            }

            $scope.backToMenuItemList = function () {
                if (confirm("Are you sure?")) {
                    $scope.scmProductList = [];
                    $scope.showMenuItemList = true;
                    $scope.comment = null;
                }
            }

            $scope.updateOrderingQty = function (product) {
                product.packagingQuantity = parseInt(product.packagingQuantity);
                product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
            }


            function validateFulfillment(reqOrder) {
                var returnList = [];
                for (var i in reqOrder.referenceOrderScmItems) {
                    if (appUtil.isEmptyObject(reqOrder.referenceOrderScmItems[i].fulfillmentType)) {
                        returnList.push(reqOrder.referenceOrderScmItems[i].productName);
                    }
                }
                return returnList;
            }

            $scope.dateformatting = function (startDate) {
                var year = new Date(startDate).getFullYear();
                var month = new Date(startDate).getMonth() + 1;
                var day = new Date(startDate).getDate();
                if (day >= 1 && day < 10)
                    day = '0' + day;
                if (month >= 1 && month < 10)
                    month = '0' + month;
                return year + "-" + month + "-" + day;
            }

            $scope.sendReferenceOrder = function (action) {
                var data = createRoObject(action);
                var validateOrder = validateFulfillment(data);
                if ($scope.fulfillmentDate == null) {
                    $toastService.create('Please fill fulfillment date!');
                    return false;
                } else if (data.referenceOrderScmItems.length == 0) {
                    $toastService.create('Please select at least a few products to order!');
                    return false;
                } else if (validateOrder.length > 0) {
                    $alertService.alert('Products found without fulfillment types', validateOrder.join(","), null, true);
                    return false;
                } else {
                    data.refOrderSource="CHAAYOS_REGULAR_ORDERING";
                    if (confirm("Are you sure you want to create the order?")) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.referenceOrderManagement.referenceOrder,
                            data: data
                        }).then(function success(response) {
                            console.log(response);
                            if (response.data != null && response.data > 0) {
                                $toastService.create("Reference order with id " + response.data + " created successfully!");
                                $state.go("menu.reqOrderMgt")
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    }
                }
            }

            function createRoObject(status) {
                return {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    requestUnit: appUtil.createRequestUnit(),
                    generatedBy: appUtil.createGeneratedBy(),
                    fulfillmentUnit: null,
                    fulfillmentDate: new Date($scope.fulfillmentDate),
                    status: status,
                    comment: $scope.comment,
                    referenceOrderMenuItems: getMenuItems(),
                    referenceOrderScmItems: getScmItems(),
                    numberOfDays:$scope.noOfDays,
                    raiseBy:$scope.raiseBy
                }
            }

            function getMenuItems() {
                var products = [];
                $scope.menuCategories.forEach(function (category) {
                    var productList = [];
                    category.productList.forEach(function (product) {
                        if (product.requestedQuantity > 0) {
                            var variantList = [];
                            product.variants.forEach(function (variant) {
                                if (variant.orderedQuantity > 0) {
                                    variantList.push({
                                        id: null,
                                        name: variant.name,
                                        conversionQuantity: variant.conversionQuantity,
                                        orderedQuantity: variant.orderedQuantity
                                    });
                                }
                            });
                            product.variants = variantList;
                            productList.push({
                                id: null,
                                productId: product.productId,
                                productName: product.productName,
                                dimension: product.dimension,
                                requestedQuantity: product.requestedQuantity,
                                requestedAbsoluteQuantity: product.requestedQuantity,
                                transferredQuantity: null,
                                receivedQuantity: null,
                                quantity: product.quantity,
                                dineInQuantity: product.dineInQuantity,
                                deliveryQuantity: product.deliveryQuantity,
                                takeawayQuantity: product.takeawayQuantity,
                                variants: product.variants
                            })
                        }
                    });
                    if (products.length == 0) {
                        products = productList;
                    } else {
                        products = products.concat(productList);
                    }
                });
                return products;
            }

            function getScmItems() {
                var scmProducts = [];
                $scope.scmProductList.forEach(function (product) {
                    if (product.orderingQuantity > 0) {
                        //console.log(product);
                        scmProducts.push({
                            id: null,
                            productId: product.id,
                            productName: product.name,
                            suggestedQuantity: product.suggestedQuantity,
                            requestedQuantity: product.orderingQuantity,
                            requestedAbsoluteQuantity: product.orderingQuantity,
                            transferredQuantity: null,
                            receivedQuantity: null,
                            fulfillmentType: product.selectedFulfillmentType,
                            unitOfMeasure: product.unitOfMeasure
                        });
                    }
                });
                return scmProducts;
            }


            function weekDays() {
                var days = [
                    {id: 1, value: 'Sunday'},
                    {id: 2, value: 'Monday'},
                    {id: 3, value: 'Tuesday'},
                    {id: 4, value: 'Wednesday'},
                    {id: 5, value: 'Thursday'},
                    {id: 6, value: 'Friday'},
                    {id: 7, value: 'Saturday'}];
                return days;
            }

            function getDayOfWeek(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                console.log('Selected Date', date.getDay());
                return weekDays()[date.getDay()].value;
            }

            function getDaysOfWeek() {
                return "NA";
            }

            function getDayOfWeekFromStr(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                return weekDays()[new Date(date).getDay()].value;
            }
            $scope.update = function (){
                $scope.raiseBy=!$scope.raiseBy;
            }

        }]).filter('toArray', function () {
    return function (obj, addKey) {
        if (!angular.isObject(obj)) return obj;
        if (addKey === false) {
            return Object.keys(obj).map(function (key) {
                return obj[key];
            });
        } else {
            return Object.keys(obj).map(function (key) {
                var value = obj[key];
                return angular.isObject(value) ?
                    Object.defineProperty(value, '$key', {enumerable: false, value: key}) :
                    {$key: key, $value: value};
            });
        }
    };
});