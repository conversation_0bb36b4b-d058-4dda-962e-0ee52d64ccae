/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('refOrderCreateCtrlV1', ['$rootScope', '$scope','$stateParams', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'recipeService', 'metaDataService', 'previewModalService', '$alertService','Popeye',
        function ($rootScope, $scope,$stateParams, apiJson, $http, appUtil, $toastService, $state, recipeService, metaDataService, previewModalService, $alertService, Popeye) {

            $scope.init = function () {
                $scope.metadataCategories = appUtil.getMenuProductCategories();
                $scope.productList = appUtil.getScmProductDetails();
                $scope.reasonsOfF9 = ["EXCESS_TREND","ON_HAND_MISMATCH","INTRANSIT_MISMATCH"];
                $scope.selectedCategories = [];
                $scope.expiryProduct = {};
                $scope.listOfEvents = [];
                $scope.warehouseordering = true;
                $scope.exceptionalProducts = [];
                $scope.expiryDataCheck = false;
                $scope.getCategories();
                // $scope.getExpiryProduct();
                $scope.noOfDays = 2;
                $scope.dineInSale=null;
                $scope.getFulfilmentUnits();
                $scope.deliverySale=null;
                $scope.hasCategoryBuffer = false;
                $scope.showFulfillmentDateSelection = true;
                $scope.showMenuItemList = false;
                $scope.totalSale = 0;
                $scope.errorInInstock = false;
                $scope.brandList = appUtil.getBrandList();
                $scope.brandList.forEach(function (value) {
                    if (value.brandCode == 'CH') {
                        $scope.chaayosId = value.brandId;
                    } else if (value.brandCode == 'GNT') {
                        $scope.gntId = value.brandId;
                    } else if (value.brandCode == 'DC') {
                        $scope.dcId = value.brandId;
                    }
                })
                $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                $scope.comment = null;
                $scope.minRefOrderFulFillmentDate = appUtil.getDate(1);
                $scope.maxRefOrderFulFillmentDate = appUtil.getDate(7);
                console.log($scope.minRefOrderFulFillmentDate + "," + $scope.maxRefOrderFulFillmentDate);
                metaDataService.getUnitProductData(function (unit) {
                    $scope.unitData = unit;
                    $scope.scmProductDetails = appUtil.getScmProductDetails();
                    $scope.menuCategories = recipeService.createMenuItemCategories();
                });
                $scope.brandDetails=[{id: 1 ,brandName: "Chaayos" },
                    {id: 3 ,brandName: "Ghee and Turmeric" },
                    {id: 0 ,brandName: "BOTH" }];
                $scope.selectedBrandDetails =null;
                $scope.showPreview = previewModalService.showPreview;
                // $scope.getSalesPercentage();
                $scope.raiseBy = false;
                $scope.f9ScmSuggestionMissedProducts = [];
                var aclData = $rootScope.aclData.action;
                //console.log(aclData);
                if (aclData["CEREFO"] == true) {
                    console.log("acl data ");
                    $scope.raiseBy = true;
                }
                $scope.regularOrderingEvent = null;
                $scope.currentUserId = appUtil.getCurrentUser().userId;
                $scope.showOnlyWarehouseItems = false;
                metaDataService.getRegularOrderingEvents(appUtil.getCurrentUser().unitId,function (events){
                    $scope.listOfEvents = events;
                    console.log("events are : ",events);
                    if ($scope.listOfEvents.length > 0) {
                        $scope.regularOrderingEvent = $scope.listOfEvents[0];
                        $scope.setDates();
                        $scope.selectedBrandDetails = setBrandDetails($scope.regularOrderingEvent.brand);
                        if ($scope.regularOrderingEvent.brand === "BOTH") {
                            $scope.showOnlyWarehouseItems = true;
                        }
                    }
                });
                $scope.currentUser = appUtil.getCurrentUser().userId;
            };

            $scope.setBrand = function (selectedBrandDetails) {
                if (selectedBrandDetails.brandName === "BOTH") {
                    $scope.showOnlyWarehouseItems = true;
                }
            };

            $scope.getFulfilmentUnits = function () {
                $scope.fulfilmentUnitsMap = {};
                $scope.fulfilmentUnitProductsMap = {};
                $http({
                    method: "GET",
                    url: apiJson.urls.referenceOrderManagement.getFulfilmentUnits,
                    params: {
                        "requestingUnitId": appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    $scope.fulfilmentUnitsMap = response.data;
                    angular.forEach($scope.fulfilmentUnitsMap, function (unitId, fulfilmentType) {
                        $scope.getAvailableProducts(unitId, fulfilmentType);
                    });
                    console.log("fulfilmentUnitsMap is : ",$scope.fulfilmentUnitsMap);
                    console.log("fulfilmentUnitProductsMap is : ",$scope.fulfilmentUnitProductsMap);
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.fulfilmentUnitsMap = {};
                    $scope.fulfilmentUnitProductsMap = {};
                });
            };

            $scope.getAvailableProducts = function (unitId, type) {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableProducts,
                    params: {
                        requestingUnit: appUtil.getCurrentUser().unitId,
                        fulfillmentUnit: unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.fulfilmentUnitProductsMap[type] = response.data;
                        console.log("Products Map = ",$scope.fulfilmentUnitProductsMap);
                    } else {
                        $scope.fulfilmentUnitProductsMap[type] = [-1];
                        $toastService.create("No products Mapped for Unit :: " + unitId);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create("Something went wrong While getting available Products. Please try again!");
                    $scope.fulfilmentUnitProductsMap[type] = [-1];
                });
            };

            function setBrandDetails(brand) {
                var result = {};
                for (var i=0;i<$scope.brandDetails.length;i++) {
                    if ($scope.brandDetails[i].brandName == brand) {
                        result = $scope.brandDetails[i];
                        break;
                    }
                }
                return result;
            }

            $scope.getCategories = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.unitMetadata.listTypes
                }).then(function success(response) {
                    $scope.categoryLists = response.data;
                    $scope.productCategory = $scope.categoryLists.CATEGORY;
                });
            }

            $scope.getExpiryProduct = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.referenceOrderManagement.expiryProductData
                }).then(function success(response) {
                    $scope.expiryProdEntry = response.data;
                    for (var i = 0; i < $scope.expiryProdEntry.length; i++) {
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id] = {};
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].id = $scope.expiryProdEntry[i].id;
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].productName = $scope.expiryProdEntry[i].code;
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].quantity = 0;
                    }
                    console.log(typeof $scope.expiryProduct);
                });
            }

            $scope.getNewExpiryProduct = function () {
                $scope.expiryDataCheck = false;
                var lastDate = $scope.OrderingDaysFinal[$scope.OrderingDaysFinal.length -1];
                $http({
                    method: 'GET',
                    url: apiJson.urls.referenceOrderManagement.getNewExpiryProductData,
                    params: {
                        "unitId":appUtil.getCurrentUser().unitId,
                        "days":$scope.noOfDays,
                        "lastDate": lastDate
                    }
                }).then(function success(response) {
                    $scope.expiryProduct = {};
                    $scope.expiryProdEntry = response.data;
                    for (var i = 0; i < $scope.expiryProdEntry.length; i++) {
                        if ($scope.expiryProduct[$scope.expiryProdEntry[i].productId] == undefined || $scope.expiryProduct[$scope.expiryProdEntry[i].productId] == null) {
                            $scope.expiryProduct[$scope.expiryProdEntry[i].productId] = {};
                            $scope.expiryProduct[$scope.expiryProdEntry[i].productId].productId = $scope.expiryProdEntry[i].productId;
                            $scope.expiryProduct[$scope.expiryProdEntry[i].productId].productName = $scope.expiryProdEntry[i].productName;
                            for (var j=0;j<$scope.dataEntry.length;j++) {
                                var product = $scope.expiryProduct[$scope.expiryProdEntry[i].productId];
                                var dateString = $scope.dataEntry[j].date;
                                product[dateString] = 0;
                                var date = appUtil.formatDate(new Date($scope.expiryProdEntry[i].expiryDate), "yyyy-MM-dd");
                                if (date == $scope.dataEntry[j].date) {
                                    product[date] = product[date] + $scope.expiryProdEntry[i].expiryQuantity;
                                }
                            }
                            console.log("current days inside is  : ",$scope.expiryProduct);
                        }
                        else {
                            var dateStr = appUtil.formatDate(new Date($scope.expiryProdEntry[i].expiryDate), "yyyy-MM-dd");
                            var productDup = $scope.expiryProduct[$scope.expiryProdEntry[i].productId];
                            var qty = productDup[dateStr];
                            if (qty == undefined || qty == null) {
                                qty = 0;
                            }
                            productDup[dateStr] = qty + $scope.expiryProdEntry[i].expiryQuantity;
                            console.log("qty is : ",productDup[dateStr]);
                        }
                    }
                    console.log($scope.expiryProduct);
                });
                $scope.getDayWiseExpiryProduct();
            }

            $scope.getDayWiseExpiryProduct = function () {
                $scope.expiryDataCheck = false;
                $http({
                    method: 'GET',
                    url: apiJson.urls.stockManagement.getDayWiseExpiryProduct,
                    params: {
                        "unitId":appUtil.getCurrentUser().unitId,
                        "dates": $scope.OrderingDaysFinal,
                        "isAggregated" : false,
                        "firstOrderingDate" : $scope.onlyOrderingDays[0]
                    }
                }).then(function success(response) {
                    console.log("response is of : ",response.data);
                    $scope.dayWiseExpiryProduct = response.data;
                    $scope.totalDayWiseExpiry = angular.copy(response.data["totalStock"]);
                    $scope.expiryDataCheck = true;
                }, function error(response) {
                    console.log("error: ",  response);
                    $scope.errorInInstock = true;
                    if (response.data.errorMsg != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    }
                    else {
                        $toastService.create("Error Occurred While getting the Day wise Expiries ..!");
                    }
                });
            }


            $scope.filterCategory = function (category) {
                // console.log(category)
                return category.detail.id != 8 // filter out combo(id==8)  categories
            }

            function makeDateString(date) {
                var newDate = new Date(date);
                var result = newDate.getFullYear() + "-" + (newDate.getMonth()+1) + "-" + (newDate.getDate());
                console.log("result Date is : ",result);
                return result;
            }

            $scope.setDates = function (date, days) {
                $scope.dataEntry = [];
                $scope.OrderingDaysFinal = [];
                $scope.remainingDaysFinal = [];
                $scope.onlyOrderingDays = [];
                $scope.fulfillmentDate = $scope.regularOrderingEvent != null ? makeDateString($scope.regularOrderingEvent.fulfilmentDate) : makeDateString(date);
                $scope.noOfDays = $scope.regularOrderingEvent != null ? $scope.regularOrderingEvent.orderingDays : days;
                $scope.fulfillmentDay = $scope.getDayOfWeekFromStr($scope.fulfillmentDate);
                $scope.stockLastingDate = appUtil.calculatedDate($scope.noOfDays - 1, $scope.fulfillmentDate);
                $scope.stockLastingDay = $scope.getDayOfWeek($scope.stockLastingDate);
                console.log(appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd"));
                console.log("Fulfilment Date is : ",$scope.fulfillmentDate);
                var regularOrderingDate = appUtil.getRegularOrderingDate();
                $scope.remainingDays = appUtil.datediffRO(regularOrderingDate, $scope.fulfillmentDate);
                for (var i = 1; i <= $scope.remainingDays - 1; i++) {
                    $scope.dataEntry.push({
                        dayType: 'REMAINING_DAY',
                        date:  appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"),
                        brands: [{
                            id: $scope.chaayosId,
                            saleAmount: 0,
                            deliverySalePercentage: 0
                        },
                            {
                                id: $scope.gntId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            },
                            {
                                id: $scope.dcId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            }],
                        
                    })
                    $scope.OrderingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                    $scope.remainingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                }
                for (var i = 0; i <= $scope.noOfDays - 1; i++) {
                    $scope.dataEntry.push({
                        dayType: 'ORDERING_DAY',
                        date: appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"),
                        brands: [{
                            id: $scope.chaayosId,
                            saleAmount: 0,
                            deliverySalePercentage: 0
                        },
                            {
                                id: $scope.gntId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            },
                            {
                                id: $scope.dcId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            }]
                    })
                    $scope.OrderingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                    $scope.onlyOrderingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                }
                console.log("data entry is :",$scope.dataEntry);
                console.log("Ordering days final is  : ",$scope.OrderingDaysFinal);
                if (appUtil.isEmptyObject($scope.noOfDays)) {
                    $toastService.create("Please Enter Ordering Days..!");
                    return false;
                }
                else {
                    $scope.getNewExpiryProduct();
                }
            };

            $scope.getDayOfWeek = getDayOfWeek;
            $scope.getDayOfWeekFromStr = getDayOfWeekFromStr;
            $scope.getDaysOfWeek = getDaysOfWeek;


            $scope.getSalesPercentage = function () {
                console.log("uit data",$scope.unitData)
                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    salesData: $scope.dataEntry,

                };


                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.salesPercentage,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        // $toastService.create("Reference order estimates calculated successfully!");
                        $scope.dataEntry = response.data.salesData;
                        $scope.dataEntry.forEach(function (data) {
                            data.date=$scope.dateformatting(data.date)
                        })
                        console.log($scope.dataEntry)
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }


            $scope.getReferenceQuantitiesV1 = function () {

                if ($scope.hasCategoryBuffer) {
                    if (!$scope.categoryBuffer) {
                        alert("please enter category buffer percentage or unselect buffer box");
                        return;
                    }
                    if ($scope.selectedCategories.length == 0) {
                        alert("please select product categories on which buffer has to be applied");
                        return;
                    }
                }
                if($scope.menuCategories==null || ($scope.menuCategories!=null && $scope.menuCategories.length==0)){
                    $toastService.create("Wait to load category list.");
                    return;
                }
                if($scope.selectedBrandDetails==null || $scope.selectedBrandDetails===undefined){
                    $toastService.create("Select Brand");
                    return;
                }
                if (!$scope.expiryDataCheck) {
                    $toastService.create("Wait to load Expiring products data ..!");
                    return;
                }
                var brandMenuCategoriesDetail = $scope.selectedBrandDetails.brandName!=="BOTH" ? getBrandWiseMenuCategories(angular.copy($scope.menuCategories)) : $scope.menuCategories;
                $scope.showFulfillmentDateSelection = false;
                $scope.showMenuItemList = true;
                var brandName=  $scope.selectedBrandDetails.brandName=== "BOTH" ? null : $scope.selectedBrandDetails.brandName;
                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    fulfillmentDate: $scope.dateformatting($scope.fulfillmentDate),
                    noOfDays: $scope.noOfDays,
                    brandName: brandName,
                    categoryList: brandMenuCategoriesDetail,
                    salesData: $scope.dataEntry,
                    bufferedCategoryList: $scope.selectedCategories,
                    categoryBufferPercentage: $scope.categoryBuffer,
                    requestedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id +"]"
                };
                $scope.totalSale = 0;
                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.suggestingOrderEstimatesV1,
                    // url: apiJson.urls.referenceOrderManagement.referenceOrderEstimates,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        $toastService.create("Reference order estimates calculated successfully!");
                        $scope.menuCategories = response.data.categoryList;
                        $scope.dineInSale = response.data.dineInSale;
                        $scope.deliverySale = response.data.deliverySale;
                        $scope.totalSale = response.data.totalSale;
                        $scope.refreshDate = response.data.refreshDate;
                        if (response.data.scmSuggestions != null) {
                            $scope.f9ScmSuggestions = response.data.scmSuggestions;
                            $scope.makeF9ScmSuggestionsTotals($scope.f9ScmSuggestions);
                        }
                        $scope.makeDayWiseMenuOrderings();
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                        $scope.totalSale = 0;
                    }
                }, function error(response) {
                    $toastService.create("Error Occurred While getting predictions from fountain 9..!");
                    $scope.totalSale = 0;
                    console.log("error:" + response);
                });
            };

            $scope.makeF9ScmSuggestionsTotals = function (scmSuggestions) {
                $scope.totalScmSuggestions = {};
                angular.forEach(scmSuggestions, function (value, key) {
                    if ($scope.remainingDaysFinal.indexOf(key) == -1 ) {
                        angular.forEach(value, function (innerValue, innerKey) {
                            if (appUtil.isEmptyObject( $scope.totalScmSuggestions[innerKey])) {
                                $scope.totalScmSuggestions[innerKey] = innerValue;
                            }
                            else {
                                $scope.totalScmSuggestions[innerKey] = $scope.totalScmSuggestions[innerKey] + innerValue;
                            }
                        });
                    }
                });
                console.log("Total of Scm Suggestions are : ",$scope.totalScmSuggestions);
            };

            $scope.makeDayWiseMenuOrderings = function () {
                $scope.dayWiseMenuProducts = {};
                console.log("day entry is :",$scope.dataEntry);
                $scope.dataEntry.forEach(function (entry) {
                    if (entry.dayType == "ORDERING_DAY") {
                        var date = entry.date;
                        var copyOfMenu = angular.copy($scope.menuCategories);
                        $scope.dayWiseMenuProducts[date] = getCopyOfDayMenu(copyOfMenu,date,false);
                    }
                    else {
                        var date = entry.date;
                        var copyOfMenu = angular.copy($scope.menuCategories);
                        $scope.dayWiseMenuProducts[date] = getCopyOfDayMenu(copyOfMenu,date,true);
                    }
                });
                console.log("day wise Menu are : ",$scope.dayWiseMenuProducts);
                $scope.makeDayWiseScmProducts($scope.dayWiseMenuProducts,$scope.dataEntry);
            };

            function getScmProducts(scmProducts, day) {
                angular.forEach(scmProducts, function (scmProd) {
                    if (!appUtil.isEmptyObject($scope.f9ScmSuggestions) && !appUtil.isEmptyObject($scope.f9ScmSuggestions[day])) {
                        var currentDaySuggestions = $scope.f9ScmSuggestions[day];
                        if (!appUtil.isEmptyObject(currentDaySuggestions[scmProd.id])) {
                            scmProd.originalOrderingQuantity = angular.copy(scmProd.orderingQuantity);
                            scmProd.originalSuggestedQuantity = angular.copy(scmProd.suggestedQuantity);
                            scmProd.orderingQuantity = currentDaySuggestions[scmProd.id];
                            scmProd.suggestedQuantity = currentDaySuggestions[scmProd.id];
                        }
                    }
                });
                return scmProducts;
            }

            $scope.makeDayWiseScmProducts = function (menuProductsCategories, dataEntryData) {
                $scope.dayWiseScmProducts = {};
                $scope.scmProdsDayWiseMap = {};
                $scope.f9ScmSuggestionMissedProducts = [];
                angular.forEach(menuProductsCategories,function (value,key) {
                    recipeService.getScmProductsFromDayWiseMenu($scope.unitData, value, function (scmProducts) {
                        $scope.dayWiseScmProducts[key] = getScmProducts(angular.copy(scmProducts), key);
                        $scope.makeCopyOfDayWiseScm(angular.copy($scope.dayWiseScmProducts[key]),key);
                        console.log("For Day ", key, " the missed Products are : ",$scope.f9ScmSuggestionMissedProducts);
                    });
                });
                console.log("Day wise SCM are : ", angular.copy($scope.dayWiseScmProducts));
                console.log("scmProdsDayWiseMap is : ", angular.copy($scope.scmProdsDayWiseMap));
                $scope.makeScmProductWiseMap(angular.copy($scope.dayWiseScmProducts),dataEntryData);
            };

            $scope.makeCopyOfDayWiseScm = function (prods,day) {
                  angular.forEach(prods, function (prod) {
                        var key = day + "_" +prod.id;
                        $scope.scmProdsDayWiseMap[key] = prod;
                  });
                  // checking for missed products while converting Menu to SCM and F9 has suggested some Quantity For SCM level
                if (!appUtil.isEmptyObject($scope.f9ScmSuggestions) && !appUtil.isEmptyObject($scope.f9ScmSuggestions[day])) {
                    var currentDaySuggestions = $scope.f9ScmSuggestions[day];
                    angular.forEach(currentDaySuggestions, function (value, productId) {
                        var key = day + "_" + productId;
                        if (appUtil.isEmptyObject($scope.scmProdsDayWiseMap[key])) {
                            var message = productId + " - " + day + " - " + value;
                            $scope.f9ScmSuggestionMissedProducts.push(message);
                        }
                    });
                }
            };

            $scope.makeScmProductWiseMap = function (scmProducts,dataEntryData) {
                $scope.copyOfExpiryProduct = angular.copy($scope.expiryProduct);
                $scope.copyOfTotalStock = angular.copy($scope.totalDayWiseExpiry);
                console.log("copy of total stock is : ",$scope.copyOfTotalStock);
                $scope.getScmProductsAfterExpiry(scmProducts,dataEntryData);
            };

            $scope.getScmProductsAfterExpiry = function (scmProducts,dataEntryData) {
                $scope.scmProductWiseMap = {};
                console.log("entries are ss: ",dataEntryData);
                for (var i=0; i<$scope.OrderingDaysFinal.length; i++) {
                    var prods = scmProducts[$scope.OrderingDaysFinal[i]];
                    if (prods != undefined && prods != null) {
                        var dup = angular.copy($scope.OrderingDaysFinal);
                        for (var j=0;j<prods.length;j++) {
                            if ($scope.copyOfTotalStock[prods[j].id] != undefined && $scope.copyOfTotalStock[prods[j].id] != null) {
                                var arr = getArray(dup, i);
                                console.log("current prod is and date is  : ", prods[j],$scope.OrderingDaysFinal[i]);
                                var finalQty = checkForExpiry(prods[j], arr);
                                if ($scope.remainingDaysFinal.indexOf($scope.OrderingDaysFinal[i]) == -1) {
                                    if ($scope.scmProductWiseMap[prods[j].id] == undefined || $scope.scmProductWiseMap[prods[j].id] == null) {
                                        $scope.scmProductWiseMap[prods[j].id] = parseFloat(finalQty);
                                    } else {
                                        var qty = $scope.scmProductWiseMap[prods[j].id];
                                        $scope.scmProductWiseMap[prods[j].id] = qty + parseFloat(finalQty);
                                    }
                                }
                                else {
                                    console.log("Changed data for remaining days qty ..!");
                                }
                            }
                        }
                    }
                }
                console.log("scm Product wise map is : ",$scope.scmProductWiseMap);
                $scope.getProductWiseStock();
            }

            $scope.getProductWiseStock = function () {
                $scope.productWiseStock = {};
                angular.forEach($scope.dayWiseExpiryProduct['inStock'],function (value,key) {
                    var total = 0;
                    angular.forEach($scope.dataEntry,function (entry) {
                        if (value[entry.date] != undefined && value[entry.date] != null) {
                            total = total + value[entry.date];
                            if (appUtil.isEmptyObject($scope.productWiseStock[key])) {
                                $scope.productWiseStock[key] = {};
                                $scope.productWiseStock[key].stockAtHand = {};
                                $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                            }
                            else {
                                if (appUtil.isEmptyObject($scope.productWiseStock[key].stockAtHand)) {
                                    $scope.productWiseStock[key].stockAtHand = {}
                                    $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                    $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                                }
                                else {
                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].stockAtHand["totalStock"])) {
                                        $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                    } else {
                                        $scope.productWiseStock[key].stockAtHand["totalStock"] = $scope.productWiseStock[key].stockAtHand["totalStock"] + total;
                                    }

                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].stockAtHand[entry.date])) {
                                        $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                                    } else {
                                        $scope.productWiseStock[key].stockAtHand[entry.date] = $scope.productWiseStock[key].stockAtHand[entry.date] + value[entry.date];
                                    }
                                }
                            }
                        }
                    });
                });

                angular.forEach($scope.dayWiseExpiryProduct['inTransit'],function (value,key) {
                    var total = 0;
                    angular.forEach($scope.dataEntry,function (entry) {
                        if (value[entry.date] != undefined && value[entry.date] != null) {
                            total = total + value[entry.date];
                            if (appUtil.isEmptyObject($scope.productWiseStock[key])) {
                                $scope.productWiseStock[key] = {};
                                $scope.productWiseStock[key].inTransit = {};
                                $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                            }
                            else {
                                if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit)) {
                                    $scope.productWiseStock[key].inTransit = {};
                                    $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                    $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                                }
                                else {
                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit["totalStock"])) {
                                        $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                    } else {
                                        $scope.productWiseStock[key].inTransit["totalStock"] = $scope.productWiseStock[key].inTransit["totalStock"] + total;
                                    }

                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit[entry.date])) {
                                        $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                                    } else {
                                        $scope.productWiseStock[key].inTransit[entry.date] = $scope.productWiseStock[key].inTransit[entry.date] + value[entry.date];
                                    }
                                }
                            }
                        }
                    });
                });

                angular.forEach($scope.dayWiseExpiryProduct['acknowledgedRoInTransit'],function (value,key) {
                    var total = 0;
                    angular.forEach($scope.dataEntry,function (entry) {
                        if (value[entry.date] != undefined && value[entry.date] != null) {
                            total = total + value[entry.date];
                            if (appUtil.isEmptyObject($scope.productWiseStock[key])) {
                                $scope.productWiseStock[key] = {};
                                $scope.productWiseStock[key].inTransit = {};
                                $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                            }
                            else {
                                if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit)) {
                                    $scope.productWiseStock[key].inTransit = {};
                                    $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                    $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                                }
                                else {
                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit["totalStock"])) {
                                        $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                    } else {
                                        $scope.productWiseStock[key].inTransit["totalStock"] = $scope.productWiseStock[key].inTransit["totalStock"] + total;
                                    }

                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit[entry.date])) {
                                        $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                                    } else {
                                        $scope.productWiseStock[key].inTransit[entry.date] = $scope.productWiseStock[key].inTransit[entry.date] + value[entry.date];
                                    }
                                }
                            }
                        }
                    });
                });
                console.log("Product wise stock of Stock at hand and intransit is : ",$scope.productWiseStock);

                $scope.makeProductWiseExpiryData($scope.productWiseStock);
            };

            $scope.makeProductWiseExpiryData = function (pws) {
                  console.log("pws is : ",pws);
                  $scope.productWiseExpiryData = {};
                  angular.forEach(pws, function (typeOfStock,productId) {
                      $scope.productWiseExpiryData[productId] = {};
                      var currentObj = $scope.productWiseExpiryData[productId];
                      angular.forEach(typeOfStock, function (dateWise, type ) {
                          angular.forEach(dateWise, function (quantity, date) {
                              if (!date.startsWith('t')) {
                                  var key = date + '_' + type;
                                  currentObj[key]  = quantity;
                                  $scope.productWiseExpiryData[productId] = currentObj;
                              }
                          });
                      });
                  });
                  console.log("Final ProductWise data is : ",$scope.productWiseExpiryData);
            };

            function getArray(dup,index) {
                var res = [];
                for (var i=0;i<dup.length;i++) {
                    if (i >= index) {
                        res.push(dup[i]);
                    }
                }
                return res;
            }

            function checkForExpiry(product,dayEntry) {
                for (var i=0;i<dayEntry.length;i++) {
                    var currentQty = parseFloat(product.orderingQuantity);
                    var expiryProduct = $scope.copyOfTotalStock[product.id];
                    if (expiryProduct != undefined && expiryProduct != null) {
                        var dayExpiry = expiryProduct[dayEntry[i]];
                        if (product.id == 100179) {
                            console.log("day expiry is : ",angular.copy(dayExpiry));
                        }
                        if (dayExpiry != undefined && dayExpiry != null) {
                            console.log("Before updating the expiry quantity is for product id: ",product.id,angular.copy(expiryProduct));
                            console.log("Date is and current qty is : ",dayEntry[i],angular.copy(currentQty));
                            if (currentQty > 0) {
                                if (currentQty >= dayExpiry) {
                                    currentQty = currentQty - dayExpiry;
                                    expiryProduct[dayEntry[i]] = 0;
                                    product.orderingQuantity = currentQty;
                                    console.log("After updating the expiry quantity is for product id: ",product.id,angular.copy(expiryProduct));
                                    console.log("After updating the expiry quantity total expiry is :  ",angular.copy($scope.copyOfTotalStock));
                                    if (currentQty == 0) {
                                        break;
                                    }
                                }
                                else {
                                    expiryProduct[dayEntry[i]] = dayExpiry - currentQty;
                                    currentQty = 0;
                                    product.orderingQuantity = currentQty;
                                    console.log("After updating the expiry quantity is for product id: ",product.id,angular.copy(expiryProduct));
                                    console.log("After updating the expiry quantity total expiry is :  ",angular.copy($scope.copyOfTotalStock));
                                    break;
                                }
                            }
                            else {
                                break;
                            }
                        }
                    }
                }
                return product.orderingQuantity;
            }

            function getCopyOfDayMenu(menuCategory,date,isSales) {
                for(var category in menuCategory){
                    for(var product in menuCategory[category].productList){
                        var qty = 0;
                        if (isSales) {
                            qty = menuCategory[category].productList[product].dateRemaining[date];
                        }
                        else {
                            qty = menuCategory[category].productList[product].dateOrderings[date];
                        }
                        if( qty != undefined && qty != null){
                            menuCategory[category].productList[product].quantity = qty;
                            menuCategory[category].productList[product].requestedQuantity = qty;
                        }
                        else {
                            menuCategory[category].productList[product].quantity = 0;
                            menuCategory[category].productList[product].requestedQuantity = 0;
                        }
                    }
                }
                return menuCategory;
            }

            $scope.viewDetailedExpiries = function () {
                var expiriesModal = Popeye.openModal({
                    templateUrl: "expiriesModal.html",
                    controller: "expiriesModalCtrl",
                    modalClass: 'custom-modal',
                    resolve: {
                        scmSuggested: function () {
                            return $scope.scmProdsDayWiseMap;
                        },
                        expiries : function () {
                            return $scope.productWiseStock;
                        },
                        dates: function () {
                            return $scope.dataEntry;
                        }
                    },
                    click: false,
                    keyboard: false
                });
            };

            function getBrandWiseMenuCategories(menuCategory){

                for(var category in menuCategory){
                    var productListCopy=[];
                    for(var product in menuCategory[category].productList){
                        if(menuCategory[category].productList[product].brandId== $scope.selectedBrandDetails.id){
                            console.log(menuCategory[category].productList[product].brandId + ":::::"+$scope.selectedBrandDetails.id);
                            productListCopy.push(menuCategory[category].productList[product]);
                        }
                    }
                    menuCategory[category].productList=productListCopy;
                }
                return menuCategory;
            }

            $scope.updateProductQuantity = function (product, variant) {
                var totalQuantity = 0;
                variant.orderedQuantity = parseInt(variant.orderedQuantity);
                product.variants.forEach(function (variant) {
                    totalQuantity += variant.orderedQuantity;
                });
                product.quantity = totalQuantity;
            }

            $scope.clearRequestOrder = function () {
                if (confirm("Are you sure?")) {
                    $scope.scmProductList = [];
                    $scope.comment = null;
                    $scope.menuCategories = recipeService.createMenuItemCategories();
                    $scope.showFulfillmentDateSelection = true;
                    $scope.showMenuItemList = false;
                    $scope.selectedCategories = [];
                    $scope.categoryBuffer = null;
                    $scope.hasCategoryBuffer = false;
                    $scope.dataEntry.forEach(function (data) {
                        data.brands.forEach(function (brand) {
                            brand.saleAmount = 0
                            brand.deliverySalePercentage=0
                        })
                    });

                }
            }
            $scope.resetMenuItem = function () {
                $scope.menuCategories = recipeService.createMenuItemCategories();
            }

            $scope.updateMenuProductQty = function (product) {
                product.requestedQuantity = parseInt(product.requestedQuantity);
            }

            $scope.createSCMProductList = function () {
                $scope.scmProductList = [];
                recipeService.getProductsFromCategoryListNewRegularOrdering($scope.unitData, $scope.menuCategories,
                    $scope.scmProductWiseMap,$scope.productWiseStock,$scope.totalScmSuggestions,$scope.fulfilmentUnitProductsMap, function (itemList) {
                    var prods = [];
                    console.log("After All days conversion SCM Products are : ",itemList);
                    itemList.forEach(function (item) {
                        if (item.selectedFulfillmentType != "EXTERNAL" && item.selectedFulfillmentType != null) {
                            // item.stock = item.stockAtHand + item.inTransit;
                            // item.stockAtHand = item.stockAtHand == 0 ? item.stockAtHand : parseFloat(item.stockAtHand).toFixed(1);
                            // item.inTransit = item.inTransit == 0 ? item.inTransit : parseFloat(item.inTransit).toFixed(1);
                            prods.push(item);
                        }
                    });
                    //prods = setMinOrderQuantity(prods);
                    $scope.scmProductList = prods;
                    // finding the missed F9 Product Suggestions
                    if (!appUtil.isEmptyObject($scope.totalScmSuggestions)) {
                        angular.forEach($scope.totalScmSuggestions , function (value, key) {
                            var found = false;
                             for (var i=0; i< prods.length ; i++) {
                                 if (parseInt(key) == prods[i].id) {
                                     found = true;
                                     break;
                                 }
                             }
                             if (!found) {
                                 var message = key + " - "+ value ;
                                 $scope.f9ScmSuggestionMissedProducts.push(message);
                             }
                        });
                    }
                    console.log("String of F9 scm Suggestions missing Products ::: ",$scope.f9ScmSuggestionMissedProducts);
                    return prods;
                });
                $scope.showMenuItemList = false;
            };

            $scope.hideWareHouseItems = function (check) {
                $scope.warehouseordering = check;
            };

            $scope.backToMenuItemList = function () {
                if (confirm("Are you sure?")) {
                    $scope.scmProductList = [];
                    $scope.showMenuItemList = true;
                    $scope.comment = null;
                }
            }

            $scope.updateOrderingQty = function (product) {
                if (product.critical) {
                    var qty = parseInt(product.packagingQuantity);
                    if (qty > product.duplicatePackagingQuantity) {
                        $toastService.create("Final Ordering Quantity can not be greater than Suggested for Critical Products..!");
                        product.packagingQuantity = product.duplicatePackagingQuantity;
                        product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                        product.reason = null;
                        return false;
                    }
                    else {
                        product.packagingQuantity = parseInt(product.packagingQuantity);
                        product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                    }
                }
                else {
                    product.packagingQuantity = parseInt(product.packagingQuantity);
                    product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                }
            }


            function validateFulfillment(reqOrder) {
                var returnList = [];
                for (var i in reqOrder.referenceOrderScmItems) {
                    if (appUtil.isEmptyObject(reqOrder.referenceOrderScmItems[i].fulfillmentType)) {
                        returnList.push(reqOrder.referenceOrderScmItems[i].productName);
                    }
                }
                return returnList;
            }

            $scope.dateformatting = function (startDate) {
                var year = new Date(startDate).getFullYear();
                var month = new Date(startDate).getMonth() + 1;
                var day = new Date(startDate).getDate();
                if (day >= 1 && day < 10)
                    day = '0' + day;
                if (month >= 1 && month < 10)
                    month = '0' + month;
                return year + "-" + month + "-" + day;
            }

            $scope.sendReferenceOrder = function (action) {
                var data = createRoObject(action);
                var validateOrder = validateFulfillment(data);
                if ($scope.fulfillmentDate == null) {
                    $toastService.create('Please fill fulfillment date!');
                    return false;
                } else if (data.referenceOrderScmItems.length == 0) {
                    $toastService.create('Please select at least a few products to order!');
                    return false;
                } else if (validateOrder.length > 0) {
                    $alertService.alert('Products found without fulfillment types', validateOrder.join(","), null, true);
                    return false;
                } else {
                    data.refOrderSource="FOUNTAIN9_DATA_SOURCE_ORDERING";
                    if (confirm("Are you sure you want to create the order?")) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.referenceOrderManagement.newReferenceOrder,
                            data: data
                        }).then(function success(response) {
                            console.log(response);
                            if (response.data != null && response.data.referenceOrderId > 0) {
                                $toastService.create("Reference order with id " + response.data.referenceOrderId + " created successfully!");
                                if (response.data.regularOrderEvents.length > 0) {
                                    $state.go("menu.refOrderCreateV1", {orderingEvents : response.data.regularOrderEvents});
                                }
                                else {
                                    $rootScope.orderingEvents = [];
                                    $state.go("menu.reqOrderMgt");
                                }
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                            if (response.data.errorMsg != null) {
                                $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                            } else {
                                $toastService.create("Error Occurred While Placing Order..!");
                            }
                        });
                    }
                }
            }

            function getMissedF9Products() {
                var message = null;
                if ($scope.f9ScmSuggestionMissedProducts.length > 0) {
                    angular.forEach($scope.f9ScmSuggestionMissedProducts, function (msg) {
                        if (message != null) {
                            message = message + "\n" +  msg;
                        } else {
                            message =  msg;
                        }
                    });
                }
                return message;
            }

            function createRoObject(status) {
                return {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    requestUnit: appUtil.createRequestUnit(),
                    generatedBy: appUtil.createGeneratedBy(),
                    fulfillmentUnit: null,
                    fulfillmentDate: new Date($scope.fulfillmentDate),
                    status: status,
                    comment: $scope.comment,
                    referenceOrderMenuItems: getMenuItems(),
                    referenceOrderScmItems: getScmItems(),
                    numberOfDays:$scope.noOfDays,
                    raiseBy:$scope.raiseBy,
                    orderEvent: $scope.regularOrderingEvent,
                    refreshDate: $scope.refreshDate,
                    missedF9Products : getMissedF9Products()
                }
            }

            function getMenuItems() {
                var products = [];
                $scope.menuCategories.forEach(function (category) {
                    var productList = [];
                    category.productList.forEach(function (product) {
                        if (product.requestedQuantity > 0) {
                            var variantList = [];
                            product.variants.forEach(function (variant) {
                                if (variant.orderedQuantity > 0) {
                                    variantList.push({
                                        id: null,
                                        name: variant.name,
                                        conversionQuantity: variant.conversionQuantity,
                                        orderedQuantity: variant.orderedQuantity
                                    });
                                }
                            });
                            product.variants = variantList;
                            productList.push({
                                id: null,
                                productId: product.productId,
                                productName: product.productName,
                                dimension: product.dimension,
                                requestedQuantity: product.requestedQuantity,
                                requestedAbsoluteQuantity: product.requestedQuantity,
                                transferredQuantity: null,
                                receivedQuantity: null,
                                quantity: product.quantity,
                                dineInQuantity: product.dineInQuantity,
                                deliveryQuantity: product.deliveryQuantity,
                                takeawayQuantity: product.takeawayQuantity,
                                variants: product.variants
                            })
                        }
                    });
                    if (products.length == 0) {
                        products = productList;
                    } else {
                        products = products.concat(productList);
                    }
                });
                return products;
            }

            function getExpiryDrillDown(productId) {
                if ($scope.productWiseExpiryData[productId] != undefined && $scope.productWiseExpiryData[productId] != null) {
                    return $scope.productWiseExpiryData[productId];
                }
                return null;
            }

            function getScmItems() {
                var scmProducts = [];
                $scope.scmProductList.forEach(function (product) {
                    if (product.orderingQuantity >= 0) {
                        //console.log(product);
                        if ($scope.showOnlyWarehouseItems) {
                            if (product.selectedFulfillmentType === 'WAREHOUSE') {
                                scmProducts.push({
                                    id: null,
                                    productId: product.id,
                                    productName: product.name,
                                    suggestedQuantity: product.suggestedQuantity,
                                    requestedQuantity: product.orderingQuantity,
                                    requestedAbsoluteQuantity: product.orderingQuantity,
                                    transferredQuantity: null,
                                    receivedQuantity: null,
                                    fulfillmentType: product.selectedFulfillmentType,
                                    unitOfMeasure: product.unitOfMeasure,
                                    predictedQuantity: product.predictedQuantity,
                                    expiryDrillDown: getExpiryDrillDown(product.id),
                                    reason: !appUtil.isEmptyObject(product.reason) ? product.reason : null
                                });
                            }
                        }
                        else {
                            if (product.selectedFulfillmentType != 'WAREHOUSE') {
                                scmProducts.push({
                                    id: null,
                                    productId: product.id,
                                    productName: product.name,
                                    suggestedQuantity: product.suggestedQuantity,
                                    requestedQuantity: product.orderingQuantity,
                                    requestedAbsoluteQuantity: product.orderingQuantity,
                                    transferredQuantity: null,
                                    receivedQuantity: null,
                                    fulfillmentType: product.selectedFulfillmentType,
                                    unitOfMeasure: product.unitOfMeasure,
                                    predictedQuantity: product.predictedQuantity,
                                    expiryDrillDown: getExpiryDrillDown(product.id),
                                    reason: !appUtil.isEmptyObject(product.reason) ? product.reason : null
                                });
                            }
                        }
                    }
                });
                return scmProducts;
            }


            function weekDays() {
                var days = [
                    {id: 1, value: 'Sunday'},
                    {id: 2, value: 'Monday'},
                    {id: 3, value: 'Tuesday'},
                    {id: 4, value: 'Wednesday'},
                    {id: 5, value: 'Thursday'},
                    {id: 6, value: 'Friday'},
                    {id: 7, value: 'Saturday'}];
                return days;
            }

            function getDayOfWeek(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                console.log('Selected Date', date.getDay());
                return weekDays()[date.getDay()].value;
            }

            function getDaysOfWeek() {
                return "NA";
            }

            function getDayOfWeekFromStr(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                return weekDays()[new Date(date).getDay()].value;
            }
            $scope.update = function (){
                $scope.raiseBy=!$scope.raiseBy;
            }

        }]).controller('expiriesModalCtrl', ['$scope', 'Popeye','scmSuggested','expiries','dates','appUtil',
        function ($scope, Popeye, scmSuggested, expiries,dates,appUtil) {
            $scope.scmSuggested = scmSuggested;
            $scope.expiries = expiries;
            $scope.dates = dates;

            $scope.colHeaders = ["Product Name"];
            angular.forEach(dates, function (currentDate) {
                var currDate = new Date(currentDate.date);
                 var col = currDate.getDate() +"/"+ (currDate.getMonth() + 1) + " (Suggested, Ordering, In-Stock, In-Transit) ";
                 $scope.colHeaders.push(col);
            });

            $scope.mapOfRows = {};

             function generateObject(prod) {
                var obj = {};
                obj.productId = prod.id;
                obj.productName = prod.name;
                obj.uom = prod.unitOfMeasure;
                angular.forEach($scope.dates, function (currDate) {
                    var key =  currDate.date + "_suggested";
                    obj[key] = 0;
                    key =  currDate.date + "_ordering";
                    obj[key] = 0;
                    key =  currDate.date + "_inStock";
                    obj[key] = 0;
                    key =  currDate.date + "_inTransit";
                    obj[key] = 0;
                    obj.total = 0;
                });
                return obj;
            }

            angular.forEach($scope.scmSuggested , function (scmProduct,scmKey) {
                var productId = scmKey.split("_");
                if (appUtil.isEmptyObject($scope.mapOfRows[productId[1]])) {
                    var obj = generateObject(scmProduct);
                    var key = productId[0] +"_ordering";
                    obj[key] = scmProduct.orderingQuantity;
                    key = productId[0] +"_suggested";
                    obj[key] = scmProduct.suggestedQuantity;
                    key = productId[0] +"_inTransit";
                    if (!appUtil.isEmptyObject($scope.expiries)) {
                        if (!appUtil.isEmptyObject($scope.expiries[productId[1]]) && !appUtil.isEmptyObject($scope.expiries[productId[1]]["inTransit"])) {
                            var inTransit = $scope.expiries[productId[1]]["inTransit"];
                            if (!appUtil.isEmptyObject(inTransit[productId[0]])) {
                                obj[key] = inTransit[productId[0]];
                            }
                        }
                    }
                    key = productId[0] +"_inStock";
                    if (!appUtil.isEmptyObject($scope.expiries)) {
                        if (!appUtil.isEmptyObject($scope.expiries[productId[1]]) && !appUtil.isEmptyObject($scope.expiries[productId[1]]["stockAtHand"])) {
                            var inStock = $scope.expiries[productId[1]]["stockAtHand"];
                            if (!appUtil.isEmptyObject(inStock[productId[0]])) {
                                obj[key] = inStock[productId[0]];
                            }
                        }
                    }
                    $scope.mapOfRows[productId[1]] = obj;
                } else {
                    // $scope.mapOfRows[productId[1]] = scmProduct;
                    var obj = $scope.mapOfRows[productId[1]];
                    var key = productId[0] +"_ordering";
                    obj[key] = scmProduct.orderingQuantity;
                    key = productId[0] +"_suggested";
                    obj[key] = scmProduct.suggestedQuantity;
                    key = productId[0] +"_inTransit";
                    if (!appUtil.isEmptyObject($scope.expiries)) {
                        if (!appUtil.isEmptyObject($scope.expiries[productId[1]]) && !appUtil.isEmptyObject($scope.expiries[productId[1]]["inTransit"])) {
                            var inTransit = $scope.expiries[productId[1]]["inTransit"];
                            if (!appUtil.isEmptyObject(inTransit[productId[0]])) {
                                obj[key] = inTransit[productId[0]];
                            }
                            if (!appUtil.isEmptyObject(inTransit["totalStock"])) {
                                obj.total = inTransit["totalStock"];
                            }
                        }
                    }
                    key = productId[0] +"_inStock";
                    if (!appUtil.isEmptyObject($scope.expiries)) {
                        if (!appUtil.isEmptyObject($scope.expiries[productId[1]]) && !appUtil.isEmptyObject($scope.expiries[productId[1]]["stockAtHand"])) {
                            var inStock = $scope.expiries[productId[1]]["stockAtHand"];
                            if (!appUtil.isEmptyObject(inStock[productId[0]])) {
                                obj[key] = inStock[productId[0]];
                            }
                            if (!appUtil.isEmptyObject(inStock["totalStock"])) {
                                obj.total = inStock["totalStock"];
                            }
                        }
                    }
                    $scope.mapOfRows[productId[1]] = obj;
                }
            });

            $scope.displayRows = Object.values($scope.mapOfRows);
            console.log("Rows are : ",$scope.displayRows);

            $scope.close = function () {
                Popeye.closeCurrentModal();
            };
        }])
    .filter('toArray', function () {
    return function (obj, addKey) {
        if (!angular.isObject(obj)) return obj;
        if (addKey === false) {
            return Object.keys(obj).map(function (key) {
                return obj[key];
            });
        } else {
            return Object.keys(obj).map(function (key) {
                var value = obj[key];
                return angular.isObject(value) ?
                    Object.defineProperty(value, '$key', {enumerable: false, value: key}) :
                    {$key: key, $value: value};
            });
        }
    };
});
