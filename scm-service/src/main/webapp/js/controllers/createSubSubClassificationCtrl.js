angular.module('scmApp').controller(
		'createSubSubClassificationCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
	        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
	        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
	                  previewModalService, Popeye, $timeout, $window) {
			
					$scope.init = function () {
                        $scope.loginType=   $rootScope.loginType=="sumo"?"GOODS":"SERVICE";
						$scope.initialize();

					}
					
					$scope.initialize = function () {
						$scope.listTypes = ["Classification"];
						$scope.getListMap();
						var map = null;
						$scope.categories = [];
						$scope.departments = [];
						$scope.divisions = [];
						$scope.categoryData = [];
						$scope.subCategoryData = [];
						$scope.subSubCategoryData = [];
					}
					
					$scope.getListMap = function (){
						$http({
							method : 'GET',
							url : apiJson.urls.serviceOrderManagement.getListData+"?baseType="+$scope.loginType,
						}).then(function success(response) {
							if(response.data != null){
								map = response.data;
							}
						}, function error(response) {
				               console.log("error:" + response);
				          })
					}
					
					$scope.getListDetails = function(selectedList){
						$scope.categoryData = map[selectedList];
					}
					
					$scope.getSubCategories = function(selectedCategory){
						for(var i = 0 ; i < $scope.categoryData.length ; i++){
							if($scope.categoryData[i].listDetailId == selectedCategory.listDetailId){
								$scope.subCategoryData = $scope.categoryData[i].listType;
							}
						}
					}
					
					$scope.getSubSubCategories = function(subCategorySelected){
						for(var i = 0 ; i < $scope.subCategoryData.length ; i++){
						if($scope.subCategoryData[i].listTypeId == subCategorySelected.listTypeId){
							$scope.subSubCategoryData = $scope.subCategoryData[i].listData;
						}
					}}
					
					$scope.addSubSubCategoryData = function () {
		                var modalInstances = Popeye.openModal({
		                    ariaLabelledBy: 'modal-title',
		                    ariaDescribedBy: 'modal-body',
		                    templateUrl: 'addSubSubCategoryModal.html',
		                    controller: 'addSubSubCategoryModalCtrl',
		                    backdrop: 'static',
		                    keyboard: false,
		                    scope:$scope,
		                    size: 'lg',
		                    resolve: {
		                        items: function () {
		                            return {
		                            	mode: 'add'
		                            }
		                        }
		                    }
		                });
		                modalInstances.closed
		                .then(function (isSuccessful) {
		                    if (isSuccessful) {
		                    	$scope.initialize();
		                    }
		                });
		            };
		            
		            $scope.editSubSubCategoryDetail = function (listData) {
		                var modalInstances = Popeye.openModal({
		                    ariaLabelledBy: 'modal-title',
		                    ariaDescribedBy: 'modal-body',
		                    templateUrl: 'addSubSubCategoryModal.html',
		                    controller: 'addSubSubCategoryModalCtrl',
		                    backdrop: 'static',
		                    keyboard: false,
		                    scope: $scope,
		                    size: 'lg',
		                    resolve: {
		                        items: function () {
		                            return {
		                            	name: listData.name, code: listData.code,
		                            	alias: listData.alias, description: listData.description,
		                            	status: listData.status,
		                            	listDataId: listData.listDataId, mode: 'edit'
		                            }
		                        }
		                    }
		                });
		                modalInstances.closed
		                .then(function (isSuccessful) {
		                    if (isSuccessful) {
		                    	$scope.initialize();
		                    }
		                });
		            };
					
					
	                
		}]
).controller('addSubSubCategoryModalCtrl', ['$scope', 'apiJson', '$http','items',  'appUtil', '$location', '$toastService', 'metaDataService',
    '$fileUploadService', '$alertService', 'previewModalService', 'Popeye','$timeout','$window',
    function ($scope, apiJson, $http, items, appUtil, $location, $toastService, metaDataService,
              $fileUploadService, $alertService, previewModalService, Popeye, $timeout, $window) {

	
	$scope.init = function (){
		$scope.periods = ["ACTIVE","IN_ACTIVE"];
		$scope.listTypeMods = ["Classification"];
		$scope.categories = [];
		$scope.subCategories = [];
		
		$scope.name = items.name;
        $scope.code = items.code;
       // $scope.make = items.make == "NA" ? "" : items.make;
        $scope.alias = items.alias;
        $scope.description = items.description;
        $scope.status = items.status;
        
        if(items.mode == "add"){
       	 $scope.listDataId = null;
       	$scope.editMode = false;
        }
        else{
       	 $scope.listDataId = items.listDataId;
       	$scope.editMode = true;
        }
	}
	
	$scope.getListDetail = function(type){
		$scope.categories = map[type];
	}
	
	$scope.getcategoryDetail = function(selectedCategory){
		for(var i = 0 ; i < $scope.categories.length ; i++){
			if($scope.categories[i].listDetailId == selectedCategory.listDetailId){
				$scope.subCategories = $scope.categories[i].listType;
			}
		}
	}
	
	$scope.submitSubSubCat = function(){
		 if($scope.listDataId == null){
			 $scope.addListData();
		 }
		 else{
			 $scope.updateListData();
		 }
	 }
	
	$scope.updateListData = function(){
		if($scope.name=="" || $scope.name==null){
 			alert("Please input  name.");
 			return;
 		}
     	if($scope.code=="" || $scope.code==null){
 			alert("Please input code.");
 			return;
 		}
     	if($scope.description=="" || $scope.description==null){
  			alert("Please input description.");
  			return;
  		}
      	if($scope.alias=="" || $scope.alias==null){
  			alert("Please input alias.");
  			return;
  		}
    	if($scope.status=="" || $scope.status==null){
 			alert("Please input status.");
 			return;
 		}
		var reqObj = {
				name: $scope.name,
				code: $scope.code,
				description: $scope.description,
				alias: $scope.alias,
				status: $scope.status,
				listDataId: $scope.listDataId
		}
		$http({
			method : 'POST',
			url : apiJson.urls.serviceOrderManagement.updateSubSubCategoryData,
			data : reqObj
		}).then(function success(response) {
			if(response.data){
				$toastService.create("Sub Sub-Category Updated Successfully!");
				closeModal(true);
			}
			else{
				$toastService.create("Error In Saving Sub Sub-Category Details!");
			}
		}, function error(response) {
               console.log("error:" + response);
          })
	}
	
	$scope.addListData = function(){
		if($scope.type=="" || $scope.type==null){
 			alert("Please input type.");
 			return;
 		}
		if($scope.categorySelected=="" || $scope.categorySelected==null){
 			alert("Please input category.");
 			return;
 		}
		if($scope.subCategorySelected=="" || $scope.subCategorySelected==null){
 			alert("Please input sub category.");
 			return;
 		}
		if($scope.name=="" || $scope.name==null){
 			alert("Please input  name.");
 			return;
 		}
     	if($scope.code=="" || $scope.code==null){
 			alert("Please input code.");
 			return;
 		}
     	if($scope.description=="" || $scope.description==null){
  			alert("Please input description.");
  			return;
  		}
      	if($scope.alias=="" || $scope.alias==null){
  			alert("Please input alias.");
  			return;
  		}
    	if($scope.status=="" || $scope.status==null){
 			alert("Please input status.");
 			return;
 		}
		var reqObj = {
				name: $scope.name,
				code: $scope.code,
				description: $scope.description,
				alias: $scope.alias,
				status: $scope.status,
				listType: $scope.subCategorySelected
		}
		$http({
			method : 'POST',
			url : apiJson.urls.serviceOrderManagement.addSubSubCategoryData,
			data : reqObj
		}).then(function success(response) {
			if(response.data){
				$toastService.create("Sub Sub-Category Added Successfully!");
				closeModal(true);
			}
			else{
				$toastService.create("Error In Saving Sub Sub-Category Details!");
			}
		}, function error(response) {
               console.log("error:" + response);
          })
	}
	
	$scope.cancel=function(){
   	  closeModal(false);
     };
     
     function closeModal(data){
      	  Popeye.closeCurrentModal(data);
        }
        
    }
]
);
