/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 11-05-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('addAttributeCtrl', ['$http', '$rootScope', '$scope', 'authService', '$location', '$state',
            'appUtil', '$stateParams', 'metaDataService', '$toastService', 'apiJson',
            function ($http, $rootScope, $scope, authService, $location, $state, appUtil, $stateParams, metaDataService, $toastService, apiJson) {

                $scope.attributes = [];
                $scope.attributeMap = null;
                $scope.addAttributeFlag = false;
                $scope.attributeType = [
                    "CATEGORY",
                    "DIMENSION",
                    "ASSET"
                ]
                $scope.selected = false;
                $scope.init = function () {

                    $scope.values = {};
                    $scope.selectedValues = [];
                    $scope.countValue = 0;
                    $scope.addedValues = [];
                    $scope.attributeId = null;
                    $scope.showflag=false;
                    $scope.mappingToEdit = {};
                    $scope.currentAttribute=null;


                }


                $scope.selectOptions = {width: '100%'};

                function getAllValues(callback) {
                    metaDataService.getAttributeValues(function(values){
                        $scope.values = values;
                        if(callback != undefined){
                            callback();
                        }
                    });
                }

                function reinit(){
                    getAllValues(function(){
                        resetForm();
                        $scope.reset();
                    });
                }

                function resetForm() {
                    $scope.getValues($scope.attributeId);
                    $scope.addedValues = [];
                    $scope.countValue = 0;
                }

                $scope.getCount = function (count) {
                    return new Array(count);
                };

                $scope.init = function () {
                    metaDataService.getAttrDefinitions(function (attributes) {
                        $scope.attributeMap = attributes;
                        $scope.originalAttributeList = attributes;
                        for (var key in $scope.originalAttributeList) {
                            var subArray = $scope.originalAttributeList[key];
                            $scope.attributes = $scope.attributes.concat(subArray);
                        }
                    });
                    getAllValues();
                };

                $scope.getValues = function (attributeId) {
                    $scope.selectedValues = $scope.values[attributeId];
                };

                $scope.addToCount = function () {
                    $scope.countValue += 1;
                };

                $scope.getAttribute = function (id) {
                    var attribute = appUtil.getAttribute(id);
                    return !appUtil.checkEmpty(attribute) ? attribute.attributeName : "Not found";
                };

                $scope.editValue = function (mapping) {
                    $scope.mappingToEdit = mapping;
                };


                $scope.reset = function () {
                    $scope.mappingToEdit = {};
                };

                $scope.submitValue = function (mappingToEdit) {
                    if (!appUtil.isEmptyObject(mappingToEdit)) {
                        $http({
                            method: 'PUT',
                            url: apiJson.urls.attributeManagement.value,
                            data: mappingToEdit
                        }).then(function (response) {
                            $toastService.create("Attribute Successfully Created");
                        }, function (response) {
                            $toastService.create("Attribute Cannot be created " + response);
                            console.log("Encoutered an error :::", response);
                        });
                    }
                };




                $scope.cancel = function () {
                    resetForm();
                };

                $scope.getAttributeById = function(id){
                    var i;
                    $scope.selected=true;
                    $scope.addAttributeFlag = false;
                    for(i=0;i<$scope.attributes.length;i++){
                        if($scope.attributes[i].attributeId==id) {
                            $scope.currentAttribute = $scope.attributes[i];
                        }
                    }
                }

                function createNewAttributeObj() {
                    var attributeObj = {};
                    attributeObj.attributeId= null;
                    attributeObj.attributeName = "";
                    attributeObj.attributeType = null;
                    attributeObj.attributeCode = "";
                    attributeObj.attributeShortCode = "";
                    attributeObj.attributeDescription = "";
                    attributeObj.attributeStatus = "ACTIVE"
                    return attributeObj;
                }

                $scope.addAttributeSelected = function() {
                    if($scope.addAttributeFlag == false){
                        $scope.attribute = createNewAttributeObj();
                        $scope.addAttributeFlag = true;

                    }

                }

                $scope.createAttribute = function() {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.attributeManagement.addAttribute,
                        data: $scope.attribute
                    }).then(function (response) {
                        $toastService.create("Attribute Successfully Created");
                        $scope.attributes.push(response.data);
                        if($scope.originalAttributeList[response.data.attributeType] != null && $scope.originalAttributeList[response.data.attributeType].length > 0){
                            $scope.originalAttributeList[response.data.attributeType].push(response.data);
                        } else {
                            $scope.attributeMap[response.data.attributeType] = [];
                            $scope.attributeMap[response.data.attributeType].push(response.data);
                        }

                        $scope.getAttributeById(response.data.attributeId);
                    }, function (response) {
                        alert(response)
                        console.log("Encoutered an error :::", response);
                    });
                }

                $scope.changeStatus = function(currentAttribute, status){
                    var previousStatus = currentAttribute.attributeStatus;
                    currentAttribute.attributeStatus = status;
                    $http({
                        method: 'PUT',
                        url: apiJson.urls.attributeManagement.updateAttribute,
                        data: currentAttribute
                    }).then(function (response) {
                        $toastService.create("Attribute Successfully Updated");
                    }, function (response) {
                        alert(response)
                        currentAttribute.attributeStatus = previousStatus;
                        console.log("Encoutered an error :::", response);
                    });
                }

            }
        ]
    );
