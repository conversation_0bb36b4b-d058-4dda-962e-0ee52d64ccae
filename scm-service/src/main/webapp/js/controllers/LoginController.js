/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */

'use strict';


angular.module('scmApp')
    .controller('loginCtrl', ['$rootScope', 'PrintService','authService','$state','$scope','$stateParams','$location','apiJson','$cookieStore','appUtil','$http','metaDataService','$window','$timeout',
        function ($rootScope,PrintService,authService,$state,$scope,$stateParams,$location, apiJson, $cookieStore, appUtil, $http, metaDataService,$window,$timeout) {

            $scope.init = function(){
                setAppVersion($window.version);
                $scope.showMessage = $stateParams.accessDenied;
                $scope.tagline = "Login";
                $scope.permissions = null;
                $scope.userId = null;
                $scope.passcode=null;
                $scope.unitList = [];
                $scope.getUnitList();
                $scope.fetchUnit($scope.userId);
                $scope.unitSelectOptions = {
                    width: '100%',
                    initSelection: function (element, callback) {
                        callback($(element).data('$ngModelController').$modelValue);
                    }
                };
                $("#userId").focus();
                $scope.changeLoginType("sumo");
                if(true){
                    PrintService.loadQZPrinter();

                }
                if (($cookieStore.get('isQZLoaded') === 'undefined'
                    || !$cookieStore.get('isQZLoaded'))) {
                        $cookieStore.put('isQZLoaded', true);

                }
                $rootScope.regularDayCloseWarning = false;
                $rootScope.showFAWarning = false;
                $scope.showMsg = false;
            };

           $scope.downloadAndRunIt = function(){
                $scope.showMsg = true;
                $timeout(function(){
                    $scope.showMsg = false;
                  },5000)
            }

            function  setAppVersion(version){
                if(version!= undefined && version != ""){
                  version = version.replace("/","");
                }
                $scope.appVersion = version;
                $timeout(function(){
                  $scope.$apply();
                })

            }

            $scope.selectUnit = function (unit) {
              $scope.selectedUnit = unit;
            };

            $scope.changeLoginType = function(type){
                $rootScope.loginType = type;
                appUtil.setLoginType(type);
            };

            $scope.fetchUnit = function(userId){
                if(String(userId).length===6){
                    $http({
                        method: 'POST',
                        url: apiJson.urls.unitManagement.userUnits,
                        data : {
                            "employeeId":Number(userId),
                            "onlyActive":true
                        },
                        showSpinner: false
                    }).then(function success(response) {
                        if (response.data != null) {
                            $scope.unitList = response.data;
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.getUnitList = function(){
                $http({
                    method: 'GET',
                    url: apiJson.urls.unitMetadata.allUnitsList,
                    showSpinner: false
                }).then(function success(response) {
                    if (response.data != null) {
                        appUtil.setUnitList(response.data);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            function getUserObj() {
                var userObj = {
                    userId: $scope.userId,
                    password: $scope.passcode,
                    unitId: 0,
                    terminalId:0,
                    macAddress: $location.search().mac,
                    application: "SERVICE_ORDER"
                };

                if($rootScope.loginType=="sumo"){
                    userObj.unitId = JSON.parse($scope.selectedUnit).id;
                    userObj.application="SCM_SERVICE";
                }
                return userObj;
            }

            // $(document).on("focus", ".select2", function (e) {
            //     if (e.originalEvent) {
            //         var s2element = $(this).siblings("select:enabled");
            //         s2element.select2("open");
            //         // Set focus back to select2 element on closing.
            //         s2element.on("select2:closing", function () {
            //             if (s2element.val()) s2element.select2("focus");
            //         });
            //     }
            // });


            $scope.login = function(){
                $http({
                    method: 'POST',
                    url: apiJson.urls.users.login,
                    data: getUserObj()
                }).then(function success(response) {
                    if (response.data == null || response.data.sessionKeyId == null) {
                        $scope.message = 'Credentials are not correct!';
                        $scope.showMessage = true;
                    } else {
                        $scope.inputLocalStorage(response);
                    }
                }, function error(response) {
                    $scope.message = 'Authentication failed! Please make sure you have access to SUMO';
                    $scope.showMessage = true;
                    console.log("error:" + response);
                });
            };

            $rootScope.switchUnitLogin = function (unitLoginData) {
                $rootScope.resetLocalStorage();
                $http({
                    method: 'POST',
                    url: apiJson.urls.users.switchUnit,
                    data: unitLoginData
                }).then(function success(response) {
                    if (response.data == null || response.data.sessionKeyId == null) {
                        $scope.message = 'Credentials are not correct!';
                        $scope.showMessage = true;
                    } else {
                        $scope.inputLocalStorage(response);
                    }
                }, function error(response) {
                    $scope.message = 'Authentication failed! Please make sure you have access to SUMO';
                    $scope.showMessage = true;
                    console.log("error:" + response);
                });
            }

            $scope.hasAccessToMenu = function (key){
                if($rootScope.aclData!=null){
                    return Object.keys($rootScope.aclData.menu).length > 0 && $rootScope.aclData.menu[key] != undefined;
                }else {
                    return false;
                }
            }

            $scope.inputLocalStorage = function (response) {
                $scope.message = 'Authentication successful. Redirecting...';
                $scope.showMessage = true;
                authService.setAuthorization(response.data.jwtToken);
                $rootScope.aclData = response.data.acl;
                appUtil.setAcl(response.data.acl);
                $rootScope.mappedUnits = response.data.user.units;
                appUtil.setPermissions(response.data.permissions);
                response.data.permissions=null;
                appUtil.setCurrentUser(response.data);
                metaDataService.getBusinessDate();
                if($rootScope.loginType=="sumo"){
                    metaDataService.getUnitData(function(unit){
                        if(unit!=null){
                            appUtil.setUnitData(unit);
                            if(appUtil.isWarehouseOrKitchen(unit)){
                                $http({
                                    method: 'GET',
                                    url: apiJson.urls.stockManagement.checkVarianceAcknowledgement,
                                    params: {
                                        id:unit.id
                                    }
                                }).then(function success(response) {
                                    $rootScope.varianceAcknowledgementWarning = false;
                                    $rootScope.varianceAcknowledgementBlocking = false;
                                    $rootScope.varianceAckWeeklyBlocking = false;
                                    $rootScope.varianceAckWeeklyWarning = false;
                                    if(response.data.varianceWeeklyBlocking){
                                        $rootScope.varianceAckWeeklyBlocking = true;
                                    }
                                    else if(response.data.varianceBlocking){
                                        $rootScope.varianceAcknowledgementBlocking = true;
                                    }
                                    else if(response.data.varianceWeeklyWarning ){
                                        $rootScope.varianceAckWeeklyWarning = true;
                                        $rootScope.varianceBlockDays = response.data.daysLeftToBlockWeekly;
                                    }
                                    else if(response.data.varianceWarning ){
                                        $rootScope.varianceAcknowledgementWarning = true;
                                        $rootScope.varianceBlockDays = response.data.daysLeftToBlock;
                                    }
                                    $http({
                                        method: 'GET',
                                        url: apiJson.urls.warehouseClosing.checkFixedAssetDayClose,
                                        params: {
                                            id:unit.id
                                        }
                                    }).then(function success(response) {
                                        if(response.data.availableAssetsDaily ||
                                            response.data.availableAssetsWeekly ||
                                            response.data.availableAssetsMonthly ){
                                            $rootScope.showFAWarning = true;
                                        }
                                        $http({
                                            method: 'GET',
                                            url: apiJson.urls.warehouseClosing.checkDayCloseThreshold,
                                            params: {
                                                id:unit.id
                                            }
                                        }).then(function success(response) {
                                            if(response.data.warning || response.data.blocking || unit.closure === "PROCESSING" || unit.closure === "INITIATED"){
                                                $rootScope.regularDayCloseWarning = true;
                                                $rootScope.disclaimer = response.data;
                                                //                                        if( unit.id == 26415 || unit.id == 26427 || unit.id == 26414 || unit.id == 26496 || unit.id == 26515 || unit.id == 26506  ){
                                                //                                            $rootScope.showFAWarning = true;
                                                //                                        }
                                                $state.go("dayCloseShutdown");
                                            }else{
//                                        if( unit.id == 26415 || unit.id == 26427 || unit.id == 26414 || unit.id == 26496 || unit.id == 26515 || unit.id == 26506  ){
//                                            $rootScope.showFAWarning = true;
//                                            $state.go("dayCloseShutdown");
//                                        }
                                                if($rootScope.showFAWarning ||
                                                    $rootScope.varianceAcknowledgementWarning ||
                                                    $rootScope.varianceAcknowledgementBlocking ||
                                                    $rootScope.varianceAckWeeklyBlocking ||
                                                    $rootScope.varianceAckWeeklyWarning || unit.closure === "PROCESSING" || unit.closure === "INITIATED"
                                                ){
                                                    $state.go("dayCloseShutdown");
                                                }else{
                                                    $state.go("metadata");
                                                }

                                            }
                                        });
                                    });
                                })

                            } else if(appUtil.isCafe()){

                                $http({
                                    method: 'GET',
                                    url: apiJson.urls.stockManagement.checkVarianceAcknowledgement,
                                    params: {
                                        id:unit.id
                                    }
                                }).then(function success(response) {

                                    $rootScope.varianceAcknowledgementWarning = false;
                                    $rootScope.varianceAcknowledgementBlocking = false;
                                    $rootScope.varianceAckWeeklyBlocking = false;
                                    $rootScope.varianceAckWeeklyWarning = false;

                                    if(response.data.varianceWeeklyBlocking){
                                        $rootScope.varianceAckWeeklyBlocking = true;
                                    }
                                    else if(response.data.varianceBlocking){
                                        $rootScope.varianceAcknowledgementBlocking = true;
                                    }
                                    else if(response.data.varianceWeeklyWarning ){
                                        $rootScope.varianceAckWeeklyWarning = true;
                                        $rootScope.varianceBlockDays = response.data.daysLeftToBlockWeekly;
                                    }
                                    else if(response.data.varianceWarning ){
                                        $rootScope.varianceAcknowledgementWarning = true;
                                        $rootScope.varianceBlockDays = response.data.daysLeftToBlock;
                                    }
                                    $http({
                                        method: 'GET',
                                        url: apiJson.urls.stockManagement.checkFixedAssetDayClose,
                                        params: {
                                            id:unit.id
                                        }
                                    }).then(function success(response) {
                                        if(response.data.availableAssetsWeekly ||
                                            response.data.availableAssetsMonthly || unit.closure === "PROCESSING" || unit.closure === "INITIATED" ){
                                            $rootScope.showFAWarning = true;
                                            $state.go("dayCloseShutdown");
                                        }else{
                                            if($rootScope.varianceAcknowledgementWarning ||
                                                $rootScope.varianceAcknowledgementBlocking ||
                                                $rootScope.varianceAckWeeklyBlocking || 
                                                $rootScope.varianceAckWeeklyWarning || unit.closure === "PROCESSING" || unit.closure === "INITIATED"){
                                                $state.go("dayCloseShutdown");
                                            }else {
                                                $state.go("metadata");
                                            }
                                        }
                                    });
                                });
                             }
                            else {
                                $state.go("metadata");
                            }
                        } else {
                            $scope.message = "Failed to fetch Metadata! Please try again";
                            $scope.showMessage = true;
                        }
                    });
                }else{
                    $state.go('menu');
                }
            }

            $rootScope.resetLocalStorage = function(){
                localStorage.removeItem("scmUnitList");
                localStorage.removeItem("scmPermissions");
                localStorage.removeItem("scmCurrentUser");
                localStorage.removeItem("scmMetadata");
                localStorage.removeItem("unitData");
                localStorage.removeItem("scmProductDetails");
                localStorage.removeItem("menuProductCategories");
                localStorage.removeItem("packagingMap");
                localStorage.removeItem("skuProductMap");
                $cookieStore.remove("scmToken");
            }
        }
    ]
).controller('metadataCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson', 'appUtil', 'metaDataService',
        function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, metaDataService) {

            $scope.initMetaData = function (){
                //getting all the metadata after login
                metaDataService.getSCMMetaData(function(metaData){
                    appUtil.metadata = metaData;

                    metaDataService.getScmProductDetails().then(function(){
                        metaDataService.getSkuProductMap().then(function(){
                            metaDataService.getAllPackagingMappings(function(packagingMappings){
                                appUtil.metadata.packagingMappings =  packagingMappings;
                                $state.go('menu');
                            },true);
                        });
                    });

                    metaDataService.getPackagingMap();
                    metaDataService.getMenuProductCategories();
                    metaDataService.getTaxProfiles();
                    metaDataService.getInventoryLists();
                    metaDataService.getCompanyList();
                    metaDataService.getBrandList();

                    metaDataService.getPurchaseRoles(appUtil.getCurrentUser().userId,function(roles){
                        $rootScope.purchaseRoles = roles;
                    });
                    metaDataService.getUOMDefinitions(function(uoms){
                        appUtil.metadata.uomMetadata = uoms;
                    });
                    metaDataService.getProfileDefinitions(function(profiles) {
                        appUtil.metadata.profileDefinitions = profiles;
                    })
                    metaDataService.getCategories(function(categories){
                        appUtil.metadata.categoryDefinitions =  categories;
                    });
                    metaDataService.getAttrDefinitions(function(attributes){
                        appUtil.metadata.attributeDefinitions = attributes;
                    });
                    metaDataService.getAttributeValues(function(attributes){
                        appUtil.metadata.attributeValues =  attributes;
                    });

                }, true);
            };
        }
    ]
).controller('shutDownCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson', 'appUtil', '$http','$interval',
        function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, $http,$interval,$timeout) {
           $scope.init = function (){
               $scope.unit = appUtil.getUnitData();
                $scope.unitName = $scope.unit.name;
               $scope.stateList = [];
               $scope.validation= {};
               $scope.errorList = [];
               $scope.unitStateStatusLoaderFlag = {};
               $scope.eventId = null;
               getUnitClosureStates($scope.unit.id);
               $scope.isWarehouse = appUtil.isWarehouse();
               $scope.isCafe = appUtil.isCafe();
               $scope.unitId = $scope.unit.id;
               $scope.userId = appUtil.getCurrentUser().userId;
               $scope.varianceWarning =  $rootScope.varianceAcknowledgementWarning;
               $scope.varianceWeeklyWarning =  $rootScope.varianceAckWeeklyWarning;
               $scope.varianceWeeklyBlocking =  $rootScope.varianceAckWeeklyBlocking;
               $scope.varianceBlocking =  $rootScope.varianceAcknowledgementBlocking;
               $scope.regularDayCloseWarning = $rootScope.regularDayCloseWarning;
               $scope.disclaimer = $rootScope.disclaimer;

               var currentTime = new Date();
               $scope.month = currentTime.toLocaleString('default', {month: 'long'});
               $scope.week = currentTime.toLocaleString('default', {weekday: 'long'});

               if ($scope.disclaimer!=undefined && $scope.disclaimer!=null && $scope.disclaimer.blocking) {
                   $scope.isBlocked = true;
               } else {
                   $scope.isBlocked = false;
               }

               if($scope.varianceWarning!= undefined && $scope.varianceWarning!=null && $scope.varianceWarning){
                   $scope.showVarianceDisclaimer = true;
               }
               if($scope.varianceBlocking!=undefined && $scope.varianceBlocking!=null && $scope.varianceBlocking){
                   $scope.showVarianceDisclaimer = true;
               }
               if($scope.varianceWeeklyWarning!=undefined && $scope.varianceWeeklyWarning!=null && $scope.varianceWeeklyWarning){
                   $scope.showVarianceWeeklyDisclaimer = true;
               }
               if($scope.varianceWeeklyBlocking!=undefined && $scope.varianceWeeklyBlocking!=null && $scope.varianceWeeklyBlocking){
                   $scope.showVarianceWeeklyDisclaimer = true;
               }
               $scope.varianceBlockDate = new Date();
               $scope.varianceBlockDate.setDate(currentTime.getDate()+$rootScope.varianceBlockDays) ;
               $scope.varianceBlockDate.setUTCHours(0,0,0,0);
               $scope.CountDownVariance.initializeClock($scope.varianceBlockDate);
               $scope.blockDate = new Date(currentTime.getFullYear(), currentTime.getMonth(), 25, 0);
               // $scope.blockDate =new Date();
               $scope.noOfDays = (((5 + 7 - currentTime.getDay()) % 7) || 7);
               $scope.blockDate.setDate(currentTime.getDate()+$scope.noOfDays) ;
               $scope.blockDate.setUTCHours(0,0,0,0);

               $scope.CountDown.initializeClock($scope.blockDate);
               $scope.assets = [];
               $scope.pendingDayClose = false;
               $scope.getAssetsToDayClose($scope.unit);

           }

           function getUnitClosureStates(unitId){
            $http({
                method: 'GET',
                url: apiJson.urls.unitMetadata.unitClosureState,
                params: {unitId:unitId}
            }).then(function success(response) {
                if (response.data.length>0) {
                    $scope.eventId = response.data[0].requestId;
                    for (var index in response.data) {
                       $scope.stateList.push(response.data[index].unitClosureStateDomain);
                    }
               }
               for(var idx in $scope.stateList){
                $scope.validate($scope.stateList[idx],unitId);
            }
            }, function error(response) {
                console.log("error:" + response);
            });
           }

           $scope.validate = function(state,unitId) {
            
            if($scope.unitStateStatusLoaderFlag[unitId] === null || $scope.unitStateStatusLoaderFlag[unitId] === undefined){
                $scope.unitStateStatusLoaderFlag[unitId] = {}
             }

            $scope.unitStateStatusLoaderFlag[unitId][state.stateId] = true;
            
            $http({
                           method: 'GET',
                           url: apiJson.urls.unitMetadata.checkStateSCM,
                           params : {
                                stateId : state.stateId,
                                unitId : unitId
                           }
                       }).then(function success(response) {
                        $scope.unitStateStatusLoaderFlag[unitId][state.stateId] = false;
                        if($scope.validation[unitId] === null || $scope.validation[unitId] === undefined){
                            $scope.validation[unitId] = {}
                         }   
                        if(response.data.length > 0){
                             
                            if(response.data.length===1 && response.data[0].checkPassed === true){
                                $scope.validation[unitId][state.stateId] = true;
                               return;
                             }
                                response.data.forEach(function(e){ $scope.errorList.push(e); })
                                $scope.validation[unitId][state.stateId] = false;
                                $scope.showUnitClosureStateOutput();
                            
                           }else{
                            $scope.unitStateStatusLoaderFlag[unitId][state.stateId] = false;
                            $scope.validation[unitId][state.stateId] = false;
                            alert(response.data.errorMsg);
                        }
                                                  
                       }, function error(response) {
                           alert("error:" + response);
                           $scope.unitStateStatusLoaderFlag[unitId][state.stateId] = false;
                       });
        };

        $scope.sendTaskDoneNotification = function(){

            $http({
                method: 'GET',
                url: apiJson.urls.unitMetadata.sendUnitClosureTaskNotification,
                params: {eventId:$scope.eventId}
            }).then(function success(response) {
                if(response.status === 200){
                    alert("Notification sent successfully !");
                }else{
                    alert("Not able to send notification, Something went wrong !");
                }
            }, function error(response) {
                console.log("error:" + response);
            });

        }

        $scope.showUnitClosureStateOutput = function(){
            if($scope.errorList.length > 0){
                $scope.gridOptions = { 
                    enableGridMenu: true,
                    exporterExcelFilename: 'download.xlsx',
                    exporterExcelSheetName: 'Sheet1',
                    enableColumnMenus: true,
                    saveFocus: false,
                    enableRowSelection: true,
                    saveScroll: true,
                    enableSelectAll: true,
                    multiSelect: true,
                    enableColumnResizing: true,
                    exporterMenuPdf : false,
                    exporterMenuExcel : true,
                    fastWatch: true,
                    data : $scope.errorList,
                    columnDefs : $scope.showStateError(),
                };
               }
        };

        $scope.showStateError = function () {
            return [
                {
                    field: 'taskId',
                    name: 'taskId',
                    enableCellEdit: false,
                    displayName: 'Task Id'
                }, 
                , {
                    field: 'keyType',
                    name: 'keyType',
                    enableCellEdit: false,
                    displayName: 'Key Type'
                }, {
                    field: 'keyValue',
                    name: 'keyValue',
                    enableCellEdit: false,
                    displayName: 'Key Value',
                }, {
                    field: 'currentKeyStatus',
                    name: 'currentKeyStatus',
                    enableCellEdit: false,
                    displayName: 'Current Status',
                },
                {
                    field: 'initiatedBy',
                    name: 'initiatedBy',
                    enableCellEdit: false,
                    displayName: 'Initiated By'
                },    
            ];
        };

            $rootScope.getAssetsToDayClose = function(unit) {
                var URL="";
                if($scope.isWarehouse == true){
                    URL  = apiJson.urls.warehouseClosing.checkFixedAssetDayClose
                }else if($scope.isCafe == true){
                    URL  = apiJson.urls.stockManagement.checkFixedAssetDayClose
                }else{
                    return;
                }
                $http({
                    method: 'GET',
                    url: URL,
                    params: {
                        id:unit.id
                    }
                }).then(function success(response) {
                    $scope.assetsDaily = response.data.availableAssetsDaily;
                    $scope.assetsWeekly = response.data.availableAssetsWeekly;
                    $scope.assetsMonthly = response.data.availableAssetsMonthly;
                    $scope.blockDaily = response.data.blockDaily;
                    $scope.blockWeekly = response.data.blockWeekly;
                    $scope.blockMonthly = response.data.blockMonthly;
//                    $scope.dayDiff = response.data.daysDiff;
                    if(response.data.blockDaily || response.data.blockWeekly || response.data.blockMonthly){
                        $scope.pendingDayClose = true;
                        $scope.lastDailyDayCloseDate = appUtil.formatDate(response.data.lastDailyDayClose , "dd-MM-yyyy");
                         $scope.lastWeeklyDayCloseDate = appUtil.formatDate(response.data.lastWeeklyDayClose , "dd-MM-yyyy");
                         $scope.lastMonthlyDayCloseDate = appUtil.formatDate(response.data.lastMonthlyDayClose , "dd-MM-yyyy");
//                        if(response.data.availableAssetsDaily == null){
//                            $scope.pendingDayClose = false;
//                        }
                    }
                    var today = new Date();
                    if(today.getDay() == 0 || today.getDay() == 6){
                        $scope.pendingDayClose = false;
                    }
                });
            }

            appUtil.getUnitList().forEach(function (unit) {
                if(unit.id==appUtil.getCurrentUser().unitId){
                    $scope.unitName = unit.name;
                }
            });

            $scope.CountDown = {
                getTimeRemaining: function(endtime) {
                    var t = Date.parse(endtime) - Date.parse(new Date());
                    var seconds = Math.floor((t / 1000) % 60);
                    var minutes = Math.floor((t / 1000 / 60) % 60);
                    var hours = Math.floor((t / (1000 * 60 * 60)) % 24);
                    var days = Math.floor(t / (1000 * 60 * 60 * 24));
                    return {
                        'total': t,
                        'days': days,
                        'hours': hours,
                        'minutes': minutes,
                        'seconds': seconds
                    };
                },

                initializeClock: function(endtime) {
                    function updateClock() {
                        var t = $scope.CountDown.getTimeRemaining(endtime);

                        $scope.CountDown.days = t.days;
                        $scope.CountDown.hours = ('0' + t.hours).slice(-2);
                        $scope.CountDown.minutes = ('0' + t.minutes).slice(-2);
                        $scope.CountDown.seconds = ('0' + t.seconds).slice(-2);

                        if (t.total <= 0) {
                            $interval.cancel(timeinterval);
                        }
                    }
                    updateClock();
                    var timeinterval = $interval(updateClock, 1000);
                }
            }

            $scope.CountDownVariance = {
                getTimeRemaining: function(endtime) {
                    var t = Date.parse(endtime) - Date.parse(new Date());
                    var seconds = Math.floor((t / 1000) % 60);
                    var minutes = Math.floor((t / 1000 / 60) % 60);
                    var hours = Math.floor((t / (1000 * 60 * 60)) % 24);
                    var days = Math.floor(t / (1000 * 60 * 60 * 24));
                    return {
                        'total': t,
                        'days': days,
                        'hours': hours,
                        'minutes': minutes,
                        'seconds': seconds
                    };
                },

                initializeClock: function(endtime) {
                    function updateClock() {
                        var t = $scope.CountDownVariance.getTimeRemaining(endtime);

                        $scope.CountDownVariance.days = t.days;
                        $scope.CountDownVariance.hours = ('0' + t.hours).slice(-2);
                        $scope.CountDownVariance.minutes = ('0' + t.minutes).slice(-2);
                        $scope.CountDownVariance.seconds = ('0' + t.seconds).slice(-2);

                        if (t.total <= 0) {
                            $interval.cancel(timeinterval);
                        }
                    }
                    updateClock();
                    var timeinterval = $interval(updateClock, 1000);
                }

            }

            $scope.hasAccessToMenu = function (key){
                if($rootScope.aclData!=null){
                    return Object.keys($rootScope.aclData.menu).length > 0 && $rootScope.aclData.menu[key] != undefined;
                }else {
                    return false;
                }
            }

            $scope.goToHome = function (){
                if($scope.hasAccessToMenu('SSG')){
                    $state.go('menu.b2bOutWardRegister')
                }else{
                    $state.go("metadata");
                }
            }

        }
    ]
);
