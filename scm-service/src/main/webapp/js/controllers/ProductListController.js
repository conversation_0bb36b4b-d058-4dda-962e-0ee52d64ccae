/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('productListCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson',
            'appUtil', '$http', '$stateParams', 'productService', '$alertService', 'Popeye', '$fileUploadService', 
            '$toastService', 'metaDataService', 'ScmApiService', 'toast',
            function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, $http, $stateParams, 
                productService, $alertService, Popeye, $fileUploadService, $toastService, metaDataService, ScmApiService, toast) {

                $scope.init = function () {
                    $scope.loginType = $rootScope.loginType == "sumo" ? "GOODS" : "SERVICE";
                    $scope.getListMap()
                    $scope.products = {};
                    $scope.productDetail = {};
                    $scope.productDetail.category = {};
                    $scope.productDetail.subCategory = {};
                    $scope.stateProductId = $stateParams.product;
                    $scope.productBaseUrl = apiJson.productBaseUrl;
                    $scope.productSelectOptions = {
                        width: '100%',
                        initSelection: function (element, callback) {
                            callback($(element).data('$ngModelController').$modelValue);
                        }
                    };
                    var metaData = appUtil.getMetadata();
                    $scope.subCategories = metaData.subCategoryDefinitions;
                    $scope.profileDefinitions = metaData.profileDefinitions;

                    $scope.searchText = "";
                    $scope.filteredProducts = [];
                    $scope.allProducts = [];

                    // productService.getAllProducts(function (products) {
                    //     $scope.products = products;
                    //     $scope.selectProduct($scope.stateProductId);
                    // });
                    getAllProductsBasicdetails();
                };

                function getAllProductsBasicdetails() {
                    ScmApiService
                    .get(apiJson.urls.productManagement.getProducts)
                    .then(function (responseData) {
                        if(!appUtil.checkEmpty(responseData) && !appUtil.checkEmpty(responseData.data)) {
                            $scope.products = Object.values(responseData.data);
                            $scope.allProducts = $scope.products;
                            if($scope.stateProductId != null) {
                                $scope.selectProduct(null, $scope.stateProductId);
                            }
                        } else {
                            toast.warning("Products not found");
                        }
                    })
                }

                $scope.filterProducts = function () {
                    var query = $scope.searchText.toLowerCase();
                    $scope.filteredProducts = $scope.products.filter(function (product) {
                        return product.name.toLowerCase().indexOf(query) !== -1;
                    });
                };

                $scope.getListMap = function () {
                    console.log("searching response")
                    $http({
                        method: 'GET',
                        url: apiJson.urls.serviceOrderManagement.getListData + "?baseType=" + $scope.loginType,
                    }).then(function success(response) {
                        if (response.data != null) {
                            console.log("response is ", response.data)
                            var map = response.data;
                            $scope.departments = map["Department"];
                            $scope.divisions = map["Division"];
                            $scope.classifications = map["Classification"];
                            $scope.subClassifications = [];
                            $scope.classifications.map(function (classification) {
                                if (classification.listType != null) {
                                    classification.listType.map(function (sub) {
                                        $scope.subClassifications.push(sub)
                                    })

                                }

                            })
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    })
                }

                $scope.showTaxDetail = function (code) {
                    showTaxCategoryDetail(code);
                };

                $scope.showBrandDetails = function(brandId) {
                    $http({
                        method: "GET",
                        dataType: 'json',
                        params: {brandId: brandId},
                        headers: {
                            "Content-Type": "application/json"
                        },
                    url: apiJson.urls.scmMetadata.getAllBrands
                    }).then(function success(response) {
                        if(response.data == null || response.data.length == 0) {
                            $toastService.create("Brand Details not found");
                            return;
                        }
                        var brand = response.data[0];
                        $alertService.alert("Brand details for brand Id : " + brandId, "<b>Brand Name:<b> " + brand.brandName + "<br><b>Brand Status: </b>" + brand.status + "<br><b>Brand TagLine: </b>" + brand.tagLine);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
    
                $scope.showCompanyDetails = function(companyId) {
                    $http({
                        method: "GET",
                        dataType: 'json',
                        params: {companyId: companyId},
                        headers: {
                            "Content-Type": "application/json"
                        },
                    url: apiJson.urls.scmMetadata.getAllCompanies
                    }).then(function success(response) {
                        if(response.data == null || response.data.length == 0) {
                            $toastService.create("Company Details not found");
                            return;
                        }
                        var company = response.data[0];
                        $alertService.alert("Company details for comapny Id : " + companyId, "<b>Company Name:<b> " + company.name);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }

                function showTaxCategoryDetail(code) {
                    var data = appUtil.getTaxProfile(code);
                    if (data == undefined || data == null) {
                        return;
                    }
                    metaDataService.getTaxHsnCodes(data.id, 1, 1).then(function (codes) {
                        if (codes) {
                            $alertService.alert('Tax Category Info for ' + data.code, '<b>Code :</b> ' + data.code + '<br> <b>CGST :</b> ' + codes[0].cgst + '<b>%</b><br><b>SGST :</b> ' + codes[0].sgst + '<b>%</b><br> <b>IGST :</b> ' + codes[0].igst + '<b>%</b><br>  <b>Description :</b> ' + data.desc + '<br> <b>Internal Description :</b> ' + data.intDesc);
                        }
                        else {
                            $alertService.alert('Tax Category Info for ' + data.code, '<b>Code :</b> ' + data.code + '<br> <b>Description :</b> ' + data.desc + '<br> <b>Internal Description :</b> ' + data.intDesc);
                        }
                    });

                }


                $scope.validateForDeactivation = function (productId, status){
                    $http({
                        method: 'GET',
                        url: apiJson.urls.scmMetadata.validateForDeactivation,
                        params: { id : productId,
                            type : "PRODUCT"}
                    }).then(function success(response) {
                        console.log("Got response ::::", response.data);
                        if(response.data.canBeDeActivated === true){
                            $scope.changeStatus(productId,status);
                        }else{
                            var msg = response.data.message;
                            $alertService.alert("Can't Deactivate" , msg , false);
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }


                $scope.changeStatus = function (productId, status) {
                    $alertService.confirm("Are you sure?", "You are going to change the status of this product", function (result) {
                        if (result) {
                            var url;
                            if (status == "IN_ACTIVE") {
                                url = apiJson.urls.productManagement.deactivateProduct;
                            } else if (status == "ACTIVE") {
                                url = apiJson.urls.productManagement.activateProduct;
                            } else if (status == "ARCHIVED") {
                                url = apiJson.urls.productManagement.archiveProduct;
                            }
                            $http({
                                method: 'PUT',
                                url: url,
                                data: productId
                            }).then(function success(response) {
                                console.log("Got response ::::", response.data);
                                if (response != undefined && response.data) {
                                    if (response.data) {
                                        $scope.productDetail.productStatus = status;
                                        if (status == "ARCHIVED") {
                                            appUtil.removeProductFromCache($scope.productDetail);
                                            $scope.init();
                                        } else {
                                            appUtil.updateProductInCache($scope.productDetail);
                                        }
                                    }
                                }
                            }, function error(response) {
                                $alertService.alert("Attention", response.data.errorMessage);
                            });
                        }
                    });
                };

                $scope.editProduct = function (productDetail) {
                    $state.go('menu.addProduct', {productDef: angular.copy(productDetail)});
                };

                $scope.selectProduct = function (product, productId) {
                    $scope.productId = productId;
                    $scope.searchText = product != null ? product.name : "";
                    $scope.filteredProducts = [];
                    getProductDetail(productId);
                };

                $scope.refreshCache = function () {
                    $http({
                        url: apiJson.urls.scmCacheManagement.scmCacheRefresh,
                        method: 'GET',
                    }).then(function (response) {
                        if (response.data) {
                            $toastService.create("Cache Refreshed Successfully");
                            $scope.init();
                        }
                    }, function (err) {
                        console.log("Encountered error at backend", err);
                    });
                }

                $scope.getSubCategory = function () {
                    var ret = null;
                    $scope.productDetail.category.subCategories.forEach(function (subCat) {
                        if (subCat.subCategoryId == $scope.productDetail.subCategoryDefinitionId) {
                            ret = subCat;
                        }
                    });
                    return ret;
                };

                $scope.getFulfillmentString = function (fulfillmentType) {
                    return fulfillmentType;
                };

                function getProductDetail(productId) {

                    ScmApiService
                        .get(apiJson.urls.productManagement.productDetail, {productId: productId})
                        .then(function (data) {
                            if(appUtil.checkEmpty(data)){
                                toast.warning("Product not found");
                                return null;
                            }
                            setListDetail(data);
                            $scope.productDetail = data;
                        }
                    );

                    // for (var index in products) {
                    //     if (products[index].id == productId) {
                    //         setListDetail(products[index]);
                    //         console.log("product",products[index]);
                    //         return products[index];
                    //     }
                    // }
                    return null;
                }

                function getSubCategory(product){
                    var result = null;
                    $scope.subCategories.forEach(function (subCategory){
                            if(subCategory.subCategoryId === product.subCategoryDefinition.id){
                                result = subCategory;
                                return;
                            }
                    });
                    return result;
                }

                function setListDetail(product) {
                    console.log(product)
                    console.log($scope.classifications)
                    console.log($scope.divisions)
                    console.log($scope.departments)
                    console.log($scope.subClassifications)
                    var subCategory = getSubCategory(product);
                    if(product.shelfLifeInDays != -1 && !appUtil.isEmptyObject(subCategory.shelfLifeRange)){
                        product.minRange = subCategory.shelfLifeRange.split(",")[0];
                        product.maxRange = subCategory.shelfLifeRange.split(",")[1];
                    }else{
                        product.minRange = null;
                        product.maxRange = null;

                    }

                    if (product.classificationId != null) {
                        $scope.classifications.map(function (classification) {
                            if (product.classificationId == classification.listDetailId) {
                                product.classificationName = classification.name;
                            }
                        })
                    }

                    if (product.divisionId != null) {
                        $scope.divisions.map(function (division) {
                            if (product.divisionId == division.listDetailId) {
                                product.divisionName = division.name;
                            }
                        })
                    }

                    if (product.departmentId != null) {
                        $scope.departments.map(function (department) {
                            if (product.departmentId == department.listDetailId) {
                                product.departmentName = department.name;
                            }
                        })
                    }

                    if (product.subClassificationId != null) {
                        $scope.subClassifications.map(function (subClassification) {
                            if (product.subClassificationId == subClassification.listTypeId) {
                                product.subClassificationName = subClassification.name;
                            }
                        })
                    }

                }

                $scope.viewDerivedMappings = function (productDetail) {
                    if (!appUtil.isEmptyObject(productDetail.derivedMappings)) {
                        var mappingModal = Popeye.openModal({
                            templateUrl: "viewDerivedMapping.html",
                            controller: "viewMappingCtrl",
                            resolve: {
                                existingMappings: function () {
                                    return productDetail.derivedMappings;
                                }
                            },
                            click: false,
                            keyboard: false
                        });
                    }
                };

                $scope.setImage = function (productDetail) {
                    if (!appUtil.isEmptyObject(productDetail)) {
                        var productImageModal = Popeye.openModal({
                            templateUrl: "productImageModal.html",
                            controller: "productImageModalCtrl",
                            resolve: {
                                productDetail: function () {
                                    return productDetail;
                                }
                            },
                            click: false,
                            keyboard: false
                        });
                    }
                    productImageModal.closed.then(function (option) {
                        if (option == "FILE") {
                            $scope.setImageViaUploadDoc(productDetail.productId);
                        }
                        if (option == "SNAP") {

                        }
                    });
                };

                $scope.getProfileName = function (profileId) {

                    for (var index in $scope.profileDefinitions) {
                        if ($scope.profileDefinitions[index].profileId == profileId) {
                            return $scope.profileDefinitions[index].profileName;
                        }
                    }
                }

                $scope.setImageViaUploadDoc = function (productId) {
                    $fileUploadService.openFileModal("Upload Product Image", "Find", function (file) {
                        if (file == null) {
                            $toastService.create('File cannot be empty');
                            return;
                        }
                        if (file.size > 204800) {
                            $toastService.create('File size should not be greater than 2 Mb');
                            return;
                        }
                        var fileExt = metaDataService.getFileExtension(file.name);
                        if (appUtil.isImage(fileExt.toLowerCase())) {
                            var mimeType = fileExt.toUpperCase();
                            var fd = new FormData();
                            fd.append('mimeType', fileExt.toUpperCase());
                            fd.append('productId', productId);
                            fd.append('file', file);
                            $http({
                                url: apiJson.urls.productManagement.uploadProductImage,
                                method: 'POST',
                                data: fd,
                                headers: {'Content-Type': undefined},
                                transformRequest: angular.identity
                            }).success(function (response) {
                                $rootScope.showFullScreenLoader = false;
                                if (response != null) {
                                    $scope.productDetail.productImage = response;
                                    appUtil.updateProductInCache($scope.productDetail);
                                    $toastService.create("Upload successful");
                                } else {
                                    $toastService.create("Upload failed");
                                }
                            }).error(function (response) {
                                $rootScope.showFullScreenLoader = false;
                                $toastService.create("Upload failed");
                            });
                        } else {
                            $toastService.create('Upload Failed , File Format not Supported');
                        }
                    });
                }

            }
        ]
    ).controller('viewMappingCtrl', ['$rootScope', '$scope', 'appUtil', '$toastService', 'metaDataService', 'existingMappings',
        function ($rootScope, $scope, appUtil, $toastService, metaDataService, existingMappings) {
            $scope.initMappings = function () {
                $scope.units = [];
                metaDataService.getUnitList(function (units) {
                    units.forEach(function (unit) {
                        $scope.units[unit.id] = unit;
                    });
                    $scope.mappings = existingMappings;
                });
            };
        }
    ]
).controller('productImageModalCtrl', ['$rootScope', '$scope', 'productDetail', 'Popeye',
        function ($rootScope, $scope, productDetail, Popeye) {
            $scope.productDetail = productDetail;

            $scope.takeSnapShot = function () {
                Popeye.closeCurrentModal("SNAP");
            };

            $scope.uploadFile = function () {
                Popeye.closeCurrentModal("FILE");
            };
        }
    ]
);
