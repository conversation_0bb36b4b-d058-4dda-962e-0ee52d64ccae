'use strict';

angular.module('scmApp')
    .controller('orderingScheduleCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'recipeService', 'metaDataService', 'previewModalService', '$alertService','Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $state, recipeService, metaDataService, previewModalService, $alertService, Popeye) {
                $scope.init = function (){
                   $scope.currentUnit = appUtil.getUnitData();
                   $scope.brandDetails=[{id: 1 ,brandName: "Chaayos" },
                        {id: 3 ,brandName: "Ghee and Turmeric" }, {id: 4 , brandName: "WareHouse"}];
                   $scope.whBrandDetails = [{id: 4 , brandName: "WareHouse"}];
                   $scope.days = ["MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY","SUNDAY"];
                   $scope.unitOrderingSchedule = [];
                   $scope.fountain9UnitsList = [];
                    $scope.dayWiseFunctionalStatus = {};
                   $scope.editMode = false;
                   $scope.unitSelected = null;
                    $scope.notFountain9Unit = false;
                   $scope.isKitchenOrWH = appUtil.isWarehouseOrKitchen($scope.currentUnit);
                    $scope.getFountain9Units();
                    $scope.stockInDays = {
                        1:[],
                        3:[],
                        4:[]
                    };
                };

                $scope.getFountain9Units = function () {
                    if ($scope.isKitchenOrWH) {
                        $("#addSchedule").hide();
                        $("#viewOrderingSchedules").hide();
                    }
                    $http({
                        method: "GET",
                        url: apiJson.urls.referenceOrderManagement.getFountain9Units,
                        params : {
                            "unitId" : appUtil.getCurrentUser().unitId,
                            "isForceLookUp" : !$scope.isKitchenOrWH
                        }
                    }).then(function success(response) {
                        if (response.data != null && response.data.length > 0) {
                            $scope.fountain9UnitsList = getUnistData(response.data);
                            if (!$scope.isKitchenOrWH) {
                                if (response.data.indexOf($scope.currentUnit.id) != -1) {
                                    $scope.getUnitOrderingSchedule();
                                } else {
                                    $scope.notFountain9Unit = true;
                                    $("#viewOrderingSchedules").hide();
                                }
                            }
                        } else {
                            $toastService.create("No Units Found..!");
                            $scope.fountain9UnitsList = [];
                            // $("#viewOrderingSchedules").hide();
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                function getUnistData(response) {
                    var allUnits = appUtil.getUnitList();
                    var result = [];
                    angular.forEach(allUnits,function (unit){
                       if (response.indexOf(unit.id) != -1) {
                           result.push(unit);
                       }
                    });
                    return result;
                }

                $scope.setSelectedUnit = function (unit) {
                    $scope.unitSelected = unit;
                    $scope.editMode = false;
                    $scope.getUnitOrderingSchedule();
                };

                $scope.getUnitOrderingSchedule = function () {
                    console.log("unit sel is ",$scope.unitSelected);
                    $http({
                        method: "GET",
                        url: apiJson.urls.referenceOrderManagement.getUnitOrderingSchedule,
                        params: {
                            "unitId":$scope.isKitchenOrWH ? $scope.unitSelected .id : $scope.currentUnit.id
                        }
                    }).then(function success(response) {
                        if (response.data != null && response.data.length > 0) {
                            $scope.unitOrderingSchedule = response.data;
                            $scope.displayOrderingSchedules = makeDisplayData(response.data);
                            $("#viewOrderingSchedules").show();
                        } else {
                            $toastService.create("No Ordering Schedule Found ..Please add an Ordering Schedule..!");
                            $scope.unitOrderingSchedule = [];
                            $scope.displayOrderingSchedules = [];
                            $("#viewOrderingSchedules").hide();
                            if ($scope.isKitchenOrWH) {
                                $("#addSchedule").show();
                            }
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                function makeDisplayData(data) {
                    var isWhScheduleAvailable = false;
                    angular.forEach(data,function (obj) {
                       var brandName;
                       angular.forEach($scope.brandDetails,function (brand) {
                            if (obj.brandId == brand.id) {
                                brandName = brand.brandName;
                            }
                       });
                       obj.brandName = brandName;
                       if (obj.brandName == "WareHouse") {
                           isWhScheduleAvailable = true;
                       }
                       angular.forEach(obj.unitOrderSchedules,function (schedule) {
                            if (schedule.orderingDays != null) {
                                schedule.orderingDays = schedule.orderingDays.toString();
                            }
                       });
                        setFunctionalSchedule(obj);
                    });
                    if (!isWhScheduleAvailable) {
                        getIsFunctionalForWhType();
                        data.push(createOrderingSchedules(false)[0]);
                    }
                    return data;
                }

                $scope.editSchedule = function () {
                    $scope.editMode = true;
                };

                $scope.closeEditSchedule = function () {
                      $scope.init();
                };

                $scope.addOrderingSchedule = function () {
                    $scope.editMode = true;
                    $("#addSchedule").hide();
                    $("#viewOrderingSchedules").show();
                    $scope.orderingSchedules = createOrderingSchedules(true);
                    $scope.displayOrderingSchedules = $scope.orderingSchedules;
                    $scope.stockInDays = {
                        1:[],
                        3:[],
                        4:[]
                    };
                    console.log("ordering is : ",$scope.orderingSchedules);
                };

                function createOrderingSchedules(isCreated) {
                    var schedules = [];
                    var currentUsed = isCreated ? $scope.brandDetails : $scope.whBrandDetails;
                    for (var i=0;i< currentUsed.length ;i++) {
                        var obj = {};
                        obj.unitId = $scope.isKitchenOrWH ? $scope.unitSelected.id :
                            $scope.currentUnit.id;
                        obj.brandId = currentUsed[i].id;
                        obj.brandName = currentUsed[i].brandName;
                        obj.functional = false;
                        obj.manual = "Manual";
                        obj.unitOrderSchedules = isCreated ? createUnitOrderSchedules(true) : createUnitOrderSchedules(false);
                        schedules.push(obj);
                    }
                    return schedules;
                }
                
                function getIsFunctionalForWhType() {
                    $scope.dayWiseFunctionalStatus = {};
                    angular.forEach($scope.unitOrderingSchedule, function (brandEntry) {
                        angular.forEach(brandEntry.unitOrderSchedules, function (schedule) {
                            if ($scope.dayWiseFunctionalStatus[schedule.orderingDayType] != undefined && $scope.dayWiseFunctionalStatus[schedule.orderingDayType] != null) {
                                if (schedule.functional) {
                                    $scope.dayWiseFunctionalStatus[schedule.orderingDayType] = schedule.functional;
                                }
                            }
                            else {
                                $scope.dayWiseFunctionalStatus[schedule.orderingDayType] = schedule.functional;
                            }
                        });
                    });
                }

                function createUnitOrderSchedules(isCreated) {
                    var result = [];
                    for (var j=0;j<$scope.days.length;j++) {
                        var innerObj = {};
                        innerObj.orderingDayType = $scope.days[j];
                        innerObj.functional = isCreated ? false :
                            $scope.dayWiseFunctionalStatus[innerObj.orderingDayType] != undefined && $scope.dayWiseFunctionalStatus[innerObj.orderingDayType] != null ? $scope.dayWiseFunctionalStatus[innerObj.orderingDayType] : false;
                        innerObj.orderingDay = false;
                        innerObj.orderingDays = null;
                        innerObj.inStock = false;
                        result.push(innerObj);
                    }
                    return result;
                }

                $scope.closeAddSchedule = function () {
                    $scope.stockInDays = {
                        1:[],
                        3:[],
                        4:[]
                    };
                    $("#viewOrderingSchedules").hide();
                    $("#addSchedule").show();
                };

                $scope.submitSchedule = function () {
                    var check = validateAllRules($scope.displayOrderingSchedules);
                    if (check) {
                        var submitOrUpdate = $scope.unitOrderingSchedule.length == 0 ? "Submit" : "Update";
                        var message = "Do you want to " + submitOrUpdate + " the Ordering Schedules";
                        var isUpdate = $scope.unitOrderingSchedule.length == 0 ? false : true;
                        $alertService.confirm("Are you sure?", message, function (result) {
                            if (result) {
                                $http({
                                    method: "POST",
                                    url: apiJson.urls.referenceOrderManagement.addUnitOrderingSchedule,
                                    data: $scope.displayOrderingSchedules,
                                    params: {
                                        "userId": appUtil.getCurrentUser().userId,
                                        "isUpdate": isUpdate
                                    }
                                }).then(function (response) {
                                    if (response != null && response.status == 200 && response.data == true) {
                                        if (isUpdate) {
                                            $toastService.create("Order Schedule Updated Successfully");
                                        } else {
                                            $toastService.create("Order Schedule Submitted Successfully");
                                        }
                                        $scope.init();
                                    }
                                }, function (response) {
                                    if (response.data.errorMsg != null) {
                                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                                    } else {
                                        $toastService.create("Error occurred while Submitting Order Schedule..!");
                                    }
                                    $scope.init();
                                });
                            }
                        });
                    }
                };

                function validateAllRules(schedules) {
                    var totalFunctional = 0;
                    var totalOrderingDays = 0;
                    var check = false;
                    for (var j in schedules) {
                        var schedule = schedules[j].unitOrderSchedules;
                        if (schedules[j].functional) {
                            for (var i = 0; i < schedule.length; i++) {
                                if (schedule[i].functional) {
                                    totalFunctional++;
                                    if (schedule[i].orderingDay) {
                                        if (schedule[i].orderingDays == null || schedule[i].orderingDays == "") {
                                            $toastService.create("Please Check the Ordering Day & Days for Brand : " + schedules[j].brandName + " Of Day : " + schedule[i].orderingDayType);
                                            return false;
                                        }
                                        totalOrderingDays = totalOrderingDays + parseInt(schedule[i].orderingDays);
                                    }
                                    if (!schedule[i].inStock) {
                                        check = true;
                                    }
                                }
                                if (check) {
                                    break;
                                }
                            }
                        }
                        if (check) {
                            break;
                        }
                    }
                    if (check || totalFunctional != totalOrderingDays) {
                        $toastService.create("Check the In STOCK correctly ..!");
                        return false;
                    }
                    return true;
                }

                $scope.setFunctionalFlag = function (item,schedule, allSchedules){
                    if (!schedule.functional) {
                        var idx = $scope.days.indexOf(schedule.orderingDayType);
                        var finIdx = (idx + 7 - 3) % 7;
                        var finSchedule = item.unitOrderSchedules[finIdx];
                        if (finSchedule.orderingDay) {
                            var msg = "Marking <b> " + schedule.orderingDayType + "</b> as non functional , marking <b> " + finSchedule.orderingDayType + " as non ordering day ..!";
                            finSchedule.orderingDay = false;
                            var prev = finSchedule.orderingDays;
                            finSchedule.orderingDays = "";
                            console.log("item bef ", item);
                            $scope.setStockDays(item, finSchedule, prev);
                            console.log("item after ", item);
                            $toastService.create(msg);
                        }
                    }
                    var dayType = schedule.orderingDayType;
                    var enableDayForWh = false;
                    for (var a = 0; a < allSchedules.length - 1; a++) {
                        for (var b = 0; b < allSchedules[a].unitOrderSchedules.length; b++) {
                            var currentSchedule = allSchedules[a].unitOrderSchedules[b];
                            if (currentSchedule.orderingDayType === dayType) {
                                if (currentSchedule.functional) {
                                    enableDayForWh = true;
                                    break;
                                }
                            }
                        }
                    }
                    var currentItem = allSchedules[allSchedules.length - 1];
                    var schedules = allSchedules[allSchedules.length - 1].unitOrderSchedules;
                    for (var i = 0; i < schedules.length; i++) {
                        if (schedules[i].orderingDayType === dayType) {
                            schedules[i].functional = enableDayForWh;
                            if (!enableDayForWh) {
                                // schedules[i].orderingDays = null;
                                // schedules[i].orderingDay = false;
                                var idxW = $scope.days.indexOf(schedules[i].orderingDayType);
                                var finIdxW = (idxW + 7 - 3) % 7;
                                var finScheduleW = schedules[finIdxW];
                                if (finScheduleW.orderingDay) {
                                    var msg = "Marking <b>" + schedules[i].orderingDayType + "</b> as non functional , marking <b>" + finScheduleW.orderingDayType + " as non ordering day ..!";
                                    finScheduleW.orderingDay = false;
                                    var prev = finScheduleW.orderingDays;
                                    finScheduleW.orderingDays = "";
                                    console.log("currentItem bef ", currentItem);
                                    $scope.setStockDays(currentItem, finScheduleW, prev);
                                    console.log("currentItem after ", currentItem);
                                    $toastService.create(msg);
                                }
                            }
                            break;
                        }
                    }
                };

                $scope.checkForFulfilmentDate = function (item,schedule) {
                    if (schedule.orderingDay) {
                        var index = $scope.days.indexOf(schedule.orderingDayType);
                        var fulfilmentDayIdx = (index + 3) % 7;
                        var day = $scope.days[fulfilmentDayIdx];
                        var check = false;
                        for (var i = 0; i < item.unitOrderSchedules.length; i++) {
                            if (day == item.unitOrderSchedules[i].orderingDayType && item.unitOrderSchedules[i].functional) {
                                check = true;
                            }
                        }
                        if (!check) {
                            schedule.orderingDay = false;
                            $toastService.create("Fulfilment date " + day + " can not be a non functional date..!");
                            return false;
                        }
                    }
                    else {
                        var prev = schedule.orderingDays;
                        schedule.orderingDays = "";
                        $scope.setStockDays(item,schedule,prev);
                    }
                }
                
                $scope.setStockDays = function (item, schedule,oldDays) {
                    console.log("item is : ",item);
                    if (oldDays != null) {
                        setFunctionalSchedule(item);
                    }
                    else {
                        getFunctionalSchedule(item,schedule);
                        setFunctionalSchedule(item);
                    }
                }

                function getFunctionalSchedule(item,schedule) {
                    var indexOfOrderingDay = ($scope.days.indexOf(schedule.orderingDayType) + 3 ) % 7;
                    var indexes = getIndexes(indexOfOrderingDay,item.brandId == 1 ? 3 : item.brandId == 4 ? 7 : 2);
                    var scheduledDays = [];
                    var check = false;
                    for (var j in indexes) {
                        var current = item.unitOrderSchedules[indexes[j]];
                        console.log("current is : ",current);
                        if (current.inStock == undefined) {
                            current.inStock = false;
                        }
                        if (current.functional && scheduledDays.length < schedule.orderingDays) {
                            if ($scope.stockInDays[item.brandId].indexOf(current.orderingDayType) == -1) {
                                scheduledDays.push(current);
                            } else {
                                check = true;
                            }
                        }
                    }
                    if (check) {
                        $toastService.create("Please check the IN STOCK flag ..it is repeating..!");
                        schedule.orderingDays = null;
                        return false;
                    }

                    if (scheduledDays.length < schedule.orderingDays) {
                        $toastService.create("Cafe is not functional for "+schedule.orderingDays+" Please mark it functional or reduce the ordering days..!");
                        schedule.orderingDays = null;
                        return false;
                    }
                    else {
                        console.log("days is : ",scheduledDays);
                        angular.forEach(scheduledDays,function (day) {
                            $scope.stockInDays[item.brandId].push(day.orderingDayType);
                            day.inStock = true;
                        });
                    }
                    return true;
                }

                function getIndexes(index,days) {
                    var result = [];
                    for (var i=0;i<days;i++) {
                        var idx = (index + i) % 7;
                        result.push(idx);
                    }
                    return result;
                }

                function setFunctionalSchedule(item) {
                    $scope.stockInDays = {
                        1:[],
                        3:[],
                        4:[]
                    };
                    for (var i=0;i<item.unitOrderSchedules.length;i++) {
                        if ($scope.stockInDays[item.brandId].indexOf(item.unitOrderSchedules[i].orderingDayType) == -1) {
                            item.unitOrderSchedules[i].inStock = false;
                        }
                        if (item.unitOrderSchedules[i].orderingDays != null) {
                            var log = getFunctionalSchedule(item,item.unitOrderSchedules[i]);
                            console.log("item is schedule ",item.unitOrderSchedules[i],log);
                        }
                    }
                }

                $scope.openCloneModal = function () {
                    var cloneModal = Popeye.openModal({
                        templateUrl: "cloneSchedules.html",
                        controller: "cloneSchedulesCtrl",
                        modalClass: 'custom-modal',
                        resolve: {
                            f9Units: function () {
                                return $scope.fountain9UnitsList;
                            }
                        },
                        click: false,
                        keyboard: false
                    });
                    cloneModal.closed.then(function (result) {
                        if (result) {
                           $scope.init();
                        }
                    });
                };
        }]).controller('cloneSchedulesCtrl', ['$scope', 'f9Units','Popeye','$http','apiJson', 'appUtil', '$toastService','$alertService',
    function ($scope, f9Units,Popeye,$http,apiJson, appUtil, $toastService, $alertService) {
        $scope.initCloneCtrl = function () {
            $scope.f9UnitsList = angular.copy(f9Units);
            $scope.unitsWithSchedules = [];
            $scope.unitsWithNoSchedules = [];

            $scope.selectedUnits = [];
            $scope.cloneFromUnit = null;
            $scope.multiSelectSetting = {
                enableSearch: true, template: '<b> {{option.name}}</b>', scrollable: true,
                scrollableHeight: '250px',clearSearchOnClose: true
            };
            getAvailableUnitsForSchedule();
        };

        $scope.close = function (result) {
            Popeye.closeCurrentModal(result);
        };

        $scope.getSelectedUnitNames = function () {
            var result = '';
            for (var i=0;i<$scope.selectedUnits.length;i++) {
                if (i === $scope.selectedUnits.length -1) {
                    result+= $scope.selectedUnits[i].name;
                } else {
                    result+= $scope.selectedUnits[i].name + " , ";
                }
            }
            return result;
        };

        function getAvailableUnitsForSchedule() {
            $http({
                url: apiJson.urls.referenceOrderManagement.availableUnitsForSchedule,
                method: 'GET'
            }).then(function success(response) {
                if (response.data != null && response.status === 200) {
                    var result = response.data;
                     for (var i=0;i< $scope.f9UnitsList.length;i++) {
                         for (var j=0; j< result['unitsWithNoSchedules'].length; j++) {
                             if ($scope.f9UnitsList[i].id === result['unitsWithNoSchedules'][j]) {
                                 $scope.unitsWithNoSchedules.push($scope.f9UnitsList[i]);
                             }
                         }
                         for (var k=0; k< result['unitsWithSchedules'].length; k++) {
                             if ($scope.f9UnitsList[i].id === result['unitsWithSchedules'][k]) {
                                 $scope.unitsWithSchedules.push($scope.f9UnitsList[i]);
                             }
                         }
                    }
                }
            }, function error(response) {
                console.log("error response is : ",response);
                $scope.unitsWithNoSchedules = [];
                $scope.unitsWithSchedules = [];
                $toastService.create("Error Occurred While Checking available Units For Schedule..!");
            });
        }

        function getUnitIdsSelectedForCloning() {
            var result = [];
            angular.forEach($scope.selectedUnits, function (unit) {
                result.push(unit.id);
            });
            return result;
        }

        $scope.submitCloneSchedules = function () {
            if (appUtil.isEmptyObject($scope.cloneFromUnit)) {
                $toastService.create("Please Select the Unit From which you want to clone...!");
                return false;
            }
            if (appUtil.isEmptyObject($scope.selectedUnits)) {
                $toastService.create("Please the units with no schedules to clone..!");
                return false;
            }
            var unitIds = getUnitIdsSelectedForCloning();
            $http({
                url: apiJson.urls.referenceOrderManagement.cloneUnitOrderingSchedule,
                method: 'POST',
                data : {
                    "cloneFromUnit" : $scope.cloneFromUnit.id,
                    "selectedUnitsForCloning" : unitIds
                },
                params : {
                    "userId": appUtil.getCurrentUser().userId
                }
            }).then(function success(response) {
                if (response.data != null && response.status === 200) {
                    if (response.data) {
                        $toastService.create("Cloned Successfully...!");
                        $scope.close(true);
                    } else {
                        $toastService.create("Error Occurred While cloning the Schedule ..! Please Retry..!");
                    }
                }
            }, function error(response) {
                if (response.data.errorMsg != null) {
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    $scope.initCloneCtrl();
                }
                else {
                    $toastService.create("Error Occurred While cloning the Schedule ..! Please Retry..!");
                }
            });
        };

    }]);