/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('specialOrderCreateCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$alertService', '$toastService', '$state', 'recipeService','metaDataService','Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $alertService, $toastService, $state, recipeService, metaDataService,Popeye) {

            $scope.init = function () {
                $scope.isUrgentVendorOrder = false;
                $scope.fulfillmentDate = appUtil.getDate(1);
            	$scope.noOfDays = 1;
                $scope.minDate = appUtil.getDate(0);
                $scope.maxDate = appUtil.getDate(7);
                $scope.clearData();
                $scope.getVendorDetails();
                $scope.timeout = null;
                $scope.comment =null;
            };

            $scope.changeVendor = function(item, vendor){
                item.vendor = vendor;
            };
            $scope.saveComment =function(message){
                $scope.comment =message;
            };

            $scope.clearProductList =function(){
            	$scope.productList.forEach(function(item){
            		item.requestedQuantity = 0;
            		item.requestedAbsoluteQuantity = 0;
            		item.vendor = null;
            	});
            };
            $scope.clearData = function(){
            	 $scope.productList = [];
                 $scope.selectedVendorId = [];
            };
            $scope.isSpecialized = function (isUrgentVendorOrder) {
                $scope.getEstimateQty(isUrgentVendorOrder);
            };
            $scope.getEstimateQty = function (isUrgentVendorOrder) {
                if(!isUrgentVendorOrder){
                    $scope.isUrgentVendorOrder = false;
                    $scope.maxDate = appUtil.getDate(7);
                }
                if(isUrgentVendorOrder){
                    $scope.maxDate = appUtil.getDate(0);
                    $scope.fulfillmentDate = appUtil.getDate(0);
                }
                if ($scope.fulfillmentDate == null) {
                    $toastService.create("Please select fulfillment date!");
                } else {
                    $scope.getUnitSkuMappings();
                	$scope.clearData();
                    var inputData = {
                        unitId: appUtil.getUnitData().id,
                        fulfillmentDate: new Date($scope.fulfillmentDate),
                        noOfDays: $scope.noOfDays,
                        categoryList: recipeService.createMenuItemCategories()
                    };
                    $http({
                        method: "POST",
                        url: apiJson.urls.referenceOrderManagement.referenceOrderEstimates,
                        data: inputData
                    }).then(function success(response) {
                        if (response.data != null) {
                        	console.log('Estimates', response.data);
                            $toastService.create("Special order estimates calculated successfully!");
                            var categoryList = response.data.categoryList;
                            metaDataService.getUnitProductData(function(unit){
                                recipeService.getProductsFromCategoryList(unit,categoryList, function (itemList) {
                                    var productPackaging = [];
                                    metaDataService.getAllPackagingMappings(function (packagingMap) {
                                        productPackaging = packagingMap;
                                    },false);
                                    var packagingDef = appUtil.getPackagingMap();
                                    var specialOrderingProducts = [];
                                    appUtil.getActiveScmProducts().forEach(function (product) {
                                        if(product.supportsSpecialOrdering && (product.productStatus == "ACTIVE")) {
                                            if((isUrgentVendorOrder && [100234, 100246, 100259, 100776, 100856, 101303, 103083, 103188, 103306, 103307, 103345,
                                                100811, 101302, 100145].includes(product.productId) )|| !isUrgentVendorOrder){
                                                if((isUrgentVendorOrder && (!appUtil.isEmptyObject($scope.vendorList[product.productId]) && ($scope.vendorList[product.productId].length>1 || $scope.vendorList[product.productId][0].entityName!=='Cash purchase')))|| !isUrgentVendorOrder){
                                                    var obj = {
                                                        id: null,
                                                        productId: product.productId,
                                                        productName: product.productName,
                                                        packagingName: null,
                                                        packagingQuantity: null,
                                                        conversionRatio: null,
                                                        requestedQuantity: 0,
                                                        requestedAbsoluteQuantity: 0,
                                                        transferredQuantity: null,
                                                        receivedQuantity: null,
                                                        unitOfMeasure: product.unitOfMeasure,
                                                        packagingId: null
                                                    };
                                                    if (productPackaging[product.productId] != null && productPackaging[product.productId] != undefined) {
                                                        productPackaging[product.productId].forEach(function (pack) {
                                                            if (pack.mappingStatus == 'ACTIVE' && pack.isDefault) {
                                                                obj.packagingName = packagingDef[pack.packagingId].packagingName;
                                                                obj.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                                                                obj.packagingId = pack.packagingId;
                                                            }
                                                        });
                                                    }
                                                    if($scope.unitSkuProductPackagingMap[product.productId] !=null && $scope.unitSkuProductPackagingMap[product.productId]!= undefined) {
                                                        obj.packagingName = packagingDef[$scope.unitSkuProductPackagingMap[product.productId]].packagingName;
                                                        obj.conversionRatio = packagingDef[$scope.unitSkuProductPackagingMap[product.productId]].conversionRatio;
                                                        obj.packagingId = $scope.unitSkuProductPackagingMap[product.productId];
                                                    }
                                                    specialOrderingProducts.push(obj);
                                                }

                                            }
                                        }
                                    });
                                    specialOrderingProducts.forEach(function (prod) {
                                        itemList.forEach(function (item) {
                                            if(prod.productId==item.id){
                                                var qty = parseFloat(isNaN(item.suggestedQuantity) ? 0 : item.suggestedQuantity);
                                                prod.packagingQuantity = Math.ceil(qty/prod.conversionRatio);
                                                prod.requestedQuantity = prod.packagingQuantity*prod.conversionRatio;
                                                prod.requestedAbsoluteQuantity = prod.packagingQuantity*prod.conversionRatio;
                                            }
                                        });
                                    });
                                    $scope.productList = specialOrderingProducts;
                                    return specialOrderingProducts;
                                });
                            });
                        } else {
                            $toastService.create("Something went wrong. Please try gain!");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            function validateAverageQty(qty,productId){
                var lastWeeekAvg  = $scope.lastWeekAverageMap[productId];
                if(lastWeeekAvg == 0){
                    return true;
                }
                var finalValue  = lastWeeekAvg * 1.5;
                if(qty > finalValue){
                    return false;
                }
                return true;
            }

            $scope.getVendorDetails = function () {
                var specialOrderingProducts = [];
                appUtil.getScmProductDetails().forEach(function (product) {
                    if(product.supportsSpecialOrdering && (product.productStatus === "ACTIVE")){
                        specialOrderingProducts.push(product.productId);
                    }
                });
                $http({
                    method: "POST",
                    url: apiJson.urls.productManagement.getUnitProductVendors,
                    data: {unitId: appUtil.getUnitData().id,productIds: specialOrderingProducts}
                }).then(function success(response) {
                    if (response.data !== null) {
                        $scope.vendorList = response.data;
                        $scope.urgentOrderVendorList ={};
                        getLastWeekAverage(Object.keys($scope.vendorList));
                        Object.keys(response.data).map(function (vendorList){
                            var vendorListSize = vendorList.length;
                            var vList =[];
                            Object.keys(response.data[vendorList]).map(function (vendor){
                                if(vendorListSize ==1 && response.data[vendorList][vendor].entityName=="Cash purchase"){
                                }
                                else if(vendorListSize >1 && response.data[vendorList][vendor].entityName=="Cash purchase"){

                                }
                                else {
                                    vList.push(response.data[vendorList][vendor]);
                                }
                            })
                            if(vList.length>0)
                            $scope.urgentOrderVendorList[vendorList] =vList;
                        });
                    } else {
                    	$alertService.alert("Error in Loading", "Error loading unit product vendors. lease try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            function getLastWeekAverage(productIds){
                $http({
                    method: "POST",
                    url: apiJson.urls.requestOrderManagement.getLastWeekAverageQty,
                    data: productIds,
                    params : {
                        unitId : appUtil.getUnitData().id,
                        isSpecial : true
                    }
                }).then(function success(response) {
                    if (response.data !== null) {
                        $scope.lastWeekAverageMap = response.data;
                        console.log("last week Average :::: ",$scope.lastWeekAverageMap);
                    } else {
                        $alertService.alert("Error in Loading", "Error loading Last Week Qty. lease try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.getUnitSkuMappings = function(){
                $scope.unitSkuProductPackagingMap = [];
                $http({
                    method: "GET",
                    url: apiJson.urls.productManagement.getUnitSkuPackagingMappings,
                    params: {unitId: appUtil.getUnitData().id}
                }).then(function success(response) {
                    if (response.data !== null && response.status == 200) {
                        $scope.unitSkuProductPackagingMap = response.data;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.calculateRequestQty = function (item) {
                if($scope.modalOpened != true){
                    item.packagingQuantity = parseInt(item.packagingQuantity);
                    item.requestedQuantity = item.packagingQuantity*item.conversionRatio;
                    item.requestedAbsoluteQuantity = item.requestedQuantity;
                    if (validateAverageQty(item.requestedQuantity, item.productId) == false) {
                        clearTimeout($scope.timeout);
                        $scope.timeout = setTimeout(function () {
                            $scope.modalOpened = true;
                            openValidateInputModal(item);
                        }, 500);

                    } else {
                        clearTimeout($scope.timeout);
                    }
                }

            };

            function openValidateInputModal(roItem){
                var distanceViewModal = Popeye.openModal({
                    templateUrl: "qtyValidation.html",
                    controller: "qtyValidationCtrl",
                    resolve: {
                        roItem : function (){
                            return roItem;
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });

                distanceViewModal.closed.then(function(data){
                    $scope.modalOpened = false;
                    if(appUtil.isEmptyObject(data)){
                        roItem.packagingQuantity = 0;
                        roItem.requestedQuantity = 0;
                        roItem.requestedAbsoluteQuantity = 0;
                    }
                });
            }

            $scope.createRoObject = function () {
                var items = $scope.getRoItems();
                if ($scope.fulfillmentDate === null) {
                    $toastService.create("Please select fulfillment date!");
                    return false;
                }else if ($scope.invalidItems(items)) {
                    $toastService.create("Please select vendor!");
                    return false;
                } else if (items.length === 0) {
                    $toastService.create("Please select at least a few products to order!");
                    return false;
                }
                var sameVendorForAll = true;
                var vendor = items[0].vendor.entityName;
                items.map(function (item) {
                    console.log(item.vendor.entityName);
                    if (item.vendor.entityName !== vendor) {
                        sameVendorForAll = false;
                    }
                });
                if ($scope.isUrgentVendorOrder && !sameVendorForAll) {
                    $toastService.create("Urgent Orders Can Only be Placed through Single Vendor!");
                    return false;
                } else {
                    $scope.sanitizeVendor(items);
                    var ro = {
                        id: null,
                        generationTime: null,
                        lastUpdateTime: null,
                        specialOrder: true,
                        requestUnit: appUtil.createRequestUnit(),
                        generatedBy: appUtil.createGeneratedBy(),
                        fulfillmentUnit: appUtil.createRequestUnit(),
                        fulfillmentDate: $scope.fulfillmentDate,
                        referenceOrderId: null,
                        status: 'CREATED',
                        comment: $scope.comment,
                        purchaseOrderId: null,
                        transferOrderId: null,
                        goodsReceivedId: null,
                        requestOrderItems: items,
                        specializedUrgentOrder: $scope.isUrgentVendorOrder,
                    };
                    $http({
                        method: "POST",
                        url: apiJson.urls.requestOrderManagement.multipleRequestOrder,
                        data: ro
                    }).then(function success(response) {
                        if (response.data != undefined && response.data != null && response.data.errorMsg == null && response.data.length > 0) {
                            if(response.data[0].urgentSpecializedOrder){
                                $alertService.alert("SUCCESSFUL", "Request Order, Transfer Order and GR for Urgent Order with id " + response.data[0].orderId + " is created successfully!");
                            }else {
                                $alertService.alert(response.data.errorTitle, "Request order with id " + response.data[0].orderId + " created successfully!");
                            }
                            $state.go("menu.reqOrderMgt");
                        } else if (response.data.errorMsg != undefined && response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, "Error in placing Request Order: \n" + response.data.errorMsg);
                        }
                    }, function error(response) {
                        if (response.data.errorMsg != undefined && response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, "Error in placing Request Order: \n" + response.data.errorMsg);
                        }
                        console.log("error:" + response);
                    });
                }
            };

            $scope.invalidItems = function (items) {
                var ret = false;
                items.forEach(function (val) {
                    if (val.vendor == null) {
                        ret = true;
                    }
                });
                return ret;
            };

            $scope.getRoItems = function () {
                var items = [];
                $scope.productList.forEach(function (product) {
                    if (product.requestedQuantity > 0) {
                        items.push(product);
                    }
                });
                return items;
            };

            $scope.sanitizeVendor = function (items) {
                items.forEach(function (item) {
                    if (item.vendor != null) {
                        var id = item.vendor.vendorId;
                        var name = item.vendor.entityName;
                        item.vendor = {
                            id: id,
                            code: null,
                            name: name
                        }
                    }
                })
            };

        }
    ]
).controller('qtyValidationCtrl', ['$scope', 'roItem','appUtil', '$toastService', 'apiJson', '$http', 'Popeye','$window',
        function ($scope, roItem,appUtil, $toastService, apiJson, $http, Popeye,$window) {
            $scope.init = function (){
                $scope.roItem = roItem;
                $scope.roItem.reValidatedPkgQty = 0;
            }

            $scope.submit = function (){
                if($scope.roItem.reValidatedPkgQty != $scope.roItem.packagingQuantity){
                    $scope.roItem.packagingQuantity = 0;
                    $scope.roItem.requestedQuantity = 0;
                    $scope.roItem.requestedAbsoluteQuantity = 0;
                    $toastService.create("Re-Entered Quantity Doesn't Match With Previously Entered Quantity!!!!")
                    $scope.closeModal();
                }
                $scope.closeModal();
            }

            $scope.calculateRequestQty = function (item) {
                item.packagingQuantity = parseInt(item.packagingQuantity);
                item.requestedQuantity = item.reValidatedPkgQty*item.conversionRatio;
                item.requestedAbsoluteQuantity = item.requestedQuantity;
            };


            $scope.closeModal = function () {
                Popeye.closeCurrentModal({success : true});
            };

        }
    ]
);
