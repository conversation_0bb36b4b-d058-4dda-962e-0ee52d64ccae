angular.module('scmApp').controller(
		'createSubClassificationCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
	        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
	        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
	                  previewModalService, Popeye, $timeout, $window) {
					
					$scope.init = function () {
                        $scope.loginType=   $rootScope.loginType=="sumo"?"GOODS":"SERVICE";
						$scope.initialize();

					}
					
					
					$scope.initialize = function () {
						$scope.listTypes = ["Classification"];
						$scope.getListMap();
						var map = null;
						$scope.categories = [];
						$scope.departments = [];
						$scope.divisions = [];
						$scope.categoryData = [];
						$scope.subCategoryData = [];
                        $scope.budgetCategories = [];
                        $scope.getBudgetCategories();
						
					}
					
					$scope.getListMap = function (){
						$http({
							method : 'GET',
							url : apiJson.urls.serviceOrderManagement.getListData+"?baseType="+$scope.loginType,
						}).then(function success(response) {
							if(response.data != null){
								map = response.data;
								/*$scope.departments = map["Department"];
								$scope.divisions = map["Division"];
								$scope.categories = map["Cost Element"];*/
							}
						}, function error(response) {
				               console.log("error:" + response);
				          })
					}


                $scope.getBudgetCategories = function () {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.serviceOrderManagement.budgetCategoryList,
                    }).then(function success(response) {
                        if (response.data != null) {
                            $scope.budgetCategories = response.data;
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    })
                }

					$scope.getListDetails = function(selectedList){
						$scope.categoryData = map[selectedList];
					}
					
					$scope.getSubCategories = function(selectedCategory){
						for(var i = 0 ; i < $scope.categoryData.length ; i++){
							if($scope.categoryData[i].listDetailId == selectedCategory.listDetailId){
								$scope.subCategoryData = $scope.categoryData[i].listType;
							}
						}
					}
					
					$scope.addSubCategoryData = function () {
		                var modalInstances = Popeye.openModal({
		                    ariaLabelledBy: 'modal-title',
		                    ariaDescribedBy: 'modal-body',
		                    templateUrl: 'addSubCategoryModal.html',
		                    controller: 'addSubCategoryModalCtrl',
		                    backdrop: 'static',
		                    keyboard: false,
		                    scope:$scope,
		                    size: 'lg',
		                    resolve: {
		                        items: function () {
		                            return {
		                            	mode: 'add'
		                            }
		                        }
		                    }
		                });
		                modalInstances.closed
		                .then(function (isSuccessful) {
		                    if (isSuccessful) {
		                    	$scope.initialize();
		                    }
		                });
		            };
		            
		            $scope.subCatEditModal = function (listType) {
		                var modalInstances = Popeye.openModal({
		                    ariaLabelledBy: 'modal-title',
		                    ariaDescribedBy: 'modal-body',
		                    templateUrl: 'addSubCategoryModal.html',
		                    controller: 'addSubCategoryModalCtrl',
		                    backdrop: 'static',
		                    keyboard: false,
		                    scope: $scope,
		                    size: 'lg',
		                    resolve: {
		                        items: function () {
		                            return {
		                            	name: listType.name, code: listType.code,
		                            	alias: listType.alias, description: listType.description,
		                            	listDetail: $scope.categorySelected,status: listType.status,
		                            	listTypeId: listType.listTypeId, mode: 'edit',
										budgetCategory:listType.budgetCategory
		                            }
		                        }
		                    }
		                });
		                modalInstances.closed
		                .then(function (isSuccessful) {
		                    if (isSuccessful) {
		                    	$scope.initialize();
		                    }
		                });
		            };
					
					
	                
		}]
).controller('addSubCategoryModalCtrl', ['$scope', 'apiJson', '$http','items',  'appUtil', '$location', '$toastService', 'metaDataService',
    '$fileUploadService', '$alertService', 'previewModalService', 'Popeye','$timeout','$window',
    function ($scope, apiJson, $http, items, appUtil, $location, $toastService, metaDataService,
              $fileUploadService, $alertService, previewModalService, Popeye, $timeout, $window) {

	
	$scope.init = function (){
		$scope.periods = ["ACTIVE","IN_ACTIVE"];
		$scope.listTypeMods = ["Classification"];
		$scope.categories = [];
		
		$scope.name = items.name;
        $scope.code = items.code;
       // $scope.make = items.make == "NA" ? "" : items.make;
        $scope.alias = items.alias;
        $scope.description = items.description;
        $scope.status = items.status;
        $scope.budgetCategory=items.budgetCategory;
        
        if(items.mode == "add"){
       	 $scope.listTypeId = null;
       	$scope.editMode = false;
        }
        else{
       	 $scope.listTypeId = items.listTypeId;
       	$scope.editMode = true;
        }
	}
	
	$scope.getListDetail = function(type){
		$scope.categories = map[type];
	}
	
	$scope.submitSubCat = function(){
		 if($scope.listTypeId == null){
			 $scope.addListType();
		 }
		 else{
			 $scope.updateListType();
		 }
	 }
	
	$scope.updateListType = function(){
		if($scope.name=="" || $scope.name==null){
 			alert("Please input  name.");
 			return;
 		}
     	if($scope.code=="" || $scope.code==null){
 			alert("Please input code.");
 			return;
 		}
     	if($scope.description=="" || $scope.description==null){
  			alert("Please input description.");
  			return;
  		}
      	if($scope.alias=="" || $scope.alias==null){
  			alert("Please input alias.");
  			return;
  		}
    	if($scope.status=="" || $scope.status==null){
 			alert("Please input status.");
 			return;
 		}

        if (($scope.budgetCategory == "" || $scope.budgetCategory == null)) {
                    alert("Please input budgetCategory.");
                    return;
                }
		var reqObj = {
				name: $scope.name,
				code: $scope.code,
				description: $scope.description,
				alias: $scope.alias,
				status: $scope.status,
				listTypeId: $scope.listTypeId,
			    budgetCategory : $scope.budgetCategory
		}
		$http({
			method : 'POST',
			url : apiJson.urls.serviceOrderManagement.updateSubCategoryData,
			data : reqObj
		}).then(function success(response) {
			if(response.data){
				$toastService.create("Sub Category Added Successfully!");
				closeModal(true);
              $scope.listSelected={};
                $("#select2-listSelectId-container").html('');

			}
			else{
				$toastService.create("Error In Saving Sub Category Details!");
			}
		}, function error(response) {
               console.log("error:" + response);
          })
	}
	
	
	$scope.addListType = function(){
		if($scope.type=="" || $scope.type==null){
 			alert("Please input type.");
 			return;
 		}
		if($scope.categorySelect=="" || $scope.categorySelect==null){
 			alert("Please input category.");
 			return;
 		}
		if($scope.name=="" || $scope.name==null){
 			alert("Please input  name.");
 			return;
 		}
     	if($scope.code=="" || $scope.code==null){
 			alert("Please input code.");
 			return;
 		}
     	if($scope.description=="" || $scope.description==null){
  			alert("Please input description.");
  			return;
  		}
      	if($scope.alias=="" || $scope.alias==null){
  			alert("Please input alias.");
  			return;
  		}
    	if($scope.status=="" || $scope.status==null){
 			alert("Please input status.");
 			return;
 		}
        if (($scope.budgetCategory == "" || $scope.budgetCategory == null)) {
            alert("Please input budgetCategory.");
            return;
        }
		var reqObj = {
				name: $scope.name,
				code: $scope.code,
				description: $scope.description,
				alias: $scope.alias,
				status: $scope.status,
				listDetail: $scope.categorySelect,
                budgetCategory : $scope.budgetCategory
		}
		$http({
			method : 'POST',
			url : apiJson.urls.serviceOrderManagement.addSubCategoryData,
			data : reqObj
		}).then(function success(response) {
			if(response.data){
				$toastService.create("Sub Category Added Successfully!");
				closeModal(true);
                $scope.listSelected={};
                $("#select2-listSelectId-container").html('');
			}
			else{
				$toastService.create("Error In Saving Sub Category Details!");
			}
		}, function error(response) {
               console.log("error:" + response);
          })
	}
	
	$scope.cancel=function(){
   	  closeModal(false);
     };
     
     function closeModal(data){
      	  Popeye.closeCurrentModal(data);
        }
        
    }
]
);
