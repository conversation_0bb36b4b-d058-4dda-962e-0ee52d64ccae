angular.module('scmApp').controller(
    'vendorToSkuMappingCtrl',
    [
        '$rootScope',
        '$scope',
        '$interval',
        'apiJson',
        '$http',
        'appUtil',
        '$toastService',
        '$timeout',
        'previewModalService',
        '$state',
        function ($rootScope, $scope, $interval, apiJson, $http, appUtil, $toastService, $timeout, previewModalService, $state) {

            $scope.init = function () {
                // $('#gridView').hide();
                $scope.mappingList = [{
                    id: 1,
                    name: 'Vendor to SKU Mapping'
                }, {
                    id: 2,
                    name: 'SKU to Vendor Mapping'
                }];
                $scope.getAllVendors();
                $scope.getAllSkus();
                $scope.valueDataList = [];
                // this is required for ui-grid
                $scope.gridOptions = {};
                $scope.modalGridOptions = {};
                $scope.hideModalGrid = true;
                $scope.showPreview = previewModalService.showPreview;
            }

            $scope.clearData = function () {
                $scope.valueDataList = [];
                $scope.valueDataType = {
                    id: 0,
                    name: ""
                };
                $timeout(function () {
                    $('#valueDataId').val($scope.valueDataType).trigger('change');
                });
                $scope.gridOptions.data = null;
            }

            $scope.getMappingTypeData = function (mappingType) {
                $scope.clearData();
                if (mappingType.id == 1) {
                    $scope.mappingTypeData = "vendorToSku";
                    $scope.valueDataList = $scope.vendorList;
                } else if (mappingType.id == 2) {
                    $scope.mappingTypeData = "skuToVendor";
                    $scope.valueDataList = $scope.skuList
                }
            }

            $scope.getAllSkus = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllSku,
                }).then(function success(response) {
                    $scope.skuList = response.data;

                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.getAllVendors = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllVendors,
                }).then(function success(response) {
                    $scope.vendorList = response.data
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.searchMappings = function () {
                if ($scope.valueDataType == undefined || $scope.valueDataType == null
                    || $scope.valueDataType.id == 0) {
                    return;
                }
                if ($scope.mappingTypeData == "vendorToSku") {
                    $scope.getSkuMappingGrid();
                } else if ($scope.mappingTypeData == "skuToVendor") {
                    $scope.getVendorMappingsGrid();
                }
            }

            $scope.getSkuMappingGrid = function () {
                $scope.gridOptions = $scope.skuGridOptions();
                $scope.getSkuForVendor();
            }

            $scope.skuGridOptions = function () {
                return {
                    enableFiltering: true,
                    enableColumnResizing: true,
                    enableColumnMenus: false,
                    columnDefs: [{
                        field: 'id',
                        displayName: 'SKU Id',
                        cellTemplate: 'skuIdTemplate.html'
                    }, {
                        field: 'name',
                        displayName: 'SKU Name'
                    }, {
                        field: 'category',
                        displayName: 'Category'
                    }, {
                        field: 'subCategory',
                        displayName: 'Sub Category'
                    }, {
                        field: 'status',
                        displayName: 'SKU Status'
                    }, {
                        field: 'mappingStatus',
                        displayName: 'Mapping Status'
                    }, {
                        field: 'code',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeButton.html'
                    }, {
                        field: 'alias',
                        displayName: 'Alias ',
                        cellTemplate: 'addAliasName.html'
                    }]
                };
            }

            $scope.getSkuForVendor = function () {
                $http({
                    url: apiJson.urls.skuMapping.getSkuForVendor,
                    method: 'POST',
                    data: $scope.valueDataType.id
                }).then(function (response) {
                    $scope.gridOptions.data = response.data;

                }, function (response) {
                    console.log("error", response);
                });
            }

            $scope.getVendorMappingsGrid = function () {
                $scope.gridOptions = $scope.vendorGridOptions();
                $scope.getVendorForSku();
            }

            $scope.removeRow = function (value) {
                var index = $scope.modalGridOptions.data.indexOf(value);
                $scope.modalGridOptions.data.splice(index, 1);
            }

            $scope.changeStatus = function (value) {
                var vendorId = null;
                var skuId = null;
                var status = value.mappingStatus == 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
                if ($scope.mappingTypeData == "vendorToSku") {
                    vendorId = $scope.valueDataType.id;
                    skuId = value.id;
                } else if ($scope.mappingTypeData == "skuToVendor") {
                    vendorId = value.id;
                    skuId = $scope.valueDataType.id;
                }
                var currentUser = appUtil.getCurrentUser();
                var payload = {
                    vendorId: vendorId,
                    skuId: skuId,
                    status: status,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }

                $http({
                    url: apiJson.urls.skuMapping.updateVendorForSku,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data == true) {
                        $scope.updateGridRowStatus(value, status);
                        var msg = 'Mapping '
                        msg = msg + (status == 'ACTIVE' ? 'Activated' : 'Deactivated')
                        msg = msg + ' Successfully'
                        $toastService.create(msg);
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.updateGridRowStatus = function (value, status) {
                var x = null;
                var id = null;
                for (x in $scope.gridOptions.data) {
                    if ($scope.gridOptions.data[x].id == value.id) {
                        id = x;
                    }
                }
                if (id != null) {
                    $scope.gridOptions.data[id].mappingStatus = status;
                }
            }

            $scope.vendorGridOptions = function () {
                return {
                    enableFiltering: true,
                    enableColumnResizing: true,
                    enableColumnMenus: false,
                    columnDefs: [{
                        field: 'id',
                        displayName: 'Vendor Id'
                    }, {
                        field: 'name',
                        displayName: 'Vendor Name'
                    }, {
                        field: 'status',
                        displayName: 'Vendor Status'
                    }, {
                        field: 'mappingStatus',
                        displayName: 'Mapping Status'
                    }, {
                        field: 'code',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeButton.html'
                    }]
                };
            }

            $scope.getVendorForSku = function () {
                $http({
                    url: apiJson.urls.skuMapping.getVendorForSku,
                    method: 'POST',
                    data: $scope.valueDataType.id
                }).then(function (response) {
                    $scope.gridOptions.data = response.data;
                }, function (response) {
                    console.log("error", response);
                });
            }

            $scope.openNewMappingModal = function () {
                if ($scope.valueDataType != undefined && $scope.valueDataType.id > 0) {
                    $scope.newMappingList = [];
                    $scope.mapperValue = $scope.valueDataType;
                    if ($scope.mappingTypeData == "vendorToSku") {
                        $scope.mapperValue.sourceType = "Vendor";
                        $scope.mapperValue.targetType = "SKU";
                        $scope.modalValueDataList = $scope.skuList;
                        $scope.getModalVendorMapGrid();
                    } else {
                        $scope.mapperValue.sourceType = "SKU";
                        $scope.mapperValue.targetType = "Vendor";
                        $scope.modalValueDataList = $scope.vendorList;
                        $scope.getModalSkuMappGrid();
                    }
                }
            }

            $scope.getModalVendorMapGrid = function () {
                $scope.modalGridOptions = $scope.skuGridOptions();
                $scope.modalGridOptions.data = [];
            }

            $scope.getModalSkuMappGrid = function () {
                $scope.modalGridOptions = $scope.vendorGridOptions();
                $scope.modalGridOptions.data = [];
            }

            $scope.addToModalGridData = function () {
                $scope.hideModalGrid = false;
                data = $scope.modalValueDataType;
                if (data == undefined || data == null) {
                    return;
                }
                data.mappingStatus = "NA";
                var index = $scope.newMappingList.indexOf(data);
                if (index > -1) {
                    $toastService.create('Duplicate Mapping');
                } else {
                    $scope.newMappingList.push(data);
                    $scope.modalGridOptions.data = $scope.newMappingList;
                }
            }

            $scope.cancelModal = function () {
                $scope.hideModalGrid = true;
                $timeout(function () {
                    $('#modalValueDataId').val('').trigger('change');
                });
            };

            $scope.cancelAliasModal = function () {
                $scope.addAliasInSku = "";
                $timeout(function () {
                    $('#addAlias').val('').trigger('change');
                });

            };

            $scope.addAlias = function (value) {
                if (value.mappingStatus == "NA") {
                    $scope.setAliasInNewMapping = true;
                } else {
                    $scope.addAliasInSku = value.alias;
                    $scope.getSkuId = value.id;
                }
            }
            $scope.onSubmittingAliasName = function (aliasName) {
                $scope.addAliasInSku = "";
                if ($scope.setAliasInNewMapping) {
                    data.alias = aliasName;
                    $scope.setAliasInNewMapping = false;
                } else {
                    var payload = {
                        id: $scope.getSkuId,
                        code: aliasName,
                        name: $scope.valueDataType.id

                    }
                    $http({
                        url: apiJson.urls.skuMapping.addSkuForVendorAlias,
                        method: 'POST',
                        data: payload
                    }).then(function (response) {
                        if (response) {
                            $toastService.create('Name Added Successfully');
                            $scope.cancelAliasModal();
                            $scope.searchMappings();
                        }
                    }, function (response) {
                        console.log("error", response);
                    });
                }
            }

            $scope.submitModalGridData = function () {

                var list = [];
                var id = "";
                var alias = "";
                skuID = [];
                var x;
                for (x in $scope.newMappingList) {
                    console.log($scope.newMappingList);
                    list.push($scope.newMappingList[x].id);
                    id = $scope.newMappingList[x].id;
                    alias = $scope.newMappingList[x].alias;
                    skuID.push({id: id, code: alias});
                }
                var currentUser = appUtil.getCurrentUser();
                if ($scope.mappingTypeData == "vendorToSku") {
                    var payload = {
                        vendorId: $scope.valueDataType.id,
                        skuIds: skuID,
                        employeeId: currentUser.userId,
                        employeeName: currentUser.user.name
                    }
                } else {
                    var payload = {
                        id: $scope.valueDataType.id,
                        mappingIds: list,
                        employeeId: currentUser.userId,
                        employeeName: currentUser.user.name
                    }
                }
                var submiURL = "";
                if ($scope.mappingTypeData == "vendorToSku") {
                    submiURL = apiJson.urls.skuMapping.addSkuForVendor;
                } else {
                    submiURL = apiJson.urls.skuMapping.addVendorForSku
                }
                $http({
                    url: submiURL,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response) {
                        $toastService.create('Mappings Added Successfully');
                        $scope.cancelModal();
                        $scope.searchMappings();
                        $state.go("menu.vendorToUnitToSkuMapping");
                    }
                }, function (response) {
                    console.log("error", response);
                });
            }
        }
    ]);