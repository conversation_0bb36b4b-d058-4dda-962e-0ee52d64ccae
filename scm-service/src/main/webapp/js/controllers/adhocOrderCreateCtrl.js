/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('adhocOrderCreateCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'metaDataService', '$stateParams', 'previewModalService', 'Popeye', '$timeout','$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $state, metaDataService, $stateParams, previewModalService, Popeye, $timeout, $alertService) {

            function addItem(selectedProduct, rqstQty, rqstAbsQty) {
                var found = false;
                var rqstQty = appUtil.isEmptyObject(rqstQty) ? 0 : rqstQty;
                var rqstAbsQty = appUtil.isEmptyObject(rqstAbsQty) ? 0 : rqstAbsQty;

                $scope.addedRoItems.forEach(function (roi) {
                    if (roi.productId == selectedProduct.productId) {
                        found = true;
                        $toastService.create("Product already added!");
                        return false;
                    }
                });

                if (!found) {
                    if (selectedProduct.productId == 100217) {
                        $scope.isManualBook[selectedProduct.productId] = true;
                    }
                    var obj = {
                        id: null,
                        productId: selectedProduct.productId,
                        productName: selectedProduct.productName,
                        requestedQuantity: rqstQty,
                        requestedAbsoluteQuantity: rqstAbsQty,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: selectedProduct.unitOfMeasure,
                        unitPrice: selectedProduct.unitPrice,
                        negotiatedUnitPrice: selectedProduct.negotiatedUnitPrice,
                        reason: null,
                        comment: null
                    };

                    var packagingDef = appUtil.getPackagingMap();
                    $scope.productPackaging[obj.productId].forEach(function (pack) {
                        if (pack.mappingStatus == 'ACTIVE' && pack.isDefault) {
                            obj.packagingName = packagingDef[pack.packagingId].packagingName;
                            obj.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                        }
                    });
                    if($scope.unitProductPackagingMap[obj.productId] !=null && $scope.unitProductPackagingMap[obj.productId]!= undefined) {
                        $scope.unitProductPackagingMap[obj.productId].forEach(function (pack) {
                            if($scope.receivingUnit.id == pack.fulfillmentUnitId) {
                                obj.packagingName = packagingDef[pack.packagingId].packagingName;
                                obj.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                            }
                        });
                    }
                    $scope.addedRoItems.push(obj);
                    return obj;
                }
            }
             

            function addCloneItemsToRO(items) {
                $toastService.create("Adding cloned items to the Request Order! Please wait...");
                $scope.finalAvailableProducts = [];
                for(var i=0;i<$scope.scmProductDetails.length;i++){
                    var res=$scope.byProducts($scope.scmProductDetails[i]);
                    if(res){
                        $scope.finalAvailableProducts.push($scope.scmProductDetails[i]);
                    }
                }
                console.log("final available products are : ",$scope.finalAvailableProducts);
                var notInProds =[];
                var bool = false;
                for(var i in items){
                    for(var j=0;j<$scope.finalAvailableProducts.length;j++){
                        if(items[i].productId == $scope.finalAvailableProducts[j].productId){
                         var addedItem = addItem($scope.finalAvailableProducts[j], items[i].requestedAbsoluteQuantity, items[i].requestedAbsoluteQuantity);
                            var dummySku = {
                                skuId : -1,
                                skuName : "Any"};
                           
                            $scope.filteredSkuMap[$scope.finalAvailableProducts[j].productId] = [];
                            if($scope.assetOrder){
                                var tempList =  $scope.skuProductMap[$scope.finalAvailableProducts[j].productId];
                                $scope.filteredSkuMap[$scope.finalAvailableProducts[j].productId]  = filterAvailableSkuForUnits(tempList);
                                addedItem.skus = filterAvailableSkuForUnits(tempList);
                                addedItem.skus.push(dummySku);
                            }
                            $scope.filteredSkuMap[$scope.finalAvailableProducts[j].productId].push(dummySku);
                            $scope.skuSelected[$scope.finalAvailableProducts[j].productId]=-1;
                            bool=false;
                            break;
                        }
                        else{
                            bool = true;
                        }
                    }
                    if(bool){
                        notInProds.push(items[i]);
                    }
                }
                console.log("not there in list items are : ",notInProds);
                console.log("Till now added items are : ",$scope.addedRoItems);
            }

            function openBudgetExceedeModal(orderResponse) {
                var mappingModal = Popeye.openModal({
                    templateUrl: "budgetExceededModal.html",
                    controller: "budgetExceededModalCtrl",
                    resolve: {
                        orderResponse: function () {
                            return orderResponse;
                        }
                    },
                    click: false,
                    keyboard: false
                });
                mappingModal.closed.then(function () {});
            }


            $scope.init = function () {
                $scope.isCafe = appUtil.isCafe();
                $scope.currentSku = {sku:{}};
                $scope.roDiscontinuedStatus = {};
                $scope.skuSelected={};
                $scope.skuProductMap = appUtil.getSkuProductMap();
                $scope.isCapex = false;
                $scope.assetOrder = $stateParams.assetOrder;
                $scope.clonedItems = $stateParams.clonedItems;
                $scope.fulfilmentUnitName = $stateParams.fulfilmentUnit;
                $scope.availableProductList =[];
                $scope.bulkOrder = false;
                //$scope.scmUnitList = appUtil.filterCurrentUnit(appUtil.getUnitList());
                $scope.getAvailableUnits();
                metaDataService.getScmProductDetails();
                metaDataService.getUnitData();
                metaDataService.getPackagingMap();
                $scope.currentUnit = appUtil.getUnitData();
                $scope.scmProductDetails = appUtil.getActiveScmOrderingProducts($scope.assetOrder);
                $scope.comment = null;
                $scope.minRefOrderFulFillmentDate = appUtil.getDate(0);
                $scope.maxRefOrderFulFillmentDate = appUtil.getDate(30);
                $scope.addedRoItems = [];
                $scope.selectedProduct = $scope.scmProductDetails[0];
                $scope.isManualBook = {};
                $scope.alternateF9Order = null;
                $scope.showF9Message = false;

                $scope.productPackaging = [];
                metaDataService.getAllPackagingMappings(function (packagingMap) {
                    $scope.productPackaging = packagingMap;
                },true);

                if (!appUtil.isEmptyObject($scope.clonedItems)) {
                    $toastService.create("Select a fulfillment unit first! Valid cloned items will be added after that");
                }
                $scope.showPreview = previewModalService.showPreview;
                $scope.reasons = ["SHORT_TREND","BULK_ORDER","PRODUCT_MISSING_IN_RO","EXCESS_ON_HAND_EXPIRY","WRONG_KETTLE_END_INVENTORY","OTHER"];
                $scope.selecetedReason = null;
                $scope.enterredComment = null;
                $scope.isFountain9Unit = false;
                $scope.checkFountain9Unit();
                $scope.validateHandOverDate();
                $scope.filteredSkuMap = {};
            };

            $scope.setAlternateForF9 = function (check) {
                $scope.alternateF9Order = check;
            };

            $scope.setBulkOrder = function (isBulk) {
                $scope.bulkOrder = isBulk;
            };

            $scope.setSelectedProduct = function (product) {
                $scope.selectedProduct = product;
            };
            $scope.onSelectChange = function(sku,productId){
                $scope.skuSelected[productId] = sku;
             }

            $scope.clearData = function () {
                $alertService.confirm("Are you sure?","All the Entered Data will be cleared..!",function(result){
                    if(result){
                        $scope.alternateF9Order = null;
                        $scope.receivingUnit = null;
                        $scope.fulfillmentDate = null;
                        $scope.havecancelledOrders = false;
                        $scope.selectedUnit = undefined;
                        $scope.selectedProduct = null;
                        $scope.addedRoItems = [];
                        $scope.selecetedReason = null;
                        $timeout(function() {
                            $('#selectedUnit').val(undefined).trigger('change');
                            $('#selectedProduct').val(null).trigger('change');
                        });
                    }
                });
            };

            $scope.checkForF9Orders = function () {
                $scope.alternateF9Order = null;
                $scope.havecancelledOrders = false;
                $scope.selectedProduct = null;
                $scope.addedRoItems = [];
                $timeout(function() {
                    $('#selectedProduct').val(null).trigger('change');
                });
                if (appUtil.isEmptyObject($scope.receivingUnit)) {
                    $toastService.create("Please select the Fulfilment unit Id first..!");
                    $scope.fulfillmentDate = null;
                    return false;
                }
                if (!$scope.assetOrder && $scope.isFountain9Unit) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.referenceOrderManagement.checkForF9Orders,
                        params: {
                            "requestingUnitId": appUtil.getCurrentUser().unitId,
                            "fulfilmentUnitId": $scope.receivingUnit.id,
                            "fulfilmentDate": appUtil.formatDate($scope.fulfillmentDate, "yyyy-MM-dd")
                        }
                    }).then(function success(response) {
                        if (response.data != null && response.data) {
                            $scope.havecancelledOrders = true;
                        } else {
                            $scope.havecancelledOrders = false;
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $scope.havecancelledOrders = false;
                    });
                }
                $scope.getDiscontinuedStatus();
            };

            function setSkuToProduct(productId){
                var skuList = $scope.skuProductMap[productId];
                skuList = filterAvailableSkuForUnits(skuList);
                return skuList;
                }

            $scope.getAvailableSKUs = function(unitId) {
                $http({
                method : "GET",
                url : apiJson.urls.filter.availableSKUs,
                params : {
                unitId : unitId
                }
                }).then(function success(response) {
                if (response.data != null && response.data.length > 0) {
                $scope.availableSkuListForTransferringUnit = response.data;
               } else {
                $toastService.create("Something went wrong. Please try again!");
                }
                }, function error(response) {
                console.log("error:" + response);
                });
            };

            function filterAvailableSkuForUnits(skuList){
            if(!appUtil.isEmptyObject($scope.availableSkuListForTransferringUnit) && $scope.availableSkuListForTransferringUnit.length > 0){
                skuList = skuList.filter(function (sku) {
                return  $scope.availableSkuListForTransferringUnit.indexOf(sku.skuId) != -1;
            });
            }
            return skuList;
            }

            function setSkuToProduct(productId){
                var skuList = $scope.skuProductMap[productId];
                skuList = filterAvailableSkuForUnits(skuList);
                return skuList;
                }

            $scope.getAvailableSKUs = function(unitId) {
                $http({
                method : "GET",
                url : apiJson.urls.filter.availableSKUs,
                params : {
                unitId : unitId
                }
                }).then(function success(response) {
                if (response.data != null && response.data.length > 0) {
                $scope.availableSkuListForTransferringUnit = response.data;
               } else {
                $toastService.create("Something went wrong. Please try again!");
                }
                }, function error(response) {
                console.log("error:" + response);
                });
            };

            function filterAvailableSkuForUnits(skuList){
            if(!appUtil.isEmptyObject($scope.availableSkuListForTransferringUnit) && $scope.availableSkuListForTransferringUnit.length > 0){
                skuList = skuList.filter(function (sku) {
                return  $scope.availableSkuListForTransferringUnit.indexOf(sku.skuId) != -1;
            });
            }
            return skuList;
            }

            $scope.checkFountain9Unit = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.referenceOrderManagement.getFountain9Units,
                    params : {
                        "unitId" : appUtil.getCurrentUser().unitId,
                        "isForceLookUp" : false
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        if (response.data.indexOf(appUtil.getCurrentUser().unitId) != -1) {
                            $scope.isFountain9Unit = true;
                        }
                    } else {
                        $scope.isFountain9Unit = false;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.isFountain9Unit = false;
                });
            };

            $scope.getAvailableUnits = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableUnits,
                    params: {
                        unitId: appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.scmUnitList = [];
                        appUtil.getUnitList().map(function (unit) {
                            var i = 0;
                            for (i = 0; i < response.data.length; i++) {
                                if (unit.id == response.data[i]) {
                                    $scope.scmUnitList.push(unit);
                                }
                            }
                        });
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.selectCategory = function (selectedUnit) {
                if (!appUtil.isEmptyObject(selectedUnit)) {
                    var result = confirm("Are you sure? All products will be cleared if you change the unit");
                    if (result) {
                        $scope.alternateF9Order = null;
                        $scope.fulfillmentDate = null;
                        $scope.havecancelledOrders = false;
                        $scope.selectedProduct = null;
                        $scope.addedRoItems = [];
                        $scope.selecetedReason = null;
                        $timeout(function() {
                            $('#selectedProduct').val(null).trigger('change');
                        });
                        selectedUnit = JSON.parse(selectedUnit);
                        $scope.receivingUnit = selectedUnit;
                        $scope.scmProductDetails = appUtil.getActiveScmOrderingProducts($scope.assetOrder);
                        $scope.scmProductDetailsCopy = angular.copy($scope.scmProductDetails);
                        $scope.getUnitProductPackagingMappings(function () {
                            $scope.getAvailableProducts(selectedUnit);
                            $scope.category = selectedUnit.category;
                            $scope.selectedProduct = null;
                            $scope.addedRoItems = [];
                        });
                    }
                }
                if($scope.isCapex && $scope.assetOrder){
                    $scope.getAvailableSKUs(selectedUnit.id);
                }
            };


            $scope.getUnitProductPackagingMappings = function (callback) {
                $http({
                    method: "GET",
                    url: apiJson.urls.productManagement.defaultPackagingMappings,
                    params: {
                        unitId : appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data != null) {
                        $scope.unitProductPackagingMap = response.data;
                    } else {
                        $toastService.create("Something went wrong while getting Default Packagings. Please try again!");
                        $scope.unitProductPackagingMap = {};
                    }
                        callback();
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.unitProductPackagingMap = {};
                    callback();
                });
            };

            $scope.getAvailableProducts = function (selectedUnit) {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableProducts,
                    params: {
                        requestingUnit: appUtil.getCurrentUser().unitId,
                        fulfillmentUnit: selectedUnit.id
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.availableProductList = response.data;
                        $scope.getLastWeekAverage($scope.availableProductList);

                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            function getScmProducts (discontinuedProds) {
                var result = [];
                angular.forEach($scope.scmProductDetailsCopy, function (prod) {
                    if ($scope.byProducts(prod)) {
                        result.push(prod);
                    }
                });
                return result;
            }

            $scope.getDiscontinuedStatus = function () {
                $http({
                    method: "POST",
                    url: apiJson.urls.requestOrderManagement.getProductDiscontinuedStatus,
                    data: $scope.availableProductList,
                    params: {
                        fulfilmentUnitId: $scope.receivingUnit.id,
                        fulfilmentDate: $scope.fulfillmentDate
                    }
                }).then(function success(response) {
                    console.log("Before the size of scm products is : ",$scope.scmProductDetails.length);
                    if (response.data != null) {
                        $scope.roDiscontinuedStatus = response.data;
                        $scope.scmProductDetails = getScmProducts(response.data);
                        console.log("after the size of scm products is : ",$scope.scmProductDetails.length);
                        if(!appUtil.isEmptyObject($scope.clonedItems)){
                            addCloneItemsToRO($scope.clonedItems);
                        }
                    } else {
                        $scope.roDiscontinuedStatus = {};
                        $toastService.create("Something went wrong while getting Ro discontinued status. Please try again!");
                    }

                }, function error(response) {
                    $toastService.create("Error Occurred while getting Ro discontinued status. Please try again!");
                    console.log("error:" + response);
                    $scope.roDiscontinuedStatus = {};
                });
            };


            $scope.byProducts = function (product) {
                if ($scope.receivingUnit.category == "CAFE" && $scope.currentUnit.family == "CAFE") {
                    return product != null && product != undefined && product.interCafeTransfer == true && $scope.availableProductList != undefined && $scope.availableProductList != null
                        && $scope.availableProductList.indexOf(product.productId) > -1
                        && $scope.roDiscontinuedStatus[product.productId];
                } else {
                    return product != null && product != undefined && $scope.availableProductList != undefined && $scope.availableProductList != null
                        && $scope.availableProductList.indexOf(product.productId) > -1 && $scope.roDiscontinuedStatus[product.productId];
                }
            };

            $scope.changeAllReasons = function (reason) {
                $scope.selecetedReason = reason;
                for (var i = 0; i < $scope.addedRoItems.length; i++) {
                    $scope.addedRoItems[i].reason = reason;
                }
            };

            $scope.changeAllComments = function (comment) {
                $scope.enterredComment = comment;
                for (var i = 0; i < $scope.addedRoItems.length; i++) {
                    $scope.addedRoItems[i].comment = comment;
                }
            };

            function checkForComment() {
                if ((!$scope.isFountain9Unit && !$scope.assetOrder) || $scope.assetOrder) {
                    return true;
                }
                else {
                    for (var i = 0; i < $scope.addedRoItems.length; i++) {
                        if ($scope.addedRoItems[i].comment != null && $scope.addedRoItems[i].comment.length > 100) {
                            $toastService.create("Comment Should not exceed 100 characters for product : " + $scope.addedRoItems[i].productName);
                            return false;
                        }
                    }
                    return true;
                }
            }

            function checkForCancelledF9Orders() {
                return true;
            }

            function checkForReason() {
                if ((!$scope.isFountain9Unit && !$scope.assetOrder) || $scope.assetOrder) {
                    return true;
                }
                else {
                    for (var i = 0; i < $scope.addedRoItems.length; i++) {
                        if (!appUtil.isEmptyObject($scope.addedRoItems[i].reason)) {
                            if ($scope.addedRoItems[i].reason == "OTHER") {
                                if ($scope.addedRoItems[i].comment == null) {
                                    $toastService.create("Please Enter minimum 3 letters in the comment for product : " + $scope.addedRoItems[i].productName);
                                    return false;
                                } else {
                                    if ($scope.addedRoItems[i].comment == "") {
                                        $toastService.create("Please Enter the comment for product : " + $scope.addedRoItems[i].productName);
                                        return false;
                                    }
                                    if ($scope.addedRoItems[i].comment.length < 3) {
                                        $toastService.create("Please Enter minimum 3 letters in the comment for product : " + $scope.addedRoItems[i].productName);
                                        return false;
                                    }
                                }
                            }
                        }
                        else {
                            $toastService.create("Please Select the reason for the product : " + $scope.addedRoItems[i].productName);
                            return false;
                        }
                    }
                    return true;
                }
            }

            $scope.createRoObject = function (budgetStatus) {
            	if (!appUtil.isEmptyObject($scope.clonedItems)) {
            		if(JSON.parse($scope.selectedUnit).name != $scope.fulfilmentUnitName){
                    $toastService.create("Fulfillment Unit selected is not correct.");
                    return false;
            		}
            	}
            	console.log($scope.fulfilmentUnitName );
                if ($scope.addedRoItems.length >= 250) {
                    $toastService.create("Please make sure there are not more than 249 items in the Request Order");
                    return false;
                } else if ($scope.selectedUnit == null) {
                    $toastService.create("Please select unit!");
                    return false;
                } else if ($scope.fulfillmentDate == null) {
                    $toastService.create("Please select fulfillment date!");
                    return false;
                } else if ($scope.assetOrder && ($scope.comment == undefined || $scope.comment == null || $scope.comment.trim() == '' || $scope.comment.length <= 20)) {
                    $toastService.create("Please specify an elaborated comment with more than 20 characters!");
                    return false;
                } else if (!checkForComment()) {
                    return false;
                } else if (!checkForReason()) {
                    return false;
                } else if (!$scope.assetOrder && !checkForCancelledF9Orders()) {
                    return false
                }
                else {
                    var ro = {
                        id: null,
                        generationTime: null,
                        lastUpdateTime: null,
                        requestUnit: appUtil.createRequestUnit(),
                        generatedBy: appUtil.createGeneratedBy(),
                        specialOrder: false,
                        fulfillmentUnit: {
                            id: JSON.parse($scope.selectedUnit).id,
                            code: "",
                            name: JSON.parse($scope.selectedUnit).name
                        },
                        fulfillmentDate: new Date($scope.fulfillmentDate),
                        searchTag: $scope.searchTag,
                        referenceOrderId: null,
                        status: 'CREATED',
                        comment: $scope.comment,
                        purchaseOrderId: null,
                        transferOrderId: null,
                        goodsReceivedId: null,
                        requestOrderItems: $scope.addedRoItems,
                        assetOrder: $scope.assetOrder,
                        alternateF9Order: $scope.alternateF9Order ? "Y" : "N",
                        applyBudget: budgetStatus,
                        budgetReason: (budgetStatus ? null : 'FORCE_SUBMIT'),
                        type : "OPEX",
                        bulkOrder: $scope.bulkOrder
                    };
                    if (ro.requestOrderItems.length == 0) {
                        $toastService.create("Please add at least a few items!");
                        return false;
                    } else if ($scope.invalidQuantity(ro.requestOrderItems)) {
                        $toastService.create("Requested item quantity cannot be 0 or less!");
                        return false;
                    } else {
                        if($scope.isCapex == true){
                            $scope.ro = ro;
                            $scope.ro.type = "CAPEX";
                            getCurrentProductPricesForFulfillmentUnit(ro);
                        }else{
                            $scope.submitRO(ro);
                        }
                    }
                }
            };

            function getCurrentProductPricesForFulfillmentUnit(ro){
                var productIds = [];
                $scope.addedRoItems.forEach(function (roItem){
                    productIds.push(roItem.productId);
                });
                console.log($scope.skuSelected);
                var currentPriceMap = {};
                $http({
                    method: "POST",
                    url: apiJson.urls.requestOrderManagement.getCurrentPriceForFullfilmentUnit,
                    data: $scope.skuSelected,
                    params : {
                        fullfilmentUnitId : ro.fulfillmentUnit.id,
                        requestingUnitId : ro.requestUnit.id,
                        isFA : $scope.assetOrder,
                    }
                }).then(function success(response) {
                    if (response.data != null) {
                        currentPriceMap = response.data;
                        if(setCurrentPrices(currentPriceMap) == true){
                            $scope.openBudgetModal(ro);
                        }
                    } else {
                        $toastService.create("Couldn't Find Ro Item Prices. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            function setCurrentPrices(currentPriceMap){
                $scope.totalTax = 0;
                var priceNotFoundList = [];
                $scope.addedRoItems.forEach(function (roItem){
                    if(appUtil.isEmptyObject(currentPriceMap[roItem.productId]) || currentPriceMap[roItem.productId].key == -1){
                        priceNotFoundList.push(roItem.productName);
                    }else{
                        roItem.negotiatedUnitPrice = currentPriceMap[roItem.productId].key;
                        $scope.totalTax += ( currentPriceMap[roItem.productId].value * roItem.packagingQuantity * roItem.conversionRatio)
                    }
                });
                if(priceNotFoundList.length > 0){
                    $alertService.alert("Price Not Found For Some Products :" , priceNotFoundList,function () {}, true);
                    return false;
                }
                return true;
            }


            $scope.submitRO = function (ro){
                $http({
                    method: "POST",
                    url: apiJson.urls.requestOrderManagement.requestOrder,
                    data: ro
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        if (response.data[0].budgetExceeded) {
                            openBudgetExceedeModal(response.data);
                            //$toastService.create("You have exceeded your budget!");
                        } else {
                            $toastService.create("Request order with id " + response.data[0].orderId + " created successfully!");
                            $state.go("menu.reqOrderMgt");
                        }
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                      $alertService.alert("Error While Submitting RO" , response.data.errorMsg,response.data.errorTitle,function () {}, true);
                    console.log("error:" + response);
                });
            }


            $scope.removeItem = function (index) {
                var removeProduct =  $scope.addedRoItems.splice(index, 1);
                 delete  $scope.skuSelected[removeProduct[0].productId];
                 delete $scope.currentSku.sku[removeProduct[0].productId];  
                if ($scope.addedRoItems.length == 0) {
                    $scope.selecetedReason = null;
                    $scope.enterredComment = null;
                }
            };

            $scope.invalidQuantity = function (items) {
                var returnVal = false;
                items.forEach(function (val) {
                    if (val.requestedQuantity == null || angular.isUndefined(val.requestedQuantity) || val.requestedQuantity <= 0) {
                        returnVal = true;
                    }
                });
                return returnVal;
            };

            $scope.addNewRoItem = function () {

                if ($scope.selectedUnit == null) {
                    $toastService.create("Please select unit!");
                    return false;
                }

                if ($scope.selectedProduct == null) {
                    $toastService.create("Select a different product");
                    return;
                }

                if ($scope.fulfillmentDate == null) {
                    $toastService.create("Please select a fulfilment date ..!");
                    return;
                }
               var addedItem =  addItem(JSON.parse($scope.selectedProduct), 0, 0);
                $scope.selectedProductObj = JSON.parse($scope.selectedProduct);
                 var dummySku = {
                    skuId : -1,
                    skuName : "Any"};
                 $scope.filteredSkuMap[$scope.selectedProductObj.productId] = [];
                if($scope.isCapex && $scope.assetOrder){
                    var tempList =  $scope.skuProductMap[$scope.selectedProductObj.productId];
                   $scope.filteredSkuMap[$scope.selectedProductObj.productId]  = filterAvailableSkuForUnits(tempList);
                   addedItem.skus = filterAvailableSkuForUnits(tempList);
                   addedItem.skus.push(dummySku);
                }
                $scope.filteredSkuMap[$scope.selectedProductObj.productId].push(dummySku);
               
                $scope.skuSelected[$scope.selectedProductObj.productId]=-1;
                $timeout(function(){
                $scope.$apply();})

            };

            $scope.openValidateInputModal = function(roItem){
                var distanceViewModal = Popeye.openModal({
                    templateUrl: "qtyValidation.html",
                    controller: "qtyValidationCtrl",
                    resolve: {
                        roItem : function (){
                            return roItem;
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });

                distanceViewModal.closed.then(function(data){
                    $scope.modalOpened = false;
                    if(appUtil.isEmptyObject(data)){
                        roItem.packagingQuantity = 0;
                        roItem.requestedQuantity = 0;
                        roItem.requestedAbsoluteQuantity = 0;
                    }
                });
            }

            $scope.calculateRequestQty = function (roi) {

                if($scope.modalOpened != true){
                    if (roi.productId == 100217) {
                        roi.packagingQuantity = 1;
                    }

                    var unit = JSON.parse($scope.selectedUnit);
                    if (unit.category == "WAREHOUSE" || unit.category == "KITCHEN") {
                        roi.packagingQuantity = parseInt(roi.packagingQuantity);
                    }
                    roi.requestedQuantity = roi.packagingQuantity * roi.conversionRatio;
                    roi.requestedAbsoluteQuantity = roi.requestedQuantity;
                    if ($scope.validateAverageQty(roi.requestedQuantity, roi.productId) == false) {
                        clearTimeout($scope.timeout);
                        $scope.timeout = setTimeout(function () {
                            $scope.modalOpened = true;
                            $scope.openValidateInputModal(roi);
                        }, 500);

                    }
                }else {
                    clearTimeout($scope.timeout);
                }
            };

            $scope.validateAverageQty =function(qty,productId){
                var lastWeeekAvg  = $scope.lastWeekAverageMap[productId];
                if(lastWeeekAvg == 0){
                    return true;
                }
                var finalValue  = lastWeeekAvg * 1.5;
                if(qty > finalValue){
                    return false;
                }
                return true;
            }


            $scope.getLastWeekAverage =function (productIds){
                $http({
                    method: "POST",
                    url: apiJson.urls.requestOrderManagement.getLastWeekAverageQty,
                    data: productIds,
                    params : {
                        unitId : appUtil.getUnitData().id,
                        isSpecial:false
                    }
                }).then(function success(response) {
                    if (response.data !== null) {
                        $scope.lastWeekAverageMap = response.data;
                    } else {
                        $alertService.alert("Error in Loading", "Error loading Last Week Qty. lease try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            function getRoItems() {
                var roi = {
                    id: null,
                    productId: null,
                    productName: null,
                    requestedQuantity: null,
                    requestedAbsoluteQuantity: null,
                    transferredQuantity: null,
                    receivedQuantity: null,
                    unitOfMeasure: null
                }
            }

            function getFixedAssetBudget(){
                $scope.budgetDetails = {};
                var promise = $http({
                    url: apiJson.urls.requestOrderManagement.getDepartmentBudgetData,
                    method: "GET",
                    params: {
                        unitId: appUtil.getCurrentUser().unitId,
                        isFixedAssetOrGoods: $scope.assetOrder ? "FA_Equipment" : "NRE_Consumable"
                    }
                }).then(function (response) {
                    if (response.status == 200) {
                        $scope.emptyCheck = appUtil.isEmptyObject(response.data);
                        if ($scope.emptyCheck) {
                            $scope.budgetDetails = {};
                            $toastService.create("Cannot find budget details");
                        }
                        else {
                            $scope.budgetDetails = response.data;
                        }
                    }
                }, function (response) {
                    console.log(response);
                });
                return promise;
            }

            $scope.validateHandOverDate = function() {
                var unitData = appUtil.getUnitData();
                if (appUtil.isCafe()) {
                    if (unitData.handoverDate == null) {
                        $scope.isCapex = true;
                    }
                    else {
                        var date = new Date(unitData.handoverDate);
                        date.setDate(date.getDate() + 3);
                        var calculatedHandOverDate = date;
                        console.log("hand over date is ",calculatedHandOverDate);
                        console.log("current date is :",new Date(appUtil.getCurrentBusinessDate()));

                        if (new Date(appUtil.getCurrentBusinessDate()) <= calculatedHandOverDate) {
                            $scope.isCapex = true;
                        }
                    }
                }

            };

            function getTotalAmount(){
                var totalAmount = 0;
                $scope.addedRoItems.forEach(function (roItem){
                    totalAmount += (roItem.negotiatedUnitPrice * roItem.packagingQuantity * roItem.conversionRatio);
                });
                return totalAmount;
            }


            $scope.openBudgetModal = function(ro){
                var totalAmount = getTotalAmount();
                ro.totalAmount = totalAmount + $scope.totalTax;
                getFixedAssetBudget().then(function (data) {
                    var budgetModal = Popeye.openModal({
                        templateUrl: 'departmentROModal.html',
                        controller: "departmentROCtrl",
                        modalClass: 'modal-large',
                        resolve: {
                            totalCost: function () {
                                return totalAmount;
                            },
                            totalTax: function () {
                                return $scope.totalTax;
                            },
                            totalAmount: function () {
                                return totalAmount + $scope.totalTax;
                            },
                            emptyCheck: function () {
                                return $scope.emptyCheck;
                            },
                            budgetDetails: function () {
                                return $scope.budgetDetails;
                            },
                            ro : function (){
                                return ro;
                            }
                        },
                        click: false,
                        keyboard: false
                    });

                    budgetModal.closed.then(function (result) {
                        if (result) {
                            $scope.submitRO($scope.ro);
                        }
                    });
                });
            };


        }]
    ).controller('budgetExceededModalCtrl', ['$scope', 'orderResponse', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye',
            function ($scope, orderResponse, appUtil, $toastService, apiJson, $http, Popeye) {
                $scope.orderResponse = orderResponse;
                //  	console.log("$scope.orderResponse",$scope.orderResponse);

                $scope.cancel = function () {
                    closeModal();
                };

                function closeModal() {
                    Popeye.closeCurrentModal();
                }

            }
        ]
    ).controller('departmentROCtrl', ['$scope','$http', 'apiJson','appUtil','$toastService','totalAmount','totalCost',
    'totalTax','Popeye','emptyCheck','budgetDetails','ro',
    function ($scope, $http,apiJson,appUtil, $toastService, totalAmount, totalCost, totalTax, Popeye, emptyCheck, budgetDetails,ro) {

        $scope.totalAmount = totalAmount;
        $scope.totalTax = totalTax;
        $scope.totalCost = totalCost;
        $scope.emptyCheck = emptyCheck;
        console.log("empty check is ",emptyCheck,budgetDetails);
        $scope.budgetDetails = budgetDetails;
        $scope.ro  = ro;

        $scope.cancel = function(){
            Popeye.closeCurrentModal(false);
        };

        $scope.submit = function (isOpex) {
            if (appUtil.isEmptyObject($scope.budgetDetails)) {
                $toastService.create("No budget Details Found Can not place Order !");
                return;
            }
            if(isOpex == false && $scope.totalAmount > $scope.budgetDetails.remainingAmount){
                $toastService.create("Exceeding Budget limit! Can not place Order !");
                return;
            }
            if(isOpex == true){
                $scope.ro.type = "OPEX";
            }
            Popeye.closeCurrentModal(true);
        };

    }
]).controller('qtyValidationCtrl', ['$scope', 'roItem','appUtil', '$toastService', 'apiJson', '$http', 'Popeye','$window',
        function ($scope, roItem,appUtil, $toastService, apiJson, $http, Popeye,$window) {
            $scope.init = function (){
                $scope.roItem = roItem;
                $scope.roItem.reValidatedPkgQty = 0;
            }

            $scope.submit = function (){
                if($scope.roItem.reValidatedPkgQty != $scope.roItem.packagingQuantity){
                    $scope.roItem.packagingQuantity = 0;
                    $scope.roItem.requestedQuantity = 0;
                    $scope.roItem.requestedAbsoluteQuantity = 0;
                    $toastService.create("Re-Entered Quantity Doesn't Match With Previously Entered Quantity!!!!")
                    $scope.closeModal();
                }
                $scope.closeModal();
            }

            $scope.calculateRequestQty = function (item) {
                item.packagingQuantity = parseInt(item.packagingQuantity);
                item.requestedQuantity = item.reValidatedPkgQty*item.conversionRatio;
                item.requestedAbsoluteQuantity = item.requestedQuantity;
            };


            $scope.closeModal = function () {
                Popeye.closeCurrentModal({success : true});
            };

        }
    ]
);
