angular.module('scmApp')
    .controller('milkBreadBypassCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location','previewModalService','$toastService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, previewModalService,$toastService) {
                $scope.init = function () {
                        $scope.pendingMilkBread = null;
                        $scope.isByPassed = false;
                        $scope.comment = null;
                        $scope.byPassReasons = ["Vendor not delivered in time","System error while doing GR"];
                        $scope.currentUser = appUtil.getCurrentUser();
                        $scope.getPendingMilkBreadOfUnit();
                };

                $scope.selectedMilkBreadReasons= [];
                $scope.multiSelectSettingsMilkBreadReasons = {
                    showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,showCheckAll:true,showUncheckAll:true,
                    scrollableHeight: '250px',trackBy:'id',clearSearchOnClose: true
                };

                $scope.getPendingMilkBreadOfUnit = function () {
                        $http({
                                method: "GET",
                                url: apiJson.urls.stockManagement.getPendingSpecialRos,
                                params: {
                                        unitId: $scope.currentUser.unitId
                                }
                        }).then(function success(response) {
                                if (response.status == 200) {
                                        $scope.pendingMilkBread = response.data;
                                } else {
                                        $scope.pendingMilkBread = null;
                                }
                        }, function error(err) {
                                $scope.pendingMilkBread = null;
                                console.log("error occurred while getting Pending Milk and Bread :: ", err);
                        });
                };

                $scope.openRemoveMilkBreadModal = function () {
                        $scope.isByPassed = true;
                };

                $scope.setEnteredComment = function (enterredComment) {
                        $scope.comment = enterredComment;
                };

                function getMilkBreadBypassObject() {
                       var result = {
                               "unitId" : $scope.currentUser.unitId,
                               "bypassReason" : $scope.selectedMilkBreadReasons.join(","),
                               "comment" : $scope.comment,
                               "bypassedBy" : $scope.currentUser.userId,
                               "roIds" : $scope.pendingMilkBread.roIds.join(",")
                       }
                       return result;
                }

                $scope.submitByPassRequest = function () {
                        if ($scope.selectedMilkBreadReasons.length == 0) {
                                $toastService.create("Please Select atleast one reason to Bypass ..!");
                                return false;
                        }
                        if (appUtil.isEmptyObject($scope.comment)) {
                                $toastService.create("Please Enter Comment ..!");
                                return false;
                        }
                        var milkBreadBypass = getMilkBreadBypassObject();
                        $http({
                                method: 'POST',
                                url: apiJson.urls.scmCacheManagement.markMilkBreadCompleteForUnit,
                                data: milkBreadBypass
                        }).then(function success(response) {
                                $toastService.create("Ros By passed Successfully ..!");
                                $scope.init();
                        }, function error(response) {
                                $toastService.create("Error Occurred While By Passing Ro's");
                                $scope.init();
                        });
                };
        }]);