/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('createPaymentRequestCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', '$timeout', '$window', 'Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService, previewModalService, $timeout, $window, Popeye) {

            $scope.init = function () {
                $scope.employeePaymentCards = [];
                $scope.uploadedCardPaymentProof = null;
                $scope.isMilkPayment = false;
                $scope.advanceTypes = ["PO_ADVANCE","SO_ADVANCE"];
                $scope.advanceUploadedDoc = null;
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.selectView = true;
                $scope.selectedGr = null;
                $scope.maxDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.errorMessage = null;
                $scope.itemDeviations = [];
                $scope.invoiceDeviations = [];
                $scope.advancePaymentVendor = null;
                $scope.pendingPoSo = [];
                $scope.poSoSelected = null;
                $scope.advanceType = null;
                $scope.enterredText = null;
                $scope.advanceSelectionView = false;
                metaDataService.getSCMMetaData(function (metadata) {
                    $scope.prRequestTypes = metadata.paymentRequestTypes.filter(function (prType) {
                        return prType.shortCode == "GR" || prType.shortCode == "ADP";
                    });
                    $scope.prRequestType = $scope.prRequestTypes == null ? null : $scope.prRequestTypes[0];
                    metadata.paymentDeviations.map(function (deviation) {
                        if (deviation.deviationType == "DEVIATION") {
                            if (deviation.deviationLevel == "INVOICE_ITEM") {
                                $scope.itemDeviations.push(deviation);
                            } else if (deviation.deviationLevel == "INVOICE") {
                                $scope.invoiceDeviations.push(deviation);
                            }
                        }
                    });
                });
                $scope.unitList = [];
                $scope.unitList.push({
                    id: null,
                    name: ""
                });
                $scope.selectedUnit = $scope.unitList[0];
                $scope.vendorList = [];
                $scope.vendorList.push({
                    id: null,
                    name: ""
                });
                $scope.selectedVendor = $scope.vendorList[0];
                $scope.getEmployeeMappedUnits();
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.showPreview = previewModalService.showPreview;
                $scope.deviationGrItems = [];
                $scope.advancePaymentVendors = [];
                $scope.advanceAmount = null;
                metaDataService.getTrimmedVendors(function (response) {
                    $scope.advancePaymentVendors = response;
                });
                $scope.getEmployeePaymentCards();
            };

            $scope.uploadCardPaymentProof = function () {
                $fileUploadService.openFileModal("Upload Card Payment Proof", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    var fileLimit = fileExt.toLowerCase() == 'png' ? 1024000 : 5120000;
                    if (file.size > fileLimit) {
                        var msg = ""
                        if (fileExt.toLowerCase() == 'png') {
                            msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
                        }
                        $toastService.create('File size should not be greater than ' + fileLimit / 1024000 + ' MB.' + msg);
                        return;
                    }

                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', appUtil.getCurrentUser().userId);
                        fd.append('docType', "CARD_PAYMENT_PROOF");
                        fd.append('file', file);
                        fd.append('docName', "CARD_PAYMENT_PROOF");
                        $http({
                            url: apiJson.urls.paymentRequestManagement.uploadCardPaymentProof,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $scope.uploadedCardPaymentProof = response;
                            } else {
                                $toastService.create("Upload failed");
                                $scope.uploadedCardPaymentProof = null;
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response.errorMsg != null) {
                                $alertService.alert(response.errorTitle, response.errorMsg, true)
                            } else {
                                $toastService.create("Upload failed");
                            }
                            $scope.uploadedCardPaymentProof = null;
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                        $scope.uploadedCardPaymentProof = null;
                    }
                });
            };

            $scope.downloadPaymentProof = function () {
                metaDataService.downloadDocument($scope.uploadedCardPaymentProof);
            };

            $scope.setPaymentCard = function (card) {
                $scope.paymentRequest.paymentCard = card;
                $scope.setCardPaymentTransactionNumber(null);
                $scope.paymentRequest.cardPaymentComment = null;
                $scope.uploadedCardPaymentProof = null;
                $scope.paymentRequest.cardPaymentProof = null;
            };

            $scope.setCardPaymentTransactionNumber = function (transactionNumber) {
                $scope.paymentRequest.cardPaymentTransactionNumber = transactionNumber;
                $scope.uploadedCardPaymentProof = null;
                $scope.paymentRequest.cardPaymentProof = null;
            };

            $scope.setPaymentCardCheck = function (check) {
                $scope.paymentRequest.paymentCardCheck = check;
                $scope.setPaymentCard(null);
                $scope.setCardPaymentTransactionNumber(null);
                $scope.paymentRequest.cardPaymentComment = null;
                $scope.paymentRequest.cardPaymentProof = null;
                $scope.uploadedCardPaymentProof = null;
            };

            $scope.getEmployeePaymentCards = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.paymentRequestManagement.employeePaymentCards,
                    params: {
                        userId: $scope.currentUser.userId
                    }
                }).then(function (response) {
                    $scope.employeePaymentCards = response.data;
                }, function (error) {
                    console.log(error);
                    $toastService.create("Error Occurred While getting Employee Payment Cards...!");
                    $scope.employeePaymentCards = [];
                });
            };

            $scope.setPrType = function (type) {
                $scope.prRequestType = type;
                $timeout(function () {
                    $('#AdpVendorList').val('').trigger('change');
                    $('#advanceTypeList').val('').trigger('change');
                });
                $scope.advancePaymentVendor = null;
                $scope.advanceType = null;
            };

            $scope.setAdvancePaymentVendor = function (vendor) {
                $scope.enterredText = null;
                $scope.advanceUploadedDoc = null;
                if (!appUtil.isEmptyObject(vendor)) {
                    $scope.advancePaymentVendor = vendor;
                    metaDataService.getVendorDetail(vendor.id, function (vendorData) {
                        if (vendorData.vendorBlocked != undefined && vendorData.vendorBlocked != null && vendorData.vendorBlocked == 'Y') {
                            if (vendorData.blockedReason == 'MANUAL') {
                                $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                                    "<br><b>Please Contact Finance Team..!</b>", function () {
                                }, true);
                            } else {
                                $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                                    "<br><b>Please Settle the Vendor Advances related to advance payments of : " + vendorData.blockedReason + "</b>", function () {
                                }, true);
                            }
                            $scope.advancePaymentVendor = null;
                            $timeout(function () {
                                $('#AdpVendorList').val('').trigger('change');
                            });
                            return;
                        }
                    });
                    $timeout(function () {
                        $('#advanceTypeList').val('').trigger('change');
                        $scope.advanceType = null;
                    });
                }
            };

            $scope.setSelectedVendor = function (vendorDetail) {
                $scope.blockedAdvancePayments = [];
                if (!appUtil.isEmptyObject(vendorDetail) && vendorDetail.id != undefined && vendorDetail.id != null) {
                    metaDataService.getVendorDetail(vendorDetail.id, function (vendor) {
                        if (vendor.vendorBlocked != undefined && vendor.vendorBlocked != null && vendor.vendorBlocked == 'Y') {
                            if (vendor.blockedReason == 'MANUAL') {
                                $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                                    "<br><b>Please Contact Finance Team..!</b>", function () {
                                }, true);
                                $scope.selectedVendor = null;
                                $timeout(function () {
                                    $('#vendorListx').val('').trigger('change');
                                });
                                return;
                            } else {
                                $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                                    "<br><b>Only GR's's Related to these Advance are allowed to process further " + vendor.blockedReason + "</b>", function () {
                                }, false);
                                $scope.blockedAdvancePayments = JSON.parse(vendor.blockedReason);
                            }
                        }
                    });
                }
            };

            $scope.checkBlockedAdvanceForPayment = function (gr) {
                if (gr.vendorAdvancePayments == null) {
                    return false;
                } else if (gr.vendorAdvancePayments.length == 0) {
                    return false;
                } else {
                    for (var i = 0; i < gr.vendorAdvancePayments.length; i++) {
                        if ($scope.blockedAdvancePayments.indexOf(gr.vendorAdvancePayments[i].advancePaymentId) != -1) {
                            return true;
                        }
                    }
                    return false;
                }
            };

            $scope.setEnterredText = function (text) {
                $scope.enterredText = text;
            };

            $scope.setAdvanceType = function (advType) {
                $scope.enterredText = null;
                if (advType != null) {
                    $scope.advanceType = advType;
                    $scope.currentVendorAdvancePayment = null;
                    $scope.advanceAmount = null;
                    if (appUtil.isEmptyObject($scope.advanceType)) {
                        $toastService.create("Please Select an Advance ..!");
                        return;
                    }
                    if (advType == "STAND_ALONE_ADVANCE") {
                        var vendorAdvancePayment = {
                            "vendorId": $scope.advancePaymentVendor.id,
                            "advanceStatus": "ALL",
                            "advanceType": advType
                        }
                        metaDataService.getVendorAdvancePayment(vendorAdvancePayment, function (advance) {
                            $scope.currentVendorAdvancePayment = advance;
                        }, function (response) {
                            if (response.data.errorMsg != null) {
                                $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                            } else {
                                $toastService.create("Error Occurred While Creating Advance Payment...!");
                                console.log("error:" + response);
                            }
                        });
                    } else {
                        $scope.poSoSelected = null;
                        $scope.getPendingPoSo(advType);
                    }
                }
            };

            $scope.disablePosSelection = function ($event, po, poSoList,isAdjustment) {
                $scope.poSoSelected = null;
                $scope.advanceUploadedDoc = null;
                if ($event.target.checked) {
                    po.disable = false;
                    if (isAdjustment != undefined && isAdjustment != null && isAdjustment) {
                        if (po.maxLimit < $scope.paymentRequest.advancePayment['finalAvailable']) {
                            $toastService.create("Can not adjust to the PO which has less amount than the remaining advance..!");
                            po.checked = false;
                            return;
                        }
                    }
                    angular.forEach(poSoList, function (poSo) {
                        if (po.id == poSo.id) {
                            poSo.disable =  false;
                            $scope.poSoSelected = po;
                        } else {
                            poSo.disable = true;
                        }
                    });
                } else {
                    $scope.poSoSelected = null;
                    angular.forEach(poSoList, function (poSo) {
                        poSo.disable = false;
                    });
                }
                $scope.advanceAmount = null;
            };

            $scope.setAdvanceAmount = function (advance) {
                $scope.advanceAmount = advance;
            };

            $scope.getPendingPoSo = function (advType, isAdjustment) {
                var url = null;
                $scope.errorMessage = null;
                if (advType == "PO_ADVANCE") {
                    url = apiJson.urls.paymentRequestManagement.getPosForAdvance;
                } else {
                    url = apiJson.urls.paymentRequestManagement.getSosForAdvance;
                }
                $http({
                    method: 'GET',
                    url: url,
                    params: {
                        vendorId: $scope.prRequestType.shortCode=='GR' ? $scope.selectedVendor.id : $scope.advancePaymentVendor.id
                    }
                }).then(function (response) {
                    $scope.pendingPoSo = response.data;
                    if (isAdjustment) {
                        var finalPoSo = [];
                        for (var i = 0; i < $scope.pendingPoSo.length; i++) {
                            if ($scope.pendingPoSo[i].id != $scope.cancelObject.poId) {
                                finalPoSo.push($scope.pendingPoSo[i]);
                            }
                        }
                        $scope.pendingPoSo = finalPoSo;
                    }
                    angular.forEach($scope.pendingPoSo, function (poSo) {
                        if (advType == "PO_ADVANCE") {
                            poSo.maxLimit = parseFloat((poSo.paidAmount + appUtil.getAdvanceBufferAmount(poSo.paidAmount)).toFixed(0));
                        } else {
                            poSo.maxLimit = parseFloat((poSo.totalAmount + appUtil.getAdvanceBufferAmount(poSo.totalAmount)).toFixed(0));
                        }
                    });
                }, function (response) {
                    console.log(response);
                    $scope.pendingPoSo = [];
                    if(response.data.errorMsg != null) {
                        $scope.errorMessage = response.data.errorMsg;
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    }else{
                        $toastService.create("Error Occurred While Creating Advance Payment...!");
                        console.log("error:" + response);
                    }
                });
            };

            function getAdvanceRequestObject() {
                var result = {
                    "vendorId" : $scope.advancePaymentVendor.id,
                    "prAmount" : $scope.advanceAmount,
                    "createdById" : appUtil.getCurrentUser().userId,
                    "advanceType" : $scope.advanceType,
                    "advanceDocId" : $scope.advanceUploadedDoc.documentId
                };
                if ($scope.advanceType == "PO_ADVANCE") {
                    result["poId"] = $scope.poSoSelected.id;
                }
                if ($scope.advanceType == "SO_ADVANCE") {
                    result["soId"] = $scope.poSoSelected.id;
                }
                return result;
            }

            $scope.createAdvancePayment = function () {
                if (appUtil.isEmptyObject($scope.advanceType)) {
                    $toastService.create("Please Select the advance Type..!");
                    return;
                } else {
                    if ($scope.advanceType != "STAND_ALONE_ADVANCE") {
                        if ($scope.poSoSelected == null) {
                            $toastService.create("Please Select at least One Item..!");
                            return;
                        }
                    }
                }

                if (appUtil.isEmptyObject($scope.advanceAmount)) {
                    $toastService.create("Please Enter the Advance Amount..!");
                    return;
                }
                if ($scope.advanceAmount <= 0) {
                    $toastService.create("Please Enter the Advance Amount greater than 0..!");
                    $scope.advanceAmount = null;
                    return;
                }
                if (appUtil.isFloat($scope.advanceAmount)) {
                    $toastService.create("No decimal value allowed..!");
                    $scope.advanceAmount = null;
                    return;
                }
                if ($scope.advanceUploadedDoc == null) {
                    $toastService.create("Please Upload the Document ..!");
                    return;
                }
                var msg = "Do You Want to Create Advance Payment of <b>Rs. " + $scope.advanceAmount;
                if ($scope.advanceType != "STAND_ALONE_ADVANCE") {
                    if ($scope.advanceType == "PO_ADVANCE") {
                        msg += " </b>For Purchase Order Id : " + $scope.poSoSelected.id + "( " + $scope.advancePaymentVendor.name +" )";
                        if ($scope.advanceAmount > $scope.poSoSelected.maxLimit) {
                            $toastService.create("Please Enter Amount less than or equal to the Maximum Limit amount of PO Selected : " + $scope.poSoSelected.maxLimit);
                            $scope.advanceAmount = null;
                            $('#advAmount').val(null);
                            return;
                        }
                    } else {
                        msg += " </b>For Service Order Id : " + $scope.poSoSelected.id + "( " + $scope.advancePaymentVendor.name +" )";
                        if ($scope.advanceAmount > $scope.poSoSelected.maxLimit) {
                            $toastService.create("Please Enter Amount less than or equal to the Maximum Limit amount of SO Selected : " + $scope.poSoSelected.maxLimit);
                            $scope.advanceAmount = null;
                            $('#advAmount').val(null);
                            return;
                        }
                    }
                }
                $alertService.confirm("Are you sure ?",msg, function (result) {
                    if (result) {
                        var obj = getAdvanceRequestObject();
                        $http({
                            method: "POST",
                            url: apiJson.urls.paymentRequestManagement.createVendorAdvancePayment,
                            data: obj
                        }).then(function success(response) {
                            if (response != null && response.data != null) {
                                $timeout(function () {
                                    $('#AdpVendorList').val('').trigger('change');
                                });
                                $scope.advancePaymentVendor = null;
                                $scope.advanceAmount = null;
                                $alertService.alert("Payment Request creation successful",
                                    "Payment request created successfully with request id " + response.data.paymentRequestId, function () {
                                        $scope.setAdvancePaymentVendor(null);
                                    });
                            } else {
                                $toastService.create("Error Occurred While Creating Advance Payment...!");
                            }
                        } , function error(response) {
                            console.log("error:" + response);
                            $scope.currentVendorAdvancePayment = null;
                            if(response.data.errorMsg != null) {
                                if (response.data.errorTitle == "Advance Payment Already Created..!") {
                                    $alertService.alert(response.data.errorTitle, response.data.errorMsg,$scope.setAdvanceType($scope.advanceType), true);
                                } else {
                                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                                }
                            }else{
                                $toastService.create("Error Occurred While Creating Advance Payment...!");
                                console.log("error:" + response);
                            }
                        });
                    }
                });
            };

            $scope.getEmployeeMappedUnits = function () {
                appUtil.getUnitList().map(function (unit) {
                    if ($rootScope.mappedUnits.indexOf(unit.id) >= 0) {
                        $scope.unitList.push(unit);
                    }
                });
                if ($scope.unitList != null && $scope.unitList.length > 0) {
                    $scope.selectedUnit = $scope.unitList[0];
                }
            };

            $scope.selectUnit = function (unit) {
                $scope.selectedUnit = unit;
                $scope.getMappedVendors();
            };

            $scope.getMappedVendors = function () {
                console.log($scope.selectedUnit);
                if ($scope.selectedUnit.id !== null) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.skuMapping.getVendorsForUnitTrimmed + "?unitId=" + $scope.selectedUnit.id
                    }).then(function success(response) {
                        $scope.vendorList = [];
                        response.data.map(function (item) {
                            $scope.vendorList.push(item);
                        });
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                } else {
                    $scope.vendorList = [{
                        id: null,
                        name: ""
                    }];
                    $scope.selectedVendor = $scope.vendorList[0];
                }

            };

            $scope.handleTypeSwitch=function(grType) {
                $scope.grType = grType;
                if ($scope.isMilkPayment == false) {
                    $scope.init();
                }
                $scope.grs = [];
                $timeout(function () {
                    $('#unitListx').val('').trigger('change');
                    $('#vendorListx').val('').trigger('change');
                })
            }

            $scope.disableGrSelection = function ($event, gr, grs) {
                if (gr.checked) {
                    if (gr.vendorAdvancePayments != null && gr.vendorAdvancePayments.length > 0) {
                        angular.forEach(grs, function (item) {
                            item.checked = false;
                            item.disabled = true;
                            if (item.id == gr.id) {
                                item.checked = true;
                                item.disabled = false;
                            }
                        });
                    } else {
                        angular.forEach(grs, function (item) {
                            if (item.id == gr.id) {
                                item.checked = true;
                                item.disabled = false;
                            }
                            if (item.vendorAdvancePayments != null && item.vendorAdvancePayments.length > 0) {
                                item.checked = false;
                                item.disabled = true;
                            }
                        });
                    }
                } else {
                    var nonAdvanceSelected = false;
                    angular.forEach(grs, function (item) {
                        if (item.vendorAdvancePayments == null) {
                            if (item.checked) {
                                nonAdvanceSelected = true;
                            }
                        }
                    });
                    if (nonAdvanceSelected) {
                        angular.forEach(grs, function (item) {
                            if (item.vendorAdvancePayments != null && item.vendorAdvancePayments.length > 0) {
                                item.checked = false;
                                item.disabled = true;
                            }
                        });
                    } else {
                        angular.forEach(grs, function (item) {
                            item.checked = false;
                            item.disabled = false;
                        });
                    }
                }
            };

            $scope.findGrs = function () {
                var url = $scope.isMilkPayment == true ? apiJson.urls.goodsReceivedManagement.getMilkInvoicesForPayment :
                    apiJson.urls.goodsReceivedManagement.findVendorGRsForPayment;
                $scope.selectedGr = null;
                $scope.showNoGR = false;
                if ($scope.selectedUnit.id == null || $scope.selectedUnit.id == "") {
                    $toastService.create("Please select unit");
                } else if ($scope.startDate == null || $scope.startDate == "") {
                    $toastService.create("Please select start date");
                } else if ($scope.endDate == null || $scope.endDate == "") {
                    $toastService.create("Please select end date");
                } else if (appUtil.isEmptyObject($scope.selectedVendor) || $scope.selectedVendor.id == null || $scope.selectedVendor.id == "") {
                    $toastService.create("Please select vendor");
                }
                else {
                    $scope.grs = [];
                    $http({
                        method: 'GET',
                        url: url,
                        params: {
                            deliveryUnitId: $scope.selectedUnit.id,
                            startDate: $scope.startDate,
                            endDate: $scope.endDate,
                            vendorId: $scope.selectedVendor.id
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            if ($scope.isMilkPayment == false) {
                                $scope.getFilteredGrsByType(response.data);
                            } else {
                                $scope.grs = response.data;
                            }
                        } else {
                            $scope.showNoGR = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            };

            $scope.openInvoice = function (invoice) {
                $window.open(invoice.invoiceUrl, '_blank');
            }

            $scope.getFilteredGrsByType = function (data) {
                $scope.grs = [];
                data.map(function (gr) {
                    if (gr.receiptType === $scope.grType) {
                        $scope.grs.push(gr);
                    }
                });
                if ($scope.grs.length === 0) {
                    $scope.showNoGR = true;
                }
            };

            $scope.backToSelectView = function () {
                $scope.selectView = true;
                $scope.advanceSelectionView = false;
                $scope.findGrs();
            };

            $scope.createPR = function (gr, forceCreated) {
                $scope.eligibleForCardPayment = false;
                $scope.selectedGr = gr;
                $scope.poSoSelected = null;
                if ($scope.selectedGr.validForPR === false) {
                    $toastService.create("Asset " + gr.id + " Status is Still in INITIATED state");
                    return;
                }
                if ($scope.isMilkPayment == true) {
                    $scope.createPRForMilk(gr, forceCreated);
                } else {
                    $scope.uploadedDocData = null;
                    var invoiceItems = [];
                    gr.grItems.map(function (item) {
                        addNewInvoiceItem(invoiceItems, item);
                    });
                    $scope.createdByUnit = {
                        id: $scope.selectedGr.deliveryUnitId.id,
                        code: "",
                        name: $scope.selectedGr.deliveryUnitId.name
                    };
                    createPaymentRequestObject(forceCreated);
                    addGrDataToInvoice(gr);
                    $scope.paymentRequest.paymentInvoice.paymentInvoiceItems = invoiceItems;
                    $scope.paymentRequest.paymentInvoice.invoiceNumber = gr.receiptNumber;
                    $scope.paymentRequest.paymentInvoice.invoiceDate = appUtil.formatDate(gr.grDocumentDate, "yyyy-MM-dd");
                    addGrItemMapping(gr.id);
                    $scope.invoiceNumber = gr.receiptNumber;
                    $scope.createdByUser = appUtil.createGeneratedBy();
                    $scope.invoiceDate = appUtil.formatDate(gr.grDocumentDate, "yyyy-MM-dd");
                    $scope.selectView = false;
                    if (gr.type !== undefined && gr.type !== null && gr.type === "OPEX") {
                        $scope.eligibleForCardPayment = true;
                    }
                }

            };

            $scope.openModalForQty = function (item) {
                item.goodsReceived.invoiceId = item.specializedOrderInvoiceId;
                openFinalQtyModal(item.goodsReceived, item);
            }

            $scope.createPRForMilk = function (item, forceCreated) {
                var gr = item.goodsReceived;
                $scope.selectedGr = gr;
                if ($scope.selectedGr.validForPR === false) {
                    $toastService.create("Asset " + gr.id + " Status is Still in INITIATED state");
                    return;
                } else {
                    $scope.uploadedDocData = null;
                    var invoiceItems = [];
                    var totalAmount = 0;
                    gr.goodsReceivedItems.map(function (item) {
                        totalAmount += ((item.unitPrice * item.receivedQuantity) + item.taxAmount);
                        addNewInvoiceItemForMilkPR(invoiceItems, item);
                    });
                    gr.totalAmount = Math.round(totalAmount);
                    $scope.createdByUnit = {
                        id: $scope.selectedGr.generationUnitId.id,
                        code: "",
                        name: $scope.selectedGr.generationUnitId.name
                    };
                    createPaymentRequestObjectForMilkPR(forceCreated);
                    addGrDataToInvoiceForMilkPR(gr);
                    $scope.paymentRequest.paymentInvoice.paymentInvoiceItems = invoiceItems;
                    //$scope.paymentRequest.paymentInvoice.invoiceNumber = gr.receiptNumber;
                    $scope.paymentRequest.paymentInvoice.invoiceDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                    //addGrItemMapping(gr.id);
                    // $scope.invoiceNumber = gr.receiptNumber;
                    $scope.createdByUser = appUtil.createGeneratedBy();
                    $scope.invoiceDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                    $scope.selectView = false;
                }

            };


            $scope.setAvailableDeviations = function (item, type) {
                $scope.availableInvoiceDevs = [];
                $scope.availableItemDevs = [];
                var devList, availableList = [];
                if (type === "INVOICE") {
                    devList = $scope.invoiceDeviations;
                } else {
                    devList = $scope.itemDeviations;
                }
                devList.map(function (iDev) {
                    var found = false;
                    item.deviations.map(function (dev) {
                        if (iDev.paymentDeviationId === dev.paymentDeviation.paymentDeviationId) {
                            found = true;
                        }
                    });
                    if (!found) {
                        availableList.push({data: iDev});
                    }
                });
                if (type === "INVOICE") {
                    $scope.availableInvoiceDevs = availableList;
                } else {
                    $scope.selectedItemForDeviation = item;
                    $scope.availableItemDevs = availableList;
                }
            };

            $scope.addDeviations = function (item, type) {
                var devList;
                if (type === "INVOICE") {
                    devList = $scope.availableInvoiceDevs;
                } else {
                    devList = $scope.availableItemDevs;
                }
                devList.map(function (dev) {
                    if (dev.checked) {
                        item.deviations.push({
                            mappingId: null,
                            paymentDeviation: dev.data,
                            deviationRemark: dev.remark,
                            currentStatus: "CREATED",
                            createdBy: appUtil.createGeneratedBy()
                        });
                    }
                });
            };

            $scope.removeDeviation = function (list, index) {
                list.splice(index, 1);
            };

            $scope.showDeviationInput = function (item) {
                item.showDeviationInput = true;
            };

            $scope.submitPaymentRequest = function (isValidate) {
                if ($scope.paymentRequest.grDocType == "INVOICE" && $scope.paymentRequest.paymentInvoice.invoiceNumber == null) {
                    $toastService.create("Please provide invoice number.");
                } else if ($scope.paymentRequest.grDocType == "INVOICE" && $scope.paymentRequest.paymentInvoice.invoiceDate == null) {
                    $toastService.create("Please provide invoice date.");
                } else if ($scope.paymentRequest.grDocType == "INVOICE" && $scope.uploadedDocData == null) {
                    $toastService.create("Please attach invoice document.");
                } else {
                    if ($scope.paymentRequest.paymentCardCheck != undefined && $scope.paymentRequest.paymentCardCheck != null && $scope.paymentRequest.paymentCardCheck) {
                        if (appUtil.isEmptyObject($scope.paymentRequest.paymentCard)) {
                            $toastService.create("Please Select the Payment Card ..!");
                            return;
                        }
                        if (appUtil.isEmptyObject($scope.paymentRequest.cardPaymentTransactionNumber)) {
                            $toastService.create("Please Enter the Payment Transaction Number ..!");
                            return;
                        }
                        if (appUtil.isEmptyObject($scope.uploadedCardPaymentProof)) {
                            $toastService.create("Please Upload the Card Payment Proof ..!");
                            return;
                        } else {
                            $scope.paymentRequest.cardPaymentProof = $scope.uploadedCardPaymentProof.documentId;
                        }
                    }
                    $scope.paymentRequest.deviationCount = $scope.paymentRequest.paymentInvoice.deviations.length;
                    $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.map(function (item) {
                        $scope.paymentRequest.deviationCount += item.deviations.length;
                    });
                    if ($scope.paymentRequest.grDocType == "INVOICE") {
                        $scope.paymentRequest.paymentInvoice.invoiceDocumentHandle = $scope.uploadedDocData.documentId;
                        //$scope.paymentRequest.paymentInvoice.invoiceDate = $scope.invoiceDate;
                    }
                    var milkInvoiceId = null;
                    if ($scope.paymentRequest.type == "MILK_BAKERY") {
                        milkInvoiceId = $scope.selectedGr.invoiceId;
                    }
                    if (!isValidate) {
                        $scope.paymentRequest.duplicatePaidAmount = angular.copy($scope.paymentRequest.proposedAmount);
                        if ($scope.selectedGr.vendorAdvancePayments != undefined && $scope.selectedGr.vendorAdvancePayments != null && $scope.selectedGr.vendorAdvancePayments.length>0) {
                            var advIds = [];
                            for (var i = 0; i < $scope.selectedGr.vendorAdvancePayments.length; i++) {
                                advIds.push($scope.selectedGr.vendorAdvancePayments[i].advancePaymentId);
                            }
                            $scope.paymentRequest.advancePaymentIds = advIds;
                            if ($scope.poSoSelected != null) {
                                $scope.paymentRequest.advancePayment.selectedSoPo = $scope.poSoSelected.id;
                            }
                            $scope.paymentRequest.proposedAmount = $scope.paymentRequest.finalProposedAmount;
                            $scope.paymentRequest.paidAmount = $scope.paymentRequest.finalProposedAmount;
                        }
                        $http({
                            url: apiJson.urls.paymentRequestManagement.paymentRequest,
                            method: 'POST',
                            data: $scope.paymentRequest,
                            params: {milkInvoiceId: milkInvoiceId}
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response != null) {
                                $alertService.alert("Payment Request creation successful",
                                    "Payment request created successfully with request id " + response.paymentRequestId, function () {
                                        if ($scope.deviationGrItems.length > 0) {
                                            saveGrPrDeviations(response.paymentRequestId);
                                        }
                                        $scope.findGrs();
                                        $scope.backToSelectView();

                                    })
                                //$toastService.create("Payment Request creation successful.");
                            } else {
                                $toastService.create("Payment Request creation failed.");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $alertService.alert("Payment Request creation failed", response.errorMsg, function () {
                            }, true)
                        });
                    } else {
                        $scope.advanceSelectionView = true;
                        $scope.selectView = false;
                        $scope.adjustAdvanceAmount();
                    }
                }
            };

            $scope.goToPrEditScreen = function () {
                $scope.paymentRequest.proposedAmount = angular.copy($scope.paymentRequest.paidAmount);
                $scope.advanceSelectionView = false;
                $scope.selectView = false;
            };


            $scope.setRefundDate = function (refundDate) {
                $scope.paymentRequest.advancePayment.refundDate = refundDate;
            };

            $scope.setPoClosed = function (flag) {
                $scope.paymentRequest.advancePayment.poSoClosed = flag;
            };

            $scope.setAdjustOrRefund = function (flag) {
                $scope.paymentRequest.advancePayment.adjustOrRefund = flag;
                $scope.paymentRequest.advancePayment.refundDate = null;
                $scope.enterredText = null;
                if ($scope.paymentRequest.advancePayment.adjustOrRefund == 'Adjust') {
                    $scope.getPendingPoSo("PO_ADVANCE", true);
                }
            };

            $scope.adjustAdvanceAmount = function () {
                $scope.showPoSoClose = false;
                $scope.paymentRequest.advancePayment = {};
                $scope.paymentRequest.advancePayment = angular.copy($scope.selectedGr.vendorAdvancePayments[0]);
                $scope.paymentRequest.advancePayment.linkedAdvancePayments = angular.copy($scope.selectedGr.vendorAdvancePayments);
                var available = 0;
                var blocked = 0;
                var totalAdvanceAmount = 0;
                for (var i = 0; i < $scope.selectedGr.vendorAdvancePayments.length ; i++) {
                    available = available + $scope.selectedGr.vendorAdvancePayments[i].availableAmount;
                    blocked = blocked + $scope.selectedGr.vendorAdvancePayments[i].blockedAmount;
                    totalAdvanceAmount = totalAdvanceAmount + $scope.selectedGr.vendorAdvancePayments[i].prAmount;
                }
                $scope.paymentRequest.advancePayment['advanceType'] = $scope.selectedGr.vendorAdvancePayments[0].advanceType;
                $scope.paymentRequest.advancePayment['totalAdvanceAmount'] = totalAdvanceAmount;
                $scope.paymentRequest.advancePayment['availableAmount'] = available;
                $scope.paymentRequest.advancePayment['blocked'] = blocked;
                $scope.paymentRequest.advancePayment['usedAmount'] = 0;
                $scope.paymentRequest.advancePayment['finalAvailable'] = 0;
                if (available >= $scope.paymentRequest.proposedAmount) {
                    $scope.paymentRequest.advancePayment['finalAvailable'] = parseFloat((available - $scope.paymentRequest.proposedAmount).toFixed(6));
                    $scope.paymentRequest.advancePayment['usedAmount'] = $scope.paymentRequest.proposedAmount;
                    $scope.paymentRequest.finalProposedAmount = 0;
                } else {
                    $scope.paymentRequest.advancePayment['finalAvailable'] = 0;
                    $scope.paymentRequest.advancePayment['usedAmount'] = available;
                    $scope.paymentRequest.finalProposedAmount = parseFloat(($scope.paymentRequest.proposedAmount - available).toFixed(6));
                }

                if ($scope.paymentRequest.advancePayment['finalAvailable'] > 0) {
                    $scope.showPoSoClose = true;
                }
            };

            $scope.initiatePR = function (forceCreated) {
                $scope.eligibleForCardPayment = false;
                var invoiceItems = [];
                $scope.poSoSelected = null;
                $scope.uploadedDocData = null;
                $scope.createdByUnit = {
                    id: $scope.grs[0].deliveryUnitId.id,
                    code: "",
                    name: $scope.grs[0].deliveryUnitId.name
                };
                $scope.selectedGrsDeliveryChallan = angular.copy($scope.grs).filter(function (gr) {
                    return gr.checked;
                });
                if ($scope.selectedGrsDeliveryChallan.length == 0) {
                    $toastService.create("Please Select atleast 1 GR");
                    return;
                }
                createPaymentRequestObject(forceCreated);
                $scope.grs.map(function (gr) {
                    if (gr.checked == true) {
                        if(gr.validForPR === false) {
                            $toastService.create("Some Assets in  " +gr.id +"  is Still in INITIATED state");
                            return ;
                        }
                        gr.grItems.map(function (grItem) {
                            //var matched = false;
                            var added = false;
                            invoiceItems.map(function (item) {
                                if (grItem.skuId == item.skuId && grItem.packagingId == item.packagingId && grItem.unitPrice == item.unitPrice && grItem.taxes.length == item.taxes.length) {
                                    //matched = true;
                                    grItem.taxes.map(function (grItemTax) {
                                        item.taxes.map(function (itemTax) {
                                            if(grItemTax.taxCategory == itemTax.taxType && grItemTax.percentage == itemTax.taxPercentage){
                                                grItemTax.matched = true;
                                            }
                                        });
                                    });
                                    var allMatched = true;
                                    grItem.taxes.map(function (grItemTax) {
                                        if(allMatched && grItemTax.matched != true){
                                            allMatched = false;
                                        }
                                    });
                                    if (allMatched) {
                                        addToExistingItem(item, grItem);
                                        added = true;
                                    } else {
                                        //addNewInvoiceItem(invoiceItems, grItem);
                                    }
                                }
                            });
                            if (!added) {
                                addNewInvoiceItem(invoiceItems, grItem);
                            }
                        });
                        addGrDataToInvoice(gr);
                        addGrItemMapping(gr.id);
                    }
                });
                $scope.createdByUser = appUtil.createGeneratedBy();
                $scope.paymentRequest.paymentInvoice.paymentInvoiceItems = invoiceItems;
                $scope.invoiceNumber = null;
                $scope.selectView = false;
            };

            $scope.downloadPRInvoice = function (prInvoice) {
                metaDataService.downloadDocument(prInvoice);
            };

            $scope.previewPRInvoice = function(prInvoice){
                if(!appUtil.isEmptyObject(prInvoice.documentLink)){
                    $http({
                        method:"POST",
                        url:apiJson.urls.vendorManagement.downloadDocument,
                        data: prInvoice,
                        responseType: 'arraybuffer'
                    }).then(function(response){
                        var arrayBufferView = new Uint8Array( response.data );
                        var blob = new Blob( [ arrayBufferView ], { type: appUtil.mimeTypes[prInvoice.mimeType] } );
                        var urlCreator = window.URL || window.webkitURL;
                        var imageUrl = urlCreator.createObjectURL( blob );
                        var preview = document.getElementById("invoicePreview");
                        preview.innerHTML = "";
                        var img = new Image();
                        img.src = imageUrl;
                        preview.appendChild(img);
                    }, function (error) {
                        $toastService.create("Could not download the document... Please try again");
                    });
                } else {
                    $toastService.create("Not a valid document... Please check");
                }
            };

            function saveGrPrDeviations(prId) {
                var data = angular.copy($scope.selectedGr);
                var items = [];
                for (var i = 0; i < $scope.deviationGrItems.length; i++) {
                    var item = angular.copy($scope.deviationGrItems[i]);
                    item.receivedQuantity = item.finalQty - item.receivedQuantity;
                    items.push(item);
                }
                data.goodsReceivedItems = items;
                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.saveGrPrDeviations,
                    data: data,
                    params: {
                        prId: prId
                    }

                }).then(function (response) {
                    $scope.findGrs();
                    console.log("SuccesFully Saved Gr Pr Deviations")
                }, function (error) {
                    $toastService.create("Could not Save Gr Pr Deviations... Please try again");
                });
            }

            /////////////////////document upload methods/////////////////////////////////

            $scope.resetScanModal = function () {
                $scope.imagesScanned = [];
                document.getElementById('images').innerHTML = "";
                var canvas = document.createElement('canvas');
                canvas.id = "scaleCanvas";
                document.getElementById('images').appendChild(canvas);
                $scope.uploadedDocData = null;
            };

            $scope.resetSnapModal = function () {
                $scope.snapRunning = false;
                if ($scope.localstream != null) {
                    $scope.localstream.getTracks()[0].stop();
                }
                $scope.uploadedDocData = null;
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                context.clearRect(0, 0, 640, 480);
            };

            $scope.startSnap = function () {
                var video = document.getElementById('video');
                // Get access to the camera!
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    // Not adding `{ audio: true }` since we only want video now
                    navigator.mediaDevices.getUserMedia({video: true}).then(function (stream) {
                        video.src = window.URL.createObjectURL(stream);
                        $scope.localstream = stream;
                        video.play();
                    });
                }
                $scope.snapRunning = true;
            };

            $scope.snapPicture = function () {
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                var video = document.getElementById('video');
                context.drawImage(video, 0, 0, 640, 480);
                video.pause();
                video.src = "";
                $scope.localstream.getTracks()[0].stop();
                $scope.snapRunning = false;
            };

            function dataURItoBlob(dataURI) {
                var byteString = atob(dataURI.split(',')[1]);
                var ab = new ArrayBuffer(byteString.length);
                var ia = new Uint8Array(ab);
                for (var i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                }
                return new Blob([ab], {type: 'image/png'});
            }

            $scope.uploadFile = function () {
                var canvas = document.getElementById('canvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            $scope.uploadDoc = function (isAdvance) {
                $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    if(file.size > 5120000){
                        $toastService.create('File size should not be greater than 5 MB.');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', appUtil.getCurrentUser().userId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                if (isAdvance) {
                                    $scope.advanceUploadedDoc = response;
                                } else {
                                    $scope.uploadedDocData = response;
                                }
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                    /*metaDataService.uploadFile("OTHERS","PAYMENT_REQUEST_INVOICE",file, function(doc){
                        $scope.uploadedDocData = doc;
                    });*/
                });
            };

            $scope.scanToPng = function () {
                scanner.scan($scope.displayImagesOnPage,
                    {
                        "output_settings": [
                            {
                                "type": "return-base64",
                                "format": "png"
                            }
                        ]
                    }
                );
            };

            $scope.displayImagesOnPage = function (successful, mesg, response) {
                if (!successful) { // On error
                    console.error('Failed: ' + mesg);
                    return;
                }
                if (successful && mesg != null && mesg.toLowerCase().indexOf('user cancel') >= 0) { // User cancelled.
                    console.info('User cancelled');
                    return;
                }
                var scannedImages = scanner.getScannedImages(response, true, false); // returns an array of ScannedImage
                $scope.imagesScanned = [];
                $scope.processScannedImage(scannedImages[0]);
                /*for(var i = 0; (scannedImages instanceof Array) && i < scannedImages.length; i++) {
                    var scannedImage = scannedImages[i];
                    $scope.processScannedImage(scannedImage);
                }*/
            };

            $scope.processScannedImage = function (scannedImage) {
                $scope.imagesScanned.push(scannedImage);
                scaleImage(scannedImage.src);
            };

            function scaleImage(src) {
                var MAX_WIDTH = 1000;
                var image = new Image();
                var canvas = document.getElementById("scaleCanvas");
                image.onload = function () {
                    //var canvas = document.getElementById("scaleCanvas");
                    if (image.width > MAX_WIDTH) {
                        image.height *= MAX_WIDTH / image.width;
                        image.width = MAX_WIDTH;
                    }
                    var ctx = canvas.getContext("2d");
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    canvas.width = image.width;
                    canvas.height = image.height;
                    ctx.drawImage(image, 0, 0, image.width, image.height);
                };
                image.src = src;
            }

            $scope.uploadScannedFile = function () {
                var canvas = document.getElementById('scaleCanvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            ///////////////////utility functions/////////////////////////////

            function addNewInvoiceItem(invoiceItems, item) {
                var invoiceItem = {
                    paymentInvoiceItemId: null,
                    skuId: item.skuId,
                    skuName: item.skuName,
                    hsn: item.hsn,
                    uom: item.unitOfMeasure,
                    packagingId: item.packagingId,
                    conversionRatio: item.conversionRatio,
                    quantity: item.receivedQuantity,
                    totalAmount: item.amountPaid,
                    totalTax: item.totalTax,
                    totalPrice: item.totalCost,
                    unitPrice: item.unitPrice,
                    packagingPrice: item.unitPrice * item.conversionRatio,
                    packagingName: item.packagingName,
                    taxes: [],
                    deviations: [],
                    showDeviationInput: true
                };
                item.taxes.map(function (tax) {
                    invoiceItem.taxes.push({
                        taxDetailId: null,
                        taxType: tax.taxCategory,
                        taxPercentage: tax.percentage,
                        taxValue: tax.value
                    });
                });
                invoiceItems.push(invoiceItem);
            }

            function addNewInvoiceItemForMilkPR(invoiceItems, item) {
                var invoiceItem = {
                    paymentInvoiceItemId: null,
                    skuId: item.skuId,
                    skuName: item.skuName,
                    hsn: item.hsnCode,
                    uom: item.uom,
                    packagingId: item.packagingDetails[0].packagingDefinitionData.packagingId,
                    conversionRatio: item.packagingDetails[0].packagingDefinitionData.conversionRatio,
                    quantity: item.receivedQuantity,
                    totalAmount: (item.unitPrice * item.receivedQuantity) + item.taxAmount,
                    totalTax: item.taxAmount,
                    totalPrice: item.unitPrice,
                    unitPrice: item.unitPrice,
                    packagingPrice: item.unitPrice * item.packagingDetails[0].packagingDefinitionData.conversionRatio,
                    packagingName: item.packagingDetails[0].packagingDefinitionData.packagingName,
                    taxes: [],
                    deviations: [],
                };
                item.taxes.map(function (tax) {
                    invoiceItem.taxes.push({
                        taxDetailId: null,
                        taxType: tax.code,
                        taxPercentage: tax.percentage,
                        taxValue: tax.value
                    });
                });
                invoiceItems.push(invoiceItem);
            }

            function addToExistingItem(item, grItem) {
                item.quantity += grItem.receivedQuantity;
                item.totalAmount += grItem.amountPaid;
                item.totalTax += grItem.totalTax;
                item.totalPrice += grItem.totalCost;
            }


            function addGrDataToInvoice(gr) {
                $scope.paymentRequest.proposedAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paidAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount += gr.billAmount;
                $scope.paymentRequest.paymentInvoice.extraCharges += gr.extraCharges;
                $scope.paymentRequest.paymentInvoice.invoiceAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paymentInvoice.paymentAmount += (gr.billAmount + gr.extraCharges);
            }

            function addGrDataToInvoiceForMilkPR(gr) {
                $scope.paymentRequest.proposedAmount += gr.totalAmount;
                $scope.paymentRequest.paidAmount += gr.totalAmount;
                $scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount += gr.totalAmount;
                $scope.paymentRequest.paymentInvoice.extraCharges += 0;
                $scope.paymentRequest.paymentInvoice.invoiceAmount += gr.totalAmount;
                $scope.paymentRequest.paymentInvoice.paymentAmount += gr.totalAmount;
            }

            function createPaymentRequestObject(forceCreated) {
                if ($scope.selectedGr == null && $scope.selectedGr == undefined) {
                    $scope.selectedGr = $scope.selectedGrsDeliveryChallan[0];
                }
                var invoice = {
                    paymentInvoiceId: null,
                    invoiceNumber: null,
                    invoiceDate: null,
                    invoiceDocumentHandle: "",
                    calculatedInvoiceAmount: 0,
                    extraCharges: 0,
                    invoiceAmount: 0,
                    paymentAmount: 0,
                    paymentInvoiceItems: null,
                    deviations: []
                };
                $scope.paymentRequest = {
                    paymentRequestId: null,
                    type: $scope.prRequestType.code,
                    vendorId: {id: $scope.selectedVendor.id},
                    createdBy: {id: appUtil.getCurrentUser().user.id},
                    state: {
                        name: $scope.selectedGr.deliveryUnitId.state,
                        code: $scope.selectedGr.deliveryUnitId.stateCode},
                    creationTime: null,
                    currentStatus: "CREATED",
                    lastUpdated: null,
                    paymentInvoice: invoice,
                    paymentCycle: null,
                    forceCreate : forceCreated,
                    paymentCycleDate: null,
                    proposedAmount: 0,
                    paidAmount: 0,
                    amountsMatch: $scope.amountsMatch,
                    blocked: false,
                    blockedBy: null,
                    requestingUnit: {id: $scope.createdByUnit.id},
                    grDocType: $scope.grType,
                    deviationCount: 0,
                    requestItemMappings: []
                };
            }

            function createPaymentRequestObjectForMilkPR(forceCreated) {
                if ($scope.selectedGr == null && $scope.selectedGr == undefined) {
                    $scope.selectedGr = $scope.selectedGrsDeliveryChallan[0];
                }
                var invoice = {
                    paymentInvoiceId: null,
                    invoiceNumber: null,
                    invoiceDate: null,
                    invoiceDocumentHandle: "",
                    calculatedInvoiceAmount: 0,
                    extraCharges: 0,
                    invoiceAmount: 0,
                    paymentAmount: 0,
                    paymentInvoiceItems: null,
                    deviations: []
                };
                var currentUnit = appUtil.getUnitData();
                $scope.paymentRequest = {
                    paymentRequestId: null,
                    type: "MILK_BAKERY",
                    vendorId: {id: $scope.selectedVendor.id},
                    createdBy: {id: appUtil.getCurrentUser().user.id},
                    state: {
                        name: currentUnit.location.state.name,
                        code: currentUnit.location.code
                    },
                    creationTime: null,
                    currentStatus: "CREATED",
                    lastUpdated: null,
                    paymentInvoice: invoice,
                    paymentCycle: null,
                    forceCreate: forceCreated,
                    paymentCycleDate: null,
                    proposedAmount: 0,
                    paidAmount: 0,
                    amountsMatch: $scope.amountsMatch,
                    blocked: false,
                    blockedBy: null,
                    requestingUnit: {id: $scope.createdByUnit.id},
                    grDocType: $scope.grType,
                    deviationCount: 0,
                    requestItemMappings: []
                };
            }

            function addGrItemMapping(grId) {
                $scope.paymentRequest.requestItemMappings.push({
                    id: null,
                    paymentRequestId: null,
                    paymentRequestType: $scope.prRequestType.code,
                    paymentRequestItemId: grId
                });
            }

            function openFinalQtyModal(selectedGr, invoiceItem) {
                $scope.deviationGrItems = [];
                var qtyPreviewModal = Popeye.openModal({
                    templateUrl: "grItemQty.html",
                    controller: "grItemQtyCtrl",
                    resolve: {
                        selectedGr: function () {
                            return selectedGr;
                        },
                        invoiceItem: function () {
                            return invoiceItem;
                        },
                        deviationGrItems: function () {
                            return $scope.deviationGrItems;
                        },
                        userId: function () {
                            return $scope.currentUser.userId;
                        },
                        vendorId: function () {
                            return $scope.selectedVendor.id;
                        }
                    },
                    modalClass: 'custom-modal',
                    click: false,
                    keyboard: false
                });

                qtyPreviewModal.closed.then(function (data) {
                    if (!appUtil.isEmptyObject(data) && data.success == true) {
                        $scope.deviationGrItems = data.deviationGrItems;
                        console.log("deviation items ::::", $scope.deviationGrItems);
                        invoiceItem.goodsReceived = data.gr;
                        $scope.createPRForMilk(invoiceItem, false);
                    }

                });
            }

        }]
    ).controller('grItemQtyCtrl', ['$scope', 'selectedGr', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window', 'invoiceItem', 'deviationGrItems', 'userId', 'vendorId',
        function ($scope, selectedGr, appUtil, $toastService, apiJson, $http, Popeye, $window, invoiceItem, deviationGrItems, userId, vendorId) {
            $scope.init = function () {
                $scope.selectedGr = selectedGr;
                $scope.invoiceItem = invoiceItem;
                $scope.invoiceGenerated = false;
                $scope.deviationGrItems = deviationGrItems;
                $scope.userId = userId;
                $scope.vendorId = vendorId;
                for (var i = 0; i < $scope.selectedGr.goodsReceivedItems.length; i++) {
                    $scope.selectedGr.goodsReceivedItems[i].originalReceivedQty = $scope.selectedGr.goodsReceivedItems[i].receivedQuantity;
                    $scope.selectedGr.goodsReceivedItems[i].finalQty = $scope.selectedGr.goodsReceivedItems[i].receivedQuantity;
                }
            }

            $scope.generateInvoice = function () {
                var isValid = $scope.checkForValidInput();
                if(isValid == false){
                    $toastService.create("Please Enter Final Qty in Multiple Of packaging !!");
                    return;
                }
                $scope.deviationGrItems = [];
                for (var i = 0; i < $scope.selectedGr.goodsReceivedItems.length; i++) {
                    if ($scope.selectedGr.goodsReceivedItems[i].originalReceivedQty != $scope.selectedGr.goodsReceivedItems[i].finalQty) {
                        $scope.deviationGrItems.push($scope.selectedGr.goodsReceivedItems[i]);
                    }
                }
                $scope.invoiceItem.goodsReceived = $scope.selectedGr;
                var gr = angular.copy($scope.selectedGr);
                for (var i = 0; i < gr.goodsReceivedItems.length; i++) {
                    gr.goodsReceivedItems[i].receivedQuantity = gr.goodsReceivedItems[i].finalQty;
                }
                if ($scope.deviationGrItems.length > 0) {
                    downloadInvoice(gr);
                } else {
                    $scope.invoiceGenerated = true;
                    $scope.submit();
                }
            }

            $scope.setInvoiceGenerated = function () {
                $scope.invoiceGenerated = true;
            }


            function downloadInvoice(gr) {
                var data = [];
                data.push(gr);

                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.generateAndSaveSpecializedOrderInvoice,
                    data: data,
                    params: {
                        userId: $scope.userId,
                        vendorId: $scope.vendorId,
                        isRegenerated: true
                    }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $toastService.create("Invoice uploaded");
                        $window.open(response.data.key, '_blank');
                        $scope.selectedGr = response.data.value;
                        $scope.invoiceGenerated = true;
                        $scope.submit();

                    }
                }, function (err) {
                    if (err.data.errorMsg != null) {
                        $toastService.create(err.data.errorMsg);
                    }
                    console.log("Encountered error at backend", err);
                });
            }

            $scope.submit = function () {
                Popeye.closeCurrentModal({success: true, deviationGrItems: $scope.deviationGrItems, gr: $scope.selectedGr});
            }


            $scope.closeModal = function () {
                Popeye.closeCurrentModal(false);
            };

            $scope.checkForValidInput = function (){
                var isValid = true;
                for (var i = 0; i < $scope.selectedGr.goodsReceivedItems.length; i++) {
                    var convertionRatio = $scope.selectedGr.goodsReceivedItems[i].packagingDetails[0].packagingDefinitionData.conversionRatio;
                    var finalQty = $scope.selectedGr.goodsReceivedItems[i].finalQty;
                    if(finalQty % convertionRatio != 0){
                        $scope.selectedGr.goodsReceivedItems[i].finalQty = $scope.selectedGr.goodsReceivedItems[i].originalReceivedQty ;
                        isValid = false;
                    }
                }
                return isValid;


            }
        }
    ]
);

