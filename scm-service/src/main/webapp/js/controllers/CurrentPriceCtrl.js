/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON><PERSON>
 */

'use strict';

angular.module('scmApp')
.controller('currentPriceCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService','previewModalService','$fileUploadService','$alertService',
	function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,previewModalService, $fileUploadService, $alertService) {
	$scope.init = function () {
		$scope.initializeGridOptions();
		$scope.scmUnitList = appUtil.getUnitList();
		$scope.scmProductDetails = appUtil.getActiveScmProducts();
		$scope.scmSkuDetails=[];                
		$scope.skuProductMap = appUtil.getSkuProductMap();
		$scope.scmSkuDetails=getScmSkuDetails($scope.skuProductMap);
		initializeParams();
        $scope.showPreview = previewModalService.showPreview;
	};
	
	function initializeParams(){
		$scope.showDetailsForm=false;
		$scope.showGetPriceBtn=true;
		$scope.priceDetailList=undefined;
		$scope.showPricedetailsForUnit=false;
		$scope.allPriceDetailListForUnit =undefined;
		$scope.costDetail = {
                id: null,
                unitId : null,
                quantity: null,
                price: null,
                uom: null,
                keyType:null,
                keyId: 0,
                lastUpdatedTimes: null
            };
	}

	function getScmSkuDetails(skuProductMap){
		for(var key in skuProductMap){
			var skuList=skuProductMap[key];
			skuList.forEach(function(sku){
				$scope.scmSkuDetails.push(sku);   
			});
		}
		return $scope.scmSkuDetails;
	}

	$scope.setSelectedProduct = function(selectedProduct){
		$scope.selectedProduct = JSON.parse(selectedProduct);
	};
	
	$scope.setSelectedSku=function(selectedSku){
		$scope.selectedSku = JSON.parse(selectedSku);	
	};
	
	$scope.setSelectedUnit=function(selectedUnit){
		if($scope.selectedProduct != undefined){
			var result = confirm("Are you sure? All details will be cleared if you change the unit");
			if(result){
				initializeParams();
			}
		}
		 $scope.selectedUnit = JSON.parse(selectedUnit);
		 $scope.isWHOrKitchen  = appUtil.isWarehouseOrKitchenByCategory($scope.selectedUnit);
	};
	
	$scope.getAllPricedetailsForUnit=function(){
		var url = apiJson.urls.pricedetails.getAllPricedetailsForUnit+"?unitId="+$scope.selectedUnit.id;
		$http({
			method: "GET",
			url: url
		}).then(function success(response) {
			$scope.showPricedetailsForUnit=true;
            $scope.allPriceDetailListForUnit = response.data;
			$scope.gridOptions.columnDefs = $scope.unitPriceGridColumns();
			$scope.gridOptions.data = $scope.allPriceDetailListForUnit;
		}, function error(response) {
			$toastService.create("Something went wrong. Please try again!");
			console.log("error:" + response);
		});
	};

	
	$scope.getPriceDetails=function(){
		if(appUtil.isEmptyObject($scope.selectedUnit)){
			$toastService.create("Please Select Unit!");
			return false;
		}
		
		if(!$scope.isWHOrKitchen && appUtil.isEmptyObject($scope.selectedProduct)){
			$toastService.create("Please Select Product!!");
			return false;
		}
		
		if($scope.isWHOrKitchen && appUtil.isEmptyObject($scope.selectedSku)){
			$toastService.create("Please Select Sku!!");
			return false;
		}
		
		var url = apiJson.urls.pricedetails.getPricedetails+"?unitId="+$scope.selectedUnit.id;
		if(!$scope.isWHOrKitchen){
			url+="&keyType=PRODUCT"+"&keyId="+$scope.selectedProduct.productId;
		}
		if($scope.isWHOrKitchen){
			url+="&keyType=SKU"+"&keyId="+$scope.selectedSku.skuId;
		}
		$http({
			method: "GET",
			url: url
		}).then(function success(response) {
			$scope.priceDetailList = response.data;
			$scope.weightedPrice = getWeightedPrice($scope.priceDetailList);
		}, function error(response) {
			$toastService.create("Something went wrong. Please try again!");
			console.log("error:" + response);
		});
	};

	function getWeightedPrice(prices) {
        var totalPrice = parseFloat(0);
	    var totalQty = parseFloat(0);
        var weightedPrice = parseFloat(0);

	    for(var i in prices){
	        var entry = prices[i];
            totalPrice += (parseFloat(entry.price) * parseFloat(entry.quantity));
            totalQty += parseFloat(entry.quantity);
        }

        if(totalQty<=0){
            weightedPrice = prices.filter(function (price) {
                return price.latest;
            })[0].price;
        }else {
            weightedPrice = parseFloat(totalPrice/totalQty).toFixed(6);
        }
        return weightedPrice;
    }

	$scope.updateDetails = function(priceData){
	    $scope.costDetail = angular.copy(priceData);
	    $scope.costDetail.oldPrice = $scope.costDetail.price;
	    $scope.addDetails("UPDATE");
    };
	
	$scope.addDetails=function(type){
		$scope.addType = type;
		$scope.showGetPriceBtn=false;
		$scope.showDetailsForm=true;
	};
	
	$scope.cancelAddDetails=function(){
		$scope.showGetPriceBtn=true;
		$scope.showDetailsForm=false;
	};

	$scope.bulkUploadOfPrices = function () {
		$fileUploadService.openFileModal("Upload Prices Sheet", "Find", function (file) {
			$scope.bulkUploadPrices(file);
		});
	};

	$scope.bulkUploadPrices = function (file) {
		$rootScope.showFullScreenLoader = true;
		var fd = new FormData(document.forms[0]);
		fd.append("file", file);
		$http({
			url: apiJson.urls.pricedetails.bulkUploadPrices,
			method: 'POST',
			data: fd,
			headers: {'Content-Type': undefined},
			transformRequest: angular.identity
		}).success(function (response) {
			$rootScope.showFullScreenLoader = false;
			$toastService.create("Bulk Prices Uploaded Successfully...!");
		}).error(function (response) {
			$rootScope.showFullScreenLoader = false;
			if (response.errorMsg != null) {
				$alertService.alert(response.errorTitle, response.errorMsg, null, true);
			} else {
				$toastService.create("Error in uploaded sheet.");
			}
		});
	};
	
	
	$scope.addPriceDetails=function(price,quantity,expiryDate){
        if(appUtil.isEmptyObject(price) || appUtil.isEmptyObject(quantity) || appUtil.isEmptyObject(expiryDate)){
			$toastService.create("Price, Quantity and Expiry Date are required!");
			return false;
		}

        if($scope.addType == "UPDATE"){
            updatePriceDetails();
        }else{
            addNewDetail();
        }
	};



	function addNewDetail() {
        $scope.costDetail.unitId = $scope.selectedUnit.id;
        if(!$scope.isWHOrKitchen){
            $scope.costDetail.keyType="PRODUCT";
            $scope.costDetail.keyId=$scope.selectedProduct.productId;
            $scope.costDetail.uom=$scope.selectedProduct.unitOfMeasure;
        }
        if($scope.isWHOrKitchen){
            $scope.costDetail.keyType="SKU";
            $scope.costDetail.keyId=$scope.selectedSku.skuId;
            $scope.costDetail.uom=$scope.selectedSku.unitOfMeasure;
            var found = false;
            $scope.priceDetailList.map(function (item) {
				if(item.latest == true){
					found = true;
					$scope.costDetail.oldPrice = item.price;
				}
            });
            if(!found){
            	$scope.costDetail.oldPrice = $scope.costDetail.price;
			}
        }
        var date = $scope.costDetail.expiryDate + " "+$scope.costDetail.expiryTime+":00:00";
        console.log( date, new Date(date));
        $scope.costDetail.expiryDate = new Date(date).getTime();
        $http({
            method: "POST",
            url: apiJson.urls.pricedetails.addPriceDetails,
            data: $scope.costDetail
        }).then(function success(response) {
            if (response.data) {
                initializeParams();
                $toastService.create("Price details added successfully!");
            }else{
                $toastService.create("Something went wrong. Please try again!");
            }
        }, function error(response) {
            $toastService.create("Something went wrong. Please try again!");
            console.log("error:" + response);
        });
    }

	
	function updatePriceDetails(){
		$http({
            method: "PUT",
            url: apiJson.urls.pricedetails.updatePriceDetails,
            data: $scope.costDetail
        }).then(function success(response) {
            if (response.data) {
            		initializeParams();
                    $toastService.create("Price details updated successfully!");
                }else{
                	 $toastService.create("Something went wrong. Please try again!");
                }
        }, function error(response) {
        	 $toastService.create("Something went wrong. Please try again!");
            console.log("error:" + response);
        });
	};
	
	$scope.initializeGridOptions = function () {
        $scope.gridOptions = {
            enableColumnMenus: false,
            saveFocus: false,
            enableFiltering: true,
        };
    };
    
    $scope.unitPriceGridColumns = function () {
        return [{
            field: 'costDetailId',
            name: 'costDetailId',
            displayName: 'Price Id'
        },{
            field: 'keyId',
            name: 'keyId',
            displayName: 'Item Id'
        }, {
            field: 'keyType',
            name: 'keyType',
            displayName: 'Item'
        }, {
            field: 'name',
            name: 'name',
            displayName: 'Name'
        }, {
            field: 'uom',
            name: 'uom',
            displayName: 'UOM'
        }, {
            field: 'quantity',
            name: 'quantity',
            displayName: 'Qty'
        },{
            field: 'price',
            name: 'price',
            displayName: 'Price'
        },{
            field: 'latest',
            name: 'latest',
            displayName: 'Current'
        }];
    };

}]);