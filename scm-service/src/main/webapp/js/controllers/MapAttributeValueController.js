/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 *
 * Created by shikhar on 23-05-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('mapAttrValueCtrl', ['$http', '$rootScope', '$scope', 'authService', '$location', '$state',
        'appUtil', '$stateParams', 'metaDataService', '$toastService', 'apiJson','productService',
        function ($http, $rootScope, $scope, authService, $location, $state, appUtil, $stateParams, metaDataService,
                  $toastService, apiJson,productService) {

            $scope.selectOptions = {width: '100%'};
            $scope.checkEmpty = appUtil.isEmptyObject;
            $scope.categories = [];
            $scope.selectedProfiles = [];
            $scope.mappedProfiles = [];
            $scope.categoryAttributeMappings = {};
            $scope.selectedMappingsList = {};
            $scope.attributeValueMap = {};
            $scope.categoryId = null;
            $scope.selectedCategory = null;
            $scope.selectedMasterValueList = {};
            $scope.attributeValueMappings = [];

            $scope.getAttribute = appUtil.getAttribute;

            function getAllCategories() {
                metaDataService.getCategories(function (definitions) {
                    $scope.categories = definitions;
                    $scope.selectedCategory = $scope.categories[0].categoryId;
                });
            }

            function getAllAttrMappings() {
                metaDataService.getCategoryAttributeMappings(function(mappings){
                    if (!appUtil.isEmptyObject(mappings)) {
                        $scope.categoryAttributeMappings = mappings;
                    }
                });
            }

            function populateAttrValues() {
                metaDataService.getAttributeValues(function(values){
                    $scope.attributeValueMap = values;
                });
            }

            function getMappedValues(categoryId) {
                if(!appUtil.checkEmpty(categoryId) && !appUtil.isEmptyObject($scope.attributeValueMappings)){
                    $scope.mappedProfiles = $scope.attributeValueMappings[categoryId];
                }else{
                    $scope.mappedProfiles = [];
                }
            }

            $scope.init = function () {
                getAllCategories();
                getAllAttrMappings();
                populateAttrValues();
                getCategoryAttributeValues();
            };

            function getActiveMappings(mappingList){
                var returnList = mappingList.filter(function(mapping){
                   return mapping.mappingStatus == "ACTIVE";
                });
                console.log("size of mapping list of active attribute mappings",returnList.length);
                return returnList;
            }

            $scope.selectCategory = function (categoryId) {
                $scope.selectedCategory = categoryId;
                $scope.selectedMappingsList = getActiveMappings($scope.categoryAttributeMappings[categoryId]);
                $scope.selectedMasterValueList = {};
                console.log("selected mapping list :::: ",$scope.selectedMappingsList);
                $scope.selectedMappingsList.forEach(function (attribute) {
                    var attributeId = attribute.attributeDefinition.id;
                    var valueList = $scope.attributeValueMap[attributeId];
                    if(!appUtil.isEmptyObject(valueList)) {
                        $scope.selectedMasterValueList[attributeId] = valueList;
                        $scope.selectedMasterValueList[attributeId].forEach(function (attributeValue) {
                            attributeValue.categoryAttributeMappingId = attribute.categoryAttributeMappingId;
                        });
                    }
                });
                console.log("master list is :::",$scope.selectedMasterValueList);

                getMappedValues(categoryId);
            };

            $scope.checkIfExists = function (list, item, index) {
                var flag = true;
                for (var pos in list) {
                    if (list[pos].attributeValueId == item.attributeValueId) {
                        flag = false;
                    }
                }
                return flag;
            };

            $scope.onDrop = function (list, item) {
                list[item.attributeValueId] = item;
            };

            $scope.removeFromSelected = function (profiles, id) {
                delete profiles[id];
            };

            function prepareMappings(selectedProfiles) {
                var returnData = [];
                $scope.selectedProfiles.forEach(function (profile) {
                    returnData.push({
                        mappingStatus : "ACTIVE",
                        categoryAttributeMappingId: profile.categoryAttributeMappingId,
                        attributeValue: {id:profile.attributeValueId}
                    });
                });
                return returnData;
            }

            function getCategoryAttributeValues() {
                productService.getAllCategoryValues(function(response){
                    $scope.attributeValueMappings = response;
                    getMappedValues($scope.categoryId);
                });
            }

            $scope.getProfileName = function(valueId){
                for(var key in $scope.attributeValueMap){
                    var values = $scope.attributeValueMap[key];
                    for(var index in values){
                        if(values[index].attributeValue.id == valueId){
                            return values[index].attributeValue;
                        }
                    }
                }
                return "Not found";
            };

            $scope.submit = function () {
                if (!appUtil.isEmptyObject($scope.selectedProfiles) && !appUtil.checkEmpty($scope.categoryId)) {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.categoryManagement.attributeValueMapping,
                        data: prepareMappings($scope.selectedProfiles)
                    }).then(function (response) {
                        console.log(response.data);
                        if (response.data) {
                            $toastService.create("Added values to category successfully", function () {
                              getCategoryAttributeValues();
                            });
                        }else{
                            $toastService.create("Could not add attribute values to category. Try again later!");
                        }
                    }, function (response) {
                        console.log(response);
                    });
                } else {
                    if(appUtil.checkEmpty($scope.categoryId)){
                        $toastService.create("Please select a mapping category first!");
                    }
                    if(appUtil.isEmptyObject($scope.selectedProfiles)){
                        $toastService.create("Please select a attribute value to map first");
                    }
                }
            };


            $scope.updateStatus = function(id,activate) {
                var requestUrl = activate ? apiJson.urls.categoryManagement.activateCategoryAttributeValue
                    : apiJson.urls.categoryManagement.deactivateCategoryAttributeValue;
                $http({
                    method:"PUT",
                    url: requestUrl,
                    data: id
                }).then(function (response) {
                    var message = !response.data ? "Update failed. Try again later." : "Update Successful.";
                    $toastService.create(message, function () {
                        if(response.data){getCategoryAttributeValues();}
                    });
                }, function (response) {
                    console.log("Error in posting data", response);
                });
            };
        }
    ]
);



