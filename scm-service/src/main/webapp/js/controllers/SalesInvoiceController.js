angular.module('scmApp')
    .controller('invoiceCreateCtrl', ['$rootScope', '$scope', '$state', 'apiJson', '$http', 'appUtil', 'metaDataService',
            '$toastService', '$alertService', 'previewModalService', '$timeout', 'Popeye', '$fileUploadService','$stateParams',
            'ScmApiService', 'toast',
            function ($rootScope, $scope, $state, apiJson, $http, appUtil, metaDataService,
                      $toastService, $alertService, previewModalService, $timeout, Popeye, 
                      $fileUploadService,$stateParams, ScmApiService, toast ) {
                function getKey(skuId, pkgId) {
                    return skuId + "-" + pkgId;
                }

                $scope.returnInvoice = $stateParams.returnInvoice;
                $scope.init = function () {
                    $scope.availableBillingType = ["REGISTERED_ADDRESS", "DELIVERY_ADDRESS"];
                    // $scope.businessTypeList = [{
                    //     id: 1,
                    //     name: 'ECOM'
                    // }, {
                    //     id: 2,
                    //     name: 'B2B_SALES'
                    // }, {
                    //     id: 3,
                    //     name: 'CHAAYOS'
                    // }];
                    metaDataService.getBusinessTypes(function (businessType) {
                        $scope.businessTypeList = businessType;
                    });
                    $scope.vendorList = [];
                    $scope.selectedBillingType = $scope.availableBillingType[0];
                    $scope.scannedAssetTagValue = "";
                    $scope.isForFixedAsset = false;
                    $scope.select2Options = {'width': "100%"};
                    // $scope.txnTypes = ["SCRAP", "RETURN_TO_VENDOR", "B2B_SALES", "ECOM"];
                    // $scope.businessTypes = ["ECOM","B2B_SALES","CHAAYOS"];
                    $scope.invoiceTypes = ["INVOICE"]; //["DELIVERY_CHALLAN","INVOICE"];
                    $scope.minDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                    $scope.maxDate = appUtil.formatDate(appUtil.getDate(5), "yyyy-MM-dd");
                    $scope.locationList = [];
                    $scope.deliveryLocations = [];
                    $scope.billingLocations = [];
                    //  $scope.message = [];
                    $scope.currentUnit = appUtil.getUnitData();
                    $scope.invoiceItems = null;
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.selectedTransportMode = null;
                    // $scope.changeType($scope.txnType);
                    // $scope.changeInvoiceType($scope.invoiceTypes[0]);
                    $scope.getTransportModes();
                    $scope.isForFixedAsset = false;
                    $scope.isForB2BInvoiceReversal = $scope.returnInvoice;
                    getAssetFromUnit(appUtil.getCurrentUser().unitId);
                    $scope.selectedDeliveryLocation = null;
                    $scope.selectedBillingLocation = null;
                    $scope.referenceInvoiceNumber = null;

                    $scope.gstTypes = [];
                    $scope.gstType = null;
                    getGstTypes();
                };

                $scope.toggleFixedAssetFlag = function () {

                };

                $scope.setReferenceInvoiceNumber = function (referenceNumber){
                    $scope.referenceInvoiceNumber = referenceNumber;
                }

                $scope.changeBusinessType = function(businessType){
                    $scope.txnType = null;
                    clearGstInvoiceType();
                    if(businessType.name == "ECOM" && !$scope.isForB2BInvoiceReversal){
                        $scope.txnTypes = ["B2B_SALES"];
                    }
                    else if(businessType.name == "B2B_SALES" && !$scope.isForB2BInvoiceReversal){
                        $scope.txnTypes = ["B2B_SALES"];
                    }
                    else if(businessType.name == "ECOM" && $scope.isForB2BInvoiceReversal){
                        $scope.txnTypes = ["B2B_RETURN"];
                    }
                    else if(businessType.name == "B2B_SALES" && $scope.isForB2BInvoiceReversal){
                        $scope.txnTypes = ["B2B_RETURN"];
                    }
                    else if(businessType.name == "RETAIL" && !$scope.isForB2BInvoiceReversal){
                        $scope.txnTypes = ["SCRAP", "RETURN_TO_VENDOR"];
                    }

                    $http({
                        url: apiJson.urls.skuMapping.getVendorForBusiness,
                        method: 'POST',
                        data: businessType.id
                    }).then(function (response) {
                        if(!appUtil.isEmptyObject(response)){
                            $scope.filteredVendorList = response.data ;
                        }else {
                            $toastService.create("Could not find vendors mapped to the selected BusinessType");
                        }
                    }, function (response) {
                        console.log("got error", response);
                    });
                }

                function getGstTypes() {
                    ScmApiService.get(apiJson.urls.invoiceManagement.getGstTypes)
                        .then(function (data) {
                            if (appUtil.isEmptyObject(data)) {
                                toast.error("Error loading Vendor gst types.");
                            } else if(appUtil.isEmptyObject(data.data)) {
                                toast.warning("No gst types found!.");
                            } else {
                                $scope.gstTypes = data.data;
                            }
                        });
                }

                $scope.cloneInvoice = function () {
                    if (appUtil.isEmptyObject($scope.clonableInvoices)) {
                        $http({
                            method: "GET",
                            url: apiJson.urls.invoiceManagement.closedInvoices,
                            params: {
                                vendorId: $scope.selectedVendor.vendorId,
                                sendingUnit: $scope.currentUnit.id,
                                dispatchId: $scope.selectedDeliveryLocation.dispatchId
                            }
                        }).then(function (response) {
                            $scope.clonableInvoices = response.data;
                        }, function (error) {
                            console.log(error);
                        });
                    }
                };

                $scope.validateAndSaveDocket = function (docketNumber) {
                    $scope.docketNumber = docketNumber.toUpperCase();
                };

                $scope.cloneThisInvoice = function (orderItems) {
                    $alertService.confirm("Are you sure?", "", function (result) {
                        if (result) {
                            $rootScope.showFullScreenLoader = true;
                            recalculate(orderItems);
                            $toastService.create("Items added to the list. Please close to see the changes");
                            $rootScope.showFullScreenLoader = false;
                        }
                    });
                };

                $scope.onScan = function () {
                    console.log($scope.scannedAssetTagValue);
                    if ($scope.scannedAssetTagValue == null || $scope.scannedAssetTagValue.length != 6) {
                        return;
                    }
                    console.log($scope.scannedAssetTagValue);
                    if ($scope.invoiceItems == null || $scope.invoiceItems.length == 0) {
                        $toastService.create("Please select sku ");
                        resetScannedAssetTagValue();
                        return;
                    }
                    /*
                        Search for asset in available assets
                     */
                    var assetId = null;
                    var associatedSKUId = null;
                    for (var index in $scope.availableAssets) {
                        if ($scope.availableAssets[index].tagValue == $scope.scannedAssetTagValue) {
                            assetId = $scope.availableAssets[index].assetId;
                            associatedSKUId = $scope.availableAssets[index].skuId;
                            break;
                        }
                    }
                    if (assetId == null) {
                        $toastService.create("Asset tag value is invalid");
                        resetScannedAssetTagValue();
                        return;
                    }
                    var foundSku = false;
                    for (var i in $scope.invoiceItems) {
                        var item = $scope.invoiceItems[i];

                        console.log(item);
                        if (item.sku.id == associatedSKUId) {
                            foundSku = true;
                            var associated = false;
                            for (var j in item.associatedAssetMappings) {
                                var assetMapping = item.associatedAssetMappings[j];
                                if (assetMapping.assetId == assetId && assetMapping.assetValidated == true && assetMapping.assetTagValue == $scope.scannedAssetTagValue) {
                                    $toastService.create("Asset already added ");
                                    resetScannedAssetTagValue();
                                    return;
                                }
                            }
                            for (var j in item.associatedAssetMappings) {
                                var assetMapping = item.associatedAssetMappings[j];
                                if (assetMapping.assetId == null || assetMapping.assetValidated == false) {
                                    assetMapping.assetId = assetId;
                                    assetMapping.assetValidated = true;
                                    associated = true;
                                    assetMapping.assetTagValue = $scope.scannedAssetTagValue;
                                    $toastService.create("Asset successfully added ");
                                    resetScannedAssetTagValue();
                                    return;
                                }
                            }
                            if (associated == false) {
                                $toastService.create("Assets are already associated for sku " + item.sku.name);
                                resetScannedAssetTagValue();
                                return;
                            }
                        }

                    }
                    if (foundSku == false) {
                        $toastService.create("Could not found appropriate sku");
                        resetScannedAssetTagValue();
                    }
                    /*
                        After performing operation set it back to empty string
                    */
                    resetScannedAssetTagValue();
                }

                $scope.changeSelectedDate = function (date) {
                    $scope.selectedDate = appUtil.isEmptyObject(date) ? null : date;
                };

                function resetScannedAssetTagValue() {
                    $scope.scannedAssetTagValue = "";
                }

                function recalculate(orderItems) {
                    $scope.invoiceItems = {};
                    for (var i in orderItems) {
                        var item = orderItems[i];
                        $scope.skuList.forEach(function (skuItem) {
                            if (skuItem.id == item.sku.id) {
                                var pkg = skuItem.skuData.packagings.filter(function (pkg) {
                                    return item.pkg.id == pkg.id;
                                })[0];
                                $scope.addSku(skuItem, pkg, pkg.price, item.pkgQty);
                                return false;//to break loop if found
                            }
                        });
                    }
                }

                $scope.getTransportModes = function () {
                    $http({
                        url: apiJson.urls.transportManagement.transportModes,
                        method: 'GET',
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null) {
                            $scope.transportModes = [];
                            response.map(function (item, index) {
                                $scope.transportModes.push({id: index, name: item});
                            });
                            $scope.selectedTransportMode = $scope.transportModes[0];
                            $scope.getVehicles($scope.selectedTransportMode);
                        } else {
                            $toastService.create("Error loading transport modes.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $alertService.alert("Error loading transport modes", response.errorMsg, function () {
                        }, true)
                    });
                };

                $scope.getVehicles = function (mode) {
                    if (appUtil.isEmptyObject(mode)) {
                        return;
                    }
                    $scope.selectedTransportMode = mode;
                    $http({
                        url: apiJson.urls.transportManagement.vehiclesByTransportMode,
                        method: 'GET',
                        params: {transportMode: $scope.selectedTransportMode.name}
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null) {
                            $scope.resetVehicleData();
                            $scope.vehicles = response;
                            $scope.vehicles.push({vehicleId: -1, name: "OTHER", status: "ACTIVE"});
                            // $scope.selectVehicle($scope.vehicles[0]);
                        } else {
                            $toastService.create("Error loading vehicles.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $alertService.alert("Error loading vehicles", response.errorMsg, function () {
                        }, true)
                    });
                };

                $scope.resetVehicleData = function () {
                    $scope.showOtherVehicleFields = false;
                    $scope.transportMode = null;
                    $scope.transportName = null;
                    $scope.transportId = null;
                    $scope.docketNumber = null;
                };

                $scope.selectVehicle = function (selectedVehicle) {
                    $scope.selectedVehicle = selectedVehicle;
                    if (appUtil.isEmptyObject(selectedVehicle)) {
                        return;
                    }
                    $scope.showOtherVehicleFields = (selectedVehicle.vehicleId == -1);
                    if (selectedVehicle != -1) {
                        $scope.transportMode = $scope.selectedTransportMode.name;
                        $scope.transportName = selectedVehicle.name;
                        $scope.validateAndSaveId(selectedVehicle.registrationNumber);
                        if ($scope.selectedTransportMode.name != "ROAD") {
                            $scope.showOtherVehicleFields = true;
                        }
                    }
                };

                $scope.changeInvoiceType = function (type) {
                    $scope.invoiceType = type;
                };

                $scope.changeType = function (txnType) {
                    var type = $scope.bussType.name == "RETAIL" ? "VENDOR" : "CUSTOMER";
                    $scope.vendorList = [];
                    //explicit condition for ECOM as its requirement is not clear
                    // if (txnType == 'ECOM') {
                    //     metaDataService.getVendorsForUnit($scope.currentUnit.id, function (vendorsForUnit) {
                    //         $scope.vendorListAll = vendorsForUnit;
                    //         //clear the select2 after change in type of transaction
                    //         $timeout(function () {
                    //             $('.select2Input').select2('val', '');
                    //         });
                    //         $scope.resetAll();
                    //     });
                    // } else {
                        metaDataService.getVendorsForUnitAndType($scope.currentUnit.id, type, function (vendorsForUnit) {
                            $scope.vendorListAll = vendorsForUnit;
                            if($scope.vendorListAll!=null){
                                for (var j = 0; j < $scope.filteredVendorList.length; j++) {
                                    for (var i = 0; i < $scope.vendorListAll.length; i++) {
                                        if ($scope.vendorListAll[i].vendorId == $scope.filteredVendorList[j].id) {
                                            if ('ACTIVE' == $scope.filteredVendorList[j].mappingStatus) {
                                                $scope.vendorListAll[i].mappingStatus = $scope.filteredVendorList[j].mappingStatus;
                                                $scope.vendorList.push($scope.vendorListAll[i]);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            //clear the select2 after change in type of transaction
                            $timeout(function () {
                                $('.select2Input').select2('val', '');
                            });
                            $scope.resetAll();
                        });
                    // }
                    // TODO add this back for B2B invoicing
                    if (txnType == "B2B_SALES") {
                        $scope.invoiceTypes = ["SYSTEM_INVOICE"];
                    } else if (txnType == "ECOM") {
                        $scope.invoiceTypes = ["DELIVERY_CHALLAN"]; //["INVOICE","DELIVERY_CHALLAN"];
                    } else {
                        $scope.invoiceTypes = ["INVOICE"]; //["INVOICE","DELIVERY_CHALLAN"];
                    }
                };

                $scope.resetAll = function () {
                    //$scope.selectedDispatchLocation = null;
                    $scope.selectedDeliveryLocation = null;
                    $scope.skuList = null;
                    $scope.invoiceItems = null;
                    $scope.additional = null;
                    $scope.selectedDate = null;
                    if ($scope.transportModes == null || $scope.transportModes.length == 0) {
                        $scope.getTransportModes();
                    }
                    $scope.selectedBillingLocation = null;
                    clearGstInvoiceType();
                };

                function clearGstInvoiceType() {
                    $timeout(function () {
                        $scope.gstType = null;
                        $('#gstTypes').val(null).trigger('change');
                    });
                }

                $scope.resetMetadata =function() {
                    var temp = $scope.isForFixedAsset;
                    $scope.init();
                    $scope.isForFixedAsset=temp;
                }

                $scope.selectVendor = function (vendor) {
                    $scope.resetAll();
                    $scope.getType = null;
                    if (!appUtil.isEmptyObject(vendor)) {
                        $scope.selectedVendor = vendor;
                        $scope.locationList = $scope.selectedVendor.dispatchLocations.filter(function (l) {
                            return l.status == "ACTIVE";
                        });
                        $scope.deliveryLocations = $scope.locationList.filter(function (l){
                            return l.locationType == "DELIVERY" || l.locationType == "DELIVERY_BILLING";
                        });
                        $scope.billingLocations = $scope.locationList.filter(function (l){
                            return l.locationType == "BILLING" || l.locationType == "DELIVERY_BILLING";
                        });
                        if(!appUtil.isEmptyObject(vendor.vendorAddress)){
                            var tempAddress = angular.copy(vendor.vendorAddress);
                            vendor.vendorAddress.address = tempAddress;
                            vendor.vendorAddress.locationName = "REGISTERED ADDRESS";
                            $scope.selectedBillingLocation = vendor.vendorAddress;
                            $scope.billingLocations.push(vendor.vendorAddress);
                        }
                    }
                };

                $scope.onChangeGstType = function() {
                    if(!appUtil.isEmptyObject($scope.gstType)) {
                        $scope.deliveryLocations = $scope.deliveryLocations.filter(function (dl) {
                            return dl.gstStatus == $scope.gstType;
                        });
                        $scope.billingLocations = $scope.billingLocations.filter(function (bl) {
                            return bl.gstStatus == $scope.gstType;
                        });

                        if( appUtil.isEmptyObject($scope.deliveryLocations) ) {
                            toast.warning("No delivery location found for the selected GST Type.");
                        }
                        if( appUtil.isEmptyObject($scope.billingLocations) ) {
                            toast.warning("No billing location found for the selected GST Type.");
                        }

                    } else {
                        $scope.deliveryLocations = $scope.locationList.filter(function (l){
                            return l.locationType == "DELIVERY" || l.locationType == "DELIVERY_BILLING";
                        });
                        $scope.billingLocations = $scope.locationList.filter(function (l){
                            return l.locationType == "BILLING" || l.locationType == "DELIVERY_BILLING";
                        });
                    }
                }

                $scope.$watch('gstType', function() {
                    $scope.onChangeGstType();
                }, true);

                $scope.selectBillingLocation = function (location){
                    if(location.locationName === "REGISTERED ADDRESS"){
                        $scope.selectedBillingType = "REGISTERED_ADDRESS";
                    }else{
                        $scope.selectedBillingType = null;
                    }
                    $scope.selectedBillingLocation = location;
                }

                $scope.selectDeliveryLocation = function (location){
                    $scope.selectedDeliveryLocation = location;
                    $scope.getPriceAndTaxData();
                }


                /*$scope.selectDispatchLocation = function (location) {
                    $scope.selectedDispatchLocation = location;
                    $scope.getPriceAndTaxData();
                };*/

                $scope.changeBillingType = function(billingType){
                    $scope.selectedBillingType = billingType;
                }

                $scope.getPriceAndTaxData = function () {
                    if (!appUtil.isEmptyObject($scope.selectedVendor)
                        && !appUtil.isEmptyObject($scope.selectedDeliveryLocation)) {
                        metaDataService.getSkuPricesAndTaxes($scope.selectedVendor.vendorId,
                            $scope.selectedDeliveryLocation.dispatchId, $scope.currentUnit.id, false ,function (skuAndTaxData) {
                                if (skuAndTaxData.length > 0) {
                                    $scope.skuList = skuAndTaxData;
                                } else {
                                    $toastService.create("Could not fetch prices for the dispatch location");
                                    $scope.skuList = [];
                                    $scope.packagingList = [];
                                }
                            });
                    } else {
                        $toastService.create("Please select Vendor Dispatch Location correctly");
                    }
                };

                $scope.selectSku = function (sku) {
                    if (!appUtil.isEmptyObject(sku)) {
                        $scope.selectedSku = sku;
                        $scope.packagingList = null;
                        $scope.packagingList = sku.skuData.packagings;
                        $scope.selectPkg(sku.skuData.packagings[0]);
                    }
                };

                $scope.selectPkg = function (packaging) {
                    $scope.selectedPackaging = packaging;
                    $scope.selectedPrice = angular.copy(packaging.price);
                };

                $scope.addSku = function (sku, pkg, price, qty) {
                    if ($scope.isForB2BInvoiceReversal && $scope.isForFixedAsset) {
                        $toastService.create("Can not Create Fixed Assets B2b Returns ..!");
                        return;
                    }
                    if (appUtil.isEmptyObject(qty)) {
                        return;
                    }
                    if (appUtil.isEmptyObject(price) || price<0) {
                        $toastService.create("Price Cannot be negative");
                        return;
                    }
                    if (appUtil.isEmptyObject($scope.invoiceItems)) {
                        $scope.invoiceItems = {};
                    }
                    var amountAndTax = getAmount(pkg, qty, sku.taxData, sku.taxData.exempted, price);
                    $timeout(function () {
                        if (!appUtil.isEmptyObject(amountAndTax)) {
                            $scope.invoiceItems[getKey(sku.id, pkg.id)] = {
                                sku: sku.skuData,
                                taxes: sku.taxData,
                                price: price,
                                pkg: pkg,
                                qty: qty,
                                amount: amountAndTax.amount,
                                appliedTax: amountAndTax.tax,
                                taxText: getTaxText(amountAndTax.taxes),
                                edit: false
                            };
                            addAssetForMapping($scope.invoiceItems[getKey(sku.id, pkg.id)], qty);
                            // add associated asset mapping || manage mapping
                            // remove if flag isForFixedAsset is false
                        }
                    });
                };

                function addAssetForMapping(item, qty) {
                    if ($scope.isForFixedAsset == true) {
                        if (item.associatedAssetMappings == undefined) {
                            item.associatedAssetMappings = [];
                            for (var i = 0; i < qty; i++) {
                                item.associatedAssetMappings.push(getEntityAssetMappingObject())
                            }
                        } else if (item.associatedAssetMappings.length < qty) {
                            for (var i = item.associatedAssetMappings.length; i < qty; i++) {
                                item.associatedAssetMappings.push(getEntityAssetMappingObject())
                            }
                        } else {
                            for (var i = item.associatedAssetMappings.length - 1; i >= qty; i--) {
                                item.associatedAssetMappings.splice(i, 1);
                            }
                        }
                    }
                }

                $scope.uploadDoc = function () {
                    if(appUtil.isEmptyObject($scope.purchasedOrderNumber) || $scope.purchasedOrderNumber==null){
                        $toastService.create("Please Enter Order No. To upload this File");
                        return;
                    }
                    $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                        if (file == null) {
                            $toastService.create('File cannot be empty');
                            return;
                        }
                        if(file.size > 307200){
                            $toastService.create('File size should not be greater than 300 kb.');
                            return;
                        }
                        var fileExt = metaDataService.getFileExtension(file.name);
                        if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                            var mimeType = fileExt.toUpperCase();
                            var fd = new FormData();
                            fd.append('type', "OTHERS");
                            fd.append('docType', "PURCHASE_ORDER");
                            fd.append('mimeType', fileExt.toUpperCase());
                            fd.append('poId', $scope.purchasedOrderNumber);
                            fd.append('userId', appUtil.getCurrentUser().userId);
                            fd.append('file', file);
                            $http({
                                url: apiJson.urls.invoiceManagement.uploadPurchaseOrder,
                                method: 'POST',
                                data: fd,
                                headers: {'Content-Type': undefined},
                                transformRequest: angular.identity
                            }).success(function (response) {
                                $rootScope.showFullScreenLoader = false;
                                if (!appUtil.isEmptyObject(response)) {
                                    $toastService.create("Upload successful");
                                    $scope.uploadedDocData = response;
                                    $scope.docPOId= response.documentId;
                                } else {
                                    $toastService.create("Upload failed");
                                }
                            }).error(function (response) {
                                $rootScope.showFullScreenLoader = false;
                                $toastService.create("Upload failed");
                            });
                        } else {
                            $toastService.create('Upload Failed , File Format not Supported');
                        }
                        /*metaDataService.uploadFile("OTHERS","PAYMENT_REQUEST_INVOICE",file, function(doc){
                            $scope.uploadedDocData = doc;
                        });*/
                    });
                };

                $scope.downloadPO = function (purchaseOrder) {
                    metaDataService.downloadDocument(purchaseOrder);
                };

                function getAssetFromUnit(unitId) {
                    $http({
                        url: apiJson.urls.assetManagement.getTransferableAssetsFromUnit,
                        method: 'GET',
                        params: {
                            unitId: unitId
                        },
                        headers: {"Content-Type": "application/json"}
                    }).then(function success(response) {
                        $scope.availableAssets = response.data;
                    }, function error(response) {
                        $scope.availableAssets = [];
                        console.log("error:" + response);
                    });
                }

                $scope.validateAssetTagValue = function (assetTagValue, asset, item) {
                    if (assetTagValue == null || assetTagValue.length != 6) {
                        asset.assetValidated = false;
                        asset.associatedAssetId = null;
                        return
                    }
                    console.log(item);
                    var count = 0;
                    for (var index in item.associatedAssetMappings) {
                        if (item.associatedAssetMappings[index].assetTagValue == assetTagValue) {
                            count++;
                            if (count > 1) {
                                $toastService.create("Duplicate asset tag value found");
                                asset.assetValidated = false;
                                asset.associatedAssetId = null;
                                asset.assetTagValue = null;
                                // $scope.availableAssets[index].tagValue = "";
                                return;
                            }
                        }
                    }
                    for (var index in $scope.availableAssets) {
                        if ($scope.availableAssets[index].tagValue == assetTagValue) {

                            if ($scope.availableAssets[index].skuId == item.sku.id) {
                                asset.assetId = $scope.availableAssets[index].assetId;
                                asset.assetValidated = true;
                                return;
                            }
                            break;
                        }
                    }
                    asset.assetValidated = false;
                    $toastService.create("Asset Tag Value Entered is not associated with selected SKU");
                }

                function getEntityAssetMappingObject() {
                    var entityAssetMappingObj = {};
                    entityAssetMappingObj.entityAssetMappingId = null;
                    entityAssetMappingObj.entityId = null;
                    entityAssetMappingObj.entityType = 'INVOICE'
                    entityAssetMappingObj.entityCategory = null;
                    entityAssetMappingObj.entitySubId = null;
                    entityAssetMappingObj.assetId = null;
                    entityAssetMappingObj.assetTagValue = null;
                    entityAssetMappingObj.assetVerified = false;
                    return entityAssetMappingObj;
                }

                $scope.byTxnType = function (vendor) {
                    if ($scope.txnType == "B2B_SALES") {
                        return vendor.type === "CUSTOMER";
                    } else {
                        return true;
                    }
                };

                function validateAssetInfo() {
                    for (var i in $scope.invoiceItems) {
                        var item = $scope.invoiceItems[i];
                        for (var j in item.associatedAssetMappings) {
                            var assetMapping = item.associatedAssetMappings[j];
                            if (assetMapping.assetId == null || assetMapping.assetValidated == false) {
                                $toastService.create("Please provide appropriate asset tag value for item no " + (i + 1));
                                return false;
                            }
                            for (var k in item.associatedAssetMappings) {
                                if (assetMapping.assetTagValue == item.associatedAssetMappings[k].assetTagValue && j != k) {
                                    $toastService.create("Asset Id " + assetMapping.assetTagValue + " has been added twice");
                                    return false;
                                }
                            }
                        }
                    }
                    return true;
                }

                $scope.previewInvoice = function (items, additional) {

                    if ($scope.showValidError) {
                        $toastService.create("Please make sure, you have entered correct value for Regd Number/GSTIN");
                        return;
                    }
                    $scope.totalCost = 0;
                    $scope.totalTax = 0;
                    $scope.totalAmount = 0;
                    $scope.additional = !appUtil.isEmptyObject(additional) ? additional : 0;
                    angular.forEach(items, function (item) {
                        $scope.totalCost += item.amount;
                        $scope.totalTax += item.appliedTax
                    });
                    $scope.totalAmount = $scope.totalCost + $scope.totalTax + $scope.additional;
                };

                $scope.removeItem = function (key) {
                    delete $scope.invoiceItems[key];
                    if (Object.keys($scope.invoiceItems).length == 0) {
                        $scope.invoiceItems = null;
                    }
                };

                $scope.submit = function (invoiceItems) {
                    if ($scope.isForFixedAsset == true) {
                        if (validateAssetInfo() == false) {
                            return;
                        }
                    }
                    if (appUtil.isEmptyObject($scope.selectedDate)) {
                        $toastService.create("Please select a proper dispatch date");
                        return;
                    }

                    $alertService.confirm("Are you sure?", "", function (result) {
                        if (result) {
                            sendRequestForInvoice($scope.prepareInvoiceItems());
                        }
                    });

                };

                // $scope.getSkuForVendor = function () {
                //     $http({
                //         url: apiJson.urls.skuMapping.getSkuForVendor,
                //         method: 'POST',
                //         data: $scope.selectedVendor.vendorId
                //     }).then(function (response) {
                //         $scope.message = response.data;
                //     }, function (response) {
                //         console.log("error", response);
                //     });
                // };

                $scope.prepareInvoiceItems = function () {
                    var invoiceItems = angular.copy($scope.invoiceItems);
                    invoiceItems = Object.values(invoiceItems).map(function (item) {
                        var amountAndTax = getAmount(item.pkg, item.qty, item.taxes, item.taxes.exempted, item.price, item.sku.hsn);
                        var data = {
                            "sku": {id: item.sku.id, code: item.sku.hsn, name: item.sku.name},
                            "uom": item.sku.uom,
                            "pkg": {id: item.pkg.id, code: "", name: item.pkg.name},
                            "ratio": item.pkg.ratio,
                            "pkgQty": item.qty,
                            "qty": item.pkg.ratio * item.qty,
                            "pkgPrice": item.pkg.price,
                            "sellPrice": item.price,
                            "sellAmount": item.price * (item.pkg.ratio * item.qty),
                            "totalTax": amountAndTax.tax,
                            "taxes": amountAndTax.taxes,
                            // add here associatedAssets
                            "associatedAssetMappings": item.associatedAssetMappings
                        }
                        if (item.sku.alias != null) {
                            data.alias = item.sku.alias;
                        }
                        return data;
                    });
                    return invoiceItems;
                };


                $scope.validateAndSaveId = function (id) {
                    $scope.showValidError = false;
                    $scope.validateErrorText = "";
                    if ($scope.selectedTransportMode.name != "ROAD") {
                        if (!appUtil.isEmptyObject(id) && validateGst(id)) {
                            $scope.transportId = id.toUpperCase();
                        } else {
                            $scope.showValidError = true;
                            $scope.validateErrorText = "GSTIN should have just 15 characters.First 2 should be the state code, next 10 should be the PAN number, last three should be alphanumeric with 'Z' in between";
                        }
                    } else {
                        if (!appUtil.isEmptyObject(id) && validateRegdNumber(id)) {
                            $scope.transportId = id.toUpperCase();
                        } else {
                            $scope.showValidError = true;
                            $scope.validateErrorText = "Vehicle Registration Number should be in the format of HR 01 AA 1111/ DL 01 C AA 1111";
                        }
                    }
                };

                $scope.validateAndSaveName = function (name) {
                    $scope.transportName = name.toUpperCase();
                };

                function sendRequestForInvoice(items) {
                    if($scope.bussType.name!="RETAIL") {
                        if (appUtil.isEmptyObject($scope.purchasedOrderNumber) || $scope.purchasedOrderNumber == null) {
                            $toastService.create("Please Enter Purchase Order No.");
                            return;
                        }
                        if (appUtil.isEmptyObject($scope.purchasedOrderDate) || $scope.purchasedOrderDate == null) {
                            $toastService.create("Please Enter Purchase Order Date");
                            return;
                        }
                        if (appUtil.isEmptyObject($scope.uploadedDocData) || $scope.uploadedDocData == null) {
                            $toastService.create("Please Upload Purchase Order Pdf");
                            return;
                        }
                    }
                    console.log("purchased order number:" + $scope.purchasedOrderNumber);
                    var billingLocationId = $scope.selectedBillingType === "REGISTERED_ADDRESS" ? null : $scope.selectedBillingLocation.dispatchId;
                    var reqObj = {
                        "vendor": {id: $scope.selectedVendor.vendorId, name: $scope.selectedVendor.entityName},
                        "dispatchLocation": {
                            id: $scope.selectedDeliveryLocation.dispatchId,
                            code: $scope.selectedDeliveryLocation.gstin,
                            name: $scope.selectedDeliveryLocation.city
                        },
                        "type": $scope.txnType,
                        "additionalCharges": $scope.additional,
                        "sendingUnit": {id: appUtil.getUnitData().id, name: appUtil.getUnitData().name},
                        "comment": $scope.comment,
                        "createdBy": appUtil.createGeneratedBy(),
                        "items": items,
                        "dispatchDate": $scope.selectedDate,
                        "docketNumber": $scope.docketNumber,
                        "invoiceType": $scope.invoiceType,
                        "purchasedOrderNumber": $scope.purchasedOrderNumber,
                        "referenceInvoiceNumber": $scope.referenceInvoiceNumber,
                        "purchaseOrderDate": $scope.purchasedOrderDate,
                        "billingType": $scope.selectedBillingType,
                        "businessType": $scope.bussType.name,
                        "vehicle": {
                            registrationNumber: $scope.transportId,
                            transportMode: $scope.transportMode,
                            name: $scope.transportName
                        },
                        "assetOrder": $scope.isForFixedAsset,
                        "docPOId": $scope.docPOId,
                        "needsApproval": $scope.invoiceType == 'INVOICE' ? false : true,
                        "billingLocationId": billingLocationId,
                        "gstInvoiceType": $scope.gstType
                    };

                    if (reqObj.items.length > 0) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.invoiceManagement.createRequest,
                            data: reqObj
                        }).then(function (response) {
                            if (response.data != null) {
                                $scope.init();
                                var message = "Invoice Request with ID: <b>" + response.data.id + "</b> raised successfully! <br>";
                                $alertService.alert("Request Raised!!", message,
                                    function () {
                                        $state.go("menu.approveInvoice", {
                                            createdInvoice: response.data,
                                            viewInvoice: true,
                                            raiseCreditNote : false
                                        });
                                    }
                                );
                            } else {
                                $toastService.create("Error raising invoice request!! Please check if all the fields are correct and try again");
                            }
                        }, function (response) {
                            console.log(response);
                            if(response.data.errorMessage!=null) {
                                $alertService.alert("Error raising invoice request", response.data.errorMessage,function () {
                                    $scope.init();
                                }, true);
                            } else if (response.data.errorMsg!=null) {
                                $alertService.alert(response.data.errorTitle, response.data.errorMsg,function () {
                                    $scope.init();
                                }, true);
                            } else{
                                $toastService.create("Error raising invoice request");
                                console.log("error:" + response);
                                $scope.init();
                            }
                        });
                    } else {
                        $toastService.create("Error raising invoice request!! No items added to create invoice request");
                    }
                }


                function getTaxValue(taxValue, amount, hsn, type) {
                    return {
                        code: hsn,
                        type: type,
                        value: (amount * taxValue) / 100,
                        percent: taxValue
                    };
                }


                function getAmount(pkg, qty, tax, isExempted, price, hsn) {
                    isExempted = isExempted != undefined ? isExempted : false;
                    var ratio = pkg.ratio;
                    var gst = 0;
                    var otherTaxes = 0;
                    var amount = parseFloat(price * qty);
                    var obj = {
                        amount: amount,
                        taxes: [],
                        tax: null
                    };

                    if (!isExempted) {
                        if (appUtil.isEmptyObject(tax.state)) {
                            $alertService.alert("Error!", "No Tax detail found for this product");
                            return;
                        }
                        if ($scope.currentUnit.location.state.code.toString() != $scope.selectedDeliveryLocation.address.stateCode.toString()) {
                            var igst = getTaxValue(tax.state.igst, amount, hsn, "IGST");
                            obj.taxes.push(igst);
                            obj.tax = igst.value;
                        } else {
                            var cgst = getTaxValue(tax.state.cgst, amount, hsn, "CGST");
                            var sgst = getTaxValue(tax.state.sgst, amount, hsn, "SGST");
                            obj.taxes.push(cgst);
                            obj.taxes.push(sgst);
                            obj.tax = cgst.value + sgst.value;
                        }

                        if (tax.others.length > 0) {
                            for (var index in tax.others) {
                                var otherTax = tax.others[index];
                                var valueAndTax = getTaxValue(otherTax.tax, getApplicableAmount(otherTax.applicability, obj), hsn, otherTax.type);
                                obj.taxes.push(valueAndTax);
                                otherTaxes += valueAndTax.value;
                            }
                            obj.tax += otherTaxes;
                        }
                    }
                    return obj;
                }

                function getApplicableAmount(applicability, obj) {
                    if (applicability == "ON_TAX") {
                        return getValue(obj.cgst) + getValue(obj.igst) + getValue(obj.sgst);
                    } else {
                        return obj.amount;
                    }
                }

                function getValue(taxObj) {
                    return appUtil.isEmptyObject(taxObj.value) ? 0 : taxObj.value;
                }

                function getTaxText(taxes) {
                    var text = "";
                    for (var key in taxes) {
                        if (!appUtil.isEmptyObject(taxes[key].value)) {
                            text += taxes[key].type.toUpperCase() + ":" + taxes[key].percent + "% ";
                        }
                    }
                    return text;
                }

                $scope.categoryFilter = function (sku) {
                    if ($scope.isForFixedAsset) {
                        if (sku.skuData.category == 3) {
                            return sku;
                        }
                    } else {
                        if (sku.skuData.category != 3) {
                            return sku;
                        }
                    }
                }

                $scope.searchByRequest = function () {
                    var mappingModal = Popeye.openModal({
                        templateUrl: "invReqSearch.html",
                        controller: "invReqSearchCtrl",
                        modalClass: "reqSearchModal",
                        resolve: {
                            skuList: function () {
                                return $scope.skuList;
                            },
                            vendor: function () {
                                return $scope.selectedVendor.entityName;
                            },
                            location: function () {
                                return $scope.selectedDeliveryLocation.city;
                            }
                        },
                        click: false,
                        keyboard: false
                    });
                    mappingModal.closed
                        .then(function (result) {
                            if (!appUtil.isEmptyObject(result)) {
                                if (!appUtil.isEmptyObject(result.error)) {
                                    var msg = getMessage(result.error, $scope.selectedVendor, $scope.selectedDeliveryLocation, $scope.currentUnit);
                                    $alertService.alert("UOM Price Mapping Error!!", msg, function () {
                                        console.log("Closed after acknowledgement");
                                    }, true);
                                } else if (!appUtil.isEmptyObject(result.skus)) {
                                    for (var i in result.skus) {
                                        var detail = result.skus[i];
                                        var skuDetail = detail.sku.skuData;
                                        var pkg = skuDetail.packagings[0];
                                        var qty = detail.qty / pkg.ratio;
                                        $scope.addSku(detail.sku, pkg, pkg.price, qty);
                                    }
                                    $toastService.create("Total " + parseInt(result.skus.length) + " SKU(s) added to the invoice");
                                }
                            } else {
                                $alertService.create("SKUs not found", "No SKUs to add to invoice");
                            }
                        });
                };

                $scope.resetInvoiceItems = function () {
                    $scope.invoiceItems = null;
                };

                function getMessage(productsWithError, vendor, location, currentUnit) {
                    productsWithError = productsWithError.filter(function (p) {
                        return !appUtil.isEmptyObject(p);
                    });
                    sendErrorSlackToSCMTeam(productsWithError, vendor, location, currentUnit);
                    var message = "UOM Pricing not found for " + vendor.entityName
                        + "[" + location.city + "] for " + currentUnit.name + "<br/>";
                    message += "<b>" + productsWithError.map(function (p) {
                        return p.name + " [" + p.uom + "]";
                    }).join(', ') + "</b>";
                    return message;
                }

                function sendErrorSlackToSCMTeam(products, vendor, location, currentUnit) {
                    var reqObj = {
                        products: products.map(function (p) {
                            return p.name + " [" + p.uom + "]";
                        }).join(", "),
                        vendor: vendor.entityName,
                        location: location.city,
                        dispatchUnit: currentUnit.name
                    };

                    $http({
                        method: "POST",
                        url: apiJson.urls.invoiceManagement.notifyError,
                        data: reqObj
                    }).then(function () {
                        console.log("Error notified to SCM team");
                    }, function (response) {
                        console.log("error:" + response);
                    });
                }

            }
        ]
    ).controller('invReqSearchCtrl', [
    '$scope', 'skuList', 'vendor', 'location', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye',
    function ($scope, skuList, vendor, location, appUtil, $toastService, apiJson, $http, Popeye) {

        $scope.initModal = function () {
            $scope.vendor = vendor + " [" + location + "]";
            $scope.productMap = prepareProductMap(angular.copy(skuList));
            $scope.selectedUnit = null;
            $scope.erroredProducts = null;
            $scope.mappedProducts = null;
            $scope.currentUnit = appUtil.getUnitData();
            getUnits();
        };

        $scope.selectUnit = function (unit) {
            $scope.selectedUnit = unit;
            $scope.getOrders();
        };

        $scope.getOrders = function () {
            if ($scope.selectedUnit != null) {
                getRequestOrders($scope.selectedUnit.id, $scope.currentUnit.id);
            }
        };


        $scope.cancel = function () {
            Popeye.closeCurrentModal();
        };

        function prepareProductMap(skuList) {
            var productMap = {};
            for (var i in skuList) {
                var sku = skuList[i];
                var foundAt = hasUomMapping(sku.skuData.packagings);
                if (foundAt != -1) {
                    sku.skuData.packagings = [sku.skuData.packagings[foundAt]];
                    if (!productMap.hasOwnProperty(sku.skuData.productId)) {
                        productMap[sku.skuData.productId] = [];
                    }
                    productMap[sku.skuData.productId].push(sku);
                }
            }
            return productMap;
        }

        function getUnits() {
            $http({
                method: "GET",
                url: apiJson.urls.skuMapping.unitsByBusinessType,
                params: {
                    type: "FIFO"
                }
            }).then(function success(response) {
                if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                    $scope.units = response.data;
                } else {
                    $toastService.create("Could not find units of type " + type);
                }

            }, function error(response) {
                console.log("error:" + response);
            });
        }

        function getRequestOrders(receivingUnit, sendingUnit) {
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.requestOrders,
                params: {
                    from: sendingUnit,
                    to: receivingUnit
                }
            }).then(function success(response) {
                if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                    $scope.orders = response.data;
                } else {
                    $toastService.create("Could not find any request orders for unit " + $scope.selectedUnit.name);
                }

            }, function error(response) {
                console.log("error:" + response);
            });
        }

        $scope.checkOrders = function () {
            $scope.erroredProducts = {};
            $scope.mappedProducts = {};
            var checkedOrders = angular.copy($scope.orders).filter(function (order) {
                return order.checked;
            });
            for (var i in checkedOrders) {
                var ro = checkedOrders[i];
                for (var i in ro.requestOrderItems) {
                    var item = ro.requestOrderItems[i];
                    if ($scope.productMap.hasOwnProperty(item.productId)) {
                        addToMappedProducts(item);
                    } else {
                        $scope.erroredProducts[item.productId] = {
                            name: item.productName,
                            uom: item.unitOfMeasure
                        };
                    }
                }
            }

            //close modal in case any non mapped product found
            if (Object.keys($scope.erroredProducts).length > 0) {
                $scope.submitToInvoice();
            }

            //close modal in case no product found with mapped SKUs
            if (Object.keys($scope.mappedProducts).length == 0) {
                $scope.submitToInvoice();
            }
        };

        function addToMappedProducts(item) {
            // club same products together and add their quantity across ROs
            if ($scope.mappedProducts.hasOwnProperty(item.productId)) {
                $scope.mappedProducts[item.productId].qty = $scope.mappedProducts[item.productId].qty + item.requestedAbsoluteQuantity;
            } else {
                // initiate mapped products object for the specific product
                $scope.mappedProducts[item.productId] = {
                    name: item.productName,
                    uom: item.unitOfMeasure,
                    skus: angular.copy($scope.productMap[item.productId]),
                    qty: item.requestedAbsoluteQuantity,
                    selectedSku: null
                };

                // initiate in case of just one sku price found
                var mappedProduct = $scope.mappedProducts[item.productId];
                if (mappedProduct.skus.length == 1) {
                    mappedProduct.selectedSku = mappedProduct.skus[0];
                }
            }
        }

        $scope.submitToInvoice = function () {
            var result = {
                error: null,
                skus: null
            };
            if (!appUtil.isEmptyObject($scope.erroredProducts)) {
                result.error = Object.values($scope.erroredProducts);
                $scope.close(result);
            } else if (!appUtil.isEmptyObject($scope.mappedProducts)) {
                if (validateProducts($scope.mappedProducts)) {
                    $scope.closeAndAddSkus($scope.mappedProducts, result);
                } else {
                    $toastService.create("Please select SKU for all products to be added");
                }
            } else {
                //this code block should be entered only in freak conditions
                $toastService.create("No Products found that are mapped to the UOM SKU pricing");
            }
        };

        $scope.close = function (result) {
            Popeye.closeCurrentModal(result);
        };

        $scope.closeAndAddSkus = function (mappedProducts, result) {
            var skus = Object.values(mappedProducts).map(function (product) {
                return {
                    sku: product.selectedSku,
                    qty: product.qty
                }
            });
            result.skus = skus;
            $scope.close(result);
        };

        function hasUomMapping(pkgs) {
            for (var j in pkgs) {
                // check if UOM
                if (parseFloat(pkgs[j].ratio) == parseFloat(1)) {
                    return j;
                }
            }
            return -1;
        }


        function validateProducts(mappedProducts) {
            for (var pid in mappedProducts) {
                if (mappedProducts[pid].selectedSku == null) {
                    return false;
                }
            }
            return true;
        }
    }
]);
