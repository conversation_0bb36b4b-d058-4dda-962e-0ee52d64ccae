'use strict';
angular.module('scmApp').controller('viewSoToSrCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http',
    'appUtil', '$toastService','$alertService','metaDataService','Popeye','$timeout',
    function ($rootScope, $stateParams, $scope, apiJson, $http,
              appUtil, $toastService,$alertService,metaDataService, Popeye, $timeout) {

        function getCreatedSRs(startDate,endDate, vendor, location, prId,capexId) {
              

            if (appUtil.isEmptyObject(startDate)) {
                $toastService.create("Please select a start date first");
                return;
            }
            if (appUtil.isEmptyObject(endDate)) {
                $toastService.create("Please select a end date first");
                return;
            }
            if($scope.startDate>$scope.endDate){
                $toastService.create("Please select a start date less than end date");
                return;
            }
            if($scope.endDate> appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd")){
                $toastService.create("Please select a end date less than today");
                return;
            }
            if($scope.startDate> appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd")){
                $toastService.create("Please select a start date less than today");
                return;
            }

            var params = {
                startDate: startDate,
                endDate: endDate,
                userId: appUtil.getCurrentUser().userId,
                prId: prId
            };

            if (!appUtil.isEmptyObject(vendor)) {
                params["vendorId"] = vendor.id;
            }


            if (!appUtil.isEmptyObject(location)) {
                params["locationId"] = location.id;
            }

            if(!appUtil.isEmptyObject(capexId)){
                params={};
                params = {capexId: capexId}             
            }

            $http({
                method: "GET",
                url: apiJson.urls.serviceReceivedManagement.getLinkedSrForSo,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Receiving found!");
                } else {
                    $scope.srRequest = response.data;
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.printSR = function (sr) {
            $scope.currentPrintSR = sr;
            $scope.currentPrintSR["companyAddress"] = $scope.companyMap[sr.company.id].registeredAddress;
            $timeout(function() {
                angular.element('#printDiv').trigger('click');
                for(var i=0;i<sr.serviceOrderList.length;i++){
                    metaDataService.downloadDocument(sr.serviceOrderList[i].soInvoiceDocument);
                }
            });
        };

        $scope.init = function () {
            var currentDate = appUtil.getCurrentBusinessDate();
            if(!appUtil.isEmptyObject(currentDate)){
                $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.vendorSelected = $stateParams.selectedVendor;
                $scope.locationSelected = $stateParams.selectedDispatchLocation;
                $scope.srRequest = [];
                $scope.currentUser = appUtil.getCurrentUser();

                metaDataService.getServiceVendors(function(vendors){
                    $scope.vendors = vendors;
                });

                metaDataService.getCompanyList(function(companies){
                    $scope.companyMap = {};
                    for(var i in companies){
                        $scope.companyMap[companies[i].id] = companies[i];
                    }
                });

                $scope.isCapexId=false; 
                $scope.capexRequestId=null;
            }
        };

        $scope.changeCapexId = function(capexId){
            $scope.capexRequestId = capexId;
        }

        $scope.checkCapexId = function(){
            $scope.vendorSelected = null;
            $scope.srRequest = [];
            $scope.capexRequestId=null;
            $timeout(function(){ $('#enterCapexId').val(null).trigger('change');});
        }

        $scope.selectVendor = function (vendor) {
            $scope.vendorSelected = vendor;
            metaDataService.getVendorLocations($scope.vendorSelected.id, function (locations) {
                $scope.locationList = locations;
            });
        };

        $scope.selectDispatchLocation = function (location) {
            $scope.locationSelected = location;
        };

        $scope.getSRs = function(){
            getCreatedSRs($scope.startDate,$scope.endDate, $scope.vendorSelected, $scope.locationSelected,$scope.prId,$scope.capexRequestId);
        };

        $scope.generateSrSheet = function(){
            
            var i =0;
            var srIds = [];
            for(i=0; i<$scope.srRequest.length; i++){
                    srIds.push($scope.srRequest[i].id);
            }

            $http({
                method: "POST",
                url: apiJson.urls.serviceReceivedManagement.generateSrSheet,
                data: srIds,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Receiving found!");
                } else {
                    var fileName =  "SR_DATA_"+new Date()+ ".xlsx";
                    var blob = new Blob(
                        [response.data],
                        {
                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }, fileName);
                    saveAs(blob, fileName);
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }
    }
]);