angular.module('scmApp')
    .controller('menuProductsConsumptionController', ['$rootScope', '$scope', '$stateParams', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'metaDataService', '$alertService', '$timeout',
        function ($rootScope, $scope, $stateParams, apiJson, $http, appUtil, $toastService, $state, metaDataService, $alertService, $timeout) {

            $scope.init = function () {
                console.log("Menu Products Consumption Controller Initialized");
                $scope.selectedBrandDetails = null;
                $scope.fulfillmentDate = null;
                $scope.noOfDays = 3;
                $scope.dataEntry = [];
                $scope.showProductsList = false;
                $scope.productConsumptionList = [];
                $scope.remainingDays = [];
                $scope.orderingDays = [];
                $scope.orderingPercentages = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
                
                // Get brand details
                $scope.brandList = appUtil.getBrandList();
//                getBrandDetails();
                $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                $scope.comment = null;
                $scope.minRefOrderFulfillmentDate = appUtil.getDate(1);
                $scope.maxRefOrderFulfillmentDate = appUtil.getDate(7);
                console.log($scope.minRefOrderFulfillmentDate + "," + $scope.maxRefOrderFulfillmentDate);
                $scope.brandDetails = [{id: 1, brandName: "CHAAYOS"},
                                    {id: 3, brandName: "GNT"},
                                    {id: 6, brandName: "DOHFUL"},
                                    {id: 0, brandName: "CHAAYOS_AND_GNT"}];
                $scope.selectedBrandDetails = $scope.brandDetails[0];
                
                // Hide loader after initialization
                $timeout(function() {
                    $rootScope.showSpinner = false;
                }, 100);
            };

//            function getBrandDetails() {
//                metaDataService.getBrandDetails(function (brandDetails) {
//                    $scope.brandDetails = brandDetails;
//                });
//            }

            $scope.setDates = function (fulfillmentDate, noOfDays) {
                if (fulfillmentDate && noOfDays) {
                    $scope.dataEntry = [];
                    $scope.remainingDays = [];
                    $scope.orderingDays = [];
                    $scope.fulfillmentDay = $scope.getDayOfWeekFromStr($scope.fulfillmentDate);
                    
                    var startDate = new Date(fulfillmentDate);
                    
                    // Create data entry for days
                    for (var i = 0; i < noOfDays; i++) {
                        var currentDate = new Date(startDate);
                        currentDate.setDate(startDate.getDate() - (noOfDays - 1 - i));
                        
                        var dayType = i < Math.floor(noOfDays / 2) ? 'REMAINING_DAY' : 'ORDERING_DAY';
                        var orderingPercentage = dayType === 'ORDERING_DAY' ? 100 : 0;
                        
                        var entry = {
                            date: currentDate.toISOString().split('T')[0],
                            dayType: dayType,
                            orderingPercentage: orderingPercentage
                        };
                        
                        $scope.dataEntry.push(entry);
                        
                        if (dayType === 'REMAINING_DAY') {
                            $scope.remainingDays.push(currentDate);
                        } else {
                            $scope.orderingDays.push(currentDate);
                        }
                    }
                }
            };

            $scope.getDayOfWeek = getDayOfWeek;
            $scope.getDayOfWeekFromStr = getDayOfWeekFromStr;

            function weekDays() {
                var days = [
                    {id: 1, value: 'Sunday'},
                    {id: 2, value: 'Monday'},
                    {id: 3, value: 'Tuesday'},
                    {id: 4, value: 'Wednesday'},
                    {id: 5, value: 'Thursday'},
                    {id: 6, value: 'Friday'},
                    {id: 7, value: 'Saturday'}];
                return days;
            }

            function getDayOfWeekFromStr(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                return weekDays()[new Date(date).getDay()].value;
            }

            function getDayOfWeek(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                console.log('Selected Date', date.getDay());
                return weekDays()[date.getDay()].value;
            }

            $scope.getMenuProductsConsumption = function () {
                if (!$scope.selectedBrandDetails || !$scope.fulfillmentDate || !$scope.noOfDays) {
                    $toastService.create("Please select all required fields!");
                    return;
                }

                $alertService.alert("Please Wait..!", "<b>Fetching menu products consumption data...</b>", function (result) {
                }, false);

                var datesList = $scope.dataEntry.map(function(entry) {
                    var date = new Date(entry.date);
                    console.log(date.toISOString().split('T')[0]);
                    return date.toISOString().split('T')[0]; // YYYY-MM-DD
                });

                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    dayList: datesList,
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    requestedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getMenuProductsConsumptionAverageNew,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        $toastService.create("Menu products consumption data fetched successfully!");
                        console.log("Menu products consumption response Data:", response.data);
                        processConsumptionData(response.data);
                        $scope.showProductsList = true;
                        $alertService.closeAlert();
                    } else {
                        $toastService.create("No data found for the selected criteria!");
                        $alertService.closeAlert();
                    }
                }, function error(response) {
                    console.log("Error:", response);
                    $toastService.create("Error fetching consumption data!");
                    $alertService.closeAlert();
                });
            };

            function processConsumptionData(consumptionMap) {
                $scope.productConsumptionList = [];
                
                // Process Map<Date, List<MenuProductSalesAverageDto>>
                var productMap = {};
                
                // Loop through each date in the map
                Object.keys(consumptionMap).forEach(function(dateKey) {
                    var productsList = consumptionMap[dateKey];
                    
                    productsList.forEach(function(product) {
                        var productKey = product.productId + "_" + product.dimension;
                        
                        if (!productMap[productKey]) {
                            productMap[productKey] = {
                                productId: product.productId,
                                productName: product.productName,
                                dimension: product.dimension,
                                remainingDaysData: {},
                                orderingDaysData: {},
                                finalQuantity: 0
                            };
                        }
                        
                        var date = new Date(dateKey);
                        var isOrderingDay = $scope.orderingDays.some(function(orderingDate) {
                            return orderingDate.toDateString() === date.toDateString();
                        });
                        
                        if (isOrderingDay) {
                            productMap[productKey].orderingDaysData[date.getTime()] = product.averageSalesQuantity || 0;
                        } else {
                            productMap[productKey].remainingDaysData[date.getTime()] = product.averageSalesQuantity || 0;
                        }
                    });
                });
                
                // Convert to array and calculate final quantities
                Object.keys(productMap).forEach(function(key) {
                    var product = productMap[key];
                    calculateFinalQuantity(product);
                    $scope.productConsumptionList.push(product);
                });
                
                console.log("Processed Product Consumption List:", $scope.productConsumptionList);
            }

            $scope.calculateFinalQuantity = function(product) {
                calculateFinalQuantity(product);
            };

            function calculateFinalQuantity(product) {
                var total = 0;
                
                // Sum up ordering days quantities
                Object.keys(product.orderingDaysData).forEach(function(dateKey) {
                    var quantity = parseFloat(product.orderingDaysData[dateKey]) || 0;
                    total += quantity;
                });
                
                product.finalQuantity = total;
            }

            $scope.saveConsumptionData = function() {
                if ($scope.productConsumptionList.length === 0) {
                    $toastService.create("No data to save!");
                    return;
                }

                var saveData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    productConsumptionData: $scope.productConsumptionList,
                    dataEntry: $scope.dataEntry,
                    savedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.saveMenuProductsConsumption,
                    data: saveData
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Consumption data saved successfully!");
                    } else {
                        $toastService.create("Error saving consumption data!");
                    }
                }, function error(response) {
                    console.log("Save Error:", response);
                    $toastService.create("Error saving consumption data!");
                });
            };

            $scope.resetData = function() {
                if (confirm("Are you sure you want to reset all data?")) {
                    $scope.productConsumptionList.forEach(function(product) {
                        Object.keys(product.remainingDaysData).forEach(function(key) {
                            product.remainingDaysData[key] = 0;
                        });
                        Object.keys(product.orderingDaysData).forEach(function(key) {
                            product.orderingDaysData[key] = 0;
                        });
                        product.finalQuantity = 0;
                    });
                    $toastService.create("Data reset successfully!");
                }
            };

            $scope.goBackToSelection = function() {
                $scope.showProductsList = false;
                $scope.productConsumptionList = [];
            };

            $scope.dateformatting = function (date) {
                return appUtil.formatDate(new Date(date), "yyyy-MM-dd");
            };

        }]);
