angular.module('scmApp')
    .controller('menuProductsConsumptionController', ['$rootScope', '$scope', '$stateParams', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'metaDataService', '$alertService', '$timeout',
        function ($rootScope, $scope, $stateParams, apiJson, $http, appUtil, $toastService, $state, metaDataService, $alertService, $timeout) {

            $scope.init = function () {
                console.log("Menu Products Consumption Controller Initialized");
                $scope.selectedBrandDetails = null;
                $scope.fulfillmentDate = null;
                $scope.noOfDays = 3;
                $scope.dataEntry = [];
                $scope.showProductsList = false;
                $scope.productConsumptionList = [];
                $scope.remainingDays = [];
                $scope.orderingDays = [];

                // SCM Products variables
                $scope.showScmProductsList = false;
                $scope.scmProductConsumptionList = [];

                // Data Tracking variables
                $scope.originalMenuProductsData = [];
                $scope.modifiedMenuProductsData = [];
                $scope.orderingPercentages = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

                // Get brand details
//                getBrandDetails();

                $scope.brandList = appUtil.getBrandList();
                $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                $scope.comment = null;
                $scope.minRefOrderFulfillmentDate = appUtil.getDate(1);
                $scope.maxRefOrderFulfillmentDate = appUtil.getDate(7);
                console.log($scope.minRefOrderFulfillmentDate + "," + $scope.maxRefOrderFulfillmentDate);
                $scope.brandDetails = [{id: 1, brandName: "CHAAYOS"},
                    {id: 3, brandName: "GNT"},
                    {id: 6, brandName: "DOHFUL"},
                    {id: 0, brandName: "CHAAYOS_AND_GNT"}];
                $scope.selectedBrandDetails = $scope.brandDetails[0];
                // Hide loader after initialization
                $timeout(function() {
                    $rootScope.showSpinner = false;
                }, 100);
            };

//            function getBrandDetails() {
//                metaDataService.getBrandDetails(function (brandDetails) {
//                    $scope.brandDetails = brandDetails;
//                });
//            }

             $scope.setBrand = function (brand) {
                $scope.selectedBrandDetails = brand;
                $scope.setDates($scope.fulfillmentDate, 2);
             };
             function makeDateString(date) {
                 var newDate = new Date(date);
                 var result = newDate.getFullYear() + "-" + (newDate.getMonth() + 1) + "-" + (newDate.getDate());
                 console.log("result Date is : ", result);
                 return result;
             }
             function weekDays() {
                 var days = [
                     {id: 1, value: 'Sunday'},
                     {id: 2, value: 'Monday'},
                     {id: 3, value: 'Tuesday'},
                     {id: 4, value: 'Wednesday'},
                     {id: 5, value: 'Thursday'},
                     {id: 6, value: 'Friday'},
                     {id: 7, value: 'Saturday'}];
                 return days;
             }
            $scope.getDayOfWeek = getDayOfWeek;
            function getDayOfWeek(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                console.log('Selected Date', date.getDay());
                return weekDays()[date.getDay()].value;
            }

            var debounceTimer = null;
            $scope.isApiInProgress = false;
            $scope.setDates = function (fulfillmentDate, noOfDays) {
                if (debounceTimer) {
                    $timeout.cancel(debounceTimer);
                }
                debounceTimer = $timeout(function () {
                   try {
                         if ($scope.isApiInProgress) return;
                         $scope.isApiInProgress = true;
                         $scope.dataEntry = [];
                         $scope.remainingDays = [];
                         $scope.orderingDays = [];
                         $scope.onlyOrderingDays = [];
                         var maxAllowedDays=7;

                         if (!noOfDays || isNaN(noOfDays) || noOfDays <= 0) {
                            $toastService.create("Please Enter Ordering Days..!");
                            $scope.noOfDays = 0;
                            $scope.isApiInProgress = false;
                            return;
                         }

                         if (noOfDays > maxAllowedDays) {
                            $toastService.create("Ordering Days cannot be more than " + maxAllowedDays + "  for selected brand.");
                            console.log("maxAllowedDays, days is : ", maxAllowedDays, noOfDays);
                            $scope.noOfDays = 0;
                            $scope.isApiInProgress = false;
                            return;
                         }

                         var startDate = new Date(fulfillmentDate);
                         $scope.fulfillmentDate = $scope.regularOrderingEvent != null ? makeDateString($scope.regularOrderingEvent.fulfilmentDate) : makeDateString(fulfillmentDate);
                         var regularOrderingDate = appUtil.getRegularOrderingDate();
                         $scope.noOfRemainingDays = appUtil.datediffRO(regularOrderingDate, $scope.fulfillmentDate);
                         $scope.noOfDays = $scope.regularOrderingEvent != null ? $scope.regularOrderingEvent.orderingDays : noOfDays;
                         $scope.stockLastingDate = appUtil.calculatedDate($scope.noOfDays - 1, $scope.fulfillmentDate);
                         $scope.stockLastingDay = $scope.getDayOfWeek($scope.stockLastingDate);
                         if (new Date($scope.stockLastingDate) > new Date($scope.maxRefOrderFulfillmentDate)) {
                            $toastService.create(" Ordering days go outside the allowed window (from " + appUtil.formatDate($scope.minRefOrderFulfillmentDate, 'yyyy-MM-dd') + " to " + appUtil.formatDate($scope.maxRefOrderFulfillmentDate, 'yyyy-MM-dd') + ")");
                            $scope.noOfDays = 0;
                            $scope.stockLastingDate = null;
                            $scope.stockLastingDay = null;
                            $scope.isApiInProgress = false;
                            return;
                         }
                         // Create data entry for days
                         for (var i = 1; i <= $scope.noOfRemainingDays - 1; i++) {
                             $scope.dataEntry.push({
                                 dayType: 'REMAINING_DAY',
                                 date: appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"),
                                 brands: [{
                                     id: $scope.chaayosId,
                                     saleAmount: 0,
                                     deliverySalePercentage: 0
                                 },
                                     {
                                         id: $scope.gntId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     },
                                     {
                                         id: $scope.dcId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     }],
                                 orderingPercentage : 100

                             })
                             $scope.remainingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                         }
                         for (var i = 0; i <= $scope.noOfDays - 1; i++) {
                             $scope.dataEntry.push({
                                 dayType: 'ORDERING_DAY',
                                 date: appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"),
                                 brands: [{
                                     id: $scope.chaayosId,
                                     saleAmount: 0,
                                     deliverySalePercentage: 0
                                 },
                                     {
                                         id: $scope.gntId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     },
                                     {
                                         id: $scope.dcId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     }],
                                 orderingPercentage : 100
                             })
                             $scope.orderingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                         }
                   } catch (e) {
                     console.log("Error in setDates: ", e);
                   } finally {
                     $timeout(function() {
                         $scope.isApiInProgress = false;
                     });
                   }
                }, 500);
            };

            $scope.getMenuProductsConsumption = function () {
                if (!$scope.selectedBrandDetails || !$scope.fulfillmentDate || !$scope.noOfDays) {
                    $toastService.create("Please select all required fields!");
                    return;
                }

                $alertService.alert("Please Wait..!", "<b>Fetching menu products consumption data...</b>", function (result) {
                }, false);

                var dayList = $scope.dataEntry.map(function(entry) {
                    return entry.date;
                });

                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    dayList: dayList,
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    requestedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getMenuProductsConsumptionAverageNew,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        $toastService.create("Menu products consumption data fetched successfully!");
                        console.log("Menu Products Consumption Data:", response.data);
                        processConsumptionData(response.data);
                        $scope.showProductsList = true;
                        $alertService.closeAlert();
                    } else {
                        $toastService.create("No data found for the selected criteria!");
                        $alertService.closeAlert();
                    }
                }, function error(response) {
                    console.log("Error:", response);
                    $toastService.create("Error fetching consumption data!");
                    $alertService.closeAlert();
                });
            };

            function processConsumptionData(consumptionMap) {
                $scope.productConsumptionList = [];

                var productMap = {};

                // Loop through each date in the map
                Object.keys(consumptionMap).forEach(function(dateKey) {
                    var productsList = consumptionMap[dateKey];

                    var date = new Date(dateKey);
                    var formattedDate = appUtil.formatDate(date, "yyyy-MM-dd");
                    var isOrderingDay = $scope.orderingDays.includes(formattedDate);

                    productsList.forEach(function(product) {
                        var productKey = product.productId + "_" + product.dimension;

                        if (!productMap[productKey]) {
                            productMap[productKey] = {
                                productId: product.productId,
                                productName: product.productName,
                                dimension: product.dimension,
                                remainingDaysData: {},
                                orderingDaysData: {},
                                finalQuantity: 0
                            };
                        }

                        console.log("Date:", formattedDate, "Avg:", product.averageSalesQuantity, "Is Ordering:", isOrderingDay);

                        if (isOrderingDay) {
                            productMap[productKey].orderingDaysData[formattedDate] = product.averageSalesQuantity || 0;
                        } else {
                            productMap[productKey].remainingDaysData[formattedDate] = product.averageSalesQuantity || 0;
                        }
                    });

                });
                console.log("Product Map:", productMap);

                // Convert to array and calculate final quantities
                Object.keys(productMap).forEach(function(key) {
                    var product = productMap[key];
                    $scope.calculateFinalQuantity(product);
                    $scope.productConsumptionList.push(product);
                });

                // Store original data (deep copy)
                $scope.originalMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));
                console.log("Original Menu Products Data Stored:", $scope.originalMenuProductsData);

                console.log("Processed Product Consumption List:", $scope.productConsumptionList);
            }

            $scope.calculateFinalQuantity = function(product) {
                var total = 0;
                // Sum up ordering days quantities
                Object.keys(product.orderingDaysData).forEach(function(dateKey) {
                    var quantity = parseFloat(product.orderingDaysData[dateKey]) || 0;
                    total += quantity;
                });

                product.finalQuantity = total;
            };

            $scope.saveConsumptionData = function() {
                if ($scope.productConsumptionList.length === 0) {
                    $toastService.create("No data to save!");
                    return;
                }

                var saveData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    productConsumptionData: $scope.productConsumptionList,
                    dataEntry: $scope.dataEntry,
                    savedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.saveMenuProductsConsumption,
                    data: saveData
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Consumption data saved successfully!");
                    } else {
                        $toastService.create("Error saving consumption data!");
                    }
                }, function error(response) {
                    console.log("Save Error:", response);
                    $toastService.create("Error saving consumption data!");
                });
            };

            $scope.resetData = function() {
                if (confirm("Are you sure you want to reset all data?")) {
                    $scope.productConsumptionList.forEach(function(product) {
                        Object.keys(product.remainingDaysData).forEach(function(key) {
                            product.remainingDaysData[key] = 0;
                        });
                        Object.keys(product.orderingDaysData).forEach(function(key) {
                            product.orderingDaysData[key] = 0;
                        });
                        product.finalQuantity = 0;
                    });
                    $toastService.create("Data reset successfully!");
                }
            };

            $scope.goBackToSelection = function() {
                $scope.showProductsList = false;
                $scope.productConsumptionList = [];
            };

            $scope.dateformatting = function (date) {
                return appUtil.formatDate(new Date(date), "yyyy-MM-dd");
            };

            // SCM Products Functions
            $scope.getScmProductsConsumption = function() {
                if ($scope.productConsumptionList.length === 0) {
                    $toastService.create("Please fetch menu products data first!");
                    return;
                }

                // Store modified data (current state with user changes)
                $scope.modifiedMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));

                // Show comparison in console
                console.log("=== DATA TRACKING ===");
                console.log("Original Data:", $scope.originalMenuProductsData);
                console.log("Modified Data:", $scope.modifiedMenuProductsData);
                console.log("=== END TRACKING ===");

                $alertService.alert("Please Wait..!", "<b>Fetching SCM products consumption data...</b>", function (result) {
                }, false);

                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    originalMenuProductsData: $scope.originalMenuProductsData, // Backend actual data
                    modifiedMenuProductsData: $scope.modifiedMenuProductsData, // User modified data
                    dates: $scope.dataEntry.map(function(entry) { return entry.date; }),
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    requestedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getScmProductsConsumptionFromMenu,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        $toastService.create("SCM products consumption data fetched successfully!");
                        processScmConsumptionData(response.data);
                        $scope.showScmProductsList = true;
                        $scope.showProductsList = false; // Hide menu products
                        $alertService.closeAlert();
                    } else {
                        $toastService.create("No SCM data found!");
                        $alertService.closeAlert();
                    }
                }, function error(response) {
                    console.log("SCM Error:", response);
                    $toastService.create("Error fetching SCM products data!");
                    $alertService.closeAlert();
                });
            };

            function processScmConsumptionData(scmConsumptionMap) {
                $scope.scmProductConsumptionList = [];

                // Process Map<Date, List<ScmProductSalesAverageDto>>
                var scmProductMap = {};

                // Loop through each date in the map
                Object.keys(scmConsumptionMap).forEach(function(dateKey) {
                    var scmProductsList = scmConsumptionMap[dateKey];

                    scmProductsList.forEach(function(scmProduct) {
                        var scmProductKey = scmProduct.productId + "_" + scmProduct.dimension;

                        if (!scmProductMap[scmProductKey]) {
                            scmProductMap[scmProductKey] = {
                                productId: scmProduct.productId,
                                productName: scmProduct.productName,
                                dimension: scmProduct.dimension,
                                unitOfMeasure: scmProduct.unitOfMeasure,
                                remainingDaysData: {},
                                orderingDaysData: {},
                                finalQuantity: 0
                            };
                        }

                        var date = new Date(dateKey);
                        var isOrderingDay = $scope.orderingDays.some(function(orderingDate) {
                            return orderingDate.toDateString() === date.toDateString();
                        });

                        if (isOrderingDay) {
                            scmProductMap[scmProductKey].orderingDaysData[date.getTime()] = scmProduct.averageQuantity || 0;
                        } else {
                            scmProductMap[scmProductKey].remainingDaysData[date.getTime()] = scmProduct.averageQuantity || 0;
                        }
                    });
                });

                // Convert to array and calculate final quantities
                Object.keys(scmProductMap).forEach(function(key) {
                    var scmProduct = scmProductMap[key];
                    calculateScmFinalQuantity(scmProduct);
                    $scope.scmProductConsumptionList.push(scmProduct);
                });

                console.log("Processed SCM Product Consumption List:", $scope.scmProductConsumptionList);
            }

            $scope.calculateScmFinalQuantity = function(scmProduct) {
               var total = 0;

               // Sum up ordering days quantities
               Object.keys(scmProduct.orderingDaysData).forEach(function(dateKey) {
                   var quantity = parseFloat(scmProduct.orderingDaysData[dateKey]) || 0;
                   total += quantity;
               });

               scmProduct.finalQuantity = total;
            };

            $scope.goBackToMenuProducts = function() {
                $scope.showScmProductsList = false;
                $scope.showProductsList = true;

                // Update modified data when going back
                $scope.modifiedMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));
                console.log("Updated Modified Data on Back:", $scope.modifiedMenuProductsData);
            };

            $scope.saveScmConsumptionData = function() {
                if ($scope.scmProductConsumptionList.length === 0) {
                    $toastService.create("No SCM data to save!");
                    return;
                }

                var saveData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    scmProductConsumptionData: $scope.scmProductConsumptionList,
                    originalMenuProductsData: $scope.originalMenuProductsData, // Original backend data
                    modifiedMenuProductsData: $scope.modifiedMenuProductsData, // User modified data
                    currentMenuProductsData: $scope.productConsumptionList, // Current state
                    dataEntry: $scope.dataEntry,
                    savedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.saveScmProductsConsumption,
                    data: saveData
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("SCM consumption data saved successfully!");
                    } else {
                        $toastService.create("Error saving SCM consumption data!");
                    }
                }, function error(response) {
                    console.log("SCM Save Error:", response);
                    $toastService.create("Error saving SCM consumption data!");
                });
            };

            $scope.resetScmData = function() {
                if (confirm("Are you sure you want to reset all SCM data?")) {
                    $scope.scmProductConsumptionList.forEach(function(scmProduct) {
                        Object.keys(scmProduct.remainingDaysData).forEach(function(key) {
                            scmProduct.remainingDaysData[key] = 0;
                        });
                        Object.keys(scmProduct.orderingDaysData).forEach(function(key) {
                            scmProduct.orderingDaysData[key] = 0;
                        });
                        scmProduct.finalQuantity = 0;
                    });
                    $toastService.create("SCM data reset successfully!");
                }
            };

            // Reset all tracking data
            $scope.resetAllData = function() {
                if (confirm("Are you sure you want to reset all data including tracking?")) {
                    $scope.originalMenuProductsData = [];
                    $scope.modifiedMenuProductsData = [];
                    $scope.productConsumptionList = [];
                    $scope.scmProductConsumptionList = [];
                    $scope.showProductsList = false;
                    $scope.showScmProductsList = false;
                    $toastService.create("All data reset successfully!");
                }
            };

        }]);
