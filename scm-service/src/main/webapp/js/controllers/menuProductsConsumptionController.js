angular.module('scmApp')
    .controller('menuProductsConsumptionController', ['$rootScope', '$scope', '$stateParams', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'metaDataService', '$alertService', '$timeout', 'recipeService',
        function ($rootScope, $scope, $stateParams, apiJson, $http, appUtil, $toastService, $state, metaDataService, $alertService, $timeout, recipeService) {

            $scope.init = function () {
                console.log("Menu Products Consumption Controller Initialized");
                $scope.scmProductDetailsProductMap = {};
                angular.forEach(appUtil.getScmProductDetails(), function (prod) {
                    $scope.scmProductDetailsProductMap[prod.productId] = prod;
                });
                $scope.selectedBrandDetails = null;
                $scope.fulfillmentDate = null;
                $scope.noOfDays = 3;
                $scope.raiseBy=false;
                $scope.dataEntry = [];
                $scope.showProductsList = false;
                $scope.productConsumptionList = [];
                $scope.remainingDays = [];
                $scope.orderingDays = [];

                // SCM Products variables
                $scope.showScmProductsList = false;
                $scope.scmProductConsumptionList = [];

                // Packaging Products variables (like refOrderCreateV2)
                $scope.showPackagingProductsList = false;
                $scope.packagingProductList = [];
                metaDataService.getUnitProductData(function (unit) {
                    $scope.unitData = unit;
                    $scope.scmProductDetails = appUtil.getScmProductDetails();
                    $scope.menuCategories = recipeService.createMenuItemCategories();
                });

                // Data Tracking variables
                $scope.originalMenuProductsData = [];
                $scope.modifiedMenuProductsData = [];
                $scope.orderingPercentages = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

                // Date-wise SCM Products variables
                $scope.showScmProductsDateWise = false;
                $scope.scmProductsDateWiseList = [];

                // Date-wise SCM Packaging variables
                $scope.showScmPackagingListDateWise = false;
                $scope.scmPackagingProductListDateWise = [];
                $scope.scmPackagingCategoriesDateWise = [];

                // Separate arrays for Kitchen and Warehouse
                $scope.kitchenProductsDateWise = [];
                $scope.warehouseProductsDateWise = [];
                $scope.showWarehouseProducts = false; // Checkbox state
                $scope.showSpecializedRoProducts = false; // Checkbox state

                $scope.brandList = appUtil.getBrandList();
                $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                $scope.comment = null;
                $scope.minRefOrderFulfillmentDate = appUtil.getDate(1);
                $scope.maxRefOrderFulfillmentDate = appUtil.getDate(7);
                console.log($scope.minRefOrderFulfillmentDate + "," + $scope.maxRefOrderFulfillmentDate);
                $scope.brandDetails = [{id: 1, brandName: "CHAAYOS"},
                    {id: 3, brandName: "GNT"},
                    {id: 6, brandName: "DOHFUL"},
                    {id: 0, brandName: "CHAAYOS_AND_GNT"}];
                $scope.selectedBrandDetails = $scope.brandDetails[0];
                // Hide loader after initialization
                $timeout(function() {
                    $rootScope.showSpinner = false;
                }, 100);
            };

             $scope.setBrand = function (brand) {
                $scope.selectedBrandDetails = brand;
                $scope.setDates($scope.fulfillmentDate, 2);
             };
             function makeDateString(date) {
                 var newDate = new Date(date);
                 var result = newDate.getFullYear() + "-" + (newDate.getMonth() + 1) + "-" + (newDate.getDate());
                 console.log("result Date is : ", result);
                 return result;
             }
             function weekDays() {
                 var days = [
                     {id: 1, value: 'Sunday'},
                     {id: 2, value: 'Monday'},
                     {id: 3, value: 'Tuesday'},
                     {id: 4, value: 'Wednesday'},
                     {id: 5, value: 'Thursday'},
                     {id: 6, value: 'Friday'},
                     {id: 7, value: 'Saturday'}];
                 return days;
             }
            $scope.getDayOfWeek = getDayOfWeek;
            function getDayOfWeek(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                console.log('Selected Date', date.getDay());
                return weekDays()[date.getDay()].value;
            }

            $scope.preventDecimal = function(event) {
                if (event.key === '.' || event.key === 'e' || event.key === '-') {
                    event.preventDefault();
                }
            };

            var debounceTimer = null;
            $scope.isApiInProgress = false;
            $scope.setDates = function (fulfillmentDate, noOfDays) {
                if (debounceTimer) {
                    $timeout.cancel(debounceTimer);
                }
                debounceTimer = $timeout(function () {
                   try {
                         if ($scope.isApiInProgress) return;
                         $scope.isApiInProgress = true;
                         $scope.dataEntry = [];
                         $scope.remainingDays = [];
                         $scope.orderingDays = [];
                         $scope.onlyOrderingDays = [];
                         var maxAllowedDays=7;

                         if (!noOfDays || isNaN(noOfDays) || noOfDays <= 0) {
                            $toastService.create("Please Enter Ordering Days..!");
                            $scope.noOfDays = 0;
                            $scope.isApiInProgress = false;
                            return;
                         }

                         if (noOfDays > maxAllowedDays) {
                            $toastService.create("Ordering Days cannot be more than " + maxAllowedDays + "  for selected brand.");
                            console.log("maxAllowedDays, days is : ", maxAllowedDays, noOfDays);
                            $scope.noOfDays = 0;
                            $scope.isApiInProgress = false;
                            return;
                         }

                         $scope.fulfillmentDate = $scope.regularOrderingEvent != null ? makeDateString($scope.regularOrderingEvent.fulfilmentDate) : makeDateString(fulfillmentDate);
                         var regularOrderingDate = appUtil.getRegularOrderingDate();
                         $scope.noOfRemainingDays = appUtil.datediffRO(regularOrderingDate, $scope.fulfillmentDate);
                         $scope.noOfDays = $scope.regularOrderingEvent != null ? $scope.regularOrderingEvent.orderingDays : noOfDays;
                         $scope.stockLastingDate = appUtil.calculatedDate($scope.noOfDays - 1, $scope.fulfillmentDate);
                         $scope.stockLastingDay = $scope.getDayOfWeek($scope.stockLastingDate);
                         if (new Date($scope.stockLastingDate) > new Date($scope.maxRefOrderFulfillmentDate)) {
                            $toastService.create(" Ordering days go outside the allowed window (from " + appUtil.formatDate($scope.minRefOrderFulfillmentDate, 'yyyy-MM-dd') + " to " + appUtil.formatDate($scope.maxRefOrderFulfillmentDate, 'yyyy-MM-dd') + ")");
                            $scope.noOfDays = 0;
                            $scope.stockLastingDate = null;
                            $scope.stockLastingDay = null;
                            $scope.isApiInProgress = false;
                            return;
                         }
                         // Create data entry for days
                         for (var i = 1; i <= $scope.noOfRemainingDays - 1; i++) {
                             $scope.dataEntry.push({
                                 dayType: 'REMAINING_DAY',
                                 date: appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"),
                                 brands: [{
                                     id: $scope.chaayosId,
                                     saleAmount: 0,
                                     deliverySalePercentage: 0
                                 },
                                     {
                                         id: $scope.gntId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     },
                                     {
                                         id: $scope.dcId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     }],
                                 orderingPercentage : 100

                             })
                             $scope.remainingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                         }
                         for (var i = 0; i <= $scope.noOfDays - 1; i++) {
                             $scope.dataEntry.push({
                                 dayType: 'ORDERING_DAY',
                                 date: appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"),
                                 brands: [{
                                     id: $scope.chaayosId,
                                     saleAmount: 0,
                                     deliverySalePercentage: 0
                                 },
                                     {
                                         id: $scope.gntId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     },
                                     {
                                         id: $scope.dcId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     }],
                                 orderingPercentage : 100
                             })
                             $scope.orderingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                         }
                   } catch (e) {
                     console.log("Error in setDates: ", e);
                   } finally {
                     $timeout(function() {
                         $scope.isApiInProgress = false;
                     });
                   }
                }, 500);
            };

            $scope.getMenuProductsConsumption = function () {
                if (!$scope.selectedBrandDetails || !$scope.fulfillmentDate || !$scope.noOfDays) {
                    $toastService.create("Please select all required fields!");
                    return;
                }

                $alertService.alert("Please Wait..!", "<b>Fetching menu products consumption data...</b>", function (result) {
                }, false);

                var dayList = $scope.dataEntry.map(function(entry) {
                    return entry.date;
                });

                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    dayList: dayList,
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    requestedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getMenuProductsConsumptionAverage,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        $toastService.create("Menu products consumption data fetched successfully!");
                        console.log("Menu Products Consumption Data:", response.data);
                        processMenuProductsConsumptionData(response.data);
                        $scope.showProductsList = true;
                        $alertService.closeAlert();
                    } else {
                        $toastService.create("No data found for the selected criteria!");
                        $alertService.closeAlert();
                    }
                }, function error(response) {
                    console.log("Error:", response);
                    $toastService.create("Error fetching consumption data!");
                    $alertService.closeAlert();
                });
            };

            function processMenuProductsConsumptionData(consumptionMap) {
                $scope.productConsumptionList = [];

                var productMap = {};

                // Loop through each date in the map
                Object.keys(consumptionMap).forEach(function(dateKey) {
                    var productsList = consumptionMap[dateKey];

                    var formattedDate = $scope.dateFormatting(dateKey);
                    var isOrderingDay = $scope.orderingDays.includes(formattedDate);

                    productsList.forEach(function(product) {
                        var productKey = product.productId + "_" + product.dimension;

                        if (!productMap[productKey]) {
                            productMap[productKey] = {
                                productId: product.productId,
                                productName: product.productName,
                                dimension: product.dimension,
                                remainingDaysData: {},
                                orderingDaysData: {},
                                finalQuantity: 0
                            };
                        }

                        if (isOrderingDay) {
                            productMap[productKey].orderingDaysData[formattedDate] = product.averageSalesQuantity || 0;
                        } else {
                            productMap[productKey].remainingDaysData[formattedDate] = product.averageSalesQuantity || 0;
                        }
                    });

                });
                console.log("Product Map:", productMap);

                // Convert to array and calculate final quantities
                Object.keys(productMap).forEach(function(key) {
                    var product = productMap[key];
                    $scope.calculateFinalQuantity(product);
                    $scope.productConsumptionList.push(product);
                });

                // Store original data (deep copy)
                $scope.originalMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));

                console.log("Original Menu Products Data Stored:", $scope.originalMenuProductsData);

                console.log("Processed Product Consumption List:", $scope.productConsumptionList);
            }

            $scope.calculateFinalQuantity = function(product) {
                var total = 0;
                // Sum up ordering days quantities
                Object.keys(product.orderingDaysData).forEach(function(dateKey) {
                    var quantity = product.orderingDaysData[dateKey] || 0;
                    total += quantity;
                });

                product.finalQuantity = total;
            };

            $scope.goBackToSelection = function() {
                $scope.showProductsList = false;
                $scope.productConsumptionList = [];
            };

            $scope.dateFormatting = function (date) {
                return appUtil.formatDate(new Date(date), "yyyy-MM-dd");
            };

            // Rounding utility function
            function roundQuantity(quantity) {
                if (!quantity || quantity === 0) return 0;
                var decimal = quantity - Math.floor(quantity);
                if (decimal < 0.5) {
                    return Math.floor(quantity);
                } else {
                    return Math.ceil(quantity);
                }
            }

            // DATE-WISE SCM Products Function (New Format)
            $scope.getScmProductsFromMenuDateWise = function() {
                if ($scope.productConsumptionList.length === 0) {
                    $toastService.create("Please fetch menu products data first!");
                    return;
                }

                // Store modified data (current state with user changes)
                $scope.modifiedMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));
                console.log("Modified Menu Products Data Stored:", $scope.modifiedMenuProductsData);

                $alertService.alert("Please Wait..!", "<b>Fetching SCM products from menu (date-wise)...</b>", function (result) {
                }, false);

                // Prepare date-wise menu products data
                var dateWiseMenuData = prepareDateWiseMenuData();
                console.log("Date-wise Menu Data for API:", dateWiseMenuData);

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getScmProductsFromMenuDateWise,
                    params: {
                        unitId: appUtil.getCurrentUser().unitId
                    },
                    data: dateWiseMenuData
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        console.log("Date-wise SCM Products Response:", response.data);
                        processDateWiseScmProducts(response.data);

                        // Directly call packaging logic after successful SCM data fetch
                        $alertService.alert("Please Wait..!", "<b>Processing packaging logic...</b>", function (result) {
                        }, false);

                        // Call packaging logic directly
                        $scope.submitScmProductsForPackagingDateWise();

                        $toastService.create("Date-wise SCM products fetched and packaging processed successfully!");
                    } else {
                        $toastService.create("No SCM products found for the selected dates!");
                        $alertService.closeAlert();
                    }
                }, function error(response) {
                    console.log("Date-wise SCM Error:", response);
                    $toastService.create("Error fetching date-wise SCM products!");
                    $alertService.closeAlert();
                });
            };

            // Prepare date-wise menu products data for API
            function prepareDateWiseMenuData() {
                var dateWiseData = [];

                // Group menu products by date
                var dateProductMap = {};

                // Loop through all dates (remaining + ordering)
                var allDates = $scope.remainingDays.concat($scope.orderingDays);

                allDates.forEach(function(date) {
                    var dateString = $scope.dateFormatting(date);
                    dateProductMap[dateString] = [];

                    // Add products for this date
                    $scope.modifiedMenuProductsData.forEach(function(menuProduct) {
                        var quantity = 0;

                        // Check if this date is in remaining days or ordering days
                        var isOrderingDay = $scope.orderingDays.includes(dateString);
                        var isRemainingDay = $scope.remainingDays.includes(dateString);

                        // Get quantity from appropriate data source
                        if (isOrderingDay && menuProduct.orderingDaysData && menuProduct.orderingDaysData[date]) {
                            quantity = menuProduct.orderingDaysData[date] || 0;
                        } else if (isRemainingDay && menuProduct.remainingDaysData && menuProduct.remainingDaysData[date]) {
                            quantity = menuProduct.remainingDaysData[date] || 0;
                        }

                        // Only add if quantity > 0
                        if (quantity > 0) {
                            dateProductMap[dateString].push({
                                productId: menuProduct.productId,
                                dimension: menuProduct.dimension || "None",
                                quantity: parseFloat(quantity)
                            });
                        }
                    });
                });

                // Convert to required format
                Object.keys(dateProductMap).forEach(function(dateString) {
                    if (dateProductMap[dateString].length > 0) {
                        dateWiseData.push({
                            date: dateString,
                            products: dateProductMap[dateString]
                        });
                    }
                });

                return dateWiseData;
            }

            // Process date-wise SCM products response
            function processDateWiseScmProducts(dateWiseScmData) {
                $scope.scmProductsDateWiseList = [];

                console.log("Processing date-wise SCM data:", dateWiseScmData);

                // Process response data
                var scmProductMap = {};

                dateWiseScmData.forEach(function(dateData) {
                    var dateString = dateData.date;
                    var scmProducts = dateData.products || [];

                    scmProducts.forEach(function(scmProduct) {
                        var scmProductKey = scmProduct.productId + "_" + (scmProduct.dimension || "None");

                        if (!scmProductMap[scmProductKey]) {
                            scmProductMap[scmProductKey] = {
                                productId: scmProduct.productId,
                                productName: scmProduct.productName || scmProduct.name || "Unknown Product",
                                dimension: scmProduct.dimension || "None",
                                uom: scmProduct.uom || "PC",
                                remainingDaysData: {},
                                orderingDaysData: {},
                                finalQuantity: 0
                            };
                        }

                        // Check if this date is in ordering days or remaining days
                        var isOrderingDay = $scope.orderingDays.includes(dateString);
                        var isRemainingDay = $scope.remainingDays.includes(dateString);

                        if (isOrderingDay) {
                            scmProductMap[scmProductKey].orderingDaysData[dateString] = scmProduct.quantity || 0;
                        } else if (isRemainingDay) {
                            scmProductMap[scmProductKey].remainingDaysData[dateString] = scmProduct.quantity || 0;
                        }
                    });
                });

                console.log("SCM Product Map:", scmProductMap);

                // Convert to array and calculate final quantities
                Object.keys(scmProductMap).forEach(function(key) {
                    var scmProduct = scmProductMap[key];
                    $scope.calculateScmFinalQuantityDateWise(scmProduct);
                    $scope.scmProductsDateWiseList.push(scmProduct);
                });

                $scope.originalScmProductsData = JSON.parse(JSON.stringify($scope.scmProductsDateWiseList));
                console.log("Original Date-wise SCM Products Data Stored:", $scope.originalScmProductsData);

                console.log("Processed Date-wise SCM Products List:", $scope.scmProductsDateWiseList);
            }

            $scope.calculateScmFinalQuantityDateWise = function(scmProduct) {
                 var total = 0;

                 // Sum up ordering days quantities
                 Object.keys(scmProduct.orderingDaysData).forEach(function(dateKey) {
                    var quantity = scmProduct.orderingDaysData[dateKey] || 0;
                    total += quantity;
                 });

                 // Apply rounding logic
                 scmProduct.finalQuantity = total;
                 scmProduct.packagingSize=scmProduct.packagingSize || 10;
                 scmProduct.packagingQuantity = Math.ceil(scmProduct.finalQuantity / scmProduct.conversionRatio);
                 scmProduct.orderingQuantity = scmProduct.packagingQuantity * scmProduct.conversionRatio;

            };

            // SUBMIT - GET PACKAGING PRODUCTS for Date-wise SCM
            $scope.submitScmProductsForPackagingDateWise = function() {
                if ($scope.scmProductsDateWiseList.length === 0) {
                    $toastService.create("No SCM products data to submit!");
                    return;
                }

                // Store current user changes
                $scope.modifiedScmProductsMap = {};
                $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                    var productKey = scmProduct.productId + "_" + (scmProduct.dimension || "None");
                    $scope.modifiedScmProductsMap[productKey] = {
                        finalQuantity: scmProduct.finalQuantity,
                        remainingDaysData: JSON.parse(JSON.stringify(scmProduct.remainingDaysData)),
                        orderingDaysData: JSON.parse(JSON.stringify(scmProduct.orderingDaysData))
                    };
                });

                console.log("Modified SCM Products Map:", $scope.modifiedScmProductsMap);

                $alertService.alert("Please Wait..!", "<b>Processing SCM products for packaging (similar to suggestive ordering)...</b>", function (result) {
                }, false);

                // Prepare data for recipeService (similar to suggestive ordering)
                var scmProductWiseMap = {};
                var totalScmSuggestions = {};
                var productWiseStock = {};
                var productMap = {};

                $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                    var productId = scmProduct.productId;

                    // Map for recipeService (similar to suggestive ordering)
                    scmProductWiseMap[productId] = scmProduct.finalQuantity;
                    totalScmSuggestions[productId] = scmProduct.finalQuantity;

                    // Product map
                    productMap[productId] = {
                        productId: productId,
                        productName: scmProduct.productName,
                        unitOfMeasure: scmProduct.uom,
                        stockAtHand: 0, // Will be fetched from backend
                        inTransit: 0
                    };

                    // Stock map (will be populated by recipeService)
                    productWiseStock[productId] = {
                        stockAtHand: 0,
                        inTransit: 0
                    };
                });

                console.log("SCM Product Wise Map:", scmProductWiseMap);
                console.log("Total SCM Suggestions:", totalScmSuggestions);
                console.log("Product Wise Stock:", productWiseStock);
                console.log("Product Map:", productMap);
                console.log("scmProductDetails Products Map:", $scope.scmProductDetailsProductMap);

                // Use recipeService similar to suggestive ordering createSCMOrderingProductList
                recipeService.getScmProductsFromSuggestions(scmProductWiseMap,productWiseStock,totalScmSuggestions,
                    {}, // fulfilmentUnitProductsMap
                    $scope.scmProductDetailsProductMap, function (itemList) {
                        console.log("After packaging logic items in SCM Products are:", itemList);

                        // Map packaging data back to original SCM products
                        var packagingDataMap = {};
                        var specializedRoProductsMap = {};
                        itemList.forEach(function (item) {
                            if (item.selectedFulfillmentType != null && item.selectedFulfillmentType !== "EXTERNAL") {
                                packagingDataMap[item.productId] = {
                                    selectedFulfillmentType: item.selectedFulfillmentType,
                                    packagingSize: item.packagingSize || item.conversionRatio || 10,
                                    stockAtHand: item.stockAtHand || 0,
                                    inTransit: item.inTransit || 0,
                                    stock: (item.stockAtHand || 0) + (item.inTransit || 0),
                                    packagingQuantity: item.packagingQuantity,
                                    packagingName: item.packagingName,
                                    orderingQuantity: item.orderingQuantity,
                                    conversionRatio: item.conversionRatio
                                };
                            }
                             else if (item.supportsSpecialOrdering && item.id !== 100234) {
                                 specializedRoProductsMap[item.productId] = item;
                             }
                        });

                        console.log("Packaging Data Map:", packagingDataMap);
                        console.log("Original SCM Products:", $scope.scmProductsDateWiseList);
                        console.log("Specialized RO Products Map:", specializedRoProductsMap);

                        // Reset arrays
                        $scope.kitchenProductsDateWise = [];
                        $scope.warehouseProductsDateWise = [];
                        $scope.specializedRoProducts = [];

                        // Categorize original SCM products by Kitchen and Warehouse
                        $scope.scmProductsDateWiseList.forEach(function (scmProduct) {
                            var packagingData = packagingDataMap[scmProduct.productId];

                            if (packagingData) {
                                // Add packaging info to original SCM product
                                scmProduct.selectedFulfillmentType = packagingData.selectedFulfillmentType;
                                scmProduct.packagingSize = packagingData.packagingSize || 10;
                                scmProduct.stockAtHand = packagingData.stockAtHand;
                                scmProduct.inTransit = packagingData.inTransit;
                                scmProduct.stock = packagingData.stock;
                                scmProduct.orderingQuantity = packagingData.orderingQuantity;
                                scmProduct.packagingQuantity = packagingData.packagingQuantity;
                                scmProduct.packagingName = packagingData.packagingName;
                                scmProduct.conversionRatio = packagingData.conversionRatio;

                                // Extract dynamic packaging info from recipeService
                                var recipeItem = itemList.find(function(item) {
                                    return item.productId === scmProduct.productId;
                                });

                                if (recipeItem && recipeItem.moqData) {
                                    // Use MOQ data for dynamic packaging
                                    scmProduct.packagingUnit = recipeItem.moqData.packagingType || 'packets';
                                    scmProduct.packagingSize = recipeItem.moqData.packagingSize || packagingData.packagingSize;
                                    scmProduct.packagingQuantityCalculated = scmProduct.packagingQuantity;
                                } else {
                                    // Fallback to default packaging
                                    scmProduct.packagingUnit = getPackagingUnit(scmProduct.uom);
                                    scmProduct.packagingSize = packagingData.packagingSize;
                                    scmProduct.packagingQuantityCalculated = scmProduct.packagingQuantity;
                                }

                                scmProduct.packagingQuantityCalculated = scmProduct.packagingQuantityCalculated;
                                scmProduct.packagingUnit = packagingData.packagingUnit || 'packet'; // optional

                                // Categorize by fulfillment type
                                if (packagingData.selectedFulfillmentType === "KITCHEN") {
                                    $scope.kitchenProductsDateWise.push(scmProduct);
                                } else if (packagingData.selectedFulfillmentType === "WAREHOUSE") {
                                    $scope.warehouseProductsDateWise.push(scmProduct);
                                }
                            }
                            else {
                                // If no packaging data, default to external
                                var specializedRoProduct = specializedRoProductsMap[scmProduct.productId];
                                if(specializedRoProduct) {
                                    specializedRoProduct.selectedFulfillmentType === "EXTERNAL";
                                    specializedRoProduct.packagingUnit = getPackagingUnit(scmProduct.uom);
                                    specializedRoProduct.packagingSize = scmProduct.packagingSize;
                                    specializedRoProduct.packagingQuantity = Math.ceil(specializedRoProduct.finalQuantity / specializedRoProduct.conversionRatio) || 0;
                                    specializedRoProduct.orderingQuantity = specializedRoProduct.packagingQuantity * specializedRoProduct.conversionRatio || 0;
                                    specializedRoProduct.orderingDaysData = scmProduct.orderingDaysData;
                                    specializedRoProduct.remainingDaysData = scmProduct.remainingDaysData;
                                    specializedRoProduct.finalQuantity = scmProduct.finalQuantity;
                                    $scope.specializedRoProducts.push(specializedRoProduct);
                                }
                            }
                        });

                        console.log("Kitchen Products:", $scope.kitchenProductsDateWise);
                        console.log("Warehouse Products:", $scope.warehouseProductsDateWise);

                        // Create categorized structure with original SCM products
                        $scope.scmPackagingCategoriesDateWise = [
                            {
                                categoryName: "KITCHEN",
                                products: $scope.kitchenProductsDateWise
                            },
                            {
                                categoryName: "WAREHOUSE",
                                products: $scope.warehouseProductsDateWise
                            }
                        ];

                        console.log("SCM Packaging Product List (Date-wise):", $scope.scmPackagingProductListDateWise);
                        $alertService.closeAlert();
                        $toastService.create("SCM packaging products processed successfully!");
                        return itemList;
                    }
                );

                $scope.showScmPackagingListDateWise = true;
                $scope.showProductsList = false; // Hide SCM consumption table
            };

            $scope.backToScmConsumptionDateWise = function() {
                if (confirm("Are you sure you want to go back?")) {
                    $scope.scmPackagingProductListDateWise = [];
                    $scope.showScmPackagingListDateWise = false;
                    $scope.showScmProductsDateWise = true;
                }
            };

            $scope.updateScmOrderingQtyDateWise = function (product) {
                product.packagingQuantity = parseInt(product.packagingQuantity);
                product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
            };

            $scope.goBackToMenuProductsDateWise = function() {
                $scope.showScmPackagingListDateWise = false;
                $scope.showProductsList = true;
                 $scope.originalScmProductsData =[];

                // Update modified data when going back
                $scope.modifiedMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));
                console.log("Updated Modified Data on Back:", $scope.modifiedMenuProductsData);
            };

            $scope.saveScmProductsDateWise = function() {
                if ($scope.scmProductsDateWiseList.length === 0) {
                    $toastService.create("No date-wise SCM data to save!");
                    return;
                }

                var saveData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    scmProductsDateWise: $scope.scmProductsDateWiseList,
                    predictedScmDataMap: $scope.predictedScmDataMap,
                    originalMenuProductsData: $scope.originalMenuProductsData,
                    modifiedMenuProductsData: $scope.modifiedMenuProductsData,
                    dataEntry: $scope.dataEntry,
                    savedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.saveScmProductsDateWise,
                    data: saveData
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Date-wise SCM data saved successfully!");
                    } else {
                        $toastService.create("Error saving date-wise SCM data!");
                    }
                }, function error(response) {
                    console.log("Date-wise SCM Save Error:", response);
                    $toastService.create("Error saving date-wise SCM data!");
                });
            };

            $scope.resetScmProductsDateWise = function() {
                if (confirm("Are you sure you want to reset all date-wise SCM data?")) {
                    $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                        Object.keys(scmProduct.remainingDaysData).forEach(function(key) {
                            scmProduct.remainingDaysData[key] = 0;
                        });
                        Object.keys(scmProduct.orderingDaysData).forEach(function(key) {
                            scmProduct.orderingDaysData[key] = 0;
                        });
                        scmProduct.finalQuantity = 0;
                    });
                    $toastService.create("Date-wise SCM data reset successfully!");
                }
            };

            // Get packaging unit based on UOM
            function getPackagingUnit(uom) {
                if (!uom) return 'packets';

                var uomLower = uom.toLowerCase();
                if (uomLower.includes('kg') || uomLower.includes('gm') || uomLower.includes('gram')) {
                    return 'sachets';
                } else if (uomLower.includes('ml') || uomLower.includes('ltr') || uomLower.includes('litre')) {
                    return 'bottles';
                } else if (uomLower.includes('pc') || uomLower.includes('piece')) {
                    return 'packets';
                } else {
                    return 'units';
                }
            }

            // Warehouse toggle function (like suggestive ordering)
            $scope.toggleWarehouseProducts = function() {
                console.log("Warehouse products visibility:", $scope.showWarehouseProducts);
            };

            $scope.toggleSpecializedRoProducts = function() {
                console.log("Specialized RO products visibility:", $scope.showSpecializedRoProducts);
            };

            // Send Reference Order (Similar to Suggestive Ordering)
            $scope.sendReferenceOrder = function (action) {
                var data = createRoObjectMenuConsumption(action);
                var validateOrder = validateFulfillmentMenuConsumption(data);

                if ($scope.fulfillmentDate == null) {
                    $toastService.create('Please fill fulfillment date!');
                    return false;
                } else if (data.referenceOrderScmItems.length == 0) {
                    $toastService.create('Please select at least a few products to order!');
                    return false;
                } else if (validateOrder.length > 0) {
                    $alertService.alert('Products found without fulfillment types', validateOrder.join(","), null, true);
                    return false;
                } else {
                    data.estimationSalesDataRequests = $scope.dataEntry;
                    data.refOrderSource = "MENU_PRODUCTS_CONSUMPTION";
                    console.log("Reference Order Data Request sending to backend:", data);

                    if (confirm("Are you sure you want to create the order?")) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.referenceOrderManagement.referenceOrder,
                            data: data
                        }).then(function success(response) {
                            console.log("Reference Order Response:", response);
                            if (response.data != null && response.data.referenceOrderId > 0) {
                                $toastService.create("Reference order with id " + response.data.referenceOrderId + " created successfully!");
                                if (response.data.regularOrderEvents != null && response.data.regularOrderEvents.length > 0) {
                                    $state.go("menu.refOrderCreateV1", {orderingEvents: response.data.regularOrderEvents});
                                } else {
                                    $rootScope.orderingEvents = [];
                                    $state.go("menu.reqOrderMgt");
                                }
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }, function error(response) {
                            console.log("Reference Order Error:", response);
                            $toastService.create("Error creating reference order!");
                        });
                    }
                }
            };

            // Create Reference Order Object (Similar to Suggestive Ordering)
            function createRoObjectMenuConsumption(status) {
                return {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    requestUnit: appUtil.createRequestUnit(),
                    generatedBy: appUtil.createGeneratedBy(),
                    fulfillmentUnit: null,
                    fulfillmentDate: new Date($scope.fulfillmentDate),
                    status: status,
                    comment: $scope.comment || "Created from Menu Products Consumption",
                    referenceOrderMenuItems: getMenuItemsConsumption(),
                    referenceOrderScmItems: getScmItemsConsumption(),
                    numberOfDays: $scope.noOfDays,
                    raiseBy: $scope.raiseBy,
                    orderEvent: null,
                    refreshDate: new Date(),
                    brand: $scope.selectedBrandDetails.brandName,
                    expiryUsageLogs: {}
                };
            }

            function getMenuItemsConsumption() {
                var menuItems = [];

                $scope.productConsumptionList.forEach(function(product) {

                    // Convert orderingDaysData and remainingDaysData to key-value maps
                    var dateOrderings = {};
                    if (product.orderingDaysData) {
                        Object.keys(product.orderingDaysData).forEach(function(date) {
                            dateOrderings[date] = parseSafeBigDecimal(product.orderingDaysData[date]);
                        });
                    }

                    var dateRemaining = {};
                    if (product.remainingDaysData) {
                        Object.keys(product.remainingDaysData).forEach(function(date) {
                            dateRemaining[date] = parseSafeBigDecimal(product.remainingDaysData[date]);
                        });
                    }

                    menuItems.push({
                        id: null,
                        productId: product.productId,
                        productName: product.productName || "",
                        dimension: product.dimension || null,
                        requestedQuantity: parseSafeBigDecimal(product.finalQuantity),
                        requestedAbsoluteQuantity: parseSafeBigDecimal(product.finalQuantity),  // You can change this if needed
                        transferredQuantity: null,
                        receivedQuantity: null,
                        quantity: null,
                        saleQuantity: null,
                        dineInQuantity: null,
                        deliveryQuantity: null,
                        takeawayQuantity: null,
                        variants: null, // or fill if needed
                        dateOrderings: dateOrderings,
                        dateRemaining: dateRemaining,
                        originalOrderingQuantity: safeParseFloat(product.originalOrderingQuantity),
                        originalSaleQuantity: null
                    });
                });

                return menuItems;
            }

            // Helper function to safely parse decimal fields
            function parseSafeBigDecimal(value) {
                var num = parseFloat(value);
                return isNaN(num) ? 0 : parseFloat(num.toFixed(6));
            }

            // Get SCM Items for Reference Order
            function getScmItemsConsumption() {
                var scmItems = [];

                var allProducts = $scope.kitchenProductsDateWise.concat($scope.warehouseProductsDateWise);

                allProducts.forEach(function (product) {

                    var dateOrderings = {};
                    if (product.orderingDaysData) {
                        Object.keys(product.orderingDaysData).forEach(function(date) {
                            dateOrderings[date] = parseSafeBigDecimal(product.orderingDaysData[date]);
                        });
                    }
                    product.dateOrderings = dateOrderings;

                    var dateRemaining = {};
                    if (product.remainingDaysData) {
                        Object.keys(product.remainingDaysData).forEach(function(date) {
                            dateRemaining[date] = parseSafeBigDecimal(product.remainingDaysData[date]);
                        });
                    }
                    product.dateRemaining = dateRemaining;

                    if (product.finalQuantity > 0) {
                        if ($scope.showWarehouseProducts) {
                            // Allow both Kitchen and Warehouse fulfillment
                            if (product.selectedFulfillmentType === 'KITCHEN' || product.selectedFulfillmentType === 'WAREHOUSE') {
                                scmItems.push(makeScmProductObject(product));
                            }
                        } else {
                            // Only allow Kitchen if warehouse ordering not enabled
                            if (product.selectedFulfillmentType === 'KITCHEN') {
                                scmItems.push(makeScmProductObject(product));
                            }
                        }
                    }
                });

                console.log("SCM Items for Reference Order:", scmItems);
                return scmItems;
            }

            function makeScmProductObject(product) {
                return {
                    id: null,
                    productId: product.productId,
                    productName: product.productName,
                    suggestedQuantity: parseFloat(parseFloat(product.suggestedQuantity).toFixed(6)),
                    requestedQuantity: parseFloat(parseFloat(product.orderingQuantity).toFixed(6)),
                    requestedAbsoluteQuantity: parseFloat(parseFloat(product.orderingQuantity).toFixed(6)),
                    suggestedQuantityBeforeMoq: parseFloat(parseFloat(product.suggestedOrderingQuantity).toFixed(6)),
                    transferredQuantity: null,
                    receivedQuantity: null,
                    fulfillmentType: product.selectedFulfillmentType,
                    unitOfMeasure: product.unitOfMeasure || product.uom,
                    predictedQuantity: parseFloat(parseFloat(product.predictedQuantity).toFixed(6)),
                    dateOrderings: product.dateOrderings,
                    dateRemaining: product.dateRemaining,
                    reason: !appUtil.isEmptyObject(product.reason) ? product.reason : null,
                };
            }

            // Validate Fulfillment Types
            function validateFulfillmentMenuConsumption(data) {
                var invalidProducts = [];

                data.referenceOrderScmItems.forEach(function(item) {
                    if (!item.fulfillmentType || item.fulfillmentType === "") {
                        invalidProducts.push(item.productName);
                    }
                });

                return invalidProducts;
            }

        }]);
