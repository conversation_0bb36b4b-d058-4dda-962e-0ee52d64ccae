angular.module('scmApp').controller(
    'vendorToSkuRequestCtrl',
    [
        '$rootScope',
        '$scope',
        '$interval',
        'apiJson',
        '$http',
        'appUtil',
        '$toastService',
        '$timeout',
        'previewModalService',
        'Popeye', '$alertService',
        function ($rootScope, $scope, $interval, apiJson, $http, appUtil, $toastService, $timeout, previewModalService,Popeye,$alertService) {

            $scope.init = function () {
                $scope.createSearchTypeList();
                $scope.getAllSkus();
                $scope.getAllVendors();
                $scope.getAllDeliveryLocations();
                $scope.priceGridOptions = $scope.locationGridOptions();
                $scope.showPreview = previewModalService.showPreview;
                $scope.acceptedPriceRequest = new Map();
                $scope.acceptedPriceRequestNew = new Map();
                $scope.rejectedPriceRequest = new Map();
                $scope.rejectedPriceRequestNew = new Map();
            };

            $scope.createSearchTypeList = function () {
                $scope.searchTypeList = [{
                    type: 'VENDOR',
                    name: 'Vendor'
                }];
            };

            $scope.showValues = function () {
                if ($scope.searchType == undefined || $scope.searchType == null) {
                    return;
                }
                $timeout(function () {
                    $('#selectSkuForPriceUpdateId').val(null).trigger('change');
                });
                $scope.priceGridOptions.data = null;
                if ($scope.isSKUValue()) {
                    $scope.searchValues = $scope.allSkuDataList;
                } else {
                    $scope.searchValues = $scope.allVendorDataList
                }
            };

            $scope.getAllSkus = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllSku
                }).then(function success(response) {
                    $scope.allSkuDataList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getVendorForSku = function (skuId) {
                $scope.vendorDataList = [];
                $http({
                    method: "POST",
                    dataType: 'json',
                    data: skuId,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getVendorForSku
                }).then(function success(response) {
                    $scope.vendorDataList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getSkuForVendor = function (vendorId) {
                $scope.skuDataList = [];
                $http({
                    method: "POST",
                    dataType: 'json',
                    data: vendorId,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getSkuForVendor
                }).then(function success(response) {
                    $scope.skuDataList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getAllVendors = function (skuId) {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllVendors,
                }).then(function success(response) {
                    $scope.allVendorDataList = response.data.filter(function(item){return item.category === 'VENDOR'});
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getAllDeliveryLocations = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.scmMetadata.activeDeliveryLocations,
                }).then(function success(response) {
                    $scope.allDeliveyLocationList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.showPricing = function () {
                if ($scope.sourceValue == undefined || $scope.sourceValue == null) {
                    return;
                }
                if ($scope.isSKUValue()) {
                    $scope.getPricingBySku();
                } else {
                    $scope.getPricingByVendor();
                }
            };

            $scope.isSKUValue = function () {
                return $scope.searchType != null && $scope.searchType.type === 'SKU';
            };

            $scope.getPricingBySku = function () {
                var payload = {
                    skuId: $scope.sourceValue.id,
                    locationId: $scope.deliveryLocation.id
                };
                $http({
                    url: apiJson.urls.skuMapping.searchSkuPrice,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    $scope.priceGridOptions.data = response.data;
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.getPricingByVendor = function () {
                var payload = {
                    vendorId: $scope.sourceValue.id
                };
                if ($scope.deliveryLocation !== undefined && $scope.deliveryLocation !==null) {
                    payload.location = $scope.deliveryLocation.code;
                }

                $http({
                    url: apiJson.urls.skuMapping.searchVendorToSkuPrice,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    $scope.priceGridOptions.data = response.data;
                    for (x in $scope.priceGridOptions.data) {
                        $scope.priceGridOptions.data[x].isUpdated = true;
                        $scope.priceGridOptions.data[x].isUpdatedOld = 'N';
                        if ($scope.priceGridOptions.data[x].updated.value) {
                            $scope.acceptedPriceRequestNew.set($scope.priceGridOptions.data[x].keyId,$scope.priceGridOptions.data[x]);
                            $scope.rejectedPriceRequest.set($scope.priceGridOptions.data[x].keyId,$scope.priceGridOptions.data[x]);
                        } else {
                            $scope.acceptedPriceRequest.set($scope.priceGridOptions.data[x].keyId,$scope.priceGridOptions.data[x]);
                        }
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.locationGridOptions = function () {
                return {
                    enableColumnMenus: false,
                    enableFiltering: true,
                    enableCellEditOnFocus: true,
                    enableColumnResizing: true,
                    cellEditableCondition: function ($scope) {
                        return $scope.row.entity.status === 'ACTIVE';
                    },
                    columnDefs: [{
                        field: 'vendor.name',
                        displayName: 'Vendor',
                        enableCellEdit: false
                    }, {
                        field: 'sku.name',
                        displayName: 'SKU',
                        cellTemplate: 'skuIdTemplate.html',
                        enableCellEdit: false
                    }, {
                        field: 'dispatch.name',
                        displayName: 'Dispatch Location',
                        enableCellEdit: false
                    },  {
                        field: 'delivery.name',
                        displayName: 'Delivery Location',
                        enableCellEdit: false
                    }, {
                        field: 'pkg.name',
                        displayName: 'Packaging',
                        enableCellEdit: false
                    }, {
                        field: 'pkg.uom',
                        displayName: 'UOM',
                        enableCellEdit: false
                    }, {
                        field: 'current.date',
                        displayName: 'Start Date',
                        type: 'date',
                        cellFilter: 'date: \'yyyy-MM-dd\'',
                        enableCellEdit: false
                    }, {
                        field: 'status',
                        displayName: 'Status',
                        enableCellEdit: false,
                        cellTemplate: 'statusBatch.html'
                    }, {
                        field: 'current.value',
                        displayName: 'Current Price',
                        enableCellEdit: false
                    }, {
                        field: 'updated.value',
                        displayName: 'Requested Price',
                        enableCellEdit: false
                    }, {
                        field: 'action',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeButton.html',
                        enableCellEdit: false
                    }],
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                            console.log(newValue);
                            // to display update
                            if (colDef.field=='leadTime'.toString()) {
                                rowEntity.update = false;
                            }
                            else
                            {
                                rowEntity.update = true;
                            }
                            console.log(JSON.stringify(rowEntity));
                            $scope.$apply();
                        });
                    }
                };
            };

            $scope.changeStatus = function (value) {
                return;
            };

            $scope.updateRow = function (value) {
                if (value.updated.value !== null) {
                   $alertService.confirm("Are you sure?","Do you want to keep the updated prices(Click on Yes to keep the updated price/For applying the old price Press NO)", function (result) {
                        if (result) {
                            $scope.acceptedPriceRequestNew.set(value.keyId,value);
                            $scope.rejectedPriceRequest.set(value.keyId,value);
                            $scope.acceptedPriceRequest.delete(value.keyId);
                            $scope.rejectedPriceRequestNew.delete(value.keyId);
                            value.isUpdated=true;
                            value.isUpdatedOld='N';
                        } else {
                            $scope.acceptedPriceRequest.set(value.keyId,value);
                            $scope.rejectedPriceRequestNew.set(value.keyId,value);
                            $scope.rejectedPriceRequest.delete(value.keyId);
                            $scope.acceptedPriceRequestNew.delete(value.keyId);
                            value.isUpdatedOld='Y';
                        }
                        $scope.$apply();
                   });
                } else {
                   $scope.acceptedPriceRequest.set(value.keyId);
                   $scope.rejectedPriceRequest.delete(value.keyId,value);
                   value.isUpdated=true;
                }
                $timeout(function() {$scope.$apply()});
            };

            $scope.cancelRow = function (value) {
                 if (value.updated.value !== null) {
                    $alertService.confirm("Are you sure?","Do you want to keep the existing prices(Click on Yes to keep the existing price/For Removing the SKU from Contract Press NO)", function (result) {
                         if (result) {
                            $scope.acceptedPriceRequest.set(value.keyId,value);
                            $scope.acceptedPriceRequestNew.delete(value.keyId);
                            $scope.rejectedPriceRequestNew.set(value.keyId,value);
                            $scope.rejectedPriceRequest.delete(value.keyId);
                            value.isUpdatedOld='Y';
                         } else {
                            $scope.acceptedPriceRequest.delete(value.keyId);
                            $scope.acceptedPriceRequestNew.delete(value.keyId);
                            $scope.rejectedPriceRequest.set(value.keyId,value);
                            $scope.rejectedPriceRequestNew.set(value.keyId,value);
                            value.isUpdated=false;
                            value.isUpdatedOld='N';
                         }
                         $scope.$apply();
                    });
                 } else {
                    $scope.acceptedPriceRequest.delete(value.keyId);
                    $scope.rejectedPriceRequest.set(value.keyId,value);
                    value.isUpdated=false;
                 }
                 $timeout(function() {$scope.$apply});
            };

            $scope.previewUpdationModel = function () {
                var mappingModal = Popeye.openModal({
                    templateUrl : "vendorToSkuPriceRequestModal.html",
                    controller : "vendorToSkuPriceRequestModalCtrl",
                    resolve : {
                        acceptedPriceRequest : function() {
                            return $scope.acceptedPriceRequest;
                        },
                        rejectedPriceRequest : function() {
                            return $scope.rejectedPriceRequest;
                        },
                        acceptedPriceRequestNew : function() {
                            return $scope.acceptedPriceRequestNew;
                        },
                        rejectedPriceRequestNew : function() {
                            return $scope.rejectedPriceRequestNew;
                        },
                        vendor : function() {
                            return $scope.sourceValue.name;
                        }
                    },
                    click : false,
                    keyboard : false
                });
                mappingModal.closed.then(function (result) {
                    if (result) {
//                        $scope.showPricing();
                        $scope.acceptedPriceRequest= [];
                        $scope.rejectedPriceRequest= [];
                        $scope.acceptedPriceRequestNew= [];
                        $scope.rejectedPriceRequestNew= [];
                        $scope.init();
                    }
                });
            }



            $scope.isEntryFoundForUpdation = function () {
                return $scope.acceptedPriceRequest.size>0 || $scope.rejectedPriceRequest.size>0;
            }

            $scope.updatePriceGridRow = function (value) {
                var x = null;
                var id = null;
                for (x in $scope.priceGridOptions.data) {
                    if ($scope.priceGridOptions.data[x].keyId == value.keyId) {
                        id = x;
                    }
                }
                if (id != null) {
                    $scope.priceGridOptions.data[id] = value;
                }
            };

            // Modal Methods
            $scope.getDataForAddModal = function () {
                $scope.selectSkuPackaging = null;
                if ($scope.isSKUValue()) {
                    $scope.selectSKUValue = $scope.sourceValue;
                    $scope.getVendorForSku($scope.sourceValue.id);
                    $scope.getSkuPackaging($scope.sourceValue);
                } else {
                    $scope.selectVendorValue = $scope.sourceValue;
                    $scope.getSkuForVendor($scope.sourceValue.id);
                    $scope.getVendorDispatchLocations($scope.sourceValue);
                }
            };

            $scope.getSkuPackaging = function (sku) {
                if (sku == undefined || sku == null) {
                    return;
                }

                $scope.selectSKUValue = sku;
                $http({
                    method: "POST",
                    dataType: 'json',
                    data: sku.id,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.skuPackaging,
                }).then(function success(response) {
                    $scope.skuPkgs = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getVendorDispatchLocations = function (vendor) {
                if (vendor == undefined || vendor == null) {
                    return;
                }
                $scope.selectVendorValue = vendor;
                $http({
                    method: "POST",
                    dataType: 'json',
                    data: vendor.id,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.vendorSites,
                }).then(function success(response) {
                    $scope.vendorDispatchLocations = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
                $timeout(function () {
                    $('#vendorDispatchLocationSelectId').val('').trigger('change');
                });
            };

            $scope.submitPriceData = function () {
                if ($scope.negotiatedPriceValue == null || $scope.selectSkuPackaging == null
                    || $scope.selectDispatchLocation == null || $scope.selectVendorValue == null
                    || $scope.selectSKUValue == null || $scope.leadTime == null) {
                    var msg = "Please fill all values.";
                    $toastService.create(msg);
                    return false;
                }
                var currentUser = appUtil.getCurrentUser();
                var payload = {
                    detail: {},
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                };
                payload.detail.sku = $scope.selectSKUValue;
                payload.detail.pkg = {
                    id: $scope.selectSkuPackaging.packagingId,
                    name: $scope.selectSkuPackaging.packagingName,
                };
                payload.detail.vendor = $scope.selectVendorValue;
                payload.detail.dispatch = $scope.selectDispatchLocation;
                payload.detail.delivery = $scope.deliveryLocation;
                payload.detail.leadTime= $scope.leadTime;
                payload.detail.current = {
                    value: $scope.negotiatedPriceValue
                };

                if ($scope.alreadyExists(payload)) {
                    var msg = "SKU price Mapping already exists! Please verify."
                    $toastService.create(msg);
                    return false;
                }

                $http({
                    url: apiJson.urls.skuMapping.addSkuPrice,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data) {
                        var msg = 'Update Submitted Successfully';
                        $toastService.create(msg);
                        $('#addSkuPriceMappingModal').closeModal();
                        $scope.showPricing();
                    }
                }, function (response) {
                    console.log("error", response);
                    var msg = 'Update Failed';
                    $toastService.create(msg);
                    $scope.showPricing();
                });
                $scope.clearAddPriceData();
            };

            $scope.alreadyExists = function (payload) {
                var payloadKey = payload.detail.sku.id + '#' + payload.detail.pkg.id + '#'
                    + payload.detail.vendor.id + '#' + payload.detail.dispatch.id + '#'
                    + payload.detail.delivery.id;

                if ($scope.priceGridOptions != null && $scope.priceGridOptions.data != null
                    && $scope.priceGridOptions.data.length > 0) {
                    var index = 0;

                    for (index = 0; index < $scope.priceGridOptions.data.length; index++) {
                        var value = $scope.priceGridOptions.data[index];
                        var innerKey = value.sku.id + '#' + value.pkg.id + '#' + value.vendor.id + '#'
                            + value.dispatch.id + '#' + value.delivery.id;
                        if (payloadKey === innerKey) {
                            return true;
                        }
                    }
                }
                return false;
            };

            $scope.clearAddPriceData = function () {
                $scope.negotiatedPriceValue = null;
                $scope.selectSkuPackaging = null;
                $scope.selectDispatchLocation = null;
                $scope.selectVendorValue = null;
                $scope.vendorDispatchLocations = null;
                $scope.skuPkgs = null;
                $timeout(function () {
                    $('#vendorValueSelectId').val('').trigger('change');
                    $('#vendorDispatchLocationSelectId').val('').trigger('change');
                    $('#skuPackSelector').val('').trigger('change');
                    $('#skuValueSelectId').val('').trigger('change');
                });
            };

            $scope.showGrid = function () {
                return $scope.sourceValue != null
                    && $scope.priceGridOptions.data != null && $scope.priceGridOptions.data.length > 0
            };

            $scope.showNoDataMsg = function () {
                return $scope.sourceValue != null
                    && $scope.priceGridOptions.data != null && $scope.priceGridOptions.data.length == 0;
            };

        }]).controller('vendorToSkuPriceRequestModalCtrl', ['$scope', 'acceptedPriceRequest','rejectedPriceRequest', 'acceptedPriceRequestNew','rejectedPriceRequestNew','vendor','Popeye','$http','apiJson', 'appUtil', '$toastService',
               function ($scope,acceptedPriceRequest, rejectedPriceRequest,acceptedPriceRequestNew, rejectedPriceRequestNew,vendor, Popeye,$http,apiJson, appUtil, $toastService) {
                   $scope.initSKUPriceUpdationModal =function () {
                        var currentUser = appUtil.getCurrentUser();
                        $scope.acceptedPriceRequest = [];
                        $scope.rejectedPriceRequest = [];
                        $scope.acceptedPriceRequestNew = [];
                        $scope.rejectedPriceRequestNew = [];
                        $scope.vendor = vendor;
                        acceptedPriceRequest.forEach(function (headerkey, headervalue) {
                            var payload = {
                                detail: headerkey,
                                employeeId: currentUser.userId,
                                employeeName: currentUser.user.name
                            };
                            $scope.acceptedPriceRequest.push(payload);
                        });
                        rejectedPriceRequest.forEach(function (headerkey, headervalue) {
                            var payload = {
                                detail: headerkey,
                                employeeId: currentUser.userId,
                                employeeName: currentUser.user.name
                            };
                            $scope.rejectedPriceRequest.push(payload);
                        });
                        acceptedPriceRequestNew.forEach(function (headerkey, headervalue) {
                            var payload = {
                                detail: headerkey,
                                employeeId: currentUser.userId,
                                employeeName: currentUser.user.name
                            };
                            $scope.acceptedPriceRequestNew.push(payload);
                        });
                        rejectedPriceRequestNew.forEach(function (headerkey, headervalue) {
                            var payload = {
                                detail: headerkey,
                                employeeId: currentUser.userId,
                                employeeName: currentUser.user.name
                            };
                            $scope.rejectedPriceRequestNew.push(payload);
                        });
                   }

                   $scope.close = function(){
                       Popeye.closeCurrentModal(false);
                   };

                   $scope.submit = function () {
                       var data = {
                                approved : $scope.acceptedPriceRequest,
                                rejected : $scope.rejectedPriceRequest,
                                approvedNew : $scope.acceptedPriceRequestNew,
                                rejectedNew : $scope.rejectedPriceRequestNew

                       }
                       $http({
                           url: apiJson.urls.skuMapping.processVendorRequestedPrice,
                           method: 'POST',
                           data: data
                       }).then(function success(response) {
                           if (response.data != null && response.status == 200) {
                               $toastService.create("Request Send Successfully");
                               Popeye.closeCurrentModal(true);
                           }
                       }, function error(response) {
                           $toastService.create("Request Send Failed");
                           Popeye.closeCurrentModal(false);
                       });
                       Popeye.closeCurrentModal(true);
                   };

                   $scope.updateData = function (id, excessQuantity) {
                   }

               }
               ]);