'use strict';

angular.module('scmApp')
    .controller('vendorAdvancePaymentCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', 'metaDataService', 'Popeye','$fileUploadService','$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, metaDataService, Popeye, $fileUploadService, $alertService) {
            $scope.init = function () {
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.selectedVendor = null;
                $scope.vendorList = [];
                $scope.listOfAdvances = [];
                $scope.statusList = ["INITIATED","CREATED", "CANCELLED", "REJECTED", "BLOCKED", "UN_BLOCKED", "ADJUST_INITIATED", "ADJUST_REJECTED",
                    "ADJUST_CANCELLED", "ADJUSTED", "REFUND_INITIATED", "REFUND_APPROVED", "REFUNDED", "COMPLETED"];
                metaDataService.getTrimmedVendors(function (response) {
                    $scope.vendorList = response;
                });
                $scope.gridOptions = appUtil.getGridOptions($scope);
                $scope.selectedRow = null;
                $scope.advancePaymentId = null;
            };

            $scope.gridColumns = function () {
                return [{
                    field: 'advancePaymentId',
                    enableCellEdit: false,
                    displayName: 'Advance Payment Id'
                }, {
                    field: 'advanceStatus',
                    enableCellEdit: false,
                    displayName: 'Advance Status'
                }, {
                    field: 'paymentRequestId',
                    enableCellEdit: false,
                    displayName: 'PR ID',
                }, {
                    field: 'prAmount',
                    enableCellEdit: false,
                    displayName: 'PR Amount'
                }, {
                    field: 'availableAmount',
                    enableCellEdit: false,
                    displayName: 'Available Amount'
                }, {
                    field: 'blockedAmount',
                    enableCellEdit: false,
                    displayName: 'Blocked Amount'
                }, {
                    field: 'poId',
                    enableCellEdit: false,
                    displayName: 'PO Id'
                }, {
                    field: 'soId',
                    enableCellEdit: false,
                    displayName: 'SO Id'
                }, {
                    field: 'createdAt',
                    type: 'date',
                    enableCellEdit: false,
                    displayName: 'Created At',
                    cellFilter: 'date:\'yyyy-MM-dd\''
                }, {
                    field: 'refundDate',
                    enableCellEdit: false,
                    displayName: 'Refund Date',
                }, {
                    field: 'createdBy',
                    enableCellEdit: false,
                    displayName: 'Created By',
                }, {
                        name: 'refundButton',
                        enableCellEdit: false,
                        displayName: 'Refund/Adjust Actions',
                        cellTemplate: 'refundButton.html',
                    }, {
                    name: 'ViewLogs',
                        enableCellEdit: false,
                        displayName: 'Usage',
                        cellTemplate: 'viewLogs.html',
                    }, {
                    name: 'ViewStatusLogs',
                        enableCellEdit: false,
                        displayName: 'Status Logs',
                        cellTemplate: 'viewStatusLogsModalGrid.html',
                    }
                ]
            };

            $scope.setSelectedVendor = function (vendor) {
                if (vendor != null) {
                    $scope.selectedVendor = vendor;
                    $scope.getVendorAdvances();
                } else {
                    $toastService.create("Please Select a vendor ..!");
                }
            };

            $scope.getVendorAdvances = function () {
                if ($scope.advancePaymentId == null) {
                    if ($scope.selectedVendor == null) {
                        $toastService.create("Please Select Vendor First..!");
                        return;
                    }
                    if (appUtil.isEmptyObject($scope.startDate)) {
                        $toastService.create("Please Select Start Date ..!");
                        return;
                    }
                    if (appUtil.isEmptyObject($scope.endDate)) {
                        $toastService.create("Please Select End Date ..!");
                        return;
                    }
                }
                $http({
                    method: "GET",
                    url: apiJson.urls.paymentRequestManagement.getAllVendorAdvances,
                    params: {
                        "startDate": $scope.startDate,
                        "endDate": $scope.endDate,
                        "vendorId": $scope.selectedVendor != null ? $scope.selectedVendor.id : null,
                        "status": $scope.selectedStatus,
                        "advancePaymentId": $scope.advancePaymentId
                    }
                }).then(function success(response) {
                    if (response != null && response.data != null) {
                        if (response.data.length > 0) {
                            $scope.listOfAdvances = response.data;
                            $scope.gridOptions.columnDefs = $scope.gridColumns();
                            $scope.gridOptions.data = response.data;
                        } else {
                            $scope.listOfAdvances = [];
                            $toastService.create("No Advance Payments Found for the selected Vendor..!");
                        }
                    } else {
                        $toastService.create("Something went wrong while getting vendor advance payments. Please try again...!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.listOfAdvances = [];
                });
            };

            $scope.openStatusLogs = function (row) {
                $scope.selectedRow = row;
            };

            $scope.setRoRow = function (row) {
                $scope.selectedRow = row;
                var viewLogsModal = Popeye.openModal({
                    templateUrl: "viewLogsModal.html",
                    controller: "viewLogsModalCtrl",
                    modalClass: 'custom-modal',
                    resolve: {
                        selectedRow: function () {
                            return $scope.selectedRow;
                        },
                        selectedVendor: function () {
                            return $scope.selectedVendor;
                        }
                    },
                    click: false,
                    keyboard: false
                });

                viewLogsModal.closed.then(function (result) {
                    if (result) {
                        $scope.selectedRow = null;
                    }
                });
            };

            $scope.openRefundModal = function (row, isView, isAdjusted) {
                $scope.selectedRow = row;
                $scope.isRefundView = isView;
                var refundModal = Popeye.openModal({
                    templateUrl: "refundModal.html",
                    controller: "refundModalCtrl",
                    modalClass: 'custom-modal',
                    resolve: {
                        selectedRow: function () {
                            return $scope.selectedRow;
                        },
                        selectedVendor: function () {
                            return $scope.selectedVendor;
                        },
                        isRefundView: function () {
                            return $scope.isRefundView;
                        },
                        isAdjustView: function () {
                            return isAdjusted;
                        }
                    },
                    click: false,
                    keyboard: false
                });

                refundModal.closed.then(function (result) {
                    if (result) {
                        $scope.selectedRow = null;
                        $scope.getVendorAdvances();
                    }
                });
            };
        }]).controller('viewLogsModalCtrl', ['$scope', 'selectedRow', 'selectedVendor', 'Popeye', '$http', 'apiJson', 'appUtil',
    function ($scope, selectedRow, selectedVendor, Popeye, $http, apiJson, appUtil) {

        $scope.selectedRow = selectedRow;

        $scope.selectedRow.vendorAdvancePaymentAuditLogs = $scope.selectedRow.vendorAdvancePaymentAuditLogs.reverse();

        $scope.selectedVendor = selectedVendor;

        $scope.gridOptionsLogs = appUtil.getGridOptions($scope);

        $scope.gridColumns = function () {
            return [
                {
                    field: 'prId',
                    enableCellEdit: false,
                    displayName: 'PR ID',
                }, {
                    field: 'amount',
                    enableCellEdit: false,
                    displayName: 'Amount'
                }, {
                    field: 'status',
                    enableCellEdit: false,
                    displayName: 'Status'
                }, {
                    field: 'loggedBy',
                    enableCellEdit: false,
                    displayName: 'Logged By'
                }, {
                    field: 'loggedAt',
                    enableCellEdit: false,
                    displayName: 'Logged At',
                    type: 'date',
                    cellFilter: 'date:\'dd-MM-yyyy hh:mm:ss a\''
                }, {
                    field: 'fromAmount',
                    enableCellEdit: false,
                    displayName: 'From Amount'
                }, {
                    field: 'toAmount',
                    enableCellEdit: false,
                    displayName: 'Balance After Usage'
                }
            ]
        };

        $scope.gridOptionsLogs.columnDefs = $scope.gridColumns();
        $scope.gridOptionsLogs.data = $scope.selectedRow.vendorAdvancePaymentAuditLogs;

        $scope.close = function () {
            Popeye.closeCurrentModal(true);
        };

    }]).controller('refundModalCtrl', ['$scope','$rootScope', 'selectedRow', 'selectedVendor', 'Popeye', '$http', 'apiJson', 'appUtil','$alertService','$toastService','isRefundView','isAdjustView','metaDataService','$fileUploadService',
    function ($scope,$rootScope, selectedRow, selectedVendor, Popeye, $http, apiJson, appUtil,$alertService,$toastService, isRefundView, isAdjustView, metaDataService, $fileUploadService) {

        $scope.selectedRow = selectedRow;
        $scope.isRefundView = isRefundView;
        $scope.isAdjustView = isAdjustView;
        $scope.refundedDate = null;
        $scope.minRefundDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");

        $scope.uploadRefundDoc = function () {
            $fileUploadService.openFileModal("Upload Refund Document", "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                var fileLimit = fileExt.toLowerCase() == 'png' ? 1024000 : 5120000;
                if (file.size > fileLimit) {
                    var msg = ""
                    if (fileExt.toLowerCase() == 'png') {
                        msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
                    }
                    $toastService.create('File size should not be greater than ' + fileLimit / 1024000 + ' MB.' + msg);
                    return;
                }

                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                    var fd = new FormData();
                    fd.append('type', "OTHERS");
                    fd.append('mimeType', fileExt.toUpperCase());
                    fd.append('userId', appUtil.getCurrentUser().userId);
                    fd.append('docType', "ADVANCE_REFUND");
                    fd.append('file', file);
                    fd.append('docName', "ADVANCE_REFUND");
                    fd.append('advancePaymentId', $scope.selectedRow.advancePaymentId);
                    $http({
                        url: apiJson.urls.paymentRequestManagement.uploadAdvanceRefund,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (!appUtil.isEmptyObject(response)) {
                            $scope.uploadedAdvanceRefund = response;
                        } else {
                            $toastService.create("Upload failed");
                            $scope.uploadedAdvanceRefund = null;
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, true)
                        } else {
                            $toastService.create("Upload failed");
                        }
                        $scope.uploadedAdvanceRefund = null;
                    });
                } else {
                    $toastService.create('Upload Failed , File Format not Supported');
                    $scope.uploadedAdvanceRefund = null;
                }
            });
        };

        $scope.downloadRefundDoc = function (doc) {
            metaDataService.downloadDocument(doc);
        };

        $scope.downloadRefundDocById = function (id) {
            metaDataService.downloadDocumentById(id);
        };

        $scope.setRefundedDate = function (date) {
            $scope.refundedDate = date;
        };

        $scope.submitRefundedDate = function (date) {
            if (appUtil.isEmptyObject($scope.uploadedAdvanceRefund)) {
                $toastService.create("Please Upload the Refund Document..!");
                return false;
            }
            $http({
                method: "POST",
                url: apiJson.urls.paymentRequestManagement.submitRefundedDate,
                params: {
                    "advancePaymentId": $scope.selectedRow.advancePaymentId,
                    "refundedDate": date,
                    "receivedBy": appUtil.getCurrentUser().userId,
                    "refundDocId":$scope.uploadedAdvanceRefund.documentId
                }
            }).then(function success(response) {
                if (response != null && response.data != null) {
                    if (response.data) {
                        $scope.close(true);
                    } else {
                        $toastService.create("Something went wrong while Marking Advance refunded. Please try again...!");
                    }
                }
            }, function error(response) {
                console.log("error:" + response);
                $toastService.create("Something went wrong while Marking Advance refunded. Please try again...!");
            });
        };

        $scope.close = function (val) {
            Popeye.closeCurrentModal(val);
        };

    }]);