'use strict';

angular.module('scmApp')
    .controller('monkIngredientsConverterCtrl', ['$http', '$scope', 'appUtil', 'metaDataService', '$toastService',
        'apiJson', '$alertService', 'Popeye',
        function ($http, $scope, appUtil, metaDataService,
                  $toastService, apiJson, $alertService, Popeye) {
            $scope.init = function () {
                $scope.productList = [];
                $scope.monkProducts = [101413, 101417, 101574, 101416, 102617];
                $scope.productList = appUtil.getScmProductDetails().filter(function (product) {
                    return $scope.monkProducts.includes(product.productId);
                });
                $scope.productList.push({productId: 11111, productName: "non Recipe"});
                $scope.selectedProduct = null;
                $scope.skuAssetMap = {};
                $scope.skuQuantityMap = {};
                $scope.currentUnit = appUtil.getUnitData();
                $scope.selectedAssets = {};
                $scope.selectedConsumableProducts = {};
                $scope.additionalProducts = [101357, 101544, 101545, 101587, 101542];
                $scope.tempProductMap = {
                    101357: {name: "Monk-Exhaust Fan", skuId: 1551},
                    101544: {name: "Monk GN pan-Water", skuId: 1781},
                    101545: {name: "Monk GN pan-Milk", skuId: 1782},
                    101587: {name: "Monk- Solenoid Valve-Washer or Gasket", skuId: 1847},
                    101542: {name: "Monk Level Sensor Rod", skuId: 1778}
                }


            }


                  $scope.updateStock = function (){
                      var ingredients = $scope.bookingData.bookingConsumption;
                      var assetIds = [];
                      ingredients.forEach(function (ingredient){
                          if(ingredient.checked){
                              if(!appUtil.isEmptyObject($scope.skuQuantityMap[ingredient.productId])){
                                  if($scope.skuQuantityMap[ingredient.productId].selectedStock != 0 ) {
                                      $scope.selectedConsumableProducts[ingredient.selectedSku.id] = $scope.skuQuantityMap[ingredient.productId].selectedStock;
                                  }
                              }else if (!appUtil.isEmptyObject($scope.selectedAssets[ingredient.selectedSku.id])) {
                                   assetIds =  assetIds.concat($scope.selectedAssets[ingredient.selectedSku.id]);
                              }
                          }
                      });

                      if(assetIds.length == 0 && appUtil.isEmptyObject($scope.selectedConsumableProducts)){
                          $toastService.create("Please Select Atleast One Product");
                          return;
                      }

                      $http({
                          url: apiJson.urls.assetManagement.autoToGrMonkAssets,
                          method: "POST",
                          data : $scope.selectedConsumableProducts,
                          params: {
                              unitId : appUtil.getCurrentUser().unitId,
                              assetIds  : assetIds
                         }
                  }).then(function (response) {
                          if (response.data != undefined && response.data != null) {
                              $alertService.alert("Success","Transfer Orders Created With To Ids : " + response.data);
                              $scope.init();
                          } else {
                              console.log("Error during Transfer")
                          }
                      }, function (response) {
                          console.log(response);
                          $toastService.create("Server Error");
                      });
                  }

            $scope.getMonkIngredients = function () {
                if ($scope.selectedProduct == 11111) {
                    var tempData = {};
                    tempData.bookingConsumption = [];
                    for (var i = 0; i < $scope.additionalProducts.length; i++) {
                        var temp2 = {};
                        temp2.productId = $scope.additionalProducts[i];
                        temp2.productName = $scope.tempProductMap[temp2.productId].name;
                        temp2.skuId = $scope.tempProductMap[temp2.productId].skuId
                        temp2.unitOfMeasure = "PC"
                        temp2.availableSkuList = [];
                        var temp3 = {
                            code: null,
                            id: $scope.tempProductMap[temp2.productId].skuId,
                            name: $scope.tempProductMap[temp2.productId].name
                        };
                        temp2.availableSkuList.push(temp3);

                        tempData.bookingConsumption.push(temp2);

                    }
                    $scope.bookingData = tempData;
                    var productIds = getNonAssetProducts();
                    getCurrentStock(productIds);

                } else {
                    $http({
                        url: apiJson.urls.productionBooking.calculateConsumption,
                        method: "GET",
                        params: {
                            productId: $scope.selectedProduct,
                            unitId: 26033,
                            quantity: 1
                        }
                    }).then(function (response) {
                        if (response.data != undefined && response.data != null) {
                            $scope.bookingData = response.data;
                            var productIds = getNonAssetProducts();
                            getCurrentStock(productIds);
                        } else {
                            $toastService.create("Error During Finding Ingredients For Selected Product");
                        }
                    }, function (response) {
                        console.log(response);
                        $toastService.create("Server Error");
                    });
                }

            }

            $scope.getMonkAssets = function () {
                if ($scope.selectedProduct == 11111) {
                    console.log("hard coded ones");
                    $http({
                        url: apiJson.urls.assetManagement.getTransferableAssetsFromUnitByProducts,
                        method: "POST",
                        data: $scope.additionalProducts,
                        params: {
                            unitId: appUtil.getCurrentUser().unitId
                        }
                    }).then(function (response) {
                        if (response.data != undefined && response.data != null) {
                            var assets = response.data;
                            for (var i = 0; i < assets.length; i++) {
                                if (appUtil.isEmptyObject($scope.skuAssetMap[assets[i].skuId])) {
                                    $scope.skuAssetMap[assets[i].skuId] = [];
                                }
                                $scope.skuAssetMap[assets[i].skuId].push(assets[i]);
                            }
                            $scope.getMonkIngredients();
                        } else {
                            var msg = "Unable to get Monk Assets For Selected Products";
                            $toastService.create(msg);
                        }
                    }, function (response) {
                        console.log(response);
                        $toastService.create("Server Error");
                    });
                } else {
                    $http({
                        url: apiJson.urls.assetManagement.getMonkAssets,
                        method: "GET",
                        params: {
                            productId: $scope.selectedProduct,
                            unitId: appUtil.getCurrentUser().unitId
                        }
                    }).then(function (response) {
                        if (response.data != undefined && response.data != null) {
                            $scope.skuAssetMap = response.data;
                                  $scope.getMonkIngredients();
                              } else {
                                  var msg = "Unable to get Monk Assets For Selected Products";
                                  $toastService.create(msg);
                              }
                          }, function (response) {
                              console.log(response);
                              $toastService.create("Server Error");
                          });
                }

            }

            function getNonAssetProducts() {
                var skus = Object.keys($scope.skuAssetMap);
                var assetMap = angular.copy($scope.skuAssetMap);
                var productIds = [];
                for (var i = 0; i < $scope.bookingData.bookingConsumption.length; i++) {
                    var productId = $scope.bookingData.bookingConsumption[i].productId;
                    var found = false;
                    for (var j = 0; j < skus.length; j++) {
                        if(!appUtil.isEmptyObject(assetMap[skus[j]])){
                            var assets = assetMap[skus[j]];
                            for(var l = 0;l<assets.length;l++){
                                if(assets[l].productId === productId){
                                    found = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (!found) {
                        productIds.push(productId);
                    }
                }
                return productIds;
            }

            $scope.openAssetsViewModal = function (skuId) {
                var assetsViewModal = Popeye.openModal({
                    templateUrl: "assetView.html",
                    controller: "assetViewModalCtrl",
                    resolve: {
                        assets: function () {
                            return $scope.skuAssetMap[skuId];
                        }
                    },
                    modalClass: 'custom-modal',
                    click: false,
                    keyboard: false
                });
                assetsViewModal.closed
                    .then(function (assets) {
                        $scope.selectedAssets[skuId]  = assets;
                    });
            }

            function getCurrentStock(productIds) {
                $http({
                    url: apiJson.urls.assetManagement.getMonkIngredientsStock,
                    method: "POST",
                    data: productIds
                    ,
                    params:{
                        unitId: $scope.currentUnit.id,
                    }
                }).then(function (response) {
                    if (response.data != undefined && response.data != null) {
                        createQuantityMap(response.data);
                    } else {
                        var msg = "Unable to get Current Stock For Selected Products";
                        $toastService.create(msg);
                    }
                }, function (response) {
                    console.log(response);
                    $toastService.create("Server Error");
                });
            }

            $scope.selectStock = function (productId){
                if($scope.skuQuantityMap[productId].selectedStock > $scope.skuQuantityMap[productId].stockAtHand || $scope.skuQuantityMap[productId].selectedStock < 0 ||
                    appUtil.isFloat($scope.skuQuantityMap[productId].selectedStock) ){
                    $scope.skuQuantityMap[productId].selectedStock = 0 ;
                    $toastService.create("Please Enter Valid Stock");
                    return;
                }
            }

            function createQuantityMap(currentStocks) {
                Object.keys(currentStocks).forEach(function (productId){
                   $scope.skuQuantityMap[productId] = {
                       stockAtHand : currentStocks[productId],
                       selectedStock : 0
                   };
                });
            }


        }]
    ).controller('assetViewModalCtrl', ['$scope', 'appUtil', '$toastService', 'Popeye', 'assets',
        function ($scope, appUtil, $toastService, Popeye, assets) {
            $scope.init = function () {
                $scope.assets = assets;
                $scope.allSelected = false;
            }

            $scope.selectAll = function () {
                for (var i = 0; i < $scope.assets.length; i++) {
                    $scope.assets[i].checked = !$scope.allSelected;
                }
                $scope.allSelected = !$scope.allSelected;

            }

            $scope.closeModal = function () {
                var selectedAssets = [];
                for (var i = 0; i < $scope.assets.length; i++) {
                    if ($scope.assets[i].checked) {
                        selectedAssets.push($scope.assets[i].assetId);
                    }
                }
                Popeye.closeCurrentModal(selectedAssets);
            };

        }
    ]
);


