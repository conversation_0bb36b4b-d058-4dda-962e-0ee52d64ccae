/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('vendorRequestCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
        '$location', '$toastService', '$alertService','$state','$stateParams','$timeout', 'metaDataService', '$fileUploadService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, $alertService,$state, $stateParams, $timeout, metaDataService, $fileUploadService) {
            $scope.resetRegistrationDataInit = function() {
                $scope.vendorRequestNameData = null;
                $scope.vendorRequestEmailData = null;
                $scope.vendorRequestCopyEmailData = null;
                $scope.vendorPanCardNumber = null;
                $scope.vendorTypeListData = null;
                $scope.selectedVendorType = null;
                $scope.uploadDocVendors = [];
                $timeout(function () {
                    $('#vendorTypeList').val(null).trigger('change');
                });
                var status = ['PENDING_USER_ACTION', 'COMPLETED'];
                $scope.showViewRequest(status, $scope.userId);
            };

            $scope.init = function (isForNewRegistration) {
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.userId = $scope.currentUser.userId;
                $scope.userName = $scope.currentUser.user.name;
                $scope.showApproveButton = $scope.currentUser.user.department.id == 105 || $scope.userId==120063;
                $scope.resetRegistrationDataInit();
                $scope.isOnlyForNewRegistration = $stateParams.isOnlyForNewRegistration;
                if (isForNewRegistration != undefined && isForNewRegistration != null && isForNewRegistration) {
                    $scope.isOnlyForNewRegistration = true;
                }
                $scope.select2Options = {'width': '100%'};
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.vendorTypes = [];
                $scope.showPendingCheck = true;
                $scope.selectedVendorType = null;
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.vendorType
                }).then(function success(response) {
                    $scope.vendorTypes = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });

                $scope.viewVendor = function (vendor,mode) {
                    $state.go('menu.viewVendor', {edit: true, vendor: vendor, mode: mode});
                };

                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.getVendorStatus
                }).then(function success(response) {
                    $scope.vendorStatusList = response.data;
                    if(!appUtil.isEmptyObject($scope.vendorStatusList)){
                        var excluded = ["ACTIVE","IN_ACTIVE"];
                        $scope.vendorStatusList = $scope.vendorStatusList.filter(function(status){
                            return excluded.indexOf(status)==-1;
                        });
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

                $http({
                    method: "GET",
                    url: apiJson.urls.users.activeUsers
                }).then(function success(response) {
                    $scope.activeUserList = response.data;
                    console.log("vendorUser", $scope.vendorUser)
                }, function error(response) {
                    console.log("error:" + response);
                });

                $("#vendorAddRequestDiv").hide();
                $("#requestForViewDiv").hide();
                $("#viewDateDiv").show();

                if ($scope.isOnlyForNewRegistration) {
                    $scope.showAddRequest();
                    $("#viewRequestButton").hide();
                }
            };


            $scope.viewDetails = function(vendorId,reqId){
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.vendor,
                    params:{
                        vendorId:vendorId
                    }
                }).then(function(response) {
                    if(!appUtil.isEmptyObject(response.data)){
                        $state.go('menu.viewVendor',{edit:false,reqId:reqId, vendor:response.data});
                    }else{
                        $toastService.create("Could not fetch vendor details! Please try again...");
                    }
                }, function(response) {
                    console.log("error:" + response);
                });
            };


            $scope.selectedUnit = {width: '100%'};
            $scope.showRequestFor = function (ids) {
                $scope.userActiveId = ids.id;
                $scope.userActiveName = ids.name;
            };

            $scope.selectVendorType = function (vendorType) {
                $scope.selectedVendorType = vendorType;
            };

            $scope.uploadDoc = function(requestObj) {
                $fileUploadService.openFileModal("Upload Document", "Find", function (file) {
                    metaDataService.uploadGenericFile("OTHERS","ACCOUNT",file,"VENDOR_REGISTRATION", "VENDOR_REGISTRATION_DOCUMENT", function(doc){
                        requestObj.documentId = doc.documentId;
                    });
                })
            }

            $scope.downloadDoc = function(id) {
                if (id === null) {
                    $toastService.create("Document ID is Null");
                    return;
                }
                metaDataService.downloadDocumentById(id);
            }

            $scope.showAddBtn = function() {
                if ($scope.vendorRequestNameData == null || $scope.vendorRequestNameData == "") {
                    return false;
                }
                if($scope.selectedVendorType == null || $scope.selectedVendorType == ""){
                    return false;
                }
                if($scope.vendorRequestEmailData == null || $scope.vendorRequestEmailData == "") {
                    return false;
                }
                if($scope.vendorRequestCopyEmailData == null || $scope.vendorRequestCopyEmailData == "") {
                    return false;
                }
                if($scope.vendorPanCardNumber == null || ($scope.vendorPanCardNumber.length != 10)) {
                    return false;
                }
                return true;
            }

            $scope.sendRequest = function () {
                if (!validateEmail($scope.vendorRequestEmailData)) {
                    $toastService.create("Email can not empty or Invalid");
                    return;
                }
                if (!validateEmail($scope.vendorRequestCopyEmailData)) {
                    $toastService.create("Copy Email can not empty or Invalid");
                    return;
                }

                var sendRequestObj = {
                    vendorName: $scope.vendorRequestNameData,
                    requestDate: null,
                    requestForId: $scope.userId,
                    requestForName: $scope.userName,
                    requestByName: $scope.userName,
                    requestById: $scope.userId,
                    email: $scope.vendorRequestEmailData,
                    copyEmails: $scope.vendorRequestCopyEmailData,
                    vendorType: $scope.selectedVendorType,
                    panCardNumber: $scope.vendorPanCardNumber,
                    documentId: null
                };

                $http({
                    url: apiJson.urls.vendorManagement.vendorRequest,
                    method: 'POST',
                    data: sendRequestObj
                }).then(function (response) {
                        $scope.sendRequestObj = response.data;
                        $toastService.create("Vendor Request Successfully added! Please Check Email");

                        if ($scope.isOnlyForNewRegistration) {
                            $scope.init();
                        } else {
                            $("#vendorAddRequestDiv").hide();
                            $scope.showViewRequest(null, null);
                            $("#requestForViewDiv").show();
                            $("#viewDateDiv").show();
                            $scope.resetRegistrationDataInit();
                        }
                    //console.log(response.data);
                }, function (response) {
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg,function () {}, true);
                    console.log("got error", response);

                });


            };
            $scope.viewRequest = function () {

                $("#vendorAddRequestDiv").hide();
                $("#requestForViewDiv").hide();
                $("#viewDateDiv").show();

            };

            $scope.showAddRequest = function () {
                $("#vendorAddRequestDiv").show();
                $("#requestForViewDiv").hide();
                $("#viewDateDiv").hide();
            };


            $scope.showViewRequest = function (status, userId) {
                status = status || [$scope.selectedStatus];
                $http({
                    url: apiJson.urls.vendorManagement.vendorRegistrationByStatus,
                    method: 'POST', 
                    data: { 
                        status: status,
                        userId: userId
                    }
                }).then(function (response) {
                    console.log(response.data);
                    if(status != null && userId != null) {
                        $scope.uploadDocVendors = response.data;
                    } else {
                        $scope.sendRequestObj = response.data;
                        $scope.sendRequestObj.length == 0 ? $scope.showMessage = true : $scope.showMessage = false;
                    }
                }, function (response) {
                    console.log("got error", response);
                });
                $("#requestForViewDiv").show();
                $("#viewDateDiv").show();
            };

            function validateEmail(email) {
                var re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(email);
            }

            $scope.changeStatus = function (status, requestOn, requestBy) {
                console.log(status + "--" + requestOn + "-" + requestBy);
            };

            $scope.requestStatusChange = function (id) {
                $alertService.confirm("Approve", "Are You Sure You Wish To CANCEL The Vendor Registration Request?", function (result) {
                    if (result) {
                        $http({
                            url: apiJson.urls.vendorManagement.vendorRequestCancel,
                            method: 'POST',
                            data: id
                        }).then(function (response) {

                            $scope.sendRequestObj.forEach(function (vendorRequestList) {
                                if (vendorRequestList.id == id) {
                                    vendorRequestList.requestStatus = "CANCELLED"
                                }
                            });

                            console.log(response.data);
                        }, function (response) {
                            console.log("got error", response);
                        });
                    } else {
                        return;
                    }
                });

            };

            $scope.addLeadTime = function(vendorReq)
            {
                if(vendorReq.leadTime==null || vendorReq.leadTime==0)
                {
                    $toastService.create("Please select Lead Time first");
                    return;
                }
                else
                    $alertService.alert("Lead Time", "Successfully Added Lead TIme");
            }

            $scope.submitDocument = function(vendor) {
                console.log("VENDOR : ", vendor);
                $http({
                    url: apiJson.urls.vendorManagement.submitVendorDocument,
                    method: 'POST',
                    data: vendor
                }).then(function (response) {
                    $toastService.create("Document uploaded SuccessFully");
                    $scope.init();
                }, function (response) {
                    console.log("got error", response);
                });
            }

            $scope.activateVendor = function (creditCycle, vendorReq) {

                if(vendorReq.requestById == $scope.userId){
                    $alertService.alert("Vendor cannot be approved by its creator" , "Vendor needs approval of a person other than its creator",null,true);
                    return;
                }
                if(vendorReq.leadTime==null || vendorReq.leadTime==0){
                    $toastService.create("Please select Lead Time first");
                    return;
                }
                if (!appUtil.isEmptyObject(creditCycle)) {

                    var data ={
                            leadTime: vendorReq.leadTime,
                            requestId: vendorReq.id,
                            creditCycle: creditCycle,
                            userId: appUtil.getCurrentUser().userId
                        }

                    $state.go('menu.viewVendor', {edit: true, vendor: vendorReq, mode: 'Edit' , data: data});
                } else {
                    $toastService.create("Please enter credit cycle before approving vendor!");
                }
            };

        }
    ]
);
