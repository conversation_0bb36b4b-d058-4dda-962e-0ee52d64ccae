
'use strict';
angular.module('scmApp')
    .controller('viewVendorPRtoGRCtrl', ['$rootScope', '$scope','$stateParams', 'apiJson', '$http', 'appUtil','metaDataService',
            '$toastService','$alertService','$timeout','previewModalService', 'PrintService','productService','$window',
            function ($rootScope, $scope,$stateParams, apiJson, $http, appUtil, metaDataService,
                      $toastService, $alertService,$timeout, previewModalService, PrintService,productService,$window ) {

                $scope.attributes = [];
                $scope.selectedGRItem = null;
                $scope.init = function (){
                    var currentDate = appUtil.getCurrentBusinessDate();
                    if(!appUtil.isEmptyObject(currentDate)){
                        $scope.currentUser = appUtil.getCurrentUser();
                        $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                        $scope.endDate = $scope.startDate;
                        $scope.selectedVendor = $stateParams.vendor;
                        $scope.selectedDispatchLocation = $stateParams.dispatchLocation;
                        $scope.grs = [];
                        $scope.unitData = appUtil.getUnitData();

                    }
                    metaDataService.getVendorsForUnit($scope.currentUser.unitId,function(vendorsForUnit){
                        $scope.vendors = vendorsForUnit;
                    });

                    metaDataService.getSkuListForUnit($scope.currentUser.unitId,function(skuForUnitList){
                        $scope.skus = skuForUnitList;
                    });
                    $scope.companyMap = appUtil.getCompanyMap();
                };

                $scope.printVendorInvoice = function (grId) {
                    openUrl(grId, "VENDOR_INVOICE");
                };

                function openUrl(vgrId, fileType) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.getUrl,
                        params: {
                            vgrId: vgrId,
                            fileType: fileType
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $window.open(response.data, '_blank');
                        } else {
                            $toastService.create("Please check if the selected invoice is correct!!");
                        }
                    }, function (err) {
                        console.log("Encountered error at backend", err);
                    });
                }

                $scope.selectVendor = function (vendor) {
                    $scope.selectedVendor = vendor;
                };

                $scope.selectSKU = function (sku) {
                    $scope.selectedSKU = sku;
                };


                $scope.getGRs = function(){
                    var params = {
                        deliveryUnitId:$scope.currentUser.unitId,
                        startDate:$scope.startDate,
                        endDate:$scope.endDate,
                        prId:$scope.prId,
                        grId:$scope.grId,
                    };

                    if(appUtil.isEmptyObject($scope.startDate)){
                        $toastService.create("Please select a start date first");
                        return;
                    }
                    if(appUtil.isEmptyObject($scope.endDate)){
                        $toastService.create("Please select a end date first");
                        return;
                    }
                    if($scope.startDate>$scope.endDate){
                        $toastService.create("Please select a start date less than end date");
                        return;
                    }
                    if($scope.endDate> appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd")){
                        $toastService.create("Please select a end date less than today");
                        return;
                    }
                    if($scope.startDate> appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd")){
                        $toastService.create("Please select a start date less than today");
                        return;
                    }

                    if(!appUtil.isEmptyObject($scope.selectedVendor)){
                        params["vendorId"] = $scope.selectedVendor.vendorId;
                    }

                    if(!appUtil.isEmptyObject($scope.selectedSKU)){
                        params["skus"] = [$scope.selectedSKU];
                    }

                    $http({
                        method:'GET',
                        url:apiJson.urls.goodsReceivedManagement.findVendorGrsForPo,
                        params:params
                    }).then(function(response){
                        if(!appUtil.isEmptyObject(response.data)){
                            $scope.grs = response.data;
                        }else{
                            $scope.grs = [];
                        }
                    },function(error){
                        console.log(error);
                    });
                };

                $scope.printGR = function(gr){
                    $scope.currentPrintGR = gr;
                    $scope.currentPrintGR.total = parseFloat(parseFloat($scope.currentPrintGR.billAmount)+
                        parseFloat($scope.currentPrintGR.extraCharges!=null ? $scope.currentPrintGR.extraCharges : 0)).toFixed(2);
                    $timeout(function() {
                        angular.element('#printDiv').trigger('click');
                        for(var i=0;i<gr.purchaseOrderList.length;i++){
                            metaDataService.downloadDocument(gr.purchaseOrderList[i].poInvoice);
                        }
                    });
                };
            }
        ]
    );