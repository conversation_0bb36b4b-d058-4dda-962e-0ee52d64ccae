'use strict';

angular.module('scmApp')
    .controller('AssetTransfersCtrl', ['$rootScope', '$scope', 'apiJson',
        'appUtil', '$http', '$stateParams', 'productService','$alertService','Popeye', '$toastService', 'metaDataService',
        function ($rootScope, $scope, apiJson, appUtil, $http, $stateParams, productService,$alertService,
                  Popeye, $toastService) {
            $scope.lastUpdatedAssetTag = null;
            $scope.init = function () {
                $scope.onlyCanUpdate = false;
                $scope.assetTransfersList = [];
                $scope.groupedTransferAssetList = [];
                $scope.duplicateGroupedTransferAssetList = [];
                $scope.currentUnit = appUtil.getUnitData();
                $scope.getAllTransfers();
                console.log("current last up is : ",$scope.lastUpdatedAssetTag);
            };

            $scope.getAllTransfers = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.transferOrderManagement.getAssetTransfers,
                    params: {
                        unitId: $scope.currentUnit.id,
                    }
                }).then(function success(response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $scope.assetTransfersList = response.data;
                        $scope.groupBySkuId($scope.assetTransfersList);
                    }
                    else {
                        $toastService.create("No Asset Transfers Found for this Unit..!");
                    }
                }, function error(response) {
                    console.log("Encountered an error", response);
                    $toastService.create("Error Occurred While fetching transferred Assets Data");
                });
            }

            $scope.groupBySkuId = function (list) {
                $scope.groupedTransferAssetList = [];
                var skuList = [];
                for(var i in list){
                    if(skuList.indexOf(list[i].skuId) < 0){
                        var groupedByAssetListObj = getGroupedByAssetListObj();
                        groupedByAssetListObj.skuId = list[i].skuId;
                        groupedByAssetListObj.details.skuName = list[i].skuName;
                        groupedByAssetListObj.details.productName = list[i].productName;
                        groupedByAssetListObj.details.total++;
                        if (list[i].assetStatus == "CORRECTED(ORIGINAL)") {
                            groupedByAssetListObj.details.originalCheck[list[i].assetTag] = true;
                        }
                        else {
                            groupedByAssetListObj.details.originalCheck[list[i].assetTag] = false;
                        }
                        if (list[i].hasChild) {
                            groupedByAssetListObj.details.child[list[i].assetTag] = true;
                        }
                        else {
                            groupedByAssetListObj.details.child[list[i].assetTag] = false;
                        }
                        groupedByAssetListObj.details.assetList.push(list[i]);
                        $scope.groupedTransferAssetList.push(groupedByAssetListObj);
                        skuList.push(list[i].skuId);
                    } else {
                        for(var j in $scope.groupedTransferAssetList){
                            if($scope.groupedTransferAssetList[j].skuId == list[i].skuId){
                                $scope.groupedTransferAssetList[j].details.total++;
                                if (list[i].assetStatus == "CORRECTED(ORIGINAL)") {
                                    $scope.groupedTransferAssetList[j].details.originalCheck[list[i].assetTag] = true;
                                }
                                if (list[i].hasChild) {
                                    $scope.groupedTransferAssetList[j].details.child[list[i].assetTag] = true;
                                }
                                $scope.groupedTransferAssetList[j].details.assetList.push(list[i]);
                            }
                        }
                    }
                }
                setCanUpdateFlag();
            };

            function setCanUpdateFlag() {
                angular.forEach($scope.groupedTransferAssetList,function (skuBody) {
                    var count =0;
                    angular.forEach(skuBody.details.assetList,function (asset) {
                        if (!asset.hasChild && asset.inventoryCheck && asset.assetStatus != 'UNIQUE' && asset.assetStatus != 'CORRECTED(ORIGINAL)' && asset.assetStatus != 'CORRECTED(UPDATED)'
                            && !skuBody.details.originalCheck[asset.assetTag]) {
                            if (skuBody.details.child[asset.assetTag]) {

                            }
                            else {
                                count++;
                            }
                        }
                        if (!asset.hasChild && asset.inventoryCheck && asset.assetStatus != 'UNIQUE' && asset.assetStatus != 'CORRECTED(ORIGINAL)' && asset.assetStatus != 'CORRECTED(UPDATED)'
                            && skuBody.details.originalCheck[asset.assetTag]) {
                            if (skuBody.details.child[asset.assetTag]) {

                            }
                            else {
                                count++;
                            }
                        }
                    });
                    skuBody.canUpdate = count;
                });
                console.log("grouped is ", $scope.groupedTransferAssetList);
                $scope.duplicateGroupedTransferAssetList = angular.copy($scope.groupedTransferAssetList);
            }

            $scope.filterCanUpdateData = function (check) {
                $scope.duplicateGroupedTransferAssetList = [];
                if (check) {
                    angular.forEach($scope.groupedTransferAssetList,function (item) {
                        if (item.canUpdate > 0) {
                            $scope.duplicateGroupedTransferAssetList.push(item);
                        }
                    });
                }
                else {
                    $scope.duplicateGroupedTransferAssetList = angular.copy($scope.groupedTransferAssetList);
                }
            };

            function getGroupedByAssetListObj(){
                var obj = {};
                obj.skuId = null;
                obj.canUpdate = 0;
                var innerObj = {};
                innerObj.skuName = "";
                innerObj.productName = "";
                innerObj.total = 0;
                innerObj.assetList = [];
                innerObj.originalCheck = {};
                innerObj.child = {};
                obj.details = innerObj;
                return obj;
            }

            $scope.updateAssetStatus = function (asset,isOriginal) {
                if (isOriginal) {
                    $scope.updateAsset(asset,isOriginal);
                }
                else {
                    $scope.availableAssets = [];
                    $scope.getAvailableAssets(asset,function () {
                        console.log("available assets are : ",$scope.availableAssets);
                        var assetCorrectionModal = Popeye.openModal({
                            templateUrl: "assetCorrections.html",
                            controller: "assetCorrectionsCtrl",
                            modalClass: 'modal-large',
                            resolve: {
                                asset: function () {
                                    return asset;
                                },
                                assetsList: function () {
                                    return angular.copy($scope.availableAssets);
                                },
                                groupList: function () {
                                    return angular.copy($scope.duplicateGroupedTransferAssetList);
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        assetCorrectionModal.closed.then(function (result) {
                            if (result.isClosed) {
                                $scope.updateAsset(asset,isOriginal,result.selectedAsset);
                            }
                        });
                    });
                }
            };

            function verifyAssetForUpdate(asset,originalCheck) {
                return asset.assetStatus != 'UNIQUE' && asset.assetStatus != 'CORRECTED(ORIGINAL)' && asset.assetStatus != 'CORRECTED(UPDATED)' && originalCheck[asset.assetTag] &&
                    (!asset.hasChild && asset.inventoryCheck) && !(asset.hasChild || asset.inventoryCheck == false);
            }

            $scope.bulkAssetUpdate = function (assetList,originalCheck,asset,currentItem) {
                var finalList = [];
                for (var i=0;i<assetList.length;i++) {
                    if (asset.assetTag == assetList[i].assetTag && verifyAssetForUpdate(assetList[i],originalCheck)) {
                        finalList.push(assetList[i]);
                    }
                }
                if (finalList.length > 0) {
                    console.log("final list is : ",finalList);
                    $scope.getAvailableAssets(asset,function () {
                        var bulkAssetUpdate = Popeye.openModal({
                            templateUrl: "bulkAssetUpdate.html",
                            controller: "bulkAssetUpdateCtrl",
                            modalClass: 'modal-large',
                            resolve: {
                                asset: function () {
                                    return asset;
                                },
                                assetsList: function () {
                                    return angular.copy($scope.availableAssets);
                                },
                                assetsFinalList: function () {
                                    return angular.copy(finalList);
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        bulkAssetUpdate.closed.then(function (result) {
                            if (result.isClosed) {
                                $scope.updateAllAssets(result.selectedAssets);
                            }
                        });
                    });
                }
                else {
                    $toastService.create("No Assets found for Bulk Updating ..!");
                }
            };

            $scope.updateAllAssets = function (list) {
                var currentUser = appUtil.getCurrentUser();
                var updatedBy = currentUser.user.name+"["+currentUser.userId+"]";
                var assetListPayload = [];
                var prevtag = null;
                for (var i=0;i<list.length;i++) {
                    list[i].updatedBy = updatedBy;
                    var updatedAsset = list[i];
                    updatedAsset.updatedAssetTag = list[i].bulkSelectedAsset.tagValue;
                    updatedAsset.updatedAssetId = list[i].bulkSelectedAsset.assetId;
                    prevtag = updatedAsset.assetTag;
                    console.log("updated assset is : ",updatedAsset);
                    assetListPayload.push(updatedAsset);
                }
                $http({
                    method: 'POST',
                    url: apiJson.urls.transferOrderManagement.bulkupdateAssetStatus,
                    params: {
                        isOriginal: false,
                        userId: appUtil.getCurrentUser().userId
                    },
                    data: assetListPayload
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Asset Status Updated Successfully..!");
                        $scope.lastUpdatedAssetTag = prevtag;
                        $scope.init();
                    } else {
                        $toastService.create("Error Occurred While Updating Asset Status..!");
                    }
                }, function error(response) {
                    console.log("Encountered an error", response);
                    $toastService.create("Error Occurred While Updating Asset Status");
                });
            };

            $scope.getAvailableAssets = function (asset,callBack) {
                var products = [];
                products.push(asset.productId)
                $http({
                    url: apiJson.urls.assetManagement.getTransferableAssetsFromUnitByProducts,
                    method: 'POST',
                    params : {
                        unitId : $scope.currentUnit.id,
                    },
                    data : products,
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    if(response.data!=null && response.data.length > 0) {
                        $scope.availableAssets = [];
                        $scope.availableAssets = response.data;
                        if (callBack != undefined && callBack != null) {
                            console.log("response data is ",response.data);
                            callBack();
                        }
                    }
                    else {
                        $toastService.create("No Assets Available for the Product : "+asset.productName);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create("Error Occurred while getting available Assets..!");
                });
            };

            $scope.updateAsset = function (asset,isOriginal,selectedAsset) {
                var currentUser = appUtil.getCurrentUser();
                var updatedBy = currentUser.user.name+"["+currentUser.userId+"]";
                asset.updatedBy = updatedBy;
                var updatedAsset = asset;
                if (!isOriginal) {
                    updatedAsset.updatedAssetTag = selectedAsset.tagValue;
                    updatedAsset.updatedAssetId = selectedAsset.assetId;
                    console.log("updated assset is : ",updatedAsset);
                }
                $http({
                    method: 'POST',
                    url: apiJson.urls.transferOrderManagement.updateAssetStatus,
                    params: {
                        isOriginal: isOriginal,
                        userId: appUtil.getCurrentUser().userId
                    },
                    data: updatedAsset
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Asset Status Updated Successfully..!");
                        $scope.lastUpdatedAssetTag = asset.assetTag;
                        $scope.init();
                    } else {
                        $toastService.create("Error Occurred While Updating Asset Status..!");
                    }
                }, function error(response) {
                    console.log("Encountered an error", response);
                    $toastService.create("Error Occurred While Updating Asset Status");
                });
            };
        }]).controller('assetCorrectionsCtrl', ['$scope','Popeye', '$toastService','asset','assetsList','groupList',
    function ($scope, Popeye, $toastService,asset, assetsList,groupList) {
        $scope.init = function () {
            $scope.originalAsset = asset;
            $scope.assetsList = assetsList;
            console.log("asset list is : ",$scope.assetsList);
            $scope.selectedAsset = null;
            $scope.groupedTransferAssetList = groupList;
        }

        $scope.close = function () {
            Popeye.closeCurrentModal({
                "isClosed":false});
        };

        $scope.setSelectedAsset = function (assetSelected) {
            $scope.selectedAsset = assetSelected;
            console.log("selected asset is : ",$scope.selectedAsset);
        };

        $scope.correctAsset = function () {
            if ($scope.selectedAsset == null) {
                $toastService.create("Please select the asset Tag..!");
                $scope.selectedAsset = null;
                return false;
            }
            if ($scope.selectedAsset.tagValue == $scope.originalAsset.assetTag) {
                $toastService.create("Can not correct assset with same tag value ...! Please select a different Asset Tag..!");
                $scope.selectedAsset = null;
                return false;
            }
            var check = false;
            for(var j in $scope.groupedTransferAssetList){
                if($scope.groupedTransferAssetList[j].skuId == $scope.originalAsset.skuId){
                    if ($scope.groupedTransferAssetList[j].details.originalCheck[$scope.selectedAsset.tagValue] != undefined
                        && $scope.groupedTransferAssetList[j].details.originalCheck[$scope.selectedAsset.tagValue]) {
                        check = true;
                        break;

                    }
                }
            }
            if (check) {
                $toastService.create("Can not assign tag which was already marked as original..! Please select a different Asset Tag..!");
                $scope.selectedAsset = null;
                return false;
            }
            Popeye.closeCurrentModal({
                "isClosed":true,
                "selectedAsset" : $scope.selectedAsset
            });
        };
    }]).controller('bulkAssetUpdateCtrl', ['$scope','Popeye', '$toastService','asset','assetsList','assetsFinalList',
    function ($scope, Popeye, $toastService,asset, assetsList,assetsFinalList) {
        $scope.init = function () {
            $scope.assetsList = assetsList;
            $scope.selectedAssets = [];
            console.log("asset list is : ",$scope.assetsList);
            $scope.originalAsset = asset;
            $scope.assetsFinalList = assetsFinalList;
            $scope.assetTagToItemMap = {};
        }

        $scope.close = function () {
            Popeye.closeCurrentModal({
                "isClosed":false});
        };

        $scope.changeRow = function (item) {
            item.bulkSelectedAsset = null;
        }

        $scope.setSelectedAsset = function (item,selectedAsset) {
            console.log("item is ",item);
            var selectedAssetCount = 0;
            if (selectedAsset.tagValue == $scope.originalAsset.assetTag) {
                item.bulkSelectedAsset = null;
                $toastService.create("Please select a tag which is not the Original Asset Tag..!");
                return false;
            }
            for (var i=0;i<$scope.assetsFinalList.length;i++) {
                if ($scope.assetsFinalList[i].checked &&  $scope.assetsFinalList[i].bulkSelectedAsset != undefined && $scope.assetsFinalList[i].bulkSelectedAsset !=null
                    && $scope.assetsFinalList[i].bulkSelectedAsset.tagValue == selectedAsset.tagValue) {
                    selectedAssetCount++;
                }
            }
            if (selectedAssetCount > 1) {
                item.bulkSelectedAsset = null;
                $toastService.create("Please select a tag which is not selected for other asset..!");
                return false;
            }
        };

        $scope.bulkGenerate = function () {
            var checkedAssets = 0;
            var finalList = [];
            angular.forEach($scope.assetsFinalList,function (asset) {
                if (asset.checked) {
                    checkedAssets++;
                    finalList.push(asset);
                }
            });
            if (checkedAssets == 0) {
                $toastService.create("Please Select at least 1 asset for Bulk Correction..!");
                return false;
            }

            var missedTags = [];
            for (var i=0;i<$scope.assetsFinalList.length;i++) {
                if ($scope.assetsFinalList[i].checked) {
                    if ($scope.assetsFinalList[i].bulkSelectedAsset == undefined || $scope.assetsFinalList[i].bulkSelectedAsset == null) {
                        missedTags.push($scope.assetsFinalList[i].toItemId);
                    }
                }
            }
            if (missedTags.length > 0) {
                $toastService.create("Please Select the asset Tag for To Item Id : "+missedTags.join(","));
                return false;
            }
            Popeye.closeCurrentModal({
                "isClosed":true,
                "selectedAssets" : finalList
            })
        };
    }]);