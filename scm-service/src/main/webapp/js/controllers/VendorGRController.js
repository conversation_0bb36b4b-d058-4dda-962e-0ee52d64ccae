'use strict';

angular.module('scmApp')
    .controller('vendorGrCreateCtrl', ['$rootScope', '$scope','$state', 'apiJson', '$http', 'appUtil','metaDataService','$fileUploadService','$window',
        '$toastService','$alertService','previewModalService','$timeout','$window', function ($rootScope, $scope, $state, apiJson, $http, appUtil, metaDataService,
                                $fileUploadService,$window,$toastService, $alertService, previewModalService,$timeout) {

            function getMinDate(poList){
                if(!appUtil.isEmptyObject(poList) && poList.length>0){
                    return poList[poList.length-1].generationTime;
                }else{
                    return appUtil.getDate(-365);
                }
            }

            $scope.uploadDoc = function (gr) {
              $fileUploadService.openFileModal("Upload Invoice Sheet", "Find", function (file,gr) {
                        $scope.uploadInvoiceSheet(file);
               });
             };

             $scope.uploadInvoiceSheet = function (file, gr) {
                         var fd = new FormData(document.forms[0]);
                         fd.append("file", file);
                         fd.append("InvoiceId", invoiceId);
                         $http({
                             url: apiJson.urls.invoiceManagement.uploadInvoicesSheet,
                             method: 'POST',
                             data: fd,
                             headers: {'Content-Type': undefined},
                             transformRequest: angular.identity
                         }).success(function (response) {
                             $rootScope.showFullScreenLoader = false;
                             if (response == true) {
                                 $scope.uploadFlag = response;
                                 $toastService.create("Excel Sheet is uploaded successfully!!.");
                                 $scope.getInvoices();
                             }
                         }).error(function (response) {
                             $rootScope.showFullScreenLoader = false;
                                 $toastService.create("Error in uploaded sheet.");
                         });
                     };


            $scope.init = function (){
                $scope.maxDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
                $scope.minDate = appUtil.formatDate(getMinDate(), "yyyy-MM-dd");
                $scope.locationList = [];
                $scope.pendingPOs = [];
                $scope.clearData();
                $scope.currentUnit = appUtil.getUnitData();
                $scope.vendorRegCheck = true;
                metaDataService.getVendorsForUnitTrimmed($scope.currentUnit.id,function(vendorsForUnit){
                        $scope.vendorList = vendorsForUnit;
                    });
                $scope.showPreview = previewModalService.showPreview;
                $scope.selectedPOType = 'REGULAR_ORDER';
                if(appUtil.isCafe()){
                    $scope.selectedPOType = 'REGULAR_ORDER';
                }
                $scope.disableEnableGRType = false;
                $scope.selectedType = null;
                $scope.resetCapexOpex();
                $scope.disableCapex = false;
                $scope.disableOpex = false;
                $scope.duplicateSelectedPo = [];
                $scope.packagingMappings = appUtil.getPackagingMap();
                $scope.posAdvance = [];
            };

            $scope.grSelected = function() {
                if(!appUtil.isEmptyObject($scope.pendingPOs)){
                    angular.forEach($scope.pendingPOs, function(po, index) {
                        if(po.checked != null){
                            po.checked = false;
                        }
                    })
                    $scope.disableCapex = false;
                    $scope.disableOpex = false;
                    $scope.duplicateSelectedPo = [];
                    $scope.posAdvance = [];
                }
            }

            function getTaxMessage(item) {
                var msg = "";
                if (!appUtil.isEmptyObject(item.igst)) {
                    msg += "IGST " + item.igst.percentage + "% ";
                }
                if (!appUtil.isEmptyObject(item.cgst)) {
                    msg += "CGST " + item.cgst.percentage + "% ";
                }
                if (!appUtil.isEmptyObject(item.sgst)) {
                    msg += "SGST " + item.sgst.percentage + "% ";
                }
                if (!appUtil.isEmptyObject(item.otherTaxes)) {
                    msg += item.otherTaxes[0].taxName + " " + item.otherTaxes[0].percentage + "% ";
                }
                return msg;
            }

            $scope.getPendingPoByType = function () {
                var isVendorBlocked = $scope.blockedAdvancePayments.length > 0;
                if(!appUtil.isEmptyObject($scope.pendingPOs)){
                    angular.forEach($scope.pendingPOs, function(po) {
                        if (po.orderType == 'REGULAR_ORDER') {
                            if(po.type == "CAPEX") {
                                $scope.pendingCapexRegularPo = $scope.pendingCapexRegularPo + 1;
                            } else {
                                $scope.pendingOpexRegularPo =  $scope.pendingOpexRegularPo + 1;
                            }
                        }
                        if (po.orderType == 'FIXED_ASSET_ORDER') {
                            if(po.type == "CAPEX") {
                                $scope.pendingCapexAssetPo = $scope.pendingCapexAssetPo + 1;
                            } else {
                                $scope.pendingOpexAssetPo =  $scope.pendingOpexAssetPo + 1;
                            }
                        }

                        if (isVendorBlocked) {
                            if (po.vendorAdvancePayments!= null && po.vendorAdvancePayments.length > 0) {
                                var check = true;
                                for (var i = 0; i < po.vendorAdvancePayments.length;i++) {
                                    if ($scope.blockedAdvancePayments.indexOf(po.vendorAdvancePayments[i].advancePaymentId) != -1) {
                                        check = false;
                                    }
                                }
                                po.vendorBlocked = check;
                            } else {
                                po.vendorBlocked = true;
                            }
                        }

                        angular.forEach(po.orderItems, function (poItem) {
                            var taxMsg = getTaxMessage(poItem);
                            poItem.taxMessage = taxMsg;
                        });
                    })
                }
            };

            $scope.disableSelection = function ($event,type,po) {
                console.log("duplicate arr is ",$scope.duplicateSelectedPo);
                var advanceCheck = null;
                if (po.vendorAdvancePayments != null && po.vendorAdvancePayments.length > 0) {
                    var key = "";
                    for(var i = 0; i < po.vendorAdvancePayments.length; i++) {
                        key = key + "_" +  po.vendorAdvancePayments[i].advancePaymentId;
                    }
                    advanceCheck = "ADVANCE_" + key;
                } else {
                    advanceCheck = "NO_ADVANCE";
                }

                if ($scope.posAdvance.length > 0) {
                    var lastItem = $scope.posAdvance[$scope.posAdvance.length - 1];
                    if (lastItem != "NO_ADVANCE") {
                        if (lastItem != advanceCheck) {
                            $toastService.create("Cannot Club PO's of Different Advance Payments");
                            po.checked = false;
                            return;
                        }
                    } else {
                        if (lastItem != advanceCheck) {
                            $toastService.create("Cannot Club PO's of Advance Payment with PO's Of Non Advance Payment");
                            po.checked = false;
                            return;
                        }
                    }
                }
                if (po.vendorAdvancePayments != null && po.vendorAdvancePayments.length > 0) {
                    for(var j = 0; j < po.vendorAdvancePayments.length; j++) {
                        if (po.vendorAdvancePayments[j].advanceStatus == 'INITIATED') {
                            var msg = [];
                            for (var k = 0; k < po.vendorAdvancePayments.length; k++) {
                                if (po.vendorAdvancePayments[k].advanceStatus == 'INITIATED') {
                                    msg.push(po.vendorAdvancePayments[k].advancePaymentId);
                                }
                            }
                            $toastService.create("Please Settle the All Vendor Advance for this PO first Pending Advances : " + msg.join(","));
                            po.checked = false;
                            return;
                        }
                        if (po.vendorAdvancePayments[j].pendingPrs.length > 0) {
                            $toastService.create("Please Settle all the Pending PR's related to this PO : " + po.vendorAdvancePayments[j].pendingPrs.join(","));
                            po.checked = false;
                            return;
                        }
                        if (po.vendorAdvancePayments[j].pendingGrs.length > 0) {
                            $toastService.create("Please Settle all the Pending GR's related to this PO : " + po.vendorAdvancePayments[j].pendingGrs.join(","));
                            po.checked = false;
                            return;
                        }
                    }
                }
                if ($scope.duplicateSelectedPo.length == 0) {
                    if ($event.target.checked) {
                        $scope.duplicateSelectedPo.push(type);
                        if (po.vendorAdvancePayments != null && po.vendorAdvancePayments.length > 0) {
                            $scope.posAdvance.push(advanceCheck);
                        } else {
                            $scope.posAdvance.push(advanceCheck);
                        }
                        if (type == "CAPEX") {
                            $scope.disableOpex = true;
                        }
                        else {
                            $scope.disableCapex = true;
                        }
                    }
                }
                else {
                    if ($event.target.checked) {
                        $scope.duplicateSelectedPo.push(type);
                        if (type == "CAPEX") {
                            $scope.disableOpex = true;
                        }
                        else {
                            $scope.disableCapex = true;
                        }
                        if (po.vendorAdvancePayments != null && po.vendorAdvancePayments.length > 0) {
                            $scope.posAdvance.push(advanceCheck);
                        } else {
                            $scope.posAdvance.push(advanceCheck);
                        }
                    }
                    else {
                        $scope.duplicateSelectedPo.pop();
                        $scope.posAdvance.pop();
                        if ($scope.duplicateSelectedPo.length == 0) {
                            $scope.disableOpex = false;
                            $scope.disableCapex = false;
                        }
                    }
                }
                console.log("duplicate arr is ",$scope.duplicateSelectedPo);
            };

            $scope.clearData = function(){
                $scope.selectedOrders = [];
                $scope.showExpandedView = false;
                $scope.grItems = null;
                $scope.extraGrItems = undefined;
                $scope.gr = null;
                $scope.showGrItems = false;
            };

            $scope.checkForVendorBlock = function(vendor) {
                $scope.blockedAdvancePayments = [];
                if (vendor.vendorBlocked != undefined && vendor.vendorBlocked != null && vendor.vendorBlocked == 'Y') {
                    if (vendor.blockedReason == 'MANUAL') {
                        $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                            "<br><b>Please Contact Finance Team..!</b>", function () {
                        }, true);
                        $scope.selectedVendor = null;
                        $scope.locationList = [];
                        $timeout(function () {
                            $('#vendorList').val('').trigger('change');
                        });
                    } else {
                        $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                            "<br><b>Only PO's Related to these Advance are allowed to process further " + vendor.blockedReason + "</b>", function () {
                        }, false);
                        $scope.blockedAdvancePayments = JSON.parse(vendor.blockedReason);
                    }
                }
            }

            $scope.selectVendor = function(vendor){
                metaDataService.getVendorDetail(vendor.id,function(vendor){
                	$scope.selectedVendor = vendor;
                    $scope.locationList = vendor.dispatchLocations.filter(function (loc) {
                        return loc.status == "ACTIVE";
                    });
                    $scope.checkForVendorBlock($scope.selectedVendor);
                });
                $scope.pendingPOs = null;
            };

            $scope.selectDispatchLocation = function(location){
                $scope.selectedDispatchLocation = location;
                $scope.vendorRegCheck = true;
                if($scope.selectedDispatchLocation.gstin == null){
                	$scope.vendorRegCheck = false;
                }
                $scope.getPendingOrders();
            };

            function priceExists(skuId) {
                var list = $scope.skuPriceList.filter(function(skuTax){
                    return skuId == skuTax.id;
                });
                return list.length>0;
            }

            $scope.getCurrentPrices = function(){
                if(appUtil.isEmptyObject($scope.selectedDispatchLocation)){
                    $toastService.create("Select Dispatch Location first");
                    return;
                }
                if(appUtil.isEmptyObject($scope.selectedPOType)){
                    $toastService.create("Select GR type");
                    return;
                }
                metaDataService.getSkuPricesAndTaxes($scope.selectedVendor.vendorId,
                    $scope.selectedDispatchLocation.dispatchId,
                    $scope.currentUnit.id,true,
                    function(skuAndTaxData){
                        if(skuAndTaxData.length>0){
                            $scope.skuPriceList = skuAndTaxData;
                            $scope.getPendingOrders();
                        }else{
                            $toastService.create("Could not fetch prices & taxes for the dispatch location");
                        }
                    }
                );
            };

            $scope.resetCapexOpex = function () {
                $scope.pendingCapexRegularPo = 0;
                $scope.pendingCapexAssetPo = 0;
                $scope.pendingOpexRegularPo = 0;
                $scope.pendingOpexAssetPo = 0;
            };

            $scope.getPendingOrders = function(){
                $scope.resetCapexOpex();
                $http({
                    method:"GET",
                    url:apiJson.urls.requestOrderManagement.getPendingPOs,
                    params:{
                        vendorId:$scope.selectedVendor.vendorId,
                        deliveryUnitId:$scope.currentUnit.id,
                        dispatchId:$scope.selectedDispatchLocation.dispatchId,
                        startDate:appUtil.formatDate(appUtil.getDate(-365), "yyyy-MM-dd"),
                        endDate:appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd")
                    }
                }).then(function(response){
                    $scope.pendingPOs = response.data;
                    $scope.getPendingPoByType();
                    if(appUtil.isEmptyObject($scope.pendingPOs)){
                        $toastService.create("Could not find pending Purchase Orders!");
                    }
                },function(error){
                    console.log(error);
                });
            };

            $scope.changeTotalAmount = function(){
                if($scope.gr.extraCharges>=parseFloat(1)){
                    $scope.gr.totalCharged = $scope.gr.totalAmount + $scope.gr.extraCharges;
                }else{
                    $scope.gr.totalCharged = $scope.gr.totalAmount;
                    $toastService.create("Extra Charges cannot be less than 1");
                }
            };

            $scope.goBack = function () {
                $scope.pendingPOs.forEach(function(po){
                   po.checked = false;
                });
                $scope.clearData();
                $scope.disableEnableGRType = false;
                $scope.disableCapex = false;
                $scope.disableOpex = false;
                $scope.duplicateSelectedPo = [];
                $scope.posAdvance = [];
            };

            function getSkus(skusOnly){
                var skus = {};
                $scope.selectedOrders.forEach(function(order){
                    order.orderItems.forEach(function(item){
                        if(item.receivedQuantity<item.requestedQuantity){
                            var tillNow = (appUtil.isEmptyObject(item.received) ? 0 : parseFloat((item.received * item.conversionRatio).toFixed(6)));
                            var received = parseFloat(item.receivedQuantity + tillNow);
                        if(Object.keys(skus).indexOf(item.skuId)==-1){
                            skus[item.skuId] =  {};
                            skus[item.skuId][item.packagingId] = {
                                requested:parseFloat(item.requestedQuantity),
                                received:parseFloat(received)
                            };
                        }else{
                            if(skus[item.skuId][item.packagingId] != undefined){
                                skus[item.skuId][item.packagingId].requested += parseFloat(item.requestedQuantity);
                                skus[item.skuId][item.packagingId].received += received;
                            }else{
                                skus[item.skuId][item.packagingId] = {
                                    requested:parseFloat(item.requestedQuantity),
                                    received:parseFloat(received)
                                };
                            }
                        }
                        }
                    });
                });
                return skusOnly ? Object.keys(skus) : skus;
            }

            $scope.removeExtraGrItem = function (item) {
                var key = item.sku.skuData.id + "_" + item.pkg.id;
                delete $scope.extraGrItems[key];
                if(Object.keys($scope.extraGrItems).length==0){
                    $scope.extraGrItems=null;
                }
                updateGr($scope.grItems,$scope.extraGrItems);
            };

            function validateExtraGrOption(){
                $scope.extraGrSkusObj = {};
                $scope.showExtraGRItems = false;
                if($scope.selectedOrders.length>0){
                    $http({
                        method:"POST",
                        url:apiJson.urls.requestOrderManagement.extraGrEligibility,
                        data:{
                            unitId:$scope.currentUnit.id,
                            vendorId:$scope.selectedVendor.vendorId,
                            dispatchId:$scope.selectedDispatchLocation.dispatchId,
                            skus: getSkus(true),
                            poIds:$scope.selectedOrders.map(function(po){
                                return po.id;
                            })
                        }
                    }).then(function(response){
                        if(!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)){
                            var filteredSkus = response.data;
                            $scope.selectedOrders.forEach(function(order){
                                order.orderItems.forEach(function(item){
                                    if(filteredSkus.indexOf(item.skuId)!=-1){
                                    $scope.extraGrSkusObj[getKey(item)] = angular.copy(item);
                                    $scope.extraGrSkusObj[getKey(item)].packagingQty = undefined;
                                    getSkus(false);
                                    }
                                });
                            });
                            $scope.extraGrSkus = Object.values($scope.extraGrSkusObj);
                            $scope.showExtraGRItems = ($scope.extraGrSkus.length > 0 && $scope.selectedPOType != 'FIXED_ASSET_ORDER');
                        }
                    },function(error){
                        console.log(error);
                    });
                }
            }

            function validateSamePricesAndTaxes(selectedOrders){
                var itemPrices = {};
                $scope.diffPriceProducts = [];
                $scope.diffTaxProducts = [];
                var check = true;
                for(var i in selectedOrders){
                    var order = selectedOrders[i];
                    for(var j in order.orderItems){
                        var item = order.orderItems[j];
                        if(appUtil.isEmptyObject(itemPrices[getKey(item)])){
                            itemPrices[getKey(item)] = item.unitPrice;
                        }else{
                            if(itemPrices[getKey(item)] != item.unitPrice){
                                if ($scope.diffPriceProducts.indexOf(item.skuName) == -1) {
                                    $scope.diffPriceProducts.push(item.skuName);
                                }
                                check = false;
                            }
                        }

                        var taxes = ["IGST","CGST","SGST","OTHER"];

                        angular.forEach(taxes, function (tax) {
                            var currentTaxType = null;
                            if (tax == "IGST") {
                                currentTaxType = item.igst;
                            } else if (tax == "CGST") {
                                currentTaxType = item.cgst;
                            } else if (tax == "SGST") {
                                currentTaxType = item.sgst;
                            } else {
                                currentTaxType = item.otherTaxes;
                            }

                            if(appUtil.isEmptyObject(itemPrices[getKey(item) + "_" + tax])) {
                                itemPrices[getKey(item) + "_" + tax] = appUtil.isEmptyObject(currentTaxType) ? "NULL" : (tax == "OTHER" ? currentTaxType[0].percentage : currentTaxType.percentage);
                            } else {
                                var taxPercentage = appUtil.isEmptyObject(currentTaxType) ? "NULL" : (tax == "OTHER" ? currentTaxType[0].percentage : currentTaxType.percentage);
                                if(itemPrices[getKey(item) + "_" + tax] != taxPercentage){
                                    if ($scope.diffTaxProducts.indexOf(item.skuName + " - " + tax) == -1) {
                                        $scope.diffTaxProducts.push(item.skuName + " - " + tax);
                                    }
                                    check = false;
                                }
                            }
                        });
                    }
                }
                return check;
            }

            $scope.selectPOs=function(){
                var selected = $scope.pendingPOs.filter(function(po){
                    return po.checked!=undefined && po.checked;
                });

                if(selected.length>0){
                    $scope.selectedOrders = angular.copy(selected);
                    if(validateSamePricesAndTaxes(selected)){
                    $scope.showExpandedView = true;
                    validateExtraGrOption();
                    $scope.minDate = appUtil.formatDate(getMinDate($scope.selectedOrders), "yyyy-MM-dd");
                    $scope.disableEnableGRType = true;
                    }else{
                        $toastService.create("Only POs having same prices and taxes for their SKUs can be merged together.");
                        if ($scope.diffPriceProducts.length > 0) {
                            $toastService.create("Different Prices products are " + $scope.diffPriceProducts.join(","));
                        }
                        if ($scope.diffTaxProducts.length > 0) {
                            $toastService.create("Different taxes products are " + $scope.diffTaxProducts.join(","));
                        }
                    }
                }else{
                    $toastService.create("Select at least one Purchase Order before moving forward.");
                }
            };

            function getKey(item){
                return item.skuId + "_" + item.packagingId;
            }

            function getTotalReceived(orders,key){
                var usedPOItems = {};
                var received = 0;
                for(var i in orders){
                    var order = orders[i];
                    for(var j in order.orderItems){
                        var item = order.orderItems[j];
                        if(getKey(item)==key && !appUtil.isEmptyObject(item.received)){
                            received += item.received;
                            usedPOItems[item.id] = item.received;
                            break;
                        }
                    }
                }
                return {usedPOItems:usedPOItems, received:received};
            }

            $scope.updateGRQty = function(item){
                // key based on sku id and packaging id of the item to make it unique
                var key = getKey(item);

                if(appUtil.isEmptyObject(item.received) || item.received==0.00 || item.received > ((item.requestedQuantity - item.receivedQuantity)/item.conversionRatio).
                toFixed(2)){
                    item.received=null;
                    //$toastService.create("You cannot Input Receive Quantity 0!!");
                    $toastService.create("Please Enter Valid Qty");
                    var keyValue = $scope.grItems[key];
                    if (keyValue != undefined && keyValue != null) {
                        console.log("Gr key is : ",keyValue);
                        var totalReceivedObj = getTotalReceived($scope.selectedOrders,key);
                        if(totalReceivedObj.received > 0) {
                            $scope.grItems[key].received = totalReceivedObj.received;
                            $scope.grItems[key].usedPOItems = totalReceivedObj.usedPOItems;
                            keyValue = recalculate(keyValue,item);
                            $scope.grItems[key] = keyValue;
                        }else{
                            delete  $scope.grItems[key];
                            if(Object.keys($scope.grItems).length==0){
                                $scope.grItems=null;
                            }
                        }
                        updateGr($scope.grItems,$scope.extraGrItems);
                    }
                    return;
                }
                if(appUtil.isEmptyObject($scope.grItems)){
                    $scope.grItems = {};
                }
                if(!appUtil.isEmptyObject(item.received)){
                    var receivedTillNow = parseFloat((parseFloat((item.receivedQuantity/item.conversionRatio).toFixed(6)) + item.received).toFixed(6));
                    if(receivedTillNow <= item.packagingQty){
                        var found = $scope.grItems[key];
                        if(appUtil.isEmptyObject(found)){
                            $scope.grItems[key] = updateGRItem(item);  //conversionRatio for conversion into base UOM
                        }else{
                            var totalReceivedObj = getTotalReceived($scope.selectedOrders,key);
                            found.received = totalReceivedObj.received;
                            found.usedPOItems = totalReceivedObj.usedPOItems;
                            found = recalculate(found,item);
                            $scope.grItems[key] = found;
                        }
                    }else{
                        $toastService.create("You cannot receive quantity greater than the requested quantity");
                    }
                } else {
                    var totalReceivedObj = getTotalReceived($scope.selectedOrders,key);
                    if(totalReceivedObj.received > 0){
                        $scope.grItems[key].received = totalReceivedObj.received;
                        $scope.grItems[key].usedPOItems = totalReceivedObj.usedPOItems;
                    }else{
                        delete  $scope.grItems[key];
                        if(Object.keys($scope.grItems).length==0){
                            $scope.grItems=null;
                        }
                    }
                }
                updateGr($scope.grItems,$scope.extraGrItems);
            };

            $scope.byNotFulfilled = function (sku){
                var skus = getSkus(false);
                if(appUtil.isEmptyObject(skus[sku.skuId])){
                    return false;
                }

                var skuItem = skus[sku.skuId][sku.packagingId];
                return (!appUtil.isEmptyObject(skuItem) && skuItem.received == skuItem.requested);
            };

            function validateExtraGrItem(extraGrItem){
                var item = $scope.grItems ? $scope.grItems[getKey(extraGrItem)] : null;
                var receivedQuantity = null;
                if(item == undefined || item == null){
                    //in case the gr item for the selected item is not there, check from the elapsed quantity
                    receivedQuantity = extraGrItem.receivedQuantity;
                }else{
                    receivedQuantity = item.received;
                }
                return receivedQuantity!=null ? parseFloat(extraGrItem.received/receivedQuantity) <= 0.05 : false;
            }

            $scope.addExtraGrItems = function(extraGrItem){
                if(validateExtraGrItem(extraGrItem)){
                    if(appUtil.isEmptyObject($scope.extraGrItems)){
                        $scope.extraGrItems = {};
                    }
                    $scope.extraGrItems[getKey(extraGrItem)] = updateGRItem(extraGrItem,true);
                    updateGr($scope.grItems,$scope.extraGrItems);
                }else{
                    $toastService.create("Only 5% of the requested quantity is allowed for Extra GR");
                }
            };

            $scope.uploadDoc = function(){
                $fileUploadService.openFileModal("Upload GR Document","Find",function(file){
                    metaDataService.uploadFile("OTHERS","GR",file, function(doc){
                        $scope.uploadedDocument = doc;
                    });
                });
            };

            // $scope.uploadVendorInvoice = function () {
            //     $fileUploadService.openFileModal("Upload Vendor Invoice", "Find", function (file) {
            //         metaDataService.uploadFile("OTHERS", "VENDOR_INVOICE", file, function (doc) {
            //             $scope.uploadedInvoiceDoc = doc;
            //         });
            //     });
            // };

            $scope.watchInvoice = function () {
                  if($scope.uploadedInvoiceDoc !=null && $scope.uploadedInvoiceDoc.fileUrl !=null )
                        $window.open($scope.uploadedInvoiceDoc.fileUrl, '_blank');
            }

            $scope.uploadVendorInvoice = function () {

                $fileUploadService.openFileModal("Upload Vendor Invoice", "Find", function (file) {
                    if (file == null) {
                        $toastService.create("File cannot be empty");
                        return;
                    }
                    if (file.size > 307200) {
                        $toastService.create("File size should not be greater than 300 kb.");
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('docType', "VENDOR_INVOICE");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', appUtil.getCurrentUser().userId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.goodsReceivedManagement.uploadVendorInvoice,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.uploadedInvoiceDoc = response;
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                });
            };



            function getPackagingObj(pack) {
                var result = {
                    "code" : pack.packagingCode,
                    "id" : pack.packagingId,
                    "leadTime" : null,
                    "name" : pack.packagingName,
                    "ratio" : pack.conversionRatio,
                    "uom" : pack.unitOfMeasure
                };
                return result;
            }

            function updateGRItem(item, isExtraGrItem) {
                var toModify = {
                    sku: {
                        skuData: {
                            id: item.skuId,
                            name: item.skuName,
                            uom: item.unitOfMeasure
                        }
                    },
                    pkg: null,
                    received: item.received,
                    totalAmount: 0,
                    totalTax: 0,
                    paidAmount: 0,
                    usedPOItems: {}
                };

                toModify.pkg = getPackagingObj($scope.packagingMappings[item.packagingId]);
                toModify.pkg.price = item.unitPrice;
                toModify.usedPOItems[item.id] = item.received;
                if (isExtraGrItem) {
                    delete toModify.usedPOItems;
                }
                return recalculate(toModify, item);
            }

            function recalculate(toModify, item){
                var amountAndTax = getAmount(toModify.pkg,toModify.received,item);
                toModify.totalAmount = amountAndTax.amount;
                toModify.totalTax = amountAndTax.tax;
                toModify.paidAmount = (amountAndTax.amount + amountAndTax.tax);
                toModify.igst = item.igst;
                toModify.sgst = item.sgst;
                toModify.cgst = item.cgst;
                toModify.hsn = item.hsn;
                toModify.otherTaxes = item.otherTaxes;
                return toModify;
            }

            function updateGr(grItems, extraGrItems){
                $scope.gr = appUtil.isEmptyObject($scope.gr) ? {totalPrice:0,totalAmount:0,totalTax:0} : $scope.gr;
                var totalPrice = 0;
                var totalAmount = 0;
                var totalTax = 0;
                if(grItems!=null){
                    for(var i in grItems){
                        var grItem = grItems[i];
                        totalPrice += grItem.totalAmount;
                        totalTax += grItem.totalTax;
                        totalAmount += grItem.paidAmount;
                    }
                }

                if(!appUtil.isEmptyObject(extraGrItems)){
                    for(var i in extraGrItems){
                        var extraGrItem = extraGrItems[i];
                        totalPrice += extraGrItem.totalAmount;
                        totalTax += extraGrItem.totalTax;
                        totalAmount += extraGrItem.paidAmount;
                    }
                }

                $scope.gr.totalTax = parseFloat(totalTax.toFixed(6));
                $scope.gr.totalPrice = parseFloat(totalPrice.toFixed(6));
                $scope.gr.totalAmount = parseFloat(totalAmount.toFixed(6));
                $scope.gr.totalCharged = parseFloat(totalAmount.toFixed(6));
            }

            function validateGR(){
                return $scope.gr.docDate!=null && $scope.gr.docNumber!=null
                    && $scope.gr.docType!=null && Object.keys($scope.grItems).length>0;
            }

            $scope.submit = function(force){
                if(validateGR()){
                    $alertService.confirm("Are you sure?","",function(result){
                        if(result){
                            sendRequestForGR(force);
                        }
                    });
                }else{
                    $toastService.create("Please fill in the document type, document date and document number properly before submitting");
                }
            };


            function getReceivedItems(order){
                return order.orderItems.filter(function(item){
                    return (!appUtil.isEmptyObject(item.received) && item.received>0);
                }).map(function(item){
                   return {
                       id:item.skuId,
                       pkg:item.packagingId,
                       qty:parseFloat((item.received * item.conversionRatio).toFixed(6))
                   };
                });
            }

            function sendRequestForGR(force){
                var reqObj = {
                    userId:appUtil.getCurrentUser().userId,
                    deliveryUnitId:$scope.currentUnit.id,
                    dispatchId:$scope.selectedDispatchLocation.dispatchId,
                    vendorId:$scope.selectedVendor.vendorId,
                    usedPOList: $scope.selectedOrders.map(function(po){
                        return {
                            id:po.id,
                            skus:getReceivedItems(po)
                        };
                    }),
                    extraCharges:$scope.gr.extraCharges,
                    docType:$scope.gr.docType,
                    docNumber:$scope.gr.docNumber,
                    docDate:$scope.gr.docDate,
                    creationType:"MANUAL",
                    amountMatched:angular.isUndefined($scope.gr.amountMatched) ? false : $scope.gr.amountMatched,
                    items:$scope.prepareGRItems($scope.grItems),
                    extraGrItems:$scope.prepareGRItems($scope.extraGrItems),
                    vendorGrType : $scope.selectedPOType,
                    forceSummit : force,
                    type: $scope.selectedOrders[0].type,
                    comment : $scope.gr.comment,
                    vendorInvoiceDocId : appUtil.isEmptyObject($scope.uploadedInvoiceDoc)?null: $scope.uploadedInvoiceDoc.documentId
                };

                if(reqObj.items.length>0){
                    $http({
                        method:"POST",
                        url:apiJson.urls.goodsReceivedManagement.createVendorGR,
                        data:reqObj
                    }).then(function(response){
                        if(response.data!=null){
                            $scope.init();
                            $toastService.create("GR with ID: " + response.data + " created", function(){
                                $state.go('menu.viewVendorGR',{vendor:$scope.selectedVendor, dispatchLocation:$scope.selectedDispatchLocation,grId:response.data});
                            });
                        }else {
                            $toastService.create("GR could not be created due to some error!!");
                        }
                    },function(error){
                        console.log(error);
                        $alertService.alert("GR could not be created due to some error!!", error.data.errorMessage);
                    });
                }
            }

            function getTaxValue(category, taxValue, amount) {
                return {
                    taxName:category,
                    taxCategory:category,
                    value: parseFloat((parseFloat((amount*taxValue).toFixed(6))/100).toFixed(6)),
                    percentage:taxValue
                };
            }

            function getAmount(pkg, qty, item) {
                var price = pkg.price;
                var ratio = pkg.ratio;
                var otherTaxes = 0;
                var amount = parseFloat((price * qty).toFixed(6));
                var obj = {
                    amount:amount,
                    taxes:[],
                    tax:0
                };
                if(!$scope.vendorRegCheck){
                	return obj;
                }

                if (!appUtil.isEmptyObject(item.igst) && item.igst.percentage != null ) {
                    var taxObj = getTaxValue("IGST",item.igst.percentage,amount);
                    obj.taxes.push(taxObj);
                    obj.tax += taxObj.value;
                } else {
                    var taxObj1 = getTaxValue("CGST",item.cgst.percentage,amount);
                    var taxObj2 = getTaxValue("SGST",item.sgst.percentage,amount);
                    obj.taxes.push(taxObj1);
                    obj.taxes.push(taxObj2);
                    obj.tax += (taxObj1.value + taxObj2.value);
                }

                if(!appUtil.isEmptyObject(item.otherTaxes) && item.otherTaxes.length>0){
                    for(var index in item.otherTaxes){
                        var otherTax = item.otherTaxes[index];
                        var valueAndTax = getTaxValue(otherTax.taxName, otherTax.percentage,
                                                getApplicableAmount("ON_SALE", obj));
                        obj.taxes.push(valueAndTax);
                        otherTaxes += valueAndTax.value;
                    }
                    obj.tax += otherTaxes; // adding other taxes to the total tax
                }
                return obj;
            }

            function getApplicableAmount(applicability,obj){
                if(applicability == "ON_TAX"){
                    return getValue(obj,"CGST") + getValue(obj,"IGST") + getValue(obj,"SGST");
                }else{
                    return obj.amount;
                }
            }

            function getValue(taxObj, type){
                var amount = 0;
                taxObj.taxes.forEach(function(tax){
                    if(type==tax.taxCategory){
                        amount = appUtil.isEmptyObject(tax.value) ? 0 : tax.value;
                        return false;
                    }
                });
                return amount;
            }

            function fixToSixDecimals(value) {
                return parseFloat(value.toFixed(6));
            }

            $scope.prepareGRItems = function(items){
                if(appUtil.isEmptyObject(items)){
                    return null;
                }
                var obj = Object.values(items).map(function (item) {
                    var amountAndTax = getAmount(item.pkg, item.received, item);
                    var grItem = {
                        "skuId": item.sku.skuData.id,
                        "skuName": item.sku.skuData.name,
                        "hsn": item.hsn,
                        "receivedQuantity": parseFloat((item.received * item.pkg.ratio).toFixed(6)),
                        "unitOfMeasure": item.sku.skuData.uom,
                        "unitPrice": fixToSixDecimals(item.pkg.price),
                        "totalCost": fixToSixDecimals(amountAndTax.amount),
                        "amountPaid": fixToSixDecimals(amountAndTax.amount + amountAndTax.tax),
                        "packagingId": item.pkg.id,
                        "packagingName": item.pkg.name,
                        "conversionRatio": item.pkg.ratio,
                        "packagingQty": fixToSixDecimals(item.received),
                        "totalTax": fixToSixDecimals(amountAndTax.tax),
                        "taxes": amountAndTax.taxes,
                        "usedPOItems":item.usedPOItems
                    };
                    return grItem;
                });

                return obj;
            };
        }
    ]
);
