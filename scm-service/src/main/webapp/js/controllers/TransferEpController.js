/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('trEpCreateCtrl', ['$rootScope', '$scope','$state', 'apiJson', '$http', 'appUtil','$toastService','metaDataService','$alertService','previewModalService',
            function ($rootScope, $scope, $state, apiJson, $http, appUtil, $toastService,metaDataService,$alertService,previewModalService) {

                $scope.init = function () {
                    $scope.currentUnit = appUtil.getUnitData();
                    $scope.comment = null;
                    $scope.transferOrderItems = null;
                    $scope.transferOrderDetail = {
                        generationUnitId : {
                            id:$scope.currentUnit.id,
                            code:"",
                            name:$scope.currentUnit.name
                        },
                        generatedBy: appUtil.createGeneratedBy(),
                        lastUpdatedBy: appUtil.createGeneratedBy(),
                        comment: $scope.comment,
                        transferOrderItems: [],
                        external:true,
                        vendorId:null,
                        dispatchId:null,
                        totalAmount:0,
                        tax:0
                    };

                    metaDataService.getVendorsForUnit($scope.currentUnit.id,function(vendorsForUnit){
                        $scope.vendorList = vendorsForUnit;
                    });
                    $scope.showPreview = previewModalService.showPreview;
                };

                $scope.selectVendor = function(vendor){
                    $scope.transferOrderItems = null;
                    $scope.selectedVendor = vendor;
                    $scope.transferOrderDetail.vendorId = vendor.vendorId;
                    $scope.locationList = vendor.dispatchLocations.filter(function (l) {
                        return l.status == "ACTIVE";
                    });

                };

                $scope.selectDispatchLocation = function(location){
                    $scope.selectedDispatchLocation = location;
                    $scope.transferOrderDetail.dispatchId = location.dispatchId;
                    $scope.getSkusMapped();
                };

                $scope.getSkusMapped = function(){
                    if(!appUtil.isEmptyObject($scope.selectedVendor)
                        && !appUtil.isEmptyObject($scope.currentUnit.id)){
                        metaDataService.getSkusMapped($scope.selectedVendor.vendorId, $scope.currentUnit.id,
                            function(response){
                                if(response.length>0){
                                    $scope.skuList = response;
                                }else{
                                    $toastService.create("Could not fetch SKU details for the vendor location");
                                }
                            });
                    }else{
                        $toastService.create("Please select Vendor & Dispatch Location correctly");
                    }
                };

                function getPackaging(id){
                    if($scope.packagingMap == undefined){
                        $scope.packagingMap = appUtil.getPackagingMap();
                    }
                    return $scope.packagingMap[id];
                }

                $scope.addTOItem = function(sku,pkg,qty,price){
                    if(appUtil.isEmptyObject(sku) || appUtil.isEmptyObject(pkg)
                        || appUtil.isEmptyObject(qty) || appUtil.isEmptyObject(price)){
                        $toastService.create("Please select proper values in order to add SKU to transfer");
                        return;
                    }

                    var itemPrice = getItemPriceDetails(price,pkg,qty);
                    var item ={
                        skuId:sku.id,
                        skuName:sku.name,
                        transferredQuantity:itemPrice.qty,
                        unitOfMeasure:sku.uom,
                        unitPrice:itemPrice.unitPrice,
                        total:itemPrice.total,
                        packagingDetails:[{
                            packagingDefinitionData: getPackaging(pkg.id),
                            conversionRatio:pkg.ratio,
                            numberOfUnitsPacked:qty,
                            transferredQuantity:itemPrice.qty,
                            pricePerUnit:itemPrice.pkgPrice
                        }],
                        tax:0
                    };
                    if($scope.transferOrderItems==null){
                        $scope.transferOrderItems = {};
                    }
                    $scope.transferOrderItems[getKey(item)] = item;
                    calculateAmount($scope.transferOrderItems);
                };

                function getItemPriceDetails(price, pkg, pkgQty) {
                    var priceDetails = {
                      unitPrice:(price / pkg.ratio),
                      pkgPrice:price,
                      total:0,
                      qty:(pkgQty * pkg.ratio)
                    };
                    priceDetails.total = (priceDetails.qty * priceDetails.unitPrice);
                    return priceDetails;
                }

                function getKey(item){
                    return item.skuId + "_" + item.packagingDetails[0].packagingDefinitionData.packagingId;
                }

                $scope.removeTrItem = function(id){
                    delete $scope.transferOrderItems[id];
                    if(Object.keys($scope.transferOrderItems).length==0){
                        $scope.transferOrderItems = null;
                    }
                };

                $scope.updateTrItemQty = function(tr, index, qty){
                    var item = tr[index];
                    var pkg = item.packagingDetails[0];
                    pkg.transferredQuantity = parseFloat(pkg.conversionRatio * qty);
                    item.transferredQuantity = pkg.transferredQuantity;
                    pkg.numberOfUnitsPacked = qty;
                    tr[index] = item;
                };

                $scope.selectSku = function(sku){
                    $scope.selectedSku = sku;
                    $scope.packagingList = null;
                    $scope.packagingList = sku.packagings;
                };

                $scope.selectPkg = function(packaging){
                    $scope.selectedPackaging = packaging;
                };

                function calculateAmount(transferOrderItems){
                    var transfer = $scope.transferOrderDetail;
                    transfer.totalAmount = 0;
                    transfer.tax = 0;
                    Object.values(transferOrderItems).forEach(function(item){
                        transfer.totalAmount = parseFloat(transfer.totalAmount) + parseFloat(item.total);
                        transfer.tax = parseFloat(transfer.tax) + parseFloat(item.tax);
                    });
                }

                function prepareTransfer(){
                    $scope.transferOrderDetail.transferOrderItems = Object.values($scope.transferOrderItems);
                    calculateAmount($scope.transferOrderItems);
                    return $scope.transferOrderDetail;
                }

                $scope.createTransfer = function(){
                    if($scope.transferOrderItems==null || Object.keys($scope.transferOrderItems).length==0){
                        $toastService.create("Please select items to be transferred!");
                        return false;
                    }else if(appUtil.isEmptyObject($scope.transferOrderDetail.comment)){
                        $toastService.create("Please enter a comment to submit Transfer");
                        return false;
                    }else{
                        $alertService.confirm("Are you sure?","You are going to create a Transfer Order for External Vendor", function(response){
                            if(response){
                                $http({
                                    method: "POST",
                                    url: apiJson.urls.transferOrderManagement.externalTransfer,
                                    data: prepareTransfer()
                                }).then(function success(response) {
                                    if (response.data != null) {
                                        if (response.data != null && response.data > 0) {
                                            $toastService.create("Transfer order with id " + response.data + " created successfully!");
                                            $("#materialize-lean-overlay-1").hide();
                                            $state.go("menu.trOrderMgt");
                                        } else {
                                            $toastService.create("Something went wrong. Please try again!");
                                        }
                                    }
                                }, function error(response) {
                                    $toastService.create("Not able to create External Transfer for Vendor. Please contact support team");
                                    console.log("error:" + response);
                                });
                            }
                        });
                    }
                };
            }
        ]
    );