'use strict';

angular.module('scmApp')
.controller('productDashboardCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state',
    '$stateParams', 'apiJson', 'appUtil', '$http', '$toastService','Popeye','metaDataService','$alertService', '$fileUploadService',
    function ($rootScope, $scope, authService, $location, $state, $stateParams, apiJson, appUtil, $http, $toastService, Popeye, metaDataService,$alertService, $fileUploadService) {
        $scope.init = function() {
            $scope.productsGrid = $scope.gridOptions();
            $scope.productsGrid.data = [];
            $scope.currentUser = appUtil.getCurrentUser().userId;
            $scope.allStatus =  ['PENDING_APPROVER_APPROVAL', 'APPROVER_REJECTED', 'PENDING_FINANCE_APPROVAL', 'FINANCE_REJECTED', 'FINANCE_APPROVED', 'CANCELLED'];
            $scope.selectedStatus = [];
            $scope.selectedStatus = ['PENDING_APPROVER_APPROVAL', 'PENDING_FINANCE_APPROVAL'];
            var currentDate = appUtil.getCurrentBusinessDate();
            $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
            $scope.endDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
            $scope.getUserCreatedProducts();
        }
        $scope.multiSelectSettingStatus = {
            template: '<b> {{option}} </b>'
        };

        $scope.getUserCreatedProducts = function() {
            if($scope.startDate == "" || $scope.startDate == null) {
                $toastService.create("Start Date is mandatory");
                return;
            }
            if($scope.endDate == "" || $scope.endDate == null) {
                $toastService.create("End Date is mandatory");
                return;
            }
            var payLoad = {
                status: $scope.selectedStatus,
                startDate: $scope.startDate,
                endDate: $scope.endDate
            }
            $scope.productsGrid = $scope.gridOptions();
            $scope.productsGrid.data = [];
            $http({
                method: "POST",
                url: apiJson.urls.productManagement.getUserProducts,
                data: payLoad
            }).then(function success(response) {
                if(response.data != null && response.data.length > 0) {
                    $scope.productsGrid.data = response.data;
                } else {
                    $toastService.create("No Products found");
                    return;
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }

        $scope.openProductPage = function(product) {
            var role = 'VIEW_ONLY';
            if(product.productStatus == 'PENDING_APPROVER_APPROVAL') {
                role = 'HOD';
            } else if(product.productStatus == 'PENDING_FINANCE_APPROVAL') {
                role = 'FINANCE'
            }
            $state.go('menu.addProduct', {productDef: angular.copy(product), empType: role});
        }

        $scope.cancelProduct = function(product) {
            $alertService.confirm('Are you sure want to cancel Product?', '', function(result) {
                if(result) {
                    $http({
                        method: "POST",
                        url: apiJson.urls.productManagement.cancelProductCreation,
                        data: product
                    }).then(function success(response) {
                        if(response.data) {
                            $toastService.create("Product Cancelled Successfully");
                            $scope.getUserCreatedProducts();
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            })
        }

        $scope.isShowUpdateOrReject = function(product) {
            if(product.productStatus == 'PENDING_APPROVER_APPROVAL') {
                return ($scope.currentUser == product.hodId);
            }
            return (product.productStatus == 'PENDING_FINANCE_APPROVAL' && $scope.hasAccess("SURNPRFA"));
        };

        $scope.hasAccess = function(key){
            if($rootScope.aclData!=null && $rootScope.aclData.action != null){
                return Object.keys($rootScope.aclData.action).length > 0 && $rootScope.aclData.action[key] != undefined;
            }else {
                return false;
            }
        };

        $scope.showCanelBtn = function(product) {
            if(product.productStatus == 'PENDING_APPROVER_APPROVAL') {
                return(product.createdBy.id == $scope.currentUser);
            }
            return false;
        }

        $scope.isRejected = function(status) {
            return (status == 'APPROVER_REJECTED' || status == 'FINANCE_REJECTED' || status == 'CANCELLED');
        }

        $scope.setStartDate = function(startDate) {
            $scope.startDate = startDate;
        }

        $scope.setEndDate = function(endDate) {
            if (new Date(endDate) >= new Date($scope.startDate)) {
                $scope.endDate = endDate;
                return;
            }
            $scope.endDate = null;
            $toastService.create("End Date Should be greater than Start Date");
        }

        // $scope.getDate = function(date) {
        //     return date | 'date:dd/MM/yyyy';
        // }

        $scope.gridOptions = function () {
            return {
                enableColumnMenus: false,
                enableFiltering: true,
                enableCellEditOnFocus: true,
                enableColumnResizing: true,
                rowHeight: 40,
                columnDefs: [{
                    field: 'productName',
                    displayName: 'Product Name',
                    enableCellEdit: false,
                    width: 200
                }, {
                    field: 'productStatus',
                    displayName: 'Product Status',
                    cellTemplate: 'statusStyle.html',
                    enableCellEdit: false
                }, {
                    field: 'createdBy.name',
                    displayName: 'Created By',
                    enableCellEdit: false,
                    width: 150
                }, {
                    field: 'creationDate',
                    displayName: 'Created At',
                    enableCellEdit: false,
                    width: 150,
                    cellTemplate: '<div class="ui-grid-cell-contents">{{row.entity.creationDate | date:"dd-MM-yyyy"}}</div>'
                }, {
                    field: 'action',
                    displayName: 'Action',
                    cellTemplate: 'viewAndChange.html',
                    enableCellEdit: false,
                }],
                onRegisterApi: function (gridApi) {
                    $scope.gridApi = gridApi;
                    gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                        if (colDef.field == 'leadTime'.toString()) {
                            rowEntity.update = false;
                        }
                        else {
                            rowEntity.update = true;
                        }
                        console.log("JSON.stringify(rowEntity)", JSON.stringify(rowEntity));
                        $scope.$apply();
                    });
                }
            };
        };

    }
]
);
