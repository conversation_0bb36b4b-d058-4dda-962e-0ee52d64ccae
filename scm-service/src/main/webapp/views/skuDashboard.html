<head>
    <link rel="stylesheet" href="css/multiselect.css">
</head>
<div class="row" data-ng-init="init()">

    <h3>SKU Request Dashboard</h3>

    <div class="row">
        <div class="col s2">
            <label>Start date</label>
            <input input-date type="text" name="created" id="inputCreated1" ng-model="startDate"
                    container="" format="yyyy-mm-dd" data-ng-change="setStartDate(startDate)" />
        </div>
        <div class="col s2">
            <label>End date</label>
            <input input-date type="text" name="created" id="inputCreated3" ng-model="endDate"
                    container="" format="yyyy-mm-dd" data-ng-change="setEndDate(endDate)" />
        </div>
        <div class="col s4" style="margin-top: 10px;">
            <label>Select Status </label>
            <div style="text-align: left" ng-dropdown-multiselect=""
                extra-settings="multiSelectSettingStatus" options="allStatus"
                selected-model="selectedStatus">
            </div>
         </div>
         <div class="col s2">
             <button class="waves-effect waves-green btn" style="margin-top: 20px;" data-ng-click="getUserCreatedSkus()">Search</button>
         </div>
    </div>


    <div class="row" id="gridView" data-ng-if="skusGrid.data.length > 0">
        <div class="col s12">
            <div
                id="mappingsGrid"
                ui-grid="skusGrid"
                ui-grid-edit
                ui-grid-row-edit
                ui-grid-cellNav
                ui-grid-resize-columns
                ui-grid-move-columns
                class="grid col s12">
            </div>
        </div>
        <script type="text/ng-template" id="viewAndChange.html">
            <div class="ui-grid-cell-contents">
                <span class="span badge active" ng-click="grid.appScope.openSkuPage(row.entity)" 
                        data-ng-show="grid.appScope.isShowUpdateOrReject(row.entity)"
                        style="cursor: pointer;">
                    Approve / Reject
                </span>
                <span class="span badge" ng-click="grid.appScope.cancelSku(row.entity)" style="background-color: red; cursor: pointer;"
                        data-ng-show="grid.appScope.showCanelBtn(row.entity)">
                    Cancel SKU
                </span>
                
                <span class="span badge" ng-click="grid.appScope.openSkuPage(row.entity)" 
                style="background-color: orange; cursor: pointer;" 
                data-ng-if="row.entity.skuStatus == 'REJECTED' || row.entity.skuStatus == 'CANCELLED'">View</span>

                <span class="span badge" ng-click="grid.appScope.openSkuPage(row.entity)" 
                style="background-color: #4cad90; cursor: pointer;"
                data-ng-if="row.entity.skuStatus == 'ACTIVE'">View Approved</span>
            </div>
        </script>
        <script type="text/ng-template" id="statusStyle.html">
            <div class="ui-grid-cell-contents">
            <span class="span badge" style="background-color: red;"
                    data-ng-hide="row.entity.skuStatus == 'INITIATED' || row.entity.skuStatus == 'ACTIVE'">
                {{row.entity.skuStatus}}
            </span>
            <span class="span badge" style="background-color: #26A69A;"
                    data-ng-show="row.entity.skuStatus == 'INITIATED'">
                {{row.entity.skuStatus}}
            </span>
            <span class="span badge active"
                    data-ng-show="row.entity.skuStatus == 'ACTIVE'">
                {{row.entity.skuStatus}}
            </span>
            </div>
        </script>
    </div>
</div>