<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12" style="border:1px solid #26a69a; ">
        <div class="row">
            <div class="col s12">
                <div class="row">
                    <h3 class="left" >Fixed Asset Recovery</h3>
                </div>
                <div class="row">
                    <div class="col s2">
                        <label>Select Unit</label>
                        <select id="unitSelect" ui-select2="{allowClear:true, placeholder: 'All Units'}" data-ng-model="selectedUnit" data-ng-change="setUnit(selectedUnit)" data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
                    </div>
                    <div class="col s2">
                        <label>Select Status</label>
                        <select id="statusSelect" ui-select2="{allowClear:true, placeholder: 'Select Status'}" data-ng-model="selectedStatus" data-ng-change="setStatus(selectedStatus)" data-ng-options="status for status in statusList "></select>
                    </div>
                    <div class="col s2">
                        <label>Select Asset</label>
                        <select id="assetNameSelect" ui-select2="{allowClear:true, placeholder: 'All Assets'}" data-ng-model="selectedAssetName" data-ng-change="setAssetName(selectedAssetName)" data-ng-options="asset as asset for asset in assetNames"></select>
                    </div>
                    <div class="col s2">
                        <label>Select Asset Id</label>
                        <select id="assetSelect" ui-select2="{allowClear:true, placeholder: 'All Assets'}" data-ng-model="selectedAssetId" data-ng-change="setAsset(selectedAssetId)" data-ng-options="asset as asset for asset in assetIds"></select>
                    </div>
                </div>
                <div class="row" data-ng-if="!selectedAssetId">
                    <div class="col s2">
                        <label>Start date</label>
                        <input input-date type="text" data-ng-model="date.startDate" format="yyyy-mm-dd" placeholder="Enter Start Date" max="{{maxDate}}"/>
                    </div>
                    <div class="col s2">
                        <label>End date</label>
                        <input input-date type="text" data-ng-model="date.endDate" format="yyyy-mm-dd" max="{{maxDate}}"/>
                    </div>
                    <div class="col s1">
                        <input type="button" class="btn btn-small"
                               value="Find" data-ng-click="findAssets()" style="margin-top:40px;" acl-action="MTTOMV" />
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<div data-ng-repeat="$item in recoveryList">
    <div class="searchingCard row white z-depth-3 custom-listing-li" style="width:95%;">
            <div class="container col" style="">
                <div style="margin-left:-11px;margin-right:10px;margin-top:-17px;">
                    <h4 style="background-color: #26a69a;padding: 15px 0px 15px 5%; color: white;font-size:20;"><b>{{$item.assetDefinition.assetName}}</b></h4>
                </div>
                <div class="row s12" >
                    <div class="col s3" style="padding-left:2%;">
                        <h6 style="padding:0.5%;"> <b>Asset Id</b> :         {{$item.assetId}}</h6>
                        <h6 style="padding:0.5%;"> <b>Asset Tag</b> :         {{$item.assetDefinition.tagValue}}</h6>
                        <h6 data-ng-if="selectedStatus == 'RECOVERED'" style="padding:0.5%;"> <b>Amount Recovered</b> :         {{$item.amountRecovered}}</h6>
                        <h6 style="padding:0.5%;"> <b>Reported By</b> :     {{$item.createdBy.name}} ({{$item.createdBy.id}})</h6>
                    </div>
                    <div class="col s3">
                        <h6 style="padding:0.5%;"> <b>Inventory Date</b> :         {{$item.assetDefinition.inventoryDate|date:'yyyy-MM-dd'}}</h6>
                        <h6 style="padding:0.5%;"> <b>Gross Block</b> :         {{$item.assetDefinition.grossBlock}}</h6>
                        <h6 data-ng-if="selectedStatus == 'RECOVERED'" style="padding:0.5%;"> <b>Insurance Recovered Amount</b> :         {{$item.insuranceRecoveryAmount}}</h6>
                        <h6 style="padding:0.5%;"> <b>Approved By</b> :     {{$item.approvedBy.name}} ({{$item.approvedBy.id}})</h6>
                    </div>
                    <div class="col s3">
                        <h6 style="padding:0.5%;"> <b>Expected Recovery Amount</b> : {{$item.amountToRecover}}</h6>
                        <h6 style="padding:0.5%;"> <b>Recovery Status</b> : <span data-ng-style="selectedStatus == 'RECOVERED' ? {'color':'#26a69a'}:{'color':'orange'}">{{$item.recoveryStatus}}</span></h6>
                        <h6 data-ng-if="selectedStatus == 'RECOVERED'" style="padding:0.5%;"> <b>Employee Recovered Amount</b> : {{$item.employeeRecoveryAmount}}</h6>
<!--                        <h6 style="padding:0.5%;"> <b>Depreciated Value</b> :         {{$item.assetDefinition.currentValue}}</h6>-->
                        </div>

                    <div class="col s3">
                        <h6 style="padding:0.5%;"> <b>Unit</b> :     {{$item.unit.name}} ({{$item.unit.id}})</h6>
                        <h6 style="padding:0.5%;"> <b>Event Id</b> : {{$item.eventId}}</h6>
                        <h6 data-ng-if="selectedStatus == 'RECOVERED'" style="padding:0.5%;"> <b>Write Off Value</b> : <span data-ng-if="$item.writeOffAmount">{{$item.writeOffAmount}}</span><span data-ng-if="!$item.writeOffAmount">0</span></h6>
                    </div>
                </div>

                <div class="row" style="margin-top:-2.8%;padding-right:10px;padding-left:10px;" data-ng-if="selectedStatus == 'PENDING' || selectedStatus == 'PARTIAL_RECOVERED'">
                    <div class="right-align" style="margin-right:20px;">
<!--                        <input type="button" class="btn" value="Reject" style="background-color: red;margin-top:10px;"-->
<!--                               data-ng-click="processRequest($item.approvalRequestId,false);"/>-->
                        <input type="button" class="btn" value="Initiate Recovery"
                               a href="#recoveryModal"
                               style="margin-left:10px;margin-top:10px;"
                               data-ng-click="initiateRecovery($item);"
                               data-ng-if="selectedStatus == 'PENDING'"
                               modal/>
                        <input type="button" class="btn" value="Continue Recovery"
                               a href="#recoveryModal"
                               style="margin-left:10px;margin-top:10px;"
                               data-ng-click="initiateRecovery($item);"
                               data-ng-if="selectedStatus == 'PARTIAL_RECOVERED'"
                               modal/>
                    </div>
                </div>
            </div>



    </div>
</div>

<!--Modal-->
<div id="recoveryModal" class="modal modal-large">

    <div class="modal-content" id="modal-body">
        <div>

            <div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-if="showEmployee">

                <div>

                    <div class="row">
                        <div>
                            <div><h3>◈ Employee Recovery</h3></div>
                            <div style="margin-left:30px" data-ng-if="!allDone.employee">
                                <input id="skipEmployeeRecovery" type="checkbox" data-ng-model="skipEmployeeRecovery" data-ng-change="skipRecovery('EMPLOYEE')"/>
                                <label for="skipEmployeeRecovery" data-ng-style="(skipEmployeeRecovery) ?{'color':'red'}:{}">Not Recovering From Employee</label>
                            </div>
                            <div class="center" data-ng-if="allDone.employee">
                                <p>Employee Recovery Already Complete</p>
                            </div>
                            <div class="center" style="margin-left:25%;width:50%;" data-ng-if="!allDone.employee">
                                <table cellpadding="0" cellspacing="0">
                                    <tr data-ng-if="!skipEmployeeRecovery">
                                        <th class="center">Employee Kettle Id</th>
                                        <th class="center">Recovery Amount</th>
                                        <th></th>
                                    </tr>
                                    <tbody ng-repeat="m in employeeRecoveryList" data-ng-if="!skipEmployeeRecovery">
                                    <tr>
                                        <td><input type="number" value="{{m.Name}}" readonly/></td>
                                        <td><input type="number" value="{{m.Amount}}" readonly/></td>
                                        <td><img src="img/trash-2.png"
                                                 width="25" height="25" ng-click="Remove($index, 'EMPLOYEE')"></td>
                                    </tr>
                                    </tbody>
                                    <tr data-ng-if="!skipEmployeeRecovery">
                                        <td><input type="number" data-ng-model="recoverFrom.name"  placeholder="Enter Kettle Id"/></td>
                                        <td><input type="number" data-ng-model="recoverFrom.amount" placeholder="Enter Recovery Amount"/></td>
                                        <td><img src="img/plus-square.png"
                                                 width="25" height="25" ng-click="Add('EMPLOYEE')"></td>
                                    </tr>
                                    <tfoot>
                                    <tr>
                                        <td><label class="center">WRITE OFF VALUE</label></td>
                                        <td><label class="center">{{amountLeft}}</label></td>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="pull-right" style="margin-right:30px;">
                                <button class="btn" data-ng-click="goto('INSURANCE')">Prev</button>
<!--                                <button class="btn modal-action modal-close" data-ng-if="(allDone.insurance && allDone.employee)" data-ng-click="backToHome();">Close</button>-->
                                <button class="btn" data-ng-if="!(allDone.insurance && allDone.employee)" data-ng-click="submitRecovery($item.recoveryId)">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-if="showInsurance">

                <div>

                    <div class="row">
                        <div>
                            <div><h3>◈ Insurance Recovery</h3></div>
                            <div style="margin-left:30px" data-ng-if="!allDone.insurance">
                                <input id="skipInsuranceRecovery" type="checkbox" data-ng-model="skipInsuranceRecovery" data-ng-change="skipRecovery('INSURANCE')"/>
                                <label for="skipInsuranceRecovery" data-ng-style="(skipInsuranceRecovery)?{'color':'red'}:{}">Not Recovering From Insurance</label>
                            </div>
                            <div class="center" style="margin-left:25%;width:50%;" data-ng-if="!skipInsuranceRecovery && !allDone.insurance">
                                <table cellpadding="0" cellspacing="0">
                                    <tr>
                                        <th class="center">Insurance Name</th>
                                        <th class="center">Recovery Amount</th>
                                        <th></th>
                                    </tr>
                                    <tbody ng-repeat="m in insuranceRecoveryList">
                                        <tr>
                                            <td><input type="text" value="{{m.Name}}" readonly/></td>
                                            <td><input type="number" value="{{m.Amount}}" readonly/></td>
                                            <td><img src="img/trash-2.png"
                                                     width="25" height="25" ng-click="Remove($index,'INSURANCE')"></td>
                                        </tr>
                                    </tbody>
                                    <tr data-ng-if="insuranceRecoveryList.length == 0">
                                        <td><input type="text" data-ng-model="insurance.name" placeholder="Enter Insurance Name"/></td>
                                        <td><input type="number" data-ng-model="insurance.amount" placeholder="Enter Amount"/></td>
                                        <td><img src="img/plus-square.png"
                                                 width="25" height="25" ng-click="Add('INSURANCE')"></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="center" data-ng-if="allDone.insurance && !submitCompleted">
                                <p>Insurance Recovery Already Complete</p>
                            </div>

                            <div class="pull-right" style="margin-right:30px;">
                                <button class="btn" data-ng-click="goto('EMPLOYEE')">Next</button>
<!--                                <button class="btn " data-ng-click="submitRecovery(processingRequest.recoveryId,'INSURANCE')">Submit</button>-->
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
        <div class="modal-footer right-align">
            <button class="modal-action modal-close waves-effect waves-green btn-flat" data-ng-click="backToHome();">Close</button>
        </div>
    </div>

</div>