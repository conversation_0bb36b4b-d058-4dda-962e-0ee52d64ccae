<div
	class="row white z-depth-3 custom-listing-li"
	data-ng-init="init()">
	<div
		class="row"
		id="showEventList"
		data-ng-show="showEventList">
		<div class="col s12">
			<div class="col s12">
				<div class="row">
					<div class="col s12">
						<h5>SKU Price Update</h5>
					</div>
				</div>
			</div>
			<div
				class="col s12"
				align="right">
				<span>
					<a
						class="btn btn-small vBtn right"
						ng-click="downloadEventTemplate()"><i
						class="large material-icons">system_update_alt</i> Download Price</a>
				</span>
				<span
					data-ng-if="skuUpdatePriceList.hasEvents==false"
					ng-click="showModal()" acl-action="SPSASD">
					<a
						href="#createNewEvent1"
						class="btn btn-small vBtn right"
						style="margin-right: 10px"
						modal>Create New Event</a>
				</span>
			</div>
		</div>
		<div
			class="col s12"
			data-ng-if="skuItemLength > 0">
			<ul class="collection striped">
				<li class="collection-item list-head">
					<div
						class="row"
						style="font-size: 12px">
						<div class="col s1">Id</div>
						<div class="col s2">Event Type</div>
						<div class="col s1">Created By</div>
						<div class="col s2">Creation Time</div>
						<div class="col s1">Action By</div>
						<div class="col s2">Action Time</div>
						<div class="col s1">Status</div>
					</div>
				</li>
				<li
					class="collection-item"
					data-ng-repeat="vd in skuUpdatePriceList.entries  track by vd.eventId">
					<div
						class="row"
						style="margin-bottom: 0px; font-size: 12px">
						<div class="tooltipped col s1">{{vd.eventId}}</div>
						<div
							class="col s2"
							data-ng-click="viewPriceUpdateDetail(skuUpdatePriceList.entries,vd.eventId)">
							<span
								style="font-size: 12px; text-decoration: underline; cursor: pointer">
								<b>{{vd.eventType}}</b>
							</span>
						</div>
						<div class="col s1">{{vd.createdByName}}</div>
						<div class="col s2">{{vd.creationTime | date:'dd-MM-yyyy
							hh:mm:ss':'+0530'}}</div>
						<div class="col s1">{{vd.eventActionType}}</div>
						<div class="col s2">{{vd.finalizationTime | date:'dd-MM-yyyy
							hh:mm:ss':'+0530'}}</div>
						<div class="col s1">{{vd.eventStatus}}</div>
						<div
							class="col s2"
							align="right">
							<span data-ng-if="vd.eventStatus=='INITIATED'">
								<button
									data-ng-if="vd.eventStatus=='INITIATED'"
									class="btn-floating red"
									style="margin-bottom: 5px">
									<div
										data-ng-click="assignValueCancel(vd.eventId)"
										style="color: white; font-size: 25px">
										<b>X</b>
									</div>
								</button>
							</span>
						</div>
					</div>
				</li>
			</ul>
		</div>
		<div
			class="col2 12"
			data-ng-if="skuItemLength == 0">
			<div
				align="center"
				style="color: black">
				<h6>
					<b>No Event found</b>
				</h6>
			</div>
		</div>
	</div>
	<div
		class="row margin-top-10"
		id="skuPriceUpdate"
		data-ng-show="skuPriceUpdate">
		<div class="col s12">
			<div class="col s12">
				<div class="row">
					<div class="col s2">
						<button
							class="modal-action modal-close waves-effect waves-green btn"
							ng-click="backSkuPrice()">Back</button>
					</div>
					<div
						class="col s6"
						align="left"
						style="font-size: 25px">SKU Price Update</div>
					<!--  <div class="col s4" align="right"><a class="waves-effect waves-green btn"	data-ng-if="viewUpdateList.eventStatus=='INITIATED' && hasSKUAdminPrivilege" ng-click="editNow(viewUpdateList)">Edit</a></div> -->
				</div>
			</div>
			<div class="col s12">
				<div
					class="row sku-pkg-row"
					style="font-size: 11px">
					<br>
					<div class="col s1">
						<label>Event Id</label>
						<br />{{viewUpdateList.eventId}}
					</div>
					<div class="col s3">
						<label>Event Type</label>
						<br />{{viewUpdateList.eventType}}
					</div>
					<div class="col s2">
						<label>Created By</label>
						<br />{{viewUpdateList.createdByName}}
					</div>
					<div class="col s3">
						<label>Create Time</label>
						<br />{{viewUpdateList.creationTime | date:'dd-MM-yyyy
						hh:mm:ss':'+0530'}}
					</div>
					<div class="col s2">
						<label>Action By</label>
						<br />{{viewUpdateList.eventActionType}}
					</div>
					<div class="col s1">
						<label>Status</label>
						<br />{{viewUpdateList.eventStatus}}
					</div>
				</div>
				<ul
					class="collapsible"
					data-collapsible="accordion">
					<li>
						<div class="collapsible-header">
							<b>SKU</b><b style="color: blue">&nbsp;({{skuCategoryList.length}})</b>
						</div>
						<div class="collapsible-body">
							<ul
								class="collection menuItemList z-depth-1-half"
								style="margin-bottom: 50px">
								<li class="collection-item z-depth-1 list-head">
									<div
										class="row"
										style="font-size: 12px">
										<div class="col s1">Id</div>
										<div class="col s2">Name</div>
										<div class="col s2">Type</div>
										<div class="col s1">Unit of Measure</div>
										<div class="col s1">Unit Price</div>
										<div class="col s1">Updated Price</div>
										<div class="col s2">Edited Price</div>
										<div
											class="col s1"
											data-ng-if="viewUpdateList.eventStatus=='APPROVED'">Approved
											Price</div>
										<div
											class="col s1"
											align="right">
											<a
												class="btn-floating black"
												data-ng-if="viewUpdateList.eventStatus=='INITIATED' && hasSKUAdminPrivilege"
												ng-click="editNow(viewUpdateList)"><i
												class="large material-icons">mode_edit</i></a>
										</div>
										<div
											class="col s1"
											align="right"
											data-ng-show="edit==true">
											<a
												class="btn-floating red"
												data-ng-if="viewUpdateList.eventStatus=='INITIATED' && hasSKUAdminPrivilege"
												ng-click="cancelNow(viewUpdateList)">X&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a>
										</div>
									</div>
								</li>
								<li data-ng-repeat="eventEntry in skuCategoryList">
									<div
										class="row"
										style="padding: 10px; font-size: 12px; background: #eee; border-bottom: #ddd 1px solid;">
										<div class="col s1">{{eventEntry.id}}</div>
										<div class="col s2">{{eventEntry.keyName}}</div>
										<div class="col s2">{{eventEntry.keyType}}</div>
										<div class="col s1">{{eventEntry.unitOfMeasure}}</div>
										<div class="col s1">{{eventEntry.unitPrice}}</div>
										<div class="col s1">{{eventEntry.updatedUnitPrice}}</div>
										<div class="col s2">
											<span data-ng-if="hasSKUAdminPrivilege && edit">
												<input
													class="input"
													type="number"
													max-width="25px"
													ng-model="eventEntry.editedUnitPrice"
													data-ng-change="changeSkuPrice()"
													min="0">
											</span>
											<span data-ng-if="hasSKUAdminPrivilege && !edit">{{eventEntry.editedUnitPrice}}</span>
										</div>
										<!-- <div class="col s2" data-ng-if="!hasSKUManagementPrivilege">
                                            {{eventEntry.editedUnitPrice}}
                                        </div> -->
										<div
											class="col s1"
											data-ng-if="viewUpdateList.eventStatus=='APPROVED'">
											{{eventEntry.approvedUnitPrice}}</div>
									</div>
								</li>
							</ul>
						</div>
					</li>
					<li>
						<div class="collapsible-header">
							<b>Product</b><b style="color: blue">&nbsp;({{productCategoryList.length}})</b>
						</div>
						<div class="collapsible-body">
							<ul
								class="collection menuItemList z-depth-1-half"
								style="margin-bottom: 50px">
								<li class="collection-item z-depth-1 list-head">
									<div
										class="row"
										style="font-size: 12px">
										<div class="col s1">Id</div>
										<div class="col s2">Name</div>
										<div class="col s2">Type</div>
										<div class="col s1">Unit of Measure</div>
										<div class="col s1">Unit Price</div>
										<div class="col s1">Updated Price</div>
										<div class="col s1">Drilled Down</div>
										<div class="col s1">Errors</div>
										<div class="col s1">Edited Price</div>
										<div
											class="col s1"
											data-ng-if="viewUpdateList.eventStatus=='APPROVED'">Approved
											Price</div>
									</div>
								</li>
								<li data-ng-repeat="eventEntry in productCategoryList">
									<div
										class="row"
										style="padding: 10px; font-size: 12px; background: #eee; border-bottom: #ddd 1px solid;">
										<div class="col s1">{{eventEntry.id}}</div>
										<div class="col s2">{{eventEntry.keyName}}</div>
										<div class="col s2">{{eventEntry.keyType}}</div>
										<div class="col s1">{{eventEntry.unitOfMeasure}}</div>
										<div class="col s1">{{eventEntry.unitPrice}}</div>
										<div class="col s1">{{eventEntry.updatedUnitPrice}}</div>
										<div class="col s1">
											<a
												href="#drilledModal"
												ng-click="viewDrillDowns(eventEntry.drilldowns)"
												style="cursor: pointer"
												modal>{{eventEntry.drilldowns.length}}</a>
										</div>
										<div class="col s1">
											<a
												href="#productErrors"
												ng-click="viewProductErrors(eventEntry.errors)"
												style="cursor: pointer; color: red"
												modal>{{eventEntry.errors.length}}</a>
										</div>
										<div class="col s1">{{eventEntry.editedUnitPrice}}</div>
										<div
											class="col s1"
											data-ng-if="viewUpdateList.eventStatus=='APPROVED'">
											{{eventEntry.approvedUnitPrice}}</div>
									</div>
								</li>
							</ul>
						</div>
					</li>
					<li>
						<div class="collapsible-header">
							<b>Receipe</b><b style="color: blue">&nbsp;({{receipeCategoryList.length}})</b>
						</div>
						<div class="collapsible-body">
							<ul
								class="collection menuItemList z-depth-1-half"
								style="margin-bottom: 50px">
								<li class="collection-item z-depth-1 list-head">
									<div
										class="row"
										style="font-size: 12px">
										<div class="col s1">Id</div>
										<div class="col s2">Name</div>
										<div class="col s2">Type</div>
										<div class="col s1">Unit of Measure</div>
										<div class="col s1">Unit Price</div>
										<div class="col s1">Updated Price</div>
										<div class="col s1">Drilled Down</div>
										<div class="col s1">Errors</div>
										<div class="col s1">Edited Price</div>
										<div
											class="col s1"
											data-ng-if="viewUpdateList.eventStatus=='APPROVED'">Approved
											Price</div>
									</div>
								</li>
								<li data-ng-repeat="eventEntry in receipeCategoryList">
									<div
										class="row"
										style="padding: 10px; font-size: 12px; background: #eee; border-bottom: #ddd 1px solid;">
										<div class="col s1">{{eventEntry.id}}</div>
										<div class="col s2">{{eventEntry.keyName}}</div>
										<div class="col s2">{{eventEntry.keyType}}</div>
										<div class="col s1">{{eventEntry.unitOfMeasure}}</div>
										<div class="col s1">{{eventEntry.unitPrice}}</div>
										<div class="col s1">{{eventEntry.updatedUnitPrice}}</div>
										<div class="col s1">
											<a
												href="#drilledModal"
												ng-click="viewDrillDowns(eventEntry.drilldowns)"
												style="cursor: pointer"
												modal>{{eventEntry.drilldowns.length}}</a>
										</div>
										<div class="col s1">
											<a
												href="#productErrors"
												ng-click="viewProductErrors(eventEntry.errors)"
												style="cursor: pointer; color: red"
												modal>{{eventEntry.errors.length}}</a>
										</div>
										<div class="col s1">{{eventEntry.editedUnitPrice}}</div>
										<div
											class="col s1"
											data-ng-if="viewUpdateList.eventStatus=='APPROVED'">
											{{eventEntry.approvedUnitPrice}}</div>
									</div>
								</li>
							</ul>
						</div>
					</li>
				</ul>
				<div class="row">
					<div
						class="col s10"
						align="right"
						data-ng-if="!edit">
						<button
							class="modal-action modal-close waves-effect waves-red btn"
							data-ng-if="viewUpdateList.eventStatus=='INITIATED' && hasSKUAdminPrivilege"
							ng-click="assignValueReject(viewUpdateList.eventId)">Reject</button>
					</div>
					<div
						class="col s12"
						data-ng-if="edit"
						align="right">
						<button
							class="modal-action modal-close waves-effect waves-green btn"
							data-ng-if="viewUpdateList.eventStatus=='INITIATED' && hasSKUAdminPrivilege"
							data-ng-click="preview()">PREVIEW</button>
					</div>
					<span
						class="col s2"
						data-ng-if="!edit">
						<button
							class="modal-action modal-close waves-effect waves-green btn"
							data-ng-if="viewUpdateList.eventStatus=='INITIATED' && hasSKUAdminPrivilege"
							ng-click="assignValueApprove(viewUpdateList.eventId)">Approve</button>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div
		class="row"
		id="previewSKUPriceList"
		data-ng-show="previewSKUPriceList">
		<div class="col s12">
			<div
				class="col s12"
				align="right">
				<span>
					<a
						class="btn btn-small vBtn right"
						ng-click="backFromPreview()">Back</a>
				</span>
			</div>
			<div class="col s12">
				<ul
					class="collection menuItemList z-depth-1-half"
					style="margin-bottom: 50px">
					<li class="collection-item z-depth-1 list-head">
						<div
							class="row"
							class="row"
							style="font-size: 12px">
							<div class="col s1">Id</div>
							<div class="col s4">Name</div>
							<div class="col s2">Unit of Measure</div>
							<div class="col s2">Unit Price</div>
							<div class="col s2">Updated Price</div>
							<div
								data-ng-if="edit"
								class="col s1">Edit Price</div>
						</div>
					</li>
					<li data-ng-repeat="eventItem in previewSkuUpdateList">
						<div
							class="row"
							data-ng-if="viewUpdateList.entries.editedValue!=''"
							style="padding: 10px; font-size: 12px; background: #eee; border-bottom: #ddd 1px solid;">
							<div class="col s1">{{eventItem.id}}</div>
							<div class="col s4">{{eventItem.keyName}}</div>
							<div class="col s2">{{eventItem.unitOfMeasure}}</div>
							<div class="col s2">{{eventItem.unitPrice}}</div>
							<div class="col s2">{{eventItem.updatedUnitPrice}}</div>
							<div
								data-ng-if="edit"
								class="col s1">{{eventItem.editedUnitPrice}}</div>
						</div>
					</li>
				</ul>
			</div>
			<div
				class="col s12"
				align="right">
				<span>
					<a
						class="btn btn-small vBtn right"
						ng-click="updateSKUPrice()">UPDATE</a>
				</span>
			</div>
		</div>
	</div>
</div>
<div
	id="createNewEvent1"
	class="modal">
	<div class="modal-content">
		<div class="row margin0">
			<div class="col s10">
				<h5>File Upload</h5>
			</div>
			<div class="col s2">
				<a
					class="modal-action modal-close waves-effect waves-green btn right">
					<span aria-hidden="true">cancel</span>
				</a>
			</div>
		</div>
		<div class="row">
			<div class="col s12">
				<div class="file-field input-field">
					<div class="btn">
						<span>File</span>
						<input
							class="pull-right"
							type="file"
							data-ng-model="fileToUpload"
							file-model="fileToUpload">
					</div>
					<div class="file-path-wrapper">
						<input
							class="file-path validate"
							type="text"
							data-ng-model="fileUploadHere"
							placeholder="upload">
					</div>
				</div>
				<div class="right-align">
					<br>
					<button
						class="modal-action modal-close waves-effect waves-green btn"
						ng-click="uploadFile()">UPLOAD</button>
				</div>
			</div>
		</div>
	</div>
</div>
<div
	id="productErrors"
	class="modal">
	<div class="modal-content">
		<div class="collapsible-header">
			Product Error List <b style="color: blue">({{productErrorsList.length}})</b>
			<button class="btn right modal-action modal-close">Close</button>
		</div>
		<div
			class="row"
			style="padding: 10px; margin-top: 10px; font-size: 12px; background: #26a69a; color: #ffffff; border-bottom: #ddd 1px solid;">
			<div class="col s3">
				<b>Errors</b>
			</div>
		</div>
		<div data-ng-repeat="prodErrors in productErrorsList">
			<div
				class="row"
				style="padding: 10px; font-size: 12px; background-color: red; border-bottom: #ddd 1px solid;">
				<div class="col s3">{{prodErrors}}&nbsp;</div>
			</div>
		</div>
		<div
			class="col2 12"
			data-ng-if="productErrorsList.length == 0">
			<div
				align="center"
				style="color: black">
				<h6>
					<b>No Errors found</b>
				</h6>
			</div>
		</div>
	</div>
</div>
<div
	id="productDrilledErrors"
	class="modal">
	<div class="modal-content">
		<div class="collapsible-header">
			Product Error List <b style="color: red">({{productDrilledErrorsList.length}})</b>
		</div>
		<div
			class="row"
			style="padding: 10px; margin-top: 10px; font-size: 12px; background: #26a69a; color: #ffffff; border-bottom: #ddd 1px solid;">
			<div class="col s3">
				<b>Errors</b>
			</div>
		</div>
		<div data-ng-repeat="prodDrilledErrors in productDrilledErrorsList">
			<div
				class="row"
				style="padding: 10px; font-size: 12px; background-color: red; border-bottom: #ddd 1px solid;">
				<div class="col s3">{{prodDrilledErrors}}&nbsp;</div>
			</div>
		</div>
		<div
			class="col2 12"
			data-ng-if="productDrilledErrorsList.length == 0">
			<div
				align="center"
				style="color: black">
				<h6>
					<b>No Errors found</b>
				</h6>
			</div>
		</div>
	</div>
</div>
<div
	id="drilledModal"
	class="modal"
	style="width: 1000px">
	<div
		class="modal-content"
		style="width: 970px">
		<div align="right">
			<button
				data-target="modal"
				align="right"
				class="modal-close">x</button>
		</div>
		<ul
			class="collapsible"
			data-collapsible="accordion">
			<li>
				<div
					class="collapsible-header"
					style="width: 950px">
					Drilled Down List <b style="color: blue">({{drillDownListView.length}})</b>
				</div>
				<div
					style="width: 950px"
					class="panel-collapse collapse in">
					<div class="col s12">
						<div
							class="row"
							style="padding: 5px; margin-top: 10px; font-size: 12px; background: #26a69a; color: #ffffff; border-bottom: #ddd 1px solid;">
							<div class="col s2">
								<b>Category</b>
							</div>
							<div class="col s1">
								<b>Drill Type</b>
							</div>
							<div class="col s1">
								<b>Cost</b>
							</div>
							<div class="col s2">
								<b>Key Name</b>
							</div>
							<div class="col s2">
								<b>Key Type</b>
							</div>
							<div class="col s1">
								<b>Qty</b>
							</div>
							<div class="col s1">
								<b>Measure</b>
							</div>
							<div class="col s1">
								<b>Unit Price </b>
							</div>
							<div class="col s1">
								<b>Error </b>
							</div>
						</div>
						<div data-ng-repeat="drillList in drillDownListView">
							<div
								class="row"
								style="padding: 5px; font-size: 12px; margin-bottom: 8px; background: #eee; border-bottom: #ddd 1px solid;">
								<div class="col s2">{{drillList.drilldownCategory}}&nbsp;</div>
								<div class="col s1">{{drillList.drilldownType}}&nbsp;</div>
								<div class="col s1">{{drillList.cost}}&nbsp;</div>
								<div class="col s2">{{drillList.keyName}}&nbsp;</div>
								<div class="col s2">{{drillList.keyType}}&nbsp;</div>
								<div class="col s1">{{drillList.quantity}}&nbsp;</div>
								<div class="col s1">{{drillList.unitOfMeasure}}&nbsp;</div>
								<div class="col s1">{{drillList.unitPrice}}&nbsp;</div>
								<div class="col s1">
									<a
										href="#productDrilledErrors"
										ng-click="showDrillError(drillList.errors)"
										style="cursor: pointer; color: red"
										modal>{{drillList.errors.length}}&nbsp;</a> <a
										href="#productErrors"
										ng-click="viewProductErrors(drillList.errors)"
										style="cursor: pointer; color: red"
										modal>{{eventEntry.errors.length}}</a>
								</div>
							</div>
						</div>
					</div>
				</div>
	</div>
	</li>
	</ul>
</div>
</div>