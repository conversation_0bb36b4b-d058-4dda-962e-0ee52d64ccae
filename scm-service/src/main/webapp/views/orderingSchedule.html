<head>
    <link rel="stylesheet" href="css/multiselect.css">
</head>
<style>
    .custom-modal {
        width:90% !important;
        height: 50% !important;
    }
</style>
<div class="row white z-depth-3" data-ng-init="init()">
    <div class="col s12 margin-top-20" data-ng-if="isKitchenOrWH">
        <div class="col s6" data-ng-if="fountain9UnitsList.length > 0">
            <label>Select unit</label>
            <select ui-select2="{placeholder: 'Select Unit'}" data-ng-model="unitSelected" data-ng-change="setSelectedUnit(unitSelected)" id="unitSelected"
                    data-ng-options="unit as unit.name for unit in fountain9UnitsList | orderBy:'id'">
            </select>
        </div>
        <div class="col s6 center margin-top-20" data-ng-if="fountain9UnitsList.length > 0">
            <button class="btn btn-medium" data-ng-click="openCloneModal()">Clone schedules</button>
        </div>
        <h3 data-ng-if="fountain9UnitsList.length == 0" class="red-text">No units Available to see/add Ordering Schedule..!</h3>
    </div>
    <div class="row" id="addSchedule">
        <div class="col s12 center-align margin-top-20" data-ng-if="unitOrderingSchedule.length == 0 && !notFountain9Unit">
            <button class="btn btn-large" data-ng-click="addOrderingSchedule()">Add Ordering Schedule</button>
        </div>
        <div class="col s12 center-align margin-top-20" data-ng-if="notFountain9Unit">
            <h3 class="red-text">Unit is not mapped with fountain 9.Please do a New Regular Order manually and come back to create Ordering Schedule ..!</h3>
        </div>
    </div>
    <div class="row" id="viewOrderingSchedules">
        <div class="col s12" data-ng-if="unitOrderingSchedule.length > 0 && editMode == false">
            <button class="btn btn-medium margin-top-20 pull-right" data-ng-click="editSchedule()">Edit Schedule</button>
        </div>
        <div class="col s12" data-ng-if="unitOrderingSchedule.length > 0 && editMode == true">
            <button class="btn btn-medium margin-top-20 pull-right" data-ng-click="closeEditSchedule()">Back</button></div>
        <div class="col s12" data-ng-if="unitOrderingSchedule.length == 0"><button class="btn btn-medium margin-top-20 pull-right" data-ng-click="closeAddSchedule()">Back</button></div>
        <div class="col s12 margin-top-20">
            <div class="col s6">
                <h4 data-ng-if="isKitchenOrWH">Unit Id : {{unitSelected.id}}</h4>
                <h4 data-ng-if="!isKitchenOrWH">Unit Id : {{currentUnit.id}}</h4>
            </div>
            <div class="col s6">
                <h4 data-ng-if="isKitchenOrWH">Unit Name : {{unitSelected.name}}</h4>
                <h4 data-ng-if="!isKitchenOrWH">Unit Name : {{currentUnit.name}}</h4>
            </div>
        </div>
        <div class="col s12">
            <table class="bordered">
                <thead class="list-head">
                    <tr>
                        <th class="center-align">Brand</th>
                        <th class="center-align">Is Functional</th>
                        <th class="center-align">Auto/Manual</th>
                        <th class="center-align">Order Schedule</th>
                    </tr>
                </thead>
                <tbody>
                    <tr data-ng-repeat="item in displayOrderingSchedules">
                        <td class="center-align">{{item.brandName}}</td>
                        <td class="center-align">
                            <input type="checkbox" id="functionalCheck-{{item.brandId}}" data-ng-model="item.functional" data-ng-disabled="!editMode">
                            <label for="functionalCheck-{{item.brandId}}"></label>
                        </td>
                        <td class="center-align">
                            <select data-ng-disabled = true>
                                <option value="Manual" selected>Manual</option>
                                <option value="Automatic">Automatic</option>
                            </select>
                        </td>
                        <td>
                            <div class="row">
                                <table class="bordered striped">
                                    <thead>
                                        <th>Day Close Business Day</th>
                                        <th data-ng-repeat="schedule in item.unitOrderSchedules">{{schedule.orderingDayType}}</th>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>Is Functional</td>
                                        <td data-ng-repeat="schedule in item.unitOrderSchedules">
                                            <input type="checkbox" id="innerFunctionalCheck-{{schedule.orderingDayType}}-{{item.brandName}}"
                                                   data-ng-change="setFunctionalFlag(item,schedule, displayOrderingSchedules)"
                                                   data-ng-model="schedule.functional"
                                                   data-ng-disabled="!editMode || !item.functional || item.brandId == 4">
                                            <label for="innerFunctionalCheck-{{schedule.orderingDayType}}-{{item.brandName}}"></label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Is Ordering Day</td>
                                        <td data-ng-repeat="schedule in item.unitOrderSchedules">
                                            <input type="checkbox" id="innerOrderingCheck-{{schedule.orderingDayType}}-{{item.brandName}}" data-ng-model="schedule.orderingDay"
                                                   data-ng-change="checkForFulfilmentDate(item,schedule)"
                                                   data-ng-disabled="!editMode || !schedule.functional || !item.functional">
                                            <label for="innerOrderingCheck-{{schedule.orderingDayType}}-{{item.brandName}}"></label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Ordering Days</td>
                                        <td data-ng-repeat="schedule in item.unitOrderSchedules">
                                            <select data-ng-model="schedule.orderingDays" data-ng-change="setStockDays(item,schedule,'{{schedule.orderingDays}}')"
                                                    data-ng-disabled = "!editMode || !schedule.orderingDay || !schedule.functional || !item.functional">
                                                <option value="1">1</option>
                                                <option value="2">2</option>
                                                <option value="3" data-ng-if="item.brandId == 1 || item.brandId == 4">3</option>
                                                <option value="4" data-ng-if="item.brandId == 4">4</option>
                                                <option value="5" data-ng-if="item.brandId == 4">5</option>
                                                <option value="6" data-ng-if="item.brandId == 4">6</option>
                                                <option value="7" data-ng-if="item.brandId == 4">7</option>
                                            </select>
                                            <br>
                                            <span class="red" style="display: block;margin-top: 20px;text-align: center" data-ng-if="schedule.inStock != undefined && !schedule.inStock">NO STOCK</span>
                                            <span class="green" style="display: block;margin-top: 20px;text-align: center" data-ng-if="schedule.inStock != undefined && schedule.inStock">IN STOCK</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <br>
        <div class="col s12" data-ng-if="editMode">
            <button class="btn btn-medium pull-right" data-ng-click="submitSchedule()">{{unitOrderingSchedule.length == 0 ? "Submit Schedule" : "Update Schedule"}}</button>
        </div>
    </div>
</div>

<script type="text/ng-template" id="cloneSchedules.html" class="custom-modal">
    <div class="modal-content" data-ng-init="initCloneCtrl()">
        <div class="row">
            <h5>Clone Schedules</b></h5>
        </div>
        <div class="row" style="width:98%;">
            <div class="col s6" id="cloneScheduleId">
                <label>Units With Schedules <span> ({{unitsWithSchedules.length}} )</span></label>
                <select data-ng-model="cloneFromUnit" data-ng-change="setSelectedCloneFromUnit(cloneFromUnit)" id="cloneFromUnit"
                        data-ng-options="unit as unit.name for unit in unitsWithSchedules | orderBy:'id'">
                </select>
            </div>
            <div class="col s6" data-ng-if="cloneFromUnit != null">
                <label>Units With No schedules <span>( {{unitsWithNoSchedules.length}} ) </span></label>
                <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="unitsWithNoSchedules"
                     selected-model="selectedUnits"
                     extra-settings="multiSelectSetting">
                </div>
            </div>
        </div>
        <div class="row" data-ng-if="cloneFromUnit != null && selectedUnits.length > 0">
            You are cloning the schedule of <b>{{cloneFromUnit.name}}</b> to {{getSelectedUnitNames()}}
        </div>
        <div class="modal-footer">
            <button class="btn btn-medium red left" data-ng-click="close(false)">Cancel</button>
            <button class="waves-effect waves-green btn right" data-ng-click="submitCloneSchedules()">Submit</button>
        </div>
    </div>
</script>