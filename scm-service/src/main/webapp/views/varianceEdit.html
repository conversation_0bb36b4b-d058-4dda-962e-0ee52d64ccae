<!--
  ~ Created By Shanmukh
  -->
<div class="row white z-depth-3 " data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 teal lighten-2 center-align">
            <div class="col s12">
                <h4 class="left">Variance Edit</h4>
            </div>
        </div>
    </div>
    <br>
    <br>
    <div style="padding: 10px; margin: 20px; border-radius: 4px; background: #a0e1e4;"
         data-ng-if="varianceEdit.varianceEditItems.length == 0">
        No Products Found To edit Variance.
    </div>
    <div class="row" data-ng-if="varianceEdit.varianceEditItems.length > 0">
        <div class="row s12">
            <div class="col s2" data-ng-if="varianceEdit.canEditVariance">
                <label>Can Edit Variance Till</label>
                {{varianceEdit.canEditVarianceTill | date:'dd-MM-yyyy hh:mm:ss'}}
            </div>
            <div class="col s3" data-ng-if="!varianceEdit.canEditVariance">
                <h6 style="background-color: red">You Cannot Edit Variance Now..!</h6>
            </div>
            <div class="col s2" data-ng-if="varianceEdit.varianceUpdatedByName != null">
                <label>Variance {{varianceEdit.varianceEditStatus == 'EDITED' ? 'Edited' : 'Updated'}} By</label>
                {{varianceEdit.varianceUpdatedByName}}
            </div>
            <div class="col s2" data-ng-if="varianceEdit.canEditVariance && varianceEdit.varianceEditStatus == null && !editVariance">
                <input type="button" class="btn btn-medium" value="Acknowledge Variance"
                       data-ng-click="acknowledgeVariance()"/>
            </div>
            <div acl-action="VEDS" class="col s2" data-ng-if="varianceEdit.canEditVariance && varianceEdit.varianceEditStatus == null">
                <input type="button" class="btn btn-medium" value="Edit Variance" data-ng-if="!editVariance"
                       data-ng-click="editVarianceCall()"/>
                <span style="background-color: yellow" data-ng-if="editVariance">You Can Edit Variance Now</span>
            </div>
        </div>
        <div class="row">
            <input type="text" id="searchProduct" data-ng-model="searchProduct" style="color: red"
                   placeholder="Enter Product Name to Search" data-ng-change="setSearchProduct(searchProduct)">
        </div>
        <table class="bordered striped">
            <thead class="collection-item list-head">
            <tr>
                <th class="center-align">PRODUCT</th>
                <th class="center-align" tooltipped data-tooltip="Expected Closing Stock for Day Close">Expected Closing</th>
                <th class="center-align" tooltipped data-tooltip="Closing Stock After Day Close">Original Closing</th>
                <th class="center-align" tooltipped data-tooltip="Variance After Day Close">Original Variance</th>
                <th class="center-align" tooltipped data-tooltip="Enter Final CLosing">Final Closing</th>
                <th class="center-align" tooltipped data-tooltip="Final Variance">Final Variance</th>
            </tr>
            </thead>
            <tbody>
            <tr data-ng-repeat="item in varianceEdit.varianceEditItems | orderBy: ['productName'] | filter : (searchProduct == null ? undefined : searchProduct) track by $index">
                <td class="center-align">
                    <div style="max-width: 240px;">
                        <span style="font-weight:700;"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></span>
                        &nbsp;
                        <span>[{{item.unitOfMeasure}}]</span>
                    </div>
                </td>
                <td class="center-align">{{(item.expectedClosing != null ? item.expectedClosing : 0) | number: 2}}</td>
                <td class="center-align">{{(item.originalClosing != null ? item.originalClosing : 0) | number: 2}}</td>
                <td class="center-align">{{(item.originalVariance != null ? item.originalVariance : 0) | number: 2}}
                </td>
                <td class="center-align" data-ng-if="varianceEdit.canEditVariance">
                    <input type="number" data-ng-model="item.finalClosing" data-ng-disabled="!editVariance"
                           data-ng-change="setFinalClosing(item, item.finalClosing)">
                </td>
                <td class="center-align" data-ng-if="!varianceEdit.canEditVariance">
                    {{(item.actualClosing != null ? item.actualClosing : 0) | number: 2}}
                </td>
                <td class="center-align" data-ng-if="varianceEdit.canEditVariance">
                    <input type="number" data-ng-model="item.finalVariance" data-ng-disabled="true">
                </td>
                <td class="center-align" data-ng-if="!varianceEdit.canEditVariance">
                    {{(item.variance != null ? item.variance : 0) | number: 2}}
                </td>
            </tr>
            </tbody>
        </table>
        <div class="row">
            <div class="col s12">
                <div data-ng-if="varianceEdit.canEditVariance && showContactZmMessage" style="padding: 10px; margin: 10px; border-radius: 4px; background: red; color: white">Please Contact Your Zonal Manager to Edit Variance ..!</div>
            </div>
            <div class="col s12">
<!--                <input acl-action="VEDS" type="button" class="btn btn-medium right margin-top-20" data-ng-click="previewVarianceEdit()"-->
<!--                       value="Preview variance" data-ng-if="varianceEdit.canEditVariance">-->
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="varianceEditModal.html" class="modal-large">
    <div class="modal-content">
        <div class="row">
            <h5><u>Variance Edit</u></h5>
        </div>
        <br>
        <div class="row" style="width:98%;">
            <div class="col s12">
                <div class="col s12 grid"
                     id="gridOptions"
                     ui-grid="gridOptions"
                     ui-grid-save-state
                     ui-grid-edit
                     ui-grid-exporter
                     ui-grid-resize-columns
                     ui-grid-move-columns
                     ui-grid-auto-resize>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-medium red left" data-ng-click="close()">Close</button>
            <button class="btn btn-medium right" data-ng-click="submit()">Submit</button>
        </div>
    </div>
</script>