<style>
    $yellow:#f5ba1a;
$black:#000000;
$grey:#cccccc;

<!--body {-->
<!--  margin: 0;-->
<!--  font-family: "Poppins", sans-serif;-->
<!--  display: flex;-->
<!--  justify-content: center;-->
<!--  align-items: center;-->
<!--  flex-direction: column;-->
<!--  background: #282a36;-->
<!--  height: 100vh;-->
<!--  color: #fff;-->
<!--}-->

.otp-field {
  display: flex;
}

.otp-field input {
  width: 42px;
  font-size: 32px;
  padding: 10px;
  text-align: center;
  border-radius: 5px;
  margin: 2px;
  border: 2px solid #55525c;
  background: #e2dfe5;
  font-weight: bold;
  color: #fff;
  outline: none;
  transition: all 0.1s;
}

.otp-field input:focus {
  border: 2px solid #a527ff;
  box-shadow: 0 0 2px 2px #a527ff6a;
}

.disabled {
  opacity: 0.5;
}

.space {
  margin-right: 1rem !important;
}

body {
	font-family: Verdana, Geneva, sans-serif;
	font-size: 14px;
	background: #f2f2f2;
}
button {
  width: 150px;
  height: 40px;
  margin: 25px auto 0px auto;
  font-family: arimo;
  font-size: 1.1rem;
  border: none;
  border-radius: 5px;
  letter-spacing: 2px;
  cursor: pointer;
  background: #b7efe9; /* fallback for old browsers */
  background: -webkit-linear-gradient(
    to right,
    #9bc5c3,
    #616161
  );
.clearfix {
	&:after {
		content: "";
		display: block;
		clear: both;
		visibility: hidden;
		height: 0;
	}
}
.form_wrapper {
	background: #fff;
	width: 80%;
	max-width: 100%;
	box-sizing: border-box;
	padding: 25px;
	margin: 8% auto 0;
	position: relative;
	z-index: 1;
	border-top: 5px solid $yellow;
	-webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
	-moz-box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
	box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
    -webkit-transform-origin: 50% 0%;
    transform-origin: 50% 0%;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
    -webkit-transition: none;
    transition: none;
    -webkit-animation: expand 0.8s 0.6s ease-out forwards;
    animation: expand 0.8s 0.6s ease-out forwards;
    opacity: 0;
	h2 {
		font-size: 1.5em;
		line-height: 1.5em;
		margin: 0;
	}
	.title_container {
		text-align: center;
		padding-bottom: 15px;
	}
	h3 {
		font-size: 1.1em;
		font-weight: normal;
		line-height: 1.5em;
		margin: 0;
	}
    label {
        font-size: 12px;
    }
	.row {
		margin: 10px -15px;
		>div {
			padding: 0 15px;
			box-sizing: border-box;
		}
	}
	.col_half {
		width: 50%;
		float: left;
	}
	.input_field {
		position: relative;
		margin-bottom: 20px;
        -webkit-animation: bounce 0.6s ease-out;
  	     animation: bounce 0.6s ease-out;
		>span {
			position: absolute;
			left: 0;
			top: 0;
			color: #333;
			height: 100%;
			border-right: 1px solid $grey;
			text-align: center;
			width: 30px;
			>i {
				padding-top: 10px;
			}
		}
	}
	.textarea_field {
		>span {
			>i {
				padding-top: 10px;
			}
		}
	}
	input {
    &[type="text"], &[type="email"], &[type="password"] {
      width: 100%;
      padding: 8px 10px 9px 35px;
      height: 35px;
      border: 1px solid $grey;
      box-sizing: border-box;
      outline: none;
      -webkit-transition: all 0.30s ease-in-out;
      -moz-transition: all 0.30s ease-in-out;
      -ms-transition: all 0.30s ease-in-out;
      transition: all 0.30s ease-in-out;
    }
    &[type="text"]:hover, &[type="email"]:hover, &[type="password"]:hover {
      background: #fafafa;
    }
    &[type="text"]:focus, &[type="email"]:focus, &[type="password"]:focus {
      -webkit-box-shadow: 0 0 2px 1px rgba(255, 169, 0, 0.5);
      -moz-box-shadow: 0 0 2px 1px rgba(255, 169, 0, 0.5);
      box-shadow: 0 0 2px 1px rgba(255, 169, 0, 0.5);
      border: 1px solid $yellow;
      background: #fafafa;
    }
    &[type="submit"] {
		background: $yellow;
		height: 35px;
		line-height: 35px;
		width: 100%;
		border: none;
		outline: none;
		cursor: pointer;
		color: #fff;
		font-size: 1.1em;
		margin-bottom: 10px;
		-webkit-transition: all 0.30s ease-in-out;
		-moz-transition: all 0.30s ease-in-out;
		-ms-transition: all 0.30s ease-in-out;
		transition: all 0.30s ease-in-out;
		&:hover {
			background: darken($yellow,7%);
		}
		&:focus {
			background: darken($yellow,7%);
		}
	}
    &[type="checkbox"], &[type="radio"] {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px;
    }
  }
}
.form_container {
	.row {
		.col_half.last {
			border-left: 1px solid $grey;
		}
	}
}
.checkbox_option{
    label{
        margin-right: 1em;
        position: relative;
        &:before {
          content: "";
          display: inline-block;
          width: 0.5em;
          height: 0.5em;
          margin-right: 0.5em;
          vertical-align: -2px;
          border: 2px solid $grey;
          padding: 0.12em;
          background-color: transparent;
          background-clip: content-box;
          transition: all 0.2s ease;
        }
        &:after {
          border-right: 2px solid $black;
          border-top: 2px solid $black;
          content: "";
          height: 20px;
          left: 2px;
          position: absolute;
          top: 7px;
          transform: scaleX(-1) rotate(135deg);
          transform-origin: left top;
          width: 7px;
          display: none;
        }
    }
    input {
    &:hover + label:before {
      border-color: $black;
    }
    &:checked + label {
      &:before {
        border-color: $black;
      }
      &:after {
        -moz-animation: check 0.8s ease 0s running;
        -webkit-animation: check 0.8s ease 0s running;
        animation: check 0.8s ease 0s running;
        display: block;
        width: 7px;
        height: 20px;
        border-color: $black;
      }
    }
  }
}
.radio_option {
  label {
      margin-right: 1em;
    &:before {
      content: "";
      display: inline-block;
      width: 0.5em;
      height: 0.5em;
      margin-right: 0.5em;
      border-radius: 100%;
      vertical-align: -3px;
      border: 2px solid $grey;
      padding: 0.15em;
      background-color: transparent;
      background-clip: content-box;
      transition: all 0.2s ease;
    }
  }
  input {
    &:hover + label:before {
      border-color: $black;
    }
    &:checked + label:before {
      background-color: $black;
      border-color: $black;
    }
  }
}
.select_option {
  position: relative;
  width: 100%;
  select {
    display: inline-block;
    width: 100%;
    height: 35px;
    padding: 0px 15px;
    cursor: pointer;
    color: #7b7b7b;
    border: 1px solid $grey;
    border-radius: 0;
    background: #fff;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    transition: all 0.2s ease;
    &::-ms-expand {
      display: none;
    }
    &:hover, &:focus {
      color: $black;
      background: #fafafa;
      border-color: $black;
      outline: none;
    }
  }
}
.select_arrow {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  width: 0;
  height: 0;
  pointer-events: none;
  border-width: 8px 5px 0 5px;
  border-style: solid;
  border-color: #7b7b7b transparent transparent transparent;
}

.select_option select {
  &:hover + .select_arrow, &:focus + .select_arrow {
    border-top-color: $black;
  }
}
.credit {
	position: relative;
	z-index: 1;
	text-align: center;
	padding: 15px;
	color: $yellow;
	a {
		color: darken($yellow,7%);
	}
}
@-webkit-keyframes check {
  0% { height: 0; width: 0; }
  25% { height: 0; width: 7px; }
  50% { height: 20px; width: 7px; }
}

@keyframes check {
  0% { height: 0; width: 0; }
  25% { height: 0; width: 7px; }
  50% { height: 20px; width: 7px; }
}

@-webkit-keyframes expand {
	0% { -webkit-transform: scale3d(1,0,1); opacity:0; }
	25% { -webkit-transform: scale3d(1,1.2,1); }
	50% { -webkit-transform: scale3d(1,0.85,1); }
	75% { -webkit-transform: scale3d(1,1.05,1); }
	100% { -webkit-transform: scale3d(1,1,1);  opacity:1; }
}

@keyframes expand {
	0% { -webkit-transform: scale3d(1,0,1); transform: scale3d(1,0,1);  opacity:0; }
	25% { -webkit-transform: scale3d(1,1.2,1); transform: scale3d(1,1.2,1); }
	50% { -webkit-transform: scale3d(1,0.85,1); transform: scale3d(1,0.85,1); }
	75% { -webkit-transform: scale3d(1,1.05,1); transform: scale3d(1,1.05,1); }
	100% { -webkit-transform: scale3d(1,1,1); transform: scale3d(1,1,1);  opacity:1; }
}

body {
  margin: 0;
  font-family: "Poppins", sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #282a36;
  height: 100vh;
  color: #fff;
}

.otp-field {
  display: flex;
}

.otp-field input {
  width: 24px;
  font-size: 32px;
  padding: 10px;
  text-align: center;
  border-radius: 5px;
  margin: 2px;
  border: 2px solid #55525c;
  background: #21232d;
  font-weight: bold;
  color: #fff;
  outline: none;
  transition: all 0.1s;
}

.otp-field input:focus {
  border: 2px solid #a527ff;
  box-shadow: 0 0 2px 2px #a527ff6a;
}

.disabled {
  opacity: 0.5;
}

.space {
  margin-right: 1rem !important;
}

@-webkit-keyframes bounce {
	0% { -webkit-transform: translate3d(0,-25px,0); opacity:0; }
	25% { -webkit-transform: translate3d(0,10px,0); }
	50% { -webkit-transform: translate3d(0,-6px,0); }
	75% { -webkit-transform: translate3d(0,2px,0); }
	100% { -webkit-transform: translate3d(0,0,0); opacity: 1; }
}

@keyframes bounce {
	0% { -webkit-transform: translate3d(0,-25px,0); transform: translate3d(0,-25px,0); opacity:0; }
	25% { -webkit-transform: translate3d(0,10px,0); transform: translate3d(0,10px,0); }
	50% { -webkit-transform: translate3d(0,-6px,0); transform: translate3d(0,-6px,0); }
	75% { -webkit-transform: translate3d(0,2px,0); transform: translate3d(0,2px,0); }
	100% { -webkit-transform: translate3d(0,0,0); transform: translate3d(0,0,0); opacity: 1; }
}
@media (max-width: 1200px) {
	.form_wrapper {
		.col_half {
			width: 100%;
			float: none;
		}
	}
	.bottom_row {
		.col_half {
			width: 50%;
			float: left;
		}
	}
	.form_container {
		.row {
			.col_half.last {
				border-left: none;
			}
		}
	}
	.remember_me {
		padding-bottom: 20px;
	}
	.ng-pristine {
		height:50px;
	}
	.nowrap {
      white-space:pre-wrap;
      word-wrap:break-word;
    }
    img.signature {
      width:400px;
      height: 200px;
      border: 1px red solid;
      margin-top: 5px;
    }

    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300&display=swap');
* {
    padding: 0;
    margin: 0;
    font-family: 'Poppins', sans-serif;
}
body {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100vw;
    background: #ececec;
    overflow: hidden;
}
.flex-row {
    display: flex;
}
.wrapper {
    border: 1px solid #4b00ff;
    border-right: 0;
}
canvas#signature-pad {
    background: #fff;
    width: 100px;
    height: 70px;
    cursor: crosshair;
}
button#clear {
    height: 100%;
    background: #4b00ff;
    border: 1px solid transparent;
    color: #fff;
    font-weight: 600;
    cursor: pointer;
}
button#clear span {
    transform: rotate(90deg);
    display: block;
}
</style>

<div class="row" data-ng-init="init()" data-ng-class="{addMargin:!editMode}">
</div>

<div class="form_wrapper" data-ng-if="!thankYou">
    <div class="form_container">
        <div class="title_container">
            <h4>{{vendor.entityName}} Work Order Contract</h4>
        </div>
        <div class="row clearfix">
            <div class="">
                <form>
                    <div class="input_field">
                        <object
                                style="zoom:1"
                                id="docPdf"
                                type='application/pdf'
                                width='100%'
                                height='600px'/>
                    </div>
                    <div class="input_field"  data-ng-if="contractInfo == null">
                        <h5 style="margin:0px; text-align:center">Invalid Request Found</h5>
                    </div>

                    <div class="input_field"  data-ng-if="contractInfo !== null">
                        <p style="margin:0px; text-align:center; font-size:20px">Greetings from Chaayos! We appreciate your interest in partnering with us as a vendor.
                            As we move forward in the process, we kindly request you to fill out the form below to formally accept or reject the proposed contract.</p>
                    </div>
                    <div   data-ng-if="contractInfo != null"> <span><i aria-hidden="true" ></i></span>
                      <span>Name</span>
                      <input style="width:50%; border-radius:10px; height:50px;" type="text" name="name" maxlength="50" placeholder="Enter Your Name"
                             data-ng-model="name" data-ng-change="setName(name)" required />
                  </div>
                  <div   data-ng-if="contractInfo != null"> <span><i aria-hidden="true" ></i></span>
                      <span>Designation</span>
                      <input style="width:50%; border-radius:10px; height:50px;" type="text" name="designation" maxlength="50" placeholder="Enter Your Designation"
                             data-ng-model="designation" data-ng-change="setDesignation(designation)" required />
                  </div>
                    <div class="input_field checkbox_option" data-ng-if="contractInfo != null">
                        <label>IP Address :: {{clientInfo.ipAddress}}</label>
                    </div>
                    <div class="input_field checkbox_option" data-ng-if="contractInfo != null">
                        <input type="checkbox" id="cb1" data-ng-model="isChecked" data-ng-click="validContract(isChecked)">
                        <label for="cb1">I agree with terms and conditions</label>
                    </div>
                    <button data-ng-click="approveSO(true)" data-ng-if="contractInfo != null">ACCEPT</button>
                    <button data-ng-click="approveSO(false)" data-ng-if="contractInfo != null">REJECT</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="form_wrapper" data-ng-if="thankYou">
    <header class="site-header" id="header">
        <h1 class="site-header__title" data-lead-id="site-header-title">THANK YOU!</h1>
    </header>

    <div class="main-content">
        <i class="fa fa-check main-content__checkmark" id="checkmark"></i>
        <p class="main-content__body" data-lead-id="main-content-body">Thanks a bunch for filling that out. It means a lot to us, just like you do! We really appreciate you giving us a moment of your time today. Thanks for being you.</p>
    </div>

    <footer class="site-footer" id="footer">
        <p class="site-footer__fineprint" id="fineprint">Copyright ©2014 | All Rights Reserved</p>
    </footer>
</div>
<!--<canvas id="signature" style="border: 1px solid black;"></canvas>-->
<!--<img id="preview" />-->
<!--<br>-->
<!--<div style="text-align:center;">-->
<!--    <input type="button" id="export" value="Export" data-ng-click="saveSignature()"/>-->
<!--    <input type="button" id="reset" value="Reset" data-ng-click="resetSignature()"/>-->
<!--</div>-->
<!--<button data-ng-click="saveSignature()">SAVE SIGNATURE</button>-->

<!--<div class="flex-row">-->
<!--    <div class="wrapper">-->
<!--        <canvas id="signature-pad" width="400" height="200"></canvas>-->
<!--    </div>-->
<!--    <div class="clear-btn">-->
<!--        <button id="clear"><span> Clear </span></button>-->
<!--    </div>-->
<!--</div>-->
<!--<script>-->
<!--&lt;!&ndash;    var canvas = document.getElementById("signature");&ndash;&gt;-->
<!--&lt;!&ndash;var w = window.innerWidth;&ndash;&gt;-->
<!--&lt;!&ndash;var h = window.innerHeight;&ndash;&gt;-->

<!--&lt;!&ndash;// As the canvas doesn't has any size, we'll specify it with JS&ndash;&gt;-->
<!--&lt;!&ndash;// The width of the canvas will be the width of the device&ndash;&gt;-->
<!--&lt;!&ndash;canvas.width = w;&ndash;&gt;-->
<!--&lt;!&ndash;// The height of the canvas will be (almost) the third part of the screen height.&ndash;&gt;-->
<!--&lt;!&ndash;canvas.height = h/2.5;&ndash;&gt;-->

<!--&lt;!&ndash;var signaturePad = new SignaturePad(canvas,{&ndash;&gt;-->
<!--&lt;!&ndash;    dotSize: 1&ndash;&gt;-->
<!--&lt;!&ndash;});&ndash;&gt;-->

<!--&lt;!&ndash;document.getElementById("export").addEventListener("click",function(e){&ndash;&gt;-->
<!--&lt;!&ndash;    // Feel free to do whatever you want with the image&ndash;&gt;-->
<!--&lt;!&ndash;    // as export to a server or even save it on the device.&ndash;&gt;-->
<!--&lt;!&ndash;    var imageURI = signaturePad.toDataURL();&ndash;&gt;-->
<!--&lt;!&ndash;    document.getElementById("preview").src = imageURI;&ndash;&gt;-->
<!--&lt;!&ndash;},false);&ndash;&gt;-->

<!--&lt;!&ndash;document.getElementById("reset").addEventListener("click",function(e){&ndash;&gt;-->
<!--&lt;!&ndash;    // Clears the canvas&ndash;&gt;-->
<!--&lt;!&ndash;    signaturePad.clear();&ndash;&gt;-->
<!--&lt;!&ndash;},false);&ndash;&gt;-->
<!--</script>-->