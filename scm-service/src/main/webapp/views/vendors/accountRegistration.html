<div class="row" data-ng-init="init()" data-ng-class="{addMargin:!editMode}">
    <div id="accountDef" class="row scm-form">
        <div class="row">
            <div class="col s9">
                <div class="left">
                    <h5 style="margin:0px;">Enter Account Details </h5>
                    <label>(Fields marked with * are mandatory. You will not be able to proceed if all mandatory fields are not filled)</label>
                    <label data-ng-show="showRequestedChangesLabel" class="yellow">Please Change the highlighted fields !!</label>
                </div>
            </div>
            <div data-ng-if="editMode" class="col s3">
                <div class="right">
                    <button class="btn" data-ng-click="goBack()">Back</button>
                </div>
            </div>
        </div>

        <form id="accountDetail" name="accountDetailForm" data-ng-model="accountDetail" class="white z-depth-3 scm-form">
            <div class="row">
                <div class="col s12 m6 l6">
                    <div class="form-element">
                        <label class="black-text" for="accountContactName">Bank Account Contact Name*</label>
                        <input id="accountContactName" name="accountContactName" data-ng-model="accountDetail.accountContactName" type="text"
                               validate="notnull" maxlength="100" required>
                        <p class="help-block"></p>
                    </div>
                    <div class="form-element row">
                        <label class="black-text" data-ng-style="detailAccount['accountContactEmail'] && {'background-color':'yellow'}" for="accountContactEmail">Bank Account Contact Email*</label>
                        <input id="accountContactEmail" name="accountContactEmail" data-ng-model="accountDetail.accountContactEmail" type="text"
                               validate="notnull,email" maxlength="100" required>
                        <p class="help-block"></p>
                        <p class="requested-changes-block" data-ng-show="detailAccount['accountContactEmail']"> {{detailAccount['accountContactEmail'].comment?detailAccount['accountContactEmail'].comment:"Field Requested For change"}}</p>
                    </div>
                    <div class="form-element row">
                        <label class="black-text" data-ng-style="detailAccount['accountContact'] && {'background-color':'yellow'}" for="accountContact">Bank Account Contact Number*</label>
                        <input id="accountContact" name="accountContact" data-ng-model="accountDetail.accountContact" type="text"
                               validate="notnull,phone" maxlength="10" required>
                        <p class="help-block"></p>
                        <p class="requested-changes-block" data-ng-show="detailAccount['accountContact']"> {{detailAccount['accountContact'].comment?detailAccount['accountContact'].comment:"Field Requested For change"}}</p>
                    </div>

                    <div class="form-element">
                        <label class="black-text" data-ng-style="detailAccount['accountNumber'] && {'background-color':'yellow'}" for="accountNumber">Bank Account Number*</label>
                        <input id="accountNumber" name="accountNumber" data-ng-model="accountDetail.accountNumber" type="text"
                               validate="notnull" maxlength="20" required>
                        <p class="help-block"></p>
                        <p class="requested-changes-block" data-ng-show="detailAccount['accountNumber']"> {{detailAccount['accountNumber'].comment?detailAccount['accountNumber'].comment:"Field Requested For change"}}</p>
                    </div>
                    <div class="form-element row">
                        <label class="black-text" data-ng-style="detailAccount['ifscCode'] && {'background-color':'yellow'}" for="ifscCode">Bank Account IFSC code*</label>
                        <input id="ifscCode" name="ifscCode" data-ng-model="accountDetail.ifscCode" type="text"
                               validate="notnull" maxlength="15" required>
                        <p class="help-block"></p>
                        <p class="requested-changes-block" data-ng-show="detailAccount['ifscCode']"> {{detailAccount['ifscCode'].comment?detailAccount['ifscCode'].comment:"Field Requested For change"}}</p>
                    </div>
					<!-- <div class="form-element row">
                        <label class="black-text" for="micrCode">Bank Account MICR code*</label>
                        <input id="micrCode" name="micrCode" data-ng-model="accountDetail.micrCode" type="text"
                              validate="notnull" maxlength="10" required>
                        <p class="help-block"></p>
                    </div> -->

                    <div class="form-element row">
                        <div class="col s4">
                            <label class=" black-text" data-ng-style="detailAccount['kindOfAccount'] && {'background-color':'yellow'}" for="kindOfAccount">Bank Account Type*</label>
                            <select id="kindOfAccount" name="kindOfAccount" data-ng-model="accountDetail.kindOfAccount"
                                    validate="notnull"
                                    data-ng-options="type as type for type in accountTypes"
                                    data-ng-change="changeAccountType(accountDetail.kindOfAccount)" required></select>
                            <p class="help-block"></p>
                            <p class="requested-changes-block" data-ng-show="detailAccount['kindOfAccount']"> {{detailAccount['kindOfAccount'].comment?detailAccount['kindOfAccount'].comment:"Field Requested For change"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailAccount['accountType']"> {{detailAccount['accountType'].comment?detailAccount['accountType'].comment:"Field Requested For change"}}</p>
                        </div>
                        <div class="col s8">
                            <label class="black-text" data-ng-style="detailAccount['cancelledCheque'] && {'background-color':'yellow'}">Upload Cancelled Cheque*</label>
                            <div class="row" ng-if="accountDetail.cancelledCheque==null">
                                <button class="btn btn-medium" data-ng-click="uploadCheque()">Upload Cheque</button>
                            </div>
                            <div class="row" ng-if="accountDetail.cancelledCheque!=null">
                                <span>{{accountDetail.cancelledCheque.documentLink}}</span>
                                <i class="material-icons pointer" ng-click="accountDetail.cancelledCheque=null;">edit</i>
                            </div>
                            <p class="requested-changes-block" data-ng-show="detailAccount['cancelledCheque']"> {{detailAccount['cancelledCheque'].comment?detailAccount['cancelledCheque'].comment:"Field Requested For change"}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="row" style="margin-top:10px;">
        <div class="col s12">
            <button data-ng-if="!editMode" data-ng-click="checkIfAlreadyFilled()" class="btn right"> Save & Continue
                <i class="material-icons right">send</i>
            </button>
            <button data-ng-if="editMode" data-ng-click="saveAccountDetails()" class="btn right"> Edit
                <i class="material-icons right">send</i>
            </button>
        </div>
    </div>
</div>
