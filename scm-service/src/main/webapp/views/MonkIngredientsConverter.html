
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4  class="center card-panel teal lighten-2 z-depth-1 cyan pulse" style="text-align: center;">Monk Ingredients Converter</h4>
            </div>
        </div>
    </div>

    <div  class=" row teal lighten-5 black-text center">
        <div class="col s10 ">
            <label for="productList" >Select Product</label>
            <select id = "productList"  data-ng-model="selectedProduct"
                    data-placeholder="Select some Product" data-ng-change = "getMonkAssets()">
                <option data-ng-repeat="product in productList "
                        value="{{product.productId}}">{{product.productName}}
                </option>
            </select>
        </div>
        <div class="col s2">
            <button class="btn btn-large teal darken-3 yellow-text center" data-ng-click="updateStock()">
                Transfer
            </button>
        </div>

    </div>

    <div class="col s12">
        <div class="row list-head" style="padding: 10px;">
            <div class="col s3">Product Name</div>
            <div class="col s3">SKU</div>
            <div class="col s1">Unit Of Measure</div>
            <div class="col s2">Quantity(Asset)</div>
            <div class="col s2">Quantity(consumable)</div>
            <div class="col s1">select</div>
        </div>
        <div class="row" data-ng-repeat="c in bookingData.bookingConsumption | orderBy: '-availableSkuList.length'"
             data-ng-class="{'list-head pad':c.bookingConsumption.length>0, 'list-item':c.bookingConsumption.length==0}">
            <div class="col s3" style="margin-bottom: 20px;"><input id="pr-{{c.productId}}"
                                                                    data-ng-if="skuAssetMap[c.selectedSku.id] !=null || (skuQuantityMap[c.productId] != null &&skuQuantityMap[c.productId].stockAtHand != 0)"
                                                                    data-ng-model="c.checked"
                                                                     type="checkbox"/>
                <label for="pr-{{c.productId}}">{{c.productName}}</label></div>
            <div class="col s3">
				<span data-ng-init="c.selectedSku=c.availableSkuList[0]"
                      data-ng-if="c.availableSkuList.length == 1"><a>{{c.availableSkuList[0].name}}</a></span>
                <span class="center-align" data-ng-if="c.availableSkuList.length != 1">
					<select  data-placeholder="Select SKU" data-ng-model="c.selectedSku"
                             data-ng-init="c.selectedSku=c.availableSkuList[0]" data-ng-options="sku as sku.name for sku in c.availableSkuList"></select>
				</span>
            </div>
            <div class="col s1">{{c.unitOfMeasure}}</div>
            <div class="col s2">{{skuAssetMap[c.selectedSku.id]!= null ? skuAssetMap[c.selectedSku.id].length : 0 }}</div>
            <div class="col s2" data-ng-show="skuQuantityMap[c.productId]!=null"> <span class="badge large" >Stock : {{skuQuantityMap[c.productId].stockAtHand}}</span>
            <input type="number"  step="1" data-ng-show="skuQuantityMap[c.productId].stockAtHand != 0 && c.checked" data-ng-model="skuQuantityMap[c.productId].selectedStock" data-ng-change="selectStock(c.productId)"/>
            </div>

            <div class="col s1" data-ng-if="skuAssetMap[c.selectedSku.id]!=null && c.checked"> <span class="badge large" >Selected : {{selectedAssets[c.selectedSku.id] == null ? 0 : selectedAssets[c.selectedSku.id].length}}</span>
                <button data-ng-click="openAssetsViewModal(c.selectedSku.id)" class="btn btn-info">Select Assets</button></div>

        </div>
    </div>


</div>

<!-- Modal Structure -->
<script type="text/ng-template" id="assetView.html">
    <div class="modal-header" data-ng-init="init()">
        <h4>Select Assets</h4>
        <p>Please Note Down Selected Asset ids For GR Purpose</p>
    </div>

    <table class="bordered striped">
        <thead>
        <tr><button class="btn btn-medium left" data-ng-click="selectAll()">Select All</button></tr>
        <tr>
            <th>Asset Id</th>
            <th>Asset Status</th>
            <th>Tag Value</th>
            <th>Owner Id</th>
            <th>Start Date</th>
            <th>Sku Id</th>
        </tr>
        </thead>
        <tbody>
        <tr data-ng-repeat="asset in assets ">
            <td><input id="ASSET-{{asset.assetId}}"
                       data-ng-model="asset.checked"
                       data-ng-change="selectAsset(asset)" type="checkbox"/>
                <label for="ASSET-{{asset.assetId}}">{{asset.assetId}}</label></td>
            <th>{{asset.assetStatus}}</th>
            <td>{{asset.tagValue}}</td>
            <td>{{asset.ownerId}}</td>
            <td>{{asset.startDate | date: 'yyyy-MM-dd'}}</td>
            <td>{{asset.skuId}}</td>
        </tr>
        </tbody>
    </table>
    <div class="modal-footer">
        <a  class="modal-close btn btn-medium right" data-ng-click=" closeModal()">Confirm</a>
    </div>

</script>
