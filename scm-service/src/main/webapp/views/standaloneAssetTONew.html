<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .grid {
         width: 100%;
    }

    .popeye-modal{
        width: 80% !important;
    }

    .table {
	width:100%;

}
.table-header {
	display:flex;
	width:100%;

	padding:(12px * 1.5) 0;
}
.table-row {
	display:flex;
	width:100%;
	padding:(12px * 1.5) 0;



}
.table-data, .header__item {
	flex: 1 1 20%;
	text-align:center;
}
.header__item {
	text-transform:uppercase;
}
    .body{
    background-color:#EBECF0 !important;
    }
    .skuContainer{

    align-items: center;
    width:100% ;
    flex:1 ;
    justify-content:space-evenly;



      font-family: 'Montserrat', sans-serif;

    }
    .skuItem{
        flex:1 ;
        padding-left: 25px;
        padding-right: 20px ;
        padding-top: 2px ;
        padding-bottom: 8px ;
        text-align: center;
        margin: auto;
        text-shadow: 1px 1px 0 #FFF;
        align-items: center;
        justify-content: center;
    }



</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()" >
    <div class="row red lighten-1 white-text z-depth-2" data-ng-if="dayClosePending">

        <div class="section">
            <div class="container center">
                <h4 class="header">Fixed Asset Day Close Pending</h4>

                <p style="line-height:24px;font-size:16px;">
                    You have not done
                    <span data-ng-if="blockWeekly">Monthly</span>
                    <span data-ng-if="blockWeekly">Weekly</span>
                    <span data-ng-if="blockWeekly">Daily</span> Fixed Assets Day close for  {{unitData.name}}  After {{LastDayCloseDate}} yet,<br>
                    All Types Of Fixed Asset Transfers and Receiving For Your Unit Is Disabled.
                    Please Complete Fixed Assets Day Close from App!!
                </p>
                <button class="btn btn-large teal lighten-5 black-text"
                        data-ng-click="showAssetGrid()" acl-action="DCDCA">SHOW ASSETS [{{assets.length}}]
                </button>
            </div>
        </div>

    </div>
    <div class="row z-depth-2" data-ng-if="showGrid" style="width:80%">
        <div
                class="grid center"
                id="grid"
                style="height:15%;"
                ui-grid="gridOptions"
                ui-grid-save-state
                ui-grid-resize-columns
                ui-grid-move-columns
                ui-grid-pinning
                ui-grid-expandable>

        </div>

    </div>
    <!--    <h3>hhhh</h3>-->

    <div class="col s12" data-ng-if="!dayClosePending">
        <div class="row" style="display: flex;">
            <div class="col s12 m6 l6" style="width:100% ;text-align:center">
                <h4>Standalone Asset Transfer Order</h4>
            </div>
        </div>

    </div>


    <div class="row" data-ng-if="!isPendingRegularTo && !dayClosePending">
        <div class="col s12 m6 l6 ">
            <label>Select Unit Type:</label>
            <select  ui-select2 ng-model="selectedCategory"  data-placeholder="Select Category"
                     data-ng-change="changeCategory(selectedCategory)" data-ng-if="allowInput" id="categories">
                <option value=""  selected>Select Category</option>

                <option ng-repeat="category in avilableCategories" value="{{category}}" >{{category}}</option>
            </select>
            <div data-ng-if="!allowInput" >
                <h6>{{unitName}}</h6>
            </div>
        </div>
        <div class="col s12 m6 l6">
            <label>Select Receiving Unit:</label>
            <select  ui-select2 ng-model="selectedUnit" data-placeholder="Select Unit"
                     data-ng-change="setReceivingUnit(selectedUnit)" data-ng-if="allowInput">
                <option value=""  selected>Select Receiving Unit</option>
                <option ng-repeat="unit in selectedUnitList" value="{{unit}}">{{unit.name}}</option>
            </select>
            <div data-ng-if="!allowInput">
                <h6>{{receivingUnitName}}</h6>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="!isPendingRegularTo && !dayClosePending">
        <div class="col s12 m6 l6">
            <label>Budget Type:</label>
            <select ui-select2 data-ng-model="selectedType" data-ng-change="validateHandOverDate(selectedType)"
                    data-ng-placeholder="selectedType" data-placeholder="Select Budget
                         Type" id="budgetTypes" data-ng-if="allowInput">
                <option value="" disabled selected>Select Type</option>
                <option value="OPEX">OPEX</option>
                <option value="CAPEX">CAPEX</option>
            </select>
            <div data-ng-if="!allowInput">
                <h6>{{budgetType}}</h6>
            </div>
        </div>
        <div class="col s12 m6 l6">
            <div class="col s6">
                <label>Select Product:</label>
                <select ui-select2 data-ng-model="selectedProduct" data-placeholder="Enter name of product" data-ng-change="setSelectedProduct(selectedProduct)" id="products">
                    <option value="" disabled selected>Select Product</option>
                    <option ng-repeat="product in scmProductDetails | filter : byProducts | filter : categoryFilter"
                            value="{{product}}">{{product.productName}}
                    </option>
                </select>
            </div>

            <div class="col s6">
                <label for="toType">Select Transfer Order type:</label>
                <select   name="toType" ng-model="transferOrderDetail.toType"
                          ng-options="toType as toType for toType in toTypeList " required></select>
                <p ng-show="trForm.toType.$error.required" class="errorMessage">Asset Tag Value is required.</p>
            </div>
            <input type="button" class="btn"  value="ADD PRODUCT" data-ng-if="allowInput" data-ng-click="addNewTOItem(1 , true)" style="margin: 15px;"  acl-action="TRNSTA"/>
            <input type="button" class="btn" value="ADD PRODUCT" data-ng-click="addNewTOItem(1 , false)"
                   data-ng-if="!allowInput" style="margin: 15px;" acl-action="TRNSTA" />
        </div>
        <input type="button" value="Refresh" class="btn " style="margin
                :15px" data-ng-click="fetchInitiatedFa()" data-ng-if="!allowInput"/>

    </div>
    <div class="row" data-ng-show="TOProducts.length>0" data-ng-if="!isPendingRegularTo && !dayClosePending">
        <form name="trForm" novalidate>
            <ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
                <li class="collection-item list-head">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s1" >#</div>
                        <div class="col s2">Product Id </div>
                        <div class="col s2" >Product Name</div>
                        <div class="col s1" style="padding-top:1em">UOM</div>
                        <div class="col s1"> Available Inventory</div>
                        <div class="col s2" >Transferred Quantity</div>
                        <div class="col s2" >Scanned Qty</div>
                        <div class="col s1">Action</div>
                    </div>
                </li>
                <li style="margin-bottom: 10px; border:#ddd 1px solid;" data-ng-repeat="item in TOProducts track by $index">
                    <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid;">
                        <div class="col s1" >{{$index+1}}</div>
                        <div class="col s2">{{item.productId}} </div>
                        <div class="col s2" ><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                        <div class="col s1" style="padding-top:1em">{{item.unitOfMeasure}}</div>
                        <div class="col s1" style="padding:1em">{{productInventorySize[item.productId]}}</div>
                        <div class="col s2" ><input type="number" name="assetCount" data-ng-if="item.isNewItem || allowInput" data-ng-model="item.transferredQuantity"
                                                    data-ng-change="validTransferQty(item ,item.transferredQuantity)"  style="width:20%" />
                            <input type="number" name="assetCount" data-ng-if="!item.isNewItem && !allowInput" data-ng-model="item.transferredQuantity"
                                   data-ng-change="validTransferQty(item,item.transferredQuantity)" style="width:20%"  disabled/>
                        </div>
                        <div class="col s2" >{{scannedAssetProductMap[item.productId].length}}</div>
                        <div class="col s1 " style="display: flex;" > <input type="button" data-ng-if="!item.isNewItem" value="X" class=" " style="width: 40px; height: 40px;border-radius: 50%;border: none;background-color: red;color: white;font-size: 20px;font-weight: bold;cursor: pointer;outline: none;"
                                                                             data-ng-click="onCancelAfterInitiate(item.productId ,$index)" />

                            <input type="button" value="+" data-ng-if="item.isNewItem && !allowInput"
                                   style="width: 40px; height: 40px;border-radius: 50%;border: none;background-color: greenyellow;color: white;font-size: 20px;font-weight: bold;cursor: pointer;outline: none;"
                                   data-ng-click="onAddAfterInitiate(item.productId , item.transferredQuantity)" style="padding-left: 12px;"/>
                        </div>
                    </div>

                    <div class="row " data-ng-repeat="(skuId,asset) in productSKuAssetMap[item.productId]  " style="width:100%">

                        <div  class="col" style="width:100%">
                            <div class="skuContainer " data-ng-click="toggle(skuId)"
                                 data-ng-style="toggleSkuList == skuId&&{'background':'#eee'}">
                                <div style="width:100% ; display:flex ; justify-content:space-between">
                                    <div class="col s1" data-ng-if="toggleSkuList != skuId"
                                         style="font-weight:bold;padding-top:1em">+</div>
                                    <div class="col s1" data-ng-if="toggleSkuList == skuId"
                                         style="font-weight:bold;padding-top:1em">-</div>
                                    <div class="col s2">{{skuId}}</div>
                                    <div class="col s2" >{{asset[0].assetName}}</div>
                                    <div class="col s1" style="padding-top:1em">{{item.unitOfMeasure}}</div>
                                    <div class="col s1" style="padding-top:1em"> {{productSKuAssetMap[item.productId][skuId].length}}</div>
                                    <div class="col s2" >N/A</div>
                                    <div class="col s2" >{{skuScannedList[skuId] ? skuScannedList[skuId]:0}}</div>
                                    <div class="col s1"></div>
                                </div>
                                <div style="width:80% ; margin:auto  ;  "  data-ng-if="toggleSkuList == skuId" >

                                    <div class="table">
                                        <div class="table-header" style="padding:10px">
                                            <div class="header__item" style="color:#26a69a ; font-weight: bold">
                                                Asset Id</div>
                                            <div class="header__item" style="color:#26a69a ; font-weight: bold">
                                                Asset Name</div>
                                            <div class="header__item" style="color:#26a69a ; font-weight: bold">
                                                Asset Status</div>
                                            <div class="header__item" style="color:#26a69a ; font-weight: bold">
                                                Asset tagValue</div>
                                        </div>
                                        <div class="table-content"
                                             data-ng-repeat="assetItem in productSKuAssetMap[item.productId][skuId]">
                                            <div class="table-row"
                                                 data-ng-style="isScanned[assetItem.assetId] == null ?
                                                   {'background-color':'red' ,'color':'white' ,'margin':'20px' }:
                                                   {'background-color':'green' , 'color':'white' ,'margin':'20px'}"
                                                 style="padding:10px">
                                                <div class="table-data" style="padding-top: 8px;">{{assetItem.assetId}}</div>
                                                <div class="table-data" style="padding-top: 8px;">{{assetItem.assetName}}</div>
                                                <div class="table-data" style="padding-top: 8px";>{{assetItem.assetStatus}}</div>
                                                <div class="table-data" style="padding-top: 8px;">{{assetItem.tagValue}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                </li>
            </ul>
            <div style="padding: 12px;" data-ng-if="pendingScanned">
                <h5 style="font-size:12px ; font-weight:bold ">
                    Your Asset Transfer Form has been created successfully
                </h5>
                <p style="font-size:12px">
                    Please login to Stock Take app and click on Transfer Out button to proceed with scanning of items
                </p>
            </div>
            <div class="row">
                <div class="col s9 form-element">
                    <label>Comment(optional):</label>
                    <textarea data-ng-model="transferOrderDetail.comment"></textarea>
                </div>
                <div class="col s12 form-element" style="align-self:center ; justify-content:space-evenly ;
                 display :flex">
                    <input type="button"   data-ng-click="onCancelTo()" class="btn" value="CANCEL" style="width:25%"/>

                    <input type="button"  data-ng-if="trForm.$valid && allowInput  " class="btn"
                           data-ng-click="initiateFATransfer() "
                           value="Initiate Request" acl-action="TRNSTA" style="width:25%" />
                    <input type="button"  data-ng-if="!allowInput  " class="btn" data-ng-click="showScanQty()"
                           value="Preview" acl-action="TRNSTA" style="width:25%" />
                </div>
            </div>
        </form>
    </div>
    <div data-ng-if="isPendingRegularTo && !dayClosePending">
        <div style="border:2px " >
            <div style="text-align:center "><h4 style="color:#b38f00">Pending Regular TO</h4></div>
            <div style="text-align:center;padding-bottom:22vh ;font-weight:bold" ><h6 style="font-weight:bold">There are already pending Regular Transfer Order . Kindly settled them and then proceed for Standalone transfer order!</h6></div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="expandableRowTemplate.html">
    <div ui-grid="row.entity.subGridOptions" style="height:{{(row.entity.subGridOptions.data.length * row.height) + row.height + 2 * row.grid.headerHeight}}px" ui-grid-auto-resize></div>
</script>
<script type="text/ng-template" id="scanViewModal.html">
    <div class="modal-header">
        <h3>Total Transferred Product Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="col s12" >
            <table class="table bordered striped" style="border: #ccc 1px solid;">
                <thead>
                <tr>
                    <th style="width:25%">Product Name</th>
                    <th style="width:25%">Scanned Qty</th>
                    <th style="width:25%">Scanned Sku Tags</th>
                    <th style="width:25%">Not Scanned Qty</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="(id,item) in diffs">
                    <td style="color: black;width:25%" data-ng-click="toggleOpen(id)">{{id}}</td>
                    <td style="color: black;width:25%" data-ng-click="toggleOpen(id)">{{diffs[id].transferredQty}}</td>

                    <td style="color: black;width:25%"  data-ng-if="item.scannedAssetTag.length" data-ng-click="toggleOpen(id)" ><p  data-ng-if="!openList[id]" style="color:blue" >{{item.scannedAssetTag[0]}}...more</p><p data-ng-click="toggleOpen(id)" data-ng-if="openList[id]" data-ng-repeat="asset in item.scannedAssetTag">{{asset}}</p></td>
                    <td style="color: black;width:25%"  data-ng-if="!item.scannedAssetTag.length"  >NULL</td>
                    <td style="color: black;width:25%" data-ng-click="toggleOpen(id)">{{diffs[id].notScannedQty}}</td>
                </tr>
                <!-- </div> -->
                </tbody>
            </table>
            <br>
            <div class="row">
                <button class="btn" style="color-red ; margin : 8px "  data-ng-click="cancel()">Cancel</button>
                <button class="btn " style="float: right; margin: 8px;" data-ng-click="submit()">Submit</button>
            </div>

        </div>

    </div>

</script>
<script type="text/ng-template" id="budgetViewModal.html">
    <div class="modal-header">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col s12">
                <div data-ng-if="emptyCheck">
                    <h5>No Budget Found For Fixed Assets(FA_Equipment) Department.Please Upload the Budget and Try Again.</h5>
                    <br>
                    <div class="row">
                        <button class="btn red pull-right"  data-ng-click="cancel()">Close</button>
                    </div>
                </div>
                <br>
            </div>
            <div class="col s12" data-ng-if="!emptyCheck">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Department Name</th>
                        <th>Total Amount</th>
                        <th>Original Amount</th>
                        <th>Budget Amount</th>
                        <th>Remaining Amount</th>
                        <th>Running Amount</th>
                        <th>Receiving Amount</th>
                        <th>Paid Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-class="{'red': transferOrderDetail.totalCost > budgetDetails.remainingAmount}">
                        <td style="color: black;">{{budgetDetails.departmentName}}</td>
                        <td style="color: black;">{{transferOrderDetail.totalCost.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.originalAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.budgetAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.remainingAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.runningAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.receivingAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.paidAmount.toFixed(2)}}</td>
                    </tr>
                    </tbody>
                </table>
                <br>
                <div class="row">
                    <button class="btn" style="color-red ; margin : 8px "  data-ng-click="cancel()">Cancel</button>
                    <button class="btn " style="float: right; color:green ; margin: 8px;" data-ng-click="submit()">Submit</button>
                </div>
            </div>
        </div>

    </div>
</script>
