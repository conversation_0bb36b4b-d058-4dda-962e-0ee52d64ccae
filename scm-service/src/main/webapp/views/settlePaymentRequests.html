<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .select2.select2-container{
        width:100% !important;
    }
</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	
    <div class="col s12">
		<div class="row">
			<div class="col s12">
				<h5 class="left">Settle Payment Requests</h5>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m6 l6">
				<label class="black-text" for="selectedCompany">Select
					Company</label> <select ui-select2="selectedCompany" id="selectedCompany"
					name="companyList" data-ng-model="selectedCompany"
					data-ng-change="changeCompany()"
					data-ng-options="company as company.name for company in companyList track by company.id"></select>
			</div>
			<div class="col s12 m6 l6">
				<label class="black-text" for="selectedBankForSettleMent">Select Bank</label> <select
					ui-select2="selectedBank" id="selectedBankForSettleMent" name="bankList"
					data-ng-model="selectedBank" data-ng-change="changeBank()"
					data-ng-options="bank as bank.name + ' - ' + bank.accountNumber for bank in bankList"></select>
			</div>
		</div>

		<div class="row">
            <div class="col s12">
                <div style="text-align: center">
                    <input type="button" class="btn" value="Upload Payment Sheet" data-ng-click="uploadDoc()" />
                    <p style="border:#f7a4a4 1px solid; padding: 10px;background:#f3c9c9;border-radius: 3px;color: #8a0a0a;"
                       data-ng-if="showNoPR">No valid payments found for settlement.</p>
                </div>
            </div>
        </div>
        <div class="row" data-ng-if="prs.length>0">
            <div class="col s12">
                <table class="table striped" style="border:#ddd 1px solid;margin-bottom: 10px;">
                    <thead>
                        <tr>
                            <th>PR Id</th>
                            <th>Invoice No.</th>
                            <th>UTR No.</th>
                            <th>Vendor</th>
                            <th>Proposed Amount</th>
                            <th>Paid Amount</th>
                            <th>Bank</th>
                            <th>Debit Acc.</th>
                            <th>Credit IFSC</th>
                            <th>Credit Acc.</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-repeat="pr in prs track by pr.paymentRequestId">
                            <td>{{pr.paymentRequestId}}</td>
                            <td>{{pr.invoiceNumber}}</td>
                            <td>{{pr.paymentDetail.utrNumber}}</td>
                            <td>{{pr.paymentDetail.vendorName}}</td>
                            <td>{{pr.paymentDetail.proposedAmount}}</td>
                            <td>{{pr.paymentDetail.paidAmount}}</td>
                            <td>{{pr.paymentDetail.debitBank}}</td>
                            <td>{{pr.paymentDetail.debitAccount}}</td>
                            <td>{{pr.paymentDetail.beneficiaryIfscCode}}</td>
                            <td>{{pr.paymentDetail.beneficiaryAccountNumber}}</td>
                        </tr>
                    </tbody>
                </table>
                <input type="button" data-ng-click="submitPayments(false)" value="Submit Payments" class="btn green" />
                <input type="button" data-ng-click="submitPayments(true)" acl-action="FUPS" value="Force Submit Payments" class="btn red" />
            </div>
        </div>
    </div>

</div>
