<style>
.grid {
	 width: 800px;
    height: 200px;
    margin-top: 10px;
    margin-bottom: 20px;
    margin-left: 10px;
}
.image-container {
  position: relative;
}
.graph {
margin-left: 50;
}
.popover {
  display: none;
  position: absolute;
  z-index: 1;
  right: 5%;
  bottom: -10%;
  max-width: 400px;
  transform: translateX(-50%);
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0,0,0,0.2);
}
.image-container:hover .popover {
  display: block;
  width: 300px;
  height: 250px;
}
.disabled {
    background: grey
}
</style>
<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h5 class="left">SKU Price History</h5>
            </div>
        </div>
    </div>

    <div>

        <div>
            <div class="row">

                <div class="col s3">
                    <label>Select Vendor *</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select'}" id="vendorList" name="vendorList" data-ng-model="selectedVendor"
                            data-ng-change="getData(selectedVendor)"
                            data-ng-options="item as item.name for item in vendorList track by item.id"></select>
                </div>
                <div class="col s3">
                    <label>Select Dispatch Location</label>
                    <select  ui-select2="{allowClear:true, placeholder: 'Select'}" id="locationList" name="locationList"
                             data-ng-model="selectedDispatchLocation"
                             data-ng-options="item as item.locationName for item in locationList track by item.id"></select>
                </div>

            </div>
            <div class="row" style="margin-bottom: 0px;">
                <div class="col s3">
                    <label>Select SKU *</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select'}" id="skuList" name="skuList"
                            data-ng-model="selectedSku"
                            data-ng-change="getSkusData(selectedSku)"
                            data-ng-options="item as item.name for item in skuList"></select>
                </div>
                <div class="col s3">
                    <label>Select Packaging</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select'}" id="packagingList" name="packagingList" data-ng-model="selectedPackaging" data-ng-change="setSelectedPackaging(selectedPackaging)"
                            data-ng-options="item as item.packagingName for item in packagingList"></select>
                </div>
                <div class="col s3">
                    <label>Select Delivery Location</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select'}" id="deliveryLocationList" name="deliveryLocationList" data-ng-model="selectedDeliveryLocation"
                            data-ng-options="item as item.name for item in deliveryLocationList"></select>
                </div>
            </div>

            <div class="row">
                <div class="row s2" style="margin-left:10px; margin-top:20px;">
                    <input type="button" class="btn" value="Find"
                           data-ng-click="showSkuGrid()" data-ng-class="{'disabled' : disableFindButton == true || selectedVendor == null || selectedSku == null}"
                           data-ng-disabled="disableFindButton"/>
                    <input type="button" class="btn" value="Graph"
                           a href="#graphModal"
                           style="margin-left:10px;"
                           data-ng-click="showPriceHistory(); createCharts();"
                           data-ng-class="{'disabled' : disableFindButton == true || selectedVendor == null || selectedSku == null}"
                           data-ng-disabled="disableFindButton"
                           modal/>
                </div>
            </div>
        </div>

        <div data-ng-if="showGrid">
            <div
                    class="grid"
                    style="width:800px"
                    id="grid"
                    ui-grid="gridOptions"
                    ui-grid-save-state
                    ui-grid-resize-columns
                    ui-grid-move-columns
                    ui-grid-pinning
                    ui-grid-exporter>

            </div>
        </div>
    </div>
</div>
<!--Modal-->
<div id="graphModal" class="modal modal-large" data-ng-if="showChart == true">

    <div class="modal-content col" id="modal-body">
        <div >

            <div id="myChart"></div>

        </div>
        <div class="modal-footer right-align">
            <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
        </div>
    </div>

</div>





