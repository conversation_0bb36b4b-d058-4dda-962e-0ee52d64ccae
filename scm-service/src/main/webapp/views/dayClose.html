<style>
    .grid {
         width: 100%;
    }

    p.custom-header {
        padding: 10px;
        background-color: #fdf3f3;
        color: #000;
    }

    p.custom-para {
        font-size: 24px;
        color: #402d2d;
        text-align: center;
    }

    div.left.step-heading {
        margin: 10px 0px;
        max-width: 80%;
    }

    button.download-list-btn {
        width: 50px !important;
        width: 50px;
        margin-top: 3px;
        margin-right: 5px;
        float: right;
        color: #000;
    }
    .popeye-modal-container .popeye-modal{
        width: 100%; !important;

    }
    .modal-large{
        width:80% !important;
         height:80% !important;
    }



</style>
<div class="row margin0" data-ng-init="init()">
    <div class="row red lighten-1 white-text z-depth-2" data-ng-if="dayClosePending">

        <div class="section">
            <div class="container center">
                <h4 class="header">Fixed Asset Day Close Pending</h4>

            <p style="line-height:24px;font-size:16px;">
                You have not done <span data-ng-if="blockWeekly">Weekly</span> Fixed Assets Day close for  {{unitData.name}}  After {{LastDayCloseDate}} yet,<br>
                Regular Day Close For Your Unit Is Disabled.
                Please Do Fixed Assets Day Close from App And Reload To Continue Regular Day Close !!
            </p>
                <button class="btn btn-large teal lighten-5 black-text"
                        data-ng-click="showAssetGrid()" acl-action="DCDCA">SHOW ASSETS [{{assets.length}}]
                </button>
            </div>
        </div>

    </div>
    <div class="row z-depth-2" data-ng-if="showGrid" style="width:80%">
                <div
                        class="grid center"
                        id="grid"
                        style="height:15%;"
                        ui-grid="gridOptions"
                        ui-grid-save-state
                        ui-grid-resize-columns
                        ui-grid-move-columns
                        ui-grid-pinning
                        ui-grid-expandable>

                </div>

    </div>
<!--    <h3>hhhh</h3>-->
    <div class="row light-green white-text z-depth-2" data-ng-if="!dayCloseInitiated && !dayClosePending && showInitiate">
        <div class="container left">
        </div>
        <button class="btn btn-xs-small download-list-btn teal lighten-5"
                data-ng-click="downloadInventorySheet()"
                data-tooltip="Download list" tooltipped>
            <i class="fa fa-download"></i>
        </button>
        <div class="section">
            <div class="container center">
                <h3 class="header">Start Day Close Process</h3>
                <p style="line-height:24px;font-size:16px;">
                    If you have completed all your transactions for the previous day,<br>
                    Please click the button below to initiate your day closing process
                </p>

                <button class="btn btn-large teal lighten-5 black-text"
                        data-ng-click="createDayCloseEvent(unitData.id,currentUser.userId)" acl-action="DCDCA">INITIATE
                </button>

                <button class="btn btn-large teal lighten-5 black-text"
                        data-ng-click="startAutoDayClose(unitData.id,currentUser.userId)" acl-action="KWHADC">Auto Day Close
                </button>
                <div class="teal center lighten-5 black-text margin-top-5" acl-action="auddc">
                    <input id="fullInventory"
                           data-ng-model="isAllListType"
                           data-ng-change="listType()" type="checkbox"/>
                    <label for="fullInventory">Full Audit Day Close</label>
                </div>
            </div>
        </div>

    </div>




    <div data-ng-if="!dayCloseInitiated && isAllListType" class="teal lighten-5 black-text center">
        <label>Select Category for Day Close</label>
        <select class="categoryList" style="width: 100%" data-ng-model="selectedCategories.selectedValues" ng-change="onCategoryChange(selectedCategories.selectedValues)"
                data-placeholder="Select some Categories" ui-select2 multiple>
            <option data-ng-repeat="category in categories "
                    value="{{category.id}}">{{category.name}}
            </option>
        </select>

        <div class="row margin-top-20">
            <div  data-ng-if="selectedCategories.selectedValues.length>0" data-ng-repeat="categoryId in selectedCategories.selectedValues ">
            <div data-ng-if="categoryId!=5" class="col s3">
                <label>Select Sub Categories For {{categories[categoryId - 1].name}} </label>
                <select class="subCategoryList"  data-ng-model="categories[categoryId - 1].selectedSubCategories" data-ng-change="onSubCategorySelect(categoryId)"
                        data-placeholder="Select Sub Categories"  ui-select2 multiple>
                    <option data-ng-repeat="subCategory in getSubCategories(categoryId) "
                            value="{{subCategory.subCategoryId}}">{{subCategory.subCategoryName}}
                    </option>
                </select>
            </div>

        </div>
        </div>
    </div>

    <a href="#pendingGrModal"  modal id="openGrModal" style="visibility: hidden;"></a>


    <div class="row margin0" data-ng-if="dayCloseInitiated">
        <div class="teal lighten-5 black-text center" data-ng-show="isAllListType && !isAccessToAuditDayClose">
            <h1>Audit Day Close In Progress....</h1>
        </div>
        <div data-ng-show="(!isAllListType || (isAllListType && isAccessToAuditDayClose))">
        <h3 class="header">Day Close Wizard
            <button class="btn btn-medium right" data-ng-if="wizardStep < 10"
                    data-ng-click="cancelDayCloseEvent(dayCloseEvent.id,currentUser.userId)">
                Cancel Day Close
            </button>
        </h3>
        <div class="row white z-depth-2" data-ng-switch="wizardStep">
            <!--RECEIVINGS VIEW-->
            <div class="col s12" data-ng-switch-when="1">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of all Receivings. Please confirm and if anything is missing, please cancel
                        the process.
                    </div>
                    <div class="right" data-ng-if="receivings!=null && receivings.length>0">
                        <button class="btn" data-ng-click="ackAllReceivings(receivings)">Next</button>
                    </div>
                    <div class="right" data-ng-if="receivings==null || receivings.length==0">
                        <button class="btn" data-ng-click="ackBookings()">Next</button>
                    </div>
                </div>
                <div class="row margin0" data-ng-if="receivings==null || receivings.length==0">
                    <p class="custom-para">No receivings found for Day Close</p>
                </div>

                <ul class="collapsible no-border" data-collapsible="accordion"
                    data-ng-if="receivings!=null && receivings.length>0" watch>
                    <li data-ng-repeat="gr in receivings">
                        <p class="collapsible-header custom-header">
                            <span class="bold">
                                {{gr.event}} for {{gr.description}} -  <span class="chip">Rs.{{gr.cost}}</span>
                            </span>
                            <span class="right-align">{{gr.time | date:'dd/MM/yyyy @ h:mma'}}</span>
                        </p>
                        <div class="collapsible-body row">
                            <table class="light-green white-text">
                                <thead>
                                <tr>
                                    <th class="center-align">S.No.</th>
                                    <th class="center-align">Sku Name</th>
                                    <th class="center-align">Qty</th>
                                    <th class="center-align">Cost</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in gr.txnItems track by $index">
                                    <td class="center-align">{{$index+1}}</td>
                                    <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}[{{item.uom}}]</a>
                                    </td>
                                    <td class="center-align">{{item.qty}}</td>
                                    <td class="center-align">{{item.cost}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </li>
                </ul>
                <div class="row margin0" data-ng-if="receivings!=null && receivings.length>0">
                    <div class="right">
                        <button class="btn" data-ng-click="ackAllReceivings(receivings)">Next</button>
                    </div>
                </div>
            </div>

            <!--BOOKINGS VIEW-->
            <div class="col s12" data-ng-switch-when="2">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of all Bookings, which also contain the consumptions if any. Please confirm
                        and if anything is missing, please cancel the process.
                    </div>
                    <div class="right" data-ng-if="bookings!=null && bookings.length>0">
                        <button class="btn" data-ng-click="ackAllBookings(bookings)">Next</button>
                    </div>
                    <div class="right" data-ng-if="bookings==null || bookings.length==0">
                        <button class="btn" data-ng-click="ackReverseBookings()">Next</button>
                    </div>
                </div>
                <div class="row margin-0" data-ng-if="bookings==null || bookings.length==0">
                    <p class="custom-para">No Bookings found</p>
                </div>

                <ul class="collapsible no-border" data-collapsible="accordion"
                    data-ng-if="bookings!=null && bookings.length>0" watch>
                    <li data-ng-repeat="gr in bookings">
                        <p class="collapsible-header custom-header">
                            <span class="bold">{{gr.name}} -  <span class="chip">Rs.{{gr.cost}}</span></span>
                        </p>
                        <div class="collapsible-body row">
                            <table class="light-green white-text">
                                <thead>
                                <tr>
                                    <th class="center-align">S.No.</th>
                                    <th class="center-align">Sku Name</th>
                                    <th class="center-align">Qty</th>
                                    <th class="center-align">Cost</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in gr.txnItems track by $index">
                                    <td class="center-align">{{$index+1}}</td>
                                    <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}[{{item.uom}}]</a>
                                    </td>
                                    <td class="center-align">{{item.qty}}</td>
                                    <td class="center-align">{{item.cost}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </li>
                </ul>
                <div class="row margin-10" data-ng-if="bookings!=null && bookings.length>0">
                    <div class="right">
                        <button class="btn" data-ng-click="ackAllBookings(bookings)">Next</button>
                    </div>
                </div>
            </div>

            <!--REVERSE BOOKINGS VIEW-->
            <div class="col s12" data-ng-switch-when="3">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of all Reverse Bookings, which also contain the consumptions if any. Please confirm
                        and if anything is missing, please cancel the process.
                    </div>
                    <div class="right" data-ng-if="reverseBookings!=null && reverseBookings.length>0">
                        <button class="btn" data-ng-click="ackAllReverseBookings(reverseBookings)">Next</button>
                    </div>
                    <div class="right" data-ng-if="reverseBookings==null || reverseBookings.length==0">
                        <button class="btn" data-ng-click="ackTransfers()">Next</button>
                    </div>
                </div>
                <div class="row margin-0" data-ng-if="reverseBookings==null || reverseBookings.length==0">
                    <p class="custom-para">No Reverse Bookings found</p>
                </div>

                <ul class="collapsible no-border" data-collapsible="accordion"
                    data-ng-if="reverseBookings!=null && reverseBookings.length>0" watch>
                    <li data-ng-repeat="gr in reverseBookings">
                        <p class="collapsible-header custom-header">
                            <span class="bold">{{gr.name}} -  <span class="chip">Rs.{{gr.cost}}</span></span>
                        </p>
                        <div class="collapsible-body row">
                            <table class="light-green white-text">
                                <thead>
                                <tr>
                                    <th class="center-align">S.No.</th>
                                    <th class="center-align">Sku Name</th>
                                    <th class="center-align">Qty</th>
                                    <th class="center-align">Cost</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in gr.txnItems track by $index">
                                    <td class="center-align">{{$index+1}}</td>
                                    <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}[{{item.uom}}]</a>
                                    </td>
                                    <td class="center-align">{{item.qty}}</td>
                                    <td class="center-align">{{item.cost}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </li>
                </ul>
                <div class="row margin-10" data-ng-if="reverseBookings!=null && reverseBookings.length>0">
                    <div class="right">
                        <button class="btn" data-ng-click="ackAllReverseBookings(reverseBookings)">Next</button>
                    </div>
                </div>
            </div>

            <!--TRANSFERS VIEW-->
            <div class="col s12" data-ng-switch-when="4">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of all Transfers. Please confirm and if anything is missing, please cancel the
                        process.
                    </div>
                    <div class="right" data-ng-if="transfers!=null && transfers.length>0">
                        <button class="btn" data-ng-click="ackAllTransfers(transfers)">Next</button>
                    </div>
                    <div class="right" data-ng-if="transfers==null || transfers.length==0">
                        <button class="btn" data-ng-click="ackGatepasses()">Next</button>
                    </div>
                </div>
                <div class="row margin0" data-ng-if="transfers==null || transfers.length==0">
                    <p class="custom-para">No Transfers found</p>
                </div>
                <ul class="collapsible no-border" data-collapsible="accordion"
                    data-ng-if="transfers!=null && transfers.length>0" watch>
                    <li data-ng-repeat="gr in transfers">
                        <p class="custom-header collapsible-header">
                            <span class="bold">{{gr.event}} for {{gr.description}} -  <span
                                    class="chip">Rs.{{gr.cost}}</span></span>
                            <span class="right-align">{{gr.time | date:'dd/MM/yyyy @ h:mma'}}</span>
                        </p>
                        <div class="collapsible-body row">
                            <table class="light-green white-text">
                                <thead>
                                <tr>
                                    <th class="center-align">S.No.</th>
                                    <th class="center-align">Sku Name</th>
                                    <th class="center-align">Qty</th>
                                    <th class="center-align">Cost</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in gr.txnItems track by $index">
                                    <td class="center-align">{{$index+1}}</td>
                                    <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}[{{item.uom}}]</a>
                                    </td>
                                    <td class="center-align">{{item.qty}}</td>
                                    <td class="center-align">{{item.cost}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </li>
                </ul>
                <div class="row margin-10" data-ng-if="transfers!=null && transfers.length>0">
                    <div class="right">
                        <button class="btn" data-ng-click="ackAllTransfers(transfers)">Next</button>
                    </div>
                </div>
            </div>


            <!--GATEPASS VIEW-->
            <div class="col s12" data-ng-switch-when="5">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of all Gatepasses. Please confirm and if anything is missing, please cancel
                        the process.
                    </div>
                    <div class="right" data-ng-if="gatepasses!=null && gatepasses.length>0">
                        <button class="btn" data-ng-click="ackAllGatepasses(gatepasses)">Next</button>
                    </div>
                    <div class="right" data-ng-if="gatepasses==null || gatepasses.length==0">
                        <button class="btn" data-ng-click="ackInvoices()">Next</button>
                    </div>
                </div>
                <div class="row margin0" data-ng-if="gatepasses==null || gatepasses.length==0">
                    <p class="custom-para">No Gatepass found</p>
                </div>

                <table data-ng-if="gatepasses!=null && gatepasses.length>0" class="light-green white-text">
                    <thead>
                    <tr>
                        <th class="center-align">Item ID</th>
                        <th class="center-align">Sku Name</th>
                        <th class="center-align">UOM</th>
                        <th class="center-align">Qty</th>
                        <th class="center-align">Cost</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="item in gatepasses track by $index">
                        <td class="center-align">{{item.event}}</td>
                        <td class="center-align"><a
                                data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}</a></td>
                        <td class="center-align">{{item.uom}}</td>
                        <td class="center-align">{{item.qty}}</td>
                        <td class="center-align">{{item.cost}}</td>
                    </tr>
                    </tbody>
                </table>

                <div class="row margin-10" data-ng-if="gatepasses!=null && gatepasses.length>0">
                    <div class="right">
                        <button class="btn" data-ng-click="ackAllGatepasses(gatepasses)">Next</button>
                    </div>
                </div>
            </div>

            <!--PERFORMA INVOICE VIEW-->
            <div class="col s12" data-ng-switch-when="6">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of all Performa Invoices. Please confirm and if anything is missing, please
                        cancel the process.
                    </div>
                    <div class="right" data-ng-if="invoices!=null && invoices.length>0">
                        <button class="btn" data-ng-click="ackAllInvoices(invoices)">Next</button>
                    </div>
                    <div class="right" data-ng-if="invoices==null || invoices.length==0">
                        <button class="btn" data-ng-click="ackReturns()">Next</button>
                    </div>
                </div>
                <div class="row margin0" data-ng-if="invoices==null || invoices.length==0">
                    <p class="custom-para">No Performa Invoice found</p>
                </div>
                <ul class="collapsible no-border" data-collapsible="accordion"
                    data-ng-if="invoices!=null && invoices.length>0" watch>
                    <li data-ng-repeat="gr in invoices">
                        <p class="custom-header collapsible-header">
                            <span class="bold">{{gr.event}} for {{gr.description}} -  <span
                                    class="chip">Rs.{{gr.cost}}</span></span>
                            <span class="right-align">{{gr.time | date:'dd/MM/yyyy @ h:mma'}}</span>
                        </p>
                        <div class="collapsible-body row">
                            <table class="light-green white-text">
                                <thead>
                                <tr>
                                    <th class="center-align">S.No.</th>
                                    <th class="center-align">Sku Name</th>
                                    <th class="center-align">Qty</th>
                                    <th class="center-align">Cost</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in gr.txnItems track by $index">
                                    <td class="center-align">{{$index+1}}</td>
                                    <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}[{{item.uom}}]</a>
                                    </td>
                                    <td class="center-align">{{item.qty}}</td>
                                    <td class="center-align">{{item.cost}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </li>
                </ul>
                <div class="row margin-10" data-ng-if="invoices!=null && invoices.length>0">
                    <div class="right">
                        <button class="btn" data-ng-click="ackAllInvoices(invoices)">Next</button>
                    </div>
                </div>
            </div>


            <!--RETURNS VIEW-->
            <div class="col s12" data-ng-switch-when="7">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of all Returns. Please confirm and if anything is missing, please cancel the
                        process.
                    </div>
                    <div class="right" data-ng-if="returns!=null && returns.length>0">
                        <button class="btn" data-ng-click="ackAllReturns(returns)">Next</button>
                    </div>
                    <div class="right" data-ng-if="returns==null || returns.length==0">
                        <button class="btn" data-ng-click="ackWastages()">Next</button>
                    </div>
                </div>
                <div class="row margin0" data-ng-if="returns==null || returns.length==0">
                    <p class="custom-para">No Returns found</p>
                </div>
                <table data-ng-if="returns!=null && returns.length>0" class="light-green white-text">
                    <thead>
                    <tr>
                        <th class="center-align">Item ID</th>
                        <th class="center-align">Sku Name</th>
                        <th class="center-align">UOM</th>
                        <th class="center-align">Qty</th>
                        <th class="center-align">Cost</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="item in returns track by $index">
                        <td class="center-align">{{item.event}}</td>
                        <td class="center-align"><a
                                data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}</a></td>
                        <td class="center-align">{{item.uom}}</td>
                        <td class="center-align">{{item.qty}}</td>
                        <td class="center-align">{{item.cost}}</td>
                    </tr>
                    </tbody>
                </table>
                <div class="row margin-10" data-ng-if="returns!=null && returns.length>0">
                    <div class="right">
                        <button class="btn" data-ng-click="ackAllReturns(returns)">Next</button>
                    </div>
                </div>
            </div>


            <!--WASTAGE VIEW-->
            <div class="col s12" data-ng-switch-when="8">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of all Wastage booked. Please confirm and if anything is missing, please
                        cancel the process.
                    </div>
                    <div class="right" data-ng-if="wastages!=null && wastages.length>0">
                        <button class="btn" data-ng-click="ackAllWastages(wastages)">Next</button>
                    </div>
                    <div class="right" data-ng-if="wastages==null || wastages.length==0">
                        <button class="btn" data-ng-click="ackInventory()">Next</button>
                    </div>
                </div>
                <div class="row" data-ng-if="wastages==null || wastages.length==0">
                    <p class="custom-para">No Wastage Events recorded for the day.</p>
                </div>
                <table data-ng-if="wastages!=null && wastages.length>0" class="light-green white-text">
                    <thead>
                    <tr>
                        <th class="center-align">S.No.</th>
                        <th class="center-align">Sku Name</th>
                        <th class="center-align">UOM</th>
                        <th class="center-align">Qty</th>
                        <th class="center-align">Cost</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="item in wastages track by $index">
                        <td class="center-align">{{$index+1}}</td>
                        <td class="center-align"><a
                                data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}</a></td>
                        <td class="center-align">{{item.uom}}</td>
                        <td class="center-align">{{item.qty}}</td>
                        <td class="center-align">{{item.cost}}</td>
                    </tr>
                    </tbody>
                </table>
                <div class="row margin-10" data-ng-if="wastages!=null && wastages.length>0">
                    <div class="right">
                        <button class="btn" data-ng-click="ackAllWastages(wastages)">Next</button>
                    </div>
                </div>
            </div>

            <!--INVENTORY VIEW-->
            <div class="col s12 center" data-ng-switch-when="9">
                <div class="row margin-10">
                    <div class="left step-heading">
                        Below is the list of the SKUs you need to fill for today.
                    </div>
                    <div class="right" data-ng-if="inventoryList!=null && loadedInventoryList">
                        <input type="button" class="btn" value="Upload Stock Sheet" data-ng-if="isAllListType" data-ng-click="uploadDoc()" />
                        <button class="btn" data-ng-if="isAllListType" data-ng-click="downloadInventorySheet()">Download</button>
                       <button class="btn" data-ng-click="fillZero()" acl-action="KWHADC">Fill Zero</button>
                       <button class="btn" data-ng-click="autoFill()" acl-action="KWHADC">Auto Fill</button>
                        <button class="btn" data-ng-show="isSubmit" data-ng-click="submitConfirmation(inventoryList)">Submit</button>
                        <button class="btn" data-ng-hide="isSubmit"  data-ng-click="showDayClosePreview()">Preview</button>
                    </div>
                </div>
                <table class="bordered inventory-table" data-ng-if="inventoryList.length > 0">
                    <thead>
                    <tr>
                        <th class="center-align">S.No.</th>
                        <th class="center-align">SKU</th>
                        <th class="center-align" tooltipped data-tooltip="Opening Stock">OPENING*</th>
                        <th class="center-align" tooltipped data-tooltip="Received">GR*</th>
                        <th class="center-align" tooltipped data-tooltip="ReceivedWithInitiatedGR">GRI*</th>
                        <th class="center-align" tooltipped data-tooltip="Bookings">BKG*</th>
                        <th class="center-align" tooltipped data-tooltip="Reverse Bookings">RBKG*</th>
                        <th class="center-align" tooltipped data-tooltip="Transferred">TO*</th>
                        <th class="center-align" tooltipped data-tooltip="Wasted">WASTE*</th>
                        <th class="center-align" tooltipped data-tooltip="Consumption">CONSP*</th>
                        <th class="center-align" tooltipped data-tooltip="Reverse Consumption">RCONSP*</th>
                        <th class="center-align" tooltipped data-tooltip="Closing Stock">CLOSING*</th>
                        <th class="center-align" tooltipped data-tooltip="Variance in Stock">VAR*</th>
                        <!--<th class="center-align" tooltipped data-tooltip="Variance Reason">VAR-RSN*</th>-->
                        <th class="center-align" tooltipped data-tooltip="Variance Cost">COST*</th>
                        <th class="center-align">Packaging List</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="item in inventoryList track by $index">
                        <td class="center-align">{{$index+1}}</td>
                        <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.name}}[{{item.uom}}]</a>
                        </td>
                        <td class="center-align">{{item.opening.toFixed(2)}}</td>
                        <td class="center-align">{{item.received.toFixed(2)}}</td>
                        <td class="center-align">{{item.receivedWithInitiatedGr}}</td>
                        <td class="center-align">{{item.booked.toFixed(2)}}</td>
                        <td class="center-align">{{item.reverseBooked.toFixed(2)}}</td>
                        <td class="center-align">{{item.transferred.toFixed(2)}}</td>
                        <td class="center-align">{{item.wasted.toFixed(2)}}</td>
                        <td class="center-align">{{item.consumption.toFixed(2)}}</td>
                        <td class="center-align">{{item.reverseConsumption.toFixed(2)}}</td>
                        <td class="center-align">{{item.stockValue}}</td>
                        <td class="center-align" data-ng-class="{'red':item.variance!=0}">{{item.variance.toFixed(2)}}

                        <div class="center-align" data-ng-show="item.variance!=0">
                            <select data-ng-model="item.varianceReason">
                                <option data-ng-repeat="reason in varianceReasonList" value="{{reason}}">
                                    {{reason}}
                                </option>
                            </select>
                        </div>
                        <div class="row" data-ng-if="item.stockValue > 0 && item.variance < 0 && item.categoryId === 4">
                            <div class="col s6">
                            <label for="inputCreated2">Select Expiry Date</label>
                            <input input-date type="text" name="created" id="inputCreated2" ng-model="item.selectedExpiry"
                                   container="" format="yyyy-mm-dd" select-years="1" min="{{currentDate}}" max="{{maxDatesOfSku[item.skuId]}}"/>
                            </div>
                            <div class="col s6" data-ng-if="item.selectedExpiry != null">
                                <label for="expiryId">Expire Time :</label>
                                <select id="expiryId" ng-model="item.expiryTime" data-placeholder="Enter Expiry Date" data-ng-change="setExpiryDate(item.selectedExpiry,item.expiryTime, item)">
                                    <option value="01">1 AM</option>
                                    <option value="13">1 PM</option>
                                </select>
                            </div>
                        </div>
                        <div class="row" data-ng-if="item.stockValue > 0 && item.variance < 0 && item.selectedExpiryDate != null && item.categoryId === 4">
                        {{item.selectedExpiryDate | date : 'yyyy-MM-dd HH:mm:ss'}}
                        </div>
                        </td>

                        <td class="center-align" data-ng-class="{'red':item.variance!=0}">
                            {{item.varianceCost.toFixed(2)}}
                        </td>
                        <td>
                            <div class="selected-detail" data-ng-repeat="packaging in item.packagings track by $index">
                                <span class="packaging-value">{{packaging.packagingName}}</span>
                                <input type="number" min="0" data-ng-model="packaging.value" class="packaging-text"
                                       data-ng-change="changeStockValue(item)"/>
                                <i class="remove-icon pointer close material-icons"
                                   data-ng-click="removePackaging(item,$index)">close</i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div data-ng-if="inventoryList.length == 0"
                     style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No SKU's Found to fill Closing For Current Day..!</div>

                <div class="row margin-10">
                    <div class="right" data-ng-if="inventoryList!=null && loadedInventoryList">
                        <button class="btn" data-ng-show="isSubmit" data-ng-click="submitConfirmation(inventoryList)">Submit</button>
                        <button class="btn" data-ng-hide="isSubmit"  data-ng-click="showDayClosePreview()">Preview</button>
                    </div>
                </div>
            </div>


            <!--SUMMARIZED VIEW-->
            <div class="col s12 center" data-ng-switch-when="10">
                <div class="row margin0">
                    <div class="left step-heading">
                        Below is the list of all negative inventory SKUs. Please reset these values to write off.
                    </div>
                    <table class="bordered inventory-table"
                           data-ng-if="correctInventory!=null && correctInventory.length>0">
                        <thead>
                        <tr>
                            <th class="center-align">SKU</th>
                            <th class="center-align" tooltipped data-tooltip="Opening Stock">OPENING*</th>
                            <th class="center-align" tooltipped data-tooltip="Received">GR*</th>
                            <th class="center-align" tooltipped data-tooltip="Bookings">BKG*</th>
                            <th class="center-align" tooltipped data-tooltip="Reverse Bookings">RBKG*</th>
                            <th class="center-align" tooltipped data-tooltip="Transferred">TO*</th>
                            <th class="center-align" tooltipped data-tooltip="Wasted">WASTE*</th>
                            <th class="center-align" tooltipped data-tooltip="Consumption">CONSP*</th>
                            <th class="center-align" tooltipped data-tooltip="Reverse Consumption">RCONSP*</th>
                            <th class="center-align" tooltipped data-tooltip="Closing Stock">CLOSING*</th>
                            <th class="center-align">VARIANCE</th>
                            <!--<th class="center-align">VARIANCE REASON</th>-->
                            <th class="center-align">COST</th>
                            <th class="center-align">EDIT</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="inventoryItem in correctInventoryItems track by inventoryItem.skuId">
                            <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{inventoryItem.name}}
                                [{{inventoryItem.uom}}]</a></td>
                            <td class="center-align">{{inventoryItem.opening}}</td>
                            <td class="center-align">{{inventoryItem.received}}</td>
                            <td class="center-align">{{inventoryItem.booked}}</td>
                            <td class="center-align">{{inventoryItem.reverseBooked}}</td>
                            <td class="center-align">{{inventoryItem.transferred}}</td>
                            <td class="center-align">{{inventoryItem.wasted}}</td>
                            <td class="center-align">{{inventoryItem.consumption}}</td>
                            <td class="center-align">{{inventoryItem.reverseConsumption}}</td>
                            <td class="center-align" data-ng-class="{'red white-text':inventoryItem.stockValue<=0}">
                                {{inventoryItem.stockValue}}

                            </td>
                            <td class="center-align">{{inventoryItem.variance.toFixed(2)}}
                                <div class="row center-align" data-ng-show="inventoryItem.variance!=0">
                                    <select data-ng-model="inventoryItem.varianceReason">
                                        <option data-ng-repeat="reason in varianceReasonList" value="{{reason}}">
                                            {{reason}}
                                        </option>
                                    </select>
                                </div>

                                <div class="row" data-ng-if="inventoryItem.stockValue > 0 && inventoryItem.variance < 0 && inventoryItem.categoryId === 4">
                                    <div class="col s6">
                                        <label for="inputCreated3">Select Expiry Date</label>
                                        <input input-date type="text" name="created" id="inputCreated3" ng-model="inventoryItem.selectedExpiry"
                                               container="" format="yyyy-mm-dd" select-years="1" min="{{currentDate}}" max="{{maxDatesOfSku[inventoryItem.skuId]}}"/>
                                    </div>
                                    <div class="col s6" data-ng-if="inventoryItem.selectedExpiry != null">
                                        <label for="expiryId2">Expire Time :</label>
                                        <select id="expiryId2" ng-model="inventoryItem.expiryTime" data-placeholder="Enter Expiry Date" data-ng-change="setExpiryDate(inventoryItem.selectedExpiry,inventoryItem.expiryTime, inventoryItem)">
                                            <option value="01">1 AM</option>
                                            <option value="13">1 PM</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row" data-ng-if="inventoryItem.stockValue > 0 && inventoryItem.variance < 0 && inventoryItem.selectedExpiryDate != null && inventoryItem.categoryId === 4">
                                    {{inventoryItem.selectedExpiryDate | date : 'yyyy-MM-dd HH:mm:ss'}}
                                </div>
                            </td>

                            <td class="center-align">{{inventoryItem.varianceCost}}</td>
                            <td>
                                <div class="selected-detail" data-ng-repeat="packaging in inventoryItem.packagings
                                            track by $index">
                                    <span class="packaging-value">{{packaging.packagingName}}</span>
                                    <input type="number" min="0" data-ng-model="packaging.value" class="packaging-text"
                                           data-ng-change="editStock(inventoryItem)"/>
                                    <i class="remove-icon pointer close material-icons"
                                       data-ng-click="removeEditedStock(inventoryItem, $index)">close</i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row" data-ng-if="correctInventory==null || correctInventory.length==0">
                    <p class="no-negative-inventory">
                        No inventory item found with negative quantity
                    </p>
                </div>
                <div class="row margin-10">
                    <div class="col s6 margin-bottom-5">
                        <ul ng-if="pager.totalPages>1" class="pagination">
                            <li class="waves-effect" data-ng-if="pager.currentPage != 1">
                                <a tooltipped data-tooltip="Previous Page" ng-click="setPage(pager.currentPage - 1)">
                                    <i class="material-icons">chevron_left</i>
                                </a>
                            </li>
                            <li class="active white-text">{{pager.currentPage}}</li>
                            <li class="waves-effect" data-ng-if="pager.currentPage != pager.totalPages">
                                <a tooltipped data-tooltip="Next Page" ng-click="setPage(pager.currentPage + 1)">
                                    <i class="material-icons">chevron_right</i>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col s6 margin-bottom-5">
                        <button tooltipped data-tooltip="Submit Day Close" class="btn btn-large right"
                                data-ng-if="(pager.currentPage == pager.totalPages)"
                                ng-click="submitDayClose()">SUBMIT DAY CLOSE
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!--Preview Of Negative Inventory Before Persisting Data In DB-->
<script type="text/ng-template" id="dayClosePreview.html">
    <div class="modal-header" data-ng-init="init()" style="background-color: #E4E067">
        <h5 class="modal-title" id="modal-title" style="margin-top: 0px; color: #FB1C1C" ;>Below is the list of all negative inventory SKUs.
            Please verify possible variance carefully. The day close cannot be cancelled once you Accept and proceed.</h5>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div data-ng-if="correctInventory!=null && correctInventory.length>0">
            <table class="bordered inventory-table"
                   data-ng-if="correctInventory!=null && correctInventory.length>0">
                <thead>
                <tr>
                    <th class="center-align">SKU</th>
                    <th class="center-align" tooltipped data-tooltip="Opening Stock">OPENING*</th>
                    <th class="center-align" tooltipped data-tooltip="Received">GR*</th>
                    <th class="center-align" tooltipped data-tooltip="Bookings">BKG*</th>
                    <th class="center-align" tooltipped data-tooltip="Reverse Bookings">RBKG*</th>
                    <th class="center-align" tooltipped data-tooltip="Transferred">TO*</th>
                    <th class="center-align" tooltipped data-tooltip="Wasted">WASTE*</th>
                    <th class="center-align" tooltipped data-tooltip="Consumption">CONSP*</th>
                    <th class="center-align" tooltipped data-tooltip="Reverse Consumption">RCONSP*</th>
                    <th class="center-align" tooltipped data-tooltip="Closing Stock">CLOSING*</th>
                    <th class="center-align"tooltipped data-tooltip="Approx variance Cost">APPROX VARIANCE COST</th>
                </tr>
                </thead>
                <tbody>
                <tr style="margin: 50px; !important; padding: 10px;" data-ng-repeat="inventoryItem in correctInventoryItems track by $index " >
                    <td class="center-align">{{inventoryItem.name}}
                        [{{inventoryItem.uom}}]</a></td>
                    <td class="center-align">{{inventoryItem.opening}}</td>
                    <td class="center-align">{{inventoryItem.received}}</td>
                    <td class="center-align">{{inventoryItem.booked}}</td>
                    <td class="center-align">{{inventoryItem.reverseBooked}}</td>
                    <td class="center-align">{{inventoryItem.transferred}}</td>
                    <td class="center-align">{{inventoryItem.wasted}}</td>
                    <td class="center-align">{{inventoryItem.consumption}}</td>
                    <td class="center-align">{{inventoryItem.reverseConsumption}}</td>
                    <td class="center-align" data-ng-class="{'red white-text':inventoryItem.stockValue<=0}">
                        {{inventoryItem.stockValue}}

                    </td>
                    <td class="center-align">{{getVarianceCost(inventoryItem.stockValue,inventoryItem.unitPrice)}}

                    </td>

                </tr>
                </tbody>
            </table>
        </div>
        <div class="row" data-ng-if="correctInventory==null || correctInventory.length==0">
            <p class="no-negative-inventory">
                No inventory item found with negative quantity
            </p>
        </div>
        <div class="row ">
            <div class="col s6 margin-bottom-5">
                <ul ng-if="pager.totalPages>1" class="pagination">
                    <li class=" white-text" data-ng-if="pager.currentPage != 1"><a tooltipped data-tooltip="first Page" ng-click="setPage(1)">
                        <i class="material-icons">first_page</i>
                    </a></li>
                    <li class="waves-effect" data-ng-if="pager.currentPage != 1">
                        <a tooltipped data-tooltip="Previous Page" ng-click="setPage(pager.currentPage - 1)">
                            <i class="material-icons">chevron_left</i>
                        </a>
                    </li>
                    <li class="active white-text">{{pager.currentPage}}</li>
                    <li class="waves-effect" data-ng-if="pager.currentPage != pager.totalPages">
                        <a tooltipped data-tooltip="Next Page" ng-click="setPage(pager.currentPage + 1)">
                            <i class="material-icons">chevron_right</i>
                        </a>
                    </li>
                    <li class=" white-text" data-ng-if="pager.currentPage != pager.totalPages"><a tooltipped data-tooltip="Last Page" ng-click="setPage(pager.totalPages)">
                        <i class="material-icons">last_page</i>
                    </a></li>


                </ul>
            </div>
            <div class="col s6 margin-bottom-5">
                <button tooltipped data-tooltip="Accept Variance" class="btn btn-medium right"
                        data-ng-if="(pager.currentPage == pager.totalPages)"
                        ng-click="acceptVariance()">Accept
                </button>
                <button tooltipped data-tooltip="Reject Variance" class="btn btn-medium left"
                        ng-click="modalClose()">Reject
                </button>
            </div>
        </div>
    </div>
</script>


    <div id="pendingGrModal"
         class="modal modal-large ">
        <div class="modal-content ">
            <h4 class="center-align z-depth-1 grey lighten-2">Pending Receivings</h4>
            <p>

            <div class="row white z-depth-3  orange lighten-4 center-align ">
                <table>
                    <tr>
                        <th>Gr Id</th>
                        <th>Transferring Unit</th>
                        <th>Generation Date</th>
                        <th>Type</th>
                        <th data-ng-if="blockOnPendingGrs == true">Blocking In</th>
                    </tr>
                    <tr  data-ng-repeat="gr in blockedGrs">
                        <td> {{gr.id}} </td>
                        <td> {{gr.generationUnitId.name}} </td>
                        <td> {{gr.generationTime | date:'dd/MM/yyyy HH:mm:ss'}}</td>
                        <td> {{gr.assetOrder ? "ASSET" : "REGULAR"}} </td>
                        <td data-ng-if="blockOnPendingGrs == true" data-ng-class="{'red': gr.blocked == true , 'orange' : gr.blockingInDays == 1}">
                            {{gr.blocked ? "Blocked" : gr.blockingInDays + " days"}}
                        </td>
                    </tr>
                </table>


            </div>
        </div>

        <div class="modal-title center-align z-depth-1 grey lighten-2" style="line-height: 5px;">
            <div  data-ng-if="blockOnPendingGrs == false">* This Are Pending Receivings For This Unit . Please Settle Them On Time . Otherwise After Some Days You Will Not Be Able To Do Day Close !!!</div>
            <div  data-ng-if="blockOnPendingGrs == true">
                <h6 data-ng-if="dayCloseBlocked == true" class="data-error">Please settle the 'Blocked' GR's shown in Red to proceed with Day Close</h6>
                <h6 data-ng-if="dayCloseBlocked == false" class="deep-orange-text bold">Please settle above GR's otherwise, tomorrow's Day Close will be blocked</h6>
            </div>
            <div style="margin-top: 20px" data-ng-if="varianceBlocking" class="data-error">* Please Acknowledge Variance To Do Day Close  !!!</div>
        </div>

        <div class="row">
            <button
                    class="modal-action modal-close  btn  right"
                    style="margin-right: 10px"
                    data-ng-class="{'orange' : dayCloseBlocked == true || varianceBlocking , 'green' : dayCloseBlocked == false || !varianceBlocking }"
                    ng-click="closeReceivingModal()">{{dayCloseBlocked || varianceBlocking ? "Close" : "Proceed"}}
            </button>
        </div>
    </div>
</div>

<script
        type="text/ng-template"
        id="expandableRowTemplate.html">
    <div ui-grid="row.entity.subGridOptions" style="height:{{(row.entity.subGridOptions.data.length * row.height) + row.height + 2 * row.grid.headerHeight}}px" ui-grid-auto-resize></div>
</script>
