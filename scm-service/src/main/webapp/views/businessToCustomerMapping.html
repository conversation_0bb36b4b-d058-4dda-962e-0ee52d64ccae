<style>
    .grid {
        height: 500px;
        padding: 0 !important;
    }

    .select2.select2-container {
        width: auto !important;
    }
</style>
<div
        class="row white z-depth-3"
        data-ng-init="init()">
    <div class="col s12">
        <h4>Business To Vendor Mapping</h4>
    </div>
    <div
            class="row"
            data-ng-show="!updateMappingRequested"
            id="mappingDivDisplay">
        <div class="col s12">
            <div
                    class="scm-form"
                    style="padding: 10px 0;">
                <div class="col s6">
                    <label
                            class="black-text"
                            for="requestForLable">Mapping Type</label>
                    <select
                            ui-select2="selectedUnit1"
                            id="requestForListData"
                            name="requestForListData"
                            data-placeholder="Select mapping type"
                            data-ng-model="mappingType"
                            data-ng-options="mappingType as mappingType.name for mappingType in mappingList"
                            ></select>
                    <label
                            class="black-text"
                            for="requestForLable">Business Types</label>
                    <!--<select ui-select2="selectedUnit3" id="types" name="types" data-ng-model="bussType" data-ng-change="changeType(bussType)">-->
                        <!--<option data-ng-repeat="type in businessTypes" value="{{type}}">{{type}}</option>-->
                    <!--</select>-->
                    <select ui-select2="selectedUnit3" id="types" name="types" data-ng-model="bussType" data-ng-change="changeType(bussType.name)"
                            data-ng-options="businessType as businessType.name for businessType in businessTypeList">
                    </select>
                    <button
                            data-ng-click="searchMappingShow()"
                            data-tooltip="Search Mappings"
                            class="btn left" tooltipped>Search</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="scm-form"
        data-ng-if="!updateMappingRequested && gridDataId != null">
    <div
            class="row margin0"
            data-ng-hide="bussType == null">
        <div class="col s4">
            <h6
                    class="card-panel slim red lighten-2 white-text "
                    align="left">
                <b>Updating Mappings for {{bussType.name}}</b>
            </h6>
        </div>
        <div
                class="col s4"
                align="right">
            <button
                    id="reset"
                    type="button"
                    class="btn btn-success"
                    data-tooltip="Reset Mappings"
                    data-ng-click="searchMappingShow()" tooltipped >Reset</button>
            <button
                    id="save"
                    type="button"
                    class="btn btn-success"
                    data-tooltip="Update Mappings"
                    data-ng-click="submitMapping()" tooltipped >Submit</button>
        </div>
    </div>
    <div class="row margin0">
        <div
                class="col s12 grid"
                id="grid"
                ui-grid="gridOptions"
                ui-grid-save-state
                ui-grid-selection
                ui-grid-resize-columns
                ui-grid-move-columns></div>
    </div>
</div>
<div
        data-ng-if="updateMappingRequested"
        id="mappingSummaryViewDiv">
    <div class="row margin0">
        <div
                class="col s6 card-panel red lighten-2"
                align="left">
            <h6 class="white-text center">
                <b>Updating Mappings for {{bussType.name}}</b>
            </h6>
        </div>
        <div
                class="col s6"
                align="right">
            <button
                    class="btn btn-success"
                    data-ng-click="backPreview()">Back</button>
            <button
                    class="btn btn-success red lighten-2"
                    data-ng-click="updateMapping()">update</button>
        </div>
    </div>
    <div
            data-ng-if="allGridViewShow.length == 0"
            class="text-center-disabled">
        No Active Mappings to display. <br>Click Update to mark all
        mappings as Inactive
    </div>
    <div
            class="row margin0"
            data-ng-if="allGridViewShow.length > 0">
        <div class="col s12">
            <ul class="collection striped">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s1">Id</div>
                        <div class="col s3">Name</div>
                        <div class="col s2">Status</div>
                        <div class="col s2">Type</div>
                        <div class="col s2">Mapping Status</div>
                    </div>
                </li>
            </ul>
            <ul
                    class="collection striped"
                    style="max-height: 350px; overflow: auto;">
                <li
                        class="collection-item clickable"
                        data-ng-repeat="allGVS in allGridViewShow | orderBy : 'id' track by $index">
                    <div
                            class="row"
                            style="margin-bottom: 0px;">
                        <div class="col s1">&nbsp;{{allGVS.id}}</div>
                        <div class="col s3">&nbsp;{{allGVS.name }}</div>
                        <div class="col s2">&nbsp;{{allGVS.status}}</div>
                        <div class="col s2">&nbsp;{{allGVS.category}}</div>
                        <div class="col s2">&nbsp;{{allGVS.mappingStatus}}</div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>