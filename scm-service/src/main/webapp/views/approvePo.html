<style>
.grid {
	 width: 800px;
    height: 200px;
    margin-top: 10px;
    margin-bottom:20px;
}
.image-container {
  position: relative;
}
.popover {
  display: none;
  position: absolute;
  z-index: 1;
  right: 5%;
  bottom: -10%;
  max-width: 400px;
  transform: translateX(-50%);
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0,0,0,0.2);
}
.image-container:hover .popover {
  display: block;
  width: 300px;
  height: 250px;
}
.advanceHeader {
    line-height: 40px;
    background: #ea9e13;
    font-weight: 700;
    border-radius: 3px;
}
</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3">
            <div class="col s12">
                <h4 data-ng-if="!showViewActions">Pending Purchase Orders Requests</h4>
                <h4 data-ng-if="showViewActions">View Purchase Orders</h4>
            </div>
            <div class="col s4" data-ng-if="!showViewActions">
                <input id="capexIdCheck" type="checkbox"  data-ng-model="isCapexId"  data-ng-click="checkCapexId()">
                <label for="capexIdCheck">Search from Capex Request ID</label>
            </div> 
            <div class="searchingCard">
            <div class="col s12 margin-bottom-15">
                <div class="col s2">
                    <label>Select Start date</label>
                    <input input-date type="text" ng-model="startDate" data-ng-disabled="isCapexId == true" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select End date</label>
                    <input input-date type="text" ng-model="endDate" data-ng-disabled="isCapexId == true" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select Unit</label>
                   <div data-ng-hide="isCapexId == true">
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Unit'}" ng-options="userMappingUnit as userMappingUnit.name for userMappingUnit in userMappingUnits"
                    data-ng-change="selectUnit(unitSelected)" data-ng-model="unitSelected"></select>
                   </div>
                    
                   <div data-ng-hide="isCapexId == false">
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Unit', disabled:true }" ng-options="userMappingUnit as userMappingUnit.name for userMappingUnit in userMappingUnits"
                    data-ng-change="selectUnit(unitSelected)" data-ng-model="unitSelected"></select>
                   </div>
                            
                        </div>
                <div class="col s2">
                <label>Purchase Order Id</label>
                <input type="number" placeholder="PO ID" data-ng-disabled="isCapexId == true" name="poId" id="poId" ng-model="poId"/>
                </div>
                 <div class="col s2">
                    <label>Select Status</label>
                    <select data-ng-model="selectedStatus" data-ng-disabled="isCapexId == true"
                            data-ng-options="type as type for type in txnStatus"></select>
                </div>

                <div class="col s2">
                    <label>Select Vendor</label>
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" ng-options="vendor as vendor.entityName for vendor in vendors"
                            data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                </div>

                <div class="col s2">
                    <div data-ng-hide="isCapexId == true">
                        <label>Select SKU</label>
                        <select id="skuList" ui-select2="{allowClear:true, placeholder: 'Select SKU'}" ng-options="sku.id as sku.name for sku in skus"
                                data-ng-change="selectSKU(skuSelected)" data-ng-model="skuSelected"></select>
                    </div>

                    <div data-ng-hide="isCapexId == false">
                        <label>Select SKU</label>
                        <select id="skuList" ui-select2="{allowClear:true, placeholder: 'Select SKU', disabled:true}" ng-options="sku.id as sku.name for sku in skus"
                                data-ng-change="selectSKU(skuSelected)" data-ng-model="skuSelected"></select>
                    </div>     
                </div>
                <div class="col s2" data-ng-if="!showViewActions">
                    <label>Capex Request ID </label>
                    <input type="text" id="enterPoCapexId" data-ng-model="capexRequestId" data-ng-disabled="isCapexId == false"  data-ng-change="changeCapexId(capexRequestId)">
                  </div>
            </div>
            <div class="col s12 margin-bottom-15">
                 <div class="col s3">
                    <button class="btn margin-top-20" data-ng-if="!isCapexId" data-ng-click="getPOs()" acl-action="VOMVPOV" style="margin-left: 410px;">Find</button>
                </div>
                <div class="col s2" data-ng-if="isCapexId">
                    <button class="btn"  acl-action="VOMVPOV" data-ng-click="getPoByCapex()" >Find</button>
              </div>
                </div>
        </div>
        <div class="row" data-ng-if="!showViewActions && isCapexId">
            <h5>Summary : </h5>
            <div data-ng-if="!capexSummaryShow && capexSummaryLoading"><p style="margin: 10px; font-size: 20px;">Loading Summary...</p></div>
            <div class="col s12" data-ng-if="capexSummaryShow">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Department</th>
                        <th>Initial Amt</th>
                        <th>Original Amt</th>
                        <th>Budget Amt</th>
                        <th>Remaining Amt</th>
                        <th>Running Amt</th>
                        <th>Receiving Amt</th>
                        <th>Paid Amt</th>
                        <th>SO #</th>
                        <th>Pending SO</th>
                        <th>PO #</th>
                        <th>Pending PO</th>
                        <th>Vendor #</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat-start="items in summaryDepartmentList">
                        <td>{{items.departmentName}}</td>
                        <td>{{items.initialAmount.toFixed(2)| number}}</td>
                        <td>{{items.originalAmount.toFixed(2)| number}}</td>
                        <td>{{items.budgetAmount.toFixed(2)| number}}</td>
                        <td>{{items.remainingAmount.toFixed(2)| number}}</td>
                        <td>{{items.runningAmount.toFixed(2)| number}}</td>
                        <td>{{items.receivingAmount.toFixed(2)| number}}</td>
                        <td>{{items.paidAmount.toFixed(2)| number}}</td>
                        <td>{{items.totalSO}}</td>
                        <td>{{items.soPendingApproval}}</td>
                        <td>{{items.totalPO}}</td>
                        <td>{{items.poPendingApproval}}</td>
                        <td>{{items.vendorCount}}</td> 
                        <td style="color: black;width: 45px; margin-top: 5px; margin-bottom: 5px;">
                            <button class="btn btn-primary" data-ng-style="isSelectedItem(items) ?
                        {'background-color' : selectedItem == item.departmentName ? '#85BF3F' : '#26a69a'} : {}"
                                    data-ng-click="showPOLevelDetail(items)">{{selectedItem == items.departmentName ? "HidePO's" : "ShowPO's"}}</button></td>
                        <td><button class="btn" data-ng-click="exportDataToSheet(items)">Download Item level Data</button></td>  
                    </tr>
                    <tr ng-if = "selectedItem == items.departmentName"  ng-repeat-end="" >
                        <td colspan="12">
                            <div
                                    class="col s12 grid"
                                    id="grid"
                                    ui-grid="gridOptions"
                                    ui-grid-save-state
                                    ui-grid-resize-columns
                                    ui-grid-move-columns
                                    ui-grid-exporter></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
            <hr>
            <div class="col s12" data-ng-if="poRequest.length==0" style="padding:30px;color:gray;font-size: 20px;">
                <div class="row margin0 center" ng-if="!showViewActions">
                    No Pending Orders found for Approval
                </div>
                <div class="row margin0 center" ng-if="showViewActions">
                    No Purchase Orders found for the selected criteria
                </div>
            </div>
            <div class="col s3 right" data-ng-if="poRequest.length>0">
                <button class="btn margin-top-20" data-ng-click="downloadExcell()" >Download Excell</button>
            </div>
            <div class="col s12" ng-if="poRequest.length>0">
                <div class="respTable standardView">
                <div class="row">
                    <ul class="collection striped center" >
                        <li class="collection-item list-head">
                            <div class="row" style="font-size:12px">
                                <div class="col s1">ID</div>
                                <div class="col s1">Receipt Number</div>
                                <div class="col s1">Generation Time</div>
                                <div class="col s1">Location Name</div>
                                <div class="col s1">Vendor name</div>
                                <div class="col s1">Delivery Date</div>
                                <div class="col s1">Bill Amount</div>
                                <div class="col s1">Paid Amount</div>
                                <div class="col s1">Status</div>
                                <div class="col s1">Expiry Date</div>
                                <div class="col s1">Lead Time</div>
                                <div class="col s1" data-ng-if="isPurchaser" align="center">Actions</div>
                            </div>
                        </li>
                        <li class="collection-item " style="padding:5px;" data-ng-repeat="poR in poRequest track by $index">
                            <div class="row margin0" data-ng-class="{'red white-text': poR.id==createdPO}" style="font-size:12px;">
                                <div class="col s1">{{poR.id}}</div>
                                <div class="col s1"><a href="#viewPODetails" data-ng-click="showDetails($index)" modal>{{poR.receiptNumber}}</a></div>
                                <div class="col s1">{{poR.generationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                                <div class="col s1">{{poR.dispatchLocation.city}}[{{poR.dispatchLocation.locationName}}]</div>
                                <div class="col s1">{{poR.generatedForVendor.name}}</div>
                                <div class="col s1">{{poR.fulfillmentDate | date :'dd-MM-yyyy'}}</div>
                                <div class="col s1">{{poR.billAmount.toFixed(2)}}</div>
                                <div class="col s1">{{poR.paidAmount.toFixed(2)}}</div>
                                <div class="col s1">{{poR.status}}</div>
                                <div class="col s1">
                                    <span data-ng-if="poR.status=='CREATED' || poR.expiryDate==null">-</span>
                                    {{poR.expiryDate | date :'dd-MM-yyyy @ h:mma'}}
                                </div>

                                <div class="col s1">
                                    <span  data-ng-if="poR.leadTime==null">-</span>
                                    {{poR.leadTime}}
                                </div>

                                <div class="col s1" data-ng-if="!showViewActions && poR.status=='CREATED'">
                                    <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                            a href="#viewPOApproveDetails" data-ng-click="showApproveDetails(poR.id,$index)" modal>Actions</button>

                                    <span data-ng-if="poR.status!='CREATED'">
                                        No Actions available for this PO
                                    </span>
                                </div>
                                <div class="col s1" data-ng-if="showViewActions && isPurchaser">

                                    <a class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                       href="#descriptionModal" data-toggle="modal"
                                       data-ng-if="((poR.status=='IN_PROGRESS' || poR.status=='APPROVED') && (poR.expiryStatus=='EXPIRED' || poR.expiryStatus=='UN_EXPIRED') && !poR.hideExtend)"
                                       data-ng-click="description(poR,$index)" modal>Extend</a>

                                    <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                            data-ng-if="(poR.status=='CREATED' || poR.status=='APPROVED')"
                                            data-ng-click="cancelPO(poR.id,$index,poR)" acl-action="VOMVPOC">Cancel</button>

                                    <button class="btn btn-medium vBtn"
                                            acl-action="HODGGRP"
                                            data-ng-if="poR.status=='PENDING_HOD_APPROVAL'"
                                            data-ng-click="openPoHodActionsModal(poR)">HOD Actions</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                            data-ng-if="poR.status=='IN_PROGRESS'"
                                            data-ng-click="closePO(poR.id,$index,poR)" acl-action="VOMVPOCL">Close</button>

                                    <span data-ng-if="!(poR.status=='CREATED' || poR.status=='IN_PROGRESS' || poR.status=='APPROVED'|| poR.status=='PENDING_HOD_APPROVAL')">
                                        No Actions available for this PO
                                    </span>
                                </div>
                            </div>
                            <div class="row bordered" data-ng-if="poR.adjustOrRefund">
                                <div class="advanceHeader">
                                    Advance : <span class="chip grey white-text">Total Advance Amount {{cancelObject.completePrAmount.toFixed(2)}}</span>
                                    <span class="chip green white-text">Available Amount {{cancelObject.completeAvailableAmount.toFixed(2)}}</span>
                                    <span class="chip red darken-1 white-text">Blocked {{cancelObject.completeBlockedAmount.toFixed(2)}}</span>
                                    <input type="button" class="btn red right" value="Close" data-ng-click="closeAdjustRefund(poR)">
                                </div>
                                <div class="row margin-top-10">
                                    <br>
                                    <div class="row">
                                        <div class="col s6">
                                            <label for="adjustOrRefund">Do You Want to get Refund Or Adjust Amount For Other PO's ?</label>
                                            <select id="adjustOrRefund" data-ng-model="cancelObject.adjustOrRefund" class="form-control"
                                                    data-ng-change="setAdjustOrRefund(cancelObject.adjustOrRefund)">
                                                <option value="Adjust">Adjust</option>
                                                <option value="Refund">Refund</option>
                                            </select>
                                        </div>
                                    </div>
                                    <br>
                                    <div class="row margin-top-10" data-ng-if="cancelObject.adjustOrRefund == 'Adjust'">
                                        <div data-ng-if="pendingPoSo.length == 0 && errorMessage == null" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">
                                            No Pending {{cancelObject.advanceType == "PO_ADVANCE" ? "Purchase orders" : "Service orders"}} Found to Adjust Advance Payment
                                        </div>
                                        <div data-ng-if="pendingPoSo.length == 0 && errorMessage != null" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">
                                            {{errorMessage}}
                                        </div>
                                        <div class="row" data-ng-if="pendingPoSo.length > 0">
                                            <input type="text" data-ng-model="enterredText" placeholder="Enter to Search" data-ng-change="setEnterredText(enterredText)">
                                        </div>
                                        <ul class="row" data-collapsible="accordion" watch data-ng-if="pendingPoSo.length > 0">
                                            <li class="row" data-ng-repeat="po in pendingPoSo | filter : enterredText">
                                                <div class="collapsible-header poNumber waves-effect waves-light lighten-5 s12">
                                                    <div class="col s5 left left-align" data-ng-click="$event.stopPropogation()">
                                                        <input id="RO-{{po.id}}" data-ng-model="po.checked" type="checkbox" data-ng-click="disablePosSelection($event,po,pendingPoSo)" ng-disabled="po.disable"/>
                                                        <label for="RO-{{po.id}}">{{po.receiptNumber}} created on {{po.generationTime | date:'dd/MM/yyyy @ h:mma'}} (PO ID : {{po.id}})</label>
                                                    </div>
                                                    <div class="col s2" style="display: inline-block;">
                                                        {{po.orderType}}
                                                    </div>
                                                    <div class="col s2" style="display: inline-block;" data-ng-if="po.type != null">
                                                        {{po.type}}
                                                    </div>
                                                    <div class="col s2" style="display: inline-block;">
                                                        Rs. {{po.paidAmount}}
                                                    </div>
                                                    <div class="right col s1">
                                                        <i class="fa fa-caret-down right"></i>
                                                        <span class="chip right" style="margin-top: 7px;">{{po.status}}</span>
                                                    </div>
                                                </div>
                                                <div class="respTable collapsible-body">
                                                    <table class="bordered striped row">
                                                        <thead>
                                                        <tr>
                                                            <th class="center-align">SKU</th>
                                                            <th class="center-align">UOM</th>
                                                            <th class="center-align">Packaging</th>
                                                            <th class="center-align">Packaging Qty</th>
                                                            <th class="center-align">Conversion Ratio</th>
                                                            <th class="center-align">Total Qty</th>
                                                            <th class="center-align">Price</th>
                                                            <th class="center-align" tooltipped data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*</th>
                                                            <th class="center-align">Taxes</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr data-ng-repeat="item in po.orderItems track by $index">
                                                            <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}}</a></td>
                                                            <td class="center-align">{{item.unitOfMeasure}}</td>
                                                            <td class="center-align">{{item.packagingName}}</td>
                                                            <td class="center-align">{{item.packagingQty}}</td>
                                                            <td class="center-align">{{item.conversionRatio}}</td>
                                                            <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                                                            <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                                            <td class="center-align">{{item.totalCost}}</td>
                                                            <td class="center-align">{{item.totalTax}}</td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="row" data-ng-if="poSoSelected != null">
                                        <input type="button" class="btn left" value="Submit" data-ng-click="submitAdjustmentRefund(poSoSelected,poR)" />
                                    </div>
                                    <div class="row" data-ng-if="cancelObject.adjustOrRefund == 'Refund'">
                                        <div class="row">
                                            <div class="col s6">
                                                <label>Select Refund date</label>
                                                <input input-date type="text" name="refundDate" id="refundDate"
                                                       ng-model="cancelObject.refundDate"
                                                       container="" format="yyyy-mm-dd"
                                                       min="{{minRefundDate}}"
                                                       data-ng-change="setRefundDate(cancelObject.refundDate)"/>
                                            </div>
                                            <div class="col s6" data-ng-if="cancelObject.refundDate != null" style="margin-top: 20px;">
                                                <input type="button" class="btn" value="Submit" data-ng-click="submitAdjustmentRefund(null,poR)" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                </div>
                <div class="TableMobileView">
                    <div class="row">
                        <ul class="collection striped center">
                            <li class="collection-item" data-ng-repeat="poR in poRequest track by $index"
                                data-ng-class="{'red white-text': poR.id==createdPO}">
                                <div class="row">
                                    <div class="col s1">ID</div>
                                    <div class="col s1">{{poR.id}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Receipt Number</div>
                                    <div class="col s1"><a href="#viewPODetails" data-ng-click="showDetails($index)"
                                                           modal>{{poR.receiptNumber}}</a></div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Generation Time</div>
                                    <div class="col s1">{{poR.generationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Location Name</div>
                                    <div class="col s1">{{poR.dispatchLocation.city}}[{{poR.dispatchLocation.locationName}}]</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Vendor name</div>
                                    <div class="col s1">{{poR.generatedForVendor.name}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Delivery Date</div>
                                    <div class="col s1">{{poR.fulfillmentDate | date :'dd-MM-yyyy'}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Bill Amount</div>
                                    <div class="col s1">{{poR.billAmount.toFixed(2)}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Paid Amount</div>
                                    <div class="col s1">{{poR.paidAmount.toFixed(2)}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Status</div>
                                    <div class="col s1">{{poR.status}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Expiry Date</div>
                                    <div class="col s1">
                                        <span data-ng-if="poR.status=='CREATED' || poR.expiryDate==null">-</span>
                                        {{poR.expiryDate | date :'dd-MM-yyyy @ h:mma'}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Lead Time</div>
                                    <div class="col s1">
                                        <span data-ng-if="poR.leadTime==null">-</span>
                                        {{poR.leadTime}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col s1" data-ng-if="isPurchaser" align="center">Actions</div>
                                    <div class="col s1" data-ng-if="!showViewActions && poR.status=='CREATED'">
                                        <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10" a
                                                href="#viewPOApproveDetails" data-ng-click="showApproveDetails(poR.id,$index)"
                                                modal>Actions</button>

                                        <span data-ng-if="poR.status!='CREATED'">
                            No Actions available for this PO
                        </span>
                                    </div>
                                    <div class="col s1" data-ng-if="showViewActions && isPurchaser">

                                        <a class="btn btn-xs-small vBtn margin-right-5 margin-top-10" href="#descriptionModal"
                                           data-toggle="modal"
                                           data-ng-if="((poR.status=='IN_PROGRESS' || poR.status=='APPROVED') && (poR.expiryStatus=='EXPIRED' || poR.expiryStatus=='UN_EXPIRED') && !poR.hideExtend)"
                                           data-ng-click="description(poR,$index)" modal>Extend</a>

                                        <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                                data-ng-if="(poR.status=='CREATED' || poR.status=='APPROVED')"
                                                data-ng-click="cancelPO(poR.id,$index,poR)" acl-action="VOMVPOC">Cancel</button>

                                        <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                                data-ng-if="poR.status=='IN_PROGRESS'" data-ng-click="closePO(poR.id,$index,poR)"
                                                acl-action="VOMVPOCL">Close</button>

                                        <span
                                                data-ng-if="!(poR.status=='CREATED' || poR.status=='IN_PROGRESS' || poR.status=='APPROVED')">
                            No Actions available for this PO
                        </span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Modal Structure -->
<div id="viewPODetails" class="modal modal-large">
    <div class="modal-content">
        <div class="row">
            <h5>
                Purchase Order Number: <b>{{selectedPO.receiptNumber}}</b>
                <button data-ng-if="selectedPO.poInvoice!=null" class="btn right" data-ng-click="downloadPO(selectedPO.poInvoice)"
                        data-tooltip="Download PO" tooltipped>
                    <i class="fa fa-download"></i>
                </button>
            </h5>
            <p><b>Created for:</b> {{selectedPO.generatedForVendor.name}} [{{selectedPO.dispatchLocation.city}}]</p>
            <p><b>GSTIN:</b> {{selectedPO.dispatchLocation.gstStatus=="REGISTERED"
                ? selectedPO.dispatchLocation.gstin : selectedPO.dispatchLocation.gstStatus}}</p>
        </div>
        <div class="row margin0 standardView">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th class="center-align">SKU</th>
                    <th class="center-align">Unit Of Measure</th>
                    <th class="center-align">Packaging</th>
                    <th class="center-align">Packaging Qty</th>
                    <th class="center-align">Pending Qty</th>
                    <th class="center-align">Total Qty</th>
                    <th class="center-align">Price</th>
                    <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                    <th class="center-align">Taxes</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="item in selectedPO.orderItems track by $index">
                    <td class="center-align">{{item.skuName}}</td>
                    <td class="center-align">{{item.unitOfMeasure}}</td>
                    <td class="center-align">{{item.packagingName}}</td>
                    <td class="center-align">{{item.packagingQty}}</td>
                    <td class="center-align">{{item.requestedQuantity.toFixed(2) - item.receivedQuantity.toFixed(2)}}</td>
                    <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                    <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                    <td class="center-align">{{item.totalCost}}</td>
                    <td class="center-align">{{item.totalTax}}</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="TableMobileView">
            <ul class="collection striped center">
                <li class="collection-item" data-ng-repeat="item in selectedPO.orderItems track by $index">
                    <div class="row">
                        <div class="col">SKU</div>
                        <div class="col">{{item.skuName}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Unit Of Measure</div>
                        <div class="col">{{item.unitOfMeasure}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Packaging</div>
                        <div class="col">{{item.packagingName}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Packaging Qty</div>
                        <div class="col">{{item.packagingQty}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Pending Qty</div>
                        <div class="col">{{item.requestedQuantity.toFixed(2) - item.receivedQuantity.toFixed(2)}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Total Qty</div>
                        <div class="col">{{item.requestedQuantity.toFixed(2)}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Price</div>
                        <div class="col">{{item.unitPrice.toFixed(2)}}</div>
                    </div>
                    <div class="row">
                        <div class="col" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</div>
                        <div class="col">{{item.totalCost}}</div>

                    </div>
                    <div class="row">
                        <div class="col">Taxes</div>
                        <div class="col">{{item.totalTax}}</div>
                    </div>
                    </tr>
                </li>
            </ul>
        </div>

        <div class="row right-align" style="line-height: 30px;">
            <b>Taxable Amount:</b> Rs.{{selectedPO.billAmount}}<br/>
            <b>Total Taxes:</b> Rs.{{selectedPO.totalTaxes}}<br/>
            <b>Paid Amount:</b> Rs.{{selectedPO.paidAmount}}
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
    </div>
</div>


<!--Modal Approve Structure-->
<div id="viewPOApproveDetails" class="modal modal-large" >
    <div class="modal-content">
        <div class="row">
            <h5>
                Purchase Order Number: <b>{{selectedPOR.receiptNumber}}</b>
                <button data-ng-if="selectedPOR.poInvoice!=null" class="btn right" data-ng-click="downloadPO(selectedPOR.poInvoice)"
                        data-tooltip="Download PO" tooltipped>
                    <i class="fa fa-download"></i>
                </button>
            </h5>
            <p><b>Created for:</b> {{selectedPOR.generatedForVendor.name}} [{{selectedPOR.dispatchLocation.city}}]</p>
            <p><b>GSTIN:</b> {{selectedPOR.dispatchLocation.gstStatus=="REGISTERED"
                ? selectedPOR.dispatchLocation.gstin : selectedPOR.dispatchLocation.gstStatus}}</p>
        </div>
        <div class="row margin0">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th class="center-align">SKU</th>
                    <th class="center-align">Unit Of Measure</th>
                    <th class="center-align">Packaging</th>
                    <th class="center-align">Packaging Qty</th>
                    <th class="center-align">Pending Qty</th>
                    <th class="center-align">Total Qty</th>
                    <th class="center-align">Price</th>
                    <th class="center-align">Price History</th>
                    <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                    <th class="center-align">Taxes</th>
                    <th class="center-align">Action</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="(id,item) in selectedPOR.orderItems track by $index">
                    <td class="center-align"><a data-ng-model="selectedSku" data-ng-click="openViewChart(item)">{{item.skuName}}</a></td>
                    <td class="center-align">{{item.unitOfMeasure}}</td>
                    <td class="center-align">{{item.packagingName}}</td>
                    <td class="center-align" data-ng-if="!item.edit">{{item.packagingQty}}</td>
                    <td class="center-align" data-ng-if="item.edit">
                        <input style="width: 120px;" type="number" ng-model="item.packagingQty">

                        <span style="color: red;" data-ng-if="item.packagingQty < 0">Enter a value greater than or equal to 0</span>
                    </td>
                    <td class="center-align">{{item.requestedQuantity.toFixed(2) - item.receivedQuantity.toFixed(2)}}</td>
                    <td class="center-align" >{{item.requestedQuantity.toFixed(2)}}</td>
                    <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                    <td class="center-align">
                        <div class="image-container">
                            <img src="img/priceHistory.svg"
                                 width="50" height="40" ng-mouseover="showCharts(true); createCharts();" ng-mouseleave="showCharts(false)">
                            <div class="popover" ng-show="showChart">
                                <div id="myChart-{{$index}}"></div>
                            </div>
                        </div>
                    </td>
                    <td class="center-align">{{item.totalCost}}</td>
                    <td class="center-align">{{item.totalTax}}</td>
                    <td class="center-align" data-ng-if="!item.edit"><button data-ng-show="!item.edit" ng-click="editItem(item , id , true)" tooltipped data-tooltip="Edit Packaging Quantity"
                                                     class="btn btn-floating green"><i class="material-icons">edit</i></button></td>
                    <td class="center-align" data-ng-if="item.edit"><img src="img/check-circle-solid.svg" data-ng-show="item.edit"
                                                  width="50" height="40" data-ng-click="editItem(item , id , false)"></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="row right-align" style="line-height: 30px;">
            <b>Taxable Amount:</b> Rs.{{selectedPOR.billAmount}}<br/>
            <b>Total Taxes:</b> Rs.{{selectedPOR.totalTaxes}}<br/>
            <b>Paid Amount:</b> Rs.{{selectedPOR.paidAmount}}
        </div>
        <div class="col s1" style="margin-bottom: 2rem">
            <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10 modal-action " data-ng-if="selectedPOR.billAmount<=20000"
                    acl-action="VAPOL1" data-ng-click="getApprove(selectedpoRId,seletedpoRIndex)">ApproveL1</button>
            <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10 modal-action " data-ng-if="selectedPOR.billAmount>20000 && selectedPOR.billAmount<=100000"
                    acl-action="VAPOL2" data-ng-click="getApprove(selectedpoRId,seletedpoRIndex)">ApproveL2</button>
            <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10 modal-action " data-ng-if="selectedPOR.billAmount>100000"
                    acl-action="VAPOL3" data-ng-click="getApprove(selectedpoRId,seletedpoRIndex)">ApproveL3</button>
            <button class="btn btn-xs-small margin-top-10 modal-action"
                    data-ng-click="showPriceHistoryGrid()">Price History</button>
            <div data-ng-if = "showGrid">
                <div
                        class="grid"
                        style="width:800px"
                        id="grid"
                        ui-grid="gridOptions"
                        ui-grid-save-state
                        ui-grid-resize-columns
                        ui-grid-move-columns
                        ui-grid-pinning
                        ui-grid-exporter>

                </div>
            </div>
            <button class="btn btn-xs-small vBtn margin-top-10 modal-action modal-close"
                    style="margin-left:20px; !important"
                    data-ng-click="getReject(selectedpoRId,seletedpoRIndex)">Reject</button>
        </div>
    
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat" data-ng-click="closeModal()">Close</button>
    </div>
</div>
<!-- Modal description Structure -->


<div class="modal fade" id="descriptionModal" role="dialog" tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">


            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel_description"> Provide Extension Description! </h4>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row">
                            <div>
                                <label>Select Reason</label>
                                <select data-ng-model="selectedReason" data-placeholder="Select Reason">
                                    <option value=""></option>
                                    <option ng-repeat="reason in availableReasons">{{reason}}</option>
                                </select>
                            </div>
                        </div>
                        <div class="row" id="dialog">
                            <div>
                                <label>Add Extension Description</label>
                                <input class="form-control" type="text" maxlength="200"
                                       data-ng-model="extensionDescription" placeholder="Add Description"
                                       required/>
                            </div>
                        </div>

                        <div class="row margin0" style="margin-top:5px;">
                            <label style="display: inline-block;margin-bottom:0px;" for="inputCreated">Select Extend date:</label>
                            <input style="max-width: 200px;" input-date type="text" name="created" id="inputCreated"
                                   data-ng-model="extendedDate" data-ng-change="changeSelectedDate(extendedDate)"
                                   container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                                   max="{{maxDate}}" />
                        </div>


                        <div class="row">
                        <div>
                        <button class=" modal-action modal-close btn btn-xs-small vBtn margin-right-5 margin-top-10" style="font-size: 15px; height: 30px; margin-top: 50px"
                                data-ng-click="extendPO(selectedpoR.id,selectedpoR.generatedForVendor.id,extensionDescription,selectedReason)">Submit</button>
                        </div>
                        <div class="modal-footer">
                            <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
                        </div>
                        <div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>

<script type="text/ng-template" id="consumptionApprove.html">
    <div class="row" data-ng-init="initChart()">
        <div class="col s12">
            <hc-chart options="chartOptions">Placeholder for generic chart</hc-chart>
        </div>
        <div class="col s12"><b>Stock At Hand:</b> {{sku.currentStock}} {{sku.skuData.uom}}</div>
        <div data-ng-if="pendingForSku != null" class="col s12">
            <span><b>Open POs:</b> {{(pendingForSku.poList!=null && pendingForSku.poList.length>0) ? pendingForSku.poList.join(", ") : 'None'}}</span><br/>
            <span><b>Pending Quantity:</b> {{(pendingForSku.requested - pendingForSku.received).toFixed(2)}}</span>
        </div>
    </div>
</script>

<script type="text/ng-template" id="hodPoActionsModal.html" class="modal-large">
    <div class="modal-content" style="overflow-x: auto; max-height: 350px;" data-ng-init="initHodActionsModal()">
        <div class="row" style="background-color: yellow">
            <h5>Request For {{lastStatus == 'APPROVED' ? 'Cancellation' : 'Closing'}} of Purchase Order</b></h5>
        </div>
        <div class="row" style="width:98%;">
            <div class="col s6">
                <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                    <p class="center"><b><u>Advance Details</u></b></p>
                    <p data-ng-if="isAdjusted"><b>Adjusted With {{so.vendorAdvancePayments[0].advanceType == 'SO_ADVANCE' ? 'SO' : 'PO'}} :</b> {{selectedSoPo}}</p>
                    <p data-ng-if="isAdjusted"><b>Adjusted Amount :</b> {{amount}}</p>
                    <p data-ng-if="!isAdjusted"><b>Refund Date :</b> {{refundSelectedDate}}</p>
                    <p data-ng-if="!isAdjusted"><b>Refund Amount :</b> {{amount}}</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="waves-effect waves-green btn-flat red left" data-ng-click="approveRejectAdjustmentRefund('REJECTED')">Reject</button>
            <button class="waves-effect waves-green btn right" data-ng-click="approveRejectAdjustmentRefund('APPROVED')">Approve</button>
        </div>
    </div>
</script>
