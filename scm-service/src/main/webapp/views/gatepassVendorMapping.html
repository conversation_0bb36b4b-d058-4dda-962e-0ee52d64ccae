<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	<div class="col s12">
		<div class="row">
			<div class="col s12">
				<h3>Gatepass Vendor Mapping</h3>
			</div>
		</div>
		<div class="row">
			<div class="col s12  m6 l6">
				<label>Select Unit</label> <select data-ui-select2
					data-ng-model="selectedUnitData"
					data-placeholder="Enter name of Unit"
					data-ng-change="changeUnit(selectedUnitData)">
					<option value=""></option>
					<option data-ng-repeat="unit in unitList" value="{{unit}}">{{unit.name}}</option>
				</select>
			</div>
			<div class="col s12 m6 l6">
				<label>Operation Type</label> <select
					data-ng-model="vendorMapping.operationType"
					data-ng-change="getOperationVendorList(vendorMapping.operationType)"
					data-ng-options="ops.name as ops.label  for ops in operationList"
					required="required"></select>
			</div>
		</div>

	</div>
	<div class="row" data-ng-show="selectedUnit!=null">
		<div class="col s12">
			<button class="btn" data-ng-click="getMappedVendors()">Get
				Vendors</button>
			<button class="btn" data-ng-click="addNewForm(true)"
				acl-action="SMUVMCAD">Add new vendor</button>
		</div>
	</div>

	<div class="row">
		<div class="col s12">
			<ul class="collection striped" data-ng-show="mappedVendors.length>0">
				<li class="collection-item list-head">
					<div class="row">
						<div class="col s2">Name</div>
						<div class="col s2">Operation Type</div>
						<div class="col s2">Unit Name</div>
						<div class="col s2">Status</div>
						<div class="col s4">Action</div>
					</div>
				</li>
				<li class="collection-item clickable"
					data-ng-repeat="vd in mappedVendors track by $index">
					<div class="row" style="margin-bottom: 0px;">
						<div class="col s2">{{vd.vendor.entityName}}</div>
						<div class="col s2">{{vd.operationType}}</div>
						<div class="col s2">{{vd.unit.name}}</div>
						<div class="col s2">{{vd.status}}</div>
						<div class="col s4">
							<button class="btn" data-ng-if="vd.status == 'ACTIVE'"
								data-ng-click="deactivateMapping(vd.id)">Deactivate</button>
							<button class="btn" data-ng-if="vd.status == 'IN_ACTIVE'"
								data-ng-click="activateMapping(vd.id)">Activate</button>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>

	<div class="row" data-ng-show="selectedUnit!=null">
		<div class="col s12">
			<div data-ng-if="showVendorAddForm" style="margin-top: 30px;">
				<div class="row">
					<div class="col s12">
						<label>Select Vendor</label> <select data-ui-select2
							data-ng-model="selectedVendor"
							data-placeholder="Enter Name Of Vendor"
							data-ng-change="selectVendor(selectedVendor)">
							<option value=""></option>
							<option
								data-ng-repeat="vendor in vendorList track by vendor.vendorId"
								value="{{vendor}}">{{vendor.entityName}}</option>
						</select>
					</div>
				</div>
				<button class="btn" data-ng-click="addMapping()"
					acl-action="SMUVMCAD" data-ng-if="editType=='Add'">Add
					Vendor Details</button>
				<!-- <button class="btn" data-ng-click="updateMapping()"
					acl-action="SMUVMCAD" data-ng-if="editType=='Update'">Update
					Vendor Details</button> -->
				<button class="btn red right" data-ng-click="cancelMapping()"
					acl-action="SMUVMCAD">Cancel</button>
			</div>
		</div>
	</div>
</div>
