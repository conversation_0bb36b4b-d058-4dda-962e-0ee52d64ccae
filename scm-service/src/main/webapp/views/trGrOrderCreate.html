<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s10">
                <h2 class="left">Create Specialized Transfer And GR</h2>
            </div>
            <div class="col s2" data-ng-hide="showPendingRequest">
                <input type="button" data-ng-click="showPendingRequest=true;" class="btn" value="Back"/>
            </div>
        </div>
        <div class="row" data-ng-hide="requestOrderDetail != null" data-ng-show="showPendingRequest">
            <div class="col s9">
                <label>Select fulfillment date</label>
                <input input-date type="text" ng-model="fulfillmentDate"
                       container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                       max="{{maxDate}}"/>
            </div>
            <div class="col s3 margin-top-10">
                <button class="btn btn-small"
                        ng-click="getPendingRequestOrders(fulfillmentDate)" style="margin-top: 14px;"
                        acl-action="TRNCTV">Get Orders
                </button>
            </div>
        </div>
        <hr>
    </div>
    <div class="row">
        <div class="col s12" data-ng-show="showPendingRequest">
            <ul class="collection menuItemList" data-ng-hide="showMessage">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s2">Id</div>
                        <div class="col s2">Request Unit</div>
                        <div class="col s2">Generated By</div>
                        <div class="col s2">Update time</div>
                        <div class="col s2">Fulfillment Date</div>
                        <div class="col s2">Actions</div>
                    </div>
                </li>
                <li class="collection-item z-depth-1 clickable"
                    data-ng-repeat="request in requestOrderList track by request.id">
                    <div class="row">
                        <div class="col s2">{{request.id}}</div>
                        <div class="col s2">{{request.requestUnit.name}}</div>
                        <div class="col s2">{{request.generatedBy.name}}</div>
                        <div class="col s2">{{request.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                        <div class="col s2">{{request.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                        <div class="col s2">
                            <button class="btn btn-small" data-ng-click="selectOpenRo(request,true)"
                                    acl-action="TRNCTA">
                                Create TO
                            </button>
                            <span style="color: red;font-weight: bold;" data-ng-show="request.specialOrder==true">(Special)</span>
                        </div>
                    </div>
                </li>
            </ul>
            <div data-ng-show="showMessage">No pending requests found.</div>
        </div>
        <div class="col s12" data-ng-hide="showPendingRequest" data-ng-if="selectedRO.roType != 'FIXED_ASSET'">
            <form name="trForm" novalidate>
                <div class="row">
                    <div data-ng-if="requestOrderDetail.id!=null" class="col s1"><label>Request
                        Id:</label>{{requestOrderDetail.id}}
                    </div>
                    <div class="col s3"><label>Requesting Unit:</label>{{requestOrderDetail.requestUnit.name}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.generatedBy)" class="col s2"><label>Requested
                        By:</label>{{requestOrderDetail.generatedBy.name}}
                    </div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.generationTime)" class="col s2"><label>Generation
                        time:</label>{{requestOrderDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}
                    </div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.lastUpdateTime)" class="col s2"><label>Last
                        Updated:</label>{{requestOrderDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}
                    </div>
                    <div class="col s2"><label>Fulfillment
                        Date:</label>{{requestOrderDetail.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}
                    </div>
                </div>
                <div class="row" data-ng-if="!isEmpty(requestOrderDetail.comment)">
                    <div class="col s12"><label>Comment:</label>{{requestOrderDetail.comment}}</div>
                </div>
                <ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
                    <li class="collection-item list-head">
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s2">Product Name</div>
                            <div class="col s2">Requested Quantity</div>
                            <div class="col s2">Requested Absolute Quantity</div>
                            <div class="col s2">Unit Of Measure</div>
                            <div class="col s2">Vendor</div>
                        </div>
                    </li>
                    <li style="margin-bottom: 10px;border:#ddd 1px solid;border-left:0px;border-right:0px;"
                        data-ng-repeat="item in requestOrderDetail.requestOrderItems | orderBy: 'productId' track by $index"
                        data-ng-class="{multiSkuColor:item.skuList.length>1}">
                        <div class="row" style="padding: 10px; background: #d8d8d8;border-bottom: #ddd 1px solid;"
                             data-ng-class="{redBg:item.transferredQuantity!=item.requestedQuantity}">
                            <div class="col s2"><a
                                    data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a>
                            </div>
                            <div class="col s2">{{item.requestedQuantity}}</div>
                            <div class="col s2">{{item.requestedAbsoluteQuantity}}</div>
                            <div class="col s2">{{item.unitOfMeasure}}</div>
                            <div class="col s2"><label class="badge">{{item.vendor.name}} {{item.wtf}}</label></div>
                        </div>
                        <div class="row">
                            <div class="col s5">
                                <label> SKU :</label>
                                <select data-ng-model="item.selectedSku"
                                        data-ng-change="onSkuChanged(item)"
                                        data-ng-options="sku as sku.skuName for sku in item.skuList | filter : bySKUs track by sku.skuId">
                                </select>
                            </div>
                            <div class="col s5">
                                <label>Packaging:</label>
                                {{item.selectedSku.skuPackagings[0].packagingDefinition.packagingName}}
                            </div>
                        </div>
                        <div class="row" data-ng-repeat="trItem in item.trPackaging track by $index"
                             style="margin-bottom: 0;">
                            <div class="col s12">
                                <div class="row" data-ng-repeat="pgd in trItem.packagingDetails track by $index"
                                     style="background:rgba(0,0,0,0.03); padding:7px; margin-bottom: 10px;">
                                    <div style="margin:-7px;padding:7px;background:#ddd;margin-bottom: 10px;">
                                        {{pgd.packagingDefinitionData.packagingName}}
                                    </div>
                                    <div class="row">
                                        <div class="col s2">
                                            <label>Units Packed:</label>
                                            <div>
                                                <input type="number" name="pgd.numberOfUnitsPacked"
                                                       data-ng-model="pgd.numberOfUnitsPacked"
                                                       ng-change="updatePackagingQty(pgd,trItem,item)" required/>
                                            </div>
                                        </div>
                                        <div class="col s2"><label>Transferred Qty:</label>
                                            {{pgd.transferredQuantity}}
                                        </div>
                                        <div class="col s2"><label>Received Qty:</label>
                                            {{pgd.numberOfUnitsReceived * pgd.packagingDefinitionData.conversionRatio}}
                                        </div>
                                        <div class="col s2"><label>Unit Of Measure:</label>
                                            {{pgd.packagingDefinitionData.unitOfMeasure}}
                                        </div>
                                        <div class="col s3" style="margin-top: 10px">
                                            <button class="btn btn-medium "
                                                    data-ng-style="item.enableRejectionInput ? {}:{'color':'#fff','background-color':'#d9534f','border-color':'#d43f3a'}"
                                                    data-ng-click="onReject(trItem,$index, item,pgd)">
                                                {{item.enableRejectionInput ? "Undo Reject" : "Reject"}}
                                            </button>
                                        </div>

                                    </div>
                                    <div class="row" data-ng-show="item.enableRejectionInput">
                                        <div class="col s3">
                                            <label>Units Rejected:</label>
                                            <input type="number" data-ng-model="pgd.numberOfUnitsRejected"
                                                   data-ng-change="enterNumberOfRejectedUnits(trItem,pgd.numberOfUnitsRejected,pgd)"
                                                   value="pgd.numberOfUnitsRejected"/>
                                        </div>
                                        <div class="col s3"><label>Reason:</label>
                                            <div>
                                                <select data-ng-model="pgd.rejectionReason"
                                                        data-ng-init="pgd.rejectionReason='Less Quantity'">
                                                    <option value="Less Quantity">Less Quantity</option>
                                                    <option value="Damaged">Damaged</option>
                                                    <option value="Wrong">Wrong</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col s3" >
                                            <label>Proof Of Rejection:</label>
                                            <input type="file" class="btn" id="multi-por-images_{{item.productId}}"  file-input="filesInput"
                                                   name="files" multiple><br><br>
                                        </div>
                                    </div>
                                    <p ng-show="pgd.numberOfUnitsPacked<pgd.numberOfUnitsRejected" class="errorMessage">
                                        Number Of Units Rejected Cannot be Greater than Units Packed </p>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
                <div class="row">
                    <div class="col s12 form-element">
                        <label>Comment(optional):</label>
                        <textarea data-ng-model="comment"></textarea>
                    </div>
                </div>
                <div class="row">
                    <input type="button" data-ng-disabled="submitted"
                           data-ng-click="createTransferOrderObject()" class="btn right"
                           value="Submit Transfer And Settle GR" acl-action="TRNCTA"/>
                </div>
            </form>
        </div>
    </div>
</div>

