<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.grid {
	height: 500px;
	padding: 0 !important;
}

.select2.select2-container {
	width: auto !important;
}
</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	<div class="col s12">
		<div class="row">
			<div class="mappingBtn" style="margin-top: 10px; font-size: 40px;">
				Add Category Definition</div>
		</div>
	</div>
            <div class="row">
                <div class="col s12 m6 l6">
                    <div class="scm-form">
                        <label class=" black-text" for="categoryName">Category Name</label>
                        <input id="categoryName" name="categoryName"  type="text" ng-maxlength="100" required>
                    </div>
                    <div class="scm-form" style="margin-top:30px">
                        <label class=" black-text" for="categoryDescription">Category Description</label>
                        <input id="categoryDescription" name="categoryDescription"  type="text" ng-maxlength="100" required>
                    </div>
                    </div>
                    <div class="col s12 m6 l6">
                    <div class="scm-form" >
                        <label class=" black-text" for="categoryCode">Category Code</label>
                        <input id="categoryCode" name="categoryCode"  type="text" ng-maxlength="100" required>
                    </div>
                    <div class="scm-form" style="margin-top:30px">
                        <label class=" black-text" for="productName">Status</label>
                        <select ng-model="status"
								data-ng-options="period as period for period in periods"></select>
                    </div>
                    </div>
                    
                    <div class="row">
		<div class="col s12" style="margin-top: 16px; margin-left: 11px;">
			<button class="btn btn-primary" type="button"
				ng-click="searchMappingShow()">Submit</button>
		</div>
	</div>
                    </div>
	
</div>


