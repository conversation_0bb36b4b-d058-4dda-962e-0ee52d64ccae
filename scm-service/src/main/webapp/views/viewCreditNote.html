<style>
    .chip {
        font-size: 11px;
    }

    .btn i {
        font-size: 1rem !important;
    }

    .btn.btn-xs-small.vBtn {
        width: 70px !important;
        font-size: 10px;
        padding: 5px !important;
        height: 40px;
        line-height: 16px;
        text-align: center;
    }

</style>
<div class="row" data-ng-init="init()">
    <div class="col s12 white z-depth-3">
        <div class="row ">
            <div class="col s12">
                <h4>View Credit Note</h4>
            </div>
        </div>
        <div class="row">
            <div class="col s3">
                <label>Start date</label>
                <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd"/>
            </div>
            <div class="col s3">
                <label>End date</label>
                <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd"/>
            </div>
            <div class="col s3">
                <label>Select Vendor</label>
                <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}"
                        ng-options="vendor as vendor.entityName for vendor in vendors"
                        data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
            </div>
            <div class="col s3">
                <label>Status</label>
                <select id="statusList"
                        ng-options="status as status for status in statusList"
                        data-ng-change="selectStatus(statusSelected)" data-ng-model="statusSelected"></select>
            </div>
        </div>
        <div class="row">
            <button class="btn margin-top-20" data-ng-click="getCreditDebitNoteDetails()">Find</button>
        </div>
        <div class="col s12">
            <ul class="collection striped " data-ng-show="creditDebitNoteDetails.length>0">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s1">Id</div>
                        <div class="col s2">Invoices</div>
                        <div class="col s2">Status</div>
                        <div class="col s2">Generation Time</div>
                        <div class="col s1">Created By</div>
                        <div class="col s2">Actions</div>
                    </div>
                </li>
                <li class="collection-item clickable" data-ng-repeat="cdnd in creditDebitNoteDetails">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s1">{{cdnd.id}}</div>
                        <div class="col s2">{{cdnd.invoiceId?cdnd.invoiceId:"-"}}</div>
                        <div class="col s2">{{cdnd.status}}</div>
                        <div class="col s2">{{cdnd.generationTime | date:'yyyy-MM-dd'}}</div>
                        <div class="col s1">{{cdnd.createdBy}}</div>
                        <div class="col s2">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                                   value="VIEW" data-ng-click="getDetails(cdnd.id)">

                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                                   data-ng-if="cdnd.status=='PENDING_APPROVAL_L1'"
                                   acl-action="VICNPL1"
                                   value="PENDING_APPROVAL_L1" data-ng-click="approveCreditNote(cdnd)">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                                   data-ng-if="cdnd.status=='PENDING_APPROVAL_L1'"
                                   acl-action="VICNPL1"
                                   value="REJECT" data-ng-click="rejectCreditNote(cdnd)">

                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                                   data-ng-if="cdnd.status=='PENDING_APPROVAL_L2'"
                                   acl-action="VICNPL2"
                                   value="PENDING_APPROVAL_L2" data-ng-click="approveCreditNote(cdnd)">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                                   data-ng-if="cdnd.status=='PENDING_APPROVAL_L2'"
                                   acl-action="VICNPL2"
                                   value="REJECT" data-ng-click="rejectCreditNote(cdnd)">

                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                                   value="VIEW INVOICE" data-ng-click="viewInvoice(cdnd)">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                                   data-ng-if="cdnd.status=='CLOSED'"
                                   value="CREDIT NOTE" data-ng-click="downloadNote(cdnd)">
                        </div>
                    </div>

                </li>
            </ul>
        </div>

    </div>
</div>
<script type="text/ng-template" id="creditNoteDetailModal.html">
    <div class="modal-header " data-ng-init="init()">
        <h4 class="modal-title"  style="margin-top: 0px;">Credit Note Detail</h4>
        <hr>
    </div>
    <div class="row">
        <div class="col s4">
            <b>Id :</b> {{creditNoteDetail.id}}
        </div>
        <div class="col s6">
            <b>Generation date :</b> {{creditNoteDetail.generationTime | date:'yyyy-MM-dd'}}
        </div>
    </div>
    <div class="row">
        <div class="col s4">
            <b>Invoice Id :</b> {{creditNoteDetail.invoiceId}}
        </div>
        <div class="col s4">
            <b>Total Tax :</b>{{creditNoteDetail.totalTax}}
        </div>
        <div class="col s4">
            <b>Total Amount :</b>{{creditNoteDetail.totalAmount}}
        </div>
    </div>

    <ul class="collection">
        <li class="collection-item list-head">
            <div class="row">
                <div class="col s1">Item Id</div>
                <div class="col s6">Item Desc</div>
                <div class="col s1">Qty</div>
                <div class="col s1">Price</div>
                <div class="col s1">Tax Percent</div>
                <div class="col s1">Tax Amount</div>
                <div class="col s1">Total Amount</div>
            </div>
        </li>

        <li class="collection-item" data-ng-repeat="item in creditNoteDetail.itemDetails " >
            <div class="row" style="margin-bottom: 0;">
                <div class="col s1">{{item.itemId}}</div>
                <div class="col s6">{{item.itemDesc}}</div>
                <div class="col s1">{{item.qty}}</div>
                <div class="col s1">{{item.price}}</div>
                <div class="col s1">{{item.taxPercent}}</div>
                <div class="col s1">{{item.taxAmount}}</div>
                <div class="col s1">{{item.totalAmount}}</div>
            </div>
        </li>
    </ul>
    <hr>
    <div class="modal-footer">
        <button class="btn red left" style="margin-right: 20px;" data-ng-click="closeModal()">Close</button>
    </div>

</script>

