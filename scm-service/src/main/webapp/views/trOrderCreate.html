<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .popeye-modal{
        width: 80% !important;
    }
    .table-header {
	display:flex;
	width:100%;

	padding:(12px * 1.5) 0;
}
.table-row {
	display:flex;
	width:100%;
	padding:(12px * 1.5) 0;


}
.table-data, .header__item {
	flex: 1 1 20%;
	text-align:center;
}
.header__item {
	text-transform:uppercase;
}
 .skuContainer{

    align-items: center;
    width:100% ;
    flex:1 ;
    justify-content:space-evenly;



      font-family: 'Montserrat', sans-serif;

    }
    .skuItem{
        flex:1 ;
        padding-left: 25px;
        padding-right: 20px ;
        padding-top: 2px ;
        padding-bottom: 8px ;
        text-align: center;
        margin: auto;
        text-shadow: 1px 1px 0 #FFF;
        align-items: center;
        justify-content: center;
    }


</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()" >
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3 class="left">Create Transfer Order {{selectedRO.roType}} {{selectedRO.count}}</h3>
                <input type="button" data-ng-click="backTransfer()" class="btn right" value="Back" data-ng-hide="showPendingRequest" style="margin-top: 35px;" />
            </div>
        </div>
        <div class="row" data-ng-hide="requestOrderDetail != null">
            <div class="col s9">
                <label for="inputCreated">Select fulfillment date</label>
                <input input-date type="text" name="created" id="inputCreated" ng-model="selectedDate"
                       data-ng-change="reset()"
                       container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                       max="{{maxDate}}" />
            </div>
            <div class="col s3 margin-top-10">
                <button class="btn btn-small" ng-click="getPendingRequestOrders(selectedDate)" style="margin-top: 14px;" acl-action="TRNCTV">Get Orders</button>
            </div>
        </div>
        <hr>
    </div>


    <div class="row">
        <div class="col s12" data-ng-show="showPendingRequest" data-ng-if="!isEmpty(clubbedROs) && showClubROBtn">
            <button class="btn pull-right" data-ng-click="clubAllROs(arrayAfterClubbing)">Club & Create TO</button>
        </div>
        <div data-ng-show="showPendingRequest && pendingList.length>0" >
            <div data-ng-show="togglePending == true" class="center">
                <h5 >{{togglePending ? "Pending Fixed Asset Event" : ""}}</h5>
            </div>
            <li class="collection-item z-depth-1 clickable"
                data-ng-repeat="request in pendingList track by request.id">
                <div class="row" data-ng-if="togglePending == true">
                    <div class="col s2">
                        <input id="RO-{{request.id}}"
                               ng-disabled="!((selectedRO.roType == null)
                                            || selectedRO.count == 0
                                            || (selectedRO.roType == 'FIXED_ASSET' && request.assetOrder)
                                            || (selectedRO.roType == 'REGULAR' && !request.assetOrder))"
                               data-ng-hide="requestOrderList.length<2"
                               data-ng-if="request.requestUnit.id!=request.fulfillmentUnit.id"
                               data-ng-model="request.checked"
                               data-ng-change="selectOpenRo(request,false)" type="checkbox"/>
                        <label for="RO-{{request.id}}">{{request.id}}</label>
                    </div>

                    <div class="col s2">{{request.requestUnit.name}}</div>
                    <div class="col s2">{{request.generatedBy.name}}</div><!--
                        <div class="col s2">{{request.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>-->
                    <div class="col s2">{{request.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                    <div class="col s2">{{request.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                    <div class="col s2">
                        <span style="color: red;font-weight: bold;" data-ng-show="request.specialOrder==true">(Special)</span></div>
                    <button class="btn btn-small" data-ng-click="selectOpenRo(request,true)" acl-action="TRNCTA">Create TO</button>
                </div>
                <div class="right">
                    <input id ="faPending"  data-ng-model="togglePending"   type="checkbox"/>
                    <label for="faPending">Fetch Pending Events </label>
                </div>
            </li>
        </div>
        <div class="col s12" data-ng-show="showPendingRequest">
            <ul class="collection menuItemList" data-ng-hide="showMessage">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s2">Id</div>
                        <div class="col s2">Request Unit</div>
                        <div class="col s2">Generated By</div>
                        <div class="col s2">Update time</div>
                        <div class="col s2">Fulfillment Date</div>
                        <div class="col s2">Actions</div>
                    </div>
                </li>
                <li class="collection-item z-depth-1 clickable"  data-ng-repeat="request in requestOrderList track by request.id">
                    <div class="row">
                        <div class="col s2">
                            <input id="RO-{{request.id}}"
                                   ng-disabled="!((selectedRO.roType == null)
                                            || selectedRO.count == 0
                                            || (selectedRO.roType == 'FIXED_ASSET' && request.assetOrder)
                                            || (selectedRO.roType == 'REGULAR' && !request.assetOrder))"
                                   data-ng-hide="requestOrderList.length<2"
                                   data-ng-if="request.requestUnit.id!=request.fulfillmentUnit.id"
                                   data-ng-model="request.checked"
                                   data-ng-change="selectOpenRo(request,false)" type="checkbox"/>
                            <label for="RO-{{request.id}}">{{request.id}}</label>
                        </div>

                        <div class="col s2">{{request.requestUnit.name}}</div>
                        <div class="col s2">{{request.generatedBy.name}}</div><!--
                        <div class="col s2">{{request.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>-->
                        <div class="col s2">{{request.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                        <div class="col s2">{{request.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                        <div class="col s2">
                            <span style="color: red;font-weight: bold;" data-ng-show="request.specialOrder==true">(Special)</span></div>
                        <button class="btn btn-small" data-ng-click="selectOpenRo(request,true)" acl-action="TRNCTA">Create TO</button>
                    </div>
                </li>
            </ul>
            <div data-ng-show="showMessage">No pending requests found.</div>
        </div>

        <div class="col s12" data-ng-hide="showPendingRequest" data-ng-if="selectedRO.roType != 'FIXED_ASSET'">
            <form name="trForm" novalidate>
                <div class="row">
                    <div data-ng-if="requestOrderDetail.id!=null" class="col s1"><label>Request Id:</label>{{requestOrderDetail.id}}</div>
                    <div class="col s3"><label>Requesting Unit:</label>{{requestOrderDetail.requestUnit.name}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.generatedBy)" class="col s2"><label>Requested By:</label>{{requestOrderDetail.generatedBy.name}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.generationTime)" class="col s2"><label>Generation time:</label>{{requestOrderDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.lastUpdateTime)" class="col s2"><label>Last Updated:</label>{{requestOrderDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                    <div class="col s2"><label>Fulfillment Date:</label>{{requestOrderDetail.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                </div>
                <div class="row margin0" style="background-color: #f3ecec;padding: 15px 0px;margin: 3px;">
                    <div class="col s3">
                        <label>Select Transport Mode</label>
                        <select data-ng-options="mode as mode.name for mode in transportModes track by mode.id"
                                data-ng-model="selectedTransportMode" data-ng-change="getVehicles(selectedTransportMode)"></select>
                    </div>
                    <div class="col s4">
                        <label>Select Vehicle</label>
                        <select data-ng-options="vehicle as vehicle.name for vehicle in vehicles track by (selectedTransportMode.name+'_'+vehicle.vehicleId)"
                                data-ng-model="selectedVehicle" data-ng-change="selectVehicle(selectedVehicle)"></select>
                    </div>
                    <div id="otherVehicle" class="col s5" data-ng-if="showOtherVehicleFields">
                        <div class="row margin0">
                            <label>Enter Details</label>
                            <input type="text" placeholder="Rgd. Number/GSTIN"
                                   style="text-transform: uppercase;"
                                   data-ng-focus="validateAndSaveId(transportId)"
                                   data-ng-change="validateAndSaveId(transportId)"
                                   data-ng-model="transportId">
                            <span style="cursor: pointer;" data-ng-if="showValidError" tooltipped data-tooltip="{{validateErrorText}}">
                            <i class="fa fa-times red-text"></i>
                        </span>
                            <input type="text" placeholder="Name"
                                   style="text-transform: uppercase;"
                                   data-ng-change = "validateAndSaveName(transportName)"
                                   data-ng-model="transportName">
                            <input data-ng-if="selectedTransportMode.name!='ROAD'"  type="text" placeholder="Docket number"
                                   style="text-transform: uppercase;"
                                   data-ng-change = "validateAndSaveDocket(docketNumber)"
                                   data-ng-model="docketNumber">
                        </div>
                    </div>
                </div>
                <div class="row" data-ng-show="selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0">
                    <dev draggable1 class="col s3">
                        <label style="color: whitesmoke;">Focus to scan   </label>
                        <input id="_draggableInputId" type="text" max-length="6" name="scan" data-ng-model="scannedAssetTagValue"  data-ng-change="onScan()" stopEvent>
                    </dev>
                </div>
                <div class="row" data-ng-if="!isEmpty(requestOrderDetail.comment)">
                    <div class="col s12"><label>Comment:</label>{{requestOrderDetail.comment}}</div>
                </div>
                <ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
                    <li class="collection-item list-head">
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s2">Product Name</div>
                            <div class="col s2">Requested Quantity</div>
                            <div class="col s2">Requested Absolute Quantity</div>
                            <div class="col s2">Transferred Quantity</div>
                            <div class="col s2">Excess Quantity</div>
                            <div class="col s1">Unit Of Measure</div>
                            <div class="col s1">Vendor</div>
                        </div>
                    </li>

                    <li style="margin-bottom: 10px;border:#ddd 1px solid;border-left:0px;border-right:0px;"
                        data-ng-repeat="item in requestOrderDetail.requestOrderItems | orderBy: 'productId' track by $index"
                        data-ng-class="{multiSkuColor:item.skuList.length>1}">
                        <div class="row" style="padding: 10px; background: #d8d8d8;border-bottom: #ddd 1px solid;" data-ng-class="{redBg:item.transferredQuantity!=item.requestedQuantity}">
                            <div class="col s2"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                            <div class="col s2">{{item.requestedQuantity}}</div>
                            <div class="col s2">{{item.requestedAbsoluteQuantity}}</div>
                            <div class="col s2">{{item.transferredQuantity==null?0:item.transferredQuantity}}</div>
                            <div class="col s2">{{item.excessQuantity}}</div>
                            <div class="col s1">{{item.unitOfMeasure}}</div>
                            <div class="col s1"><label class="badge">{{item.vendor.name}} {{item.wtf}}</label></div>
                        </div>
                        <div class="row">
                            <div class="col s5">
                                <label>Select SKU</label>
                                <select data-ng-model="item.selectedSku"
                                        data-ng-change="onSkuChanged(item)"
                                        data-ng-options="sku as sku.skuName for sku in item.skuList | filter : bySKUs track by sku.skuId">
                                </select>
                            </div>
                            <div class="col s5">
                                <label>Select Packaging:</label>
                                <select data-ng-model="item.selectedPackaging"
                                        data-ng-options="packaging as packaging.packagingDefinition.packagingName group by packaging.packagingDefinition.packagingType for packaging
                                     in item.selectedSku.skuPackagings | orderBy: 'packagingDefinition.packagingType' track by packaging.packagingId"
                                        data-ng-init="item.selectedPackaging=item.selectedSku.skuPackagings[0]"></select>
                            </div>
                            <div class="col s2">
                                <input type="button" value="Add" class="btn" data-ng-click="addPackaging(item)" style="margin-top:20px;" />
                            </div>
                        </div>
                        <div class="row" data-ng-repeat="trItem in item.trPackaging track by $index" style="margin-bottom: 0;">
                            <div class="col s12">
                                <div class="row" style="margin-bottom: 10px;">
                                    <div class="col s6"><a data-ng-click="showPreview($event, trItem.skuId,'SKU')">{{trItem.skuName}}</a></div>
                                    <div class="col s6">Transferred Qty: {{trItem.transferredQuantity}} &nbsp; {{trItem.unitOfMeasure}}</div>
                                </div>
                                <div class="row" data-ng-repeat="pgd in trItem.packagingDetails track by $index"
                                     style="background:rgba(0,0,0,0.03); padding:7px; margin-bottom: 10px;">
                                    <div style="margin:-7px;padding:7px;background:#ddd;margin-bottom: 10px;">
                                        {{pgd.packagingDefinitionData.packagingName}}
                                    </div>
                                    <div data-ng-if="!addBillBookDetails[item.productId]">
                                        <div class="col s2">
                                            <label>Units Packed:</label>
                                            <div data-ng-if="item.productId!=100217">
                                                <input type="number" name="pkgQty[$index]"
                                                       data-ng-disabled="(hasDrillDown(trItem) && item.selectedSku.skuPackagings.length <= 1)
                                                	   || (selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0 )"
                                                       data-ng-model="pgd.numberOfUnitsPacked"
                                                       ng-change="updatePackagingQty(pgd,trItem,item)" required/>
                                            </div>
                                            <div data-ng-if="item.productId==100217">
                                                <span name="pkgQty[$index]"
                                                      data-ng-init="updatePackagingQty(pgd,trItem,item)">{{pgd.numberOfUnitsPacked}}</span>
                                            </div>
                                            <p ng-show="trForm.pkgQty[$index].$error.required" class="errorMessage">
                                                Please enter valid quantity.</p>
                                        </div>
                                        <div class="col s2"><label>Transferred Qty:</label> {{pgd.transferredQuantity}}
                                        </div>
                                        <div class="col s2"><label>Unit Of Measure:</label>
                                            {{pgd.packagingDefinitionData.unitOfMeasure}}
                                        </div>
                                        <div class="col s3" data-ng-if="!hasDrillDown(trItem) ||  item.selectedSku.skuPackagings.length > 1">
                                            <button class="btn btn-small"
                                                    data-ng-click="removePackaging(trItem,$index, item)">Remove
                                            </button>
                                        </div>
                                        <div class="col s3">
                                            <button class="btn btn-small"
                                                    data-ng-if="item.productId==100217 && isWarehouse"
                                                    data-ng-init="manualDetailsRequired()"
                                                    data-ng-click="fillManualBBDetails(trItem,$index, item)"
                                                    data-ng-class="{true:'red', false:'green'}[isBillBooksDetailFilled]">
                                                Add Details
                                            </button>
                                        </div>
                                    </div>
                                    <div data-ng-if="addBillBookDetails[item.productId]"
                                         data-ng-include="'views/ManualBillBookDetail.html'"></div>
                                </div>
                                <div class="row data-division" data-ng-if="hasDrillDown(trItem)">
                                    <div class="row data-expiry">
                                        Expiry Info
                                    </div>
                                    <div class="row" style="font-weight: bold;">
                                        <div class="col s3">
                                            Quantity
                                        </div>
                                        <div class="col s3">
                                            Expiry Date
                                        </div>
                                    </div>
                                    <div class="row" data-ng-repeat="drilldown in trItem.drillDowns track by $index">
                                        <div class="col s3">
                                            {{drilldown.quantity}}
                                        </div>
                                        <div class="col s3 data-date">
                                            {{drilldown.expiryDate| date: 'yyyy-MM-dd HH:mm:ss'}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" data-ng-if="(selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0)
                                                        && (item.trPackaging != undefined && item.trPackaging.length > 0)">
                            <div class="col s4">
                                <ng-form name="innerForm">
                                    <label>Asset Tag Value:</label>
                                    <div>
                                        <input type="text"

                                               name="assetTag"
                                               data-ng-model="item.associatedAssetTagValue"
                                               data-ng-minlength="0"
                                               data-ng-maxlength="6"
                                               ng-change="validateAssetTagValue(item.associatedAssetTagValue,item, $index)" required/>
                                        <p ng-show="innerForm.assetTag.$error.required" class="errorMessage">Asset Tag Value is required.</p>
                                        <p ng-show="innerForm.assetTag.$error.maxlength" class="errorMessage">Asset Tag Value is too large.</p>
                                        <p ng-show="innerForm.assetTag.$error.min" class="errorMessage">Asset Tag Value is too small.</p>
                                        <div data-ng-if="item.assetValidated == null || !item.assetValidated">
                                            <p  class="errorMessage">Enter Valid Asset Tag Value.</p>
                                        </div>
                                    </div>
                                </ng-form>

                            </div>
                        </div>
                    </li>
                </ul>
                <!--<div class="row" data-ng-if="selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0">-->
                <!--<label for="toType">Select Transfer Order type:</label>-->
                <!--<select id="toType" class="form-control" name="toType" ng-model="requestOrderDetail.toType"-->
                <!--ng-options="toType as toType for toType in toTypeList " required></select>-->
                <!--<p ng-show="trForm.toType.$error.required" class="errorMessage">Asset Tag Value is required.</p>-->
                <!--</div>-->
                <div class="row">
                    <div class="col s12 form-element">
                        <label>Comment(optional):</label>
                        <textarea data-ng-model="comment"></textarea>
                    </div>
                    <div class="col s12 form-element">
                        <input type="button" data-ng-click="showPendingRequest=true;" class="btn" value="Back" />
                        <input type="button" data-ng-disabled="submitted"
                               data-ng-if="trForm.$valid && isInvoiceRo" data-ng-click="createTransferOrderObject()" class="btn right" value="Submit Invoice" acl-action="TRNCTA" />
                        <input type="button" data-ng-disabled="submitted"
                               data-ng-if="trForm.$valid && !isInvoiceRo" data-ng-click="createTransferOrderObject()" class="btn right" value="Submit Transfer" acl-action="TRNCTA" />
                    </div>
                </div>
            </form>
        </div>
        <div class="col s12" data-ng-hide="showPendingRequest" data-ng-if="selectedRO.roType == 'FIXED_ASSET'">
            <form name="trForm" novalidate>
                <div class="row">
                    <div data-ng-if="requestOrderDetail.id!=null" class="col s1"><label>Request Id:</label>{{requestOrderDetail.id}}</div>
                    <div class="col s3"><label>Requesting Unit:</label>{{requestOrderDetail.requestUnit.name}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.generatedBy)" class="col s2"><label>Requested By:</label>{{requestOrderDetail.generatedBy.name}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.generationTime)" class="col s2"><label>Generation time:</label>{{requestOrderDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.lastUpdateTime)" class="col s2"><label>Last Updated:</label>{{requestOrderDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                    <div class="col s2"><label>Fulfillment Date:</label>{{requestOrderDetail.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                </div>
                <div class="row margin0" style="background-color: #f3ecec;padding: 15px 0px;margin: 3px;">
                    <div class="col s3">
                        <label>Select Transport Mode</label>
                        <select data-ng-options="mode as mode.name for mode in transportModes track by mode.id"
                                data-ng-model="selectedTransportMode" data-ng-change="getVehicles(selectedTransportMode)"></select>
                    </div>
                    <div class="col s4">
                        <label>Select Vehicle</label>
                        <select data-ng-options="vehicle as vehicle.name for vehicle in vehicles track by (selectedTransportMode.name+'_'+vehicle.vehicleId)"
                                data-ng-model="selectedVehicle" data-ng-change="selectVehicle(selectedVehicle)"></select>
                    </div>
                    <div id="otherVehicle" class="col s5" data-ng-if="showOtherVehicleFields">
                        <div class="row margin0">
                            <label>Enter Details</label>
                            <input type="text" placeholder="Rgd. Number/GSTIN"
                                   style="text-transform: uppercase;"
                                   data-ng-focus="validateAndSaveId(transportId)"
                                   data-ng-change="validateAndSaveId(transportId)"
                                   data-ng-model="transportId">
                            <span style="cursor: pointer;" data-ng-if="showValidError" tooltipped data-tooltip="{{validateErrorText}}">
                            <i class="fa fa-times red-text"></i>
                        </span>
                            <input type="text" placeholder="Name"
                                   style="text-transform: uppercase;"
                                   data-ng-change = "validateAndSaveName(transportName)"
                                   data-ng-model="transportName">
                            <input data-ng-if="selectedTransportMode.name!='ROAD'"  type="text" placeholder="Docket number"
                                   style="text-transform: uppercase;"
                                   data-ng-change = "validateAndSaveDocket(docketNumber)"
                                   data-ng-model="docketNumber">
                        </div>
                    </div>
                </div>
                <!--                <div class="row" data-ng-show="selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0">-->
                <!--                    <dev draggable1 class="col s3">-->
                <!--                        <label style="color: whitesmoke;">Focus to scan   </label>-->
                <!--                        <input id="_draggableInputId" type="text" max-length="6" name="scan" data-ng-model="scannedAssetTagValue"  data-ng-change="onScan()" stopEvent>-->
                <!--                    </dev>-->
                <!--                </div>-->
                <div class="row" data-ng-if="!isEmpty(requestOrderDetail.comment)">
                    <div class="col s12"><label>Comment:</label>{{requestOrderDetail.comment}}</div>
                </div>
                <input data-ng-if="selectedRO.roType == 'FIXED_ASSET' && !allowInput" type="button" value="Refresh" class="btn " style="margin
                :15px" data-ng-click="fetchInitiatedFa(requestOrderDetail,false)" />
                <ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
                    <li class="collection-item list-head">
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s1" >#</div>
                            <div class="col s2">Product Id </div>
                            <div class="col s2" >Product Name</div>
                            <div class="col s1" style="padding-top:1em">UOM</div>
                            <div class="col s1"> Available Inventory</div>
                            <div class="col s2" >Transferred Quantity</div>
                            <div class="col s2" >Scanned Qty</div>
                            <div class="col s1">Action</div>
                        </div>
                    </li>

                    <li style="margin-bottom: 10px;border:#ddd 1px solid;border-left:0px;border-right:0px;"
                        data-ng-repeat="item in requestOrderDetail.requestOrderItems track by $index"
                    >
                        <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid; background:#d8d8d8" data-ng-style="(productSKuAssetMap[item.productId] == null) ? {'background':'red' }:{'background':'#d8d8d8'}" >
                            <div class="col s1" >{{$index+1}}</div>
                            <div class="col s2">{{item.productId}} </div>
                            <div class="col s2" ><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                            <div class="col s1" style="padding-top:1em">{{item.unitOfMeasure}}</div>
                            <div class="col s1" style="padding:1em">{{productInventorySize[item.productId]}}</div>
                            <div class="col s2" ><input data-ng-disabled="!item.isNewItem && !allowInput"   value="item.transferredQuantity"
                                                        style="margin:15px" data-ng-model="item.transferredQuantity" data-ng-change="validTransferQty(item,item.transferredQuantity)"/>
                            </div>
                            <div class="col s2" >{{scannedAssetProductMap[item.productId].length}}</div>
                            <div class="col s1 " style="display: flex;" > <input type="button"  value="X" class=" " style="width: 40px; height: 40px;border-radius: 50%;border: none;background-color: red;color: white;font-size: 20px;font-weight: bold;cursor: pointer;outline: none;"
                                                                                 data-ng-click="removePackaging(trItem,$index, item)" />


                            </div>
                        </div>
<!--                        <div class="row" style="padding: 10px;border-bottom: #ddd 1px solid;" data-ng-style="(productSKuAssetMap[item.productId] == null) ? {'background':'red' }:{'background':'#d8d8d8'}" data-ng-class="{redBg:item.transferredQuantity!=item.requestedQuantity}">-->
<!--                            <div class="col s1">{{$index+1}}</div>-->
<!--                            <div class="col s2"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>-->
<!--                            <div class="col s1">{{item.requestedQuantity}}</div>-->
<!--                            <div class="col s1">{{item.requestedAbsoluteQuantity}}</div>-->
<!--                            <div class="col s2" > <input data-ng-disabled="!allowInput"   value="item.transferredQuantity"-->
<!--                                                         style="margin:15px" data-ng-model="item.transferredQuantity"/>-->
<!--                            </div>-->
<!--                            <div class="col s2">{{scanAndtransferQty[item.productId].value != null ? scanAndtransferQty[item.productId].value : 0}}-->
<!--                            </div>-->
<!--                            <div class="col s1" style="padding-top:1em">{{item.unitOfMeasure}}</div>-->

<!--                            <div class="col s2">-->
<!--                                <button class="btn btn-small" data-ng-click="removePackaging(trItem,$index, item)">Remove</button>-->
<!--                            </div>-->

<!--                        </div>-->
                        <div class="row " data-ng-repeat="(skuId,asset) in productSKuAssetMap[item.productId]  " style="width:100%">

                            <div  class="col" style="width:100%">
                                <div class="skuContainer " data-ng-click="toggle(skuId)" data-ng-style="toggleSkuList == skuId&&{'background':'#eee'}">
                                    <div style="width:100% ; display:flex ; justify-content:space-between">
                                        <div class="col s1" data-ng-if="toggleSkuList != skuId" style="font-weight:bold;padding-top:1em">+</div>
                                        <div class="col s1" data-ng-if="toggleSkuList == skuId" style="font-weight:bold;padding-top:1em">-</div>
                                        <div class="col s2">{{skuId}}</div>
                                        <div class="col s2" >{{asset[0].assetName}}</div>
                                        <div class="col s1" style="padding-top:1em">{{item.unitOfMeasure}}</div>
                                        <div class="col s1" style="padding-top:1em"> {{productSKuAssetMap[item.productId][skuId].length}}</div>
                                        <div class="col s2" >N/A</div>
                                        <div class="col s2" >{{skuScannedList[skuId] ? skuScannedList[skuId]:0}}</div>
                                        <div class="col s1"></div>
                                    </div>

                                    <div style="width:80% ; margin:auto  ;  "  data-ng-if="toggleSkuList == skuId" >

                                        <div class="table">
                                            <div class="table-header" style="padding:10px">
                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset Name</div>
                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset Status</div>
                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset tagValue</div>
                                            </div>
                                            <div class="table-content" data-ng-repeat="assetItem in productSKuAssetMap[item.productId][skuId]">
                                                <div class="table-row" data-ng-style="isScanned[assetItem.assetId] == null ? {'background-color':'red' ,'color':'white' ,'margin':'20px' }:{'background-color':'green' , 'color':'white' ,'margin':'20px'}" style="padding:10px">
                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.assetId}}</div>
                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.assetName}}</div>
                                                    <div class="table-data" style="padding-top: 8px";>{{assetItem.assetStatus}}</div>
                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.tagValue}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            </div>
                            </ng-form>
                    </li>
                </ul>
                <div class="row">
                    <div class="col s12 form-element">
                        <label>Comment(optional):</label>
                        <textarea data-ng-model="comment"></textarea>
                    </div>
                    <div class="col s12 form-element">
                        <input type="button" data-ng-if="currentstockEventDefinition.eventId==null" data-ng-click="backTransfer()" class="btn" value="Back" />
                        <input type="button" data-ng-if="currentstockEventDefinition.eventId!=null" data-ng-click="onCancelTo()" class="btn" value="Cancel" />
                        <input type="button" data-ng-disabled="submitted"
                               data-ng-if="trForm.$valid && isInvoiceRo && !allowInput" data-ng-click="showScanQty()" class="btn right" value="Submit Invoice" acl-action="TRNCTA" />
                        <input type="button" data-ng-disabled="submitted"
                               data-ng-if="trForm.$valid && !isInvoiceRo &&!allowInput" data-ng-click="showScanQty()" class="btn right" value="Submit Transfer" acl-action="TRNCTA" />
                        <input type="button"  data-ng-if="trForm.$valid && allowInput  " class="btn right" data-ng-click="initiateFATransfer()"
                               value="Initiate Request" acl-action="TRNSTA"  />

                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()" data-ng-if="false">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3 class="left">Create Transfer Order {{selectedRO.roType}} {{selectedRO.count}}</h3>
                <input type="button" data-ng-click="backTransfer()" class="btn right" value="Back" data-ng-hide="showPendingRequest" style="margin-top: 35px;" />
            </div>
        </div>
        <div class="row" data-ng-hide="requestOrderDetail != null">
            <div class="col s9">
                <label for="inputCreated">Select fulfillment date</label>
                <input input-date type="text" name="created" id="inputCreated" ng-model="selectedDate"
                       data-ng-change="reset()"
                       container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                       max="{{maxDate}}" />
            </div>
            <div class="col s3 margin-top-10">
                <button class="btn btn-small" ng-click="getPendingRequestOrders(selectedDate)" style="margin-top: 14px;" acl-action="TRNCTV">Get Orders</button>
            </div>
        </div>
        <hr>
    </div>


    <div class="row">
        <div class="col s12" data-ng-show="showPendingRequest" data-ng-if="!isEmpty(clubbedROs) && showClubROBtn">
            <button class="btn pull-right" data-ng-click="clubAllROs(arrayAfterClubbing)">Club & Create TO</button>
        </div>
        <div class="col s12" data-ng-show="showPendingRequest">
            <ul class="collection menuItemList" data-ng-hide="showMessage">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s2">Id</div>
                        <div class="col s2">Request Unit</div>
                        <div class="col s2">Generated By</div>
                        <div class="col s2">Update time</div>
                        <div class="col s2">Fulfillment Date</div>
                        <div class="col s2">Actions</div>
                    </div>
                </li>
                <li  class="collection-item z-depth-1 clickable" data-ng-if="togglePending == true" data-ng-repeat="request in pendingList ">
                    <div class="row">
                        <div class="col s2">
                            <input id="RO-{{request.id}}"
                                   ng-disabled="!((selectedRO.roType == null)
                                            || selectedRO.count == 0
                                            || (selectedRO.roType == 'FIXED_ASSET' && request.assetOrder)
                                            || (selectedRO.roType == 'REGULAR' && !request.assetOrder))"
                                   data-ng-hide="requestOrderList.length<2"
                                   data-ng-if="request.requestUnit.id!=request.fulfillmentUnit.id"
                                   data-ng-model="request.checked"
                                   data-ng-change="selectOpenRo(request,false)" type="checkbox"/>
                            <label for="RO-{{request.id}}">{{request.id}}</label>
                        </div>

                        <div class="col s2">{{request.requestUnit.name}}</div>
                        <div class="col s2">{{request.generatedBy.name}}</div><!--
                        <div class="col s2">{{request.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>-->
                        <div class="col s2">{{request.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                        <div class="col s2">{{request.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                        <div class="col s2">
                            <span style="color: red;font-weight: bold;" data-ng-show="request.specialOrder==true">(Special)</span></div>
                        <button class="btn btn-small" data-ng-click="selectOpenRo(request,true)" acl-action="TRNCTA">Create TO</button>
                    </div>
                </li>

                <li class="collection-item z-depth-1 clickable" data-ng-repeat="request in requestOrderList track by request.id" data-ng-if="togglePending == false">
                    <div class="row">
                        <div class="col s2">
                            <input id="RO-{{request.id}}"
                                   ng-disabled="!((selectedRO.roType == null)
                                            || selectedRO.count == 0
                                            || (selectedRO.roType == 'FIXED_ASSET' && request.assetOrder)
                                            || (selectedRO.roType == 'REGULAR' && !request.assetOrder))"
                                   data-ng-hide="requestOrderList.length<2"
                                   data-ng-if="request.requestUnit.id!=request.fulfillmentUnit.id"
                                   data-ng-model="request.checked"
                                   data-ng-change="selectOpenRo(request,false)" type="checkbox"/>
                            <label for="RO-{{request.id}}">{{request.id}}</label>
                        </div>

                        <div class="col s2">{{request.requestUnit.name}}</div>
                        <div class="col s2">{{request.generatedBy.name}}</div><!--
                        <div class="col s2">{{request.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>-->
                        <div class="col s2">{{request.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                        <div class="col s2">{{request.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                        <div class="col s2">
                            <span style="color: red;font-weight: bold;" data-ng-show="request.specialOrder==true">(Special)</span></div>
                            <button class="btn btn-small" data-ng-click="selectOpenRo(request,true)" acl-action="TRNCTA">Create TO</button>
                    </div>
                </li>
            </ul>
            <div data-ng-show="showMessage">No pending requests found.</div>
        </div>

        <div class="col s12" data-ng-hide="showPendingRequest">
            <form name="trForm" novalidate>
                <div class="row">
                    <div data-ng-if="requestOrderDetail.id!=null" class="col s1"><label>Request Id:</label>{{requestOrderDetail.id}}</div>
                    <div class="col s3"><label>Requesting Unit:</label>{{requestOrderDetail.requestUnit.name}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.generatedBy)" class="col s2"><label>Requested By:</label>{{requestOrderDetail.generatedBy.name}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.generationTime)" class="col s2"><label>Generation time:</label>{{requestOrderDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                    <div data-ng-if="!isEmpty(requestOrderDetail.lastUpdateTime)" class="col s2"><label>Last Updated:</label>{{requestOrderDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                    <div class="col s2"><label>Fulfillment Date:</label>{{requestOrderDetail.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                </div>
                <div class="row margin0" style="background-color: #f3ecec;padding: 15px 0px;margin: 3px;">
                    <div class="col s3">
                        <label>Select Transport Mode</label>
                        <select data-ng-options="mode as mode.name for mode in transportModes track by mode.id"
                                data-ng-model="selectedTransportMode" data-ng-change="getVehicles(selectedTransportMode)"></select>
                    </div>
                    <div class="col s4">
                        <label>Select Vehicle</label>
                        <select data-ng-options="vehicle as vehicle.name for vehicle in vehicles track by (selectedTransportMode.name+'_'+vehicle.vehicleId)"
                                data-ng-model="selectedVehicle" data-ng-change="selectVehicle(selectedVehicle)"></select>
                    </div>
                    <div id="otherVehicle" class="col s5" data-ng-if="showOtherVehicleFields">
                        <div class="row margin0">
                            <label>Enter Details</label>
                            <input type="text" placeholder="Rgd. Number/GSTIN"
                                   style="text-transform: uppercase;"
                                   data-ng-focus="validateAndSaveId(transportId)"
                                   data-ng-change="validateAndSaveId(transportId)"
                                   data-ng-model="transportId">
                            <span style="cursor: pointer;" data-ng-if="showValidError" tooltipped data-tooltip="{{validateErrorText}}">
                            <i class="fa fa-times red-text"></i>
                        </span>
                            <input type="text" placeholder="Name"
                                   style="text-transform: uppercase;"
                                   data-ng-change = "validateAndSaveName(transportName)"
                                   data-ng-model="transportName">
                            <input data-ng-if="selectedTransportMode.name!='ROAD'"  type="text" placeholder="Docket number"
                                   style="text-transform: uppercase;"
                                   data-ng-change = "validateAndSaveDocket(docketNumber)"
                                   data-ng-model="docketNumber">
                        </div>
                    </div>
                </div>
<!--                <div class="row" data-ng-show="selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0">-->
<!--                    <dev draggable1 class="col s3">-->
<!--                        <label style="color: whitesmoke;">Focus to scan   </label>-->
<!--                        <input id="_draggableInputId" type="text" max-length="6" name="scan" data-ng-model="scannedAssetTagValue"  data-ng-change="onScan()" stopEvent>-->
<!--                    </dev>-->
<!--                </div>-->
                <div class="row" data-ng-if="!isEmpty(requestOrderDetail.comment)">
                    <div class="col s12"><label>Comment:</label>{{requestOrderDetail.comment}}</div>
                </div>
                <input data-ng-if="selectedRO.roType == 'FIXED_ASSET' && !allowInput" type="button" value="Refresh" class="btn " style="margin
                :15px" data-ng-click="fetchInitiatedFa(requestOrderDetail,false)" />
                <ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
                    <li class="collection-item list-head">
                        <div class="row" style="margin-bottom: 0;">
                            <div class ="col s1">#</div>
                            <div class="col s2">Product Name</div>
                            <div class="col s1">Requested Quantity</div>
                            <div class="col s1">Requested Absolute Quantity</div>
                            <div class="col s2">Transferred Quantity</div>
                            <div class="col s2">Scanned Quantity</div>
                            <div class="col s1">UOM</div>

                             <div class="col s2">Action</div>
                        </div>
                    </li>

                    <li style="margin-bottom: 10px;border:#ddd 1px solid;border-left:0px;border-right:0px;"
                        data-ng-repeat="item in requestOrderDetail.requestOrderItems  track by $index"

                        >
                        <div class="row" style="padding: 10px;border-bottom: #ddd 1px solid;" data-ng-style="(productSKuAssetMap[item.productId] == null) ? {'background':'red' }:{'background':'#d8d8d8'}" data-ng-class="{redBg:item.transferredQuantity!=item.requestedQuantity}">
                            <div class="col s1">{{$index+1}}}</div>
                            <div class="col s2"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                            <div class="col s1">{{item.requestedQuantity}}</div>
                            <div class="col s1">{{item.requestedAbsoluteQuantity}}</div>
                            <div class="col s2" >{{item.transferredQuantity==null?0:item.transferredQuantity}}
                            </div>
                            <div class="col s2">{{scanAndtransferQty[item.productId].value != null ? scanAndtransferQty[item.productId].value : 0}}

                            </div>
                            <div class="col s1" style="padding-top:1em">{{item.unitOfMeasure}}</div>

                            <div class="col s2">
                                <button class="btn btn-small" data-ng-click="removePackaging(trItem,$index, item)">Remove</button>
                            </div>

                                                        </div>
                                                        <div class="row " data-ng-repeat="(skuId,asset) in productSKuAssetMap[item.productId]  " style="width:100%">

                                                            <div  class="col" style="width:100%">
                                                                <div class="skuContainer " data-ng-click="toggle(skuId)" data-ng-style="toggleSkuList == skuId&&{'background':'#eee'}">
                                                                    <div style="width:100% ; display:flex ; justify-content:space-between">
                                                                        <div class="col s1"  data-ng-if="toggleSkuList != skuId" style="font-weight:bold;padding-top:2em">+</div>
                                                                        <div class="col s1" data-ng-if="toggleSkuList == skuId" style="font-weight:bold;padding-top:2em">-</div>
                                                                        <div class="col s2" style="padding-top:1em">{{asset[0].assetName}}</div>
                                                                        <div class="col s2" style="padding-top:1em">{{item.requestedQuantity}}</div>
                                                                        <div class="col s2" style="padding-top:1em">{{item.requestedAbsoluteQuantity}}</div>
                                                                        <div class="col s2" style="padding-top:1em"> {{productSKuAssetMap[item.productId][skuId].length}}</div>
                                                                        <div class="col s1" style="padding-top:2em" >{{item.unitOfMeasure}}</div>
                                                                        <div class="col s1"></div>
                                                                        <div class="col s1"></div>
                                                                    </div>
                                                                    <div style="width:80% ; margin:auto  ;  "  data-ng-if="toggleSkuList == skuId" >

                                                                        <div class="table">
                                                                            <div class="table-header" style="padding:10px">
                                                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset Name</div>
                                                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset Status</div>
                                                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset tagValue</div>
                                                                            </div>
                                                                            <div class="table-content" data-ng-repeat="assetItem in productSKuAssetMap[item.productId][skuId]">
                                                                                <div class="table-row" data-ng-style="isScanned[assetItem.assetId] == null ? {'background-color':'red' ,'color':'white' ,'margin':'20px' }:{'background-color':'green' , 'color':'white' ,'margin':'20px'}" style="padding:10px">
                                                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.assetId}}</div>
                                                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.assetName}}</div>
                                                                                    <div class="table-data" style="padding-top: 8px";>{{assetItem.assetStatus}}</div>
                                                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.tagValue}}</div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                            </div>

                                                        </div>
                                                        </div>
                                                   <!--     <div class="row " data-ng-repeat="(skuId,asset) in productSKuAssetMap[item.productId]  " style="width:100%">

                                                            <div  class="col" style="width:100%">
                                                                <div class="skuContainer " data-ng-click="toggle(skuId)" data-ng-style="toggleSkuList == skuId&&{'background':'#eee'}">
                                                                    <div style="width:100% ; display:flex ; justify-content:space-between">
                                                                        <div class="col s1"  data-ng-if="toggleSkuList != skuId" style="font-weight:bold;padding-top:2em">+</div>
                                                                        <div class="col s1" data-ng-if="toggleSkuList == skuId" style="font-weight:bold;padding-top:2em">-</div>
                                                                        <div class="col s2" style="padding-top:1em">{{asset[0].assetName}}</div>
                                                                        <div class="col s2" style="padding-top:1em">{{item.requestedQuantity}}</div>
                                                                        <div class="col s2" style="padding-top:1em">{{item.requestedAbsoluteQuantity}}</div>
                                                                        <div class="col s2" style="padding-top:1em"> {{productSKuAssetMap[item.productId][skuId].length}}</div>
                                                                        <div class="col s1" style="padding-top:2em" >{{item.unitOfMeasure}}</div>
                                                                        <div class="col s1"></div>
                                                                        <div class="col s1"></div>
                                                                    </div>
                                                                    <div style="width:80% ; margin:auto  ;  "  data-ng-if="toggleSkuList == skuId" >

                                                                        <div class="table">
                                                                            <div class="table-header" style="padding:10px">
                                                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset Name</div>
                                                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset Status</div>
                                                                                <div class="header__item" style="color:#26a69a ; font-weight: bold">Asset tagValue</div>
                                                                            </div>
                                                                            <div class="table-content" data-ng-repeat="assetItem in productSKuAssetMap[item.productId][skuId]">
                                                                                <div class="table-row" data-ng-style="isScanned[assetItem.assetId] == null ? {'background-color':'red' ,'color':'white' ,'margin':'20px' }:{'background-color':'green' , 'color':'white' ,'margin':'20px'}" style="padding:10px">
                                                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.assetId}}</div>
                                                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.assetName}}</div>
                                                                                    <div class="table-data" style="padding-top: 8px";>{{assetItem.assetStatus}}</div>
                                                                                    <div class="table-data" style="padding-top: 8px;">{{assetItem.tagValue}}</div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                        <p ng-show="innerForm.assetTag.$error.required" class="errorMessage">Asset Tag Value is required.</p>
                                        <p ng-show="innerForm.assetTag.$error.maxlength" class="errorMessage">Asset Tag Value is too large.</p>
                                        <p ng-show="innerForm.assetTag.$error.min" class="errorMessage">Asset Tag Value is too small.</p>
                                        <div data-ng-if="item.assetValidated == null || !item.assetValidated">
                                            <p  class="errorMessage">Enter Valid Asset Tag Value.</p>
                                        </div>
                                    </div>-->
                                </ng-form>

                                                        <!--                                               name="assetTag"-->
<!--                                               data-ng-model="item.associatedAssetTagValue"-->
<!--                                               data-ng-minlength="0"-->
<!--                                               data-ng-maxlength="6"-->
<!--                                               ng-change="validateAssetTagValue(item.associatedAssetTagValue,item, $index)" required/>-->
<!--                                        <p ng-show="innerForm.assetTag.$error.required" class="errorMessage">Asset Tag Value is required.</p>-->
<!--                                        <p ng-show="innerForm.assetTag.$error.maxlength" class="errorMessage">Asset Tag Value is too large.</p>-->
<!--                                        <p ng-show="innerForm.assetTag.$error.min" class="errorMessage">Asset Tag Value is too small.</p>-->
<!--                                        <div data-ng-if="item.assetValidated == null || !item.assetValidated">-->
<!--                                            <p  class="errorMessage">Enter Valid Asset Tag Value.</p>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </ng-form>-->

<!--                            </div>

                                                     <div class="row">-->
<!--                            <div class="col s5">-->
<!--                                <label>Select SKU</label>-->
<!--                                <select data-ng-model="item.selectedSku"-->
<!--                                        data-ng-change="onSkuChanged(item)"-->
<!--                                        data-ng-options="sku as sku.skuName for sku in item.skuList | filter : bySKUs track by sku.skuId">-->
<!--                                </select>-->
<!--                            </div>-->
<!--                            <div class="col s5">-->
<!--                                <label>Select Packaging:</label>-->
<!--                                <select data-ng-model="item.selectedPackaging"-->
<!--                                        data-ng-options="packaging as packaging.packagingDefinition.packagingName group by packaging.packagingDefinition.packagingType for packaging-->
<!--                                     in item.selectedSku.skuPackagings | orderBy: 'packagingDefinition.packagingType' track by packaging.packagingId"-->
<!--                                        data-ng-init="item.selectedPackaging=item.selectedSku.skuPackagings[0]"></select>-->
<!--                            </div>-->
<!--                            <div class="col s2">-->
<!--                                <input type="button" value="Add" class="btn" data-ng-click="addPackaging(item)" style="margin-top:20px;" />-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="row" data-ng-repeat="trItem in item.trPackaging track by $index" style="margin-bottom: 0;">-->
<!--                            <div class="col s12">-->
<!--                                <div class="row" style="margin-bottom: 10px;">-->
<!--                                    <div class="col s6"><a data-ng-click="showPreview($event, trItem.skuId,'SKU')">{{trItem.skuName}}</a></div>-->
<!--                                    <div class="col s6">Transferred Qty: {{trItem.transferredQuantity}} &nbsp; {{trItem.unitOfMeasure}}</div>-->
<!--                                </div>-->
<!--                                <div class="row" data-ng-repeat="pgd in trItem.packagingDetails track by $index"-->
<!--                                     style="background:rgba(0,0,0,0.03); padding:7px; margin-bottom: 10px;">-->
<!--                                    <div style="margin:-7px;padding:7px;background:#ddd;margin-bottom: 10px;">-->
<!--                                        {{pgd.packagingDefinitionData.packagingName}}-->
<!--                                    </div>-->
<!--                                    <div data-ng-if="!addBillBookDetails[item.productId]">-->
<!--                                        <div class="col s2">-->
<!--                                            <label>Units Packed:</label>-->
<!--                                            <div data-ng-if="item.productId!=100217">-->
<!--                                                <input type="number" name="pkgQty[$index]"-->
<!--                                                	   data-ng-disabled="(hasDrillDown(trItem) && item.selectedSku.skuPackagings.length <= 1)-->
<!--                                                	   || (selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0 )"-->
<!--                                                       data-ng-model="pgd.numberOfUnitsPacked"-->
<!--                                                       ng-change="updatePackagingQty(pgd,trItem,item)" required/>-->
<!--                                            </div>-->
<!--                                            <div data-ng-if="item.productId==100217">-->
<!--                                                <span name="pkgQty[$index]"-->
<!--                                                      data-ng-init="updatePackagingQty(pgd,trItem,item)">{{pgd.numberOfUnitsPacked}}</span>-->
<!--                                            </div>-->
<!--                                            <p ng-show="trForm.pkgQty[$index].$error.required" class="errorMessage">-->
<!--                                                Please enter valid quantity.</p>-->
<!--                                        </div>-->
<!--                                        <div class="col s2"><label>Transferred Qty:</label> {{pgd.transferredQuantity}}-->
<!--                                        </div>-->
<!--                                        <div class="col s2"><label>Unit Of Measure:</label>-->
<!--                                            {{pgd.packagingDefinitionData.unitOfMeasure}}-->
<!--                                        </div>-->
<!--                                        <div class="col s3" data-ng-if="!hasDrillDown(trItem) ||  item.selectedSku.skuPackagings.length > 1">-->
<!--                                            <button class="btn btn-small"-->
<!--                                                    data-ng-click="removePackaging(trItem,$index, item)">Remove-->
<!--                                            </button>-->
<!--                                        </div>-->
<!--                                        <div class="col s3">-->
<!--                                            <button class="btn btn-small"-->
<!--                                                    data-ng-if="item.productId==100217 && isWarehouse"-->
<!--                                                    data-ng-init="manualDetailsRequired()"-->
<!--                                                    data-ng-click="fillManualBBDetails(trItem,$index, item)"-->
<!--                                                    data-ng-class="{true:'red', false:'green'}[isBillBooksDetailFilled]">-->
<!--                                                Add Details-->
<!--                                            </button>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                    <div data-ng-if="addBillBookDetails[item.productId]"-->
<!--                                         data-ng-include="'views/ManualBillBookDetail.html'"></div>-->
<!--                                </div>-->
<!--                                <div class="row data-division" data-ng-if="hasDrillDown(trItem)">-->
<!--                                    <div class="row data-expiry">-->
<!--                                        Expiry Info-->
<!--                                    </div>-->
<!--                                    <div class="row" style="font-weight: bold;">-->
<!--                                        <div class="col s3">-->
<!--                                            Quantity-->
<!--                                        </div>-->
<!--                                        <div class="col s3">-->
<!--                                            Expiry Date-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                    <div class="row" data-ng-repeat="drilldown in trItem.drillDowns track by $index">-->
<!--                                        <div class="col s3">-->
<!--                                            {{drilldown.quantity}}-->
<!--                                        </div>-->
<!--                                        <div class="col s3 data-date">-->
<!--                                            {{drilldown.expiryDate| date: 'yyyy-MM-dd HH:mm:ss'}}-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                        </div>-->
<!--                        </div>-->
<!--                        <div class="row" data-ng-if="(selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0)-->
<!--                                                        && (item.trPackaging != undefined && item.trPackaging.length > 0)">-->
<!--                            <div class="col s4">-->
<!--                                <ng-form name="innerForm">-->
<!--                                    <label>Asset Tag Value:</label>-->
<!--                                    <div>-->
<!--                                        <input type="text"-->

<!--                                               name="assetTag"-->
<!--                                               data-ng-model="item.associatedAssetTagValue"-->
<!--                                               data-ng-minlength="0"-->
<!--                                               data-ng-maxlength="6"-->
<!--                                               ng-change="validateAssetTagValue(item.associatedAssetTagValue,item, $index)" required/>-->
<!--                                        <p ng-show="innerForm.assetTag.$error.required" class="errorMessage">Asset Tag Value is required.</p>-->
<!--                                        <p ng-show="innerForm.assetTag.$error.maxlength" class="errorMessage">Asset Tag Value is too large.</p>-->
<!--                                        <p ng-show="innerForm.assetTag.$error.min" class="errorMessage">Asset Tag Value is too small.</p>-->
<!--                                        <div data-ng-if="item.assetValidated == null || !item.assetValidated">-->
<!--                                            <p  class="errorMessage">Enter Valid Asset Tag Value.</p>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </ng-form>-->

<!--                            </div>-->
<!--                        </div>-->
                    </li>
                </ul>
                <!--<div class="row" data-ng-if="selectedRO.roType == 'FIXED_ASSET' && selectedRO.count > 0">-->
                    <!--<label for="toType">Select Transfer Order type:</label>-->
                    <!--<select id="toType" class="form-control" name="toType" ng-model="requestOrderDetail.toType"-->
                            <!--ng-options="toType as toType for toType in toTypeList " required></select>-->
                    <!--<p ng-show="trForm.toType.$error.required" class="errorMessage">Asset Tag Value is required.</p>-->
                <!--</div>-->
                <div class="row">
                    <div class="col s12 form-element">
                        <label>Comment(optional):</label>
                        <textarea data-ng-model="comment"></textarea>
                    </div>
                    <div class="col s12 form-element">
                        <input type="button" data-ng-if="currentstockEventDefinition.eventId==null" data-ng-click="backTransfer()" class="btn" value="Back" />
                        <input type="button" data-ng-if="currentstockEventDefinition.eventId!=null" data-ng-click="onCancelTo()" class="btn" value="Cancel" />
                        <input type="button" data-ng-disabled="submitted"
                               data-ng-if="trForm.$valid && isInvoiceRo && !allowInput" data-ng-click="showScanQty()" class="btn right" value="Submit Invoice" acl-action="TRNCTA" />
                        <input type="button" data-ng-disabled="submitted"
                               data-ng-if="trForm.$valid && !isInvoiceRo &&!allowInput" data-ng-click="showScanQty()" class="btn right" value="Submit Transfer" acl-action="TRNCTA" />
                        <input type="button"  data-ng-if="trForm.$valid && allowInput  " class="btn right" data-ng-click="initiateFATransfer()"
                               value="Initiate Request" acl-action="TRNSTA"  />

                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/ng-template" id="toQtyValidation.html">

    <div class="modal-header red-text" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Please Re-Enter  Qty For Sku : {{toItem.skuName}}</h3>
        <hr>
    </div>
    <div class="row" style="margin: 20px;">
        <table>
            <tr>
                <th>Product Name</th>
                <th>Requested Quantity</th>
                <th>Unit Of Measure</th>
                <th>Packaging Name</th>
                <th>Pkg Quantity</th>
            </tr>
            <tr>
                <td>{{toItem.skuName}}</td>
                <td>{{toItem.requestedQuantity}}</td>
                <td>{{toItem.unitOfMeasure}}</td>
                <td>{{pkgClone.packagingDefinitionData.packagingName}}</td>
                <td><input
                        style="width: 200px;"
                        type="number"
                        min="0"
                        placeholder="Quantity"
                        data-ng-model="pkgClone.numberOfUnitsPacked"
                />
                </td>
            </tr>
        </table>
    </div>

    <button class="btn-toolbar  btn-medium right " ng-click="submit()" title="send"
            style="border: whitesmoke;  color: #0f9d58; text-align: center">Send <i  class="material-icons " style="padding-bottom: 0px;">send</i>
    </button>

</script>
<script type="text/ng-template" id="scanViewModal.html">
    <div class="modal-header">
        <h3>Total Transferred Product Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="col s12" >
            <table class="table bordered striped" style="border: #ccc 1px solid;">
                <thead>
                <tr>
                    <th>Product Name</th>
                    <th>Scanned Qty</th>
                    <th>Scanned Sku Tags</th>
                    <th>Not Scanned Qty</th>
                </tr>
                </thead>
                <tbody>
                <!-- <div data-ng-repeat="(id,item) in diffs" style="width:100%">
                    <p>Hy</p> -->

                <tr data-ng-repeat="(id,item) in diffs">
                    <td style="color: black;">{{id}}</td>
                    <td style="color: black;">{{diffs[id].transferredQty}}</td>

                    <td style="color: black;width:25%"  data-ng-if="item.scannedAssetTag.length" data-ng-click="toggleOpen(id)" ><p  data-ng-if="!openList[id]" style="color:blue" >{{item.scannedAssetTag[0]}}...more</p><p data-ng-click="toggleOpen(id)" data-ng-if="openList[id]" data-ng-repeat="asset in item.scannedAssetTag">{{asset}}</p></td>
                    <td style="color: black;"  data-ng-if="!item.scannedAssetTag.length" >NULL</td>
                    <td style="color: black;">{{diffs[id].notScannedQty}}</td>
                </tr>
                <!-- </div> -->
                </tbody>
            </table>
            <br>
            <div class="row">

                <button class="btn" style="color-red ; margin : 8px "  data-ng-click="cancel()">Cancel</button>
                <button class="btn " style="float: right; margin: 8px;" data-ng-click="submit()">Submit</button>


            </div>

        </div>

    </div>

</script>