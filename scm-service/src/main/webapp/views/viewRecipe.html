<div
        class="row white z-depth-3"
        data-ng-init="init()">
    <div class="col s12">
        <h3>View Product Recipe</h3>
    </div>
    <div class="row">
        <div class="col s12 m6 l6">
            <label>Select Product:</label>
            <select ui-select2="{allowClear:true, placeholder: 'Enter name of product'}" data-ng-model="selectedProduct" data-ng-change="reset()"
                    ng-options="product as product.productName for product in scmProductDetails"></select>
        </div>
        <div class="col s12 m6 l6">
            <label>select Quantity:</label>
            <input
                    type="number"
                    min="0.1"
                    placeholder="Enter Quantity"
                    name="selectedQuantityName"
                    id="selectedQuantityId"
                    data-ng-change="getPlanForProduct()"
                    data-ng-model="selectedQuantity"/>
        </div>
    </div>
    <button class="btn" data-ng-if="selectedQuantity != null" data-ng-click="getRecipe()" data-target="semiFinishedPlanModal" modal>Get Recipe Items</button>

</div>

<div id="semiFinishedPlanModal" class="modal modal-mx">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <h5>Recipe Data Item</h5>
            </div>
        </div>
        <div class="row" data-ng-show="!showPrepPlan">
            <div class="col s12">
                <h6>{{selectedProduct.productName}}</h6>
                <label>Preparation quantity:{{selectedQuantity}}</label>
            </div>
        </div>
        <div class="row" data-ng-show="showPrepPlan">
            <div class="col s12">
                <p>
                    <button class="btn btn-xs-small" data-ng-click="showPrepPlan=false">Back</button>
                </p>
                <table class="table bordered striped" style="margin-bottom: 10px">
                    <tr>
                        <th>Item: <a data-ng-click="showPreview($event, prepPlanData.planOrderItem.id,'PRODUCT')">{{prepPlanData.planOrderItem.name}}
                            [{{prepPlanData.planOrderItem.id}}]</a></th>
                        <th>Uom: {{selectedPlanProduct.unitOfMeasure}}</th>
                        <th>Recipe Id: {{prepPlanData.recipeId}}</th>
                        <th>Preparation Qty: {{prepPlanData.preparationQuantity}}</th>
                        <th>Recipe Output Uom: {{formatRecipeOutput(prepPlanData)}}</th>
                    </tr>
                </table>
                <div class="planBody">
                    <div class="row planItem planItemHead">
                        <div class="col s2">Steps</div>
                        <div class="col s4">Ingredients</div>
                        <div class="col s3">Quantity</div>
                        <div class="col s3">Instructions</div>
                    </div>
                    <div data-ng-class="{'row planItem':item.planOrderItemPrepItems.length==0, 'row planItem planItemHead':item.planOrderItemPrepItems.length>0}"
                         data-ng-repeat="item in prepPlanData.planOrderItemPrepItems track by $index">
                        <div class="col s2">Step {{item.stepIndex}}</div>
                        <div class="col s4"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a>
                        </div>
                        <div class="col s3">{{item.quantity | formatuom :item.unitOfMeasure :item.uomConversionMapping }}</div>
                        <div class="col s3">{{item.instructions}}</div>
                        <div data-ng-if="item.planOrderItemPrepItems.length>0" class="row planBody">
                            <div class="row planItem" data-ng-repeat="itemx in item.planOrderItemPrepItems">
                                <div class="col s2"></div>
                                <div class="col s4"><a data-ng-click="showPreview($event, itemx.productId,'PRODUCT')">{{itemx.productName}}</a>
                                </div>
                                <div class="col s3">{{itemx.quantity | formatuom :itemx.unitOfMeasure :itemx.uomConversionMapping }}</div>
                                <div class="col s3">{{itemx.instructions}}</div>
                            </div>
                            <div class="row recipeNotes" data-ng-if="item.recipeNotes!=null">Step {{item.stepIndex}} -
                                Instructions: <p ng-bind-html="item.recipeNotes"></p></div>
                        </div>
                    </div>
                    <div class="row recipeNotes" data-ng-if="prepPlanData.recipeNotes!=null">Instructions:<p
                            ng-bind-html="prepPlanData.recipeNotes"></p>
                            <div style=" flex:1; align-self: center" data-ng-if="prepPlanData.imagesURL!==null" ng-repeat="image in prepPlanData.imagesURL">
                                <img width="100%"
                                     data-ng-src="{{image}}"/>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <div class="row">
            <div class="col s12">
                <button class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px" data-ng-if="showPrepPlan" data-ng-click="savePlanForSemiFinished()">
                    Save and Print
                </button>
                <button class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px">Close
                </button>
            </div>
        </div>
    </div>
</div>


<!--   printable TO section   -->
<div style="width: 100%; font-size: 12px;" id="printSection">
    <h4 style="text-align: center">Semi Finished Item Preparation Plan</h4>
    <div class="row">
        <div class="col s12">
            <p><b>Plan Item:</b> {{printPlanData.planOrderItem.name}} [{{printPlanData.planOrderItem.id}}]</p>
            <p><b>Recipe Id:</b> {{printPlanData.recipeId}}</p>
            <p><b>Preparation Qty:</b> {{printPlanData.preparationQuantity}}
                {{printPlanData.planOrderItem.unitOfMeasure}}</p>
            <p><b>Requested By:</b> {{printPlanData.requestedBy.name}} [{{printPlanData.requestedBy.id}}]</p>
            <p><b>Requesting Time:</b> {{printPlanData.requestingTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</p>
            <p><b>Recipe Output Uom:</b> {{formatRecipeOutput(printPlanData)}}</p>
        </div>
    </div>
    <div class="planBody">
        <div class="row planItem planItemHead" style="margin-bottom: 10px;">
            <div class="col s2">Steps</div>
            <div class="col s4">Ingredients</div>
            <div class="col s3">Quantity</div>
            <div class="col s3">Instructions</div>
        </div>
        <div data-ng-class="{'row planItem':item.planOrderItemPrepItems.length==0, 'row planItem planItemHead':item.planOrderItemPrepItems.length>0}"
             data-ng-repeat="item in printPlanData.planOrderItemPrepItems track by $index">
            <div class="col s2">Step {{item.stepIndex}}</div>
            <div class="col s4">{{item.productName}}</div>
            <div class="col s3" >{{item.quantity | formatuom :item.unitOfMeasure :item.uomConversionMapping}}</div>
            <div class="col s3">{{item.instructions}}</div>
            <div data-ng-if="item.planOrderItemPrepItems.length>0" class="row planBody">
                <div class="row planItem" data-ng-repeat="itemx in item.planOrderItemPrepItems">
                    <div class="col s2"></div>
                    <div class="col s4">{{itemx.productName}}</div>
                    <div class="col s3">{{itemx.quantity | formatuom :itemx.unitOfMeasure :itemx.uomConversionMapping}}</div>
                    <div class="col s3">{{itemx.instructions}}</div>
                </div>
                <div class="row recipeNotes" data-ng-if="item.recipeNotes!=null">Step {{item.stepIndex}} - Instructions:
                    <p ng-bind-html="item.recipeNotes"></p></div>
            </div>
        </div>
    </div>
    <div class="row recipeNotes" style="margin-top: 200px;background: none;"
         data-ng-if="printPlanData.recipeNotes!=null">Instructions : <p ng-bind-html="printPlanData.recipeNotes"></p>
    </div>
    <div class="image" data-ng-if="printPlanData.imagesURL!==null" ng-repeat="image in printPlanData.imagesURL">
        <img style="height: 100%; width: 100%"  data-ng-src="{{image}}"/>
    </div>
</div>
