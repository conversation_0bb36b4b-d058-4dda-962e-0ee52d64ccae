<style>
    div.service-order {
        background: #f1f5f7;
        box-shadow: 0px 1px 2px 0px #847474;
        border: 1px solid #efdfdf;
    }

    div.soNumber {
        line-height: 32px;
        background: #ea9e13;
        padding: 3px 5px;
        font-weight: 700;
        border-radius: 3px;
    }

    .custom-modal {
        width: 50% !important;
    }

    .custom-modal ul li div.row{
        margin: 0 !important;
        padding: 5px !important;
        border-radius: 3px !important;
    }

    .custom-modal ul li div.row div{
        line-height:28px;
    }

    .custom-modal ul li:nth-child(even){
        background-color: #f4f0f0;
    }

    .custom-col-header{
        font-size:16px;
        font-weight:700;
        color:#6a5353;
    }

    h5 {
        font-weight: 700;
    }

    ul.unit-collection {
        overflow: auto;
        max-height: 28rem;
    }
</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Create SR for Vendor</h4>
            </div>
            <div class="row">
                <div class="col s6">
                    <div class="row">
                        <div class="col s4">
                            <b>Vendor :</b>
                        </div>
                        <div class="col s8" data-ng-show="!showExpandedView">
                            <select ui-select2="selectedVendor" id="vendorList" name="vendorList"
                                    data-ng-model="selectedVendor"
                                    data-ng-options="vendor as vendor.name for vendor in vendorList track by vendor.id"
                                    data-ng-change="selectVendor(selectedVendor)"></select>
                        </div>
                        <div class="col s8" data-ng-show="showExpandedView">
                            <span>{{selectedVendor.name}}</span>
                        </div>
                    </div>
                </div>
                <div class="col s6" data-ng-if="locationList.length>0 && selectedVendor != null">
                    <div class="row">
                        <div class="col s4">
                            <b>Dispatch Location:</b>
                        </div>
                        <div class="col s8" data-ng-show="!showExpandedView">
                            <select id="locationList" name="locationList" data-ng-model="selectedLocation"
                                    data-ng-options="location as location.name for location in locationList track by location.id"
                                    data-ng-change="selectDispatchLocation(selectedLocation)"></select>
                        </div>
                        <div class="col s8" data-ng-show="showExpandedView">
                            <span>{{selectedLocation.name}}</span>
                        </div>
                    </div>
                </div>
            </div>
             <div class="row">
             
             
                <div class="col s6" data-ng-if="companies">
                    <div class="row">
                        <div class="col s4">
                            <b>Company :</b>
                        </div>
                        <div class="col s8" data-ng-show="!showExpandedView">
                           <select ui-select2 id="companies" name="companies" data-ng-model="selectedCompany"
                            data-ng-change="selectCompany(selectedCompany)"
                            data-ng-options="item as item.name for item in companies track by item.id"></select>
                        </div>
                        <div class="col s8" data-ng-show="showExpandedView">
                            <span>{{selectedCompany.name}}</span>
                        </div>
                    </div>
                </div>
                <div class="col s6" data-ng-if="locations.length>0">
                    <div class="row">
                        <div class="col s4">
                            <b>Delivery Location:</b>
                        </div>
                        <div class="col s8" data-ng-show="!showExpandedView">
                            <select ui-select2 id="locations" name="locations" data-ng-model="selectedDelLocation"
                            data-ng-change="selectDelLocation(selectedDelLocation)"
                            data-ng-options="item as item.name for item in locations track by item.id"></select>
                        </div>
                        <div class="col s8" data-ng-show="showExpandedView">
                            <span>{{selectedDelLocation.name}}</span>
                        </div>
                        
                    </div>
                </div>
                <div class="row" data-ng-if="locations.length>0">
                 <div class="col s3" data-ng-if="!showExpandedView">
                            <button data-ng-click="getPendingOrders()" style="margin-left:400px" class="btn">
                                Search
                            </button>
                        </div>
                </div>
            </div>
        </div>

        <div class="row white z-depth-3 custom-listing-li" style="padding:5px;" data-ng-show="!showExpandedView"
             data-ng-if="pendingSOs !=undefined && pendingSOs.length>0">
            <h5>
                Pending Service Orders for: {{selectedVendor.name}}({{selectedDispatchLocation.name}})</h5>
            <div class="col s12">
                <button tooltipped data-tooltip="Select SOs to move forward"
                        data-ng-click="selectSOs()" class="btn right">Select Orders
                </button>
            </div>
            <ul class="col s12" data-collapsible="accordion" watch>
                <li class="row" data-ng-repeat="so in pendingSOs track by so.id">
                    <div class="collapsible-header waves-effect waves-light lighten-5 service-order">
                        <div class="left" data-ng-click="$event.stopPropagation()">
                            <input id="RO-{{so.id}}" data-ng-model="so.checked" type="checkbox" data-ng-click="disableSoSelection($event, so,pendingSOs)" data-ng-disabled="so.disabled || so.vendorBlocked"/>
                            <label for="RO-{{so.id}}">SO# {{so.id}} Created: {{so.generationTime | date:'dd/MM/yyyy'}}
                                for Rs.{{so.totalAmount.toFixed(2)}}</label>
                        </div>
                        <div class="right">
                            <span class="chip">{{so.status}}</span>
                            <i class="fa fa-caret-down right"></i>
                        </div>
                        <div class="right" style="margin-right: 15px;" data-ng-if="so.vendorAdvancePayments != null && so.vendorAdvancePayments.length > 0">
                            <span class="chip">Advance SO</span>
                        </div>
                    </div>
                    <div class="collapsible-body">
                        <table class="bordered striped">
                            <thead>
                            <tr>
                                <th class="center-align">Cost Element</th>
                                <th class="center-align">UOM</th>
                                <th class="center-align">Total Qty</th>
                                <th class="center-align">Price</th>
                                <th class="center-align" tooltipped
                                    data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*
                                </th>
                                <th class="center-align">Taxes</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in so.orderItems track by $index">
                                <td class="center-align">{{item.costElementName}}[{{item.ascCode}}]({{item.costElementDate| date :'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})</td>
                                <td class="center-align">{{item.unitOfMeasure}}</td>
                                <td class="center-align">{{item.requestedQuantity.toFixed(6)}}</td>
                                <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                <td class="center-align">{{(item.totalCost + item.totalTax).toFixed(2)}}</td>
                                <td class="center-align">{{item.totalTax.toFixed(2)}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
        </div>

        <div id="expandedSoView" class="row white z-depth-3 custom-listing-li"
             style="padding:5px;" data-ng-show="showExpandedView">
            <div class="row">
                <div class="col s12">
                    <div class="col s9 center-align">
                        <button class="btn btn-medium" data-ng-click="fillAmount()">{{filled ? "Clear Amount" : "Fill Amount"}}</button>
                    </div>
                    <h5 style="min-height: 35px; margin-top: 0px;">
                        <button class="right btn btn-small" data-ng-click="goBack()">Cancel SR</button>
                    </h5>
                </div>
            </div>
			<div class="col s12">
				<div class="row">
					<ul class="col s12">
						<li class="row margin0"
							data-ng-repeat="so in selectedOrders track by so.id">
							<div class="soNumber">
                                SO# {{so.id}} <span class="chip grey white-text">Total Amount {{so.totalAmount.toFixed(2)}}</span>
                                <span class="chip blue-grey white-text">Total Pending Amount {{so.pendingAmount.toFixed(2)}}</span>
                                <span class="chip red darken-1 white-text">Total Threshold Amount {{so.thresholdWithPending.toFixed(2)}}</span>
                                <span class="chip right">{{so.status}}</span>
                                <span class="chip yellow darken-1 white-text">Consumed {{so.consumed.toFixed(2)}}</span>
                                <span class="chip light-blue darken-1 white-text">Remaining(W/T) {{so.remaining.toFixed(2)}}</span>
                                <span class="chip green darken-1 white-text">Taxable Amount(Wo/T) {{so.remainingWithoutTax.toFixed(2)}}</span>
                            </div>
							<table class="bordered striped ressonsive-table"
								style="font-size: 11px;">
								<thead>
									<tr>
										<th class="center-align">Cost Element</th>
										<th class="left-align">BCC</th>
										<th class="center-align">UOM</th>
										<th class="center-align">Requested(in UOM)</th>
										<th class="center-align">Amount</th>
										<th class="center-align">Pending(in UOM)</th>
                                        <th class="center-align">Pending Amount</th>
										<th class="center-align">Unit Price</th>
										<th class="center-align">Tax Rate(%)</th>
										<th class="center-align">Received Qty</th>
										<th class="center-align">Received Amount</th>
										<th class="center-align">New Total Amount</th>
									</tr>
								</thead>
								<tbody>
									<tr data-ng-repeat="item in so.orderItems | orderBy:['costElementName','businessCostCenterName']  track by $index">
										<td class="center-align pointer" tooltipped
											data-tooltip="{{item.serviceDescription}}">
											{{item.costElementName}} ({{item.costElementDate| date :'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})</td>
										<td class="center-align">{{item.businessCostCenterName}}</td>
										<td class="center-align">{{item.unitOfMeasure}}</td>
										<td class="center-align">{{item.requestedQuantity}}</td>
										<td class="center-align" tooltipped
											data-tooltip="Cost with Taxes:{{(item.totalCost + item.totalTax).toFixed(2)}}">
											{{(item.totalCost).toFixed(2)}}</td>
										<td class="center-align">{{(item.pendingQuantity).toFixed(2)}}</td>
                                        <td class="center-align">{{(item.pendingQuantity*item.unitPrice).toFixed(2)}}</td>
										<td class="center-align">{{item.unitPrice}}</td>
										<td class="center-align">{{item.taxRate}}</td>
										<td class="center-align"><input type="number" min="0" disabled
											step="0.001" ng-model="item.received" string-to-number></td>
										<td class="center-align"><input type="text" min="0"
											step="0.01" data-ng-change="updateSRQty(item, so)" ng-model="item.receivedCost"></td>
										<td class="center-align"><input type="text" min="0" disabled
											step="0.01" ng-model="item.totalAmount"></td>
									</tr>
								</tbody>
							</table>
						</li>
					</ul>
					
				</div>
			</div>
			<div class="col s12 right-align">
					<div class="row" style="line-height: 30px;">
						<b>Taxable Amount:</b> Rs.{{billAmount.toFixed(2)}}<br /> 
						<b>Total Taxes:</b> Rs.{{totalTaxes.toFixed(2)}}<br />
						<b>Paid Amount:</b> Rs.{{paidAmount.toFixed(2)}}
					</div>
					</div>
   
			<div class="col s12 right">
						<button class="right btn" style="margin-top: 50px"
							data-ng-click="submit()">Submit</button>
					</div>

		</div>
    </div>
</div>


<!-- Cost Allocation Modal -->
<script type="text/ng-template" id="allocateCost.html">
    <div class="row" data-ng-init="initCostModal()">
        <h5 style="color:#6a5353;">Allocate {{item.costElementName}} for Rs.{{item.receivedCost}}</h5>
        <label><i>{{item.serviceDescription}}</i></label>
        <hr>
        <ul class="collection-with-header unit-collection">
            <li class="collection-header">
                <div class="row">
                    <div class="col s6">
                        <span class="custom-col-header">Unit Name</span>
                    </div>
                    <div class="col s3">
                        <span class="custom-col-header">Received Qty</span>
                    </div>
                    <div class="col s3">
                        <span class="custom-col-header">Cost Allocated</span>
                    </div>
                </div>
            </li>
            <li class="collection-item" data-ng-repeat="unit in units | orderBy : 'name'">
                <div class="row">
                    <div class="col s6">
                        {{unit.name}}
                    </div>
                    <div class="col s3">
                        {{unit.qty!=undefined ? unit.qty : 0}}
                    </div>
                    <div class="col s3">
                        <input type="number" step="0.01" min="0"
                               data-ng-change="updateQty(unit)" data-ng-model="unit.allocatedCost">
                    </div>
                </div>
            </li>
        </ul>
        <div class="row" data-ng-if="totalAllocated > 0">
            <div class="right">
                <b>Total Allocated:</b> Rs.{{totalAllocated}}
            </div>
        </div>
    </div>
    <hr>
    <div class="row">
        <button class="btn right margin-right-5" data-ng-click="submit()">Submit</button>
        <button class="btn right margin-right-5" data-ng-click="cancel()">Cancel</button>
    </div>
</script>


