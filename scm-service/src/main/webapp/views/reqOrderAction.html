<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s2">
                <input type="button" data-ng-click="backToReqOrderMgt()" value="Back" class="btn"
                       style="margin-top:28px;"/>
            </div>
            <div class="col s10">
                <h3>Request Order Detail</h3>
            </div>
        </div>
        <div class="row">
            <div class="col s1"><label>RO Id</label>{{requestOrderDetail.id}}</div>
            <div class="col s3"><label>Creation time</label>{{requestOrderDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}
            </div>
            <div class="col s3"><label>Requesting unit</label>{{requestOrderDetail.requestUnit.name}}</div>
            <div class="col s3" data-ng-if="requestOrderDetail.fulfillmentUnit.id!=null">
                <label>Fulfillment unit</label>
                {{requestOrderDetail.fulfillmentUnit.name}}
            </div>
            <div class="col s2"><label>Status</label>{{requestOrderDetail.status}}</div>
        </div>
        <div class="row">
            <div class="col s3"><label>Last updated</label>{{requestOrderDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
            <div class="col s2"><label>Generated by</label>{{requestOrderDetail.generatedBy.name}}</div>
            <div class="col s2"><label>Last updated by</label>{{requestOrderDetail.lastUpdatedBy.name}}</div>
            <div class="col s3"><label>Fulfillment date</label>{{requestOrderDetail.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
            <div class="col s2"><label>Bulk Order</label>{{requestOrderDetail.bulkOrder ? "Yes" : "NO"}}</div>
        </div>
        <div class="row">
            <div class="col s4" data-ng-if="requestOrderDetail.parentROs.length > 0">
                <label>Parent ROs:</label>
                    <a data-ng-repeat="roId in requestOrderDetail.parentROs" style="display: inline-block; cursor: pointer;"
                                   data-ng-click="getRefOrderDetail(roId, requestOrderDetail.id)" class="margin-right-5">{{roId}}</a>
            </div>
            <div class="col s4">
                <label>Comment: {{requestOrderDetail.comment==null?'No comments':requestOrderDetail.comment}}</label>
            </div>
            <div class="col s3">
                <button print-btn class="btn btn-primary" tooltipped
                        data-tooltip="Print">
                    <i class="fa fa-print"></i>
                </button>
            </div>
            <!-- <div class="col s4">
                <label data-ng-if="requestOrderDetail.searchTag!=undefined && requestOrderDetail.searchTag!=null">
                    Search Tag: {{requestOrderDetail.searchTag}}
                </label>
                <div data-ng-if="requestOrderDetail.searchTag==null">
                    <label>Add Search Tag:</label>
                    <input type="text" placeholder="Enter Search Tag" maxlength="20"
                           data-ng-model="searchTagForRO" style="display:inline-block;width:50%;"/>
                    <button class="btn" data-ng-click="addTag(requestOrderDetail, searchTagForRO)">Add</button>
                </div>
            </div> -->
        </div>
        <div class="row">
            <div class="col s12">
                <ul class="collection">
                    <li class="collection-item list-head">
                        <div class="row">
                            <div class="col s2">Product Name</div>
                            <div class="col s2">Requested Qty.</div>
                            <div class="col s2">Absolute Qty.</div>
                            <div class="col s2">Transferred Qty.</div>
                            <div class="col s2">Received Qty.</div>
                            <div class="col s1">UOM</div>
                            <div class="col s1">Excess Qty.</div>
                        </div>
                    </li>
                    <li class="collection-item"
                        data-ng-repeat="item in requestOrderDetail.requestOrderItems | orderBy : 'productName' track by $index">
                        <div class="row" style="margin-bottom: 0px;">
                            <div class="col s2"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}} [{{item.productCode}}]</a>
                                <span style="color:red;font-weight: bold;" data-ng-if="requestOrderDetail.specialOrder==true">({{item.vendor.name}})</span></div>
                            <div class="col s2">{{item.requestedQuantity}}</div>
                            <div class="col s2">{{item.requestedAbsoluteQuantity}}</div>
                            <div class="col s2">{{item.transferredQuantity}}</div>
                            <div class="col s2">{{item.receivedQuantity}}</div>
                            <div class="col s1">{{item.unitOfMeasure}}</div>
                            <div class="col s1">{{item.excessQuantity}}</div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row">
            <div class="row">
                <div class="col s6 left-align">
                    <input type="button" class="btn" data-ng-click="backToReqOrderMgt()" value="Back" class="btn"/>
                </div>
                <div class="col s3 right-align">
                    <input type="button" class="btn" value="CANCEL" data-ng-if="requestOrderDetail.status=='CREATED' &&
                           (requestOrderDetail.referenceOrderType == null || requestOrderDetail.referenceOrderType != 'FOUNTAIN9_DATA_SOURCE_ORDERING' || canBeCancelled)"
                           data-ng-click="cancelRequestOrder()" acl-action="MTRQOMC" />
                    <input type="button" class="btn red" value="CANCEL F9 RO" data-ng-if="requestOrderDetail.status=='CREATED' && requestOrderDetail.referenceOrderType == 'FOUNTAIN9_DATA_SOURCE_ORDERING'"
                           data-ng-click="cancelRequestOrder()" acl-action="CF9RO" />
                </div>
                <div class="col s3 right-align">
                    <button class="btn" data-ng-click="cloneRO(requestOrderDetail)">Clone RO</button>
                </div> 
            </div>
        </div>
    </div>




    <!--   printable TO section   -->
    <div style="width: 100%;" id="printSection">
        <div class="row"  data-ng-if="requestOrderDetail.transferType != 'TRANSFER'">
            <!--   print-only  -->
            <div class="col s12">
                <p style="text-align: center;">
                    <b><em><span style="font-family: 'Cambria', serif;">Form
								GST INV &ndash; 1<br />
						</span></em></b><b><span
                        style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
                        style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[requestOrderDetail.requestCompany.id].name}}<br />
					</span></b><span
                        style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.line1}},
						{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.line2}}, <br /> {{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.city}},
						{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.state}}, <br /> {{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.country}},
						{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.zipCode}}<br />
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Invoice</span></b> <br />
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        cellpadding="0cm 5.4pt">
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Unit Details</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">

                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Unit Details </span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Billed To/Shipped To</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.name}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.name}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.requestCompany.id].name}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.fulfillmentCompany.id].name}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.line1}},
										{{unitData.address.line2}}, <br />
										{{unitData.address.city}},
										{{unitData.address.state}}, <br />
										{{unitData.address.country}},
										{{unitData.address.zipCode}}
									</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.address}}
									</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.location.state.name}}/{{unitData.location.state.code}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.state}}/{{generatedForUnitData.stateCode}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.tin}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.tin}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.requestCompany.id].cin}}</span>
                            </p>
                        </td>

                        <td
                                style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.fulfillmentCompany.id].cin}}</span>
                            </p>
                        </td>
                    </tr>

                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>

                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <table>
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
                                colspan="2">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Details
											</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice
										No.</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{requestOrderDetail.invoiceId}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">

                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Order ID
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{requestOrderDetail.id}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date
										of Invoice</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{requestOrderDetail.generationTime
										| date:'dd-MM-yyyy':'+0530'}}</span>
                            </p>
                        </td>
                    </tr>

                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{requestOrderDetail.requestOrderItems.length}}</span>
                            </p>
                        </td>

                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Mode
										of Transport : </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">By
										Road / By Train / By Air</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place
										Of Supply:</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.city}}</span>
                            </p>
                        </td>

                        <td
                                style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vehicle
										No :</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">____________________</span>
                            </p>
                        </td>
                    </tr>
                    <!--<tr style="height: 12.00pt; page-break-inside: avoid;">-->
                      <!---->
                    <!--</tr>-->
                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>

                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        width="765">
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="100">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="61">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 33.9pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="45">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">RO Qty.</span></b>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="49">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transferred Qty.</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <td
                                style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Uom</span></b>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="67">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="84">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;" width="89">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <td data-ng-repeat="t in availableTaxes"
                            style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                            width="{{132/availableTaxes.length}}">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t}}</span></b>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 66.8pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="89">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                    </tr>
                    <tr style="height: 12.00pt;  page-break-inside: avoid;"
                        data-ng-repeat="item in requestOrderDetail.requestOrderItems track by $index">
                        <td
                                style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="100">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.productName}} [{{item.productCode}}]</span>
                            </p>
                        </td>
                        <td
                                style="width: 46.1pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="61">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.code}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 33.9pt; border-top: none; border-left: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="45">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.requestedQuantity}}</span>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="49">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.transferredQuantity}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                        <td
                                style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.unitOfMeasure}}</span>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="67">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.negotiatedUnitPrice-->
										<!--| currency :'': 2}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="84">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{(item.requestedQuantity * item.negotiatedUnitPrice) | currency :'': 2}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="89">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"-->
                                            <!--ng-repeat="packaging in item.packagingDetails">-->
										<!--{{packaging.numberOfUnitsPacked}} &#45;&#45;-->
										<!--{{packaging.packagingDefinitionData.packagingName}} <br />-->
									<!--</span>-->
                            <!--</p>-->
                        <!--</td>-->
                         <!--<td-->
                            <!--style="width: 49.2pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                            <!--width="66">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
                                <!--<span-->
                                    <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.gst}} ({{item.gstPercentage}}%)</span>-->
                            <!--</p>-->
                        <!--</td>-->
                        <td data-ng-repeat="t in item.taxes"
                            style="border: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="{{132/item.taxes.length}}">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"
                                            > {{t.percentage}}% <br /> </span>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 66.8pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="89">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{(item.requestedQuantity * item.negotiatedUnitPrice) + item.tax-->
										<!--| currency :'': 2}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                    </tr>
                    <!--<tr style="height: 12.00pt; page-break-inside: avoid;">-->

                    <!--</tr>-->
                    <!--<tr style="height: 12.00pt; page-break-inside: avoid;">-->
                        <!--<td-->
                                <!--style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--colspan="{{6+ availableTaxes.length}}" width="676">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="89">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice-->
										<!--| currency :'': 2}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                    <!--</tr>-->
                    <!--<tr style="height: 12.00pt; page-break-inside: avoid;">-->
                        <!--<td-->
                                <!--style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--colspan="{{6 + availableTaxes.length}}" width="676">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif;">Total-->
											<!--Invoice Value (In figure)</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style="width: 66.8pt; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="89">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice-->
										<!--| currency :'': 2}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                    <!--</tr>-->
                    <!--<tr style="height: 12.00pt; page-break-inside: avoid;">-->
                        <!--<td-->
                                <!--style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--colspan="{{7 + availableTaxes.length}}" width="765">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total-->
											<!--Invoice Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>-->
                                <!--<b><span-->
                                        <!--style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalPriceInWords}}</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                    <!--</tr>-->
                    </tbody>
                </table>
                <p style="margin: .0001pt 0; line-height: normal;">
					<span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <p style="line-height: normal; margin: 0cm 0cm .0001pt">
                    <b><span style="font-family: 'Cambria', serif;">Certified
							that the particulars and the amount indicated given above are
							true and correct </span></b>
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        width="765">
                    <tbody>
                    <tr style="height: 20.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 404.0pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                                width="520">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 14.0pt; font-family: 'Cambria', serif; color: black;">TERMS
											OF SALE</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 6.0cm; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-family: 'Cambria', serif; color: black;">For
											{{companyMap[requestOrderDetail.requestCompany.id].name}}</span></b>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 73.6pt; page-break-inside: avoid;">
                        <td
                                style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                                width="510">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">1)Goods
										once sold will not be taken back or exchanged&nbsp; </span>
                            </p>
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">2)Seller is
										not responsible for any loss or damaged of goods in transit</span>
                            </p>
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">3)Buyer
										undertakes to submit prescribed declaration to sender on
										demand.</span>
                            </p>
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">4)Disputes if
										any will be subject to seller court jurisdiction</span>
                            </p>
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                            </p>
                        </td>
                        <td
                                style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                                width="256">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 17.45pt; page-break-inside: avoid;">
                        <td
                                style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
                                width="520">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                            </p>
                        </td>
                        <td
                                style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span style="font-family: 'Cambria', serif;">Authorised
											Signatory</span></b>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">Reg. Address: {{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.line1}},
										{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.line2}},
										{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.city}},
										{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.state}},
										{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.country}},
										Pin No:
					{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.zipCode}} </span>
                </p>
            </div>
        </div>

        <div class="row" data-ng-if="requestOrderDetail.transferType == 'TRANSFER'">
            <!--   print-only  -->
            <div class="col s12">
                <p style="text-align: center;">
                    <b><span
                            style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
                        style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[requestOrderDetail.requestCompany.id].name}}<br />
					</span></b><span
                        style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.line1}},
						{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.line2}}, <br /> {{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.city}},
						{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.state}}, <br /> {{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.country}},
						{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.zipCode}}<br />
						</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">STOCK TRANSFER NOTE (INTERNAL)</span></b> <br />
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        cellpadding="0cm 5.4pt">
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Unit Details</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">

                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Unit Details </span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">

                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.name}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.name}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.requestCompany.id].name}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.fulfillmentCompany.id].name}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.line1}},
										{{unitData.address.line2}}, <br />
										{{unitData.address.city}},
										{{unitData.address.state}}, <br />
										{{unitData.address.country}},
										{{unitData.address.zipCode}}
									</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.address}}
									</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.location.state.name}}/{{unitData.location.state.code}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.state}}/{{generatedForUnitData.stateCode}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.tin}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.tin}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.requestCompany.id].cin}}</span>
                            </p>
                        </td>

                        <td
                                style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[requestOrderDetail.fulfillmentCompany.id].cin}}</span>
                            </p>
                        </td>
                    </tr>

                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>

                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <table>
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
                                colspan="2">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Details
											</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice
										No.</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{requestOrderDetail.invoiceId}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">

                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Order ID
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{requestOrderDetail.id}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date
										of Invoice</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{requestOrderDetail.generationTime
										| date:'dd-MM-yyyy':'+0530'}}</span>
                            </p>
                        </td>
                    </tr>

                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{requestOrderDetail.requestOrderItems.length}}</span>
                            </p>
                        </td>

                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Mode
										of Transport : </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">By
										Road / By Train / By Air</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place
										Of Supply:</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.city}}</span>
                            </p>
                        </td>

                        <td
                                style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vehicle
										No :</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">____________________</span>
                            </p>
                        </td>
                    </tr>
                    <!--<tr style="height: 12.00pt; page-break-inside: avoid;">-->
                    <!---->
                    <!--</tr>-->
                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        width="765">
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="100">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
                            </p>
                        </td>
                        		<!--<td-->
                                    <!--style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                    <!--width="61">-->
                                    <!--<p-->
                                        <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                        <!--<b><span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>-->
                                    <!--</p>-->
                                <!--</td>-->
                        <td
                                style="width: 33.9pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="45">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">RO Qty.</span></b>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="49">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transferred Qty.</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <td
                                style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit</span></b>
                            </p>
                        </td>
                        <td data-ng-repeat="t in availableTaxes"
                            style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                            width="{{132/availableTaxes.length}}">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t}}</span></b>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="67">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;" width="89">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="84">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>-->
                            <!--</p>-->
                        <!--</td>-->

                    </tr>
                    <tr style="border: solid windowtext 1.0pt;height: 12.00pt;  page-break-inside: avoid;"
                        data-ng-repeat="item in requestOrderDetail.requestOrderItems track by $index">
                        <td
                                style="width: 74.85pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="100">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.productName}}</span>
                            </p>
                        </td>
                        <!--<td-->
                            <!--style="width: 46.1pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                            <!--width="61">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
                                <!--<span-->
                                    <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.code}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                        <td
                                style="width: 33.9pt; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="45">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.requestedQuantity}}</span>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="49">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.transferredQuantity}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                        <td
                                style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.unitOfMeasure}}</span>
                            </p>
                        </td>
                        <!--<td-->
                                <!--style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="67">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.negotiatedUnitPrice-->
										<!--| currency :'': 2}}</span>-->
                            <!--</p>-->
                        <!--</td>-->

                        <!--<td-->
                                <!--style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="89">-->
                            <!--<p-->
                                    <!--style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"-->
                                            <!--data-ng-repeat="packaging in item.packagingDetails">-->
										<!--{{packaging.numberOfUnitsPacked}} &#45;&#45;-->
										<!--{{packaging.packagingDefinitionData.packagingName}} <br />-->
									<!--</span>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="84">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.requestedQuantity*item.negotiatedUnitPrice | currency :'': 2}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                        <td data-ng-repeat="t in item.taxes"
                            style="border: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="{{132/item.taxes.length}}">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"
                                    > {{t.percentage}}% <br /> </span>
                            </p>
                        </td>
                    </tr>
                    <!--<tr style="height: 12.00pt; page-break-inside: avoid;">-->
                        <!--<td-->
                                <!--style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--colspan="4" width="676">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                        <!--<td-->
                                <!--style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--width="89">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
									<!--<span-->
                                            <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice-->
										<!--| currency :'': 2}}</span>-->
                            <!--</p>-->
                        <!--</td>-->
                    <!--</tr>-->
                    <!--<tr style="height: 12.00pt; page-break-inside: avoid;">-->
                        <!--<td-->
                                <!--style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
                                <!--colspan="5" width="765">-->
                            <!--<p style="margin: .0001pt 0; line-height: normal;">-->
                                <!--<b><span-->
                                        <!--style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total-->
											<!--Invoice Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>-->
                                <!--<b><span-->
                                        <!--style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalPriceInWords}}</span></b>-->
                            <!--</p>-->
                        <!--</td>-->
                    <!--</tr>-->
                    </tbody>
                </table>
                <p style="margin: .0001pt 0; line-height: normal;">
					<span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <p style="line-height: normal; margin: 0cm 0cm .0001pt">
                    <b><span style="font-family: 'Cambria', serif;">This is to certify that the material has been dispatched as per the above details.</span></b>
                </p>

                <table
                        style="border-collapse: collapse; border: none;"
                        width="765">
                    <tbody>
                    <tr style="height: 20.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 6.0cm; border: solid windowtext 1.0pt; border-left: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-family: 'Cambria', serif; color: black;">For
											{{companyMap[requestOrderDetail.requestCompany.id].name}} ({{unitData.name}})</span></b>
                            </p>
                        </td>

                    </tr>
                    <tr style="height: 73.6pt; page-break-inside: avoid;">

                        <td
                                style="width: 6.0cm; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                            </p>
                        </td>

                    </tr>
                    <tr style="height: 17.45pt; page-break-inside: avoid;">
                        <td
                                style="width: 6.0cm; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span style="font-family: 'Cambria', serif;">Authorised
											Signatory</span></b>
                            </p>
                        </td>

                    </tr>
                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">Reg. Address: {{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.line1}},
					{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.city}},
					{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.state}},
					{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.country}}, Pin No:
					{{companyMap[requestOrderDetail.requestCompany.id].registeredAddress.zipCode}}</span>
                </p>
            </div>
        </div>
    </div>





</div>