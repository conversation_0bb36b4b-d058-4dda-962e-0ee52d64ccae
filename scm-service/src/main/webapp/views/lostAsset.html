<style>
    .card {
      /* Add shadows to create the "card" effect */
      box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
      transition: 0.3s;
    }

    /* On mouse-over, add a deeper shadow */
    .card:hover {
      box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
    }

    /* Add some padding inside the card container */
    .container {
      padding: 2px 16px;
    }
    .disabled {
    background: grey
    }

</style>
<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12" style="border:1px solid #26a69a; ">
        <div class="row">
            <div class="col s12">
                <h4 class="center" style="padding: 1% 0% 0.2% 0%;">Approval Requests - Asset Lost</h4>
            </div>
        </div>
    </div>

</div>
<div data-ng-repeat="$item in approvalList">
    <div class="searchingCard row white z-depth-3 custom-listing-li" style="width:80%;">
        <div class="card row">
            <div class="col s3" style="margin-left:-1.1%;margin-bottom: -0.5%;">
                <img src=" {{imageURL}}{{$item.skuImage}} " style="width:250px; height:250px; border: 7px solid #26a69a;">
            </div>
            <div class="container col s9" style="margin-top:-17px;margin-left:-0.9%;">
                <h4 style="background-color: #26a69a;padding: 2% 0px 2% 5%; color: white;"><b>{{$item.skuName}}</b></h4>
                <div class="row" >
                    <div class="col s5" style="margin-left:4%;">
                        <h6 style="padding:0.5%;"> <b>Asset Id</b> :         {{$item.assetId}}</h6>
                        <h6 style="padding:0.5%;"> <b>Procurement Cost</b> : {{$item.cost}}</h6>
                        <h6 style="padding:0.5%;"> <b>Requested On</b> :     {{$item.requestDate}}</h6>
                        <h6 style="padding:0.5%;"> <b>Requested By</b> :     {{$item.requestedByName}} ({{$item.requestedBy}})</h6>
                    </div>
                    <div class="col s5">
                        <h6 style="padding:0.5%;"> <b>Unit</b> :         {{$item.unitName}}</h6>
                        <h6 style="padding:0.5%;"> <b>Event Id</b> : {{$item.eventId}}</h6>
                    </div>
                </div>

                <div class="row s2 right-align" style="margin-top:-2.8%;">
                    <input type="button" class="btn" value="Reject" style="background-color: red;"
                           data-ng-click="processRequest($item.approvalRequestId,false);"/>
                    <input type="button" class="btn" value="Accept"
                           style="margin-left:10px;"
                           data-ng-click="processRequest($item.approvalRequestId,true);"/>
                </div>
            </div>

        </div>
    </div>
</div>

