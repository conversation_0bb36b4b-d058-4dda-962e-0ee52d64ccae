<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .grid {
        height: 98vh;
        padding: 10px !important;
    }
</style>


<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Search Goods Receive</h3>
            </div>
        </div>
        <div class="row searchingCard">
            <div class="col s2">
                <label>Start date</label>
                <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
            </div>
            <div class="col s2">
                <label>End date</label>
                <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
            </div>
            <div class="col s2">
                <label>Generation Unit</label>
                <select data-ng-model="generationUnit" data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
            </div>
            <div class="col s2">
                <label>Goods Receive Id</label>
                <input type="text" data-ng-model="goodsReceiveOrderId" />
            </div>
            <div class="col s2">
                <label>Status</label>
                <select data-ng-model="status" data-ng-change="show(status)" data-ng-options="item as item for item in scmOrderStatusList"></select>
            </div>
            <div class="col s2">
                <div class="form-group">
                    <label class="control-label">Select Sku</label>
                    <select id="skus" ui-select2="{allowClear:true, placeholder: 'Select Sku'}"
                            ng-options="sku as sku.name for sku in skuList"
                            data-ng-change="true" data-ng-model="selectedSku"></select>

                </div>
            </div>
            <div class="col s2 right">
                <input type="button" class="btn btn-small " value="Find" data-ng-click="findGoodsReceiveOrders()" style="margin-top: 24px;" acl-action="MTGRMV" />
            </div>
        </div>
    </div>


    <div class="row margin0" data-ng-if="goodsReceiveOrderList.length>0">
            <div
                    class="col s12 grid margin-right-5"
                    id="grid"
                    ui-grid="gridOptions"
                    ui-grid-save-state
                    ui-grid-edit
                    ui-grid-selection
                    ui-grid-resize-columns
                    ui-grid-move-columns
                    ui-grid-exporter
            ></div>
        </div>
        <div data-ng-show="goodsReceiveOrderList.length==0">No goods receive entries found for selected criteria!</div>

    </div>
</div>
