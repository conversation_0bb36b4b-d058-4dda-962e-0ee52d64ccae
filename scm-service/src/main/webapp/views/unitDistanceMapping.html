<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.grid {
	height: 500px;
	padding: 0 !important;
}

.select2.select2-container {
	width: auto !important;
}
</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	<div class="col s12">
		<div class="row">
			<div class="mappingBtn" style="margin-top: 10px; font-size: 40px;">
				Unit Distance Mapping</div>
		</div>
	</div>
	<div class="row" id="mappingDivDisplay">
		<div class="col s12">
			<div class="scm-form" style="padding: 10px 0;">
				<div class="col s6">
					<label class="black-text" for="requestForLable">Unit 1 </label> <select
						ui-select2="selectedUnit3" id="firstUnitData"
						name="mappingValuesShow" data-placeholder="Select value"
						data-ng-model="firstUnitMapping"
						data-ng-options="unit as unit.name for unit in UnitListDetails | orderBy: 'name' track by unit.id"></select>

					<label class="black-text" for="requestForLable">Unit 2 </label> <select
						ui-select2="selectedUnit3" id="secondUnitData"
						name="mappingValuesShow" data-placeholder="Select value"
						data-ng-model="secondUnitMapping"
						data-ng-options="unit as unit.name for unit in UnitListDetails | orderBy: 'name' track by unit.id"
						data-ng-change="searchMappingShow()"></select>
				</div>
			</div>
		</div>
		<div class="row">
		<div class="col s12" style="margin-top: 16px; margin-left: 11px;">
			<button class="btn btn-primary" type="button"
				ng-click="searchMappingShow()">Submit</button>
		</div>
	</div>
	</div>
</div>
<div class="row" data-ng-show="showDistance" id="mappingDivDisplay"
	style="margin-top: 30px">
	<label class="black-text" style="font-size: x-large;">Distance
	</label>
	<div class="col s12">
		<div class="scm-form" style="padding: 10px 0;">
			<div class="col s6">
				<div class="row">
					<label class="black-text" for="requestForLable">Unit 1 To
						Unit 2 (Kms) : </label> <input type="text" ng-model="unitOne">
				</div>
				<div class="row">
					<label class="black-text" for="requestForLable">Unit 2 To
						Unit 1 (Kms) : </label> <input type="text" ng-model="unitSecond">
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col s12">
			<button class="btn btn-primary" type="button"
				ng-click="submitDistanceData()">Submit</button>
		</div>
	</div>



</div>

