<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .table,th,td{
        border: 2px solid black;
        margin-bottom: 10px;
    }
</style>
<div
        class="row white z-depth-3"
        data-ng-init="init()">
    <div class="col s12">
        <h3>Bulk Transfer Management</h3>
    </div>
    <div class="row searchingCard">
            <div class="col s4">
                <label for="startDate">Select Start date</label>
                <input
                        input-date
                        type="text"
                        name="startDate"
                        id="startDate"
                        ng-model="startDate"
                        format="yyyy-mm-dd"
                        select-years="1"
                        />
            </div>
            <div class="col s4">
                <label for="endDate">Select End date</label>
                <input
                        input-date
                        type="text"
                        name="endDate"
                        id="endDate"
                        ng-model="endDate"
                        container=""
                        format="yyyy-mm-dd"
                        select-years="1"
                        />
            </div>
        <div class="col s4 margin-top-10">
            <button
                    class="btn btn-medium"
                    data-ng-class="{disabled: isSubmitDisabled()}"
                    data-ng-disabled="isSubmitDisabled()"
                    ng-click="getEvents()"
                    style="margin-top: 14px;" acl-action="TRNPPV">Get Bulk Events
            </button>
        </div>
    </div>
    <hr>
    <div
            class="row"
            data-ng-if="startDate != null && bulkEventList.length == 0">
		<span
                class="flow-text"
                style="margin-left: 10px;"> No Bulk Event found for the
			selected date </span>
    </div>
    <div
            class="row"
            data-ng-if="bulkEventList.length>0">
        <div class="col s12">
            <ul
                    class="collapsible no-border"
                    data-collapsible="accordion"
                    watch>
                <li ng-repeat="event in bulkEventList">
                    <div class="collapsible-header custom-collection-header" data-ng-click="selectEvent(event.bulkTransferEventId)"
                         data-ng-class="{active:(selectedEventId == event.bulkTransferEventId) }">
                        <div class="row margin0" >
                            <div class="col s12">

                                <div class="col s12">
                                    <label class="col s1">ID : {{event.bulkTransferEventId}}</label>
                                    <label  class="col s1"  style="color: green";>TO Count : {{event.toCount}} </label>
                                    <label class="col s1" style="color: green"; >RO Count : {{event.roCount}} </label>
                                    <label class="col s4 ">
										<strong>Initiated On: </strong> {{event.initiationTime |
                                    date:'dd/MM/yyyy hh:mm:ss a'}}
									</label>
                                    <label class="col s3">
										<strong>Completed On: </strong> {{event.completionTime |
                                    date:'dd/MM/yyyy hh:mm:ss a'}}
									</label>
                                    <div class="col s2">
                                        <button class="btn btn-medium" data-ng-click="getTOsView()" data-ng-class="{disabled: isDisabled(event.bulkTransferEventId)}"
                                                data-ng-disabled="isDisabled(event.bulkTransferEventId)">Download Selected TO's</button>
                                        <button class="btn btn-small" data-ng-if="!event.invoiceSet" data-ng-click="setTransferInvoices(event.bulkTransferEventId)">
                                                 Set Invoices
                                        </button>

                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="collapsible-body" >
                        <div class="row">
                            <table
                                    class="bordered striped"
                                    data-ng-if="selectedEventTOs.length > 0">
                                <tr>

                                    <th><input id = "selectAll" type="checkbox" ng-model="allSelected" data-ng-change="checkAll()"/>
                                        <label for="selectAll">TO Id</label></th>
                                    <th>Unit Name</th>
                                    <th>RO ID</th>
                                    <th>Status</th>
                                    <th>TO View</th>
                                </tr>
                                <tr  data-ng-repeat="to in selectedEventTOs track by $index">
                                    <td><input
                                            id="TO-{{to.id}}"
                                            data-ng-model="to.checked"
                                            type="checkbox"
                                            data-ng-change="selectTO(to.id)"/>
                                        <label for="TO-{{to.id}}">{{to.id}}</label>
                                    </td>
                                    <td>{{to.generatedForUnitId.name}}</td>
                                    <td>{{to.requestOrderId}}</td>
                                    <td>{{to.status}}</td>
                                    <td><button class="btn btn-xs-small" data-ng-click="openTrOrderAction(to)">TO View</button></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>

</div>
