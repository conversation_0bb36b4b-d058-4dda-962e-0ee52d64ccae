<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
	.grid {
		height: 98vh;
		padding: 10px !important;
	}

</style>



<div
	class="row white z-depth-3"
	data-ng-init="init()">
	<div class="col s12">
		<h3>Production Booking History</h3>
	</div>
	<div class="row">
		<div class="col s4">
			<label for="startDate">Select Start date</label>
			<input
				input-date
				type="text"
				name="created"
				id="startDate"
				data-ng-model="startDate"
				data-ng-change="reset()"
				container=""
				format="yyyy-mm-dd"
				select-years="1" />
		</div>
		<div class="col s4">
			<label for="endDate">Select End date</label>
			<input
				input-date
				type="text"
				name="created"
				id="endDate"
				data-ng-model="endDate"
				data-ng-change="reset()"
				container=""
				format="yyyy-mm-dd"
				select-years="1"
				min="{{startDate}}" />
		</div>
		<div class="col s4 margin-top-10">
			<button
				class="btn btn-small"
				data-ng-click="getbookingHistory()"
				style="margin-top: 14px;" acl-action="TRNBHV">Search</button>
		</div>
	</div>
	<hr>
	<div
		class="row"
		data-ng-if="startDate != null && bookingHistory.length == 0">
		<span
			class="flow-text"
			style="margin-left: 10px;"> No request plans found for the
			selected duration. </span>
	</div>
	<div class="row margin0" data-ng-if="bookingHistory.length > 0">
		<div
				class="col s12 grid"
				id="grid"
				ui-grid="gridOptions"
				ui-grid-save-state
				ui-grid-edit
				ui-grid-selection
				ui-grid-resize-columns
				ui-grid-move-columns
				ui-grid-exporter
		></div>
	</div>
</div>
