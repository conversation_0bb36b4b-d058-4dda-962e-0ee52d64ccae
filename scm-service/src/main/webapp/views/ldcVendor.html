
<div class="row white z-depth-3 custom-listing-li" style="padding: 20px;" data-ng-init="init()">

    <h2>LDC Vendor</h2>


    
        <label>Select Vendor</label>
            <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" data-ng-model="vendorSelected"
                    ng-options="vendor as vendor.name for vendor in vendors"
                    data-ng-change="getLdcDataForVendor(vendorSelected)"></select>

           <button class="btn btn-primary" data-ng-click="toggleAddLdc()" style="margin-top: 20px;" data-ng-if="vendorSelected !== null && vendorSelected.id !==null && vendorSelected.name !== null && isAddLdc === false">Add LDC</button> 
         
           <div data-ng-if="isAddLdc" style="margin: 10px; padding: 5px; border: 1px solid rgb(239, 239, 239); width: 60%;">
            <button class="btn" data-ng-click="toggleAddLdc()" style="margin-top: 20px;">close</button>
            <h4>Add LDC for {{vendorSelected.name}}</h4>
            <div class="row">
                <div class="row">
                    <div class="col s6" >
                        <label style="margin-top: 10px; color: black; " >Validity From : </label>
                        <input input-date type="text" data-ng-model="selectedFromDate" data-ng-change="setFromDate(selectedFromDate)" container="" format="yyyy-mm-dd" >
                    </div>
                    <div class="col s6" >
                  <div data-ng-if="selectedFromDate!=null">
                    <label style="margin-top: 10px; color: black;">Validity To : </label>
                    <input input-date min="{{selectedFromDate}}" type="text" data-ng-model="selectedToDate" data-ng-change="setToDate(selectedToDate)" container="" format="yyyy-mm-dd" >
                  </div> 
                    </div>
                </div>
                <div class="row">
                    <div class="col s6">
                        <label style="margin-top: 10px; color: black;">LDC  Amount</label>
                          <input type="number" data-ng-model="selectedLdcAmount" data-ng-change="setLdcAmount(selectedLdcAmount)" >
                    </div>
                    <div class="col s6">
                        <label style="margin-top: 10px; color: black;">LDC Tds Rate (In percentage %)</label>
                       <input type="number" data-ng-model="selectedTdsRate" data-ng-change="setTdsRate(selectedTdsRate)" >
                    </div>
                </div>
                <div class="row">
                    <div class="col s6">
                    <label style="margin-top: 10px; color: black;" >Tds Section</label>
                    <select ui-select2 id="TDS" name="TDS"  data-ng-model="selectedTdsSection" data-ng-change="setTdsSection(selectedTdsSection)"  data-ng-options="tds as tds for tds in tdsSections"></select>
                    </div>
                    <div class="col s6">
                        <label style="margin-top: 10px; color: black;">Certificate No</label>
                        <input type="text" data-ng-model="selectedCertificateNo" data-ng-change="setCertificationNo(selectedCertificateNo)">
                    </div>
                </div>
            </div>   
            <button style="margin-top: 10px; " class="btn btn-primary" data-ng-click="submitLdcData()" >Submit</button>
         </div>
         
         <div data-ng-if="vendorSelected !== null && vendorSelected.id !==null && vendorSelected.name !== null && vendorLdcDataSize === 0" style="margin: 10px; padding: 10px; border: 1px solid rgb(239, 239, 239); color: rgb(189, 189, 189);">
                <h5>No LDC Data found for {{vendorSelected.name}}</h5>
         </div>

         <div data-ng-if="vendorSelected !== null && vendorSelected.id !==null && vendorSelected.name !== null && vendorLdcDataSize > 0" style="margin: 10px; padding: 10px; border: 1px solid rgb(239, 239, 239);">
            <table>
                <tbody>
                <tr class="white z-depth-1  teal lighten-3 center-align">
                    <th>ID</th>
                    <th>Validity From</th>
                    <th>Validity To</th>
                    <th>LDC Amount</th>
                    <th>LDC Tds Rate (%)</th>
                    <th>Tds Section</th>
                    <th>Certificate No</th>
                    <th>Remaining Limit</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
                <tr class="white z-depth-1  blue-grey lighten-5 center-align" ng-repeat="(key,value) in vendorLdcData">
                    <td>{{value.ldcId}}</td>
                    <td>{{value.ldcTenureFrom | date:'yyyy-MM-dd' }}</td>
                    <td>{{value.ldcTenureTo | date:'yyyy-MM-dd' }}</td>
                    <td>{{value.ldcLimit}}</td>
                    <td>{{value.ldcTdsRate}}</td>
                    <td>{{value.ldcTdsSection}}</td>
                    <td>{{value.ldcCertificateNo}}</td>
                    <td>{{value.remainingLimit}}</td>
                    <td>{{value.status}}</td> 
                    <td><button class="btn btn-small" data-ng-click="deleteLdcData(key)">Delete</button></td>             
                </tr>
               
                  
                </tbody>
                </table>

     </div>




    </div>




</div>

<script type="text/ng-template" id="ldcUpdateModal.html">

    <div data-ng-init="init()">
        <div  style=" padding: 5px;">
            <h5>Update LDC for {{vendorName}} (ID : {{key}})</h5>
            <div class="row">
                <div class="row">
                    <div class="col s6" >
                        <label style="margin-top: 10px; color: black; " >Validity From : </label>
                        <input input-date type="text" data-ng-model="value.ldcTenureFrom" data-ng-change="updateFromDate(value,value.ldcTenureFrom)" container="" format="yyyy-mm-dd" >
                    </div>
                    <div class="col s6" >
                        <label style="margin-top: 10px; color: black;">Validity To : </label>
                          <input input-date type="text" data-ng-model="value.ldcTenureTo" data-ng-change="updateToDate(value,value.ldcTenureTo)" container="" format="yyyy-mm-dd" > 
                    </div>
                </div>
                <div class="row">
                    <div class="col s6">
                        <label style="margin-top: 10px; color: black;">LDC  Amount</label>
                          <input type="number" data-ng-model="value.ldcLimit" data-ng-change="updateLdcLimit(value,value.ldcLimit)" >
                    </div>
                    <div class="col s6">
                        <label style="margin-top: 10px; color: black;">LDC Tds Rate (In percentage %)</label>
                       <input type="number" data-ng-model="value.ldcTdsRate" data-ng-change="updateTdsRate(value,value.ldcTdsRate)" >
                    </div>
                </div>
                <div class="row">
                    <div class="col s6">
                    <label style="margin-top: 10px; color: black;" >Tds Section</label>
                    <select   id="TDS" name="TDS"  data-ng-model="value.ldcTdsSection" data-ng-change="updateTdsSection(value,value.ldcTdsSection)"  data-ng-options="tds as tds for tds in tdsSections"></select>
                    </div>
                    <div class="col s6">
                        <label style="margin-top: 10px; color: black;">Certificate No</label>
                        <input type="text" data-ng-model="value.ldcCertificateNo" data-ng-change="updateCertificationNo(value,value.ldcCertificateNo)">
                    </div>
                </div>
            </div>   
            <button style="margin-top: 10px; " class="btn btn-primary" data-ng-click="updateLdcData(value)" >Submit</button>
           <button class="btn" style="float: right;" data-ng-click="closeModal()" >close</button> 
        </div> 
        
    </div>

 

</script>