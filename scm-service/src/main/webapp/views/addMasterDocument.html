<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Add Master Documents </h1>
    </div>
    <div class="form-group">
        <label>Document Name*</label>
        <input type="text"
               pattern=".*\S+.*"
               class="form-control"
               ng-model="documentName"
               required>
    </div>
    <div class="form-group">
        <button style="margin: 10px" class="btn btn-primary pull-right"
                ng-click="addMasterDocuments()">Add Document
        </button>
    </div>
    <div class="form-group">
        <table class="bordered striped" data-ng-show="additionalDocuments != null">
            <tr>
                <th>Document Name</th>
                <th>Created By</th>
                <th>Created Date</th>
                <th>Status</th>
            </tr>
            <tr data-ng-repeat="doc in additionalDocuments">
                <td style="font-size: 12px">{{doc.documentName}}</td>
                <td style="font-size: 12px">{{doc.createdBy}}</td>
                <td style="font-size: 12px">{{doc.createdDate | date : 'dd-MM-yyyy hh:mm:ss'}}</td>
                <td style="font-size: 12px">{{doc.status}}</td>
            </tr>
        </table>
    </div>

</div>