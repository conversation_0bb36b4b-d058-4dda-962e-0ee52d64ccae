<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4 class="left">Create Reference Order</h4>
                <div class="right-align" style="margin-top: 20px;">
                    <input type="button" class="btn" data-ng-show="!showFulfillmentDateSelection && showMenuItemList"
                           value="BACK" data-ng-click="clearRequestOrder()"/>
                    <input type="button" class="btn" data-ng-hide="showFulfillmentDateSelection || showMenuItemList"
                           value="BACK" data-ng-click="backToMenuItemList()"/>
                </div>

            </div>
        </div>
    </div>


    <div class="row">
        <div class="col s12 menuItemList" data-ng-show="showFulfillmentDateSelection && !showMenuItemList">
            <div class="form-element">
                <div class="row">
                    <div class="col s2"><label>Fulfillment date</label></div>
                    <div class="col s4"><input input-date type="text" name="created" id="inputCreated"
                                               ng-model="fulfillmentDate"
                                               container="" format="yyyy-mm-dd" select-years="1"
                                               min="{{minRefOrderFulFillmentDate}}"
                                               max="{{maxRefOrderFulFillmentDate}}"
                                               data-ng-change="setDates(fulfillmentDate, noOfDays)"/></div>
                    <div class="col s6"><label class="highlight-data">{{fulfillmentDay}}</label></div>

                </div>
                <div class="form-element">
                    <div class="row">
                        <div class="col s2"><label>Ordering Days</label></div>
                        <div class="col s4"><input type="number" min="1" max="7" placeholder="No Of Days"
                                                   name="noOfDays"
                                                   id="noOfDays" ng-model="noOfDays"
                                                   data-ng-change="setDates(fulfillmentDate, noOfDays)"/></div>
                    </div>
                </div>
                <div class="form-element">
                    <div class="row">
                        <div class="col s2"><label>Order Will Last Until</label></div>
                        <div class="col s4"><label>{{stockLastingDate | date : 'yyyy-MM-dd'}}</label></div>
                        <div class="col s6"><label class="highlight-data">{{stockLastingDay}}</label></div>
                    </div>

                </div>
                <div class="form-element">
                    <div class="row" acl-action="CEREFO">
                        <div class="col s2"><label>Order By WareHouse/ Kitchen</label></div>
                        <div class="col s4"><input type="checkbox" style="position:inherit;opacity:1; width: 20px;height: 20px;"  data-ng-model="raiseBy" data-ng-disabled="raiseBy"></div>
                    </div>
                </div>
                <div class="form-element">
                    <div class="row">
                        <div class="form-element">
                            <input id="hasCategoryBuffer" data-ng-model="hasCategoryBuffer" type="checkbox"/>
                            <label class="red-text" for="hasCategoryBuffer">* Want to apply category buffer</label>
                        </div>
                        <div ng-show="hasCategoryBuffer">
                            <div class="row">
                                <div class="col s2"><label>Category Buffer Percentage</label></div>
                                <div class="col s4">
                                    <input type="number" placeholder="Category Buffer"
                                           id="categoryBuffer" ng-model="categoryBuffer"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col s2"><label>Select Category for Buffering</label></div>
                                <div class="col s8">
                                    <select style="width: 100%" data-ng-model="selectedCategories" ui-select2 multiple>
                                        <option data-ng-repeat="category in productCategory | filter: filterCategory track by $index"
                                                value="{{category.detail.id}}">{{category.detail.code}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="form-element">
                    <div class="row">
                        <label class="highlight-row">Please enter the target sales for the below days</label>
                        <table class="table table-striped table-bordered">
                            <thead style="background-color: #e7e7e7">
                            <th>Day Type</th>
                            <th>Date</th>
                            <th style="text-align: center">Chaayos <sub style="padding-left: 17%">Delivery %</sub></th>
                            <th style="text-align: center">Ghee and Turmeric <sub style="padding-left: 11%">Delivery %</sub></th>
                            <th style="text-align: center">Desi Canteen <sub style="padding-left: 16%">Delivery %</sub></th>
                            <th>Total</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat="entry in dataEntry"
                                data-ng-class="{'red':entry.dayType=='REMAINING_DAY','green':entry.dayType=='ORDERING_DAY'}">
                                <td>{{entry.dayType}}</td>
                                <td>{{entry.date}}</td>
                                <td >
                                    <div class="col">
                                        <input  class="col s8" type="number" ng-model="entry.brands[0].saleAmount"/>
                                        <input class="col s4" type="number"
                                               ng-model="entry.brands[0].deliverySalePercentage"/>
                                    </div>
                                </td>
                                <td>
                                    <div class="col">
                                    <input class="col s8" type="number" ng-model="entry.brands[1].saleAmount"/>
                                    <input class="col s4" type="number"
                                           ng-model="entry.brands[1].deliverySalePercentage"/>
                                    </div>
                                </td>
                                <td>
                                    <!--<div class="col">-->
                                    <input class="col s8" type="number" ng-model="entry.brands[2].saleAmount"/>
                                    <input class="col s4" type="number"
                                           ng-model="entry.brands[2].deliverySalePercentage"/>

                                </td>
                                <td>{{entry.brands[0].saleAmount + entry.brands[1].saleAmount +
                                    entry.brands[2].saleAmount
                                    }}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>

                <div class="form-element">
                    <div class="row">
                        <div class="form-group col-xs-6">
                            <input type="text" class="form-control" data-ng-model="searchText"
                                   placeholder="Search the product"/>
                        </div>

                        <label class="highlight-row">Please Enter the Expiry Product Quantity</label>
                        <table class="table table-striped table-bordered" style="width:70%">
                            <thead style="background-color: #e7e7e7">
                            <th>Product Name</th>
                            <th>Quantity</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat="expiryEntry in expiryProduct | toArray | filter :searchText"
                                bgcolor="#DAF7A6">
                                <td>{{expiryEntry.productName}}</td>
                                <td><input type="number" ng-model="expiryEntry.quantity"/></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>


                <div class="row">
                    <div class="col s12">
                        <input type="button" class="btn" value="GUIDE ME" data-ng-click="getReferenceQuantities()"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="col s12 menuItemList" data-ng-show="!showFulfillmentDateSelection && showMenuItemList">
            <ul class="collapsible" data-collapsible="accordion" watch>
                <li ng-repeat="category in menuCategories track by category.id">
                    <div class="collapsible-header categoryItem">
                        <div class="col s11">{{category.name}}</div>
                        <div class="col s1 center-align">&#9660;</div>
                    </div>
                    <div class="collapsible-body" style="background:#eee;">
                        <ul class="productList">
                            <ul class="collapsible" data-collapsible="accordion" >
                                <div class="collapsible-header productListHeader">
                                    <div class="col s3">Menu Product</span></div>
                                    <div class="col s3">Requesting</div>
                                    <div class="col s3">Remaining Day Qty</div>
                                    <div class="col s3">Ordering Day Suggested Qty</div>
                                    <!--<div class="col s1">Dine In</div>-->
                                    <!--<div class="col s1">Delivery</div>-->
                                    <!--<div class="col s1">Takeaway</div>-->
                                </div>
                                <li ng-repeat="product in category.productList | orderBy:'name' track by $index">
                                    <div class="collapsible-header productListItem"
                                         ng-class="{'highlight-row' : product.quantity != null && product.quantity > 0}">
                                        <div class="col s3">{{product.productName}} <span
                                                data-ng-if="product.dimension!='None'">({{product.dimension}})</span>
                                        </div>
                                        <div class="col s3">
                                            <input type="number" data-ng-if="!product.supportsVariantLevelOrdering"
                                                   placeholder="Quantity" ng-model="product.requestedQuantity"
                                                   data-ng-change="updateMenuProductQty(product)"/>
                                            <span data-ng-if="product.supportsVariantLevelOrdering">Quantity: {{product.requestedQuantity}}</span>
                                        </div>
                                        <div class="col s3">{{product.saleQuantity || "0"}}</div>
                                        <div class="col s3">{{product.quantity}}</div>
                                        <!--<div class="col s1">{{product.dineInQuantity}}</div>-->
                                        <!--<div class="col s1">{{product.deliveryQuantity}}</div>-->
                                        <!--<div class="col s1">{{ product.takeawayQuantity}}</div>-->
                                        <div class="col s1 center-align"
                                             data-ng-if="product.supportsVariantLevelOrdering">&#9660;
                                        </div>
                                    </div>
                                    <div data-ng-if="product.supportsVariantLevelOrdering" class="collapsible-body"
                                         style="background:#eee;">
                                        <ul>
                                            <li data-ng-repeat="variant in product.variants track by $index"
                                                data-ng-if="variant.variantLevelOrdering" class="productListItem">
                                                <div class="row">
                                                    <div class="col s6">{{variant.name}} <span
                                                            data-ng-if="variant.dimension!='None'">({{variant.dimension}})</span>
                                                    </div>
                                                    <div class="col s6">
                                                        <input type="number" placeholder="Quantity"
                                                               ng-model="variant.orderedQuantity"
                                                               data-ng-change="updateProductQuantity(product, variant)"/>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                            </ul>
                        </ul>
                    </div>
                </li>
            </ul>
            <div class="row">
                <div class="col s12">
                    <input type="button" class="btn" value="BACK" data-ng-click="clearRequestOrder()"/>
                    <input type="button" class="btn" value="RESET" data-ng-click="resetMenuItem()"/>
                    <input type="button" class="btn" value="NEXT" data-ng-click="createSCMProductList()"/>
                </div>
            </div>
        </div>
        <div class="col s12 menuItemList" data-ng-hide="showFulfillmentDateSelection || showMenuItemList">

            <div class="row" style="margin-bottom: 10px;">
                <div class="row">
                    <div class="col s4"><label>Ordering Days</label> {{noOfDays}}</div>
                    <div class="col s4"><label>Fulfillment Date</label> {{fulfillmentDate | date : 'dd-MM-yyyy'}}
                        ({{fulfillmentDay}})
                    </div>
                    <div class="col s4"><label>Order Will Last Until</label>{{stockLastingDate | date :
                        'dd-MM-yyyy'}}
                        ({{stockLastingDay}})
                    </div>
                </div>
                <h4>Kitchen Items</h4>
                <ul class="collection z-depth-1-half" style="font-size: 12px;">
                    <li class="collection-item list-head">
                        <div class="row">
                            <!--<div class="col s1">Fulfilled By</div>-->
                            <div class="col s2">Product Name</div>
                            <div class="col s1">Sub Category</div>
                            <div class="col s1">Stock<b>(Stock in Hand,In Transit Stock)</b></div>
                            <div class="col s1">Probable Sale</div>
                            <div class="col s1">Expiry Product</div>
                            <div class="col s1">Suggested</div>
                            <div class="col s1">Ordering</div>
                            <div class="col s1">Unit of Measure</div>
                            <div class="col s1">Final Ordering Qty</div>
                            <div class="col s2">Packaging Name</div>
                        </div>
                    </li>
                    <li class="collection-item"
                        data-ng-repeat="scmProduct in scmProductList | filter:{selectedFulfillmentType:'KITCHEN'} | orderBy:['name'] track by $index">
                        <div class="row">
                            <!--ng-class="{externalCategoryColor: scmProduct.selectedFulfillmentType == 'EXTERNAL',
                            warehouseCategoryColor: scmProduct.selectedFulfillmentType == 'WAREHOUSE',
                            kitchenCategoryColor: scmProduct.selectedFulfillmentType == 'KITCHEN'}"-->
                            <!--<div class="col s1">{{scmProduct.selectedFulfillmentType}}</div>-->
                            <div class="col s2"><a data-ng-click="showPreview($event, scmProduct.id,'PRODUCT')">{{scmProduct.name}}</a>
                            </div>
                            <div class="col s1">{{scmProduct.subCategoryName}}</div>
                            <div class="col s1">{{scmProduct.stock +"("+scmProduct.stockAtHand +","+scmProduct.inTransit
                                +")"}}
                            </div>
                            <div class="col s1">{{scmProduct.saleQuantity}}</div>
                            <div class="col s1">{{scmProduct.expiryQuantity}}</div>
                            <div class="col s1">{{scmProduct.suggestedQuantity | number : 3}}</div>
                            <div class="col s1 orderingColumnHighlight">
                                {{scmProduct.orderingQuantity | number : 3}}
                            </div>
                            <div class="col s1">{{scmProduct.unitOfMeasure}}</div>
                            <div class="col s1"><input type="text" data-ng-model="scmProduct.packagingQuantity"
                                                       data-ng-change="updateOrderingQty(scmProduct)"/></div>
                            <div class="col s2">{{scmProduct.packagingName}}</div>
                        </div>
                    </li>
                </ul>
                <h4>Warehouse Items</h4>
                <ul class="collection z-depth-1-half" style="font-size: 12px;">
                    <li class="collection-item list-head">
                        <div class="row">
                            <!--<div class="col s1">Fulfilled By</div>-->
                            <div class="col s2">Product Name</div>
                            <div class="col s1">Sub Category</div>
                            <div class="col s1">Stock<b>(Stock in Hand,In Transit Stock)</b></div>
                            <div class="col s1">Probable Sale</div>
                            <div class="col s1">Expiry Product</div>
                            <div class="col s1">Suggested</div>
                            <div class="col s1">Ordering</div>
                            <div class="col s1">Unit of Measure</div>
                            <div class="col s1">Final Ordering Qty</div>
                            <div class="col s2">Packaging Name</div>
                        </div>
                    </li>
                    <li class="collection-item"
                        data-ng-repeat="scmProduct in scmProductList | filter:{selectedFulfillmentType:'WAREHOUSE'} | orderBy:['name'] track by $index">
                        <div class="row">
                            <!--ng-class="{externalCategoryColor: scmProduct.selectedFulfillmentType == 'EXTERNAL',
                            warehouseCategoryColor: scmProduct.selectedFulfillmentType == 'WAREHOUSE',
                            kitchenCategoryColor: scmProduct.selectedFulfillmentType == 'KITCHEN'}"-->
                            <!--<div class="col s1">{{scmProduct.selectedFulfillmentType}}</div>-->
                            <div class="col s2"><a data-ng-click="showPreview($event, scmProduct.id,'PRODUCT')">{{scmProduct.name}}</a>
                            </div>
                            <div class="col s1">{{scmProduct.subCategoryName}}</div>
                            <div class="col s1">{{scmProduct.stock +"("+scmProduct.stockAtHand +","+scmProduct.inTransit
                                +")"}}
                            </div>
                            <div class="col s1">{{scmProduct.saleQuantity}}</div>
                            <div class="col s1">{{scmProduct.expiryQuantity}}</div>
                            <div class="col s1">{{scmProduct.suggestedQuantity | number : 3}}</div>
                            <div class="col s1 orderingColumnHighlight">
                                {{scmProduct.orderingQuantity | number : 3}}
                            </div>
                            <div class="col s1">{{scmProduct.unitOfMeasure}}</div>
                            <div class="col s1"><input type="text" data-ng-model="scmProduct.packagingQuantity"
                                                       data-ng-change="updateOrderingQty(scmProduct)"/></div>
                            <div class="col s2">{{scmProduct.packagingName}}</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="row">
                <div class="col s12">
                    <div class="form-element">
                        <label>Comment(optional)</label>
                        <textarea data-ng-model="comment"></textarea>
                    </div>
                    <div class="form-element">
                        <input type="button" class="btn" value="BACK" data-ng-click="backToMenuItemList()"/>
                        <!--<input type="button" class="btn" value="SAVE" data-ng-click="sendReferenceOrder('INITIATED')" />-->
                        <input type="button" class="btn" value="SUBMIT"
                               data-ng-click="sendReferenceOrder('CREATED')"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
