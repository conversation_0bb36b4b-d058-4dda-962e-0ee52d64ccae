
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
	.list-head.pad .row a{
		color: #3b0e0e;}
</style>

<div
	class="row white z-depth-3"
	data-ng-init="init()">
	<div class="col s12">
		<h3>Production Booking</h3>
	</div>
	<div class="row">
		<div class="col s12 m6 l6">
			<label>Select Product:</label>
			<select
				ui-select2
				id="selectedProductIdselect"
				data-ng-model="selectedProductId"
				data-placeholder="Enter name of product">
				<option value=""></option>
				<option
					data-ng-repeat="product in scmProductDetails | orderBy : 'productName' | filter : bySemiFinshed"
					value="{{product.productId}}">{{product.productName}}</option>
			</select>
		</div>
		<div class="col s12 m6 l6">
			<label>Booking Quantity:</label>
			<input
				type="number"
				min="0.1"
				placeholder="Enter Quantity"
				name="bookingQuantityName"
				id="bookingQuantityId"
				data-ng-model="bookingQuantity" />
		</div>
	</div>
	<div class="row">
		<div class="col s12">
			<input
				type="button"
				class="btn right"
				value="Calculate Consumption"
				data-ng-if="(updateMapping == null) || (updateMapping == false)"
				data-ng-click="calculateBookingConsumption(false)" />
			<input
					type="button"
					class="btn left"
					value="Update Mapping"
					data-ng-if="bookingData == null"
					data-ng-click="calculateBookingConsumption(true)" />
		</div>
	</div>
	<div class="row">
		<div class="col s12">
			<input
					type="button"
					class="btn right"
					value="Find Today's Booking"
					data-ng-click="getbookingHistory()" />
		</div>
		<div class="col s12">
			<input
					type="button"
					class="btn right"
					value="Find Today's Reverse Booking"
					data-ng-click="getReversebookingHistory()" />
		</div>
	</div>
</div>
<div
		class="row"
		data-ng-if="bookingData != null">
	<div class="col s12">
		<span data-ng-if="currentUnitId == 26425">
        	<input id="hasProductionBooking" data-ng-model="hasProductionBooking" data-ng-change="changeHasProductionBooking(hasProductionBooking)" type="checkbox"/>
            <label class="red-text" for="hasProductionBooking">* Want reverse Production Booking</label>
		</span>
		<span>
			<input
					type="button"
					class="btn actionBtn right"
					value="Submit"
					data-ng-click="submitBookingConsumption()" acl-action="TRNPBA" />
		</span>
		<span>
			<input
					type="button"
					class="btn actionBtn right"
					value="Cancel"
					data-ng-click="reset()" />
		</span>
	</div>
</div>
<div data-ng-if="bookingData == null">
	<div
		class="row"
		data-ng-if="bookingHistory.length > 0">
		<div class="col s12">
			<ul
				class="collapsible no-border"
				data-collapsible="expandable"
				watch>
				<li>
					<div class="collapsible-header custom-collection-header">
						<div class="row margin0 center-align">
							<div class="col s1">PB No.</div>
							<div class="col s3">Product Name</div>
							<div class="col s1">UOM</div>
							<div class="col s1">Quantity</div>
							<div class="col s1">Date</div>
							<div class="col s1">Time</div>
							<div class="col s1">Expiry</div>
							<div class="col s2">Created By</div>
							<div class="col s1">Booking Status</div>
						</div>
					</div>
				</li>
				<li data-ng-repeat="entry in bookingHistory | orderBy : '-bookingId'">
					<div
						class="collapsible-header center-align"
						style="padding: 0;">
						<div class="row margin0">
							<div class="col s1">{{entry.bookingId}}</div>
							<div class="col s3"><a data-ng-click="showPreview($event, entry.productId,'PRODUCT')">{{entry.productName}}</a></div>
							<div class="col s1">{{entry.unitOfMeasure}}</div>
							<div class="col s1">{{entry.quantity}}</div>
							<div class="col s1">{{entry.generationTime |
								date:'dd/MM/yyyy'}}</div>
							<div class="col s1">{{entry.generationTime |
								date:'HH:mm:ss'}}</div>
							<div class="col s1">{{entry.expiryDate |
								date:'dd/MM/yyyy'}}</div>
							<div class="col s2">{{entry.generatedBy.name}}</div>
							<div class="col s1">
								<button
									class="btn btn-small"
									data-ng-if="entry.bookingStatus == 'CREATED'"
									data-ng-click="cancelBooking($event, entry.bookingId)" acl-action="TRNPBC">Cancel</button>
								<span data-ng-if="entry.bookingStatus != 'CREATED'">{{entry.bookingStatus}}</span>
							</div>
						</div>
					</div>
					<div class="collapsible-body">
						<div class="row margin0">
							<table
								class="bordered striped"
								data-ng-if="entry.bookingConsumption.length > 0">
								<tr>
									<th>Product Name</th>
									<th>SKU Name</th>
									<th>Unit Of Measure</th>
									<th>Quantity</th>
								</tr>
								<tr data-ng-repeat="c in entry.bookingConsumption track by $index">
									<td><a data-ng-click="showPreview($event, c.productId,'PRODUCT')">{{c.productName}}</a></td>
									<td>{{c.skuName}}</td>
									<td>{{c.unitOfMeasure}}</td>
									<td>{{c.calculatedQuantity}}</td>
								</tr>
							</table>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>
<div
	class="row"
	data-ng-if="bookingData != null">
	<div class="row" >
		<div class="col s12 m6 l6 date-label-prod-booking">
			<span>Current Date:</span>
			<span>{{bookingData.currentDate | date : 'dd/MM/yyyy'}}</span>
		</div>
		<div class="col s12 m6 l6 date-label-prod-booking">
		<div class="row">
			<div class="col s3"><span>Expiry Date:</span></div>
			<!-- <span>{{bookingData.expiryDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span> -->
			<div class="col s9"><select data-placeholder="Select Expiry" data-ng-model="bookingData.expiryDate"
						data-ng-options="e as e|date:'dd/MM/yyyy HH:mm:ss' : 'IST' for e in bookingData.availableDates"></select></div>
		</div>
		</div>
	</div>
	<div class="col s12" data-ng-if="fixedAsset">
		<div class="col s6">
			<label>Select Vendor</label>
			<select class="select2Input" ui-select2="select2Options" id="vendorList" name="vendorList" data-ng-model="selectedVendor"
					data-ng-options="vendor as vendor.name for vendor in vendorDataList"
					data-ng-change="selectVendor(selectedVendor)"></select>
		</div>
		<div class="col s6" data-ng-if="locationList.length>0">
			<label>Select Delivery Location</label>
			<select class="select2Input" ui-select2="select2Options" id="locationList" name="locationList" data-ng-model="selectedLocation"
					data-ng-options="location as location.name for location in locationList"
					data-ng-change="selectDispatchLocation(selectedLocation)"></select>
		</div>
	</div>
	<div class="col s12">
		<div class="row list-head" style="padding: 10px;">
			<div class="col s2">Product Name</div>
			<div class="col s2">SKU</div>
			<div class="col s2">Unit Of Measure</div>
			<div class="col s2">Quantity</div>
			<div class="col s2">Profile</div>
			<div class="col s2" data-ng-if="hasProductionBooking==true">Wastage</div>
			<div class="col s1" data-ng-if="updateMapping==true">Auto Production</div>
			<div class="col s1" data-ng-if="updateMapping==true">Mapped Auto Production</div>
		</div>
		<div class="row" data-ng-repeat="c in bookingData.bookingConsumption | orderBy: '-availableSkuList.length' track by $index"
			 data-ng-class="{'list-head pad':c.bookingConsumption.length>0, 'list-item':c.bookingConsumption.length==0}">
			<div class="col s2" style="margin-bottom: 20px;"><a data-ng-click="showPreview($event, c.productId,'PRODUCT')">{{c.productName}}</a></div>
			<div class="col s2">
				<span data-ng-init="c.selectedSku=c.availableSkuList[0]"
					  data-ng-if="c.availableSkuList.length == 1"><a data-ng-click="showPreview($event, c.availableSkuList[0].id,'SKU')">{{c.availableSkuList[0].name}}</a></span>
				<span class="center-align" data-ng-if="c.availableSkuList.length != 1">
					<select  data-placeholder="Select SKU" data-ng-model="c.selectedSku"
						data-ng-init="c.selectedSku=c.availableSkuList[0]" data-ng-options="sku as sku.name for sku in c.availableSkuList"></select>
				</span>
			</div>
			<div class="col s2">{{c.unitOfMeasure}}</div>
			<div class="col s2">{{c.calculatedQuantity}}</div>
			<div class="col s2">{{c.profile}}</div>
			<div class="col s2"  data-ng-if="hasProductionBooking==true">
				<input
						id="wastage-$index"
						type="number"
						style="color:red;"
						min="0.00001"
						placeholder="Enter Wastage"
						data-ng-change="setWastageQuantity(c,c.wastageQuantity,$index)"
						data-ng-model="c.wastageQuantity" />
				<input type="checkbox" data-ng-model="c.autoFillWastage"
					   data-ng-change="setAutoFillWastageQuantity(c,c.autoFillWastage,$index)"
					   style="position:inherit;opacity:1; width: 20px;height: 20px;">
			</div>
			<div class="col s1" data-ng-if="updateMapping==true">
				<div data-ng-if="c.autoProduction == true">
					<input type="checkbox" data-ng-model="c.autoProduction"
						   style="position:inherit;opacity:1; width: 20px;height: 20px;">
				</div>
				<div data-ng-if="c.autoProduction == false">--</div>
			</div>
			<div class="col s1" data-ng-if="updateMapping==true">
				<div data-ng-if="c.mappedAutoProduction == true">
					<input type="checkbox" data-ng-model="c.mappedAutoProduction"
						   style="position:inherit;opacity:1; width: 20px;height: 20px;">
				</div>
				<div data-ng-if="c.mappedAutoProduction == false">--</div>
			</div>

			<div class="row list-item" data-ng-if="c.bookingConsumption.length>0"
				 data-ng-repeat="cx in c.bookingConsumption"
				 data-ng-class="{'list-head pad':cx.bookingConsumption.length>0, 'list-item':cx.bookingConsumption.length==0}"
				  style="float:left;width: 100%">
				<div class="col s2"><a
						data-ng-click="showPreview($event, cx.productId,'PRODUCT')">{{cx.productName}}</a></div>
				<div class="col s2">
				<span data-ng-init="cx.selectedSku=cx.availableSkuList[0]"
					  data-ng-if="cx.availableSkuList.length == 1"><a data-ng-click="showPreview($event, cx.availableSkuList[0].id,'SKU')">{{cx.availableSkuList[0].name}}</a></span>
					<span class="center-align" data-ng-if="cx.availableSkuList.length != 1">
					<select data-placeholder="Select SKU" data-ng-model="cx.selectedSku"
							data-ng-init="cx.selectedSku=cx.availableSkuList[0]" data-ng-options="sku as sku.name for sku in cx.availableSkuList"></select>
				</span>
				</div>
				<div class="col s2">{{cx.unitOfMeasure}}</div>
				<div class="col s2">{{cx.calculatedQuantity}}</div>
				<div class="col s2">{{cx.profile}}</div>
				<div class="col s1" data-ng-if="updateMapping==true">
					<div data-ng-if="cx.autoProduction == true">
						<input type="checkbox" data-ng-model="cx.autoProduction"
							   style="position:inherit;opacity:1; width: 20px;height: 20px;">
					</div>
					<div data-ng-if="cx.autoProduction == false">--</div>
				</div>
				<div class="col s1" data-ng-if="updateMapping==true">
					<div data-ng-if="cx.mappedAutoProduction == true">
						<input type="checkbox" data-ng-model="cx.mappedAutoProduction"
							   style="position:inherit;opacity:1; width: 20px;height: 20px;">
					</div>
					<div data-ng-if="cx.mappedAutoProduction == false">--</div>
				</div>

				<div class="row list-item" data-ng-if="cx.bookingConsumption.length>0 "
					 data-ng-repeat="cy in cx.bookingConsumption"
					 data-ng-class="{'list-head pad':cy.bookingConsumption.length>0, 'list-item':cy.bookingConsumption.length==0}" style="float:left;width: 100%">
					<div class="col s2"><a
							data-ng-click="showPreview($event, cy.productId,'PRODUCT')">{{cy.productName}}</a></div>
					<div class="col s2">
				<span data-ng-init="cy.selectedSku=cy.availableSkuList[0]"
					  data-ng-if="cy.availableSkuList.length == 1"><a data-ng-click="showPreview($event, cy.availableSkuList[0].id,'SKU')">{{cy.availableSkuList[0].name}}</a></span>
						<span class="center-align" data-ng-if="cy.availableSkuList.length != 1">
					<select data-placeholder="Select SKU" data-ng-model="cy.selectedSku"
							data-ng-init="cy.selectedSku=cy.availableSkuList[0]" data-ng-options="sku as sku.name for sku in cy.availableSkuList"></select>
				</span>
					</div>
					<div class="col s2">{{cy.unitOfMeasure}}</div>
					<div class="col s2">{{cy.calculatedQuantity}}</div>
					<div class="col s2">{{cy.profile}}</div>
					<div class="col s1" data-ng-if="updateMapping==true">
						<div data-ng-if="cy.autoProduction == true">
							<input type="checkbox" data-ng-model="cy.autoProduction"
								   style="position:inherit;opacity:1; width: 20px;height: 20px;">
						</div>
						<div data-ng-if="cy.autoProduction == false">--</div>
					</div>
					<div class="col s1" data-ng-if="updateMapping==true">
						<div data-ng-if="cy.mappedAutoProduction == true">
							<input type="checkbox" data-ng-model="cy.mappedAutoProduction"
								   style="position:inherit;opacity:1; width: 20px;height: 20px;">
						</div>
						<div data-ng-if="cy.mappedAutoProduction == false">--</div>
					</div>

					<div class="row list-item" data-ng-if="cy.bookingConsumption.length>0"
						 data-ng-repeat="cz in cy.bookingConsumption"
						 data-ng-class="{'list-head pad':c.bookingConsumption.length>0, 'list-item':c.bookingConsumption.length==0}"
						 style="float:left;width: 100%; ">
						<div class="col s2"><a
								data-ng-click="showPreview($event, cz.productId,'PRODUCT')">{{cz.productName}}</a></div>
						<div  class="col s2">
				<span data-ng-init="cz.selectedSku=cz.availableSkuList[0]"
					  data-ng-if="cz.availableSkuList.length == 1"><a
						data-ng-click="showPreview($event, cz.availableSkuList[0].id,'SKU')">{{cz.availableSkuList[0].name}}</a></span>
							<span   class="center-align" data-ng-if="cz.availableSkuList.length != 1">
					<select data-placeholder="Select SKU" data-ng-model="cz.selectedSku"
							data-ng-init="cz.selectedSku=cz.availableSkuList[0]"
							data-ng-options="sku as sku.name for sku in cz.availableSkuList"></select>
				</span>
						</div>
						<div  class="col s2">{{cz.unitOfMeasure}}</div>
						<div class="col s2">{{cz.calculatedQuantity}}</div>
						<div class="col s2">{{cz.profile}}</div>
						<div class="col s1" data-ng-if="updateMapping==true">
							<div data-ng-if="cz.autoProduction == true">
								<input type="checkbox" data-ng-model="cz.autoProduction"
									   style="position:inherit;opacity:1; width: 20px;height: 20px;">
							</div>
							<div data-ng-if="cz.autoProduction == false">--</div>
						</div>
						<div class="col s1" data-ng-if="updateMapping==true">
							<div data-ng-if="cz.mappedAutoProduction == true">
								<input type="checkbox" data-ng-model="cz.mappedAutoProduction"
									   style="position:inherit;opacity:1; width: 20px;height: 20px;">
							</div>
						</div>
						<div data-ng-if="cz.mappedAutoProduction == false">--</div>
					</div>
				</div>
		   </div>
		</div>
		<!--<table class="bordered striped">
			<thead>
				<tr class="list-head">
					<th class="left-align">Product Name</th>
					<th class="left-align">SKU</th>
					<th class="center-align">Unit Of Measure</th>
					<th class="center-align">Quantity</th>
				</tr>
			</thead>
			<tbody>
				<tr data-ng-repeat="c in bookingData.bookingConsumption | orderBy: '-availableSkuList.length' track by $index">
					<td class="left-align">{{c.productName}}</td>
					<td
						class="left-align"
						data-ng-init="c.selectedSku=c.availableSkuList[0]"
						data-ng-if="c.availableSkuList.length == 1">{{c.availableSkuList[0].name}}</td>
					<td
						class="center-align"
						data-ng-if="c.availableSkuList.length != 1"><select
							data-placeholder="Select SKU"
							data-ng-model="c.selectedSku"
							data-ng-if="c.bookingConsumption.length==0"
							data-ng-init="c.selectedSku=c.availableSkuList[0]"
							data-ng-options="sku as sku.name for sku in c.availableSkuList">
						</select></td>
					<td class="center-align">{{c.unitOfMeasure}}</td>
					<td class="center-align">{{c.calculatedQuantity}}</td>
					<tr data-ng-if="c.bookingConsumption.length>0" data-ng-repeat="cx in c.bookingConsumption">
						<td class="left-align">{{cx.productName}}</td>
						<td
								class="left-align"
								data-ng-init="cx.selectedSku=cx.availableSkuList[0]"
								data-ng-if="cx.availableSkuList.length == 1">{{cx.availableSkuList[0].name}}</td>
						<td
								class="center-align"
								data-ng-if="cx.availableSkuList.length != 1"><select
								data-placeholder="Select SKU"
								data-ng-model="cx.selectedSku"
								data-ng-init="cx.selectedSku=cx.availableSkuList[0]"
								data-ng-options="sku as sku.name for sku in cx.availableSkuList">
						</select></td>
						<td class="center-align">{{cx.unitOfMeasure}}</td>
						<td class="center-align">{{cx.calculatedQuantity}}</td>
					</tr>
				</tr>
			</tbody>
		</table>-->
	</div>
</div>
<div
	class="row"
	data-ng-if="bookingData != null">
	<div class="col s12">
		<span>
			<input
				type="button"
				class="btn actionBtn right"
				value="Submit"
				data-ng-click="submitBookingConsumption()" acl-action="TRNPBA" />
		</span>
		<span>
			<input
				type="button"
				class="btn actionBtn right"
				value="Cancel"
				data-ng-click="reset()" />
		</span>
	</div>
</div>
