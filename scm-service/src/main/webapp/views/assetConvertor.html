
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4  class="center card-panel teal lighten-2 z-depth-1 cyan pulse" style="text-align: center;">Asset  Converter</h4>
            </div>
        </div>
    </div>

    <div  class=" row teal lighten-5 black-text center">
        <div class="col s2 right">
            <button class="btn btn-large teal darken-3 yellow-text center" data-ng-click="convertAssets()">
                Convert
            </button>
        </div>

    </div>

    <div class="col s12">
        <div class="row list-head" style="padding: 10px;">
            <div class="col s3">Product Name</div>
            <div class="col s3">Unit Of Measure</div>
            <div class="col s3">Quantity</div>
            <div class="col s3">Transfer Qty</div>

        </div>
        <div class="row" data-ng-repeat="c in assetList ">
            <div class="col s3" style="margin-bottom: 20px;"><input id="pr-{{c.productId}}"
                                                                    data-ng-model="c.checked"
                                                                    type="checkbox"/>
                <label for="pr-{{c.productId}}">{{c.productName}}</label></div>
            <div class="col s3">{{c.unitOfMeasure}}</div>
            <div class="col s3">{{c.qty}}</div>
            <div class="col s3"><input data-ng-model="c.transferQty"
                                       data-ng-change="validateQty(c,c.transferQty,c.qty)"
                                       type="number"/></div>

        </div>
    </div>


</div>
