<div
	class="row white z-depth-3 custom-listing-li"
	data-ng-init="init()">
	<div class="col s12">
		<h4>Employee Cost Center Mapping</h4>
	</div>
	<div class="col s12 m12 l6 xl6">
		<div class="row">
			<label class="black-text">Employees</label> 
			<select
				ui-select2="selectedEmployee"
				id="employee"
				name="employee"
				data-ng-model="employee"
				data-ng-options="e as e.name + '-' + e.id for e in employeeList | orderBy : 'name' track by e.id"
				data-ng-change="searchEmployeeMapping(employee)"></select>
		</div>
	</div>
	<div Class="col s12 m12 l6 xl6">
		<div Class="row">
			<div class="col s12">
				<span
					data-ng-click="openAddNewMappingModal()"> <a
						href="#addNewMappingModal"
						class="btn right"
						style="margin-right: 10px" modal
					><b>+</b> Add New Mapping</a>
				</span>
			</div>
		</div>
	</div>
</div>

<ul class="collection striped" data-ng-show="allCostElements.length>0">
	<li class="collection-item list-head">
	    <div class="row">
	        <div class="col s2">Cost Center</div>
	        <div class="col s2">Email</div>
	        <div class="col s2">Code</div>
	       	<div class="col s2">Description</div>
	       	<div class="col s2">Action</div>


	       
	    </div>
	</li>
      <li class="collection-item clickable"
                    data-ng-repeat="ce in allCostElements | orderBy : 'id'">
         <div class="row" style="margin-bottom: 0px;">
			<div class="col s2"> {{ce.costCenter.name}}</div>
			<div class="col s2"> {{ce.costCenter.costCenterEmail}}</div>
			<div class="col s2"> {{ce.costCenter.code}}</div>
			<div class="col s2"> {{ce.costCenter.description}}</div>
			<div class="col s2"> <div class="btn btn-small" data-ng-click="deactivateMapping(ce.costCenter.id)">Deactivate</div></div>
		 </div>
     </li>
</ul>

<div
	id="addNewMappingModal"
	class="modal modal-mx">
	<form
		id="basicDetail"
		name="basicDetailsForm"
		class="white z-depth-3 scm-form"
		novalidate>
		<div class="modal-content">
			<div class="row">
				<div class="col s12 card-panel teal lighten-2">
					<h5 class="white-text center">Add Employee Cost Center</h5>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Cost Center</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<select
					ui-select2
					style="width: 100%;"
					id="selectCostCenter"
					data-ng-model="selectedCostCenter"
					data-ng-options="o as o.name for o in allCostCenters"
					required></select>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Employee</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<select
					ui-select2
					style="width: 100%;"
					id="employeeSelector"
					data-ng-model="selectedEmployee"
					data-ng-options="o as o.name + '-' + o.id for o in employeeList"
					required></select>
			</div>
		</div>
		<div class="modal-footer">
			<div class="row">
				<div class="col s12">
					<button
						class="modal-action waves-effect waves-green btn right"
						data-ng-class="{'disabled':!basicDetailsForm.$valid}"
						style="margin-right: 10px"
						data-ng-click="createEmployeeCostCenterMap()"
						data-ng-disabled="!basicDetailsForm.$valid">Submit</button>
					<button
						class="modal-action modal-close waves-effect waves-green btn right"
						style="margin-right: 10px"
						data-ng-click="clear()">Cancel</button>
				</div>
			</div>
		</div>
	</form>
</div>