<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
    <div class="col s12" data-ng-if="bulkPrint == false">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Search Asset</h4>
                <div class="col s4">
                    <input type="text" placeholder="Enter Asset Name"
                           data-ng-model="assetName">
                </div>
                <div class="col s4" data-ng-if="assetName!=null">
                    <button class="btn" data-ng-click="searchAssetByName(assetName)">searchAssets</button>
                </div>
                <div class="col s4">
                    <button class="btn" data-ng-click="findAssets()">findAllAssets</button>
                </div>
                <div class="col s4">
                    <button class="btn" data-ng-if="assets.length>0" data-ng-click="printAllAssets()">Print Bulk Asset
                        Tags
                    </button>
                </div>
            </div>
            <div class="col s12" style="margin-top: 15px">
                <div class="col s12" data-ng-if="assets.length>0">
                    <select ui-select2="productSelectOptions"
                            ng-init="assetId = null"
                            ng-model="assetId" data-ng-change="selectAsset(assetId)"
                            data-placeholder="Enter name of a Asset Or Tag Value">
                        <option ng-repeat="asset in assets   | filter:employeeAssetFilter track by asset.assetId" value="{{asset.assetId}}">
                            {{asset.assetName+" "+asset.tagValue}}
                        </option>
                    </select>
                    <span><b>Found Assets:</b>{{assets.length}}</span>
                </div>
            </div>
        </div>

        <div data-ng-if="assetDetail.assetId != null" class="row product-detail white z-depth-3">
            <div class="col s12">
                <div class="row" style="margin-bottom:0px;">
                    <div class="col s7" style="padding-left:0px;">
                        <h5 style="display:inline-block !important;">{{assetDetail.assetName}}</h5>
                        <!--<div style="display:inline-block !important;" class="right-align form-element">-->
                        <!--<a class="btn" href="#grPreview" modal acl-action="RECPRA" style="margin-right:-690px;">Change-->
                        <!--Status</a>-->
                        <!--</div>-->
                        <div style="display:inline-block !important;" class="right-align form-element">
                            <!--<a class="btn" href="#grPreview" modal acl-action="RECPRA" style="margin-right:-690px;">Change-->
                            <!--Status</a>-->
                            <a class="waves-effect waves-light btn right"
                               data-ng-if="assetDetail.assetStatus == 'CREATED' ||
                                    assetDetail.assetStatus == 'READY_FOR_USE'"
                               data-ng-click="changeStatus('IN_USE')" style="margin: 1rem;"
                                acl-action="SOROIBFS">Put To Use</a>
                            <a class="waves-effect waves-light btn right"
                               data-ng-if="assetDetail.assetStatus == 'CREATED' ||
                                    assetDetail.assetStatus == 'IN_USE' ||
                                    assetDetail.assetStatus == 'READY_FOR_USE' ||
                                    assetDetail.assetStatus == 'IN_RENOVATION' "
                               data-ng-click="changeStatus('BROKEN')" style="margin: 1rem;"
                               acl-action="SOROIBFS">Mark Broken</a>
                            <a class="waves-effect waves-light btn right"
                               data-ng-if="assetDetail.writtenOff != true"
                               data-ng-click="printAgain()" style="margin: 1rem;"
                               acl-action="ASTTAG">Print Tag</a>
                            <input type="button" class="waves-effect waves-light btn right" value="Edit Asset Name"
                                   a href="#EditNameModal"
                                   style="margin: 1rem;"
                                   acl-action="ASTEDT"
                                   data-ng-click="editAssetName()"
                                   modal/>
                            <a class="waves-effect waves-light btn right"
                               data-ng-if="(currentUserId == 125200 || currentUserId == 120063 || currentUserId == 140199) && (assetDetail.assetStatus == 'CREATED' ||
                                    assetDetail.assetStatus == 'IN_USE' ||
                                    assetDetail.assetStatus == 'IN_RENOVATION')"
                               data-ng-click="markNotFoundInAudit(assetDetail)" style="margin: 1rem;">Not Found in Audit</a>
                        </div>
                    </div>
                </div>
                <div class="modal" id="grPreview" style="width:30%; max-height:86%;">
                    <div class="modal-content">
                        <div class="col s12">
                            <div class="row">
                                <div class="col s12">
                                    <div class="input-field col s12">
                                        <select ng-model="markvalue" style="width:300px !important;">
                                            <option value="IN_USE">In Use</option>
                                            <option value="BROKEN">Broken</option>
                                        </select>
                                    </div>

                                    <br><br>
                                    <div class="col s2 form-element">
                                        <a class="modal-action modal-close waves-effect btn" href="javascript:void(0)">BACK</a>
                                    </div>
                                    <div class="col s10 right-align form-element">
                                        <a class="modal-action modal-close waves-effect btn" href="javascript:void(0)"
                                           data-ng-click="">SUBMIT</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col s12">
                <div class="row">
                    <p class="col s4">
                        <strong>Created On: </strong>
                        {{assetDetail.creationDate | date:'dd/MM/yyyy'}}
                    </p>
                    <p class="col s4">
                        <strong>Created By: </strong> {{assetDetail.createdBy.name}}
                    </p>
                    <p class="col s4">
                        <strong>Creator Id: </strong> {{assetDetail.createdBy.id}}
                    </p>
                    <p class="col s4"><strong>Asset Id: </strong>{{assetDetail.assetId}}</p>
                    <p class="col s4"><strong>Unit Id: </strong>{{assetDetail.unitId}}</p>
                    <p class="col s4"><strong>Unit Type: </strong>{{assetDetail.unitType}}</p>
                    <p class="col s4"><strong>Asset Status: </strong>{{assetDetail.assetStatus}}</p>
                    <p class="col s4"><strong>SKU Id: </strong>{{assetDetail.skuId}}</p>
                    <p class="col s4"><strong>Product Id: </strong>{{assetDetail.productId}}</p>
                    <p class="col s4"><strong>Profile Id: </strong>{{assetDetail.profileId}}</p>
                    <!--<p class="col s4"><strong>GR Id: </strong>{{assetDetail.grId}}</p>-->
                    <!--<p class="col s4"><strong>GR Item Id: </strong>{{assetDetail.grItemId}}</p>-->
                    <!--<p class="col s4"><strong>Vendor Id: </strong>{{assetDetail.vendorId}}</p>-->
                    <!--<p class="col s4"><strong>Vendor Name: </strong>{{assetDetail.vendorName}}</p>-->
                    <p class="col s4"><strong>Owner Id: </strong>{{assetDetail.ownerId}}</p>
                    <p class="col s4"><strong>Owner Type: </strong>{{assetDetail.ownerType}}</p>
                    <!--<p class="col s4"><strong>First Owner Type: </strong>{{assetDetail.firstOwnerType}}</p>-->
                    <!--<p class="col s4"><strong>First Owner Id: </strong>{{assetDetail.firstOwnerId}}</p>-->
                    <p class="col s4"><strong>Tag Type: </strong>{{assetDetail.tagType}}</p>
                    <p class="col s4"><strong>Tag Value: </strong>{{assetDetail.tagValue}}</p>
                    <p class="col s4"><strong>Current Value: </strong> {{assetDetail.currentValue}}}</p>
                    <p class="col s4"><strong>Current Value without tax: </strong> {{assetDetail.currentValueWithoutTax}}}</p>
                    <!--<p class="col s4"><strong>Tag Print Count: </strong>{{assetDetail.tagPrintCount}}</p>-->
                    <!--<p class="col s4"><strong>Last Tag Print Date: </strong>{{assetDetail.lastTagPrintDate}}</p>-->
                    <!--<p class="col s4"><strong>Last Tag Printed By: </strong>{{assetDetail.lastTagPrintedBy}}</p>-->
                    <!--<p class="col s4"><strong>Price: </strong>{{assetDetail.price}}</p>-->
                    <!--<p class="col s4"><strong>Tax: </strong>{{assetDetail.tax}}</p>-->
                    <!--<p class="col s4"><strong>Procurement Cost: </strong>{{assetDetail.procurementCost}}</p>-->
                    <!--<p class="col s4"><strong>Quantity: </strong>{{assetDetail.quantity}}</p>-->
                    <!--<p class="col s4"><strong>LifeTime Type: </strong>{{assetDetail.lifeTimeType}}</p>-->
                    <!--<p class="col s4"><strong>LifeTime Value: </strong>{{assetDetail.lifeTimeValue}}</p>-->
                    <!--<p class="col s4"><strong>LifeTime In Days: </strong>{{assetDetail.lifeTimeInDays}}</p>-->
                    <!--<p class="col s4"><strong>Inventory Date: </strong>{{assetDetail.inventoryDate}}</p>-->
                    <!--<p class="col s4"><strong>Start Date: </strong>{{assetDetail.startDate}}</p>-->
                    <!--<p class="col s4"><strong>Expected End Date: </strong>{{assetDetail.expectedEndDate}}</p>-->
                    <!--<p class="col s4"><strong>Actual End Date: </strong>{{assetDetail.actualEndDate}}</p>-->
                    <!--<p class="col s4"><strong>Depreciation Strategy: </strong>{{assetDetail.depreciationStrategy}}</p>-->
                    <!--<p class="col s4"><strong>Depreciation Rate Per Annum: </strong>{{assetDetail.depreciationRatePa}}-->
                    <!--</p>-->
                    <!--<p class="col s4"><strong>Daily Depreciation Rate: </strong>{{assetDetail.dailyDepreciationRate}}-->
                    <!--</p>-->
                    <!--<p class="col s4"><strong>Depreciation Residue: </strong>{{assetDetail.depreciationResidue}}</p>-->
                    <!--<p class="col s4"><strong>Realized Depreciation: </strong>{{assetDetail.realizedDepreciation}}</p>-->
                    <!--<p class="col s4"><strong>Realized Depreciation Date: </strong>{{assetDetail.realizedDepreciationDate}}-->
                    <!--</p>-->
                    <!--<p class="col s4"><strong>Last Transfer Type: </strong>{{assetDetail.lastTransferType}}</p>-->
                    <!--<p class="col s4"><strong>Last Transfer Id: </strong>{{assetDetail.lastTransferId}}</p>-->
                    <!--<p class="col s4"><strong>Last Transfer Date: </strong>{{assetDetail.lastTransferDate}}</p>-->
                    <!--<p class="col s4"><strong>Last Transfer By: </strong>{{assetDetail.lastTransferedBy}}</p>-->
                    <!--<p class="col s4"><strong>Warranty Last Date: </strong>{{assetDetail.warrantyLastDate}}</p>-->
                    <!--<p class="col s4"><strong>AMC Last Date: </strong>{{assetDetail.amcLastDate}}</p>-->
                    <!--<p class="col s4"><strong>Insurance Last Date: </strong>{{assetDetail.insuranceLastDate}}</p>-->
                    <!--<p class="col s4"><strong>Profile Attribute Mapping List: </strong>{{assetDetail.profileAttributeMappingList}}-->
                    <!--</p>-->
                    <!--<p class="col s4"><strong>Entity Attribute Value Mappings: </strong>{{assetDetail.entityAttributeValueMappings}}-->
                    <!--</p>-->


                    <!--<p class="col s4"><strong>Has Insurance: </strong>{{assetDetail.hasInsurance}}</p>-->
                    <!--<p class="col s4">-->
                    <!--<i data-ng-if="assetDetail.hasInsurance" class="material-icons left">done</i>-->
                    <!--<i data-ng-if="!assetDetail.hasInsurance" class="material-icons left">error</i>-->
                    <!--Has Insurance-->
                    <!--</p>-->
                    <!--<p class="col s4">-->
                    <!--<i data-ng-if="assetDetail.hasWarranty" class="material-icons left">done</i>-->
                    <!--<i data-ng-if="!assetDetail.hasWarranty" class="material-icons left">error</i>-->
                    <!--Has Warranty-->
                    <!--</p>-->
                    <!--<p class="col s4">-->
                    <!--<i data-ng-if="assetDetail.hasAMC" class="material-icons left">done</i>-->
                    <!--<i data-ng-if="!assetDetail.hasAMC" class="material-icons left">error</i>-->
                    <!--Has AMC-->
                    <!--</p>-->
                </div>
            </div>
        </div>
    </div>
</div>

<div data-ng-if="bulkPrint == true">
    <div class="row margin-bottom-5">
        <label>Select Unit </label>
        <select data-ng-model="model.selectedUnitId" data-ng-change="changeUnit(model.selectedUnitId)"
                data-placeholder="Select Unit" ui-select2>
            <option data-ng-repeat="unit in allUnitsList"
                    value="{{unit.id}}">{{unit.name}}
            </option>
        </select>
    </div>

    <div class="row">
        <div class="btn left" data-ng-click="back()">back</div>
        <div class="right">
            <button class="btn-toolbar  btn-medium right " title="print Tags" data-ng-click="printFilteredAssetTags()"
                    style="border: whitesmoke;  color: #0f9d58; text-align: center">Print <i class="material-icons ">print</i>
            </button>
        </div>
    </div>
    <div class="row">
        <div class="col s2 btn" data-ng-click="selectAll()">Select All</div>
        <div class="col s5">
            <label>
                Select Sub Category
            </label>
            <select data-ng-show="assets.length>0" data-ng-model="model.selectedSubCategory"
                    data-ng-options="subCategory for subCategory in subCategories"
                    data-ng-change="applyFilter()"
            >
            </select>
        </div>
        <div class="col s5">
            <label>Select Asset Status </label>
            <select data-ng-model="model.selectedAssetStatuses" data-ng-change="applyFilter()"
                    data-placeholder="Select Asset Status" ui-select2 multiple>
                <option data-ng-repeat="status in assetStatuses"
                        value="{{status}}">{{status}}
                </option>
            </select>
        </div>
    </div>

    <ul class="collection">
        <li class="collection-item list-head">
            <div class="row">
                <div class="col s2">Asset Id</div>
                <div class="col s2">Asset Name.</div>
                <div class="col s2">Tag Value</div>
                <div class="col s2">Sub Category</div>
                <div class="col s2">Asset Status</div>
                <div class="col s2">Print Status</div>
            </div>
        </li>
        <li class="collection-item striped" data-ng-repeat="asset in filteredAssets">
            <div class="row" style="margin-bottom: 0;">
                <div class="col s2">
                    <input class="checkbox large"
                           id="asset-{{asset.assetId}}" data-ng-model="asset.checked" type="checkbox"/>
                    <label for="asset-{{asset.assetId}}">{{asset.assetId}}</label>
                </div>
                <div class="col s2">{{asset.assetName}}</div>
                <div class="col s2">{{asset.tagValue}}</div>
                <div class="col s2">{{asset.subCategoryName}}</div>
                <div class="col s2">{{asset.assetStatus}}</div>
                <div class="col s2" data-ng-class="{'green' : assetPrintStatus[asset.tagValue] == 'SUCCESS' , 'red' : assetPrintStatus[asset.tagValue] == 'FAILURE' ,
                 'orange' : assetPrintStatus[asset.tagValue] == 'PENDING'}">
                    {{assetPrintStatus[asset.tagValue]}}
                </div>
            </div>
        </li>
    </ul>


</div>

<!--Modal-->
<div id="EditNameModal" class="modal modal-medium">

    <div class="modal-content col s12" id="modal-body">
        <div style="margin:5%">

            <div style="border-bottom: 2px solid black">
                <h3>Edit Asset Name</h3>
            </div>
            <div class="row" style="margin-top:5%">
                <div class="col s7">
                    <input type="text" placeholder="Enter New Asset Name"
                           data-ng-model="newAssetName">
                </div>
                <div class="col s5 modal-close">
                    <button class="btn" data-ng-click="updateAssetName(assetDetail.assetId, newAssetName)">Update Asset</button>
                </div>
            </div>


        </div>
        <div class="modal-footer right-align">
            <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
        </div>
    </div>

</div>


