<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    td,th{
        padding:10px 5px !important;
    }
    .select2.select2-container{width: 100% !important;}
    #images{
        text-align: center;
    }
    img#scanned {
        height: 400px; /** Sets the display size */
        margin-right: 12px;
        margin-top: 20px;
    }
</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h5 class="left">Rejected Payment Request</h5>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==true">
        <div class="row">
            <div class="col s8" data-ng-show="prRequestType.shortCode=='GR'">
                <label>Select GR Type</label>
                <span style="margin-right: 20px">
                    <input name="group1" id="t1" type="radio" ng-model="grType" value="INVOICE"/>
                    <label for="t1">Invoice</label>
                </span>
                <span style="margin-right: 20px">
                    <input name="group1" id="t2" type="radio" ng-model="grType" value="DELIVERY_CHALLAN"/>
                    <label for="t2">Delivery Challan</label>
                </span>
            </div>
        </div>

        <div data-ng-show="prRequestType.shortCode=='GR'">

            <div class="row" data-ng-show="grType!=null">
                <div class="col s3">
                    <label>Select Unit*</label>
                    <select ui-select2 id="unitListx" name="unitListx" data-ng-model="selectedUnit"
                            data-ng-change="selectUnit(selectedUnit)"
                            data-ng-options="item as item.name for item in unitList track by item.id"></select>
                </div>
                <div class="col s3">
                    <label>Select Vendor*</label>
                    <select ui-select2 id="vendorListx" name="vendorListx" data-ng-model="selectedVendor"
                            data-ng-options="item as item.name for item in vendorList track by item.id"></select>
                </div>
                <div class="col s2">
                    <input type="button" class="btn" value="Find" data-ng-click="findGrs()" style="margin-top: 25px;" />
                </div>
            </div>

            <div class="row" data-ng-show="grs.length>0">
                <div class="col s12">
                    <table class="table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>GR Id</th>
                            <th>Company Name</th>
                            <th>PR Id</th>
                            <th>Payment Status</th>
                            <th>Rejection Time</th>
                            <th>Rejection Reason</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="gr in grs track by gr.grId">
                            <td data-ng-click="">{{gr.grId}}</td>
                            <td>{{companyMap[gr.companyId].name}}</td>
                            <td>{{gr.prId}}</td>
                            <td>{{gr.status}}</td>
                            <td>{{gr.updateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</td>
                            <td>
                                <input type="button" data-target="viewRejectionModal" class="btn btn-small" ng-click="viewPaymentRequest(gr.prId)" value="Reasons" modal>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div data-ng-if="showNoGR" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No GR with pending payment request found.</div>
        </div>
    </div>
</div>

<div id="viewRejectionModal" class="modal">
    <div class="modal-content">
        <h4>Rejection detail</h4>
        <div class="row">
            <div class="col s12">
                Payment Invoice Deviations:
                <ul>
                    <li data-ng-repeat="d in viewPr.paymentInvoice.deviations"
                        style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
                        <span class="deviationTag {{d.currentStatus}}" tooltipped
                              data-position="bottom" data-delay="10" data-tooltip="{{d.deviationRemark}} ## {{d.actionRemark}}">
                        {{d.paymentDeviation.deviationDetail}}
                    </span>
                    </li>
                </ul>
            </div>
            <div class="col s12">
                Payment Invoice Items Deviations:
                <ul>
                    <li data-ng-repeat="invoiceItemDeviation in viewPr.paymentInvoice.paymentInvoiceItems"
                        style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
                        <span data-ng-repeat="d in invoiceItemDeviation.deviations" class="deviationTag {{d.currentStatus}}" tooltipped
                              data-position="bottom" data-delay="10" data-tooltip="{{d.deviationRemark}} ## {{d.actionRemark}}">
                                    {{d.paymentDeviation.deviationDetail}}
                        </span>
                    </li>
                </ul>
            </div>
            <div class="col s12" data-ng-if="viewPr.paymentInvoice.rejections.length>0">
                Rejection Deviations:
                <ul>
                    <li data-ng-repeat="rejections in viewPr.paymentInvoice.rejections"
                        style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
                        {{rejections.paymentDeviation.deviationDetail}}
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close">Close</button>
    </div>
</div>