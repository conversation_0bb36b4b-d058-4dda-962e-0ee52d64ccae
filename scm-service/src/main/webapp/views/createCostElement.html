<div class="row" style="margin-bottom: 0px; margin-top: -20px"
	data-ng-init="init()">
	<div class="col s12">
		<h4>New Cost Element</h4>
	</div>
</div>
<div class="row">
	<div class="row white z-depth-3 custom-listing-li">
		<div class="row">
			<div class="col s12 m6 l6" style="margin-top: 25px">
				<div class="row">
					<label class=" black-text">Service Name</label> <input type="text"
						data-ng-model="elementName"></input>
				</div>
				<div class="row">
					<label class=" black-text">Unit of Measure</label> <select
						data-ng-model="uom" style="width: 100%"
						data-ng-options="uom as uom for uom in uomMetadata"></select>
				</div>
				<div class="row">
					<label class=" black-text">SAC Code</label> <select ui-select2
						data-ng-model="taxCategory"
						data-ng-options="tax as (tax.code + '-'+ tax.desc) for tax in taxCodes track by tax.code"
						data-ng-change="onTaxCategoryChange()">
					</select>
				</div>
				<div class="row">
					<label class=" black-text">Division</label> <select
						data-ng-model="selectedDivision"
						data-ng-options="division as division.name for division in divisions track by division.listDetailId"></select>
				</div>
				<div class="row">
					<label class=" black-text">Description</label>
					<textarea data-ng-model="description" name="description"
						data-ng-model="cc" data-ng-maxlength="500"></textarea>
				</div>

				

			</div>
			<div class="col s12 m6 l6" style="margin-top: 25px">
				<div class="row">
					<label class=" black-text">Department</label> <select
						data-ng-model="selectedDepartment"
						data-ng-options="department as department.name for department in departments track by department.listDetailId"></select>
				</div>
				<div class="row">
					<label class=" black-text">Select Opex Budget Category</label> <select
						data-ng-change="selectOpexBudgetCategory(selectedOpexBudgetCategory)"
						data-ng-model="selectedOpexBudgetCategory"
						data-ng-options="category for category in budgetCategoryList"></select>
				</div>
				<div data-ng-if="showOpexClassification" class="row">
					<label class=" black-text">Select Classification and Sub Classification for Opex</label>
					<label class=" black-text">(Classification  ->  sub classification)</label><select
						data-ng-change="selectOpexClassification(categoryDetailsForOpex.detail)"
						data-ng-model="categoryDetailsForOpex"
						data-ng-options="obj.classification +'&nbsp;&nbsp;' + '  ->  ' + '&nbsp;&nbsp;' + obj.subClassification for obj in opexClassificationList"></select>
				</div>
				<div class="row">
					<label class=" black-text">Select Capex Budget Category</label> <select
						data-ng-change="selectCapexBudgetCategory(selectedCapexBudgetCategory)"
						data-ng-model="selectedCapexBudgetCategory"
						data-ng-options="category for category in budgetCategoryList"></select>
				</div>
				<div data-ng-if="showCapexClassification" class="row">
					<label class=" black-text">Select Classification and Sub Classification for Capex</label>
					<label class=" black-text">(Classification  ->  sub classification)</label><select
						data-ng-change="selectCapexClassification(categoryDetailsForCapex.detail)"
						data-ng-model="categoryDetailsForCapex"
						data-ng-options="obj.classification +'&nbsp;&nbsp;' + '  ->  ' + '&nbsp;&nbsp;' + obj.subClassification for obj in capexClassificationList"></select>
				</div>
				<div class="row">
					<label class=" black-text">Sub-Sub Classification</label> <select
						data-ng-model="selectedSubSubCategory"
						data-ng-options="subSubCategory as subSubCategory.name for subSubCategory in subSubCategories track by subSubCategory.listDataId"></select>

				</div>
				<div class="row">
					<label class=" black-text">Capex</label> <select
						data-ng-model="selectedCapex"
						data-ng-options="capex as capex for capex in capexs"></select>
				</div>
				<div class="row">
					<label class=" black-text">Status</label> <select
						data-ng-model="selectedStatus"
						data-ng-options="period as period for period in periods"></select>
				</div>
				
				<button class="btn btn-primary right" style="margin-top: 15px;" type="button"
					ng-click="createCostElement()">Submit</button>
			</div>
		</div>
	</div>
</div>




