<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Search Reference Order</h3>
            </div>
        </div>
        <div class="row">
            <div class="col s3">
                <label>Start date</label>
                <input input-date type="text" ng-model="startDate"
                       container="" format="yyyy-mm-dd" />
            </div>
            <div class="col s3">
                <label>End date</label>
                <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
            </div>
            <div class="col s2">
                <label>Reference Order Id</label>
                <input type="text" data-ng-model="referenceOrderId" />
            </div>
            <div class="col s2">
                <label>Status</label>
                <select data-ng-model="status" data-ng-change="show(status)" data-ng-options="item as item for item in scmOrderStatusList"></select>
            </div>
            <div class="col s2">
                <input type="button" class="btn" value="Find" data-ng-click="findReferenceOrders()" style="margin-top: 25px;" acl-action="MTRFOMV" />
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12">
            <ul class="collection striped" data-ng-show="referenceOrderList.length>0">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s1">RO Id</div>
                        <div class="col s2">Generation Time</div>
                        <div class="col s2">Created By</div>
                        <div class="col s2">Fulfillment Date</div>
                        <div class="col s3">Comment</div>
                        <div class="col s2">Status</div>
                    </div>
                </li>
                <li class="collection-item clickable" data-ng-repeat="ro in referenceOrderList | orderBy : '-id' track by ro.id" data-ng-click="openRefOrderAction(ro.id)">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s1">{{ro.id}}</div>
                        <div class="col s2">{{ro.generationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</div>
                        <div class="col s2">{{ro.generatedBy.name}}</div>
                        <div class="col s2">{{ro.fulfillmentDate | date:'dd-MM-yyyy'}}</div>
                        <div class="col s3">{{ro.comment==null?"No comment":ro.comment}}</div>
                        <div class="col s2"><span class="badge">{{ro.status}}</span></div>
                    </div>
                </li>
            </ul>
            <p data-ng-show="referenceOrderList.length==0">No orders found for selected criteria!</p>
        </div>
    </div>
</div>