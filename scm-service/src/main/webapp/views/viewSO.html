<style>
    .custom-modal{
        width: 60% !important;
    }

    .popeye-modal-container .popeye-modal{
    width: 900px;
	}

    .advanceHeader {
        line-height: 40px;
        background: #ea9e13;
        font-weight: 700;
        border-radius: 3px;
    }

    /* Mobile-friendly styles */
    .TableMobileView {
        display: none;
        width: 100%;
        margin: 0;
        padding: 0;
    }

    .TableMobileView .collection {
        margin: 0;
        border: none;
    }

    .TableMobileView .collection-item {
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        background: #fff;
    }

    .TableMobileView .row {
        margin: 0;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .TableMobileView .row:last-child {
        border-bottom: none;
    }

    .TableMobileView .col {
        padding: 4px 8px;
        font-size: 14px;
        line-height: 1.4;
    }

    .TableMobileView .action-content {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #eee;
    }

    .TableMobileView .btn {
        width: 100%;
        margin: 4px 0;
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 4px;
        text-align: center;
        text-transform: none;
    }

    .TableMobileView .approval-btn {
        background-color: #4CAF50;
        color: white;
    }

    .TableMobileView .rejection-btn {
        background-color: #F44336;
        color: white;
    }

    .TableMobileView .no-actions {
        padding: 12px;
        background-color: #f5f5f5;
        border-radius: 4px;
        margin: 8px 0;
        text-align: center;
        font-size: 14px;
        color: #666;
    }

    /* iOS-specific fixes */
    @supports (-webkit-touch-callout: none) {
        .TableMobileView .btn {
            -webkit-appearance: none;
            -webkit-border-radius: 4px;
        }
    }

    /* Responsive breakpoints */
    @media screen and (max-width: 991px) {
        .TableMobileView {
            display: block;
        }

        .standardView {
            display: none;
        }

        .searchingCard {
            display: flex;
            flex-direction: column;
            margin: 0;
            width: 100% !important;
        }

        .searchingCard .col {
            margin-left: 0 !important;
            margin-right: 0 !important;
            width: 100% !important;
        }

        .searchingCard .btn {
            width: 100% !important;
        }

        .custom-modal {
            width: 90% !important;
        }

        .container {
            width: 100%;
            padding: 10px;
        }

        .action-header {
            margin-bottom: 15px;
        }

        .approval-buttons, .rejection-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn-block {
            display: block;
            width: 100%;
            text-align: left;
            padding: 12px 15px;
            font-size: 16px;
            border-radius: 4px;
        }

        /* Show floating buttons only in mobile view */
        .floating-action-buttons {
            display: flex !important;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            padding: 10px;
            z-index: 9999;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }

        .floating-action-buttons .btn {
            margin: 0 5px;
            min-width: 80px;
            font-size: 12px;
            padding: 8px 12px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action-buttons .btn i {
            font-size: 18px;
            margin-right: 3px;
        }

        /* Add padding to bottom of content to prevent overlap with floating buttons */
        .modal-body {
            padding-bottom: 70px;
        }

        /* Hide the original button row on mobile */
        .modal-body .row:last-child {
            display: none !important;
        }

        /* iOS-specific fixes for floating buttons */
        @supports (-webkit-touch-callout: none) {
            .floating-action-buttons {
                position: fixed;
                -webkit-transform: translate3d(0, 0, 0);
                transform: translate3d(0, 0, 0);
            }
        }
    }

    /* Small mobile devices */
    @media screen and (max-width: 480px) {
        .TableMobileView .col {
            font-size: 13px;
            padding: 4px 6px;
        }

        .TableMobileView .btn {
            padding: 10px 12px;
            font-size: 13px;
        }

        .TableMobileView .collection-item {
            padding: 8px;
            margin-bottom: 8px;
        }

        /* Further adjust floating buttons for smaller screens */
        .floating-action-buttons {
            padding: 6px;
        }

        .floating-action-buttons .btn {
            min-width: 70px;
            font-size: 11px;
            padding: 6px 10px;
        }

        .floating-action-buttons .btn i {
            font-size: 16px;
            margin-right: 2px;
        }
    }

    /* Floating Action Button styles */
    .floating-action-buttons {
        display: flex;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.98);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        padding: 10px;
        z-index: 9999;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #eee;
    }

    .floating-action-buttons .btn {
        margin: 0 5px;
        min-width: 80px;
        font-size: 12px;
        padding: 8px 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        height: 36px;
        line-height: 1;
    }

    .floating-action-buttons .btn i {
        font-size: 18px;
        margin-right: 3px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Add padding to bottom of content to prevent overlap with floating buttons */
    .modal-body {
        padding-bottom: 70px;
    }

    /* Desktop view styles */
    @media screen and (min-width: 992px) {
        .floating-action-buttons {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 8px 16px;
            z-index: 9999;
            justify-content: center;
            align-items: center;
            border-top: 1px solid #e0e0e0;
            border-radius: 0;
        }
        
        .floating-action-buttons .btn {
            margin: 0 8px;
            min-width: 120px;
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 20px;
            height: 40px;
            line-height: 1;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .floating-action-buttons .btn i {
            font-size: 20px;
            margin-right: 8px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action-buttons .btn.blue {
            background-color: #2196F3;
            color: white;
        }

        .floating-action-buttons .btn.green {
            background-color: #4CAF50;
            color: white;
        }

        .floating-action-buttons .btn.red {
            background-color: #F44336;
            color: white;
        }

        .floating-action-buttons .btn.grey {
            background-color: #9E9E9E;
            color: white;
        }

        /* Add padding to modal body to prevent content overlap */
        .modal-body {
            padding-bottom: 70px;
        }
    }

    /* Mobile view styles */
    @media screen and (max-width: 991px) {
        .floating-action-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            padding: 10px;
            z-index: 9999;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }

        .floating-action-buttons .btn {
            margin: 0 5px;
            min-width: 80px;
            font-size: 12px;
            padding: 8px 12px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            height: 36px;
            line-height: 1;
        }

        .floating-action-buttons .btn i {
            font-size: 18px;
            margin-right: 3px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
    }

    /* Small mobile devices */
    @media screen and (max-width: 480px) {
        .floating-action-buttons {
            padding: 6px;
        }

        .floating-action-buttons .btn {
            min-width: 70px;
            font-size: 11px;
            padding: 6px 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            height: 32px;
            line-height: 1;
        }

        .floating-action-buttons .btn i {
            font-size: 16px;
            margin-right: 2px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
    }

    /* iOS-specific fixes for floating buttons */
    @supports (-webkit-touch-callout: none) {
        .floating-action-buttons {
            position: fixed;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }
    }
</style>
<div class="row" data-ng-init="init()">

    <div class="col s12">
        <div class="row white z-depth-3">
            <div class="col s12">
                <h4 data-ng-if="!showViewActions">Pending Service Orders Requests</h4>
                <h4 data-ng-if="showViewActions">View Service Orders</h4>
            </div>
            <div class="col s4">
                <input id="capexIdCheck" type="checkbox"  data-ng-model="isCapexId"  data-ng-click="checkCapexId()">
                <label for="capexIdCheck">Search from Capex Request ID</label>
            </div>

            <div class="searchingCard col margin-bottom-15">
                <div class="col s2">
                    <label>Select Start date</label>
                    <input input-date type="text" data-ng-disabled="isCapexId == true" ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select End date</label>
                    <input input-date type="text" data-ng-disabled="isCapexId == true" ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select Vendor</label>
                   <select id="vendors"  ui-select2="{allowClear:true, placeholder: 'Select Vendor'}"
                        ng-options="vendor as vendor.name for vendor in vendors"
                             data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                        </div>
                <div class="col s2">
                    <label>Business Cost Center</label>
                    <div data-ng-hide="isCapexId == true" >
                        <select id="bcc" ui-select2="{allowClear:true, placeholder: 'Select Business Cost Center'}"
                        ng-options="bcc as bcc.name for bcc in bcc"
                        data-ng-change="selectBcc(bccSelected)" data-ng-model="bccSelected"></select>
                    </div>


                       <div data-ng-hide="isCapexId == false" >
                        <select id="bcc" ui-select2="{allowClear:true, placeholder: 'Select Business Cost Center', disabled:true}"
                        ng-options="bcc as bcc.name for bcc in bcc"
                        data-ng-change="selectBcc(bccSelected)" data-ng-model="bccSelected"></select>
                       </div>

                    </div>

                <div class="col s2" data-ng-disabled="isCapexId == true">
                    <label>Select Cost Center</label>
                    <div data-ng-hide="isCapexId == true">
                        <select ui-select2="{allowClear:true, placeholder: 'Select Cost Center'}"  id="centerList" name="centerList"
                        data-ng-model="costCenterSelected"
                        data-ng-change="selectCostCenter(costCenterSelected)"
                        data-ng-options="center as center.name for center in costCenters track by center.id"></select>
                    </div>

                    <div data-ng-hide="isCapexId == false">
                        <select ui-select2="{allowClear:true, placeholder: 'Select Cost Center', disabled:true }"  id="centerList" name="centerList"
                        data-ng-model="costCenterSelected"
                        data-ng-change="selectCostCenter(costCenterSelected)"
                        data-ng-options="center as center.name for center in costCenters track by center.id"></select>
                    </div>
                </div>
                <div class="col s2">
                    <label>Select Type</label>
                    <select id="types"  ui-select2="{allowClear:true, placeholder: 'Select Type'}"
                            ng-options="type as type for type in types"
                            data-ng-change="selectType(typeSelected)" data-ng-model="typeSelected"></select>
                </div>

                <div class="col s2">
                    <label>Service Order ID</label>
                    <input type="text" ng-model="serviceOrderId" data-ng-disabled="isCapexId == true"/>
                </div>
                <div class="col s2">
                    <label>Capex Request ID </label>
                    <input type="text" id="enterCapexId" data-ng-model="capexRequestId" data-ng-disabled="isCapexId == false"  data-ng-change="changeCapexId(capexRequestId)">
                  </div>


                  <div class="col s2" data-ng-if="isCapexId">
                    <button class="btn" data-ng-click="getSoByCapex()" >Find</button>
              </div>

                <div class="col s1"  data-ng-if="!isCapexId">
                    <button class="btn btn-small margin-top-20" data-ng-click="getSOs(true)" acl-action="VSRCVA">Find All</button>
                </div>
                <div class="col s1"  data-ng-if="!isCapexId">
                    <button class="btn btn-small margin-top-20" data-ng-click="getSOs(false)">Find</button>
                </div>
            </div>

            <hr>
            <div class="col s12" data-ng-if="soRequestShort.length==0" style="padding:30px;color:gray;font-size: 20px;">
                <div class="row margin0 center" ng-if="!showViewActions">
                    No Pending Orders found for Approval
                </div>
                <div class="row margin0 center" ng-if="showViewActions">
                    No Service Orders found for the selected criteria
                </div>
            </div>
            <div class="row">
                <div class="col s9" style="margin-left:105px;">
                    <div class="row" style="margin-top: 30px;" ng-if="(vendorSelected != null) && (approvedAmount != null || pendingApprovalL1 != null|| pendingApprovalL2 != null || inProgessAmount != null)">
                        <table class="bordered striped">
                            <thead>
                            <tr>
                                <th>Approved Amount</th>
                                <th>In Progress Amount</th>
                                <th>Pending Approval L1</th>
                                <th>Pending Approval L2</th>
                                <th>Pending Approval L3</th>
                                <th>Finance Pending Approval L1</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>{{approvedAmount.toFixed(2)}}</td>
                                <td>{{inProgessAmount.toFixed(2)}}</td>
                                <td>{{pendingApprovalL1.toFixed(2)}}</td>
                                <td>{{pendingApprovalL2.toFixed(2)}}</td>
                                <td>{{pendingApprovalL3.toFixed(2)}}</td>
                                <td>{{finApprovalL1.toFixed(2)}}</td>
                            </tr>
                            </tbody>
                        </table>0
                    </div>
                </div>
            </div>
            <div class="row" data-ng-if="isCapexId">
                <h5>Summary : </h5>
                <div data-ng-if="!capexSummaryShow && capexSummaryLoading"><p style="margin: 10px; font-size: 20px;">Loading Summary...</p></div>
                <div class="col s12" data-ng-if="capexSummaryShow">
                    <table class="table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>Department</th>
							<th>Initial Amt</th>
							<th>Original Amt</th>
                            <th>Budget Amt</th>
							<th>Remaining Amt</th>
							<th>Running Amt</th>
							<th>Receiving Amt</th>
							<th>Paid Amt</th>
							<th>SO #</th>
							<th>Pending SO</th>
							<th>PO #</th>
							<th>Pending PO</th>
							<th>Vendor #</th>
                            <th>SO's Department Wise</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat-start="items in summaryDepartmentList">
                            <td>{{items.departmentName}}</td>
							<td>{{items.initialAmount.toFixed(2)| number}}</td>
							<td>{{items.originalAmount.toFixed(2)| number}}</td>
							<td>{{items.budgetAmount.toFixed(2)| number}}</td>
							<td>{{items.remainingAmount.toFixed(2)| number}}</td>
							<td>{{items.runningAmount.toFixed(2)| number}}</td>
							<td>{{items.receivingAmount.toFixed(2)| number}}</td>
							<td>{{items.paidAmount.toFixed(2)| number}}</td>
							<td>{{items.totalSO}}</td>
							<td>{{items.soPendingApproval}}</td>
							<td>{{items.totalPO}}</td>
							<td>{{items.poPendingApproval}}</td>
							<td>{{items.vendorCount}}</td>
                            <td style="color: black;width: 45px; margin-top: 5px; margin-bottom: 5px;">
								<button class="btn btn-primary" data-ng-style="isSelectedItem(items) ?
							{'background-color' : selectedItem == item.departmentName ? '#85BF3F' : '#26a69a'} : {}"
										data-ng-click="showSOLevelDetail(items)">{{selectedItem == items.departmentName ? "HideSO's" : "ShowSO's"}}</button></td>
                            <td><button class="btn" data-ng-click="exportDataToSheet(items)">Download Item level Data</button></td> 
                                    </tr>
                      <tr ng-if = "selectedItem == items.departmentName"  ng-repeat-end="" >
                        <td colspan="12">
                            <div
                                    class="col s12 grid"
                                    id="grid"
                                    ui-grid="gridOptions"
                                    ui-grid-save-state
                                    ui-grid-resize-columns
                                    ui-grid-move-columns
                                    ui-grid-exporter></div>
                        </td>
                    </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col s12 respTable standardView" ng-if="soRequestShort.length>0">
                <div class="row">
                    <ul class="collection striped center" >
                         <li class="collection-item list-head">
                            <div class="row" style="font-size:12px">
                                <div class="col s1">ID</div>
                                <div class="col s1">Generation Time</div>
                                <div class="col s1">Created By</div>
                                <div class="col s1">BCC</div>
                                <div class="col s1">Vendor Name</div>
                                <div class="col s1">Location Name</div>
                                <div class="col s1">Total Cost</div>
                                <div class="col s1">Total Tax</div>
                                <div class="col s1">Total Amount</div>
                                <div class="col s1">Status</div>
                                <div class="col s2">Actions</div>
                            </div>
                        </li>
                        <li class="collection-item " style="padding:5px;" data-ng-repeat="soR in soRequestShort track by $index">
                            <div class="row margin0" data-ng-class="{'red white-text': soR.id==createdPO}"  style="font-size:12px;">
                                <div class="col s1"><div class="pointer underline"
                                          data-ng-click="showDetailsSO(soR)">{{soR.id}}</div>
                                    <div style= "color: #Ff4d4d">({{soR.type}})</div></div>
                                <div class="col s1">{{soR.generationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                                <div class="col s1">{{soR.employeeName}}[{{soR.generatedBy}}]</div>
                                <div class="col s1" data-ng-if="soR.orderItems.length==1" >{{soR.orderItems[0].businessCostCenterName}}</div>
                                <div class="col s1" data-ng-if="soR.orderItems.length > 1" >Multiple [{{soR.orderItems.length}}]</div>
                                <div class="col s1">{{soR.vendorName}}</div>
                                <div class="col s1">{{soR.dispatchLocationCity}}</div>
                                <div class="col s1">{{soR.totalCost.toFixed(2)}}</div>
                                <div class="col s1">{{soR.totalTaxes.toFixed(2)}}</div>
                                <div class="col s1">{{soR.totalAmount.toFixed(2)}}</div>
                                <div class="col s1" style="word-break: break-all;">{{soR.status}}</div>

                                <div class="col s2" data-ng-if="!showViewActions && (soR.status.includes('APPROVAL') || soR.status == 'CREATED') ">
                                    <!-- <button class="btn btn-xs-small vBtn margin-right-5"
                                            acl-action = "SOAPR" data-ng-click="approve(soR.id,$index)">Approve</button> -->
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL1" data-ng-if="soR.status == 'PENDING_APPROVAL_L1' || soR.status == 'CREATED'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L1</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL2" data-ng-if="soR.status == 'PENDING_APPROVAL_L2'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L2</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOFAPL1" data-ng-if="soR.status == 'FIN_APPROVAL_L1'" data-ng-click="changeStatusSO(soR,'APPROVED')">Finance_Approve_L1</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL3" data-ng-if="soR.status == 'PENDING_APPROVAL_L3'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L3</button>
                                    <!--<button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL4" data-ng-if="soR.status == 'PENDING_APPROVAL_L4'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L4</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL5" data-ng-if="soR.status == 'PENDING_APPROVAL_L5'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L5</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL6" data-ng-if="soR.status == 'PENDING_APPROVAL_L6'" data-ng-click="changeStatusSO(soR,'APPROVED')">Approve_L6</button> -->

                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL1" data-ng-if="soR.status == 'PENDING_APPROVAL_L1' || soR.status == 'CREATED'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L1</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL2" data-ng-if="soR.status == 'PENDING_APPROVAL_L2'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L2</button>
                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOFAPL1" data-ng-if="soR.status == 'FIN_APPROVAL_L1'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Finance_Reject_L1</button>

                                                                         <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL3" data-ng-if="soR.status == 'PENDING_APPROVAL_L3'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L3</button>
                                    <!--                                    <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL4" data-ng-if="soR.status == 'PENDING_APPROVAL_L4'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L4</button>
                                                                        <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL5" data-ng-if="soR.status == 'PENDING_APPROVAL_L5'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L5</button>
                                                                        <button class="btn btn-xs-small vBtn margin-right-5" acl-action="SOAPL6" data-ng-if="soR.status == 'PENDING_APPROVAL_L6'" style="margin-top:5px" data-ng-click="changeStatusSO(soR,'REJECTED')">Reject_L6</button> -->
                                    <button class="btn btn-xs-small vBtn margin-right-5" data-ng-if="soR.uploadedDocumentId!=null" style="margin-top: 5px"
                                        data-ng-click="downloadDocumentById(soR.uploadedDocumentId)">Download Document</button>
                                    <span data-ng-if="soR.status!='CREATED' && !soR.status.includes('APPROVAL')">
                                        No Actions available for Order
                                    </span>
                                </div>
                                <div class="col s2" data-ng-if="showViewActions">
                                    <button class="btn btn-xs-small vBtn margin-right-5"
                                            data-ng-if="(soR.status=='CREATED' || soR.status=='APPROVED' || soR.status=='PENDING_APPROVAL_L1')"
                                            data-ng-click="cancel(soR,$index)" acl-action="SOCNCL">Cancel</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5"
                                            acl-action="HODSGRP"
                                            data-ng-if="soR.status=='PENDING_HOD_APPROVAL'"
                                            data-ng-click="openHodActionsModal(soR)">HOD Actions</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5"
                                            data-ng-if="!(soR.status=='CREATED'|| soR.status=='CANCELLED' || soR.status.includes('PENDING_APPROVAL_L1'))"
                                            data-ng-click="printInvoice(soR.soInvoiceDocument,$index)">Print</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5"
                                            data-ng-if="soR.status=='IN_PROGRESS'"
                                            data-ng-click="close(soR.id,$index,soR)" acl-action="SOCLS">Close</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5" data-ng-if="soR.uploadedDocumentId !=null" style="margin-top: 5px"
                                            data-ng-click="downloadDocumentById(soR.uploadedDocumentId)">Download Document</button>
                                    <br>
                                    <span  data-ng-if="!(soR.status=='CREATED' || soR.status=='IN _PROGRESS' || soR.status=='APPROVED' || soR.status.includes('PENDING_APPROVAL_L1'))">
                                        No Actions available for this order
                                    </span>
                                </div>
                            </div>
                            <div class="row bordered" data-ng-if="soR.adjustOrRefund">
                                <div class="advanceHeader">
                                    Advance : <span class="chip grey white-text">Total Advance Amount {{cancelObject.completePrAmount.toFixed(2)}}</span>
                                    <span class="chip green white-text">Available Amount {{cancelObject.completeAvailableAmount.toFixed(2)}}</span>
                                    <span class="chip red darken-1 white-text">Blocked {{cancelObject.completeBlockedAmount.toFixed(2)}}</span>
                                    <input type="button" class="btn red right" value="Close" data-ng-click="closeAdjustRefund(soR)">
                                </div>
                                <div class="row margin-top-10">
                                    <br>
                                    <div class="row">
                                        <div class="col s6">
                                            <label for="adjustOrRefund">Do You Want to get Refund Or Adjust Amount For Other SO's ?</label>
                                            <select id="adjustOrRefund" data-ng-model="cancelObject.adjustOrRefund" class="form-control"
                                                    data-ng-change="setAdjustOrRefund(cancelObject.adjustOrRefund)">
                                                <option value="Adjust">Adjust</option>
                                                <option value="Refund">Refund</option>
                                            </select>
                                        </div>
                                    </div>
                                    <br>
                                    <div class="row margin-top-10" data-ng-if="cancelObject.adjustOrRefund == 'Adjust'">
                                        <div data-ng-if="pendingPoSo.length == 0 && errorMessage == null" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">
                                            No Pending {{cancelObject.advanceType == "PO_ADVANCE" ? "Purchase orders" : "Service orders"}} Found to Adjust Advance Payment
                                        </div>
                                        <div data-ng-if="pendingPoSo.length == 0 && errorMessage != null" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">
                                            {{errorMessage}}
                                        </div>
                                        <div class="row" data-ng-if="pendingPoSo.length > 0">
                                            <input type="text" data-ng-model="enterredText" placeholder="Enter to Search" data-ng-change="setEnterredText(enterredText)">
                                        </div>
                                        <ul class="col s12" data-collapsible="accordion" watch data-ng-if="pendingPoSo.length > 0">
                                            <li class="row" data-ng-repeat="so in pendingPoSo | filter : enterredText">
                                                <div class="collapsible-header poNumber waves-effect waves-light lighten-5 s12">
                                                    <div class="left" data-ng-click="$event.stopPropagation()">
                                                        <input id="RO-{{so.id}}" data-ng-model="so.checked" type="checkbox" data-ng-click="disableSosSelection($event,so,pendingPoSo)" ng-disabled="so.disable"/>
                                                        <label for="RO-{{so.id}}">SO# {{so.id}} Created: {{so.generationTime | date:'dd/MM/yyyy'}}
                                                            for Rs.{{so.totalAmount.toFixed(2)}}</label>
                                                    </div>
                                                    <div class="right">
                                                        <span class="chip">{{so.status}}</span>
                                                        <i class="fa fa-caret-down right"></i>
                                                    </div>
                                                </div>
                                                <div class="collapsible-body">
                                                    <table class="bordered striped">
                                                        <thead>
                                                        <tr>
                                                            <th class="center-align">Cost Element</th>
                                                            <th class="center-align">UOM</th>
                                                            <th class="center-align">Total Qty</th>
                                                            <th class="center-align">Price</th>
                                                            <th class="center-align" tooltipped
                                                                data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*
                                                            </th>
                                                            <th class="center-align">Taxes</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr data-ng-repeat="item in so.orderItems track by $index">
                                                            <td class="center-align">{{item.costElementName}}[{{item.ascCode}}]({{item.costElementDate| date :'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})</td>
                                                            <td class="center-align">{{item.unitOfMeasure}}</td>
                                                            <td class="center-align">{{item.requestedQuantity.toFixed(6)}}</td>
                                                            <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                                            <td class="center-align">{{(item.totalCost + item.totalTax).toFixed(2)}}</td>
                                                            <td class="center-align">{{item.totalTax.toFixed(2)}}</td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="row" data-ng-if="poSoSelected != null">
                                        <input type="button" class="btn left" value="Submit" data-ng-click="submitAdjustmentRefund(poSoSelected,soR)" />
                                    </div>
                                    <div class="row" data-ng-if="cancelObject.adjustOrRefund == 'Refund'">
                                        <div class="row">
                                            <div class="col s6">
                                                <label>Select Refund date</label>
                                                <input input-date type="text" name="refundDate" id="refundDate"
                                                       ng-model="cancelObject.refundDate"
                                                       container="" format="yyyy-mm-dd"
                                                       min="{{minRefundDate}}"
                                                       data-ng-change="setRefundDate(cancelObject.refundDate)"/>
                                            </div>
                                            <div class="col s6" data-ng-if="cancelObject.refundDate != null" style="margin-top: 20px;">
                                                <input type="button" class="btn" value="Submit" data-ng-click="submitAdjustmentRefund(null,soR)" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="TableMobileView" ng-if="soRequestShort.length>0">
                <ul class="collection">
                    <li class="collection-item" data-ng-repeat="soR in soRequestShort track by $index"
                        data-ng-class="{'red white-text': soR.id==createdPO}">
                        <div class="row">
                            <div class="col s6">ID</div>
                            <div class="col s6 pointer underline" data-ng-click="showDetailsSO(soR)">{{soR.id}}</div>
                        </div>
                        <div class="row">
                            <div class="col s6">Generation Time</div>
                            <div class="col s6">{{soR.generationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                        </div>
                        <div class="row">
                            <div class="col s6">Vendor Name</div>
                            <div class="col s6">{{soR.vendorName}}</div>
                        </div>
                        <div class="row">
                            <div class="col s6">Location Name</div>
                            <div class="col s6">{{soR.dispatchLocationCity}}</div>
                        </div>
                        <div class="row">
                            <div class="col s6">Total Cost</div>
                            <div class="col s6">{{soR.totalCost.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col s6">Total Tax</div>
                            <div class="col s6">{{soR.totalTaxes.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col s6">Total Amount</div>
                            <div class="col s6">{{soR.totalAmount.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col s6">Status</div>
                            <div class="col s6" style="word-break: break-all;">{{soR.status}}</div>
                        </div>
                        <div class="action-content" data-ng-if="!showViewActions && (soR.status.includes('APPROVAL') || soR.status == 'CREATED')">
                            <!-- Approval buttons -->
                            <div class="approval-buttons">
                                <button class="btn approval-btn waves-effect waves-light"
                                        acl-action="SOAPL1"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L1' || soR.status == 'CREATED'"
                                        data-ng-click="changeStatusSO(soR,'APPROVED')">
                                    <i class="material-icons left">check_circle</i> Approve L1
                                </button>

                                <button class="btn approval-btn waves-effect waves-light"
                                        acl-action="SOAPL2"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L2'"
                                        data-ng-click="changeStatusSO(soR,'APPROVED')">
                                    <i class="material-icons left">check_circle</i> Approve L2
                                </button>

                                <button class="btn approval-btn waves-effect waves-light"
                                        acl-action="SOFAPL1"
                                        data-ng-if="soR.status == 'FIN_APPROVAL_L1'"
                                        data-ng-click="changeStatusSO(soR,'APPROVED')">
                                    <i class="material-icons left">check_circle</i> Finance Approve L1
                                </button>

                                <button class="btn approval-btn waves-effect waves-light"
                                        acl-action="SOAPL3"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L3'"
                                        data-ng-click="changeStatusSO(soR,'APPROVED')">
                                    <i class="material-icons left">check_circle</i> Approve L3
                                </button>
                            </div>

                            <!-- Rejection buttons -->
                            <div class="rejection-buttons">
                                <button class="btn rejection-btn waves-effect waves-light"
                                        acl-action="SOAPL1"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L1' || soR.status == 'CREATED'"
                                        data-ng-click="changeStatusSO(soR,'REJECTED')">
                                    <i class="material-icons left">cancel</i> Reject L1
                                </button>

                                <button class="btn rejection-btn waves-effect waves-light"
                                        acl-action="SOAPL2"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L2'"
                                        data-ng-click="changeStatusSO(soR,'REJECTED')">
                                    <i class="material-icons left">cancel</i> Reject L2
                                </button>

                                <button class="btn rejection-btn waves-effect waves-light"
                                        acl-action="SOFAPL1"
                                        data-ng-if="soR.status == 'FIN_APPROVAL_L1'"
                                        data-ng-click="changeStatusSO(soR,'REJECTED')">
                                    <i class="material-icons left">cancel</i> Finance Reject L1
                                </button>

                                <button class="btn rejection-btn waves-effect waves-light"
                                        acl-action="SOAPL3"
                                        data-ng-if="soR.status == 'PENDING_APPROVAL_L3'"
                                        data-ng-click="changeStatusSO(soR,'REJECTED')">
                                    <i class="material-icons left">cancel</i> Reject L3
                                </button>
                            </div>

                            <!-- No actions available message -->
                            <div class="no-actions" data-ng-if="soR.status!='CREATED' && !soR.status.includes('APPROVAL')">
                                <p class="center-align">No Actions available for Order</p>
                            </div>
                        </div>
                        <div class="action-content" data-ng-if="showViewActions">
                            <button class="btn waves-effect waves-light"
                                    data-ng-if="(soR.status=='CREATED' || soR.status=='APPROVED' || soR.status=='PENDING_APPROVAL_L1')"
                                    data-ng-click="cancel(soR,$index)" acl-action="SOCNCL">Cancel</button>

                            <button class="btn waves-effect waves-light"
                                    data-ng-if="!(soR.status=='CREATED'|| soR.status=='CANCELLED' || soR.status.includes('PENDING_APPROVAL_L1'))"
                                    data-ng-click="printInvoice(soR.soInvoiceDocument,$index)">Print</button>

                            <button class="btn waves-effect waves-light" 
                                    data-ng-if="soR.status=='IN_PROGRESS'"
                                    data-ng-click="close(soR.id,$index,soR)" acl-action="SOCLS">Close</button>

                            <div class="no-actions" data-ng-if="!(soR.status=='CREATED' || soR.status=='IN_PROGRESS' || soR.status=='APPROVED' || soR.status.includes('PENDING_APPROVAL_L1'))">
                                <p class="center-align">No Actions available for this order</p>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<script type="text/ng-template" id="departmentModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="row">
                <h5 style="text-align-last: center;">SO# {{selectedSOR.id}} issued for {{selectedSOR.vendor.name}}
                    {{selectedSOR.dispatchLocation.city}}</h5>
                <div class="row standardView" style="max-height: 500px; overflow: auto;">
                    <table class="bordered striped">
                        <thead>
                        <tr>
                            <th>Cost Element</th>
                            <th>BCC</th>
                            <th>Service Description</th>
                            <th>Unit Of Measure</th>
                            <th>Unit Price</th>
                            <th>Requested Quantity</th>
                            <th>Tax Rate</th>
                            <th>Cost</th>
                            <th>Tax</th>
                            <th>Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="item in selectedSOR.orderItems">
                            <td>{{item.costElementName}}[{{item.ascCode}}]({{item.costElementDate| date:'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})
                            </td>
                            <td>{{item.businessCostCenterName}}</td>
                            <td style="word-break: break-all;">{{item.serviceDescription}}</td>
                            <td>{{item.unitOfMeasure}}</td>
                            <td>{{item.unitPrice}}</td>
                            <td>{{item.requestedQuantity}}</td>
                            <td>{{item.taxRate}}</td>
                            <td>{{item.totalCost.toFixed(2)}}</td>
                            <td>{{item.totalTax.toFixed(2)}}</td>
                            <td>{{item.amountPaid.toFixed(2)}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="TableMobileView">
                    <ul class="collection striped center">
                        <li class="collection-item" data-ng-repeat="item in selectedSOR.orderItems">
                            <div class="row">
                                <div class="col">Cost Element</div>
                                <div class="col">{{item.costElementName}}[{{item.ascCode}}]({{item.costElemendivate| date
                                    :'dd-MM-yyyy'}})</div>
                            </div>
                            <div class="row">
                                <div class="col">BCC</div>
                                <div class="col">{{item.businessCostCenterName}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Service Description</div>
                                <div class="col" style="word-break: break-all;">{{item.serviceDescription}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Unit Of Measure</div>
                                <div class="col">{{item.unitOfMeasure}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Unit Price</div>
                                <div class="col">{{item.unitPrice}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Requested Quantity</div>
                                <div class="col">{{item.requestedQuantity}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Tax Rate</div>
                                <div class="col">{{item.taxRate}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Cost</div>
                                <div class="col">{{item.totalCost.toFixed(2)}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Tax</div>
                                <div class="col">{{item.totalTax.toFixed(2)}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Amount</div>
                                <div class="col">{{item.amountPaid.toFixed(2)}}</div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="row right-align" style="line-height: 30px;">
            <b>Taxable Amount:</b> Rs.{{billAmount}}<br/>
            <b>Total Taxes:</b> Rs.{{totalTaxes}}<br/>
            <b>Paid Amount:</b> Rs.{{paidAmount}}<br/>
            <div data-ng-if="selectedSOR.status=='PENDING_APPROVAL_L1'">
            <b>SO Generated by:</b> {{selectedSOR.lastUpdatedBy.name}} - {{selectedSOR.lastUpdatedBy.id}}
            </div>
            <div data-ng-if="selectedSOR.status!='PENDING_APPROVAL_L1'">
            <b>Last Approved by:</b> {{selectedSOR.lastUpdatedBy.name}} - {{selectedSOR.lastUpdatedBy.id}}
            </div>



        </div>
        <div class="row">
            <h5 style="text-align-last: center;">Capex Budget Department Data</h5>
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Department Name</th>
                        <th>Total Amount</th>
                        <th>Original Amount</th>
                        <th>Budget Amount</th>
                        <th>Remaining Amount</th>
                        <th>Running Amount</th>
                        <th>Receiving Amount</th>
                        <th>Paid Amount</th>
                        <th>Allocated Cafe</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="items in summaryItem">
                        <td>{{items.departmentName}}</td>
                        <td>{{items.amountPaid.toFixed(2)}}</td>
                        <td>{{items.originalAmount.toFixed(2)}}</td>
                        <td>{{items.budgetAmount.toFixed(2)}}</td>
                        <td>{{items.remainingAmount.toFixed(2)}}</td>
                        <td>{{items.runningAmount.toFixed(2)}}</td>
                        <td>{{items.receivingAmount.toFixed(2)}}</td>
                        <td>{{items.paidAmount.toFixed(2)}}</td>
                        <td>{{items.businessCostCenterName}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <!-- Original button row for non-mobile view -->
            <div class="row right-align">
                <button class="btn waves-effect waves-light blue" 
                        data-ng-if="selectedSOR.approvalOfHodDocumentId != null" 
                        data-ng-click="downloadDocumentById(selectedSOR.approvalOfHodDocumentId)">
                    <i class="material-icons left">file_download</i> Download HOD Approval
                </button>

                <button data-ng-show="status" 
                        class="btn waves-effect waves-light green" 
                        data-ng-click="submit()">
                    <i class="material-icons left">check_circle</i> Approve
                </button>

                <button data-ng-show="!status" 
                        class="btn waves-effect waves-light red" 
                        data-ng-click="submit()">
                    <i class="material-icons left">cancel</i> Reject
                </button>

                <button class="btn waves-effect waves-light grey" 
                        data-ng-click="cancel()">
                    <i class="material-icons left">close</i> Cancel
                </button>
            </div>

            <!-- Floating action buttons for mobile view -->
            <div class="floating-action-buttons">
                <!-- Download HOD Approval button -->
                <button class="btn waves-effect waves-light blue" 
                        data-ng-if="selectedSOR.approvalOfHodDocumentId != null" 
                        data-ng-click="downloadDocumentById(selectedSOR.approvalOfHodDocumentId)">
                    <i class="material-icons">file_download</i> Download HOD
                </button>


                <!-- Cancel button -->
                <button class="btn waves-effect waves-light grey" 
                        data-ng-click="cancel()">
                    <i class="material-icons">close</i> Cancel
                </button>

                <!-- Reject button -->
                <button data-ng-show="!status" 
                        class="btn waves-effect waves-light red" 
                        data-ng-click="submit()">
                    <i class="material-icons">cancel</i> Reject
                </button>

                <!-- Approve button -->
                <button data-ng-show="status" 
                        class="btn waves-effect waves-light green" 
                        data-ng-click="submit()">
                    <i class="material-icons">check_circle</i> Approve
                </button>

                
            </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="hodActionsModal.html" class="modal-large">
    <div class="modal-content" style="overflow-x: auto; max-height: 350px;" data-ng-init="initHodModal()">
        <div class="row" style="background-color: yellow">
            <h5>Request For {{lastStatus == 'APPROVED' ? 'Cancellation' : 'Closing'}} of Service Order</b></h5>
        </div>
        <div class="row" style="width:98%;">
            <div class="col s6">
                <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                    <p class="center"><b><u>Advance Details</u></b></p>
                    <p data-ng-if="isAdjusted"><b>Adjusted With {{so.vendorAdvancePayments[0].advanceType == 'SO_ADVANCE' ? 'SO' : 'PO'}} :</b> {{selectedSoPo}}</p>
                    <p data-ng-if="isAdjusted"><b>Adjusted Amount :</b> {{amount}}</p>
                    <p data-ng-if="!isAdjusted"><b>Refund Date :</b> {{refundSelectedDate}}</p>
                    <p data-ng-if="!isAdjusted"><b>Refund Amount :</b> {{amount}}</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="waves-effect waves-green btn-flat red left" data-ng-click="approveRejectAdjustmentRefund('REJECTED')">Reject</button>
            <button class="waves-effect waves-green btn right" data-ng-click="approveRejectAdjustmentRefund('APPROVED')">Approve</button>
        </div>
    </div>
</script>
