<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12 m6 l6">
                <h4>Standalone Transfer Order</h4>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col s12 m6 l6">
            <label>Select Unit Type:</label>
            <select ui-select2 ng-model="selectedCategory" data-placeholder="Select Category" data-ng-change="changeCategory(selectedCategory)">
                <option value=""></option>
                <option ng-repeat="category in avilableCategories" value="{{category}}">{{category}}</option>
            </select>
        </div>
        <div class="col s12 m6 l6 margin-top-20" data-ng-show="isCafeToCafe" acl-action="GNTCTC">
            <input  id="onlyGnt"
                   data-ng-model="ignoreInterCafeTransferFlag"
                   type="checkbox"/>
            <label for="onlyGnt">Only GNT</label>

        </div>
    </div>

    <div class="row">
        <div class="col s12 m6 l6">
            <label>Select Receiving Unit:</label>
            <select ui-select2 ng-model="selectedUnit"  data-placeholder="Select Unit" data-ng-change="setReceivingUnit(selectedUnit)">
                <option value=""></option>
                <option ng-repeat="unit in selectedUnitList" value="{{unit}}">{{unit.name}}</option>
            </select>
        </div>
        <div class="col s12 m6 l6" data-ng-show="receivingUnit != null">
            <label>Select Product:</label>
            <select style="width: auto;" ui-select2 ng-model="selectedProduct"  data-placeholder="Enter name of product">
                <option value=""></option>
                <option ng-repeat="product in scmProductDetails | filter : byProducts | filter : categoryFilter" value="{{product}}">{{product.productName}}</option>
            </select>
            <input type="button" class="btn" value="ADD PRODUCT" data-ng-click="addNewTOItem()" acl-action="TRNSTA" />
        </div>
    </div>


    <div class="row" data-ng-show="TOProducts.length>0">
        <form name="trForm" novalidate>
            <ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
                <li class="collection-item list-head">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s3">#</div>
                        <div class="col s3">Product Name</div>
                        <div class="col s3">Transferred Quantity</div>
                        <div class="col s3">Unit Of Measure</div>
                    </div>
                </li>
                <li style="margin-bottom: 10px; border:#ddd 1px solid;" data-ng-repeat="item in TOProducts track by $index ">
                    <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid;">
                        <div class="col s3">{{$index+1}}</div>
                        <div class="col s3"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                        <div class="col s3">{{item.transferredQuantity==null?0:item.transferredQuantity}}</div>
                        <div class="col s3">{{item.unitOfMeasure}}</div>
                    </div>
                    <div class="row">
                        <div class="col s5">
                            <label>Select SKU</label>
                            <select data-ng-model="item.selectedSku" data-ng-options="sku as sku.skuName for sku in item.skuList track by sku.skuId"></select>
                        </div>
                        <div class="col s5">
                            <label>Select Packaging:</label>
                            <select data-ng-model="item.selectedPackaging"
                                    data-ng-options="packaging as packaging.packagingDefinition.packagingName group by packaging.packagingDefinition.packagingType for packaging
                                     in item.selectedSku.skuPackagings | orderBy: 'packagingDefinition.packagingType' track by packaging.packagingId"
                                    data-ng-init="item.selectedPackaging=item.selectedSku.skuPackagings[0]"></select>
                        </div>
                        <div class="col s2">
                            <input type="button" value="Add" class="btn" data-ng-click="addPackaging(item)" style="margin-top:20px;" />
                            <input type="button" value="Remove" class="btn red" data-ng-click="TOProducts.splice($index, 1)" style="margin-top:20px;" />
                        </div>
                    </div>
                    <div class="row" data-ng-repeat="trItem in item.trPackaging track by $index">
                        <div class="col s12">
                            <div class="row">
                                <div class="col s6"><a data-ng-click="showPreview($event, trItem.skuId,'SKU')">{{trItem.skuName}}</a></div>
                                <div class="col s6">Transferred Qty: {{trItem.transferredQuantity}} &nbsp; {{trItem.unitOfMeasure}}</div>
                            </div>
                            <div class="row" data-ng-repeat="pgd in trItem.packagingDetails track by $index">
                            <div data-ng-if="!addBillBookDetails[item.productId]">
                                <div class="col s2">{{pgd.packagingDefinitionData.packagingName}}</div>
                                <div class="col s2"><label>Units Packed:</label>
                                <div data-ng-if="!isManualBook[item.productId]">
                                    <input type="number" name="pkgQty[$index]" data-ng-disabled="hasDrillDown(trItem,item.semifinished)" data-ng-model="pgd.numberOfUnitsPacked"
                                        ng-change="updatePackagingQty(pgd,trItem,item)" required />
                                 </div>
                                  <div data-ng-if="isManualBook[item.productId]">
                                    <span name="pkgQty[$index]" data-ng-init="updatePackagingQty(pgd,trItem,item)">{{pgd.numberOfUnitsPacked}}</span>
                                 </div>
                                    <p ng-show="trForm.pkgQty[$index].$error.required" class="errorMessage">Please enter valid quantity.</p>
                                </div>
                                <div class="col s2"><label>Transferred Qty:</label> {{pgd.transferredQuantity}}</div>
                                <div class="col s2"><label>Unit Of Measure:</label> {{pgd.packagingDefinitionData.unitOfMeasure}}</div>
                                <div class="col s2"><button class="btn btn-small" data-ng-click="removePackaging(trItem,$index, item)">Remove</button></div>
                                <div class="col s2"><button class="btn btn-small" data-ng-if="item.productId==100217 && isWarehouse"  data-ng-init="manualDetailsRequired()"
                                data-ng-click="fillManualBBDetails(trItem,$index, item)" data-ng-class="{true:'red', false:'green'}[isBillBooksDetailFilled]">Add Details</button></div>
                               </div>
                               <div class="row data-division" data-ng-if="hasDrillDown(trItem)">
                                    <div class="row data-expiry">
                                        Expiry Info
                                    </div>
                                    <div class="row" style="font-weight: bold;">
                                        <div class="col s3">
                                            Quantity
                                        </div>
                                        <div class="col s3">
                                            Expiry Date
                                        </div>
                                    </div>
                                    <div class="row" data-ng-repeat="drilldown in trItem.drillDowns track by $index">
                                        <div class="col s3">
                                            {{drilldown.quantity}}
                                        </div>
                                        <div class="col s3 data-date">
                                            {{drilldown.expiryDate| date: 'yyyy-MM-dd HH:mm:ss'}}
                                        </div>
                                    </div>
                                </div>
                               <div data-ng-if="addBillBookDetails[item.productId]" data-ng-include="'views/ManualBillBookDetail.html'"></div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="row">
                <div class="col s12 form-element">
                    <label>Comment(optional):</label>
                    <textarea data-ng-model="comment"></textarea>
                </div>
                <div class="col s12 form-element">
                    <input type="button" data-ng-click="showPendingRequest=true" class="btn" value="Back" />
                    <input type="button" data-ng-if="trForm.$valid" data-ng-click="createTransferOrderObject()" class="btn right" value="Submit" acl-action="TRNSTA" />
                </div>
            </div>
        </form>
    </div>
</div>
