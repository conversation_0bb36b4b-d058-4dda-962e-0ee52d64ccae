<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Search Attribute Definition</h4>
                <div class="input-field">
                    <select ui-select2="selectOptions" ng-model="attributeId"
                            data-ng-change="getValues(attributeId)"
                            data-placeholder="Enter name of a attribute">
                        <option ng-repeat="attribute in attributes"
                                value="{{attribute.attributeId}}">{{attribute.attributeName}}
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <button class="btn right mappingBtn" data-ng-if="attributeId != null"
                data-ng-click="addToCount()">Add New Value</button>
        <div class="row" data-ng-if="attributeId != null" data-ng-show="selectedValues==null || selectedValues.length==0">
            <div class="flow-text mappingBtn">
                No values found
            </div>
        </div>

        <div class="row white z-depth-3">
            <form name="attrValueForm" data-ng-if="countValue!=0" class="scm-form" novalidate>
                <h4 class="flow-text">Attribute Value</h4>
                <div class="col-xs-12" data-ng-if="countValue != 0" data-ng-repeat="index in getCount(countValue) track by $index">
                    <div class="row">
                        <div class="form-element col s6">
                            <label class="black-text">Name</label>
                            <input name="attributeValue" type="text"
                                   data-ng-model = "addedValues[$index].attributeValue" class="black-text validate" required/>
                            <p ng-show="attrValueForm.attributeValue.$error.required" class="errorMessage">Name is required.</p>
                        </div>
                        <div class="form-element col s6">
                            <label class="black-text">Short Code</label>
                            <input name="attributeValueShortCode" type="text"
                                   data-ng-model = "addedValues[$index].attributeValueShortCode" class=" black-text validate" required/>
                            <p ng-show="attrValueForm.attributeValueShortCode.$error.required" class="errorMessage">Short Code is required.</p>
                        </div>
                        <button class="btn right actionBtn" data-ng-click="addAttrValues()" data-ng-if="attrValueForm.$valid">Submit</button>
                        <button class="btn right actionBtn" data-ng-click="cancel()">Cancel</button>
                    </div>
                </div>
            </form>
        </div>

        <div class="row white z-depth-3" data-ng-show="(selectedValues!=null && selectedValues.length!=0)">
            <div class="col s12" data-ng-show="selectedValues!=null && selectedValues.length!=0">
                <div class="row">
                    <div class="flow-text mappingBtn">
                        List of Values
                    </div>
                </div>
                <div class="row">
                    <table class="responsive-table centered">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>Attribute Name</th>
                            <th>Attribute Value</th>
                            <th>Short Code</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="mapping in selectedValues">
                            <td>
                                {{mapping.attributeValueId}}
                            </td>
                            <td>
                                {{getAttribute(mapping.attributeDefinitionId)}}
                            </td>
                            <td>
                                {{mapping.attributeValue}}
                            </td>
                            <td>
                                {{mapping.attributeValueShortCode}}
                            </td>
                            <td>
                                {{mapping.attributeValueStatus}}
                            </td>
                            <td>
                                <a class="btn" href="#edit" data-ng-click="editValue(mapping)" modal>Edit</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Modal Structure -->
<div id="edit" class="modal modal-small">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <p style="font-size: 18px;">Edit Value for: {{getAttribute(attributeId)}}</p>
                <form>
                    <div class="form-element">
                        <label class="black-text active" for="{{mappingToEdit.attributeValue}}">Name</label>
                        <input id="{{mappingToEdit.attributeValue}}" type="text"
                               data-ng-model="mappingToEdit.attributeValue"
                               class="validate black-text"/>
                    </div>

                    <div class="form-element">
                        <label class="black-text active"
                               for="{{mappingToEdit.attributeValueShortCode}}">Short Code</label>
                        <input id="{{mappingToEdit.attributeValueShortCode}}" type="text"
                               data-ng-model="mappingToEdit.attributeValueShortCode"
                               class="validate black-text"/>
                    </div>

                    <div class="form-element">
                        <label class="black-text active" for="{{mappingToEdit.attributeValueStatus}}">Status</label>
                        <select id="{{mappingToEdit.attributeValueStatus}}" data-ng-model = "mappingToEdit.attributeValueStatus">
                            <option value="IN_ACTIVE">IN_ACTIVE</option>
                            <option value="ACTIVE" data-ng-selected = "mappingToEdit.attributeValueStatus=='ACTIVE'">ACTIVE</option>
                        </select>
                    </div>
                </form>
                <div class="right-align">
                    <button class="modal-action modal-close waves-effect waves-green btn" data-ng-click="reset()">Cancel</button>
                    <button class="modal-action modal-close waves-effect waves-green btn" data-ng-click="submitValue(mappingToEdit)">Submit</button>
                </div>
            </div>
        </div>
    </div>
</div>