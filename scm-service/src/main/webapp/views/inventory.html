<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .btn.btn-small.inventory-btn{
        margin-right:5px;
        width: 105px;
    }
    .inventory-btn.active{
        color: #26a69a;
        background-color: white;
        border:1px solid #26a69a;
    }
    .remove-icon{
        font-size: 16px !important;
        vertical-align: middle;
    }
    .not_available {
        background: red;
    }
    .yellowColor {
        background: yellow;
    }

    .blinking {
        animation: blinker 1s linear infinite;
    }

    @keyframes blinker {
        50% {
            background-color: red;
            color: white;
        }
    }
</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="row">
        <div class="col s4">
            <h3>Update Inventory</h3>
            <label data-ng-if="!checkEmpty(lastSaved)">Last saved at: {{lastSaved | date:'dd/MM/yyyy @ h:mma'}}
                <button class="btn-small margin-top-5" data-ng-click="clearSavedHistory()">Clear Saved History</button>
            </label>
            <div data-ng-if="dayCloseBusinessDate!=null">Business Date : {{dayCloseBusinessDate | date:'dd/MM/yyyy'}} </div>
        </div>
        <div class="col s8 margin-top-10"> <!--data-ng-if="!inventoryUpdated">-->
            <div class="row">
                <div class="col s8" data-ng-if="isOpening">
                    <label class="black-text active">Upload Opening Stock</label>
                    <button class="btn btn-small inventory-btn active"
                            data-ng-model="frequencyValue"
                            data-ng-init="frequencyValue='OPENING'">{{frequencyValue}}</button>
                </div>
                <div class="col s8" data-ng-if="!isOpening && !inventoryUpdated">
                    <label class="black-text active">Select Stock Tack Type</label>
                    <div data-ng-if="unitData.closure !=null && unitData.closure==='PROCESSING'">
                        <button class="btn btn-small inventory-btn"
                        data-ng-class="{active: frequencyValue==frequency }"
                        data-ng-repeat="frequency in frequencies track by $index"
                        data-ng-if="frequency !== 'DAILY' && frequency !== 'WEEKLY' "
                        data-ng-click="selectFrequencyAtIndex($index)">
                    {{frequency}}
                </button>
                    </div>
                    <div data-ng-if="unitData.closure === null || unitData.closure !== 'PROCESSING'">
                        <button class="btn btn-small inventory-btn"
                        data-ng-class="{active: frequencyValue==frequency }"
                        data-ng-repeat="frequency in frequencies track by $index"
                        data-ng-click="selectFrequencyAtIndex($index)">
                    {{frequency}}
                </button>  
                </div>
                <div class="col s8" data-ng-if="!isOpening && inventoryUpdated">
                    <label class="black-text active">Select Stock Tack Type</label>
                    <button class="btn btn-small inventory-btn"
                            data-ng-class="{active: frequencyValue==frequency }"
                            data-ng-repeat="frequency in frequencies | filter : showFixedAssests"
                            data-ng-click="selectFrequencyAtIndex($index)">
                        {{frequency}}
                    </button>
                </div>
                <div class="col s4">
                    <button class="btn margin-top-20" data-ng-click="getProducts()">Get Products</button>
                </div>
                <div  data-ng-if="!closingInitiated" class="col s4">
                    <button class="btn margin-top-20" data-ng-click="checkClosingInitiated(unitId)">Refresh</button>
                </div>
                <div acl-action="rfrsdclose" data-ng-if="!closingInitiated && inventoryUpdated != true" class="col s4">
                    <button class="btn margin-top-20" data-ng-click="refreshDayclose()">Re-Initiate Kettle Day Close</button>
                </div>
            </div>
        </div>
    </div>
    <div class="warning-box" data-ng-if="!inventoryEventValid">WARNING: {{inventoryValidationErrorMsg}}</div>
    <div class="row margin0" data-ng-if="!inventoryUpdated && inventoryEventValid">
        <div class="col s12">
            <label>Note: Mouse over * to see details</label>
        </div>
    </div>
    <div id="inventory-markers" class="row" data-ng-if="!inventoryUpdated && inventoryEventValid">
        <div class="col s3">
            <div class="marker pointer"
                 data-ng-click="checkClosingInitiated(unitId)"
                 data-ng-class="{error: !closingInitiated, noerror: closingInitiated}" tooltipped data-tooltip="Changes to green if, Kettle day close is done. Click to check again">
                Kettle Day Close Done*
            </div>
        </div>
        <div class="col s3">
            <div class="marker" data-ng-class="{error: pendingGR.pendingGR>0, noerror: pendingGR.pendingGR==0}" tooltipped data-tooltip="Changes to green if, no pending receivings">
                <span class="chip inventory-chip" data-ng-if="pendingGR.pendingGR>0">{{pendingGR.pendingGR}}</span>
                &nbsp;
                <span>Pending Receivings*</span>
            </div>
        </div>
        <div class="col s3">
            <div class="marker" data-ng-class="{error: pendingWastage==0, noerror: pendingWastage>0}" tooltipped data-tooltip="Changes to green if, Wastage entered">
                <span class="chip inventory-chip" data-ng-if="pendingWastage>0">{{pendingWastage}}</span>
                &nbsp;
                <span>Wastage Entered*</span>
            </div>
        </div>
        <div class="col s3">
            <div class="marker" data-ng-class="{error: varianceBlocking, noerror: !varianceBlocking}" tooltipped data-tooltip="Changes to green if variance acknowledged">&nbsp;
                <span>Variance Acknowledged*</span>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="inventoryUpdated">
        <div class="col s12">
            <span data-ng-if="inventoryUpdated" class="flow-text">You have already updated inventory for today</span>
        </div>
    </div>


    <div class="row" data-ng-if="inventoryEventValid && (!inventoryUpdated || fixedAssetsOnly)">
        <div class="col s12" >
            <div class="row" data-ng-if="productList.length > 0">
                <div class="col s12 margin-bottom-5">
                    <div class="right day-close-text" data-ng-if="varianceBlocking">Acknowledge Variance to see Submit button</div>
                    <div class="right day-close-text margin-right-5" data-ng-if="!closingInitiated">Close day on Kettle to see Submit button</div>
                    <div class="right day-close-text margin-right-5" data-ng-if="closingInitiated">Close day for Fixed Assets to see Submit button</div>
                    <button class="btn btn-medium right margin-right-5" ng-click="clearMappings()">RESET/Re-Sync</button>
                    <button class="btn btn-small right margin-right-5" ng-print print-element-id="inventoryItemList">Print List</button>
                    <button class="btn btn-small right margin-right-5" data-ng-if="currentUserId == 125200 || currentUserId == 140199 || currentUserId == 141530" data-ng-click="fillZeroForAllProducts()">Fill ZERO</button>
                    <button class="btn btn-small right margin-right-5" data-ng-if="currentUserId == 125200 || currentUserId == 140199 || currentUserId == 141530" data-ng-click="autoFillInv()">Auto Fill</button>
                </div>
                <div class="col s12 margin-bottom-5" data-ng-repeat="productFillingType in productFillingTypes">
                    <div class="col s12" data-ng-if="currentProductFillingType == productFillingType">
                        <h4><u>{{productFillingType == 'OPTIONAL' ? 'OTHER' : productFillingType}} {{productFillingType != "ALL" ? "Products to fill" : "Products List"}}</u></h4>
                    </div>
                    <div class="row" data-ng-if="currentProductFillingType == productFillingType">
                        <input type="text" id="searchProduct" data-ng-model="searchProduct" style="color: red" placeholder="Enter Product Name to Search" data-ng-change="setSearchProduct(searchProduct)">
                    </div>
                    <br>
                    <table id="inventory-{{currentProductFillingType}}" class="bordered inventory-table" data-ng-if="currentProductFillingType == productFillingType
                                            && productsByFillingType[productFillingType].length > 0">
                        <thead>
                        <tr>
                            <th class="center-align">PRODUCT</th>
                            <th data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Opening Stock">OPENING*</th>
                            <th data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Transferred">TO*</th>
                            <th data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Wasted">WASTE*</th>
                            <th data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Received">GR*</th>
                            <th data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Consumption">CONSP*</th>
                            <th class="center-align" data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" tooltipped data-tooltip="Closing Stock">CLOSING*</th>
                            <th data-ng-if="!isOpening" class="center-align">VARIANCE</th>
                            <th data-ng-show="!isPreview" class="center-align">Enter the Stock in Packagings</th>
                            <th data-ng-show="!isPreview" class="center-align">Closing Re-Verification</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="roi in productsByFillingType[productFillingType] | filter : searchProduct | orderBy: getOrderBy() track by $index"
                            data-ng-class="{'blinking' :  roi.varianceWarningStatus == 'RE_VERIFICATION_REQUIRED' , 'yellowColor' : (roi.duplicateStockValue != null)}">
                            <td>
                                <div data-ng-class="{'not_available':roi.notAvl}" style="max-width: 240px;">
                                    <span style="font-weight:700;"><a data-ng-click="showPreview($event, roi.productId,'PRODUCT')">{{roi.productName}}</a></span>
                                    &nbsp;
                                    <span>[{{roi.unitOfMeasure}}]</span>
                                </div>
                            </td>
                            <td data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{(roi.opening!=null ? roi.opening : 0) | number: 2}}</td>
                            <td data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{roi.transferred!=null ? (roi.transferred | number: 2) : 0}}</td>
                            <td data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{roi.wasted!=null ? (roi.wasted | number: 2) : 0}}</td>
                            <td data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{roi.received!=null ? (roi.received | number: 2) : 0}}</td>
                            <td data-ng-if="!isOpening && (currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{roi.consumption!=null ? (roi.consumption | number: 2) : 0}}</td>
                            <td class="center-align" data-ng-if="(currentUserId == 140199 || currentUserId == 120063)">{{roi.stockValue | number: 2}}</td>
                            <td data-ng-if="!isOpening" class="center-align" ng-class="{error: (roi.variance | number: 2) != 0}">
                                <span data-ng-if="(currentUserId == 140199 || currentUserId == 120063)">{{roi.variance | number: 2}}</span>
                                <div class="row" data-ng-if="roi.stockValue > 0 && roi.dailyVariance < 0 && roi.categoryId === 4">
                                    <div class="col s6">
                                        <label for="inputCreated2">Select Expiry Date</label>
                                        <input input-date type="text" name="created" id="inputCreated2" ng-model="roi.selectedExpiry"
                                               container="" format="yyyy-mm-dd" select-years="1" min="{{currentDate}}" max="{{maxDatesOfProducts[roi.productId]}}"/>
                                    </div>
                                    <div class="col s6" data-ng-if="roi.selectedExpiry != null">
                                        <label for="expiryId">Expire Time :</label>
                                        <select id="expiryId" data-ng-model="roi.expiryTime" data-placeholder="Enter Expiry Date" data-ng-change="setExpiryDate(roi.selectedExpiry,roi.expiryTime, roi)">
                                            <option value="01">1 AM</option>
                                            <option value="13">1 PM</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row" data-ng-if="roi.stockValue > 0 && roi.dailyVariance < 0 && roi.selectedExpiryDate != null && roi.categoryId === 4">
                                    {{roi.selectedExpiryDate | date : 'yyyy-MM-dd HH:mm:ss'}}
                                </div>
                            </td>
                            <td data-ng-show="!isPreview">
                                <div class="row margin0">
                                    <div class="col s12" style="padding: 0px;">
                                        <div class="row margin0" data-ng-init="roi.hidden=false" style="padding: 0px;">
                                            <div class="col s10" data-ng-show="!roi.hidden">
                                                <div class="selected-detail" data-ng-repeat="selected in roi.packagingMappings">
                                                    <span class="packaging-value">{{selected.packagingName}}</span>
                                                    <input type="number" data-ng-model="selected.value" class="packaging-text" data-ng-disabled="currentProductFillingType == 'ALL'"
                                                           data-ng-change="changeStock(roi,selected.value,selected)"/>
                                                    <i class="remove-icon close material-icons" data-ng-if="currentProductFillingType != 'ALL'"
                                                       data-ng-click="removeMapping(roi.packagingMappings,selected,roi,false)">close</i>
                                                </div>
                                            </div>
                                            <div class="col s2">
                                                <span class="center-align" data-ng-if="roi.duplicateStockValue != null">{{roi.stockValue | number: 2}} [{{roi.unitOfMeasure}}]</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td data-ng-if="roi.varianceWarningStatus == 'RE_VERIFICATION_REQUIRED'">
                                <button class="btn btn-medium"  data-ng-click="openVarianceWarningModal(roi)">Re-Verify Stock</button>
                            </td>
                            <td data-ng-if="roi.varianceWarningStatus != 'RE_VERIFICATION_REQUIRED'" class="center-align" >
                                -
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;" data-ng-if="currentProductFillingType == productFillingType
                                            && productsByFillingType[productFillingType].length == 0 ">
                        No Products Available to Fill
                    </div>
                    <div data-ng-if="currentProductFillingType == productFillingType" class="margin-top-20 col s12">
                        <div class="col s2 left" data-ng-if="currentProductFillingType != 'MANDATORY'">
                            <button class="btn btn-small" ng-click="setProductFillingType(currentProductFillingType, 'previous')">Previous</button>
                        </div>
                        <div class="col s2 left" data-ng-if="currentProductFillingType != 'ALL'">
                            <button class="btn btn-small" ng-click="setProductFillingType(currentProductFillingType, 'next')">Next</button>
                        </div>
                        <div class="col s8 right" data-ng-if="currentProductFillingType == 'ALL'">
                            <button class="btn btn-small right" data-ng-click="submit()">SUBMIT</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- printable list for download and print -->
        <div style="padding: 20px 50px; width:100%; display: none;" id="inventoryItemList">
            <h3 class="center-align">Product List for Inventory</h3>
            <table style="border:1px solid #000;width:100%" align="center">
                <tr style="background-color: black;color:white;font-size: 12pt;font-weight: 700;">
                    <th style="border-radius: 0px;text-align:center;">Product Name</th>
                    <th style="border-radius: 0px;text-align:center;">Unit Of Measure</th>
                    <th style="border-radius: 0px;text-align:center;">Quantity</th>
                </tr>
                <tr data-ng-repeat="roi in productsByFillingType['ALL'] track by $index"
                    style="border-bottom: #000 1px solid; font-size:12pt; page-break-inside: avoid;">
                    <td style="text-align:center;border-right: 1px dashed #000;">{{roi.productName}}</td>
                    <td style="text-align:center;border-right: 1px dashed #000;">{{roi.unitOfMeasure}}</td>
                    <td style="text-align:center;border-right: 3px solid #000;"></td>
                </tr>
            </table>
        </div>
    </div>
</div>



<!--<div class="col s6 inventory" data-ng-repeat="(type,packagings) in roi.packagingMappings"
     data-ng-if="roi.packagingMappings[type].length > 0" style="margin-bottom:3px">
    <label class="packaging-detail type">{{type}}</label>




    <select class="packaging-detail packaging"
            data-ng-model="selectedPackagings[roi.productId][type].packaging"
            data-ng-change="changeStock(roi)"
            data-ng-init="selectedPackagings[roi.productId][type].packaging = roi.packagingMappings[type][0]"
            data-ng-options="packaging as packaging.packagingName for packaging in roi.packagingMappings[type]"/>
</div>-->



<script type="text/ng-template" id="inventoryPopUp.html">
  <div class="row">
     <h3> You have pending GR so settle it. </h3>
  </div>
  <div class="row">
 	<button class="btn red"  data-ng-click="goToPendingGrs()">GO</button>
   </div>
</script>

<script type="text/ng-template" id="varianceEditWarning.html">

    <div class="modal-header red-text" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Please Re-Verify Closing Quantity For Product : {{roi.productName}}</h3>
        <hr>
    </div>
    <div class="modal-body">
        <table id="varianceEditWarn" class="bordered inventory-table">
            <thead>
            <tr>
                <th class="center-align">PRODUCT</th>
                <th data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Opening Stock">OPENING*</th>
                <th data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Transferred">TO*</th>
                <th data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Wasted">WASTE*</th>
                <th data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Received">GR*</th>
                <th data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align" tooltipped data-tooltip="Consumption">CONSP*</th>
                <th class="center-align" data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" tooltipped data-tooltip="Closing Stock">CLOSING*</th>
                <th data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align">VARIANCE</th>
                <th class="center-align">Enter the Stock in Packagings</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>
                    <div style="max-width: 240px;">
                        <span style="font-weight:700;">{{roi.productName}}</span>
                        &nbsp;
                        <span>[{{roi.unitOfMeasure}}]</span>
                    </div>
                </td>
                <td data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{(roi.opening!=null ? roi.opening : 0) | number: 2}}</td>
                <td data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{roi.transferred!=null ? (roi.transferred | number: 2) : 0}}</td>
                <td data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{roi.wasted!=null ? (roi.wasted | number: 2) : 0}}</td>
                <td data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{roi.received!=null ? (roi.received | number: 2) : 0}}</td>
                <td data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align">{{roi.consumption!=null ? (roi.consumption | number: 2) : 0}}</td>
                <td class="center-align" data-ng-if="(currentUserId == 140199 || currentUserId == 120063)">{{roi.stockValue | number: 2}}</td>
                <td data-ng-if="(currentUserId == 140199 || currentUserId == 120063)" class="center-align" ng-class="{error: (roi.variance | number: 2) != 0}">{{roi.variance | number: 2}}</td>
                <td>
                    <div class="row margin0">
                        <div class="col s12" style="padding: 0px;">
                            <div class="row margin0" data-ng-init="roi.hidden=false" style="padding: 0px;">
                                <div class="col s10" data-ng-show="!roi.hidden">
                                    <div class="selected-detail" data-ng-repeat="selected in roi.packagingMappings">
                                        <span class="packaging-value">{{selected.packagingName}}</span>
                                        <input type="number" data-ng-model="selected.value" class="packaging-text"
                                               data-ng-change="changeStock(roi,selected.value,selected)"/>
                                        <i class="remove-icon close material-icons"
                                           data-ng-click="removeMapping(roi.packagingMappings,selected,roi,false)">close</i>
                                    </div>
                                </div>
                                <div class="col s2">
                                    <span class="center-align" data-ng-if="roi.duplicateStockValue != null">{{roi.stockValue | number: 2}} [{{roi.unitOfMeasure}}]</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <button class="btn btn-medium right " ng-click="submit()" title="send"
            style="margin-top: 10px">Re-Verify
    </button>

</script>