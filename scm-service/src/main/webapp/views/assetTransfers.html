<style>
    .modal-large{
        width:80% !important;
    }
    .grey{
        background-color: grey;
    }

    .collapsible-header.update{
        background-color: red;
        border: 1px solid #9E9E9E;
    }
</style>
<div class="row" data-ng-init="init()">
    <div class="row white z-depth-3" data-ng-if="assetTransfersList.length > 0">
        <div class="col s12">
            <h3>Asset Transfers</h3>
        </div>
        <div class="row">
            <div class="col s8">
                <input type="text" data-ng-model="searchProduct" placeholder="Enter Product Name or Sku Name">
            </div>
            <div class="col s4">
                <input type="checkbox" id="updateCheck" data-ng-model="onlyCanUpdate" data-ng-change="filterCanUpdateData(onlyCanUpdate)">
                <label for="updateCheck">Only Can Update</label>
            </div>
        </div>
        <ul class="col s12" data-collapsible="accordion" watch>
            <li data-ng-repeat="groupedAsset in duplicateGroupedTransferAssetList | filter : searchProduct | orderBy:['details.productName']" class="row">
                <div data-ng-class="{'collapsible-header update waves-effect waves-light lighten-5 s12' : groupedAsset.canUpdate > 0,
                'collapsible-header poNumber waves-effect waves-light lighten-5 s12' : groupedAsset.canUpdate == 0}">
                    <div class="row">
                        <div class="col s1" style="color: aliceblue;">
                            <span>SKU Id - </span><span>{{groupedAsset.skuId}}</span>
                        </div>
                        <div class="col s3" style="color: aliceblue;">
                            <span>Product Name - </span><span>{{groupedAsset.details.productName}}</span>
                        </div>
                        <div class="col s4" style="color: aliceblue;">
                            <span>SKU Name - </span>
                            <span>{{groupedAsset.details.skuName}} </span>
                        </div>
                        <div class="col s2" style="color: aliceblue;"> SKU Count - {{groupedAsset.details.total}} </div>
                        <div class="col s2" style="color: aliceblue;"> Can Update - {{groupedAsset.canUpdate}} </div>
                    </div>

                </div>
                <div class="collapsible-body">
                    <table class="bordered">
                        <thead>
                        <tr>
                            <th>To Id</th>
                            <th>To Item Id</th>
                            <th>GR Id</th>
                            <th>GR Item Id</th>
                            <th>from Unit</th>
                            <th>To Unit</th>
                            <th>Asset Id</th>
                            <th>Previous Asset Tag</th>
                            <th>Tag Value</th>
                            <th>Asset Correction Status</th>
                            <th>Correction</th>
                            <th>Bulk Correction</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr  data-ng-repeat="asset in groupedAsset.details.assetList | orderBy:['assetTag']"
                             data-ng-class="{'grey' : groupedAsset.details.child[asset.assetTag],
                                              'blue': lastUpdatedAssetTag != null && lastUpdatedAssetTag == asset.previousAssetTag }">
                            <td>{{asset.toId}}</td>
                            <th>{{asset.toItemId}}</th>
                            <th>{{asset.grId}}</th>
                            <th>{{asset.grItemId}}</th>
                            <th>{{asset.generatedUnitId}}</th>
                            <th>{{asset.generatedForUnitId}}</th>
                            <td>{{asset.assetId}}</td>
                            <td>{{asset.previousAssetTag}}</td>
                            <td>{{asset.assetTag}}</td>
                            <td>{{asset.assetStatus}}</td>
                            <td data-ng-if="!asset.hasChild && asset.inventoryCheck">
                                <span data-ng-if="asset.assetStatus == 'UNIQUE'">No Correction Required</span>
                                <span data-ng-if="asset.assetStatus == 'CORRECTED(ORIGINAL)'">Marked Original</span>
                                <span data-ng-if="asset.assetStatus == 'CORRECTED(UPDATED)'">Updated Asset</span>
                                <button data-ng-if="asset.assetStatus != 'UNIQUE' && asset.assetStatus != 'CORRECTED(ORIGINAL)' && asset.assetStatus != 'CORRECTED(UPDATED)'
                                 && !groupedAsset.details.originalCheck[asset.assetTag]" class="btn btn-medium" data-ng-click="updateAssetStatus(asset,true)"
                                        data-ng-disabled="groupedAsset.details.child[asset.assetTag]">Mark Original</button>
                                <button data-ng-if="asset.assetStatus != 'UNIQUE' && asset.assetStatus != 'CORRECTED(ORIGINAL)' && asset.assetStatus != 'CORRECTED(UPDATED)'
                                && groupedAsset.details.originalCheck[asset.assetTag]" class="btn btn-medium" data-ng-click="updateAssetStatus(asset,false)"
                                        data-ng-disabled="groupedAsset.details.child[asset.assetTag]">Update Asset</button>
                            </td>
                            <td data-ng-if="asset.hasChild || asset.inventoryCheck == false">
                                No Actions Available
                            </td>
                            <td data-ng-if="!asset.hasChild && asset.inventoryCheck">
                                <button data-ng-if="asset.assetStatus != 'UNIQUE' && asset.assetStatus != 'CORRECTED(ORIGINAL)' && asset.assetStatus != 'CORRECTED(UPDATED)'
                                && groupedAsset.details.originalCheck[asset.assetTag]" class="btn btn-medium"
                                        data-ng-disabled="groupedAsset.details.child[asset.assetTag]"
                                        data-ng-click="bulkAssetUpdate(groupedAsset.details.assetList,groupedAsset.details.originalCheck,asset,item)">Bulk Update</button>
                            </td>
                            <td data-ng-if="asset.hasChild || asset.inventoryCheck == false">
                                -
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </li>
        </ul>
    </div>
    <div class="row white z-depth-3" data-ng-if="assetTransfersList.length == 0" style="padding: 16px;">
        No Assets Transferred From Selected Unit
    </div>
</div>

<script type="text/ng-template" id="assetCorrections.html" class="modal-large">
    <div class="modal-content" style="overflow-x: auto; max-height: 350px;" data-ng-init="init()">
        <div class="row">
            <h4>Asset Correction for Asset Tag <b>: {{originalAsset.assetTag}} </b>  Product : <b>{{originalAsset.productName}}</b></h4>
            <hr>
        </div>
        <div class="row" style="width:98%;">
            <div class="col s6">
                <label>Select Asset Tag</label>
                <select data-ng-model="selectedAsset" data-ng-change="setSelectedAsset(selectedAsset)"
                        data-ng-options="assetEntry as assetEntry.tagValue for assetEntry in assetsList | orderBy:'tagValue'">
                </select>
            </div>
            <div class="col s3">
                <button style="margin-top: 20px;" class="btn btn-medium" data-ng-click="correctAsset()">Correct Asset</button>
            </div>
        </div>
        <div class="modal-footer right">
            <button class="waves-effect waves-green btn-flat" data-ng-click="close()">Cancel</button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="bulkAssetUpdate.html" class="modal-large">
    <div class="modal-content" style="overflow-x: auto;" data-ng-init="init()">
        <div class="row">
            <h4>Bulk Asset Correction for Asset Tag <b>: {{originalAsset.assetTag}} </b>  Product : <b>{{originalAsset.productName}}</b></h4>
            <hr>
        </div>
        <div class="row">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th>Check</th>
                    <th>To Item Id</th>
                    <th>Current Asset Tag</th>
                    <th>Select Tag</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="item in assetsFinalList track by $index">
                    <td>
                        <input id="itemCheck-{{$index}}" type="checkbox" data-ng-model='item.checked' data-ng-click="changeRow(item)">
                        <label for="itemCheck-{{$index}}"></label>
                    </td>
                    <td>{{item.toItemId}}</td>
                    <td>{{item.assetTag}}</td>
                    <td>
                        <select data-ng-disabled="!item.checked" data-ng-model="item.bulkSelectedAsset"
                                data-ng-options="assetEntry as assetEntry.tagValue for assetEntry in assetsList | orderBy:'tagValue'" data-ng-change="setSelectedAsset(item,item.bulkSelectedAsset)"></select>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="modal-footer">
            <button class="btn btn-medium pull-left red" data-ng-click="close()">Close</button>
            <button class="btn btn-medium pull-right green" data-ng-click="bulkGenerate()">Bulk Correct</button>
        </div>
    </div>
</script>