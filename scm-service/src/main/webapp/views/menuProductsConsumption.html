<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .custom-listing-li {
        margin-bottom: 20px;
    }
    .highlight-row {
        background-color: #e8f5e8;
        font-weight: bold;
    }
    .highlight-data {
        color: #2e7d32;
        font-weight: bold;
    }
    .orderingColumnHighlight {
        background-color: #fff3e0;
        font-weight: bold;
    }
    .collection-item.list-head {
        background-color: #f5f5f5;
        font-weight: bold;
    }
    .editable-input {
        width: 80px;
        text-align: center;
        border: 1px solid #ddd;
        padding: 2px;
    }
    .date-column {
        min-width: 100px;
        text-align: center;
    }
    .product-column {
        min-width: 200px;
    }
</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4 class="left">Menu Products Consumption Analysis</h4>
                <div class="right-align" style="margin-top: 20px;">
                    <input type="button" class="btn" data-ng-show="showProductsList"
                           value="BACK" data-ng-click="goBackToSelection()"/>
                </div>
            </div>
        </div>
    </div>

    <!-- Brand, Date, Days Selection -->
    <div class="row">
        <div class="col s12" data-ng-show="!showProductsList && !showScmProductsList && !showPackagingProductsList">
            <div class="form-element">
                    <div class="form-element">
                        <div class="row">
                            <div class="col s2 ">
                                <label>Select Brand</label>
                            </div>
                            <div class="col s4">
                                <select data-ng-model="selectedBrandDetails" class='form-control'
                                        data-ng-options="brand as brand.brandName for brand in brandDetails"
                                        data-ng-change="setBrand(selectedBrandDetails)">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col s2"><label>Fulfillment Date</label></div>
                        <div class="col s4"><input input-date type="text" name="fulfillmentDate"
                                   ng-model="fulfillmentDate"
                                   container="" format="yyyy-mm-dd" select-years="1"
                                   min="{{minRefOrderFulfillmentDate}}"
                                   max="{{maxRefOrderFulfillmentDate}}"
                                   data-ng-change="setDates(fulfillmentDate, noOfDays)"/>
                        </div>
                        <div class="col s6"><label class="highlight-data">{{fulfillmentDay}}</label></div>
                    </div>
                    <div class="form-element">
                        <div class="row">
                            <div class="col s2"><label>Ordering Days</label></div>
                            <div class="col s4"><input type="number" min="1" placeholder="No Of Days"
                                                       name="noOfDays"
                                                       id="noOfDays" ng-model="noOfDays"
                                                       ng-disabled="isApiInProgress"
                                                       data-ng-change="setDates(fulfillmentDate, noOfDays)"/></div>
                        </div>
                    </div>
                    <div class="row">
                        <input type="button" class="btn" value="GUIDE ME" data-ng-disabled="isFulfillmentInputInvalidOrLoading()" data-ng-click="getMenuProductsConsumption()"/>
                    </div>
            </div>
        </div>

            <!-- Days Selection Table -->
            <div class="form-element" data-ng-show="dataEntry.length > 0 && !showProductsList && !showScmProductsList && !showPackagingProductsList">
                <div class="row">
                    <label class="highlight-row" style="margin-top: 20px">Ordering Configuration for Days</label>
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr class="collection-item list-head">
                                <th>Day Type</th>
                                <th>Date</th>
                                <th>Ordering Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="entry in dataEntry"
                                data-ng-class="{'red':entry.dayType=='REMAINING_DAY','green':entry.dayType=='ORDERING_DAY'}">
                                <td>{{entry.dayType}}</td>
                                <td>{{entry.date}}</td>
                                <td>
                                    <select data-ng-model="entry.orderingPercentage"
                                            data-ng-disabled="entry.dayType=='REMAINING_DAY'"
                                            data-ng-options="orderPercentage as orderPercentage for orderPercentage in orderingPercentages">
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Consumption Table -->
    <div class="row" data-ng-show="showProductsList">
        <div class="col s12">
            <div class="form-element">
                <h5>Menu Products Consumption Data</h5>

                <!-- Dynamic Table -->
                <div style="overflow-x: auto;">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr class="collection-item list-head">
                                <th class="product-column">Product (Dimension)</th>
                                <th data-ng-repeat="date in remainingDays" class="date-column">
                                    {{date | date:'dd-MM'}} (Rem)
                                </th>
                                <th data-ng-repeat="date in orderingDays" class="date-column">
                                    {{date | date:'dd-MM'}} (Ord)
                                </th>
                                <th class="date-column">Final Quantity</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr data-ng-repeat="product in productConsumptionList track by $index">
                                <td class="product-column">
                                    <strong>{{product.productName}}</strong><br>
                                    <small>({{product.dimension}})</small>
                                </td>

                                <!-- Remaining Days Columns -->
                                <td data-ng-repeat="date in remainingDays" class="date-column">
                                    <input type="number" class="editable-input"
                                           data-ng-model="product.remainingDaysData[date]"
                                           data-ng-change="calculateFinalQuantity(product)"
                                           step="0.01" min="0"/>
                                </td>

                                <!-- Ordering Days Columns -->
                                <td data-ng-repeat="date in orderingDays" class="date-column orderingColumnHighlight">
                                    <input type="number" class="editable-input"
                                           data-ng-model="product.orderingDaysData[date]"
                                           data-ng-change="calculateFinalQuantity(product)"
                                           step="0.01" min="0"/>
                                </td>

                                <!-- Final Quantity -->
                                <td class="date-column highlight-data">
                                    <strong>{{product.finalQuantity | number:2}}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Action Buttons -->
                <div class="row" style="margin-top: 20px;">
                    <div class="col s12">
                        <input type="button" class="btn" value="SAVE DATA"
                               data-ng-click="saveConsumptionData()"/>
                        <input type="button" class="btn grey" value="RESET"
                               data-ng-click="resetData()" style="margin-left: 10px;"/>
                        <input type="button" class="btn blue" value="NEXT - GET SCM PRODUCTS (NEW)"
                               data-ng-click="getScmProductsConsumptionNew()" style="margin-left: 10px;"/>
                        <input type="button" class="btn blue lighten-2" value="NEXT - GET SCM PRODUCTS (OLD)"
                               data-ng-click="getScmProductsConsumption()" style="margin-left: 10px;"/>
                        <input type="button" class="btn purple" value="NEXT - GET SCM PRODUCTS (DATE-WISE)"
                               data-ng-click="getScmProductsFromMenuDateWise()" style="margin-left: 10px;"/>
<!--                        <input type="button" class="btn orange" value="SHOW DATA COMPARISON"-->
<!--                               data-ng-click="showDataComparison()" style="margin-left: 10px;"/>-->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Date-wise SCM Products Section -->
    <div class="row" data-ng-show="showScmProductsList">
        <div class="col s12">
            <div class="form-element">
                <h5>SCM Products Consumption Data</h5>

                <!-- Date-wise SCM Products Table -->
                <div style="overflow-x: auto;">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr class="collection-item list-head">
                                <th class="product-column">SCM Product Name</th>
                                <th>Dimension</th>
                                <th>UOM</th>
                                <th data-ng-repeat="date in remainingDays" class="date-column">
                                    {{date | date:'dd-MM'}} (Rem)
                                </th>
                                <th data-ng-repeat="date in orderingDays" class="date-column">
                                    {{date | date:'dd-MM'}} (Ord)
                                </th>
                                <th class="date-column">Final Quantity</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr data-ng-repeat="scmProduct in scmProductsDateWiseList track by $index">
                                <td class="product-column">
                                    <strong>{{scmProduct.productName}}</strong>
                                </td>
                                <td>{{scmProduct.dimension}}</td>
                                <td>{{scmProduct.uom}}</td>

                                <!-- Remaining Days Columns -->
                                <td data-ng-repeat="date in remainingDays" class="date-column">
                                    <input type="number" class="editable-input"
                                           data-ng-model="scmProduct.remainingDaysData[date]"
                                           data-ng-change="calculateScmFinalQuantityDateWise(scmProduct)"
                                           step="0.01" min="0"/>
                                </td>

                                <!-- Ordering Days Columns -->
                                <td data-ng-repeat="date in orderingDays" class="date-column orderingColumnHighlight">
                                    <input type="number" class="editable-input"
                                           data-ng-model="scmProduct.orderingDaysData[date]"
                                           data-ng-change="calculateScmFinalQuantityDateWise(scmProduct)"
                                           step="0.01" min="0"/>
                                </td>

                                <!-- Final Quantity -->
                                <td class="date-column highlight-data">
                                    <strong>{{scmProduct.finalQuantity | number:2}}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Date-wise SCM Action Buttons -->
                <div class="row" style="margin-top: 20px;">
                    <div class="col s12">
                        <input type="button" class="btn" value="BACK TO MENU PRODUCTS"
                               data-ng-click="goBackToMenuProductsDateWise()" style="margin-right: 10px;"/>
                        <input type="button" class="btn" value="SAVE DATE-WISE SCM DATA"
                               data-ng-click="saveScmProductsDateWise()"/>
                        <input type="button" class="btn grey" value="RESET DATE-WISE SCM DATA"
                               data-ng-click="resetScmProductsDateWise()" style="margin-left: 10px;"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Tracking Info Panel -->
<!--    <div class="row" data-ng-show="originalMenuProductsData.length > 0">-->
<!--        <div class="col s12">-->
<!--            <div class="form-element">-->
<!--                <h6>📊 Data Tracking Information</h6>-->
<!--                <div class="row">-->
<!--                    <div class="col s4">-->
<!--                        <div class="card-panel green lighten-4">-->
<!--                            <h6>Original Data</h6>-->
<!--                            <p>Backend Response: {{originalMenuProductsData.length}} products</p>-->
<!--                            <p>Status: Unchanged</p>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="col s4">-->
<!--                        <div class="card-panel orange lighten-4">-->
<!--                            <h6>Modified Data</h6>-->
<!--                            <p>User Changes: {{modifiedMenuProductsData.length}} products</p>-->
<!--                            <p>Status: User Edited</p>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="col s4">-->
<!--                        <div class="card-panel blue lighten-4">-->
<!--                            <h6>Current Data</h6>-->
<!--                            <p>Live State: {{productConsumptionList.length}} products</p>-->
<!--                            <p>Status: Real-time</p>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
</div>