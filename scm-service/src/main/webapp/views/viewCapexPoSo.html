<style>
    .custom-modal {
        width: 60% !important;
    }

    .popeye-modal-container .popeye-modal {
        width: 900px;
    }

    .advanceHeader {
        line-height: 40px;
        background: #ea9e13;
        font-weight: 700;
        border-radius: 3px;
    }

    @media screen and (max-width: 991px) {
        .searchingCard {
            display: flex;
            flex-direction: column;
            margin: 0px;
            width: 100% !important;
        }

        .searchingCard .col {
            margin-left: 0px !important;
            margin-right: 0px !important;
            width: 100% !important;
        }

        .searchingCard .btn {
            width: 100% !important;
        }

        .custom-modal {
            width: 90% !important;
        }

    }
</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3">
            <div class="col s12">
                <h4>View Capex Purchase/Service Orders</h4>
            </div>
            <div class="col s2">
                <label>Capex Request ID </label>
                <input type="text" id="enterCapexId" placeholder="Enter Capex Request Id" data-ng-model="capexRequestId"
                    style="margin-bottom: 20px" data-ng-change="changeCapexId(capexRequestId)">
            </div>


            <div class="col s2" style="margin-top: 40px" data-ng-if="capexRequestId != null">
                <button class="btn" data-ng-click="fetchBudgetSummary()">Find</button>
            </div>
        </div>

        <div class="col s12" data-ng-if="emptyMsg" style="padding:30px;color:gray;font-size: 20px;">
            <div class="row margin0 center">
                No Orders found for the selected criteria
            </div>
        </div>
        <div class="row" data-ng-if="capexRequestId != null">
            <h5 data-ng-if="capexSummaryShow">Summary : </h5>
            <div data-ng-if="!capexSummaryShow && capexSummaryLoading">
                <p style="margin: 10px; font-size: 20px;">Loading Summary...</p>
            </div>
            <div class="col s12" data-ng-if="poSoAmountDetails != null && (poSoAmountDetails.totalSoAmount != null || poSoAmountDetails.totalPoAmount != null)">
                <table class="table bordered striped" style="border: #ccc 1px solid; margin-bottom: 5px;">
                    <thead>
                        <tr>
                            <th>Total SO Amount</th>
                            <th>Total PO Amount</th>
                            <th>Total Amount</th>
                            <th>Total Uploaded Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>{{poSoAmountDetails.totalSoAmount ? poSoAmountDetails.totalSoAmount : 0}}</td>
                        <td>{{poSoAmountDetails.totalPoAmount ? poSoAmountDetails.totalPoAmount : 0}}</td>
                        <td>{{poSoAmountDetails.totalSoAmount + poSoAmountDetails.totalPoAmount}}</td>
                        <td>{{poSoAmountDetails.totalUploadedAmount}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="col s12" data-ng-if="capexSummaryShow">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                        <tr>
                            <th>Department</th>
                            <th>Initial Amt</th>
                            <th>Original Amt</th>
                            <th>Budget Amt</th>
                            <th>Remaining Amt</th>
                            <th>Running Amt</th>
                            <th>Receiving Amt</th>
                            <th>Paid Amt</th>
                            <th>SO #</th>
                            <th>Pending SO</th>
                            <th>PO #</th>
                            <th>Pending PO</th>
                            <th>Vendor #</th>
                            <th>SO's Department Wise</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat-start="items in summaryDepartmentList">
                            <td>{{items.departmentName}}</td>
                            <td>{{items.initialAmount.toFixed(2)| number}}</td>
                            <td>{{items.originalAmount.toFixed(2)| number}}</td>
                            <td>{{items.budgetAmount.toFixed(2)| number}}</td>
                            <td>{{items.remainingAmount.toFixed(2)| number}}</td>
                            <td>{{items.runningAmount.toFixed(2)| number}}</td>
                            <td>{{items.receivingAmount.toFixed(2)| number}}</td>
                            <td>{{items.paidAmount.toFixed(2)| number}}</td>
                            <td>{{items.totalSO}}</td>
                            <td>{{items.soPendingApproval}}</td>
                            <td>{{items.totalPO}}</td>
                            <td>{{items.poPendingApproval}}</td>
                            <td>{{items.vendorCount}}</td>
                            <td style="color: black;width: 45px; margin-top: 5px; margin-bottom: 5px; gap: 5px">
                                <div>

                                    <button class="btn btn-xs-small btn-spacing" data-ng-style="items.expanded ?
							        {'background-color' : isSoSelected(items.departmentName) ? '#85BF3F' : '#26a69a'} : {}"
                                            data-ng-click="showSOLevelDetail(items)" style="margin-bottom: 10px; background-color: 'orange'">{{isSoSelected(items.departmentName) ?
                                        "Hide SO's" : "Show SO's"}}</button>
                                    <button class="btn btn-xs-small btn-primary"
                                            data-ng-style="items.expanded ?
                                    {'background-color' : isPoSelected(items.departmentName) ? '#85BF3F' : '#26a69a'} : {}"
                                            data-ng-click="showPOLevelDetail(items)">{{isPoSelected(items.departmentName) ?
                                        "Hide PO's" : "Show PO's"}}</button>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <button class="btn-xs-small" data-ng-click="exportDataToSheetForSO(items)"
                                            title="DOWNLOAD SO ITEM LEVEL DATA" style="margin-bottom: 10px; background-color: 'orange'">
                                        <i class="fa fa-download"></i> SO
                                    </button>
                                    <button class="btn-xs-small" data-ng-click="exportDataToSheetForPO(items)"
                                            title="DOWNLOAD PO ITEM LEVEL DATA" style="background-color: 'orange'">
                                        <i class="fa fa-download"></i> PO
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr ng-if="isPoOrSoSelected(items.departmentName)" ng-repeat-end="">
                            <td colspan="12">
                                <div class="col s12 grid" id="grid" ui-grid="gridOptions" ui-grid-save-state
                                    ui-grid-resize-columns ui-grid-move-columns ui-grid-exporter></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>