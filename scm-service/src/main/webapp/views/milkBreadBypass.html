<head>
    <link rel="stylesheet" href="css/multiselect.css">
</head>
<style>
    .multiselect-parent .dropdown-menu {
        width: 570px;
    }
</style>
<div class="row white z-depth-3 " data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 teal lighten-2 center-align">
            <div class="col s12">
                <h4 class="left">Milk Bread Cache</h4>
            </div>
        </div>
    </div>

    <div class="col s12">
        <br>
        <div data-ng-if="pendingMilkBread == null || pendingMilkBread.receivingDone" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No Pending Milk Bread Ro's Found..!</div>
        <div data-ng-if="pendingMilkBread != null && !pendingMilkBread.receivingDone" class="col s12">
            <div class="col s4">
                <label>Ro Id's</label>
                <p>{{pendingMilkBread.roIds.join(",")}}</p>
            </div>
            <div class="col s4">
                <label>Max Allowed Time</label>
                <p>{{pendingMilkBread.maxLimitTime | date:'yyyy-MM-dd HH:mm:ss'}}</p>
            </div>
            <div class="col s4">
                <label>Action</label>
                <input type="button" class="btn btn-danger" data-ng-click="openRemoveMilkBreadModal()" value="Bypass">
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="isByPassed">
        <div class="col s6">
            <label>Select Reason*</label>
            <div class="col-lg-12" ng-dropdown-multiselect="" extra-settings="multiSelectSettingsMilkBreadReasons"
                 options="byPassReasons" selected-model="selectedMilkBreadReasons">
        </div>
    </div>
    </div>
    <div class="row col s12" data-ng-if="isByPassed">
        <div class="col s4">
            <label>Enter Comment*</label>
            <textarea data-ng-model="comment" data-ng-change="setEnteredComment(comment)"></textarea>
        </div>
        <div class="col s2" style="margin-top: 20px">
            <input type="button" class="btn btn-danger" data-ng-click="submitByPassRequest()" value="Submit">
        </div>
    </div>
</div>