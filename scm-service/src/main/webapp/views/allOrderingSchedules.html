<style>
    .grid {
        height: 70vh;
        width: 96vh;
        padding: 10px !important;
    }
    .green {
        background-color: green;
    }
    .red {
        background-color: red;
    }

</style>
<div class="row white z-depth-3" data-ng-init="init()">
    <div class="col s12">
        <h4>All Delivery Schedules</h4>
    </div>
    <div class="row margin0" data-ng-if="allSchedules.length > 0 && gridItems.length > 0">
        <div    class="col s12 grid"
                id="grid"
                ui-grid="gridOptions"
                ui-grid-save-state
                ui-grid-edit
                ui-grid-resize-columns
                ui-grid-move-columns
                ui-grid-auto-resize>
        </div>
    </div>
    <div class="col s12" data-ng-if="allSchedules.length == 0">
        <p class="red-text">No Ordering Schedules Found..!</p>
    </div>
</div>

<script type="text/ng-template" id="deliveryScheduleMonday.html">
    <div class="ui-grid-cell-contents" data-ng-class="{'green' : row.entity.mondayFunctional == 'ON','red' : row.entity.mondayFunctional == 'OFF'}" >
        <span data-ng-if="row.entity.mondayOrdering == 'Y'" class="fa fa-truck">
        </span> <span data-ng-if="row.entity.mondayOrdering == '-'">-</span>
        <span data-ng-if="row.entity.mondayDays != null">{{row.entity.mondayDays}}</span>
    </div>
</script>
<script type="text/ng-template" id="deliveryScheduleTuesday.html">
    <div class="ui-grid-cell-contents" data-ng-class="{'green' : row.entity.tuesdayFunctional == 'ON','red' : row.entity.tuesdayFunctional == 'OFF'}">
        <span data-ng-if="row.entity.tuesdayOrdering == 'Y'" class="fa fa-truck">
        </span> <span data-ng-if="row.entity.tuesdayOrdering == '-'">-</span>
        <span data-ng-if="row.entity.tuesdayDays != null">{{row.entity.tuesdayDays}}</span>
    </div>
</script>
<script type="text/ng-template" id="deliveryScheduleWednesday.html">
    <div class="ui-grid-cell-contents" data-ng-class="{'green' : row.entity.wednesdayFunctional == 'ON','red' : row.entity.wednesdayFunctional == 'OFF'}">
        <span data-ng-if="row.entity.wednesdayOrdering == 'Y'" class="fa fa-truck">
        </span> <span data-ng-if="row.entity.wednesdayOrdering == '-'">-</span>
        <span data-ng-if="row.entity.wednesdayDays != null">{{row.entity.wednesdayDays}}</span>
    </div>
</script><script type="text/ng-template" id="deliveryScheduleThursday.html">
    <div class="ui-grid-cell-contents" data-ng-class="{'green' : row.entity.thursdayFunctional == 'ON','red' : row.entity.thursdayFunctional == 'OFF'}">
        <span data-ng-if="row.entity.thursdayOrdering == 'Y'" class="fa fa-truck">
        </span> <span data-ng-if="row.entity.thursdayOrdering == '-'">-</span>
        <span data-ng-if="row.entity.thursdayDays != null">{{row.entity.thursdayDays}}</span>
    </div>
</script><script type="text/ng-template" id="deliveryScheduleFriday.html">
    <div class="ui-grid-cell-contents" data-ng-class="{'green' : row.entity.fridayFunctional == 'ON','red' : row.entity.fridayFunctional == 'OFF'}">
        <span data-ng-if="row.entity.fridayOrdering == 'Y'" class="fa fa-truck">
        </span> <span data-ng-if="row.entity.fridayOrdering == '-'">-</span>
        <span data-ng-if="row.entity.fridayDays != null">{{row.entity.fridayDays}}</span>
    </div>
</script><script type="text/ng-template" id="deliveryScheduleSaturday.html">
    <div class="ui-grid-cell-contents" data-ng-class="{'green' : row.entity.saturdayFunctional == 'ON','red' : row.entity.saturdayFunctional == 'OFF'}">
        <span data-ng-if="row.entity.saturdayOrdering == 'Y'" class="fa fa-truck">
        </span> <span data-ng-if="row.entity.saturdayOrdering == '-'">-</span>
        <span data-ng-if="row.entity.saturdayDays != null">{{row.entity.saturdayDays}}</span>
    </div>
</script><script type="text/ng-template" id="deliveryScheduleSunday.html">
    <div class="ui-grid-cell-contents" data-ng-class="{'green' : row.entity.sundayFunctional == 'ON','red' : row.entity.sundayFunctional == 'OFF'}">
        <span data-ng-if="row.entity.sundayOrdering == 'Y'" class="fa fa-truck">
        </span> <span data-ng-if="row.entity.sundayOrdering == '-'">-</span>
        <span data-ng-if="row.entity.sundayDays != null">{{row.entity.sundayDays}}</span>
    </div>
</script>