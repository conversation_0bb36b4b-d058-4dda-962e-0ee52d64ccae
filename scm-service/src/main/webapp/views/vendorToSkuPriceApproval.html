<link rel="stylesheet" href="css/vendorToSkuPriceApproval.css">
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>

<div class="sku-approval-container" data-ng-init="init()">
    <!-- Main Work Orders Grid View -->
    <div data-ng-if="!showContractItems">
        <div class="page-header">
            <h1 class="page-title"><i class="material-icons">assignment</i> Vendor to SKU Price Approval</h1>
        </div>

        <div class="sku-card" data-ng-if="workOrders.data.length > 0">
            <div class="sku-card-header">
                <i class="material-icons">work</i>
                <h2>Work Orders</h2>
            </div>
            <div class="sku-card-content">
                <div class="grid-container">
                    <div id="mappingsGrid" ui-grid="workOrders" ui-grid-edit ui-grid-row-edit ui-grid-cellNav
                        ui-grid-resize-columns ui-grid-move-columns class="grid"></div>
                </div>
            </div>
        </div>

        <!-- Templates for grid cells -->
        <script type="text/ng-template" id="showByPass.html">
            <div class="ui-grid-cell-contents">
                <span class="badge badge-orange" ng-if="row.entity.isByPassed == null">
                    No previous price updates
                </span>
                <span class="badge badge-active" ng-if="row.entity.isByPassed === 'Y'">
                    YES
                </span>
                <span class="badge badge-inactive" ng-if="row.entity.isByPassed === 'N'">
                    NO
                </span>
            </div>
        </script>

        <script type="text/ng-template" id="pendingDays.html">
            <div class="ui-grid-cell-contents">
                <span class="badge" data-ng-if="grid.appScope.dateDifference(row.entity.createdAt) == 0">
                    Requested Today
                </span>
                <span class="badge badge-inactive" data-ng-if="grid.appScope.dateDifference(row.entity.createdAt) >= 3">
                    {{grid.appScope.dateDifference(row.entity.createdAt)}} days ago
                </span>
                <span class="badge" data-ng-if="grid.appScope.dateDifference(row.entity.createdAt) <= 2 && grid.appScope.dateDifference(row.entity.createdAt) > 0">
                    {{grid.appScope.dateDifference(row.entity.createdAt)}} days ago
                </span>
            </div>
        </script>
    </div>

    <!-- Contract Items Detail View -->
    <div data-ng-if="showContractItems">
        <div class="page-header">
            <h1 class="page-title"><i class="material-icons">assignment</i> Vendor to SKU Approval</h1>
            <button class="btn btn-danger" data-ng-click="init()">
                <i class="material-icons">arrow_back</i> Back
            </button>
        </div>

        <!-- Work Order Info Card -->
        <div class="sku-card">
            <div class="sku-card-header">
                <i class="material-icons">description</i>
                <h2>Work Order Details</h2>
            </div>
            <div class="sku-card-content">
                <div class="info-panel">
                    <div class="info-item">
                        <div class="info-label"><i class="material-icons tiny">store</i> Vendor:</div>
                        <div class="info-value">{{currentWorkOrderData.vendorName}}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label"><i class="material-icons tiny">description</i> Contract ID:</div>
                        <div class="info-value">{{currentWorkOrderData.contractId}}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label"><i class="material-icons tiny">work</i> Work Order ID:</div>
                        <div class="info-value">{{currentWorkOrderData.workOrderId}}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label"><i class="material-icons tiny">category</i> Work Order Type:</div>
                        <div class="info-value">{{currentWorkOrderData.workOrderType}}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label"><i class="material-icons tiny">info</i> Work Order Status:</div>
                        <div class="info-value">{{currentWorkOrderData.workOrderStatus}}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label"><i class="material-icons tiny">date_range</i> Start Date:</div>
                        <div class="info-value">{{currentWorkOrderData.startDate | date : 'yyyy-MM-dd'}}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label"><i class="material-icons tiny">event</i> End Date:</div>
                        <div class="info-value">{{currentWorkOrderData.endDate | date : 'yyyy-MM-dd'}}</div>
                    </div>
                </div>

                <!-- Document Download Section -->
                <div class="btn-container" data-ng-if="availableDocumentId !== null">
                    <button class="btn btn-secondary" data-ng-click="downloadDocument()">
                        <i class="material-icons">file_download</i> Download Document
                    </button>
                    <button class="btn btn-primary" data-ng-click="showApprovedSkus()">
                        <i class="material-icons">check_circle</i> Show Approved SKUs
                    </button>
                </div>
                <div class="btn-container" data-ng-if="availableDocumentId === null">
                    <button class="btn btn-primary" data-ng-click="showApprovedSkus()">
                        <i class="material-icons">check_circle</i> Show Approved SKUs
                    </button>
                </div>
            </div>
        </div>

        <!-- No Pending Approvals Message -->
        <div class="sku-card" data-ng-if="showContrcatButtons">
            <div class="sku-card-content">
                <div class="alert alert-success">
                    <i class="material-icons">info</i> No Pending Approvals, please make contract or ByPass Work order
                </div>
            </div>
        </div>

        <!-- SKU Price Grid -->
        <div class="sku-card" data-ng-if="showGrid()">
            <div class="sku-card-header">
                <i class="material-icons">list</i>
                <h2>SKU Price Approval</h2>
            </div>
            <div class="sku-card-content">
                <div class="grid-container">
                    <div id="mappingsGrid" ui-grid="priceGridOptions" ui-grid-edit ui-grid-row-edit ui-grid-cellNav
                        ui-grid-resize-columns ui-grid-move-columns class="grid"></div>
                </div>

                <!-- Bulk Action Buttons -->
                <div class="btn-container">
                    <button class="btn btn-success" data-ng-if="showGrid()" data-ng-click="approveAll()">
                        <i class="material-icons">done_all</i> Approve All
                    </button>
                    <button class="btn btn-gray" data-ng-if="showGrid()" data-ng-click="selectedReasonForAll=null; rejectionReasonForAll=null; clearAll()">
                        <i class="material-icons">refresh</i> Reset
                    </button>
                </div>

                <!-- Rejection Reason Form -->
                <div class="form-group" data-ng-show="showGrid() && showRejectionDiv()">
                    <div class="sku-card rejection-details-container">
                        <div class="sku-card-header" style="gap: 100px;">
                            <h3><i class="material-icons">report_problem</i>Rejection Details</h3>
                            <button class="btn btn-danger" data-ng-if="showGrid()" data-ng-click="rejectAll()">
                                <i class="material-icons">block</i> Reject All
                            </button>
                        </div>
                        <div class="sku-card-content">
                            <div class="form-group">
                                <label class="form-label"><i class="material-icons tiny">list</i> Select Rejection Reason</label>
                                <select id="rejectionReasonSelect" class="form-control" data-ng-model="selectedReasonForAll"
                                    data-ng-change="setRejection(selectedReasonForAll, true)"
                                    data-ng-options="reason for reason in rejectionReasonOptions2">
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label"><i class="material-icons tiny">comment</i> Rejection Comment</label>
                                <input type="text" id="rejectionCommentInput" class="form-control" data-ng-model="rejectionReasonForAll"
                                    data-ng-change="setRejection(rejectionReasonForAll, false)"
                                    placeholder="Enter rejection comment for all"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Signature and Contract Buttons -->
        <div class="sku-card" data-ng-show="showContractButtons()">
            <div class="sku-card-header">
                <i class="material-icons">verified_user</i>
                <h2>Contract Approval</h2>
            </div>
            <div class="sku-card-content">
                <div id="signatureContainer" class="signature-container" data-ng-if="currentWorkOrderData.workOrderType == 'DEFAULT'">
                    <h3><i class="material-icons tiny">create</i> Digital Signature</h3>
                    <p>Please sign below to approve the contract</p>
                    <canvas id="signature" class="signature-pad" width="400" height="200"></canvas>
                    <div class="signature-buttons">
                        <button class="btn btn-primary" id="export" data-ng-click="saveSignature()">
                            <i class="material-icons">save</i> Sign
                        </button>
                        <button class="btn btn-gray" id="reset" data-ng-click="resetSignature()">
                            <i class="material-icons">clear</i> Clear
                        </button>
                    </div>
                </div>

                <div class="btn-container">
                    <button class="btn btn-danger" data-ng-click="changeByPass('Y')" data-ng-if="currentWorkOrderData.isByPassed == null || currentWorkOrderData.isByPassed == 'Y'">
                        <i class="material-icons">block</i> ByPass {{currentWorkOrderData.workOrderType == 'DEFAULT' ? 'Contract' : 'Work Order'}}
                    </button>
                    <button class="btn btn-success" data-ng-click="changeByPass('N')" data-ng-if="currentWorkOrderData.isByPassed == null || currentWorkOrderData.isByPassed == 'N'">
                        <i class="material-icons">check_circle</i> Make {{currentWorkOrderData.workOrderType == 'DEFAULT' ? 'Contract' : 'Work Order'}}
                    </button>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="btn-container" data-ng-if="isEntryFoundForApproval() && anySkuRejected.length > 0">
            <button class="btn btn-primary" data-ng-click="submit()">
                <i class="material-icons">send</i> Submit
            </button>
        </div>

        <!-- Templates for grid cells -->
        <script type="text/ng-template" id="statusBatch.html">
            <div class="ui-grid-cell-contents">
                <span class="badge badge-active" ng-if="row.entity.status === 'ACTIVE'">
                    ACTIVE
                </span>
                <span class="badge badge-inactive" ng-if="row.entity.status === 'IN_ACTIVE'">
                    IN-ACTIVE
                </span>
            </div>
        </script>

        <script type="text/ng-template" id="statusChangeButton.html">
            <div class="ui-grid-cell-contents">
                <button class="btn btn-primary btn-xs-small"
                        ng-click="grid.appScope.approvePrice(row.entity)"
                        data-ng-hide="row.entity.rejected"
                        data-ng-if="!row.entity.approved">
                    <span>Approve</span>
                </button>

                <button class="btn btn-warning btn-xs-small"
                        ng-click="grid.appScope.cancelApprove(row.entity)"
                        data-ng-if="row.entity.approved">
                    <span>Cancel Approve</span>
                </button>

                <button class="btn btn-danger btn-xs-small"
                        ng-click="grid.appScope.rejectPrice(row.entity)"
                        data-ng-hide="row.entity.approved"
                        data-ng-if="!row.entity.rejected">
                    <span>Reject</span>
                </button>

                <button class="btn btn-warning btn-xs-small"
                        ng-click="grid.appScope.cancelReject(row.entity)"
                        data-ng-if="row.entity.rejected">
                    <span>Cancel Reject</span>
                </button>
            </div>
        </script>

        <!-- Modal Templates -->
        <script type="text/ng-template" id="previousPriceHistoryOfSKU.html">
            <div data-ng-init="initPreviousPriceHistory()" class="modal-large">
                <div class="modal-body">
                    <div class="row">
                        <h5><b>Previous SKU Purchases of {{requiredInfo.skuName}} in {{requiredInfo.deliveryLocation}}</b></h5>
                    </div>
                    <div class="row" style="width:98%;">
                        <table class="bordered striped standardView">
                            <thead>
                            <tr>
                                <th class="center-align">Vendor</th>
                                <th class="center-align">Generated At</th>
                                <th class="center-align">Quantity Ordered</th>
                                <th class="center-align">Unit Price</th>
                                <th class="center-align">Total Cost</th>
                                <th class="center-align">Packaging Ordered</th>
                                <th class="center-align">Price / {{requiredInfo.packagingName}}</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="previousPurchases in previousPricesGraphData">
                                <td class="center-align">{{previousPurchases.vendorName}}</td>
                                <td class="center-align">{{previousPurchases.generatedAt | date:'dd/MM/yyyy'}}</td>
                                <td class="center-align">{{previousPurchases.totalQuantityOrdered}}</td>
                                <td class="center-align">{{previousPurchases.unitPrice}}</td>
                                <td class="center-align">{{previousPurchases.totalCost}}</td>
                                <td class="center-align">{{previousPurchases.skuPackagingName}}</td>
                                <td class="center-align">{{(previousPurchases.currentPriceForTheRequiredPackaging).toFixed(2)}}</td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="bordered striped TableMobileView">
                        </div>
                    </div>
                </div>
                <div class="modal-footer right">
                    <button class="red waves-effect waves-green btn-flat" data-ng-click="close()">Close</button>
                </div>
            </div>
        </script>

        <script type="text/ng-template" id="approvedSKUS.html">
            <div data-ng-init="initApprovedSkus()" class="modal-large">
                <div class="modal-body">
                    <div class="row">
                        <h5><b>APPROVED SKU'S</b></h5>
                    </div>
                    <div class="row"
                        id="gridView">
                        <div class="col s12">
                            <div
                                id="mappingsGrid"
                                ui-grid="approvedSkusGrid"
                                ui-grid-edit
                                ui-grid-row-edit
                                ui-grid-cellNav
                                ui-grid-resize-columns
                                ui-grid-move-columns
                                class="grid col s12">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer right">
                    <button class="red waves-effect waves-green btn-flat" data-ng-click="close()">Close</button>
                </div>
            </div>
        </script>
    </div>
</div>
