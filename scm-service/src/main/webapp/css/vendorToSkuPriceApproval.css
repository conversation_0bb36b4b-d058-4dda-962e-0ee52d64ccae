/* Vendor to SKU Price Approval Styles */
:root {
  --primary-color: #26a69a;
  --primary-light: #e0f2f1;
  --primary-dark: #00796b;
  --secondary-color: #ff9800;
  --danger-color: #f44336;
  --success-color: #4CAF50;
  --warning-color: #FFC107;
  --info-color: #2196F3;
  --text-color: #333333;
  --text-light: #757575;
  --background-light: #f5f5f5;
  --card-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --border-radius: 3px;
  --transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

/* Main Container */
.sku-approval-container {
  font-family: 'Roboto', sans-serif;
  color: var(--text-color);
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
  background-color: #f9f9f9;
}

/* <PERSON> Header */
.page-header {
  background-color: white;
  padding: 12px 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-left: 4px solid var(--primary-color);
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--primary-dark);
  margin: 0;
  letter-spacing: 0.5px;
}

/* Card Styles */
.sku-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: 16px;
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid #eee;
}

.sku-card:hover {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.sku-card-header {
  background-color: white;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  color: var(--primary-dark);
  font-weight: 500;
  display: flex;
  align-items: center;
  border-left: 4px solid var(--primary-color);
}

.sku-card-header h2,
.sku-card-header h3 {
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

.sku-card-header i {
  margin-right: 8px;
  color: var(--primary-color);
}

.sku-card-content {
  padding: 16px;
}

/* Info Panel */
.info-panel {
  background-color: #f9f9f9;
  border-radius: var(--border-radius);
  padding: 12px;
  margin-bottom: 16px;
  border: 1px solid #eee;
}

.info-item {
  margin-bottom: 6px;
  display: flex;
  border-bottom: 1px dotted #eee;
  padding-bottom: 6px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  font-weight: 500;
  width: 140px;
  color: var(--text-light);
  font-size: 13px;
}

.info-value {
  font-weight: 400;
  color: var(--text-color);
  font-size: 13px;
}

/* Grid Styles */
.grid-container {
  margin-bottom: 16px;
  border: 1px solid #eee;
  border-radius: var(--border-radius);
  overflow: hidden;
}

/* Button Styles */
.btn-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 0;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  font-size: 13px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  text-transform: none;
  letter-spacing: 0.5px;
  height: 36px;
  box-shadow: none;
}

.btn:hover {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

.btn i {
  font-size: 16px;
  margin-right: 6px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: #333;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-gray {
  background-color: #6c757d;
  color: white;
}

.btn-xs-small {
  padding: 4px 8px;
  font-size: 12px;
  height: auto;
}

/* Badge Styles */
.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  min-width: 70px;
  letter-spacing: 0.5px;
}

.badge-active {
  background-color: var(--success-color);
  color: white;
}

.badge-inactive {
  background-color: var(--danger-color);
  color: white;
}

.badge-orange {
  background-color: var(--warning-color);
  color: #333;
}

/* Alert Styles */
.alert {
  padding: 12px 16px;
  border-radius: var(--border-radius);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  font-size: 13px;
}

.alert i {
  margin-right: 8px;
  font-size: 18px;
}

.alert-success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid var(--success-color);
}

/* Form Elements */
.form-group {
  margin-bottom: 12px;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 6px;
  color: var(--text-color);
  font-size: 13px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  font-size: 13px;
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(38, 166, 154, 0.2);
}

/* Rejection Details */
.rejection-details-container {
  max-width: 400px;
  margin: 0 auto;
}

/* Validation Feedback */
@keyframes redBorderBlink {
  0% { border-color: #e0e0e0; }
  50% { border-color: var(--danger-color); }
  100% { border-color: #e0e0e0; }
}

.validation-error {
  animation: redBorderBlink 1s ease-in-out 3;
  border: 2px solid var(--danger-color) !important;
}

.signature-error {
  animation: redBorderBlink 1s ease-in-out 3;
  border: 2px solid var(--danger-color) !important;
}

/* Signature Pad */
.signature-container {
  margin: 16px 0;
  border: 1px solid #eee;
  border-radius: var(--border-radius);
  padding: 12px;
  background-color: #f9f9f9;
}

.signature-container h3 {
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--primary-dark);
}

.signature-container p {
  font-size: 13px;
  margin-bottom: 12px;
  color: var(--text-light);
}

.signature-pad {
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  background-color: #fff;
}

.signature-buttons {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

/* Modal Styles */
.modal-large {
  width: 100% !important;
}

.popeye-modal {
  width: 80% !important;
}
