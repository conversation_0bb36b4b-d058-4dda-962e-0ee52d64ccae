/* Vendor to SKU Price Mapping Styles */
:root {
  --primary-color: #26a69a;
  --primary-light: #e0f2f1;
  --primary-dark: #00796b;
  --secondary-color: #ff9800;
  --danger-color: #f44336;
  --success-color: #4CAF50;
  --warning-color: #FFC107;
  --info-color: #2196F3;
  --text-color: #333333;
  --text-light: #757575;
  --background-light: #f5f5f5;
  --card-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --border-radius: 3px;
  --transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

/* Main Container */
.sku-mapping-container {
  font-family: 'Roboto', sans-serif;
  color: var(--text-color);
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
  background-color: #f9f9f9;
}

/* <PERSON> Header */
.page-header {
  background-color: white;
  padding: 10px 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-left: 4px solid var(--primary-color);
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-dark);
  margin: 0;
  letter-spacing: 0.5px;
}

/* Card Styles */
.sku-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: 16px;
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid #eee;
}

.sku-card:hover {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.sku-card-header {
  background-color: white;
  padding: 10px 16px;
  border-bottom: 1px solid #eee;
  color: var(--primary-dark);
  font-weight: 500;
  display: flex;
  align-items: center;
  border-left: 4px solid var(--primary-color);
}

.sku-card-header h2,
.sku-card-header h3 {
  font-size: 14px;
  margin: 0;
  font-weight: 500;
}

.sku-card-header i {
  margin-right: 8px;
  color: var(--primary-color);
  font-size: 16px;
}

.sku-card-content {
  padding: 16px;
}

/* Form Elements */
.form-group {
  margin-bottom: 10px;
  position: relative;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-color);
  font-size: 11px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.form-label i {
  margin-right: 4px;
  color: var(--primary-color);
  font-size: 14px;
}

.form-control {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.2s ease;
  height: 32px;
  background-color: white;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.03);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(38, 166, 154, 0.15);
}

.form-control:hover:not(:focus) {
  border-color: #bdbdbd;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -6px;
  align-items: flex-end;
}

.form-col {
  padding: 0 6px;
  flex: 1;
  min-width: 0;
  margin-bottom: 8px;
  position: relative;
}

/* Custom Select Styling */
select.form-control {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 30px;
}

/* Multiselect Dropdown Styling */
.multiselect-parent {
  width: 100%;
}

.multiselect-parent .dropdown-toggle {
  width: 100%;
  text-align: left;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 12px;
  min-height: 32px;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.03);
  transition: all 0.2s ease;
  color: var(--text-color);
}

.multiselect-parent .dropdown-toggle:hover {
  border-color: #bdbdbd;
}

.multiselect-parent .dropdown-toggle:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(38, 166, 154, 0.15);
}

.multiselect-parent .dropdown-toggle .caret {
  position: absolute;
  right: 10px;
  top: 45%;
}

.multiselect-parent .dropdown-menu {
  width: 100%;
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  max-height: 300px;
  overflow-y: auto;
}

.multiselect-parent .dropdown-menu .input-group {
  margin-bottom: 5px;
}

.multiselect-parent .dropdown-menu .input-group input {
  border-radius: 3px;
  font-size: 12px;
  padding: 5px 8px;
  border: 1px solid #e0e0e0;
}

.multiselect-parent .dropdown-menu .input-group input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(38, 166, 154, 0.15);
}

.multiselect-parent .dropdown-menu > li > a {
  padding: 5px 8px;
  font-size: 12px;
  color: var(--text-color);
  border-radius: 3px;
}

.multiselect-parent .dropdown-menu > li > a:hover {
  background-color: var(--primary-light);
}

.multiselect-parent .dropdown-menu > li.active > a {
  background-color: var(--primary-color);
  color: white;
}

.multiselect-parent .dropdown-menu .checkbox {
  margin: 0;
  padding: 3px 0;
}

.multiselect-parent .dropdown-menu .checkbox label {
  font-size: 12px;
  font-weight: normal;
  padding: 3px 5px;
  display: flex;
  align-items: center;
}

.multiselect-parent .dropdown-menu .checkbox input[type="checkbox"] {
  margin-right: 5px;
  margin-top: 0;
}

.multiselect-parent .selected-item {
  margin: 2px 5px 2px 0;
  padding: 2px 5px;
  background-color: var(--primary-light);
  border-radius: 3px;
  display: inline-block;
  font-size: 11px;
  color: var(--primary-dark);
}

.multiselect-parent .selected-item .fa {
  margin-left: 3px;
  cursor: pointer;
}

/* Multiselect when items are selected */
.multiselect-parent .dropdown-toggle .selected-item {
  margin: 2px 5px 2px 0;
  padding: 2px 5px;
  background-color: var(--primary-light);
  border-radius: 3px;
  display: inline-block;
  font-size: 11px;
  color: var(--primary-dark);
}

/* Placeholder styling */
.multiselect-parent .dropdown-toggle .multiselect-placeholder {
  color: #999;
}

/* Input Focus Animation */
.form-control:focus + .focus-border {
  transform: scaleX(1);
  transition: transform 0.3s ease;
}

.focus-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

/* Selection Panel */
.selection-panel {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  margin-bottom: 12px;
  overflow: hidden;
  border: none;
  transition: all 0.3s ease;
}

.selection-panel:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.selection-panel-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: 8px 15px;
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.selection-panel-header:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 100%);
}

.selection-panel-header h2 {
  font-size: 13px;
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.selection-panel-header i {
  margin-right: 8px;
  font-size: 16px;
  color: rgba(255,255,255,0.9);
}

.selection-panel-content {
  padding: 15px;
  background-color: #fcfcfc;
  border-top: 1px solid rgba(0,0,0,0.05);
}

.selection-panel-section {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.selection-panel-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.selection-panel-section-title {
  font-size: 12px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
}

.selection-panel-section-title i {
  margin-right: 5px;
  font-size: 14px;
  color: var(--primary-color);
}

/* Button Styles */
.btn-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 12px 0;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
  text-transform: none;
  letter-spacing: 0.5px;
  height: 32px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: none;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.btn:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.btn:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
  transform: translateY(-1px);
}

.btn:active:after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

.btn i {
  font-size: 14px;
  margin-right: 6px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
}

.btn-danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
}

.btn-warning {
  background: linear-gradient(135deg, #FFC107 0%, #FFA000 100%);
  color: #333;
}

.btn-warning:hover {
  background: linear-gradient(135deg, #FFA000 0%, #FFC107 100%);
}

.btn-success {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: white;
}

.btn-success:hover {
  background: linear-gradient(135deg, #388E3C 0%, #4CAF50 100%);
}

.btn-secondary {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  color: white;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #F57C00 0%, #FF9800 100%);
}

.btn-gray {
  background: linear-gradient(135deg, #78909C 0%, #546E7A 100%);
  color: white;
}

.btn-gray:hover {
  background: linear-gradient(135deg, #546E7A 0%, #78909C 100%);
}

.btn-xs-small {
  padding: 0 8px;
  font-size: 11px;
  height: 24px;
}

/* Badge Styles */
.badge {
  display: inline-block;
  padding: 3px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
  letter-spacing: 0.5px;
}

.badge-active {
  background-color: var(--success-color);
  color: white;
}

.badge-inactive {
  background-color: var(--danger-color);
  color: white;
}

.badge-orange {
  background-color: var(--warning-color);
  color: #333;
}

/* Grid Styles */
.grid-container {
  margin-bottom: 12px;
  border: 1px solid #eee;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.grid-panel {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  margin-bottom: 12px;
  overflow: hidden;
  border: none;
  transition: all 0.3s ease;
}

.grid-panel:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.grid-panel-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: 8px 15px;
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.grid-panel-header:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 100%);
}

.grid-panel-header h2 {
  font-size: 13px;
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.grid-panel-header i {
  margin-right: 8px;
  font-size: 16px;
  color: rgba(255,255,255,0.9);
}

.grid-panel-content {
  padding: 0;
}

/* UI Grid Styling */
.ui-grid {
  border: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.ui-grid-header {
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.ui-grid-header-cell {
  background: #f5f5f5;
  border-right: 1px solid rgba(0,0,0,0.05);
  font-weight: 500;
  color: #333;
  font-size: 12px;
}

.ui-grid-row:nth-child(even) .ui-grid-cell {
  background-color: #f9f9f9;
}

.ui-grid-row:hover .ui-grid-cell {
  background-color: #f0f7f6;
}

.ui-grid-cell {
  border-right: 1px solid rgba(0,0,0,0.05);
  border-bottom: 1px solid rgba(0,0,0,0.05);
  font-size: 12px;
}

.ui-grid-cell-focus {
  background-color: rgba(38, 166, 154, 0.1) !important;
}

.ui-grid-menu-button {
  border: none;
  background: transparent;
  color: #333;
}

.ui-grid-menu-button:hover {
  background-color: rgba(0,0,0,0.05);
}

.ui-grid-pager-panel {
  background-color: #f5f5f5;
  border-top: 1px solid rgba(0,0,0,0.05);
  padding: 5px 0;
}

.ui-grid-pager-control button {
  background: white;
  border: 1px solid #e0e0e0;
  color: #333;
  margin: 0 2px;
}

.ui-grid-pager-control button:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
}

.ui-grid-pager-control input {
  border: 1px solid #e0e0e0;
  padding: 2px 5px;
  font-size: 12px;
}

/* Combined Panel */
.combined-panel {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: 12px;
  overflow: hidden;
  border: 1px solid #eee;
}

.combined-panel-header {
  background-color: var(--primary-color);
  padding: 6px 12px;
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.combined-panel-header h2 {
  font-size: 12px;
  margin: 0;
  font-weight: 500;
}

.combined-panel-header i {
  margin-right: 6px;
  font-size: 14px;
}

.combined-panel-content {
  padding: 10px;
}

.combined-panel-section {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.combined-panel-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.combined-panel-section-title {
  font-size: 11px;
  font-weight: 500;
  margin: 0 0 6px 0;
  color: var(--primary-dark);
}

/* Contract Toggle */
.contract-toggle {
  margin: 10px 0;
  padding: 8px 12px;
  background: linear-gradient(to right, var(--primary-light), rgba(224, 242, 241, 0.5));
  border-radius: 4px;
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary-color);
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.contract-toggle:hover {
  box-shadow: 0 2px 5px rgba(0,0,0,0.08);
  transform: translateY(-1px);
}

.contract-toggle label {
  margin-right: 15px;
  font-weight: 500;
  font-size: 12px;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
}

.contract-toggle label i {
  margin-right: 6px;
  color: var(--primary-color);
}

/* Switch Styling */
.switch {
  position: relative;
  display: inline-block;
}

.switch label {
  display: flex;
  align-items: center;
}

.switch .lever {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 18px;
  background-color: #ccc;
  border-radius: 15px;
  margin: 0 10px;
  transition: background 0.3s ease;
  vertical-align: middle;
  cursor: pointer;
}

.switch .lever:after {
  content: "";
  position: absolute;
  display: inline-block;
  width: 14px;
  height: 14px;
  background-color: #fff;
  border-radius: 50%;
  left: 2px;
  top: 2px;
  transition: left 0.3s ease, background 0.3s ease, box-shadow 0.1s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.4);
}

.switch input[type=checkbox]:checked + .lever {
  background-color: var(--primary-color);
}

.switch input[type=checkbox]:checked + .lever:after {
  left: 20px;
}

/* Error Message */
.errorMessage {
  color: var(--danger-color);
  font-size: 11px;
  margin-top: 2px;
  margin-bottom: 0;
}

/* Date Fields */
.date-field-container {
  margin-bottom: 16px;
}

.date-field-label {
  font-weight: 500;
  font-size: 12px;
  margin-bottom: 4px;
  display: block;
}

/* Date Panel */
.date-panel {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: 16px;
  overflow: hidden;
  border: 1px solid #eee;
}

.date-panel-header {
  background-color: var(--primary-color);
  padding: 8px 16px;
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.date-panel-header h2 {
  font-size: 14px;
  margin: 0;
  font-weight: 500;
}

.date-panel-header i {
  margin-right: 8px;
  font-size: 16px;
}

.date-panel-content {
  padding: 16px;
}

/* Modal Styles */
.modal-large {
  width: 100% !important;
}

.modal-small {
  width: 30% !important;
}

.popeye-modal {
  width: 80% !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .form-col {
    width: 100%;
    margin-bottom: 12px;
  }

  .btn-container {
    flex-direction: column;
  }
}
