
/* Reset and Base Styles */
.vendor-contract-container * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.vendor-contract-container {
    --primary-color: #26a69a;
    --primary-light: #80cbc4;
    --primary-dark: #00796b;
    --secondary-color: #ff9800;
    --danger-color: #f44336;
    --success-color: #4CAF50;
    --warning-color: #FFC107;
    --info-color: #2196F3;
    --text-color: #333333;
    --text-light: #757575;
    --background-light: #f5f5f5;
    --card-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    --border-radius: 4px;

    font-family: 'Roboto', sans-serif;
    color: var(--text-color);
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    padding: 0;
    margin: 0;
}

/* Layout Containers */
.vc-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 16px;
    overflow: hidden;
    width: 100%;
}

.vc-card-header {
    background-color: var(--background-light);
    padding: 16px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.vc-card-content {
    padding: 16px;
}

.vc-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
    width: 100%;
}

.vc-col {
    padding: 0 8px;
    width: 100%;
}

.vc-col-6 {
    width: 50%;
}

.vc-col-4 {
    width: 33.333%;
}

.vc-col-3 {
    width: 25%;
}

@media (max-width: 992px) {
    .vc-col-4 {
        width: 50%;
    }
    .vc-col-3 {
        width: 50%;
    }
}

@media (max-width: 768px) {
    .vc-col-6, .vc-col-4, .vc-col-3 {
        width: 100%;
    }
}

/* Typography */
.vc-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
}

.vc-subtitle {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
}

.vc-text {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 8px;
}

.vc-text-sm {
    font-size: 12px;
    color: var(--text-light);
}

.vc-text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: inline-block;
}

/* Form Elements */
.vc-form-group {
    margin-bottom: 16px;
}

.vc-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
}

.vc-input,
.vc-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 14px;
    color: var(--text-color);
    background-color: white;
}

.vc-input:focus,
.vc-select:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Buttons */
.vc-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
    box-shadow: var(--card-shadow);
    margin-right: 8px;
    margin-bottom: 8px;
}

.vc-btn:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.vc-btn i {
    margin-right: 8px;
    font-size: 18px;
}

.vc-btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.vc-btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.vc-btn-success {
    background-color: var(--success-color);
    color: white;
}

.vc-btn-warning {
    background-color: var(--warning-color);
    color: #333;
}

.vc-btn-info {
    background-color: var(--info-color);
    color: white;
}

.vc-btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

/* Badges and Chips */
.vc-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    margin-right: 8px;
    margin-bottom: 4px;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.vc-badge i {
    margin-right: 4px;
    font-size: 14px;
}

.vc-badge-success {
    background-color: var(--success-color);
    color: white;
}

.vc-badge-warning {
    background-color: var(--warning-color);
    color: #333;
}

.vc-badge-danger {
    background-color: var(--danger-color);
    color: white;
}

.vc-badge-info {
    background-color: var(--info-color);
    color: white;
}

.vc-badge-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.vc-badge-light {
    background-color: #e0e0e0;
    color: var(--text-color);
}

.vc-badge-dark {
    background-color: #757575;
    color: white;
}

.vc-chip {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 400;
    line-height: 1.5;
    margin-right: 8px;
    margin-bottom: 4px;
    background-color: #e0e0e0;
    color: var(--text-color);
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.vc-chip i {
    margin-right: 4px;
    font-size: 14px;
}

/* Tables */
.vc-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.vc-table th {
    background-color: var(--background-light);
    color: var(--text-color);
    font-weight: 500;
    text-align: left;
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
}

.vc-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
}

.vc-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.vc-table tr:last-child td {
    border-bottom: none;
}

/* Accordion */
.vc-accordion {
    margin-bottom: 16px;
    width: 100%;
}

.vc-accordion-item {
    margin-bottom: 8px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.vc-accordion-header {
    background-color: white;
    padding: 16px;
    cursor: pointer;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.2s ease;
}

.vc-accordion-header:hover {
    background-color: #f5f5f5;
}

.vc-accordion-body {
    padding: 0;
    background-color: white;
}

/* Hierarchy */
.vc-hierarchy {
    position: relative;
    padding-left: 24px;
}

.vc-hierarchy-line {
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e0e0e0;
}

.vc-hierarchy-item {
    position: relative;
    margin-bottom: 16px;
}

.vc-hierarchy-item:before {
    content: '';
    position: absolute;
    left: -16px;
    top: 12px;
    width: 16px;
    height: 2px;
    background-color: #e0e0e0;
}

.vc-hierarchy-item:last-child {
    margin-bottom: 0;
}

.vc-hierarchy-dot {
    position: absolute;
    left: -20px;
    top: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

/* Utility Classes */
.vc-mt-0 { margin-top: 0 !important; }
.vc-mt-1 { margin-top: 4px !important; }
.vc-mt-2 { margin-top: 8px !important; }
.vc-mt-3 { margin-top: 16px !important; }
.vc-mt-4 { margin-top: 24px !important; }

.vc-mb-0 { margin-bottom: 0 !important; }
.vc-mb-1 { margin-bottom: 4px !important; }
.vc-mb-2 { margin-bottom: 8px !important; }
.vc-mb-3 { margin-bottom: 16px !important; }
.vc-mb-4 { margin-bottom: 24px !important; }

.vc-ml-0 { margin-left: 0 !important; }
.vc-ml-1 { margin-left: 4px !important; }
.vc-ml-2 { margin-left: 8px !important; }
.vc-ml-3 { margin-left: 16px !important; }
.vc-ml-4 { margin-left: 24px !important; }

.vc-mr-0 { margin-right: 0 !important; }
.vc-mr-1 { margin-right: 4px !important; }
.vc-mr-2 { margin-right: 8px !important; }
.vc-mr-3 { margin-right: 16px !important; }
.vc-mr-4 { margin-right: 24px !important; }

.vc-p-0 { padding: 0 !important; }
.vc-p-1 { padding: 4px !important; }
.vc-p-2 { padding: 8px !important; }
.vc-p-3 { padding: 16px !important; }
.vc-p-4 { padding: 24px !important; }

.vc-text-center { text-align: center !important; }
.vc-text-left { text-align: left !important; }
.vc-text-right { text-align: right !important; }

.vc-d-flex { display: flex !important; }
.vc-align-center { align-items: center !important; }
.vc-justify-between { justify-content: space-between !important; }
.vc-justify-end { justify-content: flex-end !important; }
.vc-flex-wrap { flex-wrap: wrap !important; }

.vc-w-100 { width: 100% !important; }
.vc-h-100 { height: 100% !important; }

/* Loading Spinner */
.vc-spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid rgba(0,0,0,0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: vc-spin 1s ease-in-out infinite;
}

@keyframes vc-spin {
    to { transform: rotate(360deg); }
}

/* Empty State */
.vc-empty-state {
    text-align: center;
    padding: 32px 16px;
}

.vc-empty-state i {
    font-size: 48px;
    color: #bdbdbd;
    margin-bottom: 16px;
}

.vc-empty-state-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
}

.vc-empty-state-text {
    font-size: 14px;
    color: var(--text-light);
}

.work-order-header.light-red {
    background-color: #ffe6e6 !important;
}

.contract-item .collapsible-header {
    background-color: #f0f8ff;
}

.work-order-header {
    background-color: #fffde7;
}