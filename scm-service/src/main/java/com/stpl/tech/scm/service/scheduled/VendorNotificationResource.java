package com.stpl.tech.scm.service.scheduled;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.stpl.tech.scm.data.dao.SchedulerStatusDao;
import com.stpl.tech.scm.data.enums.SchedulerStatus;
import com.stpl.tech.scm.data.model.SchedulerStatusData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.config.CronTask;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskHolder;
import org.springframework.stereotype.Component;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.AppUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Chaayos on 06-09-2016.
 */
@Component
public class VendorNotificationResource {

    Logger LOG = LoggerFactory.getLogger(VendorNotificationResource.class);

    @Autowired
    private SCMNotificationService scmNotificationService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private EnvProperties envProperties;

    @Autowired
	private RequestOrderManagementService requestOrderManagementService;

    @Autowired
    SchedulerStatusDao schedulerStatusDao;

    @Autowired
    private ScheduledTaskHolder scheduledTaskHolder;


    //@Scheduled(cron = "0 0 12 * * *", zone = "GMT+05:30")
    public void sendConsolidatedROEmailsToVendorForMilk() {
        if (envProperties.sendVendorEmails()) {
            LOG.info("Triggering vendor email notification for milk");
            List<VendorDetail> vendorDetails = new ArrayList<>(scmCache.getVendorDetails().values());
            List<RequestOrder> requestOrders = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getNextDate(SCMUtil.getCurrentDateIST()), false);
            scmNotificationService.sendVendorRONotification(envProperties.sendVendorEmails(), vendorDetails, requestOrders, true, NotificationType.EMAIL);
        } else {
            LOG.info("Skipping vendor email notification for milk");
        }
    }


//	@Scheduled(cron = "0 1 7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 * * *", zone = "GMT+05:30")
	public void sendConsolidatedROEmailsToVendor(boolean isScheduled,String key) {
        SchedulerStatusData schedulerStatusData = null;
        try {
            schedulerStatusData = new SchedulerStatusData();
            schedulerStatusData.setSchedulerKey("cronToSendConsolidatedROEmailsToVendor_"+key);
            schedulerStatusData.setTimeStamp(SCMUtil.getCurrentTimestamp());
            Date endTime = AppUtils.getCurrentTimestamp();
            Date startTime = AppUtils.getDate(AppUtils.getCurrentTimestamp());
            List<Integer> requestOrdersNotified = new ArrayList<>();
            try {
                if (envProperties.sendVendorEmails()) {
                    LOG.info(String.format("Triggering vendor email notification from %s to %s ", startTime, endTime));
					List<RequestOrder> requestOrderList = requestOrderManagementService
							.getSpecializedROForNotification(NotificationType.EMAIL, startTime, endTime);
					requestOrdersNotified.addAll(scmNotificationService.sendVendorRONotification(
							envProperties.sendVendorEmails(), requestOrderList, NotificationType.EMAIL,isScheduled));
                } else {
                    LOG.info("Skipping vendor email notification for milk");
                }
            } catch (Exception e) {
                LOG.error(String.format("Unable to send vendor email notification from %s to %s ", startTime, endTime), e);
            }
            try {
                if (envProperties.sendVendorSMS()) {
                    LOG.info(String.format("Triggering vendor SMS notification from %s to %s ", startTime, endTime));
					List<RequestOrder> requestOrderList = requestOrderManagementService
							.getSpecializedROForNotification(NotificationType.EMAIL, startTime, endTime);
					requestOrdersNotified.addAll(scmNotificationService.sendVendorRONotification(
							envProperties.sendVendorEmails(), requestOrderList, NotificationType.SMS,isScheduled));
                    requestOrdersNotified.addAll(scmNotificationService.sendVendorRONotification(
                        envProperties.sendVendorSMS(), requestOrderList, NotificationType.SMS,isScheduled));
                } else {
                    LOG.info("Skipping vendor SMS notification for milk");
                }
            } catch (Exception e) {
                LOG.error(String.format("Unable to send SMS email notification from %s to %s ", startTime, endTime), e);
            }
            LOG.info(String.format("Marked %d request orders as notified ", requestOrdersNotified.size()));
            if (requestOrdersNotified.size() > 0) {
                requestOrderManagementService.markRequestOrderNotified(requestOrdersNotified);
            }
            schedulerStatusData.setStatus(SchedulerStatus.SUCCESS);
        } catch (Exception e) {
            LOG.error("Error in sending vendor RO notifications::", e);
            if(schedulerStatusData!=null){
                schedulerStatusData.setStatus(SchedulerStatus.FAILED);
            }
        }finally {
            if(schedulerStatusData!=null){
                schedulerStatusDao.save(schedulerStatusData);
            }
        }
	}



    public void setSchedulerData(){
        try{
            LOG.info("-- Setting scheduler data ----");
            Set<ScheduledTask> tasks =  scheduledTaskHolder.getScheduledTasks();
            for(ScheduledTask scheduledTask : tasks){
                if(scheduledTask.getTask() instanceof CronTask){
                    String [] name = scheduledTask.getTask().getRunnable().toString().split("\\.");
                    String functionName =	 name[name.length - 1];
                    if(functionName.equalsIgnoreCase("cronToSendConsolidatedROEmailsToVendor")){
                        SchedulerStatusData schedulerStatusData  = new SchedulerStatusData();
                        String cron = ((CronTask) scheduledTask.getTask()).getExpression();
                        schedulerStatusData.setSchedulerKey(functionName+"_"+cron);
                        schedulerStatusData.setTimeStamp(SCMUtil.getCurrentTimestamp());
                        schedulerStatusData.setStatus(SchedulerStatus.INITIATED);
                        schedulerStatusDao.save(schedulerStatusData);
                    }
                }
            }
        }catch(Exception e){
            LOG.info("Error in Scheduler data : {}",e.getMessage());
        }
    }


    //@Scheduled(cron = "0 0 11 * * *", zone = "GMT+05:30")
    public void sendConsolidatedROEmailsToBangaloreVendorForBreads() {
        if (envProperties.sendVendorEmails()) {
            LOG.info("Triggering vendor email notification for breads");
            List<VendorDetail> vendorDetails = scmCache.getVendorDetails().values().stream().filter(vendorDetail -> vendorDetail.getVendorId() == 1365).collect(Collectors.toList());
            List<RequestOrder> requestOrderList = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getNextDate(SCMUtil.getCurrentDateIST()), false);
            scmNotificationService.sendVendorRONotification(envProperties.sendVendorEmails(), vendorDetails, requestOrderList, false, NotificationType.EMAIL);
        } else {
            LOG.info("Skipping vendor email notification for breads");
        }
    }

    //@Scheduled(cron = "0 0 15 * * *", zone = "GMT+05:30")
    public void sendConsolidatedROEmailsToVendorForBreads() {
        if (envProperties.sendVendorEmails()) {
            LOG.info("Triggering vendor email notification for breads");
            List<VendorDetail> vendorDetails = new ArrayList<>(scmCache.getVendorDetails().values());
            List<RequestOrder> requestOrderList = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getNextDate(SCMUtil.getCurrentDateIST()), false);
            scmNotificationService.sendVendorRONotification(envProperties.sendVendorEmails(),vendorDetails, requestOrderList, false, NotificationType.EMAIL);
        } else {
            LOG.info("Skipping vendor email notification for breads");
        }
    }

    //@Scheduled(cron = "0 0 12 * * *", zone = "GMT+05:30")
    public void sendConsolidatedROSMSToVendorForMilk() {
        if (envProperties.sendVendorSMS()) {
            LOG.info("Triggering vendor SMS notification for milk");
            List<VendorDetail> vendorDetails = new ArrayList<>(scmCache.getVendorDetails().values());
            List<RequestOrder> requestOrderList = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getNextDate(SCMUtil.getCurrentDateIST()), false);
            scmNotificationService.sendVendorRONotification(envProperties.sendVendorSMS(), vendorDetails, requestOrderList, true, NotificationType.SMS);
        } else {
            LOG.info("Skipping vendor SMS notification for milk");
        }
    }

    //@Scheduled(cron = "0 0 15 * * *", zone = "GMT+05:30")
    public void sendConsolidatedROSMSToVendorForBreads() {
        if (envProperties.sendVendorSMS()) {
            LOG.info("Triggering vendor SMS notification for breads");
            List<VendorDetail> vendorDetails = new ArrayList<>(scmCache.getVendorDetails().values());
            List<RequestOrder> requestOrderList = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getNextDate(SCMUtil.getCurrentDateIST()), false);
            scmNotificationService.sendVendorRONotification(envProperties.sendVendorSMS(),vendorDetails, requestOrderList, false, NotificationType.SMS);
        } else {
            LOG.info("Skipping vendor SMS notification for breads");
        }
    }

    @Scheduled(cron = "0 0 11 * * *", zone = "GMT+05:30")
    public void sendConsolidatedGREmailsToVendorForMilk() {
        if (envProperties.sendVendorEmails()) {
            LOG.info("Triggering vendor email notification for milk receiving");
            List<VendorDetail> vendorDetails = new ArrayList<>(scmCache.getVendorDetails().values());
            List<RequestOrder> requestOrders = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getCurrentDateIST(), true);
            scmNotificationService.sendVendorGRNotification(envProperties.sendVendorEmails(), vendorDetails, requestOrders, true, NotificationType.EMAIL);
        } else {
            LOG.info("Skipping vendor email notification for milk receiving");
        }

    }

    @Scheduled(cron = "0 0 14 * * *", zone = "GMT+05:30")
    public void sendConsolidatedGREmailsToVendorForBreads() {
        if (envProperties.sendVendorEmails()) {
            LOG.info("Triggering vendor email notification for breads receiving");
            List<VendorDetail> vendorDetails = new ArrayList<>(scmCache.getVendorDetails().values());
            List<RequestOrder> requestOrders = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getCurrentDateIST(), true);
            scmNotificationService.sendVendorGRNotification(envProperties.sendVendorEmails(), vendorDetails,requestOrders, false, NotificationType.EMAIL);
        } else {
            LOG.info("Skipping vendor email notification for breads  receiving");
        }
    }

    @Scheduled(cron = "0 0 11 * * *", zone = "GMT+05:30")
    public void sendConsolidatedGRSMSToVendorForMilk() {
        if (envProperties.sendVendorSMS()) {
            LOG.info("Triggering vendor SMS notification for milk receiving");
            List<VendorDetail> vendorDetails = new ArrayList<>(scmCache.getVendorDetails().values());
            List<RequestOrder> requestOrders = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getCurrentDateIST(), true);
            scmNotificationService.sendVendorGRNotification(envProperties.sendVendorSMS(), vendorDetails, requestOrders, true, NotificationType.SMS);
        } else {
            LOG.info("Skipping vendor SMS notification for milk receiving");
        }
    }

    //@Scheduled(cron = "0 0 14 * * *", zone = "GMT+05:30")
    public void sendConsolidatedGRSMSToVendorForBreads() {
        if (envProperties.sendVendorSMS()) {
            LOG.info("Triggering vendor SMS notification for breads receiving");
            List<VendorDetail> vendorDetails = new ArrayList<>(scmCache.getVendorDetails().values());
            List<RequestOrder> requestOrders = requestOrderManagementService
    				.getSpecializedROForFulfillmentDate(SCMUtil.getCurrentDateIST(), true);
            scmNotificationService.sendVendorGRNotification(envProperties.sendVendorSMS(), vendorDetails, requestOrders, false, NotificationType.SMS);
        } else {
            LOG.info("Skipping vendor SMS notification for breads receiving");
        }
    }
}
