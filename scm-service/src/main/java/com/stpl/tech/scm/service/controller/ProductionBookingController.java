package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.ProductionBookingService;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnitDetail;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.SCMProductItem;
import com.stpl.tech.scm.service.annotation.DayClosureCheck;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.ActionRequest;
import com.stpl.tech.scm.service.model.DataRequest;
import com.stpl.tech.util.JSONSerializer;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.PRODUCTION_BOOKING_ROOT_CONTEXT, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON)
public class ProductionBookingController extends AbstractSCMResources{

	private final Logger LOG = LoggerFactory.getLogger(ProductionBookingController.class);

	@Autowired
	private ProductionBookingService bookingService;

	@Autowired
	private ExcelViewGenerator excelViewGenerator;

	@RequestMapping(method = RequestMethod.GET, value = "calculate-consumption")
	public ProductionBooking calculateConsumption(@RequestParam final int productId, @RequestParam final int unitId,
			@RequestParam final BigDecimal quantity) throws SumoException, DataNotFoundException {
		LOG.info("Request to Calculate Production Booking at unitId {} for productId {} and quantity {}", unitId,
				productId, quantity);
		return bookingService.calculateConsumption(unitId, productId, quantity);
	}

	@DayClosureCheck
	@RequestMapping(value = "add")
	public boolean addBooking(HttpServletRequest request,@RequestParam Boolean updateMapping, @RequestBody final ProductionBooking booking)
			throws InventoryUpdateException, SumoException, DataNotFoundException, DataUpdationException,DayCloseInitiatedException {
		if (booking.isReverseBooking()){
			if (updateMapping) {
				LOG.info("Request to add Mapping for productId {} and quantity {}", booking.getProductName()
						, booking.getQuantity());
				return bookingService.updateReverseMapping(booking, updateMapping);
			} else {
				LOG.info("Request to add Production Booking for product {} and quantity {}", booking.getProductName(),
						booking.getQuantity());
				return bookingService.addReverseBooking(booking);
			}
		} else {
			if (updateMapping) {
				LOG.info("Request to add Mapping for productId {} and quantity {}", booking.getProductName()
						, booking.getQuantity());
				return bookingService.updateMapping(booking, updateMapping);
			} else {
				LOG.info("Request to add Production Booking for product {} and quantity {}", booking.getProductName(),
						booking.getQuantity());
				return bookingService.addBooking(booking);
			}
		}
	}

	@RequestMapping(value = "bookings")
	public List<ProductionBooking> getBooking(@RequestBody final DataRequest dataRequest,@RequestParam(required = false) boolean isReverse) {
		LOG.info("Request to get Production Booking for data request {}", JSONSerializer.toJSON(dataRequest));
		return bookingService.getBookings(dataRequest.getUnitId(), dataRequest.getStartDate(),
				dataRequest.getEndDate(),isReverse);
	}

	@DayClosureCheck
	@RequestMapping(value = "cancel")
	public boolean cancelBooking(HttpServletRequest httpServletRequest,@RequestBody final ActionRequest request)
			throws InventoryUpdateException, DayCloseInitiatedException, DataNotFoundException {
		LOG.info("Request to cancel Production Booking for data request {}", JSONSerializer.toJSON(request));
		if (request.isReverseBooking()){
			return bookingService.cancelReverseBookings(request.getId(), request.getEmpId());
		}
		return bookingService.cancelBookings(request.getId(), request.getEmpId());
	}

	@DayClosureCheck
	@RequestMapping(value = "getLastBooking")
	public ProductionBooking getLastBooking(HttpServletRequest request, @RequestParam Integer unitId,@RequestParam(required = false) boolean isReverse)
		throws InventoryUpdateException, DayCloseInitiatedException, DataNotFoundException {
		LOG.info("Request to get Production Booking for Last Booking");
		return bookingService.getLastBooking(unitId,isReverse);
	}

	@RequestMapping(method = RequestMethod.POST,value = "inactiveBooking")//inactive-scm-recipe-product-mapping
	public boolean InactiveBooking(@RequestBody IdCodeName data){
		Integer productId=data.getId();
		String profile=data.getName();
		LOG.info("Inactive Products with productId{} and profile{}",productId,profile);
		return bookingService.inactiveProductFromProductionBooking(productId,profile);
	}

	@RequestMapping(method = RequestMethod.POST,value = "generate-raw-material-excel", consumes = MediaType.MULTIPART_FORM_DATA )
	public View generateRawMaterialExcel(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file,
							@RequestParam(value = "unitId") final Integer unitId) throws IOException, SumoException, DataNotFoundException {
		List<SCMProductItem> productList = bookingService.readUploadFile(file);
		return excelViewGenerator.generateRawMaterialExcel(unitId,productList);

	}

}
