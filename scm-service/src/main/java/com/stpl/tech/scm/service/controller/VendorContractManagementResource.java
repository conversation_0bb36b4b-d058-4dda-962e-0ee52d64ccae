package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.SkuMappingService;
import com.stpl.tech.scm.core.service.impl.WarehouseStockManagementServiceImpl;
import com.stpl.tech.scm.core.util.SCMConstants;
import com.stpl.tech.scm.data.model.VendorContractSoInfo;
import com.stpl.tech.scm.data.transport.model.VendorOTPValidationDomain;
import com.stpl.tech.scm.domain.model.VendorContractStatus;
import com.stpl.tech.scm.domain.model.VendorContractV2Dto;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.PreviousPricingDataVO;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.SkuPriceUpdateDetail;
import com.stpl.tech.scm.domain.model.WorkOrder;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Mohit
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT)
@Log4j2
public class VendorContractManagementResource extends AbstractSCMResources {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseStockManagementServiceImpl.class);

    @Autowired
    private SkuMappingService mappingService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private SCMConstants SCMConstants;


//    @PostMapping("/vendor/get-request-price")
//    public List<SkuPriceDetail> getVendorPriceChange(@RequestBody final VendorLookupData data) {
//        return mappingService.getVendorPriceChange(data.getVendorId(), data.getLocation());
//    }

    @RequestMapping(method = RequestMethod.POST, value = "generate/sku-price-request", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean generateSkuPriceUpdateRequest(@RequestBody final List<SkuPriceUpdate> data) throws SumoException {
        return mappingService.generateSkuPriceUpdateRequest(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor/process-price-request", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean processPriceRequestForVendor(@RequestBody final SkuPriceUpdateDetail data) {
        return mappingService.processPriceRequestForVendor(data);
    }

    @GetMapping("/vendor/preview-request-price")
    public List<SkuPriceDetail> previewVendorPriceChange(@RequestParam Integer vendorId) {
        return mappingService.previewVendorPriceChange(vendorId);
    }

    @GetMapping("/vendor/get-contract/V2")
    public List<VendorContractV2Dto> getVendorContractV2(@RequestParam(required = false) Integer vendorId,
                                                         @RequestParam(required = false) String status,
                                                         @RequestParam(required = false) String startDate,
                                                         @RequestParam(required = false) String endDate,
                                                         @RequestParam(required = false) Integer vendorContractId) {
        return mappingService.getVendorContractV2(vendorId, status, AppUtils.getDate(startDate, AppUtils.DATE_FORMAT_STRING),
                AppUtils.getDate(endDate, AppUtils.DATE_FORMAT_STRING), vendorContractId);
    }

    @GetMapping("/vendor/get-work-orders")
    public ApiResponse getWorkOrdersByContractId(@RequestParam Integer contractId) throws SumoException {
        return mappingService.getWorkOrdersByContractId(contractId);
    }

    @GetMapping("/vendor/get-items-by-woId")
    public ApiResponse getItemsByWorkOrderId(@RequestParam Integer workOrderId) throws SumoException {
        return mappingService.getItemsByWoId(workOrderId);
    }

    @PostMapping("/vendor/cancel-contract/V2")
    public boolean cancelVendorContractV2(HttpServletRequest request, @RequestParam Integer workOrderId) throws SumoException {
        return mappingService.cancelVendorContractV2(workOrderId, getLoggedInUser(request));
    }

    @GetMapping("/vendor/get-template")
    public Collection<IdCodeName> getVendorContractTemplate() {
        return SCMConstants.getVendorContractTemplate().values();
    }

    @PostMapping("/vendor/generate-contract/V2")
    public DocumentDetail generateContractUsingTemplateV2(@RequestParam Integer workOrderId,
                                                          @RequestParam(required = false) Integer templateId) throws SumoException, TemplateRenderingException {
        return mappingService.generateContractUsingTemplateV2(workOrderId, templateId);
    }

    @GetMapping("/vendor/get-contract-document")
    public String getContractDocument(@RequestParam Integer documentId) throws SumoException {
        return mappingService.getContractDocument(documentId);
    }

    @PostMapping("/vendor/trigger-mail/V2")
    public boolean triggerVendorContractMailV2(@RequestParam Integer workOrderId) throws SumoException, EmailGenerationException {
        return mappingService.triggerVendorContractMailV2(workOrderId, null);
    }

    @PostMapping("/vendor/acceptance/V2")
    public boolean vendorAcceptanceV2(HttpServletRequest request, @RequestBody WorkOrder workOrder) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException {
        return mappingService.vendorAcceptanceV2(workOrder, getLoggedInUser(request), request);
    }

    @GetMapping("/vendor-action-by-mail")
    public void vendorActionOnWOMail(HttpServletRequest request, @RequestParam String token, @RequestParam VendorContractStatus action, HttpServletResponse response) throws Exception {
        try {
            Boolean result = mappingService.vendorActionOnWOMail(token, request, action);
            String responseHtml;
            if (result == null) {
                responseHtml = "<html><body style='text-align:center;padding-top:50px;'>" +
                        "<h2 style='color:orange;'>Request has already been processed or request token not found, please contact support.</h2>" +
                        "</body></html>";
            } else if (result) {
                responseHtml = "<html><body style='text-align:center;padding-top:50px;'>" +
                        "<h2 style='color:green;'>Your " + (VendorContractStatus.VENDOR_APPROVED.equals(action) ? "Approval" : "Rejection") + " has been recorded successfully.</h2>" +
                        "</body></html>";
            } else {
                responseHtml = "<html><body style='text-align:center;padding-top:50px;'>" +
                        "<h2 style='color:red;'>We couldn't process your " + (VendorContractStatus.VENDOR_APPROVED.equals(action) ? "Approval" : "Rejection") + ". Please try again or contact support.</h2>" +
                        "</body></html>";
            }


            response.setContentType("text/html");
            response.getWriter().write(responseHtml);

        } catch (Exception e) {
            String errorHtml = "<html><body style='text-align:center;padding-top:50px;'>" +
                    "<h2 style='color:red;'>✖ An unexpected error occurred. Please try again or contact support</h2>" +
                    "</body></html>";
            response.setContentType("text/html");
            response.getWriter().write(errorHtml);
        }
    }

    @Scheduled(cron = "0 0 0 * * *", zone = "GMT+05:30")
    @PostMapping("/vendor/run-apply-contract-cron")
    public void applyContract() {
        mappingService.applyContract();
    }

    @Scheduled(cron = "0 55 23 * * *", zone = "GMT+05:30")
    @PostMapping("/vendor/run-expire-contract-cron")
    public void expiryContract() {
        mappingService.expiryContract();
    }

    @PostMapping("/vendor/trigger-otp")
    public boolean triggerEmailOtpForVendor(@RequestParam(required = false) Integer vendorId,
                                            @RequestParam(required = false) Integer employeeId) throws SumoException {
        if (Objects.nonNull(vendorId)) {
            return mappingService.triggerEmailOtpForVendor(vendorId);
        } else {
            return mappingService.triggerEmailOtpForEmployee(employeeId);
        }
    }

    @PostMapping("/vendor/validate-otp")
    public boolean validateVendorOtp(@RequestBody VendorOTPValidationDomain otp) throws SumoException {
        return mappingService.validateVendorOtp(otp);
    }

    @RequestMapping(method = RequestMethod.POST, value = "validate", produces = MediaType.APPLICATION_JSON)
    public WorkOrder validateContractRequest(@RequestBody final Map<String, String> tokenMap)
            throws VendorRegistrationException, UnsupportedEncodingException, SumoException {
        return mappingService.validateRequestV2(tokenMap.get("token"));
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor/save-digital-signature", consumes = MediaType.MULTIPART_FORM_DATA)
    public Integer saveDigitalSignature(HttpServletRequest request,
                                               @RequestParam(value = "file") final MultipartFile file, @RequestParam final Integer woId
    ) throws IOException, SumoException {
        return mappingService.saveDigitalSignature(file, woId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "validate-so-contract", produces = MediaType.APPLICATION_JSON)
    public VendorContractSoInfo validateSoContractRequest(@RequestBody final Map<String, String> tokenMap)
            throws VendorRegistrationException, UnsupportedEncodingException, SumoException {
        return mappingService.validateSoContractRequest(tokenMap.get("token"));
    }

    @PostMapping("save/byPass-vendor-contract-item")
    public boolean saveVendorContract(HttpServletRequest request, @RequestBody WorkOrder workOrder) throws SumoException {
        return mappingService.saveVendorContract(workOrder, getLoggedInUser(request));
    }

    @GetMapping("get/work-orders")
    public List<WorkOrder> getWorkOrders(HttpServletRequest request) {
        return mappingService.getWorkOrders(getLoggedInUser(request));
    }

    @GetMapping("find/sku-price-mapping")
    public WorkOrder findSkuPriceMapping(@RequestParam Integer woId, @RequestParam String status, HttpServletRequest request) throws SumoException {
        return mappingService.findSkuPriceMapping(woId, status);
    }

    @PostMapping("submit/price-approvals")
    public boolean submitPriceApprovals(HttpServletRequest request, @RequestBody WorkOrder workOrder) throws SumoException {
        return mappingService.submitPriceApprovals(workOrder, getLoggedInUser(request), request);
    }

    @GetMapping("get/bypass-status")
    public Map<String, String> getContractStatus(@RequestParam Integer vendorId) {
        return mappingService.getContractStatus(vendorId);
    }

    @PostMapping(value = "upload-document", consumes = MediaType.MULTIPART_FORM_DATA)
    public Integer uploadDocument(@RequestParam(value = "mimeType") String mimeType,
                                  @RequestParam(value = "userId") Integer userId,
                                  @RequestPart(value = "file") final MultipartFile file) {
        return mappingService.uploadDocument(mimeType, userId, file);
    }

    @GetMapping("get/previous-prices-of-sku")
    public List<PreviousPricingDataVO> getPreviousPricesOfSkuByLocation(@RequestParam Integer skuId,
                                                                        @RequestParam Integer deliveryLocationId,
                                                                        @RequestParam Integer packagingId) {
        return mappingService.getPreviousPricesOfSkuByLocation(skuId, deliveryLocationId, packagingId);
    }

    // one time use to create DEFAULT Work orders
    @PostMapping("create-default-wos")
    public void createDefaultWOsForExistingContracts() {
        mappingService.createDefaultWOsForExistingContracts();
    }

}
