<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Fullfillment Report</title>
</head>

<body>
<div style="padding: 0 30px 20px 30px">
    <div style="padding: 15px 5px;">
        <h2>Fullfillment Percentage - Kitchen and Warehouse $data.prevDay</h2>
        <table style=" border: 1px solid black;
  border-collapse: collapse;">
            <tr>
                <th style=" border: 1px solid black; border-collapse: collapse;  padding: 5px;">TRANSFERRING_UNIT</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >TOTAL_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >CRITICAL_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">NEGATIVE_IMPACT_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">BAKERY_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_1_PRODUCTS_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_2_PRODUCTS_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_3_PRODUCTS_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_4_PRODUCTS_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_5_PRODUCTS_%</th>
            </tr>
            #foreach($item in $data.ffDetailLastDay)
            <tr>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.transferingUnit</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.avgFPer)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">#if($item.isCriticalProd=="N" && $item.criticalProductFF==0.0)
                    No Critical Product
                    #else
                    $data.mathTool.roundTo(2,$item.criticalProductFF)%
                    #end
                </td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$data.mathTool.sub($item.avgFPer,$item.avgImFPer))%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.bakeryFP == -1.0)
                    N/A
                    #else
                    $data.mathTool.roundTo(2,$item.bakeryFP)%
                    #end
                </td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel1FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel1FFPer)% #end
                </td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel2FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel2FFPer)% #end</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel3FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel3FFPer)% #end</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel4FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel4FFPer)% #end</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel5FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel5FFPer)% #end</td>
            </tr>
            #end
        </table>

        <h2>Fullfillment Percentage For This Month - Kitchen or Warehouse $data.thisMonth </h2>
        <table style=" border: 1px solid black; border-collapse: collapse;">
            <tr>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TRANSFERRING_UNIT</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >TOTAL_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >CRITICAL_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">NEGATIVE_IMPACT_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">BAKERY_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_1_PRODUCTS_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_2_PRODUCTS_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_3_PRODUCTS_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_4_PRODUCTS_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LEVEL_5_PRODUCTS_%</th>
            </tr>
            #foreach($item in $data.ffDetailLastThD)
            <tr>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.transferingUnit</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.avgFPer)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">#if($item.isCriticalProd=="N" && $item.criticalProductFF==0.0)
                    No Critical Product
                    #else
                    $data.mathTool.roundTo(2,$item.criticalProductFF)%
                    #end
                </td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$data.mathTool.sub($item.avgFPer,$item.avgImFPer))%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.bakeryFP == -1.0)
                    N/A
                    #else
                    $data.mathTool.roundTo(2,$item.bakeryFP)%
                    #end
                </td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel1FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel1FFPer)% #end
                </td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel2FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel2FFPer)% #end</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel3FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel3FFPer)% #end</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel4FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel4FFPer)% #end</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.productLevel5FFPer == -1.0)  N/A
                    #else $data.mathTool.roundTo(2,$item.productLevel5FFPer)% #end</td>
            </tr>
            #end
        </table>

        <h2>Level 1 Products Summary</h2>
        <table style=" border: 1px solid black; border-collapse: collapse;">
            <tr>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">PRODUCT_NAME</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">PRODUCT_LEVEL</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LAST_DAY_IF%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >LAST_SEVEN_DAY_IF%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >MTD_IF%</th>
            </tr>
            #foreach($item in $data.criticalProducts1)
            <tr>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.productName</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.productLevel</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.lastDayFF)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.lastSevenDaysFF)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.mtdFF)%</td>
            </tr>
            #end
        </table>

        <h2>Level 2 Products Summary</h2>
        <table style=" border: 1px solid black; border-collapse: collapse;">
            <tr>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">PRODUCT_NAME</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">PRODUCT_LEVEL</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LAST_DAY_IF%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >LAST_SEVEN_DAY_IF%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >MTD_IF%</th>
            </tr>
            #foreach($item in $data.criticalProducts2)
            <tr>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.productName</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.productLevel</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.lastDayFF)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.lastSevenDaysFF)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.mtdFF)%</td>
            </tr>
            #end
        </table>


        <h2>Level 3 Products Summary</h2>
        <table style=" border: 1px solid black; border-collapse: collapse;">
            <tr>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">PRODUCT_NAME</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">PRODUCT_LEVEL</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LAST_DAY_IF%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >LAST_SEVEN_DAY_IF%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >MTD_IF%</th>
            </tr>
            #foreach($item in $data.criticalProducts3)
            <tr>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.productName</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.productLevel</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.lastDayFF)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.lastSevenDaysFF)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.mtdFF)%</td>
            </tr>
            #end
        </table>
    </div>


</div>
</body>
