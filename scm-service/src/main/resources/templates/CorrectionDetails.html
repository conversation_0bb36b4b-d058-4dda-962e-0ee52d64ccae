<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
  <title>Correction Details</title>
</head>
<body>

<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
  <div style="margin: 10px">
    <b>Invoice Id : </b> $data.correctedInvoice.invoiceId
    <br/><br/>
    <b>Additional Charges : </b>$data.correctedInvoice.additionalCharges
    <br/><br/>
    <b>Total Corrected Amount : </b>$data.correctedInvoice.totalCorrectedAmount
    <br/><br/>
    <b>Total Corrected Tax : </b>$data.correctedInvoice.totalCorrectedTax
    <br/><br/>
  </div>
  <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
    <thead>
    <tr style="background: #c90;">
      <th style="padding:5pt;text-align:center;"><b>Sku Id</b></th>
      <th style="padding:5pt;text-align:center;"><b>Sku Name</b></th>
      <th style="padding:5pt;text-align:center;"><b>Selling Price</b></th>
      <th style="padding:5pt;text-align:center;"><b>Corrected Selling Price</b></th>
      <th style="padding:5pt;text-align:center;"><b>Tax</b></th>
      <th style="padding:5pt;text-align:center;"><b>Corrected Tax</b></th>
    </tr>
    </thead>
    <tbody>
    #foreach( $item in $data.correctedInvoice.correctedInvoiceItems )
    <tr>
      <td style="padding:5pt;text-align:center;">#if($item.skuId) $item.skuId #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.skuName) $item.skuName #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.sellingPrice) $item.sellingPrice #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.correctedSellingPrice) $item.correctedSellingPrice #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.totalTax) $item.totalTax #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.correctedTotalTax) $item.correctedTotalTax #else - #end</td>

    </tr>
    #end
    </tbody>
  </table>
  <div style="margin: 5px; padding: 5px;">

  </div>
</div>
</body>