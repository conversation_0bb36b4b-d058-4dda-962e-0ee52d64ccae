<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>title</title>
    <style type="text/css">
        * {
            margin-top: -13px;
            margin-left: 0px;
            font-size: 11px;
        }

        .table {
            width: 100%;
            white-space: nowrap;
        }

        .container {
            border: 20px solid black;
            padding: 20px; /* optional: add some padding to create space between content and border */
            font-family: 'cambria';
            color: black;
            font-size: 20.0pt;
        }

        .table thead {
            border-bottom: #ccc 2px solid;
            white-space: nowrap;
        }

        .table td {
            border-top: #ccc 1px solid;
            white-space: nowrap;

            /*padding: 6px 2px;*/
        }

        .table thead td {
            font-weight: bold;
            white-space: nowrap;

        }

        .table tr:nth-child(2n) td {
            background: #f8f8f8;


        }

        .table tr:last-child td {
            font-weight: bold;


        }

        @page {
            page-break-inside: avoid;
            margin: 16mm 16mm 30mm 16mm;
        }
    </style>
</head>
<body>

<div class="row" id="printsection">
    <div class="col s12">
        <p style="float: left;">
            <span style="font-size: 10.0pt; line-height: 115%; font-family: 'cambria', serif; color: black;">INVOICE ID - $data.correctedInvoice.id</span>
            <br/>
            <span style="font-size: 10.0pt; line-height: 115%; font-family: 'cambria', serif; color: black;">E-INVOICE ID - $data.correctedInvoice.generatedDebitNoteId</span>
            <br/>
            <b><em><span style="font-family: 'cambria', serif;">FORM GST INV &ndash; 1</span></em></b>
            <br/>
            <b><span
                    style="font-size: 16.0pt; line-height: 115%; font-family: 'cambria', serif; color: black;">CHAAYOS<br/></span></b><b>
            <span style="font-size: 13.0pt; line-height: 115%; font-family: 'cambria', serif;">$data.invoice.sendingCompany.name<br/></span></b>
            <span style="font-size: 10.0pt;margin-bottom: 20px; line-height: 115%; font-family: 'cambria', serif; color: black;">
                $data.unitData.address.line1,
		#if ($data.unitData.address.line2),$data.unitData.address.line2, #end
                <br/> $data.unitData.address.city,
						$data.unitData.address.state,
                        $data.unitData.address.country,
						$data.unitData.address.zipCode
                <br/>
            </span>
            <b><span style="font-size: 13.0pt;margin-bottom: 20px; line-height: 115%; font-family: 'cambria', serif;text-align: center">DEBIT NOTE</span></b>
        </p>

        <p style="float: right;">
            <span><img src="$data.barCodeLink" width="165px" height="165px"/></span>
        </p>
        <table style="border-collapse: collapse; border: none;margin-bottom: 20px" cellpadding="0cm 5.4pt">
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="9" width="765">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'cambria', serif; text-align: left;">IRN NUMBER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                        <b><span style="font-size: 12.0pt; font-family: 'cambria', serif; text-align: right;">$data.correctedInvoice.irnNo</span></b>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="border: solid windowtext 1.0pt; border-top: 1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="4" width="388">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'cambria', serif; text-align: left;">EWAY NUMBER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                        <b><span style="font-size: 12.0pt; font-family: 'cambria', serif; text-align: right;">$data.correctedInvoice.uploadedEwayNo</span></b>
                    </p>
                </td>
                <td style="border: solid windowtext 1.0pt; border-top: 1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="4" width="388">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'cambria', serif; text-align: left;">ACK NUMBER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                        <b><span style="font-size: 12.0pt; font-family: 'cambria', serif; text-align: right;">$data.correctedInvoice.uploadedAckNo</span></b>
                    </p>
                </td>
            </tr>
            </tbody>
        </table>

        <table style="border-collapse: collapse; border: none; margin-bottom: 20px;width: 100%;">
            <tbody>
            <tr style="height: 12.00pt;width: 100%;page-break-inside: avoid;">
                <td style="width: 50%; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="100">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Billing Address</span></b>
                    </p>
                </td>
                <td style="width: 50%; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="100">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Shipping Address</span></b>
                    </p>
                </td>
            </tr>
            </tbody>
            <tbody style="margin-bottom: 20px">
            <tr style="height: 12.00pt;  page-break-inside: avoid;width: 100%;">
                <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; white-space: nowrap; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">#if($data.invoice.billingAddress) $data.invoice.billingAddress
                    #else - #end</span>
                    </p>
                </td>
                <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; white-space: nowrap; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">#if($data.invoice.deliveryAddress) $data.invoice.deliveryAddress
                    #else - #end</span>
                    </p>
                </td>
            </tr>
            </tbody>

            <tbody>
            <tr style="height: 12.00pt;width: 100%;  page-break-inside: avoid;">
                <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                    <table style="border-collapse: collapse; border: none;">
                        <tbody>
                        <tr style="height: 12.00pt;page-break-inside: avoid;">
                            <td>
                                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                    <b><span
                                            style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">State:</span></b>
                                </p>
                            </td>
                            <td>
                                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                    <b><span
                                            style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">
                                        #if($data.invoice.vendorDispatchLocation.state) $data.invoice.vendorDispatchLocation.state #else - #end</span></b>
                                </p>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr style="height: 12.00pt;">
                            <td>
                                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                    <b><span
                                            style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Gstin:</span></b>
                                </p>
                            </td>
                            <td>
                                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                    <b><span
                                            style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">
                                            #if($data.invoice.vendorDispatchLocation.gstin) $data.invoice.vendorDispatchLocation.gstin #else - #end</span>
                                    </b>
                                </p>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>

                        <tr style="height: 12.00pt;">
                            <td>
                                <p style="margin: .0001pt 0;line-height: normal;">
                                    <b><span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vendor:</span></b>
                                </p>
                            </td>
                            <td>
                                <p style="margin: .0001pt 0; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">#if($data.invoice.vendor.name) $data.invoice.vendor.name #else - #end </span>
                                </p>
                            </td>
                        </tr>

                        </tbody>
                    </table>
                </td>
                <td style="width: 50%;page-break-inside: avoid; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; padding-top: 20px;height: 12.00pt; ">
                    <table style="border-collapse: collapse; border: none;">
                        <tbody>
                        <tr style="height: 12.00pt;page-break-inside: avoid;margin-top: 20px">
                            <td>
                                <table style="border-collapse: collapse; border: none;">
                                    <tbody>
                                    <tr style="height: 12.00pt;">
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Debit Note No.</span></b>
                                            </p>
                                        </td>
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">$data.debitNoteNo</span></b>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="height: 12.00pt;">
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Purchase Order:</span></b>
                                            </p>
                                        </td>
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">-</span></b>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="height: 12.00pt;">
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Order Id:</span></b>
                                            </p>
                                        </td>
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">-</span></b>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="height: 12.00pt;">
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Place:</span></b>
                                            </p>
                                        </td>
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">
                                                   #if($data.invoice.vendorDispatchLocation.city) $data.invoice.vendorDispatchLocation.city #else - #end
                                                </span></b>
                                            </p>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <table style="border-collapse: collapse; border: none;">
                                    <tbody>
                                    <tr style="height: 12.00pt;">
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Debit Note Date:</span></b>
                                            </p>
                                        </td>
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">$data.businessDate</span></b>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="height: 12.00pt;">
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Invoice Date:</span></b>
                                            </p>
                                        </td>
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">$data.invoice.purchaseOrderDate</span></b>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="height: 12.00pt;">
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">Invoice No:</span></b>
                                            </p>
                                        </td>
                                        <td>
                                            <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                                <b><span
                                                        style="font-size: 10.0pt; font-family: 'cambria', serif; color: black;">$data.correctedInvoice.id</span></b>
                                            </p>
                                        </td>
                                        <td></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>


        <table style="border-collapse: collapse; border: none;margin-bottom: 20px;width: 100%">
            <tbody>
            <tr style="height: 12.00pt;">
                <td style="width: 10%; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">S.No</span></b>
                    </p>
                </td>
                <td style="width: 30%; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Item Description</span></b>
                    </p>
                </td>
                <td style="width: 10%; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Original Pkg Qty.</span></b>
                    </p>
                </td>
                <td style="width: 10%; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Revised Pkg Qty</span></b>
                    </p>
                </td>
                <td style="width: 10%; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Original Price</span></b>
                    </p>
                </td>
                <td style="width : 10%; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Revised Price</span></b>
                    </p>
                </td>
                <td style="width :10%; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Tax Amount</span></b>
                    </p>
                </td>
                <td style="width: 10% ; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Net Amount</span></b>
                    </p>
                </td>
<!--                <td style="width: 10% ; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
<!--                >-->
<!--                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
<!--                        <b><span-->
<!--                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Original Total Amount</span></b>-->
<!--                    </p>-->
<!--                </td>-->
<!--                <td style="width: 10%; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"-->
<!--                >-->
<!--                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">-->
<!--                        <b><span-->
<!--                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Revised Total Amount</span></b>-->
<!--                    </p>-->
<!--                </td>-->

            </tr>
            <tbody style="display: block; page-break-inside: avoid;">
            #foreach( $item in $data.correctedInvoice.salesPerformaCorrectedItems )
            <tr style="height: 12.00pt;  page-break-inside: avoid;">
                <td style="width: 10%; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        $velocityCount
                    </p>
                </td>
                <td style="width: 30%; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                          #if($item.skuName)  $item.skuName
                            #else - #end
                        </span>
                    </p>
                </td>
                <td style="width: 10%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            $data.mathTool.roundTo(2,$item.pkgQty)</span>
                    </p>
                </td>
                <td style="width: 10%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            $data.mathTool.roundTo(2,$item.revisedPkgQty)</span>
                    </p>
                </td>
                <td style="width: 10%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            $data.mathTool.roundTo(2,$item.price)</span>
                    </p>
                </td>
                <td style="width: 10%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            $data.mathTool.roundTo(2,$item.revisedPrice)</span>
                    </p>
                </td>
                <td style="width: 10%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            #set($totalTax = $item.revisedTax - $item.tax)
                                             $data.mathTool.roundTo(2,$totalTax)</span>
                    </p>
                </td>
                <td style="width: 10%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            #set($totalAmount = $item.revisedAmount - $item.amount)
                                             $data.mathTool.roundTo(2,$totalAmount)</span>
                    </p>
                </td>
<!--                <td style="width: 10%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">-->
<!--                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">-->
<!--                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">-->
<!--                            $data.mathTool.roundTo(2,$item.amount)</span>-->
<!--                    </p>-->
<!--                </td>-->
<!--                <td style="width: 10%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">-->
<!--                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">-->
<!--                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">-->
<!--                            $data.mathTool.roundTo(2,$item.revisedAmount)</span>-->
<!--                    </p>-->
<!--                </td>-->

            </tr>
            #end
            </tbody>
        </table>

        <table style="border-collapse: collapse; border: none; width: 100%;margin-bottom: 20px ">
            <tbody style="display: block; page-break-inside: avoid;">
            <tr style="height: 12.00pt;  page-break-inside: avoid;width: 100%;">
                <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                    <table style="border-collapse: collapse; border: none;margin-top: 20px">
                        <tbody>
                        <tr style="height: 12.00pt;">
                            <td>
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Bank Name :</span>
                                </p>
                            </td>
                            <td>
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Kotak Mahindra Bank</span>
                                </p>
                            </td>
                        </tr>
                        <tr style="height: 12.00pt;">
                            <td>
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Account Number :</span>
                                </p>
                            </td>
                            <td>
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">**********</span>
                                </p>
                            </td>
                        </tr>
                        <tr style="height: 12.00pt;">
                            <td>
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">IFSC CODE :</span>
                                </p>
                            </td>
                            <td>
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">KKBK0000298</span>
                                </p>
                            </td>
                        </tr>
                        <tr style="height: 12.00pt;">
                            <td>
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address :</span>
                                </p>
                            </td>
                            <td>
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Gurgaon Vatika Business Park Badshapur, <br/>
									Gurgaon, Haryana, India</span>
                                </p>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
                <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                    <table style="border-collapse: collapse; border: none;width: 100%;margin-bottom: 20px">
                        <tbody>
                        <tr style="height: 12.00pt;width: 100%;">
                            <td style="width: 50%;">
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Net Amount :</span>
                                </p>
                            </td>
                            <td style="width: 50%;">
                                <p style="margin: .0001pt 0; text-align: right; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.netAmount)</span>
                                </p>
                            </td>
                        </tr>
                        <tr style="height: 12.00pt;width: 50%;">
                            <td style="width: 50%;">
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Tax :</span>
                                </p>
                            </td>
                            <td style="width: 50%">
                                <p style="margin: .0001pt 0; text-align: right; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.totalTax)</span>
                                </p>
                            </td>
                        </tr>
                        <tr style="height: 12.00pt;width: 100%;">
                            <td style="width: 50%">
                                <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                    <b><span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Grand Total :</span></b>
                                </p>
                            </td>
                            <td style="width: 50%">
                                <p style="margin: .0001pt 0; text-align: right; line-height: normal;">
                                    <b><span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                                        #set($totalAmount=$data.netAmount + $data.totalTax)
                                             $data.mathTool.roundTo(2,$totalAmount)
                                    </span></b>
                                </p>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>

        <table style="border-collapse: collapse; border: none; width: 100%;margin-top: 20px; ">
            <tr style="width: 100%;">
                <td style="width: 25%;"></td>
                <td style="width: 25%;"></td>
                <td style="width: 25%; ">
                    <p style="margin-bottom: 10px">Sunshine Teahouse Pvt Ltd</p>
                    <p>Authorised Signature</p>
                </td>
                <td style="width: 25%;">
                    <img alt="Chaayos Free Desi Chai" src="$data.basePath/b2b_invoice_signature.png" width="100px"/><br/>
                </td>
            </tr>
        </table>



    </div>
</div>



</body>

</html>
