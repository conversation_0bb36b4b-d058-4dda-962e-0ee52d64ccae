<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head></head>
<body>
<div>
    <h3 style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Kitchen Warehouse Auto Day Close</h3>
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <tbody>
        <tr>
            #foreach ($autoDayCloseEntry in ["SUCCESSFUL_AUTO_DAYCLOSES","FAILED_AUTO_DAYCLOSES"])
            <td colspan="4" style="padding:5pt;text-align:left;">
                <p><b>$autoDayCloseEntry :</b> ($data.get($autoDayCloseEntry).size())</p>
            </td>
            #end
        </tr>
        </tbody>
    </table>
    <br>

    #foreach ($autoDayCloseEntry in ["SUCCESSFUL_AUTO_DAYCLOSES","FAILED_AUTO_DAYCLOSES"])
    #set( $autoDayCloseStatusList = $data.get($autoDayCloseEntry))
    <h3 style="text-align:left;margin-bottom:0;margin-top:5pt;font-weight:700;">$autoDayCloseEntry</h3>
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <thead>
        <tr>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Unit Id</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Unit Name</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Day Close Event</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Auto Day Close Status</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Day Close Message</th>
        </tr>
        </thead>
        <tbody>
        #foreach ($dayCloseEntry in $autoDayCloseStatusList)
        <tr>
            <td>$dayCloseEntry.unitBasicDetail.id</td>
            <td>$dayCloseEntry.unitBasicDetail.name</td>
            <td>#if($dayCloseEntry.dayCloseEventId) $dayCloseEntry.dayCloseEventId #else - #end</td>
            <td>$dayCloseEntry.autoDayCloseStatus</td>
            <td>$dayCloseEntry.autoDayCloseMessage</td>
        </tr>
        #end
        </tbody>
    </table>
    <br>
    #end
</div>
</body>
</html>