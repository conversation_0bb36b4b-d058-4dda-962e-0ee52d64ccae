<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
  <title>Invoice Details</title>
</head>
<body>

<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
  <div style="margin: 10px">
    <b>Vendor Id : </b>$data.invoice.vendor.id
    <br/>
    <b>Vehicle : </b>$data.invoice.vehicle.name
    <br/>
    <b>Additional Charges : </b>$data.mathTool.roundTo(4,$data.invoice.additionalCharges)
    <br/>
    <b>Total Amount : </b>$data.mathTool.roundTo(4,$data.invoice.totalAmount)
    <br/>
    <b>Total Tax : </b>$data.mathTool.roundTo(4,$data.invoice.totalTax)
    <br/>
    <b>Sending Unit : </b>$data.invoice.sendingUnit.name
    <br/>
    <b>Purchase Order Number : </b>$data.invoice.purchasedOrderNumber
    <br/>
    <br/>

  </div>
  <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
    <thead>
    <tr style="background: #c90;">
      <th style="padding:5pt;text-align:center;"><b>Sku Id</b></th>
      <th style="padding:5pt;text-align:center;"><b>Sku Name</b></th>
      <th style="padding:5pt;text-align:center;"><b>Uom</b></th>
      <th style="padding:5pt;text-align:center;"><b>Quantity</b></th>
      <th style="padding:5pt;text-align:center;"><b>Selling Price</b></th>
      <th style="padding:5pt;text-align:center;"><b>Total Tax</b></th>
    </tr>
    </thead>
    <tbody>
    #foreach( $item in $data.invoice.items )
    <tr>
      <td style="padding:5pt;text-align:center;">#if($item.sku.id) $item.sku.id #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.sku.name) $item.sku.name #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.uom) $item.uom #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.qty) $item.qty #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.sellPrice) $data.mathTool.roundTo(4,$item.sellPrice) #else - #end</td>
      <td style="padding:5pt;text-align:center;">#if($item.totalTax) $data.mathTool.roundTo(4,$item.totalTax) #else - #end</td>
    </tr>
    #end
    </tbody>
  </table>
  <div style="margin: 5px; padding: 5px;">

  </div>
</div>
</body>