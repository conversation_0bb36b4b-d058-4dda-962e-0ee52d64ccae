<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Lost Asset Notification</title>
</head>
<body>
<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
    <div style="text-align: left; padding:10px; font-size: 16px;">
        <p>Following Asset is notified to be lost during a stock taking event at $data.lostAsset.unitName ($data.lostAsset.unitId) on
            #if($data.lostAsset.stockTakeTime) $data.dateTool.format('d MMM, yyyy H:m:ss' ,$data.lostAsset.stockTakeTime) #end</p>
    </div>
    <div >
        <table border="1" class="center" style="width:70%;border-spacing: 0;border-color: black;">
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Asset Name </b> </td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.assetName</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Asset Id </b></td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.assetId</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Asset Tag </b> </td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.assetTag</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Asset Procurement Cost </b> </td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.procurementCost</td>
            </tr>
            #if($data.lostAsset.grId)
            <tr>
                <td style="padding:5pt;text-align:left;"><b>PO Id </b> </td>
                <td style="padding:5pt;text-align:left;">
                #if(!$data.lostAsset.poId)
                    ---
                    #else
                    $data.lostAsset.poId
                    #end
                </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>GR Id </b> </td>
                <td style="padding:5pt;text-align:left;">
                #if(!$data.lostAsset.grId)
                    ---
                    #else
                    $data.lostAsset.grId
                    #end
                    </td>
            </tr>
            #end
            #if($data.lostAsset.grId)
            <tr>
                <td style="padding:5pt;text-align:left;"><b>PR Id </b> </td>
                <td style="padding:5pt;text-align:left;">
                #if(!$data.lostAsset.prId)
                    ---
                    #else
                    $data.lostAsset.prId
                    #end
                    </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Invoice Id </b> </td>
                <td style="padding:5pt;text-align:left;">
                #if(!$data.lostAsset.invoiceId)
                    ---
                    #else
                    $data.lostAsset.invoiceId
                    #end
                    </td>
            </tr>
            #end
            <tr>
                #if(!$data.lostAsset.grId)
                <td style="padding:5pt;text-align:left;"><b>Expected Recovery Amount </b> </td>
                <td style="padding:5pt;text-align:left;">
                #if(!$data.lostAsset.expectedRecoveryAmount)
                    ---
                    #else
                    $data.lostAsset.expectedRecoveryAmount
                    #end
                    </td>
                #end
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Asset Creation Date </b></td>
                <td style="padding:5pt;text-align:left;">
                #if(!$data.lostAsset.creationDate)
                    ---
                    #else
                    $data.dateTool.format('d MMM, yyyy H:m:ss' ,$data.lostAsset.creationDate)
                    #end
                    </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>StockTake Time </b></td>
                <td style="padding:5pt;text-align:left;">
                #if(!$data.lostAsset.stockTakeTime)
                    ---
                    #else
                    $data.dateTool.format('d MMM, yyyy H:m:ss' ,$data.lostAsset.stockTakeTime)
                    #end
                    </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>StockTaking User </b></td>
                <td style="padding:5pt;text-align:left;">
                    #if(!$data.lostAsset.stockTakingUser)
                    ---
                    #else
                    $data.lostAsset.stockTakingUser
                    #end
                    </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Unit Id </b></td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.unitId</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Unit Address </b></td>
                <td style="padding:5pt;text-align:left;">
                    #if ($data.lostAsset.unitAddress.line1) $data.lostAsset.unitAddress.line1, #end
                    #if ($data.lostAsset.unitAddress.line2) $data.lostAsset.unitAddress.line2, #end
                    #if ($data.lostAsset.unitAddress.line3) $data.lostAsset.unitAddress.line3, #end
                    <br/> $data.lostAsset.unitAddress.city,
                    $data.lostAsset.unitAddress.state,
                    $data.lostAsset.unitAddress.country,
                    #if($data.lostAsset.unitAddress.zipCode)
                    $data.lostAsset.unitAddress.zipCode #end</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Area Manager </b></td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.areaManager</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Deputy Area Manager </b></td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.deputyAreaManager</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Asset Lost Approved By </b></td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.lossApprovedBy</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Event Id </b></td>
                <td style="padding:5pt;text-align:left;"> $data.lostAsset.eventId</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Last Stocktaking Time </b></td>
                <td style="padding:5pt;text-align:left;">
                    #if(!$data.lostAsset.lastStockTakeTime)
                    ---
                    #else
                    $data.dateTool.format('d MMM, yyyy H:m:ss' ,$data.lostAsset.lastStockTakeTime)
                    #end
                    </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Last StockTaking Unit </b></td>
                <td style="padding:5pt;text-align:left;">
                    #if(!$data.lostAsset.lastStockTakingUnit)
                    ---
                    #else
                    $data.lostAsset.lastStockTakingUnit
                    #end
                    </td>
            </tr>

        </table>

    </div>
</div>
</body>
</html>
