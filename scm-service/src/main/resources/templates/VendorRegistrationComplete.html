<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>New Vendor Overview</title>
</head>
<body>
<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
    <div style="text-align: center; padding:10px; font-size: 16px;">
        <p>VENDOR ID [$data.vendor.vendorId]</p>
    </div>
    <div >
        #if($data.diff)
        <div  style="margin: 5px; padding: 5px;">
            <h1>Changes Overview</h1>
            <table style="width: 100%;">
                <thead>
                <tr style="background: #c90;">
                    <th>Field Name</th>
                    <th>Old Value</th>
                    <th>New Value</th>
                </tr>
                </thead>
                <tbody>
                #foreach( $item in $data.keys )
                <tr style="background-color: #e6e6e6; margin-top: 10px; padding-left: 5px;">
                    <td style=" color: black; font-weight: bold; padding-left: 5px;">$item</td>
                    <td style="color: black; padding-left: 5px;">$data.diff.get($item).getKey()</td>
                    <td style=" color: black ; padding-left: 5px;">$data.diff.get($item).getValue()</td>
                </tr>
                #end

                </tbody>
            </table>

        </div>
        #end
        <h1>Basic Details</h1>
        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Vendor Id : </b> $data.vendor.vendorId</td>
                <td style="padding:5pt;text-align:left;"><b>Entity Name : </b> $data.vendor.entityName</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>First Name : </b> $data.vendor.firstName</td>
                <td style="padding:5pt;text-align:left;"><b>Last Name  : </b> $data.vendor.lastName</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Primary Contact : </b> $data.vendor.primaryContact</td>
                <td style="padding:5pt;text-align:left;"><b>Secondary Contact  : </b> $data.vendor.secondaryContact</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Primary Email : </b> $data.vendor.primaryEmail</td>
                <td style="padding:5pt;text-align:left;"><b>Secondary Email  : </b> $data.vendor.secondaryEmail</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Type : </b> $data.vendor.type</td>
                <td style="padding:5pt;text-align:left;"><b>Status  : </b> $data.vendor.status</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Requested By : </b> $data.vendor.requestedBy.name</td>
                <td style="padding:5pt;text-align:left;"><b>Disclaimer Accepted  : </b> $data.vendor.disclaimerAccepted</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Credit Days : </b>
                    #if(!$data.vendor.creditDays)
                        ---
                    #else
                        $data.vendor.creditDays
                    #end
                </td>
                <td style="padding:5pt;text-align:left;"><b>Company Id  : </b>
                    #if(!$data.vendor.companyId)
                    ---
                    #else
                    $data.vendor.companyId
                    #end
                </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Lead Time : </b>
                    #if(!$data.vendor.leadTime)
                    ---
                    #else
                    $data.vendor.leadTime
                    #end
                </td>
                <td style="padding:5pt;text-align:left;"><b>Tds  : </b>
                    #if(!$data.vendor.tds)
                    ---
                    #else
                    $data.vendor.tds
                    #end
                </td>
            </tr>
        </table>

        <h1>Company Details</h1>
        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Company Name : </b> $data.vendor.companyDetails.companyId</td>
                <td style="padding:5pt;text-align:left;"><b>Registered Name : </b> $data.vendor.companyDetails.registeredName</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Pan : </b> $data.vendor.companyDetails.pan</td>
                <td style="padding:5pt;text-align:left;"><b>Company Address  : </b>
                    $data.vendor.companyDetails.companyAddress.line1,
                    #if ($data.vendor.companyDetails.companyAddress.line2),$data.vendor.companyDetails.companyAddress.line2, #end
                    <br/> $data.vendor.companyDetails.companyAddress.city,
                    $data.vendor.companyDetails.companyAddress.state,
                    $data.vendor.companyDetails.companyAddress.country,
                    #if($data.vendor.companyDetails.companyAddress.zipCode),
                    $data.vendor.companyDetails.companyAddress.zipCode, #end
                    <br/>
                </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Update Time : </b> $data.dateTool.format('d MMM, yyyy H:m:s' , $data.vendor.companyDetails.updateTime)</td>
                <td style="padding:5pt;text-align:left;"><b>Business Type  : </b> $data.vendor.companyDetails.businessType</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Entity Type : </b> $data.vendor.companyDetails.entityType</td>
                <td style="padding:5pt;text-align:left;"><b>Credit Days  : </b> $data.vendor.companyDetails.creditDays</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Exempt Supplier : </b> $data.vendor.companyDetails.exemptSupplier</td>
                <td style="padding:5pt;text-align:left;"><b>Msme Registered  : </b> $data.vendor.companyDetails.msmeRegistered</td>
            </tr>


            <tr>
                <td style="padding:5pt;text-align:left;"><b>Msme Expiration Date  : </b>
                    #if(!$data.vendor.companyDetails.msmeExpirationDate)
                    ---
                    #else
                    $data.dateTool.format('d MMM, yyyy',$data.vendor.companyDetails.msmeExpirationDate)
                    #end
                </td>
            </tr>
        </table>

        <h1>Account Details</h1>
        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Account Number : </b> $data.vendor.accountDetails.accountNumber</td>
                <td style="padding:5pt;text-align:left;"><b>Ifsc Code : </b> $data.vendor.accountDetails.ifscCode</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Account Type : </b> $data.vendor.accountDetails.accountType</td>
                <td style="padding:5pt;text-align:left;"><b>Micr Code  : </b>
                    #if(!$data.vendor.accountDetails.micrCode)
                    ---
                    #else
                    $data.vendor.accountDetails.micrCode
                    #end
                </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Payment Blocked  : </b> $data.vendor.accountDetails.paymentBlocked</td>
                <td style="padding:5pt;text-align:left;"><b>Account Contact  : </b> $data.vendor.accountDetails.accountContact</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Account Contact Name : </b> $data.vendor.accountDetails.accountContactName</td>
                <td style="padding:5pt;text-align:left;"><b>Account Contact Email  : </b> $data.vendor.accountDetails.accountContactEmail</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Kind Of Account : </b> $data.vendor.accountDetails.kindOfAccount</td>
                <td style="padding:5pt;text-align:left;"><b>Update Time  : </b> $data.dateTool.format('d MMM, yyyy H:m:s',$data.vendor.accountDetails.updateTime)</td>
            </tr>

        </table>

        <h1>Dispatch Locations</h1>

        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            #foreach( $item in $data.vendor.dispatchLocations )
            <p>Location $velocityCount</p>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Dispatch Id : </b> $item.dispatchId</td>
                <td style="padding:5pt;text-align:left;"><b>Apply Tax : </b> $item.applyTax</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>City : </b> $item.city</td>
                <td style="padding:5pt;text-align:left;"><b>State  : </b> $item.state</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Country : </b> $item.country</td>
                <td style="padding:5pt;text-align:left;"><b>Address  : </b>

                    $item.address.line1,
                    #if ($item.address.line2),$item.address.line2, #end
                    <br/> $item.address.city,
                    $item.address.state,
                    $item.address.country,
                    #if($item.address.zipCode),
                    $item.address.zipCode, #end
                    <br/>
                </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;">

                    <b>Notification Type : </b>
                    #foreach($type in $item.notificationType)
                    $type,
                    #end
                </td>
                <td style="padding:5pt;text-align:left;"><b>Update Time  : </b> $item.updateTime</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Tin : </b>
                    #if(!$item.tin)
                        ---
                    #else
                        $item.tin
                    #end
                </td>
                <td style="padding:5pt;text-align:left;"><b>Gstin  : </b>
                    #if(!$item.gstin)
                    ---
                    #else
                    $item.gstin
                    #end
                </td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Gst Status : </b> $item.gstStatus</td>
                <td style="padding:5pt;text-align:left;"><b>Contact Email  : </b> $item.contactEmail</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Location Type : </b> $item.locationType</td>
                <td style="padding:5pt;text-align:left;"><b>Status  : </b> $item.status</td>
            </tr>
            #end
        </table>

    </div>
</div>
</body>
</html>
