<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head></head>
<body>
<div align="center">
	<h3 style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">PURCHASE ORDER</h3>
	<h3 style="text-align:center;margin-bottom:0;margin-top:5pt;">($data.company.name)</h3>
	<h3 style="text-align:center;margin-top:5pt;">CHAAYOS</h3>
	<h3 style="text-align:center;margin-top:5pt;font-weight:750;">$data.potype</h3>
	<table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
		<tbody>
		<tr>
			<td colspan="15" style="padding:5pt;text-align:left;">
				<p><b>Order Number:</b> $data.purchaseOrderData.receiptNumber</p>
				<p><b>Generation Date:</b> $data.dateTool.format('d MMM, yyyy',$data.purchaseOrderData.generationTime)</p>
			</td>
		</tr>
		<tr style="text-align: center;">
			<th style="padding:5pt;" colspan="2"></th>
			#if(!$data.hasIgst)
			<th style="padding:5pt;" colspan="3">Vendor</th>
			<th style="padding:5pt;" colspan="3">Dispatch From</th>
			<th style="padding:5pt;" colspan="3">Delivery To</th>
			#else
			<th style="padding:5pt;" colspan="3">Vendor</th>
			<th style="padding:5pt;" colspan="3">Dispatch From</th>
			<th style="padding:5pt;" colspan="2">Delivery To</th>
			#end
		</tr>
		<tr style="text-align: center;">
			#if(!$data.hasIgst)
			<th style="padding:5pt;" colspan="2">Company Name</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.entityName</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.locationName</td>
			<td style="padding:5pt;" colspan="3">$data.company.name ($data.deliveryUnit.name)</td>
			#else
			<th style="padding:5pt;" colspan="2">Company Name</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.entityName</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.locationName</td>
			<td style="padding:5pt;" colspan="2">$data.company.name ($data.deliveryUnit.name)</td>
			#end
		</tr>
		<tr style="text-align: center;">
			#if(!$data.hasIgst)
			<th style="padding:5pt;" colspan="2">Address</th>
			<td style="padding:5pt;" colspan="3">
				<p style="margin:0;">$data.vendorDetail.vendorAddress.line1
					#if ($data.vendorDetail.vendorAddress.line2), $data.vendorDetail.vendorAddress.line2 #end
					#if ($data.vendorDetail.vendorAddress.line3), $data.vendorDetail.vendorAddress.line3 #end
					#if ($data.vendorDetail.vendorAddress.city), $data.vendorDetail.vendorAddress.city #end
					#if ($data.vendorDetail.vendorAddress.state), $data.vendorDetail.vendorAddress.state #end
					#if ($data.vendorDetail.vendorAddress.country), $data.vendorDetail.vendorAddress.country #end
					#if ($data.vendorDetail.vendorAddress.zipcode), $data.vendorDetail.vendorAddress.zipcode #end
				</p>
			</td>
			<td style="padding:5pt;" colspan="3">
				<p style="margin:0;">$data.dispatchLocation.address.line1
					#if ($data.dispatchLocation.address.line2), $data.dispatchLocation.address.line2 #end
					#if ($data.dispatchLocation.address.line3), $data.dispatchLocation.address.line3 #end
					#if ($data.dispatchLocation.address.city), $data.dispatchLocation.address.city #end
					#if ($data.dispatchLocation.address.state), $data.dispatchLocation.address.state #end
					#if ($data.dispatchLocation.address.country), $data.dispatchLocation.address.country #end
					#if ($data.dispatchLocation.address.zipcode), $data.dispatchLocation.address.zipcode #end
				</p>
			</td>
			<td style="padding:5pt;" colspan="3">
				#set( $unit = $data.deliveryUnit)
				<p style="margin:0;"> $unit.address.line1
					#if ($unit.address.line2), $unit.address.line2 #end
					#if ($unit.address.line3), $unit.address.line3 #end
					#if ($unit.address.city), $unit.address.city #end
					#if ($unit.address.state), $unit.address.state #end
					#if ($unit.address.country), $unit.address.country #end
				</p>
			</td>
			#else
			<th style="padding:5pt;" colspan="2">Address</th>
			<td style="padding:5pt;" colspan="3">
				<p style="margin:0;">$data.vendorDetail.vendorAddress.line1
					#if ($data.vendorDetail.vendorAddress.line2), $data.vendorDetail.vendorAddress.line2 #end
					#if ($data.vendorDetail.vendorAddress.line3), $data.vendorDetail.vendorAddress.line3 #end
					#if ($data.vendorDetail.vendorAddress.city), $data.vendorDetail.vendorAddress.city #end
					#if ($data.vendorDetail.vendorAddress.state), $data.vendorDetail.vendorAddress.state #end
					#if ($data.vendorDetail.vendorAddress.country), $data.vendorDetail.vendorAddress.country #end
					#if ($data.vendorDetail.vendorAddress.zipcode), $data.vendorDetail.vendorAddress.zipcode #end
				</p>
			</td>
			<td style="padding:5pt;" colspan="3">
				<p style="margin:0;">$data.dispatchLocation.address.line1
					#if ($data.dispatchLocation.address.line2), $data.dispatchLocation.address.line2 #end
					#if ($data.dispatchLocation.address.line3), $data.dispatchLocation.address.line3 #end
					#if ($data.dispatchLocation.address.city), $data.dispatchLocation.address.city #end
					#if ($data.dispatchLocation.address.state), $data.dispatchLocation.address.state #end
					#if ($data.dispatchLocation.address.country), $data.dispatchLocation.address.country #end
					#if ($data.dispatchLocation.address.zipcode), $data.dispatchLocation.address.zipcode #end
				</p>
			</td>
			<td style="padding:5pt;" colspan="2">
				#set( $unit = $data.deliveryUnit)
				<p style="margin:0;"> $unit.address.line1
					#if ($unit.address.line2), $unit.address.line2 #end
					#if ($unit.address.line3), $unit.address.line3 #end
					#if ($unit.address.city), $unit.address.city #end
					#if ($unit.address.state), $unit.address.state #end
					#if ($unit.address.country), $unit.address.country #end
				</p>
			</td>
			#end
		</tr>
		<tr style="text-align: center;">
			#if(!$data.hasIgst)
			<th style="padding:5pt;" colspan="2">State</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.vendorAddress.state</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.state</td>
			<td style="padding:5pt;" colspan="3">$data.deliveryUnit.address.state</td>
			#else
			<th style="padding:5pt;" colspan="2">State</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.vendorAddress.state</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.state</td>
			<td style="padding:5pt;" colspan="2">$data.deliveryUnit.address.state</td>
			#end
		</tr>
		<tr style="text-align: center;">
			#if(!$data.hasIgst)
			<th style="padding:5pt;" colspan="2">State Code</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.vendorAddress.stateCode</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.stateCode</td>
			<td style="padding:5pt;" colspan="3">$data.deliveryUnit.location.state.code</td>
			#else
			<th style="padding:5pt;" colspan="2">State Code</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.vendorAddress.stateCode</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.stateCode</td>
			<td style="padding:5pt;" colspan="2">$data.deliveryUnit.location.state.code</td>
			#end
		</tr>
		<tr style="text-align: center;">
			#if(!$data.hasIgst)
			<th style="padding:5pt;" colspan="2">GSTIN</th>
			<td style="padding:5pt;" colspan="3">#if($data.dispatchLocation.gstin) $data.dispatchLocation.gstin
				#else $data.dispatchLocation.gstStatus #end</td>
			<td style="padding:5pt;" colspan="3">#if($data.dispatchLocation.gstin) $data.dispatchLocation.gstin
				#else $data.dispatchLocation.gstStatus #end</td>
			<td style="padding:5pt;" colspan="3">#if($data.deliveryUnit.tin) $data.deliveryUnit.tin #else N/A #end</td>
			#else
			<th style="padding:5pt;" colspan="2">GSTIN</th>
			<td style="padding:5pt;" colspan="3">#if($data.dispatchLocation.gstin) $data.dispatchLocation.gstin
				#else $data.dispatchLocation.gstStatus #end</td>
			<td style="padding:5pt;" colspan="3">#if($data.dispatchLocation.gstin) $data.dispatchLocation.gstin
				#else $data.dispatchLocation.gstStatus #end</td>
			<td style="padding:5pt;" colspan="2">#if($data.deliveryUnit.tin) $data.deliveryUnit.tin #else N/A #end</td>
			#end
		</tr>
		<tr style="text-align: center;">
			#if(!$data.hasIgst)
			<th style="padding:5pt;" colspan="2">Email</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.primaryEmail</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.contactEmail</td>
			<td style="padding:5pt;" colspan="3">$data.deliveryUnit.unitEmail</td>
			#else
			<th style="padding:5pt;" colspan="2">Email</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.primaryEmail</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.contactEmail</td>
			<td style="padding:5pt;" colspan="2">$data.deliveryUnit.unitEmail</td>
			#end
		</tr>
		<tr style="text-align: center;">
			#if(!$data.hasIgst)
			<th style="padding:5pt;" colspan="2">Phone</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.primaryContact</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.addressContact</td>
			<td style="padding:5pt;" colspan="3">$data.deliveryUnit.address.contact1 #if ($data.deliveryUnit.address.contact2), ${$data.deliveryUnit.address.contact2}#end</td>
			#else
			<th style="padding:5pt;" colspan="2">Phone</th>
			<td style="padding:5pt;" colspan="3">$data.vendorDetail.primaryContact</td>
			<td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.addressContact</td>
			<td style="padding:5pt;" colspan="2">$data.deliveryUnit.address.contact1 #if ($data.deliveryUnit.address.contact2), ${$data.deliveryUnit.address.contact2}#end</td>
			#end
		</tr>
		<tr>
			<th style="padding:5pt;text-align:center;">S.No.</th>
			<th style="padding:5pt;text-align:center;">Product</th>
			<th style="padding:5pt;text-align:center;">HSN</th>
			<th style="padding:5pt;text-align:center;">Total Qty</th>
			<th style="padding:5pt;text-align:center;">Packaging Qty</th>
			<th style="padding:5pt;text-align:center;">Packaging</th>
			<th style="padding:5pt;text-align:center;">Price</th>
			<th style="padding:5pt;text-align:center;">Taxable Value</th>
			#if(!$data.hasIgst)
			<th style="padding:5pt;text-align:center;">CGST</th>
			<th style="padding:5pt;text-align:center;">SGST</th>
			#else
			<th style="padding:5pt;text-align:center;">IGST</th>
			#end
			#if($data.hasOtherTaxes)
			<th style="padding:5pt;text-align:center;">Other Taxes</th>
			#end
		</tr>
		#foreach( $item in $data.purchaseOrderData.purchaseOrderItemDatas )
		<tr>
			<td style="padding:5pt;text-align:center;">$velocityCount</td>
			<td style="padding:5pt;text-align:center;">$item.skuName</td>
			<td style="padding:5pt;text-align:center;">$item.hsnCode</td>
			<td style="padding:5pt;text-align:center;">$item.requestedQuantity [$item.unitOfMeasure]</td>
			<td style="padding:5pt;text-align:center;">$item.packagingQuantity</td>
			<td style="padding:5pt;text-align:center;">$item.packagingName</td>
			<td style="padding:5pt;text-align:center;">$data.mathTool.roundTo(2,$item.unitPrice)</td>
			<td style="padding:5pt;text-align:center;">$data.mathTool.roundTo(2,$item.totalCost)</td>
			#if(!$data.hasIgst)
			<td style="padding:5pt;text-align:center;">
				#if($item.cgstValue)
				$data.mathTool.roundTo(2,$item.cgstValue)($data.mathTool.roundTo(2,$item.cgstPercentage)%)
				#else
				0.00
				#end
			</td>

			<td style="padding:5pt;text-align:center;">
				#if($item.sgstValue)
				$data.mathTool.roundTo(2,$item.sgstValue)($data.mathTool.roundTo(2,$item.sgstPercentage)%)
				#else
				0.00
				#end
			</td>
			#else
			<td style="padding:5pt;text-align:center;">
				#if($item.igstValue)
				$data.mathTool.roundTo(2,$item.igstValue)($data.mathTool.roundTo(2,$item.igstPercentage)%)
				#else
				0.00
				#end
			</td>
			#end
			#if($data.hasOtherTaxes)
			<td style="padding:5pt;text-align:center;">
				#if($item.otherTaxes)
				$data.mathTool.roundTo(2,$item.otherTaxes)
				#else
				0.00
				#end
			</td>
			#end
		</tr>
		#end
		<tr>
			<td style="padding:5pt;" colspan="7">Order Total (in words): <b>$data.amountInWords</b></td>
			<td style="padding:5pt;text-align:right;" colspan="4">
				<p><b>Total Cost:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.purchaseOrderData.billAmount)</p>
				<p><b>Total Taxes:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.purchaseOrderData.totalTaxes)</p>
				<p><b>Total Amount:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.purchaseOrderData.paidAmount)</p>
			</td>
		</tr>
		<tr>
			<td style="padding:5pt; text-align:center;" colspan="7">For $data.company.name</td>
			<td style="padding:5pt; text-align:center;" colspan="4">Confirmation By Seller</td>
		</tr>
		<tr>
			<td style="padding:5pt; text-align:center;" colspan="6">Created By $data.createdBy</td>
			<td style="padding:5pt; text-align:center;" colspan="5">Approved By $data.approvedBy </td>
		</tr>
		<tr>
			<td style="padding:5pt; text-align:left;" colspan="6">Order Raised By: $data.createdBy #if($data.employeeBasicDetail.contactNumber)($data.employeeBasicDetail.contactNumber)#end</td>
			#if($data.updatedStatus == "CANCELLED")
			<td style="padding:5pt; text-align:left;" colspan="5">#if($data.lastUpdatedBy)Cancelled By: $data.lastUpdatedBy #end</td>
			#end
			#if($data.updatedStatus == "REJECTED")
			<td style="padding:5pt; text-align:left;" colspan="5">#if($data.lastUpdatedBy)Rejected By: $data.lastUpdatedBy #end</td>
			#end
		</tr>
		<tr>
			<td style="padding:3pt; text-align:left;" colspan="11">
				<p>Comments:&nbsp;
				#if($data.purchaseOrderData.comment)
					<b style="color: red;">$data.purchaseOrderData.comment</b>
				#else
					<b style="color: red;">NA</b>
				#end</p>

			</td>
		</tr>
		<tr>
			<td style="padding:5pt; text-align:left;" colspan="11">
				<h4>Terms & Conditions:</h4>
				<ol>
					<li>Payment after delivery against invoice as per agreed payment terms</li>
					<li>Our GSTIN number is mandatory in all invoices if details are not mention then no payment will be processed</li>
					<li>Delivery of the  goods/materials must be  completed within 14 days from the date of issue of the order </li>
					<li>Invoice should be issued at the time of delivery or dispatched</li>
					<li>In case of unregistered supplier no tax to be paid. If you are registered then contact Chaayos team to update the same.</li>
					<li>Separate Invoice/Bill of Supply should be issued for taxable items and non-taxable items </li>
					<li>If  GST registration is applied then tax on purchase to be paid after receipt of Invoice with GST Details </li>
					#if($data.updatedStatus != "REJECTED")
					<li>Above Purchase order will get expired on $data.dateTool.format('d MMM, yyyy',$data.purchaseOrderData.expiryDate) , kindly fulfill order before expiry date</li>
					#end
				</ol>
			</td>
		</tr>
		</tbody>
	</table>
	<p style="font-size: 10.0pt;line-height: 115%; font-family: 'Cambria', serif; color: black;">
		This is a computer generated order hence signature is not required.
	</p>
    <table>
    <tr>
        <td class="button">
            <a href="${data.disclaimerLink}">CLICK HERE TO READ DISCLAIMNER</a>
        </td>
    </tr>
	</table>
	<p style="font-size: 10.0pt;line-height: 115%; font-family: 'Cambria', serif; color: black;">
		#set($unit = $data.deliveryUnit)
		<span>
			Reg. Address: $data.company.registeredAddress.line1,
					$data.company.registeredAddress.city,
					$data.company.registeredAddress.state,
					$data.company.registeredAddress.country,
					PIN: $data.company.registeredAddress.zipCode
		</span>
	</p>
</div>
</body>
</html>