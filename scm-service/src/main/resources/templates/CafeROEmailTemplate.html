<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Title</title>
</head>
<body>
<!--      header      -->
<!--<div style="background-color: #213141; padding: 10px; text-align: center;">
    <img src="http://cdn.lmitassets.com/gograbon/images/merchant/chaayos.jpeg" />
</div>-->

<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
    <div style="text-align: center; padding:10px; font-size: 16px;">
        <p>SUNSHINE TEAHOUSE PVT LTD.<br/>MEMORANDUM ORDER</p>
    </div>
    #if ($data.referenceOrderData)
    #if ($data.referenceOrderData.refOrderSource != "FOUNTAIN9_DATA_SOURCE_ORDERING")
    <div style="margin-bottom:20px;padding:10px; background-color: #e2e2e2; vertical-align:top;">
        <p style="text-align: center; margin-bottom: 10px;"><b>REQUEST ORDER RAISE BY WAREHOUSE/KITCHEN</b></p>
    </div>
    #end
    #end
    <div style="margin-bottom:30px;">
        <div style=" background-color: #e2e2e2;padding: 5px;">
            #set( $unit = $data.unitMap)
            <p style="margin:0;"><b>Chaayos $unit.name</b><br> $unit.address.line1
                #if ($unit.address.line2), $unit.address.line2 #end
                #if ($unit.address.line3), $unit.address.line3 #end
                #if ($unit.address.locality), $unit.address.locality #end
                #if ($unit.address.city), $unit.address.city #end
                #if ($unit.address.state), $unit.address.state #end
                #if ($unit.address.country), $unit.address.country #end
            </p>
            <p style="margin:0;"><b>Contact:</b> $unit.address.contact1 #if ($unit.address.contact2),
                ${unit.address.contact2}#end, <b>Email:</b> ${unit.unitEmail}</p>
            <p style="margin:0;">Order Type : #if ($data.referenceOrderData.refOrderSource == "FOUNTAIN9_DATA_SOURCE_ORDERING") Fountain9
                                              #elseif($data.referenceOrderData.refOrderSource == "CHAAYOS_REGULAR_ORDERING") REGULAR
                                              #else ADHOC
                                              #end</p>
        </div>

        #if ($data.orderEvent)
        <div style=" background-color: #e2e2e2;padding: 5px;">
            <p style="margin:0;"><b>Brand : </b>#if ($data.orderEvent.brand == "BOTH") Chaayos & Ghee And Turmeric #else $data.orderEvent.brand #end</p>
            <p style="margin:0;"><b>Ordering Days : </b>$data.orderEvent.orderingDays </p>
            <p style="margin:0;"><b>Regular ordering Event Id : </b>$data.orderEvent.eventId </p>
            <p style="margin:0;"><b>Generated By : </b>$data.generatedBy ($data.referenceOrderData.generatedBy) </p>
        </div>
        #end

        #foreach( $order in $data.requestOrderMap)
        <div style="padding:15px 5px;">
            <b>RO Id:</b> $order.id <b>Fulfillment UnitId:</b> $order.fulfillmentUnit.id <b>Fulfillment Unit Name:</b>($order.fulfillmentUnit.name) <b>Fulfillment Date:</b>$date.format('medium', $order.fulfillmentDate)
            <span style="float: right;"><b>Request time:</b>$date.format('medium', $order.generationTime)</span>
        </div>
        <div>
            #if ($order.comment)
            <p style="margin:0;"><b>RO Comment : </b>$order.comment</p>
            #end
        </div>

        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            <thead>
            <tr style="background: #c90;">
                <th style="padding:5pt;text-align:center;"><b>Product Name (UOM)</b></th>
                <th style="padding:5pt;text-align:center;"><b>Requested#if(${data.isReceiving})/Received#end Quantity</b></th>
                #if ($data.referenceOrderData == $null)
                <th style="padding:5pt;text-align:center;"><b>Last RO(#if ($data.lastRoData.referenceOrderData) Fountain9 #else ADHOC #end) Quantity</b></th>
                <th style="padding:5pt;text-align:center;"><b>Last RO Fulfilment Date</b></th>
                <th style="padding:5pt;text-align:center;"><b>Reason</b></th>
                <th style="padding:5pt;text-align:center;"><b>Comment</b></th>
                #else
                <th style="padding:5pt;text-align:center;"><b>Predicted</b></th>
                <th style="padding:5pt;text-align:center;"><b>Suggested</b></th>
                <th style="padding:5pt;text-align:center;"><b>Difference</b></th>
                #end
            </tr>
            </thead>
            <tbody>
            #foreach( $item in $order.requestOrderItems )
            <tr>
                <td style="padding:5pt;text-align:center;">$item.productName ($item.unitOfMeasure)</td>
                <td style="padding:5pt;text-align:center;">$item.requestedAbsoluteQuantity</td>
<!--                <td style="padding:5pt;text-align:center;">$item.unitOfMeasure</td>-->
                #if ($data.referenceOrderData == $null)
                <td style="padding:5pt;text-align:center;">#if ($data.lastRoMap.get($item.productId)) $data.lastRoMap.get($item.productId) #else NA #end</td>
                <td style="padding:5pt;text-align:center;">#if ($data.lastRoData) $data.lastRoData.fulfillmentDate #else NA #end</td>
                <td style="padding:5pt;text-align:center;">#if ($item.reason) $item.reason #else - #end</td>
                <td style="padding:5pt;text-align:center;">#if ($item.comment) $item.comment #else - #end</td>
                #else
                <th style="padding:5pt;text-align:center;"><b>#if ($item.predictedQuantity) $data.mathTool.roundTo(4,$item.predictedQuantity) #else - #end</b></th>
                <th style="padding:5pt;text-align:center;"><b>#if ($item.suggestedQuantity) $data.mathTool.roundTo(4,$item.suggestedQuantity) #else - #end</b></th>
                <th style="padding:5pt;text-align:center;"><b>
                    #if($item.diffQuantity) $data.mathTool.roundTo(4,$item.diffQuantity) #else - #end</b></th>
                #end
            </tr>
            #end
            </tbody>
        </table>
        #end
    </div>
    #if ($data.referenceOrderData)
    #if ($data.referenceOrderData.refOrderSource != "FOUNTAIN9_DATA_SOURCE_ORDERING")
    <div style="margin-top: 50px;"><b>Note:</b> This Order is Raised By Kitchen/Warehouse.</div>
    #end
    #end
</div>
</body>
</html>