<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Changes Overview</title>
</head>
<body>


<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
    <div style="padding: 15px 5px;">
        <b>
            $data.object Id : $data.Id
        </b>
    </div>

    <div  style="margin: 5px; padding: 5px;">
        <table style="width: 100%;">
            <thead>
            <tr style="background: #c90;">
                <th>Field Name</th>
                #if($data.isNew == false)
                <th>Old Value</th>
                #end
                <th>New Value</th>
            </tr>
            </thead>
            <tbody>
            #foreach( $item in $data.keys )
            <tr style="background-color: #e6e6e6; margin-top: 10px; padding-left: 5px;">
                <td style=" color: black; font-weight: bold; padding-left: 5px;">$item</td>
                #if($data.isNew == false)
                <td style="color: black; padding-left: 5px;">$data.diffs.get($item).getKey()</td>
                #end
                <td style=" color: black ; padding-left: 5px;">$data.diffs.get($item).getValue()</td>
            </tr>
            #end

            </tbody>
        </table>

    </div>
</div>
</body>
