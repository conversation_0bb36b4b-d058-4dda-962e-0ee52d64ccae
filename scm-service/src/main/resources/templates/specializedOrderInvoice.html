<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Title</title>
    <style type="text/css">
        * {
            margin-top: -13px;
            margin-left: 0px;
            font-size: 11px;
        }

        .table {
            width: 100%;
        }

        .table thead {
            border-bottom: #ccc 2px solid;
        }

        .table td {
            border-top: #ccc 1px solid;
            /*padding: 6px 2px;*/
        }

        .table thead td {
            font-weight: bold;
        }

        .table tr:nth-child(2n) td {
            background: #f8f8f8;
        }

        .table tr:last-child td {
            font-weight: bold;
        }

    </style>
</head>
<body>

<div class="row" id="printSection">
    <div class="col s12">
        <table style="border-collapse: collapse; border: 1px solid black;" cellpadding="0cm 5.4pt">
            <tbody style="text-align: center;">
            <tr style="font-size: 10.0pt;  line-height: 115%; font-family: 'Cambria', serif; color: black;">
                <td colspan="12" style="text-align: center ; border: 1px solid black;">$data.vendorDetail.entityName
                </td>
            </tr>
            <tr style="font-size: 10.0pt; border: 1px solid black; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                <td style="border: 1px solid black;" colspan="12"> $data.vendorDetail.vendorAddress.line1,
                    #if ($data.vendorDetail.vendorAddress.line2),$data.vendorDetail.vendorAddress.line2, #end
                    <br/> $data.vendorDetail.vendorAddress.city,
                    $data.vendorDetail.vendorAddress.state,
                    $data.vendorDetail.vendorAddress.country,
                    $data.vendorDetail.vendorAddress.zipcode
                    <br/>
                </td>
            </tr>
            <br/>
            <tr style="font-size: 10.0pt; border: 1px solid black; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                <td style="border: 1px solid black;" colspan="12">CIN :-
                    #if($data.vendorDetail.companyDetails.cin)$data.vendorDetail.companyDetails.cin #else NA #end
                </td>
            </tr>
            </br>
            <em>
                <tr style="font-family: 'Cambria', serif; border: 1px solid black;">
                    <td style="border: 1px solid black;" colspan="12">$data.invoiceId </td>
                </tr>
            </em>
            <br/>
            <tr style="font-size: 13.0pt; line-height: 115%; border: 1px solid black; font-family: 'Cambria', serif;">
                <td style="border: 1px solid black;" colspan="12">
                    GSTIN :-
                    #if ($data.vendorDispatchLocation.gstin)
                    $data.vendorDispatchLocation.gstin
                    #else
                    -
                    #end
                </td>
            </tr>
            </tbody>
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid; font-weight: bold;">
                <td style="border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="5">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Billing Address</span></b>
                    </p>
                </td>
                <td style="border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="5">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Shipping Address</span></b>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid; font-weight: bold;">
                <td style="border: solid windowtext 1.0pt; border-top: 1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="5">
                    <span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                       <b> $data.company.name <br/>
              $data.unitData.address.line1,
		#if ($data.unitData.address.line2),$data.unitData.address.line2, #end
                <br/> $data.unitData.address.city,
						$data.unitData.address.state,
                        $data.unitData.address.country,
						$data.unitData.address.zipCode,
                           $data.unitData.tin
                <br/>
                           </b>
            </span>

                </td>
                <td style="border: solid windowtext 1.0pt; border-top: 1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="5"
                >
                     <span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                         <b>
                        $data.company.name <br/>
                $data.unitData.address.line1,
		#if ($data.unitData.address.line2),$data.unitData.address.line2, #end
                <br/> $data.unitData.address.city,
						$data.unitData.address.state,
                        $data.unitData.address.country,
						$data.unitData.address.zipCode

                <br/>
                              </b>
            </span>
                </td>
            </tr>
            </tbody>
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style=" border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial;
                padding: 0cm 5.4pt; height: 12.00pt; " colspan="3">
                    <span style="text-align: left;"><b>State : </b>  $data.unitData.location.state.name   </span><br>
                    <span style="text-align: left;"><b>State code :</b> $data.unitData.location.state.code </span><br>
                    <span style="text-align: left;"><b>GSTIN/Unique ID :</b> $data.unitData.tin  </span>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial;
                padding: 0cm 5.4pt; height: 12.00pt; " colspan="4">
                    <span style="text-align: left;"><b>Vendor State :</b>  $data.vendorDetail.vendorAddress.state  </span> <br>
                    <span style="text-align: left;"> <b>Vendor State Code :</b> $data.vendorDetail.vendorAddress.stateCode </span><br>
                    <span style="text-align: left;"><b>Vendor State GSTIN :</b> #if($data.vendorDispatchLocation.gstin) $data.vendorDispatchLocation.gstin
                    #else -
                    #end
                    </span>
                </td>
                <td style="border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial;
                 padding: 0cm 5.4pt; height: 12.00pt; " colspan="3">
                   <!-- <span style="text-align: left;"><b>Tax Invoice :</b>  STPL/2023/MILK/$data.gr.id   </span><br>-->
                    <span style="text-align: left;"><b>Tax Invoice :</b>  $data.invoiceId  </span><br>
                    <span style="text-align: left;"> <b>Date :</b> $data.dateTool.get('yyyy-M-d')  </span><br>
                    <span style="text-align: left;"><b>Place Of Supply :</b> $data.unitData.address.state</span>

                </td>

            </tr>

            </tbody>
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid; font-weight: bold;">
                <td style=" border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sr No.</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Product Description</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN Code </span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Qty</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">UOM </span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Price</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span>
                    </p>
                </td>
                <td style="border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Tax %</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Tax Amount</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Amount</span>
                    </p>
                </td>
            </tr>
            #foreach( $item in $data.gr.goodsReceivedItems )
            <tr style="height: 12.00pt; page-break-inside: avoid; border-bottom:1pt solid windowtext; ">
                <td style=" border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$velocityCount</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.skuName</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.hsnCode</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.receivedQuantity</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.uom</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.unitPrice</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.mathTool.sub($item.calculatedAmount,$item.taxAmount))</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    #foreach( $tax in $item.taxes )
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$tax.percentage % ($tax.code), </span>
                    </p>
                    #end
                </td>
                <td style="border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.taxAmount</span>
                    </p>
                </td>
                <td style=" border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.calculatedAmount</span>
                    </p>
                </td>
            </tr>
            #end
            <tr>
                <td style=" border-top: 2pt solid windowtext; border-left: 2pt solid windowtext; border-bottom: 2pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"></td>
                <td style=" border-top: 2pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 2pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"></td>
                <td style=" border-top: 2pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 2pt solid windowtext;  padding: 0cm 5.4pt; height: 12.00pt;">
                    Total
                </td>
                <td style=" border-top: 2pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 2pt solid windowtext;  padding: 0cm 5.4pt; height: 12.00pt;"></td>
                <td style="  border-top: 2pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 2pt solid windowtext;  padding: 0cm 5.4pt; height: 12.00pt;"></td>
                <td style="  border-top: 2pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 2pt solid windowtext;  padding: 0cm 5.4pt; height: 12.00pt;"></td>
                <td style="  border-top: 2pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 2pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    $data.mathTool.roundTo(2,$data.mathTool.sub($data.gr.totalAmount,$data.totalTax))
                </td>
                <td style=" border-top: 2pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 2pt solid windowtext;  height: 12.00pt;"></td>
                <td style=" border-top: 2pt solid windowtext; border-left: 1pt solid windowtext; border-right: 1pt solid windowtext; border-bottom: 2pt solid windowtext;  padding: 0cm 5.4pt; height: 12.00pt;">
                    $data.mathTool.roundTo(2,$data.totalTax)
                </td>
                <td style=" border-top: 2pt solid windowtext; border-right: 2pt solid windowtext; border-bottom: 2pt solid windowtext;  padding: 0cm 5.4pt; height: 12.00pt;">
                    $data.gr.totalAmount
                </td>

            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style=" border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial;
                  height: 12.00pt; " colspan="5">
                    <span style="text-align: left; margin-left: 0px;"><b>Vendor Bank Details : </b>  -   </span><br>
                    <span style="text-align: left; margin-left: 0px;"><b>Account Number : </b> $data.vendorDetail.accountDetails.accountNumber  </span><br>
                    <span style="text-align: left; margin-left: 0px;"><b>IFFC code :</b> $data.vendorDetail.accountDetails.ifscCode  </span><br>
                    <span style="text-align: left; margin-left: 0px;"><b>Account Contact  :</b> $data.vendorDetail.accountDetails.accountContact </span>
                </td>
                <td style="border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial;
                 " colspan="5">
                    <span style="text-align: left; margin-left: 0px;"><b>CGST :</b> $data.totalCGST   </span><br>
                    <span style="text-align: left;  margin-left: 0px;"><b>SGST :</b> $data.totalSGST</span><br>
                    <span style="text-align: left;margin-left: 0px;"><b>ROUND OFF :</b> $data.mathTool.roundTo(2,$data.roundOffValue)</span>

                </td>

            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style=" border: 1pt solid windowtext; text-align: center; font-weight: bold; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="12">
                    Grand Total : $data.finalAmount
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style=" border: 1pt solid windowtext; text-align: center; font-weight: bold; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="12">
                    Amount in Words : $data.totalAmountInWords
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style=" border: 1pt solid windowtext; text-align: center; font-weight: bold; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="12">
                    <br><br>
                    For $data.vendorDetail.entityName
                    <br><br><br>
                    Authorised Signatory
                </td>
            </tr>
            </tbody>
        </table>
        <br/>
    </div>
</div>

</body>
</html>
