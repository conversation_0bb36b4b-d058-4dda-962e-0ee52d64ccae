<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head></head>
<body>
<div align="center">
    #if($data.isInvoiceTemplate == false && $data.isProvisional== true)
       <div style="width:100%; background-color: red; padding:20px">
           <h3 style="text-align:center; color:white" > Provisional Work Order, Approval Required.</h3>
       </div>
      #end
    #if($data.isInvoiceTemplate == false && $data.isProvisional== false)
    <div style="width:100%; background-color: green; padding:20px">
        <h3 style="text-align:center; color:white">Approved Work Order</h3>
    </div>
    #end

    #if($data.isProvisional==true)
    <h3 style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">PROVISIONAL WORK ORDER</h3>
    #else
    <h3 style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">WORK ORDER</h3>
    #end

    <h3 style="text-align:center;margin-top:5pt;">$data.company</h3>
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <tbody>
        <tr>
            <td colspan="9" style="padding:5pt;text-align:left;">
                <p><b>Order Number:</b> $data.serviceOrderData.id</p>
                <p><b>Generation Date:</b> $data.dateTool.format('d MMM, yyyy',$data.serviceOrderData.generationTime)</p>
                <p><b>Service Order Type:</b> $data.serviceOrderData.type</p>
            </td>
        </tr>
        <tr style="text-align: center;">
            <th style="padding:5pt;" colspan="2"></th>
            <th style="padding:5pt;" colspan="4">Vendor</th>
            <th style="padding:5pt;" colspan="3">Served From</th>
        </tr>
        <tr style="text-align: center;">
            <th style="padding:5pt;" colspan="2">Company Name</th>
            <td style="padding:5pt;" colspan="4">$data.vendorDetail.entityName</td>
            <td style="padding:5pt;" colspan="3">$data.dispatchLocation.locationName</td>
        </tr>
        <tr style="text-align: center;">
            <th style="padding:5pt;" colspan="2">Address</th>
            <td style="padding:5pt;" colspan="4">
                <p style="margin:0;">$data.vendorDetail.vendorAddress.line1
                    #if ($data.vendorDetail.vendorAddress.line2), $data.vendorDetail.vendorAddress.line2 #end
                    #if ($data.vendorDetail.vendorAddress.line3), $data.vendorDetail.vendorAddress.line3 #end
                    #if ($data.vendorDetail.vendorAddress.city), $data.vendorDetail.vendorAddress.city #end
                    #if ($data.vendorDetail.vendorAddress.state), $data.vendorDetail.vendorAddress.state #end
                    #if ($data.vendorDetail.vendorAddress.country), $data.vendorDetail.vendorAddress.country #end
                    #if ($data.vendorDetail.vendorAddress.zipcode), $data.vendorDetail.vendorAddress.zipcode #end
                </p>
            </td>
            <td style="padding:5pt;" colspan="3">
                <p style="margin:0;">$data.dispatchLocation.address.line1
                    #if ($data.dispatchLocation.address.line2), $data.dispatchLocation.address.line2 #end
                    #if ($data.dispatchLocation.address.line3), $data.dispatchLocation.address.line3 #end
                    #if ($data.dispatchLocation.address.city), $data.dispatchLocation.address.city #end
                    #if ($data.dispatchLocation.address.state), $data.dispatchLocation.address.state #end
                    #if ($data.dispatchLocation.address.country), $data.dispatchLocation.address.country #end
                    #if ($data.dispatchLocation.address.zipcode), $data.dispatchLocation.address.zipcode #end
                </p>
            </td>
        </tr>
        <tr style="text-align: center;">
            <th style="padding:5pt;" colspan="2">State</th>
            <td style="padding:5pt;" colspan="4">$data.vendorDetail.vendorAddress.state</td>
            <td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.state</td>
        </tr>
        <tr style="text-align: center;">
            <th style="padding:5pt;" colspan="2">State Code</th>
            <td style="padding:5pt;" colspan="4">$data.vendorDetail.vendorAddress.stateCode</td>
            <td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.stateCode</td>
        </tr>
        <tr style="text-align: center;">
            <th style="padding:5pt;" colspan="2">GSTIN</th>
            <td style="padding:5pt;" colspan="4">#if($data.dispatchLocation.gstin) $data.dispatchLocation.gstin
                #else $data.dispatchLocation.gstStatus #end
            </td>
            <td style="padding:5pt;" colspan="3">#if($data.dispatchLocation.gstin) $data.dispatchLocation.gstin
                #else $data.dispatchLocation.gstStatus #end
            </td>
        </tr>
        <tr style="text-align: center;">
            <th style="padding:5pt;" colspan="2">Email</th>
            <td style="padding:5pt;" colspan="4">$data.vendorDetail.primaryEmail</td>
            <td style="padding:5pt;" colspan="3">$data.dispatchLocation.contactEmail</td>
        </tr>
        <tr style="text-align: center;">
            <th style="padding:5pt;" colspan="2">Phone</th>
            <td style="padding:5pt;" colspan="4">$data.vendorDetail.primaryContact</td>
            <td style="padding:5pt;" colspan="3">$data.dispatchLocation.address.addressContact</td>
        </tr>
        </tbody>
        </table>
        <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <tbody>
        <tr>
            <th style="padding:5pt;text-align:center;">S.No.</th>
            <th style="padding:5pt;text-align:center;">Unit Name</th>
            <th style="padding:5pt;text-align:center;">Product (SAC Code)</th>
            <th style="padding:5pt;text-align:center;">Description</th>
            <th style="padding:5pt;text-align:center;">Total Qty</th>
            <th style="padding:5pt;text-align:center;">UOM</th>
            <th style="padding:5pt;text-align:center;">Price</th>
            <th style="padding:5pt;text-align:center;">Taxable Value</th>
            <th style="padding:5pt;text-align:center;">Total Tax</th>
            #if ($data.isDept)
            <th style="padding:5pt;text-align:center;">Department</th>
            #end
        </tr>
        #foreach( $item in $data.serviceOrderData.serviceOrderItemDataList )
        <tr>
            <td style="padding:5pt;text-align:center;">$velocityCount</td>
            <td style="padding:5pt;text-align:center;">$item.businessCostCenterName</td>
            <td style="padding:5pt;text-align:center;">$item.costElementName ($item.ascCode)</td>
            <td style="padding:5pt;text-align:center;">$item.serviceDescription</td>
            <td style="padding:5pt;text-align:center;">$item.requestedQuantity</td>
            <td style="padding:5pt;text-align:center;">[$item.unitOfMeasure]</td>
            <td style="padding:5pt;text-align:center;">$data.mathTool.roundTo(2,$item.unitPrice)</td>
            <td style="padding:5pt;text-align:center;">$data.mathTool.roundTo(2,$item.totalCost)</td>
            <td style="padding:5pt;text-align:center;">
                $data.mathTool.roundTo(2,$item.totalTax)($data.mathTool.roundTo(2,$item.taxRate)%)
            </td>
            #if ($data.isDept)
            <td style="padding:5pt;text-align:center;">$data.departmentInfo.get($item.id)</td>
            #end
        </tr>
        #end
        <tr>
            <td style="padding:5pt;" colspan="6">Order Total (in words): <b>$data.amountInWords</b></td>
            <td style="padding:5pt;text-align:right;" colspan="3">
                <p><b>Total Cost:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.serviceOrderData.totalCost)</p>
                <p><b>Total Taxes:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.serviceOrderData.totalTaxes)</p>
                <p><b>Total Amount:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.serviceOrderData.totalAmount)</p>
            </td>
        </tr>
        <tr>
            <td style="padding:5pt;" colspan="5">Created By <b>$data.createdBy</b></td>
            <td style="padding:5pt;text-align:right;" colspan="4">Approved By <b>$data.approvedBy</b></td>
        </tr>
        <!-- <tr>
            <td style="padding:5pt; text-align:center;" colspan="9">Order Raised By: $data.createdBy</td>
        </tr>
        <tr>
            <td style="padding:5pt; text-align:center;" colspan="3">City</td>
            <td style="padding:5pt; text-align:center;" colspan="3">SUNSHINE TEAHOUSE PVT LTD<br></br>For all Cafes</td>
            <td style="padding:5pt; text-align:center;" colspan="3">DKC TEA HOUSE PVT LTD<br></br>For all other units like
                Warehouse/Kitchen
            </td>
        </tr>
        <tr>
            <td style="padding:5pt; text-align:center;" colspan="3">Delhi</td>
            <td style="padding:5pt; text-align:center;" colspan="3">07AARCS3853M1ZL</td>
            <td style="padding:5pt; text-align:center;" colspan="3">07AAGCD4049P1Z7</td>
        </tr>
        <tr>
            <td style="padding:5pt; text-align:center;" colspan="3">Uttar Pradesh</td>
            <td style="padding:5pt; text-align:center;" colspan="3">09AARCS3853M1ZH</td>
            <td style="padding:5pt; text-align:center;" colspan="3"></td>
        </tr>
        <tr>
            <td style="padding:5pt; text-align:center;" colspan="3">Haryana</td>
            <td style="padding:5pt; text-align:center;" colspan="3">06AARCS3853M1ZN</td>
            <td style="padding:5pt; text-align:center;" colspan="3">06AAGCD4049P1Z9</td>
        </tr>
        <tr>
            <td style="padding:5pt; text-align:center;" colspan="3">Mumbai</td>
            <td style="padding:5pt; text-align:center;" colspan="3">27AARCS3853M1ZJ</td>
            <td style="padding:5pt; text-align:center;" colspan="3">27AAGCD4049P1Z5</td>
        </tr>
        <tr>
            <td style="padding:5pt; text-align:center;" colspan="3">Chandigarh</td>
            <td style="padding:5pt; text-align:center;" colspan="3">04AARCS3853M2ZQ</td>
            <td style="padding:5pt; text-align:center;" colspan="3"></td>
        </tr>
        <tr>
            <td style="padding:5pt; text-align:center;" colspan="3">Punjab</td>
            <td style="padding:5pt; text-align:center;" colspan="3">03AARCS3853M1ZT</td>
            <td style="padding:5pt; text-align:center;" colspan="3"></td>
        </tr> -->
        <tr>
            <td colspan="9" >
                #if($data.isInvoiceTemplate == false && $data.isProvisional== true)
                <div>
                    <h3>Click on below button to approve Work Order : </h3>
                    <a href="$data.vendorUrl">
                        <button style="font-size:20px; margin:10px; background-color: #008CBA; color:white">Proceed to Approve</button>
                    </a>
                </div>
                #end
            </td>

        </tr>
        <tr>
            <td style="padding:5pt; text-align:left;" colspan="9">
                <h4>Terms & Conditions:</h4>
                <ol>
                    <li>Issue Bill Company and City Wise as mentioned above.</li>
                    <li>Time and place of execution is written , kindly raise invoice accordingly.</li>
                    <li>Payment after delivery against invoice as per agreed payment terms.</li>
                    <li>Our GSTIN number is mandatory in all invoices if details are not mention then no payment will be processed.
                    </li>
                    <li>In case of unregistered supplier no tax to be paid. If you are registered then contact Chaayos team to update the same.
                    </li>
                    <li>Separate Invoice/Bill of Supply should be issued for taxable items and non-taxable items.</li>
                    <li>If GST registration is applied then tax on purchase to be paid after receipt ofInvoice with GST Details.
                    </li>
                </ol>
            </td>
        </tr>
        </tbody>
    </table>

    #if($data.isProvisional == false)
    <div>
        <h3> Signed by $data.vendorContractSoInfo.vendorName on  $data.dateTool.format('d MMM, yyyy', $data.vendorContractSoInfo.lastUpdateTime) (IP : $data.vendorContractSoInfo.ipAddress) </h3>
    </div>
    #end
    #if($data.isInvoiceTemplate == false && $data.isProvisional== true)
    <div style="width:100%; background-color: red; padding:20px">
        <h3 style="text-align:center; color:white" > Provisional Work Order, Approval Required.</h3>
    </div>
    #end
    #if($data.isInvoiceTemplate == false && $data.isProvisional== false)
    <div style="width:100%; background-color: green; padding:20px">
        <h3 style="text-align:center; color:white">Approved Work Order</h3>
    </div>
    #end
    <p style="font-size: 10.0pt;line-height: 115%; font-family: 'Cambria', serif; color: black;">
        This is a computer generated order hence signature is not required.
    </p>
    <!-- <table>
        <tr>
            <td class="button">
                <a href="${data.disclaimerLink}">CLICK HERE TO READ DISCLAIMNER</a>
            </td>
        </tr>
    </table> -->
</div>
</body>
</html>
