
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Dispatch E-Way Request</title>
</head>
<body>
	<div>E-Way Request for Dispatch of Vehicle, Please find JSON file
		as attachment.</div>
	<div style="margin-top: 50px;">
		<b>Dispatch Details</b>
	</div>
	<div>
		<div style="padding: 15px">Dispatch Id:
			$data.dispatch.dispatchId</div>
		<div style="padding: 15px">Dispatch Date:
			$date.format('yyyy-MM-dd',$data.dispatch.dispatchDate)</div>
		<div style="padding: 15px">Vehicle Name :
			$data.dispatch.vehicle.name</div>
		<div style="padding: 15px">Transport Mode:
			$data.dispatch.vehicle.transportMode</div>
		<div style="padding: 15px">Registration Number:
			$data.dispatch.vehicleNumber</div>
		<div style="padding: 15px">E-Way Requirement: $data.ewayRequired</div>
	</div>
	<div style="margin-top: 50px;">
		<b>Transfer Orders Data</b>
	</div>
	<table>
		<tr>
			<th style="padding: 15px">Transfer Order Number</th>
			<th style="padding: 15px">From Unit</th>
			<th style="padding: 15px">To Unit</th>
			<th style="padding: 15px">Date</th>
			<th style="padding: 15px">Value</th>
		</tr>
		#foreach( $item in $data.bills)
		<tr>
			<td style="padding: 15px">$item.transferOrder.id</td>
			<td style="padding: 15px">$item.transferOrder.generationUnitId.name</td>
			<td style="padding: 15px">$item.transferOrder.generatedForUnitId.name</td>
			<td style="padding: 15px">$item.transferOrder.generationTime</td>
			<td style="padding: 15px">$item.transferOrder.totalAmount</td>
		</tr>
		#end
	</table>
	<div style="margin-top: 50px;">
		<b>Note:</b> This is for reference purpose only.
	</div>
</body>
</html>