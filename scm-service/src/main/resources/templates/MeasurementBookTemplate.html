<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Measurement Book Details</title>
</head>
<body>
<!--      header      -->
<!--<div style="background-color: #213141; padding: 10px; text-align: center;">
            <img src="http://cdn.lmitassets.com/gograbon/images/merchant/chaayos.jpeg" />
        </div>-->

<div
        style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
    <div style="padding: 15px 5px;">
        SR Id: $data.serviceReceive.id <span style="float: right;"><b>Receiving Time:</b>$date.format('medium', $data.serviceReceive.creationTime)</span>
    </div>
    <div style="padding: 15px 5px;">Generated By :
        $data.serviceReceive.createdBy.name</div>
    <div style="padding: 15px 5px;">Total Amount:
        $data.serviceReceive.totalAmount</div>

    <div class="row" style="margin: 10px; padding: 5px; background: #c90;">
        <div class="col-3"
             style="display: inline-block; vertical-align: top; width: 15%;">
            <b>Unit Name</b>
        </div>
        <div class="col-3"
             style="display: inline-block; vertical-align: top; width: 15%;">
            <b>Cost Element Name</b>
        </div>
        <div class="col-3"
             style="display: inline-block; vertical-align: top; width: 15%;">
            <b>Unit of measure</b>
        </div>
        <div class="col-3"
             style="display: inline-block; vertical-align: top; width: 15%;">
            <b>Total Amount</b>
        </div>

    </div>
    #foreach( $item in $data.serviceReceive.serviceReceiveItems )
    <div class="row"
         style="margin: 0; padding: 5px; border-bottom: #ddd 1px solid;">
        <div class="col-3"
             style="display: inline-block; vertical-align: top; width: 15%;">$item.businessCostCenterName</div>
        <div class="col-3"
             style="display: inline-block; vertical-align: top; width: 15%;">$item.costElementName</div>
        <div class="col-3"
             style="display: inline-block; vertical-align: top; width: 15%;">$item.unitOfMeasure</div>
        <div class="col-3"
             style="display: inline-block; vertical-align: top; width: 15%;">$item.totalAmount</div>
    </div>
    #end


    <div style="margin-top: 50px;">
        <b>Note:</b> This is for reference purpose only.
    </div>
</div>
</body>
