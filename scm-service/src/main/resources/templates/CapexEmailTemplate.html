<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Capex Email Notification</title>
</head>
<body>

<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
    <div style="text-align: center; padding:10px; font-size: 16px;">
        <p>$data.subjectOfEmail [$data.userId]</p>
    </div>
    <div style=" background-color: #e2e2e2;">
        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Capex Audit Id : </b> $data.capexAuditDetailData.id</td>
                <td style="padding:5pt;text-align:left;"><b>Capex Request Id : </b> $data.capexAuditDetailData.capexRequestId</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Capex Version : </b> $data.capexAuditDetailData.version</td>
                <td style="padding:5pt;text-align:left;"><b>Capex uploaded By : </b> $data.uploadedBy</td>
            </tr>
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Capex type : </b> $data.capexRequestDetailData.type</td>
                <td style="padding:5pt;text-align:left;"><b>Capex Current Status : </b> $data.capexRequestDetailData.status</td>
            </tr>
        </table>
    </div>
    <div style="margin-bottom:30px;">
        #if ($data.budgets)
        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            <thead>
            <tr style="background: #c90;">
                <th style="padding:5pt;text-align:center;"><b>Department Id</b></th>
                <th style="padding:5pt;text-align:center;"><b>Department Name</b></th>
                <th style="padding:5pt;text-align:center;"><b>Total Amount</b></th>
            </tr>
            </thead>
            <tbody>
            #foreach( $item in $data.budgets )
            <tr>
                <td style="padding:5pt;text-align:center;">$item.deptId</td>
                <td style="padding:5pt;text-align:left;">$item.deptName</td>
                <td style="padding:5pt;text-align:center;">$item.amount</td>
            </tr>
            #end
            </tbody>
        </table>
        #else
        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            <thead>
            <tr style="background: #c90;">
                <th style="padding:5pt;text-align:center;"><b>Department Id</b></th>
                <th style="padding:5pt;text-align:center;"><b>Department Name</b></th>
                <th style="padding:5pt;text-align:center;"><b>Original Amount</b></th>
                <th style="padding:5pt;text-align:center;"><b>Budget Amount</b></th>
                <th style="padding:5pt;text-align:center;"><b>Running Amount</b></th>
                <th style="padding:5pt;text-align:center;"><b>Remaining Amount</b></th>
            </tr>
            </thead>
            <tbody>
            #foreach( $item in $data.budgetDetailData )
            <tr>
                <td style="padding:5pt;text-align:center;">$item.departmentId</td>
                <td style="padding:5pt;text-align:left;">$item.departmentName</td>
                <td style="padding:5pt;text-align:center;">#if($item.originalAmount == 0) $data.mathTool.roundTo(2,$item.originalAmount) #else $item.originalAmount #end</td>
                <td style="padding:5pt;text-align:center;">#if($item.budgetAmount == 0) $data.mathTool.roundTo(2,$item.budgetAmount) #else $item.budgetAmount #end</td>
                <td style="padding:5pt;text-align:center;">#if($item.runningAmount == 0) $data.mathTool.roundTo(2,$item.runningAmount) #else $item.runningAmount #end</td>
                <td style="padding:5pt;text-align:center;">#if($item.remainingAmount == 0) $data.mathTool.roundTo(2,$item.remainingAmount) #else $item.remainingAmount #end</td>
            </tr>
            #end
            </tbody>
        </table>
        #end
        <div style="padding:15px 5px; margin-right: 70px;">
            <span style="float: right;"><b>Total Amount:</b> $data.totalAmount</span>
        </div>
    </div>
</div>
</body>
</html>