<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="SCM Report: TO EP closure overdue"
			type="Automated" accessCode="Automated" id="1"
			fromEmail="<EMAIL>" toEmails="<EMAIL>"
			schedule="">
			<reports>
				<report id="1" name="TO EP closure overdue"
					executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    G.GATEPASS_ID,
    G.OPERATION_TYPE,
    G.RETURN_STATUS,
    DATE_ADD(G.ISSUE_DATE,
        INTERVAL G.EXPECTED_RETURN DAY) AS 'RETURN_DATE'
FROM
    KETTLE_SCM_DEV.GATEPASS_DATA G
WHERE
    G.RETURNABLE = 'Y'
        AND G.STATUS = 'PENDING_RETURN' AND DATE_ADD(G.ISSUE_DATE,
        INTERVAL G.EXPECTED_RETURN DAY)  < now();
        ]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>


